{"permissions": {"allow": ["Bash(rg:*)", "Bash(grep:*)", "<PERSON><PERSON>(mv:*)", "mcp__perplexity-ask__perplexity_ask", "mcp__perplexity-ask__perplexity_ask", "Bash(pnpm dlx:*)", "Bash(pnpm ls:*)", "Bash(ls:*)", "Bash(pnpm install:*)", "Bash(find:*)", "Bash(pnpm list:*)", "Bash(pnpm view:*)", "mcp__desktop-commander__read_file", "mcp__desktop-commander__search_files", "Bash(node:*)", "Bash(cp:*)", "<PERSON><PERSON>(diff:*)", "Bash(awk:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(pnpm run dev:web:*)", "Bash(pnpm test:*)", "Bash(xcrun simctl shutdown:*)", "Bash(xcrun simctl boot:*)", "<PERSON><PERSON>(sed:*)", "Bash(pnpm run ios:*)", "Bash(xcodebuild:*)", "<PERSON><PERSON>(touch:*)", "Bash(pod install:*)", "<PERSON><PERSON>(chmod:*)", "Bash(pnpm exec expo prebuild:*)", "Bash(pnpm exec expo run:ios:*)", "mcp__desktop-commander__search_code", "mcp__desktop-commander__create_directory", "mcp__desktop-commander__read_multiple_files", "mcp__desktop-commander__edit_block", "mcp__desktop-commander__write_file", "mcp__desktop-commander__edit_block", "Bash(npm run dev:clean:*)", "Bash(tree:*)", "mcp__desktop-commander__execute_command", "mcp__desktop-commander__list_directory", "mcp__desktop-commander__list_directory", "mcp__brave-search__brave_web_search", "Bash(yarn expo export:*)", "WebFetch(domain:docs.expo.dev)", "<PERSON><PERSON>(echo:*)", "mcp__desktop-commander__move_file", "Bash(npx expo install:*)", "Bash(git cherry-pick:*)", "Bash(gunzip:*)", "<PERSON><PERSON>(pkill:*)", "Bash(git commit:*)", "Bash(git add:*)", "Bash(wc:*)", "Bash(pnpm lint:*)", "Bash(npx tsc:*)", "Bash(rm:*)", "Bash(firebase projects:list:*)", "Bash(wc:*)", "Bash(pnpm exec tsc:*)", "Bash(pnpm exec expo export:*)", "Bash(pnpm exec eslint:*)", "Bash(firebase use:*)", "mcp__desktop-commander__read_output", "mcp__desktop-commander__force_terminate", "Bash(firebase functions:config:get:*)"], "deny": []}}