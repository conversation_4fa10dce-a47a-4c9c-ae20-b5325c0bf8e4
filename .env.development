# Development Environment Configuration
EXPO_PUBLIC_ENVIRONMENT=development

# Firebase Development Project
EXPO_PUBLIC_FIREBASE_API_KEY=AIzaSyAVDcjU_1bv3p3C0XTj0HoFq7sv1KezEs8
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=kingly-choosehealthy-dev.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=kingly-choosehealthy-dev
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=kingly-choosehealthy-dev.firebasestorage.app
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=************
EXPO_PUBLIC_FIREBASE_APP_ID=1:************:web:26673127dcef3b9b35d3a4
EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=G-2GK4YL0GJW

# Auth Configuration
EXPO_PUBLIC_FIREBASE_WEB_CLIENT_ID=************-0a08n16m8qs3emha4c4uql7f8sd5rk48.apps.googleusercontent.com
EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID=************-0a08n16m8qs3emha4c4uql7f8sd5rk48.apps.googleusercontent.com
EXPO_PUBLIC_APPLE_CLIENT_ID=com.kingly.choosehealthy

# Test Accounts (Development Only)
EXPO_PUBLIC_GOOGLE_TEST_EMAIL=<EMAIL>
EXPO_PUBLIC_GOOGLE_TEST_PASSWORD=GoogleTest123!
EXPO_PUBLIC_APPLE_TEST_EMAIL=<EMAIL>
EXPO_PUBLIC_APPLE_TEST_PASSWORD=AppleTest123!
EXPO_PUBLIC_TEST_ACCOUNT_IDS=aCGNX5YBh6WgOi6Wt4cXLvB6PFI3,kzXsQSmnuNVY36HBCgThmlLSsvD3
EXPO_PUBLIC_FORCE_TEST_CLEANUP=false

# Stripe Test Keys
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51RFr1kK89Yg8HaRnodlev53ziUH3TKpFeX8wYwytvtKGwQ1TNUJhpQ6Ff9Lz7DkueyQ2arQdGhy5PHgfiWexWO7500GVKlRQMx
EXPO_PUBLIC_STRIPE_PRICE_ID_MONTHLY=price_1RSdGRK89Yg8HaRnPqWPtX9I
EXPO_PUBLIC_STRIPE_PRICE_ID_YEARLY=price_1RSdGVK89Yg8HaRnHvxRSXbr

# PostHog Development
EXPO_PUBLIC_POSTHOG_API_KEY=phc_Wgr2j6EqgcMrsY2iKaLfOFKRTuNqTKFUGvdCKCxsWTe
EXPO_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com

# Feature Flags
EXPO_PUBLIC_ENABLE_LIDAR=true
EXPO_PUBLIC_ENABLE_OFFLINE_MODE=true
EXPO_PUBLIC_ENABLE_DEBUG=true
EXPO_PUBLIC_DEBUG_MODE=true

# Emulator Configuration (disabled by default, controlled by .env.local)
# EXPO_PUBLIC_USE_EMULATORS=false