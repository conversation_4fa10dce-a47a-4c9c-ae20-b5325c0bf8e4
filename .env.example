# Example Environment Configuration
# Copy this to .env.development, .env.staging, .env.production and fill with real values

EXPO_PUBLIC_ENVIRONMENT=development

# Firebase Project Configuration
EXPO_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.firebasestorage.app
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
EXPO_PUBLIC_FIREBASE_APP_ID=your_app_id
EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=your_measurement_id

# Auth Configuration
EXPO_PUBLIC_FIREBASE_WEB_CLIENT_ID=your_web_client_id
EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID=your_google_web_client_id
EXPO_PUBLIC_APPLE_CLIENT_ID=com.your.app

# Test Accounts (Development Only)
EXPO_PUBLIC_GOOGLE_TEST_EMAIL=<EMAIL>
EXPO_PUBLIC_GOOGLE_TEST_PASSWORD=TestPassword123!
EXPO_PUBLIC_APPLE_TEST_EMAIL=<EMAIL>
EXPO_PUBLIC_APPLE_TEST_PASSWORD=TestPassword123!
EXPO_PUBLIC_TEST_ACCOUNT_IDS=test_account_ids
EXPO_PUBLIC_FORCE_TEST_CLEANUP=false

# Stripe Configuration
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
EXPO_PUBLIC_STRIPE_PRICE_ID_MONTHLY=price_monthly_id
EXPO_PUBLIC_STRIPE_PRICE_ID_YEARLY=price_yearly_id

# PostHog Configuration
EXPO_PUBLIC_POSTHOG_API_KEY=your_posthog_key
EXPO_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com

# Feature Flags
EXPO_PUBLIC_ENABLE_LIDAR=true
EXPO_PUBLIC_ENABLE_OFFLINE_MODE=true
EXPO_PUBLIC_ENABLE_DEBUG=true
EXPO_PUBLIC_DEBUG_MODE=true

# Emulator Configuration (controlled by .env.local)
EXPO_PUBLIC_USE_EMULATORS=false