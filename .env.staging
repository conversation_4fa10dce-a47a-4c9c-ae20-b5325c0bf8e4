# Staging Environment Configuration
EXPO_PUBLIC_ENVIRONMENT=staging

# Firebase Staging Project
EXPO_PUBLIC_FIREBASE_API_KEY=staging-api-key-to-be-set
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=kingly-choosehealthy-staging.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=kingly-choosehealthy-staging
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=kingly-choosehealthy-staging.firebasestorage.app
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=staging-sender-id-to-be-set
EXPO_PUBLIC_FIREBASE_APP_ID=staging-app-id-to-be-set
EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=staging-measurement-id-to-be-set

# Auth Configuration
EXPO_PUBLIC_FIREBASE_WEB_CLIENT_ID=staging-web-client-id-to-be-set
EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID=staging-google-web-client-id-to-be-set
EXPO_PUBLIC_APPLE_CLIENT_ID=com.kingly.choosehealthy

# Test Accounts (Staging - Disabled)
EXPO_PUBLIC_GOOGLE_TEST_EMAIL=
EXPO_PUBLIC_GOOGLE_TEST_PASSWORD=
EXPO_PUBLIC_APPLE_TEST_EMAIL=
EXPO_PUBLIC_APPLE_TEST_PASSWORD=
EXPO_PUBLIC_TEST_ACCOUNT_IDS=
EXPO_PUBLIC_FORCE_TEST_CLEANUP=false

# Stripe Test Keys
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_staging-stripe-key-to-be-set
EXPO_PUBLIC_STRIPE_PRICE_ID_MONTHLY=price_staging_monthly_to_be_set
EXPO_PUBLIC_STRIPE_PRICE_ID_YEARLY=price_staging_yearly_to_be_set

# PostHog Staging
EXPO_PUBLIC_POSTHOG_API_KEY=staging-posthog-key-to-be-set
EXPO_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com

# Feature Flags
EXPO_PUBLIC_ENABLE_LIDAR=true
EXPO_PUBLIC_ENABLE_OFFLINE_MODE=true
EXPO_PUBLIC_ENABLE_DEBUG=false
EXPO_PUBLIC_DEBUG_MODE=false

# Emulator Configuration (disabled in staging)
EXPO_PUBLIC_USE_EMULATORS=false