{"expo": {"name": "<PERSON>ose Healthy", "slug": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "scheme": "choosehealthy", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"bundleIdentifier": "com.kingly.choosehealthy", "buildNumber": "1", "supportsTablet": true, "googleServicesFile": "./GoogleService-Info.plist", "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to scan food items, barcodes, and measure food portions using LiDAR for accurate nutritional information.", "NSPhotoLibraryUsageDescription": "This app needs access to your photo library to upload photos for food analysis.", "NSPhotoLibraryAddUsageDescription": "This app saves food scan images to your photo library for later reference.", "NSMicrophoneUsageDescription": "This app uses the microphone for accessibility features to allow voice commands when using LiDAR scanning.", "NSHealthShareUsageDescription": "This app requires access to Apple Health to read your health and fitness data.", "NSHealthUpdateUsageDescription": "This app requires access to Apple Health to update your health and fitness data."}, "associatedDomains": ["applinks:choosehealthy.com"]}, "android": {"package": "com.kingly.choosehealthy", "versionCode": 1, "permissions": ["android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACTIVITY_RECOGNITION", "android.permission.BODY_SENSORS", "android.permission.RECORD_AUDIO"], "adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#ffffff"}}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", ["expo-build-properties", {"android": {"googleServicesFile": "./google-services.json", "minSdkVersion": 26}, "ios": {"googleServicesFile": "./GoogleService-Info.plist"}}], ["expo-camera", {"cameraPermission": "This app uses the camera to scan food items and analyze nutritional content."}], ["expo-image-picker", {"photosPermission": "This app needs access to your photos to analyze food images."}]], "experiments": {"typedRoutes": true, "tsconfigPaths": true}, "extra": {"supportsWearable": true, "posthogApiKey": "$EXPO_PUBLIC_POSTHOG_API_KEY", "environment": "development", "eas": {"projectId": "1af73805-4a38-4623-ac05-2c02ca5a0412"}, "router": {}}, "owner": "<PERSON><PERSON><PERSON>"}}