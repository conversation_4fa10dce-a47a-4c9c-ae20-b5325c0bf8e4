module.exports = function(api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      [
        'module-resolver',
        {
          root: ['./src'],
          extensions: ['.ios.js', '.android.js', '.js', '.ts', '.tsx', '.json'],
          alias: {
            // Project alias
            '@': './src',
            // Removed lucide-react-native shim - using direct Expo icons now
            // Node.js core module shims
            'stream': './src/utils/stream-shim',
            'ws': './src/utils/ws-shim',
          },
        },
      ],
      // Reanimated plugin has to be listed last
      'react-native-reanimated/plugin',
    ],
  };
}; 