# API Security Plan for <PERSON><PERSON><PERSON><PERSON><PERSON>

**Created**: January 20, 2025  
**Priority**: CRITICAL - Exposed API keys need immediate attention

## Executive Summary

The ChooseHealthy app currently has several API keys exposed in the client-side code, including a critical OpenAI secret key. This document provides a comprehensive plan to secure all API integrations.

## Current Security Issues

### 🔴 Critical Issues
1. **OpenAI Secret Key Exposed**: Full secret key (`sk-proj-...`) is exposed client-side
2. **No API Proxy**: Direct API calls from client with exposed keys
3. **Keys in Version Control**: `.env` file with real keys is committed

### 🟡 Medium Risk Issues
1. **Unrestricted API Keys**: Google Vision, Firebase keys lack restrictions
2. **Third-party Service Keys**: Multiple service keys exposed client-side
3. **No Key Rotation**: No policy for rotating compromised keys

## MVP Security Approach for App Store

### Quick & Simple Firebase Auth Security (1-2 days)

The simplest approach is to use Firebase Authentication's built-in security. Since you already have Firebase Auth implemented, this requires minimal changes:

1. **Use Firebase Auth User Token** (Already implemented)
2. **Firebase Functions with Auth Check** (Simple addition)
3. **No additional libraries needed**

### Implementation (MVP Version)

#### Step 1: Secure Your Cloud Functions (30 minutes)

Update ALL your Firebase Cloud Functions to check auth:

```typescript
// functions/src/index.ts
import * as functions from 'firebase-functions';

// Simple auth check for ALL functions
export const analyzeFood = functions.https.onCall(async (data, context) => {
  // This is all you need for MVP security!
  if (!context.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'User must be authenticated to use this feature'
    );
  }

  // Your existing logic here
  const { imageUrl, prompt } = data;
  // ... rest of function
});
```

#### Step 2: Update Client Calls (Already Done!)

Your app already uses Firebase Auth, so the client automatically sends auth tokens:

```typescript
// This is already in your app - no changes needed!
import { getFunctions, httpsCallable } from 'firebase/functions';

const functions = getFunctions();
const analyzeFood = httpsCallable(functions, 'analyzeFood');

// The SDK automatically adds the auth token!
const result = await analyzeFood({ imageUrl, prompt });
```

#### Step 3: Basic Rate Limiting (Optional but Recommended)

Add simple per-user rate limiting to prevent abuse:

```typescript
// functions/src/rateLimiter.ts
const userCallCounts = new Map();

export function checkRateLimit(userId: string, limit: number = 100): boolean {
  const now = Date.now();
  const userCalls = userCallCounts.get(userId) || [];
  
  // Remove calls older than 1 hour
  const recentCalls = userCalls.filter(time => now - time < 3600000);
  
  if (recentCalls.length >= limit) {
    return false;
  }
  
  recentCalls.push(now);
  userCallCounts.set(userId, recentCalls);
  return true;
}

// Use in your functions
export const analyzeFood = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
  }

  // Simple rate limiting
  if (!checkRateLimit(context.auth.uid)) {
    throw new functions.https.HttpsError('resource-exhausted', 'Too many requests');
  }

  // Your function logic
});
```

### That's It for MVP! 🎉

This approach:
- ✅ Secures your APIs (only authenticated users can call them)
- ✅ Works with your existing Firebase Auth
- ✅ No new dependencies or complex setup
- ✅ App Store compliant
- ✅ Can be implemented in 1-2 days

### For App Store Submission

1. **Remove all EXPO_PUBLIC_* API keys** that shouldn't be public
2. **Keep only Firebase config** (which is meant to be public)
3. **Update your .env**:

```env
# Safe to be public (Firebase is secured by rules)
EXPO_PUBLIC_FIREBASE_API_KEY=...
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=...
EXPO_PUBLIC_FIREBASE_PROJECT_ID=...
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=...
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=...
EXPO_PUBLIC_FIREBASE_APP_ID=...

# Move these to Cloud Functions environment
# NEVER put these in the app!
OPENAI_API_KEY=...
GOOGLE_VISION_API_KEY=...
NUTRITIONIX_API_KEY=...
```

### Future Enhancements (Post-Launch)

After you're in the App Store, you can add:
- Firebase App Check (additional security layer)
- Custom API keys per user
- Advanced rate limiting
- IP whitelisting
- Request signing

But for MVP and App Store approval, Firebase Auth + Cloud Functions is sufficient!

## Implementation Plan

### Phase 1: Immediate Actions (Day 1)

#### 1.1 Revoke Compromised Keys
```bash
# IMMEDIATELY revoke these keys from their respective dashboards:
# 1. OpenAI Dashboard: https://platform.openai.com/api-keys
# 2. Google Cloud Console: https://console.cloud.google.com/apis/credentials
# 3. Other service dashboards
```

#### 1.2 Remove Keys from Version Control
```bash
# Remove .env from git history
git rm --cached .env
echo ".env" >> .gitignore
git add .gitignore
git commit -m "Remove API keys from version control"

# Create template file
cp .env .env.example
# Edit .env.example to replace all real keys with placeholders
```

#### 1.3 Create `.env.example`
```env
# .env.example - Template for environment variables

# OpenAI Configuration (Server-side only!)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-1106-preview

# Google Vision (Restricted API key)
EXPO_PUBLIC_GOOGLE_VISION_API_KEY=your_restricted_google_api_key

# Firebase Configuration (Public but secured with rules)
EXPO_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
EXPO_PUBLIC_FIREBASE_APP_ID=your_app_id

# Third-party Services (Server-side only!)
NUTRITIONIX_APP_ID=your_nutritionix_app_id
NUTRITIONIX_API_KEY=your_nutritionix_api_key
USDA_API_KEY=your_usda_api_key
CLARIFAI_API_KEY=your_clarifai_api_key
SPOONACULAR_API_KEY=your_spoonacular_api_key

# Analytics (Public)
EXPO_PUBLIC_POSTHOG_API_KEY=your_posthog_public_key
EXPO_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com

# Stripe (Public test key is safe)
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_key
STRIPE_SECRET_KEY=sk_test_your_secret_key

# Feature Flags
EXPO_PUBLIC_ENABLE_LIDAR=true
EXPO_PUBLIC_ENABLE_OFFLINE_MODE=true
EXPO_PUBLIC_ENABLE_DEBUG=false
```

### Phase 2: API Proxy Implementation (Days 2-3)

#### 2.1 Create Firebase Cloud Functions for API Calls

Create `functions/src/apiProxy.ts`:

```typescript
import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import OpenAI from 'openai';
import axios from 'axios';

// Initialize OpenAI with server-side key
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// OpenAI Food Analysis Proxy
export const analyzeFood = functions.https.onCall(async (data, context) => {
  // Check authentication
  if (!context.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'User must be authenticated to analyze food'
    );
  }

  const { imageUrl, prompt } = data;

  try {
    // Call OpenAI Vision API
    const response = await openai.chat.completions.create({
      model: "gpt-4-vision-preview",
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: prompt },
            { type: "image_url", image_url: { url: imageUrl } }
          ],
        },
      ],
      max_tokens: 1000,
    });

    // Log usage for monitoring
    await admin.firestore().collection('api_usage').add({
      userId: context.auth.uid,
      service: 'openai',
      endpoint: 'food_analysis',
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      tokens: response.usage?.total_tokens || 0,
    });

    return {
      analysis: response.choices[0].message.content,
      usage: response.usage,
    };
  } catch (error) {
    console.error('OpenAI API error:', error);
    throw new functions.https.HttpsError(
      'internal',
      'Failed to analyze food image'
    );
  }
});

// Google Vision API Proxy
export const detectLabels = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'User must be authenticated'
    );
  }

  const { imageUrl } = data;

  try {
    const response = await axios.post(
      `https://vision.googleapis.com/v1/images:annotate?key=${process.env.GOOGLE_VISION_API_KEY}`,
      {
        requests: [{
          image: { source: { imageUri: imageUrl } },
          features: [
            { type: 'LABEL_DETECTION', maxResults: 10 },
            { type: 'TEXT_DETECTION' }
          ]
        }]
      }
    );

    return {
      labels: response.data.responses[0].labelAnnotations,
      text: response.data.responses[0].textAnnotations,
    };
  } catch (error) {
    console.error('Google Vision API error:', error);
    throw new functions.https.HttpsError(
      'internal',
      'Failed to process image'
    );
  }
});

// Nutritionix API Proxy
export const getNutritionData = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'User must be authenticated'
    );
  }

  const { query } = data;

  try {
    const response = await axios.post(
      'https://trackapi.nutritionix.com/v2/natural/nutrients',
      { query },
      {
        headers: {
          'x-app-id': process.env.NUTRITIONIX_APP_ID,
          'x-app-key': process.env.NUTRITIONIX_API_KEY,
          'Content-Type': 'application/json',
        }
      }
    );

    return {
      foods: response.data.foods,
    };
  } catch (error) {
    console.error('Nutritionix API error:', error);
    throw new functions.https.HttpsError(
      'internal',
      'Failed to get nutrition data'
    );
  }
});

// USDA API Proxy
export const searchUSDAFoods = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'User must be authenticated'
    );
  }

  const { query, pageSize = 10 } = data;

  try {
    const response = await axios.get(
      'https://api.nal.usda.gov/fdc/v1/foods/search',
      {
        params: {
          query,
          pageSize,
          api_key: process.env.USDA_API_KEY,
        }
      }
    );

    return {
      foods: response.data.foods,
      totalHits: response.data.totalHits,
    };
  } catch (error) {
    console.error('USDA API error:', error);
    throw new functions.https.HttpsError(
      'internal',
      'Failed to search USDA foods'
    );
  }
});
```

#### 2.2 Deploy Cloud Functions

```bash
# Set environment variables for Cloud Functions
firebase functions:config:set \
  openai.key="your-new-openai-key" \
  google.vision_key="your-google-vision-key" \
  nutritionix.app_id="your-nutritionix-app-id" \
  nutritionix.api_key="your-nutritionix-api-key" \
  usda.api_key="your-usda-api-key"

# Deploy functions
cd functions
npm run deploy
```

#### 2.3 Update Client Services

Update `services/openaiService.ts`:

```typescript
import { getFunctions, httpsCallable } from 'firebase/functions';

const functions = getFunctions();

export async function analyzeFood(imageUrl: string, prompt: string) {
  try {
    const analyzeFood = httpsCallable(functions, 'analyzeFood');
    const result = await analyzeFood({ imageUrl, prompt });
    return result.data;
  } catch (error) {
    console.error('Error analyzing food:', error);
    throw error;
  }
}

export async function getNutritionData(query: string) {
  try {
    const getNutritionData = httpsCallable(functions, 'getNutritionData');
    const result = await getNutritionData({ query });
    return result.data;
  } catch (error) {
    console.error('Error getting nutrition data:', error);
    throw error;
  }
}
```

### Phase 3: API Key Restrictions (Day 4)

#### 3.1 Google Cloud Console Configuration

1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Navigate to APIs & Services > Credentials
3. Click on your API key
4. Add restrictions:
   - **Application restrictions**: 
     - iOS apps: Add bundle ID `com.kingly.choosehealthy`
     - Android apps: Add package name and SHA-1 certificate
   - **API restrictions**: 
     - Restrict to Google Vision API only

#### 3.2 Firebase Security Rules

Update `firestore.rules`:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // API usage tracking (read-only for users)
    match /api_usage/{document} {
      allow read: if request.auth != null && resource.data.userId == request.auth.uid;
      allow write: if false; // Only Cloud Functions can write
    }
    
    // User data with proper authentication
    match /users/{userId}/{document=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

#### 3.3 Configure Firebase App Check

Add to `app.json`:

```json
{
  "expo": {
    "plugins": [
      [
        "@react-native-firebase/app-check",
        {
          "iosAppAttestEnabled": true,
          "androidSafetyNetEnabled": true
        }
      ]
    ]
  }
}
```

Initialize in your app:

```typescript
import { initializeAppCheck, ReCaptchaV3Provider } from 'firebase/app-check';

// Initialize Firebase App Check
const appCheck = initializeAppCheck(app, {
  provider: new ReCaptchaV3Provider('your-recaptcha-site-key'),
  isTokenAutoRefreshEnabled: true
});
```

### Phase 4: Monitoring & Rate Limiting (Day 5)

#### 4.1 Implement Rate Limiting

Create `functions/src/rateLimiting.ts`:

```typescript
import * as admin from 'firebase-admin';

const RATE_LIMITS = {
  openai: { calls: 100, windowMinutes: 60 },
  google_vision: { calls: 1000, windowMinutes: 60 },
  nutritionix: { calls: 500, windowMinutes: 60 },
};

export async function checkRateLimit(
  userId: string, 
  service: string
): Promise<boolean> {
  const now = Date.now();
  const windowStart = now - (RATE_LIMITS[service].windowMinutes * 60 * 1000);
  
  const usageQuery = await admin.firestore()
    .collection('api_usage')
    .where('userId', '==', userId)
    .where('service', '==', service)
    .where('timestamp', '>', new Date(windowStart))
    .get();
  
  return usageQuery.size < RATE_LIMITS[service].calls;
}
```

#### 4.2 Add Monitoring Dashboard

Create `functions/src/monitoring.ts`:

```typescript
export const getApiUsageStats = functions.https.onCall(async (data, context) => {
  // Admin only
  if (!context.auth || !context.auth.token.admin) {
    throw new functions.https.HttpsError(
      'permission-denied',
      'Admin access required'
    );
  }

  const { startDate, endDate } = data;
  
  const usage = await admin.firestore()
    .collection('api_usage')
    .where('timestamp', '>=', startDate)
    .where('timestamp', '<=', endDate)
    .get();
  
  const stats = {
    totalCalls: usage.size,
    byService: {},
    byUser: {},
    costs: {
      openai: 0,
      google_vision: 0,
    }
  };
  
  usage.forEach(doc => {
    const data = doc.data();
    // Aggregate stats
    stats.byService[data.service] = (stats.byService[data.service] || 0) + 1;
    stats.byUser[data.userId] = (stats.byUser[data.userId] || 0) + 1;
    
    // Calculate approximate costs
    if (data.service === 'openai' && data.tokens) {
      stats.costs.openai += (data.tokens / 1000) * 0.002; // GPT-4 pricing
    }
  });
  
  return stats;
});
```

### Phase 5: Key Rotation Policy (Ongoing)

#### 5.1 Quarterly Key Rotation Schedule

1. **Create rotation checklist**:
   ```markdown
   ## Q1 2025 Key Rotation (Due: March 31)
   - [ ] OpenAI API Key
   - [ ] Google Vision API Key
   - [ ] Nutritionix API Key
   - [ ] USDA API Key
   - [ ] Update Cloud Functions config
   - [ ] Test all integrations
   - [ ] Update documentation
   ```

2. **Automate rotation reminders**:
   ```typescript
   // functions/src/keyRotationReminder.ts
   export const keyRotationReminder = functions.pubsub
     .schedule('0 0 1 */3 *') // Every 3 months
     .onRun(async (context) => {
       // Send email to admin
       await sendAdminEmail({
         subject: 'API Key Rotation Required',
         body: 'Quarterly API key rotation is due. Please rotate all keys.',
       });
     });
   ```

#### 5.2 Emergency Key Rotation Procedure

1. **If key is compromised**:
   ```bash
   # 1. Immediately revoke compromised key
   # 2. Generate new key
   # 3. Update Cloud Functions
   firebase functions:config:set service.key="new-key"
   # 4. Deploy functions
   firebase deploy --only functions
   # 5. Monitor for unauthorized usage
   ```

## Testing Plan

### API Proxy Testing

Create `functions/test/apiProxy.test.ts`:

```typescript
import * as test from 'firebase-functions-test';
import * as admin from 'firebase-admin';

describe('API Proxy Functions', () => {
  const testEnv = test();
  
  it('should require authentication', async () => {
    const wrapped = testEnv.wrap(analyzeFood);
    
    await expect(wrapped({ imageUrl: 'test' }, {}))
      .rejects.toThrow('User must be authenticated');
  });
  
  it('should enforce rate limits', async () => {
    // Test rate limiting logic
  });
  
  it('should proxy OpenAI requests', async () => {
    // Test OpenAI proxy
  });
});
```

### End-to-End Testing

```typescript
// app/tests/apiSecurity.test.ts
describe('API Security', () => {
  it('should not expose API keys', () => {
    // Check that no API keys are in the bundle
    const bundleContent = fs.readFileSync('./dist/bundle.js', 'utf8');
    expect(bundleContent).not.toContain('sk-');
    expect(bundleContent).not.toContain('AIzaSy');
  });
  
  it('should use Cloud Functions for API calls', async () => {
    // Test that services use Cloud Functions
    const result = await analyzeFood('image.jpg', 'What is this?');
    expect(result).toBeDefined();
  });
});
```

## Rollout Plan

### Day 1: Emergency Response
- [ ] Revoke all exposed keys
- [ ] Remove keys from git
- [ ] Create new keys
- [ ] Deploy temporary fix using environment variables

### Days 2-3: API Proxy
- [ ] Implement Cloud Functions
- [ ] Update client services
- [ ] Deploy and test

### Day 4: Security Hardening
- [ ] Add API key restrictions
- [ ] Implement App Check
- [ ] Update security rules

### Day 5: Monitoring
- [ ] Add rate limiting
- [ ] Create monitoring dashboard
- [ ] Set up alerts

### Week 2: Documentation & Training
- [ ] Update all documentation
- [ ] Train team on new procedures
- [ ] Create runbooks

## Success Metrics

1. **Security Metrics**:
   - Zero exposed secret keys in codebase
   - 100% API calls through proxy
   - Rate limiting active on all endpoints

2. **Performance Metrics**:
   - API response time < 500ms
   - 99.9% uptime for proxy functions
   - Cost per API call tracked

3. **Monitoring Metrics**:
   - Real-time usage dashboard
   - Automated alerts for anomalies
   - Monthly cost reports

## Maintenance

### Weekly Tasks
- Review API usage logs
- Check for unusual patterns
- Monitor costs

### Monthly Tasks
- Update rate limits if needed
- Review security alerts
- Audit access logs

### Quarterly Tasks
- Rotate API keys
- Security audit
- Update documentation

## Emergency Contacts

- **OpenAI Support**: <EMAIL>
- **Google Cloud Support**: [Console](https://console.cloud.google.com/support)
- **Firebase Support**: [Console](https://console.firebase.google.com/support)

---

**Next Steps**: Begin Phase 1 immediately. The exposed OpenAI key is the highest priority.