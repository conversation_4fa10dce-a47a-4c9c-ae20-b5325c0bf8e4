# Health App Production Plan (Remaining Tasks)

This production plan outlines the **remaining** steps to prepare, build, and deploy the Health App MVP for iOS and Android. 

## ✅ **COMPLETED ITEMS**
The following are already implemented and functional:
- ✅ **Feature Flags**: Complete implementation in `featureFlags.ts` with PostHog integration
- ✅ **LiDAR Implementation**: Comprehensive LiDAR components and services for iOS
- ✅ **Water Tracking**: Full water tracking functionality with offline support
- ✅ **Profile Features**: User authentication, profile management, and settings
- ✅ **Stripe Configuration**: Cloud Functions, webhooks, and product setup completed
- ✅ **Storage Security Rules**: Production-ready Firebase Storage rules implemented
- ✅ **Privacy Infrastructure**: Privacy policy utilities and data management
- ✅ **Environment Variables**: Development/test environment properly configured

---

## 🚨 **CRITICAL REMAINING TASKS**

### 1. Firebase Security Rules ⚠️ **URGENT**
**Status**: Currently using development rules (allow all access)

**Action Required**: Update `firestore.rules` to production security:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isSignedIn() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isAdmin() {
      return request.auth.token.admin == true;
    }
    
    // Public collections - read accessible to all, write only to authenticated
    match /public/{document=**} {
      allow read: if true;
      allow write: if isSignedIn();
    }
    
    // Special test collection for iOS connectivity verification
    match /ios_test/{document=**} {
      allow read, write: if true;
    }
    
    // App configuration
    match /app_config/{document=**} {
      allow read: if true; 
      allow write: if isAdmin();
    }
    
    // User profiles
    match /profiles/{userId} {
      allow read: if isSignedIn() && (isOwner(userId) || isAdmin());
      allow write: if isSignedIn() && (isOwner(userId) || isAdmin());
    }
    
    // Water intake - core MVP feature
    match /water_intake/{recordId} {
      allow read: if isSignedIn() && (resource.data.userId == request.auth.uid || isAdmin());
      allow create: if isSignedIn() && (request.resource.data.userId == request.auth.uid || isAdmin());
      allow update, delete: if isSignedIn() && (resource.data.userId == request.auth.uid || isAdmin());
    }
    
    // Water intake goals - core MVP feature
    match /water_intake_goals/{userId} {
      allow read: if isSignedIn() && (isOwner(userId) || isAdmin());
      allow write: if isSignedIn() && (isOwner(userId) || isAdmin());
    }
    
    // Food scan results
    match /food_scans/{recordId} {
      allow read: if isSignedIn() && (resource.data.userId == request.auth.uid || isAdmin());
      allow create: if isSignedIn() && (request.resource.data.userId == request.auth.uid || isAdmin());
      allow update, delete: if isSignedIn() && (resource.data.userId == request.auth.uid || isAdmin());
    }
    
    // LiDAR scan data - specific for 3D depth measurements
    match /lidar_scans/{scanId} {
      allow read: if isSignedIn() && (resource.data.userId == request.auth.uid || isAdmin());
      allow create: if isSignedIn() && (request.resource.data.userId == request.auth.uid || isAdmin());
      allow update, delete: if isSignedIn() && (resource.data.userId == request.auth.uid || isAdmin());
    }
    
    // Calibration data for LiDAR
    match /calibration_data/{userId} {
      allow read, write: if isSignedIn() && (isOwner(userId) || isAdmin());
    }
    
    // Stripe payment collections
    match /stripe_customers/{customerId} {
      allow read: if isSignedIn() && (resource.data.user_id == request.auth.uid || isAdmin());
    }
    
    match /stripe_user_subscriptions/{subscriptionId} {
      allow read: if isSignedIn() && (resource.data.user_id == request.auth.uid || isAdmin());
    }
    
    match /stripe_orders/{orderId} {
      allow read: if isSignedIn() && (resource.data.user_id == request.auth.uid || isAdmin());
    }
    
    // Deny all other paths
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
```

### 2. Production Environment Configuration 🔧 **HIGH PRIORITY**

**Action Required**: Create production `.env` file:

```env
# Firebase Production Configuration
EXPO_PUBLIC_FIREBASE_API_KEY=your_production_api_key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your_production_project.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your_production_project_id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your_production_project.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_production_sender_id
EXPO_PUBLIC_FIREBASE_APP_ID=your_production_app_id
EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=your_production_measurement_id

# API Keys for Food Recognition (Production)
EXPO_PUBLIC_GOOGLE_VISION_API_KEY=your_production_vision_api_key

# Stripe Production Configuration
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_production_stripe_publishable_key
EXPO_PUBLIC_STRIPE_PRICE_ID_BASIC_MONTHLY=production_price_id
EXPO_PUBLIC_STRIPE_PRICE_ID_BASIC_YEARLY=production_price_id
EXPO_PUBLIC_STRIPE_PRICE_ID_PREMIUM_MONTHLY=production_price_id
EXPO_PUBLIC_STRIPE_PRICE_ID_PREMIUM_YEARLY=production_price_id
EXPO_PUBLIC_STRIPE_PRICE_ID_FAMILY_MONTHLY=production_price_id
EXPO_PUBLIC_STRIPE_PRICE_ID_FAMILY_YEARLY=production_price_id

# Production Feature Flags
EXPO_PUBLIC_ENABLE_OFFLINE_MODE=true
EXPO_PUBLIC_ENABLE_DEBUG=false
EXPO_PUBLIC_CACHE_TTL_MINUTES=60
EXPO_PUBLIC_ENABLE_LIDAR=true
```

### 3. EAS Build Configuration 📱 **HIGH PRIORITY**

**Action Required**: Update `eas.json` for production builds:

```json
{
  "cli": {
    "version": ">= 5.9.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "ios": {
        "resourceClass": "m1-medium"
      }
    },
    "preview": {
      "distribution": "internal",
      "ios": {
        "resourceClass": "m1-medium"
      },
      "android": {
        "buildType": "apk"
      },
      "env": {
        "NODE_ENV": "staging"
      }
    },
    "production": {
      "ios": {
        "resourceClass": "m1-medium",
        "credentialsSource": "remote"
      },
      "android": {
        "credentialsSource": "remote",
        "buildType": "app-bundle"
      },
      "env": {
        "NODE_ENV": "production"
      }
    }
  },
  "submit": {
    "production": {
      "ios": {
        "appleId": "<EMAIL>",
        "ascAppId": "**********",
        "appleTeamId": "ABCDEF1234"
      },
      "android": {
        "serviceAccountKeyPath": "./path/to/service-account.json",
        "track": "production"
      }
    }
  }
}
```

### 4. App Store Configuration 🍎 **MEDIUM PRIORITY**

**Action Required**: Update `app.json` bundle identifiers and permissions:

```json
{
  "expo": {
    "name": "Choose Healthy",
    "slug": "choose-healthy", 
    "version": "1.0.0",
    "ios": {
      "bundleIdentifier": "com.yourcompany.choosehealthy",
      "buildNumber": "1",
      "infoPlist": {
        "NSCameraUsageDescription": "This app uses the camera to scan food items, barcodes, and measure food portions using LiDAR for accurate nutritional information.",
        "NSPhotoLibraryUsageDescription": "This app needs access to your photo library to upload photos for food analysis.",
        "NSPhotoLibraryAddUsageDescription": "This app saves food scan images to your photo library for later reference.",
        "NSMicrophoneUsageDescription": "This app uses the microphone for accessibility features to allow voice commands when using LiDAR scanning.",
        "NSHealthShareUsageDescription": "This app requires access to Apple Health to read your health and fitness data.",
        "NSHealthUpdateUsageDescription": "This app requires access to Apple Health to update your health and fitness data."
      },
      "associatedDomains": [
        "applinks:choosehealthy.com"
      ]
    },
    "android": {
      "package": "com.yourcompany.choosehealthy",
      "versionCode": 1,
      "permissions": [
        "CAMERA",
        "READ_EXTERNAL_STORAGE", 
        "WRITE_EXTERNAL_STORAGE",
        "android.permission.ACTIVITY_RECOGNITION",
        "android.permission.BODY_SENSORS"
      ]
    }
  }
}
```

### 5. Firebase Production Deployment 🔥 **HIGH PRIORITY**

**Action Required**: Deploy production Firebase configurations:

```bash
# Deploy production Firestore security rules
npm run deploy:prod:rules

# Deploy production storage security rules  
npm run deploy:prod:storage

# Deploy production Firebase functions for Stripe
npm run deploy:prod:functions

# OR deploy everything at once
npm run deploy:prod:all

# Set Stripe production configuration
firebase functions:config:set stripe.secret_key="sk_live_..." --project kingly-choosehealthy-prod
firebase functions:config:set stripe.webhook_secret="whsec_..." --project kingly-choosehealthy-prod
```

### 6. Stripe Production Setup 💳 **HIGH PRIORITY**

**Action Required**: Configure Stripe for production:

1. **Switch to Stripe Live Mode**:
   - Update API keys in Firebase Functions config
   - Update publishable key in environment variables

2. **Create Production Products** (run with live keys):
   ```bash
   cd infra/stripe
   node product_setup.js
   ```

3. **Configure Production Webhooks**:
   - Endpoint: `https://us-central1-[your-production-project].cloudfunctions.net/stripeWebhook`
   - Events to listen for:
     - `checkout.session.completed`
     - `customer.subscription.created`
     - `customer.subscription.updated` 
     - `customer.subscription.deleted`
     - `invoice.paid`

### 7. Apple App Store Compliance 🛡️ **CRITICAL FOR APPROVAL**

**Action Required**: Implement Apple App Store requirements:

1. **Add Privacy Disclaimers**:
   ```typescript
   // Add to relevant screens
   const healthDisclaimer = "This app does not provide medical advice. Nutritional information is for educational purposes only. Consult healthcare professionals for medical decisions.";
   
   const lidarDisclaimer = "LiDAR measurements are estimates and may not be 100% accurate. Use as general guidance only.";
   ```

2. **Add Terms of Service and Privacy Policy URLs** in app settings

3. **Implement Data Deletion** in user settings:
   ```typescript
   // Complete implementation in privacy settings
   const deleteAllUserData = async () => {
     // Delete from Firestore
     // Delete from Firebase Storage  
     // Delete from local storage
     // Revoke authentication
   };
   ```

### 8. Testing and Validation 🧪 **CRITICAL**

**Action Required**: Complete testing checklist:

**MVP Feature Testing:**
- [ ] Food scanning with camera works on physical devices
- [ ] LiDAR scanning works on LiDAR-enabled iOS devices  
- [ ] Water tracking syncs correctly between offline/online
- [ ] User profile creation and editing functions properly
- [ ] Stripe payments work end-to-end with test cards
- [ ] Authentication flows work (signup, login, logout)
- [ ] App works offline and syncs when back online

**Security Testing:**
- [ ] Firebase security rules prevent unauthorized access
- [ ] User data is properly isolated between accounts
- [ ] Payment information is handled securely
- [ ] App handles auth token expiration gracefully

**Platform Testing:**
- [ ] iOS builds and runs on physical devices
- [ ] Android builds and runs on physical devices  
- [ ] LiDAR features gracefully fallback on non-LiDAR devices
- [ ] App meets performance requirements (< 3s startup time)

### 9. Production Monitoring Setup 📊 **MEDIUM PRIORITY**

**Action Required**: Configure production monitoring:

1. **Firebase Analytics Events**:
   ```typescript
   // Add to relevant places
   analytics.logEvent('lidar_scan_complete', {
     scanDurationMs: duration,
     scanQuality: quality,
     deviceModel: Device.modelName
   });
   
   analytics.logEvent('water_intake_recorded', {
     amount: amount,
     goalPercentage: percentage
   });
   ```

2. **Stripe Webhook Monitoring**:
   - Set up alerts for payment failures
   - Monitor subscription churn rates
   - Track revenue metrics

3. **Firebase Crashlytics**:
   - Enable crash reporting
   - Set up error alerts

### 10. Launch Strategy 🚀 **MEDIUM PRIORITY**

**Action Required**: Plan phased rollout:

1. **TestFlight Beta** (1-2 weeks):
   - Invite 20-50 beta testers
   - Focus on LiDAR functionality testing
   - Gather feedback on payment flows

2. **Soft Launch** (Week 1):
   - Release to 10% of users
   - Monitor crash rates and payment issues
   - Ensure Firebase infrastructure scales

3. **Full Launch** (Week 2+):
   - Release to 100% of users
   - Monitor KPIs and user feedback
   - Prepare hotfix pipeline for issues

---

## ⚡ **IMMEDIATE NEXT STEPS**

1. **Deploy secure production rules**: `npm run deploy:prod:rules`
2. **Create production Firebase project** and environment
3. **Configure EAS build profiles** for production
4. **Set up Stripe live mode** with production webhooks
5. **Complete Apple App Store submission requirements**

**Estimated Timeline**: 1-2 weeks for production deployment readiness.

**Risk Assessment**: 
- **HIGH RISK**: Current development security rules expose all data
- **MEDIUM RISK**: No production environment configured
- **LOW RISK**: Core features are implemented and functional