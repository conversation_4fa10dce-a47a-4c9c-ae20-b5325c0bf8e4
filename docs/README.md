# ChooseHealthy Documentation

This directory contains current and active project documentation for the ChooseHealthy React Native application.

**📋 Last Updated**: January 2025  
**🗂️ Archived Docs**: See [_archive](_archive/) for completed implementations and obsolete docs

## 🚀 **Quick Navigation**

### **🎯 Production & Deployment**
- **[Production Deployment](2do-prod.md)** - Current production deployment tasks
- **[Firebase Multi-Environment Setup](firebase-setup-guide.md)** - Dev/Staging/Prod Firebase setup
- **[Environment Management](environment-management.md)** - Environment configuration guide

### **💻 Development**
- **[Quick Start Guide](quick-start-guide.md)** - Get up and running quickly
- **[Frontend Handbook](frontend-handbook.md)** - Frontend development guide
- **[Backend Handbook](backend-handbook.md)** - Backend development guide
- **[Testing Guidelines](testing-guidelines.md)** - Testing best practices

### **🏗️ Architecture & APIs**
- **[Architecture Overview](architecture.md)** - High-level system design
- **[API Reference](api-reference.md)** - Complete API documentation
- **[API Troubleshooting](api-troubleshooting.md)** - API debugging guide

### **✨ Core Features**
- **[LiDAR Scanning](LiDAR-Scanning.md)** - 3D food scanning implementation
- **[LiDAR Portion Estimation](lidar-portion-estimation.md)** - Portion size calculation
- **[Mindfulness Features](MindfulnessDocumentation.md)** - Mindfulness and wellness features
- **[Sleep Tracking](SleepTrackingDocumentation.md)** - Sleep monitoring features
- **[Image Analysis](image-analyzer.md)** - Food image recognition

### **🔧 Infrastructure**
- **[Database Setup](00-db.md)** - Database configuration and setup
- **[API Configuration](00-api.md)** - API setup and configuration
- **[Infrastructure](00-infra.md)** - Infrastructure and deployment
- **[Feature Flags](00-ft.md)** - Feature flag implementation
- **[PostHog Analytics](00-posthog.md)** - Analytics configuration

### **💳 Integrations**
- **[Stripe Integration](stripe.md)** - Payment processing setup
- **[Authentication](auth.md)** - User authentication setup

### **🔍 Operations & Monitoring**
- **[Logging](logging.md)** - Application logging
- **[Firebase Functions Logging](firebase-functions-logging.md)** - Cloud Functions logging
- **[Hidden Features](hidden-features.md)** - Developer features and debug tools

### **📱 Platform-Specific**
- **[iOS Setup](02-ios.md)** - iOS platform configuration
- **[Frontend Specifics](frontend.md)** - Frontend implementation details
- **[Backend Specifics](backend.md)** - Backend implementation details
- **[UI/UX Guidelines](ui-ux.md)** - Design system and user experience
- **[User Guide](user-guide.md)** - End-user documentation

## 📊 **Current Status**

### **✅ Completed & Archived**
- Firebase migration (Supabase → Firebase)
- LiDAR architecture refactoring (2780 → 1170 lines)
- Profile component refactoring (1600+ → 1112 lines)
- Expo Router compatibility fixes
- Critical security issues resolution
- Multi-environment Firebase rules

### **🔄 Active Development**
- Production environment setup
- Multi-environment deployment pipeline
- Stripe production configuration
- App Store compliance preparations

### **📋 Remaining Tasks**
See [2do-prod.md](2do-prod.md) for current production deployment priorities.

## 🗂️ **Archive Structure**

- **[_archive/completed-implementations/](_archive/completed-implementations/)** - Successfully completed features
- **[_archive/obsolete-docs/](_archive/obsolete-docs/)** - Outdated documentation
- **[_archive/refactoring-completed/](_archive/refactoring-completed/)** - Historical refactoring docs

## 🔍 **Finding Documentation**

**For New Developers**: Start with [Quick Start Guide](quick-start-guide.md)  
**For Production Deployment**: See [Production Tasks](2do-prod.md)  
**For Feature Implementation**: Check feature-specific docs above  
**For Troubleshooting**: See [API Troubleshooting](api-troubleshooting.md)