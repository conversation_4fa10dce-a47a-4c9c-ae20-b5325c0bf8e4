# LiDAR Scanner Module Refactoring

This document describes the architectural refactoring of the LiDAR scanning functionality in the Health App, transforming a monolithic implementation into a modular, maintainable architecture.

## Before Refactoring

Before the refactoring, the LiDAR scanning was implemented as a single large component:

- `components/ScannerWithLiDAR.tsx` (2,780 lines)

Issues with the original implementation:
- Difficult to maintain due to its size
- Mixed concerns (UI, scanning logic, state management)
- Limited reusability of subcomponents
- Challenging to test individual parts
- Duplicate code in some areas

## After Refactoring

The refactoring followed these principles:
- Single Responsibility Principle: Each component has a clear, focused purpose
- Separation of concerns: UI, state management, and utility functions are separate
- Reusability: Components can be reused in different contexts
- Testability: Components are easier to test in isolation

### New Architecture

```
components/
  ├── ScannerWithLiDAR.tsx          # Forward component (bridge to modular implementation)
  └── LiDAR/                        # Modular LiDAR components
      ├── ScannerWithLiDAR.tsx      # Main scanner component (refactored)
      ├── VolumeVisualizer.tsx      # Volume visualization component
      ├── EnhancedCalibrationModal.tsx  # Calibration UI
      ├── ARGuidanceOverlay.tsx     # AR scanning guidance
      └── index.ts                  # Export all components

hooks/
  └── LiDAR/
      └── useLiDARScanning.ts       # Custom hook for LiDAR functionality

utils/
  ├── foodDensityMap.ts             # Food density data and utilities
  └── LiDAR/
      ├── types.ts                  # TypeScript types/interfaces
      └── helpers.ts                # Helper functions
```

### Key Improvements

#### 1. Custom Hook: useLiDARScanning

Extracted all LiDAR scanning logic into a dedicated hook that:
- Manages device capability detection
- Handles scanning state and progress
- Provides methods for starting/stopping scanning
- Manages calibration

```typescript
// Custom hook usage
const {
  deviceSupportsLiDAR,
  isScanningActive,
  processingStage,
  scanProgress,
  scanQuality,
  startLiDARScanning,
  calculateLiDARVolume,
  // ... more properties and methods
} = useLiDARScanning();
```

#### 2. Component Breakdown

The monolithic component was broken down into specialized components:

1. **ScannerWithLiDAR**: Main component that orchestrates the scanning process
2. **VolumeVisualizer**: Visualizes food volume with interactive 3D representation
3. **EnhancedCalibrationModal**: Handles scanner calibration with reference objects
4. **ARGuidanceOverlay**: Provides real-time guidance during scanning

#### 3. Type Safety

Added comprehensive TypeScript interfaces and types:

```typescript
// Example of type definitions
export interface ScannerWithLiDARProps {
  onScanComplete?: (result: ScanResult) => void;
  onClose?: () => void;
  referenceObjects?: Array<ReferenceObject>;
}

export type ProcessingStage = 
  'idle' | 
  'scanning' | 
  'analyzing' | 
  'classifying' | 
  'estimating' | 
  'complete' | 
  'calibrating';

// ... more types
```

#### 4. Performance Optimizations

Added performance optimizations:
- Memoization with `useMemo` and `useCallback`
- Proper dependency arrays to prevent unnecessary re-renders
- Reused components instead of recreating them

#### 5. Accessibility Improvements

Enhanced accessibility:
- Added proper ARIA attributes
- Improved focus management
- Added screen reader support
- Enhanced UI feedback

## API Integration and Advanced Features

After the initial refactoring, we've added improved API integration and advanced features:

### 1. Dedicated API Service

Added a dedicated LiDAR scanning service that seamlessly connects to other app services:

```
services/
  ├── lidarScanningService.ts       # API integration for LiDAR scanning
  ├── foodSegmentationService.ts    # Advanced food segmentation
  ├── classificationService.ts      # Food classification
  ├── openaiNutritionService.ts     # Nutrition analysis
  └── volumeEstimator.ts            # Volume estimation
```

The `lidarScanningService.ts` provides:
- Unified scanning process
- Integration with food classification
- Cloud storage for scan results
- User-specific calibration data synchronization
- Nutrition analysis integration

### 2. Cloud Synchronization

Enhanced the LiDAR scanning with cloud data synchronization:

- **Calibration Data Sync**: User calibration data is now stored in the cloud and synced across devices
- **Scan History**: All food scans are saved to the cloud when the user is logged in
- **Image Storage**: Food images are uploaded to cloud storage for reference and retrieval

### 3. Advanced Food Segmentation

Added a new `foodSegmentationService.ts` for improved multi-item detection:

- **Food Segmentation**: Uses advanced computer vision to identify multiple food items in a single image
- **Volume Distribution**: Intelligently distributes the total volume among detected food items
- **Mask-based Segmentation**: Uses pixel-level masks for precise food item boundaries
- **Depth Integration**: Combines LiDAR depth data with segmentation for better accuracy

### 4. Authentication Integration

Integrated with the authentication system:

- **User-specific Data**: All scans are associated with the current user
- **Calibration Profiles**: Each user can have their own calibration profiles
- **Seamless Experience**: The system works offline but syncs when the user is logged in

## Migration Strategy

The refactoring used a bridge pattern to maintain backward compatibility:

1. Original `components/ScannerWithLiDAR.tsx` now forwards to modular implementation
2. Existing code using the scanner component doesn't need to change
3. New code can use either the forward component or import specific modular components

## Testing

Added unit tests for the refactored components:
- Tests for `VolumeVisualizer` component
- Mocked dependencies for isolated testing
- Accessibility testing

## Benefits

The refactoring provides several benefits:
- **Reduced Complexity**: Each file is now under 500 lines
- **Enhanced Maintainability**: Easier to understand, modify, and debug
- **Better Performance**: Optimized rendering and state management
- **Improved Accessibility**: Better support for assistive technologies
- **Better Testing**: Components can be tested in isolation
- **Easier Onboarding**: New developers can understand individual components more easily
- **Cloud Support**: Seamless cloud synchronization and multi-device support
- **Advanced Features**: Better food detection and multi-item support 