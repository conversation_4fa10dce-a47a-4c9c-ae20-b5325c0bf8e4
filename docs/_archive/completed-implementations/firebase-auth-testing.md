# Firebase Auth Testing Guide

## Development Auth Options

### Option 1: Using Auth Emulator (Default)

When `EXPO_PUBLIC_USE_EMULATORS=true` in `.env.local`:

```bash
# Start emulators
cd functions
firebase emulators:start

# Access emulator UI
open http://localhost:4000
```

Benefits:
- No real user accounts needed
- Test all auth flows locally
- Create test users instantly
- No Firebase quotas used

### Option 2: Using Real Firebase Auth in Development

To test with production Firebase Auth locally:

1. **Temporarily disable emulators** in `.env.local`:
```bash
# Comment out or set to false
# EXPO_PUBLIC_USE_EMULATORS=false
EXPO_PUBLIC_ENVIRONMENT=development
```

2. **Add localhost to authorized domains** in Firebase Console:
   - Go to Firebase Console → Authentication → Settings
   - Add `localhost` to Authorized domains
   - Add `localhost:8081` (Expo web default)
   - Add `localhost:19006` (if using different port)

3. **Configure OAuth providers** for localhost:
   - For Google Sign-In: Add localhost redirect URIs
   - For Apple Sign-In: Configure for web

## Auth Testing Scenarios

### Web Development (Expo Web)

1. **With Emulator** (Recommended):
```javascript
// Everything works automatically
await signInWithEmailAndPassword(auth, '<EMAIL>', 'password');
```

2. **With Production Auth**:
```javascript
// Same code, but connects to real Firebase
await signInWithEmailAndPassword(auth, '<EMAIL>', 'realpassword');
```

### Mobile Development

- iOS Simulator: Works with both emulator and production
- Android Emulator: May need to use `********` instead of `localhost`

## Environment Setup Examples

### Development with Emulators
```env
# .env.local
EXPO_PUBLIC_USE_EMULATORS=true
EXPO_PUBLIC_ENVIRONMENT=development
```

### Development with Production Auth
```env
# .env.local
EXPO_PUBLIC_USE_EMULATORS=false
EXPO_PUBLIC_ENVIRONMENT=development
```

### Production Build
```env
# .env.production
EXPO_PUBLIC_USE_EMULATORS=false
EXPO_PUBLIC_ENVIRONMENT=production
```

## Common Issues

### CORS Errors
If you get CORS errors with production auth:
1. Check Firebase authorized domains
2. Ensure your local development URL is added
3. Try using `127.0.0.1` instead of `localhost`

### Emulator Not Connecting
1. Check emulators are running: `firebase emulators:start`
2. Verify ports aren't blocked: 9099 (Auth), 8080 (Firestore)
3. Check browser console for connection errors

### OAuth Providers Not Working
1. Configure redirect URIs for localhost
2. Some providers (Apple) may require HTTPS even locally
3. Consider using ngrok for testing OAuth locally

## Testing Auth Flows

### Email/Password
```javascript
// Works identically in emulator and production
await createUserWithEmailAndPassword(auth, email, password);
await signInWithEmailAndPassword(auth, email, password);
```

### OAuth (Google/Apple)
```javascript
// May need additional configuration for localhost
const provider = new GoogleAuthProvider();
await signInWithPopup(auth, provider);
```

### Anonymous Auth
```javascript
// Works everywhere
await signInAnonymously(auth);
```

## Best Practices

1. **Use emulators for development** - Faster, no quotas, isolated data
2. **Test production auth before deploying** - Ensure all providers work
3. **Keep separate Firebase projects** - Dev/staging/production
4. **Use environment variables** - Easy switching between modes
5. **Document OAuth setup** - Localhost redirect URIs for team

## Switching Between Modes

Quick toggle in your terminal:
```bash
# Use emulators
export EXPO_PUBLIC_USE_EMULATORS=true
npm run web

# Use production auth
export EXPO_PUBLIC_USE_EMULATORS=false
npm run web
```