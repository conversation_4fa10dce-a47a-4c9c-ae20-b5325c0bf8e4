# Firebase Android Setup for Expo

Since this is an Expo managed project, we need to configure Firebase without modifying the Android directory directly.

## Step 1: Add Android App to Firebase

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your ChooseHealthy project
3. Click the Android icon to add an Android app
4. Register with these details:
   - **Android package name**: Use what's in `android/app/build.gradle`: `com.kingly.choosehealthy`
   - **App nickname**: ChooseHealthy Android
   - **SHA-1**: Get it using the command below

## Step 2: Get SHA-1 Certificate

For Expo projects, get the SHA-1 like this:

```bash
# For development builds
cd android
./gradlew signingReport

# For EAS builds (production)
eas credentials:configure
```

## Step 3: Download and Place Configuration File

1. Download `google-services.json` from Firebase Console
2. Place it in the **project root directory**:
   ```
   /Users/<USER>/digital/aiforge/choosehealthy/google-services.json
   ```

## Step 4: Configure app.json

Update `app.json` to include the Firebase configuration:

```json
{
  "expo": {
    // ... existing config
    "android": {
      "package": "com.kingly.choosehealthy",  // Match what's in build.gradle
      "googleServicesFile": "./google-services.json",
      // ... rest of android config
    },
    "plugins": [
      // ... existing plugins
      [
        "expo-build-properties",
        {
          "android": {
            "googleServicesFile": "./google-services.json"
          }
        }
      ]
    ]
  }
}
```

## Step 5: Install Required Plugin

```bash
npx expo install expo-build-properties
```

## Step 6: Prebuild and Test

```bash
# Clear any existing native directories
rm -rf android ios

# Prebuild with the new configuration
npx expo prebuild

# Run on Android
npm run android
```

## Important Notes

1. **Package Name Mismatch**: The current `app.json` has `com.kingly.choosehealthy` but the Android build uses `com.kingly.choosehealthy`. This needs to be fixed.

2. **Don't Modify Android Directory**: Any changes to the `android/` directory will be lost when running `expo prebuild`.

3. **Configuration Files**: Keep `google-services.json` in the project root, not in the android directory.

## Fixing the Package Name Issue

The package names need to match. Choose one:

Option 1: Use `com.kingly.choosehealthy` everywhere
- Update `app.json` android package to `com.kingly.choosehealthy`
- Register with Firebase using `com.kingly.choosehealthy`

Option 2: Use `com.kingly.choosehealthy` everywhere
- This would require modifying the native Android code
- Not recommended for Expo managed projects

## Troubleshooting

### "No matching client found" error
- Ensure package name in Firebase matches what's in your Android build
- Re-download `google-services.json` after any Firebase changes

### Build errors after prebuild
```bash
cd android
./gradlew clean
cd ..
npx expo prebuild --clear
npm run android
```