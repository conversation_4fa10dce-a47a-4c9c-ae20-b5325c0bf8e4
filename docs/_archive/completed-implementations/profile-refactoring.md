# Profile Component Refactoring Plan

## Overview

The current `app/(tabs)/profile.tsx` file is over 1600 lines long, making it difficult to maintain and understand. This document outlines a plan to refactor this file into multiple smaller, focused components following best practices for React and TypeScript development.

## Goals

1. Improve code organization and readability
2. Separate concerns into distinct components
3. Reduce file size for better maintainability
4. Fix TypeScript type errors
5. Enhance performance through better component organization
6. Keep the same functionality while improving the structure

## Directory Structure

```
app/
└── (tabs)/
    ├── profile.tsx (main container, significantly reduced)
    └── profile/
        ├── components/
        │   ├── ProfileHeader.tsx
        │   ├── StatsCard.tsx
        │   ├── PersonalInfoSection.tsx
        │   ├── SubscriptionSection.tsx
        │   ├── HealthGoalsSection.tsx
        │   ├── NotificationSettings.tsx
        │   ├── MenuSection.tsx
        │   └── ArrowIcon.tsx
        ├── hooks/
        │   ├── useProfileData.ts
        │   ├── useSubscriptionData.ts
        │   └── useNotificationPreferences.ts
        └── utils/
            ├── profileCache.ts
            ├── connectivityHelpers.ts
            └── types.ts
```

## Component Breakdown

### Main Components

1. **ProfileScreen (profile.tsx)**
   - Main container component
   - Handles routing and screen-level state
   - Composes the refactored components

2. **ProfileHeader.tsx**
   - User avatar and header information
   - Animation logic for scrolling effects

3. **StatsCard.tsx**
   - User statistics display and editing
   - Height, weight, and calorie goal information

4. **PersonalInfoSection.tsx**
   - Personal information display and editing
   - Email, name, and other user details

5. **SubscriptionSection.tsx**
   - Subscription management
   - Checkout and portal session handling

6. **HealthGoalsSection.tsx**
   - Health goals display
   - Progress indicators and metrics

7. **NotificationSettings.tsx**
   - Notification preferences
   - Toggle handlers for notification types

8. **MenuSection.tsx**
   - Menu items for additional screens
   - Navigation handlers

### Hooks

1. **useProfileData.ts**
   - Profile data fetching and caching logic
   - Connectivity checks and retry mechanisms
   - Error handling for profile operations

2. **useSubscriptionData.ts**
   - Subscription data management
   - Fetching and caching subscription information

3. **useNotificationPreferences.ts**
   - Notification preference handling
   - System permission requests

### Utility Files

1. **profileCache.ts**
   - Profile caching helpers
   - AsyncStorage operations

2. **connectivityHelpers.ts**
   - Firebase connectivity functions
   - Timeout and retry handling

3. **types.ts**
   - TypeScript interfaces for profile data
   - Shared type definitions

## TypeScript Improvements

The refactoring will address the following TypeScript errors:

1. Fix unknown types in Promise.race results
2. Add proper type assertions and interfaces
3. Use more specific types instead of `any`
4. Add proper error handling with typed catch blocks

## Implementation Steps

1. Extract utility functions first
2. Create the hooks using the extracted utilities
3. Create each component, starting with the smaller ones
4. Update the main profile.tsx to use the new components
5. Test each component thoroughly
6. Fix any TypeScript errors that arise
7. Apply consistent styling across components

## Benefits

1. **Maintainability**: Smaller files are easier to understand and maintain
2. **Performance**: More focused re-renders and better memoization opportunities
3. **Reusability**: Components can be reused in other parts of the application
4. **Collaboration**: Multiple developers can work on different components
5. **Testing**: Smaller components are easier to test in isolation
6. **Type Safety**: Better TypeScript type coverage

## Migration Strategy

The refactoring will be done incrementally to minimize disruption:

1. Create the new files without removing existing code
2. Make one component work at a time
3. Replace sections in the main file gradually
4. Run tests after each replacement
5. Remove old code only after new implementation is verified

## Future Considerations

1. Consider using React Context for sharing state between components
2. Implement performance optimizations using React.memo and useMemo
3. Add comprehensive unit tests for each component
4. Further improve error handling and fallback states 