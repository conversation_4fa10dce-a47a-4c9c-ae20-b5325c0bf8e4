# Production Testing Guide

This guide explains how to test the ChooseHealthy app in production mode without deploying to app stores.

## Summary of Current Status

### ✅ Completed
- Firebase Functions API proxy implemented
- Most services migrated to use Firebase Functions
- Environment template updated for security
- Authentication setup with email/password and Google OAuth

### 🚧 In Progress (Just Completed)
- Migration of remaining 3 services to Firebase Functions:
  - `goalRecommendationService.ts` ✅
  - `vision/foodRecognition.ts` ✅
  - `imageGenerationService.ts` ✅
- Removed direct API key usage from client-side code

### ❌ Not Started
- Firebase Functions deployment to production
- Google OAuth credentials configuration
- Production environment testing

## Quick Start for Production Testing

### 1. Set Up Firebase Production Environment

```bash
# Run the setup script
cd infra/scripts
./setup-production-test.sh
```

### 2. Configure Firebase Functions

```bash
# Set up API keys in Firebase Functions
firebase functions:config:set \
  openai.api_key="your-actual-openai-key" \
  google.vision_api_key="your-google-vision-key" \
  nutritionix.app_id="your-nutritionix-app-id" \
  nutritionix.api_key="your-nutritionix-api-key" \
  stripe.secret_key="your-stripe-secret-key"

# Deploy functions to production
cd functions
pnpm run build
firebase deploy --only functions
```

### 3. Create Production Environment File

Create `.env.production` with your Firebase production config:

```env
EXPO_PUBLIC_FIREBASE_API_KEY=your-prod-firebase-api-key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your-prod-auth-domain
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-prod-project-id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your-prod-storage-bucket
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-prod-sender-id
EXPO_PUBLIC_FIREBASE_APP_ID=your-prod-app-id
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=your-prod-stripe-key
EXPO_PUBLIC_ENVIRONMENT=production
```

### 4. Configure Authentication

#### Email/Password Auth
1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your production project
3. Navigate to Authentication > Sign-in method
4. Enable Email/Password authentication

#### Google OAuth
1. In Firebase Console, enable Google authentication
2. Download the configuration files:
   - For iOS: `GoogleService-Info.plist`
   - For Android: `google-services.json`
3. Add OAuth redirect URIs in Google Cloud Console

### 5. Run in Production Mode

```bash
# iOS Simulator
EXPO_PUBLIC_ENVIRONMENT=production pnpm run ios

# Android Emulator  
EXPO_PUBLIC_ENVIRONMENT=production pnpm run android

# Web
EXPO_PUBLIC_ENVIRONMENT=production pnpm run web
```

## Testing Checklist

### Authentication
- [ ] Email/password sign up
- [ ] Email/password sign in
- [ ] Google OAuth sign in
- [ ] Sign out
- [ ] Session persistence

### API Functions
- [ ] Food recognition from images
- [ ] Recipe generation
- [ ] Nutrition recommendations
- [ ] Image generation
- [ ] Stripe payment flow

### Core Features
- [ ] Water tracking with offline sync
- [ ] Food scanning and analysis
- [ ] Profile management
- [ ] LiDAR scanning (iOS only)

## Monitoring

### Firebase Console
1. Functions logs: Monitor API calls and errors
2. Authentication: Track user sign-ins
3. Firestore: View data operations
4. Storage: Monitor file uploads

### Error Handling
- Check browser console for web
- Use React Native Debugger for mobile
- Monitor Firebase Functions logs

## Common Issues

### API Keys Not Working
```bash
# Verify functions config
firebase functions:config:get

# Redeploy if needed
firebase deploy --only functions
```

### Authentication Failed
- Verify OAuth redirect URIs
- Check Firebase Auth settings
- Ensure proper environment variables

### Functions Not Responding
- Check deployment status
- Verify function names match
- Monitor function logs

## Production vs Development

| Feature | Development | Production |
|---------|------------|------------|
| API Keys | Local env vars | Firebase Functions |
| Firebase | Dev project | Prod project |
| Auth | Test users | Real users |
| Data | Sample data | Real data |
| Stripe | Test mode | Live mode |

## Security Checklist

- [x] No API keys in client code
- [x] Firebase security rules configured
- [ ] OAuth redirect URIs restricted
- [ ] Stripe webhook endpoints secured
- [ ] Rate limiting on API functions

## Next Steps

1. Deploy Functions to production
2. Configure OAuth providers
3. Test all authentication flows
4. Verify API proxy functions
5. Test payment integration
6. Monitor for errors