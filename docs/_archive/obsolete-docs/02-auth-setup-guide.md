# Authentication Setup Guide

This guide will help you set up authentication for both web and simulator development.

## Quick Start

### 1. Development Environment Setup

First, ensure you have the correct environment variables set up:

```bash
# Copy the template
cp env-template.txt .env

# Edit .env with your Firebase development config
EXPO_PUBLIC_FIREBASE_API_KEY=your-dev-api-key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your-dev-auth-domain
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-dev-project-id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your-dev-storage-bucket
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-dev-sender-id
EXPO_PUBLIC_FIREBASE_APP_ID=your-dev-app-id
EXPO_PUBLIC_ENVIRONMENT=development
```

### 2. Firebase Console Setup

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your development project
3. Navigate to **Authentication > Sign-in method**
4. Enable the following providers:
   - **Email/Password** - Required for basic auth
   - **Google** - For OAuth testing

### 3. Google OAuth Configuration

#### For Web Development

1. In Firebase Console > Authentication > Sign-in method > Google
2. Click on Google provider and enable it
3. Add authorized domains:
   - `localhost`
   - `localhost:8081` (Expo web default)
   - `localhost:19006` (Expo web alternate)

#### For iOS Simulator

1. Download `GoogleService-Info.plist` from Firebase Console
2. Add it to your iOS project:
   ```bash
   cp ~/Downloads/GoogleService-Info.plist ios/ChooseHealthy/
   ```
3. Ensure the file is included in Xcode project

#### For Android Emulator

1. Download `google-services.json` from Firebase Console
2. Add it to your Android project:
   ```bash
   cp ~/Downloads/google-services.json android/app/
   ```

### 4. Platform-Specific Setup

#### Web Setup

No additional setup needed. The AuthContext handles web authentication automatically.

#### iOS Simulator Setup

1. Clean and rebuild:
   ```bash
   cd ios
   pod cache clean --all
   pod install
   cd ..
   pnpm run ios
   ```

2. If Google Sign-In fails, check:
   - Bundle ID matches Firebase config
   - URL schemes are configured in Info.plist

#### Android Emulator Setup

1. Ensure Google Play Services are installed in emulator
2. Clean and rebuild:
   ```bash
   cd android
   ./gradlew clean
   cd ..
   pnpm run android
   ```

## Testing Authentication

### Using the Test Component

I've created a test component you can use to verify auth works:

```typescript
import { AuthTestView } from '@/components/AuthTestView';

// Add to any screen temporarily
export default function TestScreen() {
  return <AuthTestView />;
}
```

### Manual Testing Steps

1. **Email/Password Auth**:
   - Sign up with: `<EMAIL>` / `Test123!`
   - Sign in with same credentials
   - Verify user appears in Firebase Console

2. **Google OAuth**:
   - Click "Sign in with Google"
   - Complete OAuth flow
   - Verify profile created in Firestore

## Common Issues and Solutions

### Issue 1: Google Sign-In Not Working on Web

**Solution**: Check browser console for errors. Common fixes:
- Clear browser cache/cookies
- Ensure popup blockers are disabled
- Check Firebase Console for correct domain whitelisting

### Issue 2: Google Sign-In Not Working on Simulator

**Solution**: The app uses a test account for simulator development:
```
Email: <EMAIL>
Password: GoogleTest123!
```

This simulates Google auth without requiring actual OAuth setup.

### Issue 3: Auth State Not Persisting

**Solution**: Check persistence settings:
- Web: Uses `inMemoryPersistence` to avoid browser issues
- Mobile: Auth state should persist automatically

### Issue 4: Network Errors

**Solution**: The AuthContext includes network monitoring. Check:
- Internet connection
- Firebase project configuration
- API keys are correct

## Development Workflow

### 1. Start Development Server

```bash
# Start Metro bundler
pnpm run dev

# In another terminal, run your platform
pnpm run web    # For web development
pnpm run ios    # For iOS simulator
pnpm run android # For Android emulator
```

### 2. Monitor Firebase Console

Keep Firebase Console open to monitor:
- Authentication > Users (new sign-ups)
- Firestore > profiles (user data)
- Functions > Logs (if using production)

### 3. Test Auth Flow

1. Start with email/password to verify basic setup
2. Test Google OAuth
3. Verify profile creation in Firestore
4. Test sign out and persistence

## Production Testing

When ready to test production auth:

1. Create `.env.production` with production Firebase config
2. Run with production environment:
   ```bash
   EXPO_PUBLIC_ENVIRONMENT=production pnpm run web
   ```
3. Ensure production Firebase has auth providers enabled

## Debugging Tips

### Enable Debug Logging

Add to your app initialization:
```typescript
if (__DEV__) {
  console.log('Firebase Config:', firebaseConfig);
  console.log('Auth State:', auth.currentUser);
}
```

### Check Network Requests

Use browser dev tools or React Native Debugger to monitor:
- Firebase Auth API calls
- OAuth redirects
- Firestore queries

### Common Debug Commands

```bash
# Clear Metro cache
pnpm run dev:clean

# Reset iOS simulator
xcrun simctl erase all

# Clear Android emulator data
adb shell pm clear com.yourpackage

# Check Firebase CLI version
firebase --version

# View Firebase project list
firebase projects:list
```

## Next Steps

1. Test auth in development
2. Set up production Firebase
3. Configure production OAuth
4. Test production auth
5. Add additional providers (Apple, Facebook) as needed