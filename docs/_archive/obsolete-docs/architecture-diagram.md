# HealthApp Architecture Diagram

## System Architecture

```mermaid
graph TD
    subgraph "Frontend"
        App[App.tsx]
        Routes[Expo Router]
        UI[UI Components]
        Hooks[Custom Hooks]
        Context[Context Providers]
        ServicesF[Frontend Services]
    end

    subgraph "Backend"
        subgraph "Firebase"
            Auth[Authentication]
            Firestore[Firestore Database]
            Storage[Cloud Storage]
            Functions[Cloud Functions]
        end
        
        subgraph "External APIs"
            OpenAI[OpenAI API]
            Stripe[Stripe Payments]
            Wearables[Wearable APIs]
        end
    end

    App --> Routes
    App --> Context
    Routes --> UI
    UI --> Hooks
    Hooks --> ServicesF
    Context --> ServicesF
    
    ServicesF --> Auth
    ServicesF --> Firestore
    ServicesF --> Storage
    ServicesF --> Functions
    
    Functions --> OpenAI
    Functions --> Stripe
    Functions --> Wearables
```

## Data Flow

```mermaid
sequenceDiagram
    participant U as User
    participant A as App
    participant C as Context
    participant F as Firebase
    participant CF as Cloud Functions
    participant E as External Services

    U->>A: Interact with UI
    A->>C: Update State
    C->>F: Read/Write Data
    F-->>C: Return Data/Confirmation
    C-->>A: Update UI
    A-->>U: Display Results
    
    Note over A,F: For intensive operations
    A->>CF: Call Cloud Function
    CF->>E: Process with External Service
    E-->>CF: Return Results
    CF-->>A: Return Processed Data
    A-->>U: Display Results
```

## Component Architecture

```mermaid
graph TD
    subgraph "App Structure"
        Entry[App.tsx]
        Router[Expo Router]
        
        subgraph "Routes"
            Auth[Authentication]
            Onboarding[Onboarding]
            Home[Home Tab]
            Profile[Profile Tab]
            Admin[Admin Tab]
            Recipe[Recipe Screens]
            Scan[Scan Screens]
        end
        
        subgraph "Components"
            UI[UI Components]
            HomeC[Home Components]
            ProfileC[Profile Components]
            RecipeC[Recipe Components]
            ScanC[Scan Components]
            Shared[Shared Components]
        end
    end
    
    Entry --> Router
    Router --> Auth
    Router --> Onboarding
    Router --> Home
    Router --> Profile
    Router --> Admin
    Router --> Recipe
    Router --> Scan
    
    Home --> HomeC
    Profile --> ProfileC
    Recipe --> RecipeC
    Scan --> ScanC
    
    HomeC --> Shared
    ProfileC --> Shared
    RecipeC --> Shared
    ScanC --> Shared
    
    Auth --> UI
    Onboarding --> UI
    HomeC --> UI
    ProfileC --> UI
    RecipeC --> UI
    ScanC --> UI
```

## Database Schema

```mermaid
erDiagram
    USERS ||--o{ HEALTH_RECORDS : has
    USERS ||--o{ MEAL_LOGS : creates
    USERS ||--o{ RECIPES : saves
    USERS ||--o{ WATER_INTAKE : records
    USERS ||--o{ SLEEP_RECORDS : tracks
    USERS ||--o{ EXERCISE_LOGS : logs
    
    USERS {
        string id PK
        string email
        string displayName
        string photoURL
        date dateOfBirth
        string gender
        number height
        number weight
        json goals
        json preferences
        string subscriptionTier
        timestamp created_at
        timestamp updated_at
    }
    
    HEALTH_RECORDS {
        string id PK
        string userId FK
        string recordType
        timestamp date
        json metrics
        string notes
        string source
        timestamp created_at
        timestamp updated_at
    }
    
    MEAL_LOGS {
        string id PK
        string userId FK
        timestamp date
        string mealType
        json foodItems
        number totalCalories
        json nutrition
        string image
        timestamp created_at
    }
    
    RECIPES {
        string id PK
        string userId FK
        string title
        string description
        json ingredients
        json instructions
        string image
        json nutrition
        boolean isPublic
        timestamp created_at
        timestamp updated_at
    }
    
    WATER_INTAKE {
        string id PK
        string userId FK
        timestamp date
        number amount
        string unit
        timestamp created_at
    }
    
    SLEEP_RECORDS {
        string id PK
        string userId FK
        timestamp startTime
        timestamp endTime
        number duration
        string quality
        json stages
        string source
        timestamp created_at
    }
    
    EXERCISE_LOGS {
        string id PK
        string userId FK
        timestamp date
        string activityType
        number duration
        number calories
        json metrics
        string source
        timestamp created_at
    }
```

## Authentication Flow

```mermaid
stateDiagram-v2
    [*] --> Unauthenticated
    
    Unauthenticated --> Login: Open Login Screen
    Unauthenticated --> Signup: Open Signup Screen
    
    Login --> AuthenticatingEmail: Email/Password
    Login --> AuthenticatingSocial: Google/Apple
    
    Signup --> CreatingAccount: Submit Form
    
    AuthenticatingEmail --> Authenticated: Success
    AuthenticatingEmail --> Unauthenticated: Failure
    
    AuthenticatingSocial --> Authenticated: Success
    AuthenticatingSocial --> Unauthenticated: Failure
    
    CreatingAccount --> ProfileSetup: Success
    CreatingAccount --> Unauthenticated: Failure
    
    ProfileSetup --> Onboarding: Complete Profile
    
    Onboarding --> Authenticated: Complete Onboarding
    
    Authenticated --> HomeScreen: Navigate to Home
    HomeScreen --> [*]
    
    Authenticated --> Unauthenticated: Sign Out
```

## Deployment Architecture

```mermaid
graph LR
    subgraph "Development"
        LocalDev[Local Development]
        Testing[Testing]
    end
    
    subgraph "Deployment"
        EAS[Expo Application Services]
        CD[Continuous Deployment]
    end
    
    subgraph "Production"
        AppStores[App Stores]
        Web[Web App]
        Backend[Firebase Backend]
    end
    
    LocalDev --> Testing
    Testing --> CD
    CD --> EAS
    EAS --> AppStores
    EAS --> Web
    AppStores --> Users((Users))
    Web --> Users
    Users --> Backend
```

## Offline Sync Process

```mermaid
sequenceDiagram
    participant User
    participant App
    participant LocalDB
    participant SyncQueue
    participant Firebase
    
    User->>App: Perform Action
    
    alt Online
        App->>Firebase: Write Data
        Firebase-->>App: Confirm Success
        App->>LocalDB: Update Local Cache
        App-->>User: Show Confirmation
    else Offline
        App->>LocalDB: Write Data
        App->>SyncQueue: Add Sync Task
        App-->>User: Show Confirmation with Offline Indicator
    end
    
    Note over App,Firebase: When connection is restored
    
    SyncQueue->>App: Trigger Sync
    App->>Firebase: Sync Pending Changes
    Firebase-->>App: Sync Confirmation
    App->>SyncQueue: Clear Synced Tasks
    App->>LocalDB: Update Sync Status
    App-->>User: Update Sync Status Indicator
``` 