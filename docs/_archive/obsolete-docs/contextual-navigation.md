# Contextual Navigation System

This documentation describes the implementation and usage of the contextual navigation system, which provides intelligent navigation suggestions based on user behavior patterns.

## Overview

The contextual navigation system is designed to analyze user interactions with the app and provide intelligent navigation suggestions tailored to each user's habits and preferences. This system enhances the user experience by reducing navigation friction and making commonly accessed features more accessible.

## Core Components

### 1. Tracking Utility (`contextualNavigation.ts`)

The `contextualNavigation` utility is responsible for tracking and analyzing user navigation patterns. It records:

- Screen visits with timestamps
- Time spent on each screen
- Navigation patterns based on day of week and time of day
- Custom user shortcuts

### 2. Screen Tracking Hook (`useScreenTracking.ts`)

The `useScreenTracking` hook is attached to screens to automatically track entry and exit, enabling the collection of navigation data.

```typescript
// Example usage in a screen component
import useScreenTracking from '@/hooks/useScreenTracking';

export default function SomeScreen() {
  // Enable tracking for this screen
  useScreenTracking();
  
  // Rest of the component...
}
```

### 3. Shortcut Suggestions Component (`ShortcutSuggestions.tsx`)

The `ShortcutSuggestions` component displays a horizontal list of shortcuts based on the user's navigation patterns. It's typically placed on the home screen or dashboard.

```typescript
// Example usage
import ShortcutSuggestions from '@/components/ShortcutSuggestions';

// In your component JSX
<ShortcutSuggestions maxSuggestions={3} />
```

### 4. Contextual Menu (`ContextualMenu.tsx`)

The `ContextualMenu` is a modal menu that displays navigation suggestions categorized by relevance. It's triggered by the `ContextualMenuButton`.

### 5. Contextual Menu Button (`ContextualMenuButton.tsx`)

The `ContextualMenuButton` is a floating action button that displays the contextual menu when pressed.

```typescript
// Example usage
import ContextualMenuButton from '@/components/ContextualMenuButton';

// In your component JSX
<ContextualMenuButton />
```

## Implementation Guide

### Step 1: Initialize Core Components

Ensure the `contextualNavigation` utility is properly initialized at the app startup. This is typically done in your main App component:

```typescript
// In App.tsx or similar
import { contextualNavigation } from '@/utils/contextualNavigation';

// Initialize navigation tracking
useEffect(() => {
  contextualNavigation.loadData();
  
  return () => {
    // Save any pending data
    contextualNavigation.saveData();
  };
}, []);
```

### Step 2: Add Screen Tracking to Routes

Use the `useScreenTracking` hook in your screen components to track navigation:

```typescript
// In any screen component
import useScreenTracking from '@/hooks/useScreenTracking';

export default function SomeScreen() {
  useScreenTracking();
  
  // Rest of the component...
}
```

For consistent tracking across all routes, you can also add this to your root layout component.

### Step 3: Add Shortcut Suggestions to Home Screen

Add the `ShortcutSuggestions` component to your home screen or dashboard:

```typescript
// In your home screen/dashboard
import ShortcutSuggestions from '@/components/ShortcutSuggestions';

// In your JSX
<View style={styles.suggestionsContainer}>
  <ShortcutSuggestions maxSuggestions={5} />
</View>
```

### Step 4: Add Contextual Menu Button

Add the `ContextualMenuButton` to your app layout or specific screens:

```typescript
// In your layout component
import ContextualMenuButton from '@/components/ContextualMenuButton';

// In your JSX, typically at the bottom of the screen
<View style={styles.floatingButtonContainer}>
  <ContextualMenuButton />
</View>
```

## Customization Options

### Shortcut Suggestions

```typescript
<ShortcutSuggestions 
  maxSuggestions={5}                   // Number of suggestions to display
  onShortcutPress={(shortcut) => {}}   // Optional callback when a shortcut is pressed
/>
```

### Contextual Menu Button

```typescript
<ContextualMenuButton 
  size={50}                            // Size of the button in pixels
  style={customStyles}                 // Custom styles for the button
  iconName="compass"                   // Ionicon name for the button icon
  menuTitle="Navigation Suggestions"   // Title displayed in the menu
  maxItems={8}                         // Maximum number of items to display
/>
```

## Data Structure

The contextual navigation system stores data in the following format:

### Screen Visits

```typescript
interface ScreenVisit {
  screen: string;            // Screen path
  timestamp: number;         // Visit timestamp
  duration?: number;         // Time spent on screen
  dayOfWeek: number;         // 0-6 (Sunday-Saturday)
  timeOfDay: number;         // Hour of day (0-23)
}
```

### Time Patterns

```typescript
interface TimePattern {
  screen: string;            // Screen path
  dayOfWeek: number;         // 0-6 (Sunday-Saturday)
  timeOfDay: number;         // Hour of day (0-23)
  count: number;             // Number of visits at this time
}
```

### Shortcut Suggestions

```typescript
interface ShortcutSuggestion {
  screen: string;            // Screen to navigate to
  title: string;             // Display name
  confidence: number;        // 0-1 confidence score
  icon: string;              // Icon name
}
```

## Performance Considerations

- The system is designed to have minimal impact on app performance
- Data processing happens in the background
- Local storage is used for persistence
- Old data is automatically pruned to maintain reasonable storage size
- Pattern analysis is optimized for quick calculations

## Privacy Considerations

- All data is stored locally on the device
- No navigation data is sent to remote servers
- User can clear their navigation history at any time
- System automatically limits history to a reasonable timeframe

## Testing and Debugging

For testing and debugging purposes, you can use the `contextual-navigation-demo.tsx` screen included in the app.

You can also use the following API methods for debugging:

```typescript
// Clear navigation history
await contextualNavigation.clearHistory();

// Get current shortcut suggestions
const shortcuts = contextualNavigation.getShortcutSuggestions(5);

// Get recent screens
const recentScreens = contextualNavigation.getRecentScreens(5);
```

## Demo Screen

The app includes a demo screen (`app/contextual-navigation-demo.tsx`) that showcases all the contextual navigation components and provides controls for testing the system.

To access the demo:

1. Navigate to `/contextual-navigation-demo` in the app
2. Explore the different components and controls
3. Use the demo controls to test saving shortcuts and clearing history

## Extending the System

The contextual navigation system can be extended in several ways:

1. **Enhanced Pattern Recognition**: Add more sophisticated algorithms for recognizing usage patterns
2. **AI-Powered Suggestions**: Integrate machine learning for more intelligent suggestions
3. **Custom Categorization**: Add support for categorizing shortcuts by function or user goals
4. **Cross-Device Sync**: Enable syncing of shortcuts and preferences across devices
5. **Adaptive UI**: Use navigation data to adapt the entire UI based on usage patterns 