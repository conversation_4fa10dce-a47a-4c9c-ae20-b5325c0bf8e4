# Development Logging Quick Start

This guide helps you set up logging for Firebase Functions development.

## 1. Enable Local Log Files

Add to your `.env.local`:
```bash
LOCAL_LOG_FILES=true
FIREBASE_LOCAL_LOG_DIR=./functions/logs
```

## 2. Start the Emulators

```bash
cd functions
pnpm run serve
```

## 3. View Logs

### Option A: Terminal Output
Logs appear in the terminal where emulators are running.

### Option B: Emulator UI
Visit `http://localhost:4000/logs`

### Option C: Local Log Files
```bash
# Follow logs in real-time
node functions/scripts/view-logs.js --follow

# View errors only
node functions/scripts/view-logs.js --level=error

# Filter by function
node functions/scripts/view-logs.js --function=analyzeFood
```

## 4. Log File Location

Logs are saved in: `./functions/logs/YYYY-MM-DD-functions.log`

## 5. Debugging Tips

### View Raw JSON Logs
```bash
cat ./functions/logs/$(date +%Y-%m-%d)-functions.log | jq '.'
```

### Find Errors
```bash
cat ./functions/logs/$(date +%Y-%m-%d)-functions.log | jq 'select(.level == "error")'
```

### Search by User
```bash
grep -h "user123" ./functions/logs/*.log | jq '.'
```

## 6. Production Logs

In production, logs are automatically sent to Google Cloud Logging. Access them via:
- Firebase Console > Functions > Logs
- Google Cloud Console > Logging

## Troubleshooting

### No logs appearing?
1. Check `.env.local` has `LOCAL_LOG_FILES=true`
2. Restart the emulators
3. Verify `./functions/logs/` directory exists

### Permission errors?
```bash
chmod +x functions/scripts/view-logs.js
```