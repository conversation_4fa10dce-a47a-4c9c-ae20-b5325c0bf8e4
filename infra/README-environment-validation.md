# Environment Validation Tools

## Overview

T3 stack-inspired environment validation tools for bulletproof environment management in Expo/React Native projects.

## Features

✅ **TypeScript Type Safety** - Full type inference for environment variables  
✅ **Runtime Validation** - Catches missing/invalid variables at startup  
✅ **Environment-Specific Rules** - Different validation for dev/staging/prod  
✅ **CLI Tools** - Validate environments during development and CI/CD  
✅ **Clear Error Messages** - Detailed feedback on validation failures  

## Quick Start

### Validate All Environments
```bash
npm run validate:env
```

### Validate Specific Environment
```bash
npm run validate:env:dev      # Validate development
npm run validate:env:staging  # Validate staging  
npm run validate:env:prod     # Validate production
```

## File Structure

```
infra/
├── environment-validation.ts     # Core validation schemas
├── scripts/
│   ├── validate-env.ts          # Single environment validator
│   └── validate-all-envs.ts     # All environments validator
└── README-environment-validation.md
```

## Validation Rules

### All Environments
- Firebase configuration (API keys, project IDs, etc.)
- Auth configuration (OAuth client IDs)
- Stripe configuration (publishable keys, price IDs)  
- PostHog configuration (API keys, host)
- Feature flags (LiDAR, offline mode, debug)

### Development Specific
- ✅ Test accounts allowed
- ✅ Debug mode allowed
- ✅ Emulator controls allowed

### Production Specific  
- ❌ Test accounts must be empty
- ❌ Debug mode must be disabled
- ❌ Emulators must be disabled

## Usage in Code

```typescript
import { validateEnvironment } from '@/infra/environment-validation';

// Validate current environment
const env = validateEnvironment(process.env);

// Now you have full TypeScript support
console.log(env.EXPO_PUBLIC_FIREBASE_PROJECT_ID); // string
console.log(env.EXPO_PUBLIC_ENABLE_DEBUG);        // boolean
```

## Integration with Build Process

Add to your CI/CD pipeline:

```yaml
# GitHub Actions example
- name: Validate Environment
  run: npm run validate:env:prod
```

## Error Examples

```bash
❌ Environment validation failed for: production
Missing or invalid environment variables:
  - EXPO_PUBLIC_FIREBASE_API_KEY: Required
  - EXPO_PUBLIC_ENABLE_DEBUG: Expected 'false', received 'true'
  - EXPO_PUBLIC_TEST_ACCOUNT_IDS: Expected '', received 'test123'
```

## Benefits

1. **Catch Issues Early** - Validation runs at startup/build time
2. **Type Safety** - Full TypeScript inference and IntelliSense  
3. **Environment Consistency** - Same validation across all environments
4. **Clear Feedback** - Detailed error messages with actionable fixes
5. **Security** - Enforces production security rules

## Integration with Existing Tools

Works seamlessly with:
- ✅ Expo environment management
- ✅ Firebase emulator configuration
- ✅ Existing package.json scripts
- ✅ CI/CD pipelines
- ✅ Development workflows