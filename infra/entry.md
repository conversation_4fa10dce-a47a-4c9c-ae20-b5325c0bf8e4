# 🤖 InfraBot Autonomous Infrastructure System

> **System Prompt for Autonomous Infrastructure Completion**  
> Drop this infra folder into any project for instant infrastructure magic ✨

## 🎯 **Core Mission**

Build the ultimate self-organizing, self-healing infrastructure bot that:

1. **Instantly assesses** any codebase and presents infrastructure options
2. **Bootstraps itself** with zero-config defaults using smart naming conventions  
3. **Guides proper dev workflow** with API key management and local DX optimization
4. **Scales progressively** from dev → staging → prod with autonomous environment creation
5. **Heals continuously** with self-monitoring and auto-recovery capabilities

## 🏗️ **Design Principles**

### **1. Zero-Config Brilliance**
```yaml
principle: "Convention over Configuration"
implementation: 
  - Auto-detect project type from codebase signatures
  - Generate intelligent defaults from naming conventions
  - Only ask users for what can't be inferred
  - Store decisions for future automation
```

### **2. Progressive Infrastructure**
```yaml
principle: "Start Simple, Scale Smart"
workflow:
  day_1: "Setup dev environment + local DX optimization"
  day_N: "One-click staging/prod creation when ready"
  continuous: "Self-healing and optimization"
```

### **3. Configuration-Driven Everything**
```yaml
principle: "Pluggable Architecture"
abstraction:
  db_provider: [firebase, supabase, postgres, mongodb]
  auth_provider: [firebase_auth, supabase_auth, auth0, clerk]
  hosting: [vercel, netlify, firebase_hosting, aws_amplify]
  infrastructure: [pulumi, terraform, cdk]
```

### **4. Self-Healing by Design**
```yaml
principle: "Idempotent Operations"
behavior:
  - Re-running scripts fixes drift without breakage
  - Automatic detection and correction of configuration issues
  - Progressive healing with smart retry and backoff
  - State persistence across healing sessions
```

## 🔍 **Auto-Discovery Matrix**

### **Project Type Detection**
```javascript
const PROJECT_SIGNATURES = {
  'expo_firebase': {
    files: ['app.json', 'firebase.json', 'package.json'],
    dependencies: ['expo', '@react-native-firebase/*'],
    patterns: ['app/', 'services/', 'components/']
  },
  'nextjs_supabase': {
    files: ['next.config.js', 'supabase/', 'package.json'],
    dependencies: ['next', '@supabase/supabase-js'],
    patterns: ['pages/', 'components/', 'lib/']
  },
  'react_native_bare': {
    files: ['package.json', 'android/', 'ios/'],
    dependencies: ['react-native'],
    patterns: ['src/', 'components/']
  }
};
```

### **Infrastructure Requirements Detection**
```javascript
const INFRASTRUCTURE_NEEDS = {
  authentication: detect_auth_usage(),
  database: detect_db_calls(),
  storage: detect_file_uploads(),
  functions: detect_api_endpoints(),
  payments: detect_stripe_usage(),
  analytics: detect_tracking_calls(),
  push_notifications: detect_notification_usage(),
  realtime: detect_subscription_patterns()
};
```

## 🚀 **Bootstrap Workflow**

### **Phase 1: Assessment & Discovery**
```yaml
steps:
  1. Scan codebase for project signatures
  2. Analyze dependencies and patterns
  3. Detect existing infrastructure (if any)
  4. Present intelligent recommendations
  5. Save configuration decisions
```

### **Phase 2: Convention-Based Setup**
```yaml
naming_convention: "kingly-{project_name}-{environment}"
environments: [dev, staging, prod]
defaults:
  bundle_id: "com.kingly.{project_name}"
  project_structure: "three_layer_architecture"
  auth_providers: ["email", "google", "apple"]
  security: "defense_in_depth"
```

### **Phase 3: Guided Environment Setup**
```yaml
dev_environment:
  - Firebase emulators for local development
  - Development API keys (with usage monitoring)
  - Permissive security rules for rapid iteration
  - Local secret management

staging_environment:
  - Production-like security rules
  - Separate API keys for testing
  - Performance monitoring enabled
  - Automated testing integration

prod_environment:
  - Strict security rules and monitoring
  - Production API keys with rotation
  - Full observability and alerting
  - Backup and disaster recovery
```

## 🔐 **Secret Management Strategy**

### **Three-Tier Secret Storage**
```yaml
local_development:
  storage: ".env.local (gitignored)"
  validation: "Required variables checker"
  rotation: "Monthly developer reminder"

staging_testing:
  storage: "Firebase Remote Config"
  validation: "Automated secret validation"
  rotation: "Quarterly automated rotation"

production:
  storage: "GCP Secret Manager via Pulumi"
  validation: "Real-time secret health monitoring"
  rotation: "Automated with zero-downtime rollover"
```

### **Secret Onboarding Workflow**
```yaml
guided_setup:
  1. Detect required integrations (OpenAI, Stripe, etc.)
  2. Present setup guides with screenshots
  3. Validate API keys with test calls
  4. Store securely in appropriate tier
  5. Generate environment-specific configs
```

## 🏥 **Self-Healing Architecture**

### **Continuous Health Monitoring**
```yaml
health_checks:
  infrastructure:
    - Firebase project accessibility
    - API service enablement status
    - Firestore index performance
    - Storage bucket configuration
    - Cloud function health
  
  security:
    - Security rules validation
    - API key freshness
    - Access control consistency
    - Vulnerability scanning
  
  performance:
    - Resource utilization
    - Cost optimization opportunities
    - Database query performance
    - CDN effectiveness
```

### **Intelligent Recovery**
```yaml
recovery_strategies:
  api_key_rotation:
    trigger: "30 days before expiration"
    action: "Generate new key → Test → Rotate → Archive old"
  
  security_drift:
    trigger: "Rules modification detected"
    action: "Validate → Restore from template → Alert admin"
  
  performance_degradation:
    trigger: "Response time > 2x baseline"
    action: "Scale resources → Analyze bottlenecks → Optimize"
```

## 🎛️ **Environment Management**

### **Atomic Environment Switching**
```bash
# InfraBot should enhance these existing patterns:
./infra/scripts/env.sh staging           # Switch to staging
./infra/scripts/deploy.sh                # Deploy to current environment
./infra/scripts/heal.sh                  # Self-heal current environment
```

### **Progressive Environment Creation**
```yaml
workflow:
  1. Developer requests staging environment
  2. InfraBot scans dev config for requirements
  3. Creates staging infrastructure using Pulumi
  4. Copies secrets with environment-appropriate values
  5. Validates deployment with smoke tests
  6. Provides switch commands and monitoring links
```

## 🛠️ **Technology Stack Support**

### **Primary Stack (MVP)**
```yaml
expo_firebase:
  frontend: "React Native + Expo + TypeScript"
  backend: "Firebase (Auth + Firestore + Storage + Functions)"
  infrastructure: "Pulumi + GCP"
  monitoring: "PostHog + Firebase Analytics"
  payments: "Stripe (SaaS model)"
  testing: "Jest + Detox + Visual Regression"
```

### **Future Stacks (Roadmap)**
```yaml
nextjs_supabase:
  frontend: "Next.js + TypeScript + Tailwind"
  backend: "Supabase (Auth + Postgres + Storage + Edge Functions)"
  infrastructure: "Pulumi + AWS"
  monitoring: "PostHog + Supabase Analytics"

flutter_firebase:
  frontend: "Flutter + Dart"
  backend: "Firebase Full Stack"
  infrastructure: "Pulumi + GCP"

react_aws:
  frontend: "React + TypeScript"
  backend: "AWS (Cognito + DynamoDB + S3 + Lambda)"
  infrastructure: "Pulumi + AWS"
```

## 🔧 **Pulumi Integration**

### **Stack Management Convention**
```yaml
stack_structure:
  - {project}-dev      # Development stack
  - {project}-staging  # Staging stack  
  - {project}-prod     # Production stack

configuration:
  - Environment-specific configs
  - Secret management via Pulumi
  - Resource tagging for cost allocation
  - Cross-stack references for shared resources
```

### **Resource Naming Convention**
```yaml
pattern: "{project}-{environment}-{resource_type}-{instance}"
examples:
  - "choosehealthy-prod-firestore-main"
  - "fitnesstracker-staging-storage-uploads"
  - "aiforge-dev-function-imageprocessor"
```

## 📋 **Task Breakdown**

### **Immediate Tasks**
- [task-01-codebase-scanner.md](./tasks/task-01-codebase-scanner.md) - Auto-detection engine
- [task-02-convention-engine.md](./tasks/task-02-convention-engine.md) - Naming and defaults
- [task-03-secret-manager.md](./tasks/task-03-secret-manager.md) - Multi-tier secret handling
- [task-04-healing-system.md](./tasks/task-04-healing-system.md) - Self-healing capabilities

### **Progressive Tasks**
- [task-05-environment-orchestrator.md](./tasks/task-05-environment-orchestrator.md) - Multi-env management
- [task-06-pulumi-integration.md](./tasks/task-06-pulumi-integration.md) - IaC automation
- [task-07-monitoring-stack.md](./tasks/task-07-monitoring-stack.md) - Observability
- [task-08-security-automation.md](./tasks/task-08-security-automation.md) - Security hardening

### **Advanced Tasks**
- [task-09-cost-optimization.md](./tasks/task-09-cost-optimization.md) - Resource optimization
- [task-10-stack-templates.md](./tasks/task-10-stack-templates.md) - Multi-stack support

## 📊 **Success Metrics**

### **Developer Experience**
- **Time to first deployment**: < 15 minutes from zero to working dev environment
- **Environment creation**: < 5 minutes for staging/prod after dev setup
- **Issue resolution**: 90% of common issues auto-healed without intervention
- **API key management**: Zero manual key rotation required

### **Reliability**
- **Infrastructure drift**: Auto-detected and corrected within 1 hour
- **Security compliance**: 100% adherence to security templates
- **Environment parity**: Staging/prod environments stay in sync with dev
- **Disaster recovery**: < 4 hour recovery time for critical failures

## 🎯 **Autonomous Agent Instructions**

When a fresh Claude session loads this entry.md:

1. **Read progress.md** to understand current state
2. **Scan the current task** in /tasks/ directory  
3. **Analyze existing codebase** using auto-discovery patterns
4. **Identify gaps** between current state and target architecture
5. **Execute next logical task** with full context
6. **Update progress.md** with accomplishments and next steps
7. **Continue iteratively** until full autonomous infrastructure achieved

## 🔗 **Reference Links**

- [Existing Infrastructure Analysis](../docs/architecture.md)
- [Environment Switching Guide](../docs/backend-handbook.md)
- [Security Implementation](../docs/CRITICAL-SECURITY-ISSUES.md)
- [Current Setup Scripts](./scripts/)
- [Pulumi Configurations](./pulumi/)
- [PostHog Feature Flags](./posthog/)

---

**🎉 The Ultimate Goal**: Drop this `/infra` folder into any project and have a fully autonomous infrastructure system that thinks, learns, and evolves with minimal human intervention.

*Built for the modern app studio managing 10-50 applications with zero DevOps friction.*