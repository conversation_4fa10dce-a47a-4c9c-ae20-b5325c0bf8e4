# Environment Management Guide

## 🎯 Overview

This guide documents the comprehensive environment management strategy for ChooseHealthy, combining Expo-native patterns with Firebase multi-project architecture and granular emulator controls.

## 🏗️ Architecture Summary

### **Three-Tier Environment System**
```
┌─────────────────────────────────────────────────────────────┐
│                    ENVIRONMENT TIERS                       │
├─────────────────────────────────────────────────────────────┤
│ 1. LOCAL DEV     → Emulators + Firebase Config             │
│ 2. STAGING       → Remote Firebase + Test APIs             │  
│ 3. PRODUCTION    → Production Firebase + Live APIs         │
└─────────────────────────────────────────────────────────────┘
```

### **Key Principles**
- ✅ **Expo-Native**: Uses `EXPO_PUBLIC_ENVIRONMENT` instead of NODE_ENV switching
- ✅ **Security First**: API keys managed via Firebase Functions config, never client-side
- ✅ **Granular Control**: Mix real/emulated services per developer preference
- ✅ **Environment Isolation**: Separate Firebase projects per environment

## 📁 File Structure

### **Environment Files**
```
.env                    # Base configuration (Expo default)
.env.local              # Local dev overrides (gitignored)
.env.development        # Development environment settings
.env.staging            # Staging environment settings  
.env.production         # Production environment settings
```

### **Firebase Projects**
```
kingly-choosehealthy-dev      # Development Firebase project
kingly-choosehealthy-staging  # Staging Firebase project
kingly-choosehealthy-prod     # Production Firebase project
```

## 🔐 Security Architecture

### **API Key Management**
```
CLIENT SIDE:
❌ NEVER: EXPO_PUBLIC_OPENAI_API_KEY=real-key
✅ ALWAYS: Use Firebase Functions as secure proxy

SERVER SIDE (Firebase Functions):
✅ Development: firebase functions:config:set openai.key="dev-key"
✅ Staging:     firebase functions:config:set openai.key="staging-key"  
✅ Production:  firebase functions:config:set openai.key="prod-key"
```

### **Secure API Proxy Pattern**
```typescript
// ✅ CLIENT CODE (Secure)
const analyzeFood = httpsCallable(firebaseFunctions, 'analyzeFood');
const result = await analyzeFood({ image: base64Image });

// ✅ FIREBASE FUNCTION (Secure)
const config = functions.config();
const openaiKey = config.openai?.key; // Reads from Firebase config
```

## ⚙️ Local Development Controls

### **Granular Emulator Configuration**
```bash
# .env.local - Mix real vs emulated services
EXPO_PUBLIC_FIREBASE_EMULATOR_AUTH=false      # Use real auth
EXPO_PUBLIC_FIREBASE_EMULATOR_FIRESTORE=true  # Use fake data
EXPO_PUBLIC_FIREBASE_EMULATOR_STORAGE=true    # Use fake storage
EXPO_PUBLIC_FIREBASE_EMULATOR_FUNCTIONS=true  # Use local functions
```

### **Development Workflows**

**Full Emulation Mode:**
```bash
npm start  # EXPO_PUBLIC_USE_EMULATORS=true automatically
```

**Mixed Development:**
```bash
npm run start:dev  # Respects only .env.local settings
```

**Environment Testing:**
```bash
npm run start:staging  # Test staging environment locally
npm run start:prod     # Test production environment locally
```

## 🚀 Deployment & Scripts

### **Infrastructure Scripts**
```bash
# Environment switching (legacy)
./infra/scripts/env.sh dev|staging|prod

# Functions configuration
./infra/scripts/setup-functions-config.sh

# Complete deployment
./infra/scripts/deploy_all.sh
```

### **Package.json Scripts**
```json
{
  "scripts": {
    "start": "EXPO_PUBLIC_ENVIRONMENT=development EXPO_PUBLIC_USE_EMULATORS=true expo start",
    "start:dev": "EXPO_PUBLIC_ENVIRONMENT=development expo start",
    "start:staging": "EXPO_PUBLIC_ENVIRONMENT=staging expo start",
    "start:prod": "EXPO_PUBLIC_ENVIRONMENT=production expo start"
  }
}
```

## 🔧 Configuration Per Environment

### **Development (.env.development)**
```bash
EXPO_PUBLIC_ENVIRONMENT=development
EXPO_PUBLIC_FIREBASE_PROJECT_ID=kingly-choosehealthy-dev
EXPO_PUBLIC_FIREBASE_API_KEY=dev-api-key
# ... dev Firebase config

# Feature flags
EXPO_PUBLIC_ENABLE_DEBUG_MODE=true
EXPO_PUBLIC_ENABLE_TEST_ACCOUNTS=true
```

### **Staging (.env.staging)**
```bash
EXPO_PUBLIC_ENVIRONMENT=staging
EXPO_PUBLIC_FIREBASE_PROJECT_ID=kingly-choosehealthy-staging
EXPO_PUBLIC_FIREBASE_API_KEY=staging-api-key
# ... staging Firebase config

# Feature flags
EXPO_PUBLIC_ENABLE_DEBUG_MODE=false
EXPO_PUBLIC_ENABLE_TEST_ACCOUNTS=false
```

### **Production (.env.production)**
```bash
EXPO_PUBLIC_ENVIRONMENT=production
EXPO_PUBLIC_FIREBASE_PROJECT_ID=kingly-choosehealthy-prod
EXPO_PUBLIC_FIREBASE_API_KEY=prod-api-key
# ... production Firebase config

# Feature flags
EXPO_PUBLIC_ENABLE_DEBUG_MODE=false
EXPO_PUBLIC_ENABLE_TEST_ACCOUNTS=false
```

## 🎛️ Firebase Functions Configuration

### **Setting API Keys Per Environment**
```bash
# Switch to environment
firebase use dev|staging|prod

# Set secure API keys (examples)
firebase functions:config:set \
  openai.key="environment-specific-key" \
  google.vision_key="environment-vision-key" \
  nutritionix.app_id="environment-nutritionix-id" \
  nutritionix.api_key="environment-nutritionix-key" \
  stripe.secret_key="environment-stripe-key"

# Deploy functions with new config
firebase deploy --only functions
```

### **Reading Config in Functions**
```typescript
// functions/src/shared/config.ts
function getConfig(): Config {
  const config = functions.config();
  
  return {
    openai: {
      key: config.openai?.key || process.env.OPENAI_API_KEY || '',
    },
    google: {
      visionKey: config.google?.vision_key || process.env.GOOGLE_VISION_API_KEY || '',
    },
    // ... automatic environment detection
  };
}
```

## 📱 Platform Configuration Management

### **iOS Firebase Config**
```javascript
// app.config.js - Dynamic platform config
module.exports = ({ config }) => {
  const environment = process.env.EXPO_PUBLIC_ENVIRONMENT || 'development';
  
  return {
    ...config,
    ios: {
      ...config.ios,
      googleServicesFile: `./config/firebase/GoogleService-Info-${environment}.plist`,
    },
    android: {
      ...config.android,
      googleServicesFile: `./config/firebase/google-services-${environment}.json`,
    },
  };
};
```

## 🧪 Testing & Validation

### **Environment Switching Test**
```bash
# Test all environments work
npm run start:dev     # Should load dev Firebase config
npm run start:staging # Should load staging Firebase config
npm run start:prod    # Should load production Firebase config
```

### **Security Validation**
```bash
# Verify no client-side API keys
grep -r "EXPO_PUBLIC.*API_KEY" .env*
# Should return no results after cleanup

# Verify Functions config
firebase functions:config:get
# Should show secure API keys per environment
```

## 🚨 Security Best Practices

### **❌ NEVER DO:**
- Expose API keys in `EXPO_PUBLIC_*` variables
- Commit `.env.local` to git
- Use production API keys in development
- Make direct API calls from client code

### **✅ ALWAYS DO:**
- Use Firebase Functions as API proxy
- Store API keys in Firebase Functions config
- Use environment-specific Firebase projects
- Keep `.env.local` gitignored
- Validate environment before deployment

## 🔄 Migration History

### **Auth Refactor (Completed)**
- Modular authentication architecture implemented
- Environment-driven configuration added
- Test account system integrated
- TypeScript types comprehensively defined

### **Environment Security Cleanup (Completed)**
- Removed client-side API keys from `.env.local`
- Validated Firebase Functions config per environment
- Documented secure API proxy patterns
- Enhanced granular emulator controls

## 🎯 Future Enhancements

### **Planned Improvements**
- [ ] Automated environment health checks
- [ ] Secret rotation automation
- [ ] Enhanced deployment validation
- [ ] Cross-environment configuration sync
- [ ] Performance monitoring per environment

### **Infrastructure Integration**
- EAS Build profiles for mobile deployment
- Pulumi stack management per environment
- PostHog feature flag synchronization
- Stripe product configuration per environment

## 📚 Related Documentation

- `./scripts/setup-functions-config.sh` - Functions configuration helper
- `./scripts/env.sh` - Environment switching script (legacy)
- `./firebase/README.md` - Firebase deployment guide
- `./posthog/feature_flags/` - Feature flag management
- `../docs/00-infra.md` - Infrastructure overview