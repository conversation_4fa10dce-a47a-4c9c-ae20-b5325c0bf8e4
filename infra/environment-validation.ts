/**
 * Environment Validation Tools - T3 Stack Inspired
 * 
 * Provides bulletproof environment variable validation for Expo/React Native projects
 * Inspired by T3 stack patterns, adapted for Expo environment management
 */

const { z } = require('zod');

// Environment validation schema - mirrors the .env file structure
const EnvironmentSchema = z.object({
  // Core Environment
  EXPO_PUBLIC_ENVIRONMENT: z.enum(['development', 'staging', 'production']),

  // Firebase Configuration (required for all environments)
  EXPO_PUBLIC_FIREBASE_API_KEY: z.string().min(1, 'Firebase API key is required'),
  EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN: z.string().min(1, 'Firebase auth domain is required'),
  EXPO_PUBLIC_FIREBASE_PROJECT_ID: z.string().min(1, 'Firebase project ID is required'),
  EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET: z.string().min(1, 'Firebase storage bucket is required'),
  EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: z.string().min(1, 'Firebase messaging sender ID is required'),
  EXPO_PUBLIC_FIREBASE_APP_ID: z.string().min(1, 'Firebase app ID is required'),
  EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID: z.string().optional(),

  // Auth Configuration (required for all environments)
  EXPO_PUBLIC_FIREBASE_WEB_CLIENT_ID: z.string().min(1, 'Firebase web client ID is required'),
  EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID: z.string().min(1, 'Google web client ID is required'),
  EXPO_PUBLIC_APPLE_CLIENT_ID: z.string().min(1, 'Apple client ID is required'),

  // Test Accounts (optional, development only)
  EXPO_PUBLIC_GOOGLE_TEST_EMAIL: z.string().email().optional().or(z.literal('')),
  EXPO_PUBLIC_GOOGLE_TEST_PASSWORD: z.string().optional().or(z.literal('')),
  EXPO_PUBLIC_APPLE_TEST_EMAIL: z.string().email().optional().or(z.literal('')),
  EXPO_PUBLIC_APPLE_TEST_PASSWORD: z.string().optional().or(z.literal('')),
  EXPO_PUBLIC_TEST_ACCOUNT_IDS: z.string().optional().or(z.literal('')),
  EXPO_PUBLIC_FORCE_TEST_CLEANUP: z.enum(['true', 'false']).transform(val => val === 'true'),

  // Stripe Configuration (required for all environments)
  EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY: z.string().min(1, 'Stripe publishable key is required'),
  EXPO_PUBLIC_STRIPE_PRICE_ID_MONTHLY: z.string().min(1, 'Stripe monthly price ID is required'),
  EXPO_PUBLIC_STRIPE_PRICE_ID_YEARLY: z.string().min(1, 'Stripe yearly price ID is required'),

  // PostHog Configuration (required for all environments)
  EXPO_PUBLIC_POSTHOG_API_KEY: z.string().min(1, 'PostHog API key is required'),
  EXPO_PUBLIC_POSTHOG_HOST: z.string().url('PostHog host must be a valid URL'),

  // Feature Flags (required for all environments)
  EXPO_PUBLIC_ENABLE_LIDAR: z.enum(['true', 'false']).transform(val => val === 'true'),
  EXPO_PUBLIC_ENABLE_OFFLINE_MODE: z.enum(['true', 'false']).transform(val => val === 'true'),
  EXPO_PUBLIC_ENABLE_DEBUG: z.enum(['true', 'false']).transform(val => val === 'true'),
  EXPO_PUBLIC_DEBUG_MODE: z.enum(['true', 'false']).transform(val => val === 'true'),

  // Emulator Configuration (optional, controlled by .env.local)
  EXPO_PUBLIC_USE_EMULATORS: z.enum(['true', 'false']).transform(val => val === 'true').optional(),
  EXPO_PUBLIC_FIREBASE_EMULATOR_AUTH: z.enum(['true', 'false']).transform(val => val === 'true').optional(),
  EXPO_PUBLIC_FIREBASE_EMULATOR_FIRESTORE: z.enum(['true', 'false']).transform(val => val === 'true').optional(),
  EXPO_PUBLIC_FIREBASE_EMULATOR_STORAGE: z.enum(['true', 'false']).transform(val => val === 'true').optional(),
  EXPO_PUBLIC_FIREBASE_EMULATOR_FUNCTIONS: z.enum(['true', 'false']).transform(val => val === 'true').optional(),
  EXPO_PUBLIC_FIREBASE_EMULATOR_HOSTING: z.enum(['true', 'false']).transform(val => val === 'true').optional(),
});

// Environment-specific validation rules
const DevelopmentSchema = EnvironmentSchema.extend({
  // Development can have test accounts
  EXPO_PUBLIC_GOOGLE_TEST_EMAIL: z.string().email().optional(),
  EXPO_PUBLIC_GOOGLE_TEST_PASSWORD: z.string().min(8).optional(),
  EXPO_PUBLIC_APPLE_TEST_EMAIL: z.string().email().optional(),
  EXPO_PUBLIC_APPLE_TEST_PASSWORD: z.string().min(8).optional(),
});

const ProductionSchema = EnvironmentSchema.extend({
  // Production must have empty test accounts
  EXPO_PUBLIC_GOOGLE_TEST_EMAIL: z.literal(''),
  EXPO_PUBLIC_GOOGLE_TEST_PASSWORD: z.literal(''),
  EXPO_PUBLIC_APPLE_TEST_EMAIL: z.literal(''),
  EXPO_PUBLIC_APPLE_TEST_PASSWORD: z.literal(''),
  EXPO_PUBLIC_TEST_ACCOUNT_IDS: z.literal(''),
  
  // Production must have debug disabled
  EXPO_PUBLIC_ENABLE_DEBUG: z.literal('false').transform(() => false),
  EXPO_PUBLIC_DEBUG_MODE: z.literal('false').transform(() => false),
  
  // Production must have emulators disabled
  EXPO_PUBLIC_USE_EMULATORS: z.literal('false').transform(() => false).optional(),
});

// Validation function factory
function createEnvironmentValidator(environment: string) {
  switch (environment) {
    case 'development':
      return DevelopmentSchema;
    case 'staging':
      return EnvironmentSchema; // Standard validation
    case 'production':
      return ProductionSchema;
    default:
      throw new Error(`Unknown environment: ${environment}`);
  }
}

// Main validation function
function validateEnvironment(env: Record<string, string | undefined> = process.env) {
  const environment = env.EXPO_PUBLIC_ENVIRONMENT || 'development';
  const schema = createEnvironmentValidator(environment);
  
  try {
    const validatedEnv = schema.parse(env);
    console.log(`✅ Environment validation passed for: ${environment}`);
    return validatedEnv;
  } catch (error: any) {
    if (error instanceof z.ZodError) {
      console.error(`❌ Environment validation failed for: ${environment}`);
      console.error('Missing or invalid environment variables:');
      
      error.errors.forEach((err) => {
        const path = err.path.join('.');
        console.error(`  - ${path}: ${err.message}`);
      });
      
      throw new Error('Environment validation failed. Please check your .env files.');
    }
    throw error;
  }
}

// Format validation errors for display
function formatValidationErrors(error: any): string {
  if (error instanceof z.ZodError) {
    return error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join('\n');
  }
  return error.message || String(error);
}

// Type inference for validated environment
type ValidatedEnvironment = ReturnType<typeof EnvironmentSchema.parse>;

// CommonJS exports
module.exports = {
  validateEnvironment,
  formatValidationErrors,
  createEnvironmentValidator,
  EnvironmentSchema,
  DevelopmentSchema,
  ProductionSchema
};