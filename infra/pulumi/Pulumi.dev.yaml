config:
  gcp:project: kingly-choosehealthy-dev
  gcp:region: us-central1
  environment: dev
  projectName: choosehealthyai
  bundleId: com.kingly.choosehealthy
  
  # Firebase Configuration (Development)
  firebase:webApiKey: ${FIREBASE_DEV_WEB_API_KEY}
  firebase:storageBucket: kingly-choosehealthy-dev.appspot.com
  
  # PostHog Configuration (Development)
  posthog:apiKey: ${POSTHOG_DEV_API_KEY}
  posthog:projectId: ${POSTHOG_DEV_PROJECT_ID}
  
  # Monitoring
  monitoring:alertEmail: <EMAIL>
  
  # Secrets (Development)
  openai:apiKey: ${OPENAI_DEV_API_KEY}