#!/usr/bin/env ts-node

/**
 * Validate All Environments CLI Tool
 * 
 * Validates all environment files at once and provides a summary report
 * Usage: npx ts-node infra/scripts/validate-all-envs.ts
 */

import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';

// ANSI colors for terminal output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(color: keyof typeof colors, message: string) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function validateEnvironment(environment: string): boolean {
  try {
    const scriptPath = path.resolve(__dirname, 'validate-env.ts');
    execSync(`npx ts-node "${scriptPath}" ${environment}`, { 
      stdio: 'inherit',
      cwd: path.resolve(__dirname, '../..')
    });
    return true;
  } catch (error) {
    return false;
  }
}

function checkEnvironmentFiles(): string[] {
  const projectRoot = path.resolve(__dirname, '../..');
  const environments = ['development', 'staging', 'production'];
  const existingEnvs: string[] = [];
  
  environments.forEach(env => {
    const envFile = path.join(projectRoot, `.env.${env}`);
    if (fs.existsSync(envFile)) {
      existingEnvs.push(env);
    } else {
      log('yellow', `⚠️  Missing environment file: .env.${env}`);
    }
  });
  
  return existingEnvs;
}

function main() {
  log('blue', `${colors.bold}🔧 Environment Validation Suite${colors.reset}`);
  log('blue', '=====================================\n');
  
  // Check which environment files exist
  const existingEnvironments = checkEnvironmentFiles();
  
  if (existingEnvironments.length === 0) {
    log('red', '❌ No environment files found!');
    log('blue', 'Expected files: .env.development, .env.staging, .env.production');
    process.exit(1);
  }
  
  // Validate each environment
  const results: Record<string, boolean> = {};
  
  for (const environment of existingEnvironments) {
    const success = validateEnvironment(environment);
    results[environment] = success;
  }
  
  // Generate summary report
  log('blue', `\n${colors.bold}📊 Validation Summary${colors.reset}`);
  log('blue', '===================');
  
  let allPassed = true;
  Object.entries(results).forEach(([env, passed]) => {
    const status = passed ? `${colors.green}✅ PASS${colors.reset}` : `${colors.red}❌ FAIL${colors.reset}`;
    console.log(`  ${env.padEnd(12)}: ${status}`);
    if (!passed) allPassed = false;
  });
  
  console.log('');
  
  if (allPassed) {
    log('green', `🎉 All environment validations passed!`);
    log('blue', 'Your environment configuration is ready for deployment.');
  } else {
    log('red', '🚨 Some environment validations failed!');
    log('yellow', 'Please fix the issues above before deploying.');
  }
  
  process.exit(allPassed ? 0 : 1);
}

if (require.main === module) {
  main();
}