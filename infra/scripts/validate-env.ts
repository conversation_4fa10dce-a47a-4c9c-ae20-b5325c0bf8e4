/**
 * Environment validation CLI tool
 * Validates a specific environment configuration using T3-style validation
 */

const fs = require('fs');
const path = require('path');

// Import validation functions using require
const { validateEnvironment, formatValidationErrors } = require('../environment-validation');

/**
 * Colors for terminal output
 */
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
  dim: '\x1b[2m',
};

/**
 * Format colored output for terminal
 */
function colorize(text: string, color: keyof typeof colors): string {
  return `${colors[color]}${text}${colors.reset}`;
}

/**
 * Print header with project info
 */
function printHeader(environment: string): void {
  console.log(colorize('┌─ 🔧 ENVIRONMENT VALIDATION ────────────────────────────┐', 'cyan'));
  console.log(colorize('│', 'cyan') + ' ChooseHealthy Environment Configuration Validator     ' + colorize('│', 'cyan'));
  console.log(colorize('│', 'cyan') + ` Environment: ${colorize(environment.toUpperCase(), 'bold')}                                  ` + colorize('│', 'cyan'));
  console.log(colorize('└────────────────────────────────────────────────────────┘', 'cyan'));
  console.log('');
}

/**
 * Validate environment configuration for a specific environment
 */
function validateEnvironmentConfig(environment: string): void {
  try {
    printHeader(environment);
    
    console.log(colorize('🔍 Validating environment variables...', 'blue'));
    console.log('');
    
    // Set the environment variable for validation
    process.env.EXPO_PUBLIC_ENVIRONMENT = environment;
    
    // Load the environment file
    const envPath = path.join(process.cwd(), `.env.${environment}`);
    if (fs.existsSync(envPath)) {
      console.log(colorize(`✓ Found environment file: .env.${environment}`, 'green'));
      
      // Load environment variables from file
      const envContent = fs.readFileSync(envPath, 'utf8');
      const envVars = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));
      
      envVars.forEach(line => {
        const [key, ...valueParts] = line.split('=');
        if (key) {
          const value = valueParts.join('='); // Handle values that contain '='
          process.env[key] = value ? value.replace(/"/g, '') : '';
        }
      });
    } else {
      console.log(colorize(`⚠️  Warning: Environment file .env.${environment} not found`, 'yellow'));
    }
    
    // Validate the environment
    const validatedEnv = validateEnvironment();
    
    console.log('');
    console.log(colorize('✅ Environment validation PASSED!', 'green'));
    console.log(colorize('📋 Summary:', 'bold'));
    console.log(`   Environment: ${colorize(validatedEnv.EXPO_PUBLIC_ENVIRONMENT, 'cyan')}`);
    console.log(`   Firebase Project: ${colorize(validatedEnv.EXPO_PUBLIC_FIREBASE_PROJECT_ID, 'cyan')}`);
    console.log(`   Use Emulators: ${colorize(validatedEnv.EXPO_PUBLIC_USE_EMULATORS.toString(), 'cyan')}`);
    console.log(`   Debug Mode: ${colorize(validatedEnv.EXPO_PUBLIC_DEBUG_MODE.toString(), 'cyan')}`);
    console.log('');
    console.log(colorize('🎉 All environment variables are valid and properly configured!', 'green'));
    
  } catch (error) {
    console.log('');
    console.log(colorize('❌ Environment validation FAILED!', 'red'));
    console.log('');
    console.log(colorize('🚨 Errors found:', 'red'));
    
    const errorMessage = formatValidationErrors(error);
    console.log(colorize(errorMessage, 'red'));
    
    console.log('');
    console.log(colorize('🛠️  To fix these issues:', 'yellow'));
    console.log(colorize('   1. Check your .env files for missing or invalid values', 'dim'));
    console.log(colorize('   2. Ensure all required environment variables are set', 'dim'));
    console.log(colorize('   3. Validate environment-specific configurations', 'dim'));
    console.log('');
    
    process.exit(1);
  }
}

// CLI entry point
if (require.main === module) {
  const environment = process.argv[2];
  
  if (!environment) {
    console.error(colorize('❌ Error: Environment argument required', 'red'));
    console.error(colorize('Usage: npm run validate:env:dev | staging | prod', 'dim'));
    process.exit(1);
  }
  
  if (!['development', 'staging', 'production'].includes(environment)) {
    console.error(colorize(`❌ Error: Invalid environment "${environment}"`, 'red'));
    console.error(colorize('Valid environments: development, staging, production', 'dim'));
    process.exit(1);
  }
  
  validateEnvironmentConfig(environment);
}