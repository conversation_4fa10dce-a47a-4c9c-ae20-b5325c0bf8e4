# 🔍 Task 01: Codebase Scanner Engine

**Status**: Ready to Start  
**Priority**: High  
**Estimated Time**: 2-3 hours  
**Dependencies**: None

## 🎯 **Objective**

Build an intelligent codebase scanner that automatically detects project type, tech stack, and infrastructure requirements with zero user input. This forms the foundation for all autonomous infrastructure decisions.

## 📋 **Requirements**

### **Core Functionality**
1. **Project Type Detection**: Automatically identify framework and architecture patterns
2. **Tech Stack Analysis**: Map dependencies to infrastructure requirements
3. **Pattern Recognition**: Detect architectural patterns and conventions
4. **Requirements Mapping**: Translate code patterns to infrastructure needs
5. **Confidence Scoring**: Rate detection accuracy for validation

### **Detection Matrix**

```yaml
project_signatures:
  expo_firebase:
    confidence_indicators:
      - files: ['app.json', 'firebase.json', 'expo-env.d.ts']
      - dependencies: ['expo', '@react-native-firebase/*', 'firebase']
      - directories: ['app/', 'components/', 'services/']
      - patterns: ['expo-router', 'three-layer-architecture']
    
  nextjs_supabase:
    confidence_indicators:
      - files: ['next.config.js', 'supabase/config.toml']
      - dependencies: ['next', '@supabase/supabase-js']
      - directories: ['pages/', 'components/', 'lib/']
      - patterns: ['api-routes', 'middleware']

  react_native_bare:
    confidence_indicators:
      - files: ['metro.config.js', 'android/', 'ios/']
      - dependencies: ['react-native', '@react-native-community/*']
      - directories: ['src/', 'android/', 'ios/']
      - patterns: ['bare-workflow', 'native-modules']
```

### **Infrastructure Requirements Detection**

```yaml
feature_detection:
  authentication:
    patterns: ['firebase.auth()', 'useAuth', 'AuthContext', 'login', 'signin']
    files: ['AuthContext.tsx', 'authService.ts', 'firebase-auth']
    requirements: ['firebase_auth', 'oauth_providers']

  database:
    patterns: ['firestore', 'getDoc', 'collection', 'query']
    files: ['*Service.ts', 'firebase.ts', 'database.ts']
    requirements: ['firestore', 'security_rules', 'indexes']

  storage:
    patterns: ['uploadBytes', 'storage', 'getDownloadURL']
    files: ['storageService.ts', 'imageUpload']
    requirements: ['firebase_storage', 'bucket_policies']

  functions:
    patterns: ['httpsCallable', 'functions().call']
    files: ['functions/', 'api/']
    requirements: ['cloud_functions', 'api_proxy']

  payments:
    patterns: ['stripe', 'payment', 'subscription', 'checkout']
    files: ['stripeService.ts', 'paymentService.ts']
    requirements: ['stripe_integration', 'webhook_endpoints']

  analytics:
    patterns: ['posthog', 'analytics', 'track', 'capture']
    files: ['analyticsService.ts', 'posthogService.ts']
    requirements: ['posthog', 'feature_flags']

  push_notifications:
    patterns: ['expo-notifications', 'messaging()', 'sendNotification']
    files: ['notificationService.ts', 'pushNotifications.ts']
    requirements: ['fcm', 'notification_channels']
```

## 🛠️ **Implementation Plan**

### **Phase 1: File System Scanner**
```javascript
// Core scanning engine
class CodebaseScanner {
  async scanProject(projectPath) {
    return {
      files: await this.scanFiles(projectPath),
      dependencies: await this.analyzeDependencies(projectPath),
      directories: await this.analyzeStructure(projectPath),
      patterns: await this.detectPatterns(projectPath)
    };
  }

  async scanFiles(projectPath) {
    const criticalFiles = [
      'package.json', 'app.json', 'firebase.json', 'next.config.js',
      'metro.config.js', 'expo-env.d.ts', 'tsconfig.json',
      'eas.json', 'app.config.js', 'babel.config.js'
    ];
    
    return await this.checkFileExistence(projectPath, criticalFiles);
  }
}
```

### **Phase 2: Pattern Detection Engine**
```javascript
// Architecture pattern detection
class PatternDetector {
  detectArchitecture(scannedData) {
    const patterns = {
      three_layer: this.detectThreeLayer(scannedData),
      mvc: this.detectMVC(scannedData),
      microservices: this.detectMicroservices(scannedData),
      monolith: this.detectMonolith(scannedData)
    };
    
    return this.calculateConfidence(patterns);
  }

  detectThreeLayer(data) {
    const indicators = [
      data.directories.includes('services/'),
      data.directories.includes('hooks/'),
      data.directories.includes('components/'),
      this.hasPattern(data, 'service-hook-component-separation')
    ];
    
    return {
      detected: indicators.filter(Boolean).length >= 3,
      confidence: indicators.filter(Boolean).length / indicators.length,
      evidence: indicators
    };
  }
}
```

### **Phase 3: Requirements Mapper**
```javascript
// Infrastructure requirements mapping
class RequirementsMapper {
  mapToInfrastructure(detectedFeatures, projectType) {
    const baseRequirements = this.getBaseRequirements(projectType);
    const featureRequirements = this.mapFeaturesToInfra(detectedFeatures);
    
    return {
      ...baseRequirements,
      ...featureRequirements,
      confidence: this.calculateOverallConfidence(detectedFeatures)
    };
  }

  getBaseRequirements(projectType) {
    const requirements = {
      expo_firebase: {
        hosting: 'expo_hosting',
        auth: 'firebase_auth',
        database: 'firestore',
        storage: 'firebase_storage',
        functions: 'firebase_functions',
        infrastructure: 'pulumi_gcp'
      },
      nextjs_supabase: {
        hosting: 'vercel',
        auth: 'supabase_auth',
        database: 'supabase_postgres',
        storage: 'supabase_storage',
        functions: 'supabase_edge_functions',
        infrastructure: 'pulumi_aws'
      }
    };
    
    return requirements[projectType] || {};
  }
}
```

## 🧪 **Testing Strategy**

### **Test Cases**

```yaml
test_project_choosehealthy:
  expected_detection:
    project_type: "expo_firebase"
    confidence: "> 95%"
    features: [auth, database, storage, functions, payments, analytics]
    architecture: "three_layer"
    infrastructure: "pulumi_gcp"
    
  validation_files:
    - app.json (Expo project)
    - firebase.json (Firebase config)
    - services/ (Service layer)
    - hooks/ (Business logic)
    - components/ (UI layer)

test_project_minimal_expo:
  expected_detection:
    project_type: "expo_basic"
    confidence: "> 80%"
    features: [auth]
    architecture: "basic"
    infrastructure: "expo_hosting"

test_project_nextjs:
  expected_detection:
    project_type: "nextjs_supabase"
    confidence: "> 90%"
    features: [auth, database, ssr]
    architecture: "pages_api"
    infrastructure: "pulumi_aws"
```

### **Validation Methodology**
1. **Ground Truth**: Manually verify ChooseHealthy project structure
2. **Accuracy Testing**: Ensure 100% accuracy on known project
3. **Edge Cases**: Test with minimal/incomplete projects
4. **Performance**: Scanner should complete in <10 seconds
5. **Confidence Calibration**: Ensure confidence scores reflect accuracy

## 📁 **File Structure**

```
/infra/agent/lib/scanner/
├── CodebaseScanner.js          # Main scanner engine
├── PatternDetector.js          # Architecture pattern detection
├── RequirementsMapper.js       # Infrastructure requirements mapping
├── signatures/
│   ├── expo-firebase.json      # Project type signatures
│   ├── nextjs-supabase.json    # Alternative stack signatures
│   └── react-native-bare.json  # Bare RN signatures
├── detectors/
│   ├── AuthDetector.js         # Authentication pattern detection
│   ├── DatabaseDetector.js     # Database usage detection
│   ├── StorageDetector.js      # File storage detection
│   └── PaymentDetector.js      # Payment integration detection
└── test/
    ├── scanner.test.js         # Unit tests
    ├── fixtures/               # Test project fixtures
    └── validation.test.js      # Integration tests
```

## 🎯 **Success Criteria**

### **Functional Requirements**
- ✅ **100% accuracy** on ChooseHealthy project detection
- ✅ **>95% confidence** for well-structured projects
- ✅ **<10 second** scan time for typical projects
- ✅ **Graceful degradation** for incomplete projects

### **Technical Requirements**
- ✅ **Modular design** for easy extension to new project types
- ✅ **Configurable signatures** for different tech stacks
- ✅ **Comprehensive logging** for debugging detection issues
- ✅ **JSON output** for easy integration with other systems

### **Integration Requirements**
- ✅ **CLI integration** with infrabot.js
- ✅ **Template selection** based on detection results
- ✅ **Configuration generation** from detected requirements
- ✅ **Progress reporting** during scan operations

## 🔗 **Integration Points**

### **Input Sources**
- Project root directory path
- Optional override parameters
- Configuration preferences from previous runs

### **Output Consumers**
- Convention Engine (Task 02)
- Template Selection System
- Environment Configuration Generator
- Progress Reporting System

### **Dependencies**
- Node.js file system APIs
- JSON/YAML parsing libraries
- Pattern matching utilities
- Confidence calculation algorithms

## 📊 **Deliverables**

1. **CodebaseScanner class** with full detection capabilities
2. **Project signature definitions** for all supported stacks
3. **Requirements mapping engine** for infrastructure translation
4. **Test suite** with 100% coverage on ChooseHealthy project
5. **CLI integration** with infrabot main interface
6. **Documentation** with examples and usage patterns

## 🚀 **Next Steps**

Upon completion of Task 01:
1. **Validate** scanner accuracy on ChooseHealthy project
2. **Generate** infrastructure requirements for current project
3. **Feed results** into Task 02 (Convention Engine)
4. **Test** scanner on additional project types
5. **Update** progress.md with results and lessons learned

---

**🎯 Goal**: By the end of this task, InfraBot should be able to scan any codebase and confidently say "This is an Expo + Firebase project with authentication, database, storage, payments, and analytics requirements. I recommend a three-tier Pulumi + GCP infrastructure setup."