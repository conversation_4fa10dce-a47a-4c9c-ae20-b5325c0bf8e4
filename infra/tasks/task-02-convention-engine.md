# ⚙️ Task 02: Convention Engine

**Status**: Waiting for Task 01  
**Priority**: High  
**Estimated Time**: 2 hours  
**Dependencies**: Task 01 (Codebase Scanner)

## 🎯 **Objective**

Build an intelligent convention engine that generates smart defaults, naming conventions, and zero-config bootstrap templates based on detected project characteristics. This eliminates 90% of configuration decisions while maintaining full customizability.

## 📋 **Requirements**

### **Core Functionality**
1. **Smart Naming**: Generate consistent, predictable resource names
2. **Zero-Config Defaults**: Intelligent defaults requiring zero user input
3. **Decision Persistence**: Save and reuse configuration decisions
4. **Convention Validation**: Ensure naming consistency across environments
5. **Template Generation**: Create environment-specific configurations

### **Convention Patterns**

```yaml
naming_conventions:
  projects:
    pattern: "kingly-{project_name}-{environment}"
    examples:
      - "kingly-choosehealthy-dev"
      - "kingly-fitnesstracker-staging"
      - "kingly-aiforge-prod"

  bundle_identifiers:
    pattern: "com.kingly.{project_name}"
    examples:
      - "com.kingly.choosehealthy"
      - "com.kingly.fitnesstracker"
      - "com.kingly.aiforge"

  resources:
    pattern: "{project}-{environment}-{resource_type}-{instance}"
    examples:
      - "choosehealthy-prod-firestore-main"
      - "fitnesstracker-staging-storage-uploads"
      - "aiforge-dev-function-imageprocessor"

  environments:
    standard: ["dev", "staging", "prod"]
    purpose:
      dev: "Local development with emulators"
      staging: "Pre-production testing environment"
      prod: "Production environment with strict security"
```

## 🧠 **Smart Defaults Engine**

### **Project-Type Based Defaults**

```yaml
expo_firebase_defaults:
  architecture: "three_layer_separation"
  auth_providers: ["email", "google", "apple"]
  database: "firestore_native_mode"
  storage: "firebase_storage_multi_region"
  functions: "api_proxy_pattern"
  monitoring: "posthog_feature_flags"
  payments: "stripe_saas_model"
  security: "defense_in_depth"
  
  environments:
    dev:
      firebase_emulators: true
      security_rules: "permissive_development"
      api_keys: "development_tier"
      monitoring: "debug_mode"
      
    staging:
      firebase_emulators: false
      security_rules: "production_like"
      api_keys: "testing_tier"
      monitoring: "staging_analytics"
      
    prod:
      firebase_emulators: false
      security_rules: "strict_production"
      api_keys: "production_tier"
      monitoring: "full_observability"

nextjs_supabase_defaults:
  architecture: "pages_api_pattern"
  auth_providers: ["email", "github", "google"]
  database: "postgres_with_rls"
  storage: "supabase_storage"
  functions: "supabase_edge_functions"
  monitoring: "supabase_analytics"
  hosting: "vercel_deployment"
  security: "row_level_security"
```

## 🛠️ **Implementation Plan**

### **Phase 1: Convention Generator**
```javascript
// Core convention engine
class ConventionEngine {
  constructor(scanResults) {
    this.projectType = scanResults.projectType;
    this.features = scanResults.features;
    this.projectName = this.extractProjectName(scanResults);
  }

  generateConventions() {
    return {
      naming: this.generateNaming(),
      defaults: this.generateDefaults(),
      environments: this.generateEnvironments(),
      resources: this.generateResourceNames()
    };
  }

  generateNaming() {
    const projectName = this.sanitizeProjectName(this.projectName);
    
    return {
      projectName,
      bundleId: `com.kingly.${projectName}`,
      environments: {
        dev: `kingly-${projectName}-dev`,
        staging: `kingly-${projectName}-staging`,
        prod: `kingly-${projectName}-prod`
      },
      resourcePrefix: `${projectName}`
    };
  }

  generateDefaults() {
    const defaults = this.getProjectTypeDefaults(this.projectType);
    const featureDefaults = this.getFeatureDefaults(this.features);
    
    return this.mergeDefaults(defaults, featureDefaults);
  }
}
```

### **Phase 2: Template Generator**
```javascript
// Configuration template generation
class TemplateGenerator {
  generateEnvironmentConfigs(conventions, requirements) {
    return {
      dev: this.generateDevConfig(conventions, requirements),
      staging: this.generateStagingConfig(conventions, requirements),
      prod: this.generateProdConfig(conventions, requirements)
    };
  }

  generateDevConfig(conventions, requirements) {
    return {
      firebase: {
        projectId: conventions.naming.environments.dev,
        useEmulator: true,
        emulatorConfig: this.getEmulatorConfig(requirements)
      },
      pulumi: {
        stack: `${conventions.naming.projectName}-dev`,
        config: this.getDevPulumiConfig(conventions, requirements)
      },
      env: this.generateDevEnvFile(conventions, requirements)
    };
  }

  generateDevEnvFile(conventions, requirements) {
    const envVars = new Map();
    
    // Base Firebase configuration
    envVars.set('EXPO_PUBLIC_FIREBASE_PROJECT_ID', conventions.naming.environments.dev);
    envVars.set('EXPO_PUBLIC_USE_FIREBASE_EMULATOR', 'true');
    
    // Feature-specific environment variables
    if (requirements.includes('payments')) {
      envVars.set('EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY', 'pk_test_...');
      envVars.set('STRIPE_SECRET_KEY', 'sk_test_...');
    }
    
    if (requirements.includes('analytics')) {
      envVars.set('EXPO_PUBLIC_POSTHOG_API_KEY', 'phc_dev_...');
      envVars.set('EXPO_PUBLIC_POSTHOG_HOST', 'https://app.posthog.com');
    }
    
    return Array.from(envVars.entries())
      .map(([key, value]) => `${key}=${value}`)
      .join('\n');
  }
}
```

### **Phase 3: Decision Persistence**
```javascript
// Configuration persistence and reuse
class DecisionPersistence {
  constructor(projectPath) {
    this.configPath = path.join(projectPath, '.infrabot', 'decisions.json');
  }

  async saveDecisions(conventions, customizations = {}) {
    const decisions = {
      timestamp: new Date().toISOString(),
      projectType: conventions.projectType,
      naming: conventions.naming,
      defaults: conventions.defaults,
      customizations,
      version: '1.0.0'
    };
    
    await fs.ensureFile(this.configPath);
    await fs.writeJson(this.configPath, decisions, { spaces: 2 });
    
    return decisions;
  }

  async loadDecisions() {
    try {
      return await fs.readJson(this.configPath);
    } catch (error) {
      return null; // First time setup
    }
  }

  async updateDecisions(updates) {
    const existing = await this.loadDecisions() || {};
    const updated = {
      ...existing,
      ...updates,
      lastUpdated: new Date().toISOString()
    };
    
    await fs.writeJson(this.configPath, updated, { spaces: 2 });
    return updated;
  }
}
```

## 🧪 **Testing Strategy**

### **Test Cases**

```yaml
test_choosehealthy_conventions:
  input:
    project_name: "choosehealthy"
    project_type: "expo_firebase"
    features: [auth, database, storage, payments, analytics]
  
  expected_output:
    naming:
      bundle_id: "com.kingly.choosehealthy"
      environments:
        dev: "kingly-choosehealthy-dev"
        staging: "kingly-choosehealthy-staging"
        prod: "kingly-choosehealthy-prod"
    
    defaults:
      architecture: "three_layer_separation"
      auth_providers: ["email", "google", "apple"]
      security: "defense_in_depth"
      monitoring: "posthog_feature_flags"

test_convention_persistence:
  scenario: "Save and reload conventions"
  steps:
    1. Generate conventions for project
    2. Save to .infrabot/decisions.json
    3. Reload in new session
    4. Verify identical conventions
    5. Test partial updates

test_template_generation:
  scenario: "Generate environment configurations"
  validation:
    - Dev environment has emulator settings
    - Staging has production-like security
    - Prod has strict security rules
    - All environments have consistent naming
```

## 📁 **File Structure**

```
/infra/agent/lib/conventions/
├── ConventionEngine.js         # Main convention generation
├── TemplateGenerator.js        # Configuration template creation
├── DecisionPersistence.js      # Save/load configuration decisions
├── defaults/
│   ├── expo-firebase.yaml      # Project type defaults
│   ├── nextjs-supabase.yaml    # Alternative stack defaults
│   └── react-native-bare.yaml  # Bare RN defaults
├── templates/
│   ├── env-templates/          # Environment file templates
│   ├── pulumi-templates/       # Pulumi configuration templates
│   └── firebase-templates/     # Firebase configuration templates
└── test/
    ├── conventions.test.js     # Unit tests
    ├── templates.test.js       # Template generation tests
    └── persistence.test.js     # Configuration persistence tests
```

## 🎯 **Success Criteria**

### **Functional Requirements**
- ✅ **Zero-config bootstrap** for 90% of common scenarios
- ✅ **Consistent naming** across all environments and resources
- ✅ **Intelligent defaults** based on detected project characteristics
- ✅ **Decision persistence** for consistent future operations

### **Technical Requirements**
- ✅ **Template generation** for all environment configurations
- ✅ **Customization support** while maintaining conventions
- ✅ **Validation checks** for naming consistency
- ✅ **Migration support** when conventions change

### **Integration Requirements**
- ✅ **Scanner integration** to receive project analysis
- ✅ **Template output** for environment setup scripts
- ✅ **CLI integration** for interactive customization
- ✅ **Progress tracking** for multi-step setup

## 🔗 **Integration Points**

### **Input Sources**
- Codebase Scanner results (Task 01)
- Existing configuration decisions
- User customizations and preferences
- Project-specific overrides

### **Output Consumers**
- Environment setup scripts
- Pulumi configuration generators
- Firebase project creation scripts
- Secret management system (Task 03)

### **Configuration Files Generated**
```yaml
outputs:
  - .infrabot/decisions.json      # Persistent decisions
  - .env.dev                      # Development environment
  - .env.staging                  # Staging environment  
  - .env.prod                     # Production environment
  - infra/pulumi/Pulumi.{env}.yaml # Pulumi configurations
  - firebase.json                 # Firebase project settings
  - .firebaserc                   # Firebase project aliases
```

## 📊 **Deliverables**

1. **ConventionEngine class** with intelligent default generation
2. **TemplateGenerator** for environment-specific configurations
3. **DecisionPersistence** for saving and reusing configuration choices
4. **Default configurations** for all supported project types
5. **Template library** for common configuration patterns
6. **Test suite** validating convention consistency
7. **CLI integration** for interactive customization

## 🚀 **Next Steps**

Upon completion of Task 02:
1. **Generate conventions** for ChooseHealthy project
2. **Create environment templates** for dev/staging/prod
3. **Save configuration decisions** for future use
4. **Validate template accuracy** against existing configurations
5. **Feed conventions** into Task 03 (Secret Manager)
6. **Update progress.md** with generated configurations

---

**🎯 Goal**: By the end of this task, InfraBot should be able to say "Based on your Expo + Firebase project, I recommend the bundle ID 'com.kingly.choosehealthy' and will create projects 'kingly-choosehealthy-dev/staging/prod' with intelligent security and monitoring defaults. Here are your generated environment configurations."