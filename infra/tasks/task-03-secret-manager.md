# 🔐 Task 03: Secret Manager Enhancement

**Status**: Waiting for Task 02  
**Priority**: High  
**Estimated Time**: 3 hours  
**Dependencies**: Task 01 (Scanner), Task 02 (Conventions)

## 🎯 **Objective**

Build a sophisticated secret management system that guides users through API key setup, validates credentials, stores secrets securely across three tiers, and automates rotation scheduling. This ensures secure, maintainable credential management from development to production.

## 📋 **Requirements**

### **Core Functionality**
1. **Guided Onboarding**: Walk users through API key setup with screenshots and validation
2. **Three-Tier Storage**: Local, Firebase Remote Config, and GCP Secret Manager
3. **Credential Validation**: Test API keys with real service calls
4. **Automated Rotation**: Schedule and execute key rotation with zero downtime
5. **Environment Isolation**: Complete separation of credentials across environments

### **Three-Tier Secret Architecture**

```yaml
tier_1_local_development:
  storage: ".env.local (gitignored)"
  purpose: "Rapid development iteration"
  security: "Developer machine only"
  rotation: "Manual with monthly reminders"
  validation: "Basic format validation"
  
tier_2_staging_testing:
  storage: "Firebase Remote Config"
  purpose: "QA and staging testing"
  security: "Firebase IAM controlled"
  rotation: "Quarterly automated rotation"
  validation: "Service endpoint health checks"
  
tier_3_production:
  storage: "GCP Secret Manager via Pulumi"
  purpose: "Production workloads"
  security: "GCP Secret Manager with audit logs"
  rotation: "Automated with zero-downtime rollover"
  validation: "Full service integration testing"
```

## 🛠️ **Implementation Plan**

### **Phase 1: Secret Detection & Onboarding**
```javascript
// Secret requirements detection
class SecretRequirementsDetector {
  detectRequiredSecrets(scanResults, conventions) {
    const requirements = new Map();
    
    // Firebase secrets
    if (scanResults.features.includes('database')) {
      requirements.set('firebase', {
        keys: ['web_api_key', 'project_id', 'storage_bucket'],
        validation: 'firebase_project_access',
        tier: 'all',
        guides: ['firebase_setup_guide.md']
      });
    }
    
    // OpenAI secrets
    if (this.detectAIUsage(scanResults)) {
      requirements.set('openai', {
        keys: ['api_key'],
        validation: 'completion_test_call',
        tier: ['staging', 'prod'],
        guides: ['openai_setup_guide.md']
      });
    }
    
    // Stripe secrets
    if (scanResults.features.includes('payments')) {
      requirements.set('stripe', {
        keys: ['publishable_key', 'secret_key', 'webhook_secret'],
        validation: 'payment_intent_test',
        tier: 'all',
        guides: ['stripe_setup_guide.md']
      });
    }
    
    // PostHog secrets
    if (scanResults.features.includes('analytics')) {
      requirements.set('posthog', {
        keys: ['api_key', 'project_id'],
        validation: 'capture_test_event',
        tier: 'all',
        guides: ['posthog_setup_guide.md']
      });
    }
    
    return requirements;
  }
}
```

### **Phase 2: Guided Setup Workflow**
```javascript
// Interactive secret setup with validation
class GuidedSecretSetup {
  async runSetupWorkflow(secretRequirements, environment) {
    const results = new Map();
    
    for (const [service, config] of secretRequirements) {
      console.log(`\n🔐 Setting up ${service} for ${environment}`);
      
      // Show setup guide
      await this.showSetupGuide(service, config.guides);
      
      // Collect secrets with validation
      const secrets = await this.collectSecrets(service, config.keys);
      
      // Validate secrets with real API calls
      const validation = await this.validateSecrets(service, secrets, config.validation);
      
      if (validation.success) {
        // Store in appropriate tier
        await this.storeSecrets(service, secrets, environment, config.tier);
        results.set(service, { status: 'success', secrets: secrets });
      } else {
        results.set(service, { status: 'failed', error: validation.error });
      }
    }
    
    return results;
  }

  async validateSecrets(service, secrets, validationType) {
    const validators = {
      firebase_project_access: async (secrets) => {
        // Test Firebase project access
        try {
          const firebase = initializeApp(secrets);
          await getFirestore(firebase).collection('_test').limit(1).get();
          return { success: true };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },
      
      completion_test_call: async (secrets) => {
        // Test OpenAI API call
        try {
          const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${secrets.api_key}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              model: 'gpt-3.5-turbo',
              messages: [{ role: 'user', content: 'Test' }],
              max_tokens: 5
            })
          });
          return { success: response.ok };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },
      
      payment_intent_test: async (secrets) => {
        // Test Stripe API access
        try {
          const stripe = new Stripe(secrets.secret_key);
          await stripe.paymentIntents.create({
            amount: 100,
            currency: 'usd',
            payment_method_types: ['card']
          });
          return { success: true };
        } catch (error) {
          return { success: false, error: error.message };
        }
      }
    };
    
    return await validators[validationType](secrets);
  }
}
```

### **Phase 3: Multi-Tier Storage**
```javascript
// Three-tier secret storage implementation
class SecretStorageManager {
  constructor(environment, conventions) {
    this.environment = environment;
    this.conventions = conventions;
    this.localStorage = new LocalSecretStorage();
    this.remoteConfig = new FirebaseRemoteConfigStorage();
    this.secretManager = new GCPSecretManagerStorage();
  }

  async storeSecret(service, key, value, tier = 'all') {
    const results = {};
    
    if (tier === 'all' || tier.includes('local')) {
      results.local = await this.localStorage.store(
        `${service.toUpperCase()}_${key.toUpperCase()}`,
        value,
        this.environment
      );
    }
    
    if (tier === 'all' || tier.includes('remote')) {
      results.remote = await this.remoteConfig.store(
        `${service}_${key}`,
        value,
        this.environment
      );
    }
    
    if (tier === 'all' || tier.includes('secure')) {
      results.secure = await this.secretManager.store(
        `${this.conventions.naming.projectName}-${this.environment}-${service}-${key}`,
        value
      );
    }
    
    return results;
  }

  async rotateSecret(service, key, newValue) {
    // Zero-downtime rotation strategy
    const oldValue = await this.getSecret(service, key);
    
    // 1. Store new secret with versioning
    await this.storeSecret(service, key, newValue, 'secure');
    await this.storeSecret(service, `${key}_backup`, oldValue, 'secure');
    
    // 2. Test new secret functionality
    const validation = await this.validateSecretFunctionality(service, key, newValue);
    
    if (validation.success) {
      // 3. Update all tiers
      await this.storeSecret(service, key, newValue, 'all');
      
      // 4. Schedule cleanup of old secret
      await this.scheduleSecretCleanup(service, `${key}_backup`, '7d');
      
      return { success: true, rotated: true };
    } else {
      // Rollback on failure
      await this.storeSecret(service, key, oldValue, 'secure');
      return { success: false, error: validation.error };
    }
  }
}
```

### **Phase 4: Automated Rotation**
```javascript
// Automated secret rotation scheduler
class SecretRotationScheduler {
  constructor() {
    this.rotationPolicies = new Map();
  }

  setRotationPolicy(service, key, policy) {
    this.rotationPolicies.set(`${service}_${key}`, {
      interval: policy.interval, // '30d', '90d', '1y'
      autoRotate: policy.autoRotate || false,
      notifyBefore: policy.notifyBefore || '7d',
      lastRotation: new Date()
    });
  }

  async checkRotationNeeds() {
    const rotationTasks = [];
    
    for (const [secretKey, policy] of this.rotationPolicies) {
      const [service, key] = secretKey.split('_');
      const needsRotation = this.calculateRotationNeed(policy);
      
      if (needsRotation.urgent) {
        rotationTasks.push({
          service,
          key,
          priority: 'urgent',
          reason: needsRotation.reason
        });
      } else if (needsRotation.warning) {
        rotationTasks.push({
          service,
          key,
          priority: 'warning',
          reason: needsRotation.reason,
          daysUntilExpiry: needsRotation.daysUntilExpiry
        });
      }
    }
    
    return rotationTasks;
  }

  async executeRotationPlan(rotationTasks) {
    const results = [];
    
    for (const task of rotationTasks) {
      const rotationStrategy = this.getRotationStrategy(task.service);
      
      try {
        const result = await rotationStrategy.rotate(task.key);
        results.push({ ...task, status: 'success', result });
      } catch (error) {
        results.push({ ...task, status: 'failed', error: error.message });
      }
    }
    
    return results;
  }
}
```

## 🧪 **Testing Strategy**

### **Test Cases**

```yaml
test_secret_detection:
  scenario: "Detect required secrets from ChooseHealthy project"
  expected_secrets:
    firebase: [web_api_key, project_id, storage_bucket]
    openai: [api_key]
    stripe: [publishable_key, secret_key, webhook_secret]
    posthog: [api_key, project_id]
  
  validation:
    - All required secrets identified
    - Correct validation methods assigned
    - Appropriate tier assignments

test_guided_setup:
  scenario: "Walk through complete secret setup"
  steps:
    1. Display setup guides for each service
    2. Collect secrets with format validation
    3. Test secrets with real API calls
    4. Store in appropriate tiers
    5. Confirm successful setup
  
  validation:
    - All secrets properly validated
    - Storage completed successfully
    - Environment files updated

test_rotation_workflow:
  scenario: "Test automated secret rotation"
  steps:
    1. Set rotation policy (30 day test)
    2. Simulate time passage
    3. Trigger rotation check
    4. Execute rotation with validation
    5. Verify zero-downtime rollover
  
  validation:
    - Old secrets backed up safely
    - New secrets validated before switch
    - All tiers updated consistently
```

## 📁 **File Structure**

```
/infra/agent/lib/secrets/
├── SecretManager.js                # Main secret management orchestrator
├── SecretRequirementsDetector.js   # Detect needed secrets from scan
├── GuidedSecretSetup.js            # Interactive setup workflow
├── SecretStorageManager.js         # Multi-tier storage implementation
├── SecretRotationScheduler.js      # Automated rotation management
├── storage/
│   ├── LocalSecretStorage.js       # .env file management
│   ├── FirebaseRemoteConfig.js     # Firebase Remote Config integration
│   └── GCPSecretManager.js         # GCP Secret Manager integration
├── validators/
│   ├── FirebaseValidator.js        # Firebase credential validation
│   ├── OpenAIValidator.js          # OpenAI API validation
│   ├── StripeValidator.js          # Stripe credential validation
│   └── PostHogValidator.js         # PostHog credential validation
├── guides/
│   ├── firebase_setup_guide.md     # Firebase setup with screenshots
│   ├── openai_setup_guide.md       # OpenAI API key guide
│   ├── stripe_setup_guide.md       # Stripe integration guide
│   └── posthog_setup_guide.md      # PostHog setup guide
└── test/
    ├── secret-manager.test.js       # Unit tests
    ├── rotation.test.js             # Rotation workflow tests
    └── integration.test.js          # End-to-end secret tests
```

## 🎯 **Success Criteria**

### **Functional Requirements**
- ✅ **100% secret detection** for all required services
- ✅ **Guided setup** with clear instructions and validation
- ✅ **Multi-tier storage** with appropriate security levels
- ✅ **Automated rotation** with zero-downtime rollover

### **Security Requirements**
- ✅ **No secrets in version control** - all gitignored appropriately
- ✅ **Environment isolation** - dev/staging/prod secrets completely separate
- ✅ **Audit logging** for all secret operations
- ✅ **Encryption at rest** for all stored secrets

### **Integration Requirements**
- ✅ **Convention integration** for consistent naming
- ✅ **Environment switching** preserves appropriate secrets
- ✅ **CLI integration** for interactive management
- ✅ **Monitoring integration** for rotation alerts

## 🔗 **Integration Points**

### **Input Sources**
- Scanner results for service detection
- Convention engine for naming patterns
- User input for API keys and credentials
- Existing secret stores for migration

### **Output Consumers**
- Environment configuration files
- Pulumi secret resources
- Application runtime configuration
- Monitoring and alerting systems

### **Security Integration**
```yaml
security_controls:
  - Git ignore patterns for all secret files
  - Environment variable validation
  - Secret rotation monitoring
  - Access logging and audit trails
  - Encryption key management
```

## 📊 **Deliverables**

1. **SecretManager class** with full lifecycle management
2. **Guided setup workflow** with service-specific instructions
3. **Multi-tier storage** implementation for all environments
4. **Rotation automation** with zero-downtime capabilities
5. **Setup guides** with screenshots for all supported services
6. **Test suite** covering all secret management scenarios
7. **CLI integration** for secret management commands

## 🚀 **Next Steps**

Upon completion of Task 03:
1. **Run secret detection** on ChooseHealthy project
2. **Execute guided setup** for all required services
3. **Validate multi-tier storage** across environments
4. **Test rotation workflow** with non-critical keys
5. **Feed into Task 04** (Self-Healing System)
6. **Update progress.md** with secret management status

---

**🎯 Goal**: By the end of this task, InfraBot should be able to say "I've detected you need Firebase, OpenAI, Stripe, and PostHog credentials. Let me guide you through secure setup with validation, store them in appropriate tiers, and schedule automated rotation. Your secrets are now properly managed across all environments."