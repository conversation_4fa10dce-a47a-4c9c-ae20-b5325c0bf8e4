// metro.config.js - Fixed configuration for Firebase and SVG
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Fix for Firebase v11 and react-native-svg
config.resolver.sourceExts = [...(config.resolver.sourceExts || []), 'cjs'];
config.resolver.unstable_enablePackageExports = false;

// Configure alias for src directory
config.resolver.alias = {
  '@': path.resolve(__dirname, 'src'),
};

module.exports = config;