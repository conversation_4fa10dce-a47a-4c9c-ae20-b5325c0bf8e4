{"name": "choosehealthy-ai", "main": "./index.js", "version": "1.0.0", "private": true, "scripts": {"dev": "./scripts/dev-tmux.sh", "dev:clean-dev": "./scripts/clean-dev.sh", "dev:kill-firebase": "./scripts/kill-firebase-emulators.sh", "dev:expo": "expo start --dev-client", "dev:functions": "node scripts/start-emulators.js", "dev:clean": "expo start --clear", "dev:web": "expo start --web", "start": "EXPO_PUBLIC_ENVIRONMENT=development EXPO_PUBLIC_USE_EMULATORS=true expo start", "start:dev": "EXPO_PUBLIC_ENVIRONMENT=development expo start", "start:staging": "EXPO_PUBLIC_ENVIRONMENT=staging expo start", "start:prod": "EXPO_PUBLIC_ENVIRONMENT=production expo start", "build:web": "NODE_ENV=production expo export --platform web", "build:clean": "expo prebuild --clean", "build:dev:ios": "eas build --platform ios --profile development", "build:dev:android": "eas build --platform android --profile development", "build:staging:ios": "eas build --platform ios --profile staging", "build:staging:android": "eas build --platform android --profile staging", "build:prod:ios": "eas build --platform ios --profile production", "build:prod:android": "eas build --platform android --profile production", "lint": "expo lint", "errors": "node scripts/report-errors.js", "check": "pnpm exec tsc --noEmit && pnpm lint", "validate:env": "TS_NODE_COMPILER_OPTIONS='{\"module\":\"commonjs\"}' node -r ts-node/register infra/scripts/validate-all-envs.ts", "validate:env:dev": "TS_NODE_COMPILER_OPTIONS='{\"module\":\"commonjs\"}' node -r ts-node/register infra/scripts/validate-env.ts development", "validate:env:staging": "TS_NODE_COMPILER_OPTIONS='{\"module\":\"commonjs\"}' node -r ts-node/register infra/scripts/validate-env.ts staging", "validate:env:prod": "TS_NODE_COMPILER_OPTIONS='{\"module\":\"commonjs\"}' node -r ts-node/register infra/scripts/validate-env.ts production", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:infra": "jest tests/infra tests/security tests/config tests/services", "test:functions": "cd functions && pnpm test", "test:e2e:infra": "pnpm detox test -c ios.debug e2e/infrastructure.test.js", "test:security": "jest tests/security", "analyze:bundle": "node scripts/bundle-analyzer.js", "optimize:bundle": "node scripts/bundle-optimizer.js", "security:audit": "node scripts/security-audit.js", "test:e2e:ios": "pnpm detox test -c ios.debug", "test:e2e:android": "detox test -c android.debug", "build:e2e:ios": "pnpm detox build -c ios.debug", "build:e2e:android": "detox build -c android.debug", "test:visual": "jest --testMatch='**/tests/visual-regression/**/*.test.js'", "test:visual:update": "jest --testMatch='**/tests/visual-regression/**/*.test.js' --updateSnapshot", "postinstall": "patch-package", "setup:water-tables": "node scripts/setup-water-tables.js", "env:dev": "source scripts/load-env.sh dev", "env:staging": "source scripts/load-env.sh staging", "env:prod": "source scripts/load-env.sh prod", "deploy:dev:all": "firebase deploy --project kingly-choosehealthy-dev", "deploy:staging:all": "firebase deploy --project kingly-choosehealthy-staging", "deploy:prod:all": "firebase deploy --project kingly-choosehealthy-prod", "deploy:dev:functions": "firebase deploy --only functions --project kingly-choosehealthy-dev", "deploy:staging:functions": "firebase deploy --only functions --project kingly-choosehealthy-staging", "deploy:prod:functions": "firebase deploy --only functions --project kingly-choosehealthy-prod", "deploy:dev:rules": "./scripts/deploy-firestore-rules.sh dev", "deploy:staging:rules": "./scripts/deploy-firestore-rules.sh staging", "deploy:prod:rules": "./scripts/deploy-firestore-rules.sh prod", "deploy:dev:storage": "firebase deploy --only storage --project kingly-choosehealthy-dev", "deploy:staging:storage": "firebase deploy --only storage --project kingly-choosehealthy-staging", "deploy:prod:storage": "firebase deploy --only storage --project kingly-choosehealthy-prod", "dev:env": "./scripts/dev-env.sh", "deploy:staging": "./scripts/deploy-staging.sh", "build:staging": "EXPO_PUBLIC_ENVIRONMENT=staging pnpm run build:web", "android": "pnpm expo run:android", "ios": "pnpm expo run:ios"}, "dependencies": {"@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/runtime": "^7.27.1", "@babel/runtime-corejs3": "^7.27.1", "@expo/config-plugins": "~10.0.0", "@expo/vector-icons": "^14.1.0", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/slider": "^4.5.6", "@react-navigation/native": "^7.1.6", "@react-three/drei": "^10.0.7", "@react-three/fiber": "^9.1.2", "@shopify/flash-list": "1.7.6", "@tradle/react-native-http": "^2.0.1", "ajv": "^8.17.1", "base64-arraybuffer": "^1.0.2", "browserify-zlib": "^0.2.0", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "expo": "~53.0.9", "expo-application": "^6.1.4", "expo-auth-session": "^6.1.5", "expo-av": "~15.1.4", "expo-battery": "^9.1.4", "expo-blur": "~14.1.4", "expo-build-properties": "~0.14.6", "expo-camera": "~16.1.6", "expo-clipboard": "^7.1.4", "expo-constants": "~17.1.5", "expo-crypto": "~14.1.4", "expo-device": "^7.1.4", "expo-file-system": "~18.1.9", "expo-font": "~13.3.1", "expo-gl": "^15.1.5", "expo-haptics": "~14.1.4", "expo-image-manipulator": "~13.1.5", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.4", "expo-local-authentication": "^16.0.4", "expo-localization": "^16.1.5", "expo-media-library": "~17.1.6", "expo-navigation-bar": "~4.2.4", "expo-network": "~7.1.5", "expo-notifications": "^0.31.1", "expo-print": "~14.1.4", "expo-router": "~5.0.7", "expo-secure-store": "~14.2.3", "expo-sharing": "~13.1.5", "expo-speech": "~13.1.6", "expo-splash-screen": "~0.30.8", "expo-sqlite": "^15.2.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-three": "^8.0.0", "expo-updates": "^0.28.13", "expo-web-browser": "~14.1.6", "firebase": "^11.0.0", "https-browserify": "^1.0.0", "i18n-js": "^4.5.1", "openai": "^4.98.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "posthog-react-native": "^3.15.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-auth0": "^4.6.0", "react-native-chart-kit": "^6.12.0", "react-native-device-info": "^10.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-google-fit": "^0.21.0", "react-native-health": "^1.19.0", "react-native-health-connect": "^2.1.4", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-modal-datetime-picker": "^18.0.0", "react-native-paper": "^5.14.0", "react-native-polyfill-globals": "^3.1.0", "react-native-progress": "^5.0.1", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-toast-message": "^2.3.0", "react-native-url-polyfill": "^2.0.0", "react-native-view-shot": "4.0.3", "react-native-web": "^0.20.0", "react-native-web-hooks": "^3.0.2", "react-native-webview": "13.13.5", "react-test-renderer": "19.0.0", "react-three-fiber": "^6.0.13", "stream-browserify": "^3.0.0", "three": "^0.166.0", "three-collada-loader": "^0.0.1", "three-gltf-loader": "^1.111.0", "three-mtl-loader": "^1.0.2", "three-obj-loader": "^1.1.3", "uuid": "^11.1.0", "zod": "^3.22.4"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/generator": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-flow-strip-types": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/preset-env": "^7.27.1", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@babel/traverse": "^7.27.1", "@firebase/rules-unit-testing": "^4.0.1", "@ovalmoney/react-native-fitness": "^0.5.3", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.4.3", "@types/babel__traverse": "^7.20.7", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-native": "^0.73.0", "@types/uuid": "^10.0.0", "babel-loader": "^10.0.0", "babel-plugin-module-resolver": "^5.0.2", "concurrently": "^9.1.2", "dotenv": "^16.4.5", "eslint": "^9.0.0", "eslint-config-expo": "~9.2.0", "expo": "~53.0.9", "expo-module-scripts": "^4.1.6", "file-loader": "^6.2.0", "firebase-admin": "^12.7.0", "firebase-tools": "^13.11.2", "html-webpack-plugin": "^5.6.3", "jest": "^29.7.0", "jest-expo": "~53.0.4", "mime-types": "^3.0.1", "node-fetch": "^3.3.2", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react-native-dotenv": "^3.4.9", "react-test-renderer": "19.0.0", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "~5.8.3", "webpack": "^5.99.7", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}, "resolutions": {"hawk": "^9.0.0", "inotify": "npm:noop2@*"}, "overrides": {"react": "19.0.0", "react-dom": "19.0.0", "@types/react": "~19.0.10", "lucide-react-native": {"react": "19.0.0", "react-native-svg": "15.11.2"}, "@react-three/drei": {"react": "19.0.0", "react-dom": "19.0.0"}, "@react-three/fiber": {"react": "19.0.0"}, "expo-three": {"expo-file-system": "18.1.10", "react-native": "0.79.2"}}}