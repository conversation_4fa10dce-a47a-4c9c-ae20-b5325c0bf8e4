#!/bin/bash
# Kill common ports used by Expo and Firebase

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Cleaning up development processes and ports...${NC}"

# Kill Java processes that are Firebase emulators
echo -e "${YELLOW}Stopping Firebase emulator Java processes...${NC}"
# Find all Java processes that are Firebase emulators
ps aux | grep -E "firebase/emulators|cloud-firestore-emulator|firebase.*emulator" | grep -v grep | while read -r line; do
    PID=$(echo "$line" | awk '{print $2}')
    PROCESS_NAME=$(echo "$line" | awk '{for(i=11;i<=NF;i++) printf "%s ", $i}')
    echo -e "${RED}Killing Firebase Java process (PID: $PID)${NC}"
    echo -e "  Process: ${PROCESS_NAME:0:100}..."
    kill -9 $PID 2>/dev/null
done

# Kill specific Firebase emulator processes
echo -e "${YELLOW}Stopping Firebase emulator processes...${NC}"
# Kill Firebase-specific processes
pkill -f "firebase-tools.*emulators:start" 2>/dev/null
pkill -f "firebase.*emulators" 2>/dev/null
pkill -f "hub-cloud-functions" 2>/dev/null
pkill -f "cloud-firestore-emulator" 2>/dev/null
pkill -f "pubsub-emulator" 2>/dev/null
pkill -f "firebase-database-emulator" 2>/dev/null
pkill -f "node.*firebase.*emulators" 2>/dev/null
pkill -f "node.*start-emulators" 2>/dev/null

# Common ports used by the app
PORTS=(
    3000    # Metro bundler
    8081    # Metro bundler
    19000   # Expo
    19001   # Expo
    19002   # Expo
    8080    # Firebase Hosting
    8085    # Firebase UI
    9000    # Firebase Auth
    9199    # Firebase Storage
    5001    # Firebase Functions
    # 5000  # Removed - used by Apple AirPlay/services
    4000    # Firebase Database
    4500    # Firebase Extensions
    4400    # Firebase Hub
    9099    # Firebase Pub/Sub
    9299    # Firebase Eventarc
    9150    # Firestore websocket
)

echo -e "${YELLOW}Killing processes on common development ports...${NC}"

for PORT in "${PORTS[@]}"; do
    # Find all processes using the port (not just the first one)
    lsof -ti:$PORT 2>/dev/null | while read -r PID; do
        if [ ! -z "$PID" ]; then
            # Get process name for better logging
            PNAME=$(ps -p $PID -o comm= 2>/dev/null || echo "unknown")
            echo -e "${RED}Killing process '$PNAME' on port $PORT (PID: $PID)${NC}"
            kill -9 $PID 2>/dev/null
        fi
    done
    
    # Verify port is free
    if lsof -ti:$PORT >/dev/null 2>&1; then
        echo -e "${RED}Port $PORT is still in use, forcing cleanup...${NC}"
        lsof -ti:$PORT | xargs kill -9 2>/dev/null
    else
        echo -e "${GREEN}Port $PORT is free${NC}"
    fi
done

# Clean up any orphaned Firebase/Java processes
echo -e "${YELLOW}Final cleanup of Firebase processes...${NC}"

# Kill any remaining Java processes that look like Firebase emulators
pgrep -f "java.*firebase.*emulator" | while read -r PID; do
    echo -e "${RED}Killing orphaned Firebase Java process: $PID${NC}"
    kill -9 $PID 2>/dev/null
done

# Kill any remaining Node processes running Firebase commands
pgrep -f "node.*firebase" | while read -r PID; do
    PCMD=$(ps -p $PID -o args= 2>/dev/null || echo "")
    if [[ $PCMD == *"emulator"* ]] || [[ $PCMD == *"start-emulators"* ]]; then
        echo -e "${RED}Killing orphaned Firebase Node process: $PID${NC}"
        kill -9 $PID 2>/dev/null
    fi
done

# Clean up Firebase emulator UI processes
pgrep -f "firebase-emulators-ui" | while read -r PID; do
    echo -e "${RED}Killing Firebase Emulator UI process: $PID${NC}"
    kill -9 $PID 2>/dev/null
done

echo -e "${GREEN}Port and process cleanup complete!${NC}"

# Show any remaining Firebase/Java processes (for debugging)
REMAINING_JAVA=$(ps aux | grep -E "java.*firebase|java.*emulator" | grep -v grep)
REMAINING_NODE=$(ps aux | grep -E "node.*firebase.*emulator|node.*start-emulators" | grep -v grep)

if [ ! -z "$REMAINING_JAVA" ] || [ ! -z "$REMAINING_NODE" ]; then
    echo -e "${YELLOW}Warning: Some Firebase-related processes may still be running:${NC}"
    if [ ! -z "$REMAINING_JAVA" ]; then
        echo -e "${YELLOW}Java processes:${NC}"
        echo "$REMAINING_JAVA"
    fi
    if [ ! -z "$REMAINING_NODE" ]; then
        echo -e "${YELLOW}Node processes:${NC}"
        echo "$REMAINING_NODE"
    fi
    echo -e "${YELLOW}You may need to manually kill these processes or restart your system.${NC}"
fi