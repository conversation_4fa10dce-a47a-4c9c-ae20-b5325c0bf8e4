#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 ChooseHealthy - Error Analysis Report');
console.log('=' .repeat(50));

// Function to run command and capture output
function runCommand(command, description) {
  console.log(`\n📋 ${description}`);
  console.log('-'.repeat(30));
  
  try {
    const output = execSync(command, { 
      encoding: 'utf8', 
      cwd: process.cwd(),
      stdio: 'pipe'
    });
    
    if (output.trim()) {
      console.log(output);
      return { success: true, output: output.trim() };
    } else {
      console.log('✅ No errors found');
      return { success: true, output: '' };
    }
  } catch (error) {
    console.log('❌ Errors found:');
    console.log(error.stdout || error.message);
    return { success: false, output: error.stdout || error.message };
  }
}

// Function to analyze import paths
function analyzeImports() {
  console.log('\n📁 Import Path Analysis');
  console.log('-'.repeat(30));
  
  const rootFiles = fs.readdirSync('.').filter(f => 
    f.endsWith('.tsx') || f.endsWith('.ts') || f.endsWith('.js')
  );
  
  console.log('Root TypeScript/JavaScript files:');
  rootFiles.forEach(file => {
    console.log(`  - ${file}`);
  });
  
  // Check for missing imports in App.tsx
  if (fs.existsSync('./App.tsx')) {
    console.log('\n🔍 Analyzing App.tsx imports...');
    const content = fs.readFileSync('./App.tsx', 'utf8');
    const imports = content.match(/import.*from\s+['"]([^'"]+)['"]/g) || [];
    
    imports.forEach(imp => {
      const match = imp.match(/from\s+['"]([^'"]+)['"]/);
      if (match) {
        const importPath = match[1];
        if (importPath.startsWith('./') || importPath.startsWith('../')) {
          const fullPath = path.resolve(importPath);
          const exists = fs.existsSync(fullPath) || 
                        fs.existsSync(fullPath + '.ts') || 
                        fs.existsSync(fullPath + '.tsx') ||
                        fs.existsSync(fullPath + '.js');
          
          console.log(`  ${exists ? '✅' : '❌'} ${importPath}`);
        }
      }
    });
  }
}

// Function to check file structure
function checkFileStructure() {
  console.log('\n🏗️  File Structure Analysis');
  console.log('-'.repeat(30));
  
  const srcExists = fs.existsSync('./src');
  const appExists = fs.existsSync('./src/app');
  const layoutExists = fs.existsSync('./src/app/_layout.tsx');
  
  console.log(`src/ directory: ${srcExists ? '✅' : '❌'}`);
  console.log(`src/app/ directory: ${appExists ? '✅' : '❌'}`);
  console.log(`src/app/_layout.tsx: ${layoutExists ? '✅' : '❌'}`);
  
  // Check for duplicate files
  const rootAppTsx = fs.existsSync('./App.tsx');
  const webAppTsx = fs.existsSync('./web/App.tsx');
  
  if (rootAppTsx && (srcExists || webAppTsx)) {
    console.log('⚠️  DUPLICATE: Root App.tsx exists alongside src/ structure');
  }
}

// Main execution
async function main() {
  // 1. TypeScript errors
  const tscResult = runCommand(
    'pnpm exec tsc --noEmit --pretty false',
    'TypeScript Compilation Errors'
  );
  
  // 2. ESLint errors  
  const eslintResult = runCommand(
    'pnpm exec eslint src/ --format=stylish',
    'ESLint Analysis'
  );
  
  // 3. Import analysis
  analyzeImports();
  
  // 4. File structure check
  checkFileStructure();
  
  // 5. Summary
  console.log('\n📊 Error Summary');
  console.log('=' .repeat(30));
  
  const tscErrors = tscResult.output.split('\n').filter(line => 
    line.includes('error TS')
  ).length;
  
  const eslintErrors = eslintResult.output.split('\n').filter(line => 
    line.includes('error') || line.includes('warning')
  ).length;
  
  console.log(`TypeScript errors: ${tscErrors}`);
  console.log(`ESLint issues: ${eslintErrors}`);
  
  if (tscErrors > 0 || eslintErrors > 0) {
    console.log('\n🎯 Recommended Actions:');
    if (fs.existsSync('./App.tsx')) {
      console.log('1. Delete obsolete root App.tsx');
    }
    console.log('2. Move remaining root files to src/ if needed');
    console.log('3. Update import paths to use src/ structure');
    console.log('4. Run this script again to verify fixes');
  } else {
    console.log('\n🎉 No errors found! Codebase looks clean.');
  }
}

main().catch(console.error);