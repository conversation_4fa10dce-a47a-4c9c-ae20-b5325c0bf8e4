#!/usr/bin/env node

/**
 * Start Firebase emulators based on environment variables
 * This script dynamically builds the emulator command based on which services are enabled
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Load environment variables in order: .env.development first, then .env.local to override
function loadEnvironmentFiles() {
  const rootDir = path.resolve(process.cwd());

  // First load .env.development
  const envDevelopmentPath = path.join(rootDir, '.env.development');
  if (fs.existsSync(envDevelopmentPath)) {
    require('dotenv').config({ path: envDevelopmentPath });
    console.log('Loaded .env.development');
  } else {
    console.warn('.env.development not found');
  }

  // Then load .env.local to override
  const envLocalPath = path.join(rootDir, '.env.local');
  if (fs.existsSync(envLocalPath)) {
    require('dotenv').config({ path: envLocalPath, override: true });
    console.log('Loaded .env.local (with overrides)');
  } else {
    console.warn('.env.local not found');
  }

  // Finally load .env as fallback
  const envPath = path.join(rootDir, '.env');
  if (fs.existsSync(envPath)) {
    require('dotenv').config({ path: envPath });
    console.log('Loaded .env as fallback');
  }
}

// Load environment files
loadEnvironmentFiles();

// Read environment variables to determine which emulators to start
// Check both EXPO_PUBLIC_ prefixed and non-prefixed versions for compatibility
const useAuth = process.env.EXPO_PUBLIC_FIREBASE_EMULATOR_AUTH === 'true' || process.env.FIREBASE_EMULATOR_AUTH === 'true';
const useFirestore = process.env.EXPO_PUBLIC_FIREBASE_EMULATOR_FIRESTORE === 'true' || process.env.FIREBASE_EMULATOR_FIRESTORE === 'true';
const useStorage = process.env.EXPO_PUBLIC_FIREBASE_EMULATOR_STORAGE === 'true' || process.env.FIREBASE_EMULATOR_STORAGE === 'true';
const useFunctions = process.env.EXPO_PUBLIC_FIREBASE_EMULATOR_FUNCTIONS === 'true' || process.env.FIREBASE_EMULATOR_FUNCTIONS === 'true';
const useHosting = process.env.EXPO_PUBLIC_FIREBASE_EMULATOR_HOSTING === 'true' || process.env.FIREBASE_EMULATOR_HOSTING === 'true';

// Build list of emulators to start
const emulators = [];
if (useAuth) emulators.push('auth');
if (useFirestore) emulators.push('firestore');
if (useStorage) emulators.push('storage');
if (useFunctions) emulators.push('functions');
if (useHosting) emulators.push('hosting');

// Debug output
console.log('Emulator configuration:');
console.log(`  Auth: ${useAuth}`);
console.log(`  Firestore: ${useFirestore}`);
console.log(`  Storage: ${useStorage}`);
console.log(`  Functions: ${useFunctions}`);
console.log(`  Hosting: ${useHosting}`);

if (emulators.length === 0) {
  console.log('No emulators enabled. Set EXPO_PUBLIC_FIREBASE_EMULATOR_* or FIREBASE_EMULATOR_* environment variables to true.');
  process.exit(0);
}

// Build Firebase emulator command
const firebaseCommand = 'firebase';
const args = [
  'emulators:start',
  '--only',
  emulators.join(','),
  '--import=./emulator-data',
  '--export-on-exit'
];

console.log('Starting Firebase emulators:', emulators.join(', '));
console.log('Command:', firebaseCommand, args.join(' '));

// Start the emulators
const emulatorProcess = spawn(firebaseCommand, args, {
  stdio: 'inherit',
  shell: true
});

emulatorProcess.on('error', (error) => {
  console.error('Failed to start emulators:', error);
  process.exit(1);
});

emulatorProcess.on('exit', (code) => {
  console.log(`Emulators exited with code ${code}`);
  process.exit(code);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\nShutting down emulators...');
  emulatorProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\nShutting down emulators...');
  emulatorProcess.kill('SIGTERM');
});