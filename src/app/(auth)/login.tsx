import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Platform,
  KeyboardAvoidingView,
  Alert,
  useWindowDimensions,
} from 'react-native';
import { useRouter, <PERSON> } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { useAuth } from '@/contexts/AuthContext';
import { logFirebaseConfigStatus } from '@/lib/firebase';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Animated, { FadeIn, FadeInDown } from 'react-native-reanimated';
import AuthInput from '@/components/ui/AuthInput';
import AuthButton from '@/components/ui/AuthButton';
import AuthBackground from '@/components/ui/AuthBackground';
import type { AuthResult } from '@/contexts/AuthContext';

// Debug: Check if components are properly imported
console.log('Component imports check:', {
  Feather: typeof Feather,
  AuthInput: typeof AuthInput,
  AuthButton: typeof AuthButton,
  AuthBackground: typeof AuthBackground,
});

export default function LoginScreen() {
  const router = useRouter();
  const { colors, isDark, toggleTheme } = useTheme();
  const auth = useAuth();
  const loading = auth.loading || false;
  const insets = useSafeAreaInsets();
  const { width } = useWindowDimensions();
  const isMounted = useRef(true);

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [socialLoading, setSocialLoading] = useState<'google' | 'apple' | null>(
    null
  );

  // Component cleanup on unmount
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Watch for user changes
  useEffect(() => {
    let timeoutId: ReturnType<typeof setTimeout>;

    if (auth.user) {
      console.log('User is authenticated, navigating to home');
      // Delay navigation slightly to allow state to settle
      timeoutId = setTimeout(() => {
        if (isMounted.current) {
          // For web, force a complete page reload to ensure clean state
          if (Platform.OS === 'web') {
            console.log('Forcing clean page reload for navigation');
            window.location.href = '/';
          } else {
            router.replace('/(tabs)');
          }
        }
      }, 100);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [auth.user, router]);

  // Check Firebase configuration
  const checkFirebaseConfig = () => {
    const configStatus = logFirebaseConfigStatus()
      ? 'Configured'
      : 'Not configured';

    console.log('Firebase Configuration Status:', configStatus);
    console.log(
      'Current User:',
      auth.user ? 'Authenticated' : 'Not authenticated'
    );
    console.log('Auth Session:', auth.session ? 'Active' : 'None');

    Alert.alert(
      'Firebase Configuration',
      `Firebase: ${configStatus}\nUser: ${
        auth.user ? 'Logged In' : 'Not Logged In'
      }`
    );
  };

  const handleLogin = async () => {
    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    setError('');
    console.log('Attempting to sign in with:', {
      email,
      passwordLength: password.length,
    });

    try {
      // Clean up navigation state before login
      if (Platform.OS === 'web') {
        console.log('Preparing for clean login state in web environment');
        // Remove any cached navigation state
        sessionStorage.removeItem('expo-router-state');
        sessionStorage.removeItem('expo-router-origin-state');
        sessionStorage.removeItem('firebase:authUser');

        // Set a flag to indicate this is an intentional login
        sessionStorage.setItem('intentional_login', 'true');

        // Logging to debug
        console.log(
          'Set intentional_login flag:',
          sessionStorage.getItem('intentional_login')
        );

        // Remove service worker caches if they exist
        if ('caches' in window) {
          try {
            const cacheNames = await window.caches.keys();
            for (const cacheName of cacheNames) {
              if (cacheName.includes('firebase')) {
                await window.caches.delete(cacheName);
                console.log(`Deleted cache: ${cacheName}`);
              }
            }
          } catch (e) {
            console.warn('Failed to clear caches:', e);
          }
        }
      }

      // Attempt sign in with timeout for better error handling
      console.log('Starting auth.signIn call...');
      const loginPromise = auth.signIn(email, password);
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(
          () => reject(new Error('Login request timed out after 15s')),
          15000
        )
      );

      const result = (await Promise.race([
        loginPromise,
        timeoutPromise,
      ])) as AuthResult;

      if (!isMounted.current) return;

      console.log('Sign in result:', result);

      if (result && 'success' in result && result.success) {
        console.log('Sign in successful, navigating to home');

        // For web, force a complete page reload to ensure clean auth state
        if (Platform.OS === 'web') {
          console.log('Forcing page reload to clear any stale state');
          window.location.href = '/';
        } else {
          // For mobile, use the router to navigate
          router.replace('/(tabs)');
        }
      } else {
        console.error('Sign in error:', result.error);
        const errorMessage =
          result.error?.message || result.error || 'Invalid email or password';
        setError(
          typeof errorMessage === 'string'
            ? errorMessage
            : 'Authentication failed'
        );

        // If using a test account, show special message
        const testAccounts = ['<EMAIL>', '<EMAIL>'];
        if (testAccounts.includes(email.toLowerCase())) {
          setError(
            'Test accounts are disabled. Please use a different account.'
          );
          return;
        }

        // Show alert with more details for debugging
        if (typeof errorMessage === 'object') {
          Alert.alert(
            'Authentication Error',
            'Detailed error information has been logged to console'
          );
          console.error('Detailed auth error:', JSON.stringify(errorMessage));
        }
      }
    } catch (err: unknown) {
      if (!isMounted.current) return;

      console.error('Sign in exception:', err);
      setError(
        `Error: ${
          err instanceof Error ? err.message : 'Unknown error occurred'
        }`
      );

      // If timeout error, suggest refreshing the page
      if (err instanceof Error && err.message.includes('timed out')) {
        Alert.alert(
          'Connection Issue',
          'The login request timed out. Please try refreshing the page.',
          [
            { text: 'Cancel', style: 'cancel' },
            Platform.OS === 'web'
              ? {
                  text: 'Refresh Now',
                  onPress: () => {
                    window.location.reload();
                  },
                }
              : undefined,
          ].filter(Boolean) as any
        );
      } else {
        Alert.alert(
          'Login Error',
          'An unexpected error occurred during sign in. Please try again.'
        );
      }
    }
  };

  const handleGoogleSignIn = async () => {
    setSocialLoading('google');
    setError('');

    try {
      const result = await auth.signInWithGoogle();

      if (!isMounted.current) return;

      if (result.success) {
        console.log('Google sign in successful, user should be set shortly');
        // Navigation will happen in the useEffect when user is set
      } else {
        console.error('Google sign in error:', result.error);
        const errorMessage =
          result.error?.message ||
          result.error ||
          'Failed to sign in with Google';
        setError(
          typeof errorMessage === 'string'
            ? errorMessage
            : 'Authentication failed'
        );
      }
    } catch (error) {
      if (!isMounted.current) return;

      console.error('Google sign in exception:', error);
      setError('An unexpected error occurred during Google sign in');
    } finally {
      if (isMounted.current) {
        setSocialLoading(null);
      }
    }
  };

  const handleAppleSignIn = async () => {
    setSocialLoading('apple');
    setError('');

    try {
      const result = await auth.signInWithApple();

      if (!isMounted.current) return;

      if (result.success) {
        console.log('Apple sign in successful, user should be set shortly');
        // Navigation will happen in the useEffect when user is set
      } else {
        console.error('Apple sign in error:', result.error);
        // Display the specific error message returned from the auth context
        const errorMessage =
          typeof result.error === 'string'
            ? result.error
            : result.error?.message || 'Failed to sign in with Apple';
        setError(errorMessage);
      }
    } catch (error) {
      if (!isMounted.current) return;

      console.error('Apple sign in exception:', error);
      setError('An unexpected error occurred during Apple sign in');
    } finally {
      if (isMounted.current) {
        setSocialLoading(null);
      }
    }
  };

  const handleDebugLogin = () => {
    Alert.alert('Debug Options', 'Select an action', [
      {
        text: 'Check Config',
        onPress: checkFirebaseConfig,
      },
      {
        text: 'Force Navigate to Home',
        onPress: () => router.replace('/(tabs)'),
      },
      {
        text: 'Cancel',
        style: 'cancel',
      },
    ]);
  };

  return (
    <AuthBackground>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView
          style={styles.container}
          contentContainerStyle={[
            styles.contentContainer,
            {
              paddingTop: insets.top || 20,
              paddingBottom: insets.bottom || 20,
            },
          ]}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.topBar}>
            <TouchableOpacity
              style={[
                styles.backButton,
                {
                  backgroundColor: isDark
                    ? colors.card
                    : 'rgba(255, 255, 255, 0.9)',
                },
              ]}
              onPress={() => router.back()}
              accessibilityLabel="Go back"
            >
              <Feather name="arrow-left" size={20} color={colors.text} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.themeToggle,
                {
                  backgroundColor: isDark
                    ? colors.card
                    : 'rgba(255, 255, 255, 0.9)',
                },
              ]}
              onPress={toggleTheme}
              accessibilityLabel={
                isDark ? 'Switch to light mode' : 'Switch to dark mode'
              }
            >
              {isDark ? (
                <Feather name="sun" size={20} color={colors.text} />
              ) : (
                <Feather name="moon" size={20} color={colors.text} />
              )}
            </TouchableOpacity>
          </View>

          <Animated.View entering={FadeIn.duration(600)} style={styles.header}>
            <Animated.View entering={FadeInDown.delay(200).duration(600)}>
              <Text
                style={[
                  styles.title,
                  { color: isDark ? '#ffffff' : colors.text },
                ]}
              >
                Welcome Back
              </Text>
              <Text
                style={[
                  styles.subtitle,
                  { color: isDark ? '#ffffffcc' : colors.textSecondary },
                ]}
              >
                Sign in to continue tracking your nutrition journey
              </Text>
            </Animated.View>
          </Animated.View>

          {error ? (
            <Animated.View
              entering={FadeIn.duration(300)}
              style={[
                styles.errorContainer,
                {
                  backgroundColor: colors.error + '15',
                  borderColor: colors.error + '30',
                  borderWidth: 1,
                },
              ]}
            >
              <Text style={[styles.errorText, { color: colors.error }]}>
                {error}
              </Text>
            </Animated.View>
          ) : null}

          <Animated.View
            entering={FadeInDown.delay(300).duration(600)}
            style={styles.formCard}
          >
            {/* Social Sign In Buttons */}
            <View style={styles.socialButtonsContainer}>
              <AuthButton
                title="Continue with Google"
                onPress={handleGoogleSignIn}
                loading={socialLoading === 'google'}
                disabled={loading || socialLoading !== null}
                variant="outline"
                icon={
                  <View style={styles.socialIconContainer}>
                    <Text
                      style={{
                        fontSize: 16,
                        fontWeight: 'bold',
                        color: '#EA4335',
                      }}
                    >
                      G
                    </Text>
                  </View>
                }
                accessibilityLabel="Sign in with Google"
              />

              <AuthButton
                title="Continue with Apple"
                onPress={handleAppleSignIn}
                loading={socialLoading === 'apple'}
                disabled={loading || socialLoading !== null}
                variant="outline"
                style={{
                  backgroundColor: '#000000',
                  borderColor: '#000000',
                  marginTop: 12,
                }}
                textStyle={{ color: '#ffffff' }}
                icon={
                  <View style={styles.socialIconContainer}>
                    <Text
                      style={{
                        fontSize: 16,
                        fontWeight: 'bold',
                        color: '#ffffff',
                      }}
                    >
                      A
                    </Text>
                  </View>
                }
                accessibilityLabel="Sign in with Apple"
              />
            </View>

            {/* Divider */}
            <View style={styles.dividerContainer}>
              <View
                style={[
                  styles.divider,
                  { backgroundColor: isDark ? '#ffffff40' : colors.border },
                ]}
              />
              <Text
                style={[
                  styles.dividerText,
                  { color: isDark ? '#ffffffcc' : colors.textSecondary },
                ]}
              >
                or
              </Text>
              <View
                style={[
                  styles.divider,
                  { backgroundColor: isDark ? '#ffffff40' : colors.border },
                ]}
              />
            </View>

            <View style={styles.form}>
              <AuthInput
                label="Email"
                icon={<Feather name="mail" size={20} color={colors.textSecondary} />}
                placeholder="<EMAIL>"
                value={email}
                onChangeText={setEmail}
                autoCapitalize="none"
                keyboardType="email-address"
                accessibilityLabel="Email input"
              />

              <AuthInput
                label="Password"
                icon={<Feather name="lock" size={20} color={colors.textSecondary} />}
                placeholder="Enter your password"
                value={password}
                onChangeText={setPassword}
                secureTextEntry
                autoCapitalize="none"
                accessibilityLabel="Password input"
              />

              <TouchableOpacity
                style={styles.forgotPassword}
                accessibilityLabel="Forgot password"
              >
                <Text
                  style={[styles.forgotPasswordText, { color: colors.primary }]}
                >
                  Forgot Password?
                </Text>
              </TouchableOpacity>

              <AuthButton
                title="Sign In"
                onPress={handleLogin}
                loading={loading}
                accessibilityLabel="Sign in"
                style={{ marginTop: 8 }}
              />
            </View>
          </Animated.View>

          <Animated.View
            entering={FadeInDown.delay(600).duration(600)}
            style={styles.footer}
          >
            <Text
              style={[
                styles.footerText,
                { color: isDark ? '#ffffffcc' : colors.textSecondary },
              ]}
            >
              Don&apos;t have an account?{' '}
            </Text>
            <Link href="/(auth)/signup" asChild>
              <TouchableOpacity
                style={Platform.OS === 'web' ? { transform: [] } : undefined}
                accessibilityLabel="Go to sign up"
              >
                <Text
                  style={[
                    styles.footerLink,
                    { color: isDark ? '#ffffff' : colors.primary },
                  ]}
                >
                  Sign Up
                </Text>
              </TouchableOpacity>
            </Link>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>
    </AuthBackground>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    padding: 24,
    paddingBottom: 32,
  },
  topBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  header: {
    marginBottom: 36,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 2,
  },
  themeToggle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 2,
  },
  title: {
    fontSize: 36,
    fontWeight: '800',
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '500',
  },
  formCard: {
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
    backgroundColor: 'transparent',
  },
  errorContainer: {
    padding: 14,
    borderRadius: 12,
    marginBottom: 24,
  },
  errorText: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Social buttons styles
  socialButtonsContainer: {
    marginBottom: 20,
  },
  socialIconContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Divider styles
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  divider: {
    flex: 1,
    height: 1,
  },
  dividerText: {
    marginHorizontal: 12,
    fontSize: 14,
    fontWeight: '600',
  },
  form: {
    marginBottom: 24,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 28,
    paddingVertical: 4,
  },
  forgotPasswordText: {
    fontSize: 14,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 16,
    marginTop: 'auto',
    backgroundColor: 'transparent',
    zIndex: 10,
  },
  footerText: {
    fontSize: 15,
    fontWeight: '500',
  },
  footerLink: {
    fontSize: 15,
    fontWeight: '700',
  },
});
