import React, { useState, useRef, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TouchableOpacity, 
  ScrollView, 
  Platform, 
  KeyboardAvoidingView,
  Alert,
  useWindowDimensions
} from 'react-native';
import { useRouter, Link } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather, MaterialIcons } from '@expo/vector-icons';
import { useAuth } from '@/contexts/AuthContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Animated, { FadeIn, FadeInDown } from 'react-native-reanimated';
import AuthInput from '@/components/ui/AuthInput';
import AuthButton from '@/components/ui/AuthButton';
import AuthBackground from '@/components/ui/AuthBackground';

export default function SignupScreen() {
  const router = useRouter();
  const { colors, isDark, toggleTheme } = useTheme();
  const { signUp, signInWithGoogle, signInWithApple, loading } = useAuth();
  const insets = useSafeAreaInsets();
  const { width } = useWindowDimensions();
  const isMounted = useRef(true);
  
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [socialLoading, setSocialLoading] = useState<'google' | 'apple' | null>(null);
  
  // Component cleanup on unmount
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);
  
  // Password validation
  const [passwordFocus, setPasswordFocus] = useState(false);
  const hasMinLength = password.length >= 6;
  const hasNumber = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  const passwordsMatch = password === confirmPassword && password.length > 0;

  const isValidEmail = (email: string) => {
    return /\S+@\S+\.\S+/.test(email);
  };
  
  const handleSignup = async () => {
    if (!fullName || !email || !password || !confirmPassword) {
      setError('Please fill in all fields');
      return;
    }
    
    if (!isValidEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }
    
    if (password.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }
    
    if (!hasNumber || !hasSpecialChar) {
      setError('Password must contain at least one number and one special character');
      return;
    }
    
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }
    
    setError('');
    
    try {
      const result = await signUp(email, password, fullName);
      
      if (!isMounted.current) return;
      
      if (!result.error) {
        router.replace('/(onboarding)');
      } else {
        const errorMessage = typeof result.error === 'string' 
          ? result.error 
          : result.error?.message || 'Failed to create account';
        
        setError(errorMessage);
      }
    } catch (err) {
      if (!isMounted.current) return;
      
      console.error('Signup error:', err);
      setError('An unexpected error occurred. Please try again.');
    }
  };

  const handleGoogleSignIn = async () => {
    setSocialLoading('google');
    setError('');
    
    try {
      const result = await signInWithGoogle();
      
      if (!isMounted.current) return;
      
      if (result.success) {
        router.replace('/(onboarding)');
      } else {
        setError(result.error?.message || 'Failed to sign in with Google');
      }
    } catch (error) {
      if (!isMounted.current) return;
      
      setError('An unexpected error occurred');
      console.error('Google sign in error:', error);
    } finally {
      if (isMounted.current) {
        setSocialLoading(null);
      }
    }
  };

  const handleAppleSignIn = async () => {
    setSocialLoading('apple');
    setError('');
    
    try {
      const result = await signInWithApple();
      
      if (!isMounted.current) return;
      
      if (result.success) {
        router.replace('/(onboarding)');
      } else {
        console.error('Apple sign in error:', result.error);
        // Display the specific error message returned from the auth context
        setError(typeof result.error === 'string' 
          ? result.error 
          : result.error?.message || 'Failed to sign in with Apple');
      }
    } catch (error) {
      if (!isMounted.current) return;
      
      console.error('Apple sign in exception:', error);
      setError('An unexpected error occurred during Apple sign in');
    } finally {
      if (isMounted.current) {
        setSocialLoading(null);
      }
    }
  };

  // Password strength indicators
  const renderPasswordStrength = () => {
    if (passwordFocus || password.length > 0) {
      return (
        <View style={styles.passwordStrengthContainer}>
          <View style={styles.strengthRow}>
            {hasMinLength ? (
              <MaterialIcons name="check-circle" size={16} color={colors.success} />
            ) : (
              <Feather name="alert-circle" size={16} color={colors.textSecondary} />
            )}
            <Text style={[styles.strengthText, { 
              color: hasMinLength ? colors.success : colors.textSecondary 
            }]}>
              At least 6 characters
            </Text>
          </View>
          
          <View style={styles.strengthRow}>
            {hasNumber ? (
              <MaterialIcons name="check-circle" size={16} color={colors.success} />
            ) : (
              <Feather name="alert-circle" size={16} color={colors.textSecondary} />
            )}
            <Text style={[styles.strengthText, { 
              color: hasNumber ? colors.success : colors.textSecondary 
            }]}>
              Contains a number
            </Text>
          </View>
          
          <View style={styles.strengthRow}>
            {hasSpecialChar ? (
              <MaterialIcons name="check-circle" size={16} color={colors.success} />
            ) : (
              <Feather name="alert-circle" size={16} color={colors.textSecondary} />
            )}
            <Text style={[styles.strengthText, { 
              color: hasSpecialChar ? colors.success : colors.textSecondary 
            }]}>
              Contains a special character
            </Text>
          </View>
        </View>
      );
    }
    return null;
  };
  
  return (
    <AuthBackground>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView 
          style={styles.container}
          contentContainerStyle={[
            styles.contentContainer, 
            { paddingTop: insets.top || 20, paddingBottom: insets.bottom || 20 }
          ]}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.topBar}>
            <TouchableOpacity
              style={[styles.backButton, { backgroundColor: isDark ? colors.card : 'rgba(255, 255, 255, 0.9)' }]}
              onPress={() => router.back()}
              accessibilityLabel="Go back"
            >
              <Feather name="arrow-left" size={20} color={colors.text} />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.themeToggle, { backgroundColor: isDark ? colors.card : 'rgba(255, 255, 255, 0.9)' }]}
              onPress={toggleTheme}
              accessibilityLabel={isDark ? "Switch to light mode" : "Switch to dark mode"}
            >
              {isDark ? (
                <Feather name="sun" size={20} color={colors.text} />
              ) : (
                <Feather name="moon" size={20} color={colors.text} />
              )}
            </TouchableOpacity>
          </View>
          
          <Animated.View entering={FadeIn.duration(600)} style={styles.header}>
            <Animated.View entering={FadeInDown.delay(200).duration(600)}>
              <Text style={[styles.title, { color: isDark ? '#ffffff' : colors.text }]}>Create Account</Text>
              <Text style={[styles.subtitle, { color: isDark ? '#ffffffcc' : colors.textSecondary }]}>
                Start your journey to better nutrition with CalorieLens
              </Text>
            </Animated.View>
          </Animated.View>
          
          {error ? (
            <Animated.View 
              entering={FadeIn.duration(300)}
              style={[styles.errorContainer, { backgroundColor: colors.error + '15', borderColor: colors.error + '30', borderWidth: 1 }]}
            >
              <Text style={[styles.errorText, { color: colors.error }]}>{error}</Text>
            </Animated.View>
          ) : null}
          
          <Animated.View entering={FadeInDown.delay(300).duration(600)} style={styles.formCard}>
            {/* Social Sign In Buttons */}
            <View style={styles.socialButtonsContainer}>
              <AuthButton
                title="Continue with Google"
                onPress={handleGoogleSignIn}
                loading={socialLoading === 'google'}
                disabled={loading || socialLoading !== null}
                variant="outline"
                icon={<View style={styles.socialIconContainer}><Text style={{ fontSize: 16, fontWeight: 'bold', color: '#EA4335' }}>G</Text></View>}
                accessibilityLabel="Continue with Google"
              />
              
              <AuthButton
                title="Continue with Apple"
                onPress={handleAppleSignIn}
                loading={socialLoading === 'apple'}
                disabled={loading || socialLoading !== null}
                variant="outline"
                style={{ backgroundColor: '#000000', borderColor: '#000000', marginTop: 12 }}
                textStyle={{ color: '#ffffff' }}
                icon={<View style={styles.socialIconContainer}><Text style={{ fontSize: 16, fontWeight: 'bold', color: '#ffffff' }}>A</Text></View>}
                accessibilityLabel="Continue with Apple"
              />
            </View>
            
            {/* Divider */}
            <View style={styles.dividerContainer}>
              <View style={[styles.divider, { backgroundColor: isDark ? '#ffffff40' : colors.border }]} />
              <Text style={[styles.dividerText, { color: isDark ? '#ffffffcc' : colors.textSecondary }]}>or</Text>
              <View style={[styles.divider, { backgroundColor: isDark ? '#ffffff40' : colors.border }]} />
            </View>
            
            <View style={styles.form}>
              <AuthInput
                label="Full Name"
                icon={<Feather name="user" size={20} color={colors.textSecondary} />}
                placeholder="Your full name"
                value={fullName}
                onChangeText={setFullName}
                autoCapitalize="words"
                accessibilityLabel="Full name input"
              />
              
              <AuthInput
                label="Email"
                icon={<Feather name="mail" size={20} color={colors.textSecondary} />}
                placeholder="<EMAIL>"
                value={email}
                onChangeText={setEmail}
                autoCapitalize="none"
                keyboardType="email-address"
                accessibilityLabel="Email input"
              />
              
              <View>
                <AuthInput
                  label="Password"
                  icon={<Feather name="lock" size={20} color={colors.textSecondary} />}
                  placeholder="Create a password"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry
                  autoCapitalize="none"
                  onFocus={() => setPasswordFocus(true)}
                  onBlur={() => setPasswordFocus(false)}
                  accessibilityLabel="Password input"
                />
                
                {renderPasswordStrength()}
              </View>
              
              <AuthInput
                label="Confirm Password"
                icon={<Feather name="lock" size={20} color={colors.textSecondary} />}
                placeholder="Confirm your password"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry
                autoCapitalize="none"
                inputWrapperStyle={{
                  borderColor: confirmPassword.length > 0 
                    ? (passwordsMatch ? colors.success + '70' : colors.error + '70') 
                    : colors.border,
                }}
                error={confirmPassword.length > 0 && !passwordsMatch ? "Passwords don't match" : undefined}
                accessibilityLabel="Confirm password input"
              />
              
              <AuthButton
                title="Create Account"
                onPress={handleSignup}
                loading={loading}
                style={{ marginTop: 10 }}
                accessibilityLabel="Create account"
              />
              
              <Text style={[styles.termsText, { color: isDark ? '#ffffffaa' : colors.textSecondary }]}>
                By creating an account, you agree to our{' '}
                <Text style={[styles.termsLink, { color: colors.primary }]}>
                  Terms of Service
                </Text>{' '}
                and{' '}
                <Text style={[styles.termsLink, { color: colors.primary }]}>
                  Privacy Policy
                </Text>
              </Text>
            </View>
          </Animated.View>
          
          <Animated.View entering={FadeInDown.delay(600).duration(600)} style={styles.footer}>
            <Text style={[styles.footerText, { color: isDark ? '#ffffffcc' : colors.textSecondary }]}>
              Already have an account?{' '}
            </Text>
            <Link href="/(auth)/login" asChild>
              <TouchableOpacity 
                style={Platform.OS === 'web' ? { transform: [] } : undefined}
                accessibilityLabel="Go to sign in"
              >
                <Text style={[styles.footerLink, { color: isDark ? '#ffffff' : colors.primary }]}>
                  Sign In
                </Text>
              </TouchableOpacity>
            </Link>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>
    </AuthBackground>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    padding: 24,
    paddingBottom: 32,
  },
  topBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  header: {
    marginBottom: 36,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 2,
  },
  themeToggle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 2,
  },
  title: {
    fontSize: 36,
    fontWeight: '800',
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '500',
  },
  formCard: {
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
    backgroundColor: 'transparent',
  },
  errorContainer: {
    padding: 14,
    borderRadius: 12,
    marginBottom: 24,
  },
  errorText: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Social buttons styles
  socialButtonsContainer: {
    marginBottom: 20,
  },
  socialIconContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Divider styles
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  divider: {
    flex: 1,
    height: 1,
  },
  dividerText: {
    marginHorizontal: 12,
    fontSize: 14,
    fontWeight: '600',
  },
  form: {
    marginBottom: 24,
  },
  // Password strength
  passwordStrengthContainer: {
    marginTop: -10,
    marginBottom: 15,
    paddingHorizontal: 4,
  },
  strengthRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
  },
  strengthText: {
    marginLeft: 8,
    fontSize: 12,
    fontWeight: '500',
  },
  // Terms text
  termsText: {
    marginTop: 20,
    fontSize: 13,
    textAlign: 'center',
    lineHeight: 18,
  },
  termsLink: {
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 16,
    marginTop: 'auto',
    backgroundColor: 'transparent',
    zIndex: 10,
  },
  footerText: {
    fontSize: 15,
    fontWeight: '500',
  },
  footerLink: {
    fontSize: 15,
    fontWeight: '700',
  },
});