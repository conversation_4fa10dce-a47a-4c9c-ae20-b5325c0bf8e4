import React, { useState, useRef } from 'react';
import { StyleSheet, View, Text, Image, TouchableOpacity, Dimensions, Platform, FlatList } from 'react-native';
import { useRouter } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';

const { width, height } = Dimensions.get('window');

interface OnboardingPage {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
}

export default function WelcomeScreen() {
  const router = useRouter();
  const { colors, isDark } = useTheme();
  const insets = useSafeAreaInsets();
  const [currentPage, setCurrentPage] = useState(0);
  const [imageError, setImageError] = useState(false);
  
  const pages: OnboardingPage[] = [
    {
      id: '1',
      title: 'Welcome to\nHealthApp',
      description: 'Your personal health assistant for nutrition tracking and meal planning',
      imageUrl: 'https://images.pexels.com/photos/1435904/pexels-photo-1435904.jpeg',
    },
    {
      id: '2',
      title: 'Track Your Diet\nWith Precision',
      description: 'Snap a photo of your meal and get instant nutrition insights',
      imageUrl: 'https://images.pexels.com/photos/1640774/pexels-photo-1640774.jpeg',
    },
    {
      id: '3',
      title: 'Personalized\nRecommendations',
      description: 'Get meal plans and recipes tailored to your preferences and goals',
      imageUrl: 'https://images.pexels.com/photos/1640773/pexels-photo-1640773.jpeg',
    },
    {
      id: '4',
      title: 'Ready to Start\nYour Journey?',
      description: 'Join our community and start living a healthier lifestyle today',
      imageUrl: 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg',
    }
  ];

  const renderItem = ({ item, index }: { item: OnboardingPage; index: number }) => (
    <View style={styles.slide}>
      {/* Image container with shadow */}
      <View style={styles.imageWrapper}>
        <Image
          source={{ uri: item.imageUrl }}
          style={styles.image}
          resizeMode="cover"
          accessible={true}
          accessibilityLabel={`Onboarding image ${index + 1}`}
        />
      </View>
      
      {/* Text content */}
      <View style={styles.textContainer}>
        <Text style={styles.title} accessibilityRole="header">
          {item.title}
        </Text>
        <Text style={styles.description}>
          {item.description}
        </Text>
      </View>
    </View>
  );

  const flatListRef = useRef<FlatList<OnboardingPage>>(null);

  const handleNext = () => {
    if (currentPage < pages.length - 1) {
      setCurrentPage(currentPage + 1);
      flatListRef.current?.scrollToIndex({
        index: currentPage + 1,
        animated: true
      });
    } else {
      router.push('/(auth)/login');
    }
  };

  const onViewableItemsChanged = useRef(({ viewableItems }: { viewableItems: any[] }) => {
    if (viewableItems.length > 0) {
      setCurrentPage(viewableItems[0].index);
    }
  }).current;

  const viewabilityConfig = useRef({
    itemVisiblePercentThreshold: 50
  }).current;

  return (
    <View style={[styles.container, { backgroundColor: '#4d62e5' }]}>
      <StatusBar style="light" />
      
      {/* Skip button */}
      <TouchableOpacity 
        style={[styles.skipButton, { top: insets.top + 16, right: insets.right + 16 }]}
        onPress={() => router.push('/(auth)/login')}
        accessibilityRole="button"
        accessibilityLabel="Skip"
        accessibilityHint="Skip onboarding and go to login"
      >
        <Text style={styles.skipText}>Skip</Text>
      </TouchableOpacity>
      
      {/* Main content */}
      <View style={styles.content}>
        <FlatList
          ref={flatListRef}
          data={pages}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onViewableItemsChanged={onViewableItemsChanged}
          viewabilityConfig={viewabilityConfig}
        />
        
        {/* Pagination dots */}
        <View style={styles.paginationContainer}>
          {pages.map((_, index) => (
            <View 
              key={`dot-${index}`}
              style={[
                styles.paginationDot,
                currentPage === index && styles.paginationDotActive
              ]}
            />
          ))}
        </View>
        
        {/* Next button */}
        <TouchableOpacity
          style={styles.nextButton}
          onPress={handleNext}
          accessibilityRole="button"
          accessibilityLabel={currentPage === pages.length - 1 ? "Get Started" : "Next"}
          accessibilityHint={currentPage === pages.length - 1 ? "Complete onboarding and go to login" : "Go to next slide"}
        >
          <Text style={styles.nextButtonText}>
            {currentPage === pages.length - 1 ? "Get Started" : "Next"}
          </Text>
          <Feather name="arrow-right" size={20} color="#fff" style={styles.nextButtonIcon} />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  skipButton: {
    position: 'absolute',
    zIndex: 10,
    padding: 8,
  },
  skipText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingBottom: 40,
  },
  slide: {
    width,
    alignItems: 'center',
    paddingTop: height * 0.08,
  },
  imageWrapper: {
    width: width * 0.85,
    height: width * 0.85,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  textContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
    marginTop: 30,
    maxWidth: width * 0.9,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 24,
  },
  paginationContainer: {
    flexDirection: 'row',
    marginVertical: 30,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
  },
  paginationDotActive: {
    width: 20,
    backgroundColor: 'white',
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 30,
    minWidth: 200,
    marginBottom: 10,
  },
  nextButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  nextButtonIcon: {
    marginLeft: 4,
  },
});