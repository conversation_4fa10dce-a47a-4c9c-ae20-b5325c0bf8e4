import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ScrollView, TextInput, Platform } from 'react-native';
import { useRouter } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { updateDietaryPreferences } from '@/services/databaseService';

export default function DietaryPreferencesScreen() {
  const router = useRouter();
  const { colors } = useTheme();
  const [loading, setLoading] = useState(false);
  
  // Dietary preferences
  const [dietType, setDietType] = useState('balanced');
  const [allergies, setAllergies] = useState<string[]>([]);
  const [excludedFoods, setExcludedFoods] = useState<string[]>([]);
  const [newItem, setNewItem] = useState('');
  const [itemType, setItemType] = useState<'allergy' | 'excluded' | null>(null);
  
  const dietTypes = [
    { id: 'balanced', label: 'Balanced', description: 'A balanced mix of proteins, carbs, and fats' },
    { id: 'low_carb', label: 'Low Carb', description: 'Reduced carbohydrate intake' },
    { id: 'high_protein', label: 'High Protein', description: 'Focus on protein-rich foods' },
    { id: 'vegetarian', label: 'Vegetarian', description: 'No meat, may include dairy and eggs' },
    { id: 'vegan', label: 'Vegan', description: 'No animal products' },
    { id: 'keto', label: 'Keto', description: 'High fat, very low carb' },
    { id: 'paleo', label: 'Paleo', description: 'Based on foods presumed available to paleolithic humans' },
  ];
  
  const handleAddItem = () => {
    if (!newItem.trim() || !itemType) return;
    
    if (itemType === 'allergy') {
      if (!allergies.includes(newItem.trim())) {
        setAllergies([...allergies, newItem.trim()]);
      }
    } else {
      if (!excludedFoods.includes(newItem.trim())) {
        setExcludedFoods([...excludedFoods, newItem.trim()]);
      }
    }
    
    setNewItem('');
    setItemType(null);
  };
  
  const removeAllergy = (allergy: string) => {
    setAllergies(allergies.filter(a => a !== allergy));
  };
  
  const removeExcludedFood = (food: string) => {
    setExcludedFoods(excludedFoods.filter(f => f !== food));
  };
  
  const saveDietaryPreferences = async () => {
    setLoading(true);
    try {
      const result = await updateDietaryPreferences({
        diet_type: dietType,
        allergies,
        excluded_foods: excludedFoods
      });
      
      if (result.success) {
        router.replace('/(tabs)');
      } else {
        console.error('Failed to update dietary preferences:', result.error);
        // In a real app, you'd show an error message
      }
    } catch (error) {
      console.error('Error saving dietary preferences:', error);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>Dietary Preferences</Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Tell us about your dietary preferences to get personalized recommendations
        </Text>
      </View>
      
      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Diet Type</Text>
        <View style={styles.dietTypesContainer}>
          {dietTypes.map((diet) => (
            <TouchableOpacity
              key={diet.id}
              style={[
                styles.dietTypeOption,
                { 
                  borderColor: dietType === diet.id ? colors.primary : colors.border,
                  backgroundColor: dietType === diet.id ? colors.primaryLight : colors.subtle
                }
              ]}
              onPress={() => setDietType(diet.id)}
            >
              <View style={styles.dietTypeHeader}>
                <Text style={[
                  styles.dietTypeLabel, 
                  { 
                    color: dietType === diet.id ? colors.primary : colors.text,
                    fontWeight: dietType === diet.id ? '700' : '600'
                  }
                ]}>
                  {diet.label}
                </Text>
                {dietType === diet.id && (
                  <View style={[styles.checkMark, { backgroundColor: colors.primary }]}>
                    <Feather name="check" size={14}  color={colors.text} />
                  </View>
                )}
              </View>
              <Text style={[
                styles.dietTypeDescription, 
                { color: dietType === diet.id ? colors.primary : colors.textSecondary }
              ]}>
                {diet.description}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        
        <Text style={[styles.sectionTitle, { color: colors.text, marginTop: 24 }]}>Allergies</Text>
        <View style={styles.tagsContainer}>
          {allergies.length > 0 ? (
            allergies.map((allergy, index) => (
              <View 
                key={index} 
                style={[styles.tag, { backgroundColor: colors.error + '20', borderColor: colors.error }]}
              >
                <Text style={[styles.tagText, { color: colors.error }]}>{allergy}</Text>
                <TouchableOpacity 
                  style={styles.removeTag}
                  onPress={() => removeAllergy(allergy)}
                >
                  <Feather name="x" size={14} color={colors.error} />
                </TouchableOpacity>
              </View>
            ))
          ) : (
            <View style={[styles.emptyState, { borderColor: colors.border }]}>
              <Feather name="alert-circle" size={20} color={colors.textSecondary} style={styles.emptyIcon} />
              <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                No allergies added
              </Text>
            </View>
          )}
          <TouchableOpacity 
            style={[styles.addButton, { backgroundColor: colors.subtle, borderColor: colors.border }]}
            onPress={() => {
              setItemType('allergy');
              setNewItem('');
            }}
          >
            <Feather name="plus" size={16} color={colors.text} style={styles.addIcon} />
            <Text style={[styles.addText, { color: colors.text }]}>Add Allergy</Text>
          </TouchableOpacity>
        </View>
        
        <Text style={[styles.sectionTitle, { color: colors.text, marginTop: 24 }]}>Foods to Avoid</Text>
        <View style={styles.tagsContainer}>
          {excludedFoods.length > 0 ? (
            excludedFoods.map((food, index) => (
              <View 
                key={index} 
                style={[styles.tag, { backgroundColor: colors.subtle, borderColor: colors.border }]}
              >
                <Text style={[styles.tagText, { color: colors.text }]}>{food}</Text>
                <TouchableOpacity 
                  style={styles.removeTag}
                  onPress={() => removeExcludedFood(food)}
                >
                  <Feather name="x" size={14} color={colors.textSecondary} />
                </TouchableOpacity>
              </View>
            ))
          ) : (
            <View style={[styles.emptyState, { borderColor: colors.border }]}>
              <Feather name="alert-circle" size={20} color={colors.textSecondary} style={styles.emptyIcon} />
              <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                No excluded foods added
              </Text>
            </View>
          )}
          <TouchableOpacity 
            style={[styles.addButton, { backgroundColor: colors.subtle, borderColor: colors.border }]}
            onPress={() => {
              setItemType('excluded');
              setNewItem('');
            }}
          >
            <Feather name="plus" size={16} color={colors.text} style={styles.addIcon} />
            <Text style={[styles.addText, { color: colors.text }]}>Add Food to Avoid</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
      
      {itemType && (
        <View style={[styles.modal, { backgroundColor: colors.overlay }]}>
          <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              {itemType === 'allergy' ? 'Add Allergy' : 'Add Food to Avoid'}
            </Text>
            <TextInput
              style={[styles.modalInput, { 
                backgroundColor: colors.subtle, 
                color: colors.text,
                borderColor: colors.border
              }]}
              placeholder={itemType === 'allergy' ? 'Enter allergy' : 'Enter food to avoid'}
              placeholderTextColor={colors.textSecondary}
              value={newItem}
              onChangeText={setNewItem}
              autoFocus
              returnKeyType="done"
              onSubmitEditing={handleAddItem}
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity 
                style={[styles.modalButton, styles.cancelButton, { borderColor: colors.border }]}
                onPress={() => setItemType(null)}
              >
                <Text style={[styles.cancelButtonText, { color: colors.text }]}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[
                  styles.modalButton, 
                  styles.addItemButton, 
                  { 
                    backgroundColor: newItem.trim() ? colors.primary : colors.primaryLight,
                    opacity: newItem.trim() ? 1 : 0.7
                  }
                ]}
                onPress={handleAddItem}
                disabled={!newItem.trim()}
              >
                <Text style={styles.addItemButtonText}>Add</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
      
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.completeButton,
            { 
              backgroundColor: colors.primary,
              opacity: loading ? 0.7 : 1
            }
          ]}
          onPress={saveDietaryPreferences}
          disabled={loading}
        >
          <Text style={styles.completeButtonText}>
            Complete Setup
          </Text>
          <Feather name="arrow-right" size={20}  color={colors.text} />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: '800',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 100,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  dietTypesContainer: {
    marginTop: 4,
  },
  dietTypeOption: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  dietTypeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  dietTypeLabel: {
    fontSize: 16,
  },
  checkMark: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dietTypeDescription: {
    fontSize: 14,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 12,
    borderWidth: 1,
  },
  tagText: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 6,
  },
  removeTag: {
    width: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 12,
    borderWidth: 1,
  },
  addIcon: {
    marginRight: 4,
  },
  addText: {
    fontSize: 14,
    fontWeight: '600',
  },
  emptyState: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: 12,
    marginBottom: 16,
    width: '100%',
  },
  emptyIcon: {
    marginRight: 8,
  },
  emptyText: {
    fontSize: 14,
    fontWeight: '500',
  },
  modal: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  modalContent: {
    width: '100%',
    borderRadius: 16,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  modalInput: {
    borderWidth: 1,
    borderRadius: 12,
    height: 56,
    paddingHorizontal: 16,
    fontSize: 16,
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    height: 50,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    borderWidth: 1,
    marginRight: 12,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  addItemButton: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  addItemButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 24,
    backgroundColor: 'transparent',
  },
  completeButton: {
    flexDirection: 'row',
    height: 56,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  completeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
    marginRight: 8,
  },
});