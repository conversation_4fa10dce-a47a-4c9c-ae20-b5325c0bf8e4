import React, { useState, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  Dimensions,
  Image,
  Animated,
  TouchableOpacity,
  Platform,
  useWindowDimensions,
  ScrollView,
  Pressable,
  ImageBackground,
} from 'react-native';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons , Feather } from '@expo/vector-icons';
import { useAccessibility } from '@/components/AccessibilityProvider';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Key for storing onboarding completion status
const ONBOARDING_COMPLETE_KEY = 'onboarding_complete';

// Onboarding screens data with enhanced visuals
const ONBOARDING_SCREENS = [
  {
    id: 'welcome',
    title: 'Welcome to\nChoose Healthy',
    description: 'Your advanced food analyzer and nutrition assistant powered by AI',
    image: { uri: 'https://images.pexels.com/photos/1132047/pexels-photo-1132047.jpeg' },
    accessibilityLabel: 'Colorful array of fresh fruits on a table',
  },
  {
    id: 'analyzer',
    title: 'Advanced Food\nAnalyzer',
    description: 'Use your camera with LiDAR technology to scan and analyze your meals with precision',
    image: { uri: 'https://images.pexels.com/photos/5386754/pexels-photo-5386754.jpeg' },
    accessibilityLabel: 'Person using smartphone to analyze food with advanced camera technology',
  },
  {
    id: 'recipes',
    title: 'Instant Recipe\nGeneration',
    description: 'Take a photo of ingredients and get AI-generated recipe ideas tailored to your preferences',
    image: { uri: 'https://images.pexels.com/photos/6248968/pexels-photo-6248968.jpeg' },
    accessibilityLabel: 'Fresh ingredients and a recipe being prepared',
  },
  {
    id: 'alternatives',
    title: 'Healthier\nAlternatives',
    description: 'Our AI suggests healthier versions of your favorite meals based on your dietary goals',
    image: { uri: 'https://images.pexels.com/photos/1640774/pexels-photo-1640774.jpeg' },
    accessibilityLabel: 'Healthy meal alternatives with fresh vegetables and nutritious options',
  },
];

export default function OnboardingWelcomeScreen() {
  const { width } = useWindowDimensions();
  const insets = useSafeAreaInsets();
  const scrollViewRef = useRef<ScrollView>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollX = useRef(new Animated.Value(0)).current;
  const { isReduceMotionEnabled, isScreenReaderEnabled, announceForAccessibility } = useAccessibility();

  // Handle screen change
  const handleScreenChange = useCallback((index: number) => {
    if (isScreenReaderEnabled) {
      const screen = ONBOARDING_SCREENS[index];
      announceForAccessibility(`${screen.title}. ${screen.description}`);
    }
    
    setCurrentIndex(index);
  }, [isScreenReaderEnabled, announceForAccessibility]);

  // Handle scroll event
  const handleScroll = Animated.event(
    [{ nativeEvent: { contentOffset: { x: scrollX } } }],
    { 
      useNativeDriver: false,
      listener: (event: any) => {
        const scrollPosition = event.nativeEvent.contentOffset.x;
        const index = Math.round(scrollPosition / width);
        if (index !== currentIndex) {
          handleScreenChange(index);
        }
      },
    }
  );

  // Handle pressing Next button
  const handleNext = useCallback(() => {
    if (currentIndex < ONBOARDING_SCREENS.length - 1) {
      const nextIndex = currentIndex + 1;
      
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollTo({ x: width * nextIndex, animated: !isReduceMotionEnabled });
      }
      
      handleScreenChange(nextIndex);
    } else {
      handleComplete();
    }
  }, [currentIndex, width, isReduceMotionEnabled, handleScreenChange]);

  // Handle pressing Skip button
  const handleSkip = useCallback(() => {
    handleComplete();
  }, []);

  // Mark onboarding as complete and navigate to main app
  const handleComplete = async () => {
    try {
      await AsyncStorage.setItem(ONBOARDING_COMPLETE_KEY, 'true');
    } catch (error) {
      console.warn('Failed to save onboarding status', error);
    }
    
    // Navigate to the main app
    router.replace('/(tabs)');
  };

  // Render individual onboarding screen
  const renderScreen = ({ item, index }: { item: typeof ONBOARDING_SCREENS[0]; index: number }) => {
    return (
      <View style={[styles.screenContainer, { width }]} key={item.id}>
        {/* Image container with shadow */}
        <View style={styles.imageWrapper}>
          <Image 
            source={item.image} 
            style={styles.image} 
            resizeMode="cover"
            accessible={true}
            accessibilityLabel={item.accessibilityLabel}
          />
        </View>
        
        {/* Text content */}
        <View style={styles.textContainer}>
          <Text style={styles.title} accessibilityRole="header">
            {item.title}
          </Text>
          <Text style={styles.description}>
            {item.description}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <ImageBackground 
      source={{ uri: 'https://images.pexels.com/photos/1640773/pexels-photo-1640773.jpeg' }}
      style={styles.container}
    >
      <View style={styles.overlay} />
      <StatusBar style="light" />
      
      {/* Skip button (top right) */}
      <TouchableOpacity 
        style={[styles.skipButton, { top: insets.top + 16, right: 16 }]} 
        onPress={handleSkip}
        accessibilityLabel="Skip onboarding"
        accessibilityHint="Double tap to skip the introduction and go to the main app"
        accessibilityRole="button"
      >
        <Text style={styles.skipText}>Skip</Text>
      </TouchableOpacity>
      
      {/* Scrollable content */}
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        decelerationRate="fast"
        accessible={false}
        style={styles.scrollView}
      >
        {ONBOARDING_SCREENS.map((screen, index) => (
          renderScreen({ item: screen, index })
        ))}
      </ScrollView>
      
      {/* Bottom controls */}
      <SafeAreaView style={styles.controlsContainer}>
        {/* Pagination dots */}
        <View style={styles.indicatorContainer}>
          {ONBOARDING_SCREENS.map((_, index) => (
            <View 
              key={`dot-${index}`} 
              style={[
                styles.indicator,
                currentIndex === index && styles.indicatorActive
              ]}
            />
          ))}
        </View>
        
        {/* Next button */}
        <TouchableOpacity 
          style={styles.button}
          onPress={handleNext}
          accessibilityLabel={currentIndex === ONBOARDING_SCREENS.length - 1 ? 'Get Started' : 'Next'}
          accessibilityHint={currentIndex === ONBOARDING_SCREENS.length - 1 ? 'Finish onboarding and go to the app' : 'Go to the next screen'}
          accessibilityRole="button"
        >
          <Text style={styles.buttonText}>
            {currentIndex === ONBOARDING_SCREENS.length - 1 ? 'Get Started' : 'Next'}
          </Text>
          <Feather name="arrow-right" size={20} style={styles.buttonIcon} />
        </TouchableOpacity>
      </SafeAreaView>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 50, 0.7)',
  },
  scrollView: {
    flex: 1,
  },
  screenContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 80,
  },
  imageWrapper: {
    width: '85%',
    aspectRatio: 1,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    padding: 3,
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 18,
  },
  textContainer: {
    width: '100%',
    paddingHorizontal: 40,
    marginTop: 40,
    alignItems: 'center',
    zIndex: 1,
  },
  title: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 16,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  description: {
    fontSize: 18,
    color: 'rgba(255,255,255,0.95)',
    textAlign: 'center',
    lineHeight: 26,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  controlsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
    alignItems: 'center',
    zIndex: 1,
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
  },
  indicator: {
    height: 8,
    width: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255,255,255,0.4)',
    marginHorizontal: 6,
  },
  indicatorActive: {
    width: 20,
    backgroundColor: 'white',
  },
  skipButton: {
    position: 'absolute',
    zIndex: 10,
    padding: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  skipText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 30,
    minWidth: 200,
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    marginRight: 8,
  },
  buttonIcon: {
    marginLeft: 4,
  },
}); 