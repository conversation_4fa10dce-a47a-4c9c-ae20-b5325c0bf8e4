import React, { useState, useRef, useEffect, useCallback } from 'react';
import { StyleSheet, View, Text, Platform, ScrollView, TouchableOpacity, Animated, Alert, SafeAreaView, StatusBar } from 'react-native';
import { Stack } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';

// Import hooks and services
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { useSafeNavigation } from '@/hooks/useSafeNavigation';
import { useScanFunctions } from '@/hooks/useScanFunctions';
import { generateAlternativeRecipe, setSpecialUserStatus } from '@/services/openaiService';
import { getDietaryPreferences, getProfile, createMeal, createAlternativeRecipe } from '@/services/databaseService';
import { checkSubscription } from '@/services/stripeService';
import { suggestNextMeal } from '@/utils/mealUtils';
import { getNutritionForDish } from '@/services/nutritionService';

// Import components
import { 
  CameraViewWrapper, 
  useCameraPermissions,
  ScanHeader,
  ScanOptionsModal,
  ScanOptionsView,
  RecentScans,
  ScanLoading
} from '@/components/ScanComponents';
import { FoodAnalysisResult } from '@/components/FoodAnalysisResult';
import { AlternativeRecipeView } from '@/components/AlternativeRecipeView';
import { NoScanView } from '@/components/NoScanView';
import { NoPermissionView } from '@/components/NoPermissionView';
import { PremiumFeature } from '@/components/PremiumFeature';
import FoodAnalysisWithSegmentation from '@/components/FoodAnalysisWithSegmentation';
import { MultiItemFoodDisplay } from '@/components/ScanComponents';

// Import types
import { CameraDirection, ScanMode, RecentScan, BarcodeScanResult } from '@/types/scan.types';
import * as MediaLibrary from 'expo-media-library';

// Debug mode for development features
const DEBUG_MODE = __DEV__; // True when running in development mode

// Define CameraType for compatibility
const CameraType = {
  front: 'front' as CameraDirection,
  back: 'back' as CameraDirection
};

export default function ScanScreen() {
  const { colors, isDark } = useTheme();
  const { user } = useAuth();
  const { safeGoBack } = useSafeNavigation();
  
  // Use our custom hook for scan functions
  const {
    loading,
    setLoading,
    error,
    setError,
    imageUri,
    setImageUri,
    analysisResult,
    setAnalysisResult,
    photoPermissionDenied,
    setPhotoPermissionDenied,
    loadRecentScans,
    saveRecentScan,
    handleOpenGallery: openGallery,
    analyzeImage,
    clearImageCache
  } = useScanFunctions();
  
  // Camera permissions and state
  const [permission, requestPermission] = useCameraPermissions();
  const [cameraType, setCameraType] = useState<CameraDirection>('back');
  const [showCamera, setShowCamera] = useState(false);
  
  // UI states
  const [alternativeRecipes, setAlternativeRecipes] = useState<any[] | null>(null);
  const [selectedRecipeIndex, setSelectedRecipeIndex] = useState<number>(0);
  const [showAlternative, setShowAlternative] = useState(false);
  const [hideRecentScans, setHideRecentScans] = useState(false);
  const [dailyCalorieGoal, setDailyCalorieGoal] = useState(2000);
  const [hasSubscription, setHasSubscription] = useState(false);
  const [checkingSubscription, setCheckingSubscription] = useState(true);
  const [suggestedMeal, setSuggestedMeal] = useState(suggestNextMeal());
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [recentScans, setRecentScans] = useState<RecentScan[]>([]);
  const [scanOptionsVisible, setScanOptionsVisible] = useState(false);
  
  // Add new state variables for enhanced functionality
  const [scanMode, setScanMode] = useState<ScanMode>('food');
  const [scannedBarcode, setScannedBarcode] = useState<BarcodeScanResult | null>(null);
  const [productInfo, setProductInfo] = useState<any>(null);
  const [multiItemMode, setMultiItemMode] = useState(false);
  const [detectedItems, setDetectedItems] = useState<{ name: string; confidence: number }[]>([]);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const headerAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  
  // Refs
  const isMounted = useRef(true);
  
  // Compute derived values for animations
  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });
  
  // Start animations
  useEffect(() => {
    const startPulseAnimation = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: Platform.OS !== 'web',
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: Platform.OS !== 'web',
        })
      ]).start(() => {
        // Restart the animation
        startPulseAnimation();
      });
    };
    
    startPulseAnimation();
    
    // Start rotate animation for the sparkles icon
    Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 4000,
        useNativeDriver: Platform.OS !== 'web',
      })
    ).start();
    
    // Animate header appearance
    Animated.timing(headerAnim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: false,
    }).start();
    
  }, []);
  
  // Fetch user profile data and subscription status
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const profile = await getProfile();
        if (profile && profile.daily_calorie_goal) {
          setDailyCalorieGoal(profile.daily_calorie_goal);
        }
        
        // Check subscription status
        const subscriptionResult = await checkSubscription();
        
        // Enable premium features for specific email
        if (user?.email === '<EMAIL>') {
          setHasSubscription(true);
          // Enable special recipe generation for this user
          setSpecialUserStatus(user.email);
          console.log('Premium access granted for specified user');
        } else {
          setHasSubscription(!!subscriptionResult?.subscription);
          setSpecialUserStatus(null);
        }
        setCheckingSubscription(false);
      } catch (error) {
        console.error('Error fetching user data:', error);
        setSpecialUserStatus(null);
        setCheckingSubscription(false);
      }
    };
    
    if (user) {
      fetchUserData();
    } else {
      setCheckingSubscription(false);
      setSpecialUserStatus(null);
    }
    
    // Update suggested meal every minute
    const intervalId = setInterval(() => {
      setSuggestedMeal(suggestNextMeal());
    }, 60000);
    
    return () => clearInterval(intervalId);
  }, [user]);
  
  // Load recent scans
  useEffect(() => {
    // Request camera permissions
    (async () => {
      // Check for permissions
      const { status } = await requestMediaLibraryPermissionsAsync();
      setHasPermission(status === 'granted');
      
      // Load recent scans
      const scans = await loadRecentScans();
      setRecentScans(scans);
    })();
  }, [loadRecentScans]);
  
  // Request media library permissions
  const requestMediaLibraryPermissionsAsync = async () => {
    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      return { status };
    } catch (error) {
      console.error('Error requesting media library permissions:', error);
      return { status: 'undetermined' };
    }
  };
  
  // Handle opening gallery
  const handleOpenGallery = async () => {
    setShowCamera(false);
    setLoading(true);
    
    // Use the hook's gallery functionality with explicit user request flag
    const selectedUri = await openGallery(true);
    if (selectedUri) {
      setImageUri(selectedUri);
      await analyzeImage(selectedUri);
      
      // Save to recent scans after analysis
      if (analysisResult?.data) {
        await handleSaveToRecentScans(selectedUri, analysisResult.data);
      }
    } else {
      // Reset loading state if no image selected
      setLoading(false);
    }
  };
  
  // Handle opening camera
  const handleOpenCamera = async () => {
    if (typeof permission === 'object' && permission !== null && !permission.granted) {
      if (typeof requestPermission === 'function') {
        const permissionResult = await requestPermission();
        if (!permissionResult.granted) {
          setError("Camera permission is required to take photos");
          return;
        }
      } else {
        setError("Cannot request camera permission");
        return;
      }
    }
    
    // Clear previous data
    setImageUri('');
    setAnalysisResult(null);
    setAlternativeRecipes(null);
    setShowAlternative(false);
    setError(null);
    
    // Set camera UI state to true, but don't automatically launch camera or file picker
    // Just show the camera UI within the app
    setShowCamera(true);
  };
  
  // Add code to handle saving recent scans after analyzing image
  const handleSaveToRecentScans = async (uri: string, analysisData: any) => {
    if (!uri || !analysisData) return;
    
    try {
      const newScan = {
        id: Date.now().toString(),
        imageUri: uri,
        foodName: analysisData.name || 'Unknown Food',
        timestamp: Date.now(),
        calories: analysisData.calories || 0
      };
      
      await saveRecentScan(newScan);
      console.log('Saved to recent scans:', newScan.foodName);
      
      // Refresh the list
      const updatedScans = await loadRecentScans();
      setRecentScans(updatedScans);
      console.log('Recent scans updated, count:', updatedScans.length);
    } catch (error) {
      console.error('Error saving to recent scans:', error);
    }
  };
  
  // Update handleImageCaptured to save to recent scans
  const handleImageCaptured = async (uri: string) => {
    setImageUri(uri);
    setShowCamera(false);
    
    // Process the image
    if (scanMode === 'food') {
      if (multiItemMode) {
        try {
          await handleMultiItemDetection(uri);
          // Save to recent scans after successful analysis
          if (analysisResult?.data) {
            await handleSaveToRecentScans(uri, analysisResult.data);
          }
        } catch (error) {
          console.error('Error in multi-item detection:', error);
          setError('Could not analyze multiple items. Trying standard analysis...');
          const result = await analyzeImage(uri);
          // Save to recent scans after standard analysis
          if (result?.data) {
            await handleSaveToRecentScans(uri, result.data);
          }
        }
      } else {
        const result = await analyzeImage(uri);
        // Save to recent scans after analysis
        if (result?.data) {
          await handleSaveToRecentScans(uri, result.data);
        }
      }
    }
  };
  
  // Handle barcode scanning
  const handleBarcodeScanned = async ({ type, data }: BarcodeScanResult) => {
    setScannedBarcode({ type, data });
    setShowCamera(false);
    setLoading(true);
    
    try {
      // Call a food database API (example: Open Food Facts)
      const response = await fetch(`https://world.openfoodfacts.org/api/v0/product/${data}.json`);
      const productData = await response.json() as {
        status: number;
        product: {
          product_name: string;
          image_url: string;
          nutriments: {
            energy_value?: number;
            proteins?: number;
            carbohydrates?: number;
            fat?: number;
          }
        }
      };
      
      if (productData.status === 1) {
        // Product found
        setProductInfo(productData.product);
        
        // Create analysis result in the same format as image analysis
        const nutritionData = productData.product.nutriments || {};
        
        const analysisData = {
          success: true,
          data: {
            name: productData.product.product_name || 'Unknown Product',
            calories: nutritionData.energy_value || 0,
            protein: nutritionData.proteins || 0,
            carbs: nutritionData.carbohydrates || 0,
            fat: nutritionData.fat || 0,
            items: [{
              name: productData.product.product_name || 'Unknown Product',
              calories: nutritionData.energy_value || 0,
              protein: nutritionData.proteins || 0,
              carbs: nutritionData.carbohydrates || 0,
              fat: nutritionData.fat || 0,
            }],
            image_url: productData.product.image_url
          }
        };
        
        setAnalysisResult(analysisData);
        setImageUri(productData.product.image_url);
      } else {
        // Product not found
        setError('Product not found in the database. Try scanning the food instead.');
      }
    } catch (error) {
      console.error('Error fetching product data:', error);
      setError('Error scanning barcode. Please try again or scan the food directly.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle multi-item detection
  const handleMultiItemDetection = async (uri: string) => {
    setLoading(true);
    try {
      // Get food bounding boxes and segmentation from vision API
      const { getFoodBoundingBoxes } = await import('@/services/vision/enhancedVisionService');
      const boundingBoxResult = await getFoodBoundingBoxes(uri);
      
      if (!boundingBoxResult.success) {
        throw new Error(boundingBoxResult.error || 'Failed to detect food items');
      }
      
      // Define food item interface
      interface FoodItem {
        id: string;
        name: string;
        confidence: number;
        calories: number;
        protein?: number;
        carbs?: number;
        fat?: number;
        boundingBox?: any;
        maskUri?: string;
      }
      
      // Combine food items and segments to get the most comprehensive data
      const detectedBoxes = boundingBoxResult.foodItems || [];
      const detectedSegments = boundingBoxResult.segments || [];
      
      // If no items detected, fallback to standard analysis
      if (detectedBoxes.length === 0 && detectedSegments.length === 0) {
        console.log('No multiple items detected, falling back to standard analysis');
        return await analyzeImage(uri);
      }
      
      // Process using enhanced food analysis to get nutritional data
      const { enhancedFoodAnalysis } = await import('@/services/vision/enhancedVisionService');
      const analysisResult = await enhancedFoodAnalysis(uri);
      
      // Create food items with unique IDs, combining data from multiple sources
      const foodItems: FoodItem[] = [];
      
      // Process bounding box items
      if (detectedBoxes.length > 0) {
        detectedBoxes.forEach((item, index) => {
          foodItems.push({
            id: `box-${index}`,
            name: item.name,
            confidence: item.score,
            calories: 0, // Placeholder, will be updated with nutritional info
            boundingBox: item.boundingBox
          });
        });
      }
      
      // Add segmentation data
      if (detectedSegments.length > 0) {
        detectedSegments.forEach((segment, index) => {
          // Skip if we already have this item from the bounding boxes
          const existingItem = foodItems.find(
            item => item.name.toLowerCase() === segment.labeledAs?.toLowerCase()
          );
          
          if (existingItem) {
            // Update existing item with mask info
            existingItem.maskUri = segment.maskUri;
          } else {
            // Add as new item
            foodItems.push({
              id: `segment-${index}`,
              name: segment.labeledAs || `Food item ${index + 1}`,
              confidence: segment.confidence || 0.7,
              calories: 0,
              boundingBox: segment.boundingBox,
              maskUri: segment.maskUri
            });
          }
        });
      }
      
      // Enrich with nutritional data if available
      if (analysisResult.success) {
        // Try to match nutritional data with detected items
        const nutritionalItems = (analysisResult as any).detailedFoodInfo || [];
        
        foodItems.forEach(item => {
          // Find matching nutritional data
          const matchingNutrition = nutritionalItems.find(
            nuItem => nuItem.specificDish.toLowerCase() === item.name.toLowerCase()
          );
          
          if (matchingNutrition) {
            item.calories = matchingNutrition.nutrition.calories || 0;
            item.protein = matchingNutrition.nutrition.protein || 0;
            item.carbs = matchingNutrition.nutrition.carbs || 0;
            item.fat = matchingNutrition.nutrition.fat || 0;
          }
        });
        
        // Set analysis result with all detected items
        setAnalysisResult({
          success: true,
          data: {
            name: `Multiple food items (${foodItems.length})`,
            calories: foodItems.reduce((sum, item) => sum + (item.calories || 0), 0),
            protein: foodItems.reduce((sum, item) => sum + (item.protein || 0), 0),
            carbs: foodItems.reduce((sum, item) => sum + (item.carbs || 0), 0),
            fat: foodItems.reduce((sum, item) => sum + (item.fat || 0), 0),
            items: foodItems,
            multiItem: true, // Flag to indicate this is a multi-item result
            imageUri: uri
          }
        });
      } else {
        throw new Error('Failed to analyze food items');
      }
      
      // Update state with detected items
      setDetectedItems(foodItems);
      
      // Select highest confidence item by default
      if (foodItems.length > 0) {
        const highestConfidenceItem = [...foodItems].sort((a, b) => 
          (b.confidence || 0) - (a.confidence || 0)
        )[0];
        setSelectedItems([highestConfidenceItem.id]);
      }
      
    } catch (error) {
      console.error('Error in multi-item detection:', error);
      setError('Error detecting multiple items. Falling back to standard analysis...');
      
      // Fallback to standard analysis
      await analyzeImage(uri);
    } finally {
      setLoading(false);
    }
  };
  
  // Handle selection of individual food item from multi-item display
  const handleFoodItemSelect = (item: any) => {
    if (!analysisResult?.data?.items) return;
    
    // Update analysis result to show only the selected item
    const selectedItemData = {
      ...analysisResult,
      data: {
        ...analysisResult.data,
        name: item.name,
        calories: item.calories || 0,
        protein: item.protein || 0,
        carbs: item.carbs || 0,
        fat: item.fat || 0,
        selectedItemId: item.id, // Remember which item was selected
        // Keep the original items array for reference
      }
    };
    
    setAnalysisResult(selectedItemData);
  };
  
  // Handle combining multiple food items
  const handleCombineItems = (items: any[]) => {
    if (!analysisResult?.data?.items || items.length < 2) return;
    
    // Create a combined item
    const combinedName = items.map(item => item.name).join(' & ');
    const totalCalories = items.reduce((sum, item) => sum + (item.calories || 0), 0);
    const totalProtein = items.reduce((sum, item) => sum + (item.protein || 0), 0);
    const totalCarbs = items.reduce((sum, item) => sum + (item.carbs || 0), 0);
    const totalFat = items.reduce((sum, item) => sum + (item.fat || 0), 0);
    
    // Update analysis result with combined data
    const combinedItemData = {
      ...analysisResult,
      data: {
        ...analysisResult.data,
        name: combinedName,
        calories: totalCalories,
        protein: totalProtein,
        carbs: totalCarbs,
        fat: totalFat,
        // Create a new combined item but keep original items
        combinedItemIds: items.map(item => item.id)
      }
    };
    
    setAnalysisResult(combinedItemData);
  };
  
  // Generate alternative recipe
  const handleGenerateAlternative = async () => {
    if (!analysisResult) {
      console.error('Cannot generate alternative recipe: No analysis result available');
      return;
    }
    
    console.log('Starting generation of alternative recipe');
    setLoading(true);
    setError(null);
    
    try {
      // Get data using same pattern as handleSaveMeal to handle nested structure
      const mealData = analysisResult.data || analysisResult;
      
      console.log('Generating alternative recipe for:', mealData.name);
      
      // Create hardcoded fallback recipes for immediate testing/development
      const fallbackRecipes = [
        {
          name: `Lower-Calorie ${mealData.name}`,
          focus: "Lower Calorie",
          description: `A lighter version of ${mealData.name} with fewer calories but the same great taste.`,
          ingredients: [
            { name: "Main Ingredient (reduced portion)", amount: "75g", calories: Math.round(mealData.calories * 0.4), protein: Math.round(mealData.protein * 0.4), carbs: Math.round(mealData.carbs * 0.3), fat: Math.round(mealData.fat * 0.3) },
            { name: "Vegetables", amount: "1 cup", calories: 50, protein: 2, carbs: 10, fat: 0 },
            { name: "Herbs and Spices", amount: "to taste", calories: 5, protein: 0, carbs: 1, fat: 0 }
          ],
          instructions: [
            "Reduce portion size of main ingredients",
            "Add more vegetables for volume",
            "Use herbs and spices to maintain flavor"
          ],
          nutritionalInfo: {
            calories: Math.round(mealData.calories * 0.7),
            protein: Math.round(mealData.protein * 0.9),
            carbs: Math.round(mealData.carbs * 0.6),
            fat: Math.round(mealData.fat * 0.6)
          },
          preparationTime: "15 minutes",
          cookingTime: "20 minutes",
          healthBenefits: [
            "Reduced calories while maintaining nutrition",
            "Added fiber from vegetables"
          ],
          imagePrompt: `A lighter, healthier version of ${mealData.name} with more vegetables.`
        }
      ];
      
      // For web platform use a direct approach without animations
      if (Platform.OS === 'web') {
        setAlternativeRecipes(fallbackRecipes);
        setSelectedRecipeIndex(0);
        await new Promise(resolve => setTimeout(resolve, 50)); // Force a short wait
        setShowAlternative(true);
        setLoading(false);
        return;
      }
      
      // Check if we're using the special user email
      if (user?.email === '<EMAIL>') {
        setAlternativeRecipes(fallbackRecipes);
        setSelectedRecipeIndex(0);
        
        // Animate transition
        animateTransition(() => {
          setShowAlternative(true);
        });
        
        setLoading(false);
        return;
      }
      
      // Get user dietary preferences
      let dietaryPrefs: { diet_type?: string; allergies?: string[] } | null = null;
      try {
        dietaryPrefs = await getDietaryPreferences();
      } catch (error) {
        console.warn('Could not fetch dietary preferences:', error);
      }
      
      // Generate alternative recipe - use try-catch for API call
      let result;
      try {
        // Get the enhanced ingredients from the analysis result
        const ingredients = mealData.items || [];
        console.log('Enhancing ingredients for recipe generation:', JSON.stringify(ingredients));

        result = await generateAlternativeRecipe({
          originalFoodName: mealData.name,
          nutritionalInfo: {
            calories: mealData.calories,
            protein: mealData.protein,
            carbs: mealData.carbs,
            fat: mealData.fat
          },
          dietaryPreferences: dietaryPrefs?.diet_type ? [dietaryPrefs.diet_type as string] : undefined,
          allergies: dietaryPrefs?.allergies || undefined,
          originalIngredients: ingredients.length > 0 ? ingredients : undefined
        });
      } catch (error) {
        // Use fallback recipes on error
        result = {
          success: true,
          recipes: fallbackRecipes
        };
      }
      
      // Check for the recipes array and ensure it's not empty
      if (!result.success || !result.recipes || result.recipes.length === 0) {
        // Use fallback recipes if empty results
        result = {
          success: true,
          recipes: fallbackRecipes
        };
      }
      
      // Ensure each recipe has an image prompt
      const enhancedRecipes = result.recipes.map(recipe => {
        if (!recipe.imagePrompt) {
          recipe.imagePrompt = `A healthy, appetizing ${recipe.name} on a white plate with garnish, professional food photography, vibrant colors, studio lighting`;
        }
        return recipe;
      });
      
      setAlternativeRecipes(enhancedRecipes);
      setSelectedRecipeIndex(0);
      
      // Animate transition
      animateTransition(() => {
        setShowAlternative(true);
      });
    } catch (error) {
      console.error('Error generating alternative recipe:', error);
      
      // Use fallback recipes on any error
      setAlternativeRecipes([
        {
          name: `Healthier ${analysisResult?.data?.name || 'Meal'}`,
          focus: "Balanced Nutrition",
          description: `A healthier version with improved nutritional profile.`,
          ingredients: [
            { name: "Main Ingredient", amount: "1 cup", calories: 150, protein: 15, carbs: 10, fat: 5 },
            { name: "Vegetables", amount: "1 cup", calories: 50, protein: 2, carbs: 10, fat: 0 },
            { name: "Healthy Oil", amount: "1 tbsp", calories: 120, protein: 0, carbs: 0, fat: 14 }
          ],
          instructions: [
            "Prepare ingredients as directed",
            "Combine all ingredients and cook thoroughly",
            "Season to taste and serve"
          ],
          nutritionalInfo: {
            calories: analysisResult?.data?.calories ? Math.round(analysisResult.data.calories * 0.8) : 300,
            protein: analysisResult?.data?.protein ? Math.round(analysisResult.data.protein * 1.2) : 20,
            carbs: analysisResult?.data?.carbs ? Math.round(analysisResult.data.carbs * 0.7) : 30,
            fat: analysisResult?.data?.fat ? Math.round(analysisResult.data.fat * 0.8) : 10
          },
          preparationTime: "15 minutes",
          cookingTime: "20 minutes",
          healthBenefits: [
            "Lower in calories than the original",
            "Higher in protein for satiety"
          ],
          imagePrompt: `A healthy version of a meal on a plate with vegetables and protein.`
        }
      ]);
      setSelectedRecipeIndex(0);
      
      // Animate transition
      animateTransition(() => {
        setShowAlternative(true);
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Helper function for animation transitions
  const animateTransition = (onComplete: () => void) => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: Platform.OS !== 'web',
      }),
      Animated.timing(slideAnim, {
        toValue: -50,
        duration: 300,
        useNativeDriver: Platform.OS !== 'web',
      })
    ]).start(() => {
      onComplete();
      
      // Reset animations
      fadeAnim.setValue(0);
      slideAnim.setValue(50);
      
      // Animate new view in
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: Platform.OS !== 'web',
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: Platform.OS !== 'web',
        })
      ]).start();
    });
  };
  
  // Execute the save recipe operation
  const _executeSaveRecipe = async (recipeIndex: number) => {
    if (!alternativeRecipes || !analysisResult || recipeIndex >= alternativeRecipes.length) return;

    const recipeToSave = alternativeRecipes[recipeIndex];

    setLoading(true);
    setError(null);

    try {
      // Ensure we have a valid imagePrompt
      if (!recipeToSave.imagePrompt) {
        recipeToSave.imagePrompt = `A healthy, appetizing ${recipeToSave.name} on a white plate with garnish, professional food photography`;
      }

      // Get the original meal data
      const mealData = analysisResult.data || analysisResult;
      
      // Format date and time
      const now = new Date();
      const formattedDate = now.toISOString().split('T')[0];
      const formattedTime = now.toTimeString().split(' ')[0];

      // Ensure items array exists or provide a fallback
      const mealItems = mealData.items && Array.isArray(mealData.items)
        ? mealData.items.map((item: any) => ({
            name: item.name,
            calories: item.calories,
            protein: item.protein || 0,
            carbs: item.carbs || 0,
            fat: item.fat || 0
          }))
        : [{ 
            name: mealData.name,
            calories: mealData.calories,
            protein: mealData.protein || 0,
            carbs: mealData.carbs || 0,
            fat: mealData.fat || 0
          }];

      // Create the original meal first
      const mealResult = await createMeal(
        {
          name: mealData.name,
          date: formattedDate,
          time: formattedTime,
          total_calories: mealData.calories,
          total_protein: mealData.protein,
          total_carbs: mealData.carbs,
          total_fat: mealData.fat,
          image_url: imageUri || undefined
        },
        mealItems
      );

      // Link to original meal if created successfully
      let originalMealId: string | null = null;
      if (mealResult.success && mealResult.data) {
        originalMealId = mealResult.data.id;
      }

      // Create alternative recipe in database
      const result = await createAlternativeRecipe(
        {
          name: recipeToSave.name,
          description: recipeToSave.description,
          total_calories: recipeToSave.nutritionalInfo.calories,
          total_protein: recipeToSave.nutritionalInfo.protein,
          total_carbs: recipeToSave.nutritionalInfo.carbs,
          total_fat: recipeToSave.nutritionalInfo.fat,
          preparation_time: recipeToSave.preparationTime,
          cooking_time: recipeToSave.cookingTime,
          original_meal_id: originalMealId || undefined,
          image_prompt: recipeToSave.imagePrompt
        },
        recipeToSave.ingredients.map((ing: any) => ({
          name: ing.name,
          amount: ing.amount,
          calories: ing.calories || 0,
          protein: ing.protein || 0,
          carbs: ing.carbs || 0,
          fat: ing.fat || 0
        })),
        recipeToSave.instructions.map((instruction: any, index: number) => ({
          step_number: index + 1,
          instruction
        })),
        recipeToSave.healthBenefits.map((benefit: any) => ({
          benefit
        }))
      );

      if (!result.success) {
        throw new Error(result.error || 'Failed to save recipe');
      }

      // Navigate to history page
      safeGoBack();

    } catch (error) {
      console.error('Error saving alternative recipe:', error);
      setError('An error occurred while saving the recipe. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle recipe saving with confirmation alert
  const handleSaveRecipe = async () => {
    if (!alternativeRecipes || !analysisResult) return;

    // Create buttons for the Alert
    const alertButtons = alternativeRecipes.map((recipe, index) => ({
      text: `Save: ${recipe.name}${recipe.focus ? ' (' + recipe.focus + ')' : ''}`,
      onPress: () => _executeSaveRecipe(index),
    }));

    // Add a Cancel button
    alertButtons.push({
      text: 'Cancel',
      onPress: async () => console.log('Save recipe cancelled'), 
    });

    Alert.alert(
      'Confirm Save',
      'Which alternative recipe would you like to save?',
      alertButtons,
      { cancelable: true }
    );
  };

  // Go back from alternative recipe view
  const handleBackFromAlternative = () => {
    // Animate transition
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: Platform.OS !== 'web',
      }),
      Animated.timing(slideAnim, {
        toValue: 50,
        duration: 300,
        useNativeDriver: Platform.OS !== 'web',
      })
    ]).start(() => {
      setShowAlternative(false);
      
      // Reset animations
      fadeAnim.setValue(0);
      slideAnim.setValue(-50);
      
      // Animate main view back in
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: Platform.OS !== 'web',
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: Platform.OS !== 'web',
        })
      ]).start();
    });
  };
  
  // Reset the scan process
  const handleReset = () => {
    setImageUri('');
    setAnalysisResult(null);
    setAlternativeRecipes(null);
    setShowAlternative(false);
    setError(null);
    setShowCamera(false);
  };
  
  // Add this new function to handle updates from the manual override
  const handleAnalysisDataUpdate = async (updatedData: any) => {
    try {
      console.log('Updating food analysis data with user input:', updatedData.name);
      
      // For specific foods like Goulash, also fetch accurate nutrition data
      if (updatedData.name.toLowerCase().includes('goulash')) {
        // Get accurate nutrition data for Goulash using the USDA API
        const goulashIngredients = updatedData.items.map((item: any) => item.name);
        
        try {
          const nutritionData = await getNutritionForDish("Goulash", goulashIngredients);
          
          if (nutritionData && nutritionData.calories > 0) {
            console.log('Received accurate nutrition data for Goulash:', nutritionData);
            
            // Update the calories, macros, etc.
            updatedData.calories = nutritionData.calories;
            updatedData.protein = nutritionData.protein;
            updatedData.carbs = nutritionData.carbs;
            updatedData.fat = nutritionData.fat;
            
            // Update the items if we have new nutritional data for them
            if (nutritionData.items && nutritionData.items.length > 0) {
              updatedData.items = nutritionData.items;
            }
          }
        } catch (error) {
          console.error('Error getting nutrition data for Goulash:', error);
          // Continue with the existing data if USDA lookup fails
        }
      }
      
      // Update the state with potentially enhanced data
      setAnalysisResult(updatedData);
    } catch (error) {
      console.error('Error updating food analysis data:', error);
    }
  };
  
  // Save meal to database
  const handleSaveMeal = async () => {
    if (!analysisResult) return;
    
    setLoading(true);
    setError(null);
    
    try {
      // Format date and time
      const now = new Date();
      const formattedDate = now.toISOString().split('T')[0]; // YYYY-MM-DD
      const formattedTime = now.toTimeString().split(' ')[0]; // HH:MM:SS
      
      // Get the data, handling both direct access and nested data structure
      const mealData = analysisResult.data || analysisResult;
      
      // Ensure that items exists before mapping
      if (!mealData || !mealData.items) {
        throw new Error('Meal data is incomplete. Unable to save.');
      }
      
      // Create meal in database
      const result = await createMeal(
        {
          name: mealData.name, 
          date: formattedDate,
          time: formattedTime,
          total_calories: mealData.calories,
          total_protein: mealData.protein,
          total_carbs: mealData.carbs,
          total_fat: mealData.fat,
          image_url: imageUri || undefined
        },
        mealData.items.map((item: any) => ({
          name: item.name,
          calories: item.calories,
          protein: item.protein || 0,
          carbs: item.carbs || 0,
          fat: item.fat || 0
        }))
      );
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to save meal');
      }
      
      // Navigate to history page
      safeGoBack();
      
    } catch (error) {
      console.error('Error saving meal:', error);
      setError('An error occurred while saving the meal. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Add function to save image to media library
  const saveImageToGallery = async (uri: string) => {
    try {
      // Request permission to access media library
      const { status } = await MediaLibrary.requestPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Unable to save image without media library permission');
        return;
      }
      
      // Create asset from the image URI
      const asset = await MediaLibrary.createAssetAsync(uri);
      
      // Create an album for the app if it doesn't exist
      let album = await MediaLibrary.getAlbumAsync('Health App Foods');
      
      if (album === null) {
        // Create new album for the app
        await MediaLibrary.createAlbumAsync('Health App Foods', asset, false);
      } else {
        // Add to existing album
        await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
      }
      
      Alert.alert('Success', 'Image saved to your photo library');
    } catch (error) {
      console.error('Error saving image to gallery:', error);
      Alert.alert('Error', 'Failed to save image to your photo library');
    }
  };
  
  // Render save image button
  const SaveImageButton = () => (
    <View style={[styles.saveButton, {
      backgroundColor: isDark ? 'rgba(16, 185, 129, 0.8)' : '#10B981',
    }]}>
      <TouchableOpacity
        style={styles.saveButtonContent}
        onPress={() => imageUri && saveImageToGallery(imageUri)}
      >
        <Ionicons name="save-outline" size={20} color="white" />
        <Text style={styles.saveButtonText}>Save</Text>
      </TouchableOpacity>
    </View>
  );
  
  // Modify renderAnalysisResult to include a save image button
  const renderAnalysisResult = () => {
    // Skip if no result
    if (!analysisResult) return null;
    
    // If failed, show error
    if (!analysisResult.success) {
      return (
        <NoScanView
          error={analysisResult.error || "Analysis failed. Please try again with a clearer photo."}
          onRetry={handleReset}
        />
      );
    }
    
    // Get the data, handling both direct access and nested data structure
    const analysisData = analysisResult.data || analysisResult;
    
    // Multi-item display for detected food items
    if (analysisData.multiItem && analysisData.items && analysisData.items.length > 1) {
      return (
        <View style={{ flex: 1 }}>
          <MultiItemFoodDisplay
            imageUri={imageUri || ''}
            foodItems={analysisData.items}
            onItemSelect={handleFoodItemSelect}
            onCombineItems={handleCombineItems}
          />
          
          <FoodAnalysisResult 
            data={analysisData}
            imageUri={imageUri || ''}
            dailyCalorieGoal={dailyCalorieGoal}
            onGenerateAlternative={
              hasSubscription ? 
                handleGenerateAlternative : 
                // For non-subscribers, show premium promotion
                () => (
                  <PremiumFeature
                    featureKey="alternative-recipe-generation"
                    title="Alternative Recipe Generation"
                    description="Get healthy alternatives to any meal you scan"
                    forceAccess={false}
                  >
                    <Text>This is a premium feature</Text>
                  </PremiumFeature>
                )
            }
            onSaveMeal={handleSaveMeal}
            onScanNew={handleReset}
            loading={loading}
            hasSubscription={hasSubscription}
            alternativeSources={analysisResult.alternativeSources}
          />
          
          <SaveImageButton />
        </View>
      );
    }
    
    // Use enhanced segmentation analysis for premium users
    if (hasSubscription && imageUri) {
      return (
        <>
          <FoodAnalysisWithSegmentation
            imageUri={imageUri}
            initialAnalysisData={analysisResult}
            onBack={handleReset}
            onSave={(updatedData) => {
              handleAnalysisDataUpdate(updatedData);
            }}
          />
          <SaveImageButton />
        </>
      );
    }
    
    // Standard analysis for regular users
    return (
      <View style={{ flex: 1 }}>
        <FoodAnalysisResult 
          data={analysisResult.data}
          imageUri={imageUri || ''}
          dailyCalorieGoal={dailyCalorieGoal}
          onGenerateAlternative={
            hasSubscription ? 
              handleGenerateAlternative : 
              // For non-subscribers, show premium promotion
              () => (
                <PremiumFeature
                  featureKey="alternative-recipe-generation"
                  title="Alternative Recipe Generation"
                  description="Get healthy alternatives to any meal you scan"
                  forceAccess={false}
                >
                  <Text>This is a premium feature</Text>
                </PremiumFeature>
              )
          }
          onSaveMeal={handleSaveMeal}
          onScanNew={handleReset}
          loading={loading}
          hasSubscription={hasSubscription}
          alternativeSources={analysisResult.alternativeSources}
        />
        <SaveImageButton />
      </View>
    );
  };
  
  // Handle ScanOptionsModal actions
  const handleScanOptionSelected = (mode: 'single' | 'multi' | 'barcode') => {
    setScanOptionsVisible(false);
    
    if (mode === 'single') {
      setScanMode('food');
      setMultiItemMode(false);
      handleOpenCamera();
    } else if (mode === 'multi') {
      setScanMode('food');
      setMultiItemMode(true);
      handleOpenCamera();
    } else if (mode === 'barcode') {
      setScanMode('barcode');
      handleOpenCamera();
    }
  };
  
  // Handle recent scan selection
  const handleRecentScanSelected = async (uri: string) => {
    setImageUri(uri);
    await analyzeImage(uri);
  };
  
  // Toggle camera type (front/back)
  const toggleCameraType = () => {
    setCameraType(cameraType === 'back' ? 'front' : 'back');
  };
  
  // Toggle scan mode (food/barcode)
  const toggleScanMode = () => {
    setScanMode(scanMode === 'food' ? 'barcode' : 'food');
  };
  
  // Handle hiding the recent scans section
  const handleHideRecentScans = () => {
    setHideRecentScans(true);
  };
  
  // Main render function with components
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} />
      
      <Stack.Screen options={{ headerShown: false }} />
      
      {/* Header */}
      <ScanHeader
        isPremium={hasSubscription && !checkingSubscription}
        headerAnim={headerAnim}
        pulseAnim={pulseAnim}
        rotateAnim={rotateAnim}
        onScanOptions={() => setScanOptionsVisible(true)}
        onOpenGallery={handleOpenGallery}
        onGoBack={safeGoBack}
        onBarcodeMode={() => {
          setScanMode('barcode');
          handleOpenCamera();
        }}
      />
      
      {/* Main Content - Show appropriate component based on state */}
      {photoPermissionDenied ? (
        <NoPermissionView
          type="photos"
          onRequestPermission={async () => {
            const result = await MediaLibrary.requestPermissionsAsync();
            if (result.status === 'granted') {
              setPhotoPermissionDenied(false);
              handleOpenGallery();
            }
          }}
        />
      ) : loading ? (
        <ScanLoading scanMode={scanMode} />
      ) : error ? (
        <NoScanView error={error} onRetry={handleReset} />
      ) : analysisResult ? (
        <>
          {showAlternative && alternativeRecipes && alternativeRecipes.length > 0 ? (
            <AlternativeRecipeView
              originalImageUri={imageUri || ''}
              onSaveRecipe={handleSaveRecipe}
              onBack={handleBackFromAlternative}
              loading={loading}
              recipes={alternativeRecipes}
              selectedIndex={selectedRecipeIndex}
              onIndexChange={setSelectedRecipeIndex}
              originalNutrition={{
                calories: analysisResult?.data?.calories,
                protein: analysisResult?.data?.protein,
                carbs: analysisResult?.data?.carbs,
                fat: analysisResult?.data?.fat
              }}
            />
          ) : (
            renderAnalysisResult()
          )}
        </>
      ) : showCamera ? (
        <CameraViewWrapper
          cameraType={cameraType}
          scanMode={scanMode}
          multiItemMode={multiItemMode}
          onCapture={handleImageCaptured}
          onBarCodeScanned={handleBarcodeScanned}
          onClose={() => setShowCamera(false)}
          onToggleCameraType={toggleCameraType}
          onToggleScanMode={toggleScanMode}
        />
      ) : (
        <View style={styles.scanContainer}>
          <ScanOptionsView
            onOpenCamera={handleOpenCamera}
            onOpenGallery={handleOpenGallery}
            suggestedMeal={suggestedMeal as any}
          />
          
          {!hideRecentScans && recentScans.length > 0 && (
            <RecentScans
              scans={recentScans}
              onSelectScan={handleRecentScanSelected}
              onClose={handleHideRecentScans}
            />
          )}
        </View>
      )}
      
      {/* Show scan options modal when visible */}
      {scanOptionsVisible && (
        <ScanOptionsModal
          onClose={() => setScanOptionsVisible(false)}
          onSelectSingleFood={() => handleScanOptionSelected('single')}
          onSelectMultiFood={() => handleScanOptionSelected('multi')}
          onSelectBarcode={() => handleScanOptionSelected('barcode')}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scanContainer: {
    flex: 1,
  },
  saveButton: {
    position: 'absolute',
    right: 20,
    bottom: 40,
    borderRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  saveButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
    marginLeft: 6,
  },
}); 