import React, { useState, useEffect, useRef } from 'react';
import { Tabs, router, Stack, useRouter, useSegments } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, Platform, Animated , Pressable, useColorScheme } from 'react-native';
import { Feather, MaterialIcons, Ionicons, FontAwesome5 } from '@expo/vector-icons';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import Colors from '@/constants/Colors';
import { StatusBar } from 'expo-status-bar';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
// Firebase imports
import { getFirestore, collection, getDocs, addDoc, query, limit } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { app } from '@/lib/firebase';
import { BlurView } from 'expo-blur';
import SyncStatusIndicator from '@/components/SyncStatusIndicator';
import ConflictResolutionButton from '@/components/ConflictResolutionButton';
// Import the HeaderNotification component
import HeaderNotification from '@/components/HeaderNotification';
import { useFooterVisibility, FooterVisibilityProvider } from '@/contexts/FooterVisibilityContext';
import { LinearGradient } from 'expo-linear-gradient';

// Initialize Firebase services
const db = getFirestore(app);
const auth = getAuth(app);

// Custom header with notification only
function TabHeader() {
  const { colors, isDark } = useTheme();
  const insets = useSafeAreaInsets();
  const router = useRouter();
  
  return (
    <View 
      style={[
        styles.headerContainer, 
        { 
          paddingTop: insets.top,
          backgroundColor: isDark ? '#121622' : '#FFFFFF',
          borderBottomColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
        }
      ]}
    >
      <View style={styles.headerContent}>
        <View style={styles.logoContainer}>
          <View style={[
            styles.logoIconContainer, 
            { 
              backgroundColor: `${colors.primary}15`,
              shadowColor: colors.primary,
              shadowOffset: { width: 0, height: 0 },
              shadowOpacity: 0.2,
              shadowRadius: 8,
            }
          ]}>
            <Feather name="heart" size={20} color={colors.primary} style={styles.logoIcon} />
          </View>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            <Text style={{ color: colors.primary, fontWeight: '800' }}>Choose</Text>
            <Text style={{ fontWeight: '600' }}>Healthy</Text>
          </Text>
        </View>
        
        <View style={styles.rightContainer}>
          {/* Notification Icon */}
          <HeaderNotification
            navigateToNotifications={() => router.push('/profile-screens/notifications')}
          />
        </View>
      </View>
    </View>
  );
}

// Simplified approach to types
interface TabIconProps {
  key: string;
  label: string;
  Icon: any; // Using any for simplicity
  iconName?: string; // Optional icon name for FontAwesome5
}

// Track navigation event in the backend
async function trackNavigationEvent(screenName: string, params?: any) {
  try {
    // Get current user
    const currentUser = auth.currentUser;
    
    if (!currentUser) {
      return; // Silently fail when not authenticated
    }

    // Only try to save navigation events on native platforms, not on web
    if (Platform.OS !== 'web') {
      try {
        // Insert the navigation event into the database
        await addDoc(collection(db, 'user_navigation_events'), {
          user_id: currentUser.uid,
          screen_name: screenName,
          params: params || {},
          timestamp: new Date().toISOString()
        });
      } catch (err) {
        // Silently fail if there is an error adding the document
        // This is an analytics feature and shouldn't block user experience
        console.log('Non-critical error tracking navigation: ', err);
      }
    } else {
      // On web platform, just log the navigation event without storing in Firebase
      if (__DEV__) {
        console.log(`Navigation tracking (web): ${screenName}`, params);
      }
    }
  } catch (err) {
    // Suppress errors for navigation tracking as it's non-critical
    if (__DEV__) {
      console.log('Navigation tracking suppressed error: ', err);
    }
  }
}

function CustomTabBar(props: any) {
  const { state, navigation } = props;
  const { isDark, colors } = useTheme();
  const insets = useSafeAreaInsets();
  const backgroundColor = isDark ? '#121622' : '#FFFFFF';
  const borderColor = isDark ? '#2A3040' : '#EEEEEE';
  const activeColor = colors.primary || '#3B82F6';
  const inactiveColor = isDark ? '#8899AA' : '#667788';
  const toggleFooterVisibility = props.toggleFooterVisibility;
  const { hiddenTabs, isTabVisible } = useFooterVisibility();

  // Define navigation items in the new order with health-tracking
  const mainNavIcons: TabIconProps[] = [
    { 
      key: 'health-tracking', 
      label: 'Water Tracker', 
      Icon: Feather, 
      iconName: 'droplet' 
    },
    { 
      key: 'scan', 
      label: 'Food Scan', 
      Icon: Camera 
    },
    { 
      key: 'profile', 
      label: 'My Profile', 
      Icon: User 
    },
    // Keep these for reference but they're hidden
    { key: 'index', label: 'Home', Icon: Home },
    { key: 'food-history', label: 'Food Log', Icon: Ionicons, iconName: 'restaurant-outline' },
  ];

  // Filter out hidden tabs
  const visibleNavIcons = mainNavIcons.filter(item => isTabVisible(item.key));

  // The extended footer fixes the white line by going beyond the visible area
  return (
    <View style={{ 
      position: 'absolute', 
      bottom: 0, 
      left: 0, 
      right: 0, 
      height: 58, // Match container height
      zIndex: 900,
      backgroundColor, // Add background color to parent as well
    }}>
      {/* Main navigation bar */}
      <BlurView 
        intensity={isDark ? 30 : 50}
        tint={isDark ? 'dark' : 'light'}
        style={[
          styles.container, 
          { 
            backgroundColor: isDark ? 'rgba(18, 22, 34, 0.85)' : 'rgba(255, 255, 255, 0.85)', 
            borderTopColor: borderColor,
          }
        ]}
      >
        <View style={styles.mainRow}>
          {visibleNavIcons.map(({ key, label, Icon, iconName }) => {
            // Get tab index and set focused state
            const tabIndex = state.routes.findIndex((route: any) => route.name === key);
            const isFocused = tabIndex === state.index;
            const color = isFocused ? activeColor : inactiveColor;
            const isScanner = key === 'scan';
            
            const onPress = () => {
              if (isScanner) {
                // For the scan button, navigate to scan screen and immediately open camera
                trackNavigationEvent(`main_tab_${key}`);
                navigation.navigate(key, { openCameraDirectly: 'true' });
              } else if (!isFocused) {
                // For other tabs, just navigate normally
                trackNavigationEvent(`main_tab_${key}`);
                navigation.navigate(key);
              }
            };

            return (
              <TouchableOpacity
                key={key}
                style={[
                  styles.tabButton,
                  isFocused && styles.activeTabButton,
                  isScanner && styles.scanButton
                ]}
                onPress={onPress}
                accessibilityRole="button"
                accessibilityState={isFocused ? { selected: true } : {}}
                accessible={true}
                accessibilityLabel={`${label} tab`}
              >
                <LinearGradient
                  colors={isFocused 
                    ? isScanner 
                      ? [isDark ? '#4DA0FF' : '#4DA0FF', isDark ? '#0075FF' : '#0062D6'] 
                      : [isDark ? `${activeColor}30` : `${activeColor}15`, isDark ? `${activeColor}10` : `${activeColor}05`]
                    : ['transparent', 'transparent']}
                  style={[
                    styles.iconContainer, 
                    isFocused && styles.activeIconContainer,
                    isScanner && styles.scannerIconContainer,
                    isScanner && { 
                      elevation: 8,
                      shadowColor: isDark ? '#4DA0FF' : '#0062D6',
                      shadowOffset: { width: 0, height: 4 },
                      shadowOpacity: 0.5,
                      shadowRadius: 6,
                    }
                  ]}
                >
                  {Icon === Ionicons ? (
                    <Ionicons name={iconName as any} size={isScanner ? 32 : 26} color={isScanner ? '#FFFFFF' : color} />
                  ) : Icon === Feather ? (
                    <Feather name={iconName as any} size={isScanner ? 32 : 26} color={isScanner ? '#FFFFFF' : color} />
                  ) : (
                    <Icon size={isScanner ? 32 : 26} color={isScanner ? '#FFFFFF' : color} />
                  )}
                  
                  {isFocused && !isScanner && (
                    <View style={[
                      styles.activeIndicator, 
                      { backgroundColor: color, width: isScanner ? 0 : 24 }
                    ]} />
                  )}
                </LinearGradient>
              </TouchableOpacity>
            );
          })}
        </View>
      </BlurView>
    </View>
  );
}

/**
 * You can explore the built-in icon families and icons on the web at https://icons.expo.fyi/
 */
function TabBarIcon(props: {
  name: React.ComponentProps<typeof FontAwesome>['name'];
  color: string;
}) {
  return <FontAwesome size={28} style={{ marginBottom: -3 }} {...props} />;
}

export default function TabLayout() {
  const [showFooter, setShowFooter] = useState(true);
  const colorScheme = useColorScheme();
  const segments = useSegments();
  const router = useRouter();

  const toggleFooterVisibility = () => {
    // Navigate to the more menu instead of toggling footer
    router.push('/profile-screens/more');
  };

  // Calculate if we should show header based on current route
  const shouldShowHeader = false; // Change to false to hide header on all screens

  return (
    <FooterVisibilityProvider>
      <Tabs
        screenOptions={{
          tabBarActiveTintColor: Colors[colorScheme ?? 'light'].tint,
          // Hide the default header completely
          headerShown: false
        }}
        // Use custom tab bar
        tabBar={props => <CustomTabBar {...props} toggleFooterVisibility={toggleFooterVisibility} />}
        initialRouteName="scan"
      >
        <Tabs.Screen
          name="health-tracking"
          options={{
            title: 'Health Data'
          }}
        />
        <Tabs.Screen
          name="index"
          options={{
            title: 'Home'
          }}
        />
        <Tabs.Screen
          name="food-history"
          options={{
            title: 'Food Log'
          }}
        />
        <Tabs.Screen
          name="scan"
          options={{
            title: 'Scan'
          }}
        />
        <Tabs.Screen
          name="profile"
          options={{
            title: 'Profile'
          }}
        />
        <Tabs.Screen
          name="profile-screens"
          options={{
            title: "Profile Screens",
            headerShown: false, // Hide the header since these are handled by individual screens
            href: null, // Disable direct navigation to this tab
          }}
        />
      </Tabs>
      
      {/* Don't show TabHeader at all */}
    </FooterVisibilityProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    height: 58, // Slightly reduced height since we don't have labels
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    zIndex: 900,
    overflow: 'visible',
    paddingBottom: 0,
    ...Platform.OS === 'web' ? { transform: [] } : {},
  },
  mainRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center', // Center icons vertically
    height: '100%',
  },
  tabButton: {
    alignItems: 'center',
    justifyContent: 'center', // Center both horizontally and vertically
    flex: 1,
    height: '100%',
  },
  scanButton: {
    marginTop: -8, // Keep the scan button slightly elevated
  },
  activeTabButton: Platform.OS === 'web' 
    ? { transform: [] }
    : {
        transform: [{ scale: 1.1 }], // Slightly increase active icon scale
      },
  iconContainer: {
    width: 48, // Larger container for bigger icons
    height: 48, // Larger container for bigger icons
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scannerIconContainer: {
    width: 60, // Larger scan button
    height: 60, // Larger scan button
    borderRadius: 30,
  },
  activeIconContainer: {
    elevation: 4,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  activeIndicator: {
    position: 'absolute',
    bottom: -4,
    height: 4,
    width: 24,
    alignSelf: 'center',
    borderRadius: 2,
  },
  headerContainer: {
    borderBottomWidth: 1,
    zIndex: 1000,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  headerGradient: {
    width: '100%',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    width: '100%',
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  logoIcon: {
    marginRight: 0,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.5,
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  searchButton: {
    height: 40,
    paddingHorizontal: 12,
    borderRadius: 20,
    borderWidth: 1,
    minWidth: 120,
  },
  showFooterButton: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 1000,
  },
  tabBarExtension: {
    position: 'absolute',
    height: 100, // Plenty of height to extend beyond screen
    left: 0,
    right: 0,
    bottom: -100, // Position it below the tab bar
    zIndex: 899, // Just below the tab bar
  },
  fixedFooter: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    height: 52, // Match container height
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    zIndex: 900,
  },
  footerContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    height: '100%',
  },
  footerButton: {
    alignItems: 'center',
    justifyContent: 'flex-start', // Match tabButton
    flex: 1,
  },
  footerIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 2, // Match iconContainer
  },
  footerLabel: {
    fontSize: 11, // Match label
    fontWeight: '600',
    marginTop: 2, // Match label
    marginBottom: 0,
    paddingTop: 0,
  },
});