import React, { useState, useEffect, useRef } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity, 
  Modal, 
  ActivityIndicator,
  Alert,
  SafeAreaView
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/contexts/TranslationContext';
import { MaterialCommunityIcons , Feather } from '@expo/vector-icons';
import ChallengeCard from '@/components/ChallengeCard';
import CreateChallengeForm from '@/components/CreateChallengeForm';
import { 
  getAvailableChallenges, 
  getUserChallenges, 
  joinChallenge,
  Challenge, 
  ChallengeStatus
} from '@/services/challengeService';
import { BlurView } from 'expo-blur';
import { useIsFocused } from '@react-navigation/native';
import { useSafeNavigation } from '@/hooks/useSafeNavigation';

export default function ChallengesScreen() {
  const { colors, isDark } = useTheme();
  const { t } = useTranslation();
  const isFocused = useIsFocused();
  const { safeGoBack } = useSafeNavigation();
  
  // State
  const [activeTab, setActiveTab] = useState<'my' | 'available'>('my');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [userChallenges, setUserChallenges] = useState<Challenge[]>([]);
  const [availableChallenges, setAvailableChallenges] = useState<Challenge[]>([]);
  const [selectedChallenge, setSelectedChallenge] = useState<Challenge | null>(null);
  const [showChallengeModal, setShowChallengeModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  
  // Refs
  const flatListRef = useRef<FlatList>(null);
  
  // Load challenges on mount and when tab is focused
  useEffect(() => {
    if (isFocused) {
      loadChallenges();
    }
  }, [isFocused]);
  
  // Load challenges
  const loadChallenges = async (refresh = false) => {
    if (refresh) {
      setRefreshing(true);
    } else if (!refreshing) {
      setLoading(true);
    }
    
    try {
      // Load user challenges
      const userChallengesData = await getUserChallenges();
      setUserChallenges(userChallengesData);
      
      // Load available challenges
      const availableChallengesData = await getAvailableChallenges();
      
      // Filter out challenges that the user has already joined
      const filteredAvailableChallenges = availableChallengesData.filter(
        availableChallenge => !userChallengesData.some(
          userChallenge => userChallenge.id === availableChallenge.id
        )
      );
      
      setAvailableChallenges(filteredAvailableChallenges);
    } catch (error) {
      console.error('Error loading challenges:', error);
      Alert.alert('Error', 'Failed to load challenges. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  // Handle refreshing
  const handleRefresh = () => {
    loadChallenges(true);
  };
  
  // Handle challenge press
  const handleChallengePress = (challenge: Challenge) => {
    setSelectedChallenge(challenge);
    setShowChallengeModal(true);
  };
  
  // Handle joining a challenge
  const handleJoinChallenge = async (challenge: Challenge) => {
    try {
      const success = await joinChallenge(challenge);
      
      if (success) {
        // Close modal
        setShowChallengeModal(false);
        setSelectedChallenge(null);
        
        // Reload challenges
        await loadChallenges();
        
        // Switch to My Challenges tab
        setActiveTab('my');
        
        // Show success message
        Alert.alert(
          'Challenge Joined',
          `You've successfully joined the ${challenge.title} challenge!`,
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert('Error', 'Failed to join challenge. Please try again.');
      }
    } catch (error) {
      console.error('Error joining challenge:', error);
      Alert.alert('Error', 'Something went wrong. Please try again.');
    }
  };
  
  // Render tab button
  const renderTabButton = (tab: 'my' | 'available', label: string, icon: string) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        activeTab === tab && { backgroundColor: colors.primary },
      ]}
      onPress={() => setActiveTab(tab)}
    >
      <MaterialCommunityIcons
        name={icon as any}
        size={22}
        color={activeTab === tab ? 'white' : colors.textSecondary}
      />
      <Text
        style={[
          styles.tabButtonText,
          { color: activeTab === tab ? 'white' : colors.textSecondary },
        ]}
      >
        {label}
      </Text>
    </TouchableOpacity>
  );
  
  // Render challenge item
  const renderChallengeItem = ({ item }: { item: Challenge }) => (
    <ChallengeCard
      challenge={item}
      onPress={() => handleChallengePress(item)}
      onJoin={activeTab === 'available' ? handleJoinChallenge : undefined}
    />
  );
  
  // Render challenge modal
  const renderChallengeModal = () => (
    <Modal
      visible={showChallengeModal}
      transparent
      animationType="fade"
      onRequestClose={() => {
        setShowChallengeModal(false);
        setSelectedChallenge(null);
      }}
    >
      <View style={styles.modalContainer}>
        <BlurView
          intensity={isDark ? 70 : 50}
          tint={isDark ? 'dark' : 'light'}
          style={StyleSheet.absoluteFill}
        />
        <View style={[styles.modalContent, { backgroundColor: isDark ? colors.card : 'white' }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.modalBackButton}
              onPress={() => {
                setShowChallengeModal(false);
                setSelectedChallenge(null);
              }}
              accessibilityLabel="Go back"
              accessibilityRole="button"
            >
              <Feather name="chevron-left" size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.text }]}>Challenge Details</Text>
            <View style={{ width: 24 }} />
          </View>
          
          {selectedChallenge && (
            <View style={styles.modalScrollContent}>
              <ChallengeCard
                challenge={selectedChallenge}
                onJoin={activeTab === 'available' ? handleJoinChallenge : undefined}
              />
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
  
  // Render create challenge modal
  const renderCreateModal = () => (
    <Modal
      visible={showCreateModal}
      transparent
      animationType="slide"
      onRequestClose={() => setShowCreateModal(false)}
    >
      <View style={styles.modalContainer}>
        <BlurView
          intensity={isDark ? 70 : 50}
          tint={isDark ? 'dark' : 'light'}
          style={StyleSheet.absoluteFill}
        />
        <View style={[styles.createModalContent, { backgroundColor: isDark ? colors.card : 'white' }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.modalBackButton}
              onPress={() => setShowCreateModal(false)}
              accessibilityLabel="Go back"
              accessibilityRole="button"
            >
              <Feather name="chevron-left" size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.text }]}>Create Challenge</Text>
            <View style={{ width: 24 }} />
          </View>
          <CreateChallengeForm
            onSuccess={() => {
              setShowCreateModal(false);
              loadChallenges();
            }}
            onCancel={() => setShowCreateModal(false)}
          />
        </View>
      </View>
    </Modal>
  );
  
  // Render empty state for challenges
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <MaterialCommunityIcons
        name={activeTab === 'my' ? 'trophy-outline' : 'trophy-award'}
        size={64}
        color={colors.textSecondary}
        style={{ opacity: 0.5 }}
      />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>
        {activeTab === 'my' ? 'No Active Challenges' : 'No Available Challenges'}
      </Text>
      <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
        {activeTab === 'my'
          ? 'Join or create a challenge to get started on your health journey'
          : 'Check back later for new challenges or create your own'}
      </Text>
      
      {activeTab === 'my' && (
        <TouchableOpacity
          style={[styles.emptyButton, { backgroundColor: colors.primary }]}
          onPress={() => setActiveTab('available')}
        >
          <Text style={styles.emptyButtonText}>Find Challenges</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      
      <View style={styles.header}>
        <View style={styles.headerLeftContainer}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={safeGoBack}
            accessibilityLabel="Go back"
            accessibilityRole="button"
          >
            <Feather name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Challenges</Text>
        </View>
        <TouchableOpacity
          style={[styles.createButton, { backgroundColor: colors.primary }]}
          onPress={() => setShowCreateModal(true)}
        >
          <MaterialCommunityIcons name="plus" size={22} color="white" />
          <Text style={styles.createButtonText}>Create</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.tabsContainer}>
        {renderTabButton('my', 'My Challenges', 'star')}
        {renderTabButton('available', 'Available', 'trophy-outline')}
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading challenges...
          </Text>
        </View>
      ) : (
        <FlatList
          ref={flatListRef}
          data={activeTab === 'my' ? userChallenges : availableChallenges}
          renderItem={renderChallengeItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          refreshing={refreshing}
          onRefresh={handleRefresh}
          ListEmptyComponent={renderEmptyState}
        />
      )}
      
      {renderChallengeModal()}
      {renderCreateModal()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 12,
  },
  headerLeftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  createButtonText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 6,
  },
  tabsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  tabButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginRight: 10,
  },
  tabButtonText: {
    fontWeight: '600',
    marginLeft: 6,
  },
  listContent: {
    paddingBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
  },
  emptyButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 20,
    overflow: 'hidden',
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.2,
    shadowRadius: 15,
    elevation: 10,
  },
  createModalContent: {
    width: '90%',
    maxHeight: '90%',
    borderRadius: 20,
    overflow: 'hidden',
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.2,
    shadowRadius: 15,
    elevation: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  modalBackButton: {
    padding: 8,
    borderRadius: 20,
    marginRight: 12,
  },
  modalTitle: {
    fontSize: 28,
    fontWeight: '700',
  },
  modalScrollContent: {
    padding: 16,
  },
}); 