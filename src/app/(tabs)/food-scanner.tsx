import React, { useState } from 'react';
import { View, StyleSheet, Text, TouchableOpacity, SafeAreaView, Platform , useColorScheme } from 'react-native';
import { FoodScanner } from '@/components/FoodScanner';
import { OpenFoodFactsProduct } from '@/services/food-data/open-food-facts';
import { StatusBar } from 'expo-status-bar';
import Colors from '@/constants/Colors';
import { router } from 'expo-router';

export default function FoodScannerScreen() {
  const [scannedProduct, setScannedProduct] = useState<OpenFoodFactsProduct | null>(null);
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const handleProductSelected = (product: OpenFoodFactsProduct) => {
    setScannedProduct(product);
    // Here you could also navigate to a detailed view or save to history
    console.log('Product scanned:', product.product_name);
  };

  const handleGoBack = () => {
    router.back();
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      
      {Platform.OS === 'ios' && (
        <SafeAreaView style={styles.headerContainer}>
          <TouchableOpacity onPress={handleGoBack} style={styles.backButton}>
            <Text style={[styles.backButtonText, { color: colors.tint }]}>Back</Text>
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Food Scanner</Text>
          <View style={styles.headerSpacer} />
        </SafeAreaView>
      )}
      
      <FoodScanner onProductSelected={handleProductSelected} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    height: 50,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  backButton: {
    padding: 8,
  },
  backButtonText: {
    fontSize: 16,
  },
  headerSpacer: {
    width: 50, // Same as backButton
  },
}); 