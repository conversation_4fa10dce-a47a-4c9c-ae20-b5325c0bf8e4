import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
  RefreshControl
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useUserPreferences } from '@/contexts/UserPreferencesContext';
import { Feather } from '@expo/vector-icons';
import { DietaryPreferencesSelector } from '@/components/ui/DietaryPreferencesSelector';
import { IngredientSubstitutionCard } from '@/components/ui/IngredientSubstitutionCard';
import { HealthImpactExplainer } from '@/components/ui/HealthImpactExplainer';
import { NutritionImprovementTracker } from '@/components/ui/NutritionImprovementTracker';
import { SeasonalIngredientSuggestions } from '@/components/ui/SeasonalIngredientSuggestions';
import { 
  findFoodById, 
  foodItems as healthyFoodItems,
  Substitution, 
  FoodItem,
  findSubstitutionsFor as findLocalSubstitutions 
} from '@/services/healthySubstitutions';
import { 
  findSubstitutions as findOpenAISubstitutions,
  IngredientSubstitution 
} from '@/services/openai/healthySubstitutions';

// Use real data
const FOOD_ITEMS = healthyFoodItems;

// Enhanced getSubstitutionsFor that also uses the OpenAI service when possible
const getSubstitutionsFor = (id: string): Substitution[] => {
  // First try to get substitutions from the local database
  const localSubs = findLocalSubstitutions(id);
  
  // If we found substitutions, return them
  if (localSubs.length > 0) {
    return localSubs;
  }
  
  // Try to use the OpenAI service as a fallback
  const food = findFoodById(id);
  if (!food) return [];
  
  // Get substitutions from OpenAI database
  const aiSubs = findOpenAISubstitutions(food.name);
  
  // Convert to our substitution format
  return aiSubs.map(sub => ({
    originalId: id,
    substituteId: sub.substitution.toLowerCase().replace(/\s+/g, '-'),
    healthBenefits: [sub.benefits],
    culinaryNotes: sub.culinaryNotes || '',
    tasteImpact: 'moderate' as const,
    textureImpact: 'moderate' as const
  }));
};

export default function HealthierAlternativesScreen() {
  const { colors } = useTheme();
  const { 
    dietaryPreferences, 
    healthGoals, 
    foodRestrictions,
    hasSavedPreferences
  } = useUserPreferences();
  
  const [isLoading, setIsLoading] = useState(true);
  const [showPreferencesSelector, setShowPreferencesSelector] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'favorites' | 'recent'>('all');
  const [substitutionCards, setSubstitutionCards] = useState<React.ReactNode[]>([]);
  
  // Generate substitution cards based on preferences and goals
  useEffect(() => {
    const generateSubstitutionCards = async () => {
      try {
        setIsLoading(true);
        
        // Get relevant food items based on user preferences
        const relevantItems = FOOD_ITEMS.filter(item => {
          // Skip items that conflict with food restrictions
          if (foodRestrictions.length > 0) {
            const hasRestriction = foodRestrictions.some(restriction => {
              // Simple substring match for demo purposes
              // In a real app, would use more sophisticated matching
              return item.name.toLowerCase().includes(restriction.toLowerCase()) ||
                item.category.toLowerCase() === restriction.toLowerCase();
            });
            if (hasRestriction) return false;
          }
          
          // Include items that match dietary preferences
          if (dietaryPreferences.length > 0 && item.dietaryCategories) {
            const matchesDiet = dietaryPreferences.some(preference => {
              return item.dietaryCategories?.includes(preference);
            });
            if (matchesDiet) return true;
          }
          
          // Default to including common foods
          return ['grain', 'dairy', 'oil', 'sweetener'].includes(item.category);
        });
        
        // Find suitable substitutions
        const substitutions = relevantItems.flatMap(item => {
          const subs = getSubstitutionsFor(item.id);
          return subs.map(sub => {
            const substituteItem = findFoodById(sub.substituteId);
            if (!substituteItem) return null;
            
            // Check if substitute matches dietary preferences
            if (dietaryPreferences.length > 0 && substituteItem.dietaryCategories) {
              const matchesDiet = dietaryPreferences.some(preference => {
                return substituteItem.dietaryCategories?.includes(preference);
              });
              if (!matchesDiet) return null;
            }
            
            // Check if substitute aligns with health goals
            if (healthGoals.length > 0) {
              // Simple check for demo purposes
              // In a real app, would have more specific mapping
              const benefitMatches = sub.healthBenefits.some(benefit => {
                for (const goal of healthGoals) {
                  if (goal === 'weight-loss' && benefit.toLowerCase().includes('calorie')) return true;
                  if (goal === 'heart-health' && benefit.toLowerCase().includes('cholesterol')) return true;
                  if (goal === 'blood-sugar' && benefit.toLowerCase().includes('glycemic')) return true;
                  if (goal === 'digestion' && benefit.toLowerCase().includes('fiber')) return true;
                }
                return false;
              });
              if (!benefitMatches) return null;
            }
            
            return {
              original: item,
              substitute: substituteItem,
              substitution: sub
            };
          }).filter(Boolean);
        });
        
        // Format for display
        const cards = substitutions
          .slice(0, 5) // Limit to 5 for demo
          .map((sub, index) => {
            if (!sub) return null;
            
            // Convert health benefits to the format expected by IngredientSubstitutionCard
            const benefits = sub.substitution.healthBenefits.map(benefit => {
              const icon = benefit.toLowerCase().includes('cholesterol') ? 'heart' :
                           benefit.toLowerCase().includes('fiber') ? 'leaf' :
                           benefit.toLowerCase().includes('vitamin') ? 'info' : 'clock';
              
              return {
                title: benefit.split(' ').slice(0, 3).join(' '),
                description: benefit,
                icon
              };
            });
            
            // Prep notes from culinary notes
            const prepNotes = sub.substitution.culinaryNotes;
            
            // Long term benefits based on health focus
            let longTermBenefit = 'Regular consumption of healthier alternatives may contribute to better overall health and reduced risk of chronic disease.';
            
            if (healthGoals.includes('weight-loss')) {
              longTermBenefit = 'Consistently making this swap could help with weight management by reducing calorie intake while maintaining nutritional value.';
            } else if (healthGoals.includes('heart-health')) {
              longTermBenefit = 'Making this swap regularly may support heart health by reducing saturated fat and cholesterol intake while increasing beneficial nutrients.';
            } else if (healthGoals.includes('blood-sugar')) {
              longTermBenefit = 'Over time, this substitution may help stabilize blood sugar levels by reducing glycemic impact and improving metabolic health.';
            }
            
            return (
              <IngredientSubstitutionCard
                key={`${sub.original.id}-${sub.substitute.id}`}
                originalIngredient={{...sub.original, servingSize: "1 serving"}}
                substituteIngredient={{...sub.substitute, servingSize: "1 serving"}}
                benefits={benefits.map(b => ({
                  ...b,
                  icon: b.icon as 'heart' | 'leaf' | 'info' | 'clock'
                }))}
                longTermBenefits={longTermBenefit}
                preparationNotes={prepNotes}
              />
            );
          }).filter(Boolean);
        
        setSubstitutionCards(cards);
      } catch (error) {
        console.error('Error generating substitution cards:', error);
      } finally {
        setIsLoading(false);
        setRefreshing(false);
      }
    };
    
    generateSubstitutionCards();
  }, [dietaryPreferences, healthGoals, foodRestrictions, selectedFilter]);
  
  // Handle pull-to-refresh
  const handleRefresh = () => {
    setRefreshing(true);
    // This will trigger the useEffect above
  };
  
  // Get nutrient focus based on health goals
  const getNutrientFocus = () => {
    if (healthGoals.includes('weight-loss')) return 'calories';
    if (healthGoals.includes('heart-health')) return 'saturatedFat';
    if (healthGoals.includes('digestion')) return 'fiber';
    if (healthGoals.includes('blood-sugar')) return 'sugar';
    return 'calories';
  };
  
  // Only show the preferences selector on first load if no preferences are set
  useEffect(() => {
    if (!hasSavedPreferences) {
      setShowPreferencesSelector(true);
    }
  }, [hasSavedPreferences]);
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {showPreferencesSelector ? (
        <DietaryPreferencesSelector
          onComplete={() => setShowPreferencesSelector(false)}
        />
      ) : (
        <>
          <ScrollView 
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            refreshControl={
              <RefreshControl 
                refreshing={refreshing} 
                onRefresh={handleRefresh}
                colors={[colors.primary]}
                tintColor={colors.primary}
              />
            }
          >
            {/* Filters and preferences button */}
            <View style={styles.topRow}>
              <View style={styles.filtersRow}>
                <TouchableOpacity 
                  style={[
                    styles.filterButton, 
                    selectedFilter === 'all' && { backgroundColor: colors.primaryLight }
                  ]}
                  onPress={() => setSelectedFilter('all')}
                >
                  <Text style={[
                    styles.filterText, 
                    { color: selectedFilter === 'all' ? colors.primary : colors.textSecondary }
                  ]}>
                    All
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[
                    styles.filterButton, 
                    selectedFilter === 'favorites' && { backgroundColor: colors.primaryLight }
                  ]}
                  onPress={() => setSelectedFilter('favorites')}
                >
                  <Text style={[
                    styles.filterText, 
                    { color: selectedFilter === 'favorites' ? colors.primary : colors.textSecondary }
                  ]}>
                    Favorites
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[
                    styles.filterButton, 
                    selectedFilter === 'recent' && { backgroundColor: colors.primaryLight }
                  ]}
                  onPress={() => setSelectedFilter('recent')}
                >
                  <Text style={[
                    styles.filterText, 
                    { color: selectedFilter === 'recent' ? colors.primary : colors.textSecondary }
                  ]}>
                    Recent
                  </Text>
                </TouchableOpacity>
              </View>
              
              <View style={styles.actionsRow}>
                <TouchableOpacity 
                  style={[styles.preferencesButton, { backgroundColor: colors.primaryLight }]}
                  onPress={() => setShowPreferencesSelector(true)}
                >
                  <Sliders size={16} color={colors.primary} style={styles.preferencesIcon} />
                  <Text style={[styles.preferencesText, { color: colors.primary }]}>
                    Preferences
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={[styles.addButton, { backgroundColor: colors.primary }]}>
                  <PlusCircle size={16} color="white" />
                </TouchableOpacity>
              </View>
            </View>
            
            {/* Customization notice if preferences are set */}
            {(dietaryPreferences.length > 0 || healthGoals.length > 0) && (
              <View style={[styles.customizationBanner, { backgroundColor: colors.primaryLight }]}>
                <Feather name="info" size={16} color={colors.primary} style={styles.customizationIcon} />
                <Text style={[styles.customizationText, { color: colors.primary }]}>
                  Recommendations are personalized based on your preferences.
                </Text>
              </View>
            )}
            
            {/* Seasonal ingredient suggestions */}
            <SeasonalIngredientSuggestions />
            
            {/* Health impact explainer */}
            <HealthImpactExplainer
              nutrientFocus={getNutrientFocus()}
              frequencyOfSwap={
                dietaryPreferences.length > 2 ? 'consistent' : 
                dietaryPreferences.length > 0 ? 'regular' : 'occasional'
              }
            />
            
            {/* Progress tracker */}
            <NutritionImprovementTracker />
            
            {/* Substitutions */}
            <View style={styles.substitutionsSection}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  Recommended Swaps
                </Text>
                <TouchableOpacity style={styles.seeAll}>
                  <Text style={[styles.seeAllText, { color: colors.primary }]}>See All</Text>
                  <Feather name="chevron-down" size={16} color={colors.primary} />
                </TouchableOpacity>
              </View>
              
              {isLoading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={colors.primary} />
                  <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                    Finding the best alternatives for you...
                  </Text>
                </View>
              ) : substitutionCards.length > 0 ? (
                <View style={styles.cardsContainer}>
                  {substitutionCards}
                </View>
              ) : (
                <View style={[styles.emptyState, { borderColor: colors.border }]}>
                  <Text style={[styles.emptyStateTitle, { color: colors.text }]}>
                    No Matches Found
                  </Text>
                  <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
                    Try adjusting your preferences to see more alternatives.
                  </Text>
                  <TouchableOpacity 
                    style={[styles.emptyStateButton, { backgroundColor: colors.primary }]}
                    onPress={() => setShowPreferencesSelector(true)}
                  >
                    <Text style={styles.emptyStateButtonText}>
                      Adjust Preferences
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </ScrollView>
        </>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  filtersRow: {
    flexDirection: 'row',
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
  },
  actionsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  preferencesButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  preferencesIcon: {
    marginRight: 4,
  },
  preferencesText: {
    fontSize: 13,
    fontWeight: '500',
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  customizationBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 8,
    marginBottom: 16,
  },
  customizationIcon: {
    marginRight: 8,
  },
  customizationText: {
    fontSize: 13,
    flex: 1,
  },
  substitutionsSection: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  seeAll: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  seeAllText: {
    fontSize: 14,
    marginRight: 4,
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
  },
  cardsContainer: {
    marginBottom: 16,
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: 8,
  },
  emptyStateTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
  },
  emptyStateButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
}); 