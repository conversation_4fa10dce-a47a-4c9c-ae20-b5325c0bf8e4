import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Platform, Animated, ActivityIndicator, StatusBar, Dimensions } from 'react-native';
import { Stack } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { MealHistoryCard } from '@/components/MealHistoryCard';
import { useTheme } from '@/contexts/ThemeContext';
import { formatTimeForDisplay, getMealDefaultImage } from '@/utils/mealUtils';
import { getMeals, Meal, MealItem } from '@/services/databaseService';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { canUseNativeDriver } from '@/utils/platformUtils';

// Helper function to format date for display
const formatDateForDisplay = (date: Date): string => {
  const options: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric', year: 'numeric' };
  return date.toLocaleDateString('en-US', options);
};

// Helper function to format date for database query
const formatDateForDB = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

// Update the MealHistoryCard component usage to match the expected props
// First, let's define an interface that extends the database Meal type and includes the fields needed by MealHistoryCard
interface MealDisplayProps {
  id: string;
  name: string;
  time: string;
  items: {
    id: string;
    name: string;
    calories: number;
  }[];
  totalCalories: number;
  image: string;
}

export default function HistoryScreen() {
  const { colors, isDark } = useTheme();
  const insets = useSafeAreaInsets();
  const windowWidth = Dimensions.get('window').width;
  
  // Use current date as the initial selected date
  const currentDate = new Date();
  const [selectedDate, setSelectedDate] = useState<Date>(currentDate);
  const [selectedDateString, setSelectedDateString] = useState<string>(formatDateForDisplay(currentDate));
  const [mealHistory, setMealHistory] = useState<MealDisplayProps[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  
  const [filterVisible, setFilterVisible] = useState(false);
  const [filterByMeal, setFilterByMeal] = useState('all');
  const [groupByMeal, setGroupByMeal] = useState(false);
  
  // Daily nutrition summary
  const [dailySummary, setDailySummary] = useState({
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0
  });
  
  // Animation values
  const cardAnimations = useRef<Animated.Value[]>([]).current;
  const filterHeight = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(1)).current;
  
  // Fetch meals whenever selected date changes
  useEffect(() => {
    const fetchMeals = async () => {
      // Fade out animation for transition
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: canUseNativeDriver(),
      }).start(() => {
        setIsLoading(true);
        
        // After fadeout, fetch data
        fetchMealsData().then(() => {
          // Fade back in when done
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: canUseNativeDriver(),
          }).start();
        });
      });
    };
    
    const fetchMealsData = async () => {
      try {
        const formattedDate = formatDateForDB(selectedDate);
        console.log(`[History] Fetching meals for date: ${formattedDate}`);
        
        const meals = await getMeals(formattedDate);
        console.log(`[History] Fetched ${meals.length} meals from database`);
        
        // Convert database meal format to the format used by components
        const formattedMeals = meals.map(meal => {
          console.log(`[History] Processing meal: ${meal.name} (${meal.id}), items: ${meal.items?.length || 0}`);
          
          return {
            id: meal.id,
            name: meal.name,
            time: meal.time,
            items: meal.items?.map((item: MealItem) => ({
              id: item.id,
              name: item.name,
              calories: item.calories
            })) || [],
            totalCalories: meal.total_calories,
            // Use the existing image_url or a fallback based on meal type
            image: meal.image_url || getMealDefaultImage(meal.name)
          } as MealDisplayProps;
        });
        
        setMealHistory(formattedMeals as any);
        calculateDailySummary(meals);
        
        // Reset and create new animations for the cards
        cardAnimations.length = 0;
        formattedMeals.forEach(() => {
          cardAnimations.push(new Animated.Value(0));
        });
      } catch (error) {
        console.error('[History] Error fetching meals:', error);
        setMealHistory([]);
      } finally {
        setIsLoading(false);
        
        // Animate cards sequentially
        setTimeout(() => {
          cardAnimations.forEach((anim, index) => {
            Animated.timing(anim, {
              toValue: 1,
              duration: 400,
              delay: index * 100,
              useNativeDriver: canUseNativeDriver(),
            }).start();
          });
        }, 100);
      }
    };
    
    fetchMeals();
  }, [selectedDate]);
  
  useEffect(() => {
    // Animate filter panel
    Animated.timing(filterHeight, {
      toValue: filterVisible ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [filterVisible]);

  // Calculate daily nutrition summary from meals
  const calculateDailySummary = (meals: Meal[]) => {
    const summary = {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0
    };
    
    meals.forEach(meal => {
      summary.calories += meal.total_calories || 0;
      summary.protein += meal.total_protein || 0;
      summary.carbs += meal.total_carbs || 0;
      summary.fat += meal.total_fat || 0;
    });
    
    setDailySummary(summary);
  };

  const changeDate = (direction: 'prev' | 'next') => {
    // Create a new date object based on the current selected date
    const newDate = new Date(selectedDate);
    
    // Add or subtract one day
    if (direction === 'prev') {
      newDate.setDate(newDate.getDate() - 1);
    } else {
      newDate.setDate(newDate.getDate() + 1);
    }
    
    // Update state
    setSelectedDate(newDate);
    setSelectedDateString(formatDateForDisplay(newDate));
  };
  
  // Filter meals based on selected filter
  const filteredMeals = mealHistory.filter(meal => {
    if (filterByMeal === 'all') return true;
    return meal.name.toLowerCase() === filterByMeal.toLowerCase();
  });

  // Get percentage of daily calorie goal
  const caloriePercentage = Math.min(100, (dailySummary.calories / 2000) * 100);

  // Get color for calorie progress
  const getProgressColor = () => {
    if (caloriePercentage < 50) return ['#3B82F6', '#2563EB'] as const; // Blue
    if (caloriePercentage < 85) return ['#10B981', '#059669'] as const; // Green
    return ['#F97316', '#EA580C'] as const; // Orange/Red
  };

  // Add a refresh function to manually reload data
  const refreshData = () => {
    console.log('[History] Manually refreshing data...');
    const formattedDate = formatDateForDB(selectedDate);
    
    setIsLoading(true);
    
    getMeals(formattedDate)
      .then(meals => {
        console.log(`[History] Refresh: Fetched ${meals.length} meals from database`);
        
        // Convert database meal format to the format used by components
        const formattedMeals = meals.map(meal => ({
          id: meal.id,
          name: meal.name,
          time: meal.time,
          items: meal.items?.map((item: MealItem) => ({
            id: item.id,
            name: item.name,
            calories: item.calories
          })) || [],
          totalCalories: meal.total_calories,
          // Use the existing image_url or a fallback based on meal type
          image: meal.image_url || getMealDefaultImage(meal.name)
        } as MealDisplayProps));
        
        setMealHistory(formattedMeals as any);
        calculateDailySummary(meals);
        
        // Reset and create new animations for the cards
        cardAnimations.length = 0;
        formattedMeals.forEach(() => {
          cardAnimations.push(new Animated.Value(0));
        });
        
        // Animate cards sequentially
        setTimeout(() => {
          cardAnimations.forEach((anim, index) => {
            Animated.timing(anim, {
              toValue: 1,
              duration: 400,
              delay: index * 100,
              useNativeDriver: canUseNativeDriver(),
            }).start();
          });
        }, 100);
      })
      .catch(error => {
        console.error('[History] Error refreshing meals:', error);
        setMealHistory([]);
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  return (
    <>
      <Stack.Screen 
        options={{ 
          headerTitle: 'Meal History',
          headerShown: true,
          headerStyle: {
            backgroundColor: colors.card,
          },
          headerTintColor: colors.text,
          headerShadowVisible: false,
          headerTitleStyle: {
            fontWeight: '700',
            fontSize: 20,
          },
          headerRight: () => (
            <View style={styles.headerButtons}>
              <TouchableOpacity 
                style={styles.headerButton}
                onPress={refreshData}
              >
                <Feather name="pie-chart" size={20} color={colors.text} />
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.filterButton}
                onPress={() => setFilterVisible(!filterVisible)}
              >
                <ListFilter size={22} color={filterVisible ? colors.primary : colors.text} />
              </TouchableOpacity>
            </View>
          ),
        }} 
      />
      <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Date selection with improved styling */}
        <View style={[
          styles.dateSelector, 
          { 
            backgroundColor: colors.card, 
            borderBottomColor: colors.border,
            shadowColor: colors.shadow,
          }
        ]}>
          <TouchableOpacity 
            style={[styles.dateButton, { backgroundColor: colors.subtle }]}
            onPress={() => changeDate('prev')}
            activeOpacity={0.7}
          >
            <Feather name="chevron-left" size={22} color={colors.textSecondary} />
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.dateDisplay, { backgroundColor: colors.subtle }]}
            activeOpacity={0.7}
          >
            <Feather name="calendar" size={20} color={colors.textSecondary} style={styles.calendarIcon} />
            <Text style={[styles.dateText, { color: colors.text }]}>{selectedDateString}</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.dateButton, { backgroundColor: colors.subtle }]}
            onPress={() => changeDate('next')}
            activeOpacity={0.7}
          >
            <Feather name="chevron-right" size={22} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
        
        {/* Filter Panel with improved UI */}
        <Animated.View 
          style={[
            styles.filterPanel,
            {
              backgroundColor: colors.card,
              borderBottomColor: colors.border,
              height: filterHeight.interpolate({
                inputRange: [0, 1],
                outputRange: [0, 220]
              }),
              opacity: filterHeight,
              shadowColor: colors.shadow,
            }
          ]}
        >
          <View style={styles.filterHeader}>
            <Feather name="filter" size={18} color={colors.primary} style={styles.filterHeaderIcon} />
            <Text style={[styles.filterTitle, { color: colors.text }]}>Filter & Group Options</Text>
          </View>
          
          <View style={styles.filterSection}>
            <Text style={[styles.filterSectionTitle, { color: colors.textSecondary }]}>Meal Type</Text>
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              style={styles.filterOptionsScroll}
              contentContainerStyle={styles.filterOptionsScrollContent}
            >
              {['All', 'Breakfast', 'Morning Snack', 'Lunch', 'Afternoon Snack', 'Dinner', 'Evening Snack'].map((option) => (
                <TouchableOpacity
                  key={option}
                  style={[
                    styles.filterOption,
                    { 
                      backgroundColor: filterByMeal === option.toLowerCase() ? colors.primary : colors.subtle,
                      borderColor: filterByMeal === option.toLowerCase() ? colors.primary : colors.border,
                    }
                  ]}
                  onPress={() => setFilterByMeal(option.toLowerCase())}
                >
                  <Text 
                    style={[
                      styles.filterOptionText, 
                      { 
                        color: filterByMeal === option.toLowerCase() ? 'white' : colors.text 
                      }
                    ]}
                  >
                    {option}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
          
          <View style={styles.filterSection}>
            <Text style={[styles.filterSectionTitle, { color: colors.textSecondary }]}>View Mode</Text>
            <View style={styles.viewOptionsContainer}>
              <TouchableOpacity
                style={[
                  styles.viewOption,
                  { 
                    backgroundColor: !groupByMeal ? colors.primary : colors.subtle,
                    borderColor: !groupByMeal ? colors.primary : colors.border,
                  }
                ]}
                onPress={() => setGroupByMeal(false)}
              >
                <FolderOpen size={16} color={!groupByMeal ? 'white' : colors.text} style={styles.viewOptionIcon} />
                <Text 
                  style={[
                    styles.viewOptionText, 
                    { 
                      color: !groupByMeal ? 'white' : colors.text 
                    }
                  ]}
                >
                  Chronological
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.viewOption,
                  { 
                    backgroundColor: groupByMeal ? colors.primary : colors.subtle,
                    borderColor: groupByMeal ? colors.primary : colors.border,
                  }
                ]}
                onPress={() => setGroupByMeal(true)}
              >
                <FolderClosed size={16} color={groupByMeal ? 'white' : colors.text} style={styles.viewOptionIcon} />
                <Text 
                  style={[
                    styles.viewOptionText, 
                    { 
                      color: groupByMeal ? 'white' : colors.text 
                    }
                  ]}
                >
                  Group by Meal
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>

        {/* Enhanced daily summary card with gradient */}
        <Animated.View style={{ opacity: fadeAnim }}>
          <LinearGradient
            colors={isDark ? 
              ['rgba(15, 23, 42, 0.7)', 'rgba(30, 41, 59, 0.8)'] : 
              ['rgba(255, 255, 255, 0.8)', 'rgba(248, 250, 252, 0.95)']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={[
              styles.summaryCard, 
              { 
                borderColor: colors.border,
                shadowColor: colors.shadow,
              }
            ]}
          >
            <View style={styles.summaryHeader}>
              <Feather name="pie-chart" size={18} color={colors.primary} style={styles.summaryIcon} />
              <Text style={[styles.summaryTitle, { color: colors.text }]}>Daily Nutrition Summary</Text>
            </View>
            
            <View style={styles.summaryRow}>
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryValue, { color: colors.text }]}>{dailySummary.calories}</Text>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Calories</Text>
              </View>
              <View style={[styles.summaryDivider, { backgroundColor: colors.border }]} />
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryValue, { color: colors.text }]}>{dailySummary.protein > 0 ? `${Math.round(dailySummary.protein)}g` : '-'}</Text>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Protein</Text>
              </View>
              <View style={[styles.summaryDivider, { backgroundColor: colors.border }]} />
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryValue, { color: colors.text }]}>{dailySummary.carbs > 0 ? `${Math.round(dailySummary.carbs)}g` : '-'}</Text>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Carbs</Text>
              </View>
              <View style={[styles.summaryDivider, { backgroundColor: colors.border }]} />
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryValue, { color: colors.text }]}>{dailySummary.fat > 0 ? `${Math.round(dailySummary.fat)}g` : '-'}</Text>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Fat</Text>
              </View>
            </View>
            
            {/* Enhanced progress visualization */}
            <View style={styles.progressSection}>
              <View style={styles.progressInfo}>
                <Text style={[styles.progressLabel, { color: colors.textSecondary }]}>Daily Goal</Text>
                <Text style={[styles.progressValue, { color: colors.text }]}>
                  {dailySummary.calories} / 2,000 cal
                </Text>
              </View>
              <View style={[styles.progressBar, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
                <LinearGradient
                  colors={getProgressColor()}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={[
                    styles.progressFill,
                    { width: `${caloriePercentage}%` }
                  ]}
                />
              </View>
              
              {/* Percentage indicator */}
              <Text style={[styles.progressPercentage, { 
                color: isDark ? '#fff' : '#000',
                opacity: 0.7,
                left: `${Math.min(Math.max(caloriePercentage - 5, 0), 95)}%`
              }]}>
                {Math.round(caloriePercentage)}%
              </Text>
            </View>
          </LinearGradient>
        </Animated.View>

        {/* Meals list with improved animations */}
        <Animated.View style={[styles.mealsContainer, { opacity: fadeAnim }]}>
          <ScrollView 
            style={styles.mealsList}
            contentContainerStyle={[
              styles.mealsListContent,
              { paddingBottom: insets.bottom + 20 }
            ]}
            showsVerticalScrollIndicator={false}
          >
            <View style={styles.mealsHeader}>
              <Text style={[styles.mealsHeaderTitle, { color: colors.text }]}>
                {filteredMeals.length > 0 ? (
                  filterByMeal === 'all' ? 'All Meals' : `${filterByMeal.charAt(0).toUpperCase() + filterByMeal.slice(1)} Meals`
                ) : 'No Meals Found'}
              </Text>
              {filteredMeals.length > 0 && (
                <Text style={[styles.mealsCount, { color: colors.textSecondary }]}>
                  {filteredMeals.length} {filteredMeals.length === 1 ? 'meal' : 'meals'}
                </Text>
              )}
            </View>
            
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={[styles.loadingText, { color: colors.text }]}>Loading meals...</Text>
              </View>
            ) : (
              <>
                {filteredMeals.map((meal, index) => (
                  <Animated.View 
                    key={meal.id}
                    style={{
                      opacity: cardAnimations[index] || new Animated.Value(1),
                      transform: [
                        { 
                          translateY: (cardAnimations[index] || new Animated.Value(1)).interpolate({
                            inputRange: [0, 1],
                            outputRange: [50, 0]
                          }) 
                        }
                      ]
                    }}
                  >
                    <MealHistoryCard meal={meal} />
                  </Animated.View>
                ))}
                
                {filteredMeals.length === 0 && !isLoading && (
                  <View style={[styles.emptyState, { 
                    borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                    backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)'
                  }]}>
                    <Text style={[styles.emptyStateTitle, { color: colors.text }]}>
                      No meals found
                    </Text>
                    <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
                      No {filterByMeal !== 'all' ? filterByMeal : ''} meals recorded for {selectedDateString}.
                    </Text>
                    {filterByMeal !== 'all' && (
                      <TouchableOpacity
                        style={[styles.emptyStateButton, { backgroundColor: colors.primaryLight }]}
                        onPress={() => setFilterByMeal('all')}
                      >
                        <Text style={[styles.emptyStateButtonText, { color: colors.primary }]}>
                          Reset Filters
                        </Text>
                      </TouchableOpacity>
                    )}
                  </View>
                )}
              </>
            )}
          </ScrollView>
        </Animated.View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  dateSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.05,
        shadowRadius: 8,
      },
      android: {
        elevation: 2,
      },
      web: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.05,
        shadowRadius: 8,
      }
    }),
  },
  dateButton: {
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 24,
  },
  dateDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 18,
    paddingVertical: 12,
    borderRadius: 24,
  },
  calendarIcon: {
    marginRight: 10,
  },
  dateText: {
    fontSize: 17,
    fontWeight: '600',
  },
  filterButton: {
    marginRight: 16,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterPanel: {
    borderBottomWidth: 1,
    overflow: 'hidden',
    paddingHorizontal: 20,
    ...Platform.select({
      ios: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.05,
        shadowRadius: 8,
      },
      android: {
        elevation: 2,
      },
      web: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.05,
        shadowRadius: 8,
      }
    }),
  },
  filterHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 16,
  },
  filterHeaderIcon: {
    marginRight: 10,
  },
  filterTitle: {
    fontSize: 17,
    fontWeight: '700',
  },
  filterSection: {
    marginBottom: 18,
  },
  filterSectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 12,
  },
  filterOptionsScroll: {
    marginLeft: -5,
  },
  filterOptionsScrollContent: {
    paddingLeft: 5,
    paddingRight: 20,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  filterOption: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
  },
  filterOptionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  viewOptionsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  viewOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    marginRight: 12,
    marginBottom: 8,
    borderWidth: 1,
  },
  viewOptionIcon: {
    marginRight: 8,
  },
  viewOptionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  summaryCard: {
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 10,
    padding: 20,
    borderRadius: 20,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: {
          width: 0,
          height: 4,
        },
        shadowOpacity: 0.12,
        shadowRadius: 10,
      },
      android: {
        elevation: 4,
      },
      web: {
        shadowOffset: {
          width: 0,
          height: 4,
        },
        shadowOpacity: 0.12,
        shadowRadius: 10,
      }
    }),
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  summaryIcon: {
    marginRight: 10,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  summaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  summaryDivider: {
    width: 1,
    height: 36,
  },
  summaryValue: {
    fontSize: 20,
    fontWeight: '800',
  },
  summaryLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 5,
  },
  progressSection: {
    marginTop: 8,
    position: 'relative',
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  progressLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressValue: {
    fontSize: 14,
    fontWeight: '700',
  },
  progressBar: {
    height: 12,
    borderRadius: 6,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 6,
  },
  progressPercentage: {
    position: 'absolute',
    bottom: -18,
    fontSize: 11,
    fontWeight: '700',
  },
  mealsContainer: {
    flex: 1,
  },
  mealsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 10,
    marginTop: 10,
  },
  mealsHeaderTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  mealsCount: {
    fontSize: 14,
    fontWeight: '500',
  },
  mealsList: {
    flex: 1,
  },
  mealsListContent: {
    paddingHorizontal: 20,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '600',
  },
  emptyState: {
    padding: 30,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: 20,
    alignItems: 'center',
    marginTop: 20,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  emptyStateButton: {
    paddingHorizontal: 18,
    paddingVertical: 12,
    borderRadius: 20,
  },
  emptyStateButtonText: {
    fontWeight: '600',
    fontSize: 14,
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 4,
  },
});