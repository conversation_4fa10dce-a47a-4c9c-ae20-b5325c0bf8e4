import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { StyleSheet, View, ScrollView, RefreshControl, Modal, Text, ActivityIndicator, Pressable, Alert, TextInput, TouchableOpacity , Animated, Platform , useWindowDimensions } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { checkSubscription } from '@/services/stripeService';
import { getProfile, getMeals, getWaterIntake, updateWaterIntake, Meal, WaterIntake, Profile } from '@/services/databaseService';
import { canUseNativeDriver } from '@/utils/platformUtils';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import useScreenTracking from '@/hooks/useScreenTracking';
import { WeightCalibration } from '@/components/ScanComponents/WeightCalibration';
import { StatusBar } from 'expo-status-bar';
import { BlurView } from 'expo-blur';
import { getAuth } from 'firebase/auth';
import { auth } from '@/lib/firebase';

// Import our component files with @ alias
import { HeaderSection } from '@/components/HomeScreenComponents/HeaderSection';
import { StatsSummary } from '@/components/HomeScreenComponents/StatsSummary';
import { ActionButtons } from '@/components/HomeScreenComponents/ActionButtons';
import { NutritionSummary } from '@/components/HomeScreenComponents/NutritionSummary';
import { TodaysMeals } from '@/components/HomeScreenComponents/TodaysMeals';
import { WeeklyTrends } from '@/components/HomeScreenComponents/WeeklyTrends';
// Removed DailyRecommendations and MindfulnessReminderCard imports

export default function HomeScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const { user } = useAuth();
  const { width: screenWidth } = useWindowDimensions();
  const insets = useSafeAreaInsets();
  const [devMenuPressCount, setDevMenuPressCount] = useState(0);
  const devMenuPressTimeout = useRef<NodeJS.Timeout | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [username, setUsername] = useState('');
  const [dailyStats, setDailyStats] = useState({
    calories: 0,
    target: 2000,
    protein: 0,
    carbs: 0,
    fat: 0
  });

  const [hasSubscription, setHasSubscription] = useState(false);
  const [recentMeals, setRecentMeals] = useState<{
    id: string;
    name: string;
    time: string;
    calories: number;
    image: string;
  }[]>([]);
  
  // Use memo for static data to prevent recreating on each render
  const weeklyData = useMemo(() => [
    { day: 'Mon', calories: 1800, goal: 2000 },
    { day: 'Tue', calories: 1950, goal: 2000 },
    { day: 'Wed', calories: 1760, goal: 2000 },
    { day: 'Thu', calories: 2100, goal: 2000 }, // Over goal
    { day: 'Fri', calories: 2050, goal: 2000 }, // Over goal
    { day: 'Sat', calories: 1600, goal: 2000 },
    { day: 'Sun', calories: 1450, goal: 2000 }, // Today
  ], []);
  
  // Calculate streak (number of consecutive days logging food)
  const [streak, setStreak] = useState(7);
  
  // Water intake data
  const [waterIntake, setWaterIntake] = useState(0); // ml
  const [waterGoal, setWaterGoal] = useState(2000); // ml
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(30)).current;
  const bannerScaleX = useRef(new Animated.Value(0)).current;
  const spinAnim = useRef(new Animated.Value(0)).current;
  
  // Set initial loading state to false to ensure components render
  const [isLoading, setIsLoading] = useState(false);
  
  // Track screen for contextual navigation
  useScreenTracking();
  
  const [showWeightCalibration, setShowWeightCalibration] = useState(false);
  
  const [showQuickWaterInput, setShowQuickWaterInput] = useState<boolean>(false);
  
  // Helper function for placeholder images - memoize to prevent unnecessary recreation
  const getPlaceholderImage = useCallback((mealName: string) => {
    const mealTypes = {
      breakfast: 'https://images.pexels.com/photos/376464/pexels-photo-376464.jpeg?auto=compress&cs=tinysrgb&w=600',
      lunch: 'https://images.pexels.com/photos/1640772/pexels-photo-1640772.jpeg?auto=compress&cs=tinysrgb&w=600',
      dinner: 'https://images.pexels.com/photos/1279330/pexels-photo-1279330.jpeg?auto=compress&cs=tinysrgb&w=600',
      snack: 'https://images.pexels.com/photos/1092730/pexels-photo-1092730.jpeg?auto=compress&cs=tinysrgb&w=600'
    };
    
    const lowerName = mealName.toLowerCase();
    
    for (const [type, url] of Object.entries(mealTypes)) {
      if (lowerName.includes(type)) {
        return url;
      }
    }
    
    // Default image
    return 'https://images.pexels.com/photos/1640774/pexels-photo-1640774.jpeg?auto=compress&cs=tinysrgb&w=600';
  }, []);
  
  // Use memoized animations for better performance
  const startAnimations = useCallback(() => {
    console.debug('Home: Starting animations');
    
    try {
      // Ensure animation values are reset
      fadeAnim.setValue(0);
      translateY.setValue(30);
      bannerScaleX.setValue(0);
      
      // Start main animations
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: canUseNativeDriver(),
        }),
        Animated.timing(translateY, {
          toValue: 0,
          duration: 600,
          useNativeDriver: canUseNativeDriver(),
        }),
        Animated.spring(bannerScaleX, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: canUseNativeDriver(),
        }),
      ]).start(() => {
        console.debug('Home: Main animations completed');
        
        // Start streak spin animation after main animations complete
        Animated.loop(
          Animated.sequence([
            Animated.timing(spinAnim, {
              toValue: 1,
              duration: 10000,
              useNativeDriver: canUseNativeDriver(),
            }),
            Animated.timing(spinAnim, {
              toValue: 0,
              duration: 0,
              useNativeDriver: canUseNativeDriver(),
            }),
          ])
        ).start();
      });
    } catch (error) {
      console.error('Error in animations:', error);
    }
  }, [fadeAnim, translateY, bannerScaleX, spinAnim]);
  
  // Memoized verification function to prevent recreating on each render
  const ensureAuthenticated = useCallback(async () => {
    try {
      // Check if user is authenticated
      const currentUser = auth.currentUser;
      
      if (!currentUser) {
        console.log('No active user session');
        // Redirect to authentication screen instead of creating test users
        router.replace('/(auth)/login');
        return false;
      } else {
        console.log('User authenticated as:', currentUser.uid);
        return true;
      }
    } catch (error) {
      console.error('Authentication verification error:', error);
      return false;
    }
  }, [router]);
  
  // Load profile data immediately when the component mounts
  useEffect(() => {
    async function loadProfileData() {
      try {
        // Ensure we're authenticated
        const isAuthenticated = await ensureAuthenticated();
        if (!isAuthenticated) return;
        
        // Fetch user profile with type assertion
        const profile = await getProfile() as Profile;
        console.log('Initial profile data loaded:', profile?.full_name || 'No name available');
        
        if (profile && profile.full_name) {
          setUsername(profile.full_name);
        } else {
          // Try to use auth metadata as fallback
          const user = auth.currentUser;
          const fallbackName = user?.displayName || user?.email?.split('@')[0] || '';
          const formattedName = fallbackName ? fallbackName.charAt(0).toUpperCase() + fallbackName.slice(1) : '';
          setUsername(formattedName || '');
          console.log('Setting fallback username to:', formattedName);
        }
      } catch (error) {
        console.error('Error loading profile data:', error);
      }
    }
    
    loadProfileData();
  }, [ensureAuthenticated]);
  
  // Memoized function to load data, improving performance with useCallback
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setIsLoading(true);
      console.debug('Home: Starting to load profile data');
      
      // Ensure we're authenticated
      const isAuthenticated = await ensureAuthenticated();
      if (!isAuthenticated) return;
      
      // Fetch user profile with type assertion
      const profile = await getProfile() as Profile;
      console.debug('Home: Profile data loaded', profile);
      
      if (profile) {
        // Validate profile data before using
        if (typeof profile !== 'object') {
          console.error('Invalid profile data format');
          return;
        }
        
        // Use the profile full_name or a default value if not available
        if (profile.full_name && typeof profile.full_name === 'string') {
          console.log('Setting username to:', profile.full_name);
          setUsername(profile.full_name);
        } else {
          // Try to use auth metadata as fallback
          const user = auth.currentUser;
          const fallbackName = user?.displayName || user?.email?.split('@')[0] || '';
          const formattedName = fallbackName ? fallbackName.charAt(0).toUpperCase() + fallbackName.slice(1) : '';
          setUsername(formattedName || '');
          console.log('Setting fallback username to:', formattedName);
        }
        
        if (profile.daily_calorie_goal && typeof profile.daily_calorie_goal === 'number' && profile.daily_calorie_goal > 0) {
          setDailyStats(prev => ({ ...prev, target: profile.daily_calorie_goal || prev.target }));
        }
        if (profile.daily_water_goal && typeof profile.daily_water_goal === 'number' && profile.daily_water_goal > 0) {
          setWaterGoal(profile.daily_water_goal);
        }
      } else {
        console.warn('No profile data returned from getProfile()');
        // Don't set a fallback here - we already tried in the initial load
      }
      
      // Execute meal and water intake data fetching in parallel for better performance
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
      
      const [mealsData, waterData] = await Promise.all([
        getMeals(today) as Promise<Meal[]>,
        getWaterIntake() as Promise<WaterIntake>
      ]);
      
      // Process meals data - with validation
      console.debug('Home: Meals data loaded', mealsData);
      if (Array.isArray(mealsData) && mealsData.length > 0) {
        // Process meals to calculate total nutrition
        let totalCalories = 0;
        let totalProtein = 0;
        let totalCarbs = 0;
        let totalFat = 0;
        
        const formattedMeals = mealsData
          // Filter out invalid meal entries
          .filter(meal => 
            meal && 
            typeof meal === 'object' && 
            typeof meal.name === 'string' && 
            typeof meal.date === 'string' && 
            typeof meal.time === 'string'
          )
          .map(meal => {
            // Ensure numeric values are actually numbers
            const calories = typeof meal.total_calories === 'number' ? meal.total_calories : 0;
            const protein = typeof meal.total_protein === 'number' ? meal.total_protein : 0;
            const carbs = typeof meal.total_carbs === 'number' ? meal.total_carbs : 0; 
            const fat = typeof meal.total_fat === 'number' ? meal.total_fat : 0;
            
            totalCalories += calories;
            totalProtein += protein;
            totalCarbs += carbs;
            totalFat += fat;
            
            // Format for display
            return {
              id: meal.id,
              name: meal.name,
              time: new Date(meal.date + 'T' + meal.time).toLocaleTimeString([], { 
                hour: 'numeric', 
                minute: '2-digit' 
              }),
              calories: calories,
              image: typeof meal.image_url === 'string' ? meal.image_url : getPlaceholderImage(meal.name)
            };
          });
        
        setRecentMeals(formattedMeals);
        setDailyStats({
          calories: Math.round(totalCalories),
          target: dailyStats.target,
          protein: Math.round(totalProtein),
          carbs: Math.round(totalCarbs),
          fat: Math.round(totalFat)
        });
      } else {
        console.log('No meals found for today');
      }
      
      // Process water intake data - with validation
      console.debug('Home: Water intake data loaded', waterData);
      if (waterData && typeof waterData === 'object') {
        const amount = typeof waterData.amount_ml === 'number' ? waterData.amount_ml : 0;
        setWaterIntake(amount);
      } else {
        console.log('No water intake data found for today');
      }
      
      // Ensure animations are started
      console.debug('Home: Starting animations after data load');
      startAnimations();
      
    } catch (error) {
      console.error('Error loading data:', error);
      // Force loading to complete even if there's an error
      setIsLoading(false);
      setLoading(false);
      
      // Still try to start animations despite error
      startAnimations();
    } finally {
      console.debug('Home: Setting loading state to false');
      // Ensure loading states are set to false
      setLoading(false);
      setRefreshing(false);
      setIsLoading(false);
    }
  }, [dailyStats.target, startAnimations, ensureAuthenticated, getPlaceholderImage]);
  
  useEffect(() => {
    console.debug('Home: Component mounted');
    
    // Load data immediately instead of with delay
    loadData();
    
    // Ensure loading completes after a timeout
    const timeoutId = setTimeout(() => {
      console.debug('Home: Force-completing loading after timeout');
      setLoading(false);
      setIsLoading(false);
      setRefreshing(false);
      
      // Try to start animations again
      startAnimations();
    }, 5000);
    
    // Check subscription status
    const checkSubscriptionStatus = async () => {
      if (user) {
        try {
          const result = await checkSubscription();
          setHasSubscription(result.subscription !== null && !result.error);
        } catch (error) {
          console.error('Error checking subscription:', error);
        }
      }
    };
    
    checkSubscriptionStatus();
    
    // Clean up timers
    return () => {
      clearTimeout(timeoutId);
    };
  }, [user, loadData, startAnimations]);
  
  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    loadData();
  }, [loadData]);
  
  // Format date and get weekday name - memoize to prevent recalculation
  const formattedDate = useMemo(() => {
  const today = new Date();
  const dateOptions = { weekday: 'long', month: 'long', day: 'numeric' };
    return today.toLocaleDateString('en-US', dateOptions as any);
  }, []);
  
  // Dev menu handler - tap 5 times on header to enable
  const handleDevMenuPress = useCallback(() => {
    if (process.env.EXPO_PUBLIC_ENABLE_DEV_MENU === 'true' && __DEV__) {
      setDevMenuPressCount(count => count + 1);
      
      // Clear previous timeout
      if (devMenuPressTimeout.current) {
        clearTimeout(devMenuPressTimeout.current);
      }
      
      // Reset counter after 2 seconds
      devMenuPressTimeout.current = setTimeout(() => {
        setDevMenuPressCount(0);
      }, 2000);
      
      // Navigate to dev menu after 5 taps
      if (devMenuPressCount >= 4) {
        setDevMenuPressCount(0);
        router.push('/dev-menu');
      }
    }
  }, [devMenuPressCount, router]);

  // Handle navigation actions - memoize callbacks
  const handleAddFood = useCallback(() => {
    try {
    router.push('/(tabs)/scan');
    } catch (error) {
      console.error('Navigation error when trying to open scan screen:', error);
      // Fallback to another valid route if scan isn't available
      try {
        router.push('/');
      } catch (err) {
        console.error('Failed to navigate to fallback route:', err);
      }
    }
  }, [router]);
  
  const handleAddWater = useCallback(() => {
    try {
      // Try direct path first
      try {
    router.push('/water-tracker');
      } catch (primaryError) {
        console.log('Primary route failed, trying alternate route');
        // Try the full path with nested structure if simple path doesn't work
        router.push('/profile-screens/water-tracker');
      }
    } catch (error) {
      console.error('Navigation error when trying to open water tracker:', error);
      // Show modal as alternative UI if navigation fails
      Alert.alert(
        'Quick Water Intake',
        'Would you like to add water intake directly?',
        [
          {
            text: 'Yes',
            onPress: () => setShowQuickWaterInput(true)
          },
          {
            text: 'Cancel',
            style: 'cancel'
          }
        ]
      );
    }
  }, [router]);
  
  const handleOpenWeightCalibration = useCallback(() => {
    setShowWeightCalibration(true);
  }, []);

  const handleCloseWeightCalibration = useCallback(() => {
    setShowWeightCalibration(false);
  }, []);
  
  // Force reload function - memoize to prevent recreation
  const forceReload = useCallback(() => {
    console.debug('Force reloading dashboard components');
    // First set everything to a clean state
    setIsLoading(false);
    setLoading(false);
    setRefreshing(false);
    
    // Force start animations with a slight delay
    setTimeout(() => {
      startAnimations();
    }, 50);
  }, [startAnimations]);
  
  // Add a new useEffect to ensure animations are initialized
  useEffect(() => {
    console.debug('Home: Initializing animation values');
    // Ensure animation values are set to their initial values
    fadeAnim.setValue(0);
    translateY.setValue(30);
    bannerScaleX.setValue(0);
  }, [fadeAnim, translateY, bannerScaleX]);
  
  // Calculate bottom padding to avoid cutting off content
  const bottomPadding = Math.max(insets.bottom + 20, 50);
  
  // Memoize styles that depend on theme for better performance
  const containerStyle = useMemo(() => 
    [styles.container, { backgroundColor: isDark ? colors.background : '#f8f9fa' }],
    [isDark, colors.background]
  );

  const scrollViewContentStyle = useMemo(() => 
    [styles.scrollViewContent, {
      paddingHorizontal: screenWidth > 390 ? 16 : 12,
      paddingBottom: bottomPadding,
    }],
    [screenWidth, bottomPadding]
  );

  const mainContainerStyle = useMemo(() => 
    [styles.mainContainer, {
      opacity: fadeAnim,
      transform: [{ translateY: translateY }],
    }],
    [fadeAnim, translateY]
  );

  const sectionCardStyle = useMemo(() => 
    [styles.sectionCard, {
      backgroundColor: isDark ? 'rgba(30,30,40,0.6)' : 'rgba(255,255,255,0.6)',
      shadowOpacity: isDark ? 0.2 : 0.1,
      borderWidth: isDark ? 1 : 0,
      borderColor: 'rgba(255,255,255,0.08)',
    }],
    [isDark]
  );

  const loadingIndicatorStyle = useMemo(() => 
    [styles.loadingIndicatorContainer, {
      backgroundColor: isDark ? 'rgba(30,30,40,0.85)' : 'rgba(255,255,255,0.9)',
      borderColor: isDark ? 'rgba(80,80,100,0.3)' : 'rgba(0,0,0,0.05)',
    }],
    [isDark]
  );

  const loadingTextStyle = useMemo(() => 
    [styles.loadingText, {color: colors.text}],
    [colors.text]
  );
  
  // QuickWaterInput component for fallback when navigation fails
  const QuickWaterInput = ({ 
    visible, 
    onClose, 
    onSubmit 
  }: { 
    visible: boolean; 
    onClose: () => void; 
    onSubmit: (amount: number) => void;
  }) => {
    const { isDark, colors } = useTheme();
    const [amount, setAmount] = useState<string>('250');
    
    const handleSubmit = () => {
      const numAmount = parseInt(amount, 10);
      if (!isNaN(numAmount) && numAmount > 0) {
        onSubmit(numAmount);
        onClose();
      }
    };
    
    return (
      <Modal
        visible={visible}
        animationType="slide"
        onRequestClose={onClose}
        transparent
        accessible
        accessibilityViewIsModal={true}
        accessibilityLabel="Quick Water Input"
      >
        <Pressable 
          style={styles.modalBackdrop}
          onPress={onClose}
        >
          <Pressable onPress={e => e.stopPropagation()}>
            <BlurView 
              style={styles.modalContent}
              intensity={90}
              tint={isDark ? 'dark' : 'light'}
            >
              <View style={styles.quickWaterContainer}>
                <Text style={[styles.quickWaterTitle, { color: colors.text }]}>
                  Add Water
                </Text>
                <TextInput
                  style={[styles.quickWaterInput, { 
                    backgroundColor: isDark ? 'rgba(50,50,60,0.8)' : 'rgba(240,240,240,0.8)',
                    color: colors.text 
                  }]}
                  value={amount}
                  onChangeText={setAmount}
                  placeholder="Amount in ml"
                  placeholderTextColor={isDark ? 'rgba(200,200,200,0.6)' : 'rgba(100,100,100,0.6)'}
                  keyboardType="number-pad"
                  returnKeyType="done"
                  onSubmitEditing={handleSubmit}
                  accessible
                  accessibilityLabel="Enter water amount in milliliters"
                />
                <View style={styles.quickWaterButtonRow}>
                  <TouchableOpacity
                    style={[styles.quickWaterButton, { 
                      backgroundColor: isDark ? 'rgba(40,40,50,0.8)' : 'rgba(240,240,240,0.8)' 
                    }]}
                    onPress={onClose}
                    accessible
                    accessibilityLabel="Cancel"
                    accessibilityRole="button"
                  >
                    <Text style={[styles.quickWaterButtonText, { color: colors.text }]}>
                      Cancel
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.quickWaterButton, { backgroundColor: colors.primary }]}
                    onPress={handleSubmit}
                    accessible
                    accessibilityLabel="Add water intake"
                    accessibilityRole="button"
                  >
                    <Text style={[styles.quickWaterButtonText, { color: '#ffffff' }]}>
                      Add
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </BlurView>
          </Pressable>
        </Pressable>
      </Modal>
    );
  };
  
  return (
    <>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      
      <SafeAreaView 
        style={containerStyle}
        edges={['top', 'right', 'left']}
        accessible
        accessibilityLabel="Home Dashboard"
        accessibilityHint="Main dashboard showing your health data"
      >
        {/* HeaderSection component - tap 5 times to open dev menu */}
        <Pressable onPress={handleDevMenuPress}>
          <HeaderSection 
            username={username} 
            formattedDate={formattedDate} 
          />
        </Pressable>
        
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={scrollViewContentStyle}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
              accessible
              accessibilityLabel="Pull to refresh"
              accessibilityHint="Pull down to refresh your health data"
            />
          }
          overScrollMode="never"
          bounces={true}
        >
          {/* Animated Container */}
          <Animated.View style={mainContainerStyle}>
            {/* Stats Summary */}
            <StatsSummary 
              fadeAnim={fadeAnim} 
              dailyStats={dailyStats} 
              waterIntake={waterIntake} 
              waterGoal={waterGoal}
            />
            
            {/* Quick Actions */}
            <ActionButtons 
              fadeAnim={fadeAnim} 
              translateY={translateY} 
              onAddFood={handleAddFood} 
              onAddWater={handleAddWater} 
            />
            
            {/* Nutrition Summary */}
            <View style={sectionCardStyle}>
              <NutritionSummary 
                fadeAnim={fadeAnim} 
                translateY={translateY} 
                dailyStats={dailyStats} 
              />
            </View>
            
            {/* Today's Meals */}
            <View style={sectionCardStyle}>
              <TodaysMeals 
                fadeAnim={fadeAnim} 
                translateY={translateY} 
                recentMeals={recentMeals} 
                onAddFood={handleAddFood} 
              />
            </View>
            
            {/* Weekly Trends */}
            <View style={sectionCardStyle}>
              <WeeklyTrends 
                fadeAnim={fadeAnim} 
                translateY={translateY} 
                weeklyData={weeklyData} 
                streak={streak} 
              />
            </View>
            
            {/* Removed DailyRecommendations component */}
            
            {/* Removed MindfulnessReminderCard component */}
          </Animated.View>
        </ScrollView>
        
        {(isLoading || refreshing) && (
          <BlurView 
            style={styles.loadingOverlay}
            intensity={50}
            tint={isDark ? 'dark' : 'light'}
            accessible
            accessibilityLabel="Loading data"
            accessibilityHint="Please wait while your health data is loading"
          >
            <Animated.View style={loadingIndicatorStyle}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={loadingTextStyle}>Loading your data...</Text>
            </Animated.View>
          </BlurView>
        )}
      </SafeAreaView>
      
      {/* Weight Calibration Modal */}
      <Modal
        visible={showWeightCalibration}
        animationType="slide"
        onRequestClose={handleCloseWeightCalibration}
        transparent
        accessible
        accessibilityViewIsModal={true}
        accessibilityLabel="Weight Calibration"
      >
        <Pressable 
          style={styles.modalBackdrop}
          onPress={handleCloseWeightCalibration}
          accessible
          accessibilityLabel="Close weight calibration"
          accessibilityRole="button"
        >
          <Pressable onPress={e => e.stopPropagation()}>
            <BlurView 
              style={styles.modalContent}
              intensity={90}
              tint={isDark ? 'dark' : 'light'}
            >
              <WeightCalibration
                onComplete={handleCloseWeightCalibration}
                onClose={handleCloseWeightCalibration}
              />
            </BlurView>
          </Pressable>
        </Pressable>
      </Modal>
      
      {/* QuickWaterInput component */}
      <QuickWaterInput
        visible={showQuickWaterInput}
        onClose={() => setShowQuickWaterInput(false)}
        onSubmit={(amount) => {
          // Update state immediately for responsive UI
          setWaterIntake(prev => prev + amount);
          
          // Save to database
          updateWaterIntake(amount)
            .then(result => {
              if (result.success) {
                console.log('Water intake updated successfully');
              } else {
                console.error('Failed to update water intake:', result.error);
                // Still update the UI even if database update failed
                // Don't show error alerts to users
              }
            })
            .catch(err => {
              console.error('Exception updating water intake:', err);
              // Still update the UI even if database update failed
              // Don't show error alerts to users
            })
            .finally(() => {
              setShowQuickWaterInput(false);
            });
        }}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  mainContainer: {
    paddingTop: 16,
    gap: 16,
  },
  sectionCard: {
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowRadius: 2.22,
    elevation: 3,
    overflow: 'hidden',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
  },
  loadingIndicatorContainer: {
    padding: 24,
    borderRadius: 20,
    alignItems: 'center',
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 10,
    minWidth: 160,
  },
  loadingText: {
    marginTop: 16,
    fontWeight: '600',
    fontSize: 16,
    textAlign: 'center',
  },
  modalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxWidth: 500,
    borderRadius: 24,
    overflow: 'hidden',
  },
  quickWaterContainer: {
    padding: 24,
  },
  quickWaterTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  quickWaterInput: {
    padding: 12,
    borderRadius: 8,
    fontSize: 18,
    marginBottom: 16,
  },
  quickWaterButtonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  quickWaterButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  quickWaterButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

// Helper functions moved to their respective components