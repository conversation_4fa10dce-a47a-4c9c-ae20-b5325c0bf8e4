import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, ActivityIndicator, FlatList, RefreshControl, Image } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Feather } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import {
  getMindfulnessTips,
  getUserMindfulnessSessions,
  recordMindfulnessSession,
  MindfulnessTip as BaseMindfulnessTip,
  MindfulnessSession
} from '@/services/databaseService';
import { generatePersonalizedTip } from '@/services/openai/mindfulnessService';

// Extend the base MindfulnessTip type with image_url
interface MindfulnessTip extends BaseMindfulnessTip {
  image_url?: string;
}

// Default fallback image URL if tips don't have one
const FALLBACK_IMAGE_URL = 'https://images.pexels.com/photos/3560168/pexels-photo-3560168.jpeg?auto=compress&cs=tinysrgb&w=600';

// Function to get an appropriate image URL for a category
function getCategoryImage(category: string): string {
  switch (category) {
    case 'nutrition':
      return 'https://images.pexels.com/photos/1640773/pexels-photo-1640773.jpeg?auto=compress&cs=tinysrgb&w=600';
    case 'stress-relief':
      return 'https://images.pexels.com/photos/775417/pexels-photo-775417.jpeg?auto=compress&cs=tinysrgb&w=600';
    case 'mental-health':
      return 'https://images.pexels.com/photos/3560044/pexels-photo-3560044.jpeg?auto=compress&cs=tinysrgb&w=600';
    case 'movement':
      return 'https://images.pexels.com/photos/4056723/pexels-photo-4056723.jpeg?auto=compress&cs=tinysrgb&w=600';
    case 'wellness':
      return 'https://images.pexels.com/photos/3560168/pexels-photo-3560168.jpeg?auto=compress&cs=tinysrgb&w=600';
    default:
      return FALLBACK_IMAGE_URL;
  }
}

export default function MindfulnessScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [tips, setTips] = useState<MindfulnessTip[]>([]);
  const [sessions, setSessions] = useState<MindfulnessSession[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [stats, setStats] = useState({
    total: 0,
    streak: 0,
    thisWeek: 0,
  });
  const [generatingAiTip, setGeneratingAiTip] = useState(false);
  
  useEffect(() => {
    loadData();
  }, []);
  
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load mindfulness tips
      const tipsData = await getMindfulnessTips();
      
      // Ensure all tips have image URLs
      const tipsWithImages = tipsData.map(tip => {
        if (!tip.image_url) {
          return {
            ...tip,
            image_url: getCategoryImage(tip.category),
          } as MindfulnessTip;
        }
        return tip as MindfulnessTip;
      });
      
      setTips(tipsWithImages);
      
      // Load user's mindfulness sessions
      const sessionsData = await getUserMindfulnessSessions();
      setSessions(sessionsData);
      
      // Calculate stats
      if (sessionsData.length > 0) {
        // Get total count
        const total = sessionsData.length;
        
        // Calculate this week's sessions
        const today = new Date();
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        startOfWeek.setHours(0, 0, 0, 0);
        
        const thisWeekSessions = sessionsData.filter(session => {
          const sessionDate = new Date(session.session_date);
          return sessionDate >= startOfWeek;
        });
        
        // Calculate streak (consecutive days with sessions)
        let streak = 0;
        const sessionDates = new Set(sessionsData.map(s => s.session_date));
        const sortedDates = Array.from(sessionDates).sort((a, b) => new Date(b).getTime() - new Date(a).getTime());
        
        if (sortedDates.length > 0) {
          const latestDate = new Date(sortedDates[0]);
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          
          // Check if latest session is from today or yesterday
          const diffInDays = Math.floor((today.getTime() - latestDate.getTime()) / (1000 * 60 * 60 * 24));
          if (diffInDays <= 1) {
            streak = 1; // At least 1 if we have a session from today or yesterday
            
            // Check for continuous dates going back
            const checkDate = new Date(latestDate);
            for (let i = 1; i < 30; i++) { // Check up to 30 days back
              checkDate.setDate(checkDate.getDate() - 1);
              const dateString = checkDate.toISOString().split('T')[0];
              if (sessionDates.has(dateString)) {
                streak++;
              } else {
                break;
              }
            }
          }
        }
        
        setStats({
          total,
          streak,
          thisWeek: thisWeekSessions.length,
        });
      }
    } catch (error) {
      console.error('Error loading mindfulness data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  const handleRefresh = () => {
    setRefreshing(true);
    loadData();
  };
  
  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category === selectedCategory ? null : category);
  };
  
  const handleCompleteTip = async (tip: MindfulnessTip) => {
    try {
      // Record the mindfulness session
      const result = await recordMindfulnessSession({
        tip_id: tip.id,
        session_date: new Date().toISOString().split('T')[0],
        completed: true,
      });
      
      if (result.success) {
        // Refresh session data
        const sessionsData = await getUserMindfulnessSessions();
        setSessions(sessionsData);
        
        // Update stats
        setStats(prev => ({
          ...prev,
          total: prev.total + 1,
          thisWeek: prev.thisWeek + 1,
          // Streak is more complex and will be recalculated on full refresh
        }));
      }
    } catch (error) {
      console.error('Error completing mindfulness activity:', error);
    }
  };
  
  const generateAiTip = async () => {
    try {
      setGeneratingAiTip(true);
      
      // Generate a personalized tip based on user data
      const personalizedTip = await generatePersonalizedTip({
        sleepHours: 7, // For demo, would normally get from health data
        steps: 4500,    // For demo, would normally get from health data
        waterIntake: stats.total * 200, // Just an example correlation
      });
      
      if (personalizedTip) {
        // Convert to MindfulnessTip format
        const aiTip: MindfulnessTip = {
          id: 'ai-generated-' + Date.now(),
          title: personalizedTip.title,
          message: personalizedTip.message,
          icon: personalizedTip.icon,
          category: personalizedTip.category,
          gradient_start: personalizedTip.gradient_start,
          gradient_end: personalizedTip.gradient_end,
          image_url: personalizedTip.image_url || getCategoryImage(personalizedTip.category),
        };
        
        // Add to the beginning of the tips array
        setTips(prev => [aiTip, ...prev]);
      }
    } catch (error) {
      console.error('Error generating AI tip:', error);
    } finally {
      setGeneratingAiTip(false);
    }
  };
  
  const getIconForCategory = (category: string) => {
    switch (category) {
      case 'nutrition':
        return <Feather name="sun" size={18}  color={colors.text} />;
      case 'stress-relief':
        return <Wind size={18} color="#fff" />;
      case 'mental-health':
        return <Feather name="heart" size={18}  color={colors.text} />;
      case 'movement':
        return <Feather size={18} color="#fff" />;
      default:
        return <Droplets size={18} color="#fff" />;
    }
  };
  
  const getGradientForCategory = (category: string) => {
    switch (category) {
      case 'nutrition':
        return ['#FF9500', '#FF5A5F'];
      case 'stress-relief':
        return ['#9CECFB', '#65C7F7'];
      case 'mental-health':
        return ['#B06AB3', '#4568DC'];
      case 'movement':
        return ['#F09819', '#EDDE5D'];
      default:
        return ['#43CEA2', '#185A9D'];
    }
  };
  
  const getIconForTip = (iconName: string) => {
    switch (iconName) {
      case 'eating':
        return <Feather name="sun" size={20}  color={colors.text} />;
      case 'breathing':
        return <Wind size={20} color="#fff" />;
      case 'hydration':
        return <Droplets size={20} color="#fff" />;
      case 'gratitude':
        return <Feather name="heart" size={20}  color={colors.text} />;
      case 'stretch':
        return <Feather size={20} color="#fff" />;
      case 'nature':
        return <Feather name="sun" size={20}  color={colors.text} />;
      case 'posture':
        return <Feather size={20} color="#fff" />;
      case 'digital':
        return <Feather name="moon" size={20}  color={colors.text} />;
      case 'walking':
        return <Feather size={20} color="#fff" />;
      case 'emotion':
        return <Feather name="heart" size={20}  color={colors.text} />;
      default:
        return <Feather name="clock" size={20}  color={colors.text} />;
    }
  };
  
  // Filter tips by selected category
  const filteredTips = selectedCategory 
    ? tips.filter(tip => tip.category === selectedCategory)
    : tips;
  
  // Extract unique categories from tips
  const categories = [...new Set(tips.map(tip => tip.category))];
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDark ? colors.background : '#f8f9fa' }]}>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      
      {/* Custom Header */}
      <View style={[styles.header, { backgroundColor: isDark ? colors.card : colors.background }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          accessibilityLabel="Go back"
        >
          <Feather name="arrow-left" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Mindfulness</Text>
        <TouchableOpacity
          style={[styles.aiButton, { opacity: generatingAiTip ? 0.6 : 1 }]}
          onPress={generateAiTip}
          disabled={generatingAiTip}
        >
          {generatingAiTip ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <Feather name="zap" size={16}  color={colors.text} />
              <Text style={styles.aiButtonText}>AI Tip</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {/* Stats Section */}
        <View style={[styles.statsContainer, { 
          backgroundColor: isDark ? 'rgba(30,30,40,0.6)' : 'rgba(255,255,255,0.6)',
          borderColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.04)',
        }]}>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: colors.text }]}>{stats.total}</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Total</Text>
          </View>
          
          <View style={styles.statSeparator} />
          
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: colors.text }]}>{stats.streak}</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Streak</Text>
          </View>
          
          <View style={styles.statSeparator} />
          
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: colors.text }]}>{stats.thisWeek}</Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>This Week</Text>
          </View>
        </View>
        
        {/* Category Filters */}
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Categories</Text>
        <ScrollView 
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesContainer}
        >
          {categories.map((category) => (
            <TouchableOpacity
              key={category}
              style={[
                styles.categoryChip,
                {
                  backgroundColor: selectedCategory === category
                    ? isDark ? 'rgba(255,255,255,0.15)' : 'rgba(0,0,0,0.08)'
                    : isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)'
                }
              ]}
              onPress={() => handleCategorySelect(category)}
            >
              <View style={[
                styles.categoryIcon,
                { backgroundColor: getGradientForCategory(category)[0] }
              ]}>
                {getIconForCategory(category)}
              </View>
              <Text style={[
                styles.categoryText,
                { color: selectedCategory === category ? colors.primary : colors.text }
              ]}>
                {category.replace('-', ' ')}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
        
        {/* Mindfulness Tips */}
        <View style={styles.titleRow}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Tips & Activities</Text>
          <Text style={[styles.aiSubtitle, { color: colors.textSecondary }]}>
            AI-powered recommendations
          </Text>
        </View>
        
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
              Loading mindfulness activities...
            </Text>
          </View>
        ) : (
          <View style={styles.tipsContainer}>
            {filteredTips.map((tip) => {
              // Check if the user has already completed this tip today
              const today = new Date().toISOString().split('T')[0];
              const isCompletedToday = sessions.some(
                s => s.tip_id === tip.id && s.session_date === today
              );
              
              // Check if this is an AI-generated tip (ID starts with 'ai-generated-')
              const isAiGenerated = tip.id.startsWith('ai-generated-');
              
              return (
                <View 
                  key={tip.id}
                  style={[
                    styles.tipCard,
                    {
                      backgroundColor: isDark ? colors.card : 'white',
                      borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                    }
                  ]}
                >
                  {tip.image_url && (
                    <View style={styles.tipImageContainer}>
                      <Image
                        source={{ uri: tip.image_url || FALLBACK_IMAGE_URL }}
                        style={styles.tipImage}
                        resizeMode="cover"
                      />
                      <View style={[styles.tipImageOverlay, { backgroundColor: isDark ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.3)' }]} />
                    </View>
                  )}
                  
                  <LinearGradient
                    colors={[tip.gradient_start, tip.gradient_end]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={styles.tipIconContainer}
                  >
                    {getIconForTip(tip.icon)}
                  </LinearGradient>
                  
                  <View style={styles.tipContent}>
                    <View style={styles.tipTitleRow}>
                      <Text style={[styles.tipTitle, { color: colors.text }]}>{tip.title}</Text>
                      {isAiGenerated && (
                        <View style={styles.aiTag}>
                          <Text style={styles.aiTagText}>AI</Text>
                        </View>
                      )}
                    </View>
                    
                    <Text style={[styles.tipMessage, { color: colors.textSecondary }]}>
                      {tip.message}
                    </Text>
                    
                    <TouchableOpacity
                      style={[
                        styles.completeButton,
                        {
                          backgroundColor: isCompletedToday 
                            ? isDark ? 'rgba(0, 200, 0, 0.2)' : 'rgba(0, 200, 0, 0.1)'
                            : isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.04)'
                        }
                      ]}
                      onPress={() => handleCompleteTip(tip)}
                      disabled={isCompletedToday}
                    >
                      {isCompletedToday ? (
                        <>
                          <Feather name="check" size={16} color={isDark ? '#4ADE80' : '#16A34A'} />
                          <Text style={[
                            styles.completeButtonText,
                            { color: isDark ? '#4ADE80' : '#16A34A' }
                          ]}>
                            Completed Today
                          </Text>
                        </>
                      ) : (
                        <Text style={[styles.completeButtonText, { color: colors.primary }]}>
                          Complete Activity
                        </Text>
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              );
            })}
            
            {filteredTips.length === 0 && !loading && (
              <View style={styles.emptyStateContainer}>
                <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
                  No mindfulness tips found in this category.
                </Text>
              </View>
            )}
          </View>
        )}
        
        {/* Recent Activity */}
        {sessions.length > 0 && (
          <>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Recent Activity</Text>
            <View style={styles.recentActivityContainer}>
              {sessions.slice(0, 3).map((session, index) => (
                <View
                  key={session.id}
                  style={[
                    styles.activityItem,
                    {
                      backgroundColor: isDark ? 'rgba(30,30,40,0.6)' : 'rgba(255,255,255,0.6)',
                      borderColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.04)',
                    }
                  ]}
                >
                  <View style={styles.activityDateContainer}>
                    <Feather name="calendar" size={14} color={colors.textSecondary} />
                    <Text style={[styles.activityDate, { color: colors.textSecondary }]}>
                      {new Date(session.session_date).toLocaleDateString()}
                    </Text>
                  </View>
                  
                  <Text style={[styles.activityText, { color: colors.text }]}>
                    {(session as any).tip?.title || 'Mindfulness session'}
                  </Text>
                </View>
              ))}
              
              {sessions.length > 3 && (
                <TouchableOpacity
                  style={[
                    styles.viewAllButton,
                    {
                      backgroundColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.03)'
                    }
                  ]}
                  onPress={() => router.push('/profile')}
                >
                  <Text style={[styles.viewAllText, { color: colors.primary }]}>
                    View All {sessions.length} Activities
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  aiButton: {
    backgroundColor: '#10B981',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  aiButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
    marginLeft: 4,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'baseline',
    justifyContent: 'space-between',
    marginTop: 8,
    marginBottom: 12,
  },
  aiSubtitle: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: 16,
    paddingBottom: 40,
  },
  statsContainer: {
    flexDirection: 'row',
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
  },
  statSeparator: {
    width: 1,
    height: 40,
    backgroundColor: 'rgba(150,150,150,0.2)',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    marginTop: 8,
  },
  categoriesContainer: {
    paddingBottom: 16,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginRight: 10,
  },
  categoryIcon: {
    width: 26,
    height: 26,
    borderRadius: 13,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  loadingContainer: {
    padding: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
  },
  tipsContainer: {
    marginBottom: 16,
  },
  tipCard: {
    borderRadius: 16,
    marginBottom: 16,
    overflow: 'hidden',
    borderWidth: 1,
  },
  tipIconContainer: {
    position: 'absolute',
    top: 16,
    left: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tipContent: {
    padding: 16,
    paddingLeft: 72,
  },
  tipTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tipTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginRight: 8,
  },
  aiTag: {
    backgroundColor: '#10B981',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  aiTagText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '700',
  },
  tipMessage: {
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 16,
  },
  completeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  completeButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  emptyStateContainer: {
    padding: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyStateText: {
    textAlign: 'center',
    fontSize: 16,
  },
  recentActivityContainer: {
    marginBottom: 16,
  },
  activityItem: {
    flexDirection: 'row',
    borderRadius: 12,
    padding: 12,
    paddingVertical: 14,
    marginBottom: 10,
    alignItems: 'center',
    borderWidth: 1,
  },
  activityDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
    paddingRight: 12,
    borderRightWidth: 1,
    borderRightColor: 'rgba(150,150,150,0.2)',
  },
  activityDate: {
    fontSize: 12,
    marginLeft: 4,
  },
  activityText: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  viewAllButton: {
    borderRadius: 12,
    padding: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
  },
  tipImageContainer: {
    width: '100%',
    height: 120,
    position: 'relative',
  },
  tipImage: {
    width: '100%',
    height: '100%',
  },
  tipImageOverlay: {
    ...StyleSheet.absoluteFillObject,
  },
}); 