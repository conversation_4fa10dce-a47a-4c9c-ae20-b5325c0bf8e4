import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Stack } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { OptimisticWaterIntakeTracker } from '@/components/OptimisticWaterIntakeTracker';
import { WaterIntakeTracker } from '@/components/WaterIntakeTracker';
import { Feather } from '@expo/vector-icons';
import { useToast } from '@/components/Toast';

/**
 * Demo screen showcasing optimistic updates.
 * 
 * This screen demonstrates optimistic UI updates by comparing
 * a regular component with one that uses optimistic updates.
 */
export default function OptimisticUpdateDemo() {
  const { colors, isDark } = useTheme();
  const toast = useToast();
  
  // State for water intake values
  const [regularWaterIntake, setRegularWaterIntake] = useState(500);
  const [optimisticWaterIntake, setOptimisticWaterIntake] = useState(500);
  
  // Goal water intake
  const waterGoal = 2000;
  
  // Flag to simulate server errors for demo
  const [simulateErrors, setSimulateErrors] = useState(false);
  
  // Regular update function with artificial delay
  const handleRegularUpdate = async (newAmount: number) => {
    try {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Simulate error if flag is set
      if (simulateErrors) {
        throw new Error('Simulated server error');
      }
      
      // Update the state
      setRegularWaterIntake(newAmount);
    } catch (error) {
      toast.showToast({
        message: 'Failed to update water intake. Please try again.',
        type: 'error',
      });
      throw error;
    }
  };
  
  // Optimistic update function with artificial delay
  const handleOptimisticUpdate = async (newAmount: number) => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Simulate error if flag is set
    if (simulateErrors) {
      throw new Error('Simulated server error');
    }
    
    // Update the state
    setOptimisticWaterIntake(newAmount);
  };
  
  // Toggle error simulation
  const toggleErrorSimulation = () => {
    setSimulateErrors(!simulateErrors);
    
    toast.showToast({
      message: simulateErrors 
        ? 'Error simulation disabled' 
        : 'Error simulation enabled - updates will fail',
      type: simulateErrors ? 'success' : 'warning',
      duration: 3000,
    });
  };
  
  return (
    <>
      <Stack.Screen
        options={{
          title: 'Optimistic Updates Demo',
          headerTitleStyle: { color: colors.text },
          headerStyle: { backgroundColor: colors.card },
        }}
      />
      
      <ScrollView
        style={[styles.container, { backgroundColor: colors.background }]}
        contentContainerStyle={styles.content}
      >
        <Text style={[styles.title, { color: colors.text }]}>
          Optimistic UI Updates Demo
        </Text>
        
        <Text style={[styles.description, { color: colors.textSecondary }]}>
          Compare traditional vs. optimistic updates. Optimistic updates show changes immediately 
          while waiting for server confirmation.
        </Text>
        
        {/* Error simulation toggle */}
        <TouchableOpacity
          style={[
            styles.errorToggle,
            {
              backgroundColor: simulateErrors 
                ? isDark ? 'rgba(239, 68, 68, 0.2)' : 'rgba(239, 68, 68, 0.1)'
                : isDark ? 'rgba(16, 185, 129, 0.2)' : 'rgba(16, 185, 129, 0.1)',
              borderColor: simulateErrors ? colors.error : colors.success,
            },
          ]}
          onPress={toggleErrorSimulation}
        >
          <Feather name="alert-circle" size={20} color={simulateErrors ? colors.error : colors.success} style={styles.errorIcon} />
          <Text
            style={[
              styles.errorText,
              { color: simulateErrors ? colors.error : colors.success },
            ]}
          >
            {simulateErrors
              ? 'Server errors enabled (updates will fail)'
              : 'Server errors disabled (updates will succeed)'}
          </Text>
        </TouchableOpacity>
        
        <View style={styles.sectionContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Traditional Updates
          </Text>
          <Text style={[styles.sectionDescription, { color: colors.textSecondary }]}>
            Updates only appear after server response (1.5s delay).
          </Text>
          
          <WaterIntakeTracker
            currentIntake={regularWaterIntake}
            goalIntake={waterGoal}
            onAddWater={handleRegularUpdate}
          />
        </View>
        
        <View style={styles.sectionContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Optimistic Updates
          </Text>
          <Text style={[styles.sectionDescription, { color: colors.textSecondary }]}>
            Updates appear immediately, then sync with server.
          </Text>
          
          <OptimisticWaterIntakeTracker
            currentIntake={optimisticWaterIntake}
            goalIntake={waterGoal}
            onAddWater={handleOptimisticUpdate}
          />
        </View>
        
        <View style={styles.sectionContainer}>
          <Text style={[styles.noteTitle, { color: colors.primary }]}>
            Implementation Notes:
          </Text>
          <Text style={[styles.note, { color: colors.text }]}>
            • Updates appear instantly with optimistic updates
          </Text>
          <Text style={[styles.note, { color: colors.text }]}>
            • If server requests fail, UI automatically reverts
          </Text>
          <Text style={[styles.note, { color: colors.text }]}>
            • Toast notifications provide feedback on success/failure
          </Text>
          <Text style={[styles.note, { color: colors.text }]}>
            • Toggle errors to see how optimistic updates handle failures
          </Text>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 24,
  },
  errorToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 24,
    borderLeftWidth: 4,
  },
  errorIcon: {
    marginRight: 12,
  },
  errorText: {
    fontSize: 14,
    fontWeight: '500',
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 14,
    marginBottom: 16,
  },
  noteTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  note: {
    fontSize: 14,
    lineHeight: 22,
    marginBottom: 8,
  },
}); 