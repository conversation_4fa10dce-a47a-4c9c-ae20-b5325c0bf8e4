import React, { useEffect, useState } from 'react';
import { useLocalSearchParams, Stack, useRouter } from 'expo-router';
import { View, Text, ActivityIndicator } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';

// This is a dynamic route handler that redirects to the appropriate profile screen
export default function ProfileScreenRouter() {
  const { screen } = useLocalSearchParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const { colors } = useTheme();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Redirect to the appropriate screen
    if (screen) {
      try {
        // Try the direct profile-screens path since that's our primary approach now
        const screenRoute = `/profile-screens/${screen}`;
        console.log(`Routing to ${screenRoute}`);
        router.replace(screenRoute as any);
        
        // Set a timeout to show error if navigation doesn't complete
        const timeout = setTimeout(() => {
          setError(`Navigation to ${screenRoute} timed out. The screen may not exist.`);
          setLoading(false);
        }, 3000);
        
        return () => clearTimeout(timeout);
      } catch (err) {
        console.error('Error routing to profile screen:', err);
        setError(`Could not navigate to profile screen: ${screen}`);
        setLoading(false);
      }
    } else {
      // If no screen is provided, go back to profile
      router.replace("/(tabs)/profile");
    }
  }, [screen]);

  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: colors.background }}>
      <Stack.Screen options={{ headerShown: false }} />
      
      {loading && !error && (
        <>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={{ marginTop: 20, color: colors.text }}>Loading...</Text>
        </>
      )}
      
      {error && (
        <Text style={{ color: 'red', textAlign: 'center', padding: 20 }}>{error}</Text>
      )}
    </View>
  );
} 