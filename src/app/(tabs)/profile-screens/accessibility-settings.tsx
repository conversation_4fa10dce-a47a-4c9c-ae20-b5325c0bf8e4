import React from 'react';
import { View, Text, StyleSheet, ScrollView, Switch } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAccessibility } from '@/components/AccessibilityProvider';
import { OneHandedModeSettings } from '@/components/OneHandedModeSettings';
import { Stack } from 'expo-router';

/**
 * Accessibility Settings Screen
 * 
 * A dedicated screen for configuring all accessibility-related options,
 * including dynamic text sizing, reduced motion, screen reader settings,
 * and one-handed mode.
 */
export default function AccessibilitySettingsScreen() {
  const { colors } = useTheme();
  const {
    isReduceMotionEnabled,
    toggleReduceMotion,
    isScreenReaderEnabled,
    isDynamicTextEnabled,
    toggleDynamicText,
  } = useAccessibility();

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Accessibility',
          headerTitleStyle: { color: colors.text },
          headerStyle: { backgroundColor: colors.card },
          headerTintColor: colors.primary,
        }}
      />
      <ScrollView
        style={[styles.container, { backgroundColor: colors.background }]}
        contentContainerStyle={styles.contentContainer}
      >
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Display & Interaction
          </Text>
          
          {/* Dynamic Text Size */}
          <View style={[styles.settingRow, { borderBottomColor: colors.border }]}>
            <View style={styles.settingTextContainer}>
              <Text style={[styles.settingLabel, { color: colors.text }]}>
                Dynamic Text Size
              </Text>
              <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                Automatically adjust text size based on system settings
              </Text>
            </View>
            <Switch
              value={isDynamicTextEnabled}
              onValueChange={toggleDynamicText}
              trackColor={{ false: '#767577', true: colors.primaryLight }}
              thumbColor={isDynamicTextEnabled ? colors.primary : '#f4f3f4'}
              ios_backgroundColor="#767577"
              accessibilityLabel="Toggle dynamic text size"
              accessibilityRole="switch"
            />
          </View>
          
          {/* Reduce Motion */}
          <View style={[styles.settingRow, { borderBottomColor: colors.border }]}>
            <View style={styles.settingTextContainer}>
              <Text style={[styles.settingLabel, { color: colors.text }]}>
                Reduce Motion
              </Text>
              <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                Minimize animations throughout the app
              </Text>
            </View>
            <Switch
              value={isReduceMotionEnabled}
              onValueChange={toggleReduceMotion}
              trackColor={{ false: '#767577', true: colors.primaryLight }}
              thumbColor={isReduceMotionEnabled ? colors.primary : '#f4f3f4'}
              ios_backgroundColor="#767577"
              accessibilityLabel="Toggle reduce motion"
              accessibilityRole="switch"
            />
          </View>
        </View>
        
        {/* One-Handed Mode Section */}
        <OneHandedModeSettings />
        
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Additional Settings
          </Text>
          
          <Text style={[styles.description, { color: colors.textSecondary }]}>
            The app also respects your device's screen reader settings. To enable or disable the screen reader,
            please use your device's accessibility settings.
          </Text>
          
          <View style={[styles.statusCard, { backgroundColor: colors.card }]}>
            <Text style={[styles.statusTitle, { color: colors.text }]}>
              Screen Reader Status
            </Text>
            <Text style={[styles.statusValue, { color: isScreenReaderEnabled ? colors.success : colors.textSecondary }]}>
              {isScreenReaderEnabled ? 'Enabled' : 'Disabled'}
            </Text>
            <Text style={[styles.statusDescription, { color: colors.textSecondary }]}>
              {isScreenReaderEnabled 
                ? 'Screen reader is active and the app is optimized for voice navigation.' 
                : 'Enable your device\'s screen reader for voice-guided navigation.'}
            </Text>
          </View>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 40,
  },
  section: {
    padding: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  settingTextContainer: {
    flex: 1,
    marginRight: 16,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
  },
  statusCard: {
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  statusValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  statusDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
}); 