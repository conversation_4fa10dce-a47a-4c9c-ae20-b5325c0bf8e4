import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  ScrollView,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useAccessibility } from '@/components/AccessibilityProvider';
import { useOneHandedMode } from '@/components/OneHandedModeProvider';
import { Feather } from '@expo/vector-icons';
import KeyboardShortcutsButton from '@/components/KeyboardShortcutsButton';

export default function AccessibilityScreen() {
  const { colors, isDark } = useTheme();
  const insets = useSafeAreaInsets();
  const router = useRouter();
  
  // Get accessibility settings
  const {
    isScreenReaderEnabled,
    isReduceMotionEnabled,
    isReduceTransparencyEnabled,
    isHighContrastEnabled,
    toggleScreenReader,
    toggleReduceMotion,
    toggleReduceTransparency,
    toggleHighContrast,
  } = useAccessibility();
  
  // Get one-handed mode settings
  const {
    isOneHandedModeEnabled,
    toggleOneHandedMode,
  } = useOneHandedMode();
  
  // Local state for font size
  const [fontSizeMultiplier, setFontSizeMultiplier] = useState(1);
  
  // Keep this initial state to compare for changes
  const [initialSettings, setInitialSettings] = useState({
    isScreenReaderEnabled,
    isReduceMotionEnabled,
    isReduceTransparencyEnabled,
    isHighContrastEnabled,
    isOneHandedModeEnabled,
    fontSizeMultiplier,
  });
  
  // Save settings when component unmounts if they've changed
  useEffect(() => {
    return () => {
      const currentSettings = {
        isScreenReaderEnabled,
        isReduceMotionEnabled,
        isReduceTransparencyEnabled,
        isHighContrastEnabled,
        isOneHandedModeEnabled,
        fontSizeMultiplier,
      };
      
      // Compare initial to current to see if we need to save
      if (JSON.stringify(initialSettings) !== JSON.stringify(currentSettings)) {
        // Normally we would save to AsyncStorage or server here
        console.log('Saving accessibility settings:', currentSettings);
      }
    };
  }, [
    isScreenReaderEnabled,
    isReduceMotionEnabled,
    isReduceTransparencyEnabled,
    isHighContrastEnabled,
    isOneHandedModeEnabled,
    fontSizeMultiplier,
    initialSettings,
  ]);
  
  // Increase font size
  const increaseFontSize = () => {
    if (fontSizeMultiplier < 1.5) {
      setFontSizeMultiplier(prev => prev + 0.1);
    }
  };
  
  // Decrease font size
  const decreaseFontSize = () => {
    if (fontSizeMultiplier > 0.8) {
      setFontSizeMultiplier(prev => prev - 0.1);
    }
  };
  
  return (
    <>
      <Stack.Screen
        options={{
          title: 'Accessibility',
          headerShown: false,
        }}
      />
      <View
        style={[
          styles.container,
          { backgroundColor: colors.background, paddingTop: insets.top },
        ]}
      >
        {/* Header */}
        <View
          style={[
            styles.header,
            { borderBottomColor: colors.border },
          ]}
        >
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Feather name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Accessibility
          </Text>
        </View>
        
        <ScrollView style={styles.content}>
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>
              VISUAL
            </Text>
            
            {/* Font Size */}
            <View style={styles.settingContainer}>
              <View style={styles.settingInfo}>
                <View style={styles.settingIconContainer}>
                  <ZoomIn size={20} color={colors.primary} />
                </View>
                <Text style={[styles.settingTitle, { color: colors.text }]}>
                  Text Size
                </Text>
              </View>
              
              <View style={styles.fontSizeControls}>
                <TouchableOpacity
                  style={[
                    styles.fontSizeButton,
                    { backgroundColor: colors.card, borderColor: colors.border },
                  ]}
                  onPress={decreaseFontSize}
                  disabled={fontSizeMultiplier <= 0.8}
                >
                  <Text style={[styles.fontSizeButtonText, { color: colors.text }]}>
                    A-
                  </Text>
                </TouchableOpacity>
                
                <Text style={[styles.fontSizeValue, { color: colors.text }]}>
                  {Math.round(fontSizeMultiplier * 100)}%
                </Text>
                
                <TouchableOpacity
                  style={[
                    styles.fontSizeButton,
                    { backgroundColor: colors.card, borderColor: colors.border },
                  ]}
                  onPress={increaseFontSize}
                  disabled={fontSizeMultiplier >= 1.5}
                >
                  <Text style={[styles.fontSizeButtonText, { color: colors.text }]}>
                    A+
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            
            {/* High Contrast */}
            <View style={[styles.settingItem, { borderBottomColor: colors.border }]}>
              <View style={styles.settingInfo}>
                <View style={styles.settingIconContainer}>
                  <Feather name="eye" size={20} color={colors.primary} />
                </View>
                <View style={styles.settingTextContainer}>
                  <Text style={[styles.settingTitle, { color: colors.text }]}>
                    High Contrast
                  </Text>
                  <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                    Increase contrast between text and background
                  </Text>
                </View>
              </View>
              
              <Switch
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={isHighContrastEnabled ? 'white' : 'white'}
                ios_backgroundColor={colors.border}
                onValueChange={toggleHighContrast}
                value={isHighContrastEnabled}
              />
            </View>
            
            {/* Reduce Transparency */}
            <View style={[styles.settingItem, { borderBottomColor: colors.border }]}>
              <View style={styles.settingInfo}>
                <View style={styles.settingIconContainer}>
                  <MoveDiagonal size={20} color={colors.primary} />
                </View>
                <View style={styles.settingTextContainer}>
                  <Text style={[styles.settingTitle, { color: colors.text }]}>
                    Reduce Transparency
                  </Text>
                  <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                    Increase opacity of background elements
                  </Text>
                </View>
              </View>
              
              <Switch
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={isReduceTransparencyEnabled ? 'white' : 'white'}
                ios_backgroundColor={colors.border}
                onValueChange={toggleReduceTransparency}
                value={isReduceTransparencyEnabled}
              />
            </View>
          </View>
          
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>
              MOTION
            </Text>
            
            {/* Reduce Motion */}
            <View style={[styles.settingItem, { borderBottomColor: colors.border }]}>
              <View style={styles.settingInfo}>
                <View style={styles.settingIconContainer}>
                  <Ban size={20} color={colors.primary} />
                </View>
                <View style={styles.settingTextContainer}>
                  <Text style={[styles.settingTitle, { color: colors.text }]}>
                    Reduce Motion
                  </Text>
                  <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                    Decrease animations throughout the app
                  </Text>
                </View>
              </View>
              
              <Switch
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={isReduceMotionEnabled ? 'white' : 'white'}
                ios_backgroundColor={colors.border}
                onValueChange={toggleReduceMotion}
                value={isReduceMotionEnabled}
              />
            </View>
          </View>
          
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>
              SCREEN READER
            </Text>
            
            {/* Screen Reader */}
            <View style={[styles.settingItem, { borderBottomColor: colors.border }]}>
              <View style={styles.settingInfo}>
                <View style={styles.settingIconContainer}>
                  <Feather name="volume-2" size={20} color={colors.primary} />
                </View>
                <View style={styles.settingTextContainer}>
                  <Text style={[styles.settingTitle, { color: colors.text }]}>
                    Screen Reader
                  </Text>
                  <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                    Enable spoken feedback for screen elements
                  </Text>
                </View>
              </View>
              
              <Switch
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={isScreenReaderEnabled ? 'white' : 'white'}
                ios_backgroundColor={colors.border}
                onValueChange={toggleScreenReader}
                value={isScreenReaderEnabled}
              />
            </View>
          </View>
          
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>
              INTERACTION
            </Text>
            
            {/* One-handed Mode */}
            <View style={[styles.settingItem, { borderBottomColor: colors.border }]}>
              <View style={styles.settingInfo}>
                <View style={styles.settingIconContainer}>
                  <HandMetal size={20} color={colors.primary} />
                </View>
                <View style={styles.settingTextContainer}>
                  <Text style={[styles.settingTitle, { color: colors.text }]}>
                    One-handed Mode
                  </Text>
                  <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                    Optimize layout for one-handed use
                  </Text>
                </View>
              </View>
              
              <Switch
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={isOneHandedModeEnabled ? 'white' : 'white'}
                ios_backgroundColor={colors.border}
                onValueChange={toggleOneHandedMode}
                value={isOneHandedModeEnabled}
              />
            </View>
            
            {/* Keyboard Shortcuts - Web Only */}
            {Platform.OS === 'web' && (
              <View style={[styles.settingItem, { borderBottomColor: colors.border }]}>
                <View style={styles.settingInfo}>
                  <View style={styles.settingIconContainer}>
                    <Keyboard size={20} color={colors.primary} />
                  </View>
                  <View style={styles.settingTextContainer}>
                    <Text style={[styles.settingTitle, { color: colors.text }]}>
                      Keyboard Shortcuts
                    </Text>
                    <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                      View and manage keyboard shortcuts
                    </Text>
                  </View>
                </View>
                
                <KeyboardShortcutsButton
                  label="View"
                  showIcon={false}
                  style={[
                    styles.viewButton,
                    { backgroundColor: colors.card, borderColor: colors.border }
                  ]}
                />
              </View>
            )}
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 16,
    marginTop: 16,
    marginBottom: 8,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingTextContainer: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
  },
  settingContainer: {
    padding: 16,
  },
  fontSizeControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  fontSizeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fontSizeButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  fontSizeValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  viewButton: {
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
  },
}); 