import React, { useState } from 'react';
import { StyleSheet, Text, View, ScrollView, Image, TouchableOpacity, Platform } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Feather , FontAwesome , MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  progress: number;
  maxProgress: number;
  completed: boolean;
  date?: string;
  reward?: string;
  category: 'nutrition' | 'activity' | 'consistency' | 'milestone';
}

export default function AchievementsScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const [activeCategory, setActiveCategory] = useState<string>('all');
  
  // Sample achievements data
  const achievements: Achievement[] = [
    {
      id: '1',
      title: 'First Meal Logged',
      description: 'Log your first meal in the app',
      icon: Flame,
      progress: 1,
      maxProgress: 1,
      completed: true,
      date: '2023-05-10',
      reward: '50 points',
      category: 'nutrition'
    },
    {
      id: '2',
      title: 'One Week Streak',
      description: 'Log your meals for 7 consecutive days',
      icon: Calendar,
      progress: 7,
      maxProgress: 7,
      completed: true,
      date: '2023-05-17',
      reward: '100 points',
      category: 'consistency'
    },
    {
      id: '3',
      title: 'Nutrition Master',
      description: 'Log a perfectly balanced meal with ideal macros',
      icon: Award,
      progress: 1,
      maxProgress: 1,
      completed: true,
      date: '2023-05-20',
      reward: '150 points',
      category: 'nutrition'
    },
    {
      id: '4',
      title: 'Protein Champion',
      description: 'Meet your protein goals for 5 consecutive days',
      icon: Trophy,
      progress: 3,
      maxProgress: 5,
      completed: false,
      category: 'nutrition'
    },
    {
      id: '5',
      title: 'Calorie Conscious',
      description: 'Stay within 100 calories of your goal for 10 days',
      icon: Zap,
      progress: 6,
      maxProgress: 10,
      completed: false,
      category: 'nutrition'
    },
    {
      id: '6',
      title: 'Activity Starter',
      description: 'Log your first workout session',
      icon: Heart,
      progress: 1,
      maxProgress: 1,
      completed: true,
      date: '2023-05-25',
      reward: '75 points',
      category: 'activity'
    },
    {
      id: '7',
      title: 'One Month Milestone',
      description: 'Use the app consistently for 30 days',
      icon: Star,
      progress: 22,
      maxProgress: 30,
      completed: false,
      category: 'milestone'
    },
  ];
  
  // Filter achievements based on active category
  const filteredAchievements = activeCategory === 'all' 
    ? achievements 
    : achievements.filter(a => a.category === activeCategory);
  
  // Group achievements by completed status
  const completedAchievements = filteredAchievements.filter(a => a.completed);
  const inProgressAchievements = filteredAchievements.filter(a => !a.completed);
  
  // Calculate user stats
  const totalAchievements = achievements.length;
  const completedCount = completedAchievements.length;
  const completionPercentage = Math.round((completedCount / totalAchievements) * 100);
  
  // Helper function to get gradient colors based on category
  const getCategoryGradient = (category: string): [string, string] => {
    switch(category) {
      case 'nutrition':
        return ['#3B82F6', '#2563EB']; // Blue
      case 'activity':
        return ['#10B981', '#059669']; // Green
      case 'consistency':
        return ['#8B5CF6', '#7C3AED']; // Purple
      case 'milestone':
        return ['#F59E0B', '#D97706']; // Amber
      default:
        return ['#6B7280', '#4B5563']; // Gray
    }
  };
  
  // Categories for filter tabs
  const categories = [
    { id: 'all', name: 'All' },
    { id: 'nutrition', name: 'Nutrition' },
    { id: 'activity', name: 'Activity' },
    { id: 'consistency', name: 'Consistency' },
    { id: 'milestone', name: 'Milestones' },
  ];

  return (
    <>
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.card }]}>
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={() => router.back()}
            accessibilityLabel="Go back"
            accessibilityHint="Returns to the previous screen"
          >
            <Feather name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Achievements</Text>
          <View style={styles.placeholderRight} />
        </View>
        
        <ScrollView 
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Stats Card */}
          <View style={[styles.statsCard, { 
            backgroundColor: colors.card,
            borderColor: colors.border,
            shadowColor: colors.shadow
          }]}>
            <View style={styles.statsContent}>
              <View style={styles.statsTextContainer}>
                <Text style={[styles.statsTitle, { color: colors.text }]}>Achievement Progress</Text>
                <Text style={[styles.statsSubtitle, { color: colors.textSecondary }]}>
                  You've completed {completedCount} of {totalAchievements} achievements
                </Text>
              </View>
              <View style={styles.statsPercentageContainer}>
                <LinearGradient
                  colors={['#3B82F6', '#2563EB'] as const}
                  style={[
                    styles.percentageBadge,
                    Platform.OS === 'web' ? { transform: [] } : {}
                  ]}
                >
                  <Text style={styles.percentageText}>{completionPercentage}%</Text>
                </LinearGradient>
              </View>
            </View>
            
            <View style={[styles.progressBarContainer, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
              <LinearGradient
                colors={['#3B82F6', '#2563EB'] as const}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={[
                  styles.progressBar, 
                  { width: `${completionPercentage}%` },
                  Platform.OS === 'web' ? { transform: [] } : {}
                ]}
              />
            </View>
            
            <View style={styles.statsRow}>
              <View style={styles.statItem}>
                <LinearGradient
                  colors={getCategoryGradient('nutrition') as readonly [string, string]}
                  style={[
                    styles.statIcon,
                    Platform.OS === 'web' ? { transform: [] } : {}
                  ]}
                >
                  <MaterialIcons name="local-fire-department" size={16}  color={colors.text} />
                </LinearGradient>
                <Text style={[styles.statValue, { color: colors.text }]}>
                  {achievements.filter(a => a.category === 'nutrition' && a.completed).length}
                </Text>
                <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Nutrition</Text>
              </View>
              
              <View style={styles.statItem}>
                <LinearGradient
                  colors={getCategoryGradient('activity') as readonly [string, string]}
                  style={[
                    styles.statIcon,
                    Platform.OS === 'web' ? { transform: [] } : {}
                  ]}
                >
                  <Feather name="heart" size={16}  color={colors.text} />
                </LinearGradient>
                <Text style={[styles.statValue, { color: colors.text }]}>
                  {achievements.filter(a => a.category === 'activity' && a.completed).length}
                </Text>
                <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Activity</Text>
              </View>
              
              <View style={styles.statItem}>
                <LinearGradient
                  colors={getCategoryGradient('consistency') as readonly [string, string]}
                  style={[
                    styles.statIcon,
                    Platform.OS === 'web' ? { transform: [] } : {}
                  ]}
                >
                  <Feather name="calendar" size={16}  color={colors.text} />
                </LinearGradient>
                <Text style={[styles.statValue, { color: colors.text }]}>
                  {achievements.filter(a => a.category === 'consistency' && a.completed).length}
                </Text>
                <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Consistency</Text>
              </View>
              
              <View style={styles.statItem}>
                <LinearGradient
                  colors={getCategoryGradient('milestone') as readonly [string, string]}
                  style={[
                    styles.statIcon,
                    Platform.OS === 'web' ? { transform: [] } : {}
                  ]}
                >
                  <Feather name="star" size={16}  color={colors.text} />
                </LinearGradient>
                <Text style={[styles.statValue, { color: colors.text }]}>
                  {achievements.filter(a => a.category === 'milestone' && a.completed).length}
                </Text>
                <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Milestones</Text>
              </View>
            </View>
          </View>
          
          {/* Category Filter */}
          <ScrollView 
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesContainer}
          >
            {categories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryTab,
                  activeCategory === category.id && styles.activeTab,
                  { borderColor: colors.border }
                ]}
                onPress={() => setActiveCategory(category.id)}
              >
                <Text style={[
                  styles.categoryText,
                  { color: activeCategory === category.id ? colors.primary : colors.textSecondary }
                ]}>
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
          
          {/* In Progress Achievements */}
          {inProgressAchievements.length > 0 && (
            <View style={styles.achievementsSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                In Progress
              </Text>
              
              {inProgressAchievements.map((achievement) => (
                <View
                  key={achievement.id}
                  style={[styles.achievementCard, { 
                    backgroundColor: colors.card,
                    borderColor: colors.border,
                    shadowColor: colors.shadow
                  }]}
                >
                  <View style={styles.achievementHeader}>
                    <LinearGradient
                      colors={getCategoryGradient(achievement.category) as readonly [string, string]}
                      style={[
                        styles.achievementIcon,
                        Platform.OS === 'web' ? { transform: [] } : {}
                      ]}
                    >
                      <achievement.icon size={24} color="white" />
                    </LinearGradient>
                    <View style={styles.achievementInfo}>
                      <Text style={[styles.achievementTitle, { color: colors.text }]}>
                        {achievement.title}
                      </Text>
                      <Text style={[styles.achievementDescription, { color: colors.textSecondary }]}>
                        {achievement.description}
                      </Text>
                    </View>
                  </View>
                  
                  <View style={styles.achievementProgressContainer}>
                    <View style={[styles.achievementProgressBar, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
                      <LinearGradient
                        colors={getCategoryGradient(achievement.category) as readonly [string, string]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={[
                          styles.achievementProgress,
                          { width: `${(achievement.progress / achievement.maxProgress) * 100}%` },
                          Platform.OS === 'web' ? { transform: [] } : {}
                        ]}
                      />
                    </View>
                    <Text style={[styles.achievementProgressText, { color: colors.textSecondary }]}>
                      {achievement.progress}/{achievement.maxProgress}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          )}
          
          {/* Completed Achievements */}
          {completedAchievements.length > 0 && (
            <View style={styles.achievementsSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Completed
              </Text>
              
              {completedAchievements.map((achievement) => (
                <View
                  key={achievement.id}
                  style={[styles.achievementCard, { 
                    backgroundColor: colors.card,
                    borderColor: colors.border,
                    shadowColor: colors.shadow
                  }]}
                >
                  <View style={styles.achievementHeader}>
                    <LinearGradient
                      colors={getCategoryGradient(achievement.category) as readonly [string, string]}
                      style={[
                        styles.achievementIcon,
                        Platform.OS === 'web' ? { transform: [] } : {}
                      ]}
                    >
                      <achievement.icon size={24} color="white" />
                    </LinearGradient>
                    <View style={styles.achievementInfo}>
                      <Text style={[styles.achievementTitle, { color: colors.text }]}>
                        {achievement.title}
                      </Text>
                      <Text style={[styles.achievementDescription, { color: colors.textSecondary }]}>
                        {achievement.description}
                      </Text>
                    </View>
                    <View style={styles.completedBadgeContainer}>
                      <LinearGradient
                        colors={['#10B981', '#059669'] as const}
                        style={[
                          styles.completedBadge,
                          Platform.OS === 'web' ? { transform: [] } : {}
                        ]}
                      >
                        <Text style={styles.completedText}>Completed</Text>
                      </LinearGradient>
                    </View>
                  </View>
                  
                  <View style={styles.completedFooter}>
                    <Text style={[styles.completedDate, { color: colors.textSecondary }]}>
                      Completed on {achievement.date}
                    </Text>
                    {achievement.reward && (
                      <View style={styles.rewardContainer}>
                        <Medal size={14} color={getCategoryGradient(achievement.category)[0]} style={styles.rewardIcon} />
                        <Text style={[styles.rewardText, { color: colors.text }]}>
                          {achievement.reward}
                        </Text>
                      </View>
                    )}
                    <TouchableOpacity style={styles.shareButton}>
                      <Feather name="share-2" size={14} color={colors.textSecondary} />
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
            </View>
          )}
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  placeholderRight: {
    width: 40,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  statsCard: {
    margin: 20,
    marginTop: 10,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 4 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        transform: []
      }
    }),
  },
  statsContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statsTextContainer: {
    flex: 1,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  statsSubtitle: {
    fontSize: 14,
  },
  statsPercentageContainer: {
    marginLeft: 12,
  },
  percentageBadge: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    ...Platform.OS === 'web' ? { transform: [] } : {}
  },
  percentageText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
  },
  progressBarContainer: {
    height: 10,
    borderRadius: 5,
    marginBottom: 16,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    ...Platform.OS === 'web' ? { transform: [] } : {}
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
  },
  statIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 6,
    ...Platform.OS === 'web' ? { transform: [] } : {}
  },
  statValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  statLabel: {
    fontSize: 12,
  },
  categoriesContainer: {
    paddingHorizontal: 16,
    flexDirection: 'row',
    marginBottom: 16,
  },
  categoryTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
  },
  activeTab: {
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    borderColor: '#3B82F6',
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '600',
  },
  achievementsSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginHorizontal: 20,
    marginBottom: 12,
  },
  achievementCard: {
    marginHorizontal: 20,
    marginBottom: 12,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: { elevation: 3 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  achievementHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  achievementIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  achievementInfo: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 2,
  },
  achievementDescription: {
    fontSize: 14,
  },
  achievementProgressContainer: {
    marginTop: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  achievementProgressBar: {
    height: 8,
    borderRadius: 4,
    flex: 1,
    overflow: 'hidden',
    marginRight: 10,
  },
  achievementProgress: {
    height: '100%',
  },
  achievementProgressText: {
    fontSize: 12,
    fontWeight: '600',
    minWidth: 36,
    textAlign: 'right',
  },
  completedBadgeContainer: {
    marginLeft: 8,
  },
  completedBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 10,
    ...Platform.OS === 'web' ? { transform: [] } : {}
  },
  completedText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  completedFooter: {
    marginTop: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  completedDate: {
    fontSize: 12,
  },
  rewardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rewardIcon: {
    marginRight: 4,
  },
  rewardText: {
    fontSize: 14,
    fontWeight: '600',
  },
  shareButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(100,116,139,0.1)',
  },
}); 