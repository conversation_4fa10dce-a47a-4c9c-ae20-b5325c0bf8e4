import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, ActivityIndicator, Image, Dimensions, Platform, Alert, Switch } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import { ActivityRecord, ActivityType, fetchActivities, fetchActivityStats, getSampleActivityData } from '@/services/activityService';
import Svg, { Path, Circle, Line } from 'react-native-svg';

import { getAuth } from 'firebase/auth';
import { getFirestore, collection, addDoc, doc, updateDoc, Timestamp } from 'firebase/firestore';
import { format } from 'date-fns';


const { width: screenWidth } = Dimensions.get('window');

export default function ActivityScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [activities, setActivities] = useState<ActivityRecord[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [activeTimeframe, setActiveTimeframe] = useState<'week' | 'month' | 'year'>('week');
  const [activityFilter, setActivityFilter] = useState<ActivityType | 'all'>('all');
  const [useSampleData, setUseSampleData] = useState(false);
  const auth = getAuth();
  const user = auth.currentUser;
  
  useEffect(() => {
    loadActivities();
    loadStats();
  }, [activeTimeframe, activityFilter, useSampleData]);
  
  const loadActivities = async () => {
    setLoading(true);
    try {
      if (useSampleData) {
        // Use sample data if database tables don't exist
        const sampleData = getSampleActivityData();
        setActivities(sampleData);
      } else {
        // Calculate date range based on timeframe
        const endDate = new Date();
        const startDate = new Date();
        
        if (activeTimeframe === 'week') {
          startDate.setDate(endDate.getDate() - 7);
        } else if (activeTimeframe === 'month') {
          startDate.setMonth(endDate.getMonth() - 1);
        } else if (activeTimeframe === 'year') {
          startDate.setFullYear(endDate.getFullYear() - 1);
        }

        // Build options for fetching activities
        const options: any = {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          limit: 20
        };
        
        // Add type filter if not "all"
        if (activityFilter !== 'all') {
          options.type = activityFilter;
        }
        
        // Fetch from the real backend
        const response = await fetchActivities(options);
        
        if (response.success && response.data) {
          setActivities(response.data);
        } else {
          // If error relates to missing table, switch to sample data
          if (response.error && response.error.includes('relation') && response.error.includes('does not exist')) {
            console.log('Activities table does not exist, using sample data instead');
            setUseSampleData(true);
            const sampleData = getSampleActivityData();
            setActivities(sampleData);
          } else {
            console.error('Failed to load activities:', response.error);
            // Use sample data as fallback
            const sampleData = getSampleActivityData();
            setActivities(sampleData);
          }
        }
      }
    } catch (error) {
      console.error('Error loading activities:', error);
      // Use sample data as fallback
      const sampleData = getSampleActivityData();
      setActivities(sampleData);
    } finally {
      setLoading(false);
    }
  };
  
  const loadStats = async () => {
    try {
      if (useSampleData) {
        // Calculate sample stats from our activities
        const activities = getSampleActivityData();
        
        // Calculate stats
        const totalActivities = activities.length;
        const totalDuration = activities.reduce((sum, activity) => sum + activity.duration_minutes, 0);
        const totalCalories = activities.reduce((sum, activity) => sum + activity.calories_burned, 0);
        const totalDistance = activities.reduce((sum, activity) => sum + (activity.distance_km || 0), 0);
        
        // Group by activity type
        const activityBreakdown: Record<string, number> = {};
        activities.forEach(activity => {
          const type = activity.type;
          activityBreakdown[type] = (activityBreakdown[type] || 0) + 1;
        });
        
        setStats({
          totalActivities,
          totalDuration,
          totalCalories,
          totalDistance,
          activityBreakdown,
          timeframe: activeTimeframe
        });
      } else {
        // Fetch real stats from the backend
        const response = await fetchActivityStats(activeTimeframe);
        
        if (response.success && response.data) {
          setStats(response.data);
        } else {
          // If error relates to missing table, switch to sample data
          if (response.error && response.error.includes('relation') && response.error.includes('does not exist')) {
            console.log('Activities table does not exist, using sample data for stats');
            setUseSampleData(true);
            
            // Calculate from sample data
            const activities = getSampleActivityData();
            const totalActivities = activities.length;
            const totalDuration = activities.reduce((sum, activity) => sum + activity.duration_minutes, 0);
            const totalCalories = activities.reduce((sum, activity) => sum + activity.calories_burned, 0);
            const totalDistance = activities.reduce((sum, activity) => sum + (activity.distance_km || 0), 0);
            
            // Group by activity type
            const activityBreakdown: Record<string, number> = {};
            activities.forEach(activity => {
              const type = activity.type;
              activityBreakdown[type] = (activityBreakdown[type] || 0) + 1;
            });
            
            setStats({
              totalActivities,
              totalDuration,
              totalCalories,
              totalDistance,
              activityBreakdown,
              timeframe: activeTimeframe
            });
          } else {
            console.error('Failed to load activity stats:', response.error);
          }
        }
      }
    } catch (error) {
      console.error('Error loading stats:', error);
      
      // Fallback to sample data for stats
      const activities = getSampleActivityData();
      const totalActivities = activities.length;
      const totalDuration = activities.reduce((sum, activity) => sum + activity.duration_minutes, 0);
      const totalCalories = activities.reduce((sum, activity) => sum + activity.calories_burned, 0);
      const totalDistance = activities.reduce((sum, activity) => sum + (activity.distance_km || 0), 0);
      
      // Group by activity type
      const activityBreakdown: Record<string, number> = {};
      activities.forEach(activity => {
        const type = activity.type;
        activityBreakdown[type] = (activityBreakdown[type] || 0) + 1;
      });
      
      setStats({
        totalActivities,
        totalDuration,
        totalCalories,
        totalDistance,
        activityBreakdown,
        timeframe: activeTimeframe
      });
    }
  };
  
  // Filters for time period
  const timeframes = [
    { id: 'week', label: 'This Week' },
    { id: 'month', label: 'This Month' },
    { id: 'year', label: 'This Year' },
  ];
  
  // Activity type filters
  const activityTypes = [
    { id: 'all', label: 'All Types' },
    { id: 'running', label: 'Running' },
    { id: 'walking', label: 'Walking' },
    { id: 'cycling', label: 'Cycling' },
    { id: 'swimming', label: 'Swimming' },
    { id: 'strength', label: 'Strength' },
    { id: 'yoga', label: 'Yoga' },
    { id: 'hiit', label: 'HIIT' },
    { id: 'other', label: 'Other' },
  ];
  
  // Helper function to format duration
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };
  
  // Helper function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' });
  };
  
  // Helper function to get activity icon
  const getActivityIcon = (type: ActivityType) => {
    switch (type) {
      case 'running':
        return Zap;
      case 'walking':
        return Map;
      case 'cycling':
        return Zap;
      case 'swimming':
        return Zap;
      case 'strength':
        return Zap;
      case 'yoga':
        return Zap;
      case 'hiit':
        return Flame;
      default:
        return Clock;
    }
  };
  
  // Helper function to get activity gradient colors
  const getActivityGradient = (type: ActivityType): [string, string] => {
    switch (type) {
      case 'running':
        return ['#3B82F6', '#2563EB']; // blue
      case 'walking':
        return ['#10B981', '#059669']; // green
      case 'cycling':
        return ['#F59E0B', '#D97706']; // amber
      case 'swimming':
        return ['#0EA5E9', '#0284C7']; // sky
      case 'strength':
        return ['#8B5CF6', '#6D28D9']; // violet
      case 'yoga':
        return ['#EC4899', '#DB2777']; // pink
      case 'hiit':
        return ['#EF4444', '#DC2626']; // red
      default:
        return ['#6B7280', '#4B5563']; // gray
    }
  };
  
  // Helper function to get activity label
  const getActivityLabel = (type: ActivityType) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  // Track screen view - with try/catch to handle missing tables
  useEffect(() => {
    const trackScreenView = async () => {
      if (useSampleData) return; // Skip tracking if using sample data
      
      try {
        const auth = getAuth();
        const user = auth.currentUser;
        
        if (!user) {
          return;
        }

        // Try to record this screen view in analytics
        try {
          const db = getFirestore();
          await addDoc(collection(db, 'user_analytics'), {
            user_id: user.uid,
            screen_name: 'activity_screen',
            view_date: Timestamp.now(),
            filters: {
              timeframe: activeTimeframe,
              activityType: activityFilter
            }
          });
        } catch (err) {
          console.log('Analytics table may not exist, skipping tracking');
        }
      } catch (err) {
        console.error('Error tracking screen view:', err);
      }
    };

    trackScreenView();
  }, []);

  return (
    <>
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.card }]}>
          <TouchableOpacity 
            style={[styles.backButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.05)' }]} 
            onPress={() => router.back()}
            accessibilityLabel="Go back"
            accessibilityHint="Returns to the previous screen"
          >
            <Feather name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Activity</Text>
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: isDark ? 'rgba(59,130,246,0.15)' : 'rgba(59,130,246,0.1)' }]}
            onPress={() => {
              // Navigate to add activity form
              router.push('/(tabs)/profile-screens/add-activity' as any);
            }}
          >
            <PlusCircle size={20} color={colors.primary} />
          </TouchableOpacity>
        </View>
        
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : (
          <ScrollView 
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            {/* Activity Stats Summary */}
            <View style={[styles.statsCard, { 
              backgroundColor: colors.card,
              borderColor: colors.border,
              shadowColor: colors.shadow
            }]}>
              <View style={styles.statsHeader}>
                <View style={styles.statsHeaderLeft}>
                  <Text style={[styles.statsTitle, { color: colors.text }]}>Activity Summary</Text>
                  <Text style={[styles.statsSubtitle, { color: colors.textSecondary }]}>
                    {timeframes.find(t => t.id === activeTimeframe)?.label || 'This Week'}
                  </Text>
                </View>
                
                {/* Filter Dropdown */}
                <TouchableOpacity 
                  style={[styles.timeframeSelector, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
                  onPress={() => {
                    // Toggle through timeframes
                    const currentIndex = timeframes.findIndex(t => t.id === activeTimeframe);
                    const nextIndex = (currentIndex + 1) % timeframes.length;
                    setActiveTimeframe(timeframes[nextIndex].id as 'week' | 'month' | 'year');
                  }}
                >
                  <Feather name="calendar" size={14} color={colors.textSecondary} style={{ marginRight: 4 }} />
                  <Text style={[styles.timeframeText, { color: colors.textSecondary }]}>
                    {timeframes.find(t => t.id === activeTimeframe)?.label || 'This Week'}
                  </Text>
                  <Feather name="chevron-down" size={14} color={colors.textSecondary} style={{ marginLeft: 2 }} />
                </TouchableOpacity>
              </View>
              
              {/* Stats Grid */}
              <View style={styles.statsGrid}>
                <View style={[styles.statItem, { borderRightColor: colors.border, borderBottomColor: colors.border }]}>
                  <View style={[styles.statIconContainer, { backgroundColor: isDark ? 'rgba(59,130,246,0.2)' : 'rgba(59,130,246,0.1)' }]}>
                    <Feather name="activity" size={16}  color={colors.text} />
                  </View>
                  <Text style={[styles.statValue, { color: colors.text }]}>{stats?.totalActivities || 0}</Text>
                  <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Workouts</Text>
                </View>
                <View style={[styles.statItem, { borderBottomColor: colors.border }]}>
                  <View style={[styles.statIconContainer, { backgroundColor: isDark ? 'rgba(16,185,129,0.2)' : 'rgba(16,185,129,0.1)' }]}>
                    <Feather name="clock" size={16}  color={colors.text} />
                  </View>
                  <Text style={[styles.statValue, { color: colors.text }]}>{formatDuration(stats?.totalDuration || 0)}</Text>
                  <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Duration</Text>
                </View>
                <View style={[styles.statItem, { borderRightColor: colors.border }]}>
                  <View style={[styles.statIconContainer, { backgroundColor: isDark ? 'rgba(239,68,68,0.2)' : 'rgba(239,68,68,0.1)' }]}>
                    <MaterialIcons name="local-fire-department" size={16}  color={colors.text} />
                  </View>
                  <Text style={[styles.statValue, { color: colors.text }]}>{stats?.totalCalories || 0}</Text>
                  <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Calories</Text>
                </View>
                <View style={styles.statItem}>
                  <View style={[styles.statIconContainer, { backgroundColor: isDark ? 'rgba(245,158,11,0.2)' : 'rgba(245,158,11,0.1)' }]}>
                    <Map size={16} color="#F59E0B" />
                  </View>
                  <Text style={[styles.statValue, { color: colors.text }]}>{(stats?.totalDistance || 0).toFixed(1)}</Text>
                  <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Distance (km)</Text>
                </View>
              </View>
              
              {/* Activity Type Distribution */}
              <View style={styles.activityDistribution}>
                <Text style={[styles.distributionTitle, { color: colors.text }]}>Activity Distribution</Text>
                <View style={styles.barChart}>
                  {stats?.activityBreakdown && Object.entries(stats.activityBreakdown).map(([type, count]) => {
                    const countNum = count as number;
                    const percentage = (countNum / stats.totalActivities) * 100;
                    const gradientColors = getActivityGradient(type as ActivityType);
                    
                    return (
                      <View key={type} style={styles.chartRow}>
                        <View style={styles.chartLabelContainer}>
                          <View style={[styles.chartColorDot, { backgroundColor: gradientColors[0] }]} />
                          <Text style={[styles.chartLabel, { color: colors.text }]}>{getActivityLabel(type as ActivityType)}</Text>
                        </View>
                        <View style={styles.chartBarContainer}>
                          <View style={[styles.chartBarBackground, { backgroundColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.03)' }]}>
                            <LinearGradient
                              colors={gradientColors as readonly [string, string]}
                              start={{ x: 0, y: 0 }}
                              end={{ x: 1, y: 0 }}
                              style={[styles.chartBar, { width: `${percentage}%` }]}
                            />
                          </View>
                          <Text style={[styles.chartValue, { color: colors.textSecondary }]}>
                            {Math.round(percentage)}% ({countNum})
                          </Text>
                        </View>
                      </View>
                    );
                  })}
                </View>
              </View>
            </View>
            
            {/* Activity History */}
            <View style={styles.activityHistorySection}>
              <View style={styles.sectionHeader}>
                <View style={styles.sectionHeaderLeft}>
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>Recent Activities</Text>
                  <Text style={[styles.sectionSubtitle, { color: colors.textSecondary }]}>
                    {activityFilter === 'all' ? 'All types' : getActivityLabel(activityFilter as ActivityType)}
                  </Text>
                </View>
                
                {/* Filter Dropdown for Activity Type */}
                <TouchableOpacity 
                  style={[styles.filterSelector, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
                  onPress={() => {
                    // Toggle through activity types
                    const currentIndex = activityTypes.findIndex(t => t.id === activityFilter);
                    const nextIndex = (currentIndex + 1) % activityTypes.length;
                    setActivityFilter(activityTypes[nextIndex].id as ActivityType | 'all');
                  }}
                >
                  <Text style={[styles.filterText, { color: colors.textSecondary }]}>
                    {activityTypes.find(t => t.id === activityFilter)?.label || 'All Types'}
                  </Text>
                  <Feather name="chevron-down" size={14} color={colors.textSecondary} style={{ marginLeft: 2 }} />
                </TouchableOpacity>
              </View>
              
              {/* Activity Timeline */}
              <View style={styles.timeline}>
                {activities.length > 0 ? (
                  activities.map((activity, index) => {
                    const ActivityIcon = getActivityIcon(activity.type);
                    const gradientColors = getActivityGradient(activity.type);
                    
                    return (
                      <View key={activity.id} style={styles.timelineItem}>
                        {/* Timeline connector */}
                        <View style={styles.timelineConnector}>
                          <View style={[styles.timelineLine, { backgroundColor: colors.border, opacity: index === 0 ? 0 : 1 }]} />
                          <LinearGradient
                            colors={gradientColors as readonly [string, string]}
                            style={styles.timelineDot}
                          >
                            <ActivityIcon size={16} color="white" />
                          </LinearGradient>
                          <View style={[styles.timelineLine, { backgroundColor: colors.border, opacity: index === activities.length - 1 ? 0 : 1 }]} />
                        </View>
                        
                        {/* Activity card */}
                        <TouchableOpacity 
                          style={[styles.activityCard, { 
                            backgroundColor: colors.card,
                            borderColor: colors.border,
                            shadowColor: colors.shadow
                          }]}
                          onPress={() => {
                            // Navigate to activity details with correct path
                            router.push(`/profile-screens/activity-detail?id=${activity.id}` as any);
                          }}
                        >
                          <View style={styles.activityCardHeader}>
                            <View style={[styles.activityDateBadge, {
                              backgroundColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.05)'
                            }]}>
                              <Feather name="calendar" size={12} color={colors.textSecondary} style={{ marginRight: 4 }} />
                              <Text style={[styles.activityDate, { color: colors.textSecondary }]}>
                                {formatDate(activity.start_time)}
                              </Text>
                            </View>
                            <TouchableOpacity 
                              style={styles.activityMoreButton}
                              onPress={() => {
                                // In development mode, just show alert instead of tracking
                                Alert.alert('View options for ' + activity.type + ' activity');
                              }}
                            >
                              <Feather name="more-horizontal" size={16} color={colors.textSecondary} />
                            </TouchableOpacity>
                          </View>
                          
                          <Text style={[styles.activityTitle, { color: colors.text }]}>
                            {getActivityLabel(activity.type)}
                            {activity.distance_km ? ` • ${activity.distance_km.toFixed(1)} km` : ''}
                          </Text>
                          
                          <View style={styles.activityStats}>
                            <View style={styles.activityDetailItem}>
                              <Feather name="clock" size={14} color={colors.textSecondary} style={{ marginRight: 4 }} />
                              <Text style={[styles.activityDetailText, { color: colors.text }]}>
                                {formatDuration(activity.duration_minutes)}
                              </Text>
                            </View>
                            
                            <View style={styles.activityDetailItem}>
                              <MaterialIcons name="local-fire-department" size={14} color={colors.textSecondary} style={{ marginRight: 4 }} />
                              <Text style={[styles.activityDetailText, { color: colors.text }]}>
                                {activity.calories_burned} cal
                              </Text>
                            </View>
                            
                            {activity.heart_rate_avg && (
                              <View style={styles.activityDetailItem}>
                                <Feather name="heart" size={14} color={colors.textSecondary} style={{ marginRight: 4 }} />
                                <Text style={[styles.activityDetailText, { color: colors.text }]}>
                                  {activity.heart_rate_avg} bpm
                                </Text>
                              </View>
                            )}
                          </View>
                          
                          {activity.notes && (
                            <View style={[styles.activityNotes, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)' }]}>
                              <Text style={[styles.activityNotesText, { color: colors.textSecondary }]} numberOfLines={2}>
                                {activity.notes}
                              </Text>
                            </View>
                          )}
                        </TouchableOpacity>
                      </View>
                    );
                  })
                ) : (
                  <View style={styles.emptyState}>
                    <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
                      No activities found for the selected filters.
                    </Text>
                    <TouchableOpacity 
                      style={[styles.emptyStateButton, { backgroundColor: colors.primary }]}
                      onPress={() => {
                        router.push('/(tabs)/profile-screens/add-activity' as any);
                      }}
                    >
                      <Text style={[styles.emptyStateButtonText, { color: 'white' }]}>
                        Add Your First Activity
                      </Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
              
              {/* "View More" Button */}
              {activities.length > 0 && (
                <TouchableOpacity
                  style={[styles.viewMoreButton, { backgroundColor: isDark ? 'rgba(59,130,246,0.1)' : 'rgba(59,130,246,0.05)', borderColor: colors.primary + '30' }]}
                  onPress={loadMoreActivities}
                >
                  <Text style={[styles.viewMoreText, { color: colors.primary }]}>View More Activities</Text>
                  <Feather name="chevron-down" size={16} color={colors.primary} style={{marginLeft: 4}} />
                </TouchableOpacity>
              )}
              
              {/* No activities state */}
              {activities.length === 0 && !loading && (
                <View style={styles.emptyState}>
                  <View style={[styles.emptyStateIcon, { backgroundColor: isDark ? 'rgba(59,130,246,0.1)' : 'rgba(59,130,246,0.05)' }]}>
                    <Feather name="activity" size={32} color={colors.primary} />
                  </View>
                  <Text style={[styles.emptyStateTitle, { color: colors.text }]}>No Activities Yet</Text>
                  <Text style={[styles.emptyStateSubtitle, { color: colors.textSecondary }]}>
                    Track your first workout by tapping the button below
                  </Text>
                  <TouchableOpacity
                    style={[styles.addActivityButton, { backgroundColor: colors.primary }]}
                    onPress={() => router.push('/profile-screens/add-activity' as any)}
                  >
                    <PlusCircle size={18} color="white" style={{ marginRight: 8 }} />
                    <Text style={styles.addActivityButtonText}>Add Activity</Text>
                  </TouchableOpacity>
                </View>
              )}
              
              {/* Data source indicator (only in development) */}
              {useSampleData && __DEV__ && (
                <View style={[styles.sampleDataIndicator, { backgroundColor: isDark ? 'rgba(239,68,68,0.1)' : 'rgba(239,68,68,0.05)' }]}>
                  <Text style={[styles.sampleDataText, { color: '#ef4444' }]}>
                    Using sample data (database table may not exist)
                  </Text>
                </View>
              )}
            </View>
          </ScrollView>
        )}
        
        {/* Floating Add Button */}
        {activities.length > 0 && (
          <TouchableOpacity
            style={[styles.floatingButton, { backgroundColor: colors.primary }]}
            onPress={() => router.push('/(tabs)/profile-screens/add-activity' as any)}
          >
            <Feather name="plus" size={24}  color={colors.text} />
          </TouchableOpacity>
        )}
      </View>
    </>
  );
}

// Track user interaction with an activity
async function trackActivityInteraction(activityId: string, interactionType: string) {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return;
    }

    const db = getFirestore();
    await addDoc(collection(db, 'activity_interactions'), {
      user_id: user.uid,
      activity_id: activityId,
      interaction_type: interactionType,
      timestamp: Timestamp.now()
    });
  } catch (err) {
    console.error('Error tracking activity interaction:', err);
    throw err; // Rethrow for caller to handle
  }
}

// Track when user views more activities
async function trackViewMoreActivities() {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return;
    }

    const db = getFirestore();
    await addDoc(collection(db, 'user_actions'), {
      user_id: user.uid,
      action_type: 'view_more_activities',
      timestamp: Timestamp.now()
    });
  } catch (err) {
    console.error('Error tracking view more action:', err);
    throw err; // Rethrow for caller to handle
  }
}

// Load more activities with increased limit
async function loadMoreActivities() {
  try {
    // Implementation would be similar to loadActivities but with higher limit
    console.log('Loading more activities...');
    // In a real app, you would implement pagination here
  } catch (err) {
    console.error('Error loading more activities:', err);
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContent: {
    paddingBottom: 40,
  },
  statsCard: {
    margin: 20,
    marginTop: 10,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 4 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      }
    }),
  },
  statsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  statsHeaderLeft: {
    flex: 1,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  statsSubtitle: {
    fontSize: 13,
    marginTop: 2,
  },
  timeframeSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  timeframeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
  },
  statItem: {
    width: '50%',
    padding: 15,
    alignItems: 'center',
    borderRightWidth: 1,
    borderBottomWidth: 1,
  },
  statIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  activityDistribution: {
    marginTop: 8,
  },
  distributionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  barChart: {
    marginTop: 8,
  },
  chartRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  chartLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: 100,
  },
  chartColorDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  chartLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  chartBarContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  chartBarBackground: {
    flex: 1,
    height: 10,
    borderRadius: 5,
    overflow: 'hidden',
  },
  chartBar: {
    height: '100%',
    borderRadius: 5,
    minWidth: 10,
  },
  chartValue: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 8,
    width: 80,
    textAlign: 'right',
  },
  activityHistorySection: {
    marginTop: 20,
    paddingHorizontal: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionHeaderLeft: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  sectionSubtitle: {
    fontSize: 13,
    marginTop: 2,
    opacity: 0.8,
  },
  filterSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  filterText: {
    fontSize: 12,
    fontWeight: '600',
  },
  timeline: {
    marginBottom: 20,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  timelineConnector: {
    width: 30,
    alignItems: 'center',
  },
  timelineLine: {
    width: 2,
    flex: 1,
  },
  timelineDot: {
    width: 30,
    height: 30,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 4,
  },
  activityCard: {
    flex: 1,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    marginLeft: 10,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: { elevation: 2 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  activityCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  activityDateBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  activityDate: {
    fontSize: 12,
    fontWeight: '500',
  },
  activityMoreButton: {
    padding: 4,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 12,
  },
  activityStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  activityDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  activityDetailText: {
    fontSize: 13,
    fontWeight: '500',
  },
  activityNotes: {
    padding: 10,
    borderRadius: 8,
  },
  activityNotesText: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyStateIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    paddingHorizontal: 40,
  },
  emptyStateText: {
    fontSize: 14,
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyStateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 24,
  },
  emptyStateButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  addActivityButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 24,
  },
  addActivityButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  viewMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 20,
  },
  viewMoreText: {
    fontSize: 14,
    fontWeight: '600',
  },
  sampleDataIndicator: {
    padding: 8,
    borderRadius: 8,
    marginVertical: 10,
  },
  sampleDataText: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  floatingButton: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3B82F6',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 6,
  },
}); 