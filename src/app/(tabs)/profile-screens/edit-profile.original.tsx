/**
 * Edit Profile Screen
 * Allows users to edit their profile information
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  ActivityIndicator, 
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Image
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { db as firestore, auth, storage } from '@/lib/firebase';
import { doc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { useDatabaseType } from '@/contexts/DatabaseContext';
import { cacheProfile, updateCachedProfile } from '../profile/utils/profileCache';
import { Profile } from '../profile/utils/types';
import { LinearGradient } from 'expo-linear-gradient';
import * as ImagePicker from 'expo-image-picker';
import { updateProfile } from 'firebase/auth';
import { ProfileImageUploader } from '@/components/ProfileComponents/ProfileImageUploader';

export default function EditProfileScreen() {
  const router = useRouter();
  const { section } = useLocalSearchParams<{ section?: string }>();
  const scrollViewRef = useRef<ScrollView>(null);
  const { isDark, colors } = useTheme();
  const { db, useFirebase } = useDatabaseType();
  const insets = useSafeAreaInsets();
  const user = auth.currentUser;

  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    full_name: '',
    username: '',
    email: '',
    age: '',
    location: '',
    height: '',
    weight: '',
    daily_calorie_goal: '',
    body_fat: '',
    gender: '',
    blood_pressure: '',
    resting_heart_rate: '',
    sleep_hours: '',
    activity_level: '',
    fitness_goal: '',
    dietary_preferences: ''
  });

  // Add unmount tracking
  const [isMounted, setIsMounted] = useState(true);

  // Check for permissions on component mount
  useEffect(() => {
    (async () => {
      if (Platform.OS !== 'web') {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission Required', 'Sorry, we need camera roll permissions to upload photos!');
        }
      }
    })();
  }, []);

  // Fetch profile data
  useEffect(() => {
    async function fetchProfile() {
      if (!user || !isMounted) return;

      try {
        if (isMounted) {
          setLoading(true);
        }
        
        if (useFirebase) {
          const profileRef = doc(firestore, 'profiles', user.uid);
          const profileDoc = await getDoc(profileRef);
          
          if (!isMounted) return;
          
          if (profileDoc.exists()) {
            const data = profileDoc.data() as Profile;
            if (isMounted) {
              setProfile(data);
              // Set selected image to current profile photo URL
              if (data.photoURL) {
                setSelectedImage(data.photoURL);
              }
              setFormData({
                full_name: data.full_name || '',
                username: data.username || '',
                email: data.email || '',
                age: data.age ? String(data.age) : '',
                location: data.location || '',
                height: data.height ? String(data.height) : '',
                weight: data.weight ? String(data.weight) : '',
                daily_calorie_goal: data.daily_calorie_goal ? String(data.daily_calorie_goal) : '',
                body_fat: data.body_fat ? String(data.body_fat) : '',
                gender: data.gender || '',
                blood_pressure: data.blood_pressure || '',
                resting_heart_rate: data.resting_heart_rate ? String(data.resting_heart_rate) : '',
                sleep_hours: data.sleep_hours ? String(data.sleep_hours) : '',
                activity_level: data.activity_level || '',
                fitness_goal: data.fitness_goal || '',
                dietary_preferences: data.dietary_preferences || ''
              });
            }
          }
        } else {
          // Using the database provider
          const data = await db.getDocument('profiles', user.uid);
          
          if (!isMounted) return;
          
          if (data) {
            if (isMounted) {
              setProfile(data);
              // Set selected image to current profile photo URL
              if (data.photoURL) {
                setSelectedImage(data.photoURL);
              }
              setFormData({
                full_name: data.full_name || '',
                username: data.username || '',
                email: data.email || '',
                age: data.age ? String(data.age) : '',
                location: data.location || '',
                height: data.height ? String(data.height) : '',
                weight: data.weight ? String(data.weight) : '',
                daily_calorie_goal: data.daily_calorie_goal ? String(data.daily_calorie_goal) : '',
                body_fat: data.body_fat ? String(data.body_fat) : '',
                gender: data.gender || '',
                blood_pressure: data.blood_pressure || '',
                resting_heart_rate: data.resting_heart_rate ? String(data.resting_heart_rate) : '',
                sleep_hours: data.sleep_hours ? String(data.sleep_hours) : '',
                activity_level: data.activity_level || '',
                fitness_goal: data.fitness_goal || '',
                dietary_preferences: data.dietary_preferences || ''
              });
            }
          }
        }
      } catch (error) {
        console.error('Unexpected error fetching profile:', error);
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }

    fetchProfile();
  }, [user, firestore, db, useFirebase, isMounted]);

  // Simplify image handling with the ProfileImageUploader component
  const handleAvatarChange = (url: string) => {
    if (url) {
      setSelectedImage(url);
      // Update formData if needed
      if (profile) {
        setProfile({
          ...profile,
          photoURL: url
        });
      }
    }
  };

  // Pick image from gallery
  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.7,
      });
      
      if (!isMounted) return;
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        console.log('Selected image URI:', result.assets[0].uri);
        setSelectedImage(result.assets[0].uri);
        
        // Ask user if they want to upload the image now
        if (isMounted) {
          Alert.alert(
            "Upload Profile Picture",
            "Do you want to upload this profile picture now?",
            [
              {
                text: "Cancel",
                style: "cancel"
              },
              { 
                text: "Upload", 
                onPress: () => uploadProfilePicture(result.assets[0].uri)
              }
            ]
          );
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      if (isMounted) {
        Alert.alert('Error', 'Failed to pick image. Please try again.');
      }
    }
  };

  // Upload profile picture to Firebase Storage
  const uploadProfilePicture = async (uri: string) => {
    if (!user) {
      if (isMounted) {
        Alert.alert('Error', 'You must be logged in to upload a profile picture');
      }
      return;
    }

    try {
      if (isMounted) {
        setUploadingImage(true);
      }
      
      // Convert uri to blob
      const response = await fetch(uri);
      const blob = await response.blob();
      
      // Create a reference to the storage location
      const imageRef = ref(storage, `profile_pictures/${user.uid}/${Date.now()}`);
      
      // Upload the blob to Firebase Storage
      await uploadBytes(imageRef, blob);
      
      // Get the download URL of the uploaded image
      const downloadURL = await getDownloadURL(imageRef);
      console.log('Image uploaded successfully. URL:', downloadURL);
      
      if (!isMounted) return;
      
      // Update the profile with the new photoURL
      if (useFirebase) {
        const profileRef = doc(firestore, 'profiles', user.uid);
        await updateDoc(profileRef, { photoURL: downloadURL });
        
        // Update local profile state
        if (isMounted) {
          setProfile(prev => prev ? { ...prev, photoURL: downloadURL } : null);
        }
      } else {
        // Using the database provider
        await db.updateDocument('profiles', user.uid, { photoURL: downloadURL });
        
        // Update local profile state
        if (isMounted) {
          setProfile(prev => prev ? { ...prev, photoURL: downloadURL } : null);
        }
      }
      
      // Update user auth profile
      if (auth.currentUser) {
        await updateProfile(auth.currentUser, { photoURL: downloadURL });
      }
      
      // Update selected image with the final URL
      if (isMounted) {
        setSelectedImage(downloadURL);
        Alert.alert('Success', 'Profile picture updated successfully');
      }
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      if (isMounted) {
        Alert.alert('Error', 'Failed to upload profile picture. Please try again.');
      }
    } finally {
      if (isMounted) {
        setUploadingImage(false);
      }
    }
  };

  // Scroll to health section if specified in URL params
  useEffect(() => {
    if (section === 'health' && !loading && scrollViewRef.current) {
      // Use setTimeout to ensure the ScrollView has rendered all content
      setTimeout(() => {
        if (scrollViewRef.current) {
          // Find the health section by index (2 is the index of the health section in this case)
          const healthSectionIndex = 2;
          scrollViewRef.current.scrollTo({ y: 350, animated: true });
        }
      }, 500);
    }
  }, [section, loading]);

  // Handle form input changes
  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Add cleanup on unmount
  useEffect(() => {
    return () => {
      setIsMounted(false);
    };
  }, []);

  // Modify the handleSave function to ensure Firebase updates complete before navigation
  const handleSave = async () => {
    if (!user) {
      Alert.alert('Error', 'You must be logged in to update your profile');
      return;
    }

    try {
      setSaving(true);

      // Process form data for database
      const processedData: Record<string, any> = {
        full_name: formData.full_name,
        username: formData.username,
        location: formData.location,
        email: formData.email,
        gender: formData.gender,
        blood_pressure: formData.blood_pressure,
        activity_level: formData.activity_level,
        fitness_goal: formData.fitness_goal,
        dietary_preferences: formData.dietary_preferences,
        updated_at: serverTimestamp() // Add server timestamp
      };
      
      // Add photoURL to processed data if it has changed
      if (selectedImage && selectedImage !== profile?.photoURL) {
        processedData.photoURL = selectedImage;
      }

      // Convert numeric fields
      if (formData.age !== '') {
        processedData.age = parseInt(formData.age, 10) || 0;
      }
      
      if (formData.height !== '') {
        processedData.height = formData.height.trim();
      }
      
      if (formData.weight !== '') {
        processedData.weight = parseFloat(formData.weight) || 0;
      }
      
      if (formData.daily_calorie_goal !== '') {
        processedData.daily_calorie_goal = parseInt(formData.daily_calorie_goal, 10) || 0;
      }
      
      if (formData.body_fat !== '') {
        processedData.body_fat = parseFloat(formData.body_fat) || 0;
      }
      
      if (formData.resting_heart_rate !== '') {
        processedData.resting_heart_rate = parseInt(formData.resting_heart_rate, 10) || 0;
      }
      
      if (formData.sleep_hours !== '') {
        processedData.sleep_hours = parseFloat(formData.sleep_hours) || 0;
      }

      console.log('Saving profile data:', processedData);
      
      // Implement retry logic for profile updates
      const maxRetries = 3;
      let success = false;
      let lastError = null;
      let savedProfile: Profile | null = null;
      
      for (let attempt = 0; attempt < maxRetries && isMounted; attempt++) {
        try {
          if (attempt > 0) {
            console.log(`Retry attempt ${attempt + 1} to save profile...`);
          }

          // Update profile in Firebase - DO NOT check isMounted during the actual Firebase operations
          // to ensure they complete even if navigation starts
          if (useFirebase) {
            const profileRef = doc(firestore, 'profiles', user.uid);
            await updateDoc(profileRef, processedData);
            
            // Get the updated profile to cache it
            const updatedDoc = await getDoc(profileRef);
            if (updatedDoc.exists()) {
              const updatedProfile = updatedDoc.data() as Profile;
              // Save the updated profile for later state updates
              savedProfile = updatedProfile;
              
              // Cache the profile even if component might unmount - this is important for data consistency
              await cacheProfile(user.uid, updatedProfile);
            }
          } else {
            // Using the database provider
            await db.updateDocument('profiles', user.uid, processedData);
            
            // Get updated profile for caching
            const updatedProfile = await db.getDocument('profiles', user.uid);
            if (updatedProfile) {
              savedProfile = updatedProfile;
              await cacheProfile(user.uid, updatedProfile);
            }
          }
          
          success = true;
          break; // Exit retry loop on success
        } catch (error: any) {
          lastError = error;
          console.error(`Attempt ${attempt + 1} failed:`, error);
          
          // Wait before retrying (exponential backoff)
          if (attempt < maxRetries - 1) {
            const delay = Math.pow(2, attempt) * 500;
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
      }

      // Only update UI state if component is still mounted
      if (isMounted) {
        if (success) {
          // Update the local state for better UX
          if (savedProfile) {
            setProfile(savedProfile);
          } else {
            // Fallback to update with processed data
            setProfile(prev => {
              if (!prev) return null;
              
              return {
                ...prev,
                ...processedData,
                // Override with parsed values for correct typing
                age: formData.age ? parseInt(formData.age, 10) : prev.age,
                height: formData.height || prev.height,
                weight: formData.weight ? parseFloat(formData.weight) : prev.weight,
                daily_calorie_goal: formData.daily_calorie_goal ? 
                  parseInt(formData.daily_calorie_goal, 10) : prev.daily_calorie_goal,
                body_fat: formData.body_fat ? parseFloat(formData.body_fat) : prev.body_fat,
              };
            });
          }
          
          Alert.alert(
            'Success', 
            'Profile updated successfully', 
            [{ 
              text: 'OK', 
              onPress: () => {
                // Navigate only after confirmation
                if (isMounted) {
                  router.back();
                }
              }
            }]
          );
        } else {
          // If Firebase update failed, still update local cache
          try {
            const localUpdates = {
              full_name: formData.full_name,
              username: formData.username,
              location: formData.location,
              email: formData.email,
              age: formData.age ? parseInt(formData.age, 10) : undefined,
              height: formData.height || undefined,
              weight: formData.weight ? parseFloat(formData.weight) : undefined,
              daily_calorie_goal: formData.daily_calorie_goal ? 
                parseInt(formData.daily_calorie_goal, 10) : undefined,
              body_fat: formData.body_fat ? parseFloat(formData.body_fat) : undefined,
              gender: formData.gender || undefined,
              blood_pressure: formData.blood_pressure || undefined,
              resting_heart_rate: formData.resting_heart_rate ? parseInt(formData.resting_heart_rate, 10) : undefined,
              sleep_hours: formData.sleep_hours ? parseFloat(formData.sleep_hours) : undefined,
              activity_level: formData.activity_level || undefined,
              fitness_goal: formData.fitness_goal || undefined,
              dietary_preferences: formData.dietary_preferences || undefined,
            };
            
            // Cache updates immediately, don't depend on component mount state
            if (user) {
              await updateCachedProfile(user.uid, localUpdates);
            }
            
            // Also update local state if component is still mounted
            setProfile(prev => {
              if (!prev) return null;
              
              return {
                ...prev,
                ...processedData,
                // Override with parsed values for correct typing
                age: formData.age ? parseInt(formData.age, 10) : prev.age,
                height: formData.height || prev.height,
                weight: formData.weight ? parseFloat(formData.weight) : prev.weight,
                daily_calorie_goal: formData.daily_calorie_goal ? 
                  parseInt(formData.daily_calorie_goal, 10) : prev.daily_calorie_goal,
                body_fat: formData.body_fat ? parseFloat(formData.body_fat) : prev.body_fat,
              };
            });
            
            Alert.alert(
              'Warning', 
              'Some changes may not have been saved to the server. Your data has been saved locally and will sync when connection is available.',
              [{ 
                text: 'OK', 
                onPress: () => {
                  if (isMounted) {
                    router.back();
                  }
                }
              }]
            );
          } catch (e) {
            console.error('Failed to update cache:', e);
            
            Alert.alert(
              'Error', 
              'An error occurred while saving your changes. Please try again.',
              [{ text: 'OK' }]
            );
          }
        }
      } else {
        // Component unmounted but we still want to ensure data is cached
        console.log('Component unmounted, but still ensuring profile data is cached');
        try {
          if (user) {
            await updateCachedProfile(user.uid, {
              full_name: formData.full_name,
              username: formData.username,
              location: formData.location,
              email: formData.email, 
              age: formData.age ? parseInt(formData.age, 10) : undefined,
              height: formData.height || undefined,
              weight: formData.weight ? parseFloat(formData.weight) : undefined,
              daily_calorie_goal: formData.daily_calorie_goal ? 
                parseInt(formData.daily_calorie_goal, 10) : undefined,
              body_fat: formData.body_fat ? parseFloat(formData.body_fat) : undefined,
              gender: formData.gender || undefined,
            });
          }
        } catch (e) {
          console.error('Failed to update cache after component unmount:', e);
        }
      }
    } catch (error) {
      console.error('Unexpected error updating profile:', error);
      if (isMounted) {
        Alert.alert(
          'Error', 
          'An unexpected error occurred. Please try again.',
          [{ text: 'OK' }]
        );
      }
    } finally {
      if (isMounted) {
        setSaving(false);
      }
    }
  };

  // Helper to get gradient colors for different sections
  const getSectionGradient = (sectionType: string): readonly [string, string] => {
    switch(sectionType) {
      case 'basic':
        return isDark ? ['#3B82F620', '#2563EB10'] as const : ['#EFF6FF', '#DBEAFE'] as const;
      case 'personal':
        return isDark ? ['#8B5CF620', '#7C3AED10'] as const : ['#F5F3FF', '#EDE9FE'] as const;
      case 'health':
        return isDark ? ['#10B98120', '#059669110'] as const : ['#ECFDF5', '#D1FAE5'] as const;
      default:
        return isDark ? ['#3B82F620', '#2563EB10'] as const : ['#EFF6FF', '#DBEAFE'] as const;
    }
  };

  // Render custom form input
  const renderFormInput = (
    label: string, 
    field: keyof typeof formData, 
    placeholder: string, 
    icon: React.ReactNode, 
    keyboardType: 'default' | 'email-address' | 'number-pad' | 'decimal-pad' = 'default',
    autoCapitalize: 'none' | 'sentences' = 'sentences'
  ) => {
    return (
      <View style={[styles.formGroup, { backgroundColor: colors.card, borderColor: colors.border }]}>
        <View style={styles.labelContainer}>
          {icon}
          <Text style={[styles.label, { color: colors.textSecondary }]}>{label}</Text>
        </View>
        <TextInput
          style={[styles.input, { color: colors.text, borderColor: `${colors.border}80` }]}
          value={formData[field]}
          onChangeText={(text) => handleChange(field, text)}
          placeholder={placeholder}
          placeholderTextColor={`${colors.textSecondary}80`}
          keyboardType={keyboardType}
          autoCapitalize={autoCapitalize}
        />
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={[styles.container, { backgroundColor: colors.background }]}
    >
      <Stack.Screen
        options={{
          headerShown: false
        }}
      />

      {/* Gradient Header */}
      <LinearGradient
        colors={isDark ? ['#1E40AF', '#3B82F6'] as const : ['#3B82F6', '#60A5FA'] as const}
        style={[styles.header, { paddingTop: insets.top || 40 }]}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: 'rgba(255, 255, 255, 0.2)' }]}
            onPress={() => router.push('/(tabs)/profile')}
          >
            <Feather name="chevron-left" size={24}  color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Profile</Text>
          <TouchableOpacity
            style={[
              styles.saveButton, 
              { backgroundColor: 'rgba(255, 255, 255, 0.2)' }, 
              saving && styles.disabledButton
            ]}
            onPress={handleSave}
            disabled={saving}
          >
            {saving ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Feather name="save" size={20}  color={colors.text} />
            )}
          </TouchableOpacity>
        </View>
        
        {/* Add extra padding at the bottom of header for avatar */}
        <View style={{ height: 60 }} />
      </LinearGradient>

      {/* Avatar Section */}
      <View style={styles.avatarSection}>
        <ProfileImageUploader
          avatarUrl={selectedImage || profile?.photoURL || null}
          onAvatarChange={handleAvatarChange}
          size={100}
          disabled={loading || saving}
        />
        <Text style={[styles.username, { color: colors.text }]}>
          {formData.username || 'Username'}
        </Text>
        <Text style={[styles.email, { color: colors.textSecondary }]}>
          {formData.email || '<EMAIL>'}
        </Text>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading profile...
          </Text>
        </View>
      ) : (
        <ScrollView
          ref={scrollViewRef}
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Form Sections */}
          <LinearGradient 
            colors={getSectionGradient('basic')}
            style={styles.sectionContainer}
          >
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Basic Information</Text>
            </View>
            
            {renderFormInput(
              'Full Name', 
              'full_name', 
              'Enter your full name', 
              <Feather name="user" size={18} color={colors.primary} style={styles.inputIcon} />
            )}
            
            {renderFormInput(
              'Username', 
              'username', 
              'Enter a username', 
              <Feather name="user" size={18} style={styles.inputIcon} />,
              'default',
              'none'
            )}
            
            {renderFormInput(
              'Email', 
              'email', 
              'Enter your email', 
              <Feather name="mail" size={18} style={styles.inputIcon} />,
              'email-address',
              'none'
            )}
          </LinearGradient>

          <LinearGradient 
            colors={getSectionGradient('personal')}
            style={styles.sectionContainer}
          >
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Personal Information</Text>
            </View>
            
            {renderFormInput(
              'Age', 
              'age', 
              'Enter your age', 
              <Feather name="calendar" size={18} style={styles.inputIcon} />,
              'number-pad'
            )}
            
            {renderFormInput(
              'Location', 
              'location', 
              'Enter your location', 
              <Feather name="map-pin" size={18} style={styles.inputIcon} />
            )}
          </LinearGradient>

          <LinearGradient 
            colors={getSectionGradient('health')}
            style={styles.sectionContainer}
          >
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Health Information</Text>
            </View>
            
            {renderFormInput(
              'Height', 
              'height', 
              'Enter your height (e.g., 5\'10 or 178cm)', 
              <MaterialIcons name="straighten" size={18} style={styles.inputIcon} />
            )}
            
            {renderFormInput(
              'Weight (lbs)', 
              'weight', 
              'Enter your weight in pounds', 
              <MaterialIcons name="fitness-center" size={18} style={styles.inputIcon} />,
              'decimal-pad'
            )}
            
            {renderFormInput(
              'Daily Calorie Goal', 
              'daily_calorie_goal', 
              'Enter your daily calorie goal', 
              <Feather name="zap" size={18} style={styles.inputIcon} />,
              'number-pad'
            )}
            
            {renderFormInput(
              'Body Fat (%)', 
              'body_fat', 
              'Enter your body fat percentage', 
              <Feather name="heart" size={18} style={styles.inputIcon} />,
              'decimal-pad'
            )}
          </LinearGradient>

          {/* Advanced Health Metrics */}
          <LinearGradient 
            colors={getSectionGradient('personal')}
            style={styles.sectionContainer}
          >
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Advanced Health Metrics</Text>
            </View>
            
            {renderFormInput(
              'Gender', 
              'gender', 
              'Enter your gender', 
              <Feather name="user" size={18} style={styles.inputIcon} />
            )}
            
            {renderFormInput(
              'Blood Pressure', 
              'blood_pressure', 
              'E.g., 120/80', 
              <Feather name="heart" size={18} style={styles.inputIcon} />
            )}
            
            {renderFormInput(
              'Resting Heart Rate (bpm)', 
              'resting_heart_rate', 
              'Enter your resting heart rate', 
              <Feather name="heart" size={18} style={styles.inputIcon} />,
              'number-pad'
            )}
            
            {renderFormInput(
              'Sleep Hours (daily avg)', 
              'sleep_hours', 
              'Enter your average sleep hours', 
              <Feather name="calendar" size={18} style={styles.inputIcon} />,
              'decimal-pad'
            )}
          </LinearGradient>

          {/* Lifestyle & Preferences */}
          <LinearGradient 
            colors={getSectionGradient('basic')}
            style={styles.sectionContainer}
          >
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Lifestyle & Preferences</Text>
            </View>
            
            {renderFormInput(
              'Activity Level', 
              'activity_level', 
              'E.g., Sedentary, Light, Moderate, Active', 
              <Feather name="zap" size={18} style={styles.inputIcon} />
            )}
            
            {renderFormInput(
              'Fitness Goal', 
              'fitness_goal', 
              'E.g., Weight loss, Muscle gain, Health', 
              <Feather name="zap" size={18} style={styles.inputIcon} />
            )}
            
            {renderFormInput(
              'Dietary Preferences', 
              'dietary_preferences', 
              'E.g., Vegetarian, Keto, Paleo, None', 
              <MessageCircle size={18} color="#8B5CF6" style={styles.inputIcon} />
            )}
          </LinearGradient>

          {/* Save Button */}
          <TouchableOpacity
            style={[
              styles.submitButton,
              { backgroundColor: colors.primary },
              saving && styles.disabledButton
            ]}
            onPress={handleSave}
            disabled={saving}
          >
            {saving ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <>
                <MaterialIcons name="check-circle" size={20} style={styles.submitButtonIcon} />
                <Text style={styles.submitButtonText}>Save Changes</Text>
              </>
            )}
          </TouchableOpacity>

          {/* Cancel Button */}
          <TouchableOpacity
            style={[styles.cancelButton, { borderColor: colors.border }]}
            onPress={() => router.push('/(tabs)/profile')}
          >
            <Feather name="x" size={18} color={colors.error} style={styles.cancelButtonIcon} />
            <Text style={[styles.cancelButtonText, { color: colors.error }]}>
              Cancel
            </Text>
          </TouchableOpacity>
        </ScrollView>
      )}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: 'white',
  },
  saveButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarSection: {
    alignItems: 'center',
    marginTop: -50,
    paddingBottom: 16,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 10,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 4,
    borderColor: 'white',
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: 'white',
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  username: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  email: {
    fontSize: 14,
  },
  changePhotoText: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: 8,
  },
  disabledButton: {
    opacity: 0.5,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  sectionContainer: {
    marginBottom: 20,
    borderRadius: 16,
    padding: 16,
    paddingBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  sectionHeader: {
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  formGroup: {
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  inputIcon: {
    marginRight: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
  },
  input: {
    fontSize: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
  },
  submitButton: {
    flexDirection: 'row',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  submitButtonIcon: {
    marginRight: 8,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  cancelButton: {
    flexDirection: 'row',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
    borderWidth: 1,
    borderStyle: 'dashed',
  },
  cancelButtonIcon: {
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
  }
}); 