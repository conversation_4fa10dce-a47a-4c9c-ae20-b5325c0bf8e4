/**
 * Edit Profile Screen - REFACTORED
 * Clean architecture: No Firebase imports!
 */
import React, { useEffect, useRef } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  ActivityIndicator, 
  ScrollView,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { ProfileImageUploader } from '@/components/ProfileComponents/ProfileImageUploader';
import { useProfileEdit } from '@/hooks/useProfileEdit';
import { useAuth } from '@/contexts/AuthContext';

export default function EditProfileScreen() {
  const router = useRouter();
  const { section } = useLocalSearchParams<{ section?: string }>();
  const scrollViewRef = useRef<ScrollView>(null);
  const { isDark, colors } = useTheme();
  const insets = useSafeAreaInsets();
  const { user } = useAuth();
  
  // Use the clean hook!
  const {
    profile,
    loading,
    saving,
    formData,
    updateFormData,
    updateNestedFormData,
    saveProfile
  } = useProfileEdit();

  // Auto-scroll to section
  useEffect(() => {
    if (section && scrollViewRef.current) {
      setTimeout(() => {
        const sectionOffsets: { [key: string]: number } = {
          'personal': 0,
          'stats': 300,
          'health': 600,
          'social': 900,
        };
        
        if (sectionOffsets[section]) {
          scrollViewRef.current?.scrollTo({ y: sectionOffsets[section], animated: true });
        }
      }, 100);
    }
  }, [section]);

  const handleSave = async () => {
    const success = await saveProfile();
    if (success) {
      router.back();
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Stack.Screen
          options={{
            title: 'Edit Profile',
            headerShown: true,
            headerStyle: { backgroundColor: colors.card },
            headerTintColor: colors.text,
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      </View>
    );
  }

  const renderPersonalSection = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>Personal Information</Text>
      
      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: colors.textSecondary }]}>Full Name</Text>
        <TextInput
          style={[styles.input, { 
            backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
            color: colors.text,
            borderColor: colors.border
          }]}
          value={formData.full_name}
          onChangeText={(text) => updateFormData('full_name', text)}
          placeholder="Enter your full name"
          placeholderTextColor={colors.textSecondary}
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: colors.textSecondary }]}>Username</Text>
        <TextInput
          style={[styles.input, { 
            backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
            color: colors.text,
            borderColor: colors.border
          }]}
          value={formData.username}
          onChangeText={(text) => updateFormData('username', text)}
          placeholder="Choose a username"
          placeholderTextColor={colors.textSecondary}
          autoCapitalize="none"
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: colors.textSecondary }]}>Bio</Text>
        <TextInput
          style={[styles.input, styles.textArea, { 
            backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
            color: colors.text,
            borderColor: colors.border
          }]}
          value={formData.bio}
          onChangeText={(text) => updateFormData('bio', text)}
          placeholder="Tell us about yourself"
          placeholderTextColor={colors.textSecondary}
          multiline
          numberOfLines={4}
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: colors.textSecondary }]}>
          <Feather name="map-pin" size={16} color={colors.textSecondary} /> Location
        </Text>
        <TextInput
          style={[styles.input, { 
            backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
            color: colors.text,
            borderColor: colors.border
          }]}
          value={formData.location}
          onChangeText={(text) => updateFormData('location', text)}
          placeholder="City, Country"
          placeholderTextColor={colors.textSecondary}
        />
      </View>
    </View>
  );

  const renderStatsSection = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>Physical Stats</Text>
      
      <View style={styles.row}>
        <View style={[styles.inputGroup, { flex: 1, marginRight: 8 }]}>
          <Text style={[styles.label, { color: colors.textSecondary }]}>
            <MaterialIcons name="straighten" size={16} color={colors.textSecondary} /> Height (cm)
          </Text>
          <TextInput
            style={[styles.input, { 
              backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
              color: colors.text,
              borderColor: colors.border
            }]}
            value={formData.stats?.height?.toString() || ''}
            onChangeText={(text) => updateNestedFormData('stats', 'height', parseInt(text) || 0)}
            placeholder="170"
            placeholderTextColor={colors.textSecondary}
            keyboardType="numeric"
          />
        </View>

        <View style={[styles.inputGroup, { flex: 1, marginLeft: 8 }]}>
          <Text style={[styles.label, { color: colors.textSecondary }]}>
            <MaterialIcons name="fitness-center" size={16} color={colors.textSecondary} /> Weight (kg)
          </Text>
          <TextInput
            style={[styles.input, { 
              backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
              color: colors.text,
              borderColor: colors.border
            }]}
            value={formData.stats?.weight?.toString() || ''}
            onChangeText={(text) => updateNestedFormData('stats', 'weight', parseInt(text) || 0)}
            placeholder="70"
            placeholderTextColor={colors.textSecondary}
            keyboardType="numeric"
          />
        </View>
      </View>

      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: colors.textSecondary }]}>
          <Feather name="zap" size={16} color={colors.textSecondary} /> Activity Level
        </Text>
        <View style={styles.activityButtons}>
          {['sedentary', 'light', 'moderate', 'active', 'very active'].map((level) => (
            <TouchableOpacity
              key={level}
              style={[
                styles.activityButton,
                {
                  backgroundColor: formData.stats?.activityLevel === level 
                    ? colors.primary 
                    : isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                  borderColor: formData.stats?.activityLevel === level 
                    ? colors.primary 
                    : colors.border
                }
              ]}
              onPress={() => updateNestedFormData('stats', 'activityLevel', level)}
            >
              <Text style={[
                styles.activityButtonText,
                { 
                  color: formData.stats?.activityLevel === level 
                    ? 'white' 
                    : colors.text 
                }
              ]}>
                {level.charAt(0).toUpperCase() + level.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );

  return (
    <KeyboardAvoidingView 
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <Stack.Screen
        options={{
          title: 'Edit Profile',
          headerShown: true,
          headerStyle: { backgroundColor: colors.card },
          headerTintColor: colors.text,
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} style={{ marginLeft: 16 }}>
              <Feather name="chevron-left" size={24} color={colors.text} />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <TouchableOpacity 
              onPress={handleSave} 
              disabled={saving}
              style={{ marginRight: 16 }}
            >
              {saving ? (
                <ActivityIndicator size="small" color={colors.primary} />
              ) : (
                <Feather name="save" size={24} color={colors.primary} />
              )}
            </TouchableOpacity>
          ),
        }}
      />

      <ScrollView 
        ref={scrollViewRef}
        style={styles.scrollView}
        contentContainerStyle={{ paddingBottom: insets.bottom + 100 }}
        showsVerticalScrollIndicator={false}
      >
        {/* Profile Image Section */}
        <View style={styles.imageSection}>
          <ProfileImageUploader
            avatarUrl={formData.avatarUrl || null}
            onAvatarChange={(url) => updateFormData('avatarUrl', url)}
            size={120}
          />
          <Text style={[styles.emailText, { color: colors.textSecondary }]}>
            {user?.email}
          </Text>
        </View>

        {/* Form Sections */}
        {renderPersonalSection()}
        {renderStatsSection()}
        
        {/* Save Button */}
        <TouchableOpacity
          style={[styles.saveButton, { 
            backgroundColor: colors.primary,
            opacity: saving ? 0.6 : 1
          }]}
          onPress={handleSave}
          disabled={saving}
        >
          <LinearGradient
            colors={['#3b82f6', '#2563eb']}
            style={styles.saveButtonGradient}
          >
            {saving ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <>
                <MaterialIcons name="check-circle" size={20}  color={colors.text} />
                <Text style={styles.saveButtonText}>Save Changes</Text>
              </>
            )}
          </LinearGradient>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageSection: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  emailText: {
    marginTop: 12,
    fontSize: 16,
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
  },
  activityButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  activityButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginBottom: 8,
  },
  activityButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  saveButton: {
    marginHorizontal: 20,
    marginTop: 24,
    borderRadius: 12,
    overflow: 'hidden',
  },
  saveButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});