import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Platform, Dimensions, TextInput, ActivityIndicator } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Feather , MaterialIcons , Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { Path, Circle, G, Text as SvgText, Line, Defs, LinearGradient as SvgLinearGradient, Stop } from 'react-native-svg';
import { fetchHealthStats, updateHealthMetrics, createProfile } from '@/services/profileService';
import { getAuth } from 'firebase/auth';
import { getFirestore, collection, query, where, orderBy, getDocs } from 'firebase/firestore';
import { useAlert } from '@/hooks/useAlert';
import { getApp, getApps, initializeApp } from 'firebase/app';
import firebaseConfig from '@/firebase.config';

interface IconProps {
  size?: number;
  color?: string;
  [key: string]: any;
}

interface HealthMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  change?: number;
  history: { timestamp: string; value: number }[];
  icon: React.ComponentType<IconProps>;
  color: [string, string];
}

interface HealthStat {
  title: string;
  value: string | number;
  unit: string;
  icon: React.ReactNode;
  editable?: boolean;
  field?: string;
}

interface StatsState {
  weight?: number;
  height?: number;
  calorieGoal?: number;
  waterGoal?: number;
  consumedCalories?: number;
  consumedWater?: number;
  consumedProtein?: number;
  consumedCarbs?: number;
  consumedFat?: number;
  [key: string]: number | undefined;
}

const { width: screenWidth } = Dimensions.get('window');
const CHART_WIDTH = screenWidth - 40;
const CHART_HEIGHT = 180;

// Ensure Firebase app is initialized
const ensureFirebaseApp = () => {
  if (!getApps().length) {
    initializeApp(firebaseConfig);
  }
  return getApp();
};

// Ensure Firestore is initialized and returned
const getFirestoreInstance = () => {
  ensureFirebaseApp();
  return getFirestore();
};

export default function HealthDataScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const { showAlert } = useAlert();
  const [activeTimeframe, setActiveTimeframe] = useState<string>('month');
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<StatsState | null>(null);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<string>('');
  const [isSaving, setIsSaving] = useState(false);
  const [healthMetrics, setHealthMetrics] = useState<HealthMetric[]>([]);
  
  // Helper function to render change indicator
  const renderChangeIndicator = (change?: number) => {
    if (!change) return null;
    
    const isPositive = change > 0;
    const color = isPositive ? '#EF4444' : '#10B981'; // Red for increase, green for decrease
    const sign = isPositive ? '+' : '';
    
    return (
      <View style={[styles.changeIndicator, { backgroundColor: `${color}20` }]}>
        {isPositive ? (
          <Feather name="trending-up" size={12} color={color} style={styles.changeIcon} />
        ) : (
          <Feather name="trending-up" size={12} color={color} style={[styles.changeIcon, styles.changeIconDown]} />
        )}
        <Text style={[styles.changeText, { color }]}>
          {sign}{change}{healthMetrics[0]?.id === 'weight' ? ' lbs' : ''}
        </Text>
      </View>
    );
  };
  
  // Helper function to determine min and max values for chart
  const getMinMaxValues = (metric: HealthMetric) => {
    if (!metric.history || metric.history.length === 0) {
      return { min: 0, max: 1 };
    }
    
    let min = Math.min(...metric.history.map(p => p.value));
    let max = Math.max(...metric.history.map(p => p.value));
    
    // If min and max are the same, create a small range
    if (min === max) {
      min = min * 0.9;
      max = max * 1.1;
      // Ensure we don't go below zero for things that can't be negative
      if (min < 0 && ['weight', 'height', 'bmi', 'restingHeartRate', 'bodyfat'].includes(metric.id)) {
        min = 0;
        max = max * 2 || 1; // If max was 0, use 1
      }
    }
    
    // Add a bit of padding
    const range = max - min;
    min = min - range * 0.1;
    max = max + range * 0.1;
    
    // Ensure min doesn't go below zero for metrics that can't be negative
    if (min < 0 && ['weight', 'height', 'bmi', 'restingHeartRate', 'bodyfat'].includes(metric.id)) {
      min = 0;
    }
    
    return { min, max };
  };
  
  // Function to draw chart path
  const getChartPath = (metric: HealthMetric) => {
    if (!metric.history || metric.history.length === 0) {
      // Return empty path if no data
      return '';
    }
    
    if (metric.history.length === 1) {
      // If only one data point, draw a horizontal line
      const y = CHART_HEIGHT / 2; // Center point
      return `M0,${y} L${CHART_WIDTH},${y}`;
    }
    
    const { min, max } = getMinMaxValues(metric);
    
    // Check if min and max create a valid range
    const range = max - min;
    if (range === 0 || isNaN(range)) {
      // Draw a horizontal line in the middle if no valid range
      const y = CHART_HEIGHT / 2;
      return `M0,${y} L${CHART_WIDTH},${y}`;
    }
    
    let path = '';
    metric.history.forEach((point, index) => {
      const x = (index / (metric.history.length - 1)) * CHART_WIDTH;
      // Invert Y axis since SVG 0,0 is top-left
      const y = CHART_HEIGHT - ((point.value - min) / (max - min)) * CHART_HEIGHT;
      
      // Ensure y is a valid number
      const validY = isNaN(y) ? CHART_HEIGHT / 2 : y;
      
      if (index === 0) {
        path += `M${x},${validY}`;
      } else {
        path += ` L${x},${validY}`;
      }
    });
    
    return path;
  };

  useEffect(() => {
    loadHealthData();
    fetchHistoricalData();
  }, []);
  
  const loadHealthData = async () => {
    try {
      setLoading(true);
      
      // Ensure profile exists before fetching health stats
      await createProfile();
      
      const result = await fetchHealthStats();
      
      if (result.success) {
        setStats(result.data as StatsState);
      } else {
        showAlert({
          title: 'Error',
          message: result.error || 'Failed to load health data',
          type: 'error',
        });
      }
    } catch (error) {
      console.error('Error loading health data:', error);
      showAlert({
        title: 'Error',
        message: 'An error occurred while loading your health data',
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchHistoricalData = async () => {
    try {
      // Get the current user
      const auth = getAuth();
      const user = auth.currentUser;
      if (!user) return;

      // Initialize Firestore
      const db = getFirestoreInstance();
      
      // Fetch weight history
      const weightHistoryRef = collection(db, 'health_metrics');
      const weightQuery = query(
        weightHistoryRef,
        where('user_id', '==', user.uid),
        where('type', '==', 'weight'),
        orderBy('timestamp', 'asc')
      );
      const weightSnapshot = await getDocs(weightQuery);
      const weightHistory = weightSnapshot.docs.map(doc => ({
        timestamp: doc.data().timestamp.toDate().toISOString(),
        value: doc.data().value
      }));

      // Fetch height history
      const heightHistoryRef = collection(db, 'health_metrics');
      const heightQuery = query(
        heightHistoryRef,
        where('user_id', '==', user.uid),
        where('type', '==', 'height'),
        orderBy('timestamp', 'asc')
      );
      const heightSnapshot = await getDocs(heightQuery);
      const heightHistory = heightSnapshot.docs.map(doc => ({
        timestamp: doc.data().timestamp.toDate().toISOString(),
        value: doc.data().value
      }));

      // Fetch heart rate history
      const heartRateHistoryRef = collection(db, 'health_metrics');
      const heartRateQuery = query(
        heartRateHistoryRef,
        where('user_id', '==', user.uid),
        where('type', '==', 'heart_rate'),
        orderBy('timestamp', 'asc')
      );
      const heartRateSnapshot = await getDocs(heartRateQuery);
      const heartRateHistory = heartRateSnapshot.docs.map(doc => ({
        timestamp: doc.data().timestamp.toDate().toISOString(),
        value: doc.data().value
      }));

      // Current weight and height from stats
      const currentWeight = stats?.weight || 0;
      const currentHeight = stats?.height || 0;

      // Calculate BMI history
      const bmiHistory = weightHistory && heightHistory ? 
        weightHistory.map((w, index) => {
          const h = heightHistory[Math.min(index, heightHistory.length - 1)];
          if (!h || !h.value) return { timestamp: w.timestamp, value: 0 };
          const heightInMeters = h.value * 0.0254; // inches to meters
          const bmi = w.value / (heightInMeters * heightInMeters);
          return { timestamp: w.timestamp, value: Math.round(bmi * 10) / 10 };
        }) : [];

      // Create metrics array
      const metrics: HealthMetric[] = [
        {
          id: 'weight',
          name: 'Weight',
          value: currentWeight,
          unit: 'lbs',
          change: weightHistory && weightHistory.length > 1 ? 
            currentWeight - weightHistory[weightHistory.length - 2].value : 0,
          history: weightHistory || [],
          icon: Weight,
          color: ['#3B82F6', '#2563EB'],
        },
        {
          id: 'height',
          name: 'Height',
          value: currentHeight,
          unit: 'in',
          history: heightHistory || [],
          icon: Ruler,
          color: ['#8B5CF6', '#7C3AED'],
        },
        {
          id: 'bmi',
          name: 'BMI',
          value: currentHeight && currentWeight ? 
            Math.round((currentWeight / Math.pow(currentHeight * 0.0254, 2)) * 10) / 10 : 0,
          unit: '',
          change: bmiHistory && bmiHistory.length > 1 ?
            (bmiHistory[bmiHistory.length - 1].value - bmiHistory[bmiHistory.length - 2].value) : 0,
          history: bmiHistory || [],
          icon: Activity,
          color: ['#EC4899', '#DB2777'],
        },
        {
          id: 'restingHeartRate',
          name: 'Resting Heart Rate',
          value: heartRateHistory && heartRateHistory.length > 0 ?
            heartRateHistory[heartRateHistory.length - 1].value : 0,
          unit: 'bpm',
          change: heartRateHistory && heartRateHistory.length > 1 ?
            (heartRateHistory[heartRateHistory.length - 1].value - heartRateHistory[heartRateHistory.length - 2].value) : 0,
          history: heartRateHistory || [],
          icon: Heart,
          color: ['#EF4444', '#DC2626'],
        }
      ];

      setHealthMetrics(metrics);
    } catch (error) {
      console.error('Error fetching historical data:', error);
    }
  };
  
  const handleStartEdit = (field: string, value: number | string) => {
    setEditingField(field);
    setEditValue(value.toString());
  };
  
  const handleCancelEdit = () => {
    setEditingField(null);
    setEditValue('');
  };
  
  const handleSaveEdit = async () => {
    if (!editingField) return;
    
    try {
      setIsSaving(true);
      
      const value = parseFloat(editValue);
      if (isNaN(value)) {
        showAlert({
          title: 'Invalid Value',
          message: 'Please enter a valid number',
          type: 'error',
        });
        return;
      }
      
      const updateValues: any = {};
      updateValues[editingField] = value;
      
      const result = await updateHealthMetrics(updateValues);
      
      if (result.success) {
        // Update local state
        if (stats) {
          setStats({
            ...stats,
            [editingField]: value,
          });
        }
        
        showAlert({
          title: 'Success',
          message: 'Health data updated successfully',
          type: 'success',
        });
        
        // Also fetch historical data to reflect changes
        fetchHistoricalData();
      } else {
        showAlert({
          title: 'Error',
          message: result.error || 'Failed to update health data',
          type: 'error',
        });
      }
    } catch (error) {
      console.error('Error saving edit:', error);
      showAlert({
        title: 'Error',
        message: 'An error occurred while saving your data',
        type: 'error',
      });
    } finally {
      setIsSaving(false);
      setEditingField(null);
      setEditValue('');
    }
  };
  
  const getHealthStats = (): HealthStat[] => [
    { 
      title: 'Weight', 
      value: stats?.weight || 0, 
      unit: 'lbs', 
      icon: <Scale size={24} color={colors.primary} />,
      editable: true,
      field: 'weight'
    },
    { 
      title: 'Height', 
      value: stats?.height || 0, 
      unit: 'in', 
      icon: <MaterialIcons name="straighten" size={24}  color={colors.text} />,
      editable: true,
      field: 'height'
    },
    { 
      title: 'Daily Calorie Goal', 
      value: stats?.calorieGoal || 2000, 
      unit: 'cal', 
      icon: <Feather name="zap" size={24}  color={colors.text} />,
      editable: true,
      field: 'calorieGoal'
    },
    { 
      title: 'Daily Water Goal', 
      value: stats?.waterGoal || 2000, 
      unit: 'ml', 
      icon: <Ionicons name="water" size={24}  color={colors.text} />,
      editable: true,
      field: 'waterGoal'
    },
    { 
      title: 'Calories Consumed Today', 
      value: stats?.consumedCalories || 0, 
      unit: 'cal', 
      icon: <Feather name="zap" size={24}  color={colors.text} />,
    },
    { 
      title: 'Water Consumed Today', 
      value: stats?.consumedWater || 0, 
      unit: 'ml', 
      icon: <Ionicons name="water" size={24}  color={colors.text} />,
    },
    { 
      title: 'Protein Today', 
      value: stats?.consumedProtein || 0, 
      unit: 'g', 
      icon: <Feather name="activity" size={24}  color={colors.text} />,
    },
    { 
      title: 'Carbs Today', 
      value: stats?.consumedCarbs || 0, 
      unit: 'g', 
      icon: <Feather name="activity" size={24}  color={colors.text} />,
    },
    { 
      title: 'Fat Today', 
      value: stats?.consumedFat || 0, 
      unit: 'g', 
      icon: <Feather name="activity" size={24}  color={colors.text} />,
    },
  ];

  // Timeframes for filter tabs
  const timeframes = [
    { id: 'week', name: '1W' },
    { id: 'month', name: '1M' },
    { id: 'quarter', name: '3M' },
    { id: 'year', name: '1Y' },
    { id: 'all', name: 'All' },
  ];

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen 
        options={{ 
          headerShown: false
        }} 
      />
      
      <View style={[styles.header, { backgroundColor: colors.background }]}>
        <TouchableOpacity 
          style={[styles.backButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.05)' }]}
          onPress={() => router.back()}
        >
          <Feather name="chevron-left" size={20} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Health Data</Text>
        <TouchableOpacity 
          style={[styles.actionButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.05)' }]}
          onPress={loadHealthData}
        >
          <Feather name="refresh-cw" size={20} color={colors.text} />
        </TouchableOpacity>
      </View>
      
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={[styles.summaryCard, { 
          backgroundColor: colors.card,
          borderColor: colors.border,
          shadowColor: isDark ? '#000' : colors.shadow
        }]}>
          <View style={styles.summaryHeader}>
            <Text style={[styles.summaryTitle, { color: colors.text }]}>Today's Summary</Text>
            <Text style={[styles.lastUpdated, { color: colors.textSecondary }]}>
              Last updated: {new Date().toLocaleTimeString()}
            </Text>
          </View>
          
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading health data...</Text>
            </View>
          ) : (
            <View style={styles.statsGrid}>
              {getHealthStats().map((stat, index) => (
                <View 
                  key={index} 
                  style={[
                    styles.statCard, 
                    { 
                      backgroundColor: isDark ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.01)',
                      borderColor: colors.border
                    }
                  ]}
                >
                  <View style={styles.statHeader}>
                    <View style={[styles.statIcon, { backgroundColor: isDark ? 'rgba(59,130,246,0.15)' : 'rgba(59,130,246,0.1)' }]}>{stat.icon}</View>
                    {stat.editable && (
                      <TouchableOpacity 
                        style={[styles.editButton, { backgroundColor: isDark ? 'rgba(59,130,246,0.1)' : 'rgba(59,130,246,0.05)' }]}
                        onPress={() => handleStartEdit(stat.field!, stat.value)}
                      >
                        {isSaving && editingField === stat.field ? (
                          <ActivityIndicator size="small" color={colors.primary} />
                        ) : (
                          <Feather name="edit-3" size={16} color={colors.primary} />
                        )}
                      </TouchableOpacity>
                    )}
                  </View>
                  
                  <Text style={[styles.statTitle, { color: colors.textSecondary }]}>{stat.title}</Text>
                  
                  {editingField === stat.field ? (
                    <View style={styles.editContainer}>
                      <TextInput
                        style={[styles.editInput, { 
                          color: colors.text,
                          borderColor: colors.border,
                          backgroundColor: isDark ? 'rgba(0,0,0,0.1)' : 'rgba(0,0,0,0.02)'
                        }]}
                        value={editValue}
                        onChangeText={setEditValue}
                        keyboardType="numeric"
                        autoFocus
                      />
                      <View style={styles.editActions}>
                        <TouchableOpacity 
                          style={[styles.editActionButton, { backgroundColor: '#EF4444' }]}
                          onPress={handleCancelEdit}
                        >
                          <Text style={styles.editActionText}>Cancel</Text>
                        </TouchableOpacity>
                        <TouchableOpacity 
                          style={[styles.editActionButton, { backgroundColor: '#10B981' }]}
                          onPress={handleSaveEdit}
                        >
                          <Text style={styles.editActionText}>Save</Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  ) : (
                    <Text style={[styles.statValue, { color: colors.text }]}>
                      {stat.value} <Text style={[styles.statUnit, { color: colors.textSecondary }]}>{stat.unit}</Text>
                    </Text>
                  )}
                </View>
              ))}
            </View>
          )}
        </View>
        
        <View style={styles.timeframeContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Historical Metrics
          </Text>
          <View style={styles.timeframeTabs}>
            {timeframes.map((tf) => (
              <TouchableOpacity
                key={tf.id}
                style={[
                  styles.timeframeTab,
                  activeTimeframe === tf.id && styles.activeTimeframeTab,
                  { 
                    backgroundColor: activeTimeframe === tf.id 
                      ? colors.primary 
                      : isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
                  }
                ]}
                onPress={() => setActiveTimeframe(tf.id)}
              >
                <Text
                  style={[
                    styles.timeframeText,
                    { 
                      color: activeTimeframe === tf.id 
                        ? '#FFF' 
                        : colors.textSecondary 
                    }
                  ]}
                >
                  {tf.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        {healthMetrics.map((metric) => (
          <View 
            key={metric.id}
            style={[styles.metricCard, { 
              backgroundColor: colors.card,
              borderColor: colors.border,
              shadowColor: colors.shadow
            }]}
          >
            <View style={styles.metricHeader}>
              <View style={styles.metricTitleContainer}>
                <LinearGradient
                  colors={metric.color as readonly [string, string]}
                  style={styles.metricTitleIcon}
                >
                  <metric.icon size={18} color="white" />
                </LinearGradient>
                <Text style={[styles.metricTitle, { color: colors.text }]}>{metric.name}</Text>
              </View>
              <View style={styles.metricHeaderRight}>
                <Text style={[styles.metricCurrentValue, { color: colors.text }]}>
                  {metric.value} <Text style={[styles.metricCurrentUnit, { color: colors.textSecondary }]}>{metric.unit}</Text>
                </Text>
                {renderChangeIndicator(metric.change)}
              </View>
            </View>
            
            <Svg width={CHART_WIDTH} height={CHART_HEIGHT}>
              <Defs>
                <SvgLinearGradient id={`gradient-${metric.id}`} x1="0%" y1="0%" x2="0%" y2="100%">
                  <Stop offset="0%" stopColor={metric.color[0]} stopOpacity="0.6" />
                  <Stop offset="100%" stopColor={metric.color[0]} stopOpacity="0.1" />
                </SvgLinearGradient>
              </Defs>
              
              {/* Background grid lines */}
              {[0, 1, 2, 3, 4].map((i) => (
                <Line 
                  key={`grid-${i}`}
                  x1="0"
                  y1={i * (CHART_HEIGHT / 4)}
                  x2={CHART_WIDTH}
                  y2={i * (CHART_HEIGHT / 4)}
                  stroke={isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'}
                  strokeDasharray="5,5"
                />
              ))}
              
              {/* Chart fill */}
              <Path
                d={getChartPath(metric) ? `${getChartPath(metric)} L${CHART_WIDTH},${CHART_HEIGHT} L0,${CHART_HEIGHT} Z` : `M0,${CHART_HEIGHT} L${CHART_WIDTH},${CHART_HEIGHT} Z`}
                fill={`url(#gradient-${metric.id})`}
              />
              
              {/* Chart line */}
              {getChartPath(metric) ? (
                <Path
                  d={getChartPath(metric)}
                  stroke={metric.color[0]}
                  strokeWidth="2.5"
                  fill="none"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              ) : null}
              
              {/* Data points */}
              {metric.history.map((point, i) => {
                if (!metric.history || metric.history.length === 0) {
                  return null;
                }
                
                const { min, max } = getMinMaxValues(metric);
                const x = (i / (metric.history.length - 1)) * CHART_WIDTH;
                let y = CHART_HEIGHT - ((point.value - min) / (max - min)) * CHART_HEIGHT;
                
                // Ensure y is a valid number
                if (isNaN(y) || !isFinite(y)) {
                  y = CHART_HEIGHT / 2;
                }
                
                // Only show dots for first, middle, and last points to avoid clutter
                if (i === 0 || i === Math.floor(metric.history.length / 2) || i === metric.history.length - 1) {
                  return (
                    <G key={`point-${i}`}>
                      <Circle 
                        cx={x}
                        cy={y}
                        r={5}
                        fill={colors.card}
                        stroke={metric.color[0]}
                        strokeWidth="2"
                      />
                      <Circle 
                        cx={x}
                        cy={y}
                        r={2.5}
                        fill={metric.color[0]}
                      />
                    </G>
                  );
                }
                return null;
              })}
              
              {/* X-axis labels */}
              {metric.history.length > 0 && (
                <>
                  <SvgText
                    x={0}
                    y={CHART_HEIGHT - 5}
                    fontSize="10"
                    textAnchor="start"
                    fill={colors.textSecondary}
                  >
                    {new Date(metric.history[0].timestamp).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                  </SvgText>
                  
                  {metric.history.length > 2 && (
                    <SvgText
                      x={CHART_WIDTH / 2}
                      y={CHART_HEIGHT - 5}
                      fontSize="10"
                      textAnchor="middle"
                      fill={colors.textSecondary}
                    >
                      {new Date(metric.history[Math.floor(metric.history.length / 2)].timestamp).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                    </SvgText>
                  )}
                  
                  <SvgText
                    x={CHART_WIDTH}
                    y={CHART_HEIGHT - 5}
                    fontSize="10"
                    textAnchor="end"
                    fill={colors.textSecondary}
                  >
                    {new Date(metric.history[metric.history.length - 1].timestamp).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                  </SvgText>
                </>
              )}
            </Svg>
            
            <View style={styles.metricFooter}>
              <Text style={[styles.metricFooterText, { color: colors.textSecondary }]}>
                {metric.history.length > 0 
                  ? `${metric.history.length} data points from ${new Date(metric.history[0].timestamp).toLocaleDateString()} to ${new Date(metric.history[metric.history.length - 1].timestamp).toLocaleDateString()}`
                  : 'No historical data available'}
              </Text>
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollContent: {
    paddingBottom: 40,
  },
  summaryCard: {
    margin: 20,
    marginTop: 10,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 4 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      }
    }),
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  lastUpdated: {
    fontSize: 12,
  },
  summaryMetrics: {
    marginBottom: 20,
  },
  summaryMetric: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  metricIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 14,
  },
  metricLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  metricValueRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 18,
    fontWeight: '700',
    marginRight: 4,
  },
  metricUnit: {
    fontSize: 14,
    marginRight: 6,
  },
  changeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  changeIcon: {
    marginRight: 4,
  },
  changeIconDown: {
    transform: [{ rotate: '180deg' }],
  },
  changeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  timeframeContainer: {
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 12,
  },
  timeframeTabs: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  timeframeTab: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginRight: 8,
  },
  activeTimeframeTab: {
    backgroundColor: '#3B82F6',
  },
  timeframeText: {
    fontSize: 14,
    fontWeight: '600',
  },
  metricCard: {
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: { elevation: 2 },
      web: {
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  metricHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  metricTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metricTitleIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  metricTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  metricHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metricCurrentValue: {
    fontSize: 18,
    fontWeight: '700',
    marginRight: 6,
  },
  metricCurrentUnit: {
    fontSize: 14,
  },
  metricFooter: {
    marginTop: 12,
    alignItems: 'center',
  },
  metricFooterText: {
    fontSize: 12,
  },
  loadingContainer: {
    padding: 30,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
  },
  statCard: {
    width: '48%',
    marginHorizontal: '1%',
    marginBottom: 12,
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  editButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statTitle: {
    fontSize: 12,
    marginBottom: 4,
  },
  statValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  statUnit: {
    fontSize: 12,
    fontWeight: '400',
  },
  editContainer: {
    marginTop: 8,
  },
  editInput: {
    height: 36,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 8,
    fontSize: 16,
  },
  editActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  editActionButton: {
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    marginHorizontal: 2,
  },
  editActionText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
}); 