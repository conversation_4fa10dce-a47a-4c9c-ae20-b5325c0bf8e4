import React, { useState } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, TextInput, Platform, Linking } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';

export default function HelpSupportScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const [message, setMessage] = useState('');
  const [subject, setSubject] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Handle contact form submission
  const handleSubmitMessage = () => {
    if (!message.trim()) {
      return;
    }
    
    setIsSubmitting(true);
    
    // Simulate submission with a delay
    setTimeout(() => {
      setIsSubmitting(false);
      setMessage('');
      setSubject('');
      alert('Your message has been sent. We\'ll get back to you within 24 hours.');
    }, 1500);
  };
  
  // Handle direct email
  const openEmailClient = () => {
    const emailSubject = encodeURIComponent(subject || 'Help request');
    const emailBody = encodeURIComponent(message || '');
    Linking.openURL(`mailto:<EMAIL>?subject=${emailSubject}&body=${emailBody}`);
  };

  return (
    <>
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.card }]}>
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={() => router.push('/(tabs)/profile')}
            accessibilityLabel="Go back"
            accessibilityHint="Returns to the profile screen"
          >
            <Feather name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Contact Support</Text>
          <View style={styles.placeholderRight} />
        </View>
        
        <ScrollView 
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          {/* Hero Section with enhanced visual design */}
          <View style={[styles.heroCard, { 
            backgroundColor: colors.card,
            borderColor: colors.border,
            shadowColor: colors.shadow
          }]}>
            <LinearGradient
              colors={isDark ? ['#1E293B', '#0F172A'] : ['#E0F2FE', '#DBEAFE']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.heroGradient}
            >
              <View style={styles.heroContent}>
                <View style={styles.heroIconContainer}>
                  <LinearGradient
                    colors={['#3B82F6', '#2563EB'] as const}
                    style={styles.heroIconGradient}
                  >
                    <Feather name="help-circle" size={32}  color={colors.text} />
                  </LinearGradient>
                </View>
                <Text style={[styles.heroTitle, { color: colors.text }]}>
                  Need help with something?
                </Text>
                <Text style={[styles.heroDescription, { color: colors.textSecondary }]}>
                  Our support team is here to assist you with any issues or questions
                </Text>
              </View>
            </LinearGradient>
          </View>
          
          {/* Enhanced Contact Form */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Send us a message</Text>
            <View style={[styles.contactForm, { 
              backgroundColor: colors.card,
              borderColor: colors.border,
              shadowColor: colors.shadow
            }]}>
              <Text style={[styles.contactFormText, { color: colors.textSecondary }]}>
                We'll respond to your inquiry within 24 hours during business days. Please include as much detail as possible.
              </Text>
              
              {/* Subject Input */}
              <View style={[styles.subjectContainer, { 
                backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)',
                borderColor: colors.border
              }]}>
                <TextInput
                  style={[styles.subjectInput, { color: colors.text }]}
                  placeholder="Subject"
                  placeholderTextColor={colors.textSecondary}
                  value={subject}
                  onChangeText={setSubject}
                />
              </View>
              
              {/* Message Input */}
              <View style={[styles.messageContainer, { 
                backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)',
                borderColor: colors.border
              }]}>
                <TextInput
                  style={[styles.messageInput, { color: colors.text }]}
                  placeholder="Describe your issue in detail..."
                  placeholderTextColor={colors.textSecondary}
                  value={message}
                  onChangeText={setMessage}
                  multiline
                  numberOfLines={6}
                  textAlignVertical="top"
                />
              </View>
              
              <View style={styles.buttonGroup}>
                <TouchableOpacity 
                  style={[styles.submitButton, 
                    !message.trim() ? { opacity: 0.6 } : {},
                    { flex: 1 }
                  ]}
                  onPress={handleSubmitMessage}
                  disabled={!message.trim() || isSubmitting}
                >
                  <LinearGradient
                    colors={['#3B82F6', '#2563EB'] as const}
                    style={styles.submitButtonGradient}
                  >
                    <Text style={styles.submitButtonText}>
                      {isSubmitting ? 'Sending...' : 'Send Message'}
                    </Text>
                    <Send size={16} color="white" style={styles.submitIcon} />
                  </LinearGradient>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[styles.emailButton, { borderColor: colors.border }]}
                  onPress={openEmailClient}
                >
                  <Feather name="mail" size={20} color={colors.primary} />
                  <Text style={[styles.emailButtonText, { color: colors.primary }]}>
                    Open Email App
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
          
          {/* Support Info */}
          <View style={[styles.supportInfo, { backgroundColor: colors.card, borderColor: colors.border }]}>
            <Text style={[styles.supportInfoTitle, { color: colors.text }]}>Support Hours</Text>
            <Text style={[styles.supportInfoText, { color: colors.textSecondary }]}>
              Monday - Friday: 9:00 AM - 5:00 PM (EST)
            </Text>
            <Text style={[styles.supportInfoText, { color: colors.textSecondary }]}>
              We strive to respond to all inquiries within 24 business hours.
            </Text>
          </View>
          
          {/* Privacy Note */}
          <View style={styles.privacyNote}>
            <Feather name="lock" size={14} color={colors.textSecondary} style={styles.privacyIcon} />
            <Text style={[styles.privacyText, { color: colors.textSecondary }]}>
              Your messages are secure and confidential
            </Text>
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  placeholderRight: {
    width: 40,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  // Hero section
  heroCard: {
    margin: 20,
    marginTop: 10,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 4 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      }
    }),
  },
  heroGradient: {
    padding: 20,
  },
  heroContent: {
    alignItems: 'center',
    marginBottom: 10,
  },
  heroIconContainer: {
    marginBottom: 16,
  },
  heroIconGradient: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  heroTitle: {
    fontSize: 22,
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
  },
  heroDescription: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
  // Section styling
  section: {
    marginHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  // Contact form
  contactForm: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: { elevation: 2 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  contactFormText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  subjectContainer: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
  },
  subjectInput: {
    fontSize: 16,
  },
  messageContainer: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
  },
  messageInput: {
    fontSize: 16,
    minHeight: 120,
  },
  buttonGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  submitButton: {
    borderRadius: 12,
    overflow: 'hidden',
    marginRight: 12,
  },
  submitButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  submitIcon: {
    marginLeft: 4,
  },
  emailButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 14,
    paddingVertical: 10,
    borderRadius: 12,
    borderWidth: 1,
  },
  emailButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  // Support info
  supportInfo: {
    marginHorizontal: 20,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 20,
  },
  supportInfoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  supportInfoText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
  // Privacy note
  privacyNote: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 20,
  },
  privacyIcon: {
    marginRight: 6,
  },
  privacyText: {
    fontSize: 13,
  },
}); 