import React, { useState, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  ScrollView, 
  TouchableOpacity, 
  ActivityIndicator 
} from 'react-native';
import { Stack } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { contextualNavigation , ShortcutSuggestion } from '@/utils/contextualNavigation';
import { SafeAreaView } from 'react-native-safe-area-context';
import Svg, { G, Circle, Path, Rect, Text as SvgText } from 'react-native-svg';

interface NavigationData {
  screenVisits: Record<string, number>;
  timeOfDayData: Record<string, number>;
  dayOfWeekData: Record<string, number>;
  totalVisits: number;
  recentScreens: ShortcutSuggestion[];
}

export default function NavigationAnalytics() {
  const { colors, isDark } = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState<NavigationData>({
    screenVisits: {},
    timeOfDayData: {},
    dayOfWeekData: {},
    totalVisits: 0,
    recentScreens: []
  });

  // Load analytics data
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      // Get navigation data for analytics
      const analyticsData = await contextualNavigation.getAnalyticsData();
      
      // Get recent screens
      const recentScreens = contextualNavigation.getRecentScreens(10);
      
      // Format the data for visualization
      const formattedData: NavigationData = {
        screenVisits: analyticsData.screenVisits || {},
        timeOfDayData: analyticsData.timeOfDayData || {},
        dayOfWeekData: analyticsData.dayOfWeekData || {},
        totalVisits: analyticsData.totalVisits || 0,
        recentScreens: recentScreens
      };
      
      setData(formattedData);
    } catch (error) {
      console.error('Failed to load navigation analytics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Get top visited screens (sorted)
  const getTopScreens = (limit: number = 5) => {
    const sortedScreens = Object.entries(data.screenVisits)
      .sort(([, countA], [, countB]) => countB - countA)
      .slice(0, limit);
    
    return sortedScreens.map(([screen, count]) => {
      const { title, icon } = getScreenInfo(screen);
      return { screen, title, icon, count };
    });
  };

  // Helper to get screen info
  const getScreenInfo = (screen: string) => {
    // Try to find in recent screens
    const found = data.recentScreens.find(s => s.screen === screen);
    if (found) {
      return { title: found.title, icon: found.icon };
    }
    
    // Default fallback
    return { 
      title: screen.replace(/^\//, '').replace(/-/g, ' '), 
      icon: 'apps' 
    };
  };

  // Get day of week name
  const getDayName = (dayIndex: string) => {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[parseInt(dayIndex)] || `Day ${dayIndex}`;
  };

  // Get time of day description
  const getTimeDescription = (hourIndex: string) => {
    const hour = parseInt(hourIndex);
    if (hour >= 5 && hour < 12) return 'Morning';
    if (hour >= 12 && hour < 17) return 'Afternoon';
    if (hour >= 17 && hour < 21) return 'Evening';
    return 'Night';
  };

  // Render bar chart for screen visits
  const renderBarChart = () => {
    const topScreens = getTopScreens();
    const maxCount = Math.max(...topScreens.map(item => item.count));
    
    return (
      <View style={styles.chartContainer}>
        {topScreens.map((item, index) => (
          <View key={index} style={styles.barChartRow}>
            <View style={styles.barLabelContainer}>
              <Ionicons name={item.icon as any} size={18} color={colors.primary} style={styles.barIcon} />
              <Text 
                style={[styles.barLabel, { color: colors.text }]}
                numberOfLines={1}
              >
                {item.title}
              </Text>
            </View>
            <View style={styles.barContainer}>
              <View 
                style={[
                  styles.bar, 
                  { 
                    width: `${(item.count / maxCount) * 100}%`,
                    backgroundColor: getBarColor(index, isDark),
                  }
                ]} 
              />
              <Text style={[styles.barValue, { color: colors.textSecondary }]}>
                {item.count}
              </Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  // Render pie chart for day of week data
  const renderDayOfWeekChart = () => {
    const dayData = Object.entries(data.dayOfWeekData)
      .map(([day, count]) => ({ day: getDayName(day), count, value: day }))
      .sort((a, b) => parseInt(a.value) - parseInt(b.value));
    
    const total = dayData.reduce((sum, item) => sum + item.count, 0);
    
    // Generate pie chart segments
    let currentAngle = 0;
    const segments = dayData.map((item, index) => {
      const percentage = total > 0 ? (item.count / total) : 0;
      const angle = percentage * 360;
      
      // Calculate pie slice path
      const startAngle = currentAngle;
      const endAngle = currentAngle + angle;
      currentAngle = endAngle;
      
      const start = polarToCartesian(100, 100, 80, startAngle);
      const end = polarToCartesian(100, 100, 80, endAngle);
      
      const largeArcFlag = angle > 180 ? 1 : 0;
      
      const path = [
        `M 100 100`,
        `L ${start.x} ${start.y}`,
        `A 80 80 0 ${largeArcFlag} 1 ${end.x} ${end.y}`,
        `Z`
      ].join(' ');
      
      return {
        path,
        percentage,
        color: getPieColor(index, isDark),
        day: item.day,
        count: item.count
      };
    });
    
    // Only show pie chart if we have data
    if (total === 0) {
      return (
        <View style={styles.emptyChartContainer}>
          <Text style={[styles.emptyChartText, { color: colors.textSecondary }]}>
            Not enough data to show day patterns
          </Text>
        </View>
      );
    }
    
    return (
      <View style={styles.pieChartContainer}>
        <Svg height="220" width="100%" viewBox="0 0 200 200">
          {segments.map((segment, index) => (
            <Path
              key={index}
              d={segment.path}
              fill={segment.color}
              stroke="white"
              strokeWidth={1}
            />
          ))}
          <Circle cx="100" cy="100" r="50" fill={isDark ? '#1c1c1e' : 'white'} />
        </Svg>
        
        <View style={styles.legendContainer}>
          {segments.map((segment, index) => (
            <View key={index} style={styles.legendItem}>
              <View 
                style={[
                  styles.legendColor, 
                  { backgroundColor: segment.color }
                ]} 
              />
              <Text style={[styles.legendText, { color: colors.text }]}>
                {segment.day} ({Math.round(segment.percentage * 100)}%)
              </Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  // Render time of day chart
  const renderTimeOfDayChart = () => {
    // Group data into morning, afternoon, evening, night
    const timeGroups = {
      'Morning (5-12)': 0,
      'Afternoon (12-17)': 0,
      'Evening (17-21)': 0,
      'Night (21-5)': 0
    };
    
    // Aggregate hours into time groups
    Object.entries(data.timeOfDayData).forEach(([hour, count]) => {
      const hourNum = parseInt(hour);
      if (hourNum >= 5 && hourNum < 12) {
        timeGroups['Morning (5-12)'] += count;
      } else if (hourNum >= 12 && hourNum < 17) {
        timeGroups['Afternoon (12-17)'] += count;
      } else if (hourNum >= 17 && hourNum < 21) {
        timeGroups['Evening (17-21)'] += count;
      } else {
        timeGroups['Night (21-5)'] += count;
      }
    });
    
    const timeData = Object.entries(timeGroups).map(([time, count]) => ({ time, count }));
    
    // Calculate max count for bar height
    const maxCount = Math.max(...timeData.map(item => item.count), 1);
    
    return (
      <View style={styles.horizontalBarChartContainer}>
        <Svg height="180" width="100%" viewBox="0 0 300 180">
          {timeData.map((item, index) => {
            const barHeight = (item.count / maxCount) * 120;
            const barWidth = 40;
            const spacing = 30;
            const x = 50 + (barWidth + spacing) * index;
            const y = 140 - barHeight;
            
            return (
              <G key={index}>
                <Rect
                  x={x}
                  y={y}
                  width={barWidth}
                  height={barHeight}
                  fill={getBarColor(index, isDark)}
                  rx={6}
                />
                <SvgText
                  x={x + barWidth / 2}
                  y={150}
                  fontSize="10"
                  textAnchor="middle"
                  fill={isDark ? '#aaa' : '#555'}
                >
                  {item.time.split(' ')[0]}
                </SvgText>
                <SvgText
                  x={x + barWidth / 2}
                  y={y - 8}
                  fontSize="12"
                  textAnchor="middle"
                  fontWeight="bold"
                  fill={isDark ? 'white' : 'black'}
                >
                  {item.count}
                </SvgText>
              </G>
            );
          })}
        </Svg>
      </View>
    );
  };

  // Helper function to convert polar coordinates to cartesian for SVG paths
  const polarToCartesian = (centerX: number, centerY: number, radius: number, angleInDegrees: number) => {
    const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
    return {
      x: centerX + (radius * Math.cos(angleInRadians)),
      y: centerY + (radius * Math.sin(angleInRadians))
    };
  };

  // Get bar color based on index
  const getBarColor = (index: number, isDark: boolean): string => {
    const colors = isDark ? [
      '#4f46e5', // indigo
      '#0891b2', // cyan
      '#8b5cf6', // violet
      '#059669', // emerald
      '#0284c7', // sky
      '#7c3aed', // purple
    ] : [
      '#4338ca', // indigo
      '#0e7490', // cyan
      '#7c3aed', // violet
      '#047857', // emerald
      '#0369a1', // sky
      '#6d28d9', // purple
    ];
    
    return colors[index % colors.length];
  };

  // Get pie chart color
  const getPieColor = (index: number, isDark: boolean): string => {
    const colors = isDark ? [
      '#4f46e5', // indigo
      '#0891b2', // cyan
      '#8b5cf6', // violet
      '#059669', // emerald
      '#0284c7', // sky
      '#7c3aed', // purple
      '#14b8a6', // teal
    ] : [
      '#4338ca', // indigo
      '#0e7490', // cyan
      '#7c3aed', // violet
      '#047857', // emerald
      '#0369a1', // sky
      '#6d28d9', // purple
      '#0d9488', // teal
    ];
    
    return colors[index % colors.length];
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: isDark ? '#121212' : '#f9f9f9' }]}
      edges={['right', 'left']}
    >
      <Stack.Screen
        options={{
          title: 'Navigation Analytics',
          headerStyle: {
            backgroundColor: isDark ? '#1c1c1e' : '#ffffff',
          },
          headerTintColor: colors.text,
          headerShadowVisible: false,
        }}
      />

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Analyzing your navigation patterns...
          </Text>
        </View>
      ) : (
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Summary */}
          <View style={styles.summaryContainer}>
            <View style={[styles.statsCard, { backgroundColor: isDark ? '#1c1c1e' : '#ffffff' }]}>
              <Text style={[styles.statsTitle, { color: colors.text }]}>
                Navigation Summary
              </Text>
              <View style={styles.statsRow}>
                <View style={styles.statItem}>
                  <Text style={[styles.statValue, { color: colors.primary }]}>
                    {data.totalVisits}
                  </Text>
                  <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                    Total Visits
                  </Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={[styles.statValue, { color: colors.primary }]}>
                    {Object.keys(data.screenVisits).length}
                  </Text>
                  <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                    Unique Screens
                  </Text>
                </View>
              </View>
            </View>
          </View>

          {/* Top Visited Screens */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Most Visited Screens
            </Text>
            <View style={[styles.card, { backgroundColor: isDark ? '#1c1c1e' : '#ffffff' }]}>
              {renderBarChart()}
            </View>
          </View>

          {/* Day of Week Analysis */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Day of Week Analysis
            </Text>
            <View style={[styles.card, { backgroundColor: isDark ? '#1c1c1e' : '#ffffff' }]}>
              {renderDayOfWeekChart()}
            </View>
          </View>

          {/* Time of Day Analysis */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Time of Day Activity
            </Text>
            <View style={[styles.card, { backgroundColor: isDark ? '#1c1c1e' : '#ffffff' }]}>
              {renderTimeOfDayChart()}
            </View>
          </View>

          {/* Actions */}
          <View style={styles.actionSection}>
            <TouchableOpacity
              style={[
                styles.actionButton,
                { backgroundColor: isDark ? '#333' : '#f5f5f5' }
              ]}
              onPress={loadData}
            >
              <Ionicons name="refresh" size={18} color={colors.primary} />
              <Text style={[styles.actionButtonText, { color: colors.primary }]}>
                Refresh Analytics
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.actionButton,
                { backgroundColor: isDark ? '#333' : '#f5f5f5' }
              ]}
              onPress={() => contextualNavigation.clearHistory()}
            >
              <Ionicons name="trash-outline" size={18} color={colors.error} />
              <Text style={[styles.actionButtonText, { color: colors.error }]}>
                Clear Navigation History
              </Text>
            </TouchableOpacity>
          </View>

          {/* Privacy Note */}
          <View style={styles.privacyContainer}>
            <Ionicons 
              name="shield-checkmark" 
              size={18} 
              color={colors.success} 
              style={styles.privacyIcon} 
            />
            <Text style={[styles.privacyText, { color: colors.textSecondary }]}>
              All navigation data is stored locally on your device and is never shared with external services.
            </Text>
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  summaryContainer: {
    marginBottom: 24,
  },
  statsCard: {
    borderRadius: 16,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 12,
  },
  card: {
    borderRadius: 16,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  chartContainer: {
    marginVertical: 8,
  },
  barChartRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  barLabelContainer: {
    width: '30%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  barIcon: {
    marginRight: 8,
  },
  barLabel: {
    fontSize: 14,
    flex: 1,
  },
  barContainer: {
    flex: 1,
    height: 24,
    flexDirection: 'row',
    alignItems: 'center',
  },
  bar: {
    height: 16,
    borderRadius: 8,
  },
  barValue: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  pieChartContainer: {
    alignItems: 'center',
    marginVertical: 16,
  },
  legendContainer: {
    marginTop: 16,
    width: '100%',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  legendColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  legendText: {
    fontSize: 14,
  },
  horizontalBarChartContainer: {
    marginVertical: 16,
  },
  emptyChartContainer: {
    paddingVertical: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyChartText: {
    fontSize: 16,
    fontStyle: 'italic',
  },
  actionSection: {
    marginBottom: 24,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 12,
    marginBottom: 12,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  privacyContainer: {
    flexDirection: 'row',
    paddingHorizontal: 8,
    marginBottom: 20,
  },
  privacyIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  privacyText: {
    fontSize: 13,
    flex: 1,
    lineHeight: 18,
  }
}); 