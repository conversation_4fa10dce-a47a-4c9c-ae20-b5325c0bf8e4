import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Switch, ActivityIndicator, Platform, Alert, TextInput } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Feather , FontAwesome } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
// Firebase imports
import { getFirestore, doc, getDoc, setDoc, updateDoc } from 'firebase/firestore';
import { app } from '@/lib/firebase';
import { useAuth } from '@/contexts/AuthContext';
import { format, parse } from 'date-fns';
import * as Notifications from 'expo-notifications';
// Import DateTimePickerModal conditionally
let DateTimePickerModal: any = null;
if (Platform.OS !== 'web') {
  DateTimePickerModal = require('react-native-modal-datetime-picker').default;
}

// Initialize Firestore
const db = getFirestore(app);

export type NotificationType = 'system' | 'activity' | 'goal' | 'reminder' | 'achievement' | 'social';

interface NotificationPreferences {
  push_enabled: boolean;
  email_enabled: boolean;
  in_app_enabled: boolean;
  quiet_hours_enabled: boolean;
  quiet_hours_start: string; // 24hr format: "22:00"
  quiet_hours_end: string; // 24hr format: "07:00"
  type_preferences: Record<NotificationType, boolean>;
  custom_sounds_enabled: boolean;
  weekly_digest_enabled: boolean;
  user_id: string;
  created_at: string;
  updated_at: string;
}

export default function NotificationSettingsScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const { user, signOut } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [isTimePickerVisible, setTimePickerVisible] = useState(false);
  const [timePickerMode, setTimePickerMode] = useState<'start' | 'end'>('start');
  const [notificationPrefs, setNotificationPrefs] = useState<NotificationPreferences>({
    push_enabled: false,
    email_enabled: false,
    in_app_enabled: false,
    quiet_hours_enabled: false,
    quiet_hours_start: "22:00",
    quiet_hours_end: "07:00",
    type_preferences: {
    system: true,
    activity: true,
    goal: true,
    reminder: true,
    achievement: true,
    social: true
    },
    custom_sounds_enabled: false,
    weekly_digest_enabled: false,
    user_id: user?.id || '',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  });
  
  // Check notification permissions on component mount
  useEffect(() => {
    if (user) {
      const initialize = async () => {
        try {
          await checkNotificationPermissions();
          await loadNotificationPreferences();
        } catch (error) {
          console.error('Error initializing notification settings:', error);
          // Even if we have an error, we should stop loading state
          setLoading(false);
        }
      };
      
      initialize();
    } else {
      // If no user, stop loading
      setLoading(false);
    }
  }, [user]);
  
  const checkNotificationPermissions = async () => {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      setHasPermission(status === 'granted');
    } catch (error) {
      console.error('Error checking notification permissions:', error);
      setHasPermission(false);
    }
  };
  
  const requestNotificationPermissions = async () => {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      setHasPermission(status === 'granted');
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  };
  
  const loadNotificationPreferences = async () => {
    if (!user) {
      setLoading(false);
      return;
    }
    
    setLoading(true);
    try {
      // Get notification preferences from Firestore
      const docRef = doc(db, 'notification_preferences', user.id);
      
      try {
        const docSnap = await getDoc(docRef);
        
        if (docSnap.exists()) {
          setNotificationPrefs(docSnap.data() as NotificationPreferences);
        } else {
          // Create default preferences for new users
          const defaultPrefs = {
            ...notificationPrefs,
            user_id: user.id,
          };
          
          try {
            await setDoc(docRef, defaultPrefs);
            console.log('Created default notification preferences');
          } catch (insertError) {
            console.error('Error creating default notification preferences:', insertError);
            
            // If we failed to write to Firestore (likely due to permissions),
            // we'll just use the local default preferences
            console.log('Using local default preferences due to Firestore error');
          }
        }
      } catch (readError) {
        console.error('Error reading notification preferences:', readError);
        
        // If we can't read from Firestore (permissions issue), 
        // just continue using the default local state
        console.log('Using default preferences due to Firestore read error');
      }
    } catch (error) {
      console.error('Error in loadNotificationPreferences:', error);
      // Don't rethrow - just continue with default preferences
    } finally {
      setLoading(false);
    }
  };
  
  const handleTogglePushNotifications = async (value: boolean) => {
    if (value && !hasPermission) {
      // Need to request permissions first
      const granted = await requestNotificationPermissions();
      if (!granted) {
        Alert.alert(
          'Permission Required',
          'Push notifications require permission. Please enable notifications for this app in your device settings.',
          [{ text: 'OK' }]
        );
        return;
      }
    }
    
    handleUpdatePreference('push_enabled', value);
  };
  
  const handleToggleNotificationType = (type: NotificationType) => {
    const updatedTypePrefs = {
      ...notificationPrefs.type_preferences,
      [type]: !notificationPrefs.type_preferences[type]
    };
    
    handleUpdatePreference('type_preferences', updatedTypePrefs);
  };
  
  const handleUpdatePreference = async (key: keyof NotificationPreferences, value: any) => {
    if (!user) return;
    
    // Update local state optimistically
    setNotificationPrefs(prev => ({
      ...prev,
      [key]: value,
      updated_at: new Date().toISOString()
    }));
    
    try {
      // Try to update the preference in Firestore
      const docRef = doc(db, 'notification_preferences', user.id);
      
      try {
        await updateDoc(docRef, {
          [key]: value,
          updated_at: new Date().toISOString()
        });
        console.log(`Successfully updated ${key} preference`);
      } catch (updateError: any) {
        // Check if it's a permissions error or document doesn't exist
        if (updateError.code === 'permission-denied') {
          console.error(`Permission denied for updating ${key} preference:`, updateError);
          // Don't revert the UI - the change was intentional from the user
          // Just show a discreet message or continue silently
        } else if (updateError.code === 'not-found') {
          // Document doesn't exist, try to create it
          try {
            await setDoc(docRef, {
              ...notificationPrefs,
              [key]: value,
              updated_at: new Date().toISOString()
            });
            console.log(`Created document with ${key} preference`);
          } catch (createError) {
            console.error(`Error creating document with ${key} preference:`, createError);
            // Still don't revert the UI
          }
        } else {
          // Other errors - log but don't revert UI
          console.error(`Error in handleUpdatePreference for ${key}:`, updateError);
        }
      }
    } catch (error) {
      console.error(`Top-level error in handleUpdatePreference for ${key}:`, error);
      // We're intentionally not reverting the UI or reloading preferences
      // This keeps the user experience smooth even if backend operations fail
    }
  };
  
  const showTimePicker = (mode: 'start' | 'end') => {
    setTimePickerMode(mode);
    setTimePickerVisible(true);
  };
  
  const hideTimePicker = () => {
    setTimePickerVisible(false);
  };
  
  const handleTimeConfirm = (date: Date | string) => {
    // For web platform, we might get a string from the input
    let timeString;
    if (typeof date === 'string') {
      // Parse HH:MM format from input
      timeString = date;
    } else {
      // For native platforms, format the Date object
      timeString = format(date, 'HH:mm');
    }
    
    const key = timePickerMode === 'start' ? 'quiet_hours_start' : 'quiet_hours_end';
    handleUpdatePreference(key, timeString);
    hideTimePicker();
  };
  
  const handleSaveSettings = async () => {
    if (!user) return;
    
    setSaving(true);
    try {
      const docRef = doc(db, 'notification_preferences', user.id);
      
      try {
        await setDoc(docRef, {
          ...notificationPrefs,
          updated_at: new Date().toISOString()
        }, { merge: true });
        
        Alert.alert('Success', 'Notification preferences saved successfully.');
      } catch (error: any) {
        console.error('Error in Firebase setDoc:', error);
        
        if (error.code === 'permission-denied') {
          Alert.alert(
            'Permission Error', 
            'You don\'t have permission to save these settings to the cloud. Your settings are saved locally instead.',
            [{ text: 'OK' }]
          );
        } else {
          Alert.alert(
            'Warning', 
            'Your preferences have been saved locally but could not be synced to the cloud.',
            [{ text: 'OK' }]
          );
        }
      }
    } catch (error) {
      console.error('Error in handleSaveSettings:', error);
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setSaving(false);
    }
  };
  
  // Format time string (HH:MM) to display format
  const formatTimeString = (timeString: string) => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0);
    return format(date, 'h:mm a');
  };
  
  // List of notification types with display info
  const notificationTypes = [
    { 
      id: 'system' as NotificationType, 
      label: 'System Notifications', 
      description: 'Updates about the app and your account',
      icon: AlertCircle,
      gradient: ['#6B7280', '#4B5563'] as [string, string], // gray
      examples: ['New features available', 'Account updates', 'Security alerts']
    },
    { 
      id: 'activity' as NotificationType, 
      label: 'Activity Updates', 
      description: 'Information about your workouts and activities',
      icon: Zap,
      gradient: ['#3B82F6', '#2563EB'] as [string, string], // blue
      examples: ['Daily activity summaries', 'Workout completed', 'New activity records']
    },
    { 
      id: 'goal' as NotificationType, 
      label: 'Goal Progress', 
      description: 'Updates on your health and fitness goals',
      icon: Star,
      gradient: ['#F59E0B', '#D97706'] as [string, string], // amber
      examples: ['Daily goal achieved', 'Weekly target progress', 'Milestone reached']
    },
    { 
      id: 'reminder' as NotificationType, 
      label: 'Reminders', 
      description: 'Scheduled workout and meal reminders',
      icon: Clock,
      gradient: ['#10B981', '#059669'] as [string, string], // green
      examples: ['Time to workout', 'Meal tracking reminder', 'Hydration reminder']
    },
    { 
      id: 'achievement' as NotificationType, 
      label: 'Achievements', 
      description: 'Badges, records, and milestones',
      icon: Trophy,
      gradient: ['#8B5CF6', '#6D28D9'] as [string, string], // violet
      examples: ['New badge earned', 'Achievement unlocked', 'Streak milestone']
    },
    { 
      id: 'social' as NotificationType, 
      label: 'Social Activity', 
      description: 'Friend requests and social interactions',
      icon: Users,
      gradient: ['#EC4899', '#DB2777'] as [string, string], // pink
      examples: ['Friend request', 'Comment on your activity', 'Challenge invitation']
    }
  ];

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <>
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.card }]}>
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={() => router.back()}
            accessibilityLabel="Go back"
            accessibilityHint="Returns to the previous screen"
          >
            <Feather name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Notification Settings</Text>
          {saving ? (
            <ActivityIndicator size="small" color={colors.primary} />
          ) : (
            <TouchableOpacity 
              style={[styles.saveButton, { backgroundColor: `${colors.primary}20` }]}
              onPress={handleSaveSettings}
            >
              <Text style={[styles.saveButtonText, { color: colors.primary }]}>Save</Text>
            </TouchableOpacity>
          )}
        </View>
        
          <ScrollView 
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            {/* Notification Channels */}
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Notification Channels</Text>
              
              <View style={[styles.settingsCard, { 
                backgroundColor: colors.card,
                borderColor: colors.border,
                shadowColor: colors.shadow
              }]}>
                <View style={[styles.settingRow, { borderBottomColor: colors.border }]}>
                  <View style={styles.settingInfo}>
                    <View style={styles.settingIconContainer}>
                      <LinearGradient
                        colors={['#3B82F6', '#2563EB'] as const}
                        style={styles.settingIcon}
                      >
                        <Feather name="bell" size={16}  color={colors.text} />
                      </LinearGradient>
                    </View>
                    <View>
                      <Text style={[styles.settingTitle, { color: colors.text }]}>Push Notifications</Text>
                      <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                        Receive notifications on your device
                      </Text>
                    </View>
                  </View>
                  <Switch
                  value={notificationPrefs.push_enabled}
                  onValueChange={handleTogglePushNotifications}
                    trackColor={{ false: isDark ? '#3f3f46' : '#e4e4e7', true: `${colors.primary}70` }}
                  thumbColor={notificationPrefs.push_enabled ? colors.primary : isDark ? '#71717a' : '#a1a1aa'}
                  />
                </View>
                
              <View style={[styles.settingRow, { borderBottomColor: colors.border }]}>
                  <View style={styles.settingInfo}>
                    <View style={styles.settingIconContainer}>
                      <LinearGradient
                        colors={['#10B981', '#059669'] as const}
                        style={styles.settingIcon}
                      >
                        <Feather name="message-square" size={16}  color={colors.text} />
                      </LinearGradient>
                    </View>
                    <View>
                      <Text style={[styles.settingTitle, { color: colors.text }]}>Email Notifications</Text>
                      <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                        Receive notifications via email
                      </Text>
                    </View>
                  </View>
                  <Switch
                  value={notificationPrefs.email_enabled}
                  onValueChange={(value) => handleUpdatePreference('email_enabled', value)}
                    trackColor={{ false: isDark ? '#3f3f46' : '#e4e4e7', true: `${colors.primary}70` }}
                  thumbColor={notificationPrefs.email_enabled ? colors.primary : isDark ? '#71717a' : '#a1a1aa'}
                  />
              </View>
              
              <View style={styles.settingRow}>
                <View style={styles.settingInfo}>
                  <View style={styles.settingIconContainer}>
                    <LinearGradient
                      colors={['#8B5CF6', '#7C3AED'] as const}
                      style={styles.settingIcon}
                    >
                      <Feather name="bell" size={16}  color={colors.text} />
                    </LinearGradient>
                  </View>
                  <View>
                    <Text style={[styles.settingTitle, { color: colors.text }]}>In-App Notifications</Text>
                    <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                      Show notifications within the app
                    </Text>
                  </View>
                </View>
                <Switch
                  value={notificationPrefs.in_app_enabled}
                  onValueChange={(value) => handleUpdatePreference('in_app_enabled', value)}
                  trackColor={{ false: isDark ? '#3f3f46' : '#e4e4e7', true: `${colors.primary}70` }}
                  thumbColor={notificationPrefs.in_app_enabled ? colors.primary : isDark ? '#71717a' : '#a1a1aa'}
                />
              </View>
            </View>
          </View>
            
          {/* Quiet Hours */}
            <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Quiet Hours</Text>
              
              <View style={[styles.settingsCard, { 
                backgroundColor: colors.card,
                borderColor: colors.border,
                shadowColor: colors.shadow
              }]}>
              <View style={[styles.settingRow, { borderBottomColor: colors.border }]}>
                <View style={styles.settingInfo}>
                  <View style={styles.settingIconContainer}>
                    <LinearGradient
                      colors={['#6B7280', '#4B5563'] as const}
                      style={styles.settingIcon}
                    >
                      <Feather name="moon" size={16}  color={colors.text} />
                    </LinearGradient>
                  </View>
                  <View>
                    <Text style={[styles.settingTitle, { color: colors.text }]}>Do Not Disturb</Text>
                    <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                      Mute notifications during specified hours
                    </Text>
                  </View>
                </View>
                <Switch
                  value={notificationPrefs.quiet_hours_enabled}
                  onValueChange={(value) => handleUpdatePreference('quiet_hours_enabled', value)}
                  trackColor={{ false: isDark ? '#3f3f46' : '#e4e4e7', true: `${colors.primary}70` }}
                  thumbColor={notificationPrefs.quiet_hours_enabled ? colors.primary : isDark ? '#71717a' : '#a1a1aa'}
                />
              </View>
              
              {notificationPrefs.quiet_hours_enabled && (
                <>
                  <View style={[styles.settingRow, { borderBottomColor: colors.border }]}>
                    <View style={styles.settingInfo}>
                      <View style={[styles.timeIcon, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
                        <Feather name="clock" size={16} color={colors.text} />
                      </View>
                      <View>
                        <Text style={[styles.settingTitle, { color: colors.text }]}>Start Time</Text>
                        <Text style={[styles.timeText, { color: colors.primary }]}>
                          {formatTimeString(notificationPrefs.quiet_hours_start)}
                        </Text>
                      </View>
                    </View>
                    <TouchableOpacity 
                      style={[styles.timeButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
                      onPress={() => showTimePicker('start')}
                    >
                      <Text style={[styles.timeButtonText, { color: colors.primary }]}>Change</Text>
                    </TouchableOpacity>
                  </View>
                  
                  <View style={styles.settingRow}>
                    <View style={styles.settingInfo}>
                      <View style={[styles.timeIcon, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
                        <Feather name="clock" size={16} color={colors.text} />
                      </View>
                      <View>
                        <Text style={[styles.settingTitle, { color: colors.text }]}>End Time</Text>
                        <Text style={[styles.timeText, { color: colors.primary }]}>
                          {formatTimeString(notificationPrefs.quiet_hours_end)}
                        </Text>
                      </View>
                    </View>
                    <TouchableOpacity 
                      style={[styles.timeButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
                      onPress={() => showTimePicker('end')}
                    >
                      <Text style={[styles.timeButtonText, { color: colors.primary }]}>Change</Text>
                    </TouchableOpacity>
                  </View>
                </>
              )}
            </View>
          </View>
          
          {/* Additional Settings */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Additional Settings</Text>
            
            <View style={[styles.settingsCard, { 
              backgroundColor: colors.card,
              borderColor: colors.border,
              shadowColor: colors.shadow
            }]}>
              <View style={[styles.settingRow, { borderBottomColor: colors.border }]}>
                      <View style={styles.settingInfo}>
                        <View style={styles.settingIconContainer}>
                          <LinearGradient
                      colors={['#F59E0B', '#D97706'] as const}
                            style={styles.settingIcon}
                          >
                      <Feather name="zap" size={16}  color={colors.text} />
                          </LinearGradient>
                        </View>
                        <View>
                    <Text style={[styles.settingTitle, { color: colors.text }]}>Custom Notification Sounds</Text>
                          <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                      Use custom sounds for different notification types
                          </Text>
                        </View>
                      </View>
                      <Switch
                  value={notificationPrefs.custom_sounds_enabled}
                  onValueChange={(value) => handleUpdatePreference('custom_sounds_enabled', value)}
                        trackColor={{ false: isDark ? '#3f3f46' : '#e4e4e7', true: `${colors.primary}70` }}
                  thumbColor={notificationPrefs.custom_sounds_enabled ? colors.primary : isDark ? '#71717a' : '#a1a1aa'}
                      />
                    </View>
              
              <View style={styles.settingRow}>
                <View style={styles.settingInfo}>
                  <View style={styles.settingIconContainer}>
                    <LinearGradient
                      colors={['#EC4899', '#DB2777'] as const}
                      style={styles.settingIcon}
                    >
                      <Feather name="calendar" size={16}  color={colors.text} />
                    </LinearGradient>
                  </View>
                  <View>
                    <Text style={[styles.settingTitle, { color: colors.text }]}>Weekly Digest</Text>
                    <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                      Receive a weekly summary of your activity and progress
                    </Text>
                  </View>
                </View>
                <Switch
                  value={notificationPrefs.weekly_digest_enabled}
                  onValueChange={(value) => handleUpdatePreference('weekly_digest_enabled', value)}
                  trackColor={{ false: isDark ? '#3f3f46' : '#e4e4e7', true: `${colors.primary}70` }}
                  thumbColor={notificationPrefs.weekly_digest_enabled ? colors.primary : isDark ? '#71717a' : '#a1a1aa'}
                />
              </View>
            </View>
          </View>
          
          {/* Notification Types */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Notification Types</Text>
            
            {notificationTypes.map((type, index) => (
              <View 
                key={type.id}
                style={[
                  styles.notificationTypeCard, 
                  { 
                    backgroundColor: colors.card,
                    borderColor: colors.border,
                    shadowColor: colors.shadow,
                    marginBottom: index < notificationTypes.length - 1 ? 16 : 0
                  }
                ]}
              >
                <View style={styles.notificationTypeHeader}>
                  <View style={styles.notificationTypeHeaderLeft}>
                    <LinearGradient
                      colors={type.gradient}
                      style={styles.notificationTypeIcon}
                    >
                      <type.icon size={18} color="white" />
                    </LinearGradient>
                    <Text style={[styles.notificationTypeTitle, { color: colors.text }]}>
                      {type.label}
                    </Text>
                  </View>
                  
                  <Switch
                    value={notificationPrefs.type_preferences[type.id]}
                    onValueChange={() => handleToggleNotificationType(type.id)}
                    trackColor={{ false: isDark ? '#3f3f46' : '#e4e4e7', true: `${colors.primary}70` }}
                    thumbColor={notificationPrefs.type_preferences[type.id] ? colors.primary : isDark ? '#71717a' : '#a1a1aa'}
                  />
                </View>
                
                <Text style={[styles.notificationTypeDescription, { color: colors.textSecondary }]}>
                  {type.description}
                </Text>
                
                {/* Examples section */}
                <View style={[styles.examplesContainer, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)' }]}>
                  <Text style={[styles.examplesTitle, { color: colors.text }]}>Examples:</Text>
                  {type.examples.map((example, i) => (
                    <View key={i} style={styles.exampleItem}>
                      <View style={[styles.exampleBullet, { backgroundColor: type.gradient[0] }]} />
                      <Text style={[styles.exampleText, { color: colors.textSecondary }]}>
                        {example}
                      </Text>
                    </View>
                  ))}
                </View>
              </View>
            ))}
          </View>
          
          {/* Time Picker Modal or Web Input */}
          {Platform.OS !== 'web' ? (
            <DateTimePickerModal
              isVisible={isTimePickerVisible}
              mode="time"
              onConfirm={handleTimeConfirm}
              onCancel={hideTimePicker}
              date={new Date()}
            />
          ) : (
            // Web platform time input
            isTimePickerVisible && (
              <View style={styles.webTimePickerContainer}>
                <View style={[styles.webTimePicker, { backgroundColor: colors.card }]}>
                  <Text style={[styles.webTimePickerTitle, { color: colors.text }]}>
                    Select {timePickerMode === 'start' ? 'Start' : 'End'} Time
                  </Text>
                  <TextInput
                    style={[styles.webTimeInput, { color: colors.text, borderColor: colors.border }]}
                    placeholder="HH:MM (24-hour format)"
                    placeholderTextColor={colors.textSecondary}
                    defaultValue={timePickerMode === 'start' ? notificationPrefs.quiet_hours_start : notificationPrefs.quiet_hours_end}
                    onSubmitEditing={(e) => handleTimeConfirm(e.nativeEvent.text)}
                  />
                  <View style={styles.webTimePickerButtons}>
                    <TouchableOpacity 
                      style={[styles.webTimePickerButton, { backgroundColor: colors.primary }]}
                      onPress={() => handleTimeConfirm(
                        timePickerMode === 'start' 
                          ? notificationPrefs.quiet_hours_start 
                          : notificationPrefs.quiet_hours_end
                      )}
                    >
                      <Text style={{ color: 'white' }}>Confirm</Text>
                    </TouchableOpacity>
                    <TouchableOpacity 
                      style={[styles.webTimePickerButton, { backgroundColor: colors.border }]}
                      onPress={hideTimePicker}
                    >
                      <Text style={{ color: colors.text }}>Cancel</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            )
          )}
            
            {/* Info box */}
            <View style={[styles.infoBox, { 
              backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)',
              borderColor: `${colors.primary}30`
            }]}>
              <Feather name="info" size={18} color={colors.primary} style={{ marginRight: 10 }} />
              <Text style={[styles.infoText, { color: colors.textSecondary }]}>
              Changes to your notification settings will be applied immediately. 
              You can update these preferences at any time.
              </Text>
            </View>
          </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  saveButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContent: {
    paddingVertical: 16,
    paddingBottom: 40,
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: '700',
    marginBottom: 12,
  },
  settingsCard: {
    borderRadius: 16,
    borderWidth: 1,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 4 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      }
    }),
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    paddingRight: 16,
  },
  settingIconContainer: {
    marginRight: 12,
  },
  settingIcon: {
    width: 36,
    height: 36,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 13,
    lineHeight: 18,
  },
  timeIcon: {
    width: 36,
    height: 36,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  timeText: {
    fontSize: 15,
    fontWeight: '600',
  },
  timeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  timeButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  notificationTypeCard: {
    borderRadius: 16,
    borderWidth: 1,
    padding: 16,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 3 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      }
    }),
  },
  notificationTypeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  notificationTypeHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  notificationTypeIcon: {
    width: 36,
    height: 36,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  notificationTypeTitle: {
    fontSize: 16,
    fontWeight: '700',
  },
  notificationTypeDescription: {
    fontSize: 14,
    marginBottom: 12,
  },
  examplesContainer: {
    borderRadius: 12,
    padding: 12,
  },
  examplesTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  exampleItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  exampleBullet: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  exampleText: {
    fontSize: 13,
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginTop: 8,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  webTimePickerContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  webTimePicker: {
    width: '80%',
    maxWidth: 300,
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  webTimePickerTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 20,
  },
  webTimeInput: {
    width: '100%',
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 20,
  },
  webTimePickerButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  webTimePickerButton: {
    flex: 1,
    height: 45,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
  },
}); 