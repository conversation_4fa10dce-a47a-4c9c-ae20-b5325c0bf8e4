import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, ActivityIndicator, Image, Platform, Switch } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Feather , MaterialIcons , FontAwesome } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import { NotificationType, markNotificationsAsRead, markAllNotificationsAsRead, deleteNotifications } from '@/services/notificationService';
import { useNotifications } from '@/contexts/NotificationContext';
import { format } from 'date-fns';

export default function NotificationsScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const { notifications, loading, error, markAsRead, markAllAsRead, clearNotifications } = useNotifications();
  const [selectedFilter, setSelectedFilter] = useState<'info' | 'success' | 'warning' | 'error' | 'all'>('all');
  const [showUnreadOnly, setShowUnreadOnly] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Filter options
  const filterOptions = [
    { id: 'all', label: 'All', icon: Bell },
    { id: 'info', label: 'Info', icon: AlertCircle },
    { id: 'success', label: 'Success', icon: CheckCircle2 },
    { id: 'warning', label: 'Warning', icon: AlertTriangle },
    { id: 'error', label: 'Error', icon: AlertOctagon },
  ];
  
  // Filter notifications based on the selected filter and read status
  const filteredNotifications = notifications.filter(notification => {
    // Filter by type
    if (selectedFilter !== 'all' && notification.type !== selectedFilter) {
      return false;
    }
    
    // Filter by read status if showUnreadOnly is true
    if (showUnreadOnly && notification.read) {
      return false;
    }
    
    return true;
  });
  
  // Helper function to format date
  const formatDate = (timestamp: number) => {
    const now = new Date();
    const date = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      return diffInHours === 0 
        ? 'Just now' 
        : `${diffInHours} ${diffInHours === 1 ? 'hour' : 'hours'} ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return format(date, 'MMM d');
    }
  };
  
  // Helper function to get notification icon
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return CheckCircle2;
      case 'error':
        return AlertOctagon;
      case 'warning':
        return AlertTriangle;
      case 'info':
      default:
        return Info;
    }
  };
  
  // Helper function to get notification gradient colors
  const getNotificationGradient = (type: string): [string, string] => {
    switch (type) {
      case 'success':
        return ['#10B981', '#059669']; // green
      case 'error':
        return ['#EF4444', '#B91C1C']; // red
      case 'warning':
        return ['#F59E0B', '#D97706']; // amber
      case 'info':
      default:
        return ['#3B82F6', '#2563EB']; // blue
    }
  };
  
  // Handle marking a notification as read
  const handleMarkAsRead = async (id: string) => {
    try {
      await markAsRead(id);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };
  
  // Handle marking all notifications as read
  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };
  
  // Handle deleting a notification
  const handleDeleteNotification = async (id: string) => {
    try {
      await deleteNotifications([id]);
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };
  
  // Group notifications by date
  const groupedNotifications = filteredNotifications.reduce<Record<string, typeof notifications>>((acc, notification) => {
    const dateKey = formatDate(notification.timestamp);
    if (!acc[dateKey]) {
      acc[dateKey] = [];
    }
    acc[dateKey].push(notification);
    return acc;
  }, {});

  return (
    <>
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.card }]}>
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={() => router.back()}
            accessibilityLabel="Go back"
            accessibilityHint="Returns to the previous screen"
          >
            <Feather name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Notifications</Text>
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
            onPress={() => {
              router.push('/profile-screens/notification-settings');
            }}
          >
            <Feather name="settings" size={20} color={colors.primary} />
          </TouchableOpacity>
        </View>
        
        {/* Notification Controls */}
        <View style={[styles.controlsContainer, { borderBottomColor: colors.border }]}>
          {/* Filter Bar */}
          <ScrollView 
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.filterScrollContent}
          >
            {filterOptions.map((filter) => {
              const isActive = selectedFilter === filter.id;
              const FilterIcon = filter.icon;
              
              return (
                <TouchableOpacity
                  key={filter.id}
                  style={[
                    styles.filterButton,
                    isActive && styles.activeFilterButton,
                    { 
                      backgroundColor: isActive 
                        ? `${colors.primary}20` 
                        : isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
                      borderColor: isActive ? colors.primary : 'transparent'
                    }
                  ]}
                  onPress={() => setSelectedFilter(filter.id as any)}
                >
                  <FilterIcon size={14} color={isActive ? colors.primary : colors.textSecondary} style={{ marginRight: 6 }} />
                  <Text style={[
                    styles.filterButtonText,
                    { color: isActive ? colors.primary : colors.textSecondary }
                  ]}>
                    {filter.label}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </ScrollView>
          
          {/* Action Bar */}
          <View style={styles.actionBar}>
            <View style={styles.toggleContainer}>
              <Text style={[styles.toggleLabel, { color: colors.textSecondary }]}>Unread Only</Text>
              <Switch
                value={showUnreadOnly}
                onValueChange={setShowUnreadOnly}
                trackColor={{ false: isDark ? '#3f3f46' : '#e4e4e7', true: `${colors.primary}70` }}
                thumbColor={showUnreadOnly ? colors.primary : isDark ? '#71717a' : '#a1a1aa'}
              />
            </View>
            
            <TouchableOpacity 
              style={[styles.actionBarButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)' }]}
              onPress={handleMarkAllAsRead}
              disabled={loading}
            >
              <CheckCheck size={14} color={colors.textSecondary} style={{ marginRight: 6 }} />
              <Text style={[styles.actionBarButtonText, { color: colors.textSecondary }]}>Mark All Read</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <AlertOctagon size={60} color={colors.error} style={{ marginBottom: 16 }} />
            <Text style={[styles.errorTitle, { color: colors.text }]}>Error Loading Notifications</Text>
            <Text style={[styles.errorSubtitle, { color: colors.textSecondary }]}>
              Something went wrong while loading your notifications
            </Text>
            <TouchableOpacity 
              style={[styles.retryButton, { borderColor: colors.primary }]}
              onPress={() => {
                // Refresh the page
                setIsRefreshing(true);
                setTimeout(() => setIsRefreshing(false), 1000);
              }}
            >
              <Feather name="refresh-cw" size={16} color={colors.primary} style={{ marginRight: 8 }} />
              <Text style={[styles.retryButtonText, { color: colors.primary }]}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : filteredNotifications.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Feather name="bell" size={60} color={colors.textSecondary} style={{ marginBottom: 16 }} />
            <Text style={[styles.emptyTitle, { color: colors.text }]}>No Notifications</Text>
            <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
              {showUnreadOnly 
                ? "You've read all your notifications" 
                : "You don't have any notifications yet"}
            </Text>
          </View>
        ) : (
          <ScrollView 
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            {Object.entries(groupedNotifications).map(([dateKey, dateNotifications]) => (
              <View key={dateKey} style={styles.notificationGroup}>
                <Text style={[styles.dateLabel, { color: colors.textSecondary }]}>{dateKey}</Text>
                
                {dateNotifications.map((notification) => {
                  const NotificationIcon = getNotificationIcon(notification.type);
                  const gradientColors = getNotificationGradient(notification.type);
                  
                  return (
                    <View 
                      key={notification.id} 
                      style={[
                        styles.notificationCard,
                        { 
                          backgroundColor: colors.card,
                          borderColor: colors.border,
                          shadowColor: colors.shadow,
                          borderLeftColor: notification.read ? colors.border : gradientColors[0],
                          borderLeftWidth: notification.read ? 1 : 3
                        }
                      ]}
                    >
                      <View style={styles.notificationContent}>
                        <LinearGradient
                          colors={gradientColors as readonly [string, string]}
                          style={styles.notificationIcon}
                        >
                          <NotificationIcon size={16} color="white" />
                        </LinearGradient>
                        
                        <View style={styles.notificationTextContent}>
                          <Text style={[
                            styles.notificationTitle, 
                            { 
                              color: colors.text,
                              fontWeight: notification.read ? '600' : '700'
                            }
                          ]}>
                            {notification.title}
                          </Text>
                          <Text style={[styles.notificationMessage, { color: colors.textSecondary }]}>
                            {notification.message}
                          </Text>
                          <Text style={[styles.notificationTime, { color: colors.textSecondary }]}>
                            {formatDate(notification.timestamp)}
                          </Text>
                        </View>
                      </View>
                      
                      <View style={styles.notificationActions}>
                        {!notification.read && (
                          <TouchableOpacity 
                            style={[styles.notificationActionButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)' }]}
                            onPress={() => handleMarkAsRead(notification.id)}
                          >
                            <MaterialIcons name="check-circle" size={14} color={colors.primary} />
                          </TouchableOpacity>
                        )}
                        <TouchableOpacity
                          style={[styles.notificationActionButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)' }]}
                          onPress={() => handleDeleteNotification(notification.id)}
                        >
                          <Feather name="trash-2" size={14} color={colors.textSecondary} />
                        </TouchableOpacity>
                      </View>
                    </View>
                  );
                })}
              </View>
            ))}
          </ScrollView>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  controlsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
  },
  filterScrollContent: {
    paddingVertical: 4,
    paddingRight: 20,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginRight: 10,
    borderWidth: 1,
  },
  activeFilterButton: {
    borderWidth: 1,
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  actionBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
  },
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  toggleLabel: {
    fontSize: 13,
    fontWeight: '500',
    marginRight: 8,
  },
  actionBarButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  actionBarButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 8,
  },
  errorSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    fontWeight: '500',
    marginBottom: 20,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    fontWeight: '500',
  },
  scrollContent: {
    paddingVertical: 16,
  },
  notificationGroup: {
    marginBottom: 20,
  },
  dateLabel: {
    fontSize: 14,
    fontWeight: '700',
    marginLeft: 20,
    marginBottom: 12,
  },
  notificationCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 12,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: { elevation: 2 },
      web: {
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  notificationContent: {
    flexDirection: 'row',
    flex: 1,
  },
  notificationIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  notificationTextContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 15,
    marginBottom: 4,
  },
  notificationMessage: {
    fontSize: 13,
    lineHeight: 18,
    marginBottom: 4,
  },
  notificationTime: {
    fontSize: 11,
    marginTop: 2,
  },
  notificationActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  notificationActionButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
}); 