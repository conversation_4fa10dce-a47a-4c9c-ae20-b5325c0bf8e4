import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import { Stack } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { OneHandedForm } from '@/components/OneHandedForm';
import { OneHandedModeProvider, useOneHandedMode } from '@/components/OneHandedModeProvider';
import { getAuth } from 'firebase/auth';
import { getFirestore, doc, getDoc, setDoc, serverTimestamp } from 'firebase/firestore';

// Import the FormField type from OneHandedForm component
interface FormField {
  id: string;
  label: string;
  placeholder?: string;
  type?: 'text' | 'number' | 'email' | 'password';
  required?: boolean;
  value: string;
}

// Define the profile data interface
interface ProfileData {
  id?: string;
  user_id?: string;
  name: string;
  email: string;
  age: string;
  weight: string;
  height: string;
  updated_at?: string;
}

/**
 * Personal Information Screen
 * 
 * Allows users to update their personal information using a form
 * that's optimized for one-handed use when one-handed mode is enabled.
 */
export default function PersonalInfoScreen() {
  const { colors } = useTheme();
  const { isOneHandedModeEnabled } = useOneHandedMode();
  const [loading, setLoading] = useState(true);
  const [profileData, setProfileData] = useState<ProfileData>({
    name: '',
    email: '',
    age: '',
    weight: '',
    height: '',
  });

  // Fetch user profile data on component mount
  useEffect(() => {
    async function fetchProfileData() {
      try {
        setLoading(true);
        
        // Get the current user from Firebase Auth
        const auth = getAuth();
        const user = auth.currentUser;
        
        if (!user) {
          console.log('No user is logged in');
          setLoading(false);
          return;
        }

        try {
          // Fetch profile data from Firestore
          const db = getFirestore();
          const profileRef = doc(db, 'profiles', user.uid);
          const profileSnap = await getDoc(profileRef);
          
          if (profileSnap.exists()) {
            // Convert numeric values to strings for the form
            const data = profileSnap.data();
            setProfileData({
              name: data.name || '',
              email: data.email || user.email || '',
              age: data.age?.toString() || '',
              weight: data.weight?.toString() || '',
              height: data.height?.toString() || '',
            });
          } else {
            // If no profile exists yet, initialize with default values
            setProfileData({
              name: user.displayName || '',
              email: user.email || '',
              age: '',
              weight: '',
              height: '',
            });
          }
        } catch (err) {
          console.log('Error fetching profile, might be a new user:', err);
          // Initialize with default values
          setProfileData({
            name: user.displayName || '',
            email: user.email || '',
            age: '',
            weight: '',
            height: '',
          });
        }
      } catch (error) {
        console.error('Unexpected error fetching profile:', error);
        Alert.alert('Error', 'An unexpected error occurred while loading your profile.');
      } finally {
        setLoading(false);
      }
    }
    
    fetchProfileData();
  }, []);
  
  // Define form fields with current values from profileData
  const personalInfoFields: FormField[] = [
    {
      id: 'name',
      label: 'Full Name',
      placeholder: 'Enter your name',
      type: 'text',
      required: true,
      value: profileData.name,
    },
    {
      id: 'email',
      label: 'Email Address',
      placeholder: '<EMAIL>',
      type: 'email',
      required: true,
      value: profileData.email,
    },
    {
      id: 'age',
      label: 'Age',
      placeholder: 'Enter your age',
      type: 'number',
      required: false,
      value: profileData.age,
    },
    {
      id: 'weight',
      label: 'Weight (kg)',
      placeholder: 'Enter your weight in kg',
      type: 'number',
      required: false,
      value: profileData.weight,
    },
    {
      id: 'height',
      label: 'Height (cm)',
      placeholder: 'Enter your height in cm',
      type: 'number',
      required: false,
      value: profileData.height,
    },
  ];
  
  // Handle form submission
  const handleSubmit = async (values: Record<string, string>) => {
    try {
      setLoading(true);
      
      // Get the current user from Firebase Auth
      const auth = getAuth();
      const user = auth.currentUser;
      
      if (!user) {
        Alert.alert('Error', 'You must be logged in to update your profile.');
        return;
      }
      
      // Convert string values to appropriate types
      const updatedProfile = {
        user_id: user.uid,
        name: values.name,
        email: values.email,
        age: values.age ? parseInt(values.age, 10) : null,
        weight: values.weight ? parseFloat(values.weight) : null,
        height: values.height ? parseFloat(values.height) : null,
        updated_at: serverTimestamp()
      };
      
      // Update the profile in Firestore
      try {
        const db = getFirestore();
        const profileRef = doc(db, 'profiles', user.uid);
        await setDoc(profileRef, updatedProfile, { merge: true });
        
        // Update local state with the new values
        const updatedProfileData: ProfileData = {
          name: values.name || '',
          email: values.email || '',
          age: values.age || '',
          weight: values.weight || '',
          height: values.height || '',
        };
        setProfileData(updatedProfileData);
        
        Alert.alert('Success', 'Your profile information has been updated!');
      } catch (error) {
        console.error('Error updating profile:', error);
        Alert.alert('Error', 'Failed to update your profile information.');
      }
    } catch (error) {
      console.error('Unexpected error updating profile:', error);
      Alert.alert('Error', 'An unexpected error occurred while updating your profile.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle form cancellation
  const handleCancel = () => {
    console.log('Form cancelled');
    // No need to navigate, just reset form values to original
  };
  
  return (
    <>
      <Stack.Screen
        options={{
          title: 'Personal Information',
          headerTitleStyle: { color: colors.text },
          headerStyle: { backgroundColor: colors.card },
          headerTintColor: colors.primary,
        }}
      />
      
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {isOneHandedModeEnabled ? (
          <Text style={[styles.modeIndicator, { color: colors.primary }]}>
            One-handed mode active
          </Text>
        ) : null}
        
        {loading ? (
          <Text style={[styles.loadingText, { color: colors.text }]}>
            Loading your profile information...
          </Text>
        ) : (
          <OneHandedForm
            title="Your Personal Information"
            fields={personalInfoFields}
            submitText="Save Information"
            cancelText="Cancel"
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            initialValues={{
              name: profileData.name,
              email: profileData.email,
              age: profileData.age,
              weight: profileData.weight,
              height: profileData.height,
            }}
          />
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  modeIndicator: {
    textAlign: 'center',
    paddingVertical: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  loadingText: {
    textAlign: 'center',
    paddingVertical: 20,
    fontSize: 16,
  },
}); 