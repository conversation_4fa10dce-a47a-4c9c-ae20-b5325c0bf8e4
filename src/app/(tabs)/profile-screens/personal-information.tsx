import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, TextInput, ActivityIndicator, Platform, Image, Alert } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Feather , MaterialIcons , FontAwesome } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { doc, getDoc, updateDoc, collection, query, where, getDocs, Firestore, getFirestore } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, FirebaseStorage, getStorage } from 'firebase/storage';
import { app } from '@/lib/firebase';
import { LinearGradient } from 'expo-linear-gradient';
import * as ImagePicker from 'expo-image-picker';
import { z } from 'zod';
import { ProfileImageUploader } from '@/components/ProfileComponents/ProfileImageUploader';
import { format } from 'date-fns';

// Initialize Firestore and Storage
const db = getFirestore(app);
const storage = getStorage(app);

// Mock DateTimePickerModal component since we're missing the package
interface DateTimePickerModalProps {
  isVisible: boolean;
  mode: 'date' | 'time' | 'datetime';
  onConfirm: (date: Date) => void;
  onCancel: () => void;
  maximumDate?: Date;
  date: Date;
  accentColor?: string;
}

const DateTimePickerModal: React.FC<DateTimePickerModalProps> = ({ 
  isVisible, 
  mode, 
  onConfirm, 
  onCancel, 
  maximumDate, 
  date, 
  accentColor 
}) => {
  if (!isVisible) return null;
  
  // Simple mock implementation - in a real app this would show a proper date picker
  return (
    <View style={styles.mockDatePicker}>
      <Text>Date Picker (Mock Implementation)</Text>
      <View style={styles.mockDatePickerButtons}>
        <TouchableOpacity onPress={() => onCancel()} style={styles.mockCancelButton}>
          <Text>Cancel</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={() => onConfirm(date)} style={[styles.mockConfirmButton, accentColor ? { backgroundColor: accentColor } : {}]}>
          <Text style={{ color: 'white' }}>Confirm</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

// Define schema for profile validation
const profileSchema = z.object({
  full_name: z.string().min(2, "Name must be at least 2 characters"),
  username: z.string().min(3, "Username must be at least 3 characters").regex(/^[a-zA-Z0-9_]+$/, "Username can only contain letters, numbers and underscores"),
  email: z.string().email("Please enter a valid email"),
  birth_date: z.date().optional(),
  height: z.number().positive().optional(),
  weight: z.number().positive().optional(),
  bio: z.string().max(160, "Bio must be 160 characters or less").optional(),
});

type ProfileData = {
  id: string;
  full_name: string;
  username: string;
  email: string;
  avatar_url: string | null;
  birth_date: string | null;
  height: number | null;
  weight: number | null;
  bio: string | null;
  created_at: string;
  updated_at: string;
  completeness: number;
};

export default function PersonalInformationScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const { user } = useAuth();
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [isDatePickerVisible, setDatePickerVisible] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  
  // Store temporary values for editable fields
  const [tempValues, setTempValues] = useState<{
    full_name: string;
    username: string;
    email: string;
    birth_date: Date | null;
    height: number | null;
    weight: number | null;
    bio: string;
  }>({
    full_name: '',
    username: '',
    email: '',
    birth_date: null,
    height: null,
    weight: null,
    bio: ''
  });
  
  useEffect(() => {
    if (user) {
      const initialize = async () => {
        try {
          await fetchProfile();
        } catch (error) {
          console.error('Error initializing profile:', error);
          // Even if we have an error, we should stop loading state
          setLoading(false);
        }
      };
      
      initialize();
    } else {
      // If no user, stop loading
      setLoading(false);
    }
  }, [user]);
  
  const fetchProfile = async () => {
    if (!user) {
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      
      // Fetch profile data from Firebase
      const profileRef = doc(db, 'profiles', user.id);
      const profileSnap = await getDoc(profileRef);
      
      if (!profileSnap.exists()) {
        console.error('Profile not found');
        return;
      }
      
      const data = profileSnap.data();
      
      if (data) {
        // Calculate profile completeness
        const fields = ['full_name', 'username', 'avatar_url', 'birth_date', 'height', 'weight', 'bio'];
        const filledFields = fields.filter(field => data[field] !== null && data[field] !== '').length;
        const completeness = Math.round((filledFields / fields.length) * 100);
        
        setProfile({
          id: profileSnap.id,
          full_name: data.full_name || '',
          username: data.username || '',
          email: data.email || '',
          avatar_url: data.avatar_url || null,
          birth_date: data.birth_date || null,
          height: data.height || null,
          weight: data.weight || null,
          bio: data.bio || null,
          created_at: data.created_at || '',
          updated_at: data.updated_at || '',
          completeness
        });
        
        // Initialize temp values
        setTempValues({
          full_name: data.full_name || '',
          username: data.username || '',
          email: data.email || '',
          birth_date: data.birth_date ? new Date(data.birth_date) : null,
          height: data.height || null,
          weight: data.weight || null,
          bio: data.bio || ''
        });
      }
    } catch (error) {
      console.error('Error in fetchProfile:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleStartEditing = (field: string) => {
    setEditingField(field);
    setValidationErrors({});
  };
  
  const handleCancelEditing = () => {
    setEditingField(null);
    setValidationErrors({});
  };
  
  const validateField = (field: string, value: any) => {
    try {
      const schema = profileSchema.shape[field as keyof typeof profileSchema.shape];
      schema.parse(value);
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldError = error.errors[0]?.message || 'Invalid input';
        setValidationErrors(prev => ({ ...prev, [field]: fieldError }));
      }
      return false;
    }
  };
  
  const handleSaveField = async (field: string) => {
    if (!user) return;
    
    const value = tempValues[field];
    
    // Validate the field
    if (!validateField(field, value)) {
      return;
    }
    
    setSaving(true);
    
    try {
      // Format date for database if it's a date field
      const fieldValue = field === 'birth_date' && value 
        ? format(value as Date, 'yyyy-MM-dd')
        : value;
      
      // Update the field in Firebase
      const profileRef = doc(db, 'profiles', user.id);
      await updateDoc(profileRef, { 
        [field]: fieldValue, 
        updated_at: new Date().toISOString() 
      });
      
      // Update local state optimistically
      if (profile) {
        const updatedProfile = { 
          ...profile, 
          [field]: fieldValue 
        };
        
        // Recalculate completeness
        const fields = ['full_name', 'username', 'avatar_url', 'birth_date', 'height', 'weight', 'bio'];
        const filledFields = fields.filter(fieldName => {
          const value = fieldName === field ? fieldValue : updatedProfile[fieldName as keyof typeof updatedProfile];
          return value !== null && value !== '';
        }).length;
        
        const completeness = Math.round((filledFields / fields.length) * 100);
        setProfile({ ...updatedProfile, completeness });
      }
      
      setEditingField(null);
    } catch (error) {
      console.error('Error in handleSaveField:', error);
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setSaving(false);
    }
  };
  
  const handleAvatarChange = async (uri: string) => {
    if (!user || !uri) return;
    
    setSaving(true);
    
    try {
      // Create a random file path for the avatar
      const fileExt = uri.split('.').pop();
      const filePath = `avatars/${user.id}/${Date.now()}.${fileExt}`;
      
      // Fetch the image data from URI
      const response = await fetch(uri);
      const blob = await response.blob();
      
      // Upload image to Firebase Storage
      const storageRef = ref(storage, filePath);
      await uploadBytes(storageRef, blob);
      
      // Get the public URL
      const avatarUrl = await getDownloadURL(storageRef);
      
      // Update the profile with new avatar URL
      const profileRef = doc(db, 'profiles', user.id);
      await updateDoc(profileRef, {
        avatar_url: avatarUrl,
        updated_at: new Date().toISOString()
      });
      
      // Update local state
      if (profile) {
        const updatedProfile = { ...profile, avatar_url: avatarUrl };
        
        // Recalculate completeness
        const fields = ['full_name', 'username', 'avatar_url', 'birth_date', 'height', 'weight', 'bio'];
        const filledFields = fields.filter(field => updatedProfile[field as keyof typeof updatedProfile] !== null && updatedProfile[field as keyof typeof updatedProfile] !== '').length;
        const completeness = Math.round((filledFields / fields.length) * 100);
        
        setProfile({ ...updatedProfile, completeness });
      }
    } catch (error) {
      console.error('Error in handleAvatarChange:', error);
      Alert.alert('Error', 'Failed to update avatar. Please try again.');
    } finally {
      setSaving(false);
    }
  };
  
  const showDatePicker = () => {
    setDatePickerVisible(true);
  };

  const hideDatePicker = () => {
    setDatePickerVisible(false);
  };

  const handleDateConfirm = (date: Date) => {
    setTempValues(prev => ({ ...prev, birth_date: date }));
    hideDatePicker();
  };
  
  // Helper function to render different field editors based on field type
  const renderFieldEditor = (field: string) => {
    const value = tempValues[field];
    const error = validationErrors[field];
    
    switch (field) {
      case 'birth_date':
        return (
          <View style={styles.fieldEditorContainer}>
            <TouchableOpacity
              style={[styles.datePickerButton, { borderColor: colors.border }]}
              onPress={showDatePicker}
            >
              <Text style={[styles.datePickerText, { color: colors.text }]}>
                {value ? format(value as Date, 'MMMM d, yyyy') : 'Select your birth date'}
              </Text>
              <Feather name="calendar" size={18} color={colors.primary} />
            </TouchableOpacity>
            <DateTimePickerModal
              isVisible={isDatePickerVisible}
              mode="date"
              onConfirm={handleDateConfirm}
              onCancel={hideDatePicker}
              maximumDate={new Date()}
              date={value ? new Date(value) : new Date(2000, 0, 1)}
              accentColor={colors.primary}
            />
            {error && <Text style={styles.errorText}>{error}</Text>}
          </View>
        );
      
      case 'height':
      case 'weight':
        return (
          <View style={styles.fieldEditorContainer}>
            <TextInput
              style={[
                styles.input,
                { 
                  backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
                  color: colors.text,
                }
              ]}
              value={value !== null ? String(value) : ''}
              onChangeText={(text) => {
                const numValue = text === '' ? null : parseFloat(text);
                setTempValues(prev => ({ ...prev, [field]: numValue }));
              }}
              placeholder={field === 'height' ? 'Enter your height (cm)' : 'Enter your weight (kg)'}
              placeholderTextColor={colors.textSecondary}
              keyboardType="numeric"
            />
            {error && <Text style={styles.errorText}>{error}</Text>}
          </View>
        );
      
      case 'bio':
        return (
          <View style={styles.fieldEditorContainer}>
            <TextInput
              style={[
                styles.textArea,
                { 
                  backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
                  color: colors.text,
                }
              ]}
              value={value}
              onChangeText={(text) => setTempValues(prev => ({ ...prev, [field]: text }))}
              placeholder="Tell us about yourself"
              placeholderTextColor={colors.textSecondary}
              multiline
              numberOfLines={4}
              maxLength={160}
            />
            <Text style={[styles.charCount, { color: colors.textSecondary }]}>
              {value?.length || 0}/160
            </Text>
            {error && <Text style={styles.errorText}>{error}</Text>}
          </View>
        );
      
      default:
        return (
          <View style={styles.fieldEditorContainer}>
            <TextInput
              style={[
                styles.input,
                { 
                  backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
                  color: colors.text,
                }
              ]}
              value={value}
              onChangeText={(text) => setTempValues(prev => ({ ...prev, [field]: text }))}
              placeholder={`Enter your ${field.replace('_', ' ')}`}
              placeholderTextColor={colors.textSecondary}
              editable={field !== 'email'} // Email is not editable
            />
            {error && <Text style={styles.errorText}>{error}</Text>}
          </View>
        );
    }
  };
  
  // Render different field displays based on field type
  const renderField = (field: string, icon: React.ReactNode, title: string, subtitle: string) => {
    if (editingField === field) {
      return (
        <View style={[styles.fieldContainer, { borderBottomColor: colors.border }]}>
          <View style={styles.fieldHeader}>
            <View style={styles.fieldIcon}>{icon}</View>
            <View style={styles.fieldTitles}>
              <Text style={[styles.fieldTitle, { color: colors.text }]}>{title}</Text>
            </View>
          </View>
          
          {renderFieldEditor(field)}
          
          <View style={styles.editActions}>
            <TouchableOpacity 
              style={[styles.editActionButton, { backgroundColor: colors.error + '20' }]}
              onPress={handleCancelEditing}
            >
              <Feather name="x" size={18} color={colors.error} />
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.editActionButton, { backgroundColor: colors.primary + '20' }]}
              onPress={() => handleSaveField(field)}
              disabled={saving}
            >
              {saving ? (
                <ActivityIndicator size="small" color={colors.primary} />
              ) : (
                <Feather name="check" size={18} color={colors.primary} />
              )}
            </TouchableOpacity>
          </View>
        </View>
      );
    }
    
    // Display mode
    const value = profile?.[field as keyof typeof profile];
    let displayValue: string;
    
    if (field === 'birth_date' && value) {
      displayValue = format(new Date(value as string), 'MMMM d, yyyy');
    } else if (field === 'height' && value) {
      displayValue = `${value} cm`;
    } else if (field === 'weight' && value) {
      displayValue = `${value} kg`;
    } else {
      displayValue = value as string || 'Not set';
    }
    
    return (
      <View style={[styles.fieldContainer, { borderBottomColor: colors.border }]}>
        <View style={styles.fieldContent}>
          <View style={styles.fieldIcon}>{icon}</View>
          <View style={styles.fieldTextContainer}>
            <Text style={[styles.fieldTitle, { color: colors.text }]}>{title}</Text>
            <Text 
              style={[
                styles.fieldValue, 
                { color: value ? colors.text : colors.textSecondary }
              ]}
            >
              {displayValue}
            </Text>
            <Text style={[styles.fieldSubtitle, { color: colors.textSecondary }]}>{subtitle}</Text>
          </View>
        </View>
        
        {field !== 'email' && (
          <TouchableOpacity 
            style={[styles.editButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
            onPress={() => handleStartEditing(field)}
          >
            <Feather name="edit-2" size={16} color={colors.primary} />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <>
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.card }]}>
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={() => router.push('/(tabs)/profile')}
            accessibilityLabel="Go back"
            accessibilityHint="Returns to the profile screen"
          >
            <Feather name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Personal Information</Text>
          <View style={{ width: 40 }} />
        </View>
        
        <ScrollView 
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Profile Completeness */}
          <View style={[styles.completenessCard, { 
            backgroundColor: colors.card,
            borderColor: colors.border
          }]}>
            <View style={styles.completenessHeader}>
              <FontAwesome name="trophy" size={20} color={colors.primary} style={{ marginRight: 8 }} />
              <Text style={[styles.completenessTitle, { color: colors.text }]}>
                Profile Completeness
              </Text>
            </View>
            
            <View style={styles.completenessBarContainer}>
              <View 
                style={[
                  styles.completenessBar, 
                  { 
                    backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                  }
                ]}
              >
                <LinearGradient
                  colors={['#3B82F6', '#2563EB']}
                  style={[styles.completenessProgress, { width: `${profile?.completeness || 0}%` }]}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                />
              </View>
              <Text style={[styles.completenessPercent, { color: colors.primary }]}>
                {profile?.completeness || 0}%
              </Text>
            </View>
            
            <Text style={[styles.completenessText, { color: colors.textSecondary }]}>
              Complete your profile to get the most out of the app and personalized recommendations.
            </Text>
          </View>
          
          {/* Profile Picture */}
          <View style={[styles.avatarSection, { 
            backgroundColor: colors.card,
            borderColor: colors.border
          }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Profile Picture</Text>
            
            <View style={styles.avatarContainer}>
              <ProfileImageUploader 
                avatarUrl={profile?.avatar_url || null}
                onAvatarChange={handleAvatarChange}
                size={100}
                disabled={saving}
              />
              <Text style={[styles.avatarHint, { color: colors.textSecondary }]}>
                Tap to change your profile picture
              </Text>
            </View>
          </View>
          
          {/* Basic Information */}
          <View style={[styles.infoSection, { 
            backgroundColor: colors.card,
            borderColor: colors.border
          }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Basic Information</Text>
            
            {renderField(
              'full_name',
              <Feather name="user" size={18} color={colors.primary} />,
              'Full Name',
              'Your legal first and last name'
            )}
            
            {renderField(
              'username',
              <Feather name="user" size={18}  color={colors.text} />,
              'Username',
              'Your unique username for the app'
            )}
            
            {renderField(
              'email',
              <Feather name="mail" size={18}  color={colors.text} />,
              'Email Address',
              'Your account email (not editable)'
            )}
            
            {renderField(
              'birth_date',
              <Feather name="calendar" size={18}  color={colors.text} />,
              'Birth Date',
              'Your date of birth for age calculation'
            )}
          </View>
          
          {/* Health Information */}
          <View style={[styles.infoSection, { 
            backgroundColor: colors.card,
            borderColor: colors.border
          }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Health Information</Text>
            
            {renderField(
              'height',
              <MaterialIcons name="straighten" size={18}  color={colors.text} />,
              'Height',
              'Your height in centimeters'
            )}
            
            {renderField(
              'weight',
              <MaterialIcons name="fitness-center" size={18}  color={colors.text} />,
              'Weight',
              'Your current weight in kilograms'
            )}
          </View>
          
          {/* About You */}
          <View style={[styles.infoSection, { 
            backgroundColor: colors.card,
            borderColor: colors.border
          }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>About You</Text>
            
            {renderField(
              'bio',
              <Feather name="info" size={18}  color={colors.text} />,
              'Bio',
              'A short description about yourself'
            )}
          </View>
          
          {/* Info Box */}
          <View style={[styles.infoBox, { 
            backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)',
            borderColor: `${colors.primary}30`
          }]}>
            <Feather name="info" size={18} color={colors.primary} style={{ marginRight: 10 }} />
            <Text style={[styles.infoText, { color: colors.textSecondary }]}>
              Your information helps us personalize your health recommendations and experience.
              We value your privacy and keep your data secure.
            </Text>
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContent: {
    paddingBottom: 40,
  },
  completenessCard: {
    margin: 20,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 3 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      }
    }),
  },
  completenessHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  completenessTitle: {
    fontSize: 16,
    fontWeight: '700',
  },
  completenessBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  completenessBar: {
    flex: 1,
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
    marginRight: 12,
  },
  completenessProgress: {
    height: '100%',
    borderRadius: 4,
  },
  completenessPercent: {
    fontSize: 16,
    fontWeight: '700',
    width: 40,
    textAlign: 'right',
  },
  completenessText: {
    fontSize: 14,
    lineHeight: 20,
  },
  avatarSection: {
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 3 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      }
    }),
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: '700',
    marginBottom: 16,
  },
  avatarContainer: {
    alignItems: 'center',
  },
  avatarHint: {
    fontSize: 14,
    marginTop: 12,
  },
  infoSection: {
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 3 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      }
    }),
  },
  fieldContainer: {
    borderBottomWidth: 1,
    paddingVertical: 14,
  },
  fieldContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fieldIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(100,116,139,0.1)',
    marginRight: 12,
  },
  fieldTextContainer: {
    flex: 1,
  },
  fieldTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  fieldValue: {
    fontSize: 15,
    marginBottom: 4,
  },
  fieldSubtitle: {
    fontSize: 12,
  },
  editButton: {
    position: 'absolute',
    right: 0,
    top: 14,
    width: 34,
    height: 34,
    borderRadius: 17,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fieldHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  fieldTitles: {
    flex: 1,
  },
  fieldEditorContainer: {
    marginBottom: 10,
  },
  input: {
    height: 46,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  textArea: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingTop: 12,
    paddingBottom: 12,
    fontSize: 16,
    height: 100,
    textAlignVertical: 'top',
  },
  charCount: {
    fontSize: 12,
    alignSelf: 'flex-end',
    marginTop: 4,
  },
  datePickerButton: {
    height: 46,
    borderRadius: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  datePickerText: {
    fontSize: 16,
  },
  editActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 12,
  },
  editActionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 10,
  },
  errorText: {
    color: '#EF4444',
    fontSize: 12,
    marginTop: 4,
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 20,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  mockDatePicker: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    padding: 20,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  mockDatePickerButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    width: '100%',
  },
  mockCancelButton: {
    padding: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  mockConfirmButton: {
    padding: 10,
    borderRadius: 8,
    backgroundColor: '#3B82F6',
  },
}); 