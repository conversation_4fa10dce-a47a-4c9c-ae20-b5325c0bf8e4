import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Switch, ActivityIndicator, Platform, Alert } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '@/contexts/AuthContext';
import { doc, getDoc, updateDoc, collection, query, where, getDocs, setDoc, addDoc, Firestore, getFirestore } from 'firebase/firestore';
import { httpsCallable, getFunctions } from 'firebase/functions';
import { app } from '@/lib/firebase';
import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';
import * as Application from 'expo-application';

// Initialize Firebase services
const db = getFirestore(app);
const functions = getFunctions(app);

// In a real app, these would be fetched from a server
interface PrivacySettings {
  shareActivityData: boolean;
  shareHealthData: boolean;
  shareMealData: boolean;
  publicProfile: boolean;
  allowFriendRequests: boolean;
  dataCollection: boolean;
  biometricAuth: boolean;
  appLock: boolean;
  appLockTimeout: number; // minutes
  emergencyContacts: string[];
  user_id: string;
  updated_at: string;
}

export default function PrivacySettingsScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const { user, signOut } = useAuth();
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [hasBiometrics, setHasBiometrics] = useState(false);
  
  // Default privacy settings
  const [settings, setSettings] = useState<PrivacySettings>({
    shareActivityData: false,
    shareHealthData: false,
    shareMealData: false,
    publicProfile: false,
    allowFriendRequests: true,
    dataCollection: true,
    biometricAuth: false,
    appLock: false,
    appLockTimeout: 5, // minutes
    emergencyContacts: [],
    user_id: user?.id || '',
    updated_at: new Date().toISOString()
  });
  
  useEffect(() => {
    if (user) {
      const initialize = async () => {
        try {
          await checkBiometricAvailability();
          await loadSettings();
        } catch (error) {
          console.error('Error initializing privacy settings:', error);
          // Even if we have an error, we should stop loading state
          setLoading(false);
        }
      };
      
      initialize();
    } else {
      // If no user, stop loading
      setLoading(false);
    }
  }, [user]);
  
  const checkBiometricAvailability = async () => {
    try {
      const compatible = await LocalAuthentication.hasHardwareAsync();
      const enrolled = await LocalAuthentication.isEnrolledAsync();
      setHasBiometrics(compatible && enrolled);
    } catch (error) {
      console.error('Error checking biometric availability:', error);
      setHasBiometrics(false);
    }
  };
  
  const loadSettings = async () => {
    if (!user) {
      setLoading(false);
      return;
    }
    
    setLoading(true);
    try {
      // Fetch settings from Firebase
      const privacySettingsRef = doc(db, 'privacy_settings', user.id);
      const docSnap = await getDoc(privacySettingsRef);
      
      if (docSnap.exists()) {
        setSettings(docSnap.data() as PrivacySettings);
      } else {
        // Create default settings
        const defaultSettings = {
          ...settings,
          user_id: user.id,
        };
        
        // Add to Firebase
        await setDoc(privacySettingsRef, defaultSettings);
      }
    } catch (error) {
      console.error('Error in loadSettings:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleToggleSetting = async (key: keyof PrivacySettings, value: boolean) => {
    if (!user) return;
    
    // Special case for biometric auth
    if (key === 'biometricAuth' && value && !hasBiometrics) {
      Alert.alert(
        'Biometric Authentication Unavailable',
        'Your device does not support biometric authentication or you haven\'t set it up in your device settings.',
        [{ text: 'OK' }]
      );
      return;
    }
    
    // Special case for app lock
    if (key === 'appLock' && value) {
      const biometricResult = await authenticateWithBiometrics();
      if (!biometricResult) {
        return;
      }
    }
    
    // Update local state optimistically
    setSettings(prev => ({
      ...prev,
      [key]: value,
      updated_at: new Date().toISOString()
    }));
    
    try {
      // Update in Firebase
      const privacySettingsRef = doc(db, 'privacy_settings', user.id);
      await updateDoc(privacySettingsRef, { 
        [key]: value,
        updated_at: new Date().toISOString()
      });
    } catch (error) {
      console.error(`Error in handleToggleSetting for ${key}:`, error);
      // Revert optimistic update if there was an error
      loadSettings();
    }
  };
  
  const handleUpdateAppLockTimeout = async (minutes: number) => {
    if (!user) return;
    
    // Update local state optimistically
    setSettings(prev => ({
      ...prev,
      appLockTimeout: minutes,
      updated_at: new Date().toISOString()
    }));
    
    try {
      // Update in Firebase
      const privacySettingsRef = doc(db, 'privacy_settings', user.id);
      await updateDoc(privacySettingsRef, { 
        appLockTimeout: minutes,
        updated_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error in handleUpdateAppLockTimeout:', error);
      // Revert optimistic update if there was an error
      loadSettings();
    }
  };
  
  const authenticateWithBiometrics = async () => {
    try {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to continue',
        fallbackLabel: 'Enter passcode'
      });
      
      return result.success;
    } catch (error) {
      console.error('Error authenticating:', error);
      return false;
    }
  };
  
  const handleSaveSettings = async () => {
    if (!user) return;
    
    setSaving(true);
    try {
      // Update in Firebase
      const privacySettingsRef = doc(db, 'privacy_settings', user.id);
      await setDoc(privacySettingsRef, {
        ...settings,
        updated_at: new Date().toISOString()
      });
      
      Alert.alert('Success', 'Privacy settings saved successfully.');
    } catch (error) {
      console.error('Error in handleSaveSettings:', error);
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setSaving(false);
    }
  };
  
  const handleDataDownload = async () => {
    if (!user) return;
    
    const biometricResult = await authenticateWithBiometrics();
    if (!biometricResult) {
      Alert.alert('Authentication Required', 'Biometric authentication is required to download your data.');
      return;
    }
    
    Alert.alert(
      'Download Your Data',
      'We will prepare a download package containing all your personal data. This process may take up to 24 hours. We will send you an email with a download link when it\'s ready.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Request Data',
          onPress: async () => {
            try {
              // Request data export through Firebase function
              const requestDataExport = httpsCallable(functions, 'requestDataExport');
              const result = await requestDataExport({ user_id: user.id });
              
              Alert.alert(
                'Data Export Requested',
                'Your data export has been requested. You will receive an email with download instructions when it\'s ready.'
              );
            } catch (error) {
              console.error('Error in handleDataDownload:', error);
              Alert.alert('Error', 'Something went wrong. Please try again.');
            }
          }
        }
      ],
      { cancelable: true }
    );
  };
  
  const handleDeleteAccount = async () => {
    if (!user) return;
    
    const biometricResult = await authenticateWithBiometrics();
    if (!biometricResult) {
      Alert.alert('Authentication Required', 'Biometric authentication is required to delete your account.');
      return;
    }
    
    Alert.alert(
      'Delete Account',
      'This action is permanent and cannot be undone. All your data will be removed from our servers. Are you sure you want to delete your account?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Request account deletion through Firebase function
              const requestAccountDeletion = httpsCallable(functions, 'requestAccountDeletion');
              const result = await requestAccountDeletion({ user_id: user.id });
              
              Alert.alert(
                'Account Deletion Requested',
                'Your account deletion request has been submitted. You will receive a confirmation email shortly.',
                [
                  {
                    text: 'OK',
                    onPress: () => {
                      // Sign out the user
                      signOut();
                      // For web, force a full reload to clear any lingering state
                      if (Platform.OS === 'web' && typeof window !== 'undefined') {
                        window.location.href = '/(auth)/login';
                      } else {
                        router.replace('/(auth)/login');
                      }
                    }
                  }
                ]
              );
            } catch (error) {
              console.error('Error in handleDeleteAccount:', error);
              Alert.alert('Error', 'Something went wrong. Please try again.');
            }
          }
        }
      ],
      { cancelable: true }
    );
  };

  return (
    <>
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.card }]}>
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={() => router.back()}
            accessibilityLabel="Go back"
            accessibilityHint="Returns to the previous screen"
          >
            <Feather name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Privacy & Security</Text>
          {saving ? (
            <ActivityIndicator size="small" color={colors.primary} />
          ) : (
            <TouchableOpacity 
              style={[styles.saveButton, { backgroundColor: `${colors.primary}20` }]}
              onPress={handleSaveSettings}
            >
              <Text style={[styles.saveButtonText, { color: colors.primary }]}>Save</Text>
            </TouchableOpacity>
          )}
        </View>
        
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : (
          <ScrollView 
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            {/* Security Section */}
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Security</Text>
              
              <View style={[styles.settingsCard, { 
                backgroundColor: colors.card,
                borderColor: colors.border,
                shadowColor: colors.shadow
              }]}>
                <View style={[styles.settingRow, { borderBottomColor: colors.border }]}>
                  <View style={styles.settingInfo}>
                    <View style={styles.settingIconContainer}>
                      <LinearGradient
                        colors={['#8B5CF6', '#7C3AED'] as const}
                        style={styles.settingIcon}
                      >
                        <Fingerprint size={16} color="white" />
                      </LinearGradient>
                    </View>
                    <View>
                      <Text style={[styles.settingTitle, { color: colors.text }]}>Biometric Authentication</Text>
                      <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                        Require biometric verification for sensitive actions
                      </Text>
                    </View>
                  </View>
                  <Switch
                    value={settings.biometricAuth}
                    onValueChange={(value) => handleToggleSetting('biometricAuth', value)}
                    trackColor={{ false: isDark ? '#3f3f46' : '#e4e4e7', true: `${colors.primary}70` }}
                    thumbColor={settings.biometricAuth ? colors.primary : isDark ? '#71717a' : '#a1a1aa'}
                    disabled={!hasBiometrics}
                  />
                </View>
                
                <View style={[styles.settingRow, { borderBottomColor: colors.border }]}>
                  <View style={styles.settingInfo}>
                    <View style={styles.settingIconContainer}>
                      <LinearGradient
                        colors={['#3B82F6', '#2563EB'] as const}
                        style={styles.settingIcon}
                      >
                        <Feather name="lock" size={16}  color={colors.text} />
                      </LinearGradient>
                    </View>
                    <View>
                      <Text style={[styles.settingTitle, { color: colors.text }]}>App Lock</Text>
                      <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                        Lock the app after a period of inactivity
                      </Text>
                    </View>
                  </View>
                  <Switch
                    value={settings.appLock}
                    onValueChange={(value) => handleToggleSetting('appLock', value)}
                    trackColor={{ false: isDark ? '#3f3f46' : '#e4e4e7', true: `${colors.primary}70` }}
                    thumbColor={settings.appLock ? colors.primary : isDark ? '#71717a' : '#a1a1aa'}
                  />
                </View>
                
                {settings.appLock && (
                  <View style={styles.timeoutContainer}>
                    <Text style={[styles.timeoutTitle, { color: colors.text }]}>
                      Lock after inactivity:
                    </Text>
                    <View style={styles.timeoutOptions}>
                      {[1, 5, 15, 30].map((minutes) => (
                        <TouchableOpacity
                          key={minutes}
                          style={[
                            styles.timeoutOption,
                            { 
                              backgroundColor: settings.appLockTimeout === minutes 
                                ? `${colors.primary}20` 
                                : isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
                              borderColor: settings.appLockTimeout === minutes 
                                ? colors.primary 
                                : 'transparent'
                            }
                          ]}
                          onPress={() => handleUpdateAppLockTimeout(minutes)}
                        >
                          <Text 
                            style={[
                              styles.timeoutText, 
                              { 
                                color: settings.appLockTimeout === minutes 
                                  ? colors.primary 
                                  : colors.text
                              }
                            ]}
                          >
                            {minutes} min
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>
                )}
                
                <TouchableOpacity
                  style={styles.actionRow}
                  onPress={() => {
                    router.push('/(tabs)/profile-screens/change-password');
                  }}
                >
                  <View style={styles.settingInfo}>
                    <View style={styles.settingIconContainer}>
                      <LinearGradient
                        colors={['#F59E0B', '#D97706'] as const}
                        style={styles.settingIcon}
                      >
                        <Key size={16} color="white" />
                      </LinearGradient>
                    </View>
                    <Text style={[styles.actionText, { color: colors.text }]}>Change Password</Text>
                  </View>
                  <Feather name="arrow-right" size={18} color={colors.textSecondary} />
                </TouchableOpacity>
              </View>
            </View>
            
            {/* Data Sharing Section */}
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Data Sharing</Text>
              
              <View style={[styles.settingsCard, { 
                backgroundColor: colors.card,
                borderColor: colors.border,
                shadowColor: colors.shadow
              }]}>
                <View style={[styles.settingRow, { borderBottomColor: colors.border }]}>
                  <View style={styles.settingInfo}>
                    <View style={styles.settingIconContainer}>
                      <LinearGradient
                        colors={['#10B981', '#059669'] as const}
                        style={styles.settingIcon}
                      >
                        <Feather name="share-2" size={16}  color={colors.text} />
                      </LinearGradient>
                    </View>
                    <View>
                      <Text style={[styles.settingTitle, { color: colors.text }]}>Share Activity Data</Text>
                      <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                        Allow friends to see your workouts and achievements
                      </Text>
                    </View>
                  </View>
                  <Switch
                    value={settings.shareActivityData}
                    onValueChange={(value) => handleToggleSetting('shareActivityData', value)}
                    trackColor={{ false: isDark ? '#3f3f46' : '#e4e4e7', true: `${colors.primary}70` }}
                    thumbColor={settings.shareActivityData ? colors.primary : isDark ? '#71717a' : '#a1a1aa'}
                  />
                </View>
                
                <View style={[styles.settingRow, { borderBottomColor: colors.border }]}>
                  <View style={styles.settingInfo}>
                    <View style={styles.settingIconContainer}>
                      <LinearGradient
                        colors={['#EF4444', '#DC2626'] as const}
                        style={styles.settingIcon}
                      >
                        <Feather name="eye" size={16} style={{ transform: [{ rotate: '45deg' }] }} />
                      </LinearGradient>
                    </View>
                    <View>
                      <Text style={[styles.settingTitle, { color: colors.text }]}>Share Health Data</Text>
                      <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                        Share health metrics with friends (weight, BMI, etc.)
                      </Text>
                    </View>
                  </View>
                  <Switch
                    value={settings.shareHealthData}
                    onValueChange={(value) => handleToggleSetting('shareHealthData', value)}
                    trackColor={{ false: isDark ? '#3f3f46' : '#e4e4e7', true: `${colors.primary}70` }}
                    thumbColor={settings.shareHealthData ? colors.primary : isDark ? '#71717a' : '#a1a1aa'}
                  />
                </View>
                
                <View style={styles.settingRow}>
                  <View style={styles.settingInfo}>
                    <View style={styles.settingIconContainer}>
                      <LinearGradient
                        colors={['#EC4899', '#DB2777'] as const}
                        style={styles.settingIcon}
                      >
                        <Feather name="share-2" size={16}  color={colors.text} />
                      </LinearGradient>
                    </View>
                    <View>
                      <Text style={[styles.settingTitle, { color: colors.text }]}>Share Meal Data</Text>
                      <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                        Allow friends to see your meals and nutrition
                      </Text>
                    </View>
                  </View>
                  <Switch
                    value={settings.shareMealData}
                    onValueChange={(value) => handleToggleSetting('shareMealData', value)}
                    trackColor={{ false: isDark ? '#3f3f46' : '#e4e4e7', true: `${colors.primary}70` }}
                    thumbColor={settings.shareMealData ? colors.primary : isDark ? '#71717a' : '#a1a1aa'}
                  />
                </View>
              </View>
            </View>
            
            {/* Profile Privacy Section */}
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Profile Privacy</Text>
              
              <View style={[styles.settingsCard, { 
                backgroundColor: colors.card,
                borderColor: colors.border,
                shadowColor: colors.shadow
              }]}>
                <View style={[styles.settingRow, { borderBottomColor: colors.border }]}>
                  <View style={styles.settingInfo}>
                    <View style={styles.settingIconContainer}>
                      <LinearGradient
                        colors={['#6366F1', '#4F46E5'] as const}
                        style={styles.settingIcon}
                      >
                        <Feather name="globe" size={16}  color={colors.text} />
                      </LinearGradient>
                    </View>
                    <View>
                      <Text style={[styles.settingTitle, { color: colors.text }]}>Public Profile</Text>
                      <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                        Allow your profile to be discovered by other users
                      </Text>
                    </View>
                  </View>
                  <Switch
                    value={settings.publicProfile}
                    onValueChange={(value) => handleToggleSetting('publicProfile', value)}
                    trackColor={{ false: isDark ? '#3f3f46' : '#e4e4e7', true: `${colors.primary}70` }}
                    thumbColor={settings.publicProfile ? colors.primary : isDark ? '#71717a' : '#a1a1aa'}
                  />
                </View>
                
                <View style={styles.settingRow}>
                  <View style={styles.settingInfo}>
                    <View style={styles.settingIconContainer}>
                      <LinearGradient
                        colors={['#0EA5E9', '#0284C7'] as const}
                        style={styles.settingIcon}
                      >
                        <Feather name="users" size={16}  color={colors.text} />
                      </LinearGradient>
                    </View>
                    <View>
                      <Text style={[styles.settingTitle, { color: colors.text }]}>Friend Requests</Text>
                      <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                        Allow other users to send you friend requests
                      </Text>
                    </View>
                  </View>
                  <Switch
                    value={settings.allowFriendRequests}
                    onValueChange={(value) => handleToggleSetting('allowFriendRequests', value)}
                    trackColor={{ false: isDark ? '#3f3f46' : '#e4e4e7', true: `${colors.primary}70` }}
                    thumbColor={settings.allowFriendRequests ? colors.primary : isDark ? '#71717a' : '#a1a1aa'}
                  />
                </View>
              </View>
            </View>
            
            {/* Data Privacy Section */}
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Data & Privacy</Text>
              
              <View style={[styles.settingsCard, { 
                backgroundColor: colors.card,
                borderColor: colors.border,
                shadowColor: colors.shadow
              }]}>
                <View style={[styles.settingRow, { borderBottomColor: colors.border }]}>
                  <View style={styles.settingInfo}>
                    <View style={styles.settingIconContainer}>
                      <LinearGradient
                        colors={['#10B981', '#059669'] as const}
                        style={styles.settingIcon}
                      >
                        <Feather name="bar-chart" size={16}  color={colors.text} />
                      </LinearGradient>
                    </View>
                    <View>
                      <Text style={[styles.settingTitle, { color: colors.text }]}>Data Collection</Text>
                      <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                        Allow anonymous usage data to improve the app
                      </Text>
                    </View>
                  </View>
                  <Switch
                    value={settings.dataCollection}
                    onValueChange={(value) => handleToggleSetting('dataCollection', value)}
                    trackColor={{ false: isDark ? '#3f3f46' : '#e4e4e7', true: `${colors.primary}70` }}
                    thumbColor={settings.dataCollection ? colors.primary : isDark ? '#71717a' : '#a1a1aa'}
                  />
                </View>
                
                <TouchableOpacity
                  style={[styles.actionRow, { borderBottomColor: colors.border }]}
                  onPress={handleDataDownload}
                >
                  <View style={styles.settingInfo}>
                    <View style={styles.settingIconContainer}>
                      <LinearGradient
                        colors={['#6B7280', '#4B5563'] as const}
                        style={styles.settingIcon}
                      >
                        <Feather name="download" size={16}  color={colors.text} />
                      </LinearGradient>
                    </View>
                    <View>
                      <Text style={[styles.actionText, { color: colors.text }]}>Download My Data</Text>
                      <Text style={[styles.actionSubtext, { color: colors.textSecondary }]}>
                        Get a copy of all your personal data
                      </Text>
                    </View>
                  </View>
                  <Feather name="arrow-right" size={18} color={colors.textSecondary} />
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={styles.actionRow}
                  onPress={handleDeleteAccount}
                >
                  <View style={styles.settingInfo}>
                    <View style={styles.settingIconContainer}>
                      <LinearGradient
                        colors={['#EF4444', '#DC2626'] as const}
                        style={styles.settingIcon}
                      >
                        <UserX size={16} color="white" />
                      </LinearGradient>
                    </View>
                    <View>
                      <Text style={[styles.actionText, { color: '#EF4444' }]}>Delete My Account</Text>
                      <Text style={[styles.actionSubtext, { color: colors.textSecondary }]}>
                        Permanently remove your account and all data
                      </Text>
                    </View>
                  </View>
                  <Feather name="arrow-right" size={18} color={'#EF4444'} />
                </TouchableOpacity>
              </View>
            </View>
            
            {/* App Info */}
            <View style={styles.appInfoSection}>
              <View style={styles.appInfoRow}>
                <Text style={[styles.appInfoLabel, { color: colors.textSecondary }]}>App Version</Text>
                <Text style={[styles.appInfoValue, { color: colors.text }]}>
                  {Application.nativeApplicationVersion} ({Application.nativeBuildVersion})
                </Text>
              </View>
              
              <View style={styles.appInfoRow}>
                <Text style={[styles.appInfoLabel, { color: colors.textSecondary }]}>Last Updated</Text>
                <Text style={[styles.appInfoValue, { color: colors.text }]}>
                  {new Date(settings.updated_at).toLocaleDateString()}
                </Text>
              </View>
            </View>
            
            {/* Info box */}
            <View style={[styles.infoBox, { 
              backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)',
              borderColor: `${colors.primary}30`
            }]}>
              <Feather name="info" size={18} color={colors.primary} style={{ marginRight: 10 }} />
              <Text style={[styles.infoText, { color: colors.textSecondary }]}>
                Your privacy is important to us. Review our privacy policy to learn more about how we protect your data.
              </Text>
            </View>
            
            {/* Privacy Policy Link */}
            <TouchableOpacity
              style={[styles.policyButton, { borderColor: colors.border }]}
              onPress={() => {
                router.push('/(tabs)/profile-screens/privacy-policy');
              }}
            >
              <Text style={[styles.policyButtonText, { color: colors.primary }]}>View Privacy Policy</Text>
            </TouchableOpacity>
          </ScrollView>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  saveButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContent: {
    paddingVertical: 16,
    paddingBottom: 40,
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: '700',
    marginBottom: 12,
  },
  settingsCard: {
    borderRadius: 16,
    borderWidth: 1,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 4 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      }
    }),
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    paddingRight: 16,
  },
  settingIconContainer: {
    marginRight: 12,
  },
  settingIcon: {
    width: 36,
    height: 36,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 13,
    lineHeight: 18,
  },
  actionText: {
    fontSize: 16,
    fontWeight: '600',
  },
  actionSubtext: {
    fontSize: 13,
    lineHeight: 18,
  },
  timeoutContainer: {
    padding: 16,
    borderBottomWidth: 1,
  },
  timeoutTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
  },
  timeoutOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  timeoutOption: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    minWidth: 60,
    alignItems: 'center',
  },
  timeoutText: {
    fontSize: 14,
    fontWeight: '500',
  },
  appInfoSection: {
    marginHorizontal: 20,
    marginBottom: 24,
    padding: 16,
    borderRadius: 12,
    backgroundColor: 'rgba(100,116,139,0.05)',
  },
  appInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  appInfoLabel: {
    fontSize: 14,
  },
  appInfoValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginTop: 8,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  policyButton: {
    marginHorizontal: 20,
    marginTop: 16,
    marginBottom: 20,
    padding: 14,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
  },
  policyButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
}); 