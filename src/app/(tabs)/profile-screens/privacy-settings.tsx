import React from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Switch, ActivityIndicator, Platform, Alert } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import { usePrivacySettings } from '@/hooks/usePrivacySettings';
import * as SecureStore from 'expo-secure-store';
import * as Application from 'expo-application';

export default function PrivacySettingsScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  
  // Use the privacy settings hook for all functionality
  const {
    settings,
    loading,
    error,
    hasBiometrics,
    handleToggleSetting,
    handleAppLockTimeoutChange,
    handleResetAllSettings,
    handleRequestDataExport,
    handleDeleteAllData
  } = usePrivacySettings();

  const appLockTimeouts = [
    { label: 'Immediately', value: 0 },
    { label: '1 minute', value: 1 },
    { label: '5 minutes', value: 5 },
    { label: '15 minutes', value: 15 },
    { label: '30 minutes', value: 30 },
    { label: '1 hour', value: 60 }
  ];

  const getAppVersion = () => {
    try {
      return Application.nativeApplicationVersion || '1.0.0';
    } catch (error) {
      return '1.0.0';
    }
  };

  const renderSettingItem = (
    icon: React.ReactNode,
    title: string,
    description: string,
    value: boolean,
    onToggle: () => void,
    disabled: boolean = false
  ) => (
    <View style={[
      styles.settingItem, 
      { 
        borderBottomColor: colors.border,
        backgroundColor: disabled ? (isDark ? '#1A1C1E' : '#F5F5F5') : 'transparent'
      }
    ]}>
      <View style={styles.settingContent}>
        <View style={[styles.settingIcon, { backgroundColor: `${colors.primary}15` }]}>
          {icon}
        </View>
        <View style={styles.settingText}>
          <Text style={[
            styles.settingTitle, 
            { 
              color: disabled ? colors.textSecondary : colors.text 
            }
          ]}>
            {title}
          </Text>
          <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
            {description}
          </Text>
        </View>
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        trackColor={{ false: colors.border, true: `${colors.primary}40` }}
        thumbColor={value ? colors.primary : colors.textSecondary}
        disabled={disabled}
      />
    </View>
  );

  const renderActionItem = (
    icon: React.ReactNode,
    title: string,
    description: string,
    onPress: () => void,
    color: string = colors.primary,
    dangerous: boolean = false
  ) => (
    <TouchableOpacity 
      style={[styles.actionItem, { borderBottomColor: colors.border }]}
      onPress={onPress}
    >
      <View style={styles.settingContent}>
        <View style={[
          styles.settingIcon, 
          { backgroundColor: dangerous ? '#FF334415' : `${color}15` }
        ]}>
          {React.cloneElement(icon as React.ReactElement, { 
            color: dangerous ? '#FF3344' : color 
          })}
        </View>
        <View style={styles.settingText}>
          <Text style={[
            styles.settingTitle, 
            { color: dangerous ? '#FF3344' : colors.text }
          ]}>
            {title}
          </Text>
          <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
            {description}
          </Text>
        </View>
      </View>
      <Feather name="arrow-right" size={20} color={colors.textSecondary} />
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Stack.Screen 
          options={{
            headerShown: false
          }} 
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            Loading privacy settings...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen 
        options={{
          headerShown: false
        }} 
      />
      
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: colors.border }]}>
          <TouchableOpacity 
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Feather name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Privacy & Security
          </Text>
          
          <View style={styles.headerSpacer} />
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Hero Section */}
          <LinearGradient
            colors={isDark ? 
              ['#1E40AF', '#3B82F6', colors.background] : 
              ['#3B82F6', '#60A5FA', colors.background]
            }
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.heroSection}
          >
            <View style={styles.heroContent}>
              <View style={styles.heroIcon}>
                <Feather name="shield" size={32}  color={colors.text} />
              </View>
              <Text style={styles.heroTitle}>Your Privacy Matters</Text>
              <Text style={styles.heroDescription}>
                Control how your data is shared and secured
              </Text>
            </View>
          </LinearGradient>

          {/* Data Sharing Section */}
          <View style={[styles.section, { backgroundColor: colors.card, borderColor: colors.border }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Data Sharing
            </Text>
            <Text style={[styles.sectionDescription, { color: colors.textSecondary }]}>
              Choose what information you want to share with others
            </Text>
            
            {renderSettingItem(
              <Feather name="bar-chart" size={20} color={colors.primary} />,
              'Share Activity Data',
              'Allow friends to see your workout and activity stats',
              settings.shareActivityData,
              () => handleToggleSetting('shareActivityData', !settings.shareActivityData)
            )}
            
            {renderSettingItem(
              <Feather name="share-2" size={20} color={colors.primary} />,
              'Share Health Data',
              'Allow access to your health metrics and progress',
              settings.shareHealthData,
              () => handleToggleSetting('shareHealthData', !settings.shareHealthData)
            )}
            
            {renderSettingItem(
              <MaterialIcons name="restaurant" size={20} color={colors.primary} />,
              'Share Meal Data',
              'Allow others to see your meal history and nutrition info',
              settings.shareMealData,
              () => handleToggleSetting('shareMealData', !settings.shareMealData)
            )}
          </View>

          {/* Profile Visibility Section */}
          <View style={[styles.section, { backgroundColor: colors.card, borderColor: colors.border }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Profile Visibility
            </Text>
            <Text style={[styles.sectionDescription, { color: colors.textSecondary }]}>
              Manage who can find and interact with your profile
            </Text>
            
            {renderSettingItem(
              <Feather name="globe" size={20} color={colors.primary} />,
              'Public Profile',
              'Make your profile discoverable by other users',
              settings.publicProfile,
              () => handleToggleSetting('publicProfile', !settings.publicProfile)
            )}
            
            {renderSettingItem(
              <Feather name="users" size={20} color={colors.primary} />,
              'Allow Friend Requests',
              'Let other users send you friend requests',
              settings.allowFriendRequests,
              () => handleToggleSetting('allowFriendRequests', !settings.allowFriendRequests)
            )}
          </View>

          {/* App Security Section */}
          <View style={[styles.section, { backgroundColor: colors.card, borderColor: colors.border }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              App Security
            </Text>
            <Text style={[styles.sectionDescription, { color: colors.textSecondary }]}>
              Protect your app with biometric authentication
            </Text>
            
            {renderSettingItem(
              <Fingerprint size={20} color={colors.primary} />,
              'Biometric Authentication',
              hasBiometrics ? 'Use fingerprint or face unlock' : 'Not available on this device',
              settings.biometricAuth,
              () => handleToggleSetting('biometricAuth', !settings.biometricAuth),
              !hasBiometrics
            )}
            
            {renderSettingItem(
              <Feather name="lock" size={20} color={colors.primary} />,
              'App Lock',
              'Require authentication when opening the app',
              settings.appLock,
              () => handleToggleSetting('appLock', !settings.appLock)
            )}

            {settings.appLock && (
              <View style={styles.subSetting}>
                <Text style={[styles.subSettingTitle, { color: colors.text }]}>
                  Lock Timeout
                </Text>
                <Text style={[styles.subSettingDescription, { color: colors.textSecondary }]}>
                  How long before the app locks automatically
                </Text>
                <View style={styles.timeoutOptions}>
                  {appLockTimeouts.map((timeout) => (
                    <TouchableOpacity
                      key={timeout.value}
                      style={[
                        styles.timeoutOption,
                        {
                          backgroundColor: settings.appLockTimeout === timeout.value 
                            ? `${colors.primary}20` 
                            : colors.background,
                          borderColor: settings.appLockTimeout === timeout.value 
                            ? colors.primary 
                            : colors.border
                        }
                      ]}
                      onPress={() => handleAppLockTimeoutChange(timeout.value)}
                    >
                      <Text style={[
                        styles.timeoutOptionText,
                        {
                          color: settings.appLockTimeout === timeout.value 
                            ? colors.primary 
                            : colors.text
                        }
                      ]}>
                        {timeout.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}
          </View>

          {/* Data Collection Section */}
          <View style={[styles.section, { backgroundColor: colors.card, borderColor: colors.border }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Data Collection
            </Text>
            <Text style={[styles.sectionDescription, { color: colors.textSecondary }]}>
              Control how we collect and use your data
            </Text>
            
            {renderSettingItem(
              <Feather name="eye" size={20} color={colors.primary} />,
              'Analytics & Insights',
              'Help us improve the app with usage analytics',
              settings.dataCollection,
              () => handleToggleSetting('dataCollection', !settings.dataCollection)
            )}
          </View>

          {/* Account Actions Section */}
          <View style={[styles.section, { backgroundColor: colors.card, borderColor: colors.border }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Account Actions
            </Text>
            <Text style={[styles.sectionDescription, { color: colors.textSecondary }]}>
              Manage your account data and settings
            </Text>
            
            {renderActionItem(
              <Feather name="download" size={20}  color={colors.text} />,
              'Download My Data',
              'Get a copy of all your personal data',
              handleRequestDataExport,
              '#10B981'
            )}
            
            {renderActionItem(
              <Key size={20} />,
              'Reset Privacy Settings',
              'Reset all privacy settings to defaults',
              handleResetAllSettings,
              '#F59E0B'
            )}
            
            {renderActionItem(
              <Feather name="trash-2" size={20}  color={colors.text} />,
              'Delete All Data',
              'Permanently delete your account and all data',
              handleDeleteAllData,
              '#FF3344',
              true
            )}
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <Text style={[styles.footerText, { color: colors.textSecondary }]}>
              App Version {getAppVersion()}
            </Text>
            <Text style={[styles.footerText, { color: colors.textSecondary }]}>
              Last updated: {new Date(settings.updated_at).toLocaleDateString()}
            </Text>
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: Platform.OS === 'ios' ? 60 : 20,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  headerSpacer: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  heroSection: {
    padding: 24,
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  heroContent: {
    alignItems: 'center',
    textAlign: 'center',
  },
  heroIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
  },
  heroDescription: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.9)',
    textAlign: 'center',
  },
  section: {
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 4,
  },
  sectionDescription: {
    fontSize: 14,
    paddingHorizontal: 16,
    paddingBottom: 16,
    lineHeight: 20,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    lineHeight: 18,
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  subSetting: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  subSettingTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  subSettingDescription: {
    fontSize: 12,
    marginBottom: 12,
  },
  timeoutOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  timeoutOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  timeoutOptionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  footer: {
    padding: 24,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    marginBottom: 4,
  },
});