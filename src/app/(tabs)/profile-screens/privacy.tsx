import React, { useState } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Switch, Platform, Alert } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import { updateUserProfile } from '@/services/profileService';
import { useAuth } from '@/contexts/AuthContext';

interface PrivacyOption {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  type: 'toggle' | 'action';
  value?: boolean;
  action?: () => void;
  color: [string, string];
  dangerAction?: boolean;
}

interface PrivacySettings {
  dataSharing: boolean;
  locationTracking: boolean;
  thirdPartyAnalytics: boolean;
  pushNotifications: boolean;
  emailNotifications: boolean;
  marketingCommunications: boolean;
}

export default function PrivacyScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const { user } = useAuth();
  
  // State for toggle options
  const [privacyOptions, setPrivacyOptions] = useState<PrivacyOption[]>([
    {
      id: 'dataCollection',
      title: 'Health Data Collection',
      description: 'Allow the app to collect and analyze your health data to provide personalized insights.',
      icon: FileText,
      type: 'toggle',
      value: true,
      color: ['#3B82F6', '#2563EB'], // Blue
    },
    {
      id: 'locationTracking',
      title: 'Location Tracking',
      description: 'Allow the app to use your location to find nearby restaurants and food options.',
      icon: ToggleLeft,
      type: 'toggle',
      value: false,
      color: ['#10B981', '#059669'], // Green
    },
    {
      id: 'thirdPartySharing',
      title: 'Third-Party Data Sharing',
      description: 'Share anonymous data with third parties to improve our services and recommendations.',
      icon: Eye,
      type: 'toggle',
      value: false,
      color: ['#F59E0B', '#D97706'], // Amber
    },
    {
      id: 'marketingNotifications',
      title: 'Marketing Notifications',
      description: 'Receive marketing and promotional notifications from us and our partners.',
      icon: Bell,
      type: 'toggle',
      value: false,
      color: ['#8B5CF6', '#7C3AED'], // Purple
    },
    {
      id: 'exportData',
      title: 'Export Your Data',
      description: 'Download a copy of all your personal data and health information.',
      icon: Download,
      type: 'action',
      action: () => handleExportData(),
      color: ['#3B82F6', '#2563EB'], // Blue
    },
    {
      id: 'deleteData',
      title: 'Delete Your Data',
      description: 'Permanently delete all your personal data from our servers.',
      icon: Trash2,
      type: 'action',
      action: () => handleDeleteData(),
      color: ['#EF4444', '#DC2626'], // Red
      dangerAction: true,
    },
    {
      id: 'deleteAccount',
      title: 'Delete Account',
      description: 'Permanently delete your account and all associated data.',
      icon: UserX,
      type: 'action',
      action: () => handleDeleteAccount(),
      color: ['#EF4444', '#DC2626'], // Red
      dangerAction: true,
    },
  ]);
  
  // Privacy settings state
  const [settings, setSettings] = useState<PrivacySettings>({
    dataSharing: false,
    locationTracking: false,
    thirdPartyAnalytics: false,
    pushNotifications: true,
    emailNotifications: true,
    marketingCommunications: false,
  });
  
  const [saving, setSaving] = useState(false);
  
  // Handle toggle change
  const handleToggleChange = (id: string, value: boolean) => {
    setPrivacyOptions(privacyOptions.map(option => 
      option.id === id ? { ...option, value } : option
    ));
  };
  
  // Action handlers
  const handleExportData = () => {
    Alert.alert(
      'Export Data',
      'We will prepare your data export and notify you when it\'s ready to download.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Export', onPress: () => console.log('Export initiated') }
      ]
    );
  };
  
  const handleDeleteData = () => {
    Alert.alert(
      'Delete Your Data',
      'This action cannot be undone. All your personal data will be permanently deleted.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive', 
          onPress: () => {
            console.log('Delete data initiated');
            // Additional confirmation for safety
            Alert.alert(
              'Confirm Deletion',
              'Are you absolutely sure? This action is permanent.',
              [
                { text: 'No', style: 'cancel' },
                { 
                  text: 'Yes, Delete All My Data', 
                  style: 'destructive', 
                  onPress: () => console.log('Data deletion confirmed')
                }
              ]
            );
          } 
        }
      ]
    );
  };
  
  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'Your account and all associated data will be permanently deleted. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete Account', 
          style: 'destructive', 
          onPress: () => {
            console.log('Delete account initiated');
            // Additional confirmation for safety
            Alert.alert(
              'Confirm Account Deletion',
              'Are you absolutely sure you want to delete your account? This is permanent and cannot be undone.',
              [
                { text: 'No', style: 'cancel' },
                { 
                  text: 'Yes, Delete My Account', 
                  style: 'destructive', 
                  onPress: () => {
                    console.log('Account deletion confirmed');
                    // For web, force a full reload to clear any lingering state
                    if (Platform.OS === 'web' && typeof window !== 'undefined') {
                      window.location.href = '/(auth)/login';
                    } else {
                      router.replace('/(auth)/login');
                    }
                  }
                }
              ]
            );
          } 
        }
      ]
    );
  };

  const handleToggleSetting = async (setting: keyof PrivacySettings) => {
    try {
      setSaving(true);
      
      // Update local state immediately for responsive UI
      const updatedSettings = { 
        ...settings, 
        [setting]: !settings[setting] 
      };
      
      setSettings(updatedSettings);
      
      // Save to database (in a real app, you'd store these in a separate table)
      // For now, we'll just simulate saving
      // const result = await updatePrivacySettings(updatedSettings);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // In a real app, we'd handle errors here
      // if (!result.success) {
      //   Alert.alert('Error', result.error || 'Failed to update privacy settings');
      //   setSettings(settings); // Revert to previous state
      // }
    } catch (error) {
      console.error('Error updating privacy setting:', error);
      Alert.alert('Error', 'Failed to update privacy setting');
      // Revert to previous state on error
      setSettings(settings);
    } finally {
      setSaving(false);
    }
  };
  
  const privacyItems = [
    {
      title: 'Data Sharing',
      description: 'Allow sharing of anonymized health data for research purposes',
      icon: <Feather name="database" size={20} color={colors.primary} />,
      setting: 'dataSharing' as keyof PrivacySettings,
    },
    {
      title: 'Location Tracking',
      description: 'Allow app to access your location during workouts',
      icon: <Feather name="lock" size={20}  color={colors.text} />,
      setting: 'locationTracking' as keyof PrivacySettings,
    },
    {
      title: 'Third-Party Analytics',
      description: 'Allow anonymous usage statistics to be collected',
      icon: <Feather name="eye" size={20}  color={colors.text} />,
      setting: 'thirdPartyAnalytics' as keyof PrivacySettings,
    },
    {
      title: 'Push Notifications',
      description: 'Receive reminders and updates on your device',
      icon: <Feather name="bell" size={20}  color={colors.text} />,
      setting: 'pushNotifications' as keyof PrivacySettings,
    },
    {
      title: 'Email Notifications',
      description: 'Receive important updates via email',
      icon: <Feather name="share-2" size={20}  color={colors.text} />,
      setting: 'emailNotifications' as keyof PrivacySettings,
    },
    {
      title: 'Marketing Communications',
      description: 'Receive news and promotional content',
      icon: <Feather name="bell" size={20}  color={colors.text} />,
      setting: 'marketingCommunications' as keyof PrivacySettings,
    },
  ];

  return (
    <>
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <LinearGradient
          colors={isDark ? ['#1E293B', '#0F172A'] : ['#3B82F6', '#1D4ED8']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.header}
        >
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Feather name="chevron-left" size={24}  color={colors.text} />
          </TouchableOpacity>
          <View style={styles.headerContent}>
            <Feather name="shield" size={24} style={styles.headerIcon} />
            <Text style={styles.headerTitle}>Privacy</Text>
          </View>
          <View style={styles.headerRight} />
        </LinearGradient>
        
        <ScrollView 
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Privacy Intro Card */}
          <View style={[styles.introCard, { 
            backgroundColor: colors.card,
            borderColor: colors.border,
            shadowColor: colors.shadow
          }]}>
            <View style={styles.introIconContainer}>
              <LinearGradient
                colors={['#3B82F6', '#2563EB'] as const}
                style={styles.introIconGradient}
              >
                <Feather name="shield" size={28}  color={colors.text} />
              </LinearGradient>
            </View>
            <Text style={[styles.introTitle, { color: colors.text }]}>Your Privacy Matters</Text>
            <Text style={[styles.introDescription, { color: colors.textSecondary }]}>
              Control how your data is collected, used, and shared. Your health information is important,
              and we're committed to protecting it.
            </Text>
            <TouchableOpacity 
              style={[styles.privacyPolicyButton, { borderColor: colors.border }]}
              onPress={() => console.log('Privacy policy opened')}
            >
              <Text style={[styles.privacyPolicyText, { color: colors.primary }]}>Read Privacy Policy</Text>
            </TouchableOpacity>
          </View>
          
          {/* Privacy Options */}
          <View style={styles.sectionContainer}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Data Collection & Sharing</Text>
            
            {privacyItems.map((item, index) => (
              <View 
                key={index}
                style={[
                  styles.optionCard,
                  { 
                    backgroundColor: colors.card,
                    borderColor: colors.border,
                    shadowColor: colors.shadow
                  },
                  index < privacyItems.length - 1 && { borderBottomWidth: 1, borderBottomColor: colors.border }
                ]}
              >
                <View style={styles.optionHeader}>
                  <View style={styles.optionIconContainer}>
                    {item.icon}
                  </View>
                  <View style={styles.optionTextContainer}>
                    <Text style={[styles.optionTitle, { color: colors.text }]}>{item.title}</Text>
                    <Text style={[styles.optionDescription, { color: colors.textSecondary }]}>{item.description}</Text>
                  </View>
                  <Switch
                    trackColor={{ false: colors.border, true: colors.primary }}
                    thumbColor={Platform.OS === 'ios' ? 'white' : (settings[item.setting] ? colors.primary : '#f4f3f4')}
                    ios_backgroundColor={colors.border}
                    onValueChange={() => handleToggleSetting(item.setting)}
                    value={settings[item.setting]}
                    disabled={saving}
                  />
                </View>
              </View>
            ))}
          </View>
          
          <View style={styles.sectionContainer}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Manage Your Data</Text>
            
            {privacyOptions.filter(option => option.type === 'action').map((option) => (
              <TouchableOpacity 
                key={option.id}
                style={[styles.optionCard, { 
                  backgroundColor: colors.card,
                  borderColor: option.dangerAction ? option.color[0] + '40' : colors.border,
                  shadowColor: colors.shadow
                }]}
                onPress={option.action}
              >
                <View style={styles.optionHeader}>
                  <LinearGradient
                    colors={option.color as readonly [string, string]}
                    style={styles.optionIcon}
                  >
                    <option.icon size={20} color="white" />
                  </LinearGradient>
                  <View style={styles.optionTextContainer}>
                    <Text style={[
                      styles.optionTitle, 
                      { color: option.dangerAction ? option.color[0] : colors.text }
                    ]}>
                      {option.title}
                    </Text>
                    <Text style={[styles.optionDescription, { color: colors.textSecondary }]}>{option.description}</Text>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>
          
          {/* Additional Information */}
          <View style={[styles.infoCard, { 
            backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)',
            borderColor: '#3B82F6' + '20'
          }]}>
            <Feather name="info" size={18} style={styles.infoIcon} />
            <Text style={[styles.infoText, { color: colors.textSecondary }]}>
              Your data is stored securely and is protected by industry-standard encryption. 
              You can request or delete your data at any time in accordance with applicable privacy laws.
            </Text>
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  headerContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: 'white',
  },
  headerRight: {
    width: 40,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  introCard: {
    margin: 20,
    marginTop: 10,
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 4 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      }
    }),
  },
  introIconContainer: {
    marginBottom: 16,
  },
  introIconGradient: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  introTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 12,
    textAlign: 'center',
  },
  introDescription: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
    marginBottom: 16,
  },
  privacyPolicyButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 20,
    borderWidth: 1,
  },
  privacyPolicyText: {
    fontSize: 14,
    fontWeight: '600',
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginLeft: 20,
    marginBottom: 12,
  },
  optionCard: {
    marginHorizontal: 20,
    marginBottom: 12,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: { elevation: 2 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  optionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.05)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  optionTextContainer: {
    flex: 1,
    marginRight: 8,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  infoCard: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginTop: 10,
    marginBottom: 20,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
  },
  infoIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
}); 