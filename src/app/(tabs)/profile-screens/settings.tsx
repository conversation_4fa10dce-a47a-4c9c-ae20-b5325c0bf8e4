import React from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Switch, Platform, Image } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
// import type { LucideIcon } from 'lucide-react-native'; // Removed - using Expo icons
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '@/contexts/AuthContext';
import { useAlert } from '@/hooks/useAlert';

// Define interfaces for our settings items
interface BaseSettingItem {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  color: readonly [string, string];
}

interface ActionSettingItem extends BaseSettingItem {
  onPress: () => void;
  toggle?: undefined;
  value?: undefined;
  onToggle?: undefined;
}

interface ToggleSettingItem extends BaseSettingItem {
  toggle: true;
  value: boolean;
  onToggle: () => void;
  onPress?: undefined;
}

type SettingItem = ActionSettingItem | ToggleSettingItem;

interface SettingsSection {
  title: string;
  items: SettingItem[];
}

export default function SettingsScreen() {
  const { colors, isDark, theme, setTheme } = useTheme();
  const router = useRouter();
  const { user, signOut } = useAuth();
  const { showAlert } = useAlert();

  const handleSignOut = async () => {
    showAlert({
      title: 'Sign Out',
      message: 'Are you sure you want to sign out of your account?',
      type: 'warning',
      confirmText: 'Sign Out',
      cancelText: 'Cancel',
      onConfirm: async () => {
        const result = await signOut();
        if (result.success) {
          // For web, force a full reload to clear any lingering state
          if (Platform.OS === 'web' && typeof window !== 'undefined') {
            window.location.href = '/(auth)/login';
          } else {
            router.replace('/(auth)/login');
          }
        }
      }
    });
  };

  const handleThemeToggle = () => {
    const newTheme = theme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);
  };

  // Settings sections with grouped items
  const settingsSections: SettingsSection[] = [
    {
      title: 'Account',
      items: [
        {
          id: 'profile',
          title: 'Personal Information',
          description: 'Update your profile details',
          icon: User,
          color: ['#3B82F6', '#2563EB'] as const, 
          onPress: () => router.push('/(tabs)/profile-screens/personal-information' as any)
        },
        {
          id: 'notifications',
          title: 'Notifications',
          description: 'Manage your notification preferences',
          icon: Bell,
          color: ['#10B981', '#059669'] as const,
          onPress: () => router.push('/(tabs)/profile-screens/notification-settings' as any)
        },
        {
          id: 'privacy',
          title: 'Privacy & Security',
          description: 'Control your data and privacy settings',
          icon: Shield,
          color: ['#8B5CF6', '#7C3AED'] as const,
          onPress: () => router.push('/(tabs)/profile-screens/privacy-settings' as any)
        },
        {
          id: 'billing',
          title: 'Subscription & Billing',
          description: 'Manage your subscription plan and payment methods',
          icon: CreditCard,
          color: ['#F59E0B', '#D97706'] as const,
          onPress: () => router.push('/(tabs)/profile-screens/billing' as any)
        }
      ]
    },
    {
      title: 'App Settings',
      items: [
        {
          id: 'theme',
          title: 'Dark Mode',
          description: 'Switch between light and dark themes',
          icon: Moon,
          color: ['#6B7280', '#4B5563'] as const,
          toggle: true,
          value: theme === 'dark',
          onToggle: handleThemeToggle
        },
        {
          id: 'units',
          title: 'Units & Measurements',
          description: 'Choose between metric and imperial units',
          icon: Zap,
          color: ['#EC4899', '#DB2777'] as const,
          onPress: () => {
            showAlert({
              title: 'Units & Measurements',
              message: 'This feature will be available in a future update.',
              type: 'info'
            });
          }
        },
        {
          id: 'language',
          title: 'Language',
          description: 'Select your preferred language',
          icon: Languages,
          color: ['#0EA5E9', '#0284C7'] as const,
          onPress: () => {
            showAlert({
              title: 'Language Settings',
              message: 'Language customization will be available in a future update.',
              type: 'info'
            });
          }
        },
        {
          id: 'devices',
          title: 'Connected Devices',
          description: 'Manage your fitness trackers and smart devices',
          icon: SmartphoneNfc,
          color: ['#EF4444', '#DC2626'] as const,
          onPress: () => {
            router.push('/profile-screens/connected-devices');
          }
        }
      ]
    },
    {
      title: 'Health Data',
      items: [
        {
          id: 'health',
          title: 'Health Metrics',
          description: 'Configure your health data display and tracking',
          icon: HeartPulse,
          color: ['#EF4444', '#DC2626'] as const,
          onPress: () => router.push('/(tabs)/profile-screens/health-data' as any)
        },
        {
          id: 'export',
          title: 'Export Health Data',
          description: 'Download or share your health information',
          icon: Share2,
          color: ['#8B5CF6', '#7C3AED'] as const,
          onPress: () => {
            showAlert({
              title: 'Export Health Data',
              message: 'Data export functionality will be available in a future update.',
              type: 'info'
            });
          }
        }
      ]
    },
    {
      title: 'Support',
      items: [
        {
          id: 'help',
          title: 'Help & Support',
          description: 'Get assistance and view FAQs',
          icon: HelpCircle,
          color: ['#10B981', '#059669'] as const,
          onPress: () => {
            showAlert({
              title: 'Help & Support',
              message: 'Support center will be available in a future update.',
              type: 'info'
            });
          }
        },
        {
          id: 'feedback',
          title: 'Give Feedback',
          description: 'Share your thoughts and suggestions',
          icon: MessageSquare,
          color: ['#3B82F6', '#2563EB'] as const,
          onPress: () => {
            showAlert({
              title: 'Feedback',
              message: 'Thank you for wanting to share feedback! This feature will be available soon.',
              type: 'info'
            });
          }
        },
        {
          id: 'about',
          title: 'About',
          description: 'App version and legal information',
          icon: BadgeAlert,
          color: ['#6B7280', '#4B5563'] as const,
          onPress: () => {
            showAlert({
              title: 'About Health App',
              message: 'Version 1.0.0\n\n© 2024 Health App\nAll rights reserved.',
              type: 'info'
            });
          }
        }
      ]
    }
  ];

  return (
    <>
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.card }]}>
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={() => router.push('/(tabs)/profile')}
            accessibilityLabel="Go back"
            accessibilityHint="Returns to the profile screen"
          >
            <Feather name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <View style={styles.headerContent}>
            <Feather name="settings" size={22} color={colors.primary} style={styles.headerIcon} />
            <Text style={[styles.headerTitle, { color: colors.text }]}>Settings</Text>
          </View>
          <View style={{ width: 40 }} />
        </View>

        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* User summary at the top */}
          <View style={[styles.userSection, { 
            backgroundColor: colors.card,
            borderColor: colors.border
          }]}>
            <View style={styles.userInfo}>
              <Image
                source={{ uri: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=600' }}
                style={styles.userAvatar}
              />
              <View style={styles.userTextInfo}>
                <Text style={[styles.userName, { color: colors.text }]}>
                  {user?.email?.split('@')[0] || 'User'}
                </Text>
                <Text style={[styles.userEmail, { color: colors.textSecondary }]}>
                  {user?.email || '<EMAIL>'}
                </Text>
              </View>
            </View>
            <TouchableOpacity 
              style={[styles.viewProfileButton, { backgroundColor: `${colors.primary}15` }]}
              onPress={() => router.back()}
            >
              <Text style={[styles.viewProfileText, { color: colors.primary }]}>View Profile</Text>
            </TouchableOpacity>
          </View>

          {/* Settings sections */}
          {settingsSections.map((section, sectionIndex) => (
            <View key={`section-${sectionIndex}`} style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>{section.title}</Text>
              <View style={[styles.sectionCard, { 
                backgroundColor: colors.card,
                borderColor: colors.border
              }]}>
                {section.items.map((item, itemIndex) => (
                  <TouchableOpacity 
                    key={item.id}
                    style={[
                      styles.settingItem,
                      itemIndex < section.items.length - 1 && { 
                        borderBottomWidth: 1, 
                        borderBottomColor: colors.border 
                      }
                    ]}
                    onPress={item.toggle ? item.onToggle : item.onPress}
                    activeOpacity={0.7}
                  >
                    <View style={styles.settingItemLeft}>
                      <LinearGradient
                        colors={item.color}
                        style={styles.settingIconContainer}
                      >
                        <item.icon size={18} color="white" />
                      </LinearGradient>
                      <View style={styles.settingTextContainer}>
                        <Text style={[styles.settingTitle, { color: colors.text }]}>
                          {item.title}
                        </Text>
                        <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                          {item.description}
                        </Text>
                      </View>
                    </View>
                    {item.toggle ? (
                      <Switch
                        value={item.value}
                        onValueChange={item.onToggle}
                        trackColor={{ 
                          false: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)', 
                          true: `${colors.primary}70` 
                        }}
                        thumbColor={item.value ? colors.primary : isDark ? '#71717a' : '#f4f4f5'}
                        ios_backgroundColor={isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}
                      />
                    ) : (
                      <Feather name="chevron-right" size={20} color={colors.textSecondary} />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          ))}

          {/* Sign out button */}
          <TouchableOpacity
            style={[styles.signOutButton, { borderColor: colors.border }]}
            onPress={handleSignOut}
          >
            <Feather name="log-out" size={20} color={isDark ? '#EF4444' : '#DC2626'} style={styles.signOutIcon} />
            <Text style={[styles.signOutText, { color: isDark ? '#EF4444' : '#DC2626' }]}>
              Sign Out
            </Text>
          </TouchableOpacity>

          <Text style={[styles.versionText, { color: colors.textSecondary }]}>
            Version 1.0.0
          </Text>

          <View style={styles.footerButtons}>
            <TouchableOpacity 
              style={[styles.cancelButton, { borderColor: colors.border }]}
              onPress={() => router.push('/(tabs)/profile')}
            >
              <Text style={[styles.cancelButtonText, { color: colors.text }]}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  scrollContent: {
    paddingVertical: 16,
    paddingBottom: 40,
  },
  userSection: {
    marginHorizontal: 20,
    marginBottom: 24,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        shadowColor: '#000',
      },
      android: { elevation: 4 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        shadowColor: '#000',
        transform: []
      }
    }),
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  userAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  userTextInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
    textTransform: 'capitalize',
  },
  userEmail: {
    fontSize: 14,
  },
  viewProfileButton: {
    paddingVertical: 10,
    borderRadius: 10,
    alignItems: 'center',
  },
  viewProfileText: {
    fontSize: 14,
    fontWeight: '600',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: '700',
    marginHorizontal: 20,
    marginBottom: 12,
  },
  sectionCard: {
    marginHorizontal: 20,
    borderRadius: 16,
    borderWidth: 1,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        shadowColor: '#000',
      },
      android: { elevation: 4 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        shadowColor: '#000',
        transform: []
      }
    }),
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  settingTextContainer: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 13,
    lineHeight: 18,
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
    marginVertical: 24,
    paddingVertical: 16,
    borderRadius: 16,
    borderWidth: 1,
  },
  signOutIcon: {
    marginRight: 8,
  },
  signOutText: {
    fontSize: 16,
    fontWeight: '600',
  },
  versionText: {
    textAlign: 'center',
    fontSize: 12,
    marginBottom: 16,
  },
  footerButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 24,
  },
  cancelButton: {
    padding: 16,
    borderWidth: 1,
    borderRadius: 10,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
}); 