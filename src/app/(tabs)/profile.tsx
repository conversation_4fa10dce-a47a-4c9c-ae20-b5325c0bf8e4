import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, ActivityIndicator, Platform, Dimensions, Animated, useWindowDimensions } from 'react-native';
import { Link, useRouter } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { useProfileView } from '@/hooks/useProfileView';
import type { ProfileData } from '@/services/profileViewService';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import ProfileStatsCard from './profile/components/ProfileStatsCard';
import ActivitySummaryCard from './profile/components/ActivitySummaryCard';
import { useTheme } from '@/contexts/ThemeContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import StatisticCard from './profile/components/StatisticCard';
import { withSafeWebTransform } from '@/utils/platformUtils';
import { SafeWebLink, SafeTouchable } from '@/utils/webLinkFix';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { Path } from 'react-native-svg';

// Type alias for compatibility with existing components
type Profile = ProfileData;

// Calculate percentage width helper function for web platform
const getPercentageWidth = (percent: number) => {
  if (Platform.OS === 'web') {
    // Get screen width in pixels
    const { width } = Dimensions.get('window');
    // Convert percentage to fixed pixel value for web
    return Math.floor((width * percent) / 100);
  }
  return `${percent}%`;
};

export default function ProfileScreen() {
  const router = useRouter();
  const { isDark, colors } = useTheme();
  const insets = useSafeAreaInsets();
  const [activeSection, setActiveSection] = useState('account');
  const scrollY = new Animated.Value(0);
  const auth = useAuth();
  
  // Use the new hook for all profile data
  const { profile, loading, error, completionPercentage, daysSinceCreation, loadProfileData } = useProfileView();

  // Animation values
  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [1, 0.95],
    extrapolate: 'clamp',
  });

  const headerTranslateY = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [0, -20],
    extrapolate: 'clamp',
  });


  // Navigation handler for profile links
  const handleNavigation = (route: any) => {
    console.log(`Navigating to: ${route}`);
    try {
      router.push(route);
    } catch (error) {
      console.error('Navigation error:', error);
      // Fallback navigation
      setTimeout(() => {
        router.push(route);
      }, 100);
    }
  };

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading profile...</Text>
      </View>
    );
  }

  if (!profile) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <Feather name="user" size={50} color={colors.textSecondary} />
        <Text style={[styles.emptyStateText, { color: colors.text }]}>No profile found</Text>
        <Text style={[styles.emptyStateSubtext, { color: colors.textSecondary }]}>Please log in to continue</Text>
      </View>
    );
  }
  
  const hasRecentAchievements = profile.achievements && profile.achievements.length > 0;
  
  const renderLink = ({ title, icon, href, color, badge }: { title: string, icon: React.ReactNode, href: any, color?: string, badge?: number | string }) => (
    <SafeWebLink href={href}>
      <SafeTouchable style={[styles.linkRow, { borderColor: colors.border }]}>
        <View style={[styles.linkIcon, { backgroundColor: color ? `${color}15` : `${colors.primary}15` }]}>
          {icon}
        </View>
        <View style={styles.linkTextContainer}>
          <Text style={[styles.linkText, { color: colors.text }]}>{title}</Text>
          {badge && (
            <View style={[styles.badge, { backgroundColor: color || colors.primary }]}>
              <Text style={styles.badgeText}>{badge}</Text>
            </View>
          )}
        </View>
        <Feather name="chevron-right" size={18} color={colors.textSecondary} />
      </SafeTouchable>
    </SafeWebLink>
  );


  return (
    <Animated.ScrollView 
      style={[styles.container, { backgroundColor: colors.background }]}
      contentContainerStyle={{ paddingBottom: insets.bottom + 80 }}
      showsVerticalScrollIndicator={false}
      onScroll={Animated.event(
        [{ nativeEvent: { contentOffset: { y: scrollY } } }],
        { useNativeDriver: false }
      )}
      scrollEventThrottle={16}
    >
      {/* Profile Header */}
      <Animated.View 
        style={{ 
          opacity: headerOpacity, 
          transform: [{ translateY: headerTranslateY }]
        }}
      >
        <LinearGradient
          colors={isDark ? 
            ['#2563EB', '#1E40AF', colors.background] : 
            ['#3B82F6', '#60A5FA', colors.background]
          }
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={withSafeWebTransform(styles.headerGradient)}
        >
          {/* Background pattern overlay */}
          <View style={styles.patternOverlay}>
            {Array.from({ length: 5 }).map((_, i) => (
              <View 
                key={i} 
                style={[
                  styles.patternCircle, 
                  { 
                    left: `${10 + (i * 20)}%`, 
                    top: `${(i % 3) * 15 + 5}%`,
                    opacity: 0.07 + (i * 0.01),
                    backgroundColor: isDark ? '#FFFFFF' : '#3B82F6',
                    width: 80 + (i * 10),
                    height: 80 + (i * 10),
                  }
                ]}
              />
            ))}
          </View>
          
          <View style={styles.headerContent}>
            <View style={styles.avatarOuterContainer}>
              <TouchableOpacity 
                style={[styles.avatarContainer, { 
                  borderColor: 'rgba(255,255,255,0.3)',
                  shadowColor: isDark ? '#000000' : '#3B82F6',
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.3,
                  shadowRadius: 8,
                  elevation: 10,
                }]}
                onPress={() => {
                  // Using a try/catch to handle any navigation errors
                  try {
                    console.log('Navigating to edit profile...');
                    router.push('/profile-screens/edit-profile');
                  } catch (error) {
                    console.error('Error navigating to edit profile:', error);
                    // Fallback navigation with delay
                    setTimeout(() => {
                      router.push('/profile-screens/edit-profile');
                    }, 100);
                  }
                }}
              >
                {profile.photoURL ? (
                  <View style={styles.avatar}>
                    <Image 
                      source={{ uri: profile.photoURL }} 
                      style={{ width: '100%', height: '100%' }}
                      resizeMode="cover"
                      onLoad={() => console.log('Profile image loaded successfully')}
                      onError={(e) => {
                        console.log('Error loading profile image:', e.nativeEvent.error);
                        // If the image fails to load, force a refresh to try again
                        setTimeout(() => {
                          loadProfileData();
                        }, 500);
                      }}
                    />
                  </View>
                ) : (
                  <View style={[styles.avatarPlaceholder, { backgroundColor: 'rgba(255,255,255,0.2)' }]}>
                    <Feather name="user" size={45}  color={colors.text} />
                  </View>
                )}
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.editAvatarButton, { 
                  backgroundColor: isDark ? 'rgba(30, 64, 175, 0.95)' : 'rgba(59, 130, 246, 0.95)',
                  borderColor: 'rgba(255,255,255,0.3)',
                  borderWidth: 1,
                }]}
                onPress={() => handleNavigation('/profile-screens/edit-profile')}
              >
                <Feather name="edit-2" size={14}  color={colors.text} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.userInfo}>
              <Text style={[styles.username, { 
                color: '#FFFFFF',
                textShadowColor: 'rgba(0, 0, 0, 0.1)',
                textShadowOffset: {width: 0, height: 1},
                textShadowRadius: 2,
              }]}>
                {profile.name || profile.full_name || 'User'}
              </Text>
              <Text style={[styles.email, { color: 'rgba(255,255,255,0.8)' }]}>
                {profile.email || 'No email'}
              </Text>
              
              {daysSinceCreation && (
                <View style={[styles.memberSince, { marginTop: 10 }]}>
                  <View style={styles.memberBadge}>
                    <Feather name="calendar" size={14}  color={colors.text} />
                  </View>
                  <Text style={[styles.memberSinceText, { color: 'rgba(255,255,255,0.9)' }]}>
                    Member for {daysSinceCreation} days
                  </Text>
                </View>
              )}
            </View>
          </View>
        </LinearGradient>
      </Animated.View>
      
      {/* Add a decorative wave pattern at the bottom of the header */}
      <View style={styles.waveContainer}>
        <Svg
          height="40"
          width="100%"
          viewBox="0 0 1440 120"
          style={styles.waveSvg}
          preserveAspectRatio="none"
        >
          <Path
            d="M0,64L60,69.3C120,75,240,85,360,80C480,75,600,53,720,48C840,43,960,53,1080,58.7C1200,64,1320,64,1380,64L1440,64L1440,0L1380,0C1320,0,1200,0,1080,0C960,0,840,0,720,0C600,0,480,0,360,0C240,0,120,0,60,0L0,0Z"
            fill={isDark ? colors.background : "#FFFFFF"}
          />
        </Svg>
      </View>
      
      {/* Quick Stats */}
      <View style={[styles.quickStatsContainer, { backgroundColor: isDark ? colors.card : 'white' }]}>
        {renderQuickStat('Weight', profile.weight ? `${profile.weight} lbs` : 'Not set', colors.primary)}
        <View style={[styles.quickStatDivider, { backgroundColor: colors.border }]} />
        <View style={[styles.quickStatDivider, { backgroundColor: colors.border }]} />
        {renderQuickStat('Height', profile.height || 'Not set', '#8B5CF6')}
        {renderQuickStat('Goal', profile.daily_calorie_goal ? `${profile.daily_calorie_goal} cal` : 'Not set', '#F59E0B')}
      </View>
      
      {/* Today's Summary Card */}
      {/* Hiding Today's Summary Card as requested */}
      {/* <View style={[styles.summaryCard, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
        <View style={styles.summaryHeader}>
          <Text style={[styles.summaryTitle, { color: colors.text }]}>Today's Summary</Text>
          <SafeWebLink href="/profile-screens/history">
            <Text style={[styles.viewAllText, { color: colors.primary }]}>View All</Text>
          </SafeWebLink>
        </View>
        
        <View style={styles.summaryMetrics}>
          <View style={styles.summaryMetric}>
            <View style={[styles.summaryIconContainer, { backgroundColor: '#EF444425' }]}>
              <Feather name="zap" size={20}  color={colors.text} />
            </View>
            <View>
              <Text style={[styles.summaryMetricValue, { color: colors.text }]}>
                {profile.stats?.calories || 0}
              </Text>
              <Text style={[styles.summaryMetricLabel, { color: colors.textSecondary }]}>Calories</Text>
            </View>
            <View style={[styles.targetIndicator, { borderColor: '#EF4444' }]}>
              <Text style={[styles.targetText, { color: '#EF4444' }]}>
                {profile.daily_calorie_goal ? `${Math.floor((profile.stats?.calories || 0) / profile.daily_calorie_goal * 100)}%` : '0%'}
              </Text>
            </View>
          </View>
          
          <View style={[styles.divider, { backgroundColor: colors.border }]} />
          
          <View style={styles.summaryMetric}>
            <View style={[styles.summaryIconContainer, { backgroundColor: '#3B82F625' }]}>
              <MessageCircle size={20} color="#3B82F6" />
            </View>
            <View>
              <Text style={[styles.summaryMetricValue, { color: colors.text }]}>
                {profile.stats?.water || 0}
              </Text>
              <Text style={[styles.summaryMetricLabel, { color: colors.textSecondary }]}>Water (oz)</Text>
            </View>
            <View style={[styles.targetIndicator, { borderColor: '#3B82F6' }]}>
              <Text style={[styles.targetText, { color: '#3B82F6' }]}>
                {profile.daily_water_goal ? `${Math.floor((profile.stats?.water || 0) / profile.daily_water_goal * 100)}%` : '0%'}
              </Text>
            </View>
          </View>
        </View>
      </View> */}
      
      {/* Body Statistics Card */}
      {profile && (
        <View style={withSafeWebTransform({})}>
          <ProfileStatsCard profile={profile} />
        </View>
      )}
      
      {/* Activity Summary Card */}
      {/* Hiding Activity Summary as requested */}
      {/* {profile && (
        <ActivitySummaryCard profile={profile} />
      )} */}
      
      {/* Recent Achievements */}
      {/* Hiding Achievements as requested */}
      {/* {hasRecentAchievements && (
        <View style={[styles.achievementsContainer, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
          <View style={styles.achievementsHeader}>
            <Text style={[styles.achievementsTitle, { color: colors.text }]}>Recent Achievements</Text>
            <SafeWebLink href="/profile-screens/achievements">
              <Text style={[styles.viewAllText, { color: colors.primary }]}>View All</Text>
            </SafeWebLink>
          </View>
          
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.achievementsScroll}
          >
            {(profile.achievements ?? []).slice(0, 3).map((achievement, index) => (
              <View key={index} style={[styles.achievementCard, { backgroundColor: isDark ? colors.background : '#F9FAFB' }]}>
                <View style={[styles.achievementIconContainer, { backgroundColor: `${achievement.color}25` || '#10B98125' }]}>
                  <Feather name="award" size={20} color={achievement.color || '#10B981'} />
                </View>
                <Text style={[styles.achievementTitle, { color: colors.text }]}>
                  {achievement.title}
                </Text>
                <Text style={[styles.achievementDescription, { color: colors.textSecondary }]} numberOfLines={2}>
                  {achievement.description}
                </Text>
                <Text style={[styles.achievementDate, { color: colors.textSecondary }]}>
                  {achievement.date}
                </Text>
              </View>
            ))}
            
            <SafeWebLink href="/profile-screens/achievements">
              <View style={[styles.viewMoreAchievements, { backgroundColor: isDark ? colors.background : '#F9FAFB' }]}>
                <Text style={[styles.viewMoreText, { color: colors.primary }]}>View More</Text>
                <Feather name="chevron-right" size={16} color={colors.primary} />
              </View>
            </SafeWebLink>
          </ScrollView>
        </View>
      )} */}
      
      {/* Account Section - Always visible now that tabs are removed */}
      <View style={styles.accountContainer}>
        {/* Profile Management */}
        <View style={[styles.accountSection, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
          <Text style={[styles.accountSectionTitle, { color: isDark ? '#FFFFFF' : '#000000', fontWeight: '700', letterSpacing: 0.5 }]}>PROFILE MANAGEMENT</Text>
          
          <TouchableOpacity 
            style={[styles.accountItem, { borderColor: colors.border }]}
            onPress={() => handleNavigation('/profile-screens/edit-profile')}
          >
            <View style={[styles.accountIconContainer, { backgroundColor: `${colors.primary}15` }]}>
              <Feather name="user" size={18} color={colors.primary} />
            </View>
            <View style={styles.accountItemContent}>
              <Text style={[styles.accountItemTitle, { color: isDark ? '#FFFFFF' : '#000000', fontWeight: '600' }]}>
                Personal Information
              </Text>
              <Text style={[styles.accountItemDescription, { color: isDark ? '#E0E0E0' : '#333333', lineHeight: 16 }]}>
                Edit your profile details and photo
              </Text>
            </View>
            <Feather name="chevron-right" size={18} color={colors.textSecondary} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.accountItem, { borderColor: colors.border }]}
            onPress={() => handleNavigation('/profile-screens/settings')}
          >
            <View style={[styles.accountIconContainer, { backgroundColor: '#8B5CF615' }]}>
              <Feather name="settings" size={18}  color={colors.text} />
            </View>
            <View style={styles.accountItemContent}>
              <Text style={[styles.accountItemTitle, { color: isDark ? '#FFFFFF' : '#000000', fontWeight: '600' }]}>
                Account Settings
              </Text>
              <Text style={[styles.accountItemDescription, { color: isDark ? '#E0E0E0' : '#333333', lineHeight: 16 }]}>
                Manage app preferences and notifications
              </Text>
            </View>
            <Feather name="chevron-right" size={18} color={colors.textSecondary} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.accountItem, { borderColor: colors.border }]}
            onPress={() => handleNavigation('/profile-screens/privacy')}
          >
            <View style={[styles.accountIconContainer, { backgroundColor: '#3B82F615' }]}>
              <Feather name="shield" size={18}  color={colors.text} />
            </View>
            <View style={styles.accountItemContent}>
              <Text style={[styles.accountItemTitle, { color: isDark ? '#FFFFFF' : '#000000', fontWeight: '600' }]}>
                Privacy & Security
              </Text>
              <Text style={[styles.accountItemDescription, { color: isDark ? '#E0E0E0' : '#333333', lineHeight: 16 }]}>
                Manage permissions and data settings
              </Text>
            </View>
            <Feather name="chevron-right" size={18} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
        
        {/* Content & History - Hidden as requested */}
        {/* <View style={[styles.accountSection, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
          <Text style={[styles.accountSectionTitle, { color: isDark ? '#FFFFFF' : '#000000', fontWeight: '700', letterSpacing: 0.5 }]}>CONTENT & HISTORY</Text>
          
          <TouchableOpacity 
            style={[styles.accountItem, { borderColor: colors.border }]}
            onPress={() => handleNavigation('/profile-screens/achievements')}
          >
            <View style={[styles.accountIconContainer, { backgroundColor: '#F59E0B15' }]}>
              <Feather name="award" size={18}  color={colors.text} />
            </View>
            <View style={styles.accountItemContent}>
              <Text style={[styles.accountItemTitle, { color: isDark ? '#FFFFFF' : '#000000', fontWeight: '600' }]}>
                Achievements
              </Text>
              <Text style={[styles.accountItemDescription, { color: isDark ? '#E0E0E0' : '#333333', lineHeight: 16 }]}>
                View your earned badges and rewards
              </Text>
            </View>
            {(profile.achievements?.length ?? 0) > 0 && (
              <View style={[styles.accountBadge, { backgroundColor: '#F59E0B' }]}>
                <Text style={styles.accountBadgeText}>
                  {profile.achievements?.length ?? 0}
                </Text>
              </View>
            )}
            <Feather name="chevron-right" size={18} color={colors.textSecondary} style={{ marginLeft: 8 } />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.accountItem, { borderColor: colors.border }]}
            onPress={() => handleNavigation('/profile-screens/history')}
          >
            <View style={[styles.accountIconContainer, { backgroundColor: '#10B98115' }]}>
              <MaterialIcons name="history" size={18}  color={colors.text} />
            </View>
            <View style={styles.accountItemContent}>
              <Text style={[styles.accountItemTitle, { color: isDark ? '#FFFFFF' : '#000000', fontWeight: '600' }]}>
                Activity History
              </Text>
              <Text style={[styles.accountItemDescription, { color: isDark ? '#E0E0E0' : '#333333', lineHeight: 16 }]}>
                View your past activity and records
              </Text>
            </View>
            <Feather name="chevron-right" size={18} color={colors.textSecondary} />
          </TouchableOpacity>
        </View> */}
        
        {/* Support & More */}
        <View style={[styles.accountSection, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
          <Text style={[styles.accountSectionTitle, { color: isDark ? '#FFFFFF' : '#000000', fontWeight: '700', letterSpacing: 0.5 }]}>SUPPORT & MORE</Text>
          
          <TouchableOpacity 
            style={[styles.accountItem, { borderColor: colors.border }]}
            onPress={() => handleNavigation('/profile-screens/help-support')}
          >
              <View style={[styles.accountIconContainer, { backgroundColor: '#EC489915' }]}>
                <Feather name="help-circle" size={18}  color={colors.text} />
              </View>
              <View style={styles.accountItemContent}>
                <Text style={[styles.accountItemTitle, { color: isDark ? '#FFFFFF' : '#000000', fontWeight: '600' }]}>
                  Contact
                </Text>
                <Text style={[styles.accountItemDescription, { color: isDark ? '#E0E0E0' : '#333333', lineHeight: 16 }]}>
                  Get answers and contact support
                </Text>
              </View>
              <Feather name="chevron-right" size={18} color={colors.textSecondary} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.accountItem, { borderColor: colors.border }]}
            onPress={() => {
              // Share app with friends
              if (Platform.OS === 'web') {
                // For web, open a share dialog if supported
                if (navigator.share) {
                  navigator.share({
                    title: 'Check out this health app',
                    text: 'I\'m tracking my health goals with this amazing app. Join me!',
                    url: 'https://healthapp.example.com',
                  }).catch(error => console.log('Error sharing:', error));
                } else {
                  // Fallback for browsers that don't support navigator.share
                  alert('Copy this link to share: https://healthapp.example.com');
                }
              } else {
                // For native mobile
                handleNavigation('/profile-screens/invite');
              }
            }}
          >
            <View style={[styles.accountIconContainer, { backgroundColor: '#10B98115' }]}>
              <UserPlus size={18} color="#10B981" />
            </View>
            <View style={styles.accountItemContent}>
              <Text style={[styles.accountItemTitle, { color: isDark ? '#FFFFFF' : '#000000', fontWeight: '600' }]}>
                Invite Friends
              </Text>
              <Text style={[styles.accountItemDescription, { color: isDark ? '#E0E0E0' : '#333333', lineHeight: 16 }]}>
                Share the app and track health goals together
              </Text>
            </View>
            <View style={styles.shareOptionsContainer}>
              {Platform.OS !== 'web' && (
                <Text style={[styles.sharePrompt, { color: colors.primary }]}>Share now</Text>
              )}
            </View>
            <Feather name="chevron-right" size={18} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
        
        {/* Dev Menu Button (only shown when env flag is set) */}
        {process.env.EXPO_PUBLIC_ENABLE_DEV_MENU === 'true' && __DEV__ && (
          <TouchableOpacity 
            style={[styles.devButton, { borderColor: colors.border, backgroundColor: isDark ? '#1F2937' : '#F9FAFB' }]}
            onPress={() => router.push('/dev-menu')}
          >
            <Text style={[styles.devButtonText, { color: colors.primary }]}>🛠️ Dev Tools</Text>
          </TouchableOpacity>
        )}
        
        {/* Logout Button */}
        <TouchableOpacity 
          style={[styles.logoutButton, { borderColor: colors.border }]}
          onPress={async () => {
            try {
              const result = await auth.signOut();
              if (result.success) {
                console.log('User signed out successfully');
                // For web, force a full reload to clear any lingering state
                if (Platform.OS === 'web' && typeof window !== 'undefined') {
                  window.location.href = '/(auth)/login';
                } else {
                  router.replace('/(auth)/login');
                }
              } else {
                console.error('Error signing out:', result.error);
              }
            } catch (error) {
              console.error('Error signing out:', error);
            }
          }}
        >
          <Feather name="log-out" size={18}  color={colors.text} />
          <Text style={[styles.logoutText, { color: "#FF3B30", fontWeight: '600' }]}>Log Out</Text>
        </TouchableOpacity>
      </View>
    </Animated.ScrollView>
  );

  function getCompletionColor() {
    if (completionPercentage < 40) return '#EF4444';
    if (completionPercentage < 70) return '#F59E0B';
    return '#10B981';
  }

  function renderQuickStat(label: string, value: string | number, color: string) {
    return (
      <View style={styles.quickStat}>
        <Text style={[styles.quickStatValue, { color }]}>{value}</Text>
        <Text style={[styles.quickStatLabel, { color: colors.textSecondary }]}>{label}</Text>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  headerGradient: {
    paddingTop: 40,
    paddingBottom: 30,
    marginBottom: 16,
  },
  headerContent: {
    alignItems: 'center',
    position: 'relative',
    zIndex: 2,
  },
  avatarOuterContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 4,
    overflow: 'hidden',
    zIndex: 2,
  },
  avatar: {
    width: '100%',
    height: '100%',
  },
  avatarPlaceholder: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 50,
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 5,
    zIndex: 3,
  },
  completionRingContainer: {
    position: 'absolute',
    top: -10,
    left: -10,
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  completionRingBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    borderRadius: 60,
    backgroundColor: 'rgba(255,255,255,0.15)',
  },
  completionRingProgress: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: 10,
    borderTopLeftRadius: 5,
    borderTopRightRadius: 5,
  },
  completionRingInner: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'transparent',
  },
  completionText: {
    position: 'absolute',
    right: 8,
    top: 0,
    fontSize: 12,
    fontWeight: 'bold',
    color: '#10B981',
  },
  userInfo: {
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  username: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  email: {
    fontSize: 14,
    marginBottom: 8,
  },
  memberSince: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    backgroundColor: 'rgba(0,0,0,0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  memberSinceText: {
    fontSize: 12,
    marginLeft: 4,
  },
  profileCompletionContainer: {
    width: 200,
    marginTop: 12,
    alignItems: 'center',
  },
  profileCompletionText: {
    fontSize: 12,
    marginBottom: 6,
  },
  progressBar: {
    width: '100%',
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  quickStatsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 20,
    marginBottom: 16,
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  quickStat: {
    alignItems: 'center',
    flex: 1,
  },
  quickStatValue: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  quickStatLabel: {
    fontSize: 12,
  },
  quickStatDivider: {
    width: 1,
    height: 30,
    opacity: 0.2,
  },
  summaryCard: {
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    padding: 16,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  viewAllText: {
    fontSize: 12,
    fontWeight: '600',
  },
  summaryMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryMetric: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  summaryIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  summaryMetricValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  summaryMetricLabel: {
    fontSize: 12,
  },
  targetIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    marginLeft: 'auto',
  },
  targetText: {
    fontSize: 10,
    fontWeight: 'bold',
  },
  divider: {
    width: 1,
    height: '80%',
    alignSelf: 'center',
    marginHorizontal: 10,
  },
  achievementsContainer: {
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    padding: 16,
  },
  achievementsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  achievementsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  achievementsScroll: {
    paddingRight: 16,
  },
  achievementCard: {
    width: 150,
    padding: 12,
    borderRadius: 12,
    marginRight: 12,
  },
  achievementIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  achievementTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  achievementDescription: {
    fontSize: 12,
    marginBottom: 8,
    lineHeight: 16,
  },
  achievementDate: {
    fontSize: 10,
  },
  viewMoreAchievements: {
    width: 100,
    height: 150,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewMoreText: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  statsContainer: {
    margin: 20,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    marginTop: 0,
    marginBottom: 16,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  sectionContainer: {
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: '600',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    letterSpacing: 0.5,
  },
  linkRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  linkIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  linkTextContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  linkText: {
    fontSize: 16,
  },
  badge: {
    minWidth: 22,
    height: 22,
    borderRadius: 11,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
    marginLeft: 8,
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  accountContainer: {
    padding: 20,
  },
  accountSection: {
    padding: 16,
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: 16,
  },
  accountSectionTitle: {
    fontSize: 12,
    fontWeight: '700',
    marginBottom: 12,
    letterSpacing: 0.5,
  },
  accountItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
  },
  accountIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  accountItemContent: {
    flex: 1,
  },
  accountItemTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  accountItemDescription: {
    fontSize: 12,
    lineHeight: 16,
  },
  accountBadge: {
    padding: 4,
    borderRadius: 8,
    marginLeft: 'auto',
  },
  accountBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
    marginBottom: 20,
    paddingVertical: 14,
    borderRadius: 12,
    borderWidth: 1,
    borderStyle: 'dashed',
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  devButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
    marginBottom: 16,
    paddingVertical: 14,
    borderRadius: 12,
    borderWidth: 1,
  },
  devButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  shareOptionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sharePrompt: {
    fontSize: 12,
    fontWeight: '600',
    marginRight: 4,
  },
  patternOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    zIndex: 1,
  },
  patternCircle: {
    position: 'absolute',
    borderRadius: 40,
  },
  waveContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: '100%',
    height: 40,
  },
  waveSvg: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
  },
  memberBadge: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 4,
  },
});