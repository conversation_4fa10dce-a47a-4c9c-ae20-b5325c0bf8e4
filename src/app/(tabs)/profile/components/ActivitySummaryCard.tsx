/**
 * ActivitySummaryCard Component
 * Provides a visually appealing display of activity metrics
 */
import React from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TouchableOpacity,
  Dimensions,
  ActivityIndicator
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Profile } from '../utils/types';
import { withSafeWebTransform } from '@/utils/platformUtils';
import { SafeWebLink } from '@/utils/webLinkFix';
import { Feather , Ionicons , MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { useActivitySummary } from '@/hooks/useActivitySummary';

interface ActivitySummaryCardProps {
  profile: Profile;
}

export default function ActivitySummaryCard({ profile }: ActivitySummaryCardProps) {
  const { isDark, colors } = useTheme();
  const router = useRouter();
  const { width: screenWidth } = Dimensions.get('window');
  const chartWidth = Math.min(screenWidth - 60, 300);
  
  // Use the new hook for all activity data
  const { 
    activityHistory, 
    isLoading, 
    error, 
    timeRange, 
    setTimeRange, 
    maxValues, 
    formatDisplayDate 
  } = useActivitySummary();
  
  // Default stats if none are present or for demo purposes
  const stats = profile?.stats || {};
  
  const activityMetrics = [
    { 
      id: 'calories', 
      title: 'Calories', 
      value: stats.calories || 0, 
      icon: (props) => <Feather name="zap"  size={20}  color={colors.text} />, 
      color: '#EF4444',
      target: profile?.daily_calorie_goal || 2000,
      unit: 'kcal',
      data: activityHistory.calories
    },
    { 
      id: 'water', 
      title: 'Water', 
      value: stats.water || 0, 
      icon: (props) => <Ionicons name="water"  size={20}  color={colors.text} />, 
      color: '#3B82F6',
      target: profile?.daily_water_goal || 64,
      unit: 'oz',
      data: activityHistory.water
    },
    { 
      id: 'meals', 
      title: 'Meals', 
      value: stats.meals || 0, 
      icon: (props) => <MaterialIcons name="restaurant"  size={20}  color={colors.text} />, 
      color: '#F59E0B',
      target: 3,
      unit: 'meals',
      data: activityHistory.meals
    },
    { 
      id: 'steps', 
      title: 'Steps', 
      value: stats.steps || 0, 
      icon: (props) => <Feather name="activity"  size={20}  color={colors.text} />, 
      color: '#10B981',
      target: 10000,
      unit: 'steps',
      data: activityHistory.steps
    }
  ];
  
  // Helper to get time range label
  const getTimeRangeLabel = () => {
    const today = new Date();
    
    switch(timeRange) {
      case 'week':
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        const endOfWeek = new Date(today);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        return `${startOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${endOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`;
      
      case 'month':
        return today.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
        
      default: // day
        return today.toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' });
    }
  };
  
  // Calculate percentage for progress circle
  const calculatePercentage = (value: number, target: number) => {
    if (!value || !target) return 0;
    const percentage = (value / target) * 100;
    return Math.min(percentage, 100);
  };
  
  // Format value with appropriate units
  const formatValue = (value: number, unit: string) => {
    if (unit === 'kcal') {
      return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    return value;
  };
  
  // Function to render mini bar chart
  const renderMiniChart = (data: number[], color: string, height: number = 30, barWidth: number = 4) => {
    if (!data || data.length === 0) {
      return (
        <View style={styles.miniChartContainer}>
          <Text style={{ color: colors.textSecondary, fontSize: 10 }}>No data</Text>
        </View>
      );
    }
    
    const maxValue = Math.max(...data);
    
    return (
      <View style={styles.miniChartContainer}>
        {data.map((value, index) => {
          const barHeight = maxValue > 0 ? (value / maxValue) * height : 0;
          
          return (
            <View 
              key={`bar-${index}`} 
              style={[
                styles.miniChartBar, 
                { 
                  height: barHeight || 1, // Minimum height of 1
                  backgroundColor: color,
                  width: barWidth,
                  marginRight: index < data.length - 1 ? 2 : 0,
                  opacity: timeRange === 'day' ? (index === data.length - 1 ? 1 : 0.3) : 1
                }
              ]}
            />
          );
        })}
      </View>
    );
  };
  
  return (
    <View style={[styles.container, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={[styles.title, { color: colors.text }]}>Activity Summary</Text>
          <View style={styles.timeRangeSelector}>
            <Feather name="calendar" size={14} color={colors.textSecondary} />
            <Text style={[styles.timeRangeText, { color: colors.textSecondary }]}>
              {getTimeRangeLabel()}
            </Text>
          </View>
        </View>
        
        <View style={styles.timeOptions}>
          <TouchableOpacity 
            style={[
              styles.timeOption, 
              timeRange === 'day' && [styles.activeTimeOption, { borderColor: colors.primary }]
            ]}
            onPress={() => setTimeRange('day')}
          >
            <Text style={[
              styles.timeOptionText, 
              { color: timeRange === 'day' ? colors.primary : colors.textSecondary }
            ]}>
              Day
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[
              styles.timeOption, 
              timeRange === 'week' && [styles.activeTimeOption, { borderColor: colors.primary }]
            ]}
            onPress={() => setTimeRange('week')}
          >
            <Text style={[
              styles.timeOptionText, 
              { color: timeRange === 'week' ? colors.primary : colors.textSecondary }
            ]}>
              Week
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[
              styles.timeOption, 
              timeRange === 'month' && [styles.activeTimeOption, { borderColor: colors.primary }]
            ]}
            onPress={() => setTimeRange('month')}
          >
            <Text style={[
              styles.timeOptionText, 
              { color: timeRange === 'month' ? colors.primary : colors.textSecondary }
            ]}>
              Month
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading activity data...
          </Text>
        </View>
      ) : (
        <View style={styles.metricsGrid}>
          {activityMetrics.map((metric) => {
            const percentage = calculatePercentage(metric.value, metric.target);
            const Icon = metric.icon;
            
            return (
              <View key={metric.id} style={styles.metricCard}>
                <LinearGradient
                  colors={[`${metric.color}10`, `${metric.color}05`] as const}
                  style={[
                    styles.metricCardInner,
                    { borderColor: isDark ? 'transparent' : `${metric.color}20` }
                  ]}
                >
                  <View style={styles.metricHeader}>
                    <View style={[styles.metricIconContainer, { backgroundColor: `${metric.color}15` }]}>
                      <Icon size={16} color={metric.color} />
                    </View>
                    <Text style={[styles.metricTitle, { color: colors.textSecondary }]}>
                      {metric.title}
                    </Text>
                  </View>
                  
                  <View style={styles.metricContent}>
                    <View style={styles.metricValues}>
                      <Text style={[styles.metricValue, { color: colors.text }]}>
                        {formatValue(metric.value, metric.unit)}
                      </Text>
                      <Text style={[styles.metricUnit, { color: colors.textSecondary }]}>
                        {metric.unit}
                      </Text>
                    </View>
                    
                    {renderMiniChart(metric.data, metric.color)}
                  </View>
                  
                  <View style={styles.metricFooter}>
                    <View style={styles.progressContainer}>
                      <View style={[styles.progressBar, { backgroundColor: `${metric.color}15` }]}>
                        <View 
                          style={[
                            styles.progressFill, 
                            { 
                              width: `${percentage}%`,
                              backgroundColor: metric.color
                            }
                          ]} 
                        />
                      </View>
                      <Text style={[styles.progressText, { color: colors.textSecondary }]}>
                        {percentage.toFixed(0)}% of goal
                      </Text>
                    </View>
                  </View>
                </LinearGradient>
              </View>
            );
          })}
        </View>
      )}
      
      <TouchableOpacity 
        style={[
          styles.viewHistoryButton, 
          { borderColor: colors.border, backgroundColor: isDark ? colors.card : '#F9FAFB' }
        ]}
        onPress={() => router.push('/profile-screens/history')}
      >
        <Text style={[styles.viewHistoryText, { color: colors.primary }]}>
          View Complete History
        </Text>
        <BarChart3 size={16} color={colors.primary} style={{ marginRight: 4 }} />
        <Feather name="arrow-right" size={14} color={colors.primary} />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 12,
  },
  titleContainer: {
    marginBottom: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  timeRangeSelector: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeRangeText: {
    fontSize: 12,
    marginLeft: 4,
  },
  timeOptions: {
    flexDirection: 'row',
    marginTop: 8,
  },
  timeOption: {
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 20,
    marginRight: 8,
  },
  activeTimeOption: {
    borderWidth: 1,
    backgroundColor: 'transparent',
  },
  timeOptionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 8,
    justifyContent: 'space-between',
  },
  metricCard: {
    width: '48%',
    marginBottom: 12,
  },
  metricCardInner: {
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    height: 140,
    justifyContent: 'space-between',
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  metricIconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  metricTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  metricContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  metricValues: {
    flexDirection: 'column',
  },
  metricValue: {
    fontSize: 20,
    fontWeight: '700',
  },
  metricUnit: {
    fontSize: 12,
    marginTop: 2,
  },
  miniChartContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 30,
  },
  miniChartBar: {
    borderRadius: 1,
  },
  metricFooter: {
    
  },
  progressContainer: {
    marginTop: 4,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 10,
    textAlign: 'right',
  },
  viewHistoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  viewHistoryText: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 8,
  }
}); 