/**
 * ProfileStatsCard Component
 * A simplified version of StatsCard for the profile page
 */
import React, { useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  Platform,
  TouchableOpacity
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Profile } from '../utils/types';
import { withSafeWebTransform } from '@/utils/platformUtils';
import { SafeWebLink } from '@/utils/webLinkFix';
import { Feather } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';

interface ProfileStatsCardProps {
  profile: Profile;
}

export default function ProfileStatsCard({ profile }: ProfileStatsCardProps) {
  const { isDark, colors } = useTheme();
  const router = useRouter();
  
  useEffect(() => {
    console.log('ProfileStatsCard profile:', {
      height: profile?.height,
      weight: profile?.weight,
      daily_calorie_goal: profile?.daily_calorie_goal,
      body_fat: profile?.body_fat
    });
  }, [profile]);
  
  // Mock data for trends (would normally come from actual historical data)
  const trends = {
    height: 'stable',
    weight: 'down',
    daily_calorie_goal: 'up',
    body_fat: 'down'
  };
  
  // Define percentage goals and ranges for visual indicators
  const getPercentageForStat = (statId: string, value: string | number) => {
    if (value === '--' || !value) return 0;
    
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    
    switch(statId) {
      case 'height':
        // Average height range 5'0" - 6'6" (60-78 inches)
        // This is simplified - would need proper parsing for height formats
        return typeof value === 'string' ? 50 : Math.min(Math.max((numValue - 60) / (78 - 60), 0), 1) * 100;
      case 'weight': 
        // Example range 100-250 lbs
        return Math.min(Math.max((numValue - 100) / (250 - 100), 0), 1) * 100;
      case 'daily_calorie_goal':
        // Example range 1200-3000 calories
        return Math.min(Math.max((numValue - 1200) / (3000 - 1200), 0), 1) * 100;
      case 'body_fat':
        // Range 5-40%
        return Math.min(Math.max((numValue - 5) / (40 - 5), 0), 1) * 100;
      default:
        return 50;
    }
  };
  
  // Get color based on trend
  const getTrendColor = (trend: string) => {
    if (trend === 'up') return '#10B981';
    if (trend === 'down') return '#EF4444';
    return '#F59E0B';
  };
  
  // Get background gradient for stat card based on type
  const getStatGradient = (statId: string) => {
    if (isDark) {
      switch(statId) {
        case 'height': return ['#3B82F615', '#1E40AF10'] as const;
        case 'weight': return ['#8B5CF615', '#6D28D910'] as const;
        case 'daily_calorie_goal': return ['#F59E0B15', '#D9770610'] as const;
        case 'body_fat': return ['#10B98115', '#04785710'] as const;
        default: return ['#3B82F615', '#1E40AF10'] as const;
      }
    } else {
      switch(statId) {
        case 'height': return ['#EFF6FF', '#DBEAFE'] as const;
        case 'weight': return ['#F5F3FF', '#EDE9FE'] as const;
        case 'daily_calorie_goal': return ['#FFFBEB', '#FEF3C7'] as const;
        case 'body_fat': return ['#ECFDF5', '#D1FAE5'] as const;
        default: return ['#EFF6FF', '#DBEAFE'] as const;
      }
    }
  };
  
  // Get icon color based on stat type
  const getStatColor = (statId: string) => {
    switch(statId) {
      case 'height': return '#3B82F6';
      case 'weight': return '#8B5CF6';
      case 'daily_calorie_goal': return '#F59E0B';
      case 'body_fat': return '#10B981';
      default: return '#3B82F6';
    }
  };
  
  // Get trend icon based on trend
  const renderTrendIcon = (trend: string) => {
    const color = getTrendColor(trend);
    const size = 16;
    
    switch(trend) {
      case 'up': return <Feather name="chevron-up" color={color} />;
      case 'down': return <Feather name="chevron-down" color={color} />;
      default: return null;
    }
  };
  
  // Helper for stat display
  const formatStatValue = (statId: string, value: string | number) => {
    if (value === '--' || !value) return 'Not Set';
    
    switch(statId) {
      case 'daily_calorie_goal':
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") + ' cal';
      case 'weight':
        return value + ' lbs';
      case 'body_fat':
        return value + '%';
      default:
        return value;
    }
  };
  
  // Simplify the stats for display only
  const stats = [
    {
      id: 'height',
      label: 'Height',
      value: profile?.height || '--',
      unit: '',
      description: 'Your current height measurement'
    },
    {
      id: 'weight',
      label: 'Weight',
      value: profile?.weight || '--',
      unit: 'lbs',
      description: 'Your current weight measurement'
    },
    {
      id: 'daily_calorie_goal',
      label: 'Calorie Goal',
      value: profile?.daily_calorie_goal || '--',
      unit: 'cal',
      description: 'Your daily calorie intake goal'
    },
    {
      id: 'body_fat',
      label: 'Body Fat',
      value: profile?.body_fat || '--',
      unit: '%',
      description: 'Your body fat percentage'
    }
  ];
  
  console.log('Rendered stats:', stats);
  
  return (
    <View style={[styles.container, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>Body Statistics</Text>
        <TouchableOpacity 
          onPress={() => router.push('/profile-screens/edit-profile')}
        >
          <Text style={[styles.editText, { color: colors.primary }]}>
            Edit
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.statsGrid}>
        {stats.map((stat) => (
          <View key={stat.id} style={styles.statCard}>
            <LinearGradient
              colors={getStatGradient(stat.id)}
              style={[
                styles.statCardInner,
                { borderColor: isDark ? 'transparent' : `${getStatColor(stat.id)}20` }
              ]}
            >
              <View style={styles.statHeader}>
                <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                  {stat.label}
                </Text>
                <View style={styles.infoButton}>
                  <Feather name="info" size={14} color={getStatColor(stat.id)} />
                </View>
              </View>
              
              <View style={styles.statValueContainer}>
                <Text style={[
                  styles.statValue, 
                  { color: colors.text },
                  stat.value === '--' && { color: colors.textSecondary, fontSize: 16 }
                ]}>
                  {formatStatValue(stat.id, stat.value)}
                </Text>
                {trends[stat.id] !== 'stable' && (
                  <View style={styles.trendContainer}>
                    <View>{renderTrendIcon(trends[stat.id])}</View>
                  </View>
                )}
              </View>
              
              {stat.value !== '--' && (
                <View style={styles.progressContainer}>
                  <View style={[styles.progressBar, { backgroundColor: `${getStatColor(stat.id)}15` }]}>
                    <View 
                      style={[
                        styles.progressFill, 
                        { 
                          width: `${getPercentageForStat(stat.id, stat.value)}%`,
                          backgroundColor: getStatColor(stat.id)
                        }
                      ]} 
                    />
                  </View>
                </View>
              )}
            </LinearGradient>
          </View>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  editText: {
    fontSize: 14,
    fontWeight: '600',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 8,
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%',
    marginBottom: 12,
  },
  statCardInner: {
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    height: 120,
    justifyContent: 'space-between',
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  infoButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
  },
  trendContainer: {
    marginLeft: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendIndicator: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 2,
  },
  progressContainer: {
    marginTop: 4,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  allStatsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  allStatsText: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 8,
  }
}); 