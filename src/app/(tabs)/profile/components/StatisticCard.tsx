import React from 'react';
import { StyleSheet, View, Text, Platform } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , Ionicons , MaterialIcons } from '@expo/vector-icons';

interface StatisticCardProps {
  title: string;
  value?: number | string;
  icon: string;
  color: string;
  description?: string;
  date?: string;
}

export function StatisticCard({ title, value, icon, color, description, date }: StatisticCardProps) {
  const { isDark, colors } = useTheme();

  const renderIcon = () => {
    const props = { size: 20, color: color };
    
    switch (icon) {
      case 'utensils': return <MaterialIcons name="restaurant"  size={20}  color={colors.text} />;
      case 'droplet': return <Ionicons name="water"  size={20}  color={colors.text} />;
      case 'fire': return <MaterialIcons name="local-fire-department"  size={20}  color={colors.text} />;
      case 'activity': return <Feather name="activity"  size={20}  color={colors.text} />;
      case 'award': return <Feather name="award"  size={20}  color={colors.text} />;
      default: return <Feather name="activity"  size={20}  color={colors.text} />;
    }
  };

  return (
    <View style={[
      styles.card, 
      { backgroundColor: colors.card },
      Platform.OS === 'web' ? { transform: [] } : {},
    ]}>
      <View style={[
        styles.header,
        Platform.OS === 'web' ? { transform: [] } : {},
      ]}>
        {renderIcon()}
        <Text style={[styles.title, { color: colors.textSecondary }]}>{title}</Text>
      </View>
      {value !== undefined && (
        <Text style={[styles.value, { color: colors.text }]}>{value}</Text>
      )}
      {description && (
        <Text style={[styles.description, { color: colors.textSecondary }]}>{description}</Text>
      )}
      {date && (
        <Text style={[styles.date, { color: colors.textSecondary }]}>{date}</Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  value: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  description: {
    fontSize: 12,
    marginTop: 4,
  },
  date: {
    fontSize: 11,
    marginTop: 8,
  }
});

export default StatisticCard; 