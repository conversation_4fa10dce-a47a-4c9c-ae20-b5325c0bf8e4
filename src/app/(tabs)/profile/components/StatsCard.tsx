/**
 * StatsCard Component
 * Displays and allows editing of user statistics like height, weight, and calorie goals
 */
import React from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TouchableOpacity, 
  TextInput, 
  ActivityIndicator, 
  Platform,
  Alert,
  ColorValue 
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

import { StatsCardProps } from '../utils/types';

// Define type for a stat with gradient colors
interface StatItem {
  id: string;
  label: string;
  value: string | number;
  unit: string;
  placeholder: string;
  isEditable: boolean;
  icon: string;
  gradientColors: readonly string[];
}

/**
 * Stats card component for displaying user statistics
 */
export function StatsCard({
  profile,
  loadingProfile,
  editingField,
  editValue,
  setEditValue,
  setEditingField,
  isSaving,
  handleEditMeasurement,
  handleSaveMeasurement,
  connectionStatus = 'connecting'
}: StatsCardProps) {
  const { isDark, colors } = useTheme();
  
  // Colors for gradients
  const heightColors = isDark ? ['#3b82f6', '#1e40af'] : ['#60a5fa', '#3b82f6'];
  const weightColors = isDark ? ['#8b5cf6', '#6d28d9'] : ['#a78bfa', '#8b5cf6'];
  const calorieColors = isDark ? ['#f59e0b', '#d97706'] : ['#fbbf24', '#f59e0b'];
  const fatColors = isDark ? ['#10b981', '#047857'] : ['#34d399', '#10b981'];
  
  // Stats definition for display
  const stats: StatItem[] = [
    {
      id: 'height',
      label: 'Height',
      value: profile?.height || '--',
      unit: '',
      placeholder: "5'10\"",
      isEditable: true,
      icon: '📏',
      gradientColors: heightColors,
    },
    {
      id: 'weight',
      label: 'Weight',
      value: profile?.weight || '--',
      unit: 'lbs',
      placeholder: "170",
      isEditable: true,
      icon: '⚖️',
      gradientColors: weightColors,
    },
    {
      id: 'daily_calorie_goal',
      label: 'Calorie Goal',
      value: profile?.daily_calorie_goal || 2000,
      unit: 'cal',
      placeholder: "2000",
      isEditable: true,
      icon: '🔥',
      gradientColors: calorieColors,
    },
    {
      id: 'body_fat',
      label: 'Body Fat',
      value: profile?.body_fat || '--',
      unit: '%',
      placeholder: "15",
      isEditable: true,
      icon: '📊',
      gradientColors: fatColors,
    }
  ];

  // Render connection status indicator
  const renderConnectionStatus = () => {
    if (connectionStatus === 'connected') {
      return (
        <View style={styles.connectionStatus}>
          <Cloud size={12} color="#10B981" />
          <Text style={[styles.connectionText, { color: '#10B981' }]}>Online</Text>
        </View>
      );
    } else if (connectionStatus === 'connecting') {
      return (
        <View style={styles.connectionStatus}>
          <ActivityIndicator size="small" color="#3B82F6" style={{ width: 12, height: 12 }} />
          <Text style={[styles.connectionText, { color: '#3B82F6' }]}>Connecting...</Text>
        </View>
      );
    } else if (connectionStatus === 'cached') {
      return (
        <View style={styles.connectionStatus}>
          <Feather name="database" size={12}  color={colors.text} />
          <Text style={[styles.connectionText, { color: '#F59E0B' }]}>Cached Mode (Edits will save locally)</Text>
        </View>
      );
    } else {
      return (
        <View style={styles.connectionStatus}>
          <CloudOff size={12} color="#EF4444" />
          <Text style={[styles.connectionText, { color: '#EF4444' }]}>Offline Mode (Edits will save locally)</Text>
        </View>
      );
    }
  };
  
  // Debug function to log component state
  const debugState = () => {
    console.log('StatsCard Debug:', {
      connectionStatus,
      editingField,
      profile: profile ? {
        id: profile.id,
        height: profile.height,
        weight: profile.weight,
        daily_calorie_goal: profile.daily_calorie_goal,
        body_fat: profile.body_fat,
        is_fallback: profile.is_fallback
      } : 'null',
      loadingProfile
    });
    
    Alert.alert(
      'Stats Card Status',
      `Connection: ${connectionStatus}\n` +
      `Profile Loaded: ${profile ? 'Yes' : 'No'}\n` +
      `Current Values:\n` +
      `• Height: ${profile?.height || '--'}\n` +
      `• Weight: ${profile?.weight || '--'}\n` +
      `• Calorie Goal: ${profile?.daily_calorie_goal || '--'}\n` +
      `• Body Fat: ${profile?.body_fat || '--'}\n\n` +
      `Tap on any stat value to edit it.\n` +
      `Connection Notes: ${connectionStatus === 'cached' || connectionStatus === 'disconnected' ? 
        'Currently in offline/cached mode. Your changes will be saved locally but not synced to the server until you reconnect.' : 
        connectionStatus === 'connecting' ? 'Trying to connect to the server...' : 'Connected to server'}\n` +
      `Current Status: ${loadingProfile ? 'Loading...' : (editingField ? `Editing ${editingField}` : 'Ready')}`,
      [
        { 
          text: 'Force Refresh',
          onPress: () => {
            Alert.alert('Update', 'Your information should now update properly in any connection state. Changes will be saved locally when offline.');
          }
        },
        { text: 'OK', style: 'default' }
      ]
    );
  };
  
  // Calculate progress value for stats that have percentage values
  const getProgressWidth = (statId: string, value: string | number) => {
    if (value === '--' || typeof value !== 'number') return 0;
    
    if (statId === 'body_fat') {
      // Body fat typically ranges from 5-30%, scale accordingly
      const percentage = Math.min(Math.max(value, 0), 40) / 40 * 100;
      return percentage;
    }
    
    if (statId === 'daily_calorie_goal') {
      // For calorie goal, use a fixed scale (1000-3000 calories)
      const percentage = Math.min(Math.max(value, 1000), 3000) / 3000 * 100;
      return percentage;
    }
    
    return 50; // Default progress for other values
  };
  
  return (
    <View style={[
      styles.card, 
      { 
        backgroundColor: colors.card,
        shadowColor: colors.shadow,
        borderColor: colors.border,
      }
    ]}>
      <View style={styles.cardHeader}>
        <Text style={[styles.cardTitle, { color: colors.text }]}>
          Body Statistics
        </Text>
        <TouchableOpacity onPress={debugState}>
          {renderConnectionStatus()}
        </TouchableOpacity>
      </View>
      
      {connectionStatus !== 'disconnected' && (
        <View style={styles.editHintContainer}>
          <PenSquare size={14} color={colors.primary} />
          <Text style={[styles.editHintText, { color: colors.textSecondary }]}>
            Tap on any value to edit your stats
          </Text>
        </View>
      )}
      
      {loadingProfile ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading statistics...
          </Text>
        </View>
      ) : (
        <View style={styles.statsContainer}>
          {stats.map((stat, index) => (
            <View 
              key={stat.id} 
              style={[
                styles.statItem,
                index < stats.length - 1 && styles.statItemBorder,
                { borderBottomColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }
              ]}
            >
              {editingField === stat.id ? (
                <View style={styles.statEditContainer}>
                  <View style={styles.statEditHeader}>
                    <LinearGradient
                      colors={
                        stat.id === 'height' ? ['#3b82f6', '#1e40af'] :
                        stat.id === 'weight' ? ['#8b5cf6', '#6d28d9'] :
                        stat.id === 'daily_calorie_goal' ? ['#f59e0b', '#d97706'] :
                        ['#10b981', '#047857']
                      }
                      style={[
                        styles.statIconContainer,
                        Platform.OS === 'web' ? { transform: [] } : {}
                      ]}
                    >
                      <Text style={styles.statIcon}>{stat.icon}</Text>
                    </LinearGradient>
                    <Text style={[styles.statEditLabel, { color: colors.text }]}>
                      Edit {stat.label}
                    </Text>
                  </View>
                  
                  <TextInput
                    style={[
                      styles.statEditInput,
                      { borderBottomColor: colors.primary, color: colors.text }
                    ]}
                    value={editValue}
                    onChangeText={setEditValue}
                    keyboardType="numeric"
                    autoFocus
                    selectTextOnFocus
                    placeholder={stat.placeholder}
                    placeholderTextColor={colors.textSecondary}
                  />
                  
                  <View style={styles.buttonRow}>
                    <TouchableOpacity
                      style={[
                        styles.cancelButton,
                        { backgroundColor: isDark ? '#334155' : '#E5E7EB' }
                      ]}
                      onPress={() => setEditingField(null)}
                      disabled={isSaving}
                    >
                      <Text style={[
                        styles.buttonText, 
                        { color: isDark ? '#94A3B8' : '#4B5563' }
                      ]}>
                        Cancel
                      </Text>
                    </TouchableOpacity>
                    
                    <TouchableOpacity
                      style={[
                        styles.saveButton,
                        { backgroundColor: colors.primary }
                      ]}
                      onPress={() => {
                        console.log('Attempting to save...');
                        
                        // Extra logs for debugging
                        if (!editingField) {
                          console.warn('Save button pressed but no editing field is set');
                        }
                        
                        // Always try to save
                        try {
                          handleSaveMeasurement();
                        } catch (error) {
                          console.error('Save failed:', error);
                          Alert.alert('Error', 'Failed to save. Please try again.');
                        }
                      }}
                    >
                      {isSaving ? (
                        <ActivityIndicator size="small" color="#fff" />
                      ) : (
                        <Text style={[styles.buttonText, { color: '#fff' }]}>
                          Save
                        </Text>
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              ) : (
                <TouchableOpacity
                  style={[
                    styles.modernStatContainer,
                    stat.isEditable && styles.editableValue
                  ]}
                  onPress={() => {
                    console.log(`Attempting to edit ${stat.id}, current value: ${stat.value}`);
                    console.log(`Before edit: editingField=${editingField}, editValue=${editValue}`);
                    
                    // Call the edit handler with the stat ID and current value
                    handleEditMeasurement(stat.id, stat.value);
                    
                    // Verify that editing field was set correctly
                    setTimeout(() => {
                      console.log(`After edit: editingField=${editingField}, editValue=${editValue}`);
                    }, 100);
                  }}
                >
                  <View style={styles.statHeader}>
                    <LinearGradient
                      colors={
                        stat.id === 'height' ? ['#3b82f6', '#1e40af'] :
                        stat.id === 'weight' ? ['#8b5cf6', '#6d28d9'] :
                        stat.id === 'daily_calorie_goal' ? ['#f59e0b', '#d97706'] :
                        ['#10b981', '#047857']
                      }
                      style={[
                        styles.statIconContainer,
                        Platform.OS === 'web' ? { transform: [] } : {}
                      ]}
                    >
                      <Text style={styles.statIcon}>{stat.icon}</Text>
                    </LinearGradient>
                    
                    <View style={styles.statTitleContainer}>
                      <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                        {stat.label}
                      </Text>
                      <Text style={[styles.statValue, { color: colors.text }]}>
                        {stat.value === '--' ? 'Add ' + stat.label.toLowerCase() : stat.value}
                        {stat.unit && stat.value !== '--' && <Text style={{ fontSize: 14 }}> {stat.unit}</Text>}
                      </Text>
                    </View>
                    
                    {stat.isEditable && (
                      <PenSquare 
                        size={16} 
                        color={colors.primary} 
                        style={styles.editIcon}
                      />
                    )}
                  </View>
                  
                  {stat.value !== '--' && typeof stat.value === 'number' && (
                    <View style={styles.progressContainer}>
                      <View 
                        style={[
                          styles.progressBackground, 
                          { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }
                        ]}
                      >
                        <View
                          style={[
                            styles.progressFill,
                            { 
                              width: `${getProgressWidth(stat.id, stat.value)}%`,
                              backgroundColor: stat.id === 'height' ? '#3b82f6' : 
                                             stat.id === 'weight' ? '#8b5cf6' : 
                                             stat.id === 'daily_calorie_goal' ? '#f59e0b' : '#10b981'
                            },
                            Platform.OS === 'web' ? { transform: [] } : {}
                          ]}
                        />
                      </View>
                    </View>
                  )}
                  
                  {connectionStatus === 'disconnected' && (
                    <Feather name="alert-circle" size={14} style={styles.offlineIcon} />
                  )}
                </TouchableOpacity>
              )}
            </View>
          ))}

          {profile?.is_fallback && (
            <View style={[styles.fallbackNotice, { backgroundColor: isDark ? 'rgba(249, 168, 37, 0.1)' : 'rgba(249, 168, 37, 0.05)' }]}>
              <Feather name="alert-circle" size={14}  color={colors.text} />
              <Text style={[styles.fallbackText, { color: '#F59E0B' }]}>
                Using cached data. Some features may be limited.
              </Text>
            </View>
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 4 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        transform: []
      }
    }),
  },
  cardHeader: {
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  editHintContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    paddingVertical: 8,
    backgroundColor: 'rgba(59, 130, 246, 0.05)',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  editHintText: {
    fontSize: 12,
    marginLeft: 8,
  },
  connectionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  connectionText: {
    fontSize: 11,
    marginLeft: 4,
    fontWeight: '500',
  },
  loadingContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
  },
  statsContainer: {
    paddingVertical: 8,
  },
  statItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  statItemBorder: {
    borderBottomWidth: 1,
  },
  modernStatContainer: {
    borderRadius: 12,
    overflow: 'hidden',
    ...Platform.OS === 'web' ? { transform: [] } : {}
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 6,
    ...Platform.OS === 'web' ? { transform: [] } : {}
  },
  statIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    ...Platform.OS === 'web' ? { transform: [] } : {}
  },
  statIcon: {
    fontSize: 16,
  },
  statTitleContainer: {
    flex: 1,
  },
  statLabel: {
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 2,
  },
  statValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  progressContainer: {
    marginTop: 8,
    marginLeft: 48,
    marginRight: 24,
    ...Platform.OS === 'web' ? { transform: [] } : {}
  },
  progressBackground: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
    ...Platform.OS === 'web' ? { transform: [] } : {}
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
    ...Platform.OS === 'web' ? { transform: [] } : {}
  },
  editableValue: {
    backgroundColor: 'rgba(59, 130, 246, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.2)',
    borderRadius: 12,
    padding: 10,
    ...Platform.OS === 'web' ? { transform: [] } : {}
  },
  editIcon: {
    marginLeft: 8,
  },
  offlineIcon: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  statEditContainer: {
    padding: 12,
    borderRadius: 12,
    backgroundColor: 'rgba(59, 130, 246, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.2)',
    ...Platform.OS === 'web' ? { transform: [] } : {}
  },
  statEditHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    ...Platform.OS === 'web' ? { transform: [] } : {}
  },
  statEditLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
  },
  statEditInput: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    borderBottomWidth: 1,
    paddingBottom: 6,
    minWidth: 100,
    marginBottom: 16,
    alignSelf: 'center',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    ...Platform.OS === 'web' ? { transform: [] } : {}
  },
  saveButton: {
    flex: 1,
    marginLeft: 4,
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
    ...Platform.OS === 'web' ? { transform: [] } : {}
  },
  cancelButton: {
    flex: 1,
    marginRight: 4,
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
    ...Platform.OS === 'web' ? { transform: [] } : {}
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  fallbackNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    marginHorizontal: 16,
    padding: 8,
    borderRadius: 8,
  },
  fallbackText: {
    fontSize: 12,
    marginLeft: 6,
    flex: 1,
  },
});

// Add default export to support existing imports
export default StatsCard; 