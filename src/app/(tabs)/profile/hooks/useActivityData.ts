/**
 * Hook for managing activity data
 */
import { useState, useEffect, useCallback } from 'react';
import { getAuth } from 'firebase/auth';
import { getDatabase, ref, get } from 'firebase/database';

interface UseActivityDataProps {
  user: any | null;
}

interface UseActivityDataResult {
  activityData: any[];
  loading: boolean;
  error: Error | null;
  refreshActivityData: () => Promise<void>;
}

export function useActivityData({ user }: UseActivityDataProps): UseActivityDataResult {
  const [activityData, setActivityData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Function to fetch activity data
  const fetchActivityData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user) {
        setActivityData([]);
        return;
      }

      
      const auth = getAuth();
      const db = getDatabase();
      
      if (!auth.currentUser) {
        throw new Error('No authenticated user found');
      }
      
      const activityRef = ref(db, `users/${auth.currentUser.uid}/activity`);
      const snapshot = await get(activityRef);
      
      if (snapshot.exists()) {
        const data = snapshot.val();
        // Convert object to array if needed
        const activityArray = Object.keys(data).map(key => ({
          id: key,
          ...data[key]
        }));
        setActivityData(activityArray);
      } else {
        // No data available
        setActivityData([]);
      }
    } catch (err) {
      console.error('Error fetching activity data:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch activity data'));
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Load data when component mounts or user changes
  useEffect(() => {
    fetchActivityData();
  }, [fetchActivityData]);

  // Function to refresh data
  const refreshActivityData = useCallback(async () => {
    await fetchActivityData();
  }, [fetchActivityData]);

  return {
    activityData,
    loading,
    error,
    refreshActivityData,
  };
}

// Default export for the hook to fix routing error
export default useActivityData; 