/**
 * Hook for managing subscription data
 */
import { useState, useEffect, useCallback } from 'react';
import { Platform, Linking } from 'react-native';
import { Firestore, collection, query, where, getDocs, limit, doc, getDoc } from 'firebase/firestore';
import { User } from 'firebase/auth';

import { UserSubscription, DatabaseResponse } from '../utils/types';
import { getCachedSubscription, cacheSubscription } from '../utils/profileCache';
import { PRODUCTS, DEFAULT_PRODUCT } from '@/stripe-config';
import { createCheckoutSession, createPortalSession } from '@/services/stripeService';

interface UseSubscriptionDataProps {
  user: User | null;
  firestore: Firestore;
}

interface UseSubscriptionDataResult {
  subscription: UserSubscription | null;
  loadingSubscription: boolean;
  error: Error | null;
  isSubscriptionActive: boolean;
  loading: boolean;
  handleSubscribe: () => Promise<void>;
  handleManageSubscription: () => Promise<void>;
}

// Get origin for Stripe callback URLs
const getOrigin = () => {
  if (Platform.OS === 'web' && typeof global.window !== 'undefined') {
    return global.window.location.origin;
  }
  return 'calorielens://';
};

/**
 * Hook for managing subscription data
 */
export function useSubscriptionData({ user, firestore }: UseSubscriptionDataProps): UseSubscriptionDataResult {
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [loadingSubscription, setLoadingSubscription] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  
  // Derived state
  const isSubscriptionActive = subscription?.subscription_status === 'active';
  
  /**
   * Fetch subscription data
   */
  const fetchSubscription = useCallback(async () => {
    if (!user) {
      setLoadingSubscription(false);
      return;
    }
    
    try {
      console.log('ProfileScreen: Fetching subscription data');
      setLoadingSubscription(true);
      
      // Special case for admin user - automatically grant subscription access
      if (user.email === '<EMAIL>') {
        console.log('ProfileScreen: Admin user detected - granting full access');
        setSubscription({
          id: 'admin-subscription',
          user_id: user.uid,
          subscription_status: 'active',
          current_period_end: Math.floor(new Date().setFullYear(new Date().getFullYear() + 10) / 1000), // 10 years from now
          product_id: 'admin-unlimited',
          is_admin: true,
        } as unknown as UserSubscription);
        setLoadingSubscription(false);
        return;
      }
      
      // Try to get from cache first
      const cachedSub = await getCachedSubscription(user.uid);
      if (cachedSub) {
        console.log('ProfileScreen: Using cached subscription data');
        setSubscription(cachedSub);
        setLoadingSubscription(false);
        
        // Update in background
        fetchSubscriptionFromDB(true);
        return;
      }
      
      // No valid cache, fetch from DB
      await fetchSubscriptionFromDB();
    } catch (error) {
      const errorObj = error instanceof Error ? error : new Error(String(error));
      console.error('ProfileScreen: Error in subscription flow:', errorObj);
      setError(errorObj);
      setSubscription(null);
      setLoadingSubscription(false);
    }
  }, [user, firestore]);
  
  /**
   * Fetch subscription from database
   */
  const fetchSubscriptionFromDB = useCallback(async (isBackground = false) => {
    if (!user) return;
    
    try {
      // Add timeout protection
      const fetchPromise = new Promise<DatabaseResponse<UserSubscription>>(async (resolve) => {
        try {
          const subscriptionsRef = collection(firestore, 'subscriptions');
          const q = query(subscriptionsRef, where('user_id', '==', user.uid), limit(1));
          const querySnapshot = await getDocs(q);
          
          if (querySnapshot.empty) {
            resolve({ data: null, error: null });
          } else {
            const doc = querySnapshot.docs[0];
            const data = doc.data() as UserSubscription;
            resolve({ data, error: null });
          }
        } catch (error) {
          resolve({ data: null, error: error as Error });
        }
      });
      
      const timeoutPromise = new Promise<DatabaseResponse<UserSubscription>>((resolve) => {
        setTimeout(() => {
          resolve({ 
            data: null, 
            error: new Error('Subscription fetch timeout') 
          });
        }, isBackground ? 20000 : 15000);
      });
      
      const result = await Promise.race([fetchPromise, timeoutPromise]);
      
      if (result.error) {
        console.error('ProfileScreen: Subscription fetch error:', result.error.message);
        
        // Only update state if not background refresh
        if (!isBackground) {
          setSubscription(null);
          setError(result.error);
        }
        return;
      }
      
      if (result.data) {
        // Only update state if it's changed or not background refresh
        if (!isBackground || JSON.stringify(result.data) !== JSON.stringify(subscription)) {
          setSubscription(result.data);
          
          // Cache the result
          cacheSubscription(user.uid, result.data);
        }
      } else if (!isBackground) {
        console.log('ProfileScreen: No subscription found');
        setSubscription(null);
      }
    } catch (error) {
      const errorObj = error instanceof Error ? error : new Error(String(error));
      console.error('ProfileScreen: Error fetching subscription:', errorObj);
      
      // Only update state if not background refresh
      if (!isBackground) {
        setError(errorObj);
        setSubscription(null);
      }
    } finally {
      // Only update loading state if not background refresh
      if (!isBackground) {
        setLoadingSubscription(false);
      }
    }
  }, [user, firestore, subscription]);
  
  /**
   * Handle subscription purchase
   */
  const handleSubscribe = useCallback(async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      
      const origin = getOrigin();
      const product = DEFAULT_PRODUCT;
      const result = await createCheckoutSession({
        priceId: product.priceId,
        mode: product.mode,
        successUrl: `${origin}profile?session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: `${origin}profile`,
      });
      
      if (result.error) {
        setError(new Error(result.error));
        return;
      }
      
      if (result.url) {
        if (Platform.OS === 'web' && typeof global.window !== 'undefined') {
          global.window.location.href = result.url;
        } else {
          Linking.openURL(result.url);
        }
      }
    } catch (error) {
      const errorObj = error instanceof Error ? error : new Error(String(error));
      console.error('Error creating checkout session:', errorObj);
      setError(errorObj);
    } finally {
      setLoading(false);
    }
  }, [user]);
  
  /**
   * Handle subscription management
   */
  const handleManageSubscription = useCallback(async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      
      if (isSubscriptionActive) {
        const origin = getOrigin();
        const { url, error } = await createPortalSession(origin);
        
        if (error) {
          console.error('Error creating portal link:', error);
          setError(new Error(error));
          return;
        }
        
        if (url) {
          if (Platform.OS === 'web' && typeof global.window !== 'undefined') {
            global.window.location.href = url;
          } else {
            Linking.openURL(url);
          }
        }
      } else {
        handleSubscribe();
      }
    } catch (error) {
      const errorObj = error instanceof Error ? error : new Error(String(error));
      console.error('Error managing subscription:', errorObj);
      setError(errorObj);
    } finally {
      setLoading(false);
    }
  }, [user, isSubscriptionActive, handleSubscribe]);
  
  // Load subscription data on mount and when user changes
  useEffect(() => {
    fetchSubscription();
  }, [fetchSubscription]);
  
  return {
    subscription,
    loadingSubscription,
    error,
    isSubscriptionActive,
    loading,
    handleSubscribe,
    handleManageSubscription,
  };
}

export default useSubscriptionData; 