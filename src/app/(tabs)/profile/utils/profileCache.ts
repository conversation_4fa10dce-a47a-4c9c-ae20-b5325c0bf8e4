/**
 * Profile caching utilities
 * Handles saving and retrieving profile data from AsyncStorage
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Profile, CacheData } from './types';

// Cache expiration constants
export const CACHE_EXPIRY = 1000 * 60 * 60 * 24; // Extend to 24 hours for better data retention
export const FALLBACK_CACHE_EXPIRY = 1000 * 60 * 30; // 30 minutes for fallback profiles

/**
 * Get cached profile data for a user
 * @param userId The user ID to get the profile for
 * @returns The cached profile or null if not found or expired
 */
export async function getCachedProfile(userId: string): Promise<Profile | null> {
  try {
    const cacheKey = `profile_${userId}`;
    const cachedData = await AsyncStorage.getItem(cacheKey);
    
    if (cachedData) {
      const { data, timestamp, isFallback } = JSON.parse(cachedData) as CacheData<Profile>;
      // Use different expiry times for real vs fallback profiles
      const expiryTime = isFallback ? FALLBACK_CACHE_EXPIRY : CACHE_EXPIRY;
      const isExpired = Date.now() - timestamp > expiryTime;
      
      if (!isExpired) {
        console.log(`ProfileScreen: Using cached profile data ${isFallback ? '(fallback)' : ''}`);
        return data;
      } else {
        console.log('ProfileScreen: Cached profile data expired');
        // Keep expired data available for reference
        return { ...data, isExpired: true };
      }
    }
    return null;
  } catch (error) {
    console.warn('ProfileScreen: Error reading from cache:', error);
    return null;
  }
}

/**
 * Cache profile data for a user
 * @param userId The user ID to cache the profile for
 * @param profileData The profile data to cache
 */
export async function cacheProfile(userId: string, profileData: Profile): Promise<void> {
  try {
    const cacheKey = `profile_${userId}`;
    const isFallback = profileData.is_fallback === true;
    
    const cacheValue = JSON.stringify({
      data: profileData,
      timestamp: Date.now(),
      isFallback
    } as CacheData<Profile>);
    
    await AsyncStorage.setItem(cacheKey, cacheValue);
    console.log(`ProfileScreen: Profile cached ${isFallback ? '(fallback)' : ''}`);
  } catch (error) {
    console.warn('ProfileScreen: Error caching profile:', error);
  }
}

/**
 * Update specific fields in the cached profile
 * @param userId The user ID to update the profile for
 * @param fieldsToUpdate The profile fields to update
 */
export async function updateCachedProfile(userId: string, fieldsToUpdate: Partial<Profile>): Promise<void> {
  try {
    const cachedProfile = await getCachedProfile(userId);
    
    if (!cachedProfile) {
      console.warn('ProfileScreen: No cached profile found to update');
      return;
    }
    
    // Update the cached profile with the new fields
    const updatedProfile = {
      ...cachedProfile,
      ...fieldsToUpdate,
      updated_at: new Date().toISOString() // Add client timestamp
    };
    
    // Re-cache the updated profile
    await cacheProfile(userId, updatedProfile);
    console.log('ProfileScreen: Cached profile updated with new fields');
  } catch (error) {
    console.warn('ProfileScreen: Error updating cached profile:', error);
  }
}

/**
 * Cache subscription data for a user
 * @param userId The user ID to cache the subscription for
 * @param subscriptionData The subscription data to cache
 */
export async function cacheSubscription(userId: string, subscriptionData: any): Promise<void> {
  try {
    const cacheKey = `subscription_${userId}`;
    
    const cacheValue = JSON.stringify({
      data: subscriptionData,
      timestamp: Date.now()
    });
    
    await AsyncStorage.setItem(cacheKey, cacheValue);
    console.log('ProfileScreen: Subscription cached');
  } catch (error) {
    console.warn('ProfileScreen: Error caching subscription:', error);
  }
}

/**
 * Get cached subscription data for a user
 * @param userId The user ID to get the subscription for
 * @returns The cached subscription or null if not found or expired
 */
export async function getCachedSubscription(userId: string): Promise<any | null> {
  try {
    const cacheKey = `subscription_${userId}`;
    const cachedData = await AsyncStorage.getItem(cacheKey);
    
    if (cachedData) {
      const { data, timestamp } = JSON.parse(cachedData);
      const isExpired = Date.now() - timestamp > CACHE_EXPIRY;
      
      if (!isExpired) {
        console.log('ProfileScreen: Using cached subscription data');
        return data;
      }
    }
    return null;
  } catch (error) {
    console.warn('ProfileScreen: Error reading subscription cache:', error);
    return null;
  }
}

/**
 * Save connection status to cache
 * @param hasConnected Whether a successful connection was established
 */
export async function saveConnectionStatus(hasConnected: boolean): Promise<void> {
  try {
    await AsyncStorage.setItem('has_connected_to_firebase', hasConnected ? 'true' : 'false');
  } catch (error) {
    console.warn('ProfileScreen: Error saving connection status:', error);
  }
}

/**
 * Get saved connection status
 * @returns Whether a successful connection was previously established
 */
export async function getConnectionStatus(): Promise<boolean> {
  try {
    const status = await AsyncStorage.getItem('has_connected_to_firebase');
    return status === 'true';
  } catch (error) {
    console.warn('ProfileScreen: Error getting connection status:', error);
    return false;
  }
}

/**
 * Clear cached profile data for a user
 * @param userId The user ID to clear the profile cache for
 */
export async function clearProfileCache(userId: string): Promise<void> {
  try {
    const cacheKey = `profile_${userId}`;
    await AsyncStorage.removeItem(cacheKey);
    console.log('ProfileScreen: Profile cache cleared');
  } catch (error) {
    console.warn('ProfileScreen: Error clearing profile cache:', error);
  }
}

export default {
  getCachedProfile,
  cacheProfile,
  updateCachedProfile,
  clearProfileCache,
  saveConnectionStatus,
  getConnectionStatus
}; 