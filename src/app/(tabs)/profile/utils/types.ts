/**
 * Profile and related data type definitions
 */


import { Router } from 'expo-router';
import React from 'react';
import { View } from 'react-native';

// User data structure
export interface User {
  id: string;
  email?: string | null;
  app_metadata?: any;
  user_metadata?: any;
  aud?: string;
  created_at?: string;
  emailVerified?: boolean;
  uid?: string;
  displayName?: string | null;
  photoURL?: string | null;
  phoneNumber?: string | null;
  isAnonymous?: boolean;
  providerData?: {
    providerId: string;
    uid: string;
    displayName: string | null;
    email: string | null;
    phoneNumber: string | null;
    photoURL: string | null;
  }[];
  metadata?: {
    creationTime?: string;
    lastSignInTime?: string;
  };
}

// Profile stats structure
export interface ProfileStats {
  meals_logged?: number;
  water_goal_achieved?: number;
  avg_calories?: number;
  active_days?: number;
  calories?: number;
  water?: number;
  meals?: number;
  steps?: number;
}

// Achievement structure
export interface Achievement {
  title: string;
  description: string;
  date: string;
  icon: string;
  color: string;
}

// Profile data structure
export interface Profile {
  id: string;
  created_at?: string;
  updated_at?: string;
  email?: string;
  avatar_url?: string | null;
  full_name?: string;
  username?: string;
  age?: number;
  location?: string;
  height?: string | number;
  weight?: number;
  daily_calorie_goal?: number;
  daily_water_goal?: number;
  body_fat?: number;
  gender?: string;
  blood_pressure?: string;
  resting_heart_rate?: number;
  sleep_hours?: number;
  activity_level?: string;
  fitness_goal?: string;
  dietary_preferences?: string;
  is_fallback?: boolean;
  stats?: ProfileStats;
  achievements?: Achievement[];
  [key: string]: any; // For any additional fields from database
}

// Database response type
export interface DatabaseResponse<T> {
  data: T | null;
  error: Error | null;
}

// Subscription data structure
export interface UserSubscription {
  id: string;
  user_id: string;
  subscription_status: 'active' | 'inactive' | 'cancelled' | 'trialing' | 'past_due';
  current_period_end: number;
  product_id: string;
  created_at?: string;
  updated_at?: string;
}

// Notification preferences
export interface NotificationPreferences {
  enabled: boolean;
  mealReminders: boolean;
  waterReminders: boolean;
  progressUpdates: boolean;
  weeklyReports: boolean;
}

// Notification preference mapping to database format
export interface NotificationPreferencesDB {
  enablePush: boolean;
  enableEmail: boolean;
  types: {
    system: boolean;
    activity: boolean;
    goal: boolean;
    reminder: boolean;
    achievement: boolean;
    social: boolean;
  };
}

// Props for ProfileHeader component
export interface ProfileHeaderProps {
  profile: Profile | null;
  user: User | null;
  loadingProfile: boolean;
  scrollY: any; // Animated.Value
  router: Router;
  onAvatarChange: (url: string) => void;
}

// Props for StatsCard component
export interface StatsCardProps {
  profile: Profile | null;
  loadingProfile: boolean;
  editingField: string | null;
  editValue: string;
  setEditValue: (value: string) => void;
  setEditingField: (field: string | null) => void;
  isSaving: boolean;
  handleEditMeasurement: (measurement: string, currentValue: string | number) => void;
  handleSaveMeasurement: () => Promise<void>;
  connectionStatus?: 'connected' | 'connecting' | 'disconnected' | 'cached';
}

// Props for PersonalInfoSection component
export interface PersonalInfoSectionProps {
  profile: Profile | null;
  user: User | null;
  loadingProfile: boolean;
  editingField: string | null;
  editValue: string;
  setEditValue: (value: string) => void;
  isSaving: boolean;
  handleStartEdit: (field: string, value: string) => void;
  handleSaveEdit: () => Promise<void>;
}

// Props for SubscriptionSection component
export interface SubscriptionSectionProps {
  subscription: UserSubscription | null;
  loadingSubscription: boolean;
  loading: boolean;
  handleSubscribe: () => Promise<void>;
  handleManageSubscription: () => Promise<void>;
  isSubscriptionActive: boolean;
}

// Props for HealthGoalsSection component
export interface HealthGoalsSectionProps {
  isDark: boolean;
  colors: any; // Theme colors
}

// Props for NotificationSettings component
export interface NotificationSettingsProps {
  notificationPreferences: NotificationPreferences;
  handleNotificationToggle: (type: keyof NotificationPreferences, value: boolean) => Promise<void>;
  isDark: boolean;
  colors: any; // Theme colors
}

// Props for MenuSection component
export interface MenuSectionProps {
  handleSignOut: () => Promise<void>;
  router: Router;
  isDark: boolean;
  colors: any; // Theme colors
}

// Cache storage format
export interface CacheData<T> {
  data: T;
  timestamp: number;
  isFallback?: boolean;
}

// Profile data fetch options
export interface ProfileFetchOptions {
  isBackground?: boolean;
  maxRetries?: number;
  initialRetryDelay?: number;
  timeoutMs?: number;
}

// Type for Promise with timeout
export interface PromiseWithTimeout<T> extends Promise<T> {
  timeout?: NodeJS.Timeout;
}

// Export a dummy value to satisfy module requirements
export const PROFILE_TYPES_VERSION = '1.0.0';

// Add a default export to prevent this file from being treated as a route
export default function ProfileTypes() {
  return React.createElement(View, { style: { display: 'none' } });
}