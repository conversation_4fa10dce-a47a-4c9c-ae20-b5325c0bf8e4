import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Text, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { App<PERSON>, Button, Card, Divider, FAB, Paragraph, SegmentedButtons, Title } from 'react-native-paper';
import { SocialShareCard } from '@/components/SocialShareCard';
import { MealShareData, ProgressShareData } from '@/services/socialSharingService';

export default function SocialScreen() {
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  const [activeTab, setActiveTab] = useState('meals');
  const [meals, setMeals] = useState<MealShareData[]>([]);
  const [achievements, setAchievements] = useState<ProgressShareData[]>([]);

  // Load example data
  useEffect(() => {
    // Sample meal data
    const sampleMeals: MealShareData[] = [
      {
        mealId: '1',
        mealName: 'Grilled Salmon with Vegetables',
        imageUri: 'https://images.unsplash.com/photo-1467003909585-2f8a72700288?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        nutritionData: {
          calories: 420,
          protein: 32,
          carbs: 18,
          fat: 12,
        },
        date: new Date().toISOString(),
        description: 'Fresh grilled salmon with a side of steamed vegetables. A perfect healthy dinner!',
      },
      {
        mealId: '2',
        mealName: 'Vegetarian Buddha Bowl',
        imageUri: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        nutritionData: {
          calories: 380,
          protein: 15,
          carbs: 45,
          fat: 8,
        },
        date: new Date(Date.now() - 86400000).toISOString(), // Yesterday
        description: 'A nutritious bowl packed with quinoa, roasted vegetables, avocado, and fresh greens.',
      },
    ];

    // Sample achievement data
    const sampleAchievements: ProgressShareData[] = [
      {
        achievementType: 'milestone',
        title: 'Completed 30-Day Challenge',
        description: 'Successfully completed 30 days of consistent healthy eating and exercise!',
        date: new Date().toISOString(),
        imageUri: 'https://images.unsplash.com/photo-1541534741688-6078c6bfb5c5?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
        stats: {
          'Workouts': 28,
          'Healthy Meals': 85,
          'Weight Lost': '4.2 lbs',
        },
      },
      {
        achievementType: 'streak',
        title: '10-Day Workout Streak',
        description: 'Completed 10 consecutive days of exercise. Keep up the great work!',
        date: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
        stats: {
          'Total Minutes': 320,
          'Calories Burned': 2850,
        },
      },
    ];

    setMeals(sampleMeals);
    setAchievements(sampleAchievements);
  }, []);

  // Handle share completion
  const handleShareComplete = () => {
    // In a real app, you might update some state or show a confirmation
    console.log('Share completed successfully');
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <StatusBar style="auto" />

      <Appbar.Header>
        <Appbar.Content title="Social Sharing" />
      </Appbar.Header>

      {/* Tab Selector */}
      <SegmentedButtons
        value={activeTab}
        onValueChange={setActiveTab}
        buttons={[
          { value: 'meals', label: 'Meals' },
          { value: 'achievements', label: 'Achievements' },
        ]}
        style={styles.segmentedButtons}
      />

      <ScrollView style={styles.content}>
        {/* Introduction Card */}
        <Card style={styles.introCard}>
          <Card.Content>
            <Title>Share Your Health Journey</Title>
            <Paragraph>
              Share your healthy meals and achievements with friends and family. Inspire others with your progress!
            </Paragraph>
          </Card.Content>
        </Card>

        <Divider style={styles.divider} />

        {/* Display appropriate content based on active tab */}
        {activeTab === 'meals' ? (
          <>
            <Title style={styles.sectionTitle}>Recent Meals</Title>
            {meals.length > 0 ? (
              meals.map((meal) => (
                <SocialShareCard
                  key={meal.mealId}
                  type="meal"
                  data={meal}
                  onShare={handleShareComplete}
                />
              ))
            ) : (
              <Card style={styles.emptyCard}>
                <Card.Content>
                  <Paragraph>No meals available to share yet. Track your first meal!</Paragraph>
                </Card.Content>
              </Card>
            )}
          </>
        ) : (
          <>
            <Title style={styles.sectionTitle}>Your Achievements</Title>
            {achievements.length > 0 ? (
              achievements.map((achievement, index) => (
                <SocialShareCard
                  key={index}
                  type="achievement"
                  data={achievement}
                  onShare={handleShareComplete}
                />
              ))
            ) : (
              <Card style={styles.emptyCard}>
                <Card.Content>
                  <Paragraph>No achievements to share yet. Complete your first goal!</Paragraph>
                </Card.Content>
              </Card>
            )}
          </>
        )}

        <View style={styles.spacer} />
      </ScrollView>

      {/* Add FAB for creating new content */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => {
          // In a real app, this would navigate to a screen to create new content
          console.log(`Create new ${activeTab === 'meals' ? 'meal' : 'achievement'}`);
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  segmentedButtons: {
    margin: 16,
  },
  content: {
    flex: 1,
    padding: 8,
  },
  introCard: {
    marginHorizontal: 8,
    marginBottom: 16,
    borderRadius: 12,
  },
  divider: {
    marginVertical: 16,
  },
  sectionTitle: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  emptyCard: {
    margin: 16,
    padding: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#007AFF',
  },
  spacer: {
    height: 80, // Space for FAB
  },
}); 