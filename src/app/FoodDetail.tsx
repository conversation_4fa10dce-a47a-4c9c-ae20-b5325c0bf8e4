import React, { useState, useEffect, useRef } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Image, 
  ScrollView, 
  TouchableOpacity, 
  ActivityIndicator,
  Alert
, Dimensions } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { getHistoryItem, updateHistoryItem, deleteFromHistory } from '@/services/foodCacheService';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { ShareOptionsModal } from '@/components/ScanComponents/ShareOptionsModal';
import { shareFoodAnalysis } from '@/services/sharing/foodSharingService';

// Define interfaces needed for this component
interface FoodItem {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  estimatedAmount?: string;
  fiber?: number;
  allergens?: string[];
  ingredients?: string[];
}

interface FoodAnalysisData {
  id: string;
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  description?: string;
  items: FoodItem[];
  servingInfo?: {
    servingSize: string;
    totalServings: number;
    caloriesPerServing: number;
    proteinPerServing: number;
    carbsPerServing: number;
    fatPerServing: number;
    fiberPerServing?: number;
    sugarPerServing?: number;
    sodiumPerServing?: number;
  };
  healthHighlights?: {
    positives: string[];
    considerations: string[];
  };
  preparationMethod?: string;
  cuisineType?: string;
  mealType?: string;
  allergens?: string[];
  imageUri: string;
  date: string; // ISO date string
  isSaved: boolean;
  dishName?: string;
}

// Meal type options
const MEAL_TYPES = [
  { id: 'breakfast', label: 'Breakfast', icon: 'sunny-outline' },
  { id: 'lunch', label: 'Lunch', icon: 'cafe-outline' },
  { id: 'dinner', label: 'Dinner', icon: 'moon-outline' },
  { id: 'snack', label: 'Snack', icon: 'nutrition-outline' }
];

// Simple Pie Chart Component to replace the imported PieChart
const MacroNutrientChart = ({ data, width }) => {
  const { colors } = useTheme();
  
  // Calculate total for percentages
  const total = data.reduce((sum, item) => sum + item.percentage, 0);
  
  return (
    <View style={{ width, alignItems: 'center' }}>
      <View style={styles.pieChartContainer}>
        {data.map((item, index) => {
          const angle = (item.percentage / total) * 360;
          
          return (
            <View key={index} style={styles.legendItem}>
              <View style={[styles.colorIndicator, { backgroundColor: item.color }]} />
              <Text style={[styles.legendText, { color: colors.text }]}>
                {item.name}: {item.percentage}%
              </Text>
            </View>
          );
        })}
      </View>
      
      <View style={styles.macroBarContainer}>
        {data.map((item, index) => (
          <View 
            key={index}
            style={[
              styles.macroBar,
              { 
                backgroundColor: item.color,
                width: `${(item.percentage / total) * 100}%`
              }
            ]}
          />
        ))}
      </View>
    </View>
  );
};

export default function FoodDetailScreen() {
  const { colors, isDark } = useTheme();
  const route = useRoute();
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const { width } = Dimensions.get('window');
  
  const [food, setFood] = useState<FoodAnalysisData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectingMealType, setSelectingMealType] = useState(false);
  const [shareModalVisible, setShareModalVisible] = useState(false);
  const detailViewRef = useRef(null);
  
  const foodId = (route.params as any)?.foodId;
  
  useEffect(() => {
    loadFoodDetails();
  }, [foodId]);
  
  const loadFoodDetails = async () => {
    if (!foodId) {
      navigation.goBack();
      return;
    }
    
    setLoading(true);
    try {
      // Get food details from cache
      const details = await getHistoryItem(foodId);
      if (!details) {
        Alert.alert('Error', 'Food item not found');
        navigation.goBack();
        return;
      }
      
      setFood(details);
    } catch (error) {
      console.error('Error loading food details:', error);
      Alert.alert('Error', 'Failed to load food details');
    } finally {
      setLoading(false);
    }
  };
  
  const handleChangeMealType = async (mealType: string) => {
    if (!food) return;
    
    try {
      const updatedFood = { ...food, mealType };
      await updateHistoryItem(updatedFood);
      setFood(updatedFood);
      setSelectingMealType(false);
    } catch (error) {
      console.error('Error updating meal type:', error);
      Alert.alert('Error', 'Failed to update meal type');
    }
  };
  
  const handleDelete = async () => {
    if (!food) return;
    
    Alert.alert(
      'Confirm Delete',
      'Are you sure you want to delete this food item from your history?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteFromHistory(food.id);
              navigation.goBack();
            } catch (error) {
              console.error('Error deleting food item:', error);
              Alert.alert('Error', 'Failed to delete food item');
            }
          }
        }
      ]
    );
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString(undefined, {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  const getMealTypeIcon = (mealType?: string) => {
    if (!mealType) return 'restaurant-outline';
    
    const type = mealType.toLowerCase();
    if (type === 'breakfast') return 'sunny-outline';
    if (type === 'lunch') return 'cafe-outline';
    if (type === 'dinner') return 'moon-outline';
    if (type === 'snack') return 'nutrition-outline';
    
    return 'restaurant-outline';
  };
  
  const calculateMacroPercentages = () => {
    if (!food) return { protein: 0, carbs: 0, fat: 0 };
    
    const proteinCalories = (food.protein || 0) * 4;
    const carbsCalories = (food.carbs || 0) * 4;
    const fatCalories = (food.fat || 0) * 9;
    const totalCalories = proteinCalories + carbsCalories + fatCalories;
    
    if (totalCalories === 0) return { protein: 33, carbs: 34, fat: 33 }; // Default even distribution
    
    return {
      protein: Math.round((proteinCalories / totalCalories) * 100),
      carbs: Math.round((carbsCalories / totalCalories) * 100),
      fat: Math.round((fatCalories / totalCalories) * 100)
    };
  };
  
  // If loading, show spinner
  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.text }]}>
          Loading food details...
        </Text>
      </View>
    );
  }
  
  // If no food data, show error
  if (!food) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: colors.background }]}>
        <Ionicons name="alert-circle-outline" size={48} color={colors.error} />
        <Text style={[styles.errorText, { color: colors.text }]}>
          Food item not found
        </Text>
        <TouchableOpacity 
          style={[styles.backButton, { backgroundColor: colors.primary }]}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  // Calculate macronutrient data for chart
  const macros = calculateMacroPercentages();
  const chartData = [
    {
      name: 'Protein',
      percentage: macros.protein,
      color: '#4CAF50',
      legendFontColor: colors.text,
      legendFontSize: 13
    },
    {
      name: 'Carbs',
      percentage: macros.carbs,
      color: '#2196F3',
      legendFontColor: colors.text,
      legendFontSize: 13
    },
    {
      name: 'Fat',
      percentage: macros.fat,
      color: '#FF9800',
      legendFontColor: colors.text,
      legendFontSize: 13
    }
  ];
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      
      {/* Header Image */}
      <View style={styles.headerContainer}>
        {food.imageUri ? (
          <Image 
            source={{ uri: food.imageUri }} 
            style={styles.headerImage}
            resizeMode="cover"
          />
        ) : (
          <View style={[styles.headerPlaceholder, { backgroundColor: colors.border }]}>
            <Ionicons name="image-outline" size={48} color={colors.primary} />
          </View>
        )}
        
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.7)']}
          style={styles.headerGradient}
        />
        
        <View style={[styles.headerBar, { paddingTop: insets.top }]}>
          <TouchableOpacity 
            style={styles.backIconButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.deleteIconButton}
            onPress={handleDelete}
          >
            <Ionicons name="trash-outline" size={24} color="white" />
          </TouchableOpacity>
        </View>
        
        <View style={styles.titleContainer}>
          <Text style={styles.title}>
            {food.dishName || food.name || 'Food Item'}
          </Text>
          
          <View style={styles.dateContainer}>
            <Ionicons name="calendar-outline" size={16} color="white" />
            <Text style={styles.dateText}>
              {formatDate(food.date)}
            </Text>
          </View>
        </View>
      </View>
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Meal Type Section */}
        <View style={[styles.sectionContainer, { backgroundColor: colors.card }]}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Meal Category
            </Text>
            
            <TouchableOpacity 
              style={styles.editButton}
              onPress={() => setSelectingMealType(!selectingMealType)}
            >
              <Ionicons 
                name={selectingMealType ? "close-outline" : "pencil-outline"} 
                size={18} 
                color={colors.primary} 
              />
            </TouchableOpacity>
          </View>
          
          {selectingMealType ? (
            <View style={styles.mealTypeSelector}>
              {MEAL_TYPES.map(type => (
                <TouchableOpacity
                  key={type.id}
                  style={[
                    styles.mealTypeOption,
                    food.mealType === type.id && { 
                      backgroundColor: colors.primary + '20',
                      borderColor: colors.primary 
                    },
                    { borderColor: colors.border }
                  ]}
                  onPress={() => handleChangeMealType(type.id)}
                >
                  <Ionicons 
                    name={type.icon as any} 
                    size={20} 
                    color={food.mealType === type.id ? colors.primary : colors.text} 
                  />
                  <Text 
                    style={[
                      styles.mealTypeOptionText, 
                      { 
                        color: food.mealType === type.id ? colors.primary : colors.text 
                      }
                    ]}
                  >
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <View style={styles.currentMealType}>
              <Ionicons 
                name={getMealTypeIcon(food.mealType)} 
                size={24} 
                color={colors.primary} 
              />
              <Text style={[styles.currentMealTypeText, { color: colors.text }]}>
                {food.mealType 
                  ? food.mealType.charAt(0).toUpperCase() + food.mealType.slice(1) 
                  : 'Not Categorized'}
              </Text>
            </View>
          )}
        </View>
        
        {/* Nutrition Summary Section */}
        <View style={[styles.sectionContainer, { backgroundColor: colors.card }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Nutrition Summary
          </Text>
          
          <View style={styles.nutritionRow}>
            <View style={styles.nutritionItem}>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>
                {food.calories || 0}
              </Text>
              <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                Calories
              </Text>
            </View>
            
            <View style={styles.nutritionItem}>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>
                {food.protein || 0}g
              </Text>
              <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                Protein
              </Text>
            </View>
            
            <View style={styles.nutritionItem}>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>
                {food.carbs || 0}g
              </Text>
              <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                Carbs
              </Text>
            </View>
            
            <View style={styles.nutritionItem}>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>
                {food.fat || 0}g
              </Text>
              <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                Fat
              </Text>
            </View>
          </View>
          
          {/* Macronutrient chart */}
          <View style={styles.chartContainer}>
            <Text style={[styles.chartTitle, { color: colors.text }]}>
              Macronutrient Breakdown
            </Text>
            
            <MacroNutrientChart data={chartData} width={width - 64} />
          </View>
          
          {food.servingInfo && (
            <View style={styles.servingInfo}>
              <Text style={[styles.servingInfoTitle, { color: colors.text }]}>
                Serving Information
              </Text>
              
              <View style={styles.servingInfoRow}>
                {food.servingInfo.servingSize && (
                  <View style={styles.servingInfoItem}>
                    <Text style={[styles.servingInfoLabel, { color: colors.textSecondary }]}>
                      Serving Size
                    </Text>
                    <Text style={[styles.servingInfoValue, { color: colors.text }]}>
                      {food.servingInfo.servingSize}
                    </Text>
                  </View>
                )}
                
                {food.servingInfo.totalServings && (
                  <View style={styles.servingInfoItem}>
                    <Text style={[styles.servingInfoLabel, { color: colors.textSecondary }]}>
                      Total Servings
                    </Text>
                    <Text style={[styles.servingInfoValue, { color: colors.text }]}>
                      {food.servingInfo.totalServings}
                    </Text>
                  </View>
                )}
              </View>
            </View>
          )}
        </View>
        
        {/* Description Section */}
        {food.description && (
          <View style={[styles.sectionContainer, { backgroundColor: colors.card }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Description
            </Text>
            <Text style={[styles.descriptionText, { color: colors.textSecondary }]}>
              {food.description}
            </Text>
          </View>
        )}
        
        {/* Health Highlights Section */}
        {food.healthHighlights && (
          (food.healthHighlights.positives?.length > 0 || food.healthHighlights.considerations?.length > 0) && (
            <View style={[styles.sectionContainer, { backgroundColor: colors.card }]}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Health Highlights
              </Text>
              
              {food.healthHighlights.positives && food.healthHighlights.positives.length > 0 && (
                <View style={styles.highlightsSection}>
                  <Text style={[styles.highlightTitle, { color: colors.success }]}>
                    Positives
                  </Text>
                  {food.healthHighlights.positives.map((item, idx) => (
                    <View key={idx} style={styles.highlightItem}>
                      <Ionicons name="checkmark-circle-outline" size={18} color={colors.success} />
                      <Text style={[styles.highlightText, { color: colors.text }]}>{item}</Text>
                    </View>
                  ))}
                </View>
              )}
              
              {food.healthHighlights.considerations && food.healthHighlights.considerations.length > 0 && (
                <View style={styles.highlightsSection}>
                  <Text style={[styles.highlightTitle, { color: colors.warning }]}>
                    Considerations
                  </Text>
                  {food.healthHighlights.considerations.map((item, idx) => (
                    <View key={idx} style={styles.highlightItem}>
                      <Ionicons name="information-circle-outline" size={18} color={colors.warning} />
                      <Text style={[styles.highlightText, { color: colors.text }]}>{item}</Text>
                    </View>
                  ))}
                </View>
              )}
            </View>
          )
        )}
        
        {/* Food Items Section */}
        {food.items && food.items.length > 0 && (
          <View style={[styles.sectionContainer, { backgroundColor: colors.card }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Food Items
            </Text>
            
            {food.items.map((item, index) => (
              <View key={index} style={styles.foodItem}>
                <View style={styles.foodItemHeader}>
                  <Text style={[styles.foodItemName, { color: colors.text }]}>
                    {item.name}
                  </Text>
                  {item.estimatedAmount && (
                    <Text style={[styles.foodItemAmount, { color: colors.textSecondary }]}>
                      {item.estimatedAmount}
                    </Text>
                  )}
                </View>
                
                <View style={styles.foodItemNutrition}>
                  <Text style={[styles.foodItemNutritionText, { color: colors.textSecondary }]}>
                    {item.calories || 0} cal • {item.protein || 0}g protein • {item.carbs || 0}g carbs • {item.fat || 0}g fat
                  </Text>
                </View>
              </View>
            ))}
          </View>
        )}
        
        {/* Additional Info Section */}
        <View style={[styles.sectionContainer, { backgroundColor: colors.card }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Additional Information
          </Text>
          
          <View style={styles.additionalInfoContainer}>
            {food.cuisineType && (
              <View style={styles.additionalInfoItem}>
                <Ionicons name="earth-outline" size={18} color={colors.primary} />
                <Text style={[styles.additionalInfoLabel, { color: colors.textSecondary }]}>
                  Cuisine Type:
                </Text>
                <Text style={[styles.additionalInfoValue, { color: colors.text }]}>
                  {food.cuisineType}
                </Text>
              </View>
            )}
            
            {food.preparationMethod && (
              <View style={styles.additionalInfoItem}>
                <Ionicons name="flame-outline" size={18} color={colors.primary} />
                <Text style={[styles.additionalInfoLabel, { color: colors.textSecondary }]}>
                  Preparation Method:
                </Text>
                <Text style={[styles.additionalInfoValue, { color: colors.text }]}>
                  {food.preparationMethod}
                </Text>
              </View>
            )}
            
            {food.allergens && food.allergens.length > 0 && (
              <View style={styles.additionalInfoItem}>
                <Ionicons name="warning-outline" size={18} color={colors.primary} />
                <Text style={[styles.additionalInfoLabel, { color: colors.textSecondary }]}>
                  Allergens:
                </Text>
                <Text style={[styles.additionalInfoValue, { color: colors.text }]}>
                  {food.allergens.join(', ')}
                </Text>
              </View>
            )}
          </View>
        </View>
      </ScrollView>
      
      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity 
          style={[styles.scanAgainButton, { borderColor: colors.border }]}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={20} color={colors.text} />
          <Text style={[styles.buttonText, { color: colors.text }]}>
            Go Back
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.saveButton, { backgroundColor: colors.primary }]}
          onPress={() => setShareModalVisible(true)}
        >
          <Ionicons name="share-outline" size={20} color="white" />
          <Text style={[styles.buttonText, { color: 'white' }]}>
            Share
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* Share Options Modal */}
      <ShareOptionsModal
        visible={shareModalVisible}
        onClose={() => setShareModalVisible(false)}
        onShareAsText={(options) => {
          if (food) {
            shareFoodAnalysis(food, food.imageUri, null, options, false)
              .then(() => setShareModalVisible(false))
              .catch(error => console.error('Error sharing:', error));
          }
        }}
        onShareAsImage={(options) => {
          if (food) {
            shareFoodAnalysis(food, food.imageUri, detailViewRef, options, true)
              .then(() => setShareModalVisible(false))
              .catch(error => console.error('Error sharing:', error));
          }
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    height: 220,
    position: 'relative',
  },
  headerImage: {
    width: '100%',
    height: '100%',
  },
  headerPlaceholder: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 100,
  },
  headerBar: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    zIndex: 10,
  },
  backIconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteIconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    position: 'absolute',
    bottom: 20,
    left: 16,
    right: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: 'white',
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
    marginBottom: 4,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    color: 'white',
    fontSize: 14,
    marginLeft: 6,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 30,
  },
  sectionContainer: {
    borderRadius: 12,
    marginBottom: 16,
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  editButton: {
    padding: 4,
  },
  currentMealType: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currentMealTypeText: {
    fontSize: 16,
    marginLeft: 12,
  },
  mealTypeSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  mealTypeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
  },
  mealTypeOptionText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  nutritionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  nutritionItem: {
    alignItems: 'center',
  },
  nutritionValue: {
    fontSize: 20,
    fontWeight: '700',
  },
  nutritionLabel: {
    fontSize: 14,
    marginTop: 4,
  },
  chartContainer: {
    marginTop: 20,
    alignItems: 'center',
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    textAlign: 'center',
  },
  servingInfo: {
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
  },
  servingInfoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  servingInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  servingInfoItem: {
    width: '48%',
  },
  servingInfoLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  servingInfoValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  descriptionText: {
    fontSize: 16,
    lineHeight: 24,
  },
  highlightsSection: {
    marginBottom: 16,
  },
  highlightTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  highlightItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  highlightText: {
    marginLeft: 8,
    fontSize: 15,
    flex: 1,
  },
  foodItem: {
    marginBottom: 12,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  foodItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  foodItemName: {
    fontSize: 16,
    fontWeight: '500',
  },
  foodItemAmount: {
    fontSize: 14,
  },
  foodItemNutrition: {
    marginTop: 4,
  },
  foodItemNutritionText: {
    fontSize: 14,
  },
  additionalInfoContainer: {
    marginTop: 4,
  },
  additionalInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  additionalInfoLabel: {
    fontSize: 14,
    marginLeft: 8,
    marginRight: 4,
  },
  additionalInfoValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    marginTop: 12,
    marginBottom: 20,
  },
  backButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  pieChartContainer: {
    width: '100%',
    alignItems: 'center',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  colorIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  legendText: {
    fontSize: 14,
  },
  macroBarContainer: {
    width: '100%',
    height: 20,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 10,
    marginTop: 8,
  },
  macroBar: {
    height: '100%',
    borderRadius: 10,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
  },
  scanAgainButton: {
    padding: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    borderRadius: 8,
  },
  saveButton: {
    padding: 12,
    borderRadius: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
}); 