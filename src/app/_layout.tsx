import React, { useEffect } from 'react';
import { Stack, Redirect, Slot, useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { ThemeProvider, useTheme } from '@/contexts/ThemeContext';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';
import { AlertProvider } from '@/hooks/useAlert';
import { TranslationProvider } from '@/contexts/TranslationContext';
import { DatabaseProvider, DatabaseTypeProviderComponent } from '@/contexts/DatabaseContext';
import { Alert, Text, View, StyleSheet } from 'react-native';
import { validateEnvironment, formatValidationErrors } from '../../infra/environment-validation';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import NavigationGuard from '@/components/NavigationGuard';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ContextualNavigationProvider from '@/components/ContextualNavigationProvider';

// Import polyfills and shims before anything else
import '@/utils/stream-shim';
import '@/utils/module-mocks';
import '@/utils/websocket-polyfill';
import '@/utils/ws-shim';
import 'react-native-url-polyfill/auto';

function RootLayoutNav() {
  const { isDark } = useTheme();
  const { user, loading } = useAuth();
  
  useFrameworkReady();

  useEffect(() => {
    // T3-style environment validation at app start
    try {
      validateEnvironment();
      console.log('✅ Environment validation passed');
    } catch (error) {
      const errorMessage = formatValidationErrors(error);
      console.error('❌ Environment validation failed:', errorMessage);
      
      // In development, show detailed error and prevent app from continuing
      if (__DEV__) {
        Alert.alert(
          '🚨 Environment Validation Failed',
          `The app cannot start due to environment configuration errors:\n\n${errorMessage}\n\nPlease fix these issues and restart the app.`,
          [{ text: 'Exit', onPress: () => process.exit(1) }]
        );
        // Also throw to prevent further execution
        throw new Error(`Environment validation failed: ${errorMessage}`);
      } else {
        // In production, still fail fast but more gracefully
        throw new Error('Environment configuration is invalid');
      }
    }
  }, []);
  
  // While authenticating, show nothing
  if (loading) {
    return null;
  }

  return (
    <>
      <Stack>
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="(auth)" options={{ headerShown: false }} />
        <Stack.Screen name="(onboarding)" options={{ headerShown: false }} />
        <Stack.Screen name="profile-screens" options={{ headerShown: false }} />
        <Stack.Screen 
          name="profile-screens/nutrition-goals" 
          options={{ headerShown: false }} 
        />
        <Stack.Screen 
          name="recipe/[id]" 
          options={{ headerShown: false }}
        />
        <Stack.Screen 
          name="recipe/alternatives" 
          options={{ headerShown: false }}
        />
        <Stack.Screen 
          name="food-logger" 
          options={{ headerShown: false, presentation: 'modal' }} 
        />
        <Stack.Screen 
          name="water-tracker" 
          options={{ headerShown: false, presentation: 'modal' }} 
        />
        <Stack.Screen 
          name="dietitian" 
          options={{ headerShown: false }} 
        />
        <Stack.Screen 
          name="nutrition-timeline" 
          options={{ headerShown: false }} 
        />
        <Stack.Screen 
          name="goal-recommendations" 
          options={{ headerShown: false }} 
        />
        <Stack.Screen 
          name="achievements" 
          options={{ headerShown: false }} 
        />
      </Stack>
      <StatusBar style={isDark ? 'light' : 'dark'} />
    </>
  );
}

// Define app-wide settings
const TRACKING_ENABLED_KEY = 'contextual_nav_tracking_enabled';
const MENU_BUTTON_ENABLED_KEY = 'contextual_nav_menu_button_enabled';
const BUTTON_POSITION_KEY = 'contextual_nav_button_position';

export default function RootLayout() {
  useFrameworkReady();
  
  // Check if user has completed onboarding
  useEffect(() => {
    // Fix for splash screen freeze - add a small delay to give JS context time to load
    const checkOnboardingTimer = setTimeout(() => {
      checkOnboardingStatus();
    }, 100);
    
    return () => clearTimeout(checkOnboardingTimer);
  }, []);

  // Load contextual navigation settings
  const [navigationSettings, setNavigationSettings] = React.useState({
    enableTracking: true,
    showMenuButton: true,
    buttonPosition: 'bottom-right' as 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left',
    excludeScreens: [
      '/(onboarding)',
      '/(auth)',
      '/contextual-navigation-settings',
      '/contextual-navigation-demo'
    ]
  });

  // Load navigation settings
  useEffect(() => {
    loadNavigationSettings();
  }, []);

  // Load saved navigation settings
  const loadNavigationSettings = async () => {
    try {
      // Load tracking enabled setting
      const trackingEnabledValue = await AsyncStorage.getItem(TRACKING_ENABLED_KEY);
      // Load menu button enabled setting
      const menuButtonEnabledValue = await AsyncStorage.getItem(MENU_BUTTON_ENABLED_KEY);
      // Load button position setting
      const buttonPositionValue = await AsyncStorage.getItem(BUTTON_POSITION_KEY);

      setNavigationSettings(prev => ({
        ...prev,
        enableTracking: trackingEnabledValue !== null ? trackingEnabledValue === 'true' : prev.enableTracking,
        showMenuButton: menuButtonEnabledValue !== null ? menuButtonEnabledValue === 'true' : prev.showMenuButton,
        buttonPosition: buttonPositionValue as any || prev.buttonPosition,
      }));
    } catch (error) {
      console.error('Failed to load navigation settings:', error);
    }
  };

  const checkOnboardingStatus = async () => {
    try {
      const hasCompletedOnboarding = await AsyncStorage.getItem('hasCompletedOnboarding');
      const router = useRouter();
      
      // Get the current path to check if we're already on the login screen or welcome screen
      // Added check for 'window' to prevent errors on native platforms
      let currentPath = '';
      try {
        // Safe access to window.location which may not exist on native platforms
        currentPath = typeof window !== 'undefined' ? window.location.pathname : '';
      } catch (e) {
        // If window.location fails, we'll default to empty string
        console.warn('Could not access window.location, defaulting path check');
      }
      
      // Don't redirect to onboarding if we're on an auth screen or the user deliberately signed out
      const isOnAuthScreen = currentPath.includes('login') || currentPath.includes('welcome') || 
                             currentPath.includes('signup') || currentPath.includes('auth');
                             
      if (hasCompletedOnboarding !== 'true' && !isOnAuthScreen) {
        router.replace('/(onboarding)');
      }
    } catch (error) {
      console.error('Failed to check onboarding status:', error);
    }
  };

  // Wrap all providers, with Translation provider as the outermost provider
  return (
    <SafeAreaProvider>
      <TranslationProvider>
        <ThemeProvider>
          <AuthProvider>
            <DatabaseProvider>
              <DatabaseTypeProviderComponent>
                <AlertProvider>
                  <ContextualNavigationProvider
                    enableTracking={navigationSettings.enableTracking}
                    showMenuButton={navigationSettings.showMenuButton}
                    excludeScreens={navigationSettings.excludeScreens}
                    buttonPosition={navigationSettings.buttonPosition}
                  >
                    <NavigationGuard
                      maxDepth={3}
                      showAlerts={__DEV__}
                      onDepthExceeded={(currentPath, targetPath, currentDepth, maxDepth) => {
                        // Log navigation depth issues
                        console.warn(
                          `Navigation depth issue detected - Current path: ${currentPath} (depth: ${currentDepth}), ` +
                          `Target path: ${targetPath}, Max allowed: ${maxDepth}`
                        );
                      }}
                      bypassAuthInDev={__DEV__}
                    >
                      <RootLayoutNav />
                    </NavigationGuard>
                  </ContextualNavigationProvider>
                </AlertProvider>
              </DatabaseTypeProviderComponent>
            </DatabaseProvider>
          </AuthProvider>
        </ThemeProvider>
      </TranslationProvider>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});