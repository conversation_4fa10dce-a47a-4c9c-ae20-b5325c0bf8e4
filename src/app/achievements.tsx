import React, { useState, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  SafeAreaView, 
  TouchableOpacity, 
  StatusBar, 
  ScrollView,
  ActivityIndicator
} from 'react-native';
import { Stack, useNavigation } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , FontAwesome , MaterialIcons } from '@expo/vector-icons';
import AchievementBadgeCard from '@/components/AchievementBadgeCard';
import { 
  getUserBadges, 
  getUserAchievements, 
  getUserRewards,
  getRewardsHistory,
  Badge,
  Achievement,
  Reward,
  getLevelInfo
} from '@/services/rewardsService';

type TabType = 'badges' | 'achievements' | 'rewards';

export default function AchievementsScreen() {
  const { colors, isDark } = useTheme();
  const navigation = useNavigation();
  
  const [activeTab, setActiveTab] = useState<TabType>('badges');
  const [loading, setLoading] = useState(true);
  const [badges, setBadges] = useState<Badge[]>([]);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [rewards, setRewards] = useState<{
    totalPoints: number;
    level: number;
    badgeCount: number;
    achievements: number;
  }>({
    totalPoints: 0,
    level: 1,
    badgeCount: 0,
    achievements: 0
  });
  const [rewardsHistory, setRewardsHistory] = useState<Reward[]>([]);
  
  useEffect(() => {
    loadData();
  }, []);
  
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load badges
      const userBadges = await getUserBadges();
      setBadges(userBadges);
      
      // Load achievements
      const userAchievements = await getUserAchievements();
      setAchievements(userAchievements);
      
      // Load rewards
      const userRewards = await getUserRewards();
      setRewards({
        totalPoints: userRewards.totalPoints,
        level: userRewards.level,
        badgeCount: userRewards.badgeCount,
        achievements: userRewards.achievements
      });
      
      // Load rewards history
      const history = await getRewardsHistory(20);
      setRewardsHistory(history);
    } catch (error) {
      console.error('Error loading achievements data:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const levelInfo = getLevelInfo(rewards.totalPoints);
  
  // Group badges by category
  const badgesByCategory = badges.reduce<Record<string, Badge[]>>((acc, badge) => {
    const category = badge.category;
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(badge);
    return acc;
  }, {});
  
  // Filter achievements by completion status
  const completedAchievements = achievements.filter(a => a.isCompleted);
  const inProgressAchievements = achievements.filter(a => !a.isCompleted && !a.isSecret);
  const secretAchievements = achievements.filter(a => a.isSecret && !a.isCompleted);
  
  const renderTabs = () => {
    return (
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'badges' && [styles.activeTab, { borderColor: colors.primary }]
          ]}
          onPress={() => setActiveTab('badges')}
        >
          <Medal 
            size={18} 
            color={activeTab === 'badges' ? colors.primary : colors.textSecondary} 
          />
          <Text
            style={[
              styles.tabText,
              { color: activeTab === 'badges' ? colors.primary : colors.textSecondary }
            ]}
          >
            Badges
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'achievements' && [styles.activeTab, { borderColor: colors.primary }]
          ]}
          onPress={() => setActiveTab('achievements')}
        >
          <FontAwesome name="trophy" size={18} color={activeTab === 'achievements' ? colors.primary : colors.textSecondary} />
          <Text
            style={[
              styles.tabText,
              { color: activeTab === 'achievements' ? colors.primary : colors.textSecondary }
            ]}
          >
            Achievements
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'rewards' && [styles.activeTab, { borderColor: colors.primary }]
          ]}
          onPress={() => setActiveTab('rewards')}
        >
          <Feather name="star" size={18} color={activeTab === 'rewards' ? colors.primary : colors.textSecondary} />
          <Text
            style={[
              styles.tabText,
              { color: activeTab === 'rewards' ? colors.primary : colors.textSecondary }
            ]}
          >
            Rewards
          </Text>
        </TouchableOpacity>
      </View>
    );
  };
  
  const renderBadgesTab = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      );
    }
    
    if (badges.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Medal size={50} color={colors.textSecondary} style={styles.emptyIcon} />
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            No badges yet. Complete challenges and achievements to earn badges!
          </Text>
        </View>
      );
    }
    
    return (
      <ScrollView style={styles.tabContent}>
        <View style={styles.badgeStats}>
          <View style={[styles.statCard, { backgroundColor: isDark ? colors.card : '#fff' }]}>
            <Text style={[styles.statValue, { color: colors.primary }]}>
              {badges.filter(b => b.dateEarned).length}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Badges Earned
            </Text>
          </View>
          <View style={[styles.statCard, { backgroundColor: isDark ? colors.card : '#fff' }]}>
            <Text style={[styles.statValue, { color: colors.primary }]}>
              {badges.length - badges.filter(b => b.dateEarned).length}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Badges Locked
            </Text>
          </View>
        </View>
        
        {Object.entries(badgesByCategory).map(([category, categoryBadges]) => (
          <View key={category} style={styles.categorySection}>
            <Text style={[styles.categoryTitle, { color: colors.text }]}>
              {category.charAt(0).toUpperCase() + category.slice(1)} Badges
            </Text>
            
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.badgesRow}>
              {categoryBadges.map(badge => (
                <View key={badge.id} style={styles.badgeCardContainer}>
                  <AchievementBadgeCard badge={badge} />
                </View>
              ))}
            </ScrollView>
          </View>
        ))}
      </ScrollView>
    );
  };
  
  const renderAchievementsTab = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      );
    }
    
    if (achievements.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <FontAwesome name="trophy" size={50} color={colors.textSecondary} style={styles.emptyIcon} />
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            No achievements available. Check back later!
          </Text>
        </View>
      );
    }
    
    return (
      <ScrollView style={styles.tabContent}>
        <View style={styles.badgeStats}>
          <View style={[styles.statCard, { backgroundColor: isDark ? colors.card : '#fff' }]}>
            <Text style={[styles.statValue, { color: colors.primary }]}>
              {completedAchievements.length}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Completed
            </Text>
          </View>
          <View style={[styles.statCard, { backgroundColor: isDark ? colors.card : '#fff' }]}>
            <Text style={[styles.statValue, { color: colors.primary }]}>
              {inProgressAchievements.length}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              In Progress
            </Text>
          </View>
        </View>
        
        {completedAchievements.length > 0 && (
          <View style={styles.achievementSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Completed
            </Text>
            
            {completedAchievements.map(achievement => (
              <View 
                key={achievement.id} 
                style={[styles.achievementCard, { backgroundColor: isDark ? colors.card : '#fff' }]}
              >
                <View style={[styles.achievementIcon, { backgroundColor: 'rgba(46, 213, 115, 0.1)' }]}>
                  <MaterialIcons name="check-circle" size={20}  color={colors.text} />
                </View>
                <View style={styles.achievementInfo}>
                  <Text style={[styles.achievementTitle, { color: colors.text }]}>
                    {achievement.name}
                  </Text>
                  <Text style={[styles.achievementDesc, { color: colors.textSecondary }]}>
                    {achievement.description}
                  </Text>
                  <Text style={[styles.achievementReward, { color: colors.primary }]}>
                    +{achievement.pointsReward} points
                  </Text>
                </View>
              </View>
            ))}
          </View>
        )}
        
        {inProgressAchievements.length > 0 && (
          <View style={styles.achievementSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              In Progress
            </Text>
            
            {inProgressAchievements.map(achievement => (
              <View 
                key={achievement.id} 
                style={[styles.achievementCard, { backgroundColor: isDark ? colors.card : '#fff' }]}
              >
                <View style={[styles.achievementIcon, { backgroundColor: 'rgba(30, 144, 255, 0.1)' }]}>
                  <Feather name="trending-up" size={20}  color={colors.text} />
                </View>
                <View style={styles.achievementInfo}>
                  <Text style={[styles.achievementTitle, { color: colors.text }]}>
                    {achievement.name}
                  </Text>
                  <Text style={[styles.achievementDesc, { color: colors.textSecondary }]}>
                    {achievement.description}
                  </Text>
                  <View style={styles.progressRow}>
                    <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
                      <View 
                        style={[
                          styles.progressFill, 
                          { 
                            backgroundColor: colors.primary,
                            width: `${Math.min((achievement.progress / achievement.target) * 100, 100)}%` 
                          }
                        ]} 
                      />
                    </View>
                    <Text style={[styles.progressText, { color: colors.textSecondary }]}>
                      {achievement.progress}/{achievement.target}
                    </Text>
                  </View>
                </View>
              </View>
            ))}
          </View>
        )}
        
        {secretAchievements.length > 0 && (
          <View style={styles.achievementSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Secret Achievements
            </Text>
            
            {secretAchievements.map(achievement => (
              <View 
                key={achievement.id} 
                style={[styles.achievementCard, { backgroundColor: isDark ? colors.card : '#fff' }]}
              >
                <View style={[styles.achievementIcon, { backgroundColor: 'rgba(155, 89, 182, 0.1)' }]}>
                  <Feather name="award" size={20}  color={colors.text} />
                </View>
                <View style={styles.achievementInfo}>
                  <Text style={[styles.achievementTitle, { color: colors.text }]}>
                    Mystery Achievement
                  </Text>
                  <Text style={[styles.achievementDesc, { color: colors.textSecondary }]}>
                    Complete special actions to unlock this secret achievement
                  </Text>
                </View>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    );
  };
  
  const renderRewardsTab = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      );
    }
    
    return (
      <ScrollView style={styles.tabContent}>
        <View style={[styles.levelCard, { backgroundColor: isDark ? colors.card : '#fff' }]}>
          <View style={styles.levelHeader}>
            <Text style={[styles.levelTitle, { color: colors.text }]}>
              Level {levelInfo.level}
            </Text>
            <Text style={[styles.pointsText, { color: colors.primary }]}>
              {rewards.totalPoints} points
            </Text>
          </View>
          
          <View style={styles.levelProgressContainer}>
            <View style={[styles.levelProgressBar, { backgroundColor: colors.border }]}>
              <View 
                style={[
                  styles.levelProgressFill, 
                  { 
                    backgroundColor: colors.primary,
                    width: `${levelInfo.progress}%` 
                  }
                ]} 
              />
            </View>
            <Text style={[styles.levelProgressText, { color: colors.textSecondary }]}>
              {levelInfo.currentPoints}/{levelInfo.nextLevelPoints - levelInfo.currentPoints} points to level {levelInfo.level + 1}
            </Text>
          </View>
        </View>
        
        <View style={styles.rewardsStatsContainer}>
          <View style={[styles.rewardsStat, { backgroundColor: isDark ? colors.card : '#fff' }]}>
            <Feather name="award" size={24} color={colors.primary} style={styles.rewardsStatIcon} />
            <Text style={[styles.rewardsStatValue, { color: colors.text }]}>
              {completedAchievements.length}
            </Text>
            <Text style={[styles.rewardsStatLabel, { color: colors.textSecondary }]}>
              Achievements
            </Text>
          </View>
          
          <View style={[styles.rewardsStat, { backgroundColor: isDark ? colors.card : '#fff' }]}>
            <Medal size={24} color="#FFD700" style={styles.rewardsStatIcon} />
            <Text style={[styles.rewardsStatValue, { color: colors.text }]}>
              {badges.filter(b => b.dateEarned).length}
            </Text>
            <Text style={[styles.rewardsStatLabel, { color: colors.textSecondary }]}>
              Badges
            </Text>
          </View>
          
          <View style={[styles.rewardsStat, { backgroundColor: isDark ? colors.card : '#fff' }]}>
            <FontAwesome name="trophy" size={24} style={styles.rewardsStatIcon} />
            <Text style={[styles.rewardsStatValue, { color: colors.text }]}>
              {rewardsHistory.length}
            </Text>
            <Text style={[styles.rewardsStatLabel, { color: colors.textSecondary }]}>
              Rewards
            </Text>
          </View>
        </View>
        
        <View style={styles.recentActivitySection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Recent Activity
          </Text>
          
          {rewardsHistory.length === 0 ? (
            <View style={styles.emptyActivity}>
              <Text style={[styles.emptyActivityText, { color: colors.textSecondary }]}>
                No recent activity. Complete challenges and achievements to earn points!
              </Text>
            </View>
          ) : (
            rewardsHistory.map((reward, index) => (
              <View 
                key={reward.id} 
                style={[
                  styles.activityItem, 
                  { backgroundColor: isDark ? colors.card : '#fff' },
                  index === rewardsHistory.length - 1 && styles.lastActivityItem
                ]}
              >
                <View style={styles.activityTimeContainer}>
                  <Feather name="clock" size={16} color={colors.textSecondary} />
                  <Text style={[styles.activityTime, { color: colors.textSecondary }]}>
                    {new Date(reward.dateEarned).toLocaleDateString()}
                  </Text>
                </View>
                
                <Text style={[styles.activityDesc, { color: colors.text }]}>
                  {reward.description}
                </Text>
                
                <Text style={[styles.activityPoints, { color: colors.primary }]}>
                  +{reward.points}
                </Text>
              </View>
            ))
          )}
        </View>
      </ScrollView>
    );
  };
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
      />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      
      <View style={styles.header}>
        <TouchableOpacity 
          onPress={() => navigation.goBack()} 
          style={styles.backButton}
        >
          <Feather name="chevron-left" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <View style={styles.titleContainer}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Achievements & Rewards
          </Text>
          <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
            Track your progress and rewards
          </Text>
        </View>
        
        <Feather name="award" size={24} color={colors.primary} />
      </View>
      
      {renderTabs()}
      
      <View style={styles.content}>
        {activeTab === 'badges' && renderBadgesTab()}
        {activeTab === 'achievements' && renderAchievementsTab()}
        {activeTab === 'rewards' && renderRewardsTab()}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 4,
  },
  titleContainer: {
    flex: 1,
    marginHorizontal: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginTop: 8,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginRight: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  activeTab: {
    borderWidth: 1,
  },
  tabText: {
    marginLeft: 8,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    marginTop: 16,
  },
  tabContent: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyIcon: {
    marginBottom: 16,
    opacity: 0.5,
  },
  emptyText: {
    textAlign: 'center',
    lineHeight: 22,
  },
  badgeStats: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginHorizontal: 4,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
  },
  categorySection: {
    marginBottom: 24,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginHorizontal: 16,
    marginBottom: 12,
  },
  badgesRow: {
    paddingLeft: 16,
    marginBottom: 8,
  },
  badgeCardContainer: {
    width: 200,
    marginRight: 12,
  },
  achievementSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginHorizontal: 16,
    marginBottom: 12,
  },
  achievementCard: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginBottom: 12,
    padding: 16,
    borderRadius: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  achievementIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  achievementInfo: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  achievementDesc: {
    fontSize: 14,
    marginBottom: 8,
  },
  achievementReward: {
    fontSize: 12,
    fontWeight: '500',
  },
  progressRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
    flex: 1,
    marginRight: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
  },
  levelCard: {
    margin: 16,
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  levelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  levelTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  pointsText: {
    fontSize: 16,
    fontWeight: '600',
  },
  levelProgressContainer: {
    marginBottom: 8,
  },
  levelProgressBar: {
    height: 12,
    borderRadius: 6,
    overflow: 'hidden',
    marginBottom: 8,
  },
  levelProgressFill: {
    height: '100%',
    borderRadius: 6,
  },
  levelProgressText: {
    fontSize: 12,
    textAlign: 'center',
  },
  rewardsStatsContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginBottom: 24,
  },
  rewardsStat: {
    flex: 1,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginHorizontal: 4,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  rewardsStatIcon: {
    marginBottom: 8,
  },
  rewardsStatValue: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  rewardsStatLabel: {
    fontSize: 12,
  },
  recentActivitySection: {
    marginBottom: 24,
  },
  emptyActivity: {
    paddingHorizontal: 16,
    paddingVertical: 24,
    alignItems: 'center',
  },
  emptyActivityText: {
    textAlign: 'center',
  },
  activityItem: {
    marginHorizontal: 16,
    marginBottom: 12,
    padding: 16,
    borderRadius: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  lastActivityItem: {
    marginBottom: 0,
  },
  activityTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  activityTime: {
    fontSize: 12,
    marginLeft: 8,
  },
  activityDesc: {
    fontSize: 14,
    marginBottom: 8,
  },
  activityPoints: {
    fontSize: 14,
    fontWeight: '600',
  },
}); 