import React from 'react';
import { BlogList } from '../../web/blog/components/BlogList';
import { useRouter } from 'expo-router';

export default function BlogScreen() {
  const router = useRouter();
  
  const handleNavigate = (route: string, params?: any) => {
    if (route === 'blog-post' && params?.slug) {
      router.push(`/blog/${params.slug}`);
    } else if (route === 'blog' && params?.category) {
      router.push(`/blog?category=${params.category}`);
    }
  };

  return (
    <BlogList 
      onNavigate={handleNavigate}
      selectedCategory={undefined}
    />
  );
}