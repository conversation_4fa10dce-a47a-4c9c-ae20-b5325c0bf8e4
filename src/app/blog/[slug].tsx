import React from 'react';
import { BlogPost } from '../../../web/blog/components/BlogPost';
import { useLocalSearchParams, useRouter } from 'expo-router';

export default function BlogPostScreen() {
  const { slug } = useLocalSearchParams();
  const router = useRouter();
  
  const handleNavigate = (route: string, params?: any) => {
    if (route === 'blog') {
      router.push('/blog');
    } else if (route === 'blog-post' && params?.slug) {
      router.push(`/blog/${params.slug}`);
    }
  };

  return (
    <BlogPost 
      slug={slug as string}
      onNavigate={handleNavigate}
    />
  );
}