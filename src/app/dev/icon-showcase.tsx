import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Ionicons, Feather, MaterialCommunityIcons, MaterialIcons, FontAwesome5 } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { router } from 'expo-router';

interface IconExample {
  name: string;
  library: string;
  component: React.ComponentType<any>;
}

// Sample of commonly used icons in the app
const iconExamples: IconExample[] = [
  { name: 'scan', library: 'Ionicons', component: Ionicons },
  { name: 'camera', library: 'Feather', component: Feather },
  { name: 'settings', library: 'Feather', component: Feather },
  { name: 'user', library: 'Feather', component: Feather },
  { name: 'home', library: 'Feather', component: Feather },
  { name: 'menu', library: 'Feather', component: Feather },
  { name: 'search', library: 'Feather', component: Feather },
  { name: 'heart', library: 'Feather', component: Feather },
  { name: 'restaurant', library: 'MaterialIcons', component: MaterialIcons },
  { name: 'fitness-center', library: 'MaterialIcons', component: MaterialIcons },
];

export default function IconShowcase() {
  const { colors, isDark } = useTheme();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <TouchableOpacity onPress={() => router.back()}>
          <Feather name="arrow-left" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Expo Vector Icons
        </Text>
      </View>

      <ScrollView style={styles.content}>
        <Text style={[styles.description, { color: colors.textSecondary }]}>
          Migration from lucide-react-native to @expo/vector-icons completed successfully.
          The app now uses mature, well-supported icon libraries.
        </Text>

        <View style={styles.iconGrid}>
          {iconExamples.map((icon, index) => {
            const IconComponent = icon.component;
            return (
              <View key={index} style={[styles.iconItem, { backgroundColor: colors.surface }]}>
                <IconComponent name={icon.name} size={32} color={colors.text} />
                <Text style={[styles.iconName, { color: colors.text }]}>{icon.name}</Text>
                <Text style={[styles.iconLibrary, { color: colors.textSecondary }]}>{icon.library}</Text>
              </View>
            );
          })}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 16,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 24,
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  iconItem: {
    width: 100,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  iconName: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 8,
    textAlign: 'center',
  },
  iconLibrary: {
    fontSize: 10,
    marginTop: 4,
    textAlign: 'center',
  },
});