import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  SafeAreaView, 
  StatusBar, 
  ActivityIndicator,
  Platform,
  Alert
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Stack, router } from 'expo-router';
import { Feather, MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useSafeNavigation } from '@/hooks/useSafeNavigation';
import { LinearGradient } from 'expo-linear-gradient';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import our components
import NutritionInteractiveChart from '@/components/DietitianComponents/NutritionInteractiveChart';
import AIAdviceCard from '@/components/DietitianComponents/AIAdviceCard';
import MealPlannerCard from '@/components/DietitianComponents/MealPlannerCard';
import DietitianOnboarding from '@/components/DietitianComponents/DietitianOnboarding';
import NutritionLessonsCard from '@/components/DietitianComponents/NutritionLessonsCard';
import PremiumDietPlansCard from '@/components/DietitianComponents/PremiumDietPlansCard';
import SubscriptionModal from '@/components/DietitianComponents/SubscriptionModal';
import NutritionistConsultationBooking from '@/components/DietitianComponents/NutritionistConsultationBooking';
import NutritionistBookingModal from '@/components/DietitianComponents/NutritionistBookingModal';
import MealPlanGroceryModal from '@/components/DietitianComponents/MealPlanGroceryModal';
import SocialSharingModal from '@/components/DietitianComponents/SocialSharingModal';

// Import services
import { getCustomizedNutritionAdvice } from '@/services/dietitianService';
import { generateFitnessGoalMealPlan } from '@/services/dietitianService';
import { getFeaturedNutritionLessons, completeLessonById } from '@/services/educationService';

// Define tab items for our nutritional sections
const TABS = [
  { id: 'overview', title: 'Nutrition Overview', icon: 'calendar' },
  { id: 'advice', title: 'Personalized Advice', icon: 'info' },
  { id: 'premium', title: 'Premium Plans', icon: 'star' },
];

interface DietPlan {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  benefits: string[];
  isPremium: boolean;
  price?: string;
  isPopular?: boolean;
}

interface Nutritionist {
  id: string;
  name: string;
  avatar: string;
  specialties: string[];
  rating: number;
  reviewCount: number;
  experience: string;
  price: string;
  availability: {
    date: string;
    slots: {
      time: string;
      isAvailable: boolean;
    }[];
  }[];
  consultationTypes: ('video' | 'chat')[];
  bio: string;
}

export default function EnhancedDietitianScreen() {
  const { colors, isDark } = useTheme();
  const insets = useSafeAreaInsets();
  const safeGoBack = useSafeNavigation().goBack;
  const [activeTab, setActiveTab] = useState('overview');
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [nutritionData, setNutritionData] = useState({
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    fiber: 0
  });
  const [dailyGoals, setDailyGoals] = useState({
    calories: 2000,
    protein: 80,
    carbs: 250,
    fat: 65,
    fiber: 30
  });
  const [advice, setAdvice] = useState<string[]>([]);
  const [mealPlan, setMealPlan] = useState<any>(null);
  const [loadingAdvice, setLoadingAdvice] = useState(false);
  const [nutritionLessons, setNutritionLessons] = useState<any[]>([]);
  
  // Subscription related state
  const [isSubscriptionModalVisible, setIsSubscriptionModalVisible] = useState(false);
  const [userSubscription, setUserSubscription] = useState<{
    isSubscribed: boolean;
    tier: 'basic' | 'premium' | 'pro';
    expiresAt: string;
  } | null>(null);

  // State for grocery list modal
  const [isGroceryModalVisible, setIsGroceryModalVisible] = useState(false);
  
  // State for social sharing modal
  const [isSharingModalVisible, setIsSharingModalVisible] = useState(false);
  const [sharingContent, setSharingContent] = useState({
    title: 'My Weekly Meal Plan',
    message: 'Check out my healthy meal plan for this week!',
    imageUrl: 'https://images.unsplash.com/photo-1498837167922-ddd27525d352'
  });
  
  // State for nutritionist booking
  const [isBookingModalVisible, setIsBookingModalVisible] = useState(false);
  const [selectedNutritionist, setSelectedNutritionist] = useState<Nutritionist | null>(null);

  useEffect(() => {
    // Check if the user has completed onboarding
    const checkOnboardingStatus = async () => {
      try {
        const onboardingCompleted = await AsyncStorage.getItem('dietitianOnboardingCompleted');
        if (onboardingCompleted === 'true') {
          setHasCompletedOnboarding(true);
        } else {
          setShowOnboarding(true);
        }
      } catch (error) {
        console.error('Error checking onboarding status:', error);
        // Default to showing onboarding if we can't check
        setShowOnboarding(true);
      }
    };

    // Check subscription status
    const checkSubscriptionStatus = async () => {
      try {
        const subscription = await AsyncStorage.getItem('userSubscription');
        if (subscription) {
          setUserSubscription(JSON.parse(subscription));
        } else {
          setUserSubscription({
            isSubscribed: false,
            tier: 'basic',
            expiresAt: ''
          });
        }
      } catch (error) {
        console.error('Error checking subscription status:', error);
        setUserSubscription({
          isSubscribed: false,
          tier: 'basic',
          expiresAt: ''
        });
      }
    };

    checkOnboardingStatus();
    checkSubscriptionStatus();
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      // Simulate fetching user's current nutrition data and goals
      // In a real app, these would come from your API/database
      setTimeout(() => {
        setNutritionData({
          calories: 1450,
          protein: 65,
          carbs: 180,
          fat: 48,
          fiber: 22
        });
        setIsLoading(false);
      }, 1000);

      // Load personalized advice
      setLoadingAdvice(true);
      const nutritionAdvice = await getCustomizedNutritionAdvice();
      setAdvice(nutritionAdvice);
      setLoadingAdvice(false);

      // Load meal plan for today
      const today = new Date().toISOString().split('T')[0];
      const todayMealPlan = await generateFitnessGoalMealPlan(today);
      setMealPlan(todayMealPlan);
      
      // Load featured nutrition lessons
      const lessons = getFeaturedNutritionLessons(2);
      setNutritionLessons(lessons);
    } catch (error) {
      console.error('Error loading dietitian data:', error);
      setIsLoading(false);
      setLoadingAdvice(false);
    }
  };

  const handleOnboardingComplete = async () => {
    try {
      await AsyncStorage.setItem('dietitianOnboardingCompleted', 'true');
      setShowOnboarding(false);
      setHasCompletedOnboarding(true);
    } catch (error) {
      console.error('Error saving onboarding status:', error);
    }
  };

  const handleOnboardingSkip = async () => {
    try {
      await AsyncStorage.setItem('dietitianOnboardingCompleted', 'true');
      setShowOnboarding(false);
      setHasCompletedOnboarding(true);
    } catch (error) {
      console.error('Error saving onboarding status:', error);
    }
  };

  const handleAdviceFeedback = (isPositive: boolean, adviceIndex: number) => {
    // In a real app, you would send this feedback to your backend
    console.log(`User ${isPositive ? 'liked' : 'disliked'} advice at index ${adviceIndex}`);
  };

  const handleRefreshAdvice = async () => {
    setLoadingAdvice(true);
    try {
      const nutritionAdvice = await getCustomizedNutritionAdvice();
      setAdvice(nutritionAdvice);
    } catch (error) {
      console.error('Error refreshing advice:', error);
    } finally {
      setLoadingAdvice(false);
    }
  };

  const handleSaveMeal = (meal: any) => {
    // In a real app, you would save this to the user's saved meals
    console.log('Saving meal:', meal.name);
  };

  const handleShareMeal = (meal: any) => {
    // In a real app, you would implement sharing functionality
    console.log('Sharing meal:', meal.name);
  };

  const handleGenerateAlternative = (mealType: string) => {
    // In a real app, you would generate an alternative meal
    console.log('Generating alternative for:', mealType);
  };

  const handleChartInfoPress = () => {
    // Show info modal about nutrition chart
    console.log('Show info about nutrition chart');
  };

  const handleLessonComplete = (lessonId: string) => {
    completeLessonById(lessonId);
    console.log(`Lesson ${lessonId} completed!`);
  };
  
  const handleOpenSubscriptionModal = () => {
    setIsSubscriptionModalVisible(true);
  };
  
  const handleCloseSubscriptionModal = () => {
    setIsSubscriptionModalVisible(false);
  };
  
  const handleSubscribe = async (plan: any) => {
    try {
      // In a real app, this would process the payment and update the subscription in your backend
      console.log(`Subscribing to ${plan.name} plan for ${plan.price}`);
      
      // Simulate a successful subscription
      const expiryDate = new Date();
      
      // Set expiry based on plan
      if (plan.id === 'monthly') {
        expiryDate.setMonth(expiryDate.getMonth() + 1);
      } else if (plan.id === 'quarterly') {
        expiryDate.setMonth(expiryDate.getMonth() + 3);
      } else if (plan.id === 'annual') {
        expiryDate.setFullYear(expiryDate.getFullYear() + 1);
      }
      
      const newSubscription = {
        isSubscribed: true,
        tier: 'premium' as 'basic' | 'premium' | 'pro',
        expiresAt: expiryDate.toISOString()
      };
      
      // Save to storage
      await AsyncStorage.setItem('userSubscription', JSON.stringify(newSubscription));
      
      // Update state
      setUserSubscription(newSubscription);
      
      // Notify user
      Alert.alert(
        'Subscription Successful',
        `You are now subscribed to the ${plan.name} plan. Thank you for your support!`,
        [{ text: 'OK' }]
      );
      
      return Promise.resolve();
    } catch (error) {
      console.error('Error processing subscription:', error);
      return Promise.reject(error);
    }
  };
  
  const handleSelectDietPlan = (plan: any) => {
    // In a real app, this would load the selected diet plan
    console.log('Selected diet plan:', plan.title);
    
    // Navigate to the diet plan details screen
    router.push({
      pathname: '/(tabs)/dietitian/diet-plan-details',
      params: { planId: plan.id }
    } as any);
  };

  const handleOpenGroceryModal = () => {
    setIsGroceryModalVisible(true);
  };

  const handleCloseGroceryModal = () => {
    setIsGroceryModalVisible(false);
  };

  const handleShareMealPlan = (mealPlanTitle: string) => {
    setSharingContent({
      title: mealPlanTitle,
      message: `Check out my ${mealPlanTitle.toLowerCase()}!`,
      imageUrl: 'https://images.unsplash.com/photo-1498837167922-ddd27525d352'
    });
    setIsSharingModalVisible(true);
  };

  const handleCloseSharingModal = () => {
    setIsSharingModalVisible(false);
  };
  
  const handleSelectNutritionist = (nutritionist: Nutritionist) => {
    setSelectedNutritionist(nutritionist);
    setIsBookingModalVisible(true);
  };
  
  const handleCloseBookingModal = () => {
    setIsBookingModalVisible(false);
  };
  
  const handleBookConsultation = async (
    nutritionist: Nutritionist, 
    date: Date, 
    time: string, 
    consultationType: 'video' | 'chat'
  ) => {
    console.log('Booking consultation:', { nutritionist, date, time, consultationType });
    return new Promise<void>((resolve) => {
      // Simulate API call
      setTimeout(() => {
        resolve();
      }, 1500);
    });
  };

  const renderOverviewTab = () => {
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading your nutrition data...
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.tabContent}>
        <NutritionInteractiveChart
          title="Today's Nutrition"
          subtitle="Your current intake vs. goals"
          nutritionData={nutritionData}
          dailyGoals={dailyGoals}
          onInfoPress={handleChartInfoPress}
        />

        <AIAdviceCard
          title="Personalized Nutrition Insights"
          advice={advice}
          isLoading={loadingAdvice}
          onFeedback={handleAdviceFeedback}
          onRefresh={handleRefreshAdvice}
        />

        <NutritionLessonsCard
          title="Learn About Nutrition"
          lessons={nutritionLessons}
          onLessonComplete={handleLessonComplete}
        />
        
        <PremiumDietPlansCard
          onSubscribe={handleOpenSubscriptionModal}
          onSelectPlan={handleSelectDietPlan}
          userSubscription={userSubscription || undefined}
        />

        {mealPlan && (
          <MealPlannerCard
            date={new Date().toISOString()}
            meals={mealPlan.meals}
            onSaveMeal={handleSaveMeal}
            onShareMeal={handleShareMeal}
            onGenerateAlternative={handleGenerateAlternative}
          />
        )}
      </View>
    );
  };

  const renderAdviceTab = () => {
    return (
      <View style={styles.tabContent}>
        <AIAdviceCard
          title="Today's Nutrition Recommendations"
          advice={advice}
          isLoading={loadingAdvice}
          onFeedback={handleAdviceFeedback}
          onRefresh={handleRefreshAdvice}
        />

        <View style={[styles.adviceCard, { backgroundColor: isDark ? '#1E293B' : '#F8FAFC' }]}>
          <Text style={[styles.adviceCardTitle, { color: colors.text }]}>
            Nutritional Goals
          </Text>
          <View style={styles.goalItem}>
            <Text style={[styles.goalLabel, { color: colors.textSecondary }]}>
              Daily Calories
            </Text>
            <Text style={[styles.goalValue, { color: colors.text }]}>
              {dailyGoals.calories} calories
            </Text>
          </View>
          <View style={styles.goalItem}>
            <Text style={[styles.goalLabel, { color: colors.textSecondary }]}>
              Protein
            </Text>
            <Text style={[styles.goalValue, { color: colors.text }]}>
              {dailyGoals.protein}g ({Math.round((dailyGoals.protein * 4 / dailyGoals.calories) * 100)}% of calories)
            </Text>
          </View>
          <View style={styles.goalItem}>
            <Text style={[styles.goalLabel, { color: colors.textSecondary }]}>
              Carbohydrates
            </Text>
            <Text style={[styles.goalValue, { color: colors.text }]}>
              {dailyGoals.carbs}g ({Math.round((dailyGoals.carbs * 4 / dailyGoals.calories) * 100)}% of calories)
            </Text>
          </View>
          <View style={styles.goalItem}>
            <Text style={[styles.goalLabel, { color: colors.textSecondary }]}>
              Fat
            </Text>
            <Text style={[styles.goalValue, { color: colors.text }]}>
              {dailyGoals.fat}g ({Math.round((dailyGoals.fat * 9 / dailyGoals.calories) * 100)}% of calories)
            </Text>
          </View>
          <TouchableOpacity
            style={[styles.adjustGoalsButton, { backgroundColor: colors.primary }]}
            onPress={() => router.push('/profile-screens/nutrition-goals')}
          >
            <Text style={styles.adjustGoalsButtonText}>
              Adjust Nutrition Goals
            </Text>
          </TouchableOpacity>
        </View>

        <View style={[styles.adviceNote, { backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)' }]}>
          <Feather name="info" size={20} color={colors.primary} style={styles.adviceNoteIcon} />
          <Text style={[styles.adviceNoteText, { color: colors.textSecondary }]}>
            These recommendations are generated based on your recorded meals and nutrition goals. Always consult with a healthcare professional for medical advice.
          </Text>
        </View>
      </View>
    );
  };
  
  const renderPremiumTab = () => {
    return (
      <View style={styles.tabContent}>
        <PremiumDietPlansCard
          onSubscribe={handleOpenSubscriptionModal}
          onSelectPlan={handleSelectDietPlan}
          userSubscription={userSubscription || undefined}
        />
      </View>
    );
  };

  if (showOnboarding) {
    return (
      <DietitianOnboarding
        onComplete={handleOnboardingComplete}
        onSkip={handleOnboardingSkip}
      />
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      
      <LinearGradient
        colors={isDark ? ['#1E293B', '#0F172A'] : ['#EEF2FF', '#E0E7FF']}
        style={[styles.header, { paddingTop: insets.top > 0 ? 0 : 16 }]}
      >
        <View style={styles.headerRow}>
          <TouchableOpacity 
            onPress={safeGoBack} 
            style={[styles.backButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
            accessibilityLabel="Go back"
            accessibilityRole="button"
          >
            <Feather name="chevron-left" size={24} color={isDark ? '#FFFFFF' : '#312E81'} />
          </TouchableOpacity>
          
          <Text style={[styles.headerTitle, { color: isDark ? '#FFFFFF' : '#312E81' }]}>
            AI Dietitian
          </Text>
          
          <TouchableOpacity 
            style={[styles.settingsButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
            accessibilityLabel="Nutrition Settings"
            accessibilityRole="button"
            onPress={() => router.push('/profile-screens/nutrition-goals')}
          >
            <Feather name="settings" size={20} color={isDark ? '#FFFFFF' : '#312E81'} />
          </TouchableOpacity>
        </View>
        
        <View style={styles.tabBar}>
          {TABS.map((tab) => {
            const isActive = activeTab === tab.id;
            return (
              <TouchableOpacity
                key={tab.id}
                style={[
                  styles.tab,
                  isActive && styles.activeTab,
                  isActive && { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }
                ]}
                onPress={() => setActiveTab(tab.id)}
                accessibilityRole="tab"
                accessibilityState={{ selected: isActive }}
                accessibilityLabel={tab.title}
              >
                <Feather
                  name={tab.icon}
                  size={18}
                  color={isActive ? (isDark ? '#FFFFFF' : '#312E81') : (isDark ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.4)')}
                  style={styles.tabIcon}
                />
                <Text
                  style={[
                    styles.tabText,
                    {
                      color: isActive
                        ? isDark ? '#FFFFFF' : '#312E81'
                        : isDark ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.4)'
                    }
                  ]}
                >
                  {tab.title}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </LinearGradient>
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {activeTab === 'overview' 
          ? renderOverviewTab() 
          : activeTab === 'advice'
            ? renderAdviceTab()
            : renderPremiumTab()}
      </ScrollView>
      
      <SubscriptionModal 
        isVisible={isSubscriptionModalVisible}
        onClose={handleCloseSubscriptionModal}
        onSubscribe={handleSubscribe}
      />
      
      <NutritionistConsultationBooking 
        onSelectNutritionist={handleSelectNutritionist}
        onViewAllNutritionists={() => console.log('View all nutritionists')}
      />
      
      <MealPlanGroceryModal
        isVisible={isGroceryModalVisible}
        onClose={handleCloseGroceryModal}
        mealPlan={mealPlan}
      />
      
      <SocialSharingModal
        isVisible={isSharingModalVisible}
        onClose={handleCloseSharingModal}
        contentType="meal-plan"
        content={{
          title: sharingContent.title,
          imageUrl: sharingContent.imageUrl,
          message: sharingContent.message
        }}
      />
      
      <NutritionistBookingModal
        isVisible={isBookingModalVisible}
        onClose={handleCloseBookingModal}
        nutritionist={selectedNutritionist}
        onBookConsultation={handleBookConsultation}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
      },
      android: {
        elevation: 4,
      },
      web: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
      }
    }),
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabBar: {
    flexDirection: 'row',
    marginTop: 8,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  activeTab: {
    borderRadius: 8,
  },
  tabIcon: {
    marginRight: 8,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  tabContent: {
    flex: 1,
  },
  adviceCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
      },
      android: {
        elevation: 3,
      },
      web: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
      }
    }),
  },
  adviceCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  goalItem: {
    marginBottom: 12,
  },
  goalLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  goalValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  adjustGoalsButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  adjustGoalsButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  adviceNote: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  adviceNoteIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  adviceNoteText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
}); 