import React, { useState, useCallback } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, SafeAreaView, Animated, StatusBar, useWindowDimensions } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Stack, Link , useRouter } from 'expo-router';
import { format } from 'date-fns';
import { DietitianSummary } from '@/components/DietitianSummary';
import MealPlanSuggestion from '@/components/MealPlanSuggestion';
import { Feather } from '@expo/vector-icons';
import { useTranslation } from '@/contexts/TranslationContext';
import { getCustomizedNutritionAdvice } from '@/services/dietitianService';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useSafeNavigation } from '@/hooks/useSafeNavigation';
import { LinearGradient } from 'expo-linear-gradient';

// Define tab items
const TABS = [
  { id: 'summary', title: 'Nutrition Summary', icon: 'calendar' },
  { id: 'mealPlan', title: 'Suggested Meal Plan', icon: 'info' },
  { id: 'advice', title: 'Personalized Advice', icon: 'shield' },
];

export default function DietitianScreen() {
  const { colors, isDark } = useTheme();
  const { t } = useTranslation();
  const { safeGoBack } = useSafeNavigation();
  const insets = useSafeAreaInsets();
  const { width } = useWindowDimensions();
  const [selectedDate, setSelectedDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [activeTab, setActiveTab] = useState('summary');
  const [personalizedAdvice, setPersonalizedAdvice] = useState<string[]>([]);
  const [loadingAdvice, setLoadingAdvice] = useState(false);
  const router = useRouter();
  
  const handleTabChange = useCallback(async (tabId: string) => {
    setActiveTab(tabId);
    
    // If selecting advice tab, load personalized advice
    if (tabId === 'advice' && personalizedAdvice.length === 0 && !loadingAdvice) {
      setLoadingAdvice(true);
      try {
        const advice = await getCustomizedNutritionAdvice();
        setPersonalizedAdvice(advice);
      } catch (error) {
        console.error('Error fetching personalized advice:', error);
      } finally {
        setLoadingAdvice(false);
      }
    }
  }, [personalizedAdvice, loadingAdvice]);
  
  const renderAdviceTab = () => {
    return (
      <View style={styles.adviceContainer}>
        {loadingAdvice ? (
          <View style={styles.loadingContainer}>
            <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
              Analyzing your profile and nutrition data...
            </Text>
          </View>
        ) : (
          <>
            <LinearGradient
              colors={isDark ? ['#1E293B', '#0F172A'] : ['#EEF2FF', '#E0E7FF']}
              style={[styles.adviceHeader, { 
                borderBottomColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                borderBottomWidth: 1,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 3,
                elevation: 2,
                marginBottom: 16,
              }]}
            >
              <Feather name="info" size={24} color={isDark ? '#60A5FA' : '#4F46E5'} style={styles.adviceIcon} />
              <Text style={[styles.adviceTitle, { color: isDark ? '#FFFFFF' : '#312E81' }]}>
                Personalized Nutrition Advice
              </Text>
            </LinearGradient>
            
            {personalizedAdvice.length > 0 ? (
              <View style={styles.adviceList}>
                {personalizedAdvice.map((advice, index) => (
                  <View key={index} style={[styles.adviceItem, { 
                    backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)',
                    borderLeftColor: colors.primary,
                    borderLeftWidth: 3,
                  }]}>
                    <Text style={[styles.adviceText, { color: colors.text }]}>
                      {advice}
                    </Text>
                  </View>
                ))}
              </View>
            ) : (
              <View style={styles.noAdviceContainer}>
                <Feather name="info" size={40} color={colors.primary} style={{ marginBottom: 16 }} />
                <Text style={[styles.noAdviceText, { color: colors.textSecondary }]}>
                  No personalized advice available. Get custom nutrition recommendations based on your profile.
                </Text>
                <TouchableOpacity
                  style={[styles.refreshButton, { 
                    backgroundColor: colors.primary,
                    shadowColor: colors.primary,
                    shadowOffset: { width: 0, height: 4 },
                    shadowOpacity: 0.2,
                    shadowRadius: 8,
                    elevation: 4,
                  }]}
                  onPress={() => handleTabChange('advice')}
                  accessibilityLabel="Get nutrition advice"
                  accessibilityRole="button"
                >
                  <Text style={styles.refreshButtonText}>
                    Get Advice
                  </Text>
                </TouchableOpacity>
              </View>
            )}
            
            <View style={styles.adviceNote}>
              <Text style={[styles.adviceNoteText, { color: colors.textSecondary }]}>
                Note: This advice is generated based on your recorded meals and nutrition goals. Consult with a healthcare professional for medical advice.
              </Text>
            </View>
          </>
        )}
      </View>
    );
  };
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      
      <LinearGradient
        colors={isDark ? ['#1E293B', '#0F172A'] : ['#EEF2FF', '#E0E7FF']}
        style={[styles.header, { paddingTop: insets.top > 0 ? 0 : 8 }]}
      >
        <View style={styles.headerRow}>
          <TouchableOpacity 
            onPress={safeGoBack} 
            style={[styles.backButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
            accessibilityLabel="Go back"
            accessibilityRole="button"
          >
            <Feather name="chevron-left" size={24} color={isDark ? '#FFFFFF' : '#312E81'} />
          </TouchableOpacity>
          
          <Text style={[styles.headerTitle, { color: isDark ? '#FFFFFF' : '#312E81' }]}>
            Dietitian
          </Text>
          
          <TouchableOpacity 
            style={[styles.settingsButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
            accessibilityLabel="Settings"
            accessibilityRole="button"
            onPress={() => router.push('/(tabs)/profile')}
          >
            <Feather name="settings" size={20} color={isDark ? '#FFFFFF' : '#312E81'} />
          </TouchableOpacity>
        </View>
        
        <View style={[styles.dateContainer, { 
          backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
        }]}>
          <Feather name="calendar" size={18} color={isDark ? '#60A5FA' : '#4F46E5'} style={styles.calendarIcon} />
          <Text style={[styles.dateText, { color: isDark ? '#FFFFFF' : '#312E81' }]}>
            {format(new Date(selectedDate), 'MMMM d, yyyy')}
          </Text>
        </View>
      </LinearGradient>
      
      <View style={[styles.tabsContainer, { 
        borderBottomColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
        shadowColor: isDark ? '#000' : '#bbb',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
        backgroundColor: isDark ? 'rgba(15, 23, 42, 0.8)' : 'rgba(255, 255, 255, 0.8)',
      }]}>
        {TABS.map(tab => (
          <TouchableOpacity
            key={tab.id}
            style={[
              styles.tabButton,
              activeTab === tab.id && [
                styles.activeTabButton, 
                { 
                  borderBottomColor: isDark ? '#60A5FA' : '#4F46E5',
                  backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)',
                }
              ]
            ]}
            onPress={() => handleTabChange(tab.id)}
            accessibilityLabel={tab.title}
            accessibilityRole="tab"
            accessibilityState={{ selected: activeTab === tab.id }}
          >
            <Feather 
              name={tab.icon}
              size={16} 
              color={activeTab === tab.id 
                ? (isDark ? '#60A5FA' : '#4F46E5') 
                : (isDark ? '#94A3B8' : '#6B7280')
              } 
              style={styles.tabIcon} 
            />
            <Text
              style={[
                styles.tabText,
                { color: isDark ? '#94A3B8' : '#6B7280' },
                activeTab === tab.id && { 
                  color: isDark ? '#60A5FA' : '#4F46E5', 
                  fontWeight: '600' 
                }
              ]}
            >
              {tab.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      
      <View style={styles.content}>
        {activeTab === 'summary' && (
          <DietitianSummary
            selectedDate={selectedDate}
            onDateChange={setSelectedDate}
            hasSubscription={true}
          />
        )}
        
        {activeTab === 'mealPlan' && (
          <MealPlanSuggestion
            selectedDate={selectedDate}
            hasSubscription={true}
          />
        )}
        
        {activeTab === 'advice' && renderAdviceTab()}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 12,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
  },
  settingsButton: {
    padding: 8,
    borderRadius: 20,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  calendarIcon: {
    marginRight: 6,
  },
  dateText: {
    fontSize: 14,
  },
  tabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  tabIcon: {
    marginRight: 6,
  },
  activeTabButton: {
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: 13,
  },
  content: {
    flex: 1,
  },
  adviceContainer: {
    flex: 1,
    padding: 16,
  },
  adviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 12,
    paddingHorizontal: 16,
    paddingTop: 16,
    borderRadius: 12,
  },
  adviceIcon: {
    marginRight: 8,
  },
  adviceTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  adviceList: {
    marginBottom: 16,
  },
  adviceItem: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  adviceText: {
    fontSize: 15,
    lineHeight: 22,
  },
  noAdviceContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  noAdviceText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 22,
  },
  refreshButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 24,
  },
  refreshButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  adviceNote: {
    marginTop: 'auto',
    padding: 16,
  },
  adviceNoteText: {
    fontSize: 12,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
  },
}); 