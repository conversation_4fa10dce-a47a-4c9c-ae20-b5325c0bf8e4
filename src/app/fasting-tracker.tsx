import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { SafeAreaView } from 'react-native-safe-area-context';
import FastingTracker from '@/components/FastingTracker';
import { Feather } from '@expo/vector-icons';

export default function FastingTrackerScreen() {
  const { colors, isDark } = useTheme();
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDark ? colors.background : '#f9f9f9' }]}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.headerContainer}>
          <Text style={[styles.title, { color: colors.text }]}>Intermittent Fasting</Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            Track your fasting periods and eating windows
          </Text>
        </View>
        
        <FastingTracker />
        
        <View style={[styles.infoSection, { backgroundColor: isDark ? colors.card : 'white' }]}>
          <View style={styles.infoHeader}>
            <Feather name="info" size={18} color={colors.primary} style={styles.infoIcon} />
            <Text style={[styles.infoTitle, { color: colors.text }]}>About Intermittent Fasting</Text>
          </View>
          
          <Text style={[styles.infoText, { color: colors.textSecondary }]}>
            Intermittent fasting is an eating pattern that cycles between periods of fasting and eating. It doesn't specify which foods to eat but rather when you should eat them.
          </Text>
          
          <View style={styles.fastingTypesContainer}>
            <View style={styles.fastingType}>
              <Text style={[styles.fastingTypeTitle, { color: colors.text }]}>16:8</Text>
              <Text style={[styles.fastingTypeDescription, { color: colors.textSecondary }]}>
                Fast for 16 hours, eat during an 8-hour window daily.
              </Text>
            </View>
            
            <View style={styles.fastingType}>
              <Text style={[styles.fastingTypeTitle, { color: colors.text }]}>18:6</Text>
              <Text style={[styles.fastingTypeDescription, { color: colors.textSecondary }]}>
                Fast for 18 hours, eat during a 6-hour window daily.
              </Text>
            </View>
            
            <View style={styles.fastingType}>
              <Text style={[styles.fastingTypeTitle, { color: colors.text }]}>5:2</Text>
              <Text style={[styles.fastingTypeDescription, { color: colors.textSecondary }]}>
                Eat normally 5 days a week, restrict calories (500-600) on 2 non-consecutive days.
              </Text>
            </View>
          </View>
          
          <Text style={[styles.disclaimerText, { color: colors.textSecondary }]}>
            Note: Consult with a healthcare professional before starting any fasting regimen, especially if you have medical conditions or are taking medications.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  headerContainer: {
    padding: 16,
    marginBottom: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 22,
  },
  infoSection: {
    margin: 16,
    padding: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoIcon: {
    marginRight: 8,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  infoText: {
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 16,
  },
  fastingTypesContainer: {
    marginBottom: 16,
  },
  fastingType: {
    marginBottom: 12,
  },
  fastingTypeTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  fastingTypeDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  disclaimerText: {
    fontSize: 14,
    fontStyle: 'italic',
    lineHeight: 20,
  },
}); 