import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { auth } from '@/lib/firebase';

export default function FirebaseAuthTestScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [user, setUser] = useState<any>(null);
  const [storedUser, setStoredUser] = useState<any>(null);
  const [status, setStatus] = useState('idle');

  // Check current auth state on mount
  useEffect(() => {
    const currentUser = auth.currentUser;
    setStoredUser(currentUser);
    if (currentUser) {
      setStatus('stored');
    }
  }, []);

  // Listen for auth state changes
  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((currentUser) => {
      setUser(currentUser);
      setStatus(currentUser ? 'authenticated' : 'unauthenticated');
    });

    // Cleanup subscription
    return () => unsubscribe();
  }, []);

  // Handle sign in
  const handleSignIn = async () => {
    if (!email || !password) {
      setError('Email and password are required');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      await auth.signInWithEmailAndPassword(email, password);
      setStatus('authenticated');
    } catch (err: any) {
      console.error('Sign in error:', err);
      setError(err.message);
      setStatus('error');
    } finally {
      setLoading(false);
    }
  };

  // Handle sign up
  const handleSignUp = async () => {
    if (!email || !password) {
      setError('Email and password are required');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      await auth.createUserWithEmailAndPassword(email, password);
      setStatus('authenticated');
    } catch (err: any) {
      console.error('Sign up error:', err);
      setError(err.message);
      setStatus('error');
    } finally {
      setLoading(false);
    }
  };

  // Handle sign out
  const handleSignOut = async () => {
    setLoading(true);
    
    try {
      await auth.signOut();
      setStatus('unauthenticated');
      setStoredUser(null);
    } catch (err: any) {
      console.error('Sign out error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.header}>Firebase Auth Test (v9)</Text>
      
      {/* Status display */}
      <View style={styles.statusContainer}>
        <Text style={styles.label}>Status:</Text>
        <Text 
          style={[
            styles.status, 
            status === 'authenticated' ? styles.authenticated : 
            status === 'stored' ? styles.stored :
            status === 'error' ? styles.error : 
            styles.unauthenticated
          ]}
        >
          {status}
        </Text>
      </View>

      {/* User info */}
      {user && (
        <View style={styles.userInfo}>
          <Text style={styles.label}>Logged in as:</Text>
          <Text style={styles.value}>{user.email}</Text>
          <Text style={styles.label}>User ID:</Text>
          <Text style={styles.value}>{user.uid}</Text>
          
          {/* Sign out button */}
          <TouchableOpacity 
            style={styles.signOutButton} 
            onPress={handleSignOut}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.buttonText}>Sign Out</Text>
            )}
          </TouchableOpacity>
        </View>
      )}

      {/* Stored user info (from AsyncStorage) */}
      {!user && storedUser && (
        <View style={[styles.userInfo, styles.storedUserInfo]}>
          <Text style={styles.storedUserTitle}>Stored User Data:</Text>
          <Text style={styles.label}>Email:</Text>
          <Text style={styles.value}>{storedUser.email}</Text>
          <Text style={styles.label}>User ID:</Text>
          <Text style={styles.value}>{storedUser.uid}</Text>
          
          {/* Sign out button */}
          <TouchableOpacity 
            style={styles.signOutButton} 
            onPress={handleSignOut}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.buttonText}>Clear Stored User</Text>
            )}
          </TouchableOpacity>
        </View>
      )}

      {/* Login/Registration form */}
      {!user && (
        <>
          {/* Error message */}
          {error ? (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          ) : null}

          {/* Email input */}
          <TextInput
            style={styles.input}
            placeholder="Email"
            value={email}
            onChangeText={setEmail}
            autoCapitalize="none"
            keyboardType="email-address"
            editable={!loading}
          />
          
          {/* Password input */}
          <TextInput
            style={styles.input}
            placeholder="Password"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
            editable={!loading}
          />
          
          {/* Buttons */}
          <View style={styles.buttonsContainer}>
            <TouchableOpacity 
              style={[styles.button, styles.signInButton]} 
              onPress={handleSignIn}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.buttonText}>Sign In</Text>
              )}
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.button, styles.signUpButton]} 
              onPress={handleSignUp}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.buttonText}>Sign Up</Text>
              )}
            </TouchableOpacity>
          </View>
        </>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingVertical: 10,
    paddingHorizontal: 15,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
  },
  label: {
    fontWeight: 'bold',
    marginRight: 5,
  },
  status: {
    fontWeight: 'bold',
  },
  authenticated: {
    color: 'green',
  },
  stored: {
    color: 'blue',
  },
  unauthenticated: {
    color: 'orange',
  },
  error: {
    color: 'red',
  },
  userInfo: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  storedUserInfo: {
    backgroundColor: '#e3f2fd',
    borderColor: '#2196f3',
    borderWidth: 1,
  },
  storedUserTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#2196f3',
  },
  value: {
    marginBottom: 15,
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  errorText: {
    color: 'red',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginBottom: 15,
    paddingHorizontal: 15,
    fontSize: 16,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  signInButton: {
    backgroundColor: '#4285F4',
    marginRight: 10,
  },
  signUpButton: {
    backgroundColor: '#34A853',
    marginLeft: 10,
  },
  signOutButton: {
    backgroundColor: '#EA4335',
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
}); 