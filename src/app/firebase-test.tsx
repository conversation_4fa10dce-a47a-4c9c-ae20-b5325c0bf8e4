import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FirebaseTest } from '@/utils/FirebaseTest';

export default function FirebaseTestScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.header}>Firebase Test Page</Text>
      <Text style={styles.subheader}>Checks if Firebase services are properly initialized</Text>
      <FirebaseTest />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    fontSize: 22,
    fontWeight: 'bold',
    margin: 20,
    marginBottom: 5,
  },
  subheader: {
    fontSize: 14,
    color: 'gray',
    marginHorizontal: 20,
    marginBottom: 20,
  },
}); 