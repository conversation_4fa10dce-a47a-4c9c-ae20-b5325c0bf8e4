import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TextInput, 
  TouchableOpacity, 
  ScrollView, 
  ActivityIndicator,
  Alert
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import SavedMealsComponent, { SavedMeal, FoodItem } from '@/components/SavedMealsComponent';
import QuickAddComponent from '@/components/QuickAddComponent';
import FrequentFoodsComponent from '@/components/FrequentFoodsComponent';
import SmartManualLoggingInput from '@/components/SmartManualLoggingInput';
import MultiFoodLoggingModal from '@/components/MultiFoodLoggingModal';

export default function FoodLoggerScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  
  const [mealType, setMealType] = useState<'breakfast' | 'lunch' | 'dinner' | 'snack'>('breakfast');
  const [searchQuery, setSearchQuery] = useState('');
  const [searching, setSearching] = useState(false);
  const [selectedFoods, setSelectedFoods] = useState<FoodItem[]>([]);
  const [saving, setSaving] = useState(false);
  const [showSavedMeals, setShowSavedMeals] = useState(false);
  const [showQuickAdd, setShowQuickAdd] = useState(false);
  const [showFrequentFoods, setShowFrequentFoods] = useState(false);
  const [showMultiFoodModal, setShowMultiFoodModal] = useState(false);
  
  // Mock search results
  const mockResults: FoodItem[] = [
    {
      id: '1',
      name: 'Banana',
      calories: 105,
      protein: 1.3,
      carbs: 27,
      fat: 0.4,
      servingSize: '1 medium (118g)',
      servings: 1,
    },
    {
      id: '2',
      name: 'Greek Yogurt, Plain',
      calories: 100,
      protein: 18,
      carbs: 5,
      fat: 0.4,
      servingSize: '170g container',
      servings: 1,
    },
    {
      id: '3',
      name: 'Oatmeal, cooked',
      calories: 166,
      protein: 5.9,
      carbs: 28,
      fat: 3.6,
      servingSize: '1 cup (234g)',
      servings: 1,
    },
  ];
  
  const [searchResults, setSearchResults] = useState<FoodItem[]>([]);
  
  const handleSearch = () => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }
    
    setSearching(true);
    
    // Simulate API call with timeout
    setTimeout(() => {
      const filtered = mockResults.filter(item => 
        item.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setSearchResults(filtered);
      setSearching(false);
    }, 500);
  };
  
  const addFood = (food: FoodItem) => {
    setSelectedFoods(prev => [...prev, { ...food, id: `${food.id}-${Date.now()}` }]);
    setSearchQuery('');
    setSearchResults([]);
  };

  const addFoods = (foods: FoodItem[]) => {
    setSelectedFoods(prev => [
      ...prev,
      ...foods.map(food => ({
        ...food,
        id: `${food.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      }))
    ]);
  };
  
  const removeFood = (id: string) => {
    setSelectedFoods(prev => prev.filter(food => food.id !== id));
  };
  
  const calculateTotals = () => {
    return selectedFoods.reduce(
      (acc, food) => {
        const multiplier = food.servings;
        return {
          calories: acc.calories + food.calories * multiplier,
          protein: acc.protein + food.protein * multiplier,
          carbs: acc.carbs + food.carbs * multiplier,
          fat: acc.fat + food.fat * multiplier,
        };
      },
      { calories: 0, protein: 0, carbs: 0, fat: 0 }
    );
  };
  
  const handleSaveMeal = () => {
    if (selectedFoods.length === 0) {
      Alert.alert('No Foods Added', 'Please add at least one food to your meal');
      return;
    }
    
    setSaving(true);
    
    // Simulate saving with timeout
    setTimeout(() => {
      setSaving(false);
      Alert.alert(
        'Meal Logged Successfully',
        `Your ${mealType} has been logged.`,
        [
          { text: 'OK', onPress: () => router.back() }
        ]
      );
    }, 1000);
  };
  
  const handleGoBack = () => {
    router.back();
  };

  const handleSelectSavedMeal = (savedMeal: SavedMeal) => {
    // Set meal type to the saved meal's type
    setMealType(savedMeal.type);
    
    // Add all foods from the saved meal
    setSelectedFoods(savedMeal.foods);
    
    // Hide saved meals view
    setShowSavedMeals(false);
  };

  const handleAddFoodsFromHistory = (foods: FoodItem[], type: 'breakfast' | 'lunch' | 'dinner' | 'snack') => {
    setMealType(type);
    setSelectedFoods(foods);
    setShowQuickAdd(false);
  };

  const handleAddFrequentFood = (food: FoodItem) => {
    addFood(food);
  };

  const toggleView = (view: 'main' | 'saved' | 'quick' | 'frequent') => {
    if (view === 'main') {
      setShowSavedMeals(false);
      setShowQuickAdd(false);
      setShowFrequentFoods(false);
    } else if (view === 'saved') {
      setShowSavedMeals(true);
      setShowQuickAdd(false);
      setShowFrequentFoods(false);
    } else if (view === 'quick') {
      setShowQuickAdd(true);
      setShowSavedMeals(false);
      setShowFrequentFoods(false);
    } else if (view === 'frequent') {
      setShowFrequentFoods(true);
      setShowSavedMeals(false);
      setShowQuickAdd(false);
    }
  };
  
  const totals = calculateTotals();
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDark ? colors.background : '#f9f9f9' }]}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
          <Feather name="arrow-left" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Log Food</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity 
            style={styles.headerButton}
            onPress={() => setShowMultiFoodModal(true)}
          >
            <Feather name="layers" size={24} color={colors.text} />
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.headerButton, showFrequentFoods && styles.activeHeaderButton]}
            onPress={() => toggleView(showFrequentFoods ? 'main' : 'frequent')}
          >
            <Feather name="menu" size={24} color={showFrequentFoods ? colors.primary : colors.text} />
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.headerButton, showQuickAdd && styles.activeHeaderButton]}
            onPress={() => toggleView(showQuickAdd ? 'main' : 'quick')}
          >
            <MaterialIcons name="history" size={24} color={showQuickAdd ? colors.primary : colors.text} />
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.headerButton, showSavedMeals && styles.activeHeaderButton]}
            onPress={() => toggleView(showSavedMeals ? 'main' : 'saved')}
          >
            <Feather name="bookmark" size={24} color={showSavedMeals ? colors.primary : colors.text} />
          </TouchableOpacity>
        </View>
      </View>
      
      {!showSavedMeals && !showQuickAdd && !showFrequentFoods ? (
        <>
          <View style={styles.mealTypeSelector}>
            {(['breakfast', 'lunch', 'dinner', 'snack'] as const).map((type) => (
              <TouchableOpacity
                key={type}
                style={[
                  styles.mealTypeButton,
                  mealType === type && { backgroundColor: colors.primary }
                ]}
                onPress={() => setMealType(type)}
              >
                <Text 
                  style={[
                    styles.mealTypeText,
                    { color: mealType === type ? 'white' : colors.text }
                  ]}
                >
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Smart Manual Logging Input */}
          <View style={styles.smartInputContainer}>
            <SmartManualLoggingInput 
              onFoodsAdded={addFoods} 
              mealType={mealType}
            />
          </View>
          
          <View style={[styles.searchContainer, { borderColor: colors.border }]}>
            <View style={[styles.searchInputContainer, { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }]}>
              <Feather name="search" size={20} color={colors.textSecondary} style={styles.searchIcon} />
              <TextInput
                style={[styles.searchInput, { color: colors.text }]}
                placeholder="Search foods..."
                placeholderTextColor={colors.textSecondary}
                value={searchQuery}
                onChangeText={setSearchQuery}
                onSubmitEditing={handleSearch}
                returnKeyType="search"
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity 
                  style={styles.clearButton}
                  onPress={() => setSearchQuery('')}
                >
                  <Feather name="x" size={16} color={colors.textSecondary} />
                </TouchableOpacity>
              )}
            </View>
            <TouchableOpacity 
              style={[styles.scanButton, { backgroundColor: colors.primary }]}
              onPress={() => router.push('/(tabs)/scan')}
            >
              <Feather name="camera" size={20}  color={colors.text} />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.scrollView}>
            {/* Search Results */}
            {searchQuery.length > 0 && (
              <View style={styles.searchResultsContainer}>
                {searching ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="small" color={colors.primary} />
                    <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Searching...</Text>
                  </View>
                ) : (
                  <>
                    {searchResults.length === 0 ? (
                      <Text style={[styles.noResultsText, { color: colors.textSecondary }]}>
                        No results found. Try a different search term.
                      </Text>
                    ) : (
                      <>
                        <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>
                          Results for "{searchQuery}"
                        </Text>
                        {searchResults.map((food) => (
                          <View 
                            key={food.id} 
                            style={[styles.foodItem, { borderBottomColor: colors.border }]}
                          >
                            <View style={styles.foodItemInfo}>
                              <Text style={[styles.foodName, { color: colors.text }]}>{food.name}</Text>
                              <Text style={[styles.foodDetails, { color: colors.textSecondary }]}>
                                {food.servingSize} • {food.calories} cal
                              </Text>
                            </View>
                            <TouchableOpacity 
                              style={[styles.addButton, { backgroundColor: colors.primary }]}
                              onPress={() => addFood(food)}
                            >
                              <Feather name="plus" size={18}  color={colors.text} />
                            </TouchableOpacity>
                          </View>
                        ))}
                      </>
                    )}
                  </>
                )}
              </View>
            )}
            
            {/* Selected Foods */}
            {selectedFoods.length > 0 && (
              <View style={styles.selectedFoodsContainer}>
                <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>
                  Added to {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
                </Text>
                
                {selectedFoods.map((food) => (
                  <View 
                    key={food.id} 
                    style={[styles.foodItem, { borderBottomColor: colors.border }]}
                  >
                    <View style={styles.foodItemInfo}>
                      <Text style={[styles.foodName, { color: colors.text }]}>{food.name}</Text>
                      <Text style={[styles.foodDetails, { color: colors.textSecondary }]}>
                        {food.servingSize} • {Math.round(food.calories * food.servings)} cal
                      </Text>
                    </View>
                    <TouchableOpacity 
                      style={[styles.removeButton, { borderColor: colors.border }]}
                      onPress={() => removeFood(food.id)}
                    >
                      <Feather name="x" size={18} color={colors.danger} />
                    </TouchableOpacity>
                  </View>
                ))}
                
                <View style={[styles.macroSummary, { backgroundColor: isDark ? colors.subtle : '#f5f5f5' }]}>
                  <Text style={[styles.macroSummaryTitle, { color: colors.text }]}>Meal Totals</Text>
                  <View style={styles.macroRow}>
                    <Text style={[styles.macroLabel, { color: colors.textSecondary }]}>Calories</Text>
                    <Text style={[styles.macroValue, { color: colors.text }]}>{Math.round(totals.calories)}</Text>
                  </View>
                  <View style={styles.macroRow}>
                    <Text style={[styles.macroLabel, { color: colors.textSecondary }]}>Protein</Text>
                    <Text style={[styles.macroValue, { color: colors.text }]}>{totals.protein.toFixed(1)}g</Text>
                  </View>
                  <View style={styles.macroRow}>
                    <Text style={[styles.macroLabel, { color: colors.textSecondary }]}>Carbs</Text>
                    <Text style={[styles.macroValue, { color: colors.text }]}>{totals.carbs.toFixed(1)}g</Text>
                  </View>
                  <View style={styles.macroRow}>
                    <Text style={[styles.macroLabel, { color: colors.textSecondary }]}>Fat</Text>
                    <Text style={[styles.macroValue, { color: colors.text }]}>{totals.fat.toFixed(1)}g</Text>
                  </View>
                </View>
              </View>
            )}
            
            <View style={styles.spacer} />
          </ScrollView>
          
          <View style={[styles.bottomBar, { backgroundColor: isDark ? colors.card : 'white', borderTopColor: colors.border }]}>
            <TouchableOpacity 
              style={[
                styles.saveButton, 
                { backgroundColor: colors.primary },
                selectedFoods.length === 0 && { opacity: 0.6 }
              ]}
              onPress={handleSaveMeal}
              disabled={selectedFoods.length === 0 || saving}
            >
              {saving ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Text style={styles.saveButtonText}>Save {mealType.charAt(0).toUpperCase() + mealType.slice(1)}</Text>
              )}
            </TouchableOpacity>
          </View>
        </>
      ) : showSavedMeals ? (
        <View style={styles.savedMealsContainer}>
          <Text style={[styles.viewTitle, { color: colors.text }]}>Saved Meals</Text>
          <SavedMealsComponent 
            currentMealType={mealType}
            onSelectMeal={handleSelectSavedMeal}
            currentFoods={selectedFoods}
          />
        </View>
      ) : showQuickAdd ? (
        <View style={styles.quickAddContainer}>
          <Text style={[styles.viewTitle, { color: colors.text }]}>Quick Add</Text>
          <QuickAddComponent 
            currentMealType={mealType}
            onAddFoods={handleAddFoodsFromHistory}
          />
        </View>
      ) : (
        <View style={styles.frequentFoodsContainer}>
          <Text style={[styles.viewTitle, { color: colors.text }]}>Frequent Foods</Text>
          <FrequentFoodsComponent 
            currentMealType={mealType}
            onAddFood={handleAddFrequentFood}
          />
        </View>
      )}

      {/* Multi-Food Logging Modal */}
      <MultiFoodLoggingModal
        visible={showMultiFoodModal}
        onClose={() => setShowMultiFoodModal(false)}
        onAddFoods={addFoods}
        mealType={mealType}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    height: 60,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
  activeHeaderButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 8,
  },
  mealTypeSelector: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  mealTypeButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 4,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  mealTypeText: {
    fontSize: 14,
    fontWeight: '500',
  },
  smartInputContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 16,
    alignItems: 'center',
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 16,
  },
  clearButton: {
    padding: 4,
  },
  scanButton: {
    width: 48,
    height: 48,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollView: {
    flex: 1,
  },
  searchResultsContainer: {
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
  },
  noResultsText: {
    textAlign: 'center',
    paddingVertical: 20,
    fontSize: 14,
  },
  sectionTitle: {
    fontSize: 14,
    marginBottom: 12,
  },
  foodItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  foodItemInfo: {
    flex: 1,
  },
  foodName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  foodDetails: {
    fontSize: 14,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 12,
  },
  removeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 12,
    borderWidth: 1,
  },
  selectedFoodsContainer: {
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  macroSummary: {
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  macroSummaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  macroRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  macroLabel: {
    fontSize: 15,
  },
  macroValue: {
    fontSize: 15,
    fontWeight: '500',
  },
  spacer: {
    height: 100,
  },
  bottomBar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  saveButton: {
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  savedMealsContainer: {
    flex: 1,
    padding: 16,
  },
  quickAddContainer: {
    flex: 1,
    padding: 16,
  },
  frequentFoodsContainer: {
    flex: 1,
    padding: 16,
  },
  viewTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
}); 