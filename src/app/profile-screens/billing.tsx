import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Platform, ActivityIndicator, Linking } from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
// Removed firebase import
import { LinearGradient } from 'expo-linear-gradient';
import { createPortalSession } from '@/services/stripeService';

// Declare global for React Native Web compatibility
declare const global: { window?: { location: { href: string } } };

interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal';
  last4?: string;
  brand?: string;
  expMonth?: number;
  expYear?: number;
  isDefault: boolean;
}

interface BillingHistory {
  id: string;
  date: string;
  amount: number;
  status: 'paid' | 'processing' | 'failed';
  description: string;
}

interface Subscription {
  id: string;
  name: string;
  price: number;
  interval: 'month' | 'year';
  status: 'active' | 'canceled' | 'trialing';
  currentPeriodEnd: number; // unix timestamp
  cancelAtPeriodEnd: boolean;
}

interface StripeSubscription {
  id: string;
  subscription_id: string;
  subscription_status: 'active' | 'canceled' | 'trialing';
  current_period_end: number;
  cancel_at_period_end: boolean;
}

export default function BillingScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [billingHistory, setBillingHistory] = useState<BillingHistory[]>([]);
  
  // Get session_id from URL if coming from checkout
  const params = useLocalSearchParams();
  const sessionId = params.session_id;
  
  useEffect(() => {
    if (user) {
      fetchSubscriptionData();
    }
  }, [user, sessionId]);
  
  const fetchSubscriptionData = async () => {
    try {
      setLoading(true);
      
      // Fetch subscription
      const { data: subData, error: subError } = await firebase
        .from('stripe_user_subscriptions')
        .select('*')
        .maybeSingle();
      
      if (subError) {
        console.error('Error fetching subscription:', subError);
      } else if (subData) {
        // Use a safer type assertion with unknown as intermediate step
        const typedSubData = subData as unknown as StripeSubscription;
        setSubscription({
          id: typedSubData.id,
          name: 'AI Friend',
          price: 5.00,
          interval: 'month',
          status: typedSubData.subscription_status,
          currentPeriodEnd: typedSubData.current_period_end,
          cancelAtPeriodEnd: typedSubData.cancel_at_period_end,
        });
      }
      
      // Mock payment methods for demo
      setPaymentMethods([
        {
          id: 'pm_1',
          type: 'card',
          last4: '4242',
          brand: 'Visa',
          expMonth: 12,
          expYear: 2025,
          isDefault: true,
        },
      ]);
      
      // Mock billing history for demo
      setBillingHistory([
        {
          id: 'in_1',
          date: '2023-05-01',
          amount: 5.00,
          status: 'paid',
          description: 'AI Friend Subscription',
        },
        {
          id: 'in_2',
          date: '2023-04-01',
          amount: 5.00,
          status: 'paid',
          description: 'AI Friend Subscription',
        },
        {
          id: 'in_3',
          date: '2023-03-01',
          amount: 5.00,
          status: 'paid',
          description: 'AI Friend Subscription',
        },
      ]);
      
    } catch (error) {
      console.error('Error fetching billing data:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Format date from unix timestamp
  const formatDate = (unixTimestamp: number) => {
    const date = new Date(unixTimestamp * 1000);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
  };
  
  // Handle subscription actions
  const handleManageSubscription = async () => {
    try {
      // Use the current URL or fallback for native
      const returnUrl = Platform.OS === 'web' 
        ? (global.window?.location.href || 'https://yourapp.com/billing')
        : 'yourapp://billing';
      
      const { data, error } = await createPortalSession(returnUrl);
      
      if (error) {
        console.error('Error creating portal link:', error);
        return;
      }
      
      if (data?.url) {
        if (Platform.OS === 'web' && global.window) {
          global.window.location.href = data.url;
        } else {
          Linking.openURL(data.url);
        }
      }
    } catch (error) {
      console.error('Error creating portal link:', error);
    }
  };

  return (
    <>
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.card }]}>
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={() => router.back()}
            accessibilityLabel="Go back"
            accessibilityHint="Returns to the previous screen"
          >
            <Feather name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Billing & Subscription</Text>
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
            onPress={() => console.log('Open settings')}
          >
            <Feather name="settings" size={20} color={colors.primary} />
          </TouchableOpacity>
        </View>
        
        <ScrollView 
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                Loading billing information...
              </Text>
            </View>
          ) : (
            <>
              {/* Subscription Card */}
              <View style={[styles.subscriptionCard, { 
                backgroundColor: colors.card,
                borderColor: colors.border,
                shadowColor: colors.shadow
              }]}>
                <View style={styles.subscriptionHeader}>
                  <LinearGradient
                    colors={['#3B82F6', '#2563EB'] as const}
                    style={styles.subscriptionIcon}
                  >
                    <Feather name="credit-card" size={22}  color={colors.text} />
                  </LinearGradient>
                  <View style={styles.subscriptionInfo}>
                    <Text style={[styles.subscriptionName, { color: colors.text }]}>
                      {subscription?.name || 'No active subscription'}
                    </Text>
                    {subscription ? (
                      <View style={styles.subscriptionDetails}>
                        <Text style={[styles.subscriptionPrice, { color: colors.primary }]}>
                          ${subscription.price.toFixed(2)}/{subscription.interval}
                        </Text>
                        {subscription.status === 'active' && (
                          <View style={[styles.statusBadge, { backgroundColor: 'rgba(16, 185, 129, 0.1)' }]}>
                            <Text style={[styles.statusText, { color: '#10B981' }]}>Active</Text>
                          </View>
                        )}
                        {subscription.status === 'trialing' && (
                          <View style={[styles.statusBadge, { backgroundColor: 'rgba(59, 130, 246, 0.1)' }]}>
                            <Text style={[styles.statusText, { color: '#3B82F6' }]}>Trial</Text>
                          </View>
                        )}
                        {subscription.status === 'canceled' && (
                          <View style={[styles.statusBadge, { backgroundColor: 'rgba(239, 68, 68, 0.1)' }]}>
                            <Text style={[styles.statusText, { color: '#EF4444' }]}>Canceled</Text>
                          </View>
                        )}
                      </View>
                    ) : (
                      <TouchableOpacity 
                        style={[styles.subscribeButton, { backgroundColor: colors.primary + '20' }]}
                        onPress={() => router.push('/(tabs)/profile')}
                      >
                        <Text style={[styles.subscribeButtonText, { color: colors.primary }]}>Subscribe now</Text>
                      </TouchableOpacity>
                    )}
                  </View>
                </View>
                
                {subscription && subscription.status === 'active' && (
                  <View style={styles.subscriptionContent}>
                    <View style={[styles.renewalInfo, { borderColor: colors.border }]}>
                      <View style={styles.renewalRow}>
                        <Feather name="clock" size={16} color={colors.textSecondary} style={styles.renewalIcon} />
                        <Text style={[styles.renewalText, { color: colors.textSecondary }]}>
                          {subscription.cancelAtPeriodEnd 
                            ? 'Your subscription will end on ' 
                            : 'Your subscription renews on '}
                          <Text style={{ color: colors.text, fontWeight: '600' }}>
                            {formatDate(subscription.currentPeriodEnd)}
                          </Text>
                        </Text>
                      </View>
                      
                      {subscription.cancelAtPeriodEnd ? (
                        <TouchableOpacity 
                          style={[styles.reactivateButton, { borderColor: colors.primary }]}
                          onPress={handleManageSubscription}
                        >
                          <Text style={[styles.reactivateButtonText, { color: colors.primary }]}>
                            Reactivate
                          </Text>
                        </TouchableOpacity>
                      ) : (
                        <TouchableOpacity 
                          style={[styles.cancelButton, { borderColor: colors.border }]}
                          onPress={handleManageSubscription}
                        >
                          <Text style={[styles.cancelButtonText, { color: colors.text }]}>
                            Cancel
                          </Text>
                        </TouchableOpacity>
                      )}
                    </View>
                    
                    <TouchableOpacity 
                      style={styles.manageSubscriptionButton}
                      onPress={handleManageSubscription}
                    >
                      <Text style={[styles.manageSubscriptionText, { color: colors.primary }]}>
                        Manage Subscription
                      </Text>
                      <Feather name="arrow-right" size={16} color={colors.primary} />
                    </TouchableOpacity>
                  </View>
                )}
              </View>
              
              {/* Payment Methods */}
              <View style={styles.sectionContainer}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>Payment Methods</Text>
                <View style={[styles.paymentMethodsCard, { 
                  backgroundColor: colors.card,
                  borderColor: colors.border,
                  shadowColor: colors.shadow
                }]}>
                  {paymentMethods.length > 0 ? (
                    paymentMethods.map((method) => (
                      <View 
                        key={method.id}
                        style={[styles.paymentMethod, { borderBottomColor: colors.border }]}
                      >
                        <View style={styles.paymentMethodLeft}>
                          <View style={[
                            styles.cardIcon, 
                            { backgroundColor: method.brand === 'Visa' ? '#1A1F71' : '#2962FF' }
                          ]}>
                            <Text style={styles.cardIconText}>
                              {method.brand?.charAt(0) || 'C'}
                            </Text>
                          </View>
                          <View>
                            <View style={styles.cardDetails}>
                              <Text style={[styles.cardBrand, { color: colors.text }]}>
                                {method.brand}
                              </Text>
                              {method.isDefault && (
                                <View style={styles.defaultBadge}>
                                  <Text style={styles.defaultText}>Default</Text>
                                </View>
                              )}
                            </View>
                            <Text style={[styles.cardNumber, { color: colors.textSecondary }]}>
                              •••• {method.last4}
                            </Text>
                            <Text style={[styles.cardExpiry, { color: colors.textSecondary }]}>
                              Expires {method.expMonth}/{method.expYear}
                            </Text>
                          </View>
                        </View>
                        <TouchableOpacity 
                          style={[styles.editButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
                          onPress={handleManageSubscription}
                        >
                          <Text style={[styles.editButtonText, { color: colors.primary }]}>Edit</Text>
                        </TouchableOpacity>
                      </View>
                    ))
                  ) : (
                    <View style={styles.noPaymentMethods}>
                      <Text style={[styles.noPaymentMethodsText, { color: colors.textSecondary }]}>
                        No payment methods added
                      </Text>
                    </View>
                  )}
                  
                  <TouchableOpacity 
                    style={[styles.addPaymentMethod, { borderTopColor: colors.border }]}
                    onPress={handleManageSubscription}
                  >
                    <Feather name="plus" size={18} color={colors.primary} style={styles.addIcon} />
                    <Text style={[styles.addPaymentMethodText, { color: colors.primary }]}>
                      Add Payment Method
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
              
              {/* Billing History */}
              <View style={styles.sectionContainer}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>Billing History</Text>
                <View style={[styles.billingHistoryCard, { 
                  backgroundColor: colors.card,
                  borderColor: colors.border,
                  shadowColor: colors.shadow
                }]}>
                  {billingHistory.length > 0 ? (
                    billingHistory.map((item, index) => (
                      <View 
                        key={item.id}
                        style={[
                          styles.billingItem, 
                          index < billingHistory.length - 1 && { borderBottomColor: colors.border, borderBottomWidth: 1 }
                        ]}
                      >
                        <View style={styles.billingItemLeft}>
                          <View style={[styles.billIcon, { backgroundColor: item.status === 'paid' ? '#10B981' : '#6B7280' }]}>
                            {item.status === 'paid' ? (
                              <MaterialIcons name="check-circle" size={16}  color={colors.text} />
                            ) : (
                              <Feather name="clock" size={16}  color={colors.text} />
                            )}
                          </View>
                          <View>
                            <Text style={[styles.billingDescription, { color: colors.text }]}>
                              {item.description}
                            </Text>
                            <Text style={[styles.billingDate, { color: colors.textSecondary }]}>
                              {new Date(item.date).toLocaleDateString('en-US', { 
                                year: 'numeric', 
                                month: 'short', 
                                day: 'numeric' 
                              })}
                            </Text>
                          </View>
                        </View>
                        <View style={styles.billingItemRight}>
                          <Text style={[styles.billingAmount, { color: colors.text }]}>
                            ${item.amount.toFixed(2)}
                          </Text>
                          <TouchableOpacity style={styles.downloadButton}>
                            <Feather name="download" size={14} color={colors.textSecondary} />
                          </TouchableOpacity>
                        </View>
                      </View>
                    ))
                  ) : (
                    <View style={styles.noBillingHistory}>
                      <Text style={[styles.noBillingHistoryText, { color: colors.textSecondary }]}>
                        No billing history available
                      </Text>
                    </View>
                  )}
                </View>
              </View>
              
              {/* Billing Notifications */}
              <View style={[styles.notificationsCard, { 
                backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)',
                borderColor: '#3B82F6' + '20'
              }]}>
                <View style={styles.notificationsHeader}>
                  <Feather name="bell" size={18} style={styles.notificationIcon} />
                  <Text style={[styles.notificationsTitle, { color: colors.text }]}>
                    Billing Notifications
                  </Text>
                </View>
                <Text style={[styles.notificationsText, { color: colors.textSecondary }]}>
                  You will receive email notifications for all billing-related activities,
                  including upcoming charges, receipts, and payment issues.
                </Text>
                <TouchableOpacity 
                  style={[styles.notificationsButton, { borderColor: '#3B82F6' }]}
                >
                  <Text style={[styles.notificationsButtonText, { color: '#3B82F6' }]}>
                    Manage Notifications
                  </Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollContent: {
    paddingBottom: 40,
  },
  loadingContainer: {
    flex: 1,
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  subscriptionCard: {
    margin: 20,
    marginTop: 10,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 4 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      }
    }),
  },
  subscriptionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subscriptionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  subscriptionInfo: {
    flex: 1,
  },
  subscriptionName: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 6,
  },
  subscriptionDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subscriptionPrice: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  subscribeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    alignSelf: 'flex-start',
  },
  subscribeButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  subscriptionContent: {
    marginTop: 16,
  },
  renewalInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    marginBottom: 16,
  },
  renewalRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  renewalIcon: {
    marginRight: 8,
  },
  renewalText: {
    fontSize: 14,
    flex: 1,
  },
  cancelButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  reactivateButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
  },
  reactivateButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  manageSubscriptionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  manageSubscriptionText: {
    fontSize: 15,
    fontWeight: '600',
    marginRight: 6,
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginHorizontal: 20,
    marginBottom: 12,
  },
  paymentMethodsCard: {
    marginHorizontal: 20,
    borderRadius: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: { elevation: 2 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  paymentMethod: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  paymentMethodLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  cardIconText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '700',
  },
  cardDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  cardBrand: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  defaultBadge: {
    backgroundColor: '#10B981',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  defaultText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  cardNumber: {
    fontSize: 14,
    marginBottom: 2,
  },
  cardExpiry: {
    fontSize: 12,
  },
  editButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  editButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  noPaymentMethods: {
    padding: 16,
    alignItems: 'center',
  },
  noPaymentMethodsText: {
    fontSize: 14,
  },
  addPaymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderTopWidth: 1,
  },
  addIcon: {
    marginRight: 6,
  },
  addPaymentMethodText: {
    fontSize: 15,
    fontWeight: '600',
  },
  billingHistoryCard: {
    marginHorizontal: 20,
    borderRadius: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: { elevation: 2 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  billingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  billingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  billIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  billingDescription: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 2,
  },
  billingDate: {
    fontSize: 13,
  },
  billingItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  billingAmount: {
    fontSize: 16,
    fontWeight: '700',
    marginRight: 10,
  },
  downloadButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(100,116,139,0.1)',
  },
  noBillingHistory: {
    padding: 16,
    alignItems: 'center',
  },
  noBillingHistoryText: {
    fontSize: 14,
  },
  notificationsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
  },
  notificationsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  notificationIcon: {
    marginRight: 10,
  },
  notificationsTitle: {
    fontSize: 16,
    fontWeight: '700',
  },
  notificationsText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  notificationsButton: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
  },
  notificationsButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
}); 