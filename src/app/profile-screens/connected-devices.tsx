import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Switch, ActivityIndicator, Platform, Image, Alert } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import { useWearableDevices, ConnectedDevice, HealthMetric } from '@/hooks/useWearableDevices';
import { HealthDataType } from '@/services/healthIntegrationService';
import { useAlert } from '@/hooks/useAlert';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  isBackgroundSyncRegistered, 
  initBackgroundSync, 
  stopBackgroundSync,
  triggerSync,
  getLastSyncTime
} from '@/services/backgroundSyncService';

// Mock implementation for HomeWidget using AsyncStorage
const HomeWidget = {
  hasWidget: async (name: string): Promise<boolean> => {
    try {
      const value = await AsyncStorage.getItem(`widget_installed_${name}`);
      return value === 'true';
    } catch {
      return false;
    }
  },
  
  removeWidget: async (name: string): Promise<void> => {
    try {
      await AsyncStorage.setItem(`widget_installed_${name}`, 'false');
    } catch (error) {
      console.error('Error removing widget:', error);
    }
  },
  
  setItem: async (key: string, value: string, appGroup?: string): Promise<void> => {
    try {
      await AsyncStorage.setItem(`widget_${key}`, value);
    } catch (error) {
      console.error('Error setting widget item:', error);
    }
  },
  
  reloadAllTimelines: async (): Promise<void> => {
    // This would normally refresh widgets, just a mock operation
    console.log('Widget timelines would be reloaded');
  },
  
  updateWidget: async (config: { name: string, widgetData: any }): Promise<void> => {
    try {
      await AsyncStorage.setItem(
        `widget_data_${config.name}`, 
        JSON.stringify(config.widgetData)
      );
    } catch (error) {
      console.error('Error updating widget:', error);
    }
  }
};

export default function ConnectedDevicesScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'devices' | 'data' | 'settings'>('devices');
  const [selectedDataType, setSelectedDataType] = useState<HealthDataType>(HealthDataType.STEPS);
  const [isBackgroundEnabled, setIsBackgroundEnabled] = useState(false);
  const [isWidgetEnabled, setIsWidgetEnabled] = useState(false);
  const [syncInterval, setSyncInterval] = useState<'15min' | '30min' | '1hour' | '6hours'>('30min');
  const [lastBackgroundSync, setLastBackgroundSync] = useState<Date | null>(null);
  const { showAlert } = useAlert();
  
  const {
    isInitialized,
    isInitializing,
    initialize,
    connectedDevices,
    healthData,
    isLoading,
    error,
    fetchHealthData,
    syncHealth,
    disconnect,
    supportsWearable,
    getSupportedPlatforms
  } = useWearableDevices();

  // Check platform support
  const platforms = getSupportedPlatforms();

  // Initialize and check background sync status
  useEffect(() => {
    const checkBackgroundSync = async () => {
      const isRegistered = await isBackgroundSyncRegistered();
      setIsBackgroundEnabled(isRegistered);
      
      const lastSync = await getLastSyncTime();
      if (lastSync) {
        setLastBackgroundSync(lastSync);
      }
    };
    
    const checkWidgetStatus = async () => {
      try {
        if (Platform.OS === 'ios') {
          const hasWidget = await HomeWidget.hasWidget('HealthDataWidget');
          setIsWidgetEnabled(hasWidget);
        } else if (Platform.OS === 'android') {
          const hasWidget = await HomeWidget.hasWidget('HealthWidgetProvider');
          setIsWidgetEnabled(hasWidget);
        }
      } catch (error) {
        console.error('Error checking widget status:', error);
      }
    };
    
    checkBackgroundSync();
    checkWidgetStatus();
  }, []);

  // Fetch initial data
  useEffect(() => {
    if (isInitialized && !isLoading) {
      fetchHealthData(HealthDataType.STEPS);
      setLastUpdated(new Date().toISOString());
    }
  }, [isInitialized]);

  // Refresh data
  const handleRefresh = async () => {
    if (!isInitialized) {
      showAlert({
        title: 'Not Connected',
        message: 'Please connect to a wearable device first',
        type: 'warning'
      });
      return;
    }
    
    await fetchHealthData(selectedDataType);
    setLastUpdated(new Date().toISOString());
  };

  // Sync data with server
  const handleSync = async () => {
    if (!isInitialized) {
      showAlert({
        title: 'Not Connected',
        message: 'Please connect to a wearable device first',
        type: 'warning'
      });
      return;
    }
    
    await syncHealth(selectedDataType);
    setLastUpdated(new Date().toISOString());
  };

  // Connect to device
  const handleConnect = async () => {
    if (isInitialized) {
      showAlert({
        title: 'Already Connected',
        message: 'You already have connected devices',
        type: 'info'
      });
      return;
    }
    
    await initialize();
  };

  // Disconnect device
  const handleDisconnect = () => {
    disconnect();
  };

  // Toggle background sync
  const toggleBackgroundSync = async () => {
    if (isBackgroundEnabled) {
      const success = await stopBackgroundSync();
      if (success) {
        setIsBackgroundEnabled(false);
        showAlert({
          title: 'Background Sync Disabled',
          message: 'Background sync has been disabled',
          type: 'success'
        });
      }
    } else {
      const success = await initBackgroundSync();
      if (success) {
        setIsBackgroundEnabled(true);
        showAlert({
          title: 'Background Sync Enabled',
          message: 'Your health data will sync periodically in the background',
          type: 'success'
        });
      }
    }
  };

  // Manage home screen widget
  const toggleWidget = async () => {
    try {
      if (isWidgetEnabled) {
        // Remove widget
        if (Platform.OS === 'ios') {
          await HomeWidget.removeWidget('HealthDataWidget');
        } else if (Platform.OS === 'android') {
          await HomeWidget.removeWidget('HealthWidgetProvider');
        }
        setIsWidgetEnabled(false);
        
        showAlert({
          title: 'Widget Removed',
          message: 'The health data widget has been removed',
          type: 'success'
        });
      } else {
        // Show instructions for adding widget
        showAlert({
          title: 'Add Widget',
          message: Platform.OS === 'ios' 
            ? 'Long press on your home screen, tap the + button, and search for Health Data widget' 
            : 'Long press on your home screen, select Widgets, and look for Health Data',
          type: 'info'
        });
      }
    } catch (error) {
      console.error('Error managing widget:', error);
      showAlert({
        title: 'Widget Error',
        message: 'Failed to manage widget',
        type: 'error'
      });
    }
  };

  // Update widget data
  const updateWidget = async () => {
    try {
      if (!isWidgetEnabled) {
        showAlert({
          title: 'No Widget',
          message: 'Please add the Health Data widget first',
          type: 'warning'
        });
        return;
      }

      // Get latest health data for widget
      const stepsData = healthData[HealthDataType.STEPS];
      const heartData = healthData[HealthDataType.HEART_RATE];
      
      const widgetData = {
        steps: stepsData && stepsData.length > 0 ? Math.round(stepsData[stepsData.length - 1].value) : 0,
        heart: heartData && heartData.length > 0 ? Math.round(heartData[heartData.length - 1].value) : 0,
        lastUpdated: new Date().toISOString()
      };
      
      if (Platform.OS === 'ios') {
        await HomeWidget.setItem('healthData', JSON.stringify(widgetData), 'group.com.yourapp.healthwidget');
        await HomeWidget.reloadAllTimelines();
      } else if (Platform.OS === 'android') {
        await HomeWidget.updateWidget({
          name: 'HealthWidgetProvider',
          widgetData
        });
      }
      
      showAlert({
        title: 'Widget Updated',
        message: 'The health data widget has been updated with the latest data',
        type: 'success'
      });
    } catch (error) {
      console.error('Error updating widget:', error);
      showAlert({
        title: 'Widget Error',
        message: 'Failed to update widget',
        type: 'error'
      });
    }
  };

  // Format date for display
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get device icon based on type
  const getDeviceIcon = (device: ConnectedDevice) => {
    if (device.type === 'watch') {
      return <WatchIcon size={24} color={colors.primary} />;
    }
    if (device.type === 'tracker') {
      return <Feather name="activity" size={24} color={colors.primary} />;
    }
    return <Feather name="smartphone" size={24} color={colors.primary} />;
  };

  // Get data type icon
  const getDataTypeIcon = (dataType: HealthDataType) => {
    switch (dataType) {
      case HealthDataType.STEPS:
        return <Feather name="activity" size={20} color={colors.primary} />;
      case HealthDataType.HEART_RATE:
        return <Feather name="heart" size={20} color={colors.danger} />;
      case HealthDataType.WEIGHT:
        return <Feather name="activity" size={20} color={colors.success} />;
      default:
        return <Feather name="info" size={20} color={colors.text} />;
    }
  };

  // Render device card
  const renderDeviceCard = (device: ConnectedDevice) => {
    return (
      <View 
        key={device.id} 
        style={[
          styles.deviceCard, 
          { 
            backgroundColor: colors.card,
            borderColor: colors.border,
          }
        ]}
      >
        <View style={styles.deviceHeader}>
          <View style={styles.deviceInfo}>
            {getDeviceIcon(device)}
            <View style={styles.deviceTextContainer}>
              <Text style={[styles.deviceName, { color: colors.text }]}>{device.name}</Text>
              <Text style={[styles.deviceType, { color: colors.textSecondary }]}>
                {device.platform === 'apple' ? 'Apple' : 'Google'} {device.type}
              </Text>
            </View>
          </View>
          <Switch
            value={device.connected}
            onValueChange={() => {
              if (device.connected) {
                handleDisconnect();
              } else {
                handleConnect();
              }
            }}
            trackColor={{ false: colors.border, true: `${colors.primary}50` }}
            thumbColor={device.connected ? colors.primary : colors.textSecondary}
          />
        </View>
        
        <View style={[styles.divider, { backgroundColor: colors.border }]} />
        
        <View style={styles.deviceDetails}>
          <Text style={[styles.deviceDetailLabel, { color: colors.textSecondary }]}>Last Synced:</Text>
          <Text style={[styles.deviceDetailValue, { color: colors.text }]}>
            {formatDate(device.lastSync)}
          </Text>
        </View>
        
        <View style={styles.deviceActions}>
          <TouchableOpacity 
            style={[styles.deviceAction, { backgroundColor: `${colors.primary}20` }]}
            onPress={handleRefresh}
            disabled={isLoading || !device.connected}
          >
            <Feather name="refresh-cw" size={16} color={colors.primary} />
            <Text style={[styles.deviceActionText, { color: colors.primary }]}>Refresh</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.deviceAction, { backgroundColor: `${colors.danger}20` }]}
            onPress={handleDisconnect}
            disabled={isLoading || !device.connected}
          >
            <Feather name="trash-2" size={16} color={colors.danger} />
            <Text style={[styles.deviceActionText, { color: colors.danger }]}>Disconnect</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // Render health data section
  const renderHealthData = () => {
    const data = healthData[selectedDataType] || [];
    
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading health data...
          </Text>
        </View>
      );
    }
    
    if (data.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Feather name="info" size={48} color={colors.textSecondary} style={styles.emptyIcon} />
          <Text style={[styles.emptyText, { color: colors.text }]}>
            No health data available
          </Text>
          <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
            Sync with your wearable device to see data here
          </Text>
          <TouchableOpacity 
            style={[styles.syncButton, { backgroundColor: colors.primary }]}
            onPress={handleRefresh}
            disabled={!isInitialized}
          >
            <Feather name="refresh-cw" size={16} color={colors.buttonText} />
            <Text style={[styles.syncButtonText, { color: colors.buttonText }]}>
              Sync Now
            </Text>
          </TouchableOpacity>
        </View>
      );
    }
    
    // Format date for X-axis
    const formatChartDate = (dateString: string) => {
      const date = new Date(dateString);
      return `${date.getMonth() + 1}/${date.getDate()}`;
    };
    
    return (
      <View style={styles.dataContainer}>
        <View style={styles.dataHeader}>
          <Text style={[styles.dataTitle, { color: colors.text }]}>
            {selectedDataType.charAt(0).toUpperCase() + selectedDataType.slice(1)} Data
          </Text>
          {lastUpdated && (
            <Text style={[styles.lastUpdated, { color: colors.textSecondary }]}>
              Updated: {formatDate(lastUpdated)}
            </Text>
          )}
        </View>
        
        <ScrollView 
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.dataTypeSelector}
          contentContainerStyle={styles.dataTypeSelectorContent}
        >
          {Object.values(HealthDataType).map((type) => (
            <TouchableOpacity
              key={type}
              style={[
                styles.dataTypeButton,
                selectedDataType === type && { 
                  backgroundColor: `${colors.primary}20`,
                  borderColor: colors.primary 
                },
                { borderColor: colors.border }
              ]}
              onPress={() => {
                setSelectedDataType(type);
                fetchHealthData(type);
              }}
            >
              {getDataTypeIcon(type)}
              <Text 
                style={[
                  styles.dataTypeText, 
                  { color: selectedDataType === type ? colors.primary : colors.text }
                ]}
              >
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
        
        <View style={[styles.chartContainer, { backgroundColor: `${colors.card}50` }]}>
          {/* Simple bar chart representation */}
          <View style={styles.barChart}>
            {data.slice(-7).map((item, index) => {
              const maxValue = Math.max(...data.slice(-7).map(d => d.value));
              const barHeight = (item.value / maxValue) * 150;
              
              return (
                <View key={index} style={styles.barContainer}>
                  <View 
                    style={[
                      styles.bar, 
                      { 
                        height: barHeight, 
                        backgroundColor: colors.primary 
                      }
                    ]} 
                  />
                  <Text style={[styles.barLabel, { color: colors.text }]}>
                    {formatChartDate(item.date)}
                  </Text>
                </View>
              );
            })}
          </View>
        </View>
        
        <View style={styles.dataActions}>
          <TouchableOpacity 
            style={[styles.dataAction, { backgroundColor: colors.primary }]}
            onPress={handleRefresh}
            disabled={isLoading}
          >
            <Feather name="refresh-cw" size={16} color={colors.buttonText} />
            <Text style={[styles.dataActionText, { color: colors.buttonText }]}>
              Refresh
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.dataAction, { backgroundColor: colors.success }]}
            onPress={handleSync}
            disabled={isLoading}
          >
            <Feather name="activity" size={16} color={colors.buttonText} />
            <Text style={[styles.dataActionText, { color: colors.buttonText }]}>
              Sync to Server
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // Render settings section
  const renderSettings = () => {
    return (
      <View style={styles.settingsContainer}>
        <View style={[styles.settingSection, { backgroundColor: colors.card, borderColor: colors.border }]}>
          <Text style={[styles.settingSectionTitle, { color: colors.text }]}>
            Background Sync
          </Text>
          
          <View style={[styles.settingRow, { borderBottomColor: colors.border }]}>
            <View style={styles.settingInfo}>
              <Feather name="clock" size={22} color={colors.primary} style={styles.settingIcon} />
              <View style={styles.settingTextContainer}>
                <Text style={[styles.settingTitle, { color: colors.text }]}>
                  Enable Background Sync
                </Text>
                <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                  Sync your health data automatically in the background
                </Text>
              </View>
            </View>
            <Switch
              value={isBackgroundEnabled}
              onValueChange={toggleBackgroundSync}
              trackColor={{ false: colors.border, true: `${colors.primary}50` }}
              thumbColor={isBackgroundEnabled ? colors.primary : colors.textSecondary}
            />
          </View>
          
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Feather name="refresh-cw" size={22} color={colors.primary} style={styles.settingIcon} />
              <View style={styles.settingTextContainer}>
                <Text style={[styles.settingTitle, { color: colors.text }]}>
                  Manual Sync
                </Text>
                <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                  Sync all health data now
                </Text>
                {lastBackgroundSync && (
                  <Text style={[styles.settingSubtext, { color: colors.textSecondary }]}>
                    Last sync: {formatDate(lastBackgroundSync.toISOString())}
                  </Text>
                )}
              </View>
            </View>
            <TouchableOpacity
              style={[styles.iconButton, { backgroundColor: colors.primary }]}
              onPress={async () => {
                await triggerSync();
                const lastSync = await getLastSyncTime();
                if (lastSync) setLastBackgroundSync(lastSync);
                showAlert({
                  title: 'Sync Complete',
                  message: 'Your health data has been synced',
                  type: 'success'
                });
              }}
              disabled={isLoading}
            >
              <Feather name="refresh-cw" size={16} color={colors.buttonText} />
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={[styles.settingSection, { backgroundColor: colors.card, borderColor: colors.border }]}>
          <Text style={[styles.settingSectionTitle, { color: colors.text }]}>
            Home Screen Widget
          </Text>
          
          <View style={[styles.settingRow, { borderBottomColor: colors.border }]}>
            <View style={styles.settingInfo}>
              <Feather name="home" size={22} color={colors.primary} style={styles.settingIcon} />
              <View style={styles.settingTextContainer}>
                <Text style={[styles.settingTitle, { color: colors.text }]}>
                  Health Data Widget
                </Text>
                <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                  Show health metrics on your home screen
                </Text>
              </View>
            </View>
            <Switch
              value={isWidgetEnabled}
              onValueChange={toggleWidget}
              trackColor={{ false: colors.border, true: `${colors.primary}50` }}
              thumbColor={isWidgetEnabled ? colors.primary : colors.textSecondary}
            />
          </View>
          
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Feather name="refresh-cw" size={22} color={colors.primary} style={styles.settingIcon} />
              <View style={styles.settingTextContainer}>
                <Text style={[styles.settingTitle, { color: colors.text }]}>
                  Update Widget
                </Text>
                <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                  Refresh widget with latest data
                </Text>
              </View>
            </View>
            <TouchableOpacity
              style={[styles.iconButton, { 
                backgroundColor: isWidgetEnabled ? colors.primary : colors.border
              }]}
              onPress={updateWidget}
              disabled={!isWidgetEnabled || isLoading}
            >
              <Feather name="refresh-cw" size={16} color={isWidgetEnabled ? colors.buttonText : colors.textSecondary} />
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={[styles.settingSection, { backgroundColor: colors.card, borderColor: colors.border }]}>
          <Text style={[styles.settingSectionTitle, { color: colors.text }]}>
            Wearable App
          </Text>
          
          <TouchableOpacity 
            style={styles.settingAction}
            onPress={() => {
              // Mock navigation to wearable app
              showAlert({
                title: 'Wearable Interface',
                message: 'The wearable interface would open here but is not currently available.',
                type: 'info'
              });
            }}
          >
            <View style={styles.settingInfo}>
              <WatchIcon size={22} color={colors.primary} style={styles.settingIcon} />
              <View style={styles.settingTextContainer}>
                <Text style={[styles.settingTitle, { color: colors.text }]}>
                  Open Wearable Interface
                </Text>
                <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                  View a streamlined UI designed for watches and small screens
                </Text>
              </View>
            </View>
            <Feather name="chevron-left" size={20} color={colors.textSecondary} style={{ transform: [{ rotate: '180deg' }] }} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // Check if wearables are supported
  if (!supportsWearable) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={[styles.header, { backgroundColor: colors.background }]}>
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: `${colors.text}10` }]}
            onPress={() => router.back()}
          >
            <Feather name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Connected Devices</Text>
          <View style={{ width: 40 }} />
        </View>
        
        <View style={styles.notSupportedContainer}>
          <SmartphoneNfc size={64} color={colors.textSecondary} style={styles.notSupportedIcon} />
          <Text style={[styles.notSupportedTitle, { color: colors.text }]}>
            Wearable Devices Not Supported
          </Text>
          <Text style={[styles.notSupportedText, { color: colors.textSecondary }]}>
            Your device does not support integration with wearable devices. 
            Please use a device that supports Apple Health or Google Fit.
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.background }]}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: `${colors.text}10` }]}
          onPress={() => router.back()}
        >
          <Feather name="chevron-left" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Connected Devices</Text>
        <View style={{ width: 40 }} />
      </View>
      
      <View style={[styles.tabContainer, { borderBottomColor: colors.border }]}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'devices' && styles.activeTab,
            { borderBottomColor: activeTab === 'devices' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setActiveTab('devices')}
        >
          <SmartphoneNfc size={20} color={activeTab === 'devices' ? colors.primary : colors.textSecondary} />
          <Text 
            style={[
              styles.tabText, 
              { color: activeTab === 'devices' ? colors.primary : colors.textSecondary }
            ]}
          >
            Devices
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'data' && styles.activeTab,
            { borderBottomColor: activeTab === 'data' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setActiveTab('data')}
        >
          <Feather name="activity" size={20} color={activeTab === 'data' ? colors.primary : colors.textSecondary} />
          <Text 
            style={[
              styles.tabText, 
              { color: activeTab === 'data' ? colors.primary : colors.textSecondary }
            ]}
          >
            Health Data
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'settings' && styles.activeTab,
            { borderBottomColor: activeTab === 'settings' ? colors.primary : 'transparent' }
          ]}
          onPress={() => setActiveTab('settings')}
        >
          <SettingsIcon size={20} color={activeTab === 'settings' ? colors.primary : colors.textSecondary} />
          <Text 
            style={[
              styles.tabText, 
              { color: activeTab === 'settings' ? colors.primary : colors.textSecondary }
            ]}
          >
            Settings
          </Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {isInitializing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
              Initializing wearable devices...
            </Text>
          </View>
        ) : (
          <>
            {activeTab === 'devices' && (
              <>
                {connectedDevices.length > 0 ? (
                  connectedDevices.map(device => renderDeviceCard(device))
                ) : (
                  <View style={styles.emptyContainer}>
                    <SmartphoneNfc size={48} color={colors.textSecondary} style={styles.emptyIcon} />
                    <Text style={[styles.emptyText, { color: colors.text }]}>
                      No Devices Connected
                    </Text>
                    <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
                      Connect to a wearable device to track your health data
                    </Text>
                    <TouchableOpacity 
                      style={[styles.connectButton, { backgroundColor: colors.primary }]}
                      onPress={handleConnect}
                    >
                      <PlusCircle size={16} color={colors.buttonText} />
                      <Text style={[styles.connectButtonText, { color: colors.buttonText }]}>
                        Connect Device
                      </Text>
                    </TouchableOpacity>
                  </View>
                )}
                
                <View style={styles.platformSupport}>
                  <Text style={[styles.platformTitle, { color: colors.text }]}>
                    Supported Platforms
                  </Text>
                  <View style={styles.platformIcons}>
                    {platforms.apple && (
                      <View 
                        style={[
                          styles.platformIcon, 
                          { 
                            backgroundColor: isDark ? '#333' : '#f4f4f4',
                            borderColor: colors.border 
                          }
                        ]}
                      >
                        <WatchIcon size={24} color={colors.text} />
                        <Text style={[styles.platformText, { color: colors.text }]}>
                          Apple Health
                        </Text>
                      </View>
                    )}
                    
                    {platforms.google && (
                      <View 
                        style={[
                          styles.platformIcon, 
                          { 
                            backgroundColor: isDark ? '#333' : '#f4f4f4',
                            borderColor: colors.border 
                          }
                        ]}
                      >
                        <Feather name="activity" size={24} color={colors.text} />
                        <Text style={[styles.platformText, { color: colors.text }]}>
                          Google Fit
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
                
                <View style={styles.deviceInfoSection}>
                  <Text style={[styles.infoTitle, { color: colors.text }]}>
                    About Wearable Integration
                  </Text>
                  <Text style={[styles.infoText, { color: colors.textSecondary }]}>
                    Connect your wearable device to track steps, heart rate, and other health metrics.
                    Data is securely synchronized with your account and can be viewed in the Health Data tab.
                  </Text>
                </View>
              </>
            )}
            
            {activeTab === 'data' && renderHealthData()}
            
            {activeTab === 'settings' && renderSettings()}
          </>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderBottomWidth: 2,
  },
  activeTab: {
    borderBottomWidth: 2,
  },
  tabText: {
    marginLeft: 8,
    fontSize: 15,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingVertical: 20,
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  deviceCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
  },
  deviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  deviceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deviceTextContainer: {
    marginLeft: 12,
  },
  deviceName: {
    fontSize: 16,
    fontWeight: '600',
  },
  deviceType: {
    fontSize: 13,
    marginTop: 2,
  },
  divider: {
    height: 1,
    marginVertical: 12,
  },
  deviceDetails: {
    marginBottom: 12,
  },
  deviceDetailLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  deviceDetailValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  deviceActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  deviceAction: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    flex: 0.48,
  },
  deviceActionText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    marginTop: 40,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
  },
  connectButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  connectButtonText: {
    marginLeft: 8,
    fontSize: 15,
    fontWeight: '600',
  },
  platformSupport: {
    marginTop: 16,
    marginBottom: 16,
  },
  platformTitle: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 16,
  },
  platformIcons: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
  },
  platformIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 1,
    paddingVertical: 10,
    paddingHorizontal: 12,
    marginRight: 12,
    marginBottom: 12,
  },
  platformText: {
    marginLeft: 8,
    fontSize: 14,
  },
  deviceInfoSection: {
    marginVertical: 20,
  },
  infoTitle: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 15,
  },
  notSupportedContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  notSupportedIcon: {
    marginBottom: 16,
  },
  notSupportedTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 12,
    textAlign: 'center',
  },
  notSupportedText: {
    fontSize: 15,
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: 20,
  },
  dataContainer: {
    flex: 1,
  },
  dataHeader: {
    marginBottom: 16,
  },
  dataTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  lastUpdated: {
    fontSize: 13,
  },
  dataTypeSelector: {
    marginBottom: 20,
  },
  dataTypeSelectorContent: {
    paddingRight: 20,
  },
  dataTypeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 10,
  },
  dataTypeText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
  },
  chartContainer: {
    height: 200,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  barChart: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  barContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  bar: {
    width: 20,
    borderRadius: 4,
    marginBottom: 8,
  },
  barLabel: {
    fontSize: 10,
  },
  dataActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  dataAction: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flex: 0.48,
  },
  dataActionText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '600',
  },
  syncButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  syncButtonText: {
    marginLeft: 8,
    fontSize: 15,
    fontWeight: '600',
  },
  // Settings styles
  settingsContainer: {
    flex: 1,
  },
  settingSection: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 24,
    borderWidth: 1,
  },
  settingSectionTitle: {
    fontSize: 17,
    fontWeight: '600',
    padding: 16,
    borderBottomWidth: 1,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    marginRight: 12,
  },
  settingTextContainer: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 13,
    lineHeight: 18,
  },
  settingSubtext: {
    fontSize: 12,
    marginTop: 4,
  },
  iconButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  settingAction: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
}); 