import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, ScrollView, Image, ActivityIndicator, FlatList } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , MaterialIcons , FontAwesome5, Ionicons } from '@expo/vector-icons';
import { getHistory, getFoodsByDate } from '@/services/foodCacheService';
import { explainNutritionalImpact } from '@/services/openai/nutritionalExplanation';

// Define the interfaces for food data
interface FoodItem {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  confidence?: number;
  servingSize?: string;
  cookingMethod?: string;
  ingredients?: string[];
  estimatedAmount?: string;
}

interface FoodHistoryItem {
  id: string;
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  description?: string;
  items: FoodItem[];
  imageUri: string;
  date: string;
  isSaved: boolean;
  servingInfo?: {
    servingSize: string;
    totalServings: number;
    caloriesPerServing: number;
    proteinPerServing: number;
    carbsPerServing: number;
    fatPerServing: number;
  };
  healthHighlights?: {
    positives: string[];
    considerations: string[];
  };
}

interface NutritionalExplanation {
  summary: string;
  healthBenefits: string[];
  considerations: string[];
  dietaryImplications: string[];
  balanceAssessment: string;
}

export default function DietitianScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const [foodHistory, setFoodHistory] = useState<FoodHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedFood, setSelectedFood] = useState<FoodHistoryItem | null>(null);
  const [nutritionalExplanation, setNutritionalExplanation] = useState<NutritionalExplanation | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [activeTab, setActiveTab] = useState<'insights' | 'history'>('insights');

  useEffect(() => {
    loadFoodHistory();
  }, []);

  useEffect(() => {
    if (selectedFood) {
      analyzeSelectedFood(selectedFood);
    }
  }, [selectedFood]);

  const loadFoodHistory = async () => {
    try {
      setIsLoading(true);
      const history = await getHistory();
      setFoodHistory(history);
      
      // If we have history items, select the most recent one by default
      if (history.length > 0) {
        setSelectedFood(history[0]);
      }
    } catch (error) {
      console.error('Error loading food history:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const analyzeSelectedFood = async (food: FoodHistoryItem) => {
    try {
      setIsAnalyzing(true);
      const result = await explainNutritionalImpact({
        name: food.name,
        calories: food.calories,
        protein: food.protein,
        carbs: food.carbs,
        fat: food.fat,
        items: food.items
      });

      if (result.success && result.explanation) {
        setNutritionalExplanation(result.explanation);
      }
    } catch (error) {
      console.error('Error analyzing food:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleBack = () => {
    // Navigate directly to the home tab instead of going back
    router.replace('/(tabs)');
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
  };

  const renderHistoryItem = ({ item }: { item: FoodHistoryItem }) => {
    const isSelected = selectedFood?.id === item.id;
    
    return (
      <TouchableOpacity
        style={[
          styles.historyItem,
          { 
            backgroundColor: isDark ? '#1A1C2A' : '#FFFFFF',
            borderColor: isSelected ? colors.primary : isDark ? '#2A2E3F' : '#EEEEEE',
          }
        ]}
        onPress={() => setSelectedFood(item)}
      >
        <View style={styles.historyItemContent}>
          {item.imageUri ? (
            <Image 
              source={{ uri: item.imageUri }} 
              style={styles.foodThumbnail}
              resizeMode="cover"
            />
          ) : (
            <View style={[styles.foodThumbnail, { backgroundColor: isDark ? '#2A2E3F' : '#F5F5F5' }]}>
              <MaterialIcons name="restaurant" size={24} color={colors.primary} />
            </View>
          )}
          <View style={styles.historyItemDetails}>
            <Text style={[styles.foodName, { color: colors.text }]} numberOfLines={1}>
              {item.name}
            </Text>
            <Text style={[styles.foodDate, { color: isDark ? '#AAAAAA' : '#666666' }]}>
              {formatDate(item.date)}
            </Text>
            <View style={styles.macroRow}>
              <Text style={[styles.macroText, { color: isDark ? '#BBBBBB' : '#444444' }]}>
                {item.calories} cal
              </Text>
              <Text style={[styles.macroText, { color: isDark ? '#BBBBBB' : '#444444' }]}>
                P: {item.protein}g
              </Text>
              <Text style={[styles.macroText, { color: isDark ? '#BBBBBB' : '#444444' }]}>
                C: {item.carbs}g
              </Text>
              <Text style={[styles.macroText, { color: isDark ? '#BBBBBB' : '#444444' }]}>
                F: {item.fat}g
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyHistory = () => (
    <View style={styles.emptyContainer}>
      <FontAwesome5 name="utensils" size={48} color={isDark ? '#3A3C4A' : '#DDDDDD'} />
      <Text style={[styles.emptyTitle, { color: colors.text }]}>No Food Scans Yet</Text>
      <Text style={[styles.emptyDescription, { color: isDark ? '#AAAAAA' : '#666666' }]}>
        Scan meals to get personalized nutritional insights from our AI dietitian.
      </Text>
    </View>
  );

  const renderInsights = () => {
    if (!selectedFood) {
      return (
        <View style={styles.noSelectionContainer}>
          <Text style={[styles.noSelectionText, { color: colors.text }]}>
            Select a meal to see personalized insights
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.insightsContainer}>
        <View style={[styles.foodCard, { backgroundColor: isDark ? '#1A1C2A' : '#FFFFFF' }]}>
          <View style={styles.foodCardHeader}>
            {selectedFood.imageUri ? (
              <Image 
                source={{ uri: selectedFood.imageUri }} 
                style={styles.selectedFoodImage}
                resizeMode="cover"
              />
            ) : (
              <View style={[styles.selectedFoodImage, { backgroundColor: isDark ? '#2A2E3F' : '#F5F5F5' }]}>
                <MaterialIcons name="restaurant" size={36} color={colors.primary} />
              </View>
            )}
            <View style={styles.foodCardTitleContainer}>
              <Text style={[styles.foodCardTitle, { color: colors.text }]}>
                {selectedFood.name}
              </Text>
              <Text style={[styles.foodCardDate, { color: isDark ? '#AAAAAA' : '#666666' }]}>
                {formatDate(selectedFood.date)}
              </Text>
            </View>
          </View>
          
          <View style={styles.nutritionSummary}>
            <View style={styles.nutritionItem}>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>{selectedFood.calories}</Text>
              <Text style={[styles.nutritionLabel, { color: isDark ? '#AAAAAA' : '#666666' }]}>calories</Text>
            </View>
            <View style={styles.nutritionItem}>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>{selectedFood.protein}g</Text>
              <Text style={[styles.nutritionLabel, { color: isDark ? '#AAAAAA' : '#666666' }]}>protein</Text>
            </View>
            <View style={styles.nutritionItem}>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>{selectedFood.carbs}g</Text>
              <Text style={[styles.nutritionLabel, { color: isDark ? '#AAAAAA' : '#666666' }]}>carbs</Text>
            </View>
            <View style={styles.nutritionItem}>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>{selectedFood.fat}g</Text>
              <Text style={[styles.nutritionLabel, { color: isDark ? '#AAAAAA' : '#666666' }]}>fat</Text>
            </View>
          </View>
        </View>

        {isAnalyzing ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.text }]}>
              Analyzing nutritional profile...
            </Text>
          </View>
        ) : nutritionalExplanation ? (
          <View style={styles.analysisContainer}>
            <Text style={[styles.analysisTitle, { color: colors.text }]}>
              AI Dietitian Analysis
            </Text>
            
            <Text style={[styles.summarySectionTitle, { color: colors.primary }]}>
              Nutritional Summary
            </Text>
            <Text style={[styles.summaryText, { color: colors.text }]}>
              {nutritionalExplanation.summary}
            </Text>
            
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>
              Health Benefits
            </Text>
            {nutritionalExplanation.healthBenefits.map((benefit, index) => (
              <View key={`benefit-${index}`} style={styles.bulletPoint}>
                <FontAwesome5 name="check-circle" size={16} color={colors.primary} style={styles.bulletIcon} />
                <Text style={[styles.bulletText, { color: colors.text }]}>{benefit}</Text>
              </View>
            ))}
            
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>
              Considerations
            </Text>
            {nutritionalExplanation.considerations.map((consideration, index) => (
              <View key={`consideration-${index}`} style={styles.bulletPoint}>
                <FontAwesome5 name="info-circle" size={16} color={isDark ? '#FFCC00' : '#FF9500'} style={styles.bulletIcon} />
                <Text style={[styles.bulletText, { color: colors.text }]}>{consideration}</Text>
              </View>
            ))}
            
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>
              Balance Assessment
            </Text>
            <Text style={[styles.balanceText, { color: colors.text }]}>
              {nutritionalExplanation.balanceAssessment}
            </Text>

            <TouchableOpacity 
              style={[styles.alternativesButton, { backgroundColor: colors.primary }]}
              onPress={() => {
                if (selectedFood) {
                  router.push({
                    pathname: "/recipe/alternatives",
                    params: {
                      originalImageUri: selectedFood.imageUri,
                      originalData: JSON.stringify({
                        name: selectedFood.name,
                        calories: selectedFood.calories,
                        protein: selectedFood.protein,
                        carbs: selectedFood.carbs,
                        fat: selectedFood.fat
                      })
                    }
                  });
                }
              }}
            >
              <Text style={styles.alternativesButtonText}>View Healthier Alternatives</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.noAnalysisContainer}>
            <Text style={[styles.noAnalysisText, { color: colors.text }]}>
              Nutritional analysis not available for this meal.
            </Text>
          </View>
        )}
      </View>
    );
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            Loading food history...
          </Text>
        </View>
      );
    }

    if (activeTab === 'insights') {
      return renderInsights();
    }

    return (
      <View style={styles.historyContainer}>
        {foodHistory.length > 0 ? (
          <FlatList
            data={foodHistory}
            renderItem={renderHistoryItem}
            keyExtractor={item => item.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.historyList}
          />
        ) : (
          renderEmptyHistory()
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen options={{ headerShown: false }} />
      
      {/* Custom Header */}
      <View style={[styles.header, { borderBottomColor: isDark ? '#2A3040' : '#EEEEEE' }]}>
        <TouchableOpacity 
          onPress={handleBack}
          style={styles.backButton}
          accessibilityLabel="Go back"
          accessibilityRole="button"
        >
          <Feather name="chevron-left" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>AI Dietitian</Text>
        <View style={styles.rightPlaceholder} />
      </View>

      {/* Tab Navigation */}
      <View style={[styles.tabBar, { borderBottomColor: isDark ? '#2A3040' : '#EEEEEE' }]}>
        <TouchableOpacity 
          style={[
            styles.tab, 
            activeTab === 'insights' && [styles.activeTab, { borderBottomColor: colors.primary }]
          ]}
          onPress={() => setActiveTab('insights')}
        >
          <Ionicons 
            name="analytics" 
            size={20} 
            color={activeTab === 'insights' ? colors.primary : isDark ? '#8899AA' : '#667788'} 
            style={styles.tabIcon}
          />
          <Text 
            style={[
              styles.tabText, 
              { color: activeTab === 'insights' ? colors.primary : isDark ? '#8899AA' : '#667788' }
            ]}
          >
            Insights
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[
            styles.tab, 
            activeTab === 'history' && [styles.activeTab, { borderBottomColor: colors.primary }]
          ]}
          onPress={() => setActiveTab('history')}
        >
          <Ionicons 
            name="time" 
            size={20} 
            color={activeTab === 'history' ? colors.primary : isDark ? '#8899AA' : '#667788'} 
            style={styles.tabIcon}
          />
          <Text 
            style={[
              styles.tabText, 
              { color: activeTab === 'history' ? colors.primary : isDark ? '#8899AA' : '#667788' }
            ]}
          >
            History
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* Main Content */}
      <View style={styles.contentContainer}>
        {renderContent()}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  rightPlaceholder: {
    width: 40,
  },
  tabBar: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  activeTab: {
    borderBottomWidth: 2,
  },
  tabIcon: {
    marginRight: 6,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  contentContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  historyContainer: {
    flex: 1,
  },
  historyList: {
    padding: 16,
  },
  historyItem: {
    borderRadius: 12,
    marginBottom: 12,
    padding: 12,
    borderWidth: 2,
  },
  historyItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  foodThumbnail: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  historyItemDetails: {
    flex: 1,
    marginLeft: 12,
  },
  foodName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  foodDate: {
    fontSize: 13,
    marginBottom: 6,
  },
  macroRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  macroText: {
    fontSize: 12,
    marginRight: 10,
  },
  insightsContainer: {
    flex: 1,
    padding: 16,
  },
  noSelectionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  noSelectionText: {
    fontSize: 16,
    textAlign: 'center',
  },
  foodCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 3,
  },
  foodCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedFoodImage: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
  },
  foodCardTitleContainer: {
    flex: 1,
    marginLeft: 16,
  },
  foodCardTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  foodCardDate: {
    fontSize: 14,
  },
  nutritionSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(150, 150, 150, 0.2)',
  },
  nutritionItem: {
    alignItems: 'center',
  },
  nutritionValue: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  nutritionLabel: {
    fontSize: 12,
  },
  analysisContainer: {
    backgroundColor: 'transparent',
    padding: 16,
  },
  analysisTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 16,
    textAlign: 'center',
  },
  summarySectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 8,
  },
  summaryText: {
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    marginTop: 16,
    marginBottom: 8,
  },
  bulletPoint: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  bulletIcon: {
    marginTop: 3,
    marginRight: 8,
  },
  bulletText: {
    fontSize: 15,
    lineHeight: 22,
    flex: 1,
  },
  balanceText: {
    fontSize: 15,
    lineHeight: 22,
  },
  noAnalysisContainer: {
    padding: 24,
    alignItems: 'center',
  },
  noAnalysisText: {
    fontSize: 16,
    textAlign: 'center',
  },
  alternativesButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 24,
  },
  alternativesButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
}); 