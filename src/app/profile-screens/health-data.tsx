import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Platform, Dimensions, ActivityIndicator, Alert } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { Path, Circle, G, Text as SvgText, Line, Defs, LinearGradient as SvgLinearGradient, Stop } from 'react-native-svg';
import { HealthDashboard } from '@/components/HealthDashboard';
import { initHealthIntegration, isWearableSupported } from '@/services/healthIntegrationService';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface HealthMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  change?: number;
  history: { date: string; value: number }[];
  icon: React.ElementType;
  color: [string, string];
}

const { width: screenWidth } = Dimensions.get('window');
const CHART_WIDTH = screenWidth - 40;
const CHART_HEIGHT = 180;

export default function HealthDataScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const [activeTimeframe, setActiveTimeframe] = useState<string>('month');
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  
  // Sample health metrics data
  const healthMetrics: HealthMetric[] = [
    {
      id: 'weight',
      name: 'Weight',
      value: 165,
      unit: 'lbs',
      change: -2.5,
      history: [
        { date: '2023-04-01', value: 170 },
        { date: '2023-04-15', value: 168 },
        { date: '2023-05-01', value: 167 },
        { date: '2023-05-15', value: 165.5 },
        { date: '2023-06-01', value: 165 },
      ],
      icon: Weight,
      color: ['#3B82F6', '#2563EB'],
    },
    {
      id: 'height',
      name: 'Height',
      value: 71,
      unit: 'in',
      history: [
        { date: '2023-04-01', value: 71 },
        { date: '2023-05-01', value: 71 },
        { date: '2023-06-01', value: 71 },
      ],
      icon: Ruler,
      color: ['#8B5CF6', '#7C3AED'],
    },
    {
      id: 'bmi',
      name: 'BMI',
      value: 23.1,
      unit: '',
      change: -0.4,
      history: [
        { date: '2023-04-01', value: 23.9 },
        { date: '2023-04-15', value: 23.6 },
        { date: '2023-05-01', value: 23.4 },
        { date: '2023-05-15', value: 23.2 },
        { date: '2023-06-01', value: 23.1 },
      ],
      icon: Activity,
      color: ['#EC4899', '#DB2777'],
    },
    {
      id: 'restingHeartRate',
      name: 'Resting Heart Rate',
      value: 62,
      unit: 'bpm',
      change: -3,
      history: [
        { date: '2023-04-01', value: 65 },
        { date: '2023-04-15', value: 64 },
        { date: '2023-05-01', value: 63 },
        { date: '2023-05-15', value: 63 },
        { date: '2023-06-01', value: 62 },
      ],
      icon: Heart,
      color: ['#EF4444', '#DC2626'],
    },
    {
      id: 'bodyfat',
      name: 'Body Fat',
      value: 18.5,
      unit: '%',
      change: -1.2,
      history: [
        { date: '2023-04-01', value: 20.5 },
        { date: '2023-04-15', value: 19.8 },
        { date: '2023-05-01', value: 19.2 },
        { date: '2023-05-15', value: 18.8 },
        { date: '2023-06-01', value: 18.5 },
      ],
      icon: TrendingUp,
      color: ['#F59E0B', '#D97706'],
    },
  ];
  
  // Timeframes for filter tabs
  const timeframes = [
    { id: 'week', name: '1W' },
    { id: 'month', name: '1M' },
    { id: 'quarter', name: '3M' },
    { id: 'year', name: '1Y' },
    { id: 'all', name: 'All' },
  ];
  
  // Helper function to render change indicator
  const renderChangeIndicator = (change?: number) => {
    if (!change) return null;
    
    const isPositive = change > 0;
    const color = isPositive ? '#EF4444' : '#10B981'; // Red for increase, green for decrease
    const sign = isPositive ? '+' : '';
    
    return (
      <View style={[styles.changeIndicator, { backgroundColor: `${color}20` }]}>
        {isPositive ? (
          <Feather name="trending-up" size={12} color={color} style={styles.changeIcon} />
        ) : (
          <Feather name="trending-up" size={12} color={color} style={[styles.changeIcon, styles.changeIconDown]} />
        )}
        <Text style={[styles.changeText, { color }]}>
          {sign}{change}{healthMetrics[0].id === 'weight' ? ' lbs' : ''}
        </Text>
      </View>
    );
  };
  
  // Get the latest metrics to show in summary
  const latestWeight = healthMetrics.find(m => m.id === 'weight')?.value || 0;
  const latestHeight = healthMetrics.find(m => m.id === 'height')?.value || 0;
  const latestBMI = healthMetrics.find(m => m.id === 'bmi')?.value || 0;
  
  // Function to get BMI status text and color
  const getBMIStatus = (bmi: number): { text: string; color: string } => {
    if (bmi < 18.5) return { text: 'Underweight', color: '#3B82F6' };
    if (bmi < 25) return { text: 'Healthy', color: '#10B981' };
    if (bmi < 30) return { text: 'Overweight', color: '#F59E0B' };
    return { text: 'Obese', color: '#EF4444' };
  };
  
  const bmiStatus = getBMIStatus(latestBMI);
  
  // Function to find min and max values for chart scaling
  const getMinMaxValues = (metric: HealthMetric) => {
    const values = metric.history.map(h => h.value);
    let min = Math.min(...values);
    let max = Math.max(...values);
    
    // Add some padding
    const padding = (max - min) * 0.2;
    min = min - padding;
    max = max + padding;
    
    return { min, max };
  };
  
  // Function to draw chart path
  const getChartPath = (metric: HealthMetric) => {
    const { min, max } = getMinMaxValues(metric);
    
    return metric.history.map((point, index) => {
      const x = (index / (metric.history.length - 1)) * CHART_WIDTH;
      // Invert Y axis since SVG 0,0 is top-left
      const y = CHART_HEIGHT - ((point.value - min) / (max - min)) * CHART_HEIGHT;
      return `${index === 0 ? 'M' : 'L'}${x},${y}`;
    }).join(' ');
  };

  useEffect(() => {
    checkHealthIntegration();
  }, []);

  const checkHealthIntegration = async () => {
    setIsLoading(true);
    try {
      const isSupported = isWearableSupported();
      if (isSupported) {
        const isInitialized = await initHealthIntegration();
        setIsConnected(isInitialized);
      }
    } catch (error) {
      console.error('Error checking health integration:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleConnectDevice = async () => {
    if (isConnected) {
      Alert.alert(
        'Disconnect Device',
        'Are you sure you want to disconnect your wearable device?',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Disconnect', 
            style: 'destructive',
            onPress: () => {
              // Would handle disconnection here
              setIsConnected(false);
              Alert.alert('Device Disconnected', 'Your wearable device has been disconnected.');
            }
          }
        ]
      );
      return;
    }

    setIsScanning(true);
    try {
      const result = await initHealthIntegration();
      setIsConnected(result);
      
      if (result) {
        Alert.alert('Device Connected', 'Your wearable device has been successfully connected.');
      } else {
        Alert.alert('Connection Failed', 'Unable to connect to wearable device. Please try again.');
      }
    } catch (error) {
      Alert.alert('Connection Error', 'There was an error connecting to your device.');
    } finally {
      setIsScanning(false);
    }
  };

  return (
    <>
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Custom Header */}
        <LinearGradient
          colors={isDark ? ['#1E293B', '#0F172A'] : ['#3B82F6', '#2563EB']}
          style={[styles.header, { paddingTop: insets.top || 40 }]}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity 
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Feather name="arrow-left" size={22}  color={colors.text} />
            </TouchableOpacity>
            
            <Text style={styles.headerTitle}>Health Data</Text>
            
            <TouchableOpacity 
              style={styles.settingsButton}
              onPress={() => router.push('/profile-screens/health-settings')}
            >
              <Feather name="settings" size={22}  color={colors.text} />
            </TouchableOpacity>
          </View>

          <View style={styles.deviceStatusContainer}>
            <View style={[
              styles.deviceStatus, 
              { backgroundColor: isConnected ? 'rgba(34, 197, 94, 0.2)' : 'rgba(255, 255, 255, 0.2)' }
            ]}>
              {isConnected ? (
                <>
                  <Feather name="link" size={16}  color={colors.text} />
                  <Text style={styles.deviceStatusText}>Device Connected</Text>
                </>
              ) : (
                <>
                  <Feather name="unlink" size={16}  color={colors.text} />
                  <Text style={styles.deviceStatusText}>No Device Connected</Text>
                </>
              )}
            </View>
            
            <TouchableOpacity 
              style={[
                styles.connectButton,
                { backgroundColor: isConnected ? 'rgba(239, 68, 68, 0.2)' : 'rgba(34, 197, 94, 0.2)' }
              ]}
              onPress={handleConnectDevice}
              disabled={isScanning}
            >
              {isScanning ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Text style={[
                  styles.connectButtonText,
                  { color: isConnected ? '#EF4444' : '#22C55E' }
                ]}>
                  {isConnected ? 'Disconnect' : 'Connect'}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </LinearGradient>
        
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.text }]}>
              Loading health data...
            </Text>
          </View>
        ) : (
          <HealthDashboard 
            onConnectDevice={handleConnectDevice}
            showWaterTracker={true}
          />
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: 'white',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  deviceStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  deviceStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  deviceStatusText: {
    fontSize: 14,
    color: 'white',
    marginLeft: 8,
  },
  connectButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 100,
  },
  connectButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  summaryCard: {
    margin: 20,
    marginTop: 10,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 4 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      }
    }),
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  lastUpdated: {
    fontSize: 12,
  },
  summaryMetrics: {
    marginBottom: 20,
  },
  summaryMetric: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  metricIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 14,
  },
  metricLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  metricValueRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 18,
    fontWeight: '700',
    marginRight: 4,
  },
  metricUnit: {
    fontSize: 14,
    marginRight: 6,
  },
  changeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  changeIcon: {
    marginRight: 2,
  },
  changeIconDown: {
    transform: [{ rotate: '180deg' }],
  },
  changeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  bmiStatus: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    marginLeft: 6,
  },
  bmiStatusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  addMeasurementButton: {
    overflow: 'hidden',
    borderRadius: 12,
  },
  addMeasurementGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  addMeasurementText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  timeframesContainer: {
    paddingHorizontal: 16,
    flexDirection: 'row',
    marginBottom: 16,
  },
  timeframeTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
  },
  activeTimeframe: {
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    borderColor: '#3B82F6',
  },
  timeframeText: {
    fontSize: 14,
    fontWeight: '600',
  },
  metricCard: {
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 4 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      }
    }),
  },
  metricHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  metricTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metricTitleIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  metricTitle: {
    fontSize: 16,
    fontWeight: '700',
  },
  metricHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metricCurrentValue: {
    fontSize: 16,
    fontWeight: '700',
    marginRight: 6,
  },
  metricCurrentUnit: {
    fontSize: 12,
    fontWeight: '400',
  },
  chartContainer: {
    marginBottom: 8,
    alignItems: 'center',
  },
  chartLabelsContainer: {
    position: 'relative',
    height: 20,
    marginBottom: 10,
  },
  chartLabel: {
    position: 'absolute',
    fontSize: 12,
  },
  viewHistoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
  },
  viewHistoryText: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 4,
  },
}); 