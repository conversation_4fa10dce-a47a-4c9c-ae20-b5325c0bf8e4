import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Switch, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import { isWearableSupported, initHealthIntegration } from '@/services/healthIntegrationService';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default function HealthSettingsScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  
  // State for settings
  const [isLoading, setIsLoading] = useState(true);
  const [wearableConnected, setWearableConnected] = useState(false);
  const [autoSync, setAutoSync] = useState(true);
  const [useMetricUnits, setUseMetricUnits] = useState(false);
  const [healthNotifications, setHealthNotifications] = useState(true);
  const [privacyMode, setPrivacyMode] = useState(false);
  const [syncFrequency, setSyncFrequency] = useState('hourly');
  const [isSyncing, setIsSyncing] = useState(false);
  
  useEffect(() => {
    checkWearableStatus();
  }, []);
  
  const checkWearableStatus = async () => {
    setIsLoading(true);
    try {
      const isSupported = isWearableSupported();
      if (isSupported) {
        const isConnected = await initHealthIntegration();
        setWearableConnected(isConnected);
      }
    } catch (error) {
      console.error('Error checking wearable status:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleWearableToggle = async () => {
    if (wearableConnected) {
      Alert.alert(
        'Disconnect Wearable',
        'Are you sure you want to disconnect your wearable device? This will stop syncing health data.',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Disconnect', 
            style: 'destructive', 
            onPress: () => {
              // Implement disconnect logic here
              setWearableConnected(false);
              Alert.alert('Device Disconnected', 'Your wearable has been disconnected.');
            }
          }
        ]
      );
    } else {
      try {
        setIsLoading(true);
        const isConnected = await initHealthIntegration();
        setWearableConnected(isConnected);
        if (isConnected) {
          Alert.alert('Wearable Connected', 'Your wearable device has been successfully connected.');
        } else {
          Alert.alert('Connection Failed', 'Unable to connect to wearable device. Please try again.');
        }
      } catch (error) {
        Alert.alert('Connection Error', 'There was an error connecting to your device.');
      } finally {
        setIsLoading(false);
      }
    }
  };
  
  const handleManualSync = async () => {
    setIsSyncing(true);
    try {
      // Implement manual sync logic
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulated sync delay
      Alert.alert('Sync Complete', 'Your health data has been successfully synchronized.');
    } catch (error) {
      Alert.alert('Sync Failed', 'Failed to synchronize health data. Please try again.');
    } finally {
      setIsSyncing(false);
    }
  };
  
  const handleSyncFrequencyChange = (frequency: string) => {
    setSyncFrequency(frequency);
  };
  
  const handleClearHealthData = () => {
    Alert.alert(
      'Clear Health Data',
      'Are you sure you want to clear all your health data? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Clear All Data', 
          style: 'destructive', 
          onPress: () => {
            // Implement clear data logic
            Alert.alert('Data Cleared', 'All your health data has been cleared.');
          }
        }
      ]
    );
  };
  
  const renderSetting = (
    icon: React.ReactNode,
    title: string,
    description: string,
    control: React.ReactNode
  ) => (
    <View style={[styles.settingItem, { borderBottomColor: colors.border }]}>
      <View style={styles.settingIconContainer}>
        {icon}
      </View>
      <View style={styles.settingContent}>
        <Text style={[styles.settingTitle, { color: colors.text }]}>{title}</Text>
        <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>{description}</Text>
      </View>
      <View style={styles.settingControl}>
        {control}
      </View>
    </View>
  );
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen options={{ headerShown: false }} />
      
      {/* Header */}
      <LinearGradient
        colors={isDark ? ['#1E293B', '#0F172A'] : ['#3B82F6', '#2563EB']}
        style={[styles.header, { paddingTop: insets.top || 40 }]}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Feather name="arrow-left" size={22}  color={colors.text} />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>Health Settings</Text>
          
          <View style={styles.placeholderButton} />
        </View>
      </LinearGradient>
      
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>Loading settings...</Text>
        </View>
      ) : (
        <ScrollView style={styles.scrollView}>
          <View style={styles.sectionContainer}>
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>WEARABLE DEVICE</Text>
            
            {renderSetting(
              <Feather name="smartphone" size={22} color={wearableConnected ? colors.primary : colors.textSecondary} />,
              'Wearable Connection',
              wearableConnected ? 'Your wearable device is currently connected' : 'Connect your wearable device to track health data',
              <Switch
                value={wearableConnected}
                onValueChange={handleWearableToggle}
                trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                thumbColor={wearableConnected ? colors.primary : '#f4f3f4'}
              />
            )}
            
            {wearableConnected && (
              <>
                {renderSetting(
                  <Feather name="refresh-cw" size={22} color={autoSync ? colors.primary : colors.textSecondary} />,
                  'Auto-Sync',
                  'Automatically sync data from your wearable device',
                  <Switch
                    value={autoSync}
                    onValueChange={setAutoSync}
                    trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                    thumbColor={autoSync ? colors.primary : '#f4f3f4'}
                  />
                )}
                
                <View style={[styles.settingItem, { borderBottomColor: colors.border }]}>
                  <View style={styles.settingIconContainer}>
                    <Feather name="refresh-cw" size={22} color={colors.textSecondary} />
                  </View>
                  <View style={styles.settingContent}>
                    <Text style={[styles.settingTitle, { color: colors.text }]}>Sync Frequency</Text>
                    <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
                      How often to sync with your wearable
                    </Text>
                    
                    <View style={styles.syncFrequencyOptions}>
                      {['hourly', 'daily', 'manual'].map(freq => (
                        <TouchableOpacity 
                          key={freq}
                          style={[
                            styles.syncFrequencyOption,
                            syncFrequency === freq && {
                              backgroundColor: `${colors.primary}20`,
                              borderColor: colors.primary
                            },
                            { borderColor: colors.border }
                          ]}
                          onPress={() => handleSyncFrequencyChange(freq)}
                        >
                          <Text 
                            style={[
                              styles.syncFrequencyText,
                              { color: syncFrequency === freq ? colors.primary : colors.text }
                            ]}
                          >
                            {freq.charAt(0).toUpperCase() + freq.slice(1)}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>
                </View>
                
                <TouchableOpacity
                  style={[styles.syncButton, {
                    backgroundColor: isSyncing ? `${colors.primary}50` : `${colors.primary}20`,
                    borderColor: colors.primary
                  }]}
                  onPress={handleManualSync}
                  disabled={isSyncing}
                >
                  {isSyncing ? (
                    <ActivityIndicator size="small" color={colors.primary} />
                  ) : (
                    <>
                      <Feather name="refresh-cw" size={18} color={colors.primary} style={styles.syncIcon} />
                      <Text style={[styles.syncButtonText, { color: colors.primary }]}>Sync Now</Text>
                    </>
                  )}
                </TouchableOpacity>
              </>
            )}
          </View>
          
          <View style={styles.sectionContainer}>
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>PREFERENCES</Text>
            
            {renderSetting(
              <MaterialIcons name="straighten" size={22} color={useMetricUnits ? colors.primary : colors.textSecondary} />,
              'Metric Units',
              useMetricUnits ? 'Using kg, cm, etc.' : 'Using lbs, in, etc.',
              <Switch
                value={useMetricUnits}
                onValueChange={setUseMetricUnits}
                trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                thumbColor={useMetricUnits ? colors.primary : '#f4f3f4'}
              />
            )}
            
            {renderSetting(
              <Feather name="bell" size={22} color={healthNotifications ? colors.primary : colors.textSecondary} />,
              'Health Notifications',
              'Get alerts about your health metrics',
              <Switch
                value={healthNotifications}
                onValueChange={setHealthNotifications}
                trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                thumbColor={healthNotifications ? colors.primary : '#f4f3f4'}
              />
            )}
          </View>
          
          <View style={styles.sectionContainer}>
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>PRIVACY & DATA</Text>
            
            {renderSetting(
              <Feather name="lock" size={22} color={privacyMode ? colors.primary : colors.textSecondary} />,
              'Privacy Mode',
              'Hide health data from the home screen',
              <Switch
                value={privacyMode}
                onValueChange={setPrivacyMode}
                trackColor={{ false: colors.border, true: `${colors.primary}80` }}
                thumbColor={privacyMode ? colors.primary : '#f4f3f4'}
              />
            )}
            
            <TouchableOpacity 
              style={[styles.dangerButton, { borderColor: '#EF4444' }]}
              onPress={handleClearHealthData}
            >
              <Text style={styles.dangerButtonText}>Clear All Health Data</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.footer}>
            <Text style={[styles.footerText, { color: colors.textSecondary }]}>
              Your health data is stored securely and is not shared with third parties.
            </Text>
          </View>
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: 'white',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  placeholderButton: {
    width: 40,
    height: 40,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  sectionContainer: {
    paddingHorizontal: 16,
    paddingTop: 24,
    paddingBottom: 8,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
    letterSpacing: 1,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  settingIconContainer: {
    width: 40,
    alignItems: 'center',
  },
  settingContent: {
    flex: 1,
    marginLeft: 12,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
  },
  settingControl: {
    marginLeft: 12,
  },
  syncFrequencyOptions: {
    flexDirection: 'row',
    marginTop: 12,
  },
  syncFrequencyOption: {
    borderWidth: 1,
    borderRadius: 20,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 8,
  },
  syncFrequencyText: {
    fontSize: 14,
    fontWeight: '500',
  },
  syncButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    marginHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  syncIcon: {
    marginRight: 8,
  },
  syncButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  dangerButton: {
    marginTop: 24,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  dangerButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#EF4444',
  },
  footer: {
    padding: 24,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    textAlign: 'center',
  },
}); 