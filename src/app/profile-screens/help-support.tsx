import React, { useState } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, TextInput, Platform, Linking } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: 'account' | 'nutrition' | 'subscription' | 'technical';
}

interface SupportOption {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  action: () => void;
  gradient: [string, string];
}

export default function HelpSupportScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [message, setMessage] = useState('');
  
  // Sample FAQs
  const faqs: FAQ[] = [
    {
      id: '1',
      question: 'How accurate is the calorie tracking?',
      answer: 'Our calorie tracking is based on comprehensive nutritional databases and is typically accurate to within 10%. For packaged foods with barcodes, accuracy improves to about 95%. We regularly update our food database to ensure the most accurate information.',
      category: 'nutrition',
    },
    {
      id: '2',
      question: 'How do I cancel my subscription?',
      answer: 'You can cancel your subscription anytime from the Profile > Billing & Subscription section. Your premium features will remain active until the end of your current billing cycle.',
      category: 'subscription',
    },
    {
      id: '3',
      question: 'How do I reset my password?',
      answer: 'Go to the login screen and tap "Forgot Password". Enter your email address, and we\'ll send you a password reset link. If you don\'t receive the email, check your spam folder or contact support.',
      category: 'account',
    },
    {
      id: '4',
      question: 'The app is crashing, what should I do?',
      answer: 'Please try these steps: 1) Restart the app, 2) Ensure your app is updated to the latest version, 3) Restart your device, 4) If problems persist, contact our support team with details about your device and when the crash occurs.',
      category: 'technical',
    },
    {
      id: '5',
      question: 'Can I export my nutrition data?',
      answer: 'Yes! Go to Profile > Privacy & Data and tap "Export Your Data". You\'ll receive an email with a link to download your data in CSV format, which can be imported into spreadsheets or other apps.',
      category: 'account',
    },
    {
      id: '6',
      question: 'How does the AI meal recommendation work?',
      answer: 'Our AI analyzes your dietary preferences, nutritional goals, and meal history to suggest personalized meals. The more you use the app, the better the recommendations become, as the system learns your preferences.',
      category: 'nutrition',
    },
    {
      id: '7',
      question: 'Is my data secure and private?',
      answer: 'Yes, we take data privacy seriously. Your data is encrypted both in transit and at rest. We never sell your personal information to third parties. You can review and adjust your privacy settings in the app anytime.',
      category: 'account',
    },
  ];
  
  // Support options
  const supportOptions: SupportOption[] = [
    {
      id: 'chat',
      title: 'Live Chat',
      description: 'Chat with our support team',
      icon: MessageSquare,
      action: () => console.log('Open live chat'),
      gradient: ['#3B82F6', '#2563EB'],
    },
    {
      id: 'email',
      title: 'Email Support',
      description: 'Get help via email',
      icon: Mail,
      action: () => Linking.openURL('mailto:<EMAIL>'),
      gradient: ['#8B5CF6', '#7C3AED'],
    },
    {
      id: 'video',
      title: 'Video Guides',
      description: 'Watch tutorial videos',
      icon: Video,
      action: () => console.log('Open video guides'),
      gradient: ['#F59E0B', '#D97706'],
    },
    {
      id: 'docs',
      title: 'Documentation',
      description: 'Read detailed guides',
      icon: FileText,
      action: () => console.log('Open documentation'),
      gradient: ['#10B981', '#059669'],
    },
  ];
  
  // Filter FAQs based on search and category
  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) || 
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });
  
  // Toggle FAQ expansion
  const toggleFAQ = (id: string) => {
    setExpandedFAQ(expandedFAQ === id ? null : id);
  };
  
  // Handle contact form submission
  const handleSubmitMessage = () => {
    if (!message.trim()) {
      return;
    }
    
    // In a real app, this would send the message to your support system
    console.log('Submitting message:', message);
    setMessage('');
    alert('Your message has been sent. We\'ll get back to you soon!');
  };
  
  // Category options
  const categories = [
    { id: 'all', name: 'All' },
    { id: 'account', name: 'Account' },
    { id: 'nutrition', name: 'Nutrition' },
    { id: 'subscription', name: 'Subscription' },
    { id: 'technical', name: 'Technical' },
  ];

  return (
    <>
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.card }]}>
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={() => router.back()}
            accessibilityLabel="Go back"
            accessibilityHint="Returns to the previous screen"
          >
            <Feather name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Help & Support</Text>
          <View style={styles.placeholderRight} />
        </View>
        
        <ScrollView 
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Hero Section */}
          <View style={[styles.heroCard, { 
            backgroundColor: colors.card,
            borderColor: colors.border,
            shadowColor: colors.shadow
          }]}>
            <View style={styles.heroContent}>
              <View style={styles.heroIconContainer}>
                <LinearGradient
                  colors={['#3B82F6', '#2563EB'] as const}
                  style={styles.heroIconGradient}
                >
                  <Feather name="help-circle" size={32}  color={colors.text} />
                </LinearGradient>
              </View>
              <Text style={[styles.heroTitle, { color: colors.text }]}>
                How can we help you?
              </Text>
              <Text style={[styles.heroDescription, { color: colors.textSecondary }]}>
                Search our help center or browse the frequently asked questions below
              </Text>
            </View>
            
            {/* Search Bar */}
            <View style={[styles.searchContainer, { 
              backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
              borderColor: colors.border
            }]}>
              <Feather name="search" size={20} color={colors.textSecondary} style={styles.searchIcon} />
              <TextInput
                style={[styles.searchInput, { color: colors.text }]}
                placeholder="Search for help..."
                placeholderTextColor={colors.textSecondary}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity 
                  style={styles.clearButton}
                  onPress={() => setSearchQuery('')}
                >
                  <Text style={[styles.clearButtonText, { color: colors.primary }]}>Clear</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
          
          {/* Support Options */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Contact Support</Text>
            <View style={styles.supportOptionsGrid}>
              {supportOptions.map((option) => (
                <TouchableOpacity 
                  key={option.id}
                  style={[styles.supportOption, { 
                    backgroundColor: colors.card,
                    borderColor: colors.border,
                    shadowColor: colors.shadow
                  }]}
                  onPress={option.action}
                >
                  <LinearGradient
                    colors={option.gradient as readonly [string, string]}
                    style={styles.supportIconContainer}
                  >
                    <option.icon size={20} color="white" />
                  </LinearGradient>
                  <Text style={[styles.supportOptionTitle, { color: colors.text }]}>
                    {option.title}
                  </Text>
                  <Text style={[styles.supportOptionDescription, { color: colors.textSecondary }]} numberOfLines={2}>
                    {option.description}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          
          {/* Category Tabs */}
          <ScrollView 
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesContainer}
          >
            {categories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryTab,
                  selectedCategory === category.id && styles.activeCategory,
                  { borderColor: colors.border }
                ]}
                onPress={() => setSelectedCategory(category.id)}
              >
                <Text style={[
                  styles.categoryText,
                  { color: selectedCategory === category.id ? colors.primary : colors.textSecondary }
                ]}>
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
          
          {/* FAQ Section */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Frequently Asked Questions
              {filteredFAQs.length > 0 && ` (${filteredFAQs.length})`}
            </Text>
            
            {filteredFAQs.length > 0 ? (
              <View style={styles.faqContainer}>
                {filteredFAQs.map((faq) => (
                  <View 
                    key={faq.id}
                    style={[styles.faqItem, { 
                      backgroundColor: colors.card,
                      borderColor: colors.border,
                      shadowColor: colors.shadow
                    }]}
                  >
                    <TouchableOpacity 
                      style={styles.faqQuestion}
                      onPress={() => toggleFAQ(faq.id)}
                    >
                      <Text style={[styles.faqQuestionText, { color: colors.text }]}>
                        {faq.question}
                      </Text>
                      {expandedFAQ === faq.id ? (
                        <Feather name="chevron-up" size={20} color={colors.primary} />
                      ) : (
                        <Feather name="chevron-down" size={20} color={colors.textSecondary} />
                      )}
                    </TouchableOpacity>
                    
                    {expandedFAQ === faq.id && (
                      <View style={[styles.faqAnswer, { borderTopColor: colors.border }]}>
                        <Text style={[styles.faqAnswerText, { color: colors.textSecondary }]}>
                          {faq.answer}
                        </Text>
                      </View>
                    )}
                  </View>
                ))}
              </View>
            ) : (
              <View style={[styles.noResultsContainer, { backgroundColor: colors.card }]}>
                <Text style={[styles.noResultsText, { color: colors.textSecondary }]}>
                  No matching FAQs found. Try a different search term or category.
                </Text>
              </View>
            )}
          </View>
          
          {/* Contact Form */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Still Need Help?</Text>
            <View style={[styles.contactForm, { 
              backgroundColor: colors.card,
              borderColor: colors.border,
              shadowColor: colors.shadow
            }]}>
              <Text style={[styles.contactFormText, { color: colors.textSecondary }]}>
                Can't find what you're looking for? Send us a message and we'll get back to you as soon as possible.
              </Text>
              
              <View style={[styles.messageContainer, { 
                backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)',
                borderColor: colors.border
              }]}>
                <TextInput
                  style={[styles.messageInput, { color: colors.text }]}
                  placeholder="Type your message here..."
                  placeholderTextColor={colors.textSecondary}
                  value={message}
                  onChangeText={setMessage}
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
              </View>
              
              <TouchableOpacity 
                style={[styles.submitButton, 
                  !message.trim() ? { opacity: 0.6 } : {}
                ]}
                onPress={handleSubmitMessage}
                disabled={!message.trim()}
              >
                <LinearGradient
                  colors={['#3B82F6', '#2563EB'] as const}
                  style={styles.submitButtonGradient}
                >
                  <Text style={styles.submitButtonText}>Send Message</Text>
                  <Send size={16} color="white" style={styles.submitIcon} />
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
          
          {/* Help Center Link */}
          <TouchableOpacity 
            style={[styles.helpCenterLink, { borderColor: colors.border }]}
            onPress={() => Linking.openURL('https://help.calorielens.app')}
          >
            <Text style={[styles.helpCenterText, { color: colors.primary }]}>
              Visit our Help Center
            </Text>
            <ExternalLink size={16} color={colors.primary} />
          </TouchableOpacity>
          
          {/* Privacy Note */}
          <View style={styles.privacyNote}>
            <Feather name="lock" size={14} color={colors.textSecondary} style={styles.privacyIcon} />
            <Text style={[styles.privacyText, { color: colors.textSecondary }]}>
              Your messages are secure and confidential
            </Text>
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  placeholderRight: {
    width: 40,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  heroCard: {
    margin: 20,
    marginTop: 10,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 4 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      }
    }),
  },
  heroContent: {
    alignItems: 'center',
    marginBottom: 20,
  },
  heroIconContainer: {
    marginBottom: 16,
  },
  heroIconGradient: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  heroTitle: {
    fontSize: 22,
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
  },
  heroDescription: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  clearButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  clearButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  section: {
    marginHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  supportOptionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -6,
  },
  supportOption: {
    width: '48%',
    margin: '1%',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: { elevation: 2 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  supportIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  supportOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  supportOptionDescription: {
    fontSize: 14,
  },
  categoriesContainer: {
    paddingHorizontal: 16,
    flexDirection: 'row',
    marginBottom: 16,
  },
  categoryTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
  },
  activeCategory: {
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    borderColor: '#3B82F6',
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '600',
  },
  faqContainer: {
    gap: 12,
  },
  faqItem: {
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: { elevation: 2 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  faqQuestion: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  faqQuestionText: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 10,
  },
  faqAnswer: {
    padding: 16,
    paddingTop: 12,
    borderTopWidth: 1,
  },
  faqAnswerText: {
    fontSize: 14,
    lineHeight: 20,
  },
  noResultsContainer: {
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  noResultsText: {
    fontSize: 14,
    textAlign: 'center',
  },
  contactForm: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: { elevation: 2 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  contactFormText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  messageContainer: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
  },
  messageInput: {
    fontSize: 16,
    minHeight: 100,
  },
  submitButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  submitButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  submitIcon: {
    marginLeft: 4,
  },
  helpCenterLink: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
    marginBottom: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
  helpCenterText: {
    fontSize: 15,
    fontWeight: '600',
    marginRight: 6,
  },
  privacyNote: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 40,
  },
  privacyIcon: {
    marginRight: 6,
  },
  privacyText: {
    fontSize: 13,
  },
}); 