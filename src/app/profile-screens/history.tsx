import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ScrollView, ActivityIndicator, Dimensions } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , Ionicons , MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getAuth } from 'firebase/auth';
import { getFirestore, collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { LinearGradient } from 'expo-linear-gradient';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface ActivityEntry {
  id: string;
  date: string;
  calories: number;
  water: number;
  meals: number;
  steps: number;
}

export default function HistoryScreen() {
  const { isDark, colors } = useTheme();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const [isLoading, setIsLoading] = useState(true);
  const [activityHistory, setActivityHistory] = useState<ActivityEntry[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState('week');
  
  useEffect(() => {
    fetchActivityHistory();
  }, [selectedPeriod]);
  
  const fetchActivityHistory = async () => {
    setIsLoading(true);
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      
      if (!user) {
        console.log('No user logged in');
        setIsLoading(false);
        return;
      }
      
      const db = getFirestore();
      const userId = user.uid;
      
      // Determine date range based on selected period
      const today = new Date();
      let startDate = new Date();
      
      if (selectedPeriod === 'week') {
        startDate.setDate(today.getDate() - 7);
      } else if (selectedPeriod === 'month') {
        startDate.setMonth(today.getMonth() - 1);
      } else if (selectedPeriod === 'year') {
        startDate.setFullYear(today.getFullYear() - 1);
      } else {
        // Default to 30 days if 'all' is selected
        startDate.setDate(today.getDate() - 30);
      }
      
      // Format dates for Firestore query
      const startDateString = startDate.toISOString().split('T')[0];
      const endDateString = today.toISOString().split('T')[0];
      
      // Query activity logs collection
      const activityRef = collection(db, 'activity_logs');
      const q = query(
        activityRef,
        where('user_id', '==', userId),
        where('date', '>=', startDateString),
        where('date', '<=', endDateString),
        orderBy('date', 'desc')
      );
      
      console.log(`Fetching activity data for user ${userId} from ${startDateString} to ${endDateString}`);
      
      const querySnapshot = await getDocs(q);
      const history: ActivityEntry[] = [];
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        history.push({
          id: doc.id,
          date: data.date,
          calories: data.calories || 0,
          water: data.water || 0,
          meals: data.meals || 0,
          steps: data.steps || 0
        });
      });
      
      console.log(`Found ${history.length} activity logs`);
      setActivityHistory(history);
    } catch (error) {
      console.error('Error fetching activity history:', error);
      setActivityHistory([]);
    } finally {
      setIsLoading(false);
    }
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  // Calculate day difference from today
  const getDayDifference = (dateString: string) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const date = new Date(dateString);
    date.setHours(0, 0, 0, 0);
    
    const diffTime = Math.abs(today.getTime() - date.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    return `${diffDays} days ago`;
  };
  
  // Get percentage of goal
  const getPercentage = (value: number, goalType: string) => {
    let goal = 2000; // Default calorie goal
    
    switch (goalType) {
      case 'calories':
        goal = 2000;
        break;
      case 'water':
        goal = 64;
        break;
      case 'meals':
        goal = 3;
        break;
      case 'steps':
        goal = 10000;
        break;
    }
    
    const percentage = Math.min(Math.round((value / goal) * 100), 100);
    return percentage;
  };
  
  // Render progress bar
  const renderProgressBar = (value: number, goalType: string, color: string) => {
    const percentage = getPercentage(value, goalType);
    
    return (
      <View style={styles.progressBarContainer}>
        <View style={[styles.progressBar, { backgroundColor: `${color}20` }]}>
          <View 
            style={[
              styles.progressFill, 
              { 
                width: `${percentage}%`,
                backgroundColor: color
              }
            ]} 
          />
        </View>
        <Text style={[styles.progressText, { color: colors.textSecondary }]}>
          {percentage}%
        </Text>
      </View>
    );
  };
  
  const renderPeriodSelector = () => {
    const periods = [
      { id: 'week', label: 'Week' },
      { id: 'month', label: 'Month' },
      { id: 'year', label: 'Year' },
      { id: 'all', label: 'All Time' }
    ];
    
    return (
      <View style={[styles.periodSelector, { backgroundColor: isDark ? colors.card : 'white' }]}>
        {periods.map((period) => (
          <TouchableOpacity
            key={period.id}
            style={[
              styles.periodButton,
              selectedPeriod === period.id && [
                styles.selectedPeriod, 
                { 
                  borderColor: colors.primary,
                  backgroundColor: isDark ? `${colors.primary}20` : `${colors.primary}10` 
                }
              ]
            ]}
            onPress={() => setSelectedPeriod(period.id)}
          >
            <Text 
              style={[
                styles.periodButtonText, 
                { color: selectedPeriod === period.id ? colors.primary : colors.textSecondary }
              ]}
            >
              {period.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen options={{ headerShown: false }} />
      
      {/* Gradient Header */}
      <LinearGradient
        colors={isDark ? 
          ['#1E40AF', '#3B82F6'] : 
          ['#3B82F6', '#60A5FA']
        }
        style={[styles.header, { paddingTop: insets.top || 40 }]}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Feather name="chevron-left" size={24}  color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Activity History</Text>
          <TouchableOpacity
            style={styles.filterButton}
          >
            <Feather name="filter" size={20}  color={colors.text} />
          </TouchableOpacity>
        </View>
      </LinearGradient>
      
      {/* Period Selector */}
      {renderPeriodSelector()}
      
      {/* History List */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading history...
          </Text>
        </View>
      ) : (
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {activityHistory.length === 0 ? (
            <View style={styles.emptyStateContainer}>
              <Feather name="calendar" size={60} color={colors.textSecondary} />
              <Text style={[styles.emptyStateText, { color: colors.text }]}>
                No activity data found
              </Text>
              <Text style={[styles.emptyStateSubtext, { color: colors.textSecondary }]}>
                There is no activity data in the selected time period. Start tracking your calories, water, meals, and steps to see your history here.
              </Text>
              <View style={styles.emptyStateButtonsRow}>
                <TouchableOpacity 
                  style={[styles.emptyStateButton, { backgroundColor: '#3B82F6' }]}
                  onPress={() => router.push('/')}
                >
                  <Text style={styles.emptyStateButtonText}>Log Activity</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[styles.emptyStateButton, { backgroundColor: '#10B981', marginLeft: 10 }]}
                  onPress={() => setSelectedPeriod('all')}
                >
                  <Text style={styles.emptyStateButtonText}>View All Time</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            activityHistory.map((entry, index) => (
              <View 
                key={entry.id} 
                style={[
                  styles.historyCard, 
                  { 
                    backgroundColor: isDark ? colors.card : 'white', 
                    borderColor: colors.border,
                    marginTop: index === 0 ? 20 : 0,
                  }
                ]}
              >
                <View style={styles.historyCardHeader}>
                  <View style={styles.dateContainer}>
                    <View style={[styles.dateIconContainer, { backgroundColor: '#3B82F615' }]}>
                      <Feather name="calendar" size={18}  color={colors.text} />
                    </View>
                    <View>
                      <Text style={[styles.dateText, { color: colors.text }]}>
                        {formatDate(entry.date)}
                      </Text>
                      <Text style={[styles.dateSubtext, { color: colors.textSecondary }]}>
                        {getDayDifference(entry.date)}
                      </Text>
                    </View>
                  </View>
                </View>
                
                <View style={styles.metricsContainer}>
                  <View style={styles.metricRow}>
                    <View style={styles.metricItem}>
                      <View style={[styles.metricIcon, { backgroundColor: '#EF444415' }]}>
                        <Feather name="zap" size={18}  color={colors.text} />
                      </View>
                      <View style={styles.metricDetails}>
                        <View style={styles.metricTitleRow}>
                          <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>
                            Calories
                          </Text>
                          <Text style={[styles.metricValue, { color: colors.text }]}>
                            {entry.calories.toLocaleString()}
                          </Text>
                        </View>
                        {renderProgressBar(entry.calories, 'calories', '#EF4444')}
                      </View>
                    </View>
                  </View>
                  
                  <View style={styles.metricRow}>
                    <View style={styles.metricItem}>
                      <View style={[styles.metricIcon, { backgroundColor: '#3B82F615' }]}>
                        <Ionicons name="water" size={18}  color={colors.text} />
                      </View>
                      <View style={styles.metricDetails}>
                        <View style={styles.metricTitleRow}>
                          <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>
                            Water
                          </Text>
                          <Text style={[styles.metricValue, { color: colors.text }]}>
                            {entry.water} oz
                          </Text>
                        </View>
                        {renderProgressBar(entry.water, 'water', '#3B82F6')}
                      </View>
                    </View>
                  </View>
                  
                  <View style={styles.metricRow}>
                    <View style={styles.metricItem}>
                      <View style={[styles.metricIcon, { backgroundColor: '#F59E0B15' }]}>
                        <MaterialIcons name="restaurant" size={18}  color={colors.text} />
                      </View>
                      <View style={styles.metricDetails}>
                        <View style={styles.metricTitleRow}>
                          <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>
                            Meals
                          </Text>
                          <Text style={[styles.metricValue, { color: colors.text }]}>
                            {entry.meals}
                          </Text>
                        </View>
                        {renderProgressBar(entry.meals, 'meals', '#F59E0B')}
                      </View>
                    </View>
                  </View>
                  
                  <View style={styles.metricRow}>
                    <View style={styles.metricItem}>
                      <View style={[styles.metricIcon, { backgroundColor: '#10B98115' }]}>
                        <Feather name="activity" size={18}  color={colors.text} />
                      </View>
                      <View style={styles.metricDetails}>
                        <View style={styles.metricTitleRow}>
                          <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>
                            Steps
                          </Text>
                          <Text style={[styles.metricValue, { color: colors.text }]}>
                            {entry.steps.toLocaleString()}
                          </Text>
                        </View>
                        {renderProgressBar(entry.steps, 'steps', '#10B981')}
                      </View>
                    </View>
                  </View>
                </View>
                
                <TouchableOpacity style={[styles.detailsButton, { borderColor: colors.border }]}>
                  <Text style={[styles.detailsButtonText, { color: colors.primary }]}>
                    View Details
                  </Text>
                </TouchableOpacity>
              </View>
            ))
          )}
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: 'white',
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  periodSelector: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  periodButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'transparent',
    marginRight: 10,
  },
  selectedPeriod: {
    borderWidth: 1,
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  emptyStateContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 80,
  },
  emptyStateText: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 20,
  },
  emptyStateSubtext: {
    fontSize: 16,
    marginTop: 8,
    textAlign: 'center',
    paddingHorizontal: 40,
    lineHeight: 22,
  },
  emptyStateButtonsRow: {
    flexDirection: 'row',
    marginTop: 24,
  },
  emptyStateButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  historyCard: {
    borderRadius: 16,
    borderWidth: 1,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  historyCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  dateText: {
    fontSize: 16,
    fontWeight: '700',
  },
  dateSubtext: {
    fontSize: 13,
    marginTop: 2,
  },
  metricsContainer: {
    marginBottom: 16,
  },
  metricRow: {
    marginBottom: 16,
  },
  metricItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  metricIcon: {
    width: 36,
    height: 36,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  metricDetails: {
    flex: 1,
  },
  metricTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  metricValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  metricLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    flex: 1,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    marginLeft: 8,
    width: 32,
    textAlign: 'right',
  },
  detailsButton: {
    paddingVertical: 12,
    marginTop: 4,
    borderTopWidth: 1,
    alignItems: 'center',
  },
  detailsButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
}); 