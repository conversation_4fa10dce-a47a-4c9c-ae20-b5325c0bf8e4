import React, { useState } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, TextInput, Platform, Share, Alert, Image } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import * as Clipboard from 'expo-clipboard';

interface Referral {
  id: string;
  name: string;
  email: string;
  status: 'pending' | 'registered' | 'subscribed';
  date: string;
}

export default function InviteFriendsScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [copied, setCopied] = useState(false);
  
  // Sample referral code
  const referralCode = 'HEALTH2023';
  const referralLink = 'https://calorielens.app/join?ref=' + referralCode;
  
  // Sample referrals data
  const [referrals, setReferrals] = useState<Referral[]>([
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      status: 'subscribed',
      date: '2023-05-15',
    },
    {
      id: '2',
      name: '<PERSON> <PERSON>',
      email: '<EMAIL>',
      status: 'registered',
      date: '2023-05-28',
    },
    {
      id: '3',
      name: 'Jessica Smith',
      email: '<EMAIL>',
      status: 'pending',
      date: '2023-06-01',
    },
  ]);
  
  // Handle copy referral link/code
  const handleCopy = async (text: string) => {
    await Clipboard.setStringAsync(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
    Alert.alert('Copied!', 'Referral information copied to clipboard.');
  };
  
  // Handle share
  const handleShare = async () => {
    try {
      await Share.share({
        message: `Join me on CalorieLens, the app that makes tracking your nutrition effortless! Use my referral code ${referralCode} to get started: ${referralLink}`,
        url: referralLink, // Only works on iOS
        title: 'Join me on CalorieLens',
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };
  
  // Handle email invite
  const handleEmailInvite = () => {
    if (!email) {
      Alert.alert('Please enter an email address');
      return;
    }
    
    if (!isValidEmail(email)) {
      Alert.alert('Please enter a valid email address');
      return;
    }
    
    // Add to referrals list
    const newReferral: Referral = {
      id: Date.now().toString(),
      name: email.split('@')[0],
      email,
      status: 'pending',
      date: new Date().toISOString().split('T')[0],
    };
    
    setReferrals([newReferral, ...referrals]);
    setEmail('');
    
    Alert.alert('Invitation sent!', `We've sent an invitation to ${email}.`);
  };
  
  // Helper to validate email
  const isValidEmail = (email: string) => {
    return /\S+@\S+\.\S+/.test(email);
  };
  
  // Get status information
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          icon: Clock,
          color: '#6B7280',
          text: 'Pending',
          background: 'rgba(107, 114, 128, 0.1)',
        };
      case 'registered':
        return {
          icon: CheckCircle,
          color: '#3B82F6',
          text: 'Registered',
          background: 'rgba(59, 130, 246, 0.1)',
        };
      case 'subscribed':
        return {
          icon: Award,
          color: '#10B981',
          text: 'Subscribed',
          background: 'rgba(16, 185, 129, 0.1)',
        };
      default:
        return {
          icon: Clock,
          color: '#6B7280',
          text: 'Unknown',
          background: 'rgba(107, 114, 128, 0.1)',
        };
    }
  };

  return (
    <>
      <Stack.Screen 
        options={{ 
          headerShown: false,
        }} 
      />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.card }]}>
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={() => router.back()}
            accessibilityLabel="Go back"
            accessibilityHint="Returns to the previous screen"
          >
            <Feather name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Invite Friends</Text>
          <View style={styles.placeholderRight} />
        </View>
        
        <ScrollView 
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Hero Section */}
          <View style={[styles.heroCard, { 
            backgroundColor: colors.card,
            borderColor: colors.border,
            shadowColor: colors.shadow
          }]}>
            <View style={styles.heroContent}>
              <View style={styles.heroImageContainer}>
                <LinearGradient
                  colors={['#3B82F6', '#2563EB']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.heroImageBackground}
                >
                  <View style={styles.heroImageCircle}>
                    <Feather name="users" size={32}  color={colors.text} />
                  </View>
                  <Text style={styles.heroImageText}>Refer Friends</Text>
                </LinearGradient>
              </View>
              <Text style={[styles.heroTitle, { color: colors.text }]}>
                Invite Friends & Earn Rewards
              </Text>
              <Text style={[styles.heroDescription, { color: colors.textSecondary }]}>
                For every friend that joins and subscribes, you'll both get one month free premium access!
              </Text>
            </View>
            
            <View style={styles.rewardCard}>
              <LinearGradient
                colors={['#6366F1', '#4F46E5'] as const}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.rewardGradient}
              >
                <View style={styles.rewardIconContainer}>
                  <Gift size={28} color="white" />
                </View>
                <Text style={styles.rewardTitle}>Get 1 Month Free</Text>
                <Text style={styles.rewardText}>
                  For each friend who subscribes using your referral code
                </Text>
              </LinearGradient>
            </View>
          </View>
          
          {/* Referral Code Section */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Your Referral Code</Text>
            <View style={[styles.codeCard, { 
              backgroundColor: colors.card,
              borderColor: colors.border,
              shadowColor: colors.shadow
            }]}>
              <Text style={[styles.codeText, { color: colors.text }]}>{referralCode}</Text>
              <TouchableOpacity 
                style={[styles.copyButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
                onPress={() => handleCopy(referralCode)}
              >
                {copied ? (
                  <MaterialIcons name="check-circle" size={20} color={colors.primary} />
                ) : (
                  <Feather name="copy" size={20} color={colors.primary} />
                )}
              </TouchableOpacity>
            </View>
          </View>
          
          {/* Referral Link Section */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Share Your Link</Text>
            <View style={[styles.linkCard, { 
              backgroundColor: colors.card,
              borderColor: colors.border,
              shadowColor: colors.shadow
            }]}>
              <Text style={[styles.linkText, { color: colors.textSecondary }]} numberOfLines={1}>
                {referralLink}
              </Text>
              <TouchableOpacity 
                style={[styles.copyButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
                onPress={() => handleCopy(referralLink)}
              >
                {copied ? (
                  <MaterialIcons name="check-circle" size={20} color={colors.primary} />
                ) : (
                  <Feather name="copy" size={20} color={colors.primary} />
                )}
              </TouchableOpacity>
            </View>
          </View>
          
          {/* Share Options */}
          <View style={styles.shareOptionsContainer}>
            <TouchableOpacity 
              style={styles.shareOption}
              onPress={handleShare}
            >
              <LinearGradient
                colors={['#3B82F6', '#2563EB'] as const}
                style={styles.shareIconContainer}
              >
                <Feather name="share-2" size={20}  color={colors.text} />
              </LinearGradient>
              <Text style={[styles.shareOptionText, { color: colors.text }]}>Share</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.shareOption}>
              <LinearGradient
                colors={['#F59E0B', '#D97706'] as const}
                style={styles.shareIconContainer}
              >
                <Feather name="mail" size={20}  color={colors.text} />
              </LinearGradient>
              <Text style={[styles.shareOptionText, { color: colors.text }]}>Email</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.shareOption}>
              <LinearGradient
                colors={['#10B981', '#059669'] as const}
                style={styles.shareIconContainer}
              >
                <Feather name="message-square" size={20}  color={colors.text} />
              </LinearGradient>
              <Text style={[styles.shareOptionText, { color: colors.text }]}>Message</Text>
            </TouchableOpacity>
          </View>
          
          {/* Email Invite */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Invite by Email</Text>
            <View style={[styles.inviteCard, { 
              backgroundColor: colors.card,
              borderColor: colors.border,
              shadowColor: colors.shadow
            }]}>
              <View style={[styles.emailInputContainer, { borderColor: colors.border }]}>
                <TextInput
                  style={[styles.emailInput, { color: colors.text }]}
                  placeholder="Enter email address"
                  placeholderTextColor={colors.textSecondary}
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>
              <TouchableOpacity 
                style={styles.inviteButton}
                onPress={handleEmailInvite}
              >
                <LinearGradient
                  colors={['#3B82F6', '#2563EB'] as const}
                  style={styles.inviteButtonGradient}
                >
                  <Text style={styles.inviteButtonText}>Send Invite</Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
          
          {/* Referral Tracking */}
          {referrals.length > 0 && (
            <View style={styles.section}>
              <View style={styles.referralHeaderRow}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>Your Referrals</Text>
                <TouchableOpacity>
                  <Text style={[styles.viewAllText, { color: colors.primary }]}>
                    {referrals.length} {referrals.length === 1 ? 'Person' : 'People'}
                  </Text>
                </TouchableOpacity>
              </View>
              
              <View style={[styles.referralsCard, { 
                backgroundColor: colors.card,
                borderColor: colors.border,
                shadowColor: colors.shadow
              }]}>
                {referrals.map((referral, index) => {
                  const statusInfo = getStatusInfo(referral.status);
                  const StatusIcon = statusInfo.icon;
                  
                  return (
                    <View 
                      key={referral.id}
                      style={[
                        styles.referralItem,
                        index < referrals.length - 1 && { 
                          borderBottomWidth: 1,
                          borderBottomColor: colors.border,
                        }
                      ]}
                    >
                      <View style={styles.referralInfo}>
                        <View style={[styles.avatarCircle, { backgroundColor: colors.primary + '20' }]}>
                          <Text style={[styles.avatarText, { color: colors.primary }]}>
                            {referral.name.charAt(0).toUpperCase()}
                          </Text>
                        </View>
                        <View style={styles.referralNameEmail}>
                          <Text style={[styles.referralName, { color: colors.text }]} numberOfLines={1}>
                            {referral.name}
                          </Text>
                          <Text style={[styles.referralEmail, { color: colors.textSecondary }]} numberOfLines={1}>
                            {referral.email}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.statusBadge, { backgroundColor: statusInfo.background }]}>
                        <StatusIcon size={14} color={statusInfo.color} style={styles.statusIcon} />
                        <Text style={[styles.statusText, { color: statusInfo.color }]}>
                          {statusInfo.text}
                        </Text>
                      </View>
                    </View>
                  );
                })}
              </View>
            </View>
          )}
          
          {/* Reference Program FAQ */}
          <View style={[styles.faqCard, { 
            backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)',
            borderColor: '#3B82F6' + '20'
          }]}>
            <View style={styles.faqHeader}>
              <Feather name="users" size={18} style={styles.faqIcon} />
              <Text style={[styles.faqTitle, { color: colors.text }]}>
                How the Referral Program Works
              </Text>
            </View>
            <Text style={[styles.faqText, { color: colors.textSecondary }]}>
              1. Invite friends using your unique referral code
            </Text>
            <Text style={[styles.faqText, { color: colors.textSecondary }]}>
              2. When they sign up and subscribe to premium, you both get 1 month free
            </Text>
            <Text style={[styles.faqText, { color: colors.textSecondary }]}>
              3. There's no limit to how many friends you can refer!
            </Text>
            <TouchableOpacity style={[styles.faqButton, { borderColor: '#3B82F6' }]}>
              <Text style={[styles.faqButtonText, { color: '#3B82F6' }]}>
                Program Terms
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  placeholderRight: {
    width: 40,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  heroCard: {
    margin: 20,
    marginTop: 10,
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 4 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      }
    }),
  },
  heroContent: {
    alignItems: 'center',
  },
  heroImageContainer: {
    width: 160, 
    height: 120,
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  heroImageBackground: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  heroImageCircle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  heroImageText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '700',
  },
  heroTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
  },
  heroDescription: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
    marginBottom: 24,
  },
  rewardCard: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  rewardGradient: {
    padding: 20,
    alignItems: 'center',
  },
  rewardIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  rewardTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
    marginBottom: 6,
  },
  rewardText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  section: {
    marginHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 12,
  },
  codeCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: { elevation: 2 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  codeText: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: 1,
  },
  copyButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  linkCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: { elevation: 2 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  linkText: {
    fontSize: 14,
    flex: 1,
    marginRight: 10,
  },
  shareOptionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginHorizontal: 20,
    marginBottom: 24,
  },
  shareOption: {
    alignItems: 'center',
  },
  shareIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  shareOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  inviteCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: { elevation: 2 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  emailInputContainer: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginBottom: 12,
  },
  emailInput: {
    fontSize: 16,
  },
  inviteButton: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  inviteButtonGradient: {
    paddingVertical: 12,
    alignItems: 'center',
  },
  inviteButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  referralHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
  },
  referralsCard: {
    borderRadius: 12,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: { elevation: 2 },
      web: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  referralItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  referralInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  avatarText: {
    fontSize: 16,
    fontWeight: '700',
  },
  referralNameEmail: {
    flex: 1,
  },
  referralName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  referralEmail: {
    fontSize: 14,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 10,
  },
  statusIcon: {
    marginRight: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  faqCard: {
    margin: 20,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
  },
  faqHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  faqIcon: {
    marginRight: 10,
  },
  faqTitle: {
    fontSize: 16,
    fontWeight: '700',
  },
  faqText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  faqButton: {
    alignSelf: 'flex-start',
    marginTop: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
  },
  faqButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  usersIconContainer: {
    position: 'absolute',
    top: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
}); 