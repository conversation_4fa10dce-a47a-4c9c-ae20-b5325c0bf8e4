import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ScrollView, SafeAreaView, Platform } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , FontAwesome , MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useAuth } from '@/contexts/AuthContext';

// Define gradient type to fix the TypeScript error
type GradientColors = readonly [string, string];

export default function MoreScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { signOut } = useAuth();

  // Menu items grid - 4 columns per row
  const menuItems = [
    // Row 1
    { 
      icon: Zap, 
      label: 'Activity', 
      onPress: () => router.push('/profile-screens/activity'),
      gradient: ['#3B82F6', '#2563EB'] as GradientColors,
    },
    { 
      icon: Heart, 
      label: 'Health', 
      onPress: () => router.push('/profile-screens/health-data'),
      gradient: ['#F97316', '#EA580C'] as GradientColors,
    },
    { 
      icon: Trophy, 
      label: 'Challenges', 
      onPress: () => router.navigate("/(tabs)/challenges" as any),
      gradient: ['#8B5CF6', '#7C3AED'] as GradientColors,
    },
    { 
      icon: Bell, 
      label: 'Alerts', 
      onPress: () => router.push('/profile-screens/notifications'),
      gradient: ['#EC4899', '#DB2777'] as GradientColors,
    },
    
    // Row 2
    { 
      icon: Watch, 
      label: 'Wearables', 
      onPress: () => router.navigate("/wearable" as any),
      gradient: ['#06B6D4', '#0891B2'] as GradientColors,
    },
    { 
      icon: Utensils, 
      label: 'Dietitian', 
      onPress: () => router.push('/dietitian'),
      gradient: ['#F43F5E', '#D1446D'] as GradientColors,
    },
    { 
      icon: Award, 
      label: 'Achievements', 
      onPress: () => router.push('/achievements'),
      gradient: ['#9333EA', '#7E22CE'] as GradientColors,
    },
    { 
      icon: FileBarChart, 
      label: 'Timeline', 
      onPress: () => router.push('/nutrition-timeline'),
      gradient: ['#6366F1', '#4F46E5'] as GradientColors,
    },
    // Sleep Tracker item
    { 
      icon: Moon, 
      label: 'Sleep', 
      onPress: () => router.push('/sleep-tracker'),
      gradient: ['#4F46E5', '#3730A3'] as GradientColors,
    },
    { 
      icon: Settings, 
      label: 'Settings', 
      onPress: () => router.push('/profile-screens/settings'),
      gradient: ['#64748B', '#475569'] as GradientColors,
    },
    { 
      icon: CreditCard, 
      label: 'Billing', 
      onPress: () => router.push('/profile-screens/billing'),
      gradient: ['#10B981', '#059669'] as GradientColors,
    },
    
    // Row 3
    { 
      icon: Shield, 
      label: 'Privacy', 
      onPress: () => router.push('/profile-screens/privacy-settings'),
      gradient: ['#0EA5E9', '#0284C7'] as GradientColors,
    },
    { 
      icon: Share, 
      label: 'Share', 
      onPress: () => router.push('/profile-screens/invite-friends'),
      gradient: ['#F59E0B', '#D97706'] as GradientColors,
    },
    { 
      icon: LifeBuoy, 
      label: 'Help', 
      onPress: () => router.push('/profile-screens/help-support'),
      gradient: ['#F43F5E', '#BE185D'] as GradientColors,
    },
  ];

  const handleSignOut = async () => {
    try {
      const result = await signOut();
      if (result.success) {
        // For web, force a full reload to clear any lingering state
        if (Platform.OS === 'web' && typeof window !== 'undefined') {
          window.location.href = '/(auth)/login';
        } else {
          router.replace('/(auth)/login');
        }
      } else {
        console.error('Sign out failed:', result.error);
      }
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <TouchableOpacity 
          onPress={() => router.back()}
          style={[styles.backButton, { backgroundColor: colors.subtle }]}
        >
          <Feather name="arrow-left" size={20} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>More</Text>
        <View style={styles.placeholder} />
      </View>
      
      {/* Content */}
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={[
          styles.contentContainer, 
          { paddingBottom: insets.bottom + 20 }
        ]}
      >
        {/* Menu Grid */}
        <View style={styles.menuGrid}>
          {menuItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.menuItem}
              onPress={item.onPress}
            >
              <LinearGradient
                colors={item.gradient}
                style={styles.menuIconContainer}
              >
                <item.icon size={24} color="white" />
              </LinearGradient>
              <Text style={[styles.menuLabel, { color: colors.text }]}>{item.label}</Text>
            </TouchableOpacity>
          ))}
        </View>
        
        {/* Sign Out Button */}
        <TouchableOpacity
          style={[
            styles.signOutButton,
            {
              backgroundColor: isDark ? 'rgba(239, 68, 68, 0.1)' : 'rgba(239, 68, 68, 0.05)',
              borderColor: colors.border,
            }
          ]}
          onPress={handleSignOut}
        >
          <Feather name="log-out" size={18} style={styles.signOutIcon} />
          <Text style={styles.signOutText}>Sign Out</Text>
        </TouchableOpacity>
        
        {/* Version Info */}
        <Text style={[styles.versionText, { color: colors.textSecondary }]}>
          CalorieLens v1.0.0
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  menuGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  menuItem: {
    width: '22%',
    alignItems: 'center',
    marginBottom: 24,
  },
  menuIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 3,
      },
      android: {
        elevation: 4,
      },
      web: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 3,
      },
    }),
  },
  menuLabel: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderWidth: 1,
  },
  signOutIcon: {
    marginRight: 8,
  },
  signOutText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#EF4444',
  },
  versionText: {
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '500',
  },
}); 