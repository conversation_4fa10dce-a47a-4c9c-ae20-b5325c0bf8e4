import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  TextInput,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { 
  getUserProfile, 
  updateUserProfile, 
  calculateNutritionGoals,
  NutritionGoalType,
  ActivityLevel,
  DietaryPreference,
  UserProfile
} from '@/services/nutritionGoalService';
import { useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function NutritionGoalsScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [editedGoals, setEditedGoals] = useState({
    calorieGoal: '',
    proteinGoal: '',
    carbsGoal: '',
    fatGoal: '',
    waterGoal: '',
  });
  const [editedProfile, setEditedProfile] = useState({
    age: '',
    gender: '',
    height: '',
    weight: '',
    goalType: NutritionGoalType.MAINTENANCE,
    activityLevel: ActivityLevel.MODERATELY_ACTIVE,
    dietaryPreference: DietaryPreference.STANDARD,
  });
  const [showCalculator, setShowCalculator] = useState(false);
  
  // Load user profile on mount
  useEffect(() => {
    loadUserProfile();
  }, []);
  
  const loadUserProfile = async () => {
    try {
      setLoading(true);
      const userProfile = await getUserProfile();
      
      if (userProfile) {
        setProfile(userProfile);
        
        // Set initial form values
        setEditedGoals({
          calorieGoal: userProfile.calorieGoal.toString(),
          proteinGoal: userProfile.proteinGoal.toString(),
          carbsGoal: userProfile.carbsGoal.toString(),
          fatGoal: userProfile.fatGoal.toString(),
          waterGoal: userProfile.waterGoal.toString(),
        });
        
        setEditedProfile({
          age: userProfile.age ? userProfile.age.toString() : '',
          gender: userProfile.gender || '',
          height: userProfile.height ? userProfile.height.toString() : '',
          weight: userProfile.weight ? userProfile.weight.toString() : '',
          goalType: userProfile.goalType,
          activityLevel: userProfile.activityLevel,
          dietaryPreference: userProfile.dietaryPreference,
        });
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
      Alert.alert('Error', 'Failed to load your nutrition profile');
    } finally {
      setLoading(false);
    }
  };
  
  const handleGoBack = () => {
    router.back();
  };
  
  const handleSave = async () => {
    try {
      if (!profile) return;
      
      setSaving(true);
      
      // Parse and validate numeric values
      const calorieGoal = parseInt(editedGoals.calorieGoal);
      const proteinGoal = parseInt(editedGoals.proteinGoal);
      const carbsGoal = parseInt(editedGoals.carbsGoal);
      const fatGoal = parseInt(editedGoals.fatGoal);
      const waterGoal = parseInt(editedGoals.waterGoal);
      
      if (isNaN(calorieGoal) || isNaN(proteinGoal) || isNaN(carbsGoal) || isNaN(fatGoal) || isNaN(waterGoal)) {
        Alert.alert('Invalid Input', 'Please enter valid numbers for all goals');
        return;
      }
      
      // Create updated profile object
      const updatedProfile: Partial<UserProfile> = {
        calorieGoal,
        proteinGoal,
        carbsGoal,
        fatGoal,
        waterGoal,
      };
      
      // Update user profile
      const result = await updateUserProfile(updatedProfile);
      
      if (result) {
        Alert.alert('Success', 'Your nutrition goals have been updated');
        router.back();
      } else {
        Alert.alert('Error', 'Failed to update your nutrition goals');
      }
    } catch (error) {
      console.error('Error saving nutrition goals:', error);
      Alert.alert('Error', 'Failed to save your nutrition goals');
    } finally {
      setSaving(false);
    }
  };
  
  const handleCalculateGoals = async () => {
    try {
      // Parse and validate profile inputs
      const age = parseInt(editedProfile.age);
      const height = parseInt(editedProfile.height);
      const weight = parseInt(editedProfile.weight);
      
      if (isNaN(age) || isNaN(height) || isNaN(weight) || !editedProfile.gender) {
        Alert.alert('Missing Information', 'Please fill in all fields to calculate your nutrition goals');
        return;
      }
      
      if (editedProfile.gender !== 'male' && editedProfile.gender !== 'female' && editedProfile.gender !== 'other') {
        Alert.alert('Invalid Input', 'Gender must be male, female, or other');
        return;
      }
      
      // Calculate nutrition goals
      const calculatedGoals = calculateNutritionGoals(
        age,
        editedProfile.gender as 'male' | 'female' | 'other',
        height,
        weight,
        editedProfile.activityLevel,
        editedProfile.goalType,
        editedProfile.dietaryPreference
      );
      
      // Update form values with calculated goals
      setEditedGoals({
        calorieGoal: calculatedGoals.calorieGoal.toString(),
        proteinGoal: calculatedGoals.proteinGoal.toString(),
        carbsGoal: calculatedGoals.carbsGoal.toString(),
        fatGoal: calculatedGoals.fatGoal.toString(),
        waterGoal: calculatedGoals.waterGoal.toString(),
      });
      
      // Hide calculator section
      setShowCalculator(false);
      
      // Show success message
      Alert.alert('Goals Calculated', 'Your nutrition goals have been calculated based on your profile information. You can adjust them if needed before saving.');
    } catch (error) {
      console.error('Error calculating nutrition goals:', error);
      Alert.alert('Error', 'Failed to calculate nutrition goals');
    }
  };
  
  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: isDark ? colors.background : '#f9f9f9' }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading your nutrition profile...</Text>
        </View>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDark ? colors.background : '#f9f9f9' }]}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
          <Feather name="arrow-left" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Nutrition Goals</Text>
        <TouchableOpacity 
          style={[styles.saveButton, { backgroundColor: colors.primary }]} 
          onPress={handleSave}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#ffffff" />
          ) : (
            <>
              <Feather name="save" size={18} style={styles.saveIcon} />
              <Text style={styles.saveButtonText}>Save</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={[styles.card, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
          <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, { color: colors.text }]}>Manual Goal Setting</Text>
            <TouchableOpacity 
              style={[styles.calculatorButton, { borderColor: colors.primary }]}
              onPress={() => setShowCalculator(!showCalculator)}
            >
              <Feather name="calculator" size={16} color={colors.primary} style={styles.calculatorIcon} />
              <Text style={[styles.calculatorButtonText, { color: colors.primary }]}>
                {showCalculator ? 'Hide Calculator' : 'Use Calculator'}
              </Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>Daily Calorie Goal</Text>
            <TextInput
              style={[styles.input, { 
                backgroundColor: isDark ? colors.subtle : '#f5f5f5', 
                color: colors.text,
                borderColor: colors.border 
              }]}
              value={editedGoals.calorieGoal}
              onChangeText={(text) => setEditedGoals(prev => ({ ...prev, calorieGoal: text }))}
              keyboardType="numeric"
              placeholder="e.g. 2000"
              placeholderTextColor={colors.textSecondary}
            />
          </View>
          
          <View style={styles.macroSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Macronutrients</Text>
            
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>Protein (g)</Text>
              <TextInput
                style={[styles.input, { 
                  backgroundColor: isDark ? colors.subtle : '#f5f5f5', 
                  color: colors.text,
                  borderColor: colors.border 
                }]}
                value={editedGoals.proteinGoal}
                onChangeText={(text) => setEditedGoals(prev => ({ ...prev, proteinGoal: text }))}
                keyboardType="numeric"
                placeholder="e.g. 150"
                placeholderTextColor={colors.textSecondary}
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>Carbohydrates (g)</Text>
              <TextInput
                style={[styles.input, { 
                  backgroundColor: isDark ? colors.subtle : '#f5f5f5', 
                  color: colors.text,
                  borderColor: colors.border 
                }]}
                value={editedGoals.carbsGoal}
                onChangeText={(text) => setEditedGoals(prev => ({ ...prev, carbsGoal: text }))}
                keyboardType="numeric"
                placeholder="e.g. 200"
                placeholderTextColor={colors.textSecondary}
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>Fat (g)</Text>
              <TextInput
                style={[styles.input, { 
                  backgroundColor: isDark ? colors.subtle : '#f5f5f5', 
                  color: colors.text,
                  borderColor: colors.border 
                }]}
                value={editedGoals.fatGoal}
                onChangeText={(text) => setEditedGoals(prev => ({ ...prev, fatGoal: text }))}
                keyboardType="numeric"
                placeholder="e.g. 65"
                placeholderTextColor={colors.textSecondary}
              />
            </View>
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>Daily Water Goal (ml)</Text>
            <TextInput
              style={[styles.input, { 
                backgroundColor: isDark ? colors.subtle : '#f5f5f5', 
                color: colors.text,
                borderColor: colors.border 
              }]}
              value={editedGoals.waterGoal}
              onChangeText={(text) => setEditedGoals(prev => ({ ...prev, waterGoal: text }))}
              keyboardType="numeric"
              placeholder="e.g. 2500"
              placeholderTextColor={colors.textSecondary}
            />
          </View>
        </View>
        
        {showCalculator && (
          <View style={[styles.card, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
            <Text style={[styles.cardTitle, { color: colors.text }]}>Goal Calculator</Text>
            
            <View style={[styles.calculatorInfo, { backgroundColor: colors.primary + '15' }]}>
              <Feather name="info" size={18} color={colors.primary} style={styles.infoIcon} />
              <Text style={[styles.calculatorInfoText, { color: colors.text }]}>
                Fill in your details below to automatically calculate your recommended nutrition goals based on your profile.
              </Text>
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>Age</Text>
              <TextInput
                style={[styles.input, { 
                  backgroundColor: isDark ? colors.subtle : '#f5f5f5', 
                  color: colors.text,
                  borderColor: colors.border 
                }]}
                value={editedProfile.age}
                onChangeText={(text) => setEditedProfile(prev => ({ ...prev, age: text }))}
                keyboardType="numeric"
                placeholder="e.g. 30"
                placeholderTextColor={colors.textSecondary}
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>Gender</Text>
              <View style={styles.segmentedControl}>
                <TouchableOpacity 
                  style={[
                    styles.segmentButton, 
                    editedProfile.gender === 'male' && { 
                      backgroundColor: colors.primary,
                    }
                  ]} 
                  onPress={() => setEditedProfile(prev => ({ ...prev, gender: 'male' }))}
                >
                  <Text 
                    style={[
                      styles.segmentButtonText, 
                      { color: editedProfile.gender === 'male' ? '#ffffff' : colors.text }
                    ]}
                  >
                    Male
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[
                    styles.segmentButton, 
                    editedProfile.gender === 'female' && { 
                      backgroundColor: colors.primary,
                    }
                  ]} 
                  onPress={() => setEditedProfile(prev => ({ ...prev, gender: 'female' }))}
                >
                  <Text 
                    style={[
                      styles.segmentButtonText, 
                      { color: editedProfile.gender === 'female' ? '#ffffff' : colors.text }
                    ]}
                  >
                    Female
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[
                    styles.segmentButton, 
                    editedProfile.gender === 'other' && { 
                      backgroundColor: colors.primary,
                    }
                  ]} 
                  onPress={() => setEditedProfile(prev => ({ ...prev, gender: 'other' }))}
                >
                  <Text 
                    style={[
                      styles.segmentButtonText, 
                      { color: editedProfile.gender === 'other' ? '#ffffff' : colors.text }
                    ]}
                  >
                    Other
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>Height (cm)</Text>
              <TextInput
                style={[styles.input, { 
                  backgroundColor: isDark ? colors.subtle : '#f5f5f5', 
                  color: colors.text,
                  borderColor: colors.border 
                }]}
                value={editedProfile.height}
                onChangeText={(text) => setEditedProfile(prev => ({ ...prev, height: text }))}
                keyboardType="numeric"
                placeholder="e.g. 175"
                placeholderTextColor={colors.textSecondary}
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>Weight (kg)</Text>
              <TextInput
                style={[styles.input, { 
                  backgroundColor: isDark ? colors.subtle : '#f5f5f5', 
                  color: colors.text,
                  borderColor: colors.border 
                }]}
                value={editedProfile.weight}
                onChangeText={(text) => setEditedProfile(prev => ({ ...prev, weight: text }))}
                keyboardType="numeric"
                placeholder="e.g. 70"
                placeholderTextColor={colors.textSecondary}
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>Goal Type</Text>
              <View style={[styles.picker, { 
                backgroundColor: isDark ? colors.subtle : '#f5f5f5',
                borderColor: colors.border 
              }]}>
                {Object.values(NutritionGoalType).map((goalType) => (
                  <TouchableOpacity
                    key={goalType}
                    style={[
                      styles.pickerItem,
                      editedProfile.goalType === goalType && { 
                        backgroundColor: colors.primary + '30',
                      }
                    ]}
                    onPress={() => setEditedProfile(prev => ({ ...prev, goalType: goalType }))}
                  >
                    <Text style={[
                      styles.pickerItemText, 
                      { color: colors.text },
                      editedProfile.goalType === goalType && { 
                        fontWeight: '600',
                        color: colors.primary, 
                      }
                    ]}>
                      {goalType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>Activity Level</Text>
              <View style={[styles.picker, { 
                backgroundColor: isDark ? colors.subtle : '#f5f5f5',
                borderColor: colors.border 
              }]}>
                {Object.values(ActivityLevel).map((activityLevel) => (
                  <TouchableOpacity
                    key={activityLevel}
                    style={[
                      styles.pickerItem,
                      editedProfile.activityLevel === activityLevel && { 
                        backgroundColor: colors.primary + '30',
                      }
                    ]}
                    onPress={() => setEditedProfile(prev => ({ ...prev, activityLevel: activityLevel }))}
                  >
                    <Text style={[
                      styles.pickerItemText, 
                      { color: colors.text },
                      editedProfile.activityLevel === activityLevel && { 
                        fontWeight: '600',
                        color: colors.primary, 
                      }
                    ]}>
                      {activityLevel.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { color: colors.textSecondary }]}>Dietary Preference</Text>
              <View style={[styles.picker, { 
                backgroundColor: isDark ? colors.subtle : '#f5f5f5',
                borderColor: colors.border 
              }]}>
                {Object.values(DietaryPreference).map((dietaryPreference) => (
                  <TouchableOpacity
                    key={dietaryPreference}
                    style={[
                      styles.pickerItem,
                      editedProfile.dietaryPreference === dietaryPreference && { 
                        backgroundColor: colors.primary + '30',
                      }
                    ]}
                    onPress={() => setEditedProfile(prev => ({ ...prev, dietaryPreference: dietaryPreference }))}
                  >
                    <Text style={[
                      styles.pickerItemText, 
                      { color: colors.text },
                      editedProfile.dietaryPreference === dietaryPreference && { 
                        fontWeight: '600',
                        color: colors.primary, 
                      }
                    ]}>
                      {dietaryPreference.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            
            <TouchableOpacity 
              style={[styles.calculateButton, { backgroundColor: colors.primary }]}
              onPress={handleCalculateGoals}
            >
              <Feather name="calculator" size={18} style={styles.calculateIcon} />
              <Text style={styles.calculateButtonText}>Calculate My Goals</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    height: 60,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  saveIcon: {
    marginRight: 6,
  },
  saveButtonText: {
    color: '#ffffff',
    fontWeight: '600',
    fontSize: 14,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingVertical: 16,
  },
  card: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  calculatorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
  },
  calculatorIcon: {
    marginRight: 6,
  },
  calculatorButtonText: {
    fontSize: 13,
    fontWeight: '500',
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  input: {
    height: 50,
    borderRadius: 8,
    paddingHorizontal: 16,
    borderWidth: 1,
    fontSize: 16,
  },
  macroSection: {
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  calculatorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  infoIcon: {
    marginRight: 12,
  },
  calculatorInfoText: {
    fontSize: 14,
    flex: 1,
  },
  segmentedControl: {
    flexDirection: 'row',
    height: 50,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#f5f5f5',
  },
  segmentButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  segmentButtonText: {
    fontWeight: '500',
  },
  picker: {
    borderRadius: 8,
    borderWidth: 1,
  },
  pickerItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e5e5',
  },
  pickerItemText: {
    fontSize: 15,
  },
  calculateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
    borderRadius: 8,
    marginTop: 8,
  },
  calculateIcon: {
    marginRight: 8,
  },
  calculateButtonText: {
    color: '#ffffff',
    fontWeight: '600',
    fontSize: 16,
  },
}); 