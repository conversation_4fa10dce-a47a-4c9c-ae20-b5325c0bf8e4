import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Image, 
  ScrollView, 
  TouchableOpacity, 
  ActivityIndicator,
  Share,
  Alert
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { getRecipeById, getSimilarRecipes, Recipe } from '@/services/recipeRecommendationService';

export default function RecipeDetailScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  
  const [loading, setLoading] = useState(true);
  const [recipe, setRecipe] = useState<Recipe | null>(null);
  const [similarRecipes, setSimilarRecipes] = useState<Recipe[]>([]);
  const [isFavorited, setIsFavorited] = useState(false);
  
  useEffect(() => {
    if (id) {
      loadRecipe(id);
    }
  }, [id]);
  
  const loadRecipe = async (recipeId: string) => {
    try {
      setLoading(true);
      
      // Get recipe details
      const recipeDetails = await getRecipeById(recipeId);
      if (recipeDetails) {
        setRecipe(recipeDetails);
        setIsFavorited(recipeDetails.isFavorite || false);
        
        // Get similar recipes
        const similar = await getSimilarRecipes(recipeId);
        setSimilarRecipes(similar);
      }
    } catch (error) {
      console.error('Error loading recipe:', error);
      Alert.alert('Error', 'Failed to load recipe details');
    } finally {
      setLoading(false);
    }
  };
  
  const handleGoBack = () => {
    router.back();
  };
  
  const handleToggleFavorite = () => {
    setIsFavorited(!isFavorited);
    // In a real app, we would save this to the user's favorites
    // updateRecipeFavoriteStatus(recipe.id, !isFavorited);
  };
  
  const handleShareRecipe = async () => {
    if (!recipe) return;
    
    try {
      await Share.share({
        message: `Check out this recipe: ${recipe.name}\n\n${recipe.description}\n\nIngredients:\n${recipe.ingredients.map(i => `- ${i.amount} ${i.unit} ${i.name}`).join('\n')}`,
        title: recipe.name,
      });
    } catch (error) {
      console.error('Error sharing recipe:', error);
    }
  };
  
  const handleSimilarRecipePress = (similarRecipe: Recipe) => {
    // For now, just log the action until we resolve the routing issue
    console.log('Navigate to similar recipe:', similarRecipe.id);
    
    // This approach should work with proper path segments
    try {
      router.push(`/recipe/${similarRecipe.id}` as any);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };
  
  if (loading || !recipe) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: isDark ? colors.background : '#f9f9f9' }]}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
            <Feather name="arrow-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Recipe Details</Text>
          <View style={styles.placeholderButton} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading recipe details...</Text>
        </View>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: isDark ? colors.background : '#f9f9f9' }]}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
          <Feather name="arrow-left" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Recipe Details</Text>
        <TouchableOpacity style={styles.favoriteButton} onPress={handleToggleFavorite}>
          {isFavorited ? (
            <BookmarkMinus size={24} color={colors.primary} />
          ) : (
            <BookmarkPlus size={24} color={colors.text} />
          )}
        </TouchableOpacity>
      </View>
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
      >
        <Image 
          source={{ uri: recipe.imageUrl }} 
          style={styles.recipeImage} 
          resizeMode="cover" 
        />
        
        <View style={styles.recipeContentContainer}>
          <Text style={[styles.recipeName, { color: colors.text }]}>{recipe.name}</Text>
          <Text style={[styles.recipeDescription, { color: colors.textSecondary }]}>{recipe.description}</Text>
          
          <View style={styles.metadataContainer}>
            <View style={styles.metadataItem}>
              <Feather name="clock" size={18} color={colors.primary} style={styles.metadataIcon} />
              <View>
                <Text style={[styles.metadataLabel, { color: colors.textSecondary }]}>Total Time</Text>
                <Text style={[styles.metadataValue, { color: colors.text }]}>{recipe.prepTime + recipe.cookTime} min</Text>
              </View>
            </View>
            
            <View style={styles.metadataItem}>
              <Feather name="users" size={18} color={colors.primary} style={styles.metadataIcon} />
              <View>
                <Text style={[styles.metadataLabel, { color: colors.textSecondary }]}>Servings</Text>
                <Text style={[styles.metadataValue, { color: colors.text }]}>{recipe.servings}</Text>
              </View>
            </View>
            
            <View style={styles.metadataItem}>
              <ChefHat size={18} color={colors.primary} style={styles.metadataIcon} />
              <View>
                <Text style={[styles.metadataLabel, { color: colors.textSecondary }]}>Difficulty</Text>
                <Text style={[styles.metadataValue, { color: colors.text }]}>{recipe.difficulty}</Text>
              </View>
            </View>
          </View>
          
          <View style={styles.actionButtons}>
            <TouchableOpacity 
              style={[styles.actionButton, { backgroundColor: colors.primary }]}
              onPress={handleShareRecipe}
            >
              <Feather name="share-2" size={18} style={styles.actionIcon} />
              <Text style={styles.actionText}>Share</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.actionButton, { backgroundColor: isFavorited ? '#F87171' : colors.primary }]}
              onPress={handleToggleFavorite}
            >
              <Feather name="heart" size={18} style={styles.actionIcon} />
              <Text style={styles.actionText}>{isFavorited ? 'Saved' : 'Save'}</Text>
            </TouchableOpacity>
          </View>
          
          <View style={[styles.nutritionSection, { backgroundColor: isDark ? colors.subtle : '#f5f5f5', borderColor: colors.border }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Nutrition Facts</Text>
            <View style={styles.nutritionGrid}>
              <View style={styles.nutritionItem}>
                <MaterialIcons name="local-fire-department" size={18} color={colors.primary} style={styles.nutritionIcon} />
                <Text style={[styles.nutritionValue, { color: colors.text }]}>{recipe.nutritionFacts.calories}</Text>
                <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Calories</Text>
              </View>
              
              <View style={styles.nutritionItem}>
                <Text style={[styles.nutritionValue, { color: colors.text }]}>{recipe.nutritionFacts.protein}g</Text>
                <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Protein</Text>
              </View>
              
              <View style={styles.nutritionItem}>
                <Text style={[styles.nutritionValue, { color: colors.text }]}>{recipe.nutritionFacts.carbs}g</Text>
                <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Carbs</Text>
              </View>
              
              <View style={styles.nutritionItem}>
                <Text style={[styles.nutritionValue, { color: colors.text }]}>{recipe.nutritionFacts.fat}g</Text>
                <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Fat</Text>
              </View>
            </View>
          </View>
          
          <View style={styles.ingredientsSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Ingredients</Text>
            <Text style={[styles.servingsInfo, { color: colors.textSecondary }]}>for {recipe.servings} serving(s)</Text>
            
            {recipe.ingredients.map((ingredient, index) => (
              <View key={index} style={[styles.ingredientItem, { borderBottomColor: colors.border }]}>
                <Salad size={16} color={colors.primary} style={styles.ingredientIcon} />
                <Text style={[styles.ingredientText, { color: colors.text }]}>
                  {ingredient.amount} {ingredient.unit} {ingredient.name}
                </Text>
              </View>
            ))}
          </View>
          
          <View style={styles.instructionsSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Instructions</Text>
            
            {recipe.instructions.map((instruction, index) => (
              <View key={index} style={styles.instructionItem}>
                <View style={[styles.instructionNumber, { backgroundColor: colors.primary }]}>
                  <Text style={styles.instructionNumberText}>{index + 1}</Text>
                </View>
                <Text style={[styles.instructionText, { color: colors.text }]}>{instruction}</Text>
              </View>
            ))}
          </View>
          
          {similarRecipes.length > 0 && (
            <View style={styles.similarRecipesSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Similar Recipes</Text>
              
              <View style={styles.similarRecipesList}>
                {similarRecipes.map((similarRecipe) => (
                  <TouchableOpacity 
                    key={similarRecipe.id}
                    style={[styles.similarRecipeCard, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}
                    onPress={() => handleSimilarRecipePress(similarRecipe)}
                  >
                    <Image 
                      source={{ uri: similarRecipe.imageUrl }} 
                      style={styles.similarRecipeImage} 
                    />
                    <View style={styles.similarRecipeInfo}>
                      <Text style={[styles.similarRecipeName, { color: colors.text }]} numberOfLines={2}>
                        {similarRecipe.name}
                      </Text>
                      <View style={styles.similarRecipeMetadata}>
                        <Feather name="clock" size={12} color={colors.textSecondary} />
                        <Text style={[styles.similarRecipeMetadataText, { color: colors.textSecondary }]}>
                          {similarRecipe.prepTime + similarRecipe.cookTime} min
                        </Text>
                      </View>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    height: 60,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  favoriteButton: {
    padding: 8,
  },
  placeholderButton: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingBottom: 30,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  recipeImage: {
    width: '100%',
    height: 250,
  },
  recipeContentContainer: {
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  recipeName: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },
  recipeDescription: {
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 20,
  },
  metadataContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  metadataItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metadataIcon: {
    marginRight: 8,
  },
  metadataLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  metadataValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginRight: 12,
  },
  actionIcon: {
    marginRight: 8,
  },
  actionText: {
    color: 'white',
    fontWeight: '600',
  },
  nutritionSection: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  nutritionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    alignItems: 'center',
  },
  nutritionIcon: {
    marginBottom: 8,
  },
  nutritionValue: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  nutritionLabel: {
    fontSize: 12,
  },
  ingredientsSection: {
    marginBottom: 24,
  },
  servingsInfo: {
    fontSize: 14,
    marginTop: -10,
    marginBottom: 16,
  },
  ingredientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  ingredientIcon: {
    marginRight: 12,
  },
  ingredientText: {
    fontSize: 15,
  },
  instructionsSection: {
    marginBottom: 24,
  },
  instructionItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  instructionNumber: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  instructionNumberText: {
    color: 'white',
    fontWeight: '700',
    fontSize: 14,
  },
  instructionText: {
    fontSize: 15,
    lineHeight: 22,
    flex: 1,
  },
  similarRecipesSection: {
    marginBottom: 16,
  },
  similarRecipesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  similarRecipeCard: {
    width: '48%',
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    overflow: 'hidden',
  },
  similarRecipeImage: {
    width: '100%',
    height: 100,
  },
  similarRecipeInfo: {
    padding: 10,
  },
  similarRecipeName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 6,
  },
  similarRecipeMetadata: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  similarRecipeMetadataText: {
    fontSize: 12,
    marginLeft: 4,
  },
}); 