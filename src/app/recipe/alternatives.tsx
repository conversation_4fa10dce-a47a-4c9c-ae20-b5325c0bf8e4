import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert, TouchableOpacity } from 'react-native';
import { useRouter, useLocalSearchParams, Link } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { SafeAreaView } from 'react-native-safe-area-context';
import { AlternativeRecipeView } from '@/components/AlternativeRecipeView';
import { generateAlternativeRecipe } from '@/services/openai/recipeGeneration';
import { useUserPreferences } from '@/contexts/UserPreferencesContext';
import { Feather } from '@expo/vector-icons';
import { ScanLoading } from '@/components/ScanComponents/ScanLoading';

// Define interface for Recipe compatible with AlternativeRecipeView
interface RecipeIngredient {
  name: string;
  amount: string;
  calories: number;
  protein?: number;
  carbs?: number;
  fat?: number;
}

interface Recipe {
  id?: string;
  name: string;
  focus?: string;
  description: string;
  ingredients: RecipeIngredient[];
  instructions: string[];
  nutritionalInfo: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber?: number;
    sugar?: number;
  };
  preparationTime: string;
  cookingTime: string;
  healthBenefits?: string[];
  imagePrompt?: string;
  image_url?: string;
  originalNutrition?: OriginalNutrition;
  originalIngredients?: string[] | string;
}

interface OriginalNutrition {
  calories?: number;
  protein?: number;
  carbs?: number;
  fat?: number;
}

export default function HealthierAlternativesScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const params = useLocalSearchParams<{ 
    originalImageUri: string;
    originalData: string;
  }>();
  
  const [loading, setLoading] = useState(true);
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('Preparing recipe generation...');
  
  // Get user preferences from context
  const { dietaryPreferences, healthGoals } = useUserPreferences();
  
  // Parse the original data from params
  let originalData: {
    name: string;
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
    ingredients?: {
      name: string;
      estimatedAmount?: string;
      calories?: number;
      protein?: number;
      carbs?: number;
      fat?: number;
      ingredients?: string[];
    }[];
  } = { name: 'Unknown Food' };
  
  try {
    if (params.originalData) {
      originalData = JSON.parse(params.originalData);
      console.log('Parsed original data:', originalData);
    }
  } catch (e) {
    console.error('Error parsing original data:', e);
  }
  
  // Create nutrition object for comparison
  const originalNutrition: OriginalNutrition = {
    calories: originalData.calories,
    protein: originalData.protein,
    carbs: originalData.carbs,
    fat: originalData.fat
  };

  // Get the original image URI
  const originalImageUri = params.originalImageUri || '';
  
  useEffect(() => {
    generateAlternatives();
  }, [dietaryPreferences, healthGoals]);
  
  const generateAlternatives = async () => {
    try {
      setLoading(true);
      setProgress(0);
      setCurrentStep('Starting recipe generation...');
      
      // Add delays to show progress
      await new Promise(resolve => setTimeout(resolve, 300));
      setProgress(20);
      setCurrentStep('Analyzing original food...');
      
      await new Promise(resolve => setTimeout(resolve, 300));
      setProgress(40);
      setCurrentStep('Creating healthier alternatives...');
      
      // Generate alternative recipes
      const result = await generateAlternativeRecipe({
        originalFoodName: originalData.name,
        nutritionalInfo: {
          calories: originalData.calories || 0,
          protein: originalData.protein || 0,
          carbs: originalData.carbs || 0,
          fat: originalData.fat || 0
        },
        dietaryPreferences, // Pass user dietary preferences
        allergies: [], // Could be expanded in future
        originalIngredients: originalData.ingredients // Use the ingredients array from parsed data
      });
      
      setProgress(70);
      setCurrentStep('Finalizing recipes...');
      await new Promise(resolve => setTimeout(resolve, 300));
      
      if (result.success && result.recipes && result.recipes.length > 0) {
        // Convert API recipe format to our local Recipe format
        // This ensures compatibility with AlternativeRecipeView
        const compatibleRecipes: Recipe[] = result.recipes.map(apiRecipe => {
          // Ensure ingredients have required calories property
          const ingredients = apiRecipe.ingredients.map(ing => ({
            ...ing,
            calories: ing.calories || 0 // Provide default if missing
          }));
          
          return {
            ...apiRecipe,
            ingredients,
            originalNutrition,
            // Ensure other required properties exist
            preparationTime: apiRecipe.preparationTime || '15 minutes',
            cookingTime: apiRecipe.cookingTime || '30 minutes',
            healthBenefits: apiRecipe.healthBenefits || []
          };
        });
        
        setProgress(90);
        setCurrentStep('Preparing recipe display...');
        await new Promise(resolve => setTimeout(resolve, 300));
        
        setRecipes(compatibleRecipes);
        setProgress(100);
        setCurrentStep('Generation complete!');
      } else {
        throw new Error(result.error || 'No alternative recipes generated');
      }
    } catch (error) {
      console.error('Error generating alternatives:', error);
      Alert.alert(
        'Generation Error',
        'Failed to generate healthier alternatives. Please try again.',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    } finally {
      // Wait a bit before hiding loading screen
      await new Promise(resolve => setTimeout(resolve, 500));
      setLoading(false);
    }
  };
  
  const handleSaveRecipe = async () => {
    try {
      // Placeholder for saving recipe to user's saved recipes
      Alert.alert('Success', 'Recipe saved to your collection!');
      return Promise.resolve();
    } catch (error) {
      console.error('Error saving recipe:', error);
      Alert.alert('Error', 'Failed to save recipe. Please try again.');
      return Promise.reject(error);
    }
  };
  
  const handleChangeIndex = (index: number) => {
    setSelectedIndex(index);
  };
  
  const handleBack = () => {
    router.back();
  };
  
  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <ScanLoading progress={progress} message={currentStep} />
        </View>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {recipes.length > 0 ? (
        <AlternativeRecipeView
          recipes={recipes}
          selectedIndex={selectedIndex}
          onIndexChange={handleChangeIndex}
          originalImageUri={originalImageUri}
          onBack={handleBack}
          onSaveRecipe={handleSaveRecipe}
          originalNutrition={originalNutrition}
          userDietaryPreferences={dietaryPreferences}
          userHealthGoals={healthGoals}
        />
      ) : (
        <View style={styles.loadingContainer}>
          <ScanLoading progress={progress} message={currentStep} />
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 50,
  },
  settingsButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  }
}); 