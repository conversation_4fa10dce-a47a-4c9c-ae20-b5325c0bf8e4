import React, { useState, useMemo, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  Pressable, 
  useColorScheme, 
  Image, 
  FlatList, 
  TouchableOpacity, 
  Modal 
} from 'react-native';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

import ScannerWithLiDAR from '@/components/ScannerWithLiDAR';
import * as MealHistory from '@/utils/mealHistoryManager';

// Add this new component to display past scan history
const MealHistoryItem = ({ item, onPress }: { 
  item: MealHistory.MealScanRecord, 
  onPress: () => void 
}) => {
  const { colors } = useTheme();
  
  // Format date/time
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  // Format meal type with icon
  const getMealTypeIcon = (type?: 'breakfast' | 'lunch' | 'dinner' | 'snack') => {
    switch (type) {
      case 'breakfast':
        return { name: 'sunny-outline', label: 'Breakfast' };
      case 'lunch':
        return { name: 'restaurant-outline', label: 'Lunch' };
      case 'dinner':
        return { name: 'moon-outline', label: 'Dinner' };
      case 'snack':
        return { name: 'cafe-outline', label: 'Snack' };
      default:
        return { name: 'nutrition-outline', label: 'Meal' };
    }
  };
  
  const mealType = getMealTypeIcon(item.mealType);
  
  return (
    <TouchableOpacity 
      style={[styles.historyItem, { backgroundColor: colors.card }]}
      onPress={onPress}
    >
      {item.image ? (
        <Image 
          source={{ uri: item.image }} 
          style={styles.historyItemImage} 
        />
      ) : (
        <View style={[styles.historyItemImagePlaceholder, { backgroundColor: colors.border }]}>
          <Ionicons name="image-outline" size={24} color={colors.textSecondary} />
        </View>
      )}
      
      <View style={styles.historyItemContent}>
        <View style={styles.historyItemHeader}>
          <Text style={[styles.historyItemTitle, { color: colors.text }]}>
            {item.foodItems.length > 1 
              ? `${item.foodItems[0].foodName} & ${item.foodItems.length - 1} more`
              : item.foodItems[0]?.foodName || 'Unknown Food'}
          </Text>
          
          {item.favorite && (
            <Ionicons name="star" size={16} color="#FFD700" />
          )}
        </View>
        
        <View style={styles.historyItemDetails}>
          <Text style={[styles.historyItemDate, { color: colors.textSecondary }]}>
            {formatDate(item.timestamp)}
          </Text>
          
          <View style={styles.mealTypeTag}>
            <Ionicons name={mealType.name as any} size={12} color={colors.textSecondary} />
            <Text style={[styles.mealTypeText, { color: colors.textSecondary }]}>
              {mealType.label}
            </Text>
          </View>
        </View>
        
        <View style={styles.historyItemFooter}>
          <View style={styles.historyItemStat}>
            <Text style={[styles.historyItemStatValue, { color: colors.text }]}>
              {Math.round(item.totalWeight)}g
            </Text>
            <Text style={[styles.historyItemStatLabel, { color: colors.textTertiary }]}>
              Weight
            </Text>
          </View>
          
          {item.totalCalories && (
            <View style={styles.historyItemStat}>
              <Text style={[styles.historyItemStatValue, { color: colors.text }]}>
                {item.totalCalories}
              </Text>
              <Text style={[styles.historyItemStatLabel, { color: colors.textTertiary }]}>
                Calories
              </Text>
            </View>
          )}
          
          <View style={styles.historyItemStat}>
            <Text style={[styles.historyItemStatValue, { color: colors.text }]}>
              {item.foodItems.length}
            </Text>
            <Text style={[styles.historyItemStatLabel, { color: colors.textTertiary }]}>
              Items
            </Text>
          </View>
        </View>
      </View>
      
      <Ionicons 
        name="chevron-forward" 
        size={20} 
        color={colors.textTertiary} 
        style={styles.historyItemChevron}
      />
    </TouchableOpacity>
  );
};

// Add this new component for detailed meal view
const MealDetailModal = ({ 
  visible, 
  meal, 
  onClose,
  onToggleFavorite
}: { 
  visible: boolean,
  meal: MealHistory.MealScanRecord | null,
  onClose: () => void,
  onToggleFavorite: (id: string, favorite: boolean) => void
}) => {
  const { colors } = useTheme();
  
  if (!meal) return null;
  
  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={[styles.modalContainer, { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
        <View style={[styles.modalContent, { backgroundColor: colors.background }]}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={onClose}
            >
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
            
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Meal Details
            </Text>
            
            <TouchableOpacity
              style={styles.modalFavoriteButton}
              onPress={() => onToggleFavorite(meal.id, !meal.favorite)}
            >
              <Ionicons 
                name={meal.favorite ? "star" : "star-outline"} 
                size={24} 
                color={meal.favorite ? "#FFD700" : colors.text} 
              />
            </TouchableOpacity>
          </View>
          
          {meal.image && (
            <Image 
              source={{ uri: meal.image }} 
              style={styles.modalImage} 
              resizeMode="cover"
            />
          )}
          
          <ScrollView style={styles.modalScroll}>
            <View style={styles.modalSection}>
              <Text style={[styles.modalSectionTitle, { color: colors.text }]}>
                Food Items
              </Text>
              
              {meal.foodItems.map((item, index) => (
                <View key={index} style={styles.modalFoodItem}>
                  <Text style={[styles.modalFoodItemName, { color: colors.text }]}>
                    {item.foodName}
                  </Text>
                  <Text style={[styles.modalFoodItemDetails, { color: colors.textSecondary }]}>
                    {Math.round(item.weightGrams)}g • {Math.round(item.volumeCm3)}cm³
                  </Text>
                </View>
              ))}
            </View>
            
            {meal.nutritionSummary && (
              <View style={styles.modalSection}>
                <Text style={[styles.modalSectionTitle, { color: colors.text }]}>
                  Nutrition Summary
                </Text>
                <Text style={[styles.modalNutritionText, { color: colors.textSecondary }]}>
                  {meal.nutritionSummary}
                </Text>
              </View>
            )}
            
            <View style={styles.modalSection}>
              <Text style={[styles.modalSectionTitle, { color: colors.text }]}>
                Details
              </Text>
              
              <View style={styles.modalDetailRow}>
                <Text style={[styles.modalDetailLabel, { color: colors.textSecondary }]}>
                  Date
                </Text>
                <Text style={[styles.modalDetailValue, { color: colors.text }]}>
                  {new Date(meal.timestamp).toLocaleDateString()}
                </Text>
              </View>
              
              <View style={styles.modalDetailRow}>
                <Text style={[styles.modalDetailLabel, { color: colors.textSecondary }]}>
                  Time
                </Text>
                <Text style={[styles.modalDetailValue, { color: colors.text }]}>
                  {new Date(meal.timestamp).toLocaleTimeString()}
                </Text>
              </View>
              
              <View style={styles.modalDetailRow}>
                <Text style={[styles.modalDetailLabel, { color: colors.textSecondary }]}>
                  Meal Type
                </Text>
                <Text style={[styles.modalDetailValue, { color: colors.text }]}>
                  {meal.mealType ? meal.mealType.charAt(0).toUpperCase() + meal.mealType.slice(1) : 'Not specified'}
                </Text>
              </View>
              
              <View style={styles.modalDetailRow}>
                <Text style={[styles.modalDetailLabel, { color: colors.textSecondary }]}>
                  Total Weight
                </Text>
                <Text style={[styles.modalDetailValue, { color: colors.text }]}>
                  {Math.round(meal.totalWeight)}g
                </Text>
              </View>
              
              {meal.totalCalories && (
                <View style={styles.modalDetailRow}>
                  <Text style={[styles.modalDetailLabel, { color: colors.textSecondary }]}>
                    Total Calories
                  </Text>
                  <Text style={[styles.modalDetailValue, { color: colors.text }]}>
                    {meal.totalCalories} kcal
                  </Text>
                </View>
              )}
            </View>
            
            {meal.tags && meal.tags.length > 0 && (
              <View style={styles.modalSection}>
                <Text style={[styles.modalSectionTitle, { color: colors.text }]}>
                  Tags
                </Text>
                <View style={styles.modalTagsContainer}>
                  {meal.tags.map((tag, index) => (
                    <View 
                      key={index} 
                      style={[styles.modalTag, { backgroundColor: colors.card }]}
                    >
                      <Text style={[styles.modalTagText, { color: colors.textSecondary }]}>
                        {tag}
                      </Text>
                    </View>
                  ))}
                </View>
              </View>
            )}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

// Local implementation of useTheme hook
function useTheme() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const colors = useMemo(() => {
    if (isDark) {
      return {
        primary: '#0A84FF',
        background: '#000000',
        card: '#1C1C1E',
        text: '#FFFFFF',
        textSecondary: '#ABABAB',
        textTertiary: '#757575',
        border: '#38383A',
      };
    }
    
    return {
      primary: '#007AFF',
      background: '#F2F2F7',
      card: '#FFFFFF',
      text: '#000000',
      textSecondary: '#6D6D72',
      textTertiary: '#A9A9A9',
      border: '#E5E5EA',
    };
  }, [isDark]);

  return { colors, isDark };
}

export default function LiDARPortionScannerScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  
  const [showScanner, setShowScanner] = useState(false);
  const [scanResult, setScanResult] = useState<any>(null);
  
  // Add new state for meal history
  const [mealHistory, setMealHistory] = useState<MealHistory.MealScanRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMeal, setSelectedMeal] = useState<MealHistory.MealScanRecord | null>(null);
  const [showMealDetail, setShowMealDetail] = useState(false);
  
  // Load meal history on component mount
  useEffect(() => {
    loadMealHistory();
  }, []);
  
  // Reload meal history when returning from scanner
  useEffect(() => {
    if (!showScanner) {
      loadMealHistory();
    }
  }, [showScanner]);
  
  const loadMealHistory = async () => {
    setLoading(true);
    try {
      // Fetch the most recent meals (limit to 10)
      const history = await MealHistory.getMealHistory(10);
      setMealHistory(history);
    } catch (error) {
      console.error('Failed to load meal history:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleMealItemPress = (meal: MealHistory.MealScanRecord) => {
    setSelectedMeal(meal);
    setShowMealDetail(true);
  };
  
  const handleToggleFavorite = async (id: string, favorite: boolean) => {
    try {
      // Update the meal in storage
      await MealHistory.updateMealRecord(id, { favorite });
      
      // Update local state
      setMealHistory(prev => 
        prev.map(meal => 
          meal.id === id ? { ...meal, favorite } : meal
        )
      );
      
      // Update selected meal if it's currently displayed
      if (selectedMeal && selectedMeal.id === id) {
        setSelectedMeal({ ...selectedMeal, favorite });
      }
    } catch (error) {
      console.error('Failed to update favorite status:', error);
    }
  };
  
  const handleScanComplete = (result: any) => {
    setShowScanner(false);
    setScanResult(result);
  };
  
  const handleStartScan = () => {
    setShowScanner(true);
  };
  
  const handleBackPress = () => {
    router.back();
  };
  
  const renderEmptyHistory = () => {
    return (
      <View style={styles.emptyHistoryContainer}>
        <Ionicons 
          name="nutrition-outline" 
          size={60} 
          color={colors.textTertiary} 
        />
        <Text style={[styles.emptyHistoryText, { color: colors.textSecondary }]}>
          No meal scans yet
        </Text>
        <Text style={[styles.emptyHistorySubtext, { color: colors.textTertiary }]}>
          Start scanning your meals to see them here
        </Text>
      </View>
    );
  };
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      
      {showScanner ? (
        <ScannerWithLiDAR 
          onScanComplete={handleScanComplete}
          onClose={() => setShowScanner(false)}
        />
      ) : (
        <>
          <View style={styles.header}>
            <Pressable
              style={styles.backButton}
              onPress={handleBackPress}
            >
              <Ionicons name="arrow-back" size={24} color={colors.text} />
            </Pressable>
            <Text style={[styles.title, { color: colors.text }]}>
              LiDAR Portion Scanner
            </Text>
            <View style={styles.headerSpacer} />
          </View>
          
          {scanResult ? (
            <ScrollView 
              style={styles.scrollView}
              contentContainerStyle={styles.scrollContent}
            >
              <View style={[styles.resultCard, { backgroundColor: colors.card }]}>
                <Text style={[styles.resultTitle, { color: colors.text }]}>
                  Scan Results
                </Text>
                
                {/* Food thumbnail - optional, if we had an image */}
                {scanResult.imageUri && (
                  <View style={styles.resultImageContainer}>
                    <Image 
                      source={{ uri: scanResult.imageUri }} 
                      style={styles.resultImage}
                      resizeMode="cover"
                    />
                  </View>
                )}
                
                {/* Basic scan info - with better spacing */}
                <View style={[styles.resultItem, { marginTop: scanResult.imageUri ? 16 : 0 }]}>
                  <Text style={[styles.resultLabel, { color: colors.textSecondary }]}>
                    Food
                  </Text>
                  <Text style={[styles.resultValue, { color: colors.text }]}>
                    {scanResult.foodName}
                  </Text>
                </View>
                
                {/* 3D Measurements */}
                <Text style={[styles.measurementsTitle, { color: colors.primary }]}>
                  3D Measurements
                </Text>
                
                <View style={styles.measurementsGrid}>
                  <View style={[styles.measurementItem, { borderColor: colors.border }]}>
                    <View style={styles.measurementIconContainer}>
                      <Ionicons name="cube-outline" size={24} color={colors.primary} />
                    </View>
                    <Text style={[styles.measurementValue, { color: colors.text }]}>
                      {Math.round(scanResult.volumeCm3)} cm³
                    </Text>
                    <Text style={[styles.measurementLabel, { color: colors.textSecondary }]}>
                      Volume
                    </Text>
                  </View>
                  
                  <View style={[styles.measurementItem, { borderColor: colors.border }]}>
                    <View style={styles.measurementIconContainer}>
                      <Ionicons name="scale-outline" size={24} color={colors.primary} />
                    </View>
                    <Text style={[styles.measurementValue, { color: colors.text }]}>
                      {Math.round(scanResult.weightGrams)} g
                    </Text>
                    <Text style={[styles.measurementLabel, { color: colors.textSecondary }]}>
                      Weight
                    </Text>
                  </View>
                  
                  {scanResult.dimensions && (
                    <>
                      <View style={[styles.measurementItem, { borderColor: colors.border }]}>
                        <View style={styles.measurementIconContainer}>
                          <Ionicons name="resize-outline" size={24} color={colors.primary} />
                        </View>
                        <Text style={[styles.measurementValue, { color: colors.text }]}>
                          {Math.round(scanResult.dimensions.width)} cm
                        </Text>
                        <Text style={[styles.measurementLabel, { color: colors.textSecondary }]}>
                          Width
                        </Text>
                      </View>
                      
                      <View style={[styles.measurementItem, { borderColor: colors.border }]}>
                        <View style={styles.measurementIconContainer}>
                          <Ionicons name="resize-outline" size={24} color={colors.primary} />
                        </View>
                        <Text style={[styles.measurementValue, { color: colors.text }]}>
                          {Math.round(scanResult.dimensions.height)} cm
                        </Text>
                        <Text style={[styles.measurementLabel, { color: colors.textSecondary }]}>
                          Height
                        </Text>
                      </View>
                    </>
                  )}
                </View>
                
                <View style={styles.divider} />
                
                {/* Nutrition Information - If available */}
                {scanResult.nutritionData && (
                  <>
                    <Text style={[styles.nutritionTitle, { color: colors.text }]}>
                      Estimated Nutrition
                    </Text>
                    
                    <View style={styles.nutritionGrid}>
                      <View style={styles.nutritionItem}>
                        <Ionicons name="flame-outline" size={22} color={colors.primary} />
                        <Text style={[styles.nutritionValue, { color: colors.text }]}>
                          {scanResult.nutritionData.calories || 'N/A'}
                        </Text>
                        <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                          Calories
                        </Text>
                      </View>
                      
                      <View style={styles.nutritionItem}>
                        <Ionicons name="fitness-outline" size={22} color={colors.primary} />
                        <Text style={[styles.nutritionValue, { color: colors.text }]}>
                          {scanResult.nutritionData.protein ? `${scanResult.nutritionData.protein}g` : 'N/A'}
                        </Text>
                        <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                          Protein
                        </Text>
                      </View>
                      
                      <View style={styles.nutritionItem}>
                        <Ionicons name="leaf-outline" size={22} color={colors.primary} />
                        <Text style={[styles.nutritionValue, { color: colors.text }]}>
                          {scanResult.nutritionData.carbs ? `${scanResult.nutritionData.carbs}g` : 'N/A'}
                        </Text>
                        <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                          Carbs
                        </Text>
                      </View>
                      
                      <View style={styles.nutritionItem}>
                        <Ionicons name="water-outline" size={22} color={colors.primary} />
                        <Text style={[styles.nutritionValue, { color: colors.text }]}>
                          {scanResult.nutritionData.fat ? `${scanResult.nutritionData.fat}g` : 'N/A'}
                        </Text>
                        <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                          Fat
                        </Text>
                      </View>
                    </View>
                    
                    <View style={styles.divider} />
                  </>
                )}
                
                {/* Analysis */}
                <Text style={[styles.nutritionTitle, { color: colors.text }]}>
                  Nutrition Analysis
                </Text>
                
                <Text style={[styles.nutritionText, { color: colors.textSecondary }]}>
                  {scanResult.nutritionAnalysis.analysis}
                </Text>
                
                {/* Health Insights - if available */}
                {scanResult.nutritionAnalysis.healthInsights && scanResult.nutritionAnalysis.healthInsights.length > 0 && (
                  <View style={styles.insightsContainer}>
                    <Text style={[styles.insightsTitle, { color: colors.text }]}>
                      Health Insights
                    </Text>
                    
                    {scanResult.nutritionAnalysis.healthInsights.map((insight, index) => (
                      <View key={index} style={styles.insightItem}>
                        <Ionicons name="checkmark-circle-outline" size={20} color={colors.primary} />
                        <Text style={[styles.insightText, { color: colors.text }]}>
                          {insight}
                        </Text>
                      </View>
                    ))}
                  </View>
                )}
                
                {/* Recommendations - if available */}
                {scanResult.nutritionAnalysis.recommendations && scanResult.nutritionAnalysis.recommendations.length > 0 && (
                  <View style={styles.recommendationsContainer}>
                    <Text style={[styles.recommendationsTitle, { color: colors.text }]}>
                      Recommendations
                    </Text>
                    
                    {scanResult.nutritionAnalysis.recommendations.map((recommendation, index) => (
                      <View key={index} style={styles.recommendationItem}>
                        <Ionicons name="bulb-outline" size={20} color={colors.primary} />
                        <Text style={[styles.recommendationText, { color: colors.text }]}>
                          {recommendation}
                        </Text>
                      </View>
                    ))}
                  </View>
                )}
                
                {/* Confidence Badge */}
                {scanResult.confidence && (
                  <View style={[styles.confidenceBadge, { backgroundColor: 'rgba(0,122,255,0.1)' }]}>
                    <Text style={[styles.confidenceText, { color: colors.primary }]}>
                      Analysis Confidence: {Math.round(scanResult.confidence * 100)}%
                    </Text>
                  </View>
                )}
                
                {/* Scan date/time */}
                <Text style={[styles.scanTimestamp, { color: colors.textSecondary }]}>
                  Scanned: {new Date().toLocaleString()}
                </Text>
              </View>
              
              {/* Action buttons */}
              <View style={styles.actionButtonsContainer}>
                <TouchableOpacity
                  style={[styles.actionButton, styles.rescanButton, { backgroundColor: isDark ? '#374151' : '#e5e7eb' }]}
                  onPress={handleStartScan}
                >
                  <Ionicons name="refresh-outline" size={22} color={colors.text} />
                  <Text style={[styles.actionButtonText, { color: colors.text }]}>
                    Scan Again
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.actionButton, styles.saveButton, { backgroundColor: colors.primary }]}
                  onPress={() => {
                    // Add save functionality if needed
                    console.log('Saving meal data...');
                  }}
                >
                  <Ionicons name="save-outline" size={22} color="white" />
                  <Text style={[styles.actionButtonText, { color: 'white' }]}>
                    Save Meal
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Recent Meal History */}
              {mealHistory.length > 0 && (
                <View style={styles.historySection}>
                  <Text style={[styles.historySectionTitle, { color: colors.text }]}>
                    Recent Meals
                  </Text>
                  
                  {mealHistory.slice(0, 3).map((meal) => (
                    <MealHistoryItem 
                      key={meal.id} 
                      item={meal} 
                      onPress={() => handleMealItemPress(meal)}
                    />
                  ))}
                </View>
              )}
            </ScrollView>
          ) : (
            <>
              <View style={styles.introContainer}>
                <View style={styles.iconContainer}>
                  <Ionicons 
                    name="scan-outline" 
                    size={80} 
                    color={colors.primary} 
                  />
                </View>
                
                <Text style={[styles.introTitle, { color: colors.text }]}>
                  LiDAR Food Scanner
                </Text>
                
                <Text style={[styles.introText, { color: colors.textSecondary }]}>
                  Use your device's LiDAR scanner to accurately measure food portions
                  and get precise nutritional information.
                </Text>
                
                <View style={styles.featureList}>
                  <View style={styles.featureItem}>
                    <Ionicons name="scan" size={24} color={colors.primary} />
                    <Text style={[styles.featureText, { color: colors.text }]}>
                      Measures food volume in cm³
                    </Text>
                  </View>
                  
                  <View style={styles.featureItem}>
                    <Ionicons name="restaurant" size={24} color={colors.primary} />
                    <Text style={[styles.featureText, { color: colors.text }]}>
                      Identifies food with AI
                    </Text>
                  </View>
                  
                  <View style={styles.featureItem}>
                    <Ionicons name="calculator" size={24} color={colors.primary} />
                    <Text style={[styles.featureText, { color: colors.text }]}>
                      Estimates weight in grams
                    </Text>
                  </View>
                  
                  <View style={styles.featureItem}>
                    <Ionicons name="nutrition" size={24} color={colors.primary} />
                    <Text style={[styles.featureText, { color: colors.text }]}>
                      Analyzes nutritional content
                    </Text>
                  </View>
                </View>
                
                <Pressable
                  style={[styles.scanButton, { backgroundColor: colors.primary }]}
                  onPress={handleStartScan}
                >
                  <Text style={styles.scanButtonText}>
                    Start Scanning
                  </Text>
                </Pressable>
                
                <Text style={[styles.deviceNote, { color: colors.textTertiary }]}>
                  Note: LiDAR scanning requires iPhone 12 Pro or newer.
                  Other devices will use a standard camera.
                </Text>
              </View>
              
              {/* Meal History Section */}
              <View style={styles.historyContainer}>
                <Text style={[styles.historyTitle, { color: colors.text }]}>
                  Recent Meal Scans
                </Text>
                
                {loading ? (
                  <View style={styles.loadingContainer}>
                    {/* Loading indicator */}
                    <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                      Loading meal history...
                    </Text>
                  </View>
                ) : mealHistory.length === 0 ? (
                  renderEmptyHistory()
                ) : (
                  <FlatList
                    data={mealHistory}
                    keyExtractor={(item) => item.id}
                    renderItem={({ item }) => (
                      <MealHistoryItem 
                        item={item} 
                        onPress={() => handleMealItemPress(item)}
                      />
                    )}
                    style={styles.historyList}
                    contentContainerStyle={styles.historyListContent}
                  />
                )}
              </View>
            </>
          )}
          
          <MealDetailModal
            visible={showMealDetail}
            meal={selectedMeal}
            onClose={() => setShowMealDetail(false)}
            onToggleFavorite={handleToggleFavorite}
          />
        </>
      )}
    </SafeAreaView>
  );
}

// Add these styles to the StyleSheet
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerSpacer: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  introContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    marginBottom: 20,
  },
  introTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  introText: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 30,
  },
  featureList: {
    width: '100%',
    marginBottom: 30,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  featureText: {
    fontSize: 16,
    marginLeft: 12,
  },
  scanButton: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 10,
    width: '100%',
  },
  scanButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  deviceNote: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 10,
  },
  resultCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  resultImageContainer: {
    width: '100%',
    height: 180,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 8,
  },
  resultImage: {
    width: '100%',
    height: '100%',
  },
  resultTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  measurementsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 12,
  },
  measurementsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  measurementItem: {
    width: '48%',
    borderWidth: 1,
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    alignItems: 'center',
  },
  measurementIconContainer: {
    marginBottom: 8,
  },
  measurementValue: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  measurementLabel: {
    fontSize: 14,
  },
  nutritionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  nutritionText: {
    fontSize: 16,
    lineHeight: 24,
  },
  historyContainer: {
    flex: 1,
    padding: 16,
  },
  historyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  emptyHistoryContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  emptyHistoryText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyHistorySubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  historyList: {
    flex: 1,
  },
  historyListContent: {
    paddingBottom: 20,
  },
  historyItem: {
    flexDirection: 'row',
    borderRadius: 12,
    marginBottom: 12,
    overflow: 'hidden',
  },
  historyItemImage: {
    width: 70,
    height: 70,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
  },
  historyItemImagePlaceholder: {
    width: 70,
    height: 70,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  historyItemContent: {
    flex: 1,
    padding: 12,
  },
  historyItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  historyItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  historyItemDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  historyItemDate: {
    fontSize: 12,
    marginRight: 8,
  },
  mealTypeTag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    backgroundColor: 'rgba(150,150,150,0.1)',
  },
  mealTypeText: {
    fontSize: 10,
    marginLeft: 4,
  },
  historyItemFooter: {
    flexDirection: 'row',
  },
  historyItemStat: {
    marginRight: 16,
  },
  historyItemStatValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  historyItemStatLabel: {
    fontSize: 10,
  },
  historyItemChevron: {
    alignSelf: 'center',
    paddingRight: 12,
  },
  historySection: {
    marginTop: 20,
  },
  historySectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(150,150,150,0.2)',
  },
  modalCloseButton: {
    padding: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalFavoriteButton: {
    padding: 8,
  },
  modalImage: {
    width: '100%',
    height: 200,
  },
  modalScroll: {
    flex: 1,
  },
  modalSection: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(150,150,150,0.2)',
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  modalFoodItem: {
    marginBottom: 10,
  },
  modalFoodItemName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  modalFoodItemDetails: {
    fontSize: 14,
  },
  modalNutritionText: {
    fontSize: 14,
    lineHeight: 20,
  },
  modalDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  modalDetailLabel: {
    fontSize: 14,
  },
  modalDetailValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  modalTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  modalTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  modalTagText: {
    fontSize: 12,
  },
  resultItem: {
    marginBottom: 12,
  },
  resultLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  resultValue: {
    fontSize: 18,
    fontWeight: '600',
  },
  divider: {
    height: 1,
    backgroundColor: 'rgba(150,150,150,0.2)',
    marginVertical: 16,
  },
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  nutritionItem: {
    width: '48%',
    padding: 12,
    marginBottom: 12,
    alignItems: 'center',
  },
  nutritionValue: {
    fontSize: 18,
    fontWeight: '700',
    marginVertical: 6,
  },
  nutritionLabel: {
    fontSize: 14,
  },
  insightsContainer: {
    marginTop: 16,
  },
  insightsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  insightText: {
    fontSize: 15,
    marginLeft: 8,
    flex: 1,
  },
  recommendationsContainer: {
    marginTop: 16,
  },
  recommendationsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  recommendationText: {
    fontSize: 15,
    marginLeft: 8,
    flex: 1,
  },
  confidenceBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginTop: 16,
  },
  confidenceText: {
    fontSize: 14,
    fontWeight: '500',
  },
  scanTimestamp: {
    fontSize: 12,
    marginTop: 16,
    textAlign: 'right',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    flex: 1,
  },
  rescanButton: {
    marginRight: 8,
  },
  saveButton: {
    marginLeft: 8,
  },
  actionButtonText: {
    fontWeight: '600',
    fontSize: 16,
    marginLeft: 8,
  },
}); 