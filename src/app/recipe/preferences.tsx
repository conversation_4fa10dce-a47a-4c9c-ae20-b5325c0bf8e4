import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, ScrollView } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { DietaryPreferencesSelector } from '@/components/DietaryPreferencesSelector';
import { useUserPreferences } from '@/contexts/UserPreferencesContext';

export default function HealthierAlternativePreferencesScreen() {
  const { colors } = useTheme();
  const router = useRouter();
  const { 
    dietaryPreferences, 
    healthGoals, 
    setDietaryPreferences, 
    setHealthGoals 
  } = useUserPreferences();

  const handleBack = () => {
    router.back();
  };

  const handlePreferencesChange = (preferences: string[]) => {
    setDietaryPreferences(preferences);
  };

  const handleHealthGoalsChange = (goals: string[]) => {
    setHealthGoals(goals);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen
        options={{
          title: 'Healthier Alternative Preferences',
          headerShown: false,
        }}
      />
      
      <View style={[styles.header, { backgroundColor: colors.card, borderBottomColor: colors.border }]}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.subtle }]}
          onPress={handleBack}
        >
          <Feather name="arrow-left" size={20} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Healthier Alternative Preferences
        </Text>
        <View style={styles.headerPlaceholder} />
      </View>
      
      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.introduction}>
          <Text style={[styles.introTitle, { color: colors.text }]}>
            Personalize Your Healthier Alternatives
          </Text>
          <Text style={[styles.introText, { color: colors.textSecondary }]}>
            We use your preferences to tailor healthier recipe alternatives that match your 
            dietary needs and health objectives. Select the options that matter to you.
          </Text>
        </View>
        
        <DietaryPreferencesSelector
          onPreferencesChange={handlePreferencesChange}
          onHealthGoalsChange={handleHealthGoalsChange}
        />
        
        <View style={[styles.infoBox, { backgroundColor: colors.primaryLight }]}>
          <Feather name="info" size={20} color={colors.primary} style={styles.infoIcon} />
          <Text style={[styles.infoText, { color: colors.primary }]}>
            These preferences will be used every time we generate healthier alternatives
            for the foods you scan or recipes you view.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  headerPlaceholder: {
    width: 36,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 40,
  },
  introduction: {
    padding: 16,
    paddingBottom: 0,
  },
  introTitle: {
    fontSize: 22,
    fontWeight: '700',
    marginBottom: 8,
  },
  introText: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 24,
  },
  infoBox: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  infoIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
}); 