import React, { useState, useEffect, useCallback } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  Image, 
  TextInput,
  FlatList, 
  Modal,
  ActivityIndicator, 
  Alert
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { 
  getSavedRecipes, 
  getRecipeCollections, 
  createRecipeCollection,
  rateRecipe,
  addRecipeToCollection,
  removeRecipeFromCollection,
  deleteSavedRecipe,
  SavedRecipe,
  RecipeCollection
} from '@/services/recipeCollectionService';
import { getPlaceholderFoodImage } from '@/services/imageGenerationService';

export default function SavedRecipesScreen() {
  const { colors } = useTheme();
  const router = useRouter();
  
  // State
  const [recipes, setRecipes] = useState<SavedRecipe[]>([]);
  const [collections, setCollections] = useState<RecipeCollection[]>([]);
  const [selectedCollection, setSelectedCollection] = useState<string | null>(null);
  const [isGridView, setIsGridView] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [showNewCollectionModal, setShowNewCollectionModal] = useState(false);
  const [newCollectionName, setNewCollectionName] = useState('');
  const [newCollectionDescription, setNewCollectionDescription] = useState('');
  const [showAddToCollectionModal, setShowAddToCollectionModal] = useState(false);
  const [selectedRecipeId, setSelectedRecipeId] = useState<string | null>(null);
  const [showRatingModal, setShowRatingModal] = useState(false);
  const [currentRating, setCurrentRating] = useState(0);
  
  // Load saved recipes and collections
  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      const [recipesData, collectionsData] = await Promise.all([
        getSavedRecipes(),
        getRecipeCollections()
      ]);
      
      setRecipes(recipesData);
      setCollections(collectionsData);
    } catch (error) {
      console.error('Error loading saved recipes:', error);
      Alert.alert('Error', 'Failed to load saved recipes');
    } finally {
      setLoading(false);
    }
  }, []);
  
  useEffect(() => {
    loadData();
  }, [loadData]);
  
  // Filter recipes based on search and collection
  const filteredRecipes = recipes.filter(recipe => {
    const matchesSearch = searchQuery === '' || 
      recipe.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      recipe.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCollection = selectedCollection === null || 
      (recipe.collectionIds && recipe.collectionIds.includes(selectedCollection));
    
    return matchesSearch && matchesCollection;
  });
  
  // Handlers
  const handleAddCollection = async () => {
    if (!newCollectionName.trim()) {
      Alert.alert('Error', 'Please enter a collection name');
      return;
    }
    
    setLoading(true);
    try {
      const collection = await createRecipeCollection(
        newCollectionName.trim(),
        newCollectionDescription.trim() || undefined
      );
      
      if (collection) {
        setCollections(prev => [...prev, collection]);
        setShowNewCollectionModal(false);
        setNewCollectionName('');
        setNewCollectionDescription('');
      } else {
        throw new Error('Failed to create collection');
      }
    } catch (error) {
      console.error('Error creating collection:', error);
      Alert.alert('Error', 'Failed to create collection');
    } finally {
      setLoading(false);
    }
  };
  
  const handleAddToCollection = async (collectionId: string) => {
    if (!selectedRecipeId) return;
    
    setLoading(true);
    try {
      const success = await addRecipeToCollection(selectedRecipeId, collectionId);
      
      if (success) {
        // Update local recipes state to reflect the change
        setRecipes(prev => prev.map(recipe => {
          if (recipe.id === selectedRecipeId) {
            const collectionIds = recipe.collectionIds || [];
            if (!collectionIds.includes(collectionId)) {
              return {
                ...recipe,
                collectionIds: [...collectionIds, collectionId]
              };
            }
          }
          return recipe;
        }));
        
        // Update collection count
        setCollections(prev => prev.map(collection => {
          if (collection.id === collectionId) {
            return {
              ...collection,
              recipeCount: (collection.recipeCount || 0) + 1
            };
          }
          return collection;
        }));
        
        setShowAddToCollectionModal(false);
        setSelectedRecipeId(null);
      } else {
        throw new Error('Failed to add to collection');
      }
    } catch (error) {
      console.error('Error adding to collection:', error);
      Alert.alert('Error', 'Failed to add recipe to collection');
    } finally {
      setLoading(false);
    }
  };
  
  const handleRateRecipe = async () => {
    if (!selectedRecipeId) return;
    
    setLoading(true);
    try {
      const success = await rateRecipe(selectedRecipeId, currentRating);
      
      if (success) {
        // Update local recipes state to reflect the change
        setRecipes(prev => prev.map(recipe => {
          if (recipe.id === selectedRecipeId) {
            return {
              ...recipe,
              rating: currentRating
            };
          }
          return recipe;
        }));
        
        setShowRatingModal(false);
        setSelectedRecipeId(null);
        setCurrentRating(0);
      } else {
        throw new Error('Failed to rate recipe');
      }
    } catch (error) {
      console.error('Error rating recipe:', error);
      Alert.alert('Error', 'Failed to rate recipe');
    } finally {
      setLoading(false);
    }
  };
  
  const handleDeleteRecipe = async (recipeId: string) => {
    Alert.alert(
      'Delete Recipe',
      'Are you sure you want to delete this recipe?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            setLoading(true);
            try {
              const success = await deleteSavedRecipe(recipeId);
              
              if (success) {
                // Update local recipes state to reflect the change
                setRecipes(prev => prev.filter(recipe => recipe.id !== recipeId));
              } else {
                throw new Error('Failed to delete recipe');
              }
            } catch (error) {
              console.error('Error deleting recipe:', error);
              Alert.alert('Error', 'Failed to delete recipe');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };
  
  const handleRemoveFromCollection = async (recipeId: string, collectionId: string) => {
    setLoading(true);
    try {
      const success = await removeRecipeFromCollection(recipeId, collectionId);
      
      if (success) {
        // Update local recipes state to reflect the change
        setRecipes(prev => prev.map(recipe => {
          if (recipe.id === recipeId) {
            return {
              ...recipe,
              collectionIds: (recipe.collectionIds || []).filter(id => id !== collectionId)
            };
          }
          return recipe;
        }));
        
        // Update collection count
        setCollections(prev => prev.map(collection => {
          if (collection.id === collectionId) {
            return {
              ...collection,
              recipeCount: Math.max((collection.recipeCount || 0) - 1, 0)
            };
          }
          return collection;
        }));
      } else {
        throw new Error('Failed to remove from collection');
      }
    } catch (error) {
      console.error('Error removing from collection:', error);
      Alert.alert('Error', 'Failed to remove recipe from collection');
    } finally {
      setLoading(false);
    }
  };
  
  const handleOpenRecipe = (recipe: SavedRecipe) => {
    // Navigate to recipe detail screen (placeholder for future implementation)
    console.log('Opening recipe:', recipe.id);
    Alert.alert('Feature Coming Soon', 'Recipe detail view is not yet implemented');
  };
  
  // Render recipe item
  const renderRecipeItem = ({ item }: { item: SavedRecipe }) => {
    const recipe = item;
    const recipeCollections = collections.filter(
      collection => recipe.collectionIds?.includes(collection.id)
    );
    
    if (isGridView) {
      return (
        <TouchableOpacity
          style={[styles.gridItem, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={() => handleOpenRecipe(recipe)}
        >
          <Image
            source={{ uri: recipe.imageUrl || getPlaceholderFoodImage(recipe.name) }}
            style={styles.gridImage}
            resizeMode="cover"
          />
          <View style={styles.gridContent}>
            <Text style={[styles.gridTitle, { color: colors.text }]} numberOfLines={1}>
              {recipe.name}
            </Text>
            <Text style={[styles.gridSubtitle, { color: colors.textSecondary }]} numberOfLines={1}>
              {recipe.originalFoodName}
            </Text>
            <View style={styles.gridFooter}>
              <View style={styles.ratingContainer}>
                {Array.from({ length: 5 }).map((_, i) => (
                  <Feather name="star" size={12} color={i < (recipe.rating || 0) ? colors.primary : colors.subtle} />
                ))}
              </View>
              <Text style={[styles.calorieText, { color: colors.primary }]}>
                {recipe.nutritionalInfo.calories} cal
              </Text>
            </View>
          </View>
          <View style={styles.gridActions}>
            <TouchableOpacity
              style={[styles.iconButton, { backgroundColor: colors.subtle }]}
              onPress={() => {
                setSelectedRecipeId(recipe.id);
                setShowAddToCollectionModal(true);
              }}
            >
              <FolderPlus size={16} color={colors.text} />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.iconButton, { backgroundColor: colors.subtle }]}
              onPress={() => {
                setSelectedRecipeId(recipe.id);
                setCurrentRating(recipe.rating || 0);
                setShowRatingModal(true);
              }}
            >
              <Feather name="star" size={16} color={colors.text} />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.iconButton, { backgroundColor: colors.subtle }]}
              onPress={() => handleDeleteRecipe(recipe.id)}
            >
              <Feather name="trash" size={16} color={colors.text} />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      );
    } else {
      // List view
      return (
        <TouchableOpacity
          style={[styles.listItem, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={() => handleOpenRecipe(recipe)}
        >
          <Image
            source={{ uri: recipe.imageUrl || getPlaceholderFoodImage(recipe.name) }}
            style={styles.listImage}
            resizeMode="cover"
          />
          <View style={styles.listContent}>
            <Text style={[styles.listTitle, { color: colors.text }]}>
              {recipe.name}
            </Text>
            <Text style={[styles.listSubtitle, { color: colors.textSecondary }]} numberOfLines={1}>
              {recipe.originalFoodName}
            </Text>
            <View style={styles.listFooter}>
              <View style={styles.ratingContainer}>
                {Array.from({ length: 5 }).map((_, i) => (
                  <Feather name="star" size={12} color={i < (recipe.rating || 0) ? colors.primary : colors.subtle} />
                ))}
              </View>
              <Text style={[styles.calorieText, { color: colors.primary }]}>
                {recipe.nutritionalInfo.calories} cal
              </Text>
            </View>
            {recipeCollections.length > 0 && (
              <View style={styles.collectionsContainer}>
                {recipeCollections.map(collection => (
                  <TouchableOpacity
                    key={collection.id}
                    style={[styles.collectionTag, { backgroundColor: colors.subtle }]}
                    onPress={() => handleRemoveFromCollection(recipe.id, collection.id)}
                  >
                    <Text style={[styles.collectionTagText, { color: colors.textSecondary }]}>
                      {collection.name}
                    </Text>
                    <Text style={[styles.removeTag, { color: colors.error }]}>×</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>
          <View style={styles.listActions}>
            <TouchableOpacity
              style={[styles.iconButton, { backgroundColor: colors.subtle }]}
              onPress={() => {
                setSelectedRecipeId(recipe.id);
                setShowAddToCollectionModal(true);
              }}
            >
              <FolderPlus size={16} color={colors.text} />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.iconButton, { backgroundColor: colors.subtle }]}
              onPress={() => {
                setSelectedRecipeId(recipe.id);
                setCurrentRating(recipe.rating || 0);
                setShowRatingModal(true);
              }}
            >
              <Feather name="star" size={16} color={colors.text} />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.iconButton, { backgroundColor: colors.subtle }]}
              onPress={() => handleDeleteRecipe(recipe.id)}
            >
              <Feather name="trash" size={16} color={colors.text} />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      );
    }
  };
  
  // Render collection tabs
  const renderCollectionTab = ({ item }: { item: RecipeCollection | null }) => {
    const isSelected = selectedCollection === (item?.id || null);
    
    return (
      <TouchableOpacity
        style={[
          styles.collectionTab,
          { 
            backgroundColor: isSelected ? colors.primaryLight : colors.subtle,
            borderColor: isSelected ? colors.primary : colors.border,
          }
        ]}
        onPress={() => setSelectedCollection(item?.id || null)}
      >
        <Text
          style={[
            styles.collectionTabText,
            { color: isSelected ? colors.primary : colors.textSecondary }
          ]}
        >
          {item ? item.name : 'All Recipes'}
          {item && ` (${item.recipeCount || 0})`}
        </Text>
      </TouchableOpacity>
    );
  };
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen
        options={{
          title: 'Saved Recipes',
          headerShown: false,
        }}
      />
      
      <View style={[styles.header, { backgroundColor: colors.card, borderBottomColor: colors.border }]}>
        <TouchableOpacity 
          style={[styles.backButton, { backgroundColor: colors.subtle }]}
          onPress={() => router.back()}
        >
          <Feather name="arrow-left" size={20} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Saved Healthier Alternatives
        </Text>
        <View style={styles.placeholder} />
      </View>
      
      <View style={styles.searchContainer}>
        <View style={[styles.searchBar, { backgroundColor: colors.subtle }]}>
          <Feather name="search" size={18} color={colors.textSecondary} style={styles.searchIcon} />
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="Search recipes"
            placeholderTextColor={colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
        <TouchableOpacity
          style={[styles.viewToggle, { backgroundColor: colors.subtle }]}
          onPress={() => setIsGridView(!isGridView)}
        >
          {isGridView ? (
            <Feather name="list" size={18} color={colors.text} />
          ) : (
            <Feather name="grid" size={18} color={colors.text} />
          )}
        </TouchableOpacity>
      </View>
      
      <View style={styles.collectionTabsContainer}>
        <FlatList
          data={[null, ...collections]}
          renderItem={renderCollectionTab}
          keyExtractor={item => item?.id || 'all'}
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.collectionTabsList}
        />
        <TouchableOpacity
          style={[styles.addCollectionButton, { backgroundColor: colors.primaryLight }]}
          onPress={() => setShowNewCollectionModal(true)}
        >
          <Feather name="plus" size={18} color={colors.primary} />
        </TouchableOpacity>
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading your saved recipes...
          </Text>
        </View>
      ) : filteredRecipes.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Feather name="heart" size={60} color={colors.primary} style={styles.emptyIcon} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            No saved recipes found
          </Text>
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            {searchQuery ? 
              'Try adjusting your search or collection filter' : 
              'Save healthier alternatives to find them here'}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredRecipes}
          renderItem={renderRecipeItem}
          keyExtractor={item => item.id}
          numColumns={isGridView ? 2 : 1}
          key={isGridView ? 'grid' : 'list'}
          contentContainerStyle={styles.recipeList}
        />
      )}
      
      {/* New Collection Modal */}
      <Modal
        visible={showNewCollectionModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowNewCollectionModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              New Collection
            </Text>
            <TextInput
              style={[styles.textInput, { backgroundColor: colors.subtle, color: colors.text }]}
              placeholder="Collection name"
              placeholderTextColor={colors.textSecondary}
              value={newCollectionName}
              onChangeText={setNewCollectionName}
            />
            <TextInput
              style={[styles.textInput, { backgroundColor: colors.subtle, color: colors.text }]}
              placeholder="Description (optional)"
              placeholderTextColor={colors.textSecondary}
              value={newCollectionDescription}
              onChangeText={setNewCollectionDescription}
              multiline
            />
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.subtle }]}
                onPress={() => setShowNewCollectionModal(false)}
              >
                <Text style={[styles.modalButtonText, { color: colors.text }]}>
                  Cancel
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.primary }]}
                onPress={handleAddCollection}
              >
                <Text style={styles.modalButtonTextPrimary}>
                  Create
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      
      {/* Add to Collection Modal */}
      <Modal
        visible={showAddToCollectionModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowAddToCollectionModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Add to Collection
            </Text>
            <ScrollView style={styles.collectionsScrollView}>
              {collections.length === 0 ? (
                <Text style={[styles.emptyCollectionsText, { color: colors.textSecondary }]}>
                  You don't have any collections yet. Create one first.
                </Text>
              ) : (
                collections.map(collection => (
                  <TouchableOpacity
                    key={collection.id}
                    style={[styles.collectionOption, { borderBottomColor: colors.border }]}
                    onPress={() => handleAddToCollection(collection.id)}
                  >
                    <Text style={[styles.collectionOptionText, { color: colors.text }]}>
                      {collection.name}
                    </Text>
                    <Text style={[styles.collectionCount, { color: colors.textSecondary }]}>
                      {collection.recipeCount || 0} recipes
                    </Text>
                  </TouchableOpacity>
                ))
              )}
            </ScrollView>
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.subtle }]}
                onPress={() => setShowAddToCollectionModal(false)}
              >
                <Text style={[styles.modalButtonText, { color: colors.text }]}>
                  Cancel
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.primary }]}
                onPress={() => {
                  setShowAddToCollectionModal(false);
                  setShowNewCollectionModal(true);
                }}
              >
                <Text style={styles.modalButtonTextPrimary}>
                  New Collection
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      
      {/* Rating Modal */}
      <Modal
        visible={showRatingModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowRatingModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Rate Recipe
            </Text>
            <View style={styles.ratingButtons}>
              {Array.from({ length: 5 }).map((_, i) => (
                <TouchableOpacity
                  key={i}
                  onPress={() => setCurrentRating(i + 1)}
                >
                  <Feather name="star" size={30} color={colors.primary} />
                </TouchableOpacity>
              ))}
            </View>
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.subtle }]}
                onPress={() => setShowRatingModal(false)}
              >
                <Text style={[styles.modalButtonText, { color: colors.text }]}>
                  Cancel
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.primary }]}
                onPress={handleRateRecipe}
              >
                <Text style={styles.modalButtonTextPrimary}>
                  Save Rating
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  placeholder: {
    width: 36,
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 40,
    marginRight: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    height: 40,
  },
  viewToggle: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  collectionTabsContainer: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingLeft: 16,
    alignItems: 'center',
  },
  collectionTabsList: {
    flexGrow: 0,
    marginRight: 8,
  },
  collectionTab: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    borderWidth: 1,
  },
  collectionTabText: {
    fontSize: 13,
    fontWeight: '500',
  },
  addCollectionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  recipeList: {
    padding: 16,
  },
  gridItem: {
    flex: 1,
    margin: 6,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
  },
  gridImage: {
    width: '100%',
    height: 120,
  },
  gridContent: {
    padding: 10,
  },
  gridTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  gridSubtitle: {
    fontSize: 12,
    marginBottom: 6,
  },
  gridFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
  },
  calorieText: {
    fontSize: 12,
    fontWeight: '600',
  },
  gridActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  iconButton: {
    flex: 1,
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listItem: {
    flexDirection: 'row',
    marginBottom: 12,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
  },
  listImage: {
    width: 100,
    height: 100,
  },
  listContent: {
    flex: 1,
    padding: 12,
  },
  listTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  listSubtitle: {
    fontSize: 14,
    marginBottom: 8,
  },
  listFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  listActions: {
    justifyContent: 'space-around',
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  collectionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  collectionTag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 4,
    marginBottom: 4,
  },
  collectionTagText: {
    fontSize: 12,
    marginRight: 4,
  },
  removeTag: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    borderRadius: 12,
    padding: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  textInput: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginBottom: 12,
    fontSize: 14,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  modalButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginLeft: 8,
  },
  modalButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  modalButtonTextPrimary: {
    fontSize: 14,
    fontWeight: '500',
    color: 'white',
  },
  collectionsScrollView: {
    maxHeight: 200,
  },
  collectionOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  collectionOptionText: {
    fontSize: 16,
  },
  collectionCount: {
    fontSize: 14,
  },
  emptyCollectionsText: {
    textAlign: 'center',
    paddingVertical: 20,
    fontSize: 14,
  },
  ratingButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 16,
  },
}); 