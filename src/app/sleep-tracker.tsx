import React, { useEffect } from 'react';
import { StyleSheet, Text, View , useColorScheme } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { Stack } from 'expo-router';

import SleepTracker from '@/components/SleepTracker';
import Colors from '@/constants/Colors';
import { configureSleepNotifications, updateSleepReminders } from '@/services/sleepReminderService';

export default function SleepTrackerScreen() {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];

  // Setup notifications when the component mounts
  useEffect(() => {
    configureSleepNotifications();
    updateSleepReminders().catch(console.error);
  }, []);

  return (
    <SafeAreaView 
      style={[styles.container, { backgroundColor: colors.background }]}
      edges={['top', 'left', 'right']}
    >
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      <Stack.Screen
        options={{
          title: 'Sleep Tracker',
          headerShown: true,
          headerStyle: {
            backgroundColor: colors.background,
          },
          headerTintColor: colors.text,
        }}
      />
      <SleepTracker />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
}); 