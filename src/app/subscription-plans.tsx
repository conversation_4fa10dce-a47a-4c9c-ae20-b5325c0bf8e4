import React, { useState } from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity, Platform, Linking } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { PRODUCTS, BILLING_CYCLES } from '@/stripe-config';
import { createCheckoutSession } from '@/services/stripeService';
import { LinearGradient } from 'expo-linear-gradient';

// Define an interface for feature items
interface FeatureItem {
  text: string;
  included: boolean;
}

export default function SubscriptionPlans() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const { user } = useAuth();
  const [selectedPlan, setSelectedPlan] = useState(0);
  const [selectedCycle, setSelectedCycle] = useState(0);
  const [loading, setLoading] = useState(false);

  const handleSubscribe = async () => {
    if (!user) {
      // Fix the type error by using a valid route with object syntax
      router.push({ pathname: "/" });
      return;
    }

    setLoading(true);
    try {
      // Use your app's domain or a placeholder - Stripe handles this properly
      const baseUrl = 'https://yourdomain.com';
        
      const result = await createCheckoutSession({
        priceId: PRODUCTS[selectedPlan].priceId,
        mode: 'subscription',
        successUrl: `${baseUrl}/profile-screens/billing?session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: `${baseUrl}/subscription-plans`,
      });

      if (result.error) {
        console.error('Error creating checkout session:', result.error);
        return;
      }

      // Handle the redirect URL
      if (result.url) {
        // Works for both web and native
        Linking.openURL(result.url);
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
    } finally {
      setLoading(false);
    }
  };

  // Calculate discounted price with proper type annotations
  const getPrice = (basePrice: number, cycleIndex: number): string | number => {
    const discount = BILLING_CYCLES[cycleIndex].discount;
    if (discount === 0) return basePrice;
    return (basePrice * (100 - discount) / 100).toFixed(2);
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.card }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
            accessibilityLabel="Go back"
          >
            <Feather name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Choose a Plan</Text>
          <View style={{ width: 40 }} />
        </View>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.content}
          showsVerticalScrollIndicator={false}
        >
          {/* Billing Cycle Toggle */}
          <View style={[styles.cycleToggle, { backgroundColor: colors.card, borderColor: colors.border }]}>
            {BILLING_CYCLES.map((cycle, index) => (
              <TouchableOpacity
                key={cycle.id}
                style={[
                  styles.cycleOption,
                  selectedCycle === index && { 
                    backgroundColor: colors.primary,
                  }
                ]}
                onPress={() => setSelectedCycle(index)}
              >
                <Text style={[
                  styles.cycleText,
                  { color: selectedCycle === index ? 'white' : colors.text }
                ]}>
                  {cycle.name}
                  {cycle.discount > 0 && ` (Save ${cycle.discount}%)`}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Plan Cards */}
          {PRODUCTS.map((product, index) => (
            <TouchableOpacity
              key={product.priceId}
              style={[
                styles.planCard,
                {
                  backgroundColor: colors.card,
                  borderColor: selectedPlan === index ? colors.primary : colors.border,
                }
              ]}
              onPress={() => setSelectedPlan(index)}
            >
              {selectedPlan === index && (
                <View style={[styles.selectedBadge, { backgroundColor: colors.primary }]}>
                  <Feather name="check" size={16}  color={colors.text} />
                </View>
              )}
              
              <Text style={[styles.planName, { color: colors.text }]}>
                {product.name}
              </Text>
              
              <View style={styles.priceContainer}>
                <Text style={[styles.currency, { color: colors.textSecondary }]}>$</Text>
                <Text style={[styles.price, { color: colors.text }]}>
                  {getPrice(product.price, selectedCycle)}
                </Text>
                <Text style={[styles.pricePeriod, { color: colors.textSecondary }]}>
                  /{BILLING_CYCLES[selectedCycle].id.replace('ly', '')}
                </Text>
              </View>
              
              <Text style={[styles.planDescription, { color: colors.textSecondary }]}>
                {product.description}
              </Text>
              
              <View style={styles.featuresContainer}>
                {getFeaturesForPlan(product.name).map((feature, idx) => (
                  <View key={idx} style={styles.featureItem}>
                    {feature.included ? (
                      <Feather name="check" size={16} color={colors.primary} style={styles.featureIcon} />
                    ) : (
                      <Feather name="x" size={16} color={colors.textSecondary} style={styles.featureIcon} />
                    )}
                    <Text style={[
                      styles.featureText,
                      { color: feature.included ? colors.text : colors.textSecondary }
                    ]}>
                      {feature.text}
                    </Text>
                  </View>
                ))}
              </View>
            </TouchableOpacity>
          ))}

          {/* Subscribe Button */}
          <TouchableOpacity
            style={[styles.subscribeButton, { backgroundColor: colors.primary }]}
            onPress={handleSubscribe}
            disabled={loading}
          >
            <Feather name="credit-card" size={18} style={styles.buttonIcon} />
            <Text style={styles.subscribeButtonText}>
              {loading ? 'Processing...' : 'Subscribe Now'}
            </Text>
          </TouchableOpacity>

          {/* Terms and Conditions */}
          <Text style={[styles.termsText, { color: colors.textSecondary }]}>
            By subscribing, you agree to our Terms of Service and Privacy Policy.
            You can cancel your subscription anytime from your account settings.
          </Text>
        </ScrollView>
      </View>
    </>
  );
}

// Helper function to get features for each plan
function getFeaturesForPlan(planName: string): FeatureItem[] {
  const basicFeatures: FeatureItem[] = [
    { text: 'Nutrition tracking', included: true },
    { text: 'Meal logging', included: true },
    { text: 'Basic analytics', included: true },
    { text: 'AI meal recommendations', included: false },
    { text: 'Advanced analytics', included: false },
    { text: 'Priority support', included: false },
  ];

  const premiumFeatures: FeatureItem[] = [
    { text: 'Nutrition tracking', included: true },
    { text: 'Meal logging', included: true },
    { text: 'Basic analytics', included: true },
    { text: 'AI meal recommendations', included: true },
    { text: 'Advanced analytics', included: true },
    { text: 'Priority support', included: false },
  ];

  const familyFeatures: FeatureItem[] = [
    { text: 'Nutrition tracking', included: true },
    { text: 'Meal logging', included: true },
    { text: 'Basic analytics', included: true },
    { text: 'AI meal recommendations', included: true },
    { text: 'Advanced analytics', included: true },
    { text: 'Priority support', included: true },
    { text: 'Up to 5 family accounts', included: true },
  ];

  if (planName.includes('Premium')) return premiumFeatures;
  if (planName.includes('Family')) return familyFeatures;
  return basicFeatures;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingBottom: 40,
  },
  cycleToggle: {
    flexDirection: 'row',
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
    marginBottom: 20,
  },
  cycleOption: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cycleText: {
    fontSize: 14,
    fontWeight: '600',
  },
  planCard: {
    borderRadius: 16,
    borderWidth: 2,
    padding: 20,
    marginBottom: 16,
    position: 'relative',
  },
  selectedBadge: {
    position: 'absolute',
    top: -12,
    right: 20,
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  planName: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 8,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 12,
  },
  currency: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 2,
  },
  price: {
    fontSize: 32,
    fontWeight: '800',
  },
  pricePeriod: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
    marginLeft: 2,
  },
  planDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  featuresContainer: {
    marginTop: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  featureIcon: {
    marginRight: 10,
  },
  featureText: {
    fontSize: 14,
  },
  subscribeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    paddingVertical: 16,
    marginTop: 8,
    marginBottom: 20,
  },
  buttonIcon: {
    marginRight: 8,
  },
  subscribeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
  },
  termsText: {
    fontSize: 12,
    textAlign: 'center',
    paddingHorizontal: 20,
    lineHeight: 18,
  },
}); 