import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator, Alert, Platform } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  getConnectedWearables, 
  connectWearable, 
  disconnectWearable, 
  WearablePlatform, 
  ConnectionStatus, 
  WearableConnection, 
  getDeviceInfo 
} from '@/services/wearableService';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function ConnectDevicesScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [connectingPlatform, setConnectingPlatform] = useState<WearablePlatform | null>(null);
  const [connections, setConnections] = useState<WearableConnection[]>([]);
  
  useEffect(() => {
    loadConnections();
  }, []);
  
  const loadConnections = async (refresh = false) => {
    try {
      if (refresh) {
        setRefreshing(true);
      } else if (!refreshing) {
        setLoading(true);
      }
      
      const wearables = await getConnectedWearables();
      
      // Get device info for connected wearables
      for (const wearable of wearables) {
        if (wearable.status === ConnectionStatus.CONNECTED && !wearable.deviceInfo) {
          const deviceInfo = await getDeviceInfo(wearable.platform);
          if (deviceInfo) {
            wearable.deviceInfo = deviceInfo;
          }
        }
      }
      
      setConnections(wearables);
    } catch (error) {
      console.error('Error loading connections:', error);
      Alert.alert('Error', 'Failed to load connected devices. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  const handleConnectDevice = async (platform: WearablePlatform) => {
    try {
      // Check platform compatibility
      if (
        (platform === WearablePlatform.APPLE_HEALTH && Platform.OS !== 'ios') ||
        ((platform === WearablePlatform.GOOGLE_FIT || platform === WearablePlatform.SAMSUNG_HEALTH) && 
         Platform.OS !== 'android')
      ) {
        Alert.alert(
          'Platform Not Supported',
          `${getPlatformName(platform)} is not supported on this device.`
        );
        return;
      }
      
      setConnectingPlatform(platform);
      
      const connection = await connectWearable(platform);
      
      if (connection) {
        // Refresh connections list
        await loadConnections(true);
        Alert.alert(
          'Connection Successful',
          `Successfully connected to ${getPlatformName(platform)}.`
        );
      } else {
        Alert.alert(
          'Connection Failed',
          `Failed to connect to ${getPlatformName(platform)}. Please try again.`
        );
      }
    } catch (error) {
      console.error(`Error connecting to ${platform}:`, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      Alert.alert('Error', `Failed to connect to ${getPlatformName(platform)}: ${errorMessage}`);
    } finally {
      setConnectingPlatform(null);
    }
  };
  
  const handleDisconnectDevice = async (platform: WearablePlatform) => {
    try {
      Alert.alert(
        'Disconnect Device',
        `Are you sure you want to disconnect from ${getPlatformName(platform)}?`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Disconnect',
            style: 'destructive',
            onPress: async () => {
              setConnectingPlatform(platform);
              
              const success = await disconnectWearable(platform);
              
              if (success) {
                // Refresh connections list
                await loadConnections(true);
                Alert.alert(
                  'Disconnection Successful',
                  `Successfully disconnected from ${getPlatformName(platform)}.`
                );
              } else {
                Alert.alert(
                  'Disconnection Failed',
                  `Failed to disconnect from ${getPlatformName(platform)}. Please try again.`
                );
              }
              
              setConnectingPlatform(null);
            },
          },
        ]
      );
    } catch (error) {
      console.error(`Error disconnecting from ${platform}:`, error);
      Alert.alert('Error', `Failed to disconnect from ${getPlatformName(platform)}.`);
      setConnectingPlatform(null);
    }
  };
  
  const getPlatformName = (platform: WearablePlatform): string => {
    switch (platform) {
      case WearablePlatform.FITBIT:
        return 'Fitbit';
      case WearablePlatform.GARMIN:
        return 'Garmin';
      case WearablePlatform.APPLE_HEALTH:
        return 'Apple Health';
      case WearablePlatform.GOOGLE_FIT:
        return 'Google Fit';
      case WearablePlatform.SAMSUNG_HEALTH:
        return 'Samsung Health';
      default:
        return platform;
    }
  };
  
  const getPlatformColor = (platform: WearablePlatform): string => {
    switch (platform) {
      case WearablePlatform.FITBIT:
        return '#00B0B9';
      case WearablePlatform.GARMIN:
        return '#007CC3';
      case WearablePlatform.APPLE_HEALTH:
        return '#FF2D55';
      case WearablePlatform.GOOGLE_FIT:
        return '#4285F4';
      case WearablePlatform.SAMSUNG_HEALTH:
        return '#1CA0E3';
      default:
        return '#8B5CF6';
    }
  };
  
  const renderConnectionStatus = (connection: WearableConnection) => {
    const isConnecting = connectingPlatform === connection.platform;
    
    switch (connection.status) {
      case ConnectionStatus.CONNECTED:
        return (
          <View style={[styles.statusBadge, { backgroundColor: '#10B981' }]}>
            {isConnecting ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <Feather name="check" size={12} style={styles.statusIcon} />
                <Text style={styles.statusText}>Connected</Text>
              </>
            )}
          </View>
        );
      case ConnectionStatus.CONNECTING:
        return (
          <View style={[styles.statusBadge, { backgroundColor: '#F59E0B' }]}>
            <ActivityIndicator size="small" color="#fff" />
            <Text style={styles.statusText}>Connecting</Text>
          </View>
        );
      case ConnectionStatus.DISCONNECTED:
        return (
          <View style={[styles.statusBadge, { backgroundColor: '#6B7280' }]}>
            {isConnecting ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <Feather name="wifi-off" size={12} style={styles.statusIcon} />
                <Text style={styles.statusText}>Disconnected</Text>
              </>
            )}
          </View>
        );
      case ConnectionStatus.ERROR:
        return (
          <View style={[styles.statusBadge, { backgroundColor: '#EF4444' }]}>
            {isConnecting ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <Feather name="x" size={12} style={styles.statusIcon} />
                <Text style={styles.statusText}>Error</Text>
              </>
            )}
          </View>
        );
      default:
        return null;
    }
  };
  
  const renderConnectedDevice = (connection: WearableConnection) => {
    const isConnected = connection.status === ConnectionStatus.CONNECTED;
    const platformColor = getPlatformColor(connection.platform);
    const platformName = getPlatformName(connection.platform);
    
    return (
      <View 
        key={connection.id} 
        style={[
          styles.deviceCard, 
          { 
            backgroundColor: isDark ? colors.card : '#fff',
            borderColor: isConnected ? platformColor + '80' : colors.border
          }
        ]}
      >
        <View style={styles.deviceHeader}>
          <View style={styles.deviceInfo}>
            <View 
              style={[
                styles.platformIcon, 
                { backgroundColor: platformColor + '20', borderColor: platformColor + '40' }
              ]}
            >
              <Text style={[styles.platformIconText, { color: platformColor }]}>
                {platformName.substring(0, 1)}
              </Text>
            </View>
            <View>
              <Text style={[styles.deviceName, { color: colors.text }]}>{platformName}</Text>
              {connection.deviceInfo && (
                <Text style={[styles.deviceModel, { color: colors.textSecondary }]}>
                  {connection.deviceInfo.name || connection.deviceInfo.model}
                </Text>
              )}
            </View>
          </View>
          {renderConnectionStatus(connection)}
        </View>
        
        {isConnected && connection.deviceInfo && (
          <View style={styles.deviceDetails}>
            {connection.lastSynced && (
              <View style={styles.detailItem}>
                <Feather name="refresh-cw" size={14} color={colors.textSecondary} style={styles.detailIcon} />
                <Text style={[styles.detailText, { color: colors.textSecondary }]}>
                  Last synced: {new Date(connection.lastSynced).toLocaleString()}
                </Text>
              </View>
            )}
            
            {connection.deviceInfo.batteryLevel !== undefined && (
              <View style={styles.detailItem}>
                <Feather name="battery" size={14} color={colors.textSecondary} style={styles.detailIcon} />
                <Text style={[styles.detailText, { color: colors.textSecondary }]}>
                  Battery: {connection.deviceInfo.batteryLevel}%
                </Text>
              </View>
            )}
          </View>
        )}
        
        <View style={styles.deviceActions}>
          {isConnected ? (
            <TouchableOpacity
              style={[styles.actionButton, { borderColor: colors.border }]}
              onPress={() => handleDisconnectDevice(connection.platform)}
              disabled={connectingPlatform === connection.platform}
            >
              <Feather name="unlink" size={16} color={colors.text} style={styles.actionIcon} />
              <Text style={[styles.actionText, { color: colors.text }]}>Disconnect</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[styles.actionButton, { borderColor: colors.border, backgroundColor: colors.primary + '20' }]}
              onPress={() => handleConnectDevice(connection.platform)}
              disabled={connectingPlatform === connection.platform}
            >
              <Feather name="link" size={16} color={colors.primary} style={styles.actionIcon} />
              <Text style={[styles.actionText, { color: colors.primary }]}>Connect</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[styles.actionButton, { borderColor: colors.border }]}
            onPress={() => router.push({
              pathname: '/wearable/device-data' as any,
              params: { platform: connection.platform }
            })}
            disabled={!isConnected}
          >
            <Feather name="info" size={16} color={isConnected ? colors.text : colors.textSecondary} style={styles.actionIcon} />
            <Text style={[styles.actionText, { color: isConnected ? colors.text : colors.textSecondary }]}>View Data</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  
  const renderAvailableDevices = () => {
    // Get all platforms that aren't already in the connections list
    const availablePlatforms = Object.values(WearablePlatform).filter(
      platform => !connections.some(conn => conn.platform === platform)
    );
    
    if (availablePlatforms.length === 0) {
      return null;
    }
    
    return (
      <View style={styles.sectionContainer}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Add a Device</Text>
        <View style={styles.devicesGrid}>
          {availablePlatforms.map(platform => (
            <TouchableOpacity
              key={platform}
              style={[
                styles.platformCard,
                { 
                  backgroundColor: isDark ? colors.card : '#fff',
                  borderColor: colors.border
                }
              ]}
              onPress={() => handleConnectDevice(platform)}
              disabled={connectingPlatform === platform}
            >
              <View style={[styles.platformIconLarge, { backgroundColor: getPlatformColor(platform) + '20' }]}>
                {connectingPlatform === platform ? (
                  <ActivityIndicator size="small" color={getPlatformColor(platform)} />
                ) : (
                  <Text style={[styles.platformIconLargeText, { color: getPlatformColor(platform) }]}>
                    {getPlatformName(platform).substring(0, 1)}
                  </Text>
                )}
              </View>
              <Text style={[styles.platformName, { color: colors.text }]}>{getPlatformName(platform)}</Text>
              <Text style={[styles.platformDescription, { color: colors.textSecondary }]}>
                {platform === WearablePlatform.APPLE_HEALTH && Platform.OS === 'ios' && 'Connect to Apple Health'}
                {platform === WearablePlatform.GOOGLE_FIT && Platform.OS === 'android' && 'Connect to Google Fit'}
                {platform === WearablePlatform.SAMSUNG_HEALTH && Platform.OS === 'android' && 'Connect to Samsung Health'}
                {platform === WearablePlatform.FITBIT && 'Connect your Fitbit device'}
                {platform === WearablePlatform.GARMIN && 'Connect your Garmin device'}
              </Text>
              <LinearGradient
                colors={[getPlatformColor(platform) + '20', getPlatformColor(platform) + '40']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.connectButton}
              >
                <Text style={[styles.connectButtonText, { color: getPlatformColor(platform) }]}>Connect</Text>
              </LinearGradient>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <TouchableOpacity 
          onPress={() => router.back()}
          style={[styles.backButton, { backgroundColor: colors.subtle }]}
        >
          <Feather name="arrow-left" size={20} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Connect Devices</Text>
        <TouchableOpacity 
          style={[styles.refreshButton, { backgroundColor: colors.subtle }]}
          onPress={() => loadConnections(true)}
          disabled={refreshing}
        >
          {refreshing ? (
            <ActivityIndicator size="small" color={colors.text} />
          ) : (
            <Feather name="refresh-cw" size={20} color={colors.text} />
          )}
        </TouchableOpacity>
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading connected devices...
          </Text>
        </View>
      ) : (
        <ScrollView 
          style={styles.scrollView} 
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Help text */}
          <View style={[styles.helpBox, { backgroundColor: colors.primary + '10', borderColor: colors.primary + '30' }]}>
            <Text style={[styles.helpText, { color: colors.text }]}>
              Connect your wearable devices to sync fitness and health data. This will help provide more personalized nutrition recommendations.
            </Text>
          </View>
          
          {/* Connected devices section */}
          {connections.length > 0 && (
            <View style={styles.sectionContainer}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Connected Devices</Text>
              {connections.map(renderConnectedDevice)}
            </View>
          )}
          
          {/* Available devices section */}
          {renderAvailableDevices()}
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  helpBox: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 24,
  },
  helpText: {
    fontSize: 14,
    lineHeight: 20,
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  deviceCard: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 12,
  },
  deviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  deviceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  platformIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  platformIconText: {
    fontSize: 16,
    fontWeight: '700',
  },
  deviceName: {
    fontSize: 16,
    fontWeight: '600',
  },
  deviceModel: {
    fontSize: 14,
    marginTop: 2,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusIcon: {
    marginRight: 4,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  deviceDetails: {
    marginBottom: 12,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  detailIcon: {
    marginRight: 6,
  },
  detailText: {
    fontSize: 13,
  },
  deviceActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    flex: 1,
    marginHorizontal: 4,
  },
  actionIcon: {
    marginRight: 6,
  },
  actionText: {
    fontSize: 13,
    fontWeight: '500',
  },
  devicesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  platformCard: {
    width: '48%',
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
  },
  platformIconLarge: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  platformIconLargeText: {
    fontSize: 24,
    fontWeight: '700',
  },
  platformName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    textAlign: 'center',
  },
  platformDescription: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 12,
  },
  connectButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    minWidth: '80%',
    alignItems: 'center',
  },
  connectButtonText: {
    fontWeight: '600',
  },
}); 