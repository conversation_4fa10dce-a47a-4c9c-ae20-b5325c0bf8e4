import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator, Alert, Dimensions, Platform } from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LineChart } from 'react-native-chart-kit';
import { 
  syncHealthData, 
  getHealthMetrics,
  getDeviceInfo,
  WearablePlatform, 
  HealthMetricType, 
  HealthMetric 
} from '@/services/wearableService';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// Date formatting helper
const formatDate = (date: Date): string => {
  return date.toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric' 
  });
};

// Format value with units helper
const formatValueWithUnits = (value: number, type: HealthMetricType): string => {
  switch (type) {
    case HealthMetricType.STEPS:
      return `${value.toLocaleString()} steps`;
    case HealthMetricType.HEART_RATE:
      return `${value} bpm`;
    case HealthMetricType.SLEEP:
      return `${value} hours`;
    case HealthMetricType.CALORIES_BURNED:
      return `${value} kcal`;
    case HealthMetricType.ACTIVE_MINUTES:
      return `${value} min`;
    case HealthMetricType.DISTANCE:
      return `${value.toFixed(2)} km`;
    case HealthMetricType.WEIGHT:
      return `${value.toFixed(1)} kg`;
    default:
      return `${value}`;
  }
};

export default function DeviceDataScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const params = useLocalSearchParams();
  
  // Get platform from URL params
  const platformParam = params.platform as string;
  const platform = platformParam as WearablePlatform;
  
  // States
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState<any>(null);
  const [dataRange, setDataRange] = useState<'day' | 'week' | 'month'>('week');
  const [activeMetricType, setActiveMetricType] = useState<HealthMetricType>(HealthMetricType.STEPS);
  const [metricsData, setMetricsData] = useState<{[key in HealthMetricType]?: HealthMetric[]}>({});
  
  // Load data on mount
  useEffect(() => {
    if (platform) {
      loadData();
    }
  }, [platform]);
  
  // Load data when data range changes
  useEffect(() => {
    if (platform) {
      loadMetricsData();
    }
  }, [dataRange, platform]);
  
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load device info
      const info = await getDeviceInfo(platform);
      setDeviceInfo(info);
      
      // Load metrics data
      await loadMetricsData();
    } catch (error) {
      console.error('Error loading data:', error);
      Alert.alert('Error', 'Failed to load device data. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  const loadMetricsData = async () => {
    try {
      // Define date range
      const endDate = new Date();
      const startDate = new Date(endDate);
      
      switch (dataRange) {
        case 'day':
          startDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1);
          break;
      }
      
      // Fetch all metric types
      const metricTypes = Object.values(HealthMetricType);
      const newMetricsData: {[key in HealthMetricType]?: HealthMetric[]} = {};
      
      for (const type of metricTypes) {
        const metrics = await getHealthMetrics(
          type, 
          startDate.toISOString(), 
          endDate.toISOString()
        );
        
        if (metrics.length > 0) {
          newMetricsData[type] = metrics;
        }
      }
      
      setMetricsData(newMetricsData);
      
      // Set active metric type to the first one with data
      if (Object.keys(newMetricsData).length > 0) {
        const availableMetrics = Object.keys(newMetricsData) as HealthMetricType[];
        if (!newMetricsData[activeMetricType] && availableMetrics.length > 0) {
          setActiveMetricType(availableMetrics[0]);
        }
      }
    } catch (error) {
      console.error('Error loading metrics data:', error);
      Alert.alert('Error', 'Failed to load metrics data. Please try again.');
    }
  };
  
  const handleSyncData = async () => {
    try {
      setSyncing(true);
      
      // Sync all metric types
      await syncHealthData(platform);
      
      // Reload metrics data
      await loadMetricsData();
      
      Alert.alert('Sync Complete', 'Successfully synced data from your device.');
    } catch (error) {
      console.error('Error syncing data:', error);
      Alert.alert('Sync Failed', 'Failed to sync data from your device. Please try again.');
    } finally {
      setSyncing(false);
    }
  };
  
  const getMetricIcon = (type: HealthMetricType, size: number = 20, color: string = colors.text) => {
    switch (type) {
      case HealthMetricType.STEPS:
        return <MaterialIcons name="directions-walk" color={color} />;
      case HealthMetricType.HEART_RATE:
        return <Feather name="heart" color={color} />;
      case HealthMetricType.SLEEP:
        return <Feather name="moon" color={color} />;
      case HealthMetricType.CALORIES_BURNED:
        return <MaterialIcons name="local-fire-department" color={color} />;
      case HealthMetricType.ACTIVE_MINUTES:
        return <Feather name="clock" color={color} />;
      case HealthMetricType.DISTANCE:
        return <MaterialIcons name="straighten" color={color} />;
      case HealthMetricType.WEIGHT:
        return <MaterialIcons name="fitness-center" color={color} />;
      default:
        return <Feather name="activity" color={color} />;
    }
  };
  
  const getMetricName = (type: HealthMetricType): string => {
    switch (type) {
      case HealthMetricType.STEPS:
        return 'Steps';
      case HealthMetricType.HEART_RATE:
        return 'Heart Rate';
      case HealthMetricType.SLEEP:
        return 'Sleep';
      case HealthMetricType.CALORIES_BURNED:
        return 'Calories Burned';
      case HealthMetricType.ACTIVE_MINUTES:
        return 'Active Minutes';
      case HealthMetricType.DISTANCE:
        return 'Distance';
      case HealthMetricType.WEIGHT:
        return 'Weight';
      default:
        return type;
    }
  };
  
  const getMetricColor = (type: HealthMetricType): string => {
    switch (type) {
      case HealthMetricType.STEPS:
        return '#3B82F6';
      case HealthMetricType.HEART_RATE:
        return '#EF4444';
      case HealthMetricType.SLEEP:
        return '#8B5CF6';
      case HealthMetricType.CALORIES_BURNED:
        return '#F59E0B';
      case HealthMetricType.ACTIVE_MINUTES:
        return '#10B981';
      case HealthMetricType.DISTANCE:
        return '#6366F1';
      case HealthMetricType.WEIGHT:
        return '#EC4899';
      default:
        return '#6B7280';
    }
  };
  
  const getPlatformName = (platformValue: WearablePlatform): string => {
    switch (platformValue) {
      case WearablePlatform.FITBIT:
        return 'Fitbit';
      case WearablePlatform.GARMIN:
        return 'Garmin';
      case WearablePlatform.APPLE_HEALTH:
        return 'Apple Health';
      case WearablePlatform.GOOGLE_FIT:
        return 'Google Fit';
      case WearablePlatform.SAMSUNG_HEALTH:
        return 'Samsung Health';
      default:
        return platformValue;
    }
  };
  
  const formatChartData = () => {
    const metrics = metricsData[activeMetricType] || [];
    
    if (metrics.length === 0) {
      return {
        labels: [],
        datasets: [{ data: [] }],
      };
    }
    
    // Sort by timestamp ascending
    const sortedMetrics = [...metrics].sort(
      (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
    
    // Group by day for week/month view
    let groupedData: { [key: string]: number[] } = {};
    
    if (dataRange === 'day') {
      // For day view, use hourly data points
      sortedMetrics.forEach(metric => {
        const date = new Date(metric.timestamp);
        const hour = date.getHours();
        const key = `${hour}:00`;
        
        if (!groupedData[key]) {
          groupedData[key] = [];
        }
        
        groupedData[key].push(metric.value);
      });
    } else {
      // For week/month view, group by day
      sortedMetrics.forEach(metric => {
        const date = new Date(metric.timestamp);
        const key = formatDate(date);
        
        if (!groupedData[key]) {
          groupedData[key] = [];
        }
        
        groupedData[key].push(metric.value);
      });
    }
    
    // Calculate average for each group
    const labels: string[] = [];
    const values: number[] = [];
    
    Object.entries(groupedData).forEach(([key, vals]) => {
      if (vals.length > 0) {
        const average = vals.reduce((sum, val) => sum + val, 0) / vals.length;
        labels.push(key);
        values.push(average);
      }
    });
    
    // Limit to last 7 data points for better visualization
    const maxDataPoints = 7;
    if (labels.length > maxDataPoints) {
      labels.splice(0, labels.length - maxDataPoints);
      values.splice(0, values.length - maxDataPoints);
    }
    
    return {
      labels,
      datasets: [{ data: values }],
    };
  };
  
  const renderMetricSelector = () => {
    const availableMetrics = Object.keys(metricsData) as HealthMetricType[];
    
    if (availableMetrics.length === 0) {
      return null;
    }
    
    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.metricSelectorContainer}
        contentContainerStyle={styles.metricSelectorContent}
      >
        {availableMetrics.map(type => (
          <TouchableOpacity
            key={type}
            style={[
              styles.metricButton,
              activeMetricType === type && { 
                backgroundColor: getMetricColor(type) + '20',
                borderColor: getMetricColor(type)
              },
              activeMetricType !== type && { borderColor: colors.border }
            ]}
            onPress={() => setActiveMetricType(type)}
          >
            <View style={[
              styles.metricIcon,
              { backgroundColor: getMetricColor(type) + '20' }
            ]}>
              {getMetricIcon(type, 16, getMetricColor(type))}
            </View>
            <Text 
              style={[
                styles.metricButtonText, 
                { color: activeMetricType === type ? getMetricColor(type) : colors.textSecondary }
              ]}
            >
              {getMetricName(type)}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };
  
  const renderChart = () => {
    const chartData = formatChartData();
    const metrics = metricsData[activeMetricType] || [];
    
    if (metrics.length === 0) {
      return (
        <View style={[styles.noDataContainer, { borderColor: colors.border }]}>
          <Text style={[styles.noDataText, { color: colors.textSecondary }]}>
            No {getMetricName(activeMetricType).toLowerCase()} data available for the selected time range.
          </Text>
          <TouchableOpacity
            style={[styles.syncButton, { backgroundColor: colors.primary }]}
            onPress={handleSyncData}
            disabled={syncing}
          >
            {syncing ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <>
                <Feather name="refresh-cw" size={14} style={styles.syncButtonIcon} />
                <Text style={styles.syncButtonText}>Sync Data</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      );
    }
    
    return (
      <View style={styles.chartContainer}>
        <LineChart
          data={chartData}
          width={SCREEN_WIDTH - 40}
          height={220}
          chartConfig={{
            backgroundColor: 'transparent',
            backgroundGradientFrom: isDark ? colors.card : 'white',
            backgroundGradientTo: isDark ? colors.card : 'white',
            decimalPlaces: 0,
            color: (opacity = 1) => getMetricColor(activeMetricType) + (opacity * 255).toString(16).padStart(2, '0'),
            labelColor: (opacity = 1) => colors.textSecondary,
            style: {
              borderRadius: 16,
            },
            propsForDots: {
              r: "5",
              strokeWidth: "1",
              stroke: getMetricColor(activeMetricType),
            },
          }}
          bezier
          style={{
            borderRadius: 16,
            paddingRight: 0,
            paddingVertical: 8,
          }}
        />
        
        {/* Chart Summary */}
        <View style={styles.chartSummary}>
          {metrics.length > 0 && (
            <>
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Latest</Text>
                <Text style={[styles.summaryValue, { color: colors.text }]}>
                  {formatValueWithUnits(metrics[0].value, activeMetricType)}
                </Text>
              </View>
              
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Average</Text>
                <Text style={[styles.summaryValue, { color: colors.text }]}>
                  {formatValueWithUnits(
                    metrics.reduce((sum, metric) => sum + metric.value, 0) / metrics.length,
                    activeMetricType
                  )}
                </Text>
              </View>
              
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>Max</Text>
                <Text style={[styles.summaryValue, { color: colors.text }]}>
                  {formatValueWithUnits(
                    Math.max(...metrics.map(metric => metric.value)),
                    activeMetricType
                  )}
                </Text>
              </View>
            </>
          )}
        </View>
      </View>
    );
  };
  
  const renderTimeRangeSelector = () => {
    return (
      <View style={styles.timeRangeSelector}>
        <TouchableOpacity
          style={[
            styles.timeRangeButton,
            dataRange === 'day' && styles.activeTimeRangeButton,
            dataRange === 'day' && { backgroundColor: colors.primary },
            dataRange !== 'day' && { borderColor: colors.border }
          ]}
          onPress={() => setDataRange('day')}
        >
          <Text style={[
            styles.timeRangeButtonText, 
            { color: dataRange === 'day' ? 'white' : colors.textSecondary }
          ]}>
            Day
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.timeRangeButton,
            dataRange === 'week' && styles.activeTimeRangeButton,
            dataRange === 'week' && { backgroundColor: colors.primary },
            dataRange !== 'week' && { borderColor: colors.border }
          ]}
          onPress={() => setDataRange('week')}
        >
          <Text style={[
            styles.timeRangeButtonText, 
            { color: dataRange === 'week' ? 'white' : colors.textSecondary }
          ]}>
            Week
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.timeRangeButton,
            dataRange === 'month' && styles.activeTimeRangeButton,
            dataRange === 'month' && { backgroundColor: colors.primary },
            dataRange !== 'month' && { borderColor: colors.border }
          ]}
          onPress={() => setDataRange('month')}
        >
          <Text style={[
            styles.timeRangeButtonText, 
            { color: dataRange === 'month' ? 'white' : colors.textSecondary }
          ]}>
            Month
          </Text>
        </TouchableOpacity>
      </View>
    );
  };
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <TouchableOpacity 
          onPress={() => router.back()}
          style={[styles.backButton, { backgroundColor: colors.subtle }]}
        >
          <Feather name="arrow-left" size={20} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          {platform ? getPlatformName(platform) : 'Device'} Data
        </Text>
        <TouchableOpacity 
          style={[styles.syncButton, { backgroundColor: colors.primary }]}
          onPress={handleSyncData}
          disabled={syncing}
        >
          {syncing ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <>
              <Feather name="refresh-cw" size={14} style={styles.syncButtonIcon} />
              <Text style={styles.syncButtonText}>Sync</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading health data...
          </Text>
        </View>
      ) : (
        <ScrollView 
          style={styles.scrollView} 
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Device Info */}
          {deviceInfo && (
            <View style={[styles.deviceInfoCard, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
              <Text style={[styles.deviceName, { color: colors.text }]}>
                {deviceInfo.name || getPlatformName(platform)}
              </Text>
              {deviceInfo.model && (
                <Text style={[styles.deviceModel, { color: colors.textSecondary }]}>
                  {deviceInfo.model}
                </Text>
              )}
              {deviceInfo.batteryLevel !== undefined && (
                <View style={styles.batteryContainer}>
                  <View 
                    style={[
                      styles.batteryBar, 
                      { backgroundColor: colors.border }
                    ]}
                  >
                    <View 
                      style={[
                        styles.batteryFill, 
                        { 
                          width: `${deviceInfo.batteryLevel}%`,
                          backgroundColor: deviceInfo.batteryLevel > 20 ? '#10B981' : '#EF4444'
                        }
                      ]} 
                    />
                  </View>
                  <Text style={[styles.batteryText, { color: colors.textSecondary }]}>
                    {deviceInfo.batteryLevel}% Battery
                  </Text>
                </View>
              )}
            </View>
          )}
          
          {/* Time Range Selector */}
          {renderTimeRangeSelector()}
          
          {/* Metric Selector */}
          {renderMetricSelector()}
          
          {/* Chart */}
          {renderChart()}
          
          {/* Health Insights */}
          <View style={[styles.insightsCard, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
            <Text style={[styles.insightsTitle, { color: colors.text }]}>Health Insights</Text>
            <Text style={[styles.insightsText, { color: colors.textSecondary }]}>
              Your health data from {getPlatformName(platform)} helps us provide more personalized nutrition recommendations.
              We use this information to adjust your calorie goals, suggest meal timing, and recommend specific nutrients.
            </Text>
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  syncButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
  },
  syncButtonIcon: {
    marginRight: 6,
  },
  syncButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  deviceInfoCard: {
    borderRadius: 16,
    borderWidth: 1,
    padding: 16,
    marginBottom: 16,
  },
  deviceName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  deviceModel: {
    fontSize: 14,
    marginBottom: 12,
  },
  batteryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  batteryBar: {
    height: 8,
    width: 100,
    borderRadius: 4,
    marginRight: 10,
    overflow: 'hidden',
  },
  batteryFill: {
    height: '100%',
    borderRadius: 4,
  },
  batteryText: {
    fontSize: 14,
  },
  timeRangeSelector: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  timeRangeButton: {
    flex: 1,
    paddingVertical: 10,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  timeRangeButtonText: {
    fontWeight: '600',
    fontSize: 14,
  },
  activeTimeRangeButton: {
    borderWidth: 0,
  },
  metricSelectorContainer: {
    marginBottom: 16,
  },
  metricSelectorContent: {
    paddingRight: 16,
  },
  metricButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
  },
  metricIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 6,
  },
  metricButtonText: {
    fontWeight: '500',
    fontSize: 13,
  },
  chartContainer: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 16,
    backgroundColor: 'white',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  chartSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  noDataContainer: {
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    marginBottom: 16,
  },
  noDataText: {
    textAlign: 'center',
    marginBottom: 16,
    fontSize: 14,
    lineHeight: 20,
  },
  insightsCard: {
    borderRadius: 16,
    borderWidth: 1,
    padding: 16,
  },
  insightsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  insightsText: {
    fontSize: 14,
    lineHeight: 20,
  },
}); 