import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator, Image } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { SafeAreaView } from 'react-native-safe-area-context';
import { 
  getConnectedWearables, 
  getHealthMetrics,
  WearablePlatform, 
  ConnectionStatus, 
  HealthMetricType 
} from '@/services/wearableService';

export default function WearableDashboardScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  
  const [loading, setLoading] = useState(true);
  const [connectedDevices, setConnectedDevices] = useState(0);
  const [hasStepsData, setHasStepsData] = useState(false);
  const [hasHeartRateData, setHasHeartRateData] = useState(false);
  const [hasSleepData, setHasSleepData] = useState(false);
  const [latestSteps, setLatestSteps] = useState<number | null>(null);
  
  useEffect(() => {
    loadData();
  }, []);
  
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Get connected devices
      const wearables = await getConnectedWearables();
      const connected = wearables.filter(w => w.status === ConnectionStatus.CONNECTED).length;
      setConnectedDevices(connected);
      
      // Check if we have different types of data
      if (connected > 0) {
        // Get today's date
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        // Check steps data
        const stepsData = await getHealthMetrics(
          HealthMetricType.STEPS, 
          today.toISOString()
        );
        setHasStepsData(stepsData.length > 0);
        
        if (stepsData.length > 0) {
          // Get the most recent steps data
          const latestStepsData = stepsData.sort(
            (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
          )[0];
          setLatestSteps(latestStepsData.value);
        }
        
        // Check heart rate data
        const heartRateData = await getHealthMetrics(
          HealthMetricType.HEART_RATE, 
          today.toISOString()
        );
        setHasHeartRateData(heartRateData.length > 0);
        
        // Check sleep data
        const sleepData = await getHealthMetrics(
          HealthMetricType.SLEEP, 
          today.toISOString()
        );
        setHasSleepData(sleepData.length > 0);
      }
    } catch (error) {
      console.error('Error loading wearable data:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleRefresh = () => {
    loadData();
  };
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <TouchableOpacity 
          onPress={() => router.back()}
          style={[styles.backButton, { backgroundColor: colors.subtle }]}
        >
          <Feather name="arrow-left" size={20} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Wearable Devices</Text>
        <TouchableOpacity 
          style={[styles.refreshButton, { backgroundColor: colors.subtle }]}
          onPress={handleRefresh}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color={colors.text} />
          ) : (
            <Feather name="refresh-cw" size={20} color={colors.text} />
          )}
        </TouchableOpacity>
      </View>
      
      <ScrollView 
        style={styles.scrollView} 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Connected devices summary */}
        <View style={[styles.summaryCard, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
          <View style={styles.summaryHeader}>
            <MaterialIcons name="watch" size={24} color={colors.primary} />
            <Text style={[styles.summaryTitle, { color: colors.text }]}>Connected Devices</Text>
          </View>
          
          <Text style={[styles.summaryCount, { color: colors.text }]}>
            {loading ? '...' : connectedDevices}
          </Text>
          
          <TouchableOpacity 
            style={[styles.summaryButton, { backgroundColor: colors.primary }]}
            onPress={() => router.push('/wearable/connect-devices' as any)}
          >
            <Feather name="plus" size={16} style={styles.summaryButtonIcon} />
            <Text style={styles.summaryButtonText}>
              {connectedDevices > 0 ? 'Manage Devices' : 'Connect a Device'}
            </Text>
          </TouchableOpacity>
        </View>
        
        {/* Health metrics grid */}
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Health Metrics</Text>
        <View style={styles.metricsGrid}>
          <TouchableOpacity 
            style={[
              styles.metricCard, 
              { 
                backgroundColor: isDark ? colors.card : 'white',
                borderColor: hasStepsData ? '#3B82F6' : colors.border,
                opacity: hasStepsData ? 1 : 0.7
              }
            ]}
            onPress={() => {
              if (connectedDevices > 0) {
                router.push('/wearable/device-data?platform=fitbit' as any);
              } else {
                router.push('/wearable/connect-devices' as any);
              }
            }}
          >
            <View style={[styles.metricIconContainer, { backgroundColor: '#3B82F620' }]}>
              <MaterialIcons name="directions-walk" size={24}  color={colors.text} />
            </View>
            <Text style={[styles.metricName, { color: colors.text }]}>Steps</Text>
            {latestSteps !== null ? (
              <Text style={[styles.metricValue, { color: '#3B82F6' }]}>
                {latestSteps.toLocaleString()}
              </Text>
            ) : (
              <Text style={[styles.metricEmptyText, { color: colors.textSecondary }]}>
                {connectedDevices > 0 ? 'No data yet' : 'Connect a device'}
              </Text>
            )}
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[
              styles.metricCard, 
              { 
                backgroundColor: isDark ? colors.card : 'white',
                borderColor: hasHeartRateData ? '#EF4444' : colors.border,
                opacity: hasHeartRateData ? 1 : 0.7
              }
            ]}
            onPress={() => {
              if (connectedDevices > 0) {
                router.push('/wearable/device-data?platform=fitbit' as any);
              } else {
                router.push('/wearable/connect-devices' as any);
              }
            }}
          >
            <View style={[styles.metricIconContainer, { backgroundColor: '#EF444420' }]}>
              <Feather name="heart" size={24}  color={colors.text} />
            </View>
            <Text style={[styles.metricName, { color: colors.text }]}>Heart Rate</Text>
            <Text style={[styles.metricEmptyText, { color: colors.textSecondary }]}>
              {hasHeartRateData ? 'View data' : connectedDevices > 0 ? 'No data yet' : 'Connect a device'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[
              styles.metricCard, 
              { 
                backgroundColor: isDark ? colors.card : 'white',
                borderColor: hasSleepData ? '#8B5CF6' : colors.border,
                opacity: hasSleepData ? 1 : 0.7
              }
            ]}
            onPress={() => {
              if (connectedDevices > 0) {
                router.push('/wearable/device-data?platform=fitbit' as any);
              } else {
                router.push('/wearable/connect-devices' as any);
              }
            }}
          >
            <View style={[styles.metricIconContainer, { backgroundColor: '#8B5CF620' }]}>
              <Feather name="moon" size={24}  color={colors.text} />
            </View>
            <Text style={[styles.metricName, { color: colors.text }]}>Sleep</Text>
            <Text style={[styles.metricEmptyText, { color: colors.textSecondary }]}>
              {hasSleepData ? 'View data' : connectedDevices > 0 ? 'No data yet' : 'Connect a device'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[
              styles.metricCard, 
              { 
                backgroundColor: isDark ? colors.card : 'white',
                borderColor: colors.border,
                opacity: 0.7
              }
            ]}
            onPress={() => {
              if (connectedDevices > 0) {
                router.push('/wearable/device-data?platform=fitbit' as any);
              } else {
                router.push('/wearable/connect-devices' as any);
              }
            }}
          >
            <View style={[styles.metricIconContainer, { backgroundColor: '#F59E0B20' }]}>
              <Feather name="activity" size={24}  color={colors.text} />
            </View>
            <Text style={[styles.metricName, { color: colors.text }]}>Activity</Text>
            <Text style={[styles.metricEmptyText, { color: colors.textSecondary }]}>
              {connectedDevices > 0 ? 'No data yet' : 'Connect a device'}
            </Text>
          </TouchableOpacity>
        </View>
        
        {/* Integration benefits */}
        <View style={[styles.benefitsCard, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
          <Text style={[styles.benefitsTitle, { color: colors.text }]}>
            Benefits of Wearable Integration
          </Text>
          <View style={styles.benefitRow}>
            <View style={[styles.benefitIcon, { backgroundColor: colors.primary + '20' }]}>
              <Text style={[styles.benefitIconText, { color: colors.primary }]}>1</Text>
            </View>
            <View style={styles.benefitContent}>
              <Text style={[styles.benefitName, { color: colors.text }]}>Personalized Nutrition</Text>
              <Text style={[styles.benefitDescription, { color: colors.textSecondary }]}>
                Get meal recommendations based on your activity level and health metrics.
              </Text>
            </View>
          </View>
          
          <View style={styles.benefitRow}>
            <View style={[styles.benefitIcon, { backgroundColor: colors.primary + '20' }]}>
              <Text style={[styles.benefitIconText, { color: colors.primary }]}>2</Text>
            </View>
            <View style={styles.benefitContent}>
              <Text style={[styles.benefitName, { color: colors.text }]}>Calorie Adjustments</Text>
              <Text style={[styles.benefitDescription, { color: colors.textSecondary }]}>
                Automatically adjust your daily calorie goals based on your activity.
              </Text>
            </View>
          </View>
          
          <View style={styles.benefitRow}>
            <View style={[styles.benefitIcon, { backgroundColor: colors.primary + '20' }]}>
              <Text style={[styles.benefitIconText, { color: colors.primary }]}>3</Text>
            </View>
            <View style={styles.benefitContent}>
              <Text style={[styles.benefitName, { color: colors.text }]}>Sleep Insights</Text>
              <Text style={[styles.benefitDescription, { color: colors.textSecondary }]}>
                Optimize your nutrition based on sleep patterns and recovery needs.
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  summaryCard: {
    borderRadius: 16,
    borderWidth: 1,
    padding: 20,
    marginBottom: 24,
    alignItems: 'center',
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  summaryCount: {
    fontSize: 48,
    fontWeight: '700',
    marginBottom: 16,
  },
  summaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
  },
  summaryButtonIcon: {
    marginRight: 8,
  },
  summaryButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  metricCard: {
    width: '48%',
    borderRadius: 16,
    borderWidth: 1,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
  },
  metricIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  metricName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: '700',
  },
  metricEmptyText: {
    fontSize: 13,
    textAlign: 'center',
  },
  benefitsCard: {
    borderRadius: 16,
    borderWidth: 1,
    padding: 20,
  },
  benefitsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  benefitRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  benefitIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  benefitIconText: {
    fontSize: 16,
    fontWeight: '700',
  },
  benefitContent: {
    flex: 1,
  },
  benefitName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  benefitDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
}); 