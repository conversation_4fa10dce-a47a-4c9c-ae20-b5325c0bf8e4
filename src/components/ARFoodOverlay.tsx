import React, { useRef, useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, Animated, Dimensions, Alert } from 'react-native';
import { CameraView, useCameraPermissions } from 'expo-camera';
import * as Haptics from 'expo-haptics';
import { GLView } from 'expo-gl';
import { THREE } from 'expo-three';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/contexts/TranslationContext';
import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Bar } from 'react-native-progress';
import * as Device from 'expo-device';
import Reanimated, { FadeInDown, FadeOutDown } from 'react-native-reanimated';
import { convertShadowToBoxShadow } from '@/utils/styles';

// Add DOM typings for web
declare global {
  interface Window {
    document: Document;
  }
}

// Import Camera type properly
type CameraInstance = React.ComponentType<any> & {
  requestCameraPermissionsAsync: () => Promise<any>;
  Constants: {
    Type: {
      back: any;
      front: any;
    }
  }
};

interface ARFoodOverlayProps {
  onClose: () => void;
  foodData: {
    name: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    items?: {
      name: string;
      calories: number;
      protein?: number;
      carbs?: number;
      fat?: number;
    }[];
  };
}

// RDI (Reference Daily Intake) values
const RDI = {
  calories: 2000,
  protein: 50,   // g
  carbs: 275,    // g
  fat: 78,       // g
  fiber: 28,     // g
};

const ARFoodOverlay: React.FC<ARFoodOverlayProps> = ({ onClose, foodData }) => {
  const { colors, isDark } = useTheme();
  const { t } = useTranslation();
  const cameraRef = useRef<any>(null);
  const [permission, requestPermission] = useCameraPermissions();
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [detected, setDetected] = useState(false);
  const [detectedItems, setDetectedItems] = useState<any[]>([]);
  const [isTracking, setIsTracking] = useState(false);
  const [detectionConfidence, setDetectionConfidence] = useState(0);
  const [showDetailedNutrition, setShowDetailedNutrition] = useState(false);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [deviceSupportsAR, setDeviceSupportsAR] = useState(true);
  const [progressValue, setProgressValue] = useState(0);
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const detectionProgress = useRef(new Animated.Value(0)).current;
  
  // Request camera permission and check device capability
  useEffect(() => {
    (async () => {
      // Check for camera permission
      if (permission?.granted) {
        setHasPermission(true);
      } else {
        const { granted } = await requestPermission();
        setHasPermission(granted);
      }
      
      // Check if device supports AR capabilities
      if (Platform.OS !== 'web') {
        const deviceInfo = await Device.getDeviceTypeAsync();
        // Most modern devices support our basic AR, but we could implement more advanced checks
        // For now, we'll assume all devices except very old ones can handle our AR
        setDeviceSupportsAR(deviceInfo !== Device.DeviceType.UNKNOWN);
      }
      
      // Start intro animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true
        })
      ]).start();
    })();
    
    // Clean up animations
    return () => {
      fadeAnim.setValue(0);
      scaleAnim.setValue(0.8);
      pulseAnim.setValue(1);
      detectionProgress.setValue(0);
    };
  }, [permission]);

  // Set up pulse animation
  useEffect(() => {
    if (detected) {
      const animation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.05,
            duration: 1000,
            useNativeDriver: true
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true
          })
        ])
      );
      
      animation.start();
      
      return () => {
        animation.stop();
      };
    }
  }, [detected]);

  // Simulate food detection for demo purposes
  // In a real implementation, this would use computer vision models
  useEffect(() => {
    if (hasPermission) {
      // Start detection progress animation
      Animated.timing(detectionProgress, {
        toValue: 1,
        duration: 3000,
        useNativeDriver: false
      }).start(({ finished }) => {
        if (finished) setProgressValue(1);
      });
      
      // Add interpolation listener
      detectionProgress.addListener(({ value }) => setProgressValue(value));
      
      // Simulate gradual confidence increase
      const confidenceInterval = setInterval(() => {
        setDetectionConfidence(prev => {
          const newValue = prev + (Math.random() * 0.15);
          return newValue > 0.95 ? 0.95 : newValue;
        });
      }, 300);
      
      // Simulate detection after delay
      const timer = setTimeout(() => {
        // Simulate detection success
        setDetected(true);
        setIsTracking(true);
        setDetectionConfidence(0.95);
        clearInterval(confidenceInterval);
        
        // Use provided food items or create a simple one
        const items = foodData.items?.length 
          ? foodData.items.map((item, index) => ({
              ...item,
              id: `item-${index}`,
              position: {
                x: Math.random() * 0.8 - 0.4, // Random position
                y: Math.random() * 0.8 - 0.4,
                z: -1 - Math.random() * 2, // Depth
              },
              size: 0.5 + Math.random() * 0.5, // Random size
              highlighted: false,
            }))
          : [{
              id: 'item-0',
              name: foodData.name,
              calories: foodData.calories,
              protein: foodData.protein,
              carbs: foodData.carbs,
              fat: foodData.fat,
              position: { x: 0, y: 0, z: -2 },
              size: 1,
              highlighted: false,
            }];
            
        setDetectedItems(items);
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }, 3000);
      
      // Clean up the listener
      return () => {
        clearTimeout(timer);
        clearInterval(confidenceInterval);
        detectionProgress.removeAllListeners();
      };
    }
  }, [hasPermission, foodData]);

  // Initialize 3D scene
  const onContextCreate = async (gl: any) => {
    // Create a WebGLRenderer without a DOM element
    const renderer = new THREE.WebGLRenderer({
      context: gl,
      antialias: true,
      alpha: true,
    });
    
    const { drawingBufferWidth: width, drawingBufferHeight: height } = gl;
    renderer.setSize(width, height);
    
    // Create a scene and camera
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
    camera.position.z = 5;
    
    // Add ambient light
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    scene.add(ambientLight);
    
    // Add directional light
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(0, 10, 5);
    scene.add(directionalLight);
    
    // Create and add text labels for detected items when they're ready
    const addLabels = () => {
      if (detectedItems.length > 0) {
        // Remove any existing labels
        scene.children = scene.children.filter(child => 
          child.type !== 'Sprite' && !child.userData?.isLabel);
          
        // On mobile platforms, we use React Native views for labels (rendered in the component)
        // For Three.js scene, create simple placeholder objects
        detectedItems.forEach(item => {
          try {
            // Create a simple colored plane to represent the label
            const isHighlighted = item.id === selectedItem;
            
            // Create a plane geometry for the label background
            const geometry = new THREE.PlaneGeometry(1, 0.6);
            
            // Create material with a color based on selection state
            const material = new THREE.MeshBasicMaterial({ 
              color: isHighlighted ? 0x3b82f6 : (isDark ? 0x333333 : 0xffffff),
              opacity: 0.8,
              transparent: true,
              side: THREE.DoubleSide
            });
            
            // Create a mesh with the geometry and material
            const mesh = new THREE.Mesh(geometry, material);
            
            // Position the mesh
            mesh.position.set(
              item.position.x, 
              item.position.y, 
              item.position.z
            );
            
            // Scale the mesh
            const scaleX = isHighlighted ? item.size * 2.5 : item.size * 2;
            mesh.scale.set(scaleX, item.size * 1.2, 1);
            
            // Mark as label for cleaning up
            mesh.userData = { isLabel: true, itemId: item.id };
            
            // Add to scene
            scene.add(mesh);
          } catch (error) {
            console.error('Error creating label:', error);
          }
        });
      }
    };
    
    // Animation loop
    const render = () => {
      // Simulate motion tracking by slightly moving items
      if (isTracking && detectedItems.length > 0) {
        detectedItems.forEach(item => {
          // Add small random movement to simulate tracking
          if (Math.random() > 0.7) {
            item.position.x += (Math.random() - 0.5) * 0.005;
            item.position.y += (Math.random() - 0.5) * 0.005;
          }
        });
      }
      
      // Add/update labels when items are detected
      if (detected && detectedItems.length > 0) {
        if (Platform.OS === 'web') {
          addLabels();
        }
      }
      
      // Render the scene
      renderer.render(scene, camera);
      gl.endFrameEXP();
      
      // Keep the animation loop going
      requestAnimationFrame(render);
    };
    
    // Start the animation loop
    render();
  };

  // Toggle detailed nutrition view
  const toggleDetailedNutrition = () => {
    if (!showDetailedNutrition) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    setShowDetailedNutrition(!showDetailedNutrition);
  };

  // Handle item selection
  const handleSelectItem = (itemId: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedItem(itemId === selectedItem ? null : itemId);
    
    // Update item highlight status
    setDetectedItems(detectedItems.map(item => ({
      ...item,
      highlighted: item.id === itemId && itemId !== selectedItem
    })));
  };

  // If we don't have camera permission yet
  if (hasPermission === null) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Text style={[styles.permissionText, { color: colors.text }]}>
          {t('common.loading')}
        </Text>
      </View>
    );
  }

  // If camera permission was denied
  if (hasPermission === false) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Text style={[styles.permissionText, { color: colors.text }]}>
          {t('common.error')}
        </Text>
        <Text style={[styles.permissionDescription, { color: colors.textSecondary }]}>
          Camera permission is required for AR overlay
        </Text>
        <TouchableOpacity 
          style={[styles.permissionButton, { backgroundColor: colors.primary }]}
          onPress={onClose}
        >
          <Text style={styles.permissionButtonText}>{t('common.back')}</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  // If device doesn't support AR
  if (!deviceSupportsAR) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Text style={[styles.permissionText, { color: colors.text }]}>
          Device Not Supported
        </Text>
        <Text style={[styles.permissionDescription, { color: colors.textSecondary }]}>
          Your device does not support AR features needed for this overlay.
        </Text>
        <TouchableOpacity 
          style={[styles.permissionButton, { backgroundColor: colors.primary }]}
          onPress={onClose}
        >
          <Text style={styles.permissionButtonText}>{t('common.back')}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <Animated.View 
      style={[
        styles.container, 
        { 
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }]
        }
      ]}
    >
      {/* Camera background */}
      <CameraView
        ref={cameraRef}
        style={styles.camera}
        facing="back"
      />
      
      {/* GL View for AR overlay */}
      <GLView
        style={styles.arOverlay}
        onContextCreate={onContextCreate}
      />
      
      {/* Native labels for mobile platforms */}
      {Platform.OS !== 'web' && detected && (
        <View style={styles.labelsContainer}>
          {detectedItems.map((item) => (
            <TouchableOpacity 
              key={item.id}
              style={[
                styles.label,
                {
                  left: `${(item.position.x + 0.5) * 100}%`,
                  top: `${(item.position.y + 0.5) * 100}%`,
                  backgroundColor: item.id === selectedItem 
                    ? (isDark ? 'rgba(59, 130, 246, 0.9)' : 'rgba(37, 99, 235, 0.9)')
                    : (isDark ? 'rgba(30, 30, 30, 0.8)' : 'rgba(255, 255, 255, 0.8)'),
                  borderColor: item.id === selectedItem ? colors.primary : (isDark ? '#555' : '#ddd'),
                }
              ]}
              onPress={() => handleSelectItem(item.id)}
              activeOpacity={0.8}
            >
              <Animated.View 
                style={[
                  styles.labelContent, 
                  { transform: [{ scale: item.id === selectedItem ? pulseAnim : 1 }] }
                ]}
              >
                <Text 
                  style={[
                    styles.labelTitle, 
                    { 
                      color: item.id === selectedItem ? 'white' : colors.text,
                      fontSize: item.id === selectedItem ? 17 : 16
                    }
                  ]}
                >
                  {item.name}
                </Text>
                <Text 
                  style={[
                    styles.labelCalories, 
                    { 
                      color: item.id === selectedItem ? 'white' : colors.primary,
                      fontWeight: item.id === selectedItem ? '700' : '500'
                    }
                  ]}
                >
                  {item.calories} cal
                </Text>
                {item.protein && item.carbs && item.fat && (
                  <View style={styles.macroBarContainer}>
                    <View style={styles.macroBar}>
                      {/* Protein */}
                      <View 
                        style={[
                          styles.macroSegment, 
                          { 
                            backgroundColor: '#3b82f6',
                            flex: item.protein
                          }
                        ]} 
                      />
                      {/* Carbs */}
                      <View 
                        style={[
                          styles.macroSegment, 
                          { 
                            backgroundColor: '#8b5cf6',
                            flex: item.carbs
                          }
                        ]} 
                      />
                      {/* Fat */}
                      <View 
                        style={[
                          styles.macroSegment, 
                          { 
                            backgroundColor: '#f97316',
                            flex: item.fat
                          }
                        ]} 
                      />
                    </View>
                    
                    <Text 
                      style={[
                        styles.labelMacros, 
                        { 
                          color: item.id === selectedItem ? 'white' : colors.textSecondary
                        }
                      ]}
                    >
                      P: {item.protein}g C: {item.carbs}g F: {item.fat}g
                    </Text>
                  </View>
                )}
              </Animated.View>
            </TouchableOpacity>
          ))}
        </View>
      )}
      
      {/* Loading indicator */}
      {!detected && (
        <View style={styles.loadingContainer}>
          <View style={[styles.loadingBox, { backgroundColor: isDark ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.9)', borderColor: colors.primary }]}>
            <Text style={[styles.loadingText, { color: colors.text }]}>
              {t('foodAnalysis.analyzing')}
            </Text>
            <View style={styles.progressContainer}>
              <Bar 
                progress={progressValue}
                width={200}
                height={8}
                color={colors.primary}
                unfilledColor={isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'}
                borderWidth={0}
                borderRadius={4}
              />
              <Text style={[styles.confidenceText, { color: colors.textSecondary }]}>
                {Math.round(detectionConfidence * 100)}% {t('foodAnalysis.confidence')}
              </Text>
            </View>
          </View>
        </View>
      )}
      
      {/* Close button with shadow */}
      <LinearGradient
        colors={['rgba(0,0,0,0.5)', 'transparent']}
        style={[styles.topGradient, { pointerEvents: 'none' }]}
      />
      <TouchableOpacity 
        style={styles.closeButton}
        onPress={onClose}
      >
        <MaterialIcons name="close" size={24} color="white" />
      </TouchableOpacity>
      
      {/* Info panel */}
      <LinearGradient
        colors={['transparent', 'rgba(0,0,0,0.6)']}
        style={[styles.bottomGradient, { pointerEvents: 'none' }]}
      />
      <View style={styles.bottomControls}>
        {/* Basic info panel */}
        <TouchableOpacity 
          style={[
            styles.infoPanel, 
            { 
              backgroundColor: isDark 
                ? 'rgba(30, 30, 30, 0.9)' 
                : 'rgba(255, 255, 255, 0.9)',
              borderColor: colors.border
            }
          ]}
          onPress={toggleDetailedNutrition}
          activeOpacity={0.9}
        >
          <View style={styles.infoPanelHeader}>
            <Text style={[styles.infoPanelTitle, { color: colors.text }]}>
              {t('foodAnalysis.arMode')}
            </Text>
            <MaterialIcons 
              name={showDetailedNutrition ? "expand-less" : "expand-more"} 
              size={24} 
              color={colors.text} 
            />
          </View>
          <Text style={[styles.infoPanelText, { color: colors.textSecondary }]}>
            {detected 
              ? (selectedItem 
                ? t('foodAnalysis.tapToExploreMore') 
                : t('foodAnalysis.tapItemForDetails'))
              : t('foodAnalysis.pointCameraAtFood')
            }
          </Text>
        </TouchableOpacity>
        
        {/* Detailed nutrition panel */}
        {showDetailedNutrition && (
          <Animated.View 
            style={[
              styles.detailedNutritionPanel, 
              { 
                backgroundColor: isDark 
                  ? 'rgba(30, 30, 30, 0.95)' 
                  : 'rgba(255, 255, 255, 0.95)',
                borderColor: colors.border,
                opacity: fadeAnim,
                transform: [{ translateY: scaleAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [50, 0]
                })}]
              }
            ]}
          >
            <Text style={[styles.nutritionTitle, { color: colors.text }]}>
              {foodData.name}
            </Text>
            
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                Calories
              </Text>
              <View style={styles.nutritionValueContainer}>
                <Text style={[styles.nutritionValue, { color: colors.text }]}>
                  {foodData.calories}
                </Text>
                <View style={styles.nutritionBarContainer}>
                  <View style={[styles.nutritionBarBg, { backgroundColor: isDark ? '#444' : '#eee' }]}>
                    <View 
                      style={[
                        styles.nutritionBarFill, 
                        { 
                          backgroundColor: colors.primary,
                          width: `${Math.min((foodData.calories / RDI.calories) * 100, 100)}%`
                        }
                      ]} 
                    />
                  </View>
                  <Text style={[styles.nutritionBarPercent, { color: colors.textSecondary }]}>
                    {Math.round((foodData.calories / RDI.calories) * 100)}% DV
                  </Text>
                </View>
              </View>
            </View>
            
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                Protein
              </Text>
              <View style={styles.nutritionValueContainer}>
                <Text style={[styles.nutritionValue, { color: colors.text }]}>
                  {foodData.protein}g
                </Text>
                <View style={styles.nutritionBarContainer}>
                  <View style={[styles.nutritionBarBg, { backgroundColor: isDark ? '#444' : '#eee' }]}>
                    <View 
                      style={[
                        styles.nutritionBarFill, 
                        { 
                          backgroundColor: '#3b82f6',
                          width: `${Math.min((foodData.protein / RDI.protein) * 100, 100)}%`
                        }
                      ]} 
                    />
                  </View>
                  <Text style={[styles.nutritionBarPercent, { color: colors.textSecondary }]}>
                    {Math.round((foodData.protein / RDI.protein) * 100)}% DV
                  </Text>
                </View>
              </View>
            </View>
            
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                Carbs
              </Text>
              <View style={styles.nutritionValueContainer}>
                <Text style={[styles.nutritionValue, { color: colors.text }]}>
                  {foodData.carbs}g
                </Text>
                <View style={styles.nutritionBarContainer}>
                  <View style={[styles.nutritionBarBg, { backgroundColor: isDark ? '#444' : '#eee' }]}>
                    <View 
                      style={[
                        styles.nutritionBarFill, 
                        { 
                          backgroundColor: '#8b5cf6',
                          width: `${Math.min((foodData.carbs / RDI.carbs) * 100, 100)}%`
                        }
                      ]} 
                    />
                  </View>
                  <Text style={[styles.nutritionBarPercent, { color: colors.textSecondary }]}>
                    {Math.round((foodData.carbs / RDI.carbs) * 100)}% DV
                  </Text>
                </View>
              </View>
            </View>
            
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                Fat
              </Text>
              <View style={styles.nutritionValueContainer}>
                <Text style={[styles.nutritionValue, { color: colors.text }]}>
                  {foodData.fat}g
                </Text>
                <View style={styles.nutritionBarContainer}>
                  <View style={[styles.nutritionBarBg, { backgroundColor: isDark ? '#444' : '#eee' }]}>
                    <View 
                      style={[
                        styles.nutritionBarFill, 
                        { 
                          backgroundColor: '#f97316',
                          width: `${Math.min((foodData.fat / RDI.fat) * 100, 100)}%`
                        }
                      ]} 
                    />
                  </View>
                  <Text style={[styles.nutritionBarPercent, { color: colors.textSecondary }]}>
                    {Math.round((foodData.fat / RDI.fat) * 100)}% DV
                  </Text>
                </View>
              </View>
            </View>
          </Animated.View>
        )}
      </View>
    </Animated.View>
  );
};

const SCREEN_WIDTH = Dimensions.get('window').width;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  camera: {
    ...StyleSheet.absoluteFillObject,
  },
  arOverlay: {
    ...StyleSheet.absoluteFillObject,
  },
  labelsContainer: {
    ...StyleSheet.absoluteFillObject,
  },
  label: {
    position: 'absolute',
    padding: 10,
    borderRadius: 12,
    borderWidth: 2,
    minWidth: 150,
    minHeight: 80,
    alignItems: 'center',
    justifyContent: 'center',
    transform: [{ translateX: -75 }, { translateY: -60 }],
    elevation: 5,
    ...(Platform.OS === 'web' 
      ? { boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.25)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
        }
    ),
  },
  labelContent: {
    width: '100%',
    alignItems: 'center',
  },
  labelTitle: {
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'center',
  },
  labelCalories: {
    fontSize: 14,
    fontWeight: '500',
    marginVertical: 5,
  },
  labelMacros: {
    fontSize: 12,
    marginTop: 2,
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
    elevation: 3,
    ...(Platform.OS === 'web' 
      ? { boxShadow: '0px 2px 2px rgba(0, 0, 0, 0.3)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.3,
          shadowRadius: 2,
        }
    ),
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingBox: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 16,
    borderWidth: 2,
    elevation: 5,
    ...(Platform.OS === 'web' 
      ? { boxShadow: '0px 2px 5px rgba(0, 0, 0, 0.3)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.3,
          shadowRadius: 5,
        }
    ),
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  progressContainer: {
    alignItems: 'center',
  },
  confidenceText: {
    fontSize: 14,
    marginTop: 10,
  },
  bottomControls: {
    position: 'absolute',
    bottom: 40,
    left: 20,
    right: 20,
    zIndex: 10,
  },
  infoPanel: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    elevation: 5,
    ...(Platform.OS === 'web' 
      ? { boxShadow: '0px 2px 3px rgba(0, 0, 0, 0.3)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.3,
          shadowRadius: 3,
        }
    ),
  },
  infoPanelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  infoPanelTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  infoPanelText: {
    fontSize: 14,
    textAlign: 'left',
  },
  macroBarContainer: {
    width: '100%',
    marginTop: 5,
  },
  macroBar: {
    flexDirection: 'row',
    height: 6,
    width: '100%',
    borderRadius: 3,
    overflow: 'hidden',
  },
  macroSegment: {
    height: '100%',
  },
  detailedNutritionPanel: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginTop: 10,
    elevation: 5,
    ...(Platform.OS === 'web' 
      ? { boxShadow: '0px 2px 3px rgba(0, 0, 0, 0.3)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.3,
          shadowRadius: 3,
        }
    ),
  },
  nutritionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  nutritionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  nutritionLabel: {
    fontSize: 15,
    fontWeight: '500',
    flex: 1,
  },
  nutritionValueContainer: {
    flex: 2,
    alignItems: 'flex-end',
  },
  nutritionValue: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 4,
  },
  nutritionBarContainer: {
    width: '100%',
    alignItems: 'flex-end',
  },
  nutritionBarBg: {
    width: '100%',
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  nutritionBarFill: {
    height: '100%',
  },
  nutritionBarPercent: {
    fontSize: 12,
    marginTop: 2,
    textAlign: 'right',
  },
  topGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 100,
  },
  bottomGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 180,
  },
  permissionText: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  permissionDescription: {
    fontSize: 16,
    marginBottom: 24,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  permissionButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ARFoodOverlay; 