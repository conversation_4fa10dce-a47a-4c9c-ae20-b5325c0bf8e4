import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';

interface Ingredient {
  id: string;
  name: string;
  amount: number;
  unit: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
}

interface RecipeData {
  id: string;
  name: string;
  originalServings: number;
  ingredients: Ingredient[];
  totalNutrition: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
}

interface ScalingInfo {
  originalServings: number;
  newServings: number;
  scalingFactor: number;
}

interface AdjustableServingComponentProps {
  recipe: RecipeData;
  onScaledRecipe?: (recipe: RecipeData, scaling: ScalingInfo) => void;
}

export function AdjustableServingComponent({ 
  recipe: initialRecipe, 
  onScaledRecipe 
}: AdjustableServingComponentProps) {
  const { colors, isDark } = useTheme();
  
  const [recipe, setRecipe] = useState<RecipeData>(initialRecipe);
  const [servings, setServings] = useState(initialRecipe.originalServings);
  const [customServingInput, setCustomServingInput] = useState('');
  const [customServingMode, setCustomServingMode] = useState(false);
  
  // Calculate scaling factor and update nutrition values
  useEffect(() => {
    const scalingFactor = servings / recipe.originalServings;
    
    const scaledIngredients = initialRecipe.ingredients.map(ingredient => ({
      ...ingredient,
      amount: Number((ingredient.amount * scalingFactor).toFixed(2)),
      calories: Math.round(ingredient.calories * scalingFactor),
      protein: Number((ingredient.protein * scalingFactor).toFixed(1)),
      carbs: Number((ingredient.carbs * scalingFactor).toFixed(1)),
      fat: Number((ingredient.fat * scalingFactor).toFixed(1)),
    }));
    
    const totalNutrition = scaledIngredients.reduce(
      (acc, ingredient) => ({
        calories: acc.calories + ingredient.calories,
        protein: acc.protein + ingredient.protein,
        carbs: acc.carbs + ingredient.carbs,
        fat: acc.fat + ingredient.fat,
      }),
      { calories: 0, protein: 0, carbs: 0, fat: 0 }
    );
    
    const updatedRecipe = {
      ...initialRecipe,
      ingredients: scaledIngredients,
      totalNutrition,
    };
    
    setRecipe(updatedRecipe);
    
    if (onScaledRecipe) {
      onScaledRecipe(updatedRecipe, {
        originalServings: initialRecipe.originalServings,
        newServings: servings,
        scalingFactor,
      });
    }
  }, [servings, initialRecipe]);
  
  const handleIncrementServings = () => {
    setServings(prev => prev + 1);
    setCustomServingMode(false);
  };
  
  const handleDecrementServings = () => {
    if (servings > 1) {
      setServings(prev => prev - 1);
      setCustomServingMode(false);
    }
  };
  
  const handleHalfRecipe = () => {
    const newServings = Math.max(Math.floor(initialRecipe.originalServings / 2), 1);
    setServings(newServings);
    setCustomServingMode(false);
  };
  
  const handleDoubleRecipe = () => {
    setServings(initialRecipe.originalServings * 2);
    setCustomServingMode(false);
  };
  
  const handleResetRecipe = () => {
    setServings(initialRecipe.originalServings);
    setCustomServingMode(false);
  };
  
  const handleCustomServingConfirm = () => {
    const newServings = parseInt(customServingInput);
    if (isNaN(newServings) || newServings <= 0) {
      Alert.alert('Invalid Input', 'Please enter a valid number of servings');
      return;
    }
    
    setServings(newServings);
    setCustomServingMode(false);
    setCustomServingInput('');
  };
  
  const renderIngredientList = () => {
    return (
      <View style={styles.ingredientsContainer}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Ingredients
        </Text>
        
        {recipe.ingredients.map((ingredient, index) => (
          <View 
            key={ingredient.id} 
            style={[
              styles.ingredientItem,
              index !== recipe.ingredients.length - 1 && { 
                borderBottomWidth: 1, 
                borderBottomColor: colors.border 
              }
            ]}
          >
            <Text style={[styles.ingredientAmount, { color: colors.primary }]}>
              {ingredient.amount} {ingredient.unit}
            </Text>
            <Text style={[styles.ingredientName, { color: colors.text }]}>
              {ingredient.name}
            </Text>
          </View>
        ))}
      </View>
    );
  };
  
  const renderNutrition = () => {
    return (
      <View style={styles.nutritionContainer}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Nutrition Information ({servings} servings)
        </Text>
        
        <View style={styles.nutritionGrid}>
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {recipe.totalNutrition.calories}
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
              Calories
            </Text>
          </View>
          
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {recipe.totalNutrition.protein}g
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
              Protein
            </Text>
          </View>
          
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {recipe.totalNutrition.carbs}g
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
              Carbs
            </Text>
          </View>
          
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {recipe.totalNutrition.fat}g
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
              Fat
            </Text>
          </View>
        </View>
        
        <View style={styles.nutritionGrid}>
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {Math.round(recipe.totalNutrition.calories / servings)}
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
              Cal/Serving
            </Text>
          </View>
          
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {(recipe.totalNutrition.protein / servings).toFixed(1)}g
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
              Protein/Serving
            </Text>
          </View>
          
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {(recipe.totalNutrition.carbs / servings).toFixed(1)}g
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
              Carbs/Serving
            </Text>
          </View>
          
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {(recipe.totalNutrition.fat / servings).toFixed(1)}g
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
              Fat/Serving
            </Text>
          </View>
        </View>
      </View>
    );
  };
  
  return (
    <ScrollView style={styles.container}>
      <View style={[styles.card, { backgroundColor: isDark ? colors.card : 'white' }]}>
        <View style={styles.headerContainer}>
          <Text style={[styles.recipeTitle, { color: colors.text }]}>
            {recipe.name}
          </Text>
          
          <View style={styles.servingAdjusterContainer}>
            <Text style={[styles.servingLabel, { color: colors.textSecondary }]}>
              Adjust Servings:
            </Text>
            
            <View style={styles.servingControls}>
              {customServingMode ? (
                <View style={styles.customServingContainer}>
                  <TextInput
                    style={[
                      styles.customServingInput,
                      { 
                        color: colors.text,
                        borderColor: colors.border,
                        backgroundColor: isDark ? colors.subtle : '#f5f5f5'
                      }
                    ]}
                    value={customServingInput}
                    onChangeText={setCustomServingInput}
                    placeholder="Enter servings"
                    placeholderTextColor={colors.textSecondary}
                    keyboardType="number-pad"
                    returnKeyType="done"
                    onSubmitEditing={handleCustomServingConfirm}
                  />
                  <TouchableOpacity
                    style={[styles.customServingButton, { backgroundColor: colors.primary }]}
                    onPress={handleCustomServingConfirm}
                  >
                    <Text style={styles.customServingButtonText}>Set</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <View style={styles.incrementContainer}>
                  <TouchableOpacity
                    style={[
                      styles.controlButton,
                      { borderColor: colors.border },
                      servings <= 1 && { opacity: 0.5 }
                    ]}
                    onPress={handleDecrementServings}
                    disabled={servings <= 1}
                  >
                    <Feather name="minus" size={18} color={colors.text} />
                  </TouchableOpacity>
                  
                  <Text style={[styles.servingCount, { color: colors.text }]}>
                    {servings}
                  </Text>
                  
                  <TouchableOpacity
                    style={[styles.controlButton, { borderColor: colors.border }]}
                    onPress={handleIncrementServings}
                  >
                    <Feather name="plus" size={18} color={colors.text} />
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </View>
          
          <View style={styles.quickButtons}>
            <TouchableOpacity
              style={[styles.quickButton, { backgroundColor: isDark ? colors.subtle : '#f5f5f5' }]}
              onPress={handleHalfRecipe}
            >
              <Divide size={16} color={colors.primary} style={styles.quickButtonIcon} />
              <Text style={[styles.quickButtonText, { color: colors.text }]}>
                Half
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.quickButton, { backgroundColor: isDark ? colors.subtle : '#f5f5f5' }]}
              onPress={handleDoubleRecipe}
            >
              <Text style={[styles.quickButtonMultiplier, { color: colors.primary }]}>
                2x
              </Text>
              <Text style={[styles.quickButtonText, { color: colors.text }]}>
                Double
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.quickButton, { backgroundColor: isDark ? colors.subtle : '#f5f5f5' }]}
              onPress={() => setCustomServingMode(true)}
            >
              <Feather name="calculator" size={16} color={colors.primary} style={styles.quickButtonIcon} />
              <Text style={[styles.quickButtonText, { color: colors.text }]}>
                Custom
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.quickButton, { backgroundColor: isDark ? colors.subtle : '#f5f5f5' }]}
              onPress={handleResetRecipe}
            >
              <Feather name="refresh-cw" size={16} color={colors.primary} style={styles.quickButtonIcon} />
              <Text style={[styles.quickButtonText, { color: colors.text }]}>
                Reset
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.divider} />
        
        {renderIngredientList()}
        
        <View style={styles.divider} />
        
        {renderNutrition()}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    margin: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  headerContainer: {
    marginBottom: 16,
  },
  recipeTitle: {
    fontSize: 22,
    fontWeight: '700',
    marginBottom: 16,
  },
  servingAdjusterContainer: {
    marginBottom: 16,
  },
  servingLabel: {
    fontSize: 16,
    marginBottom: 8,
  },
  servingControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  incrementContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  controlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  servingCount: {
    fontSize: 20,
    fontWeight: '600',
    marginHorizontal: 16,
    width: 30,
    textAlign: 'center',
  },
  customServingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  customServingInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    width: 120,
  },
  customServingButton: {
    height: 40,
    borderRadius: 8,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  customServingButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  quickButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  quickButton: {
    flex: 1,
    height: 64,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
  },
  quickButtonIcon: {
    marginBottom: 6,
  },
  quickButtonMultiplier: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 6,
  },
  quickButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  divider: {
    height: 1,
    backgroundColor: 'rgba(0,0,0,0.1)',
    marginVertical: 16,
  },
  ingredientsContainer: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  ingredientItem: {
    flexDirection: 'row',
    paddingVertical: 10,
  },
  ingredientAmount: {
    fontSize: 15,
    fontWeight: '500',
    width: 80,
  },
  ingredientName: {
    fontSize: 15,
    flex: 1,
  },
  nutritionContainer: {
    marginBottom: 8,
  },
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  nutritionItem: {
    width: '50%',
    paddingVertical: 8,
    paddingRight: 8,
  },
  nutritionValue: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 2,
  },
  nutritionLabel: {
    fontSize: 12,
  },
});

export default AdjustableServingComponent; 