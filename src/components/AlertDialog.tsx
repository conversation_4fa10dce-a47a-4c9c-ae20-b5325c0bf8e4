import React, { useEffect, useState } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TouchableOpacity, 
  Modal, 
  Pressable,
  Animated,
  Dimensions,
  Platform,
  TouchableWithoutFeedback
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { MaterialIcons , Feather } from '@expo/vector-icons';
import { canUseNativeDriver } from '@/utils/platformUtils';

export type AlertType = 'success' | 'error' | 'info' | 'warning';

interface AlertDialogProps {
  visible: boolean;
  title: string;
  message: string;
  type?: AlertType;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  onDismiss: () => void;
}

const { height } = Dimensions.get('window');

const AlertDialog: React.FC<AlertDialogProps> = ({
  visible,
  title,
  message,
  type = 'info',
  confirmText = 'OK',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  onDismiss
}) => {
  const { colors, isDark } = useTheme();
  const [slideAnim] = useState(new Animated.Value(height));
  const [opacity] = useState(new Animated.Value(0));

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: canUseNativeDriver(),
        }),
        Animated.timing(opacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: canUseNativeDriver(),
        })
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: height,
          duration: 200,
          useNativeDriver: canUseNativeDriver(),
        }),
        Animated.timing(opacity, {
          toValue: 0,
          duration: 200,
          useNativeDriver: canUseNativeDriver(),
        })
      ]).start();
    }
  }, [visible, slideAnim, opacity]);

  const getAlertIcon = () => {
    switch (type) {
      case 'success':
        return <MaterialIcons name="check-circle" size={28}  color={colors.text} />;
      case 'error':
        return <Feather name="x-circle" size={28}  color={colors.text} />;
      case 'warning':
        return <Feather name="alert-triangle" size={28}  color={colors.text} />;
      case 'info':
      default:
        return <Feather name="info" size={28}  color={colors.text} />;
    }
  };

  const getAlertColor = (): string => {
    switch (type) {
      case 'success':
        return '#10B981';
      case 'error':
        return '#EF4444';
      case 'warning':
        return '#F59E0B';
      case 'info':
      default:
        return '#3B82F6';
    }
  };

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    }
    onDismiss();
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    onDismiss();
  };

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onDismiss}
    >
      <Pressable 
        style={[styles.overlay, { backgroundColor: 'rgba(0,0,0,0.5)' }]} 
        onPress={onDismiss}
      >
        <Animated.View
          style={[
            styles.container,
            { 
              backgroundColor: colors.card, 
              borderColor: colors.border,
              transform: [{ translateY: slideAnim }],
              opacity: opacity
            }
          ]}
        >
          <View style={styles.closeButtonContainer}>
            <TouchableOpacity 
              style={[styles.closeButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]} 
              onPress={onDismiss}
            >
              <Feather name="x" size={18} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.iconContainer}>
            <View style={[styles.iconBackground, { backgroundColor: `${getAlertColor()}20` }]}>
              {getAlertIcon()}
            </View>
          </View>
          
          <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
          <Text style={[styles.message, { color: colors.textSecondary }]}>{message}</Text>
          
          <View style={styles.buttonContainer}>
            {onCancel && (
              <TouchableOpacity
                style={[styles.button, styles.cancelButton, { 
                  borderColor: getAlertColor(),
                  backgroundColor: isDark ? 'transparent' : 'transparent'
                }]}
                onPress={handleCancel}
              >
                <Text style={[styles.buttonText, { color: getAlertColor() }]}>{cancelText}</Text>
              </TouchableOpacity>
            )}
            
            <TouchableOpacity
              style={[
                styles.button, 
                styles.confirmButton, 
                { 
                  backgroundColor: getAlertColor(),
                  ...(onCancel ? { flex: 1 } : { minWidth: 200 })
                }
              ]}
              onPress={handleConfirm}
            >
              <Text style={[styles.buttonText, styles.confirmButtonText]}>{confirmText}</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  container: {
    width: '100%',
    maxWidth: 340,
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: { elevation: 6 },
      web: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      }
    }),
  },
  closeButtonContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
    zIndex: 1,
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 16,
  },
  iconBackground: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
    gap: 12,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    borderWidth: 1,
  },
  confirmButton: {
    backgroundColor: '#3B82F6',
  },
  confirmButtonText: {
    color: 'white',
  },
});

export default AlertDialog; 