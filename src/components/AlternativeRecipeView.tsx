import React, { useEffect, useState, useCallback } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, Platform, Image, ActivityIndicator, Alert, Modal } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { getPlaceholderFoodImage, generateRecipeImage, canGenerateImage, updateImageGenerationStatus } from '@/services/imageGenerationService';
import { generateDalleImage } from '@/services/openaiService';
import * as MediaLibrary from 'expo-media-library';
import { WhyThisSwapExplainer } from './WhyThisSwapExplainer';
import { HealthImpactExplainer } from './HealthImpactExplainer';

interface RecipeIngredient {
  name: string;
  amount: string;
  calories: number;
  protein?: number;
  carbs?: number;
  fat?: number;
}

// Interface for the original nutrition data passed as prop
interface OriginalNutrition {
  calories?: number;
  protein?: number;
  carbs?: number;
  fat?: number;
}

interface Recipe {
  id?: string;
  name: string;
  focus?: string;
  description: string;
  ingredients: RecipeIngredient[];
  instructions: string[];
  nutritionalInfo: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber?: number;
    sugar?: number;
  };
  preparationTime: string;
  cookingTime: string;
  healthBenefits?: string[];
  imagePrompt?: string;
  image_url?: string;
  image_generation_status?: string;
  originalNutrition?: OriginalNutrition;
  originalIngredients?: string[] | string;
}

interface AlternativeRecipeViewProps {
  recipes: Recipe[];
  selectedIndex: number;
  onIndexChange: (index: number) => void;
  originalImageUri: string;
  onBack: () => void;
  onSaveRecipe: () => Promise<void>;
  loading?: boolean;
  originalNutrition?: OriginalNutrition; // Destructure the new prop
  userDietaryPreferences?: string[];
  userHealthGoals?: string[];
}

export function AlternativeRecipeView({ 
  recipes,
  selectedIndex,
  onIndexChange,
  originalImageUri, 
  onBack, 
  onSaveRecipe, 
  loading = false,
  originalNutrition,
  userDietaryPreferences = [],
  userHealthGoals = []
}: AlternativeRecipeViewProps) {
  const { colors, isDark } = useTheme();
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [generatingImage, setGeneratingImage] = useState(false);
  const [canRegenerate, setCanRegenerate] = useState(false);
  const [imageLoadError, setImageLoadError] = useState(false);
  const [imageFallbackUsed, setImageFallbackUsed] = useState(false);
  const [showSwapExplainer, setShowSwapExplainer] = useState(false);
  const [showHealthImpact, setShowHealthImpact] = useState(false);
  
  // Derive the recipe to display based on the selected index
  const recipe = recipes[selectedIndex];

  // Set initial image - either from recipe or placeholder
  useEffect(() => {
    if (recipe) {
      // Reset states when recipe changes
      setImageLoadError(false);
      setImageFallbackUsed(false);
      
      if (recipe.image_url) {
        setImageUrl(recipe.image_url);
      } else {
        // Set placeholder image while we generate a real one
        const placeholderUrl = getPlaceholderFoodImage(recipe.name);
        setImageUrl(placeholderUrl);
        setImageFallbackUsed(true);
        
        // Auto-generate an image for the recipe
        generateRecipeHeaderImage();
      }
      
      // Check if we can regenerate the image if the recipe has an ID
      if (recipe.id) {
        checkIfCanRegenerate();
      }
    }
  }, [recipe]);
  
  // Check if we can regenerate the image
  const checkIfCanRegenerate = async () => {
    try {
      const allowed = await canGenerateImage();
      setCanRegenerate(allowed);
      
      // If status is "generating", set the generating flag
      if (recipe.image_generation_status === 'generating') {
        setGeneratingImage(true);
        
        // Poll for completion every 5 seconds
        const interval = setInterval(async () => {
          try {
            const response = await fetch(`${process.env.EXPO_PUBLIC_FIREBASE_URL}/rest/v1/alternative_recipes?id=eq.${recipe.id}&select=image_url,image_generation_status`, {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${process.env.EXPO_PUBLIC_FIREBASE_API_KEY}`,
              },
            });
            
            if (response.ok) {
              const data = await response.json() as any[];
              if (data && data.length > 0) {
                const status = data[0].image_generation_status;
                if (status === 'completed' || status === 'failed') {
                  setGeneratingImage(false);
                  if (status === 'completed' && data[0].image_url) {
                    setImageUrl(data[0].image_url);
                    setImageFallbackUsed(false);
                    setImageLoadError(false);
                  }
                  clearInterval(interval);
                  setCanRegenerate(status === 'failed');
                }
              }
            }
          } catch (error) {
            console.error('Error polling for image generation status:', error);
            clearInterval(interval);
            setGeneratingImage(false);
            setCanRegenerate(true);
          }
        }, 5000);
        
        return () => clearInterval(interval);
      }
    } catch (error) {
      console.error('Error checking if can regenerate:', error);
      setCanRegenerate(false);
    }
  };
  
  // Function to generate header image for the recipe
  const generateRecipeHeaderImage = async () => {
    if (generatingImage) return; // Prevent multiple simultaneous generations
    
    setGeneratingImage(true);
    
    try {
      console.log('Auto-generating header image for recipe:', recipe.name);
      
      // Create a default prompt if none exists
      const imagePrompt = recipe.imagePrompt || 
        `A professional food photo of ${recipe.name}, a healthy meal presented on a white plate with garnish, studio lighting, high resolution`;
      
      let imageUrl;
      
      try {
        // Try DALL-E first if available
        imageUrl = await generateDalleImage(imagePrompt);
        console.log('DALL-E image generated successfully');
      } catch (dalleError) {
        console.log('DALL-E generation failed, using fallback method');
        
        // Fallback to a food image API or placeholder
        const keywords = recipe.name.split(' ').join(',');
        imageUrl = `https://source.unsplash.com/featured/?food,${keywords}`;
        imageUrl += `&t=${new Date().getTime()}`;
      }
      
      // Update UI with generated image
      setImageUrl(imageUrl);
      setImageFallbackUsed(false);
      
      // Update recipe status in database if we have an ID
      if (recipe.id) {
        try {
          await updateImageGenerationStatus(recipe.id, 'completed', imageUrl);
        } catch (updateError) {
          console.error('Error updating image status:', updateError);
        }
      }
    } catch (error) {
      console.error('Error generating image:', error);
      // Keep the placeholder image we already set
    } finally {
      setGeneratingImage(false);
      setCanRegenerate(true);
    }
  };
  
  // Function to generate a printable recipe image
  const handleGenerateImage = async () => {
    // Set generating state regardless of recipe properties
    setGeneratingImage(true);
    
    try {
      console.log('Generating printable recipe image for:', recipe.name);
      
      const imagePrompt = `A beautiful recipe card for "${recipe.name}" showing ingredients and instructions, with a small picture of the dish in the corner, cookbook style with elegant typography`;
      
      try {
        // Generate the recipe card
        const recipeCardUrl = await generateDalleImage(imagePrompt);
        
        if (Platform.OS !== 'web') {
          // For native platforms: Try to save to media library
          try {
            const { status } = await MediaLibrary.requestPermissionsAsync();
            
            if (status === 'granted') {
              await MediaLibrary.createAssetAsync(recipeCardUrl);
              
              Alert.alert(
                "Recipe Card Saved", 
                "Your recipe card has been saved to your photo library!",
                [{ text: "OK" }]
              );
            } else {
              // Permission denied
              Alert.alert(
                "Recipe Card Generated", 
                "The recipe card was created but couldn't be saved due to permission issues.",
                [{ text: "OK" }]
              );
            }
          } catch (saveError) {
            console.error('Error saving recipe card:', saveError);
            Alert.alert(
              "Recipe Card Generated", 
              "Your recipe card has been created but couldn't be saved automatically.",
              [{ text: "OK" }]
            );
          }
        } else {
          // For web: Just show a success message
          Alert.alert(
            "Recipe Card Generated", 
            "Your recipe card has been generated successfully. The image URL has been logged to the console.",
            [{ text: "OK" }]
          );
          
          // Log the URL so developers can see it
          console.log('Recipe card URL:', recipeCardUrl);
        }
      } catch (error) {
        console.error('Error generating recipe card:', error);
        Alert.alert(
          "Generation Error", 
          "We couldn't generate a recipe card at this time. Please try again later."
        );
      }
      
    } catch (error) {
      console.error('Error in recipe card generation:', error);
      Alert.alert("Error", "Failed to generate recipe card");
    } finally {
      setGeneratingImage(false);
    }
  };
  
  const handleImageError = () => {
    console.log('Image failed to load, using placeholder instead');
    setImageLoadError(true);
    setImageFallbackUsed(true);
    setImageUrl(getPlaceholderFoodImage(recipe.name));
  };
  
  // Extract ingredient lists for the swap explainer
  const extractIngredients = useCallback(() => {
    // Get the original ingredients from the first recipe's originalIngredients field
    // or generate an estimated list from the recipe name
    const originalIngredients = 
      recipes[0]?.originalIngredients || 
      recipes[0]?.name.split(/\s+/).filter(word => 
        word.length > 3 && 
        !['with', 'and', 'the', 'for', 'from'].includes(word.toLowerCase())
      );
    
    // Get the current recipe's ingredients
    const currentIngredients = recipe?.ingredients?.map(ing => ing.name) || [];
    
    return {
      originalIngredients: Array.isArray(originalIngredients) ? originalIngredients : [originalIngredients],
      healthierIngredients: currentIngredients
    };
  }, [recipe, recipes]);
  
  if (!recipe) return null;
  
  return (
    <View style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Back button */}
        <TouchableOpacity 
          style={[styles.floatingBackButton, { backgroundColor: isDark ? 'rgba(40,40,40,0.8)' : 'rgba(255,255,255,0.9)' }]}
          onPress={onBack}
        >
          <Feather name="arrow-left" size={20} color={colors.text} />
        </TouchableOpacity>
        
        {/* Recipe image */}
        <View style={[styles.recipeImageContainer, { 
          backgroundColor: colors.subtle,
          borderColor: colors.border,
        }]}>
          {imageUrl ? (
            <>
              <Image 
                source={{ uri: imageUrl }} 
                style={styles.recipeImage}
                resizeMode="cover"
                onError={handleImageError}
              />
            </>
          ) : (
            <View style={styles.noImageContainer}>
              <ImageIcon size={40} color={colors.textSecondary} />
              <Text style={[styles.noImageText, { color: colors.textSecondary }]}>
                Recipe image will be generated
              </Text>
            </View>
          )}
          
          {generatingImage && (
            <View style={styles.imageOverlay}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={styles.imageOverlayText}>Generating image...</Text>
            </View>
          )}
          
          {imageLoadError && !generatingImage && (
            <TouchableOpacity 
              style={[styles.refreshImageButton, { backgroundColor: 'rgba(0,0,0,0.5)' }]}
              onPress={handleGenerateImage}
            >
              <RefreshCcw size={18} color="white" />
              <Text style={styles.refreshButtonText}>Retry</Text>
            </TouchableOpacity>
          )}
          
          {recipe.id && canRegenerate && !generatingImage && !imageLoadError && (
            <TouchableOpacity 
              style={[styles.refreshImageButton, { backgroundColor: 'rgba(0,0,0,0.5)' }]}
              onPress={handleGenerateImage}
            >
              <RefreshCcw size={18} color="white" />
            </TouchableOpacity>
          )}
        </View>
        
        <View style={styles.titleContainer}>
          <Text style={[styles.recipeTitle, { color: colors.text }]}>
            {recipe.name}
          </Text>
          {/* Display Focus Tag if available */}
          {recipe.focus && (
            <View style={[styles.focusTag, { backgroundColor: colors.subtle }]}>
              <Text style={[styles.focusTagText, { color: colors.primary }]}>
                {recipe.focus}
              </Text>
            </View>
          )}
          <View style={[styles.calorieBadge, { backgroundColor: colors.primaryLight }]}>
            <Text style={[styles.calorieText, { color: colors.primary }]}>
              {recipe.nutritionalInfo.calories} cal
            </Text>
          </View>
        </View>
        
        <Text style={[styles.recipeDescription, { color: colors.textSecondary }]}>
          {recipe.description}
        </Text>
        
        {/* Add buttons container for both explainers */}
        <View style={styles.explainerButtonsContainer}>
          {/* Why This Swap button */}
          <TouchableOpacity
            style={[styles.swapExplainerButton, { backgroundColor: colors.primaryLight }]}
            onPress={() => setShowSwapExplainer(true)}
          >
            <Feather name="info" size={16} color={colors.primary} style={styles.swapButtonIcon} />
            <Text style={[styles.swapButtonText, { color: colors.primary }]}>
              Why These Swaps?
            </Text>
          </TouchableOpacity>
          
          {/* Health Impact button */}
          <TouchableOpacity
            style={[styles.healthImpactButton, { backgroundColor: colors.primaryLight }]}
            onPress={() => setShowHealthImpact(true)}
          >
            <Feather name="activity" size={16} color={colors.primary} style={styles.swapButtonIcon} />
            <Text style={[styles.swapButtonText, { color: colors.primary }]}>
              Health Impact
            </Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.timeInfo}>
          <View style={styles.timeItem}>
            <View style={[styles.timeIconContainer, { backgroundColor: colors.subtle }]}>
              <Feather name="clock" size={16} color={colors.textSecondary} />
            </View>
            <View>
              <Text style={[styles.timeLabel, { color: colors.textSecondary }]}>
                Prep Time
              </Text>
              <Text style={[styles.timeValue, { color: colors.text }]}>
                {recipe.preparationTime}
              </Text>
            </View>
          </View>
          
          <View style={styles.timeItem}>
            <View style={[styles.timeIconContainer, { backgroundColor: colors.subtle }]}>
              <MaterialIcons name="restaurant" size={16} color={colors.textSecondary} />
            </View>
            <View>
              <Text style={[styles.timeLabel, { color: colors.textSecondary }]}>
                Cook Time
              </Text>
              <Text style={[styles.timeValue, { color: colors.text }]}>
                {recipe.cookingTime}
              </Text>
            </View>
          </View>
        </View>
        
        {/* Nutritional Summary */}
        <View style={[styles.nutritionCard, { backgroundColor: colors.subtle, borderColor: colors.border }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Nutrition Facts (vs. Original)
          </Text>
          <View style={styles.nutritionGrid}>
            {/* Calories Comparison */}
            <NutritionComparisonItem 
              label="Calories"
              unit="kcal"
              originalValue={originalNutrition?.calories}
              newValue={recipe.nutritionalInfo.calories}
              lowerIsBetter={true} // Lower calories are generally better
              colors={colors}
            />
            {/* Protein Comparison */}
            <NutritionComparisonItem 
              label="Protein"
              unit="g"
              originalValue={originalNutrition?.protein}
              newValue={recipe.nutritionalInfo.protein}
              lowerIsBetter={false} // Higher protein is generally better
              colors={colors}
            />
            {/* Carbs Comparison */}
            <NutritionComparisonItem 
              label="Carbs"
              unit="g"
              originalValue={originalNutrition?.carbs}
              newValue={recipe.nutritionalInfo.carbs}
              lowerIsBetter={true} // Lower carbs are often desired
              colors={colors}
            />
            {/* Fat Comparison */}
            <NutritionComparisonItem 
              label="Fat"
              unit="g"
              originalValue={originalNutrition?.fat}
              newValue={recipe.nutritionalInfo.fat}
              lowerIsBetter={true} // Lower fat is generally better
              colors={colors}
            />
            
            {/* Optional Fiber (show only new value if original missing) */}
            {recipe.nutritionalInfo.fiber !== undefined && (
               <NutritionComparisonItem 
                label="Fiber"
                unit="g"
                originalValue={undefined} // Assuming original fiber isn't tracked
                newValue={recipe.nutritionalInfo.fiber}
                lowerIsBetter={false} // Higher fiber is better
                colors={colors}
              />
            )}
            
             {/* Optional Sugar (show only new value if original missing) */}
            {recipe.nutritionalInfo.sugar !== undefined && (
              <NutritionComparisonItem 
                label="Sugar"
                unit="g"
                originalValue={undefined} // Assuming original sugar isn't tracked
                newValue={recipe.nutritionalInfo.sugar}
                lowerIsBetter={true} // Lower sugar is better
                colors={colors}
              />
            )}
          </View>
        </View>
        
        {/* Health Benefits */}
        <View style={styles.benefitsSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Health Benefits
          </Text>
          <View style={styles.benefitsList}>
            {recipe.healthBenefits?.map((benefit, index) => (
              <View key={index} style={[styles.benefitItem, { backgroundColor: colors.subtle }]}>
                <View style={[styles.checkCircle, { backgroundColor: colors.primary }]}>
                  <Feather name="check" size={12}  color={colors.text} />
                </View>
                <Text style={[styles.benefitText, { color: colors.text }]}>
                  {benefit}
                </Text>
              </View>
            ))}
          </View>
        </View>
        
        {/* Ingredients */}
        <View style={styles.ingredientsSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Ingredients
          </Text>
          <View style={[
            styles.ingredientsCard, 
            { 
              backgroundColor: colors.card,
              borderColor: colors.border,
              shadowColor: colors.shadow,
            }
          ]}>
            {recipe.ingredients.map((ingredient, index) => (
              <View 
                key={index} 
                style={[
                  styles.ingredientRow,
                  index < recipe.ingredients.length - 1 && 
                    { borderBottomColor: colors.border, borderBottomWidth: 1 }
                ]}
              >
                <View style={styles.ingredientNameContainer}>
                  <View style={[styles.ingredientDot, { backgroundColor: colors.primary }]} />
                  <Text style={[styles.ingredientName, { color: colors.text }]}>
                    {ingredient.name}
                  </Text>
                </View>
                <Text style={[styles.ingredientAmount, { color: colors.textSecondary }]}>
                  {ingredient.amount}
                </Text>
              </View>
            ))}
          </View>
        </View>
        
        {/* Instructions */}
        <View style={styles.instructionsSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Instructions
          </Text>
          {recipe.instructions.map((instruction, index) => (
            <View key={index} style={styles.instructionItem}>
              <View style={[styles.stepNumberContainer, { backgroundColor: colors.primary }]}>
                <Text style={styles.stepNumber}>{index + 1}</Text>
              </View>
              <Text style={[styles.instructionText, { color: colors.text }]}>
                {instruction}
              </Text>
            </View>
          ))}
        </View>
      </ScrollView>
      
      {/* Pagination Buttons */}
      {recipes.length > 1 && (
        <View style={styles.paginationContainer}>
          <TouchableOpacity
            style={[
              styles.paginationButton,
              { backgroundColor: selectedIndex === 0 ? colors.subtle : colors.primary },
              selectedIndex === 0 && styles.disabledButton // Apply disabled style
            ]}
            onPress={() => onIndexChange(selectedIndex - 1)}
            disabled={selectedIndex === 0}
          >
            <Text style={[styles.paginationButtonText, { color: selectedIndex === 0 ? colors.textSecondary : 'white' }]}>Previous</Text>
          </TouchableOpacity>
          
          <Text style={[styles.paginationText, { color: colors.textSecondary }]}>
            {selectedIndex + 1} / {recipes.length}
          </Text>
          
          <TouchableOpacity
            style={[
              styles.paginationButton,
              { backgroundColor: selectedIndex === recipes.length - 1 ? colors.subtle : colors.primary },
              selectedIndex === recipes.length - 1 && styles.disabledButton // Apply disabled style
            ]}
            onPress={() => onIndexChange(selectedIndex + 1)}
            disabled={selectedIndex === recipes.length - 1}
          >
            <Text style={[styles.paginationButtonText, { color: selectedIndex === recipes.length - 1 ? colors.textSecondary : 'white' }]}>Next</Text>
          </TouchableOpacity>
        </View>
      )}

      <View style={[styles.footer, { backgroundColor: colors.card, borderTopColor: colors.border }]}>
        <TouchableOpacity 
          style={[
            styles.generateImageButton, 
            { 
              backgroundColor: colors.success,
              shadowColor: colors.success,
              opacity: generatingImage ? 0.7 : 1
            }
          ]}
          onPress={handleGenerateImage}
          disabled={generatingImage}
        >
          {generatingImage ? (
            <ActivityIndicator color="white" size="small" />
          ) : (
            <>
              <ImageIcon size={18} color="white" style={styles.buttonIcon} />
              <Text style={styles.buttonText}>Generate Recipe Image</Text>
            </>
          )}
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[
            styles.saveButton, 
            { 
              backgroundColor: colors.primary,
              shadowColor: colors.primary,
              opacity: loading ? 0.7 : 1
            }
          ]}
          onPress={onSaveRecipe}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="white" size="small" />
          ) : (
            <>
              <Feather name="save" size={18} style={styles.saveIcon} />
              <Text style={styles.buttonText}>Save Recipe</Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      {/* Add the Why This Swap explainer modal */}
      {showSwapExplainer && (
        <Modal
          visible={showSwapExplainer}
          animationType="slide"
          presentationStyle="fullScreen"
          onRequestClose={() => setShowSwapExplainer(false)}
        >
          <WhyThisSwapExplainer
            {...extractIngredients()}
            dietaryPreferences={userDietaryPreferences}
            userHealthGoals={userHealthGoals}
            onClose={() => setShowSwapExplainer(false)}
          />
        </Modal>
      )}
      
      {/* Add the Health Impact explainer modal */}
      {showHealthImpact && (
        <Modal
          visible={showHealthImpact}
          animationType="slide"
          presentationStyle="fullScreen"
          onRequestClose={() => setShowHealthImpact(false)}
        >
          <HealthImpactExplainer
            originalCalories={originalNutrition?.calories || 0}
            healthierCalories={recipe.nutritionalInfo.calories}
            originalFat={originalNutrition?.fat || 0}
            healthierFat={recipe.nutritionalInfo.fat}
            timesPicked={3} // Placeholder for user history data
            onClose={() => setShowHealthImpact(false)}
          />
        </Modal>
      )}
    </View>
  );
}

// Helper component for displaying nutrition comparison
interface NutritionComparisonItemProps {
  label: string;
  unit: string;
  originalValue?: number;
  newValue: number;
  lowerIsBetter: boolean;
  colors: any; // Pass theme colors
}

function NutritionComparisonItem({ 
  label, 
  unit, 
  originalValue, 
  newValue, 
  lowerIsBetter, 
  colors 
}: NutritionComparisonItemProps) {
  const difference = originalValue !== undefined ? newValue - originalValue : undefined;
  const differenceText = difference !== undefined
    ? `${difference > 0 ? '+' : ''}${difference.toFixed(0)}${unit}`
    : 'N/A'; // Show N/A if original value is missing

  let textColor = colors.text; // Default text color
  if (difference !== undefined && difference !== 0) {
    // Determine if the change is an improvement
    const improved = lowerIsBetter ? difference < 0 : difference > 0;
    textColor = improved ? colors.success : colors.error;
  }

  return (
    <View style={styles.nutritionItem}>
      <Text style={[styles.nutriValue, { color: colors.text }]}>
        {newValue.toFixed(0)}{unit}
      </Text>
      <Text style={[styles.nutriLabel, { color: colors.textSecondary }]}>
        {label}
      </Text>
      {/* Display difference only if original value exists */}
      {originalValue !== undefined && (
        <Text style={[styles.nutriDifference, { color: textColor }]}>
          ({differenceText})
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100, // Increased padding to avoid overlap with footer and pagination
  },
  recipeImageContainer: {
    width: '100%',
    height: 200,
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 20,
    borderWidth: 1,
    position: 'relative',
  },
  recipeImage: {
    width: '100%',
    height: '100%',
  },
  noImageContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  noImageText: {
    marginTop: 12,
    textAlign: 'center',
    fontSize: 14,
  },
  imageOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageOverlayText: {
    color: 'white',
    marginTop: 10,
    fontSize: 14,
    fontWeight: '600',
  },
  refreshImageButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  recipeTitle: {
    fontSize: 24,
    fontWeight: '700',
    flex: 1,
    marginRight: 8,
  },
  focusTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  focusTagText: {
    fontSize: 12,
    fontWeight: '600',
  },
  calorieBadge: {
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
  },
  calorieText: {
    fontSize: 14,
    fontWeight: '700',
  },
  recipeDescription: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 20,
  },
  timeInfo: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  timeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
  },
  timeIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  timeLabel: {
    fontSize: 12,
  },
  timeValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  nutritionCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  nutritionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  nutritionItem: {
    width: '33%',
    marginBottom: 16,
  },
  nutriValue: {
    fontSize: 18,
    fontWeight: '700',
  },
  nutriLabel: {
    fontSize: 12,
    marginTop: 2,
  },
  // Add style for the difference text
  nutriDifference: {
    fontSize: 11,
    fontWeight: '600',
    marginTop: 2,
  },
  benefitsSection: {
    marginBottom: 24,
  },
  benefitsList: {
    marginTop: 8,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    padding: 12,
    borderRadius: 10,
  },
  checkCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  benefitText: {
    fontSize: 14,
    flex: 1,
  },
  ingredientsSection: {
    marginBottom: 24,
  },
  ingredientsCard: {
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
      web: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  ingredientRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 14,
  },
  ingredientNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  ingredientDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 10,
  },
  ingredientName: {
    fontSize: 14,
    fontWeight: '500',
  },
  ingredientAmount: {
    fontSize: 14,
  },
  instructionsSection: {
    marginBottom: 60, // Extra margin to account for the footer
  },
  instructionItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  stepNumberContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  stepNumber: {
    color: 'white',
    fontSize: 12,
    fontWeight: '700',
  },
  instructionText: {
    fontSize: 16,
    flex: 1,
    lineHeight: 24,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    paddingBottom: Platform.OS === 'ios' ? 30 : 16, // Adjust padding for safe area on iOS
    borderTopWidth: 1,
  },
  generateImageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginRight: 8,
    ...Platform.select({
      ios: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
      web: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      }
    }),
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    ...Platform.select({
      ios: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
      web: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      }
    }),
  },
  saveIcon: {
    marginRight: 8,
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16, // Added padding
    marginBottom: 10, // Added margin below dots
  },
  paginationButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  paginationButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  paginationText: {
    fontSize: 14,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.6, // Style for disabled buttons
  },
  refreshButtonText: {
    color: 'white',
    fontSize: 12,
    marginLeft: 4,
    fontWeight: '600',
  },
  explainerButtonsContainer: {
    flexDirection: 'row',
    marginTop: 12,
    marginBottom: 16,
  },
  swapExplainerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    marginRight: 8,
  },
  healthImpactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
  },
  swapButtonIcon: {
    marginRight: 6,
  },
  swapButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  floatingBackButton: {
    position: 'absolute',
    top: 10,
    left: 10,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
});