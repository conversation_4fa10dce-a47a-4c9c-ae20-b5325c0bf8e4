import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  Platform,
  ScrollView,
  Keyboard
} from 'react-native';
import { CameraView, Camera } from 'expo-camera';
import { useTheme } from '@/contexts/ThemeContext';
import { FoodItem } from '@/types/food';
import {
  recognizeBarcodeGlobal,
  ProductRegion,
  getSupportedRegions,
  BarcodeDatabase,
  getGlobalBarcodeConfig,
  setPreferredRegion,
  getBarcodeStatistics,
  setOfflineMode
} from '@/services/globalBarcodeService';
import { useIsFocused } from '@react-navigation/native';
import { Feather , Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import * as Network from 'expo-network';
import { useBarcodeScanner } from '@/hooks/useBarcodeScanner';

interface BarcodeScannerProps {
  onProductFound: (product: FoodItem) => void;
  onClose: () => void;
}

/**
 * Barcode Scanner component
 * Provides a camera interface for scanning various barcode types
 * Now uses the new useBarcodeScanner hook and expo-camera
 */
export function BarcodeScanner({ onProductFound, onClose }: BarcodeScannerProps) {
  const { colors, isDark } = useTheme();
  const [scanned, setScanned] = useState(false);
  const [manualBarcode, setManualBarcode] = useState('');
  const [searching, setSearching] = useState(false);
  const [lastSearchedBarcode, setLastSearchedBarcode] = useState('');
  const [flashOn, setFlashOn] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [selectedRegion, setSelectedRegion] = useState<ProductRegion>(
    getGlobalBarcodeConfig().defaultRegion
  );
  const [offlineMode, setOfflineModeState] = useState(getGlobalBarcodeConfig().offlineMode);
  const [statistics, setStatistics] = useState<{
    cachedBarcodes: number;
    totalLookups: number;
    successRate: number;
  } | null>(null);
  
  const isFocused = useIsFocused();
  const cameraRef = useRef(null);
  
  // Use the new barcode scanner hook
  const {
    hasPermission,
    scanning,
    handleBarcodeScanned,
    startScanning,
    stopScanning,
    requestPermission
  } = useBarcodeScanner(async (barcodeData) => {
    if (scanned || searching) return;
    
    setScanned(true);
    
    // Trigger haptic feedback if available
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    } catch (e) {
      // Haptics not available, ignore
    }
    
    await searchBarcode(barcodeData.data);
  });
  
  // Check network status and update offline mode
  useEffect(() => {
    const checkNetworkStatus = async () => {
      const networkState = await Network.getNetworkStateAsync();
      const isOffline = !networkState.isConnected || !networkState.isInternetReachable;
      
      if (isOffline !== offlineMode) {
        setOfflineModeState(isOffline);
        setOfflineMode(isOffline);
      }
    };
    
    checkNetworkStatus();
    
    // Check network status every 10 seconds
    const interval = setInterval(checkNetworkStatus, 10000);
    
    return () => clearInterval(interval);
  }, [offlineMode]);
  
  // Load statistics
  useEffect(() => {
    const loadStatistics = async () => {
      const stats = await getBarcodeStatistics();
      setStatistics({
        cachedBarcodes: stats.cachedBarcodes,
        totalLookups: stats.totalLookups,
        successRate: stats.successRate
      });
    };
    
    loadStatistics();
  }, []);
  
  // Start scanning when focused
  useEffect(() => {
    if (isFocused && hasPermission && !scanned) {
      startScanning();
    } else {
      stopScanning();
    }
  }, [isFocused, hasPermission, scanned]);
  
  // Handle manual barcode search
  const handleManualSearch = async () => {
    if (!manualBarcode || searching) return;
    
    Keyboard.dismiss();
    await searchBarcode(manualBarcode);
  };
  
  // Search barcode in databases
  const searchBarcode = async (barcode: string) => {
    try {
      setSearching(true);
      setLastSearchedBarcode(barcode);
      
      const result = await recognizeBarcodeGlobal(barcode, selectedRegion);
      
      if (result.success && result.product) {
        onProductFound(result.product);
      } else {
        Alert.alert(
          'Product Not Found',
          `We couldn't find a product with barcode ${barcode}${result.cachedResult ? ' in our cache' : ''}. ${
            result.error ? `\n\nError: ${result.error}` : ''
          }`,
          [
            { 
              text: 'Try Again', 
              onPress: () => {
                setScanned(false);
                setSearching(false);
                startScanning();
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error searching barcode:', error);
      Alert.alert(
        'Error',
        `An error occurred while searching for barcode ${barcode}. Please try again.`,
        [
          { 
            text: 'OK', 
            onPress: () => {
              setScanned(false);
              setSearching(false);
              startScanning();
            }
          }
        ]
      );
    } finally {
      setSearching(false);
    }
  };
  
  // Handle region change
  const handleRegionChange = async (region: ProductRegion) => {
    setSelectedRegion(region);
    await setPreferredRegion(region);
  };
  
  // Toggle flash
  const toggleFlash = () => {
    setFlashOn(!flashOn);
  };
  
  // Reset scan state
  const resetScan = () => {
    setScanned(false);
    setManualBarcode('');
    startScanning();
  };

  // If access to camera is not granted yet
  if (hasPermission === null) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? colors.background : '#f9f9f9' }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.permissionText, { color: colors.text }]}>
          Requesting camera permission...
        </Text>
      </View>
    );
  }

  // If camera permission is denied
  if (hasPermission === false) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? colors.background : '#f9f9f9' }]}>
        <Text style={[styles.permissionText, { color: colors.text }]}>
          No access to camera. Please enable camera permissions in your device settings.
        </Text>
        <TouchableOpacity
          style={[styles.closeButton, { backgroundColor: colors.primary }]}
          onPress={requestPermission}
        >
          <Text style={styles.closeButtonText}>Request Permission</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.closeButton, { backgroundColor: colors.card, marginTop: 10 }]}
          onPress={onClose}
        >
          <Text style={[styles.closeButtonText, { color: colors.text }]}>Close</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: isDark ? colors.background : '#f9f9f9' }]}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.closeIcon} onPress={onClose}>
          <Feather name="x" size={24} color={colors.text} />
        </TouchableOpacity>
        
        <Text style={[styles.title, { color: colors.text }]}>Scan Barcode</Text>
        
        <TouchableOpacity style={styles.settingsIcon} onPress={() => setShowSettings(!showSettings)}>
          <Feather name="settings" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>
      
      {showSettings ? (
        <ScrollView style={styles.settingsContainer}>
          <Text style={[styles.settingsSectionTitle, { color: colors.text }]}>
            Regional Settings
          </Text>
          
          <Text style={[styles.settingsDescription, { color: colors.textSecondary }]}>
            Select your region to improve product recognition accuracy
          </Text>
          
          {getSupportedRegions().map((region) => (
            <TouchableOpacity
              key={region.id}
              style={[
                styles.regionOption,
                { backgroundColor: selectedRegion === region.id ? (isDark ? colors.card : '#e9f5ff') : 'transparent' }
              ]}
              onPress={() => handleRegionChange(region.id)}
            >
              <View style={styles.regionTextContainer}>
                <Text style={[styles.regionName, { color: colors.text }]}>{region.name}</Text>
                <Text style={[styles.regionDescription, { color: colors.textSecondary }]}>
                  {region.description}
                </Text>
              </View>
              
              {selectedRegion === region.id && (
                <Feather name="check" size={20} color={colors.primary} />
              )}
            </TouchableOpacity>
          ))}
          
          <View style={styles.divider} />
          
          <Text style={[styles.settingsSectionTitle, { color: colors.text }]}>
            Barcode Statistics
          </Text>
          
          {statistics ? (
            <View style={styles.statisticsContainer}>
              <View style={styles.statisticItem}>
                <Text style={[styles.statisticValue, { color: colors.primary }]}>
                  {statistics.cachedBarcodes}
                </Text>
                <Text style={[styles.statisticLabel, { color: colors.textSecondary }]}>
                  Cached Products
                </Text>
              </View>
              
              <View style={styles.statisticItem}>
                <Text style={[styles.statisticValue, { color: colors.primary }]}>
                  {statistics.totalLookups}
                </Text>
                <Text style={[styles.statisticLabel, { color: colors.textSecondary }]}>
                  Total Lookups
                </Text>
              </View>
              
              <View style={styles.statisticItem}>
                <Text style={[styles.statisticValue, { color: colors.primary }]}>
                  {statistics.successRate.toFixed(1)}%
                </Text>
                <Text style={[styles.statisticLabel, { color: colors.textSecondary }]}>
                  Success Rate
                </Text>
              </View>
            </View>
          ) : (
            <ActivityIndicator size="small" color={colors.primary} />
          )}
          
          <TouchableOpacity
            style={[styles.doneButton, { backgroundColor: colors.primary }]}
            onPress={() => setShowSettings(false)}
          >
            <Text style={styles.doneButtonText}>Done</Text>
          </TouchableOpacity>
        </ScrollView>
      ) : (
        <>
          <View style={styles.cameraContainer}>
            {isFocused && (
              <CameraView
                style={styles.camera}
                onBarcodeScanned={scanning ? handleBarcodeScanned : undefined}
                enableTorch={flashOn}
                barcodeScannerSettings={{
                  barcodeTypes: ['ean13', 'ean8', 'upc_e', 'upc_a'],
                }}
              >
                <View style={styles.scanFrame}>
                  <View style={styles.scanCorner} />
                  <View style={styles.scanCorner} />
                  <View style={styles.scanCorner} />
                  <View style={styles.scanCorner} />
                </View>
                
                <View style={styles.cameraOverlay}>
                  <TouchableOpacity
                    style={[styles.flashButton, { backgroundColor: isDark ? 'rgba(0,0,0,0.6)' : 'rgba(255,255,255,0.6)' }]}
                    onPress={toggleFlash}
                  >
                    <Feather name={flashOn ? "zap-off" : "zap"} size={24} color={colors.text} />
                  </TouchableOpacity>
                </View>
              </CameraView>
            )}
            
            {searching && (
              <View style={styles.searchingOverlay}>
                <ActivityIndicator size="large" color="white" />
                <Text style={styles.searchingText}>
                  Searching for barcode {lastSearchedBarcode}...
                </Text>
              </View>
            )}
          </View>
          
          <View style={styles.bottomContainer}>
            {offlineMode && (
              <View style={styles.offlineBanner}>
                <Feather name="wifi-off" size={16} color="white" style={styles.offlineIcon} />
                <Text style={styles.offlineText}>Offline Mode - Using cached data only</Text>
              </View>
            )}
            
            <Text style={[styles.instructionText, { color: colors.textSecondary }]}>
              {scanned
                ? 'Tap the button below to scan another barcode'
                : 'Position the barcode within the frame to scan'}
            </Text>
            
            <View style={styles.inputContainer}>
              <TextInput
                style={[
                  styles.barcodeInput,
                  {
                    backgroundColor: isDark ? colors.card : 'white',
                    color: colors.text,
                    borderColor: colors.border
                  }
                ]}
                placeholder="Enter barcode manually"
                placeholderTextColor={colors.textSecondary}
                value={manualBarcode}
                onChangeText={setManualBarcode}
                keyboardType="number-pad"
                editable={!searching}
              />
              
              <TouchableOpacity
                style={[
                  styles.searchButton,
                  { backgroundColor: colors.primary },
                  (!manualBarcode || searching) && { opacity: 0.6 }
                ]}
                onPress={handleManualSearch}
                disabled={!manualBarcode || searching}
              >
                <Feather name="search" size={20} color="white" />
              </TouchableOpacity>
            </View>
            
            {scanned && !searching && (
              <TouchableOpacity
                style={[styles.scanAgainButton, { backgroundColor: colors.primary }]}
                onPress={resetScan}
              >
                <Text style={styles.scanAgainButtonText}>Scan Again</Text>
              </TouchableOpacity>
            )}
          </View>
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 50 : 16,
    paddingBottom: 16,
    position: 'relative',
    zIndex: 10,
  },
  closeIcon: {
    position: 'absolute',
    left: 16,
    padding: 8,
  },
  settingsIcon: {
    position: 'absolute',
    right: 16,
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  permissionText: {
    textAlign: 'center',
    marginHorizontal: 32,
    marginVertical: 16,
    fontSize: 16,
    lineHeight: 24,
  },
  cameraContainer: {
    flex: 1,
    overflow: 'hidden',
    position: 'relative',
  },
  camera: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanFrame: {
    width: 280,
    height: 280,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 16,
    position: 'relative',
  },
  scanCorner: {
    width: 20,
    height: 20,
    borderColor: 'white',
    position: 'absolute',
  },
  cameraOverlay: {
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  flashButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 5,
  },
  searchingText: {
    color: 'white',
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
  bottomContainer: {
    paddingHorizontal: 16,
    paddingVertical: 24,
  },
  offlineBanner: {
    backgroundColor: '#ff3b30',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 16,
  },
  offlineIcon: {
    marginRight: 8,
  },
  offlineText: {
    color: 'white',
    fontWeight: '500',
  },
  instructionText: {
    textAlign: 'center',
    marginBottom: 16,
    fontSize: 14,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  barcodeInput: {
    flex: 1,
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    marginRight: 8,
  },
  searchButton: {
    width: 50,
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanAgainButton: {
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanAgainButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  closeButton: {
    marginTop: 16,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignSelf: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  settingsContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  settingsSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  settingsDescription: {
    fontSize: 14,
    marginBottom: 16,
  },
  regionOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  regionTextContainer: {
    flex: 1,
  },
  regionName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  regionDescription: {
    fontSize: 14,
  },
  divider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginVertical: 24,
  },
  statisticsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 16,
  },
  statisticItem: {
    alignItems: 'center',
    flex: 1,
  },
  statisticValue: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  statisticLabel: {
    fontSize: 12,
  },
  doneButton: {
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24,
  },
  doneButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});