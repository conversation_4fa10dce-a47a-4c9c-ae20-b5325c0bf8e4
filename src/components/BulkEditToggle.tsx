import React from 'react';
import { TouchableOpacity, Text, StyleSheet, Platform } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useBulkEdit } from './BulkEditProvider';


interface BulkEditToggleProps {
  /**
   * Style for the button
   */
  style?: any;
  
  /**
   * Label to show on the button
   */
  label?: string;
  
  /**
   * Whether to show an icon
   */
  showIcon?: boolean;
  
  /**
   * Label to show when bulk edit is active
   */
  activeLabel?: string;
}

/**
 * Button component to toggle bulk edit mode
 */
export function BulkEditToggle({
  style,
  label = 'Select',
  showIcon = true,
  activeLabel = 'Cancel',
}: BulkEditToggleProps) {
  const { colors, isDark } = useTheme();
  const { isActive, toggleBulkEdit } = useBulkEdit();
  
  return (
    <TouchableOpacity
      style={[
        styles.button,
        {
          backgroundColor: isActive
            ? `${colors.primary}20`
            : isDark
              ? colors.card
              : '#F5F5F5',
        },
        style,
      ]}
      onPress={toggleBulkEdit}
    >
      {showIcon && (
        <CheckSquare
          size={16}
          color={isActive ? colors.primary : colors.textSecondary}
          style={styles.icon}
        />
      )}
      
      <Text
        style={[
          styles.label,
          {
            color: isActive ? colors.primary : colors.textSecondary,
            fontWeight: isActive ? '600' : '500',
          },
        ]}
      >
        {isActive ? activeLabel : label}
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  icon: {
    marginRight: 6,
  },
  label: {
    fontSize: 14,
  },
});

export default BulkEditToggle; 