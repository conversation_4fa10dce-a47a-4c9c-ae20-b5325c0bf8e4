import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
  Platform,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useBulkEdit } from './BulkEditProvider';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface BulkEditAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  onPress: () => void;
  destructive?: boolean;
}

interface BulkEditToolbarProps {
  /**
   * Title to display
   */
  title?: string;
  
  /**
   * Custom actions to display
   */
  actions?: BulkEditAction[];
  
  /**
   * Callback when delete is pressed
   */
  onDelete?: () => Promise<void>;
  
  /**
   * Callback when update is pressed
   */
  onUpdate?: () => void;
  
  /**
   * Whether to show the delete action
   */
  showDelete?: boolean;
  
  /**
   * Whether to show the update action
   */
  showUpdate?: boolean;
}

/**
 * Toolbar component that appears when bulk edit mode is active
 */
export function BulkEditToolbar<T>({
  title = 'Bulk Edit',
  actions = [],
  onDelete,
  onUpdate,
  showDelete = true,
  showUpdate = true,
}: BulkEditToolbarProps) {
  const { colors, isDark } = useTheme();
  const insets = useSafeAreaInsets();
  const { isActive, selectedCount, toggleBulkEdit, deselectAll } = useBulkEdit();
  
  // State for loading state
  const [isDeleting, setIsDeleting] = useState(false);
  
  // Animation value for slide-in effect
  const [slideAnim] = useState(new Animated.Value(-100));
  
  // Start slide-in animation when toolbar becomes active
  React.useEffect(() => {
    if (isActive) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: -100,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  }, [isActive, slideAnim]);
  
  // Handle delete action
  const handleDelete = async () => {
    if (!onDelete) return;
    
    setIsDeleting(true);
    try {
      await onDelete();
    } catch (error) {
      console.error('Error deleting items:', error);
    } finally {
      setIsDeleting(false);
    }
  };
  
  // If not in bulk edit mode, don't render
  if (!isActive) {
    return null;
  }
  
  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: isDark ? colors.card : '#FFFFFF',
          paddingBottom: Math.max(insets.bottom, 16),
          transform: [{ translateY: slideAnim }],
          borderTopColor: colors.border,
        },
      ]}
    >
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          {selectedCount > 0 
            ? `${selectedCount} item${selectedCount !== 1 ? 's' : ''} selected` 
            : title}
        </Text>
        
        <TouchableOpacity
          style={styles.closeButton}
          onPress={toggleBulkEdit}
        >
          <Feather name="x-circle" size={24} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
      
      <View style={styles.actions}>
        {/* Cancel selection */}
        <TouchableOpacity
          style={styles.actionButton}
          onPress={deselectAll}
          disabled={selectedCount === 0}
        >
          <Feather name="x-circle" size={20} color={selectedCount === 0 ? colors.textSecondary : colors.textSecondary} />
          <Text
            style={[
              styles.actionText,
              {
                color: selectedCount === 0 ? colors.textSecondary : colors.textSecondary,
              },
            ]}
          >
            Clear
          </Text>
        </TouchableOpacity>
        
        {/* Update action */}
        {showUpdate && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={onUpdate}
            disabled={selectedCount === 0 || !onUpdate}
          >
            <Feather name="edit" size={20} color={selectedCount === 0 ? colors.textSecondary : colors.primary} />
            <Text
              style={[
                styles.actionText,
                {
                  color: selectedCount === 0 ? colors.textSecondary : colors.primary,
                },
              ]}
            >
              Update
            </Text>
          </TouchableOpacity>
        )}
        
        {/* Delete action */}
        {showDelete && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleDelete}
            disabled={selectedCount === 0 || isDeleting || !onDelete}
          >
            {isDeleting ? (
              <ActivityIndicator size="small" color={colors.error} />
            ) : (
              <Feather name="trash-2" size={20} color={selectedCount === 0 ? colors.textSecondary : colors.error} />
            )}
            <Text
              style={[
                styles.actionText,
                {
                  color: selectedCount === 0 ? colors.textSecondary : colors.error,
                },
              ]}
            >
              Delete
            </Text>
          </TouchableOpacity>
        )}
        
        {/* Custom actions */}
        {actions.map(action => (
          <TouchableOpacity
            key={action.id}
            style={styles.actionButton}
            onPress={action.onPress}
            disabled={selectedCount === 0}
          >
            {action.icon}
            <Text
              style={[
                styles.actionText,
                {
                  color: selectedCount === 0
                    ? colors.textSecondary
                    : action.destructive
                      ? colors.error
                      : colors.primary,
                },
              ]}
            >
              {action.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    paddingTop: 16,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
    zIndex: 1000,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  actionButton: {
    alignItems: 'center',
    padding: 8,
  },
  actionText: {
    fontSize: 12,
    marginTop: 4,
  },
});

export default BulkEditToolbar; 