import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Dimensions } from 'react-native';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { Circle, Defs, LinearGradient as SvgLinearGradient, Stop, RadialGradient, Path, G, Rect, Text as SvgText } from 'react-native-svg';

interface MacroData {
  protein: number;
  carbs: number;
  fat: number;
}

interface MealBreakdown {
  breakfast: number;
  lunch: number;
  dinner: number;
  snacks: number;
}

interface CalorieProgressCardProps {
  calories?: number;
  target?: number;
  macros?: MacroData;
  mealBreakdown?: MealBreakdown;
  weeklyData?: {day: string, calories: number, target: number}[];
  activeCalories?: number;
  restingCalories?: number;
  totalCalories?: number;
  goalCalories?: number;
}

// Define gradient colors based on percentage
function getGradientColors(percent: number): [string, string] {
  if (percent > 100) return ['#8B5CF6', '#6D28D9']; // Purple gradient (Over)
  if (percent > 80) return ['#10B981', '#059669']; // Green gradient (Good)
  if (percent > 50) return ['#06B6D4', '#0891B2']; // Teal gradient (Okay)
  return ['#3B82F6', '#2563EB']; // Blue gradient (Starting)
}

export function CalorieProgressCard({ 
  calories = 0, 
  target = 2000, 
  macros = { protein: 0, carbs: 0, fat: 0 }, 
  mealBreakdown = { breakfast: 300, lunch: 450, dinner: 600, snacks: 150 },
  weeklyData = [
    { day: 'Mon', calories: 1800, target: 2000 },
    { day: 'Tue', calories: 1950, target: 2000 },
    { day: 'Wed', calories: 1760, target: 2000 },
    { day: 'Thu', calories: 2100, target: 2000 },
    { day: 'Fri', calories: 2050, target: 2000 },
    { day: 'Sat', calories: 1600, target: 2000 },
    { day: 'Sun', calories: 0, target: 2000 },
  ],
  activeCalories = 0,
  restingCalories = 0,
  totalCalories = 0,
  goalCalories = 0
}: CalorieProgressCardProps) {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  
  const actualCalories = totalCalories || calories;
  const actualTarget = goalCalories || target;
  
  const updatedWeeklyData = [...weeklyData];
  if (updatedWeeklyData.length > 0) {
    updatedWeeklyData[updatedWeeklyData.length - 1] = {
      ...updatedWeeklyData[updatedWeeklyData.length - 1],
      calories: actualCalories,
      target: actualTarget
    };
  }
  
  const caloriePercentage = Math.round((actualCalories / actualTarget) * 100);
  const clampedPercentage = Math.min(caloriePercentage, 100); // Clamp for progress display
  const progressAngle = (clampedPercentage / 100) * 360;
  const gradientId = `calorieProgressGradient-${caloriePercentage}`; // Unique ID based on percentage
  const glowId = `calorieProgressGlow-${caloriePercentage}`; // Unique ID for glow
  const gradientColors = getGradientColors(caloriePercentage);
  
  // Calculate total macros in grams and calories
  const totalMacrosGrams = macros.protein + macros.carbs + macros.fat;
  const macroCalories = {
    protein: macros.protein * 4, // 4 calories per gram
    carbs: macros.carbs * 4,     // 4 calories per gram
    fat: macros.fat * 9,         // 9 calories per gram
  };
  const totalMacroCalories = macroCalories.protein + macroCalories.carbs + macroCalories.fat;
  
  // Calculate macro percentages (of total macros)
  const macroPercent = {
    protein: Math.round((macros.protein / totalMacrosGrams) * 100) || 0,
    carbs: Math.round((macros.carbs / totalMacrosGrams) * 100) || 0,
    fat: Math.round((macros.fat / totalMacrosGrams) * 100) || 0,
  };
  
  // Calculate calorie percentages (of total calories)
  const caloriePercent = {
    protein: Math.round((macroCalories.protein / totalMacroCalories) * 100) || 0,
    carbs: Math.round((macroCalories.carbs / totalMacroCalories) * 100) || 0,
    fat: Math.round((macroCalories.fat / totalMacroCalories) * 100) || 0,
  };
  
  // Define color identity for different macro elements
  const macroColors = {
    protein: {
      primary: '#3B82F6',
      secondary: '#60A5FA',
      light: isDark ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'
    },
    carbs: {
      primary: '#8B5CF6',
      secondary: '#A78BFA',
      light: isDark ? 'rgba(139, 92, 246, 0.2)' : 'rgba(139, 92, 246, 0.1)'
    },
    fat: {
      primary: '#F97316',
      secondary: '#FB923C',
      light: isDark ? 'rgba(249, 115, 22, 0.2)' : 'rgba(249, 115, 22, 0.1)'
    }
  };
  
  // Calculate trend compared to yesterday
  const yesterday = weeklyData[weeklyData.length - 2];
  const calorieChange = actualCalories - yesterday.calories;
  const changePercent = Math.round((calorieChange / yesterday.calories) * 100);
  
  // Helper functions for progress colors
  function getProgressBadgeColor(percent: number, isDark: boolean) {
    if (percent > 100) return isDark ? '#4338CA20' : '#EEF2FF';
    if (percent > 80) return isDark ? '#15803D20' : '#DCFCE7';
    if (percent > 50) return isDark ? '#0D948820' : '#CCFBF1';
    return isDark ? '#0369A120' : '#E0F2FE';
  }
  
  function getProgressTextColor(percent: number) {
    if (percent > 100) return '#6366F1';
    if (percent > 80) return '#16A34A';
    if (percent > 50) return '#14B8A6';
    return '#0EA5E9';
  }
  
  const circleRadius = 70;
  const strokeWidth = 10;
  const circumference = 2 * Math.PI * circleRadius;
  const strokeDashoffset = circumference - (clampedPercentage / 100) * circumference;
  
  // Get status message based on percentage
  const getStatusMessage = () => {
    if (caloriePercentage > 105) return "Over target";
    if (caloriePercentage > 95) return "On target";
    if (caloriePercentage > 60) return "Good progress";
    if (caloriePercentage > 30) return "Making progress";
    return "Just started";
  };

  return (
    <LinearGradient
      colors={isDark ? 
        [colors.card, 'rgba(30, 41, 59, 0.8)'] : 
        [colors.card, '#F1F5F9']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={[
        styles.progressCard,
        {
          borderColor: colors.border,
          shadowColor: '#000',
        }
      ]}
    >
      {/* Header Section */}
      <View style={styles.cardSection}>
        <View style={styles.progressHeader}>
          <View style={styles.titleContainer}>
            <LinearGradient
              colors={gradientColors}
              style={styles.iconBubble}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <MaterialIcons name="local-fire-department" size={20}  color={colors.text} />
            </LinearGradient>
            <View>
              <Text style={[styles.progressTitle, { color: colors.text }]}>Today's Progress</Text>
              <Text style={[styles.progressSubtitle, { color: colors.textSecondary }]}>
                {getStatusMessage()}
              </Text>
            </View>
          </View>
          <View style={[styles.progressBadge, { 
            backgroundColor: getProgressBadgeColor(caloriePercentage, isDark) 
          }]}>
            <Text style={[styles.progressBadgeText, { 
              color: getProgressTextColor(caloriePercentage) 
            }]}>
              {caloriePercentage}% of goal
            </Text>
          </View>
        </View>
      </View>
      
      {/* Main Content Section */}
      <View style={styles.cardSection}>
        <View style={styles.mainContent}>
          {/* Left column - Circular Progress */}
          <View style={styles.circleContainer}>
            <Svg width={circleRadius * 2 + strokeWidth * 2} height={circleRadius * 2 + strokeWidth * 2} viewBox={`0 0 ${circleRadius * 2 + strokeWidth * 2} ${circleRadius * 2 + strokeWidth * 2}`}>
              <Defs>
                <SvgLinearGradient id={gradientId} x1="0%" y1="0%" x2="100%" y2="100%">
                  <Stop offset="0%" stopColor={gradientColors[0]} />
                  <Stop offset="100%" stopColor={gradientColors[1]} />
                </SvgLinearGradient>
                <RadialGradient 
                  id={glowId} 
                  cx="50%" 
                  cy="50%" 
                  rx="50%" 
                  ry="50%" 
                  fx="50%" 
                  fy="50%" 
                  gradientUnits="userSpaceOnUse"
                >
                  <Stop
                    offset="0%"
                    stopColor={gradientColors[0]}
                    stopOpacity="0.7"
                  />
                  <Stop
                    offset="100%"
                    stopColor={gradientColors[0]}
                    stopOpacity="0"
                  />
                </RadialGradient>
              </Defs>
              
              {/* Glow effect */}
              {caloriePercentage > 20 && (
                <Circle
                  cx={circleRadius + strokeWidth}
                  cy={circleRadius + strokeWidth}
                  r={circleRadius + strokeWidth/2}
                  fill={`url(#${glowId})`}
                  opacity={0.25}
                />
              )}
              
              {/* Background Circle */}
              <Circle
                cx={circleRadius + strokeWidth}
                cy={circleRadius + strokeWidth}
                r={circleRadius}
                stroke={isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.08)'}
                strokeWidth={strokeWidth - 2}
                strokeLinecap="round"
                fill="none"
              />
              
              {/* Progress Circle */}
              <Circle
                cx={circleRadius + strokeWidth}
                cy={circleRadius + strokeWidth}
                r={circleRadius}
                stroke={`url(#${gradientId})`} // Apply gradient
                strokeWidth={strokeWidth}
                fill="none"
                strokeDasharray={circumference}
                strokeDashoffset={strokeDashoffset}
                strokeLinecap="round"
                transform={`rotate(-90 ${circleRadius + strokeWidth} ${circleRadius + strokeWidth})`} // Start from top
              />
            </Svg>
            
            {/* Text Content Inside Circle */}
            <View style={styles.calorieCircleContent}>
              <Text style={[styles.calorieCircleValue, { color: isDark ? 'white' : '#1E293B' }]}>
                {actualCalories}
              </Text>
              <Text style={[styles.calorieCircleLabel, { color: colors.textSecondary }]}>
                of {actualTarget} cal
              </Text>
              {calorieChange !== 0 && (
                <View style={[styles.trendBadge, { 
                  backgroundColor: calorieChange > 0 ? 
                    (isDark ? 'rgba(239, 68, 68, 0.2)' : 'rgba(239, 68, 68, 0.1)') : 
                    (isDark ? 'rgba(16, 185, 129, 0.2)' : 'rgba(16, 185, 129, 0.1)')
                }]}>
                  {calorieChange > 0 ? (
                    <Feather name="arrow-up-right" size={12} color="#EF4444" style={{marginRight: 2}} />
                  ) : (
                    <Feather name="arrow-down-right" size={12} color="#10B981" style={{marginRight: 2}} />
                  )}
                  <Text style={[styles.trendText, { 
                    color: calorieChange > 0 ? '#EF4444' : '#10B981'
                  }]}>
                    {Math.abs(changePercent)}% vs yesterday
                  </Text>
                </View>
              )}
            </View>
          </View>
          
          {/* Right column - Weekly Trend */}
          <View style={styles.trendContainer}>
            <Text style={[styles.trendTitle, { color: colors.text }]}>Weekly Trend</Text>
            <Svg width="100%" height={120} style={styles.trendChart}>
              <G>
                {/* Background line */}
                <Path 
                  d={`M10,60 L${weeklyData.length * 35 - 10},60`} 
                  stroke={isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'} 
                  strokeWidth="1" 
                  strokeDasharray="5,5"
                />
                
                {/* Data points and connections */}
                <Path 
                  d={weeklyData.map((day, i) => {
                    const x = 15 + i * 35;
                    // Handle potential division by zero
                    const ratio = day.target > 0 ? (day.calories / day.target) : 0.5;
                    const y = 100 - (ratio * 70);
                    // Ensure y is a valid number
                    const validY = isNaN(y) || !isFinite(y) ? 60 : y;
                    return `${i === 0 ? 'M' : 'L'}${x},${validY}`;
                  }).join(' ')}
                  stroke={gradientColors[0]}
                  strokeWidth="2.5"
                  fill="none"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                
                {/* Draw day labels */}
                {weeklyData.map((day, i) => {
                  const x = 15 + i * 35;
                  // Handle potential division by zero
                  const ratio = day.target > 0 ? (day.calories / day.target) : 0.5;
                  const y = 100 - (ratio * 70);
                  // Ensure y is a valid number
                  const validY = isNaN(y) || !isFinite(y) ? 60 : y;
                  
                  return (
                    <G key={i}>
                      <Circle 
                        cx={x} 
                        cy={validY} 
                        r={i === weeklyData.length - 1 ? 4 : 3}
                        fill={i === weeklyData.length - 1 ? gradientColors[0] : 'white'}
                        stroke={gradientColors[0]}
                        strokeWidth="1.5"
                      />
                      <SvgText 
                        x={x} 
                        y={105} 
                        fontSize="10"
                        fill={i === weeklyData.length - 1 ? gradientColors[0] : colors.textSecondary}
                        fontWeight={i === weeklyData.length - 1 ? "bold" : "normal"}
                        textAnchor="middle"
                      >
                        {day.day}
                      </SvgText>
                    </G>
                  );
                })}
              </G>
            </Svg>
          </View>
        </View>
      </View>
      
      {/* Macro Section with Improved Visualization */}
      <View style={styles.cardSection}>
        <View style={styles.macroSection}>
          <View style={styles.sectionTitleRow}>
            <Text style={[styles.macroSectionTitle, { color: colors.textSecondary }]}>
              Macronutrients
            </Text>
            <Text style={[styles.macroCalorieText, { color: colors.textSecondary }]}>
              {totalMacroCalories} cal tracked
            </Text>
          </View>
          
          {/* Macro Distribution Bar */}
          <View style={styles.macroBarContainer}>
            <View style={styles.macroBar}>
              <View 
                style={[
                  styles.macroBarSegment, 
                  { 
                    backgroundColor: macroColors.protein.primary,
                    width: `${caloriePercent.protein}%` 
                  }
                ]} 
              />
              <View 
                style={[
                  styles.macroBarSegment, 
                  { 
                    backgroundColor: macroColors.carbs.primary,
                    width: `${caloriePercent.carbs}%` 
                  }
                ]} 
              />
              <View 
                style={[
                  styles.macroBarSegment, 
                  { 
                    backgroundColor: macroColors.fat.primary,
                    width: `${caloriePercent.fat}%` 
                  }
                ]} 
              />
            </View>
          </View>
          
          {/* Macro Items */}
          <View style={styles.macroGrid}>
            {/* Protein */}
            <View style={styles.macroGridItem}>
              <View style={[styles.macroCircle, { backgroundColor: macroColors.protein.primary }]} />
              <View style={styles.macroContent}>
                <View style={styles.macroLabelRow}>
                  <Text style={[styles.macroLabel, { color: colors.text }]}>
                    Protein
                  </Text>
                  <Text style={[styles.macroPercentage, { color: macroColors.protein.primary }]}>
                    {macroPercent.protein}%
                  </Text>
                </View>
                <View style={styles.macroValueRow}>
                  <Text style={[styles.macroValue, { color: colors.text }]}>
                    {macros.protein}g
                  </Text>
                  <Text style={[styles.macroCals, { color: colors.textSecondary }]}>
                    {macroCalories.protein} cal
                  </Text>
                </View>
              </View>
            </View>
            
            {/* Carbs */}
            <View style={styles.macroGridItem}>
              <View style={[styles.macroCircle, { backgroundColor: macroColors.carbs.primary }]} />
              <View style={styles.macroContent}>
                <View style={styles.macroLabelRow}>
                  <Text style={[styles.macroLabel, { color: colors.text }]}>
                    Carbs
                  </Text>
                  <Text style={[styles.macroPercentage, { color: macroColors.carbs.primary }]}>
                    {macroPercent.carbs}%
                  </Text>
                </View>
                <View style={styles.macroValueRow}>
                  <Text style={[styles.macroValue, { color: colors.text }]}>
                    {macros.carbs}g
                  </Text>
                  <Text style={[styles.macroCals, { color: colors.textSecondary }]}>
                    {macroCalories.carbs} cal
                  </Text>
                </View>
              </View>
            </View>
            
            {/* Fat */}
            <View style={styles.macroGridItem}>
              <View style={[styles.macroCircle, { backgroundColor: macroColors.fat.primary }]} />
              <View style={styles.macroContent}>
                <View style={styles.macroLabelRow}>
                  <Text style={[styles.macroLabel, { color: colors.text }]}>
                    Fat
                  </Text>
                  <Text style={[styles.macroPercentage, { color: macroColors.fat.primary }]}>
                    {macroPercent.fat}%
                  </Text>
                </View>
                <View style={styles.macroValueRow}>
                  <Text style={[styles.macroValue, { color: colors.text }]}>
                    {macros.fat}g
                  </Text>
                  <Text style={[styles.macroCals, { color: colors.textSecondary }]}>
                    {macroCalories.fat} cal
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>
      
      {/* Meal Breakdown Section */}
      <View style={styles.cardSection}>
        <View style={styles.mealSection}>
          <Text style={[styles.mealSectionTitle, { color: colors.textSecondary }]}>
            Meal Breakdown
          </Text>
          
          <View style={styles.mealBreakdownContainer}>
            {/* Breakfast */}
            <View style={[styles.mealItem, { width: `${(mealBreakdown.breakfast / actualCalories) * 100}%` }]}>
              <LinearGradient
                colors={['#F59E0B', '#D97706']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.mealItemGradient}
              >
                <Text style={styles.mealItemText}>B</Text>
              </LinearGradient>
              <Text style={[styles.mealItemValue, { color: colors.textSecondary }]}>{mealBreakdown.breakfast}</Text>
            </View>
            
            {/* Lunch */}
            <View style={[styles.mealItem, { width: `${(mealBreakdown.lunch / actualCalories) * 100}%` }]}>
              <LinearGradient
                colors={['#10B981', '#059669']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.mealItemGradient}
              >
                <Text style={styles.mealItemText}>L</Text>
              </LinearGradient>
              <Text style={[styles.mealItemValue, { color: colors.textSecondary }]}>{mealBreakdown.lunch}</Text>
            </View>
            
            {/* Dinner */}
            <View style={[styles.mealItem, { width: `${(mealBreakdown.dinner / actualCalories) * 100}%` }]}>
              <LinearGradient
                colors={['#3B82F6', '#2563EB']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.mealItemGradient}
              >
                <Text style={styles.mealItemText}>D</Text>
              </LinearGradient>
              <Text style={[styles.mealItemValue, { color: colors.textSecondary }]}>{mealBreakdown.dinner}</Text>
            </View>
            
            {/* Snacks */}
            <View style={[styles.mealItem, { width: `${(mealBreakdown.snacks / actualCalories) * 100}%` }]}>
              <LinearGradient
                colors={['#8B5CF6', '#7C3AED']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.mealItemGradient}
              >
                <Text style={styles.mealItemText}>S</Text>
              </LinearGradient>
              <Text style={[styles.mealItemValue, { color: colors.textSecondary }]}>{mealBreakdown.snacks}</Text>
            </View>
          </View>
          
          <View style={styles.mealLegend}>
            <View style={styles.legendItem}>
              <View style={[styles.legendDot, { backgroundColor: '#F59E0B' }]} />
              <Text style={[styles.legendText, { color: colors.textSecondary }]}>Breakfast</Text>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.legendDot, { backgroundColor: '#10B981' }]} />
              <Text style={[styles.legendText, { color: colors.textSecondary }]}>Lunch</Text>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.legendDot, { backgroundColor: '#3B82F6' }]} />
              <Text style={[styles.legendText, { color: colors.textSecondary }]}>Dinner</Text>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.legendDot, { backgroundColor: '#8B5CF6' }]} />
              <Text style={[styles.legendText, { color: colors.textSecondary }]}>Snacks</Text>
            </View>
          </View>
        </View>
      </View>
      
      {/* Action Buttons */}
      <View style={[styles.cardSection, styles.actionSection]}>
        <TouchableOpacity 
          style={[styles.actionButton, { 
            backgroundColor: gradientColors[0] + '20',
            borderWidth: 1,
            borderColor: gradientColors[0] + '30'
          }]}
          onPress={() => router.push('/(tabs)/scan')}
          accessibilityLabel="Log your next meal"
          accessibilityHint="Navigate to the scan screen to log your next meal"
        >
          <MaterialIcons name="restaurant" size={16} color={gradientColors[0]} style={{marginRight: 6}} />
          <Text style={[styles.actionButtonText, { color: gradientColors[0] }]}>Log your next meal</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.detailsButton}
          accessibilityLabel="Nutrition Details"
          accessibilityHint="View detailed nutritional information"
        >
          <Text style={[styles.detailsButtonText, { color: colors.primary }]}>Nutrition Details</Text>
          <Feather name="arrow-right" size={14} color={colors.primary} />
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  progressCard: {
    borderRadius: 24,
    padding: 0,
    marginHorizontal: 0,
    marginBottom: 24,
    borderWidth: 1,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 6,
    overflow: 'hidden',
    width: '100%',
  },
  cardSection: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(100,116,139,0.1)',
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconBubble: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  progressTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  progressSubtitle: {
    fontSize: 13,
    marginTop: 2,
  },
  progressBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  progressBadgeText: {
    fontSize: 14,
    fontWeight: '600',
  },
  mainContent: {
    flexDirection: 'row',
  },
  circleContainer: {
    width: '50%',
    alignItems: 'center',
    position: 'relative',
  },
  calorieCircleContent: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  calorieCircleValue: {
    fontSize: 36,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  calorieCircleLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  trendBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 8,
  },
  trendText: {
    fontSize: 10,
    fontWeight: '600',
  },
  trendContainer: {
    width: '50%',
    paddingLeft: 10,
  },
  trendTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  trendChart: {
    height: 120,
  },
  macroSection: {
    marginTop: 0,
  },
  sectionTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  macroSectionTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  macroCalorieText: {
    fontSize: 12,
  },
  macroBarContainer: {
    marginBottom: 16,
  },
  macroBar: {
    height: 8,
    borderRadius: 4,
    flexDirection: 'row',
    overflow: 'hidden',
  },
  macroBarSegment: {
    height: '100%',
  },
  macroGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -5,
  },
  macroGridItem: {
    width: '33.33%',
    paddingHorizontal: 5,
    marginBottom: 5,
  },
  macroCircle: {
    width: 10,
    height: 10,
    borderRadius: 5,
    position: 'absolute',
    top: 5,
    left: 0,
  },
  macroContent: {
    paddingLeft: 18,
  },
  macroLabelRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  macroLabel: {
    fontSize: 13,
    fontWeight: '600',
  },
  macroPercentage: {
    fontSize: 12,
    fontWeight: '700',
  },
  macroValueRow: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginTop: 2,
  },
  macroValue: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 6,
  },
  macroCals: {
    fontSize: 11,
  },
  mealSection: {
    marginTop: 0,
  },
  mealSectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
  },
  mealBreakdownContainer: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  mealItem: {
    alignItems: 'center',
    paddingHorizontal: 3,
  },
  mealItemGradient: {
    width: 26,
    height: 26,
    borderRadius: 13,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  mealItemText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '700',
  },
  mealItemValue: {
    fontSize: 10,
  },
  mealLegend: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
    marginBottom: 4,
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  legendText: {
    fontSize: 11,
  },
  actionSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 0,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  detailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailsButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 4,
  },
}); 