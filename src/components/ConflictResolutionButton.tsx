import React, { useState } from 'react';
import { TouchableOpacity, StyleSheet, Text, View } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useDatabase } from '@/contexts/DatabaseContext';
import { Feather } from '@expo/vector-icons';
import ConflictResolutionModal from './ConflictResolutionModal';

export const ConflictResolutionButton = () => {
  const { colors } = useTheme();
  const { hasConflicts, refreshConflicts } = useDatabase();
  const [modalVisible, setModalVisible] = useState(false);

  const openModal = () => {
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
  };

  const handleResolved = async () => {
    // After resolving, check if there are more conflicts
    const conflicts = await refreshConflicts();
    if (conflicts.length === 0) {
      closeModal();
    }
  };

  if (!hasConflicts) {
    return null;
  }

  return (
    <>
      <TouchableOpacity
        style={[styles.button, { backgroundColor: colors.error }]}
        onPress={openModal}
      >
        <Feather name="alert-triangle" size={24}  color={colors.text} />
        <Text style={styles.text}>Resolve Conflicts</Text>
      </TouchableOpacity>

      <ConflictResolutionModal
        visible={modalVisible}
        onClose={closeModal}
        onResolved={handleResolved}
      />
    </>
  );
};

const styles = StyleSheet.create({
  button: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    borderRadius: 28,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 1000,
  },
  text: {
    color: 'white',
    marginLeft: 8,
    fontWeight: 'bold',
  },
});

export default ConflictResolutionButton; 