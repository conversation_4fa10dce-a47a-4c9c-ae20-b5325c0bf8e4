import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { DataConflict, ConflictResolutionStrategy, resolveConflict, getUnresolvedConflicts } from '@/services/databaseService';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { format } from 'date-fns';

interface ConflictResolutionModalProps {
  visible: boolean;
  onClose: () => void;
  onResolved?: () => void;
}

export const ConflictResolutionModal: React.FC<ConflictResolutionModalProps> = ({
  visible,
  onClose,
  onResolved
}) => {
  const { colors } = useTheme();
  const [conflicts, setConflicts] = useState<DataConflict[]>([]);
  const [loading, setLoading] = useState(true);
  const [resolving, setResolving] = useState(false);
  const [selectedConflict, setSelectedConflict] = useState<DataConflict | null>(null);

  // Load conflicts when modal opens
  useEffect(() => {
    if (visible) {
      loadConflicts();
    }
  }, [visible]);

  const loadConflicts = async () => {
    setLoading(true);
    try {
      const unresolvedConflicts = await getUnresolvedConflicts();
      setConflicts(unresolvedConflicts);
    } catch (error) {
      console.error('Error loading conflicts:', error);
      Alert.alert('Error', 'Failed to load data conflicts');
    } finally {
      setLoading(false);
    }
  };

  // Handle conflict resolution
  const handleResolveConflict = async (conflictId: string, strategy: ConflictResolutionStrategy) => {
    setResolving(true);
    try {
      const result = await resolveConflict(conflictId, strategy);
      if (result.success) {
        // Remove resolved conflict from the list
        setConflicts(prevConflicts => prevConflicts.filter(c => c.id !== conflictId));
        setSelectedConflict(null);
        
        if (typeof onResolved === 'function') {
          onResolved();
        }
      } else {
        Alert.alert('Error', `Failed to resolve conflict: ${result.error}`);
      }
    } catch (error) {
      console.error('Error resolving conflict:', error);
      Alert.alert('Error', 'An unexpected error occurred while resolving the conflict');
    } finally {
      setResolving(false);
    }
  };

  // Function to render differences between local and server data
  const renderDifferences = (conflict: DataConflict) => {
    const { localData, serverData, tableName } = conflict;
    
    if (tableName === 'meals') {
      return (
        <View style={styles.comparisonContainer}>
          <View style={styles.comparisonColumn}>
            <Text style={[styles.columnHeader, { color: colors.primary }]}>Your changes</Text>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>Name: {localData.name}</Text>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>Date: {localData.date}</Text>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>Time: {localData.time}</Text>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>Calories: {localData.total_calories}</Text>
            {localData.total_protein && (
              <Text style={[styles.fieldLabel, { color: colors.text }]}>Protein: {localData.total_protein}g</Text>
            )}
            {localData.total_carbs && (
              <Text style={[styles.fieldLabel, { color: colors.text }]}>Carbs: {localData.total_carbs}g</Text>
            )}
            {localData.total_fat && (
              <Text style={[styles.fieldLabel, { color: colors.text }]}>Fat: {localData.total_fat}g</Text>
            )}
          </View>
          
          <View style={styles.comparisonColumn}>
            <Text style={[styles.columnHeader, { color: colors.error }]}>Server changes</Text>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>Name: {serverData.name}</Text>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>Date: {serverData.date}</Text>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>Time: {serverData.time}</Text>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>Calories: {serverData.total_calories}</Text>
            {serverData.total_protein && (
              <Text style={[styles.fieldLabel, { color: colors.text }]}>Protein: {serverData.total_protein}g</Text>
            )}
            {serverData.total_carbs && (
              <Text style={[styles.fieldLabel, { color: colors.text }]}>Carbs: {serverData.total_carbs}g</Text>
            )}
            {serverData.total_fat && (
              <Text style={[styles.fieldLabel, { color: colors.text }]}>Fat: {serverData.total_fat}g</Text>
            )}
          </View>
        </View>
      );
    } else if (tableName === 'meal_items') {
      return (
        <View style={styles.comparisonContainer}>
          <View style={styles.comparisonColumn}>
            <Text style={[styles.columnHeader, { color: colors.primary }]}>Your changes</Text>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>Name: {localData.name}</Text>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>Calories: {localData.calories}</Text>
            {localData.protein && (
              <Text style={[styles.fieldLabel, { color: colors.text }]}>Protein: {localData.protein}g</Text>
            )}
            {localData.carbs && (
              <Text style={[styles.fieldLabel, { color: colors.text }]}>Carbs: {localData.carbs}g</Text>
            )}
            {localData.fat && (
              <Text style={[styles.fieldLabel, { color: colors.text }]}>Fat: {localData.fat}g</Text>
            )}
          </View>
          
          <View style={styles.comparisonColumn}>
            <Text style={[styles.columnHeader, { color: colors.error }]}>Server changes</Text>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>Name: {serverData.name}</Text>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>Calories: {serverData.calories}</Text>
            {serverData.protein && (
              <Text style={[styles.fieldLabel, { color: colors.text }]}>Protein: {serverData.protein}g</Text>
            )}
            {serverData.carbs && (
              <Text style={[styles.fieldLabel, { color: colors.text }]}>Carbs: {serverData.carbs}g</Text>
            )}
            {serverData.fat && (
              <Text style={[styles.fieldLabel, { color: colors.text }]}>Fat: {serverData.fat}g</Text>
            )}
          </View>
        </View>
      );
    }

    return (
      <Text style={[styles.fieldLabel, { color: colors.error }]}>
        Unable to display differences for this type of data
      </Text>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={[styles.modalContainer, { backgroundColor: 'rgba(0, 0, 0, 0.5)' }]}>
        <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
          <View style={styles.header}>
            <Text style={[styles.title, { color: colors.text }]}>Resolve Data Conflicts</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Feather name="x" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={[styles.loadingText, { color: colors.text }]}>Loading conflicts...</Text>
            </View>
          ) : conflicts.length === 0 ? (
            <View style={styles.emptyContainer}>
              <MaterialIcons name="check-circle" size={48} color={colors.success} />
              <Text style={[styles.emptyText, { color: colors.text }]}>No data conflicts found!</Text>
            </View>
          ) : (
            <>
              {selectedConflict ? (
                <View style={styles.conflictDetailsContainer}>
                  <Text style={[styles.conflictTitle, { color: colors.text }]}>
                    Conflict in {selectedConflict.tableName === 'meals' ? 'Meal' : 'Meal Item'}
                  </Text>
                  <Text style={[styles.conflictTimestamp, { color: colors.textSecondary }]}>
                    Detected: {format(new Date(selectedConflict.timestamp), 'PPpp')}
                  </Text>

                  <ScrollView style={styles.detailsScrollView}>
                    {renderDifferences(selectedConflict)}
                  </ScrollView>

                  <View style={styles.actionButtonsContainer}>
                    <TouchableOpacity
                      style={[styles.actionButton, styles.localButton, { backgroundColor: colors.primary }]}
                      onPress={() => handleResolveConflict(selectedConflict.id, ConflictResolutionStrategy.LOCAL_WINS)}
                      disabled={resolving}
                    >
                      {resolving ? (
                        <ActivityIndicator size="small" color="#fff" />
                      ) : (
                        <Text style={styles.actionButtonText}>Keep My Changes</Text>
                      )}
                    </TouchableOpacity>
                    
                    <TouchableOpacity
                      style={[styles.actionButton, styles.serverButton, { backgroundColor: colors.error }]}
                      onPress={() => handleResolveConflict(selectedConflict.id, ConflictResolutionStrategy.SERVER_WINS)}
                      disabled={resolving}
                    >
                      {resolving ? (
                        <ActivityIndicator size="small" color="#fff" />
                      ) : (
                        <Text style={styles.actionButtonText}>Use Server Version</Text>
                      )}
                    </TouchableOpacity>
                  </View>
                  
                  <TouchableOpacity
                    style={styles.backButton}
                    onPress={() => setSelectedConflict(null)}
                    disabled={resolving}
                  >
                    <Text style={[styles.backButtonText, { color: colors.primary }]}>Back to List</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <>
                  <View style={styles.conflictListHeader}>
                    <Feather name="alert-triangle" size={20} color={colors.warning} />
                    <Text style={[styles.conflictListHeaderText, { color: colors.text }]}>
                      {conflicts.length} {conflicts.length === 1 ? 'conflict' : 'conflicts'} to resolve
                    </Text>
                  </View>
                  
                  <ScrollView style={styles.conflictList}>
                    {conflicts.map(conflict => (
                      <TouchableOpacity
                        key={conflict.id}
                        style={[styles.conflictItem, { borderBottomColor: colors.border }]}
                        onPress={() => setSelectedConflict(conflict)}
                      >
                        <Text style={[styles.conflictItemTitle, { color: colors.text }]}>
                          {conflict.tableName === 'meals' ? 'Meal: ' : 'Meal Item: '}
                          {conflict.tableName === 'meals' ? conflict.localData.name : conflict.localData.name}
                        </Text>
                        <Text style={[styles.conflictItemTimestamp, { color: colors.textSecondary }]}>
                          {format(new Date(conflict.timestamp), 'PP')}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </>
              )}
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  modalContent: {
    width: '95%',
    maxHeight: '90%',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  loadingContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
  },
  conflictListHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 4,
  },
  conflictListHeaderText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
  },
  conflictList: {
    maxHeight: 400,
  },
  conflictItem: {
    paddingVertical: 12,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
  },
  conflictItemTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  conflictItemTimestamp: {
    fontSize: 12,
    marginTop: 4,
  },
  conflictDetailsContainer: {
    paddingTop: 12,
  },
  conflictTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  conflictTimestamp: {
    fontSize: 12,
    marginTop: 4,
    marginBottom: 16,
  },
  detailsScrollView: {
    maxHeight: 300,
  },
  comparisonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  comparisonColumn: {
    flex: 1,
    padding: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 8,
    marginHorizontal: 4,
  },
  columnHeader: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  fieldLabel: {
    fontSize: 14,
    marginBottom: 6,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
  },
  localButton: {
    backgroundColor: '#4caf50',
  },
  serverButton: {
    backgroundColor: '#f44336',
  },
  actionButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  backButton: {
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 16,
  },
  backButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default ConflictResolutionModal; 