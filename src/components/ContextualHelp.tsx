import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Pressable,
  Animated,
  Dimensions,
  Platform,
  ViewStyle,
  TextStyle,
  StyleProp,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useAccessibility } from './AccessibilityProvider';
import * as Haptics from 'expo-haptics';
import { Feather } from '@expo/vector-icons';

// Types
interface HelpContent {
  title: string;
  description: string;
  examples?: string[];
  imageUrl?: string;
  links?: { text: string; url: string }[];
}

interface ContextualHelpProps {
  /**
   * The help content to display
   */
  content: HelpContent;
  
  /**
   * Whether to display the help icon inline or as a button
   */
  displayMode?: 'icon' | 'button' | 'inline';
  
  /**
   * The position of the help tooltip
   */
  position?: 'top' | 'right' | 'bottom' | 'left';
  
  /**
   * Optional custom styles for the container
   */
  containerStyle?: StyleProp<ViewStyle>;
  
  /**
   * Optional custom styles for the button
   */
  buttonStyle?: StyleProp<ViewStyle>;
  
  /**
   * Optional custom styles for the tooltip
   */
  tooltipStyle?: StyleProp<ViewStyle>;
  
  /**
   * Optional custom styles for the text
   */
  textStyle?: StyleProp<TextStyle>;
  
  /**
   * Button text if display mode is 'button'
   */
  buttonText?: string;
  
  /**
   * Color for the help icon
   */
  iconColor?: string;
  
  /**
   * Size for the help icon
   */
  iconSize?: number;
  
  /**
   * Whether to show the tooltip in a modal on mobile
   */
  useModal?: boolean;
  
  /**
   * Custom trigger element to replace the default icon or button
   */
  triggerElement?: React.ReactNode;
  
  /**
   * Callback when help is opened
   */
  onOpen?: () => void;
  
  /**
   * Callback when help is closed
   */
  onClose?: () => void;
}

/**
 * A component that provides contextual help for complex inputs.
 * It can be displayed as an icon, a button, or inline with content.
 */
export function ContextualHelp({
  content,
  displayMode = 'icon',
  position = 'bottom',
  containerStyle,
  buttonStyle,
  tooltipStyle,
  textStyle,
  buttonText = 'Help',
  iconColor,
  iconSize = 18,
  useModal = Platform.OS !== 'web',
  triggerElement,
  onOpen,
  onClose,
}: ContextualHelpProps) {
  const { colors, isDark } = useTheme();
  const { isReduceMotionEnabled } = useAccessibility();
  
  // State for tooltip visibility
  const [isHelpVisible, setIsHelpVisible] = useState(false);
  const [tooltipLayout, setTooltipLayout] = useState({ width: 0, height: 0 });
  const [triggerLayout, setTriggerLayout] = useState({ x: 0, y: 0, width: 0, height: 0 });
  
  // Animation value for fading
  const fadeAnim = useRef(new Animated.Value(0)).current;
  
  // Show the help tooltip
  const showHelp = () => {
    if (Platform.OS !== 'web' && !isReduceMotionEnabled) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    
    setIsHelpVisible(true);
    onOpen?.();
    
    // Animate in
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: isReduceMotionEnabled ? 0 : 200,
      useNativeDriver: true,
    }).start();
  };
  
  // Hide the help tooltip
  const hideHelp = () => {
    // Animate out and then hide
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: isReduceMotionEnabled ? 0 : 150,
      useNativeDriver: true,
    }).start(() => {
      setIsHelpVisible(false);
      onClose?.();
    });
  };
  
  // Measure the trigger element position
  const measureTrigger = (e: any) => {
    if (!e) return;
    
    e.measure((x: number, y: number, width: number, height: number, pageX: number, pageY: number) => {
      setTriggerLayout({ x: pageX, y: pageY, width, height });
    });
  };
  
  // Calculate tooltip position based on trigger and position preference
  const getTooltipPosition = () => {
    if (!triggerLayout.width || !tooltipLayout.width) return {};
    
    const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
    const { x, y, width, height } = triggerLayout;
    const { width: tooltipWidth, height: tooltipHeight } = tooltipLayout;
    
    const padding = 10; // Padding between trigger and tooltip
    
    switch (position) {
      case 'top':
        return {
          top: Math.max(0, y - tooltipHeight - padding),
          left: Math.max(0, Math.min(screenWidth - tooltipWidth, x + width / 2 - tooltipWidth / 2)),
        };
      case 'right':
        return {
          top: Math.max(0, Math.min(screenHeight - tooltipHeight, y + height / 2 - tooltipHeight / 2)),
          left: Math.min(screenWidth - tooltipWidth, x + width + padding),
        };
      case 'left':
        return {
          top: Math.max(0, Math.min(screenHeight - tooltipHeight, y + height / 2 - tooltipHeight / 2)),
          left: Math.max(0, x - tooltipWidth - padding),
        };
      case 'bottom':
      default:
        return {
          top: Math.min(screenHeight - tooltipHeight, y + height + padding),
          left: Math.max(0, Math.min(screenWidth - tooltipWidth, x + width / 2 - tooltipWidth / 2)),
        };
    }
  };
  
  // Render the trigger element (icon or button)
  const renderTrigger = () => {
    // If a custom trigger is provided, use it
    if (triggerElement) {
      return <View ref={measureTrigger}>{triggerElement}</View>;
    }
    
    // Otherwise, render based on display mode
    switch (displayMode) {
      case 'button':
        return (
          <TouchableOpacity
            style={[
              styles.helpButton,
              { backgroundColor: colors.primaryLight },
              buttonStyle,
            ]}
            onPress={showHelp}
            accessibilityLabel="Get help with this field"
            accessibilityRole="button"
            accessibilityHint="Opens a help tooltip with detailed information"
            ref={measureTrigger}
          >
            <Feather name="help-circle" color={iconColor || colors.primary} style={styles.buttonIcon} />
            <Text
              style={[
                styles.buttonText,
                { color: colors.primary },
                textStyle,
              ]}
            >
              {buttonText}
            </Text>
          </TouchableOpacity>
        );
      case 'inline':
        return (
          <View
            style={[styles.inlineContainer, containerStyle]}
            ref={measureTrigger}
          >
            <TouchableOpacity
              onPress={showHelp}
              accessibilityLabel="Get help with this field"
              accessibilityRole="button"
              accessibilityHint="Opens a help tooltip with detailed information"
              style={styles.inlineButton}
            >
              <Feather name="info" color={iconColor || colors.primary} />
            </TouchableOpacity>
            <Text
              style={[
                styles.inlineText,
                { color: colors.textSecondary },
                textStyle,
              ]}
            >
              {content.title}
            </Text>
          </View>
        );
      case 'icon':
      default:
        return (
          <TouchableOpacity
            onPress={showHelp}
            accessibilityLabel="Get help with this field"
            accessibilityRole="button"
            accessibilityHint="Opens a help tooltip with detailed information"
            style={[styles.iconButton, containerStyle]}
            ref={measureTrigger}
          >
            <Feather name="help-circle" color={iconColor || colors.primary} />
          </TouchableOpacity>
        );
    }
  };
  
  // Render the help content
  const renderTooltipContent = () => (
    <View>
      <View style={styles.tooltipHeader}>
        <Text style={[styles.tooltipTitle, { color: colors.text }]}>
          {content.title}
        </Text>
        {useModal && (
          <TouchableOpacity
            onPress={hideHelp}
            accessibilityLabel="Close help tooltip"
            accessibilityRole="button"
          >
            <Feather name="x" size={18} color={colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>
      
      <Text style={[styles.tooltipDescription, { color: colors.text }]}>
        {content.description}
      </Text>
      
      {content.examples && content.examples.length > 0 && (
        <View style={styles.examplesContainer}>
          <Text style={[styles.examplesTitle, { color: colors.textSecondary }]}>
            Examples:
          </Text>
          {content.examples.map((example, index) => (
            <Text
              key={`example-${index}`}
              style={[styles.exampleText, { color: colors.text }]}
            >
              • {example}
            </Text>
          ))}
        </View>
      )}
      
      {content.links && content.links.length > 0 && (
        <View style={styles.linksContainer}>
          {content.links.map((link, index) => (
            <TouchableOpacity
              key={`link-${index}`}
              style={[styles.linkButton, { borderColor: colors.border }]}
              accessibilityRole="link"
            >
              <Text style={[styles.linkText, { color: colors.primary }]}>
                {link.text}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );
  
  // Render tooltip or modal based on platform and preference
  const renderHelpContent = () => {
    if (!isHelpVisible) return null;
    
    if (useModal) {
      return (
        <Modal
          transparent
          visible={isHelpVisible}
          animationType="fade"
          onRequestClose={hideHelp}
        >
          <View style={styles.modalContainer}>
            <Pressable
              style={styles.modalBackdrop}
              onPress={hideHelp}
            />
            <Animated.View
              style={[
                styles.modalContent,
                {
                  backgroundColor: isDark ? colors.card : '#FFFFFF',
                  opacity: fadeAnim,
                  transform: [
                    {
                      translateY: fadeAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [20, 0],
                      }),
                    },
                  ],
                },
                tooltipStyle,
              ]}
              onLayout={(e) => setTooltipLayout(e.nativeEvent.layout)}
            >
              {renderTooltipContent()}
            </Animated.View>
          </View>
        </Modal>
      );
    }
    
    // Tooltip for web and other platforms
    return (
      <Animated.View
        style={[
          styles.tooltip,
          {
            backgroundColor: isDark ? colors.card : '#FFFFFF',
            shadowColor: colors.shadow,
            ...getTooltipPosition(),
            opacity: fadeAnim,
          },
          tooltipStyle,
        ]}
        onLayout={(e) => setTooltipLayout(e.nativeEvent.layout)}
      >
        {renderTooltipContent()}
      </Animated.View>
    );
  };
  
  return (
    <View style={[styles.container, containerStyle]}>
      {renderTrigger()}
      {renderHelpContent()}
      
      {/* Invisible touch area to detect outside presses when tooltip is shown */}
      {isHelpVisible && !useModal && (
        <Pressable
          style={[StyleSheet.absoluteFill, { pointerEvents: 'box-none' }]}
          onPress={hideHelp}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  iconButton: {
    padding: 6,
  },
  helpButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 16,
  },
  buttonIcon: {
    marginRight: 4,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  inlineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
  },
  inlineButton: {
    marginRight: 8,
  },
  inlineText: {
    fontSize: 14,
  },
  tooltip: {
    position: 'absolute',
    width: 250,
    padding: 12,
    borderRadius: 8,
    zIndex: 999,
    elevation: 5,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  tooltipHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  tooltipTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  tooltipDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  examplesContainer: {
    marginBottom: 12,
  },
  examplesTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  exampleText: {
    fontSize: 14,
    marginLeft: 8,
    lineHeight: 20,
  },
  linksContainer: {
    marginTop: 8,
  },
  linkButton: {
    marginTop: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
    borderWidth: 1,
    alignItems: 'center',
  },
  linkText: {
    fontSize: 14,
    fontWeight: '500',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBackdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '85%',
    maxWidth: 320,
    padding: 16,
    borderRadius: 12,
    elevation: 5,
    ...(Platform.OS === 'web'
      ? { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.2)' }
      : {
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.2,
          shadowRadius: 4,
        }
    ),
  },
});

export default ContextualHelp; 