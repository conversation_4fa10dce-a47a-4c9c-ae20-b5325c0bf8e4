import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Modal,
  TextInput,
  ScrollView,
  Switch,
  Platform
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface MacroGoal {
  protein: number;
  carbs: number;
  fat: number;
  calories?: number; // Optional, can be calculated
}

interface MealGoal {
  id: string;
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack' | 'custom';
  customName?: string;
  macros: MacroGoal;
  enabled: boolean;
}

interface DailyProgress {
  breakfast: MacroGoal;
  lunch: MacroGoal;
  dinner: MacroGoal;
  snack: MacroGoal;
  total: MacroGoal;
}

interface CustomMacroGoalsComponentProps {
  date?: string; // ISO date string, defaults to today
  onSave?: () => void;
}

export function CustomMacroGoalsComponent({ 
  date,
  onSave 
}: CustomMacroGoalsComponentProps) {
  const { colors, isDark } = useTheme();
  const [mealGoals, setMealGoals] = useState<MealGoal[]>([]);
  const [progress, setProgress] = useState<DailyProgress>({
    breakfast: { protein: 0, carbs: 0, fat: 0, calories: 0 },
    lunch: { protein: 0, carbs: 0, fat: 0, calories: 0 },
    dinner: { protein: 0, carbs: 0, fat: 0, calories: 0 },
    snack: { protein: 0, carbs: 0, fat: 0, calories: 0 },
    total: { protein: 0, carbs: 0, fat: 0, calories: 0 },
  });
  
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingGoal, setEditingGoal] = useState<MealGoal | null>(null);
  const [tempProtein, setTempProtein] = useState('');
  const [tempCarbs, setTempCarbs] = useState('');
  const [tempFat, setTempFat] = useState('');
  const [tempCalories, setTempCalories] = useState('');
  const [tempName, setTempName] = useState('');
  const [isCustomMeal, setIsCustomMeal] = useState(false);
  const [autoCalories, setAutoCalories] = useState(true);
  
  useEffect(() => {
    loadMealGoals();
    loadProgress();
  }, [date]);
  
  const loadMealGoals = async () => {
    try {
      // In a real app, we would fetch from a database
      // For demo, we'll check AsyncStorage or use default values
      const savedGoals = await AsyncStorage.getItem('mealMacroGoals');
      
      if (savedGoals) {
        setMealGoals(JSON.parse(savedGoals));
      } else {
        // Set default goals
        setMealGoals([
          {
            id: 'breakfast',
            mealType: 'breakfast',
            macros: {
              protein: 25,
              carbs: 40,
              fat: 10,
              calories: 350
            },
            enabled: true
          },
          {
            id: 'lunch',
            mealType: 'lunch',
            macros: {
              protein: 35,
              carbs: 45,
              fat: 15,
              calories: 450
            },
            enabled: true
          },
          {
            id: 'dinner',
            mealType: 'dinner',
            macros: {
              protein: 40,
              carbs: 35,
              fat: 20,
              calories: 480
            },
            enabled: true
          },
          {
            id: 'snack',
            mealType: 'snack',
            macros: {
              protein: 10,
              carbs: 15,
              fat: 8,
              calories: 170
            },
            enabled: true
          }
        ]);
      }
    } catch (error) {
      console.error('Failed to load meal goals', error);
    }
  };
  
  const loadProgress = async () => {
    try {
      // In a real app, we would fetch from a database based on the date
      // For demo, we'll use mock data
      const mockProgress: DailyProgress = {
        breakfast: { protein: 18, carbs: 35, fat: 8, calories: 284 },
        lunch: { protein: 32, carbs: 42, fat: 14, calories: 418 },
        dinner: { protein: 30, carbs: 25, fat: 18, calories: 390 },
        snack: { protein: 8, carbs: 12, fat: 6, calories: 134 },
        total: { protein: 88, carbs: 114, fat: 46, calories: 1226 }
      };
      
      setProgress(mockProgress);
    } catch (error) {
      console.error('Failed to load progress data', error);
    }
  };
  
  const saveMealGoals = async (goals: MealGoal[]) => {
    try {
      await AsyncStorage.setItem('mealMacroGoals', JSON.stringify(goals));
      if (onSave) onSave();
    } catch (error) {
      console.error('Failed to save meal goals', error);
    }
  };
  
  const handleEditGoal = (goal: MealGoal) => {
    setEditingGoal(goal);
    setTempProtein(goal.macros.protein.toString());
    setTempCarbs(goal.macros.carbs.toString());
    setTempFat(goal.macros.fat.toString());
    setTempCalories(goal.macros.calories?.toString() || '');
    setTempName(goal.customName || '');
    setIsCustomMeal(goal.mealType === 'custom');
    setAutoCalories(!!goal.macros.calories);
    setShowEditModal(true);
  };
  
  const handleSaveGoal = () => {
    if (!editingGoal) return;
    
    const protein = parseInt(tempProtein) || 0;
    const carbs = parseInt(tempCarbs) || 0;
    const fat = parseInt(tempFat) || 0;
    const calories = autoCalories 
      ? (protein * 4) + (carbs * 4) + (fat * 9) 
      : (parseInt(tempCalories) || 0);
    
    const updatedGoal: MealGoal = {
      ...editingGoal,
      mealType: isCustomMeal ? 'custom' : editingGoal.mealType,
      customName: isCustomMeal ? tempName : undefined,
      macros: {
        protein,
        carbs,
        fat,
        calories: autoCalories ? calories : (parseInt(tempCalories) || undefined)
      }
    };
    
    const updatedGoals = mealGoals.map(goal => 
      goal.id === updatedGoal.id ? updatedGoal : goal
    );
    
    setMealGoals(updatedGoals);
    saveMealGoals(updatedGoals);
    setShowEditModal(false);
  };
  
  const handleToggleGoal = (goalId: string, enabled: boolean) => {
    const updatedGoals = mealGoals.map(goal => 
      goal.id === goalId ? { ...goal, enabled } : goal
    );
    
    setMealGoals(updatedGoals);
    saveMealGoals(updatedGoals);
  };
  
  const calculateCalories = () => {
    const protein = parseInt(tempProtein) || 0;
    const carbs = parseInt(tempCarbs) || 0;
    const fat = parseInt(tempFat) || 0;
    return (protein * 4) + (carbs * 4) + (fat * 9);
  };
  
  const getMealTypeName = (mealType: string, customName?: string) => {
    if (mealType === 'custom' && customName) {
      return customName;
    }
    
    return mealType.charAt(0).toUpperCase() + mealType.slice(1);
  };
  
  const calculateProgress = (current: number, target: number) => {
    if (target === 0) return 0;
    const progress = (current / target) * 100;
    return Math.min(progress, 100); // Cap at 100%
  };
  
  const getProgressColor = (progress: number) => {
    if (progress < 50) {
      return colors.primaryLight;
    }
    if (progress < 80) {
      return colors.primary;
    }
    if (progress <= 100) {
      return colors.success;
    }
    // Over 100%
    return colors.danger;
  };
  
  const renderMacroProgress = (macro: 'protein' | 'carbs' | 'fat' | 'calories', mealType: keyof DailyProgress, goal: MealGoal) => {
    if (!goal.enabled) return null;
    
    const current = progress[mealType][macro] || 0;
    const target = goal.macros[macro] || 0;
    const progressPercent = calculateProgress(current, target);
    const progressColor = getProgressColor(progressPercent);
    const unit = macro === 'calories' ? 'cal' : 'g';
    
    return (
      <View style={styles.macroItem}>
        <View style={styles.macroLabelRow}>
          <Text style={[styles.macroLabel, { color: colors.textSecondary }]}>
            {macro.charAt(0).toUpperCase() + macro.slice(1)}
          </Text>
          <Text style={[styles.macroValues, { color: colors.text }]}>
            {current} / {target} {unit}
          </Text>
        </View>
        
        <View 
          style={[
            styles.progressBackground, 
            { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }
          ]}
        >
          <View
            style={[
              styles.progressFill,
              { 
                backgroundColor: progressColor,
                width: `${progressPercent}%` 
              }
            ]}
          />
        </View>
      </View>
    );
  };
  
  const renderTotalProgress = () => {
    // Calculate total goals from enabled meal goals
    const totalGoal = mealGoals.reduce(
      (acc, goal) => {
        if (!goal.enabled) return acc;
        return {
          protein: acc.protein + (goal.macros.protein || 0),
          carbs: acc.carbs + (goal.macros.carbs || 0),
          fat: acc.fat + (goal.macros.fat || 0),
          calories: acc.calories + (goal.macros.calories || 0)
        };
      },
      { protein: 0, carbs: 0, fat: 0, calories: 0 }
    );
    
    return (
      <View style={[styles.totalCard, { backgroundColor: isDark ? colors.card : 'white' }]}>
        <Text style={[styles.totalTitle, { color: colors.text }]}>
          Daily Totals
        </Text>
        
        <View style={styles.totalMacros}>
          {(['protein', 'carbs', 'fat', 'calories'] as const).map(macro => {
            const current = progress.total[macro] || 0;
            const target = totalGoal[macro] || 0;
            const progressPercent = calculateProgress(current, target);
            const progressColor = getProgressColor(progressPercent);
            const unit = macro === 'calories' ? 'cal' : 'g';
            
            return (
              <View key={macro} style={styles.totalMacroItem}>
                <View style={styles.totalMacroHeader}>
                  <Text style={[styles.totalMacroLabel, { color: colors.text }]}>
                    {macro.charAt(0).toUpperCase() + macro.slice(1)}
                  </Text>
                  <Text style={[styles.totalMacroValue, { color: progressColor }]}>
                    {current} / {target} {unit}
                  </Text>
                </View>
                
                <View 
                  style={[
                    styles.totalProgressBackground, 
                    { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }
                  ]}
                >
                  <View
                    style={[
                      styles.totalProgressFill,
                      { 
                        backgroundColor: progressColor,
                        width: `${progressPercent}%` 
                      }
                    ]}
                  />
                </View>
              </View>
            );
          })}
        </View>
      </View>
    );
  };
  
  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {renderTotalProgress()}
        
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Meal-Specific Goals
        </Text>
        
        {mealGoals.map(goal => (
          <View 
            key={goal.id}
            style={[
              styles.goalCard, 
              { 
                backgroundColor: isDark ? colors.card : 'white',
                opacity: goal.enabled ? 1 : 0.6
              }
            ]}
          >
            <View style={styles.goalHeader}>
              <View style={styles.goalInfo}>
                <Text style={[styles.goalTitle, { color: colors.text }]}>
                  {getMealTypeName(goal.mealType, goal.customName)}
                </Text>
                
                <Text style={[styles.goalSummary, { color: colors.textSecondary }]}>
                  P: {goal.macros.protein}g • C: {goal.macros.carbs}g • F: {goal.macros.fat}g
                  {goal.macros.calories && ` • ${goal.macros.calories} cal`}
                </Text>
              </View>
              
              <View style={styles.goalActions}>
                <Switch
                  value={goal.enabled}
                  onValueChange={(value) => handleToggleGoal(goal.id, value)}
                  trackColor={{ false: colors.subtle, true: colors.primary }}
                  thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
                  ios_backgroundColor={colors.subtle}
                  style={styles.goalSwitch}
                />
                
                <TouchableOpacity
                  style={styles.editButton}
                  onPress={() => handleEditGoal(goal)}
                >
                  <Feather name="edit" size={16} color={colors.textSecondary} />
                </TouchableOpacity>
              </View>
            </View>
            
            {goal.enabled && (
              <View style={styles.macroProgressList}>
                {(['protein', 'carbs', 'fat', 'calories'] as const).map(macro => (
                  goal.macros[macro] ? renderMacroProgress(macro, goal.mealType as keyof DailyProgress, goal) : null
                ))}
              </View>
            )}
          </View>
        ))}
      </ScrollView>
      
      {/* Edit Goal Modal */}
      <Modal
        visible={showEditModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowEditModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: isDark ? colors.card : 'white' }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                {isCustomMeal ? 'Custom Meal Goal' : `${getMealTypeName(editingGoal?.mealType || '')} Goal`}
              </Text>
              
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowEditModal(false)}
              >
                <Feather name="x" size={20} color={colors.text} />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalBody}>
              {isCustomMeal && (
                <View style={styles.inputGroup}>
                  <Text style={[styles.inputLabel, { color: colors.text }]}>
                    Meal Name
                  </Text>
                  <TextInput
                    style={[
                      styles.input,
                      { 
                        backgroundColor: isDark ? colors.subtle : '#f5f5f5',
                        color: colors.text,
                        borderColor: colors.border
                      }
                    ]}
                    placeholder="Enter custom meal name"
                    placeholderTextColor={colors.textSecondary}
                    value={tempName}
                    onChangeText={setTempName}
                  />
                </View>
              )}
              
              <View style={styles.inputGroup}>
                <Text style={[styles.inputLabel, { color: colors.text }]}>
                  Protein (g)
                </Text>
                <TextInput
                  style={[
                    styles.input,
                    { 
                      backgroundColor: isDark ? colors.subtle : '#f5f5f5',
                      color: colors.text,
                      borderColor: colors.border
                    }
                  ]}
                  placeholder="0"
                  placeholderTextColor={colors.textSecondary}
                  value={tempProtein}
                  onChangeText={setTempProtein}
                  keyboardType="number-pad"
                />
              </View>
              
              <View style={styles.inputGroup}>
                <Text style={[styles.inputLabel, { color: colors.text }]}>
                  Carbs (g)
                </Text>
                <TextInput
                  style={[
                    styles.input,
                    { 
                      backgroundColor: isDark ? colors.subtle : '#f5f5f5',
                      color: colors.text,
                      borderColor: colors.border
                    }
                  ]}
                  placeholder="0"
                  placeholderTextColor={colors.textSecondary}
                  value={tempCarbs}
                  onChangeText={setTempCarbs}
                  keyboardType="number-pad"
                />
              </View>
              
              <View style={styles.inputGroup}>
                <Text style={[styles.inputLabel, { color: colors.text }]}>
                  Fat (g)
                </Text>
                <TextInput
                  style={[
                    styles.input,
                    { 
                      backgroundColor: isDark ? colors.subtle : '#f5f5f5',
                      color: colors.text,
                      borderColor: colors.border
                    }
                  ]}
                  placeholder="0"
                  placeholderTextColor={colors.textSecondary}
                  value={tempFat}
                  onChangeText={setTempFat}
                  keyboardType="number-pad"
                />
              </View>
              
              <View style={styles.caloriesSection}>
                <View style={styles.autoCaloriesRow}>
                  <Text style={[styles.inputLabel, { color: colors.text }]}>
                    Auto-calculate calories
                  </Text>
                  <Switch
                    value={autoCalories}
                    onValueChange={setAutoCalories}
                    trackColor={{ false: colors.subtle, true: colors.primary }}
                    thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
                    ios_backgroundColor={colors.subtle}
                  />
                </View>
                
                {autoCalories ? (
                  <Text style={[styles.calculatedCalories, { color: colors.primary }]}>
                    Calculated: {calculateCalories()} calories
                  </Text>
                ) : (
                  <View style={styles.inputGroup}>
                    <Text style={[styles.inputLabel, { color: colors.text }]}>
                      Calories
                    </Text>
                    <TextInput
                      style={[
                        styles.input,
                        { 
                          backgroundColor: isDark ? colors.subtle : '#f5f5f5',
                          color: colors.text,
                          borderColor: colors.border
                        }
                      ]}
                      placeholder="0"
                      placeholderTextColor={colors.textSecondary}
                      value={tempCalories}
                      onChangeText={setTempCalories}
                      keyboardType="number-pad"
                    />
                  </View>
                )}
              </View>
            </ScrollView>
            
            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.cancelButton, { backgroundColor: colors.subtle }]}
                onPress={() => setShowEditModal(false)}
              >
                <Text style={[styles.buttonText, { color: colors.text }]}>
                  Cancel
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.saveButton, { backgroundColor: colors.primary }]}
                onPress={handleSaveGoal}
              >
                <Feather name="save" size={16} style={styles.buttonIcon} />
                <Text style={[styles.buttonText, { color: 'white' }]}>
                  Save Goal
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginVertical: 16,
  },
  totalCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  totalTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  totalMacros: {
    gap: 12,
  },
  totalMacroItem: {
    marginBottom: 8,
  },
  totalMacroHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  totalMacroLabel: {
    fontSize: 15,
    fontWeight: '500',
  },
  totalMacroValue: {
    fontSize: 15,
    fontWeight: '500',
  },
  totalProgressBackground: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  totalProgressFill: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    borderRadius: 4,
  },
  goalCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  goalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  goalInfo: {
    flex: 1,
  },
  goalTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  goalSummary: {
    fontSize: 14,
  },
  goalActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  goalSwitch: {
    marginRight: 8,
  },
  editButton: {
    padding: 6,
  },
  macroProgressList: {
    gap: 12,
  },
  macroItem: {
    marginBottom: 4,
  },
  macroLabelRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  macroLabel: {
    fontSize: 14,
  },
  macroValues: {
    fontSize: 14,
  },
  progressBackground: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    borderRadius: 3,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  modalBody: {
    padding: 16,
    maxHeight: '60%',
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 15,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    height: 50,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    borderWidth: 1,
  },
  caloriesSection: {
    marginTop: 8,
    marginBottom: 16,
  },
  autoCaloriesRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  calculatedCalories: {
    fontSize: 15,
    marginBottom: 16,
  },
  modalFooter: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  cancelButton: {
    flex: 1,
    height: 50,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  saveButton: {
    flex: 2,
    flexDirection: 'row',
    height: 50,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CustomMacroGoalsComponent; 