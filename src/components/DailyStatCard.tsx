import React, { useEffect, useRef } from 'react';
import { StyleSheet, Text, View, Platform, Animated, TouchableOpacity } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';

interface DailyStatCardProps {
  calories: number;
  target: number;
  protein: number;
  carbs: number;
  fat: number;
}

export function DailyStatCard({ calories, target, protein, carbs, fat }: DailyStatCardProps) {
  const { colors, isDark } = useTheme();
  const caloriesPercent = Math.min(100, Math.round((calories / target) * 100));
  
  // Animation for the progress bar
  const progressAnim = useRef(new Animated.Value(0)).current;
  const circleProgressAnim = useRef(new Animated.Value(0)).current;
  const macroBarAnim = useRef([
    new Animated.Value(0), // protein
    new Animated.Value(0), // carbs
    new Animated.Value(0)  // fat
  ]).current;
  
  useEffect(() => {
    // Animate the calorie progress bar
    Animated.timing(progressAnim, {
      toValue: caloriesPercent / 100,
      duration: 1500,
      useNativeDriver: false,
    }).start();
    
    // Animate the circle progress
    Animated.timing(circleProgressAnim, {
      toValue: caloriesPercent / 100,
      duration: 1800,
      useNativeDriver: false,
    }).start();
    
    // Animate the macro bars sequentially
    Animated.stagger(300, [
      Animated.timing(macroBarAnim[0], {
        toValue: 1,
        duration: 800,
        useNativeDriver: false,
      }),
      Animated.timing(macroBarAnim[1], {
        toValue: 1,
        duration: 800,
        useNativeDriver: false,
      }),
      Animated.timing(macroBarAnim[2], {
        toValue: 1,
        duration: 800,
        useNativeDriver: false,
      })
    ]).start();
  }, [calories, protein, carbs, fat]);
  
  const width = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
  });
  
  // Circle progress animation
  const circleDegree = circleProgressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg']
  });
  
  // Calculate macro percentages
  const totalMacros = protein * 4 + carbs * 4 + fat * 9; // calories from macros
  const proteinPercentage = Math.round((protein * 4 / totalMacros) * 100) || 0;
  const carbsPercentage = Math.round((carbs * 4 / totalMacros) * 100) || 0;
  const fatPercentage = Math.round((fat * 9 / totalMacros) * 100) || 0;
  
  // Animated width for the macro bars
  const proteinWidth = macroBarAnim[0].interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', `${proteinPercentage}%`]
  });
  
  const carbsWidth = macroBarAnim[1].interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', `${carbsPercentage}%`]
  });
  
  const fatWidth = macroBarAnim[2].interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', `${fatPercentage}%`]
  });
  
  // Calorie status indicator
  const getCalorieStatus = () => {
    const remaining = target - calories;
    if (remaining > 0) {
      return {
        icon: <Feather name="arrow-down" size={14}  color={colors.text} />,
        text: `${remaining} remaining`,
        color: '#10B981'
      };
    } else if (remaining < 0) {
      return {
        icon: <Feather name="arrow-up" size={14}  color={colors.text} />,
        text: `${Math.abs(remaining)} over`,
        color: '#EF4444'
      };
    } else {
      return {
        icon: <Feather name="trending-up" size={14}  color={colors.text} />,
        text: 'On target',
        color: '#F59E0B'
      };
    }
  };
  
  const calorieStatus = getCalorieStatus();
  
  return (
    <View style={[
      styles.card, 
      { 
        backgroundColor: colors.card,
        shadowColor: colors.shadow,
        borderColor: colors.border,
      }
    ]}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={[styles.title, { color: colors.text }]}>Daily Nutrition</Text>
          <View style={[styles.statusPill, { backgroundColor: getStatusPillColor(calorieStatus.color, isDark) }]}>
            <View style={styles.statusContainer}>
              {calorieStatus.icon}
              <Text style={[styles.statusText, { color: getStatusTextColor(calorieStatus.color, isDark) }]}>
                {calorieStatus.text}
              </Text>
            </View>
          </View>
        </View>
        <TouchableOpacity 
          style={[styles.infoButton, { backgroundColor: colors.subtle }]}
          activeOpacity={0.7}
        >
          <Feather name="info" size={18} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
      
      <View style={styles.calorieSection}>
        <View style={styles.calorieCircleContainer}>
          <View style={[styles.calorieCircle, { borderColor: colors.subtle }]}>
            <Animated.View 
              style={[
                styles.calorieProgress,
                { 
                  borderColor: getCalorieColor(caloriesPercent, colors),
                  borderTopColor: 'transparent',
                  borderLeftColor: 'transparent',
                  transform: [{ rotate: circleDegree }]
                }
              ]} 
            />
            <View style={styles.calorieInner}>
              <Text style={[styles.calorieValue, { color: colors.text }]}>{calories}</Text>
              <Text style={[styles.calorieLabel, { color: colors.textSecondary }]}>calories</Text>
            </View>
          </View>
        </View>
        
        <View style={styles.calorieRightContainer}>
          <View style={styles.calorieGoal}>
            <View style={styles.goalHeader}>
              <Text style={[styles.goalText, { color: colors.textSecondary }]}>
                Daily Goal
              </Text>
              <Text style={[styles.targetValue, { color: colors.text }]}>{target}</Text>
            </View>
            <View style={styles.goalPercentage}>
              <Text style={[styles.percentValue, { color: getTextColor(caloriesPercent, colors) }]}>
                {caloriesPercent}%
              </Text>
              <Text style={[styles.percentLabel, { color: colors.textSecondary }]}>
                of goal
              </Text>
            </View>
          </View>
          
          <View style={[styles.progressContainer, { backgroundColor: colors.subtle }]}>
            <Animated.View 
              style={[
                styles.progressBar, 
                { 
                  width: width, 
                  backgroundColor: getCalorieColor(caloriesPercent, colors),
                  shadowColor: colors.primary,
                  shadowOffset: { width: 0, height: 0 },
                  shadowOpacity: isDark ? 0.5 : 0.3,
                  shadowRadius: 8,
                }
              ]} 
            />
          </View>
        </View>
      </View>
      
      <View style={styles.macrosTitle}>
        <Text style={[styles.macrosTitleText, { color: colors.textSecondary }]}>
          Macronutrients
        </Text>
        <TouchableOpacity style={styles.macrosInfo}>
          <Feather name="info" size={14} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
      
      <View style={styles.macrosContainer}>
        {/* Protein */}
        <View style={styles.macroItemContainer}>
          <View style={styles.macroItemHeader}>
            <View style={styles.macroLabelContainer}>
              <View style={[styles.macroIndicator, { backgroundColor: '#3B82F6' }]} />
              <Text style={[styles.macroLabel, { color: colors.text }]}>Protein</Text>
            </View>
            <View style={styles.macroValues}>
              <Text style={[styles.macroValue, { color: colors.text }]}>{protein}g</Text>
              <Text style={[styles.macroPercent, { color: '#3B82F6' }]}>{proteinPercentage}%</Text>
            </View>
          </View>
          <View style={[styles.macroProgressContainer, { backgroundColor: colors.subtle }]}>
            <Animated.View style={[styles.macroProgressBar, { 
              width: proteinWidth, 
              backgroundColor: '#3B82F6',
            }]} />
          </View>
        </View>
        
        {/* Carbs */}
        <View style={styles.macroItemContainer}>
          <View style={styles.macroItemHeader}>
            <View style={styles.macroLabelContainer}>
              <View style={[styles.macroIndicator, { backgroundColor: '#8B5CF6' }]} />
              <Text style={[styles.macroLabel, { color: colors.text }]}>Carbs</Text>
            </View>
            <View style={styles.macroValues}>
              <Text style={[styles.macroValue, { color: colors.text }]}>{carbs}g</Text>
              <Text style={[styles.macroPercent, { color: '#8B5CF6' }]}>{carbsPercentage}%</Text>
            </View>
          </View>
          <View style={[styles.macroProgressContainer, { backgroundColor: colors.subtle }]}>
            <Animated.View style={[styles.macroProgressBar, { 
              width: carbsWidth, 
              backgroundColor: '#8B5CF6',
            }]} />
          </View>
        </View>
        
        {/* Fat */}
        <View style={styles.macroItemContainer}>
          <View style={styles.macroItemHeader}>
            <View style={styles.macroLabelContainer}>
              <View style={[styles.macroIndicator, { backgroundColor: '#F97316' }]} />
              <Text style={[styles.macroLabel, { color: colors.text }]}>Fat</Text>
            </View>
            <View style={styles.macroValues}>
              <Text style={[styles.macroValue, { color: colors.text }]}>{fat}g</Text>
              <Text style={[styles.macroPercent, { color: '#F97316' }]}>{fatPercentage}%</Text>
            </View>
          </View>
          <View style={[styles.macroProgressContainer, { backgroundColor: colors.subtle }]}>
            <Animated.View style={[styles.macroProgressBar, { 
              width: fatWidth, 
              backgroundColor: '#F97316',
            }]} />
          </View>
        </View>
      </View>
      
      <View style={styles.macroTotalContainer}>
        <View style={[styles.macroTotalBar, { backgroundColor: colors.subtle }]}>
          <Animated.View style={[styles.macroTotalSegment, { 
            width: proteinWidth, 
            backgroundColor: '#3B82F6',
            borderTopLeftRadius: 6,
            borderBottomLeftRadius: 6,
          }]} />
          <Animated.View style={[styles.macroTotalSegment, { 
            width: carbsWidth, 
            backgroundColor: '#8B5CF6' 
          }]} />
          <Animated.View style={[styles.macroTotalSegment, { 
            width: fatWidth, 
            backgroundColor: '#F97316',
            borderTopRightRadius: carbsPercentage + fatPercentage === 100 ? 6 : 0,
            borderBottomRightRadius: carbsPercentage + fatPercentage === 100 ? 6 : 0,
          }]} />
        </View>
        
        <Text style={[styles.macroTotalLabel, { color: colors.textSecondary }]}>
          Total distribution
        </Text>
      </View>
    </View>
  );
}

// Helper function to get color based on percentage
function getCalorieColor(percent: number, colors: any) {
  if (percent < 25) return '#10B981'; // far under target - green
  if (percent <= 75) return colors.primary; // under target - primary
  if (percent <= 95) return '#10B981'; // approaching target - green
  if (percent <= 105) return '#F59E0B'; // at target - amber
  return '#EF4444'; // over target - red
}

// Get text color for percentage indicator
function getTextColor(percent: number, colors: any) {
  if (percent <= 75) return colors.primary;
  if (percent <= 95) return '#10B981';
  if (percent <= 105) return '#F59E0B';
  return '#EF4444';
}

// Status pill background color
function getStatusPillColor(statusColor: string, isDark: boolean) {
  if (statusColor === '#10B981') return isDark ? 'rgba(16, 185, 129, 0.2)' : 'rgba(16, 185, 129, 0.15)';
  if (statusColor === '#F59E0B') return isDark ? 'rgba(245, 158, 11, 0.2)' : 'rgba(245, 158, 11, 0.15)';
  if (statusColor === '#EF4444') return isDark ? 'rgba(239, 68, 68, 0.2)' : 'rgba(239, 68, 68, 0.15)';
  return 'rgba(59, 130, 246, 0.15)';
}

// Status text color
function getStatusTextColor(statusColor: string, isDark: boolean) {
  return isDark ? statusColor : statusColor;
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 24,
    padding: 24,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: {
          width: 0,
          height: 4,
        },
        shadowOpacity: 0.12,
        shadowRadius: 12,
      },
      android: {
        elevation: 5,
      },
      web: {
        shadowOffset: {
          width: 0,
          height: 4,
        },
        shadowOpacity: 0.12,
        shadowRadius: 12,
      }
    }),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  headerLeft: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 8,
  },
  statusPill: {
    alignSelf: 'flex-start',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 20,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 13,
    fontWeight: '600',
    marginLeft: 6,
  },
  infoButton: {
    padding: 10,
    borderRadius: 20,
  },
  calorieSection: {
    flexDirection: 'row',
    marginBottom: 28,
  },
  calorieCircleContainer: {
    width: '42%',
  },
  calorieCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 10,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  calorieProgress: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: 60,
    borderWidth: 10,
  },
  calorieInner: {
    alignItems: 'center',
  },
  calorieValue: {
    fontSize: 32,
    fontWeight: '800',
    letterSpacing: -0.5,
  },
  calorieLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: 4,
  },
  calorieRightContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingLeft: 16,
  },
  calorieGoal: {
    marginBottom: 16,
  },
  goalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  goalText: {
    fontSize: 14,
    fontWeight: '600',
  },
  targetValue: {
    fontSize: 18,
    fontWeight: '700',
  },
  goalPercentage: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 12,
  },
  percentValue: {
    fontSize: 22,
    fontWeight: '800',
    marginRight: 6,
  },
  percentLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressContainer: {
    height: 10,
    borderRadius: 5,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 5,
  },
  macrosTitle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  macrosTitleText: {
    fontSize: 16,
    fontWeight: '600',
  },
  macrosInfo: {
    padding: 4,
  },
  macrosContainer: {
    marginBottom: 20,
  },
  macroItemContainer: {
    marginBottom: 14,
  },
  macroItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  macroLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  macroIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 8,
  },
  macroLabel: {
    fontSize: 15,
    fontWeight: '600',
  },
  macroValues: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  macroValue: {
    fontSize: 15,
    fontWeight: '700',
    marginRight: 8,
  },
  macroPercent: {
    fontSize: 13,
    fontWeight: '700',
  },
  macroProgressContainer: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  macroProgressBar: {
    height: '100%',
  },
  macroTotalContainer: {
    marginTop: 6,
  },
  macroTotalBar: {
    height: 12,
    borderRadius: 6,
    marginBottom: 8,
    flexDirection: 'row',
    overflow: 'hidden',
  },
  macroTotalSegment: {
    height: '100%',
  },
  macroTotalLabel: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
});