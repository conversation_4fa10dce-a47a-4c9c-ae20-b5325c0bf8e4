import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ScrollView, TextInput, ActivityIndicator } from 'react-native';
import { Feather } from '@expo/vector-icons';
// Firebase imports
import { getFirestore, collection, getDocs, query, limit, where, runTransaction, getDoc, doc } from 'firebase/firestore';
import { app } from '@/lib/firebase';

// Initialize Firestore
const db = getFirestore(app);

interface DataBrowserProps {
  colors: any;
  schema: any;
  selectedTable: string | null;
}

interface ColumnInfo {
  name: string;
  type: string;
  is_nullable?: boolean;
}

interface TableRow {
  [key: string]: any;
}

export const DataBrowser = ({ colors, schema, selectedTable }: DataBrowserProps) => {
  const [data, setData] = useState<TableRow[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [filteredData, setFilteredData] = useState<TableRow[]>([]);
  const [columns, setColumns] = useState<ColumnInfo[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [sqlQuery, setSqlQuery] = useState<string>('');
  const [showSqlEditor, setShowSqlEditor] = useState<boolean>(false);
  const [selectedColumnFilters, setSelectedColumnFilters] = useState<{ [key: string]: boolean }>({});
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (selectedTable) {
      loadTableData(selectedTable);
    }
  }, [selectedTable]);

  const loadTableData = async (tableName: string) => {
    setLoading(true);
    setError(null);
    
    try {
      // Get columns from schema
      if (schema.tables[tableName]) {
        const columnInfos = schema.tables[tableName].columns.map((col: any) => ({
          name: col.name,
          type: col.type
        }));
        setColumns(columnInfos);
      }
      
      // Build SQL query
      const sql = `SELECT * FROM ${tableName} LIMIT 100;`;
      setSqlQuery(sql);
      
      // Fetch data from Firestore
      const q = query(collection(db, tableName), limit(100));
      const querySnapshot = await getDocs(q);
      const data = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      setData(data || []);
      setFilteredData(data || []);
      
      // Reset filters and sorting
      setSortColumn(null);
      setSortDirection('asc');
      setSearchQuery('');
      setSelectedColumnFilters({});
    } catch (error: any) {
      console.error('Error loading table data:', error);
      setError(error.message || 'Failed to load data');
      setData([]);
      setFilteredData([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    
    if (!query.trim()) {
      setFilteredData(data);
      return;
    }
    
    const filtered = data.filter(row => {
      return Object.entries(row).some(([key, value]) => {
        // Skip if column is hidden
        if (selectedColumnFilters[key]) return false;
        
        // Check if value contains search query
        if (value === null || value === undefined) return false;
        
        let stringValue = String(value);
        if (typeof value === 'object') {
          try {
            stringValue = JSON.stringify(value);
          } catch (e) {
            stringValue = String(value);
          }
        }
        
        return stringValue.toLowerCase().includes(query.toLowerCase());
      });
    });
    
    setFilteredData(filtered);
  };

  const handleSort = (columnName: string) => {
    if (sortColumn === columnName) {
      // Toggle sort direction
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Sort by new column
      setSortColumn(columnName);
      setSortDirection('asc');
    }
    
    // Sort data
    const sorted = [...filteredData].sort((a, b) => {
      const aVal = a[columnName];
      const bVal = b[columnName];
      
      // Handle nulls
      if (aVal === null && bVal === null) return 0;
      if (aVal === null) return sortDirection === 'asc' ? -1 : 1;
      if (bVal === null) return sortDirection === 'asc' ? 1 : -1;
      
      // Sort by type
      if (typeof aVal === 'string' && typeof bVal === 'string') {
        return sortDirection === 'asc'
          ? aVal.localeCompare(bVal)
          : bVal.localeCompare(aVal);
      }
      
      if (typeof aVal === 'number' && typeof bVal === 'number') {
        return sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
      }
      
      // Convert to string and compare
      const aStr = String(aVal);
      const bStr = String(bVal);
      
      return sortDirection === 'asc'
        ? aStr.localeCompare(bStr)
        : bStr.localeCompare(aStr);
    });
    
    setFilteredData(sorted);
  };

  const toggleColumnVisibility = (columnName: string) => {
    setSelectedColumnFilters({
      ...selectedColumnFilters,
      [columnName]: !selectedColumnFilters[columnName]
    });
  };

  const renderCellValue = (value: any) => {
    if (value === null || value === undefined) {
      return <Text style={[styles.nullValue, { color: colors.textSecondary }]}>NULL</Text>;
    }
    
    if (typeof value === 'object') {
      try {
        return <Text style={{ color: colors.text }}>{JSON.stringify(value)}</Text>;
      } catch (e) {
        return <Text style={{ color: colors.text }}>{String(value)}</Text>;
      }
    }
    
    if (typeof value === 'boolean') {
      return (
        <View style={[styles.booleanBadge, { backgroundColor: value ? '#DCFCE7' : '#FEE2E2' }]}>
          <Text style={{ color: value ? '#22C55E' : '#EF4444', fontWeight: '600' }}>
            {value ? 'true' : 'false'}
          </Text>
        </View>
      );
    }
    
    return <Text style={{ color: colors.text }}>{String(value)}</Text>;
  };

  const refreshData = () => {
    if (selectedTable) {
      loadTableData(selectedTable);
    }
  };

  const executeCustomQuery = async () => {
    if (!sqlQuery.trim()) return;
    
    setLoading(true);
    setError(null);
    
    try {
      // Note: Firebase doesn't support direct SQL execution
      // This is a simplified implementation that can only handle basic collection reads
      if (sqlQuery.toLowerCase().includes('select') && selectedTable) {
        const collectionRef = collection(db, selectedTable);
        const querySnapshot = await getDocs(query(collectionRef, limit(100)));
        
        const data = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        
        setData(data);
        setFilteredData(data);
        
        // Update columns based on the first row
        if (data.length > 0) {
          const newColumns = Object.keys(data[0]).map(key => ({
            name: key,
            type: typeof data[0][key]
          }));
          setColumns(newColumns);
        }
      } else {
        setError('Custom SQL queries are not fully supported with Firebase. Only basic SELECT queries will work.');
        refreshData();
      }
    } catch (error: any) {
      console.error('Error executing custom query:', error);
      setError(error.message || 'Failed to execute query');
    } finally {
      setLoading(false);
    }
  };

  if (!selectedTable) {
    return (
      <View style={[styles.emptyContainer, { backgroundColor: colors.card }]}>
        <Feather name="database" size={48} color={colors.textSecondary} style={{ marginBottom: 16 }} />
        <Text style={[styles.emptyTitle, { color: colors.text }]}>
          Select a Table
        </Text>
        <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
          Choose a table from the schema to view its data
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <View style={styles.tableInfo}>
          <Feather name="database" size={18} color={colors.primary} style={{ marginRight: 8 }} />
          <Text style={[styles.tableTitle, { color: colors.text }]}>
            {selectedTable}
          </Text>
          {loading && <ActivityIndicator size="small" color={colors.primary} style={{ marginLeft: 8 }} />}
        </View>
        
        <View style={styles.actions}>
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: colors.subtle }]}
            onPress={refreshData}
          >
            <Feather name="refresh-cw" size={16} color={colors.textSecondary} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: showSqlEditor ? colors.primaryLight : colors.subtle }]}
            onPress={() => setShowSqlEditor(!showSqlEditor)}
          >
            <Code size={16} color={showSqlEditor ? colors.primary : colors.textSecondary} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: colors.subtle }]}
            onPress={() => {/* Export to CSV would go here */}}
          >
            <Feather name="download" size={16} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </View>
      
      {showSqlEditor && (
        <View style={[styles.sqlEditor, { borderBottomColor: colors.border }]}>
          <View style={styles.sqlEditorHeader}>
            <Text style={[styles.sqlEditorTitle, { color: colors.text }]}>SQL Query</Text>
            <View style={styles.sqlEditorActions}>
              <TouchableOpacity 
                style={[styles.executeButton, { backgroundColor: colors.primary }]}
                onPress={executeCustomQuery}
              >
                <Text style={{ color: '#fff', fontWeight: '600' }}>Execute</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.copyButton}
                onPress={() => {/* Copy SQL would go here */}}
              >
                <Feather name="copy" size={14} color={colors.primary} />
                <Text style={[styles.copyButtonText, { color: colors.primary }]}>Copy</Text>
              </TouchableOpacity>
            </View>
          </View>
          <View style={[styles.sqlEditorContent, { backgroundColor: colors.subtle }]}>
            <TextInput
              style={[styles.sqlText, { color: colors.text }]}
              value={sqlQuery}
              onChangeText={setSqlQuery}
              multiline
              numberOfLines={3}
              placeholder="Enter SQL query..."
              placeholderTextColor={colors.textSecondary}
            />
          </View>
        </View>
      )}
      
      {error && (
        <View style={[styles.errorContainer, { backgroundColor: '#FEE2E2' }]}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity onPress={() => setError(null)}>
            <Feather name="x" size={16}  color={colors.text} />
          </TouchableOpacity>
        </View>
      )}
      
      <View style={[styles.searchBar, { borderBottomColor: colors.border }]}>
        <View style={[styles.searchInputContainer, { backgroundColor: colors.subtle }]}>
          <Feather name="search" size={16} color={colors.textSecondary} style={{ marginRight: 8 }} />
          <TextInput 
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="Search data..."
            placeholderTextColor={colors.textSecondary}
            value={searchQuery}
            onChangeText={handleSearch}
          />
          {searchQuery !== '' && (
            <TouchableOpacity 
              style={styles.clearSearch}
              onPress={() => handleSearch('')}
            >
              <Feather name="x" size={14} color={colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
        
        <View style={styles.columnFilters}>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.columnFiltersContent}
          >
            {columns.map(column => (
              <TouchableOpacity
                key={column.name}
                style={[
                  styles.columnFilter,
                  {
                    backgroundColor: selectedColumnFilters[column.name]
                      ? colors.subtle
                      : colors.primaryLight,
                    borderColor: selectedColumnFilters[column.name]
                      ? colors.border
                      : colors.primary,
                  },
                ]}
                onPress={() => toggleColumnVisibility(column.name)}
              >
                <Text
                  style={[
                    styles.columnFilterText,
                    {
                      color: selectedColumnFilters[column.name]
                        ? colors.textSecondary
                        : colors.primary,
                    },
                  ]}
                >
                  {column.name}
                </Text>
                {selectedColumnFilters[column.name] && (
                  <Feather name="x" size={12} color={colors.textSecondary} style={{ marginLeft: 4 }} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
      
      <ScrollView
        style={styles.tableContainer}
        contentContainerStyle={{ paddingBottom: 80 }}
      >
        <ScrollView horizontal showsHorizontalScrollIndicator={true}>
          <View>
            {/* Table Header */}
            <View style={[styles.tableHeader, { backgroundColor: colors.subtle }]}>
              {columns
                .filter(col => !selectedColumnFilters[col.name])
                .map(column => (
                  <TouchableOpacity
                    key={column.name}
                    style={[
                      styles.tableHeaderCell,
                      sortColumn === column.name && { backgroundColor: colors.primaryLight },
                    ]}
                    onPress={() => handleSort(column.name)}
                  >
                    <Text
                      style={[
                        styles.tableHeaderText,
                        {
                          color: sortColumn === column.name
                            ? colors.primary
                            : colors.text,
                          fontWeight: sortColumn === column.name ? '700' : '600',
                        },
                      ]}
                    >
                      {column.name}
                    </Text>
                    {sortColumn === column.name && (
                      sortDirection === 'asc' ? (
                        <Feather name="arrow-up" size={12} color={colors.primary} />
                      ) : (
                        <Feather name="arrow-down" size={12} color={colors.primary} />
                      )
                    )}
                  </TouchableOpacity>
                ))}
            </View>
            
            {/* Table Rows */}
            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
              </View>
            ) : (
              filteredData.length === 0 ? (
                <View style={styles.noDataContainer}>
                  <Text style={[styles.noDataText, { color: colors.textSecondary }]}>
                    No data found
                  </Text>
                </View>
              ) : (
                filteredData.map((row, rowIndex) => (
                  <View
                    key={rowIndex}
                    style={[
                      styles.tableRow,
                      rowIndex % 2 === 0 && { backgroundColor: colors.subtle + '33' },
                      rowIndex === filteredData.length - 1 && { borderBottomWidth: 0 },
                    ]}
                  >
                    {columns
                      .filter(col => !selectedColumnFilters[col.name])
                      .map(column => (
                        <View key={column.name} style={styles.tableCell}>
                          {renderCellValue(row[column.name])}
                        </View>
                      ))}
                  </View>
                ))
              )
            )}
          </View>
        </ScrollView>
      </ScrollView>
      
      <View style={[styles.footer, { borderTopColor: colors.border }]}>
        <Text style={[styles.rowCount, { color: colors.textSecondary }]}>
          {filteredData.length} rows {data.length !== filteredData.length && `(filtered from ${data.length})`}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
    margin: 12,
    marginBottom: 0,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  tableInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tableTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  actions: {
    flexDirection: 'row',
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  sqlEditor: {
    padding: 16,
    borderBottomWidth: 1,
  },
  sqlEditorHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  sqlEditorTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  sqlEditorActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  executeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    marginRight: 12,
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  copyButtonText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  sqlEditorContent: {
    padding: 12,
    borderRadius: 8,
  },
  sqlText: {
    fontFamily: 'monospace',
    fontSize: 12,
    minHeight: 60,
  },
  errorContainer: {
    margin: 16,
    marginTop: 0,
    padding: 12,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  errorText: {
    color: '#B91C1C',
    flex: 1,
  },
  searchBar: {
    padding: 16,
    borderBottomWidth: 1,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    height: 40,
    borderRadius: 8,
    marginBottom: 12,
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 14,
  },
  clearSearch: {
    padding: 4,
  },
  columnFilters: {
    marginTop: 4,
  },
  columnFiltersContent: {
    paddingRight: 16,
  },
  columnFilter: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    borderWidth: 1,
  },
  columnFilterText: {
    fontSize: 12,
    fontWeight: '500',
  },
  tableContainer: {
    flex: 1,
  },
  tableHeader: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  tableHeaderCell: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    width: 180,
    flexDirection: 'row',
    alignItems: 'center',
  },
  tableHeaderText: {
    fontSize: 13,
    marginRight: 4,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  tableCell: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    width: 180,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    borderRadius: 12,
    margin: 12,
    marginBottom: 0,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
  },
  nullValue: {
    fontStyle: 'italic',
    opacity: 0.7,
  },
  booleanBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
  },
  rowCount: {
    fontSize: 12,
  },
  loadingContainer: {
    padding: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noDataContainer: {
    padding: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noDataText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
});

export default DataBrowser; 