import React, { useState } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TouchableOpacity, 
  ScrollView, 
  TextInput, 
  ActivityIndicator,
  Alert 
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useDatabaseExplorer } from '@/hooks/useDatabaseExplorer';
import * as Clipboard from 'expo-clipboard';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';

interface DataBrowserProps {
  colors: any;
  schema: any;
  selectedTable: string | null;
}

export const DataBrowser = ({ colors, selectedTable }: DataBrowserProps) => {
  const [showFilters, setShowFilters] = useState(false);
  
  const {
    data,
    columns,
    loading,
    error,
    searchQuery,
    setSearchQuery,
    sortColumn,
    sortDirection,
    handleSort,
    refreshData,
    exportAsJson,
    exportAsCsv,
    currentPage,
    totalPages,
    goToPage
  } = useDatabaseExplorer(selectedTable);

  const handleExportJson = async () => {
    try {
      const json = await exportAsJson();
      const fileUri = FileSystem.documentDirectory + `${selectedTable}_export.json`;
      await FileSystem.writeAsStringAsync(fileUri, json);
      
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri);
      } else {
        Alert.alert('Success', 'Data exported to: ' + fileUri);
      }
    } catch (err: any) {
      Alert.alert('Export Error', err.message);
    }
  };

  const handleExportCsv = async () => {
    try {
      const csv = await exportAsCsv();
      const fileUri = FileSystem.documentDirectory + `${selectedTable}_export.csv`;
      await FileSystem.writeAsStringAsync(fileUri, csv);
      
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri);
      } else {
        Alert.alert('Success', 'Data exported to: ' + fileUri);
      }
    } catch (err: any) {
      Alert.alert('Export Error', err.message);
    }
  };

  const handleCopyValue = async (value: any) => {
    await Clipboard.setStringAsync(String(value));
    Alert.alert('Copied', 'Value copied to clipboard');
  };

  const renderSortIcon = (column: string) => {
    if (sortColumn !== column) return null;
    return sortDirection === 'asc' ? 
      <Feather name="arrow-up" size={16} color={colors.primary} /> : 
      <Feather name="arrow-down" size={16} color={colors.primary} />;
  };

  const renderValue = (value: any) => {
    if (value === null || value === undefined) {
      return <Text style={[styles.nullValue, { color: colors.textSecondary }]}>null</Text>;
    }
    if (typeof value === 'boolean') {
      return <Text style={{ color: value ? colors.success : colors.error }}>{String(value)}</Text>;
    }
    if (typeof value === 'object') {
      return <Text style={{ color: colors.primary }}>{JSON.stringify(value)}</Text>;
    }
    return <Text style={{ color: colors.text }}>{String(value)}</Text>;
  };

  if (!selectedTable) {
    return (
      <View style={[styles.emptyState, { backgroundColor: colors.card }]}>
        <Feather name="database" size={48} color={colors.textSecondary} />
        <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
          Select a table to view data
        </Text>
      </View>
    );
  }

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.card }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading {selectedTable}...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: colors.card }]}>
        <Text style={[styles.errorText, { color: colors.error }]}>Error: {error}</Text>
        <TouchableOpacity 
          style={[styles.retryButton, { backgroundColor: colors.primary }]}
          onPress={refreshData}
        >
          <Feather name="refresh-cw" size={20}  color={colors.text} />
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      {/* Toolbar */}
      <View style={[styles.toolbar, { borderBottomColor: colors.border }]}>
        <View style={styles.toolbarLeft}>
          <View style={[styles.searchContainer, { backgroundColor: colors.background }]}>
            <Feather name="search" size={20} color={colors.textSecondary} />
            <TextInput
              style={[styles.searchInput, { color: colors.text }]}
              placeholder="Search..."
              placeholderTextColor={colors.textSecondary}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>
        </View>
        
        <View style={styles.toolbarRight}>
          <TouchableOpacity 
            style={styles.toolButton}
            onPress={() => setShowFilters(!showFilters)}
          >
            <Feather name="filter" size={20} color={colors.text} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.toolButton}
            onPress={refreshData}
          >
            <Feather name="refresh-cw" size={20} color={colors.text} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.toolButton}
            onPress={handleExportJson}
          >
            <Feather name="download" size={20} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Data Table */}
      <ScrollView horizontal showsHorizontalScrollIndicator={true}>
        <View>
          {/* Header */}
          <View style={[styles.tableHeader, { backgroundColor: colors.background }]}>
            {columns.map((column, index) => (
              <TouchableOpacity
                key={column}
                style={[
                  styles.headerCell,
                  index === 0 && styles.firstCell,
                  { borderRightColor: colors.border }
                ]}
                onPress={() => handleSort(column)}
              >
                <View style={styles.headerContent}>
                  <Text style={[styles.headerText, { color: colors.text }]}>
                    {column}
                  </Text>
                  {renderSortIcon(column)}
                </View>
              </TouchableOpacity>
            ))}
          </View>

          {/* Rows */}
          <ScrollView style={styles.tableBody} showsVerticalScrollIndicator={true}>
            {data.map((row, rowIndex) => (
              <View
                key={row.id || rowIndex}
                style={[
                  styles.tableRow,
                  rowIndex % 2 === 1 && { backgroundColor: colors.background },
                  { borderBottomColor: colors.border }
                ]}
              >
                {columns.map((column, colIndex) => (
                  <TouchableOpacity
                    key={`${rowIndex}-${column}`}
                    style={[
                      styles.cell,
                      colIndex === 0 && styles.firstCell,
                      { borderRightColor: colors.border }
                    ]}
                    onPress={() => handleCopyValue(row[column])}
                  >
                    {renderValue(row[column])}
                  </TouchableOpacity>
                ))}
              </View>
            ))}
          </ScrollView>
        </View>
      </ScrollView>

      {/* Pagination */}
      {totalPages > 1 && (
        <View style={[styles.pagination, { borderTopColor: colors.border }]}>
          <TouchableOpacity
            style={[styles.pageButton, { opacity: currentPage === 1 ? 0.5 : 1 }]}
            onPress={() => goToPage(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <Feather name="chevron-left" size={20} color={colors.text} />
          </TouchableOpacity>
          
          <Text style={[styles.pageInfo, { color: colors.text }]}>
            Page {currentPage} of {totalPages}
          </Text>
          
          <TouchableOpacity
            style={[styles.pageButton, { opacity: currentPage === totalPages ? 0.5 : 1 }]}
            onPress={() => goToPage(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            <Feather name="chevron-right" size={20} color={colors.text} />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    borderRadius: 12,
  },
  emptyStateText: {
    marginTop: 16,
    fontSize: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    borderRadius: 12,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    borderRadius: 12,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  toolbar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  toolbarLeft: {
    flex: 1,
    marginRight: 16,
  },
  toolbarRight: {
    flexDirection: 'row',
    gap: 12,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    borderRadius: 8,
    height: 40,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  toolButton: {
    padding: 8,
  },
  tableHeader: {
    flexDirection: 'row',
    borderBottomWidth: 2,
  },
  headerCell: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    minWidth: 120,
    borderRightWidth: 1,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  headerText: {
    fontWeight: '700',
    fontSize: 14,
  },
  tableBody: {
    maxHeight: 400,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  cell: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    minWidth: 120,
    borderRightWidth: 1,
  },
  firstCell: {
    minWidth: 150,
  },
  nullValue: {
    fontStyle: 'italic',
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    borderTopWidth: 1,
    gap: 16,
  },
  pageButton: {
    padding: 8,
  },
  pageInfo: {
    fontSize: 14,
  },
});