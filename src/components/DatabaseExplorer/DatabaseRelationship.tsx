import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, ScrollView, Dimensions } from 'react-native';
import { PanGestureHandler, PinchGestureHandler, State, GestureHandlerRootView } from 'react-native-gesture-handler';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { canUseNativeDriver } from '@/utils/platformUtils';

export function DatabaseRelationship() {
  const { colors } = useTheme();
  
  // Animation variables
  const infoFadeAnim = useRef(new Animated.Value(0)).current;
  const translateXAnim = useRef(new Animated.Value(0)).current;
  const translateYAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  
  // State variables 
  const [showInfo, setShowInfo] = useState(false);
  const [scale, setScale] = useState(1);
  const [translateX, setTranslateX] = useState(0);
  const [translateY, setTranslateY] = useState(0);
  
  // Show info panel temporarily
  Animated.sequence([
    Animated.delay(2000),
    Animated.timing(infoFadeAnim, {
      toValue: 0,
      duration: 1000,
      useNativeDriver: canUseNativeDriver()
    })
  ]).start(() => setShowInfo(false));

  // Handle pan gesture (move the diagram)
  const onPanGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateXAnim, translationY: translateYAnim } }],
    { useNativeDriver: canUseNativeDriver() }
  );

  // Handle pinch gesture (zoom the diagram)
  const onPinchGestureEvent = Animated.event(
    [{ nativeEvent: { scale: scaleAnim } }],
    { useNativeDriver: canUseNativeDriver() }
  );

  // Reset the view to default
  const resetView = () => {
    Animated.parallel([
      Animated.timing(translateXAnim, {
        toValue: -translateX,
        duration: 500,
        useNativeDriver: canUseNativeDriver()
      }),
      Animated.timing(translateYAnim, {
        toValue: -translateY,
        duration: 500,
        useNativeDriver: canUseNativeDriver()
      }),
      Animated.timing(scaleAnim, {
        toValue: 1/scale,
        duration: 500,
        useNativeDriver: canUseNativeDriver()
      })
    ]).start(() => {
      setScale(1);
      setTranslateX(0);
      setTranslateY(0);
      translateXAnim.setValue(0);
      translateYAnim.setValue(0);
      scaleAnim.setValue(1);
    });
  };

  // Zoom in and out functions
  const zoomIn = () => {
    const newScale = Math.min(scale * 1.2, 3);
    Animated.timing(scaleAnim, {
      toValue: newScale / scale,
      duration: 300,
      useNativeDriver: canUseNativeDriver()
    }).start(() => {
      setScale(newScale);
      scaleAnim.setValue(1);
    });
  };

  const zoomOut = () => {
    const newScale = Math.max(scale / 1.2, 0.5);
    Animated.timing(scaleAnim, {
      toValue: newScale / scale,
      duration: 300,
      useNativeDriver: canUseNativeDriver()
    }).start(() => {
      setScale(newScale);
      scaleAnim.setValue(1);
    });
  };

  return (
    <TouchableOpacity 
      style={[styles.infoButton, { backgroundColor: colors.primaryLight }]}
      onPress={() => {
        setShowInfo(true);
        Animated.timing(infoFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: canUseNativeDriver()
        }).start();
        
        // Auto-hide after 3 seconds
        setTimeout(() => {
          Animated.timing(infoFadeAnim, {
            toValue: 0,
            duration: 500,
            useNativeDriver: canUseNativeDriver()
          }).start(() => setShowInfo(false));
        }, 3000);
      }}
    >
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  infoButton: {
    // styles here
  },
  // other styles
}); 