import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ScrollView, Animated, Dimensions } from 'react-native';
import { Feather } from '@expo/vector-icons';
import Svg, { Path, Circle, Rect, Text as SvgText, G } from 'react-native-svg';

// Get screen dimensions
const { width } = Dimensions.get('window');

interface Constraint {
  type: string;
  definition: string;
  // Add other properties if needed
}

interface ForeignKey {
  definition: string;
  // Add other properties if needed
}

interface SchemaVisualizerProps {
  schema: any;
  colors: any;
  onSelectTable: (tableName: string) => void;
}

export const SchemaVisualizer = ({ schema, colors, onSelectTable }: SchemaVisualizerProps) => {
  const [selectedTable, setSelectedTable] = useState<string | null>(null);
  const [focusedNode, setFocusedNode] = useState<string | null>(null);
  const [highlightRelations, setHighlightRelations] = useState<boolean>(false);
  const [scale, setScale] = useState(1);

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // Process schema to get table nodes and relationships
  const tables = Object.keys(schema.tables);
  
  // Get all relationships between tables
  const relationships = tables.flatMap(tableName => {
    const table = schema.tables[tableName];
    return table.foreign_keys.map((fk: ForeignKey) => {
      const matches = fk.definition.match(/FOREIGN KEY \(([^)]+)\) REFERENCES ([^(]+)\(([^)]+)\)/);
      if (!matches) return null;
      
      return {
        source: tableName,
        sourceField: matches[1].trim(),
        target: matches[2].trim(),
        targetField: matches[3].trim()
      };
    }).filter(Boolean);
  });

  // Group tables by categories for better visualization
  const tableGroups: { [key: string]: string[] } = {
    'User Data': ['profiles', 'dietary_preferences'],
    'Nutrition': ['meals', 'meal_items', 'water_intake'],
    'Recipes': ['alternative_recipes', 'recipe_ingredients', 'recipe_instructions', 'recipe_health_benefits'],
    'Billing': ['stripe_customers', 'stripe_subscriptions', 'stripe_orders']
  };

  // Calculate positions for each table group
  const calculateNodePositions = () => {
    const nodes: { [key: string]: { x: number; y: number; group: string } } = {};
    const baseRadius = 250;
    const centerX = width / 2;
    const centerY = 300;
    
    // Position groups in a circle
    let groupIndex = 0;
    Object.entries(tableGroups).forEach(([groupName, groupTables]) => {
      const groupAngle = (groupIndex / Object.keys(tableGroups).length) * 2 * Math.PI;
      const groupX = centerX + Math.cos(groupAngle) * baseRadius;
      const groupY = centerY + Math.sin(groupAngle) * baseRadius;
      
      // Position tables within each group in a smaller circle
      groupTables.forEach((tableName, tableIndex) => {
        if (!tables.includes(tableName)) return;
        
        const tableAngle = (tableIndex / groupTables.length) * 2 * Math.PI;
        const radius = 100;
        const x = groupX + Math.cos(tableAngle) * radius;
        const y = groupY + Math.sin(tableAngle) * radius;
        
        nodes[tableName] = { x, y, group: groupName };
      });
      
      groupIndex++;
    });
    
    // Handle any tables not in groups
    tables.filter(t => !Object.values(tableGroups).flat().includes(t)).forEach((tableName, index) => {
      const angle = (index / tables.length) * 2 * Math.PI;
      const x = centerX + Math.cos(angle) * (baseRadius * 0.6);
      const y = centerY + Math.sin(angle) * (baseRadius * 0.6);
      
      nodes[tableName] = { x, y, group: 'Other' };
    });
    
    return nodes;
  };

  const nodePositions = calculateNodePositions();

  // Calculate path for relationships
  const calculatePath = (source: string, target: string) => {
    const sourceNode = nodePositions[source];
    const targetNode = nodePositions[target];
    
    if (!sourceNode || !targetNode) return '';
    
    const startX = sourceNode.x;
    const startY = sourceNode.y;
    const endX = targetNode.x;
    const endY = targetNode.y;
    
    // Create curved path
    const dx = endX - startX;
    const dy = endY - startY;
    const distance = Math.sqrt(dx * dx + dy * dy);
    
    // Determine control points for curve
    const midX = (startX + endX) / 2;
    const midY = (startY + endY) / 2;
    
    // Add some curvature
    const curveFactor = 30;
    const controlX = midX - dy * curveFactor / distance;
    const controlY = midY + dx * curveFactor / distance;
    
    return `M${startX},${startY} Q${controlX},${controlY} ${endX},${endY}`;
  };

  useEffect(() => {
    // Animate on mount
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1.05,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Get related tables for a selected table
  const getRelatedTables = (tableName: string) => {
    return relationships.filter(rel => 
      rel.source === tableName || rel.target === tableName
    );
  };

  // When clicking a table node
  const handleSelectTable = (tableName: string) => {
    setSelectedTable(tableName);
    setHighlightRelations(true);
    onSelectTable(tableName);
  };

  // Handle node hover (focus)
  const handleFocusNode = (tableName: string) => {
    setFocusedNode(tableName);
  };

  // Check if a relationship should be highlighted
  const isRelationshipHighlighted = (source: string, target: string) => {
    if (!highlightRelations) return false;
    if (selectedTable === source || selectedTable === target) return true;
    return false;
  };

  // Get primary key count for a table
  const getPrimaryKeyCount = (tableName: string) => {
    const table = schema.tables[tableName];
    if (!table) return 0;
    
    const pkConstraint = table.constraints.find((c: Constraint) => c.type === 'p');
    if (!pkConstraint) return 0;
    
    const match = pkConstraint.definition.match(/PRIMARY KEY \(([^)]+)\)/);
    if (!match) return 0;
    
    return match[1].split(',').length;
  };

  // Get foreign key count for a table
  const getForeignKeyCount = (tableName: string) => {
    const table = schema.tables[tableName];
    if (!table) return 0;
    
    return table.foreign_keys.length;
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Feather name="database" size={22} color={colors.primary} style={{ marginRight: 8 }} />
          <Text style={[styles.title, { color: colors.text }]}>Schema Visualization</Text>
        </View>
      </View>
      
      <View style={styles.legendContainer}>
        <View style={styles.legendItem}>
          <View style={[styles.tableLegend, { backgroundColor: colors.card, borderColor: colors.border }]} />
          <Text style={[styles.legendText, { color: colors.textSecondary }]}>Table</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.relationLegend, { backgroundColor: colors.primary }]} />
          <Text style={[styles.legendText, { color: colors.textSecondary }]}>Relationship</Text>
        </View>
        <View style={styles.legendItem}>
          <Feather name="shield" size={12} color={colors.primary} />
          <Text style={[styles.legendText, { color: colors.textSecondary }]}>RLS Enabled</Text>
        </View>
      </View>
      
      <Animated.View
        style={[
          styles.diagramContainer,
          {
            backgroundColor: colors.subtle,
            borderColor: colors.border,
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }]
          }
        ]}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          maximumZoomScale={2}
          minimumZoomScale={0.5}
          bouncesZoom
        >
          <View style={styles.diagramContent}>
            <Svg height="800" width="800" viewBox="0 0 800 800">
              {/* Draw relationship lines */}
              {relationships.map((rel, index) => (
                <Path
                  key={`rel-${index}`}
                  d={calculatePath(rel.source, rel.target)}
                  stroke={isRelationshipHighlighted(rel.source, rel.target) ? colors.primary : colors.border}
                  strokeWidth={isRelationshipHighlighted(rel.source, rel.target) ? 2 : 1}
                  opacity={isRelationshipHighlighted(rel.source, rel.target) ? 1 : 0.5}
                  fill="none"
                />
              ))}
              
              {/* Draw tables */}
              {tables.map((tableName) => {
                const table = schema.tables[tableName];
                const node = nodePositions[tableName];
                const isSelected = selectedTable === tableName;
                const isFocused = focusedNode === tableName;
                const hasRelationWithSelected = selectedTable && 
                  relationships.some(rel => 
                    (rel.source === selectedTable && rel.target === tableName) ||
                    (rel.target === selectedTable && rel.source === tableName)
                  );
                
                if (!node) return null;
                
                const nodeWidth = 120;
                const nodeHeight = 70;
                
                return (
                  <G key={tableName}>
                    {/* Table node with shadow effect */}
                    <Rect
                      x={node.x - nodeWidth/2 + 3}
                      y={node.y - nodeHeight/2 + 3}
                      width={nodeWidth}
                      height={nodeHeight}
                      rx={8}
                      ry={8}
                      fill="rgba(0,0,0,0.1)"
                      opacity={0.3}
                    />
                    <Rect
                      x={node.x - nodeWidth/2}
                      y={node.y - nodeHeight/2}
                      width={nodeWidth}
                      height={nodeHeight}
                      rx={8}
                      ry={8}
                      fill={isSelected ? colors.primaryLight : colors.card}
                      stroke={hasRelationWithSelected || isSelected ? colors.primary : colors.border}
                      strokeWidth={isSelected || hasRelationWithSelected ? 2 : 1}
                      onPress={() => handleSelectTable(tableName)}
                      onPressIn={() => handleFocusNode(tableName)}
                      onPressOut={() => setFocusedNode(null)}
                    />
                    
                    {/* Table name */}
                    <SvgText
                      x={node.x}
                      y={node.y - 12}
                      fontSize={12}
                      fontWeight="bold"
                      fill={isSelected ? colors.primary : colors.text}
                      textAnchor="middle"
                      onPress={() => handleSelectTable(tableName)}
                    >
                      {tableName}
                    </SvgText>
                    
                    {/* Stats */}
                    <SvgText
                      x={node.x}
                      y={node.y + 8}
                      fontSize={9}
                      fill={colors.textSecondary}
                      textAnchor="middle"
                      onPress={() => handleSelectTable(tableName)}
                    >
                      {table.columns.length} columns
                    </SvgText>
                    
                    {/* Key counts */}
                    <SvgText
                      x={node.x}
                      y={node.y + 22}
                      fontSize={9}
                      fill={colors.textSecondary}
                      textAnchor="middle"
                      onPress={() => handleSelectTable(tableName)}
                    >
                      {getPrimaryKeyCount(tableName)} PK • {getForeignKeyCount(tableName)} FK
                    </SvgText>
                    
                    {/* RLS badge */}
                    {table.is_rls_enabled && (
                      <Circle
                        cx={node.x + nodeWidth/2 - 10}
                        cy={node.y - nodeHeight/2 + 10}
                        r={8}
                        fill={colors.primaryLight}
                      />
                    )}
                    
                    {/* Group label - show only for first table in each group */}
                    {tableGroups[node.group] && tableGroups[node.group][0] === tableName && (
                      <SvgText
                        x={node.x}
                        y={node.y - nodeHeight/2 - 15}
                        fontSize={11}
                        fontWeight="bold"
                        fill={colors.primary}
                        textAnchor="middle"
                      >
                        {node.group}
                      </SvgText>
                    )}
                  </G>
                );
              })}
            </Svg>
          </View>
        </ScrollView>
      </Animated.View>
      
      <View style={styles.controlsContainer}>
        <View style={[styles.controls, { backgroundColor: colors.card, borderColor: colors.border }]}>
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: colors.subtle }]}
            onPress={() => setScale(Math.max(0.5, scale - 0.1))}
          >
            <Text style={[styles.controlButtonText, { color: colors.text }]}>-</Text>
          </TouchableOpacity>
          
          <Text style={[styles.scaleText, { color: colors.textSecondary }]}>
            {Math.round(scale * 100)}%
          </Text>
          
          <TouchableOpacity
            style={[styles.controlButton, { backgroundColor: colors.subtle }]}
            onPress={() => setScale(Math.min(2, scale + 0.1))}
          >
            <Text style={[styles.controlButtonText, { color: colors.text }]}>+</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      {selectedTable && (
        <View style={[styles.selectionInfo, { backgroundColor: colors.primaryLight }]}>
          <Text style={[styles.selectionText, { color: colors.primary }]}>
            Selected: {selectedTable}
          </Text>
          <TouchableOpacity
            onPress={() => {
              setSelectedTable(null);
              setHighlightRelations(false);
            }}
          >
            <Text style={[styles.clearButton, { color: colors.primary }]}>Clear</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
  },
  legendContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  tableLegend: {
    width: 14,
    height: 14,
    borderRadius: 4,
    borderWidth: 1,
    marginRight: 4,
  },
  relationLegend: {
    width: 14,
    height: 2,
    marginRight: 4,
  },
  legendText: {
    fontSize: 11,
  },
  diagramContainer: {
    flex: 1,
    margin: 16,
    borderRadius: 16,
    borderWidth: 1,
    overflow: 'hidden',
  },
  scrollContent: {
    width: 800,
    height: 800,
  },
  diagramContent: {
    width: 800,
    height: 800,
  },
  controlsContainer: {
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  controlButton: {
    width: 26,
    height: 26,
    borderRadius: 13,
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  scaleText: {
    marginHorizontal: 10,
    fontSize: 12,
    fontWeight: '500',
  },
  selectionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    margin: 16,
    marginTop: 0,
    borderRadius: 8,
  },
  selectionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  clearButton: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default SchemaVisualizer; 