import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { Feather } from '@expo/vector-icons';

// Types
interface Column {
  name: string;
  type: string;
  is_nullable: boolean;
  has_default: boolean;
}

interface Constraint {
  name: string;
  type: string;
  definition: string;
}

interface ForeignKey {
  name: string;
  definition: string;
}

interface TableStructure {
  name: string;
  columns: Column[];
  constraints: Constraint[];
  foreign_keys: ForeignKey[];
  is_rls_enabled: boolean;
}

interface RelationshipInfo {
  sourceName: string;
  sourceField: string;
  targetName: string;
  targetField: string;
  onDelete?: string;
}

interface TableDetailViewProps {
  table: TableStructure;
  relatedTables: RelationTable[];
  colors: any;
  onBack: () => void;
  onSelectTable: (tableName: string) => void;
}

interface RelationTable {
  name: string;
  relation: {
    sourceField: string;
    targetField: string;
    type: string;
  };
  isSource: boolean;
}

export const TableDetailView = ({
  table,
  relatedTables,
  colors,
  onBack,
  onSelectTable,
}: TableDetailViewProps) => {
  const [showColumns, setShowColumns] = useState(true);
  const [showConstraints, setShowConstraints] = useState(true);
  const [showRelationships, setShowRelationships] = useState(true);
  const [filterPrimaryKeys, setFilterPrimaryKeys] = useState(false);
  const [filterForeignKeys, setFilterForeignKeys] = useState(false);

  // Get Primary Keys
  const primaryKeys = table.constraints
    .filter((c) => c.type === 'p')
    .map((c) => {
      const match = c.definition.match(/PRIMARY KEY \(([^)]+)\)/);
      return match ? match[1].split(',').map(k => k.trim()) : [];
    })
    .flat();

  // Get Foreign Keys
  const foreignKeys = table.foreign_keys
    .map((fk) => {
      const matches = fk.definition.match(/FOREIGN KEY \(([^)]+)\)/);
      return matches ? matches[1].split(',').map(k => k.trim()) : [];
    })
    .flat();

  // Filter columns based on active filters
  const filteredColumns = table.columns.filter((column) => {
    if (!filterPrimaryKeys && !filterForeignKeys) return true;
    if (filterPrimaryKeys && primaryKeys.includes(column.name)) return true;
    if (filterForeignKeys && foreignKeys.includes(column.name)) return false;
    return !filterPrimaryKeys && !filterForeignKeys;
  });

  // Format column type for display
  const formatType = (type: string) => {
    if (type.includes('character varying')) {
      return type.replace('character varying', 'varchar');
    }
    if (type.includes('timestamp with time zone')) {
      return 'timestamp tz';
    }
    return type;
  };

  // Determine constraint type label
  const getConstraintLabel = (type: string) => {
    switch (type) {
      case 'p':
        return { label: 'PRIMARY KEY', color: '#F59E0B', bgColor: '#FEF9C3' };
      case 'f':
        return { label: 'FOREIGN KEY', color: '#3B82F6', bgColor: '#DBEAFE' };
      case 'u':
        return { label: 'UNIQUE', color: '#10B981', bgColor: '#D1FAE5' };
      default:
        return { label: type.toUpperCase(), color: colors.text, bgColor: colors.subtle };
    }
  };

  // Extract related table info
  const getRelationshipInfo = (fk: ForeignKey): RelationshipInfo | null => {
    const matches = fk.definition.match(/FOREIGN KEY \(([^)]+)\) REFERENCES ([^(]+)\(([^)]+)\)( ON DELETE ([A-Z]+))?/);
    if (!matches) return null;

    return {
      sourceName: table.name,
      sourceField: matches[1].trim(),
      targetName: matches[2].trim(),
      targetField: matches[3].trim(),
      onDelete: matches[5],
    };
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.subtle }]}
          onPress={onBack}
        >
          <Feather name="arrow-left" size={16} color={colors.text} />
        </TouchableOpacity>
        <View style={styles.headerTitle}>
          <Feather name="database" size={18} color={colors.primary} style={{ marginRight: 8 }} />
          <Text style={[styles.tableName, { color: colors.text }]}>{table.name}</Text>
          {table.is_rls_enabled && (
            <View style={[styles.rlsBadge, { backgroundColor: colors.primaryLight }]}>
              <Feather name="lock" size={10} color={colors.primary} />
              <Text style={[styles.rlsText, { color: colors.primary }]}>RLS</Text>
            </View>
          )}
        </View>
      </View>

      <ScrollView style={styles.contentScroll}>
        {/* Columns Section */}
        <View style={styles.sectionContainer}>
          <TouchableOpacity
            style={[styles.sectionHeader, { borderBottomColor: showColumns ? colors.border : 'transparent' }]}
            onPress={() => setShowColumns(!showColumns)}
          >
            <View style={styles.sectionTitleContainer}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Columns
              </Text>
              <View style={styles.sectionBadge}>
                <Text style={[styles.sectionBadgeText, { color: colors.textSecondary }]}>
                  {table.columns.length}
                </Text>
              </View>
            </View>
            <View style={styles.sectionHeaderRight}>
              {showColumns && (
                <View style={styles.filterContainer}>
                  <TouchableOpacity
                    style={[
                      styles.filterBadge,
                      {
                        backgroundColor: filterPrimaryKeys
                          ? '#FEF9C3'
                          : colors.subtle,
                      },
                    ]}
                    onPress={() => setFilterPrimaryKeys(!filterPrimaryKeys)}
                  >
                    <Text
                      style={[
                        styles.filterText,
                        { color: filterPrimaryKeys ? '#F59E0B' : colors.textSecondary },
                      ]}
                    >
                      PK
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.filterBadge,
                      {
                        backgroundColor: filterForeignKeys
                          ? '#DBEAFE'
                          : colors.subtle,
                      },
                    ]}
                    onPress={() => setFilterForeignKeys(!filterForeignKeys)}
                  >
                    <Text
                      style={[
                        styles.filterText,
                        { color: filterForeignKeys ? '#3B82F6' : colors.textSecondary },
                      ]}
                    >
                      FK
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
              {showColumns ? (
                <Feather name="chevron-up" size={18} color={colors.textSecondary} />
              ) : (
                <Feather name="chevron-down" size={18} color={colors.textSecondary} />
              )}
            </View>
          </TouchableOpacity>

          {showColumns && (
            <View style={styles.sectionContent}>
              <View style={[styles.columnHeader, { borderBottomColor: colors.border }]}>
                <Text style={[styles.columnHeaderText, { color: colors.textSecondary, flex: 0.4 }]}>Name</Text>
                <Text style={[styles.columnHeaderText, { color: colors.textSecondary, flex: 0.4 }]}>Type</Text>
                <Text style={[styles.columnHeaderText, { color: colors.textSecondary, flex: 0.2, textAlign: 'right' }]}>Nullable</Text>
              </View>
              {filteredColumns.map((column, index) => {
                const isPrimaryKey = primaryKeys.includes(column.name);
                const isForeignKey = foreignKeys.includes(column.name);
                
                return (
                  <View
                    key={column.name}
                    style={[
                      styles.columnRow,
                      index < filteredColumns.length - 1 && {
                        borderBottomWidth: 1,
                        borderBottomColor: colors.border + '40',
                      },
                    ]}
                  >
                    <View style={[styles.columnName, { flex: 0.4 }]}>
                      <Text
                        style={[
                          styles.columnNameText,
                          {
                            color: isPrimaryKey
                              ? '#F59E0B'
                              : isForeignKey
                              ? '#3B82F6'
                              : colors.text,
                            fontWeight: isPrimaryKey || isForeignKey ? '600' : 'normal',
                          },
                        ]}
                        numberOfLines={1}
                      >
                        {column.name}
                      </Text>
                      <View style={styles.keyIndicators}>
                        {isPrimaryKey && (
                          <View
                            style={[
                              styles.keyIndicator,
                              { backgroundColor: '#FEF9C3' },
                            ]}
                          >
                            <Text style={[styles.keyIndicatorText, { color: '#F59E0B' }]}>
                              PK
                            </Text>
                          </View>
                        )}
                        {isForeignKey && (
                          <View
                            style={[
                              styles.keyIndicator,
                              { backgroundColor: '#DBEAFE' },
                            ]}
                          >
                            <Text
                              style={[styles.keyIndicatorText, { color: '#3B82F6' }]}
                            >
                              FK
                            </Text>
                          </View>
                        )}
                      </View>
                    </View>
                    <Text
                      style={[
                        styles.columnType,
                        { color: colors.textSecondary, flex: 0.4 },
                      ]}
                      numberOfLines={1}
                    >
                      {formatType(column.type)}
                    </Text>
                    <Text
                      style={[
                        styles.columnNullable,
                        {
                          color: column.is_nullable
                            ? colors.textSecondary
                            : '#EF4444',
                          flex: 0.2,
                          textAlign: 'right',
                        },
                      ]}
                    >
                      {column.is_nullable ? 'YES' : 'NO'}
                    </Text>
                  </View>
                );
              })}
            </View>
          )}
        </View>

        {/* Constraints Section */}
        {table.constraints.length > 0 && (
          <View style={styles.sectionContainer}>
            <TouchableOpacity
              style={[
                styles.sectionHeader,
                {
                  borderBottomColor: showConstraints ? colors.border : 'transparent',
                },
              ]}
              onPress={() => setShowConstraints(!showConstraints)}
            >
              <View style={styles.sectionTitleContainer}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  Constraints
                </Text>
                <View style={styles.sectionBadge}>
                  <Text
                    style={[styles.sectionBadgeText, { color: colors.textSecondary }]}
                  >
                    {table.constraints.length}
                  </Text>
                </View>
              </View>
              {showConstraints ? (
                <Feather name="chevron-up" size={18} color={colors.textSecondary} />
              ) : (
                <Feather name="chevron-down" size={18} color={colors.textSecondary} />
              )}
            </TouchableOpacity>

            {showConstraints && (
              <View style={styles.sectionContent}>
                {table.constraints.map((constraint, index) => {
                  const { label, color, bgColor } = getConstraintLabel(constraint.type);
                  return (
                    <View
                      key={constraint.name}
                      style={[
                        styles.constraintItem,
                        index < table.constraints.length - 1 && {
                          borderBottomWidth: 1,
                          borderBottomColor: colors.border + '40',
                        },
                      ]}
                    >
                      <View style={styles.constraintHeader}>
                        <Text
                          style={[styles.constraintName, { color: colors.text }]}
                          numberOfLines={1}
                        >
                          {constraint.name}
                        </Text>
                        <View
                          style={[
                            styles.constraintType,
                            { backgroundColor: bgColor },
                          ]}
                        >
                          <Text
                            style={[styles.constraintTypeText, { color: color }]}
                          >
                            {label}
                          </Text>
                        </View>
                      </View>
                      <Text
                        style={[
                          styles.constraintDefinition,
                          { color: colors.textSecondary },
                        ]}
                      >
                        {constraint.definition}
                      </Text>
                    </View>
                  );
                })}
              </View>
            )}
          </View>
        )}

        {/* Relationships Section */}
        {relatedTables.length > 0 && (
          <View style={styles.sectionContainer}>
            <TouchableOpacity
              style={[
                styles.sectionHeader,
                {
                  borderBottomColor: showRelationships ? colors.border : 'transparent',
                },
              ]}
              onPress={() => setShowRelationships(!showRelationships)}
            >
              <View style={styles.sectionTitleContainer}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  Relationships
                </Text>
                <View style={styles.sectionBadge}>
                  <Text
                    style={[styles.sectionBadgeText, { color: colors.textSecondary }]}
                  >
                    {relatedTables.length}
                  </Text>
                </View>
              </View>
              {showRelationships ? (
                <Feather name="chevron-up" size={18} color={colors.textSecondary} />
              ) : (
                <Feather name="chevron-down" size={18} color={colors.textSecondary} />
              )}
            </TouchableOpacity>

            {showRelationships && (
              <View style={styles.sectionContent}>
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.relatedTablesContainer}
                >
                  {relatedTables.map((item, index) => (
                    <TouchableOpacity
                      key={`${item.name}-${index}`}
                      style={[
                        styles.relatedTableCard,
                        { backgroundColor: colors.background, borderColor: colors.border },
                      ]}
                      onPress={() => onSelectTable(item.name)}
                      activeOpacity={0.7}
                    >
                      <View style={styles.relatedTableHeader}>
                        <Text
                          style={[styles.relatedTableName, { color: colors.text }]}
                          numberOfLines={1}
                        >
                          {item.name}
                        </Text>
                      </View>
                      <View style={styles.relationDetail}>
                        <Text style={[styles.relationLabel, { color: colors.primary }]}>
                          {item.isSource ? 'References' : 'Referenced by'}
                        </Text>
                        <View style={styles.relationFields}>
                          <Text
                            style={[
                              styles.relationField,
                              { color: colors.text, fontWeight: '600' },
                            ]}
                            numberOfLines={1}
                          >
                            {item.isSource ? item.relation.targetField : item.relation.sourceField}
                          </Text>
                          <Text style={[styles.relationConnector, { color: colors.textSecondary }]}>
                            ⟷
                          </Text>
                          <Text
                            style={[
                              styles.relationField,
                              { color: colors.text, fontWeight: '600' },
                            ]}
                            numberOfLines={1}
                          >
                            {item.isSource ? item.relation.sourceField : item.relation.targetField}
                          </Text>
                        </View>
                      </View>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            )}
          </View>
        )}

        {/* Additional information */}
        {table.is_rls_enabled && (
          <View
            style={[
              styles.rlsInfoContainer,
              { backgroundColor: colors.primaryLight, marginHorizontal: 15, marginBottom: 15 },
            ]}
          >
            <Feather name="lock" size={16} color={colors.primary} />
            <Text
              style={[styles.rlsInfoText, { color: colors.primary, marginLeft: 8 }]}
            >
              This table has Row Level Security enabled. Access to rows is restricted based on database policies.
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
    margin: 12,
    marginBottom: 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  tableName: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  rlsBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  rlsText: {
    fontSize: 10,
    fontWeight: '700',
    marginLeft: 4,
  },
  contentScroll: {
    flex: 1,
  },
  sectionContainer: {
    marginVertical: 8,
    marginHorizontal: 15,
    backgroundColor: 'transparent',
    borderRadius: 8,
    overflow: 'hidden',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
  },
  sectionHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  sectionBadge: {
    marginLeft: 8,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  sectionBadgeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  sectionContent: {
    paddingTop: 8,
  },
  columnHeader: {
    flexDirection: 'row',
    paddingVertical: 8,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
  },
  columnHeaderText: {
    fontSize: 12,
    fontWeight: '600',
  },
  columnRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 4,
  },
  columnName: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  columnNameText: {
    fontSize: 13,
    marginRight: 4,
  },
  keyIndicators: {
    flexDirection: 'row',
  },
  keyIndicator: {
    paddingHorizontal: 4,
    paddingVertical: 1,
    borderRadius: 4,
    marginLeft: 4,
  },
  keyIndicatorText: {
    fontSize: 8,
    fontWeight: '700',
  },
  columnType: {
    fontSize: 12,
  },
  columnNullable: {
    fontSize: 12,
    fontWeight: '500',
  },
  filterContainer: {
    flexDirection: 'row',
    marginRight: 8,
  },
  filterBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginRight: 4,
  },
  filterText: {
    fontSize: 10,
    fontWeight: '600',
  },
  constraintItem: {
    paddingVertical: 10,
    paddingHorizontal: 4,
  },
  constraintHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  constraintName: {
    fontSize: 13,
    fontWeight: '500',
    flex: 1,
  },
  constraintType: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  constraintTypeText: {
    fontSize: 10,
    fontWeight: '600',
  },
  constraintDefinition: {
    fontSize: 12,
    lineHeight: 16,
  },
  relatedTablesContainer: {
    paddingTop: 8,
    paddingBottom: 12,
  },
  relatedTableCard: {
    width: 180,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 12,
    overflow: 'hidden',
  },
  relatedTableHeader: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  relatedTableName: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  relationDetail: {
    padding: 12,
    alignItems: 'center',
  },
  relationLabel: {
    fontSize: 10,
    fontWeight: '600',
    marginBottom: 4,
  },
  relationFields: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flexWrap: 'wrap',
  },
  relationField: {
    fontSize: 12,
    maxWidth: 70,
    textAlign: 'center',
  },
  relationConnector: {
    marginHorizontal: 4,
    fontSize: 14,
  },
  rlsInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
  },
  rlsInfoText: {
    fontSize: 12,
    flex: 1,
  },
});

export default TableDetailView; 