import React, { useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';

import TableDetailView from './TableDetailView';
import SchemaVisualizer from './SchemaVisualizer';
import DataBrowser from './DataBrowser';

interface DatabaseExplorerProps {
  schema: any;
}

interface RelatedTable {
  name: string;
  relation: {
    sourceField: string;
    targetField: string;
    type: 'outgoing' | 'incoming';
  };
  isSource: boolean;
}

interface ForeignKey {
  definition: string;
  // Add other properties if needed
}

// Define interface for SchemaExplorer props
interface SchemaExplorerProps {
  schema: any;
  colors: any;
  onSelectTable: (tableName: string) => void;
}

export const DatabaseExplorer = ({ schema }: DatabaseExplorerProps) => {
  const { colors } = useTheme();
  const [activeTab, setActiveTab] = useState<'schema' | 'visualize' | 'browse'>('schema');
  const [selectedTable, setSelectedTable] = useState<string | null>(null);

  // Get related tables for a selected table
  const getRelatedTables = (tableName: string): RelatedTable[] => {
    if (!tableName || !schema.tables[tableName]) return [];

    const relatedTables: RelatedTable[] = [];
    
    // Check outgoing foreign keys
    schema.tables[tableName].foreign_keys.forEach((fk: ForeignKey) => {
      const matches = fk.definition.match(/FOREIGN KEY \(([^)]+)\) REFERENCES ([^(]+)\(([^)]+)\)/);
      if (matches) {
        relatedTables.push({
          name: matches[2].trim(),
          relation: {
            sourceField: matches[1].trim(),
            targetField: matches[3].trim(),
            type: 'outgoing'
          },
          isSource: true
        });
      }
    });
    
    // Check incoming foreign keys (tables that reference this table)
    Object.entries(schema.tables).forEach(([otherTableName, otherTable]: [string, any]) => {
      if (otherTableName === tableName) return;
      
      otherTable.foreign_keys.forEach((fk: ForeignKey) => {
        const matches = fk.definition.match(/FOREIGN KEY \(([^)]+)\) REFERENCES ([^(]+)\(([^)]+)\)/);
        if (matches && matches[2].trim() === tableName) {
          relatedTables.push({
            name: otherTableName,
            relation: {
              sourceField: matches[1].trim(),
              targetField: matches[3].trim(),
              type: 'incoming'
            },
            isSource: false
          });
        }
      });
    });
    
    return relatedTables;
  };

  // Handle table selection
  const handleSelectTable = (tableName: string) => {
    setSelectedTable(tableName);
    // If in visualization view, switch to schema view to see details
    if (activeTab === 'visualize') {
      setActiveTab('schema');
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.card, borderBottomColor: colors.border }]}>
        <View style={styles.headerLeft}>
          <Feather name="database" size={22} color={colors.primary} style={{ marginRight: 10 }} />
          <Text style={[styles.headerTitle, { color: colors.text }]}>Database Explorer</Text>
        </View>
        <TouchableOpacity 
          style={[styles.settingsButton, { backgroundColor: colors.subtle }]}
        >
          <Feather name="settings" size={18} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
      
      <View style={styles.content}>
        <View style={[styles.tabBar, { backgroundColor: colors.card, borderBottomColor: colors.border }]}>
          <TouchableOpacity
            style={[
              styles.tab,
              activeTab === 'schema' && styles.activeTab,
              { borderBottomColor: activeTab === 'schema' ? colors.primary : 'transparent' }
            ]}
            onPress={() => setActiveTab('schema')}
          >
            <Table2 
              size={18} 
              color={activeTab === 'schema' ? colors.primary : colors.textSecondary} 
              style={{ marginRight: 6 }} 
            />
            <Text 
              style={[
                styles.tabText,
                { color: activeTab === 'schema' ? colors.primary : colors.textSecondary }
              ]}
            >
              Schema
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.tab,
              activeTab === 'visualize' && styles.activeTab,
              { borderBottomColor: activeTab === 'visualize' ? colors.primary : 'transparent' }
            ]}
            onPress={() => setActiveTab('visualize')}
          >
            <Workflow 
              size={18} 
              color={activeTab === 'visualize' ? colors.primary : colors.textSecondary} 
              style={{ marginRight: 6 }} 
            />
            <Text 
              style={[
                styles.tabText,
                { color: activeTab === 'visualize' ? colors.primary : colors.textSecondary }
              ]}
            >
              Visualize
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.tab,
              activeTab === 'browse' && styles.activeTab,
              { borderBottomColor: activeTab === 'browse' ? colors.primary : 'transparent' }
            ]}
            onPress={() => setActiveTab('browse')}
          >
            <Code 
              size={18} 
              color={activeTab === 'browse' ? colors.primary : colors.textSecondary} 
              style={{ marginRight: 6 }} 
            />
            <Text 
              style={[
                styles.tabText,
                { color: activeTab === 'browse' ? colors.primary : colors.textSecondary }
              ]}
            >
              Data Browser
            </Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.tabContent}>
          {activeTab === 'schema' && (
            selectedTable ? (
              <TableDetailView 
                table={schema.tables[selectedTable]}
                relatedTables={getRelatedTables(selectedTable)}
                colors={colors}
                onBack={() => setSelectedTable(null)}
                onSelectTable={handleSelectTable}
              />
            ) : (
              <SchemaExplorer 
                schema={schema}
                colors={colors}
                onSelectTable={handleSelectTable}
              />
            )
          )}
          
          {activeTab === 'visualize' && (
            <SchemaVisualizer 
              schema={schema}
              colors={colors}
              onSelectTable={handleSelectTable}
            />
          )}
          
          {activeTab === 'browse' && (
            <DataBrowser 
              schema={schema}
              colors={colors}
              selectedTable={selectedTable}
            />
          )}
        </View>
      </View>
      
      {selectedTable && activeTab !== 'schema' && (
        <View style={[styles.selectedTableBar, { backgroundColor: colors.primaryLight }]}>
          <Text style={[styles.selectedTableText, { color: colors.primary }]}>
            Selected: {selectedTable}
          </Text>
          <TouchableOpacity
            style={[styles.selectedTableButton, { backgroundColor: colors.primary }]}
            onPress={() => {
              setActiveTab('schema');
            }}
          >
            <Text style={styles.selectedTableButtonText}>View Schema</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

// Schema Explorer Component - This displays the list of tables
const SchemaExplorer = ({ schema, colors, onSelectTable }: SchemaExplorerProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  
  // Group tables by categories
  const tableGroups = {
    'User Data': ['profiles', 'dietary_preferences'],
    'Nutrition Tracking': ['meals', 'meal_items', 'water_intake'],
    'Recipes': ['alternative_recipes', 'recipe_ingredients', 'recipe_instructions', 'recipe_health_benefits'],
    'Billing': ['stripe_customers', 'stripe_subscriptions', 'stripe_orders']
  };
  
  // Get all tables
  const allTables = Object.keys(schema.tables);
  
  // Filter tables if search query is present
  const filteredTables = searchQuery.trim() === '' 
    ? allTables 
    : allTables.filter(tableName => 
        tableName.toLowerCase().includes(searchQuery.toLowerCase())
      );
  
  // Group filtered tables
  const groupedTables: { [key: string]: string[] } = {};
  Object.entries(tableGroups).forEach(([groupName, tables]) => {
    const groupTables = tables.filter(t => filteredTables.includes(t));
    if (groupTables.length > 0) {
      groupedTables[groupName] = groupTables;
    }
  });
  
  // Find tables not in any group
  const otherTables = filteredTables.filter(t => 
    !Object.values(tableGroups).flat().includes(t)
  );
  
  if (otherTables.length > 0) {
    groupedTables['Other'] = otherTables;
  }
  
  return (
    <View style={[styles.schemaExplorer, { backgroundColor: colors.card }]}>
      <View style={styles.schemaHeader}>
        <Text style={[styles.schemaTitle, { color: colors.text }]}>Database Schema</Text>
        <Text style={[styles.schemaSubtitle, { color: colors.textSecondary }]}>
          {allTables.length} tables
        </Text>
      </View>
      
      <View style={styles.tableGroups}>
        {Object.entries(groupedTables).map(([groupName, tables]) => (
          <View key={groupName} style={styles.tableGroup}>
            <Text style={[styles.groupTitle, { color: colors.primary }]}>{groupName}</Text>
            
            {tables.map(tableName => {
              const table = schema.tables[tableName];
              if (!table) return null;
              
              return (
                <TouchableOpacity 
                  key={tableName}
                  style={[
                    styles.tableItem, 
                    { backgroundColor: colors.subtle, borderLeftColor: table.is_rls_enabled ? colors.primary : 'transparent' }
                  ]}
                  onPress={() => onSelectTable(tableName)}
                >
                  <Table2 size={16} color={colors.textSecondary} style={{ marginRight: 8 }} />
                  <View style={styles.tableItemContent}>
                    <Text style={[styles.tableItemName, { color: colors.text }]}>{tableName}</Text>
                    <Text style={[styles.tableItemInfo, { color: colors.textSecondary }]}>
                      {table.columns.length} columns
                    </Text>
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  tabBar: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 12,
    marginRight: 16,
    borderBottomWidth: 2,
  },
  activeTab: {
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  tabContent: {
    flex: 1,
  },
  selectedTableBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    marginHorizontal: 12,
    marginBottom: 12,
    borderRadius: 8,
  },
  selectedTableText: {
    fontSize: 14,
    fontWeight: '600',
  },
  selectedTableButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  selectedTableButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  schemaExplorer: {
    flex: 1,
    margin: 12,
    marginBottom: 0,
    borderRadius: 12,
    overflow: 'hidden',
  },
  schemaHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  schemaTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  schemaSubtitle: {
    fontSize: 14,
  },
  tableGroups: {
    padding: 16,
  },
  tableGroup: {
    marginBottom: 24,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  tableItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 3,
  },
  tableItemContent: {
    flex: 1,
  },
  tableItemName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  tableItemInfo: {
    fontSize: 12,
  },
});

export default DatabaseExplorer; 