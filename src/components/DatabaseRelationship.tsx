import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ScrollView, Dimensions, Animated, Platform } from 'react-native';
import Svg, { Path, Circle, G, Rect, Text as SvgText } from 'react-native-svg';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { PanGestureHandler, PinchGestureHandler, State, GestureHandlerRootView } from 'react-native-gesture-handler';

const { width, height } = Dimensions.get('window');

interface TableNode {
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  fields: string[];
}

interface Relationship {
  source: string;
  sourceField: string;
  target: string;
  targetField: string;
  type: 'one-to-many' | 'one-to-one' | 'many-to-many';
}

// Helper function to get appropriate icon for table groups
const getGroupIcon = (tableName: string) => {
  // Return the User icon for user-related tables
  if (tableName.toLowerCase().includes('user') || tableName.toLowerCase().includes('profile')) {
    return <Feather name="user" size={14}  color={colors.text} />;
  }
  // Default to database icon
  return <Feather name="database" size={14}  color={colors.text} />;
};

export function DatabaseRelationship({ schema }: { schema: any }) {
  const { colors, isDark } = useTheme();
  const [scale, setScale] = useState(1);
  const [translateX, setTranslateX] = useState(0);
  const [translateY, setTranslateY] = useState(0);
  const [nodes, setNodes] = useState<TableNode[]>([]);
  const [relationships, setRelationships] = useState<Relationship[]>([]);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [showInfo, setShowInfo] = useState(true);
  
  const pinchRef = useRef(null);
  const panRef = useRef(null);
  const translateXAnim = useRef(new Animated.Value(0)).current;
  const translateYAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const infoFadeAnim = useRef(new Animated.Value(1)).current;
  
  // Process schema to create visualization data
  useEffect(() => {
    // Create nodes (tables)
    const tableNodes: TableNode[] = [];
    const tableKeys = Object.keys(schema.tables);
    
    // Position tables in a force-directed layout
    const positionTables = () => {
      // Start with a circle layout
      const centerX = width * 2;
      const centerY = height * 2;
      const radius = Math.min(width, height) * 1.2;
      
      tableKeys.forEach((tableName, index) => {
        const table = schema.tables[tableName];
        const angle = (index / tableKeys.length) * 2 * Math.PI;
        
        const node: TableNode = {
          name: tableName,
          x: centerX + radius * Math.cos(angle),
          y: centerY + radius * Math.sin(angle),
          width: 150,
          height: 100 + Math.min(table.columns.length * 10, 80), // Dynamic height based on number of fields
          fields: table.columns.map((col: any) => col.name)
        };
        
        tableNodes.push(node);
      });
    };
    
    positionTables();
    setNodes(tableNodes);
    
    // Create relationships
    const rels: Relationship[] = [];
    
    tableKeys.forEach(tableName => {
      const table = schema.tables[tableName];
      table.foreign_keys.forEach((fk: any) => {
        // Extract table and field info from definition string
        const matches = fk.definition.match(/FOREIGN KEY \(([^)]+)\) REFERENCES ([^(]+)\(([^)]+)\)/);
        if (matches) {
          const rel: Relationship = {
            source: tableName,
            sourceField: matches[1].trim(),
            target: matches[2].trim(),
            targetField: matches[3].trim(),
            type: 'one-to-many' // Default relationship type
          };
          rels.push(rel);
        }
      });
    });
    
    setRelationships(rels);
    
    // Fade in the diagram
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true
    }).start();
    
    // Show info panel temporarily
    Animated.sequence([
      Animated.delay(2000),
      Animated.timing(infoFadeAnim, {
        toValue: 0,
        duration: 1000,
        useNativeDriver: true
      })
    ]).start(() => setShowInfo(false));
    
  }, [schema]);
  
  // Handle pan gesture (move the diagram)
  const onPanGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateXAnim, translationY: translateYAnim } }],
    { useNativeDriver: true }
  );
  
  // Handle pinch gesture (zoom the diagram)
  const onPinchGestureEvent = Animated.event(
    [{ nativeEvent: { scale: scaleAnim } }],
    { useNativeDriver: true }
  );
  
  // Functions to handle gesture state changes
  const onPanHandlerStateChange = (event: any) => {
    if (event.nativeEvent.oldState === State.ACTIVE) {
      setTranslateX(translateX + event.nativeEvent.translationX);
      setTranslateY(translateY + event.nativeEvent.translationY);
      translateXAnim.setValue(0);
      translateYAnim.setValue(0);
    }
  };
  
  const onPinchHandlerStateChange = (event: any) => {
    if (event.nativeEvent.oldState === State.ACTIVE) {
      setScale(scale * event.nativeEvent.scale);
      scaleAnim.setValue(1);
    }
  };
  
  // Reset the view to default
  const resetView = () => {
    Animated.parallel([
      Animated.timing(translateXAnim, {
        toValue: -translateX,
        duration: 500,
        useNativeDriver: true
      }),
      Animated.timing(translateYAnim, {
        toValue: -translateY,
        duration: 500,
        useNativeDriver: true
      }),
      Animated.timing(scaleAnim, {
        toValue: 1/scale,
        duration: 500,
        useNativeDriver: true
      })
    ]).start(() => {
      setScale(1);
      setTranslateX(0);
      setTranslateY(0);
      translateXAnim.setValue(0);
      translateYAnim.setValue(0);
      scaleAnim.setValue(1);
    });
  };
  
  // Zoom in and out functions
  const zoomIn = () => {
    const newScale = Math.min(scale * 1.2, 3);
    Animated.timing(scaleAnim, {
      toValue: newScale / scale,
      duration: 300,
      useNativeDriver: true
    }).start(() => {
      setScale(newScale);
      scaleAnim.setValue(1);
    });
  };
  
  const zoomOut = () => {
    const newScale = Math.max(scale / 1.2, 0.5);
    Animated.timing(scaleAnim, {
      toValue: newScale / scale,
      duration: 300,
      useNativeDriver: true
    }).start(() => {
      setScale(newScale);
      scaleAnim.setValue(1);
    });
  };
  
  // Find a table node
  const findNode = (name: string) => {
    return nodes.find(node => node.name === name);
  };
  
  // Calculate path between two tables
  const calculatePath = (rel: Relationship) => {
    const sourceNode = findNode(rel.source);
    const targetNode = findNode(rel.target);
    
    if (!sourceNode || !targetNode) return '';
    
    // Calculate starting and ending points
    const startX = sourceNode.x + sourceNode.width / 2;
    const startY = sourceNode.y + sourceNode.height / 2;
    const endX = targetNode.x + targetNode.width / 2;
    const endY = targetNode.y + targetNode.height / 2;
    
    // Calculate the angle for better path generation
    const dx = endX - startX;
    const dy = endY - startY;
    const angle = Math.atan2(dy, dx);
    
    // Adjust start and end points to be on the edges of the nodes
    const sourceRadius = Math.sqrt(Math.pow(sourceNode.width / 2, 2) + Math.pow(sourceNode.height / 2, 2));
    const targetRadius = Math.sqrt(Math.pow(targetNode.width / 2, 2) + Math.pow(targetNode.height / 2, 2));
    
    const adjustedStartX = startX + Math.cos(angle) * (sourceNode.width / 2);
    const adjustedStartY = startY + Math.sin(angle) * (sourceNode.height / 2);
    
    const adjustedEndX = endX - Math.cos(angle) * (targetNode.width / 2);
    const adjustedEndY = endY - Math.sin(angle) * (targetNode.height / 2);
    
    // Create a bezier curve path
    const distance = Math.sqrt(dx * dx + dy * dy);
    const controlPoint1X = adjustedStartX + Math.cos(angle) * distance * 0.25;
    const controlPoint1Y = adjustedStartY + Math.sin(angle) * distance * 0.25;
    const controlPoint2X = adjustedEndX - Math.cos(angle) * distance * 0.25;
    const controlPoint2Y = adjustedEndY - Math.sin(angle) * distance * 0.25;
    
    return `M${adjustedStartX},${adjustedStartY} C${controlPoint1X},${controlPoint1Y} ${controlPoint2X},${controlPoint2Y} ${adjustedEndX},${adjustedEndY}`;
  };
  
  // Add to useEffect or where you manage the selected node
  useEffect(() => {
    if (selectedNode) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [selectedNode]);
  
  return (
    <GestureHandlerRootView style={styles.gestureContainer}>
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Workflow size={24} color={colors.primary} style={{ marginRight: 10 }} />
            <Text style={[styles.title, { color: colors.text }]}>Database Relationships</Text>
          </View>
          <TouchableOpacity 
            style={[styles.infoButton, { backgroundColor: colors.primaryLight }]}
            onPress={() => {
              setShowInfo(true);
              Animated.timing(infoFadeAnim, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true
              }).start();
              
              // Auto-hide after 3 seconds
              setTimeout(() => {
                Animated.timing(infoFadeAnim, {
                  toValue: 0,
                  duration: 500,
                  useNativeDriver: true
                }).start(() => setShowInfo(false));
              }, 3000);
            }}
          >
            <Feather name="info" size={18} color={colors.primary} />
          </TouchableOpacity>
        </View>
        
        <View style={styles.diagramControls}>
          <Text style={[styles.zoomText, { color: colors.textSecondary }]}>
            Zoom: {Math.round(scale * 100)}%
          </Text>
          <View style={styles.controlButtons}>
            <TouchableOpacity 
              style={[styles.controlButton, { backgroundColor: colors.subtle }]}
              onPress={zoomOut}
            >
              <ZoomOut size={18} color={colors.textSecondary} />
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.controlButton, { backgroundColor: colors.subtle }]}
              onPress={resetView}
            >
              <Feather name="refresh-cw" size={18} color={colors.textSecondary} />
            </TouchableOpacity>
            <TouchableOpacity 
              style={[styles.controlButton, { backgroundColor: colors.subtle }]}
              onPress={zoomIn}
            >
              <ZoomIn size={18} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>
        </View>
        
        {showInfo && (
          <Animated.View 
            style={[
              styles.infoPanel, 
              { 
                backgroundColor: colors.primaryLight,
                opacity: infoFadeAnim
              }
            ]}
          >
            <View style={styles.infoContent}>
              <View style={styles.infoHeader}>
                <Feather name="info" size={18} color={colors.primary} style={{ marginRight: 8 }} />
                <Text style={[styles.infoTitle, { color: colors.primary }]}>
                  Interactive Diagram
                </Text>
              </View>
              <Text style={[styles.infoText, { color: colors.primary }]}>
                • Pinch to zoom in and out
              </Text>
              <Text style={[styles.infoText, { color: colors.primary }]}>
                • Drag to move the diagram
              </Text>
              <Text style={[styles.infoText, { color: colors.primary }]}>
                • Tap on a table to see its relationships
              </Text>
            </View>
          </Animated.View>
        )}
        
        <Animated.View 
          style={[
            styles.diagramContainer, 
            { 
              backgroundColor: colors.subtle,
              opacity: fadeAnim
            }
          ]}
        >
          <PinchGestureHandler
            ref={pinchRef}
            onGestureEvent={onPinchGestureEvent}
            onHandlerStateChange={onPinchHandlerStateChange}
          >
            <Animated.View style={{ flex: 1 }}>
              <PanGestureHandler
                ref={panRef}
                onGestureEvent={onPanGestureEvent}
                onHandlerStateChange={onPanHandlerStateChange}
                minDist={10}
              >
                <Animated.View
                  style={[
                    styles.diagramContent,
                    {
                      transform: [
                        { translateX: Animated.add(translateXAnim, new Animated.Value(translateX)) },
                        { translateY: Animated.add(translateYAnim, new Animated.Value(translateY)) },
                        { scale: Animated.multiply(scaleAnim, new Animated.Value(scale)) }
                      ]
                    }
                  ]}
                >
                  <Svg height="2000" width="2000">
                    {/* Draw relationship lines */}
                    {relationships.map((rel, index) => {
                      const path = calculatePath(rel);
                      const isSelected = selectedNode === rel.source || selectedNode === rel.target;
                      return (
                        <Path
                          key={`line-${index}`}
                          d={path}
                          stroke={isSelected ? colors.primary : colors.border}
                          strokeWidth={isSelected ? 2 : 1.5}
                          fill="none"
                          strokeDasharray={rel.type === 'many-to-many' ? '5,5' : undefined}
                        />
                      );
                    })}
                    
                    {/* Draw connection points */}
                    {relationships.map((rel, index) => {
                      const sourceNode = findNode(rel.source);
                      const targetNode = findNode(rel.target);
                      
                      if (!sourceNode || !targetNode) return null;
                      
                      const startX = sourceNode.x + sourceNode.width / 2;
                      const startY = sourceNode.y + sourceNode.height / 2;
                      const endX = targetNode.x + targetNode.width / 2;
                      const endY = targetNode.y + targetNode.height / 2;
                      
                      const isSelected = selectedNode === rel.source || selectedNode === rel.target;
                      
                      return (
                        <React.Fragment key={`points-${index}`}>
                          <Circle
                            cx={startX}
                            cy={startY}
                            r={4}
                            fill={isSelected ? colors.primary : colors.border}
                          />
                          <Circle
                            cx={endX}
                            cy={endY}
                            r={4}
                            fill={isSelected ? colors.primary : colors.border}
                          />
                        </React.Fragment>
                      );
                    })}
                  </Svg>
                  
                  {/* Draw table nodes */}
                  {nodes.map(node => {
                    const isSelected = selectedNode === node.name;
                    // Find relationships involving this node
                    const nodeRelationships = relationships.filter(
                      rel => rel.source === node.name || rel.target === node.name
                    );
                    
                    return (
                      <TouchableOpacity
                        key={node.name}
                        style={[
                          styles.tableNode,
                          {
                            left: node.x,
                            top: node.y,
                            width: node.width,
                            height: node.height,
                            backgroundColor: isSelected ? colors.primaryLight : colors.card,
                            borderColor: isSelected ? colors.primary : 
                              nodeRelationships.some(rel => 
                                selectedNode && (rel.source === selectedNode || rel.target === selectedNode)
                              ) ? colors.primary : colors.border,
                            borderWidth: 2,
                            transform: [{ scale: isSelected ? 1.05 : 1 }],
                          }
                        ]}
                        onPress={() => setSelectedNode(selectedNode === node.name ? null : node.name)}
                        activeOpacity={0.9}
                      >
                        <View style={[
                          styles.tableNodeHeader, 
                          { 
                            borderBottomColor: isSelected ? colors.primary : colors.border,
                            backgroundColor: isSelected ? colors.primary + '20' : 'transparent',
                          }
                        ]}>
                          <Text style={[
                            styles.tableNodeName, 
                            { 
                              color: isSelected ? colors.primary : colors.text,
                              fontWeight: isSelected ? '700' : '600'
                            }
                          ]}>
                            {node.name}
                          </Text>
                        </View>
                        <View style={styles.tableNodeFields}>
                          {node.fields.slice(0, 5).map(field => {
                            // Check if this field is part of a relationship
                            const isRelationshipField = relationships.some(
                              rel => (rel.source === node.name && rel.sourceField === field) ||
                                    (rel.target === node.name && rel.targetField === field)
                            );
                            
                            return (
                              <Text 
                                key={field} 
                                style={[
                                  styles.tableNodeField, 
                                  { 
                                    color: isRelationshipField ? 
                                      (isSelected ? colors.primary : '#3B82F6') : 
                                      (isSelected ? colors.primary : colors.textSecondary)
                                  }
                                ]}
                                numberOfLines={1}
                                ellipsizeMode="tail"
                              >
                                {isRelationshipField ? '➢ ' : ''}
                                {field}
                              </Text>
                            );
                          })}
                          {node.fields.length > 5 && (
                            <Text style={[
                              styles.moreFields, 
                              { color: isSelected ? colors.primary : colors.textSecondary }
                            ]}>
                              +{node.fields.length - 5} more
                            </Text>
                          )}
                        </View>
                      </TouchableOpacity>
                    );
                  })}
                </Animated.View>
              </PanGestureHandler>
            </Animated.View>
          </PinchGestureHandler>
        </Animated.View>
        
        {selectedNode && (
          <Animated.View 
            style={[
              styles.detailsPanel, 
              { 
                backgroundColor: colors.card,
                borderTopColor: colors.border,
                opacity: fadeAnim 
              }
            ]}
          >
            <View style={styles.detailsHeader}>
              <View style={styles.selectedTableInfo}>
                <Feather name="database" size={18} color={colors.primary} style={{ marginRight: 8 }} />
                <Text style={[styles.selectedTableName, { color: colors.text }]}>
                  {selectedNode}
                </Text>
              </View>
              <TouchableOpacity
                style={[styles.closeButton, { backgroundColor: colors.subtle }]}
                onPress={() => setSelectedNode(null)}
              >
                <Text style={[styles.closeButtonText, { color: colors.textSecondary }]}>Close</Text>
              </TouchableOpacity>
            </View>
            
            {relationships.filter(rel => rel.source === selectedNode || rel.target === selectedNode).length > 0 ? (
              <ScrollView 
                style={styles.relatedTables}
                contentContainerStyle={styles.relatedTablesContent}
                horizontal
                showsHorizontalScrollIndicator={false}
              >
                {relationships
                  .filter(rel => rel.source === selectedNode || rel.target === selectedNode)
                  .map((rel, index) => {
                    const isSource = rel.source === selectedNode;
                    const connectedTo = isSource ? rel.target : rel.source;
                    
                    return (
                      <TouchableOpacity 
                        key={index}
                        style={[
                          styles.relatedTableItem, 
                          { 
                            backgroundColor: colors.card, 
                            borderColor: colors.primary,
                            shadowColor: colors.shadow,
                          }
                        ]}
                        onPress={() => setSelectedNode(connectedTo)}
                        activeOpacity={0.8}
                      >
                        <View style={styles.relationDirection}>
                          <LinkIcon size={14} color={colors.primary} style={{ marginRight: 4 }} />
                          <Text style={[styles.directionText, { color: colors.primary }]}>
                            {isSource ? 'References' : 'Referenced by'}
                          </Text>
                        </View>
                        <Text style={[styles.relatedTableName, { color: colors.text }]}>
                          {connectedTo}
                        </Text>
                        <View style={styles.relationFields}>
                          <Text style={[styles.relationFieldName, { color: colors.text }]}>
                            {isSource ? rel.sourceField : rel.targetField}
                          </Text>
                          <View style={[styles.relationArrow, { borderColor: colors.border }]}>
                            <View style={[styles.arrowHead, { borderLeftColor: colors.border }]} />
                          </View>
                          <Text style={[styles.relationFieldName, { color: colors.text }]}>
                            {isSource ? rel.targetField : rel.sourceField}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    );
                  })
                }
              </ScrollView>
            ) : (
              <View style={styles.noRelationsContainer}>
                <Text style={[styles.noRelationsText, { color: colors.textSecondary }]}>
                  This table has no relationships with other tables.
                </Text>
              </View>
            )}
          </Animated.View>
        )}
      </View>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  gestureContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 10 : 10,
    paddingBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
  },
  infoButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  diagramControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 8,
  },
  zoomText: {
    fontSize: 14,
    fontWeight: '500',
  },
  controlButtons: {
    flexDirection: 'row',
  },
  controlButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  infoPanel: {
    margin: 16,
    marginTop: 0,
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
  },
  infoContent: {
    
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '700',
  },
  infoText: {
    fontSize: 13,
    lineHeight: 20,
  },
  diagramContainer: {
    flex: 1,
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  diagramContent: {
    flex: 1,
    width: 2000,
    height: 2000,
    backgroundColor: 'transparent',
  },
  tableNode: {
    position: 'absolute',
    borderRadius: 10,
    borderWidth: 1.5,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  tableNodeHeader: {
    padding: 8,
    borderBottomWidth: 1,
  },
  tableNodeName: {
    fontSize: 14,
    textAlign: 'center',
  },
  tableNodeFields: {
    padding: 8,
  },
  tableNodeField: {
    fontSize: 10,
    marginBottom: 3,
  },
  moreFields: {
    fontSize: 9,
    fontStyle: 'italic',
    marginTop: 4,
    textAlign: 'center',
  },
  detailsPanel: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    borderTopWidth: 1,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    ...Platform.select({
      ios: {
        shadowOffset: {
          width: 0,
          height: -3,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  detailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  selectedTableInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedTableName: {
    fontSize: 16,
    fontWeight: '700',
  },
  closeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  closeButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  relatedTables: {
    marginBottom: 20,
  },
  relatedTablesContent: {
    paddingRight: 20,
  },
  relatedTableItem: {
    padding: 12,
    marginRight: 12,
    borderRadius: 12,
    borderWidth: 1,
    width: 180,
    ...Platform.select({
      ios: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  relationDirection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  directionText: {
    fontSize: 10,
    fontWeight: '700',
  },
  relatedTableName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  relationFields: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  relationFieldName: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  relationArrow: {
    width: 20,
    height: 0,
    borderTopWidth: 1,
    marginHorizontal: 4,
    position: 'relative',
  },
  arrowHead: {
    position: 'absolute',
    right: -4,
    top: -4,
    width: 0,
    height: 0,
    borderTopWidth: 4,
    borderRightWidth: 0,
    borderBottomWidth: 4,
    borderLeftWidth: 6,
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
  },
  noRelationsContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  noRelationsText: {
    fontSize: 14,
    fontStyle: 'italic',
  }
});