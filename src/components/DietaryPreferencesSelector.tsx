import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Feather } from '@expo/vector-icons';

// Dietary preference options
const DIETARY_PREFERENCES = [
  { id: 'gluten-free', label: 'Gluten Free' },
  { id: 'dairy-free', label: 'Dairy Free' },
  { id: 'vegan', label: 'Vegan' },
  { id: 'vegetarian', label: 'Vegetarian' },
  { id: 'keto', label: 'Keto' },
  { id: 'paleo', label: 'Paleo' },
  { id: 'low-sodium', label: 'Low Sodium' },
  { id: 'low-sugar', label: 'Low Sugar' },
  { id: 'nut-free', label: 'Nut Free' },
  { id: 'shellfish-free', label: 'Shellfish Free' },
];

// Health goal options
const HEALTH_GOALS = [
  { id: 'weight-loss', label: 'Weight Loss' },
  { id: 'heart-health', label: 'Heart Health' },
  { id: 'diabetes-management', label: 'Diabetes Management' },
  { id: 'reduce-fat', label: 'Reduce Fat' },
  { id: 'increase-protein', label: 'Increase Protein' },
  { id: 'increase-fiber', label: 'Increase Fiber' },
  { id: 'reduce-sugar', label: 'Reduce Sugar' },
  { id: 'reduce-sodium', label: 'Reduce Sodium' },
  { id: 'clean-eating', label: 'Clean Eating' },
];

// Storage keys
const STORAGE_KEYS = {
  DIETARY_PREFERENCES: 'user_dietary_preferences',
  HEALTH_GOALS: 'user_health_goals',
};

interface DietaryPreferencesSelectorProps {
  onPreferencesChange?: (preferences: string[]) => void;
  onHealthGoalsChange?: (goals: string[]) => void;
}

export function DietaryPreferencesSelector({
  onPreferencesChange,
  onHealthGoalsChange,
}: DietaryPreferencesSelectorProps) {
  const { colors } = useTheme();
  const [selectedPreferences, setSelectedPreferences] = useState<string[]>([]);
  const [selectedGoals, setSelectedGoals] = useState<string[]>([]);

  // Load saved preferences on component mount
  useEffect(() => {
    const loadSavedPreferences = async () => {
      try {
        const savedPreferences = await AsyncStorage.getItem(STORAGE_KEYS.DIETARY_PREFERENCES);
        const savedGoals = await AsyncStorage.getItem(STORAGE_KEYS.HEALTH_GOALS);
        
        if (savedPreferences) {
          const parsedPreferences = JSON.parse(savedPreferences);
          setSelectedPreferences(parsedPreferences);
          onPreferencesChange?.(parsedPreferences);
        }
        
        if (savedGoals) {
          const parsedGoals = JSON.parse(savedGoals);
          setSelectedGoals(parsedGoals);
          onHealthGoalsChange?.(parsedGoals);
        }
      } catch (error) {
        console.error('Failed to load dietary preferences:', error);
      }
    };
    
    loadSavedPreferences();
  }, [onPreferencesChange, onHealthGoalsChange]);

  // Save preferences when they change
  const savePreferences = async (preferences: string[]) => {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.DIETARY_PREFERENCES, JSON.stringify(preferences));
      setSelectedPreferences(preferences);
      onPreferencesChange?.(preferences);
    } catch (error) {
      console.error('Failed to save dietary preferences:', error);
    }
  };

  // Save health goals when they change
  const saveHealthGoals = async (goals: string[]) => {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.HEALTH_GOALS, JSON.stringify(goals));
      setSelectedGoals(goals);
      onHealthGoalsChange?.(goals);
    } catch (error) {
      console.error('Failed to save health goals:', error);
    }
  };

  // Toggle a dietary preference
  const togglePreference = (preferenceId: string) => {
    const updatedPreferences = selectedPreferences.includes(preferenceId)
      ? selectedPreferences.filter(id => id !== preferenceId)
      : [...selectedPreferences, preferenceId];
    
    savePreferences(updatedPreferences);
  };

  // Toggle a health goal
  const toggleHealthGoal = (goalId: string) => {
    const updatedGoals = selectedGoals.includes(goalId)
      ? selectedGoals.filter(id => id !== goalId)
      : [...selectedGoals, goalId];
    
    saveHealthGoals(updatedGoals);
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Dietary Preferences
        </Text>
        <Text style={[styles.sectionDescription, { color: colors.textSecondary }]}>
          Select any dietary restrictions or preferences
        </Text>
        <View style={styles.optionGrid}>
          {DIETARY_PREFERENCES.map(preference => {
            const isSelected = selectedPreferences.includes(preference.id);
            return (
              <TouchableOpacity
                key={preference.id}
                style={[
                  styles.optionButton,
                  {
                    backgroundColor: isSelected ? colors.primaryLight : colors.subtle,
                    borderColor: isSelected ? colors.primary : colors.border,
                  },
                ]}
                onPress={() => togglePreference(preference.id)}
              >
                {isSelected && (
                  <View style={[styles.checkIcon, { backgroundColor: colors.primary }]}>
                    <Feather name="check" size={12}  color={colors.text} />
                  </View>
                )}
                <Text
                  style={[
                    styles.optionLabel,
                    { color: isSelected ? colors.primary : colors.text },
                  ]}
                >
                  {preference.label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>

      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Health Goals
        </Text>
        <Text style={[styles.sectionDescription, { color: colors.textSecondary }]}>
          Select your health priorities for recipe alternatives
        </Text>
        <View style={styles.optionGrid}>
          {HEALTH_GOALS.map(goal => {
            const isSelected = selectedGoals.includes(goal.id);
            return (
              <TouchableOpacity
                key={goal.id}
                style={[
                  styles.optionButton,
                  {
                    backgroundColor: isSelected ? colors.primaryLight : colors.subtle,
                    borderColor: isSelected ? colors.primary : colors.border,
                  },
                ]}
                onPress={() => toggleHealthGoal(goal.id)}
              >
                {isSelected && (
                  <View style={[styles.checkIcon, { backgroundColor: colors.primary }]}>
                    <Feather name="check" size={12}  color={colors.text} />
                  </View>
                )}
                <Text
                  style={[
                    styles.optionLabel,
                    { color: isSelected ? colors.primary : colors.text },
                  ]}
                >
                  {goal.label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
      
      <View style={styles.infoBox}>
        <Text style={[styles.infoTitle, { color: colors.text }]}>
          How we use your preferences
        </Text>
        <Text style={[styles.infoText, { color: colors.textSecondary }]}>
          Your selections help us personalize healthier recipe alternatives 
          to match your dietary needs and health objectives. We'll prioritize 
          ingredient substitutions that align with your preferences.
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 14,
    marginBottom: 16,
  },
  optionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    margin: 4,
    minWidth: '45%',
  },
  checkIcon: {
    width: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 6,
  },
  optionLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  infoBox: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#F0F9FF',
    marginBottom: 24,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
  },
}); 