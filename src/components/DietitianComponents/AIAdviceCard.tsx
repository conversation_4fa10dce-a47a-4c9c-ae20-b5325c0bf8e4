import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, Image } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import { Feather } from '@expo/vector-icons';

interface AIAdviceCardProps {
  title: string;
  advice: string[];
  isLoading?: boolean;
  onFeedback?: (isPositive: boolean, adviceIndex: number) => void;
  onRefresh?: () => void;
}

const AIAdviceCard: React.FC<AIAdviceCardProps> = ({
  title,
  advice,
  isLoading = false,
  onFeedback,
  onRefresh
}) => {
  const { colors, isDark } = useTheme();
  const [expanded, setExpanded] = useState(true);
  const [feedbackGiven, setFeedbackGiven] = useState<{[key: number]: 'positive' | 'negative' | null}>({});

  const handleFeedback = (isPositive: boolean, index: number) => {
    if (onFeedback) {
      onFeedback(isPositive, index);
    }
    setFeedbackGiven(prev => ({
      ...prev,
      [index]: isPositive ? 'positive' : 'negative'
    }));
  };

  return (
    <View style={[styles.container, { backgroundColor: isDark ? '#1E293B' : '#F8FAFC' }]}>
      <TouchableOpacity 
        style={styles.headerContainer}
        onPress={() => setExpanded(!expanded)}
        accessibilityRole="button"
        accessibilityState={{ expanded }}
        accessibilityLabel={`${title}, tap to ${expanded ? 'collapse' : 'expand'}`}
      >
        <LinearGradient
          colors={isDark ? ['#334155', '#1E293B'] : ['#F1F5F9', '#E2E8F0']}
          style={styles.header}
        >
          <View style={styles.titleContainer}>
            <Brain size={20} color={isDark ? '#60A5FA' : '#3B82F6'} style={styles.headerIcon} />
            <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
          </View>
          
          {expanded ? (
            <Feather name="chevron-up" size={20} color={colors.textSecondary} />
          ) : (
            <Feather name="chevron-down" size={20} color={colors.textSecondary} />
          )}
        </LinearGradient>
      </TouchableOpacity>
      
      {expanded && (
        <View style={styles.contentContainer}>
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                Analyzing your nutrition data...
              </Text>
            </View>
          ) : (
            <>
              <View style={styles.aiInfoContainer}>
                <View style={[styles.aiAvatarContainer, { backgroundColor: isDark ? '#334155' : '#E0E7FF' }]}>
                  <Brain 
                    size={24} 
                    color={isDark ? '#60A5FA' : '#3B82F6'} 
                    style={styles.aiAvatarIcon}
                  />
                </View>
                <View style={styles.aiDescription}>
                  <Text style={[styles.aiName, { color: colors.text }]}>AI Nutrition Advisor</Text>
                  <Text style={[styles.aiSubtitle, { color: colors.textSecondary }]}>
                    Personalized advice based on your data
                  </Text>
                </View>
              </View>
              
              <View style={styles.adviceList}>
                {advice.map((item, index) => (
                  <View 
                    key={`advice-${index}`} 
                    style={[
                      styles.adviceItem, 
                      { borderBottomColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }
                    ]}
                  >
                    <Text style={[styles.adviceText, { color: colors.text }]}>{item}</Text>
                    
                    {onFeedback && (
                      <View style={styles.feedbackContainer}>
                        {feedbackGiven[index] ? (
                          <Text style={[styles.feedbackText, { color: colors.textSecondary }]}>
                            {feedbackGiven[index] === 'positive' ? 'Thanks for your feedback!' : 'We\'ll improve our advice.'}
                          </Text>
                        ) : (
                          <>
                            <Text style={[styles.feedbackText, { color: colors.textSecondary }]}>Was this helpful?</Text>
                            <View style={styles.feedbackButtons}>
                              <TouchableOpacity 
                                style={[
                                  styles.feedbackButton, 
                                  { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)' }
                                ]}
                                onPress={() => handleFeedback(true, index)}
                                accessibilityLabel="This advice was helpful"
                                accessibilityRole="button"
                              >
                                <Feather name="thumbs-up" size={16} color={isDark ? '#60A5FA' : '#3B82F6'} />
                              </TouchableOpacity>
                              
                              <TouchableOpacity 
                                style={[
                                  styles.feedbackButton, 
                                  { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)' }
                                ]}
                                onPress={() => handleFeedback(false, index)}
                                accessibilityLabel="This advice was not helpful"
                                accessibilityRole="button"
                              >
                                <Feather name="thumbs-down" size={16} color={isDark ? '#94A3B8' : '#64748B'} />
                              </TouchableOpacity>
                            </View>
                          </>
                        )}
                      </View>
                    )}
                  </View>
                ))}
              </View>
              
              {onRefresh && (
                <TouchableOpacity 
                  style={[
                    styles.refreshButton, 
                    { backgroundColor: isDark ? '#334155' : '#E2E8F0' }
                  ]}
                  onPress={onRefresh}
                  accessibilityLabel="Refresh advice"
                  accessibilityRole="button"
                >
                  <Text style={[styles.refreshText, { color: colors.text }]}>
                    Get New Advice
                  </Text>
                </TouchableOpacity>
              )}
              
              <Text style={[styles.disclaimer, { color: colors.textSecondary }]}>
                This advice is generated based on your nutrition data and is not medical advice. 
                Consult healthcare professionals for medical concerns.
              </Text>
            </>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    overflow: 'hidden',
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  headerContainer: {
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: 10,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
  },
  aiInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  aiAvatarContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  aiAvatarIcon: {
    width: 24,
    height: 24,
  },
  aiDescription: {
    marginLeft: 12,
  },
  aiName: {
    fontSize: 16,
    fontWeight: '600',
  },
  aiSubtitle: {
    fontSize: 12,
  },
  adviceList: {
    marginTop: 8,
  },
  adviceItem: {
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  adviceText: {
    fontSize: 15,
    lineHeight: 22,
  },
  feedbackContainer: {
    marginTop: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  feedbackText: {
    fontSize: 13,
  },
  feedbackButtons: {
    flexDirection: 'row',
  },
  feedbackButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  refreshButton: {
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginTop: 20,
  },
  refreshText: {
    fontSize: 14,
    fontWeight: '600',
  },
  disclaimer: {
    fontSize: 12,
    fontStyle: 'italic',
    marginTop: 16,
  },
});

export default AIAdviceCard; 