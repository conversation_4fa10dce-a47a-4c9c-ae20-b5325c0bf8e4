import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Dimensions,
  Animated,
  Platform,
  SafeAreaView,
  TextInput
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

interface DietitianOnboardingProps {
  onComplete: () => void;
  onSkip: () => void;
}

const DietitianOnboarding: React.FC<DietitianOnboardingProps> = ({
  onComplete,
  onSkip
}) => {
  const { colors, isDark } = useTheme();
  const insets = useSafeAreaInsets();
  const [currentStep, setCurrentStep] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);
  const { width: screenWidth } = Dimensions.get('window');

  // Diet preference selection
  const [selectedDiet, setSelectedDiet] = useState<string | null>(null);
  
  // Goals selection
  const [selectedGoal, setSelectedGoal] = useState<string | null>(null);
  
  // Form values
  const [height, setHeight] = useState('');
  const [weight, setWeight] = useState('');
  const [age, setAge] = useState('');
  const [activityLevel, setActivityLevel] = useState<string | null>(null);

  const onboardingSteps = [
    {
      title: 'Welcome to AI Dietitian',
      subtitle: 'Your personal nutrition coach powered by AI',
      icon: <Salad size={100} color={isDark ? '#60A5FA' : '#3B82F6'} />,
      content: 'Get personalized meal plans, nutrition advice, and health insights tailored just for you.'
    },
    {
      title: 'Track Your Progress',
      subtitle: 'Monitor your nutrition journey',
      icon: <LineChart size={100} color={isDark ? '#60A5FA' : '#3B82F6'} />,
      content: 'Visualize your nutrition data with interactive charts and see your progress toward health goals.'
    },
    {
      title: 'Smart Meal Planning',
      subtitle: 'Delicious meals that match your goals',
      icon: <UtensilsCrossed size={100} color={isDark ? '#60A5FA' : '#3B82F6'} />,
      content: 'Receive AI-generated meal plans customized to your dietary preferences, restrictions, and nutritional needs.'
    },
    {
      title: 'Personalized Advice',
      subtitle: 'Get nutrition guidance that evolves with you',
      icon: <Brain size={100} color={isDark ? '#60A5FA' : '#3B82F6'} />,
      content: 'Receive actionable nutrition recommendations based on your food choices and health data.'
    },
    {
      title: 'Let\'s Get Started',
      subtitle: 'Tell us about your dietary preferences',
      type: 'diet-selection',
      options: [
        { id: 'no_restrictions', label: 'No Restrictions', description: 'I eat everything' },
        { id: 'vegetarian', label: 'Vegetarian', description: 'No meat, but I eat dairy and eggs' },
        { id: 'vegan', label: 'Vegan', description: 'No animal products' },
        { id: 'pescatarian', label: 'Pescatarian', description: 'No meat except fish' },
        { id: 'keto', label: 'Keto', description: 'High fat, low carb' },
        { id: 'paleo', label: 'Paleo', description: 'Whole foods, no processed items' },
        { id: 'gluten_free', label: 'Gluten Free', description: 'No gluten-containing foods' },
        { id: 'dairy_free', label: 'Dairy Free', description: 'No dairy products' }
      ]
    },
    {
      title: 'What\'s Your Goal?',
      subtitle: 'Tell us what you want to achieve',
      type: 'goal-selection',
      options: [
        { id: 'weight_loss', label: 'Weight Loss', description: 'Reduce body fat and weight' },
        { id: 'muscle_gain', label: 'Muscle Gain', description: 'Build muscle mass and strength' },
        { id: 'maintenance', label: 'Maintenance', description: 'Maintain current weight and body composition' },
        { id: 'general_health', label: 'General Health', description: 'Improve overall health and wellbeing' },
        { id: 'athletic_performance', label: 'Athletic Performance', description: 'Optimize nutrition for sports and fitness' }
      ]
    },
    {
      title: 'Your Profile Details',
      subtitle: 'Help us create your personalized plan',
      type: 'profile-info'
    },
    {
      title: 'All Set!',
      subtitle: 'Your AI Dietitian is ready',
      type: 'completion',
      content: "We've prepared personalized nutrition recommendations and meal plans based on your profile. Your journey to better nutrition starts now!"
    }
  ];

  const handleNext = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    if (currentStep < onboardingSteps.length - 1) {
      scrollViewRef.current?.scrollTo({
        x: screenWidth * (currentStep + 1),
        animated: true
      });
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      scrollViewRef.current?.scrollTo({
        x: screenWidth * (currentStep - 1),
        animated: true
      });
      setCurrentStep(currentStep - 1);
    }
  };

  const handleScroll = (event: any) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const newStep = Math.round(contentOffsetX / screenWidth);
    if (newStep !== currentStep) {
      setCurrentStep(newStep);
    }
  };

  const handleComplete = () => {
    // Save user preferences to storage or API
    const userProfile = {
      dietPreference: selectedDiet,
      goal: selectedGoal,
      height: height ? parseFloat(height) : null,
      weight: weight ? parseFloat(weight) : null,
      age: age ? parseInt(age) : null,
      activityLevel
    };

    console.log('Saving user profile:', userProfile);
    
    // Call the onComplete callback
    onComplete();
  };

  const renderStepIndicators = () => {
    return (
      <View style={styles.stepIndicatorContainer}>
        {onboardingSteps.map((_, index) => (
          <View
            key={`step-${index}`}
            style={[
              styles.stepIndicator,
              {
                backgroundColor: index === currentStep ? 
                  colors.primary : 
                  (index < currentStep ? colors.primary + '80' : isDark ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.1)')
              }
            ]}
          />
        ))}
      </View>
    );
  };

  const renderDietOptions = () => {
    const { options = [] } = onboardingSteps[4];
    
    return (
      <View style={styles.optionsContainer}>
        {options.map((option) => (
          <TouchableOpacity
            key={option.id}
            style={[
              styles.optionItem,
              selectedDiet === option.id && {
                borderColor: colors.primary,
                backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'
              },
              { borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)' }
            ]}
            onPress={() => setSelectedDiet(option.id)}
            accessibilityRole="radio"
            accessibilityState={{ checked: selectedDiet === option.id }}
            accessibilityLabel={`${option.label}: ${option.description}`}
          >
            <View style={styles.optionContent}>
              <Text style={[styles.optionLabel, { color: colors.text }]}>
                {option.label}
              </Text>
              <Text style={[styles.optionDescription, { color: colors.textSecondary }]}>
                {option.description}
              </Text>
            </View>
            {selectedDiet === option.id && (
              <View style={[styles.checkContainer, { backgroundColor: colors.primary }]}>
                <Feather name="check" size={16}  color={colors.text} />
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderGoalOptions = () => {
    const { options = [] } = onboardingSteps[5];
    
    return (
      <View style={styles.optionsContainer}>
        {options.map((option) => (
          <TouchableOpacity
            key={option.id}
            style={[
              styles.optionItem,
              selectedGoal === option.id && {
                borderColor: colors.primary,
                backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'
              },
              { borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)' }
            ]}
            onPress={() => setSelectedGoal(option.id)}
            accessibilityRole="radio"
            accessibilityState={{ checked: selectedGoal === option.id }}
            accessibilityLabel={`${option.label}: ${option.description}`}
          >
            <View style={styles.optionContent}>
              <Text style={[styles.optionLabel, { color: colors.text }]}>
                {option.label}
              </Text>
              <Text style={[styles.optionDescription, { color: colors.textSecondary }]}>
                {option.description}
              </Text>
            </View>
            {selectedGoal === option.id && (
              <View style={[styles.checkContainer, { backgroundColor: colors.primary }]}>
                <Feather name="check" size={16}  color={colors.text} />
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderProfileForm = () => {
    const activityOptions = [
      { id: 'sedentary', label: 'Sedentary', description: 'Little to no exercise' },
      { id: 'light', label: 'Lightly Active', description: '1-3 days/week' },
      { id: 'moderate', label: 'Moderately Active', description: '3-5 days/week' },
      { id: 'very', label: 'Very Active', description: '6-7 days/week' },
      { id: 'extra', label: 'Extra Active', description: 'Very intense daily exercise/sports' }
    ];
    
    return (
      <View style={styles.formContainer}>
        <View style={styles.formRow}>
          <View style={styles.formField}>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>Height (cm)</Text>
            <TextInput
              style={[
                styles.textInput, 
                { 
                  backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
                  color: colors.text,
                  borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'
                }
              ]}
              value={height}
              onChangeText={setHeight}
              placeholder="Enter height"
              placeholderTextColor={colors.textSecondary}
              keyboardType="numeric"
              accessibilityLabel="Height in centimeters"
            />
          </View>
          
          <View style={styles.formField}>
            <Text style={[styles.fieldLabel, { color: colors.text }]}>Weight (kg)</Text>
            <TextInput
              style={[
                styles.textInput, 
                { 
                  backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
                  color: colors.text,
                  borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'
                }
              ]}
              value={weight}
              onChangeText={setWeight}
              placeholder="Enter weight"
              placeholderTextColor={colors.textSecondary}
              keyboardType="numeric"
              accessibilityLabel="Weight in kilograms"
            />
          </View>
        </View>
        
        <View style={styles.formField}>
          <Text style={[styles.fieldLabel, { color: colors.text }]}>Age</Text>
          <TextInput
            style={[
              styles.textInput, 
              { 
                backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
                color: colors.text,
                borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'
              }
            ]}
            value={age}
            onChangeText={setAge}
            placeholder="Enter age"
            placeholderTextColor={colors.textSecondary}
            keyboardType="numeric"
            accessibilityLabel="Age in years"
          />
        </View>
        
        <Text style={[styles.fieldLabel, { color: colors.text, marginTop: 16 }]}>
          Activity Level
        </Text>
        <View style={styles.activityOptions}>
          {activityOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={[
                styles.activityOption,
                activityLevel === option.id && {
                  borderColor: colors.primary,
                  backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)'
                },
                { borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)' }
              ]}
              onPress={() => setActivityLevel(option.id)}
              accessibilityRole="radio"
              accessibilityState={{ checked: activityLevel === option.id }}
            >
              <Text style={[styles.activityLabel, { color: colors.text }]}>
                {option.label}
              </Text>
              <Text style={[styles.activityDescription, { color: colors.textSecondary }]}>
                {option.description}
              </Text>
              {activityLevel === option.id && (
                <View style={[styles.checkContainer, { backgroundColor: colors.primary }]}>
                  <Feather name="check" size={14}  color={colors.text} />
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderCompletion = () => {
    return (
      <View style={styles.completionContainer}>
        <LinearGradient
          colors={isDark ? ['#3B82F6', '#2563EB'] : ['#60A5FA', '#3B82F6']}
          style={styles.completionCircle}
        >
          <Feather name="check" size={48}  color={colors.text} />
        </LinearGradient>
        
        <Text style={[styles.completionText, { color: colors.text }]}>
          {onboardingSteps[7].content}
        </Text>
        
        <TouchableOpacity
          style={[styles.completionButton, { backgroundColor: colors.primary }]}
          onPress={handleComplete}
          accessibilityLabel="Start using AI Dietitian"
          accessibilityRole="button"
        >
          <Text style={styles.completionButtonText}>Let's Go!</Text>
          <Feather name="arrow-right" size={20}  color={colors.text} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderStepContent = (step: any, index: number) => {
    switch (step.type) {
      case 'diet-selection':
        return renderDietOptions();
      case 'goal-selection':
        return renderGoalOptions();
      case 'profile-info':
        return renderProfileForm();
      case 'completion':
        return renderCompletion();
      default:
        return (
          <View style={styles.introStep}>
            <View style={[
              styles.iconContainer, 
              { backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)' }
            ]}>
              {step.icon}
            </View>
            <Text style={[styles.stepContent, { color: colors.text }]}>
              {step.content}
            </Text>
          </View>
        );
    }
  };

  const isLastStep = currentStep === onboardingSteps.length - 1;
  const canAdvance = (() => {
    if (currentStep === 4) return !!selectedDiet;
    if (currentStep === 5) return !!selectedGoal;
    if (currentStep === 6) return !!age && !!weight && !!height && !!activityLevel;
    return true;
  })();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { paddingTop: insets.top > 0 ? 0 : 16 }]}>
        {currentStep > 0 && (
          <TouchableOpacity
            style={[styles.backButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
            onPress={handleBack}
            accessibilityLabel="Go back"
            accessibilityRole="button"
          >
            <Feather name="chevron-right" size={20} color={colors.text} style={styles.backIcon} />
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={styles.skipButton}
          onPress={onSkip}
          accessibilityLabel="Skip onboarding"
          accessibilityRole="button"
        >
          <Text style={[styles.skipText, { color: colors.textSecondary }]}>
            {currentStep < onboardingSteps.length - 1 ? 'Skip' : 'Close'}
          </Text>
          <Feather name="x" size={16} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>

      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        scrollEnabled={false}
        onMomentumScrollEnd={handleScroll}
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
      >
        {onboardingSteps.map((step, index) => (
          <View key={`step-${index}`} style={[styles.step, { width: screenWidth }]}>
            <View style={styles.stepHeader}>
              <Text style={[styles.stepTitle, { color: colors.text }]}>
                {step.title}
              </Text>
              {step.subtitle && (
                <Text style={[styles.stepSubtitle, { color: colors.textSecondary }]}>
                  {step.subtitle}
                </Text>
              )}
            </View>
            
            {renderStepContent(step, index)}
          </View>
        ))}
      </ScrollView>

      {renderStepIndicators()}

      {!isLastStep && (
        <View style={[styles.footer, { paddingBottom: insets.bottom > 0 ? insets.bottom : 24 }]}>
          <TouchableOpacity
            style={[
              styles.nextButton,
              { backgroundColor: colors.primary },
              !canAdvance && { opacity: 0.5 }
            ]}
            onPress={handleNext}
            disabled={!canAdvance}
            accessibilityLabel="Next step"
            accessibilityRole="button"
          >
            <Text style={styles.nextButtonText}>Continue</Text>
            <Feather name="chevron-right" size={20}  color={colors.text} />
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backIcon: {
    transform: [{ rotate: '180deg' }],
  },
  skipButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  skipText: {
    fontSize: 16,
    marginRight: 8,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  step: {
    flex: 1,
    padding: 24,
  },
  stepHeader: {
    marginBottom: 32,
  },
  stepTitle: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 8,
  },
  stepSubtitle: {
    fontSize: 16,
    lineHeight: 22,
  },
  introStep: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    width: 200,
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
    borderRadius: 24,
  },
  stepImage: {
    width: '100%',
    height: 240,
    marginBottom: 32,
  },
  stepContent: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
  },
  stepIndicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 32,
  },
  stepIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  footer: {
    padding: 24,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 12,
  },
  nextButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  optionsContainer: {
    flex: 1,
    paddingBottom: 16,
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: 12,
  },
  optionContent: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
  },
  checkContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  formContainer: {
    flex: 1,
    paddingBottom: 16,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  formField: {
    flex: 1,
    marginRight: 12,
    marginBottom: 16,
  },
  fieldLabel: {
    fontSize: 16,
    marginBottom: 8,
    fontWeight: '500',
  },
  textInput: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 16,
    borderWidth: 1,
    fontSize: 16,
  },
  activityOptions: {
    marginTop: 8,
  },
  activityOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 8,
  },
  activityLabel: {
    fontSize: 15,
    fontWeight: '500',
    flex: 1,
  },
  activityDescription: {
    fontSize: 13,
    marginRight: 8,
  },
  completionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  completionCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
  },
  completionText: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  completionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
  },
  completionButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    marginRight: 8,
  },
});

export default DietitianOnboarding; 