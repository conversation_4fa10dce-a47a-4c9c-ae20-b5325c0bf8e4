import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView,
  ActivityIndicator,
  Switch,
  Share,
  Alert
, Platform } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as Print from 'expo-print';
import * as MediaLibrary from 'expo-media-library';

interface GroceryItem {
  id: string;
  name: string;
  quantity: string;
  category: string;
  checked: boolean;
}

interface GroceryListExportProps {
  mealPlan: any;
  onClose: () => void;
}

const GroceryListExport: React.FC<GroceryListExportProps> = ({ 
  mealPlan, 
  onClose 
}) => {
  const { colors, isDark } = useTheme();
  const [groceryItems, setGroceryItems] = useState<GroceryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [groupByCategory, setGroupByCategory] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [exportFormat, setExportFormat] = useState<'pdf' | 'text'>('text');

  useEffect(() => {
    if (mealPlan) {
      extractGroceryItems();
    }
  }, [mealPlan]);

  const extractGroceryItems = () => {
    setIsLoading(true);
    
    // In a real app, you would have a more sophisticated algorithm to extract
    // and consolidate ingredients from the meal plan
    if (!mealPlan || !mealPlan.meals) {
      setGroceryItems([]);
      setIsLoading(false);
      return;
    }

    const items: GroceryItem[] = [];
    let itemId = 0;

    // Extract ingredients from all meals
    mealPlan.meals.forEach((meal: any) => {
      if (meal.ingredients && Array.isArray(meal.ingredients)) {
        meal.ingredients.forEach((ingredient: any) => {
          // Check if ingredient already exists (by name)
          const existingItem = items.find(item => 
            item.name.toLowerCase() === ingredient.name.toLowerCase()
          );

          if (existingItem) {
            // Could add logic to combine quantities, but for simplicity we'll just note it's needed in multiple recipes
            existingItem.quantity += ` + ${ingredient.quantity || ''}`;
          } else {
            // Add new ingredient
            items.push({
              id: `item-${itemId++}`,
              name: ingredient.name,
              quantity: ingredient.quantity || '',
              category: inferCategory(ingredient.name),
              checked: false
            });
          }
        });
      }
    });

    setGroceryItems(items);
    setIsLoading(false);
  };

  // Simple function to infer category based on ingredient name
  const inferCategory = (ingredientName: string): string => {
    const name = ingredientName.toLowerCase();
    
    if (name.includes('apple') || name.includes('banana') || name.includes('berry') || 
        name.includes('fruit') || name.includes('orange') || name.includes('melon')) {
      return 'Fruits';
    } else if (name.includes('carrot') || name.includes('broccoli') || name.includes('spinach') || 
               name.includes('lettuce') || name.includes('vegetable') || name.includes('onion')) {
      return 'Vegetables';
    } else if (name.includes('chicken') || name.includes('beef') || name.includes('pork') || 
               name.includes('fish') || name.includes('meat') || name.includes('salmon')) {
      return 'Meat & Seafood';
    } else if (name.includes('milk') || name.includes('cheese') || name.includes('yogurt') || 
               name.includes('cream') || name.includes('butter')) {
      return 'Dairy';
    } else if (name.includes('rice') || name.includes('pasta') || name.includes('bread') || 
               name.includes('cereal') || name.includes('flour') || name.includes('grain')) {
      return 'Grains & Pasta';
    } else if (name.includes('oil') || name.includes('vinegar') || name.includes('sauce') || 
               name.includes('spice') || name.includes('herb') || name.includes('salt')) {
      return 'Condiments & Spices';
    } else {
      return 'Other';
    }
  };

  const toggleItemCheck = (id: string) => {
    setGroceryItems(prevItems => 
      prevItems.map(item => 
        item.id === id ? { ...item, checked: !item.checked } : item
      )
    );
  };

  const toggleGroupByCategory = () => {
    setGroupByCategory(!groupByCategory);
  };

  const filterByCategory = (category: string) => {
    if (selectedCategory === category) {
      setSelectedCategory(null);
    } else {
      setSelectedCategory(category);
    }
  };

  const getCategories = (): string[] => {
    const categories = new Set(groceryItems.map(item => item.category));
    return Array.from(categories).sort();
  };

  const filteredItems = selectedCategory 
    ? groceryItems.filter(item => item.category === selectedCategory)
    : groceryItems;

  const sortedItems = groupByCategory
    ? [...filteredItems].sort((a, b) => a.category.localeCompare(b.category))
    : filteredItems;

  const handleExport = async () => {
    if (groceryItems.length === 0) {
      Alert.alert("Nothing to Export", "Your grocery list is empty.");
      return;
    }

    if (exportFormat === 'pdf') {
      await exportAsPdf();
    } else {
      await exportAsText();
    }
  };

  const exportAsText = async () => {
    // Organize items by category if grouping is enabled
    let textContent = `Grocery List for ${mealPlan.title || 'Your Meal Plan'}\n\n`;
    
    if (groupByCategory) {
      const categories = getCategories();
      categories.forEach(category => {
        const categoryItems = sortedItems.filter(item => item.category === category);
        if (categoryItems.length > 0) {
          textContent += `${category}:\n`;
          categoryItems.forEach(item => {
            textContent += `- ${item.name} ${item.quantity ? `(${item.quantity})` : ''}\n`;
          });
          textContent += '\n';
        }
      });
    } else {
      sortedItems.forEach(item => {
        textContent += `- ${item.name} ${item.quantity ? `(${item.quantity})` : ''}\n`;
      });
    }

    try {
      if (Platform.OS === 'web') {
        // For web, we'll just copy to clipboard or offer a download link
        Alert.alert("Export on Web", "On web, you would be able to download this file or copy to clipboard.");
      } else {
        // Share as text
        await Share.share({
          message: textContent,
          title: 'Grocery List'
        });
      }
    } catch (error) {
      console.error('Error sharing text:', error);
      Alert.alert('Export Failed', 'There was an error exporting your grocery list.');
    }
  };

  const exportAsPdf = async () => {
    // Create HTML for PDF
    let htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Grocery List</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #3B82F6; }
            .category { margin-top: 20px; }
            .category-title { color: #3B82F6; font-weight: bold; }
            .item { margin: 8px 0; }
          </style>
        </head>
        <body>
          <h1>Grocery List for ${mealPlan.title || 'Your Meal Plan'}</h1>
    `;

    if (groupByCategory) {
      const categories = getCategories();
      categories.forEach(category => {
        const categoryItems = sortedItems.filter(item => item.category === category);
        if (categoryItems.length > 0) {
          htmlContent += `<div class="category">
            <p class="category-title">${category}</p>
            <ul>`;
          
          categoryItems.forEach(item => {
            htmlContent += `<li class="item">${item.name} ${item.quantity ? `(${item.quantity})` : ''}</li>`;
          });
          
          htmlContent += `</ul></div>`;
        }
      });
    } else {
      htmlContent += `<ul>`;
      sortedItems.forEach(item => {
        htmlContent += `<li class="item">${item.name} ${item.quantity ? `(${item.quantity})` : ''}</li>`;
      });
      htmlContent += `</ul>`;
    }

    htmlContent += `</body></html>`;

    try {
      if (Platform.OS === 'web') {
        // For web, we would use a different approach
        Alert.alert("Export on Web", "On web, you would be able to download this PDF file.");
      } else {
        // Generate PDF file
        const { uri } = await Print.printToFileAsync({ html: htmlContent });
        
        // On iOS, share the file
        if (Platform.OS === 'ios') {
          await Sharing.shareAsync(uri);
        } 
        // On Android, save to media library then share
        else if (Platform.OS === 'android') {
          const permission = await MediaLibrary.requestPermissionsAsync();
          
          if (permission.granted) {
            const asset = await MediaLibrary.createAssetAsync(uri);
            await MediaLibrary.createAlbumAsync('Downloads', asset, false);
            Alert.alert('PDF Saved', 'Your grocery list PDF has been saved to your Downloads folder.');
            
            // Also allow sharing
            await Sharing.shareAsync(uri);
          } else {
            // If permission not granted, just share the file
            await Sharing.shareAsync(uri);
          }
        }
      }
    } catch (error) {
      console.error('Error creating PDF:', error);
      Alert.alert('Export Failed', 'There was an error creating your PDF grocery list.');
    }
  };

  const toggleExportFormat = () => {
    setExportFormat(prevFormat => prevFormat === 'pdf' ? 'text' : 'pdf');
  };

  const addCustomItem = () => {
    // In a real app, this would open a modal to add a custom item
    Alert.alert(
      "Add Item",
      "In the complete app, this would open a form to add your own custom item to the grocery list.",
      [{ text: "OK" }]
    );
  };

  // Render category sections if grouping is enabled
  const renderCategorySections = () => {
    const categories = getCategories();
    return categories.map(category => {
      const categoryItems = sortedItems.filter(item => item.category === category);
      if (categoryItems.length === 0) return null;
      
      return (
        <View key={category} style={styles.categorySection}>
          <Text style={[styles.categoryTitle, { color: colors.primary }]}>
            {category}
          </Text>
          {categoryItems.map(renderGroceryItem)}
        </View>
      );
    });
  };

  // Render individual grocery item
  const renderGroceryItem = (item: GroceryItem) => (
    <TouchableOpacity
      key={item.id}
      style={[
        styles.groceryItem,
        { borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)' }
      ]}
      onPress={() => toggleItemCheck(item.id)}
      accessibilityRole="checkbox"
      accessibilityState={{ checked: item.checked }}
      accessibilityLabel={`${item.name}, ${item.quantity}, ${item.category}`}
    >
      <View 
        style={[
          styles.checkBox, 
          { 
            backgroundColor: item.checked ? colors.primary : 'transparent',
            borderColor: item.checked ? colors.primary : isDark ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.3)'
          }
        ]}
      >
        {item.checked && <Feather name="check" size={16}  color={colors.text} />}
      </View>
      
      <View style={styles.itemDetails}>
        <Text 
          style={[
            styles.itemName, 
            { 
              color: colors.text,
              textDecorationLine: item.checked ? 'line-through' : 'none'
            }
          ]}
        >
          {item.name}
        </Text>
        
        {item.quantity ? (
          <Text style={[styles.itemQuantity, { color: colors.textSecondary }]}>
            {item.quantity}
          </Text>
        ) : null}
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Feather name="shopping-cart" size={24} color={colors.primary} style={styles.headerIcon} />
          <Text style={[styles.title, { color: colors.text }]}>
            Grocery List
          </Text>
        </View>
        
        <TouchableOpacity 
          onPress={onClose}
          style={[styles.closeButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
          accessibilityLabel="Close grocery list"
          accessibilityRole="button"
        >
          <Feather name="x" size={20} color={colors.text} />
        </TouchableOpacity>
      </View>
      
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Generating grocery list...
          </Text>
        </View>
      ) : (
        <>
          <View style={styles.options}>
            <View style={styles.optionItem}>
              <Text style={[styles.optionLabel, { color: colors.text }]}>
                Group by category
              </Text>
              <Switch
                value={groupByCategory}
                onValueChange={toggleGroupByCategory}
                trackColor={{ false: '#767577', true: colors.primary + '50' }}
                thumbColor={groupByCategory ? colors.primary : '#f4f3f4'}
                ios_backgroundColor="#3e3e3e"
              />
            </View>
            
            <View style={styles.optionItem}>
              <Text style={[styles.optionLabel, { color: colors.text }]}>
                Export as PDF
              </Text>
              <Switch
                value={exportFormat === 'pdf'}
                onValueChange={toggleExportFormat}
                trackColor={{ false: '#767577', true: colors.primary + '50' }}
                thumbColor={exportFormat === 'pdf' ? colors.primary : '#f4f3f4'}
                ios_backgroundColor="#3e3e3e"
              />
            </View>
          </View>
          
          {groupByCategory && (
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false} 
              style={styles.categoriesScroll}
              contentContainerStyle={styles.categoriesContent}
            >
              {getCategories().map(category => (
                <TouchableOpacity
                  key={category}
                  style={[
                    styles.categoryChip,
                    selectedCategory === category && { backgroundColor: colors.primary },
                    { borderColor: colors.primary }
                  ]}
                  onPress={() => filterByCategory(category)}
                  accessibilityRole="button"
                  accessibilityLabel={`Filter by ${category}`}
                  accessibilityState={{ selected: selectedCategory === category }}
                >
                  <Text 
                    style={[
                      styles.categoryChipText,
                      { color: selectedCategory === category ? '#FFFFFF' : colors.primary }
                    ]}
                  >
                    {category}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}
          
          {groceryItems.length > 0 ? (
            <ScrollView 
              style={styles.groceryList}
              contentContainerStyle={styles.groceryListContent}
              showsVerticalScrollIndicator={false}
            >
              {groupByCategory ? renderCategorySections() : sortedItems.map(renderGroceryItem)}
              
              <TouchableOpacity
                style={[styles.addItemButton, { borderColor: colors.primary }]}
                onPress={addCustomItem}
                accessibilityLabel="Add custom item"
                accessibilityRole="button"
              >
                <Feather name="plus" size={20} color={colors.primary} />
                <Text style={[styles.addItemText, { color: colors.primary }]}>
                  Add Custom Item
                </Text>
              </TouchableOpacity>
            </ScrollView>
          ) : (
            <View style={styles.emptyState}>
              <Feather name="shopping-cart" size={48} color={isDark ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)'} />
              <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
                No ingredients found in your meal plan.
              </Text>
            </View>
          )}
          
          <View style={styles.footer}>
            <TouchableOpacity
              style={[
                styles.exportButton,
                { backgroundColor: colors.primary },
                groceryItems.length === 0 && { opacity: 0.5 }
              ]}
              onPress={handleExport}
              disabled={groceryItems.length === 0}
              accessibilityLabel={`Export as ${exportFormat === 'pdf' ? 'PDF' : 'text'}`}
              accessibilityRole="button"
            >
              <Text style={styles.exportButtonText}>
                Export {exportFormat === 'pdf' ? 'PDF' : 'List'}
              </Text>
              {exportFormat === 'pdf' ? (
                <Feather name="download" size={20}  color={colors.text} />
              ) : (
                <ShareIcon size={20} color="#FFFFFF" />
              )}
            </TouchableOpacity>
          </View>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  options: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionLabel: {
    fontSize: 15,
    marginRight: 8,
  },
  categoriesScroll: {
    maxHeight: 44,
    marginBottom: 8,
  },
  categoriesContent: {
    paddingHorizontal: 16,
  },
  categoryChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    marginRight: 8,
  },
  categoryChipText: {
    fontSize: 14,
    fontWeight: '500',
  },
  groceryList: {
    flex: 1,
  },
  groceryListContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  categorySection: {
    marginBottom: 16,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  groceryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  checkBox: {
    width: 24,
    height: 24,
    borderRadius: 6,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    marginBottom: 2,
  },
  itemQuantity: {
    fontSize: 14,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyStateText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  exportButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  exportButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  addItemButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: 8,
    marginTop: 16,
  },
  addItemText: {
    fontSize: 15,
    fontWeight: '500',
    marginLeft: 8,
  },
});

export default GroceryListExport; 