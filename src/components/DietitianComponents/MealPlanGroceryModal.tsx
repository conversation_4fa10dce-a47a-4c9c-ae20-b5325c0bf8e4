import React from 'react';
import { 
  View, 
  StyleSheet, 
  Modal, 
  TouchableWithoutFeedback,
  Dimensions
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { BlurView } from 'expo-blur';
import GroceryListExport from './GroceryListExport';

interface MealPlanGroceryModalProps {
  isVisible: boolean;
  onClose: () => void;
  mealPlan: any;
}

const MealPlanGroceryModal: React.FC<MealPlanGroceryModalProps> = ({ 
  isVisible, 
  onClose,
  mealPlan
}) => {
  const { isDark } = useTheme();
  const { height: screenHeight } = Dimensions.get('window');
  
  return (
    <Modal
      transparent
      visible={isVisible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <BlurView
        intensity={10}
        tint={isDark ? 'dark' : 'light'}
        style={styles.backdrop}
      >
        <TouchableWithoutFeedback onPress={onClose}>
          <View style={styles.backdrop} />
        </TouchableWithoutFeedback>
        
        <View 
          style={[
            styles.modalContainer, 
            { 
              height: screenHeight * 0.85,
              backgroundColor: isDark ? '#0F172A' : '#FFFFFF'
            }
          ]}
        >
          <GroceryListExport mealPlan={mealPlan} onClose={onClose} />
        </View>
      </BlurView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    overflow: 'hidden',
    // Shadow for iOS
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -5 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    // Shadow for Android
    elevation: 5,
  },
});

export default MealPlanGroceryModal; 