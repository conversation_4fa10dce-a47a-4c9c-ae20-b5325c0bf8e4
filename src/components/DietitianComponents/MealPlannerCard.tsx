import React, { useState , useCallback } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Image, 
  ScrollView, 
  Animated, 
  useWindowDimensions,
  Platform
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import { Feather } from '@expo/vector-icons';
import { NutritionInfo } from '@/types/api-responses';
import { router } from 'expo-router';
import MealPlanGroceryModal from './MealPlanGroceryModal';
import SocialSharingModal from './SocialSharingModal';

export interface MealItem {
  id: string;
  name: string;
  description: string;
  imageUrl?: string;
  prepTime: number;
  nutrition: NutritionInfo;
  ingredients: string[];
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
}

interface MealPlannerCardProps {
  date: string;
  meals: {
    breakfast?: MealItem;
    lunch?: MealItem;
    dinner?: MealItem;
    snacks?: MealItem[];
  };
  onSaveMeal?: (meal: MealItem) => void;
  onShareMeal?: (meal: MealItem) => void;
  onGenerateAlternative?: (mealType: string) => void;
}

const MealPlannerCard: React.FC<MealPlannerCardProps> = ({
  date,
  meals,
  onSaveMeal,
  onShareMeal,
  onGenerateAlternative
}) => {
  const { colors, isDark } = useTheme();
  const { width } = useWindowDimensions();
  const [activeMeal, setActiveMeal] = useState<'breakfast' | 'lunch' | 'dinner' | 'snacks'>('breakfast');
  const [saved, setSaved] = useState<{[key: string]: boolean}>({});
  const [isGroceryModalVisible, setIsGroceryModalVisible] = useState(false);
  const [isSharingModalVisible, setIsSharingModalVisible] = useState(false);
  const [mealToShare, setMealToShare] = useState<MealItem | null>(null);

  const getMealTypeColor = (mealType: string) => {
    switch (mealType) {
      case 'breakfast':
        return isDark ? '#F59E0B' : '#F59E0B';
      case 'lunch':
        return isDark ? '#10B981' : '#059669';
      case 'dinner':
        return isDark ? '#8B5CF6' : '#7C3AED';
      case 'snack':
      case 'snacks':
        return isDark ? '#EC4899' : '#DB2777';
      default:
        return colors.primary;
    }
  };

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const handleSaveMeal = (meal: MealItem) => {
    if (onSaveMeal) {
      onSaveMeal(meal);
    }
    setSaved(prev => ({
      ...prev,
      [meal.id]: true
    }));
  };

  const handleShareMeal = (meal: MealItem) => {
    setMealToShare(meal);
    setIsSharingModalVisible(true);
    
    if (onShareMeal) {
      onShareMeal(meal);
    }
  };

  const handleShareMealPlan = () => {
    setIsSharingModalVisible(true);
    setMealToShare(null); // Set to null to indicate we're sharing the whole meal plan
  };

  const renderMealTab = (mealType: 'breakfast' | 'lunch' | 'dinner' | 'snacks', label: string) => {
    const isActive = activeMeal === mealType;
    const mealColor = getMealTypeColor(mealType);
    
    return (
      <TouchableOpacity
        style={[
          styles.mealTab,
          isActive && [styles.activeMealTab, { borderColor: mealColor }]
        ]}
        onPress={() => setActiveMeal(mealType)}
        accessibilityRole="tab"
        accessibilityState={{ selected: isActive }}
        accessibilityLabel={`${label} tab`}
      >
        <Text 
          style={[
            styles.mealTabText,
            { color: isActive ? mealColor : colors.textSecondary }
          ]}
        >
          {label}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderMealCard = (meal?: MealItem) => {
    if (!meal) {
      return (
        <View style={[styles.noMealContainer, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)' }]}>
          <Text style={[styles.noMealText, { color: colors.textSecondary }]}>
            No meal planned for this time
          </Text>
          {onGenerateAlternative && (
            <TouchableOpacity
              style={[styles.generateButton, { backgroundColor: colors.primary }]}
              onPress={() => onGenerateAlternative(activeMeal)}
              accessibilityLabel={`Generate a ${activeMeal} meal`}
              accessibilityRole="button"
            >
              <Feather name="plus" size={16} style={styles.generateIcon} />
              <Text style={styles.generateText}>Generate Meal</Text>
            </TouchableOpacity>
          )}
        </View>
      );
    }

    return (
      <View style={styles.mealCardContainer}>
        <TouchableOpacity
          style={[styles.mealCard, { backgroundColor: isDark ? '#1E293B' : '#FFFFFF' }]}
          onPress={() => router.push(`/recipe/${meal.id}`)}
          accessibilityLabel={`View ${meal.name} recipe`}
          accessibilityRole="button"
        >
          {meal.imageUrl ? (
            <Image
              source={{ uri: meal.imageUrl }}
              style={styles.mealImage}
              accessibilityLabel={`Image of ${meal.name}`}
            />
          ) : (
            <View 
              style={[
                styles.mealImagePlaceholder, 
                { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }
              ]} 
            />
          )}
          
          <View style={styles.mealInfo}>
            <View style={styles.mealHeader}>
              <Text style={[styles.mealType, { color: getMealTypeColor(meal.mealType) }]}>
                {meal.mealType.charAt(0).toUpperCase() + meal.mealType.slice(1)}
              </Text>
              <View style={styles.prepTimeContainer}>
                <Feather name="clock" size={14} color={colors.textSecondary} style={styles.clockIcon} />
                <Text style={[styles.prepTime, { color: colors.textSecondary }]}>
                  {meal.prepTime} min
                </Text>
              </View>
            </View>
            
            <Text 
              style={[styles.mealName, { color: colors.text }]}
              numberOfLines={2}
            >
              {meal.name}
            </Text>
            
            <Text 
              style={[styles.mealDescription, { color: colors.textSecondary }]}
              numberOfLines={2}
            >
              {meal.description}
            </Text>
            
            <View style={styles.nutritionInfo}>
              <View style={styles.nutritionItem}>
                <Text style={[styles.nutritionValue, { color: colors.text }]}>
                  {meal.nutrition.calories}
                </Text>
                <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                  calories
                </Text>
              </View>
              
              <View style={styles.nutritionItem}>
                <Text style={[styles.nutritionValue, { color: colors.text }]}>
                  {meal.nutrition.protein}g
                </Text>
                <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                  protein
                </Text>
              </View>
              
              <View style={styles.nutritionItem}>
                <Text style={[styles.nutritionValue, { color: colors.text }]}>
                  {meal.nutrition.carbs}g
                </Text>
                <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                  carbs
                </Text>
              </View>
              
              <View style={styles.nutritionItem}>
                <Text style={[styles.nutritionValue, { color: colors.text }]}>
                  {meal.nutrition.fat}g
                </Text>
                <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                  fat
                </Text>
              </View>
            </View>
          </View>
        </TouchableOpacity>
        
        <View style={styles.mealActions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: isDark ? '#334155' : '#F1F5F9' }]}
            onPress={() => handleSaveMeal(meal)}
            disabled={saved[meal.id]}
            accessibilityLabel={`${saved[meal.id] ? 'Saved' : 'Save'} this meal`}
            accessibilityRole="button"
          >
            <Feather name="bookmark" size={16} color={saved[meal.id] ? (isDark ? '#60A5FA' : '#3B82F6') : colors.textSecondary} />
            <Text style={[
              styles.actionText,
              saved[meal.id] && { color: isDark ? '#60A5FA' : '#3B82F6' },
              { color: saved[meal.id] ? (isDark ? '#60A5FA' : '#3B82F6') : colors.textSecondary }
            ]}>
              {saved[meal.id] ? 'Saved' : 'Save'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: isDark ? '#334155' : '#F1F5F9' }]}
            onPress={() => handleShareMeal(meal)}
            accessibilityLabel="Share this meal"
            accessibilityRole="button"
          >
            <Feather name="share-2" size={16} color={colors.textSecondary} />
            <Text style={[styles.actionText, { color: colors.textSecondary }]}>
              Share
            </Text>
          </TouchableOpacity>
          
          {onGenerateAlternative && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: isDark ? '#334155' : '#F1F5F9' }]}
              onPress={() => onGenerateAlternative(activeMeal)}
              accessibilityLabel="Generate alternative meal"
              accessibilityRole="button"
            >
              <Feather name="star" size={16} color={colors.textSecondary} />
              <Text style={[styles.actionText, { color: colors.textSecondary }]}>
                Alternative
              </Text>
            </TouchableOpacity>
          )}
        </View>
        
        <View style={[styles.ingredientsContainer, { borderTopColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
          <Text style={[styles.ingredientsTitle, { color: colors.text }]}>
            Ingredients
          </Text>
          <View style={styles.ingredientsList}>
            {meal.ingredients.slice(0, 3).map((ingredient, index) => (
              <View 
                key={`ingredient-${index}`} 
                style={styles.ingredientItem}
              >
                <View style={[styles.ingredientDot, { backgroundColor: getMealTypeColor(meal.mealType) }]} />
                <Text style={[styles.ingredientText, { color: colors.text }]}>
                  {ingredient}
                </Text>
              </View>
            ))}
            
            {meal.ingredients.length > 3 && (
              <TouchableOpacity
                style={styles.viewAllIngredientsButton}
                onPress={() => router.push(`/recipe/${meal.id}`)}
                accessibilityLabel="View all ingredients"
                accessibilityRole="button"
              >
                <Text style={[styles.viewAllText, { color: colors.primary }]}>
                  View all {meal.ingredients.length} ingredients
                </Text>
                <Feather name="chevron-right" size={16} color={colors.primary} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    );
  };

  const renderContent = () => {
    let currentMeal;
    
    if (activeMeal === 'snacks') {
      if (!meals.snacks || meals.snacks.length === 0) {
        return renderMealCard(undefined);
      }
      
      return (
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.snacksContainer}
          contentContainerStyle={styles.snacksContent}
          decelerationRate="fast"
          snapToInterval={width * 0.85 + 20}
          snapToAlignment="center"
        >
          {meals.snacks.map((snack, index) => (
            <View key={`snack-${index}`} style={styles.snackWrapper}>
              {renderMealCard(snack)}
            </View>
          ))}
        </ScrollView>
      );
    } else {
      switch (activeMeal) {
        case 'breakfast':
          currentMeal = meals.breakfast;
          break;
        case 'lunch':
          currentMeal = meals.lunch;
          break;
        case 'dinner':
          currentMeal = meals.dinner;
          break;
      }
      
      return renderMealCard(currentMeal);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Text style={[styles.title, { color: colors.text }]}>
            Your Meal Plan
          </Text>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={[styles.headerButton, { backgroundColor: isDark ? '#334155' : '#F1F5F9' }]}
              onPress={handleShareMealPlan}
              accessibilityLabel="Share meal plan"
              accessibilityRole="button"
            >
              <Feather name="share-2" size={16} color={colors.primary} style={styles.headerButtonIcon} />
              <Text style={[styles.headerButtonText, { color: colors.primary }]}>
                Share
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.groceryButton, { backgroundColor: isDark ? '#334155' : '#F1F5F9' }]}
              onPress={() => setIsGroceryModalVisible(true)}
              accessibilityLabel="Create grocery list"
              accessibilityRole="button"
            >
              <Feather name="shopping-cart" size={16} color={colors.primary} style={styles.groceryIcon} />
              <Text style={[styles.groceryText, { color: colors.primary }]}>
                Grocery List
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        <Text style={[styles.date, { color: colors.textSecondary }]}>
          {formatDate(date)}
        </Text>
      </View>
      
      <View style={styles.tabContainer}>
        {renderMealTab('breakfast', 'Breakfast')}
        {renderMealTab('lunch', 'Lunch')}
        {renderMealTab('dinner', 'Dinner')}
        {renderMealTab('snacks', 'Snacks')}
      </View>
      
      {renderContent()}

      <MealPlanGroceryModal 
        isVisible={isGroceryModalVisible} 
        onClose={() => setIsGroceryModalVisible(false)} 
        mealPlan={{
          title: `Meal Plan for ${formatDate(date)}`,
          meals: [
            meals.breakfast,
            meals.lunch,
            meals.dinner,
            ...(meals.snacks || [])
          ].filter(Boolean)
        }} 
      />
      
      <SocialSharingModal
        isVisible={isSharingModalVisible}
        onClose={() => setIsSharingModalVisible(false)}
        contentType={mealToShare ? 'recipe' : 'meal-plan'}
        content={mealToShare || {
          title: `Meal Plan for ${formatDate(date)}`,
          meals: [
            meals.breakfast,
            meals.lunch,
            meals.dinner,
            ...(meals.snacks || [])
          ].filter(Boolean),
          imageUrl: meals.breakfast?.imageUrl || meals.lunch?.imageUrl || meals.dinner?.imageUrl
        }}
        appName="HealthApp" 
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  header: {
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  headerButtonIcon: {
    marginRight: 4,
  },
  headerButtonText: {
    fontSize: 13,
    fontWeight: '500',
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
  },
  date: {
    fontSize: 14,
  },
  groceryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  groceryIcon: {
    marginRight: 4,
  },
  groceryText: {
    fontSize: 13,
    fontWeight: '500',
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  mealTab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeMealTab: {
    borderBottomWidth: 2,
  },
  mealTabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  mealCardContainer: {
    marginHorizontal: 16,
    borderRadius: 16,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
      web: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  mealCard: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  mealImage: {
    width: '100%',
    height: 180,
    resizeMode: 'cover',
  },
  mealImagePlaceholder: {
    width: '100%',
    height: 180,
  },
  mealInfo: {
    padding: 16,
  },
  mealHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  mealType: {
    fontSize: 14,
    fontWeight: '700',
  },
  prepTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  clockIcon: {
    marginRight: 4,
  },
  prepTime: {
    fontSize: 14,
  },
  mealName: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 8,
  },
  mealDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  nutritionInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    alignItems: 'center',
  },
  nutritionValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  nutritionLabel: {
    fontSize: 12,
    marginTop: 2,
  },
  mealActions: {
    flexDirection: 'row',
    padding: 16,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    justifyContent: 'space-around',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    minWidth: 90,
    justifyContent: 'center',
  },
  actionText: {
    fontSize: 14,
    marginLeft: 6,
  },
  ingredientsContainer: {
    borderTopWidth: 1,
    padding: 16,
  },
  ingredientsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  ingredientsList: {
  },
  ingredientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  ingredientDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  ingredientText: {
    fontSize: 14,
  },
  viewAllIngredientsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  viewAllText: {
    fontSize: 14,
    marginRight: 4,
  },
  snacksContainer: {
    marginBottom: 16,
  },
  snacksContent: {
    paddingHorizontal: 8,
  },
  snackWrapper: {
    width: '85%',
    marginHorizontal: 10,
  },
  noMealContainer: {
    marginHorizontal: 16,
    borderRadius: 16,
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noMealText: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  generateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
  },
  generateIcon: {
    marginRight: 8,
  },
  generateText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default MealPlannerCard; 