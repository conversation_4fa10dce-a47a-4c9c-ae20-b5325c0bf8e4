import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Pressable, Animated, ScrollView, Dimensions, useWindowDimensions } from 'react-native';
import { PanGestureHandler } from 'react-native-gesture-handler';
import Svg, { G, Circle, Path, Line, Text as SvgText } from 'react-native-svg';
import { useTheme } from '@/contexts/ThemeContext';
import { NutritionInfo } from '@/types/api-responses';
import { useAccessibility } from '@/components/AccessibilityProvider';
import { Feather } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Tooltip from '../ui/Tooltip';

interface NutritionGoals {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber?: number;
}

interface NutritionDataPoint {
  date: string;
  nutrition: NutritionInfo;
}

interface NutritionInteractiveChartProps {
  title: string;
  subtitle?: string;
  nutritionData: NutritionInfo;
  dailyGoals: NutritionGoals;
  historicalData?: NutritionDataPoint[];
  onInfoPress?: () => void;
  onMorePress?: () => void;
  showControls?: boolean;
}

const NutritionInteractiveChart: React.FC<NutritionInteractiveChartProps> = ({
  title,
  subtitle,
  nutritionData,
  dailyGoals,
  historicalData,
  onInfoPress,
  onMorePress,
  showControls = true
}) => {
  const { colors, isDark } = useTheme();
  const { isReduceMotionEnabled } = useAccessibility();
  const { width } = useWindowDimensions();
  const [selectedMacro, setSelectedMacro] = useState<'calories' | 'protein' | 'carbs' | 'fat' | 'fiber'>('calories');
  const [tooltipVisible, setTooltipVisible] = useState(false);
  const [tooltipText, setTooltipText] = useState('');
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [animationCompleted, setAnimationCompleted] = useState(false);
  const animatedValues = {
    calories: new Animated.Value(0),
    protein: new Animated.Value(0),
    carbs: new Animated.Value(0),
    fat: new Animated.Value(0),
    fiber: new Animated.Value(0)
  };

  // Calculate percentages
  const percentages = {
    calories: Math.min((nutritionData.calories / dailyGoals.calories) * 100, 100),
    protein: Math.min((nutritionData.protein / dailyGoals.protein) * 100, 100),
    carbs: Math.min((nutritionData.carbs / dailyGoals.carbs) * 100, 100),
    fat: Math.min((nutritionData.fat / dailyGoals.fat) * 100, 100),
    fiber: nutritionData.fiber && dailyGoals.fiber 
      ? Math.min((nutritionData.fiber / dailyGoals.fiber) * 100, 100) 
      : 0
  };

  // Animated progress rings
  useEffect(() => {
    if (isReduceMotionEnabled) {
      // Set values immediately if reduced motion is enabled
      Object.keys(animatedValues).forEach(key => {
        animatedValues[key as keyof typeof animatedValues].setValue(
          percentages[key as keyof typeof percentages]
        );
      });
      setAnimationCompleted(true);
      return;
    }

    // Staggered animation
    const animations = Object.keys(animatedValues).map((key, index) => {
      return Animated.timing(animatedValues[key as keyof typeof animatedValues], {
        toValue: percentages[key as keyof typeof percentages],
        duration: 1000,
        delay: index * 150,
        useNativeDriver: false
      });
    });

    Animated.parallel(animations).start(() => {
      setAnimationCompleted(true);
    });
  }, [nutritionData, dailyGoals, isReduceMotionEnabled]);

  // Calculate color based on percentage
  const getColorForPercentage = (percentage: number) => {
    if (percentage <= 33) return isDark ? '#4ADE80' : '#22C55E';
    if (percentage <= 66) return isDark ? '#FACC15' : '#EAB308';
    return isDark ? '#F87171' : '#EF4444';
  };

  const renderProgressRing = (
    type: 'calories' | 'protein' | 'carbs' | 'fat' | 'fiber',
    value: number,
    goal: number,
    color: string,
    index: number
  ) => {
    const percentage = Math.min((value / goal) * 100, 100);
    const radius = 40;
    const strokeWidth = 8;
    const isSelected = selectedMacro === type;
    
    // Animation interpolated value
    const animatedPercentage = animatedValues[type].interpolate({
      inputRange: [0, 100],
      outputRange: [0, 2 * Math.PI * radius]
    });

    // SVG positioning
    const center = radius + strokeWidth;
    const circumference = 2 * Math.PI * radius;
    
    // Make gaps between segments
    const adjustedRadius = isSelected ? radius + 5 : radius;
    
    // Calculate angle for nutrition type label
    const angleInRadians = (index / 5) * 2 * Math.PI;
    const labelX = center + (radius + 25) * Math.cos(angleInRadians - Math.PI / 2);
    const labelY = center + (radius + 25) * Math.sin(angleInRadians - Math.PI / 2);

    return (
      <Pressable 
        onPress={() => setSelectedMacro(type)}
        style={styles.ringContainer}
        accessibilityLabel={`${type} progress: ${percentage.toFixed(0)}% of goal`}
        accessibilityRole="button"
      >
        <Svg width={center * 2} height={center * 2} viewBox={`0 0 ${center * 2} ${center * 2}`}>
          {/* Background circle */}
          <Circle
            cx={center}
            cy={center}
            r={radius}
            stroke={isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}
            strokeWidth={strokeWidth}
            fill="transparent"
          />
          
          {/* Progress circle */}
          <AnimatedCircle
            cx={center}
            cy={center}
            r={radius}
            stroke={color}
            strokeWidth={isSelected ? strokeWidth + 2 : strokeWidth}
            strokeDasharray={[circumference, circumference]}
            strokeDashoffset={animatedPercentage}
            strokeLinecap="round"
            fill="transparent"
            transform={`rotate(-90, ${center}, ${center})`}
          />
          
          {/* Center value */}
          <SvgText
            x={center}
            y={center + 5}
            fontSize={14}
            fontWeight="bold"
            fill={isSelected ? color : isDark ? '#FFFFFF' : '#000000'}
            textAnchor="middle"
          >
            {percentage.toFixed(0)}%
          </SvgText>
        </Svg>
        
        <View style={[styles.ringLabel, isSelected && { borderBottomColor: color, borderBottomWidth: 2 }]}>
          <Text style={[styles.ringLabelText, { color: isSelected ? color : colors.text }]}>
            {type.charAt(0).toUpperCase() + type.slice(1)}
          </Text>
          <View style={styles.ringValues}>
            <Text style={[styles.currentValue, { color: isSelected ? color : colors.text }]}>
              {value}{type !== 'calories' && 'g'}
            </Text>
            <Text style={[styles.goalValue, { color: colors.textSecondary }]}>
              /{goal}{type !== 'calories' && 'g'}
            </Text>
          </View>
        </View>
      </Pressable>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: isDark ? '#1E293B' : '#F8FAFC' }]}>
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
          {subtitle && (
            <Text style={[styles.subtitle, { color: colors.textSecondary }]}>{subtitle}</Text>
          )}
        </View>
        
        {showControls && (
          <View style={styles.headerControls}>
            {onInfoPress && (
              <Pressable 
                onPress={onInfoPress}
                style={({ pressed }) => [
                  styles.iconButton,
                  { backgroundColor: pressed ? (isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)') : 'transparent' }
                ]}
                accessibilityLabel="Information"
                accessibilityRole="button"
              >
                <Feather name="help-circle" size={20} color={colors.textSecondary} />
              </Pressable>
            )}
            
            {onMorePress && (
              <Pressable 
                onPress={onMorePress}
                style={({ pressed }) => [
                  styles.iconButton,
                  { backgroundColor: pressed ? (isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)') : 'transparent' }
                ]}
                accessibilityLabel="More options"
                accessibilityRole="button"
              >
                <Feather name="more-horizontal" size={20} color={colors.textSecondary} />
              </Pressable>
            )}
          </View>
        )}
      </View>
      
      {/* Interactive Ring Charts */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.chartsScrollView}
        contentContainerStyle={styles.chartsContainer}
      >
        {renderProgressRing(
          'calories', 
          nutritionData.calories, 
          dailyGoals.calories, 
          getColorForPercentage(percentages.calories),
          0
        )}
        
        {renderProgressRing(
          'protein', 
          nutritionData.protein, 
          dailyGoals.protein, 
          getColorForPercentage(percentages.protein),
          1
        )}
        
        {renderProgressRing(
          'carbs', 
          nutritionData.carbs, 
          dailyGoals.carbs, 
          getColorForPercentage(percentages.carbs),
          2
        )}
        
        {renderProgressRing(
          'fat', 
          nutritionData.fat, 
          dailyGoals.fat, 
          getColorForPercentage(percentages.fat),
          3
        )}
        
        {nutritionData.fiber && dailyGoals.fiber && renderProgressRing(
          'fiber', 
          nutritionData.fiber, 
          dailyGoals.fiber, 
          getColorForPercentage(percentages.fiber),
          4
        )}
      </ScrollView>
      
      {/* Selected Nutrition Details */}
      <View style={[styles.detailsContainer, { backgroundColor: isDark ? 'rgba(0,0,0,0.2)' : 'rgba(0,0,0,0.05)' }]}>
        <Text style={[styles.detailsTitle, { color: colors.text }]}>
          {selectedMacro.charAt(0).toUpperCase() + selectedMacro.slice(1)} Details
        </Text>
        
        <View style={styles.detailsContent}>
          <View style={styles.detailsRow}>
            <Text style={[styles.detailsLabel, { color: colors.textSecondary }]}>Current:</Text>
            <Text style={[styles.detailsValue, { color: colors.text }]}>
              {nutritionData[selectedMacro as keyof NutritionInfo] || 0}
              {selectedMacro !== 'calories' && 'g'}
            </Text>
          </View>
          
          <View style={styles.detailsRow}>
            <Text style={[styles.detailsLabel, { color: colors.textSecondary }]}>Goal:</Text>
            <Text style={[styles.detailsValue, { color: colors.text }]}>
              {dailyGoals[selectedMacro as keyof NutritionGoals] || 0}
              {selectedMacro !== 'calories' && 'g'}
            </Text>
          </View>
          
          <View style={styles.detailsRow}>
            <Text style={[styles.detailsLabel, { color: colors.textSecondary }]}>Progress:</Text>
            <Text 
              style={[
                styles.detailsValue, 
                { color: getColorForPercentage(percentages[selectedMacro as keyof typeof percentages]) }
              ]}
            >
              {percentages[selectedMacro as keyof typeof percentages].toFixed(1)}%
            </Text>
          </View>
        </View>
        
        {/* Nutritional Tip */}
        <View style={[styles.tipContainer, { borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)' }]}>
          <Text style={[styles.tipTitle, { color: colors.primary }]}>Nutrition Tip</Text>
          <Text style={[styles.tipContent, { color: colors.text }]}>
            {selectedMacro === 'calories' && "Calories are your body's fuel. Balance intake with activity for optimal health."}
            {selectedMacro === 'protein' && "Protein is essential for muscle repair and growth. Distribute intake throughout the day."}
            {selectedMacro === 'carbs' && "Focus on complex carbs like whole grains for sustained energy release."}
            {selectedMacro === 'fat' && "Healthy fats support hormone function. Prioritize unsaturated sources."}
            {selectedMacro === 'fiber' && "Fiber aids digestion and helps maintain stable blood sugar levels."}
          </Text>
        </View>
      </View>
      
      {/* Tooltip */}
      {tooltipVisible && (
        <Tooltip
          text={tooltipText}
          position={tooltipPosition}
          onClose={() => setTooltipVisible(false)}
        />
      )}
    </View>
  );
};

// Create animated SVG circle component
const AnimatedCircle = Animated.createAnimatedComponent(Circle);

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 16,
    margin: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  headerControls: {
    flexDirection: 'row',
  },
  iconButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  chartsScrollView: {
    marginBottom: 16,
  },
  chartsContainer: {
    paddingHorizontal: 8,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  ringContainer: {
    alignItems: 'center',
    marginHorizontal: 12,
  },
  ringLabel: {
    alignItems: 'center',
    paddingTop: 8,
    paddingBottom: 2,
    marginTop: 4,
  },
  ringLabelText: {
    fontSize: 14,
    fontWeight: '500',
  },
  ringValues: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginTop: 2,
  },
  currentValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  goalValue: {
    fontSize: 12,
  },
  detailsContainer: {
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
  },
  detailsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  detailsContent: {
    marginBottom: 16,
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 6,
  },
  detailsLabel: {
    fontSize: 14,
  },
  detailsValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  tipContainer: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
  },
  tipTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 6,
  },
  tipContent: {
    fontSize: 13,
    lineHeight: 18,
  },
});

export default NutritionInteractiveChart; 