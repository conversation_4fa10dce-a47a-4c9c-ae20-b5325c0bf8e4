import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  Image, 
  Animated,
  useWindowDimensions,
  ImageBackground
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';

interface NutritionLessonItem {
  id: string;
  title: string;
  subtitle: string;
  imageUrl?: string;
  duration: string; // e.g., "3 min read"
  content: string[];
  quiz?: {
    question: string;
    options: string[];
    correctAnswerIndex: number;
    explanation: string;
  }[];
  completed?: boolean;
}

interface NutritionLessonsCardProps {
  title?: string;
  lessons: NutritionLessonItem[];
  onLessonComplete?: (lessonId: string) => void;
}

const NutritionLessonsCard: React.FC<NutritionLessonsCardProps> = ({
  title = "Nutrition Education",
  lessons,
  onLessonComplete
}) => {
  const { colors, isDark } = useTheme();
  const { width } = useWindowDimensions();
  const [expandedLessonId, setExpandedLessonId] = useState<string | null>(null);
  const [selectedAnswers, setSelectedAnswers] = useState<{[key: string]: number}>({});
  const [quizSubmitted, setQuizSubmitted] = useState<{[key: string]: boolean}>({});
  const [lessonProgress, setLessonProgress] = useState<{[key: string]: number}>({});

  const handleLessonPress = (lesson: NutritionLessonItem) => {
    if (expandedLessonId === lesson.id) {
      setExpandedLessonId(null);
    } else {
      setExpandedLessonId(lesson.id);
      
      // Initialize progress when opening a lesson for the first time
      if (!lessonProgress[lesson.id]) {
        setLessonProgress(prev => ({
          ...prev,
          [lesson.id]: 0
        }));
      }
    }
  };

  const selectAnswer = (lessonId: string, quizIndex: number, answerIndex: number) => {
    if (quizSubmitted[`${lessonId}-${quizIndex}`]) return;
    
    setSelectedAnswers(prev => ({
      ...prev,
      [`${lessonId}-${quizIndex}`]: answerIndex
    }));
  };

  const submitQuizAnswer = (lesson: NutritionLessonItem, quizIndex: number) => {
    const quizKey = `${lesson.id}-${quizIndex}`;
    setQuizSubmitted(prev => ({
      ...prev,
      [quizKey]: true
    }));
    
    // Update progress
    if (lesson.quiz && lesson.content) {
      const totalSteps = lesson.content.length + lesson.quiz.length;
      const currentSteps = Object.keys(quizSubmitted).filter(key => key.startsWith(lesson.id)).length + 1;
      const newProgress = Math.min(Math.round((currentSteps / totalSteps) * 100), 100);
      
      setLessonProgress(prev => ({
        ...prev,
        [lesson.id]: newProgress
      }));
      
      // If all quizzes are completed, mark lesson as complete
      if (newProgress === 100 && onLessonComplete) {
        onLessonComplete(lesson.id);
      }
    }
  };

  const isAnswerCorrect = (lessonId: string, quizIndex: number, quiz: {
    question: string;
    options: string[];
    correctAnswerIndex: number;
    explanation: string;
  }) => {
    const quizKey = `${lessonId}-${quizIndex}`;
    const selectedAnswer = selectedAnswers[quizKey];
    return quizSubmitted[quizKey] && selectedAnswer === quiz.correctAnswerIndex;
  };

  const renderLessonCard = (lesson: NutritionLessonItem) => {
    const isExpanded = expandedLessonId === lesson.id;
    const progress = lessonProgress[lesson.id] || 0;
    const progressColor = progress === 100 ? '#10B981' : colors.primary;

    return (
      <View 
        key={lesson.id} 
        style={[
          styles.lessonCard, 
          { backgroundColor: isDark ? '#1E293B' : '#FFFFFF' }
        ]}
      >
        <TouchableOpacity
          style={styles.lessonHeader}
          onPress={() => handleLessonPress(lesson)}
          accessibilityRole="button"
          accessibilityLabel={`${lesson.title} lesson, ${progress}% completed`}
          accessibilityHint="Tap to expand or collapse this lesson"
        >
          <View style={styles.lessonHeaderLeft}>
            <Book 
              size={18} 
              color={isDark ? colors.primary : '#6366F1'} 
              style={styles.lessonIcon} 
            />
            <View>
              <Text style={[styles.lessonTitle, { color: colors.text }]}>
                {lesson.title}
              </Text>
              <Text style={[styles.lessonSubtitle, { color: colors.textSecondary }]}>
                {lesson.subtitle} • {lesson.duration}
              </Text>
            </View>
          </View>
          
          <View style={styles.lessonHeaderRight}>
            {progress > 0 && (
              <View style={styles.progressContainer}>
                <View 
                  style={[
                    styles.progressBar, 
                    { 
                      backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                      width: 50 
                    }
                  ]}
                >
                  <View 
                    style={[
                      styles.progressFill, 
                      { 
                        backgroundColor: progressColor,
                        width: `${progress}%` 
                      }
                    ]} 
                  />
                </View>
                <Text style={[styles.progressText, { color: colors.textSecondary }]}>{progress}%</Text>
              </View>
            )}
            
            {isExpanded ? (
              <Feather name="chevron-left" size={20} color={colors.textSecondary} />
            ) : (
              <Feather name="chevron-right" size={20} color={colors.textSecondary} />
            )}
          </View>
        </TouchableOpacity>
        
        {isExpanded && (
          <View style={styles.lessonContent}>
            {lesson.imageUrl && (
              <Image 
                source={{ uri: lesson.imageUrl }} 
                style={styles.lessonImage}
                accessibilityLabel={`Illustration for ${lesson.title}`}
              />
            )}
            
            <View style={styles.lessonContentText}>
              {lesson.content.map((paragraph, index) => (
                <Text 
                  key={`paragraph-${index}`} 
                  style={[styles.paragraph, { color: colors.text }]}
                >
                  {paragraph}
                </Text>
              ))}
            </View>
            
            {lesson.quiz && lesson.quiz.length > 0 && (
              <View style={styles.quizSection}>
                <Text style={[styles.quizTitle, { color: colors.text }]}>
                  Knowledge Check
                </Text>
                
                {lesson.quiz.map((quiz, quizIndex) => {
                  const quizKey = `${lesson.id}-${quizIndex}`;
                  const selectedAnswer = selectedAnswers[quizKey];
                  const isSubmitted = quizSubmitted[quizKey];
                  const correct = isAnswerCorrect(lesson.id, quizIndex, quiz);
                  
                  return (
                    <View 
                      key={`quiz-${quizIndex}`} 
                      style={[
                        styles.quizContainer, 
                        { backgroundColor: isDark ? 'rgba(0,0,0,0.2)' : 'rgba(0,0,0,0.05)' }
                      ]}
                    >
                      <Text style={[styles.quizQuestion, { color: colors.text }]}>
                        {quiz.question}
                      </Text>
                      
                      <View style={styles.optionsContainer}>
                        {quiz.options.map((option, optionIndex) => (
                          <TouchableOpacity
                            key={`option-${optionIndex}`}
                            style={[
                              styles.optionButton,
                              selectedAnswer === optionIndex && { 
                                backgroundColor: isSubmitted 
                                  ? (optionIndex === quiz.correctAnswerIndex 
                                    ? isDark ? 'rgba(16, 185, 129, 0.2)' : 'rgba(16, 185, 129, 0.1)' 
                                    : isDark ? 'rgba(239, 68, 68, 0.2)' : 'rgba(239, 68, 68, 0.1)')
                                  : isDark ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)'
                              },
                              isSubmitted && optionIndex === quiz.correctAnswerIndex && {
                                backgroundColor: isDark ? 'rgba(16, 185, 129, 0.2)' : 'rgba(16, 185, 129, 0.1)',
                                borderColor: '#10B981'
                              },
                              { borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)' }
                            ]}
                            onPress={() => selectAnswer(lesson.id, quizIndex, optionIndex)}
                            disabled={isSubmitted}
                            accessibilityRole="radio"
                            accessibilityState={{ selected: selectedAnswer === optionIndex }}
                            accessibilityLabel={option}
                          >
                            <Text 
                              style={[
                                styles.optionText, 
                                { color: colors.text },
                                isSubmitted && optionIndex === quiz.correctAnswerIndex && { color: '#10B981' }
                              ]}
                            >
                              {option}
                            </Text>
                            
                            {isSubmitted && selectedAnswer === optionIndex && (
                              <View style={styles.answerIndicator}>
                                {optionIndex === quiz.correctAnswerIndex ? (
                                  <Feather name="check" size={16}  color={colors.text} />
                                ) : (
                                  <Feather name="x-circle" size={16}  color={colors.text} />
                                )}
                              </View>
                            )}
                          </TouchableOpacity>
                        ))}
                      </View>
                      
                      {!isSubmitted && (
                        <TouchableOpacity
                          style={[
                            styles.submitButton,
                            { 
                              backgroundColor: selectedAnswer !== undefined ? colors.primary : isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                              opacity: selectedAnswer !== undefined ? 1 : 0.5
                            }
                          ]}
                          onPress={() => submitQuizAnswer(lesson, quizIndex)}
                          disabled={selectedAnswer === undefined}
                          accessibilityLabel="Submit answer"
                          accessibilityRole="button"
                        >
                          <Text style={[styles.submitButtonText, { color: '#FFFFFF' }]}>
                            Submit Answer
                          </Text>
                        </TouchableOpacity>
                      )}
                      
                      {isSubmitted && (
                        <View 
                          style={[
                            styles.explanationContainer,
                            { 
                              backgroundColor: correct 
                                ? isDark ? 'rgba(16, 185, 129, 0.1)' : 'rgba(16, 185, 129, 0.05)' 
                                : isDark ? 'rgba(239, 68, 68, 0.1)' : 'rgba(239, 68, 68, 0.05)',
                              borderColor: correct ? '#10B981' : '#EF4444' 
                            }
                          ]}
                        >
                          <Text style={[styles.explanationTitle, { color: correct ? '#10B981' : '#EF4444' }]}>
                            {correct ? 'Correct!' : 'Incorrect'}
                          </Text>
                          <Text style={[styles.explanationText, { color: colors.text }]}>
                            {quiz.explanation}
                          </Text>
                        </View>
                      )}
                    </View>
                  );
                })}
              </View>
            )}
            
            {progress === 100 && (
              <View style={styles.completionContainer}>
                <LinearGradient
                  colors={isDark ? ['#059669', '#10B981'] : ['#10B981', '#34D399']}
                  style={styles.completionBadge}
                >
                  <Feather name="award" size={24}  color={colors.text} />
                </LinearGradient>
                <Text style={[styles.completionText, { color: colors.text }]}>
                  Congratulations! You've completed this lesson.
                </Text>
              </View>
            )}
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <View style={styles.headerLeft}>
          <Book size={20} color={colors.primary} style={styles.headerIcon} />
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {title}
          </Text>
        </View>
        
        <TouchableOpacity
          style={[styles.viewAllButton, { backgroundColor: colors.primary + '15' }]}
          onPress={() => router.push('/dietitian')}
          accessibilityLabel="View all nutrition lessons"
          accessibilityRole="button"
        >
          <Text style={[styles.viewAllText, { color: colors.primary }]}>
            View All
          </Text>
          <Feather name="chevron-right" size={16} color={colors.primary} />
        </TouchableOpacity>
      </View>
      
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
      >
        {lessons.length > 0 ? (
          lessons.map(renderLessonCard)
        ) : (
          <View style={[styles.emptyState, { backgroundColor: isDark ? '#1E293B' : '#FFFFFF' }]}>
            <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
              No nutrition lessons available right now. Check back soon!
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  scrollView: {
    maxHeight: 600,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  lessonCard: {
    borderRadius: 12,
    marginBottom: 12,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  lessonHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  lessonHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  lessonIcon: {
    marginRight: 12,
  },
  lessonTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  lessonSubtitle: {
    fontSize: 13,
  },
  lessonHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    marginRight: 6,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
  },
  lessonContent: {
    padding: 16,
    paddingTop: 0,
  },
  lessonImage: {
    width: '100%',
    height: 160,
    borderRadius: 8,
    marginBottom: 16,
  },
  lessonContentText: {
    marginBottom: 16,
  },
  paragraph: {
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 12,
  },
  quizSection: {
    marginTop: 8,
  },
  quizTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  quizContainer: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  quizQuestion: {
    fontSize: 15,
    fontWeight: '500',
    marginBottom: 16,
  },
  optionsContainer: {
    marginBottom: 16,
  },
  optionButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 8,
  },
  optionText: {
    fontSize: 14,
    flex: 1,
  },
  answerIndicator: {
    marginLeft: 8,
  },
  submitButton: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  explanationContainer: {
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginTop: 12,
  },
  explanationTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  explanationText: {
    fontSize: 14,
    lineHeight: 20,
  },
  completionContainer: {
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 8,
  },
  completionBadge: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  completionText: {
    fontSize: 15,
    fontWeight: '500',
    textAlign: 'center',
  },
  emptyState: {
    borderRadius: 12,
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyStateText: {
    fontSize: 15,
    textAlign: 'center',
  },
});

export default NutritionLessonsCard; 