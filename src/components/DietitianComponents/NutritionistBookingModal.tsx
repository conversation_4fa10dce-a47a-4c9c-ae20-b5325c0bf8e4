import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  Image,
  Platform
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { BlurView } from 'expo-blur';
import { Feather } from '@expo/vector-icons';
import { format, addDays, isSameDay } from 'date-fns';

interface TimeSlot {
  time: string;
  isAvailable: boolean;
}

interface AvailabilityDay {
  date: Date;
  slots: TimeSlot[];
}

interface Nutritionist {
  id: string;
  name: string;
  avatar: string;
  specialties: string[];
  rating: number;
  reviewCount: number;
  experience: string;
  price: string;
  availability: {
    date: string;
    slots: {
      time: string;
      isAvailable: boolean;
    }[];
  }[];
  consultationTypes: ('video' | 'chat')[];
  bio: string;
}

interface NutritionistBookingModalProps {
  isVisible: boolean;
  onClose: () => void;
  nutritionist: Nutritionist | null;
  onBookConsultation: (
    nutritionist: Nutritionist, 
    date: Date, 
    time: string, 
    consultationType: 'video' | 'chat'
  ) => Promise<void>;
}

const NutritionistBookingModal: React.FC<NutritionistBookingModalProps> = ({
  isVisible,
  onClose,
  nutritionist,
  onBookConsultation
}) => {
  const { colors, isDark } = useTheme();
  const [currentStep, setCurrentStep] = useState<'details' | 'schedule' | 'confirm'>('details');
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  const [selectedConsultationType, setSelectedConsultationType] = useState<'video' | 'chat'>('video');
  const [isLoading, setIsLoading] = useState(false);
  
  // Generate dates for the next 14 days
  const generateAvailabilityDays = (): AvailabilityDay[] => {
    if (!nutritionist) return [];
    
    const days: AvailabilityDay[] = [];
    const today = new Date();
    
    // Convert string dates from nutritionist data to Date objects
    const availabilityMap = new Map(
      nutritionist.availability.map(a => {
        const [year, month, day] = a.date.split('-').map(Number);
        return [
          format(new Date(year, month - 1, day), 'yyyy-MM-dd'),
          a.slots
        ];
      })
    );
    
    // Generate availability for the next 14 days
    for (let i = 0; i < 14; i++) {
      const date = addDays(today, i);
      const dateString = format(date, 'yyyy-MM-dd');
      
      // Use actual data if available, otherwise generate some random slots
      const slots = availabilityMap.get(dateString) || [
        { time: '09:00 AM', isAvailable: Math.random() > 0.3 },
        { time: '10:00 AM', isAvailable: Math.random() > 0.3 },
        { time: '11:00 AM', isAvailable: Math.random() > 0.3 },
        { time: '01:00 PM', isAvailable: Math.random() > 0.3 },
        { time: '02:00 PM', isAvailable: Math.random() > 0.3 },
        { time: '03:00 PM', isAvailable: Math.random() > 0.3 },
        { time: '04:00 PM', isAvailable: Math.random() > 0.3 }
      ];
      
      days.push({
        date,
        slots
      });
    }
    
    return days;
  };
  
  const availabilityDays = generateAvailabilityDays();
  
  const handleSelectDate = (date: Date) => {
    setSelectedDate(date);
    setSelectedTime(null);
  };
  
  const handleSelectTime = (time: string) => {
    setSelectedTime(time);
  };
  
  const handleSelectConsultationType = (type: 'video' | 'chat') => {
    setSelectedConsultationType(type);
  };
  
  const handleContinue = () => {
    if (currentStep === 'details') {
      setCurrentStep('schedule');
    } else if (currentStep === 'schedule' && selectedDate && selectedTime) {
      setCurrentStep('confirm');
    }
  };
  
  const handleBack = () => {
    if (currentStep === 'schedule') {
      setCurrentStep('details');
    } else if (currentStep === 'confirm') {
      setCurrentStep('schedule');
    }
  };
  
  const handleBookConsultation = async () => {
    if (!nutritionist || !selectedDate || !selectedTime) return;
    
    try {
      setIsLoading(true);
      await onBookConsultation(
        nutritionist,
        selectedDate,
        selectedTime,
        selectedConsultationType
      );
      setIsLoading(false);
      onClose();
    } catch (error) {
      setIsLoading(false);
      console.error('Booking error:', error);
    }
  };
  
  const resetModal = () => {
    setCurrentStep('details');
    setSelectedDate(null);
    setSelectedTime(null);
  };
  
  const handleCloseModal = () => {
    onClose();
    setTimeout(resetModal, 300); // Reset after animation completes
  };
  
  const renderSelectedDateInfo = () => {
    if (!selectedDate) return null;
    
    return (
      <View style={[styles.selectedDateContainer, { backgroundColor: colors.primary + '15' }]}>
        <Feather name="calendar" size={16} color={colors.primary} style={styles.selectedDateIcon} />
        <Text style={[styles.selectedDateText, { color: colors.primary }]}>
          {format(selectedDate, 'EEEE, MMMM d, yyyy')}
        </Text>
      </View>
    );
  };
  
  const renderDetailsStep = () => {
    if (!nutritionist) return null;
    
    return (
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={styles.nutritionistHeader}>
          <Image 
            source={{ uri: nutritionist.avatar }} 
            style={styles.avatar}
            accessibilityLabel={`Photo of ${nutritionist.name}`}
          />
          
          <View style={styles.headerInfo}>
            <Text style={[styles.nutritionistName, { color: colors.text }]}>
              {nutritionist.name}
            </Text>
            
            <View style={styles.ratingContainer}>
              <Feather name="star" size={16} style={styles.ratingIcon} />
              <Text style={[styles.ratingText, { color: colors.text }]}>
                {nutritionist.rating} 
              </Text>
              <Text style={[styles.reviewCount, { color: colors.textSecondary }]}>
                ({nutritionist.reviewCount} reviews)
              </Text>
            </View>
            
            <Text style={[styles.experienceText, { color: colors.textSecondary }]}>
              {nutritionist.experience} experience
            </Text>
          </View>
        </View>
        
        <View style={styles.specialtiesContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Specialties
          </Text>
          
          <View style={styles.specialtiesList}>
            {nutritionist.specialties.map((specialty, index) => (
              <View 
                key={index} 
                style={[
                  styles.specialtyBadge, 
                  { backgroundColor: isDark ? '#334155' : '#F1F5F9' }
                ]}
              >
                <Text style={[styles.specialtyText, { color: colors.text }]}>
                  {specialty}
                </Text>
              </View>
            ))}
          </View>
        </View>
        
        <View style={styles.bioContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            About
          </Text>
          
          <Text style={[styles.bioText, { color: colors.textSecondary }]}>
            {nutritionist.bio}
          </Text>
        </View>
        
        <View style={styles.consultationTypesContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Consultation Type
          </Text>
          
          <View style={styles.typeOptions}>
            {nutritionist.consultationTypes.includes('video') && (
              <TouchableOpacity
                style={[
                  styles.typeOption,
                  selectedConsultationType === 'video' && [
                    styles.selectedType,
                    { borderColor: colors.primary }
                  ],
                  { backgroundColor: isDark ? '#1E293B' : '#FFFFFF' }
                ]}
                onPress={() => handleSelectConsultationType('video')}
                disabled={!nutritionist.consultationTypes.includes('video')}
                accessibilityRole="radio"
                accessibilityState={{ checked: selectedConsultationType === 'video' }}
                accessibilityLabel="Video consultation"
              >
                <Feather name="video" size={24} color={selectedConsultationType === 'video' ? colors.primary : colors.textSecondary} style={styles.typeIcon} />
                <Text 
                  style={[
                    styles.typeText, 
                    { color: selectedConsultationType === 'video' ? colors.primary : colors.text }
                  ]}
                >
                  Video Call
                </Text>
              </TouchableOpacity>
            )}
            
            {nutritionist.consultationTypes.includes('chat') && (
              <TouchableOpacity
                style={[
                  styles.typeOption,
                  selectedConsultationType === 'chat' && [
                    styles.selectedType,
                    { borderColor: colors.primary }
                  ],
                  { backgroundColor: isDark ? '#1E293B' : '#FFFFFF' }
                ]}
                onPress={() => handleSelectConsultationType('chat')}
                disabled={!nutritionist.consultationTypes.includes('chat')}
                accessibilityRole="radio"
                accessibilityState={{ checked: selectedConsultationType === 'chat' }}
                accessibilityLabel="Chat consultation"
              >
                <MessageCircle 
                  size={24} 
                  color={selectedConsultationType === 'chat' ? colors.primary : colors.textSecondary} 
                  style={styles.typeIcon} 
                />
                <Text 
                  style={[
                    styles.typeText, 
                    { color: selectedConsultationType === 'chat' ? colors.primary : colors.text }
                  ]}
                >
                  Chat Session
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
        
        <View style={styles.pricingContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Pricing
          </Text>
          
          <View 
            style={[
              styles.pricingCard, 
              { backgroundColor: isDark ? '#334155' : '#F1F5F9' }
            ]}
          >
            <Text style={[styles.pricingTitle, { color: colors.text }]}>
              30-Minute Consultation
            </Text>
            <Text style={[styles.pricingAmount, { color: colors.text }]}>
              {nutritionist.price}
            </Text>
          </View>
        </View>
      </ScrollView>
    );
  };
  
  const renderScheduleStep = () => {
    if (!nutritionist) return null;
    
    return (
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={styles.stepHeader}>
          <TouchableOpacity
            onPress={handleBack}
            style={styles.backButton}
            accessibilityRole="button"
            accessibilityLabel="Back to nutritionist details"
          >
            <Feather name="chevron-left" size={20} color={colors.text} />
            <Text style={[styles.backButtonText, { color: colors.text }]}>
              Back
            </Text>
          </TouchableOpacity>
          
          <Text style={[styles.stepTitle, { color: colors.text }]}>
            Select Date & Time
          </Text>
        </View>
        
        <Text style={[styles.scheduleSubtitle, { color: colors.textSecondary }]}>
          Choose an available date for your consultation with {nutritionist.name}
        </Text>
        
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.datesScrollView}
          contentContainerStyle={styles.datesContainer}
        >
          {availabilityDays.map((day, index) => {
            const isSelected = selectedDate && isSameDay(selectedDate, day.date);
            const hasAvailableSlots = day.slots.some(slot => slot.isAvailable);
            
            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.dateCard,
                  isSelected && [styles.selectedDateCard, { borderColor: colors.primary }],
                  !hasAvailableSlots && styles.unavailableDateCard,
                  { backgroundColor: isDark ? '#1E293B' : '#FFFFFF' }
                ]}
                onPress={() => hasAvailableSlots ? handleSelectDate(day.date) : null}
                disabled={!hasAvailableSlots}
                accessibilityRole="radio"
                accessibilityState={{ checked: !!isSelected }}
                accessibilityLabel={`${format(day.date, 'EEE, MMM d')}${hasAvailableSlots ? '' : ', no available slots'}`}
              >
                <Text 
                  style={[
                    styles.dayName, 
                    { 
                      color: isSelected 
                        ? colors.primary 
                        : !hasAvailableSlots 
                          ? isDark ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.3)' 
                          : colors.text 
                    }
                  ]}
                >
                  {format(day.date, 'EEE')}
                </Text>
                <Text 
                  style={[
                    styles.dateNumber, 
                    { 
                      color: isSelected 
                        ? colors.primary 
                        : !hasAvailableSlots 
                          ? isDark ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.3)' 
                          : colors.text 
                    }
                  ]}
                >
                  {format(day.date, 'd')}
                </Text>
                <Text 
                  style={[
                    styles.monthName, 
                    { 
                      color: isSelected 
                        ? colors.primary 
                        : !hasAvailableSlots 
                          ? isDark ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.3)' 
                          : colors.textSecondary 
                    }
                  ]}
                >
                  {format(day.date, 'MMM')}
                </Text>
                
                {isSelected && (
                  <View 
                    style={[styles.selectedDateIndicator, { backgroundColor: colors.primary }]}
                  />
                )}
              </TouchableOpacity>
            );
          })}
        </ScrollView>
        
        {selectedDate && (
          <>
            <Text style={[styles.timeSelectionTitle, { color: colors.text }]}>
              Available Time Slots
            </Text>
            
            {renderSelectedDateInfo()}
            
            <View style={styles.timeSlotsContainer}>
              {availabilityDays
                .find(day => selectedDate && isSameDay(day.date, selectedDate))
                ?.slots
                .filter(slot => slot.isAvailable)
                .map((slot, index) => {
                  const isSelected = selectedTime === slot.time;
                  
                  return (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.timeSlot,
                        isSelected && [styles.selectedTimeSlot, { borderColor: colors.primary }],
                        { backgroundColor: isDark ? '#1E293B' : '#FFFFFF' }
                      ]}
                      onPress={() => handleSelectTime(slot.time)}
                      accessibilityRole="radio"
                      accessibilityState={{ checked: isSelected }}
                      accessibilityLabel={`Time slot at ${slot.time}`}
                    >
                      <Feather name="clock" size={16} color={isSelected ? colors.primary : colors.textSecondary} style={styles.timeIcon} />
                      <Text 
                        style={[
                          styles.timeText, 
                          { color: isSelected ? colors.primary : colors.text }
                        ]}
                      >
                        {slot.time}
                      </Text>
                      
                      {isSelected && (
                        <View style={styles.selectedTimeCheck}>
                          <Feather name="check" size={14}  color={colors.text} />
                        </View>
                      )}
                    </TouchableOpacity>
                  );
                })}
            </View>
          </>
        )}
      </ScrollView>
    );
  };
  
  const renderConfirmStep = () => {
    if (!nutritionist || !selectedDate || !selectedTime) return null;
    
    return (
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={styles.stepHeader}>
          <TouchableOpacity
            onPress={handleBack}
            style={styles.backButton}
            accessibilityRole="button"
            accessibilityLabel="Back to schedule selection"
          >
            <Feather name="chevron-left" size={20} color={colors.text} />
            <Text style={[styles.backButtonText, { color: colors.text }]}>
              Back
            </Text>
          </TouchableOpacity>
          
          <Text style={[styles.stepTitle, { color: colors.text }]}>
            Confirm Booking
          </Text>
        </View>
        
        <View 
          style={[
            styles.confirmationCard, 
            { backgroundColor: isDark ? '#1E293B' : '#FFFFFF' }
          ]}
        >
          <View style={styles.confirmationHeader}>
            <Text style={[styles.confirmationTitle, { color: colors.text }]}>
              Consultation Details
            </Text>
          </View>
          
          <View style={styles.confirmNutritionistInfo}>
            <Image 
              source={{ uri: nutritionist.avatar }} 
              style={styles.confirmAvatar}
              accessibilityLabel={`Photo of ${nutritionist.name}`}
            />
            
            <View style={styles.confirmInfoText}>
              <Text style={[styles.confirmName, { color: colors.text }]}>
                {nutritionist.name}
              </Text>
              <Text style={[styles.confirmSpecialties, { color: colors.textSecondary }]}>
                {nutritionist.specialties.join(', ')}
              </Text>
            </View>
          </View>
          
          <View 
            style={[
              styles.consultationDetails, 
              { backgroundColor: isDark ? '#334155' : '#F1F5F9' }
            ]}
          >
            <View style={styles.detailRow}>
              <Feather name="calendar" size={16} color={colors.primary} style={styles.detailIcon} />
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                Date:
              </Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {format(selectedDate, 'EEEE, MMMM d, yyyy')}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <Feather name="clock" size={16} color={colors.primary} style={styles.detailIcon} />
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                Time:
              </Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {selectedTime}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              {selectedConsultationType === 'video' ? (
                <Feather name="video" size={16} color={colors.primary} style={styles.detailIcon} />
              ) : (
                <MessageCircle size={16} color={colors.primary} style={styles.detailIcon} />
              )}
              <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
                Type:
              </Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {selectedConsultationType === 'video' ? 'Video Call' : 'Chat Session'}
              </Text>
            </View>
          </View>
          
          <View style={styles.pricingSummary}>
            <Text style={[styles.pricingSummaryTitle, { color: colors.text }]}>
              Payment Summary
            </Text>
            
            <View style={styles.priceRow}>
              <Text style={[styles.priceLabel, { color: colors.textSecondary }]}>
                Consultation Fee
              </Text>
              <Text style={[styles.priceValue, { color: colors.text }]}>
                {nutritionist.price}
              </Text>
            </View>
            
            <View style={[styles.totalRow, { borderTopColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)' }]}>
              <Text style={[styles.totalLabel, { color: colors.text }]}>
                Total
              </Text>
              <Text style={[styles.totalValue, { color: colors.text }]}>
                {nutritionist.price}
              </Text>
            </View>
          </View>
          
          <Text style={[styles.noteText, { color: colors.textSecondary }]}>
            You will receive a confirmation email with details to join the consultation.
          </Text>
        </View>
      </ScrollView>
    );
  };

  return (
    <Modal
      transparent
      visible={isVisible}
      animationType="slide"
      onRequestClose={handleCloseModal}
    >
      <BlurView
        intensity={10}
        tint={isDark ? 'dark' : 'light'}
        style={styles.backdrop}
      >
        <View 
          style={[
            styles.modalContainer, 
            { backgroundColor: colors.background }
          ]}
        >
          <View style={styles.header}>
            <TouchableOpacity 
              style={[styles.closeButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
              onPress={handleCloseModal}
              accessibilityLabel="Close booking modal"
              accessibilityRole="button"
            >
              <Feather name="x" size={20} color={colors.text} />
            </TouchableOpacity>
          </View>
          
          {currentStep === 'details' && renderDetailsStep()}
          {currentStep === 'schedule' && renderScheduleStep()}
          {currentStep === 'confirm' && renderConfirmStep()}
          
          <View style={styles.footer}>
            {currentStep === 'details' && (
              <TouchableOpacity
                style={[styles.continueButton, { backgroundColor: colors.primary }]}
                onPress={handleContinue}
                accessibilityRole="button"
                accessibilityLabel="Continue to schedule selection"
              >
                <Text style={styles.continueButtonText}>
                  Select Schedule
                </Text>
                <Feather name="chevron-right" size={20}  color={colors.text} />
              </TouchableOpacity>
            )}
            
            {currentStep === 'schedule' && (
              <TouchableOpacity
                style={[
                  styles.continueButton, 
                  { 
                    backgroundColor: selectedDate && selectedTime ? colors.primary : isDark ? '#334155' : '#E2E8F0',
                    opacity: selectedDate && selectedTime ? 1 : 0.7
                  }
                ]}
                onPress={handleContinue}
                disabled={!selectedDate || !selectedTime}
                accessibilityRole="button"
                accessibilityLabel="Continue to booking confirmation"
                accessibilityState={{ disabled: !selectedDate || !selectedTime }}
              >
                <Text 
                  style={[
                    styles.continueButtonText,
                    { color: selectedDate && selectedTime ? '#FFFFFF' : colors.textSecondary }
                  ]}
                >
                  Continue
                </Text>
                <Feather name="chevron-right" size={20} color={selectedDate && selectedTime ? '#FFFFFF' : colors.textSecondary} />
              </TouchableOpacity>
            )}
            
            {currentStep === 'confirm' && (
              <TouchableOpacity
                style={[styles.continueButton, { backgroundColor: colors.primary }]}
                onPress={handleBookConsultation}
                disabled={isLoading}
                accessibilityRole="button"
                accessibilityLabel="Confirm booking"
              >
                <Text style={styles.continueButtonText}>
                  Confirm Booking
                </Text>
                <Feather name="check" size={20}  color={colors.text} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </BlurView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    height: '90%',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -5 },
        shadowOpacity: 0.1,
        shadowRadius: 6,
      },
      android: {
        elevation: 5,
      },
      web: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -5 },
        shadowOpacity: 0.1,
        shadowRadius: 6,
      }
    }),
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 16,
    alignItems: 'flex-end',
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  nutritionistHeader: {
    flexDirection: 'row',
    marginTop: 16,
    marginBottom: 24,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginRight: 16,
  },
  headerInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  nutritionistName: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 6,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  ratingIcon: {
    marginRight: 4,
  },
  ratingText: {
    fontWeight: '600',
    marginRight: 4,
  },
  reviewCount: {
    fontSize: 14,
  },
  experienceText: {
    fontSize: 14,
  },
  specialtiesContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  specialtiesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  specialtyBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  specialtyText: {
    fontSize: 14,
  },
  bioContainer: {
    marginBottom: 24,
  },
  bioText: {
    fontSize: 15,
    lineHeight: 22,
  },
  consultationTypesContainer: {
    marginBottom: 24,
  },
  typeOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  typeOption: {
    width: '48%',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
      web: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      }
    }),
  },
  selectedType: {
  },
  typeIcon: {
    marginBottom: 8,
  },
  typeText: {
    fontSize: 16,
    fontWeight: '500',
  },
  pricingContainer: {
    marginBottom: 24,
  },
  pricingCard: {
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pricingTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  pricingAmount: {
    fontSize: 18,
    fontWeight: '700',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.05)',
  },
  continueButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 14,
    borderRadius: 12,
  },
  continueButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  stepHeader: {
    marginTop: 8,
    marginBottom: 24,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  backButtonText: {
    fontSize: 16,
    marginLeft: 4,
  },
  stepTitle: {
    fontSize: 22,
    fontWeight: '700',
    textAlign: 'center',
  },
  scheduleSubtitle: {
    fontSize: 15,
    textAlign: 'center',
    marginBottom: 24,
  },
  datesScrollView: {
    marginBottom: 24,
  },
  datesContainer: {
    paddingHorizontal: 4,
  },
  dateCard: {
    width: 68,
    height: 90,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 6,
    borderWidth: 2,
    borderColor: 'transparent',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
      web: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      }
    }),
  },
  selectedDateCard: {
  },
  unavailableDateCard: {
    opacity: 0.5,
  },
  dayName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 6,
  },
  dateNumber: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  monthName: {
    fontSize: 14,
  },
  selectedDateIndicator: {
    position: 'absolute',
    bottom: -2,
    width: 16,
    height: 4,
    borderRadius: 2,
  },
  timeSelectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  selectedDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    marginBottom: 16,
  },
  selectedDateIcon: {
    marginRight: 8,
  },
  selectedDateText: {
    fontSize: 14,
    fontWeight: '500',
  },
  timeSlotsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  timeSlot: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: 'transparent',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 1,
      },
      android: {
        elevation: 1,
      },
      web: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 1,
      }
    }),
  },
  selectedTimeSlot: {
  },
  timeIcon: {
    marginRight: 8,
  },
  timeText: {
    fontSize: 14,
    fontWeight: '500',
  },
  selectedTimeCheck: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#10B981',
    alignItems: 'center',
    justifyContent: 'center',
  },
  confirmationCard: {
    borderRadius: 16,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
      web: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  confirmationHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  confirmationTitle: {
    fontSize: 18,
    fontWeight: '700',
    textAlign: 'center',
  },
  confirmNutritionistInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  confirmAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 12,
  },
  confirmInfoText: {
    flex: 1,
  },
  confirmName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  confirmSpecialties: {
    fontSize: 14,
  },
  consultationDetails: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailIcon: {
    marginRight: 8,
  },
  detailLabel: {
    width: 45,
    fontSize: 14,
    marginRight: 8,
  },
  detailValue: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
  },
  pricingSummary: {
    padding: 16,
  },
  pricingSummaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  priceLabel: {
    fontSize: 14,
  },
  priceValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 12,
    marginTop: 4,
    borderTopWidth: 1,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '700',
  },
  totalValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  noteText: {
    fontSize: 12,
    fontStyle: 'italic',
    paddingHorizontal: 16,
    paddingBottom: 16,
    textAlign: 'center',
  },
});

export default NutritionistBookingModal; 