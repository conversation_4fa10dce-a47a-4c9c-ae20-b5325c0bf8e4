import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Platform
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { router } from 'expo-router';

interface Nutritionist {
  id: string;
  name: string;
  avatar: string;
  specialties: string[];
  rating: number;
  reviewCount: number;
  experience: string;
  price: string;
  availability: {
    date: string;
    slots: {
      time: string;
      isAvailable: boolean;
    }[];
  }[];
  consultationTypes: ('video' | 'chat')[];
  bio: string;
}

interface NutritionistConsultationBookingProps {
  onSelectNutritionist: (nutritionist: Nutritionist) => void;
  onViewAllNutritionists: () => void;
}

const NutritionistConsultationBooking: React.FC<NutritionistConsultationBookingProps> = ({
  onSelectNutritionist,
  onViewAllNutritionists
}) => {
  const { colors, isDark } = useTheme();
  const [filter, setFilter] = useState<'all' | 'video' | 'chat'>('all');
  
  const nutritionists: Nutritionist[] = [
    {
      id: '1',
      name: 'Dr. <PERSON>',
      avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330',
      specialties: ['Sports Nutrition', 'Weight Management'],
      rating: 4.9,
      reviewCount: 124,
      experience: '8 years',
      price: '$45 / 30 min',
      availability: [
        {
          date: '2023-06-10',
          slots: [
            { time: '09:00 AM', isAvailable: true },
            { time: '11:00 AM', isAvailable: true },
            { time: '02:00 PM', isAvailable: false },
            { time: '04:00 PM', isAvailable: true }
          ]
        },
        {
          date: '2023-06-11',
          slots: [
            { time: '10:00 AM', isAvailable: true },
            { time: '01:00 PM', isAvailable: true },
            { time: '03:00 PM', isAvailable: true }
          ]
        }
      ],
      consultationTypes: ['video', 'chat'],
      bio: 'Registered Dietitian specializing in performance nutrition for athletes and weight management plans tailored to individual needs.'
    },
    {
      id: '2',
      name: 'Mark Thompson',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e',
      specialties: ['Diabetes Management', 'Heart Health'],
      rating: 4.7,
      reviewCount: 98,
      experience: '12 years',
      price: '$50 / 30 min',
      availability: [
        {
          date: '2023-06-10',
          slots: [
            { time: '10:00 AM', isAvailable: true },
            { time: '12:00 PM', isAvailable: false },
            { time: '03:00 PM', isAvailable: true }
          ]
        },
        {
          date: '2023-06-12',
          slots: [
            { time: '09:00 AM', isAvailable: true },
            { time: '11:00 AM', isAvailable: true },
            { time: '02:00 PM', isAvailable: true }
          ]
        }
      ],
      consultationTypes: ['video'],
      bio: 'Clinical nutritionist with extensive experience in metabolic disorders and cardiovascular health. Focused on evidence-based nutrition plans.'
    },
    {
      id: '3',
      name: 'Lisa Chen, RD',
      avatar: 'https://images.unsplash.com/photo-1580489944761-15a19d654956',
      specialties: ['Plant-Based Nutrition', 'Food Allergies'],
      rating: 4.8,
      reviewCount: 86,
      experience: '6 years',
      price: '$40 / 30 min',
      availability: [
        {
          date: '2023-06-11',
          slots: [
            { time: '08:00 AM', isAvailable: true },
            { time: '12:00 PM', isAvailable: true },
            { time: '04:00 PM', isAvailable: true }
          ]
        },
        {
          date: '2023-06-13',
          slots: [
            { time: '09:00 AM', isAvailable: true },
            { time: '01:00 PM', isAvailable: false },
            { time: '05:00 PM', isAvailable: true }
          ]
        }
      ],
      consultationTypes: ['chat', 'video'],
      bio: 'Specializing in plant-based nutrition and food sensitivity management. Passionate about helping clients discover personalized nutrition solutions.'
    }
  ];

  const filterNutritionists = () => {
    if (filter === 'all') return nutritionists;
    return nutritionists.filter(nutritionist => 
      nutritionist.consultationTypes.includes(filter as 'video' | 'chat')
    );
  };

  const handleFilterChange = (newFilter: 'all' | 'video' | 'chat') => {
    setFilter(newFilter);
  };

  const renderFilterOptions = () => {
    return (
      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[
            styles.filterOption,
            filter === 'all' && [styles.activeFilter, { backgroundColor: colors.primary + '15' }]
          ]}
          onPress={() => handleFilterChange('all')}
          accessibilityRole="button"
          accessibilityLabel="Show all nutritionists"
          accessibilityState={{ selected: filter === 'all' }}
        >
          <Text 
            style={[
              styles.filterText, 
              filter === 'all' ? { color: colors.primary } : { color: colors.textSecondary }
            ]}
          >
            All
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.filterOption,
            filter === 'video' && [styles.activeFilter, { backgroundColor: colors.primary + '15' }]
          ]}
          onPress={() => handleFilterChange('video')}
          accessibilityRole="button"
          accessibilityLabel="Show nutritionists with video consultation"
          accessibilityState={{ selected: filter === 'video' }}
        >
          <Feather name="video" size={16} color={filter === 'video' ? colors.primary : colors.textSecondary} style={styles.filterIcon} />
          <Text 
            style={[
              styles.filterText, 
              filter === 'video' ? { color: colors.primary } : { color: colors.textSecondary }
            ]}
          >
            Video
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.filterOption,
            filter === 'chat' && [styles.activeFilter, { backgroundColor: colors.primary + '15' }]
          ]}
          onPress={() => handleFilterChange('chat')}
          accessibilityRole="button"
          accessibilityLabel="Show nutritionists with chat consultation"
          accessibilityState={{ selected: filter === 'chat' }}
        >
          <MessageCircle 
            size={16} 
            color={filter === 'chat' ? colors.primary : colors.textSecondary} 
            style={styles.filterIcon} 
          />
          <Text 
            style={[
              styles.filterText, 
              filter === 'chat' ? { color: colors.primary } : { color: colors.textSecondary }
            ]}
          >
            Chat
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderNutritionistCard = (nutritionist: Nutritionist) => {
    return (
      <TouchableOpacity
        key={nutritionist.id}
        style={[
          styles.nutritionistCard,
          { backgroundColor: isDark ? '#1E293B' : '#FFFFFF' }
        ]}
        onPress={() => onSelectNutritionist(nutritionist)}
        accessibilityRole="button"
        accessibilityLabel={`View ${nutritionist.name}'s profile and book a consultation`}
      >
        <View style={styles.cardHeader}>
          <Image 
            source={{ uri: nutritionist.avatar }} 
            style={styles.avatar}
            accessibilityLabel={`Photo of ${nutritionist.name}`}
          />
          <View style={styles.nutritionistInfo}>
            <Text style={[styles.nutritionistName, { color: colors.text }]}>
              {nutritionist.name}
            </Text>
            
            <View style={styles.specialtiesContainer}>
              {nutritionist.specialties.map((specialty, index) => (
                <View 
                  key={index} 
                  style={[
                    styles.specialtyBadge, 
                    { backgroundColor: isDark ? '#334155' : '#F1F5F9' }
                  ]}
                >
                  <Text style={[styles.specialtyText, { color: colors.textSecondary }]}>
                    {specialty}
                  </Text>
                </View>
              ))}
            </View>
            
            <View style={styles.ratingContainer}>
              <Feather name="star" size={16} style={styles.ratingIcon} />
              <Text style={[styles.ratingText, { color: colors.text }]}>
                {nutritionist.rating} 
              </Text>
              <Text style={[styles.reviewCount, { color: colors.textSecondary }]}>
                ({nutritionist.reviewCount} reviews)
              </Text>
            </View>
          </View>
        </View>
        
        <View style={styles.cardFooter}>
          <View style={styles.consultationTypes}>
            {nutritionist.consultationTypes.includes('video') && (
              <View style={styles.consultationType}>
                <Feather name="video" size={16} color={colors.primary} style={styles.consultationIcon} />
                <Text style={[styles.consultationText, { color: colors.text }]}>
                  Video
                </Text>
              </View>
            )}
            
            {nutritionist.consultationTypes.includes('chat') && (
              <View style={styles.consultationType}>
                <MessageCircle size={16} color={colors.primary} style={styles.consultationIcon} />
                <Text style={[styles.consultationText, { color: colors.text }]}>
                  Chat
                </Text>
              </View>
            )}
          </View>
          
          <View style={styles.priceContainer}>
            <Text style={[styles.priceText, { color: colors.text }]}>
              {nutritionist.price}
            </Text>
          </View>
        </View>
        
        <View style={styles.availabilityContainer}>
          <View style={styles.availabilityHeader}>
            <Feather name="calendar" size={14} color={colors.textSecondary} style={styles.availabilityIcon} />
            <Text style={[styles.availabilityTitle, { color: colors.textSecondary }]}>
              Next Available
            </Text>
          </View>
          
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.availabilitySlots}
          >
            {nutritionist.availability[0].slots
              .filter(slot => slot.isAvailable)
              .slice(0, 3)
              .map((slot, index) => (
                <View 
                  key={index} 
                  style={[
                    styles.timeSlot, 
                    { backgroundColor: isDark ? '#334155' : '#F1F5F9' }
                  ]}
                >
                  <Feather name="clock" size={12} color={colors.textSecondary} style={styles.timeIcon} />
                  <Text style={[styles.timeText, { color: colors.text }]}>
                    {slot.time}
                  </Text>
                </View>
              ))}
          </ScrollView>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Feather name="calendar" size={20} color={colors.primary} style={styles.headerIcon} />
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Nutritionist Consultation
          </Text>
        </View>
        
        <TouchableOpacity
          style={[styles.viewAllButton, { backgroundColor: colors.primary + '15' }]}
          onPress={onViewAllNutritionists}
          accessibilityLabel="View all nutritionists"
          accessibilityRole="button"
        >
          <Text style={[styles.viewAllText, { color: colors.primary }]}>
            View All
          </Text>
          <Feather name="chevron-right" size={16} color={colors.primary} />
        </TouchableOpacity>
      </View>
      
      <View style={styles.actionHeader}>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Book a session with a certified nutritionist for personalized guidance
        </Text>
        
        <TouchableOpacity 
          style={[styles.filterButton, { backgroundColor: isDark ? '#334155' : '#F1F5F9' }]}
          onPress={() => router.push('/(tabs)/dietitian/nutritionist-filters' as any)}
          accessibilityLabel="Filter nutritionists"
          accessibilityRole="button"
        >
          <Feather name="filter" size={16} color={colors.text} />
        </TouchableOpacity>
      </View>
      
      {renderFilterOptions()}
      
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
      >
        {filterNutritionists().map(renderNutritionistCard)}
        
        <TouchableOpacity
          style={[
            styles.showMoreButton, 
            { 
              backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)',
              borderColor: colors.primary 
            }
          ]}
          onPress={onViewAllNutritionists}
          accessibilityLabel="Show all available nutritionists"
          accessibilityRole="button"
        >
          <Text style={[styles.showMoreText, { color: colors.primary }]}>
            Show All Nutritionists
          </Text>
        </TouchableOpacity>
        
        <View style={{ height: 20 }} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  actionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 14,
    flex: 1,
    marginRight: 16,
  },
  filterButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    borderRadius: 16,
  },
  activeFilter: {
    borderWidth: 1,
    borderColor: 'transparent',
  },
  filterIcon: {
    marginRight: 4,
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
  },
  scrollView: {
    maxHeight: 600,
  },
  scrollContent: {
    paddingHorizontal: 16,
  },
  nutritionistCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
      web: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      }
    }),
  },
  cardHeader: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  avatar: {
    width: 70,
    height: 70,
    borderRadius: 35,
    marginRight: 12,
  },
  nutritionistInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  nutritionistName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 6,
  },
  specialtiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 6,
  },
  specialtyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginRight: 6,
    marginBottom: 6,
  },
  specialtyText: {
    fontSize: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingIcon: {
    marginRight: 4,
  },
  ratingText: {
    fontWeight: '600',
    marginRight: 4,
  },
  reviewCount: {
    fontSize: 12,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  consultationTypes: {
    flexDirection: 'row',
  },
  consultationType: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  consultationIcon: {
    marginRight: 4,
  },
  consultationText: {
    fontSize: 14,
  },
  priceContainer: {
  },
  priceText: {
    fontSize: 14,
    fontWeight: '600',
  },
  availabilityContainer: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.05)',
    paddingTop: 12,
  },
  availabilityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  availabilityIcon: {
    marginRight: 4,
  },
  availabilityTitle: {
    fontSize: 12,
  },
  availabilitySlots: {
    flexDirection: 'row',
  },
  timeSlot: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    marginRight: 8,
  },
  timeIcon: {
    marginRight: 4,
  },
  timeText: {
    fontSize: 12,
  },
  showMoreButton: {
    padding: 12,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
    borderWidth: 1,
    borderStyle: 'dashed',
  },
  showMoreText: {
    fontSize: 15,
    fontWeight: '500',
  },
});

export default NutritionistConsultationBooking; 