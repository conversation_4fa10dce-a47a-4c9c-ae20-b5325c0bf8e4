import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Platform
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';

interface DietPlan {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  benefits: string[];
  isPremium: boolean;
  price?: string;
  isPopular?: boolean;
}

interface PremiumDietPlansCardProps {
  onSubscribe: () => void;
  onSelectPlan: (plan: DietPlan) => void;
  userSubscription?: {
    isSubscribed: boolean;
    tier: 'basic' | 'premium' | 'pro';
    expiresAt: string;
  };
}

const PremiumDietPlansCard: React.FC<PremiumDietPlansCardProps> = ({
  onSubscribe,
  onSelectPlan,
  userSubscription
}) => {
  const { colors, isDark } = useTheme();
  const [expandedInfoId, setExpandedInfoId] = useState<string | null>(null);
  
  const isUserSubscribed = userSubscription?.isSubscribed || false;
  const userTier = userSubscription?.tier || 'basic';
  
  const dietPlans: DietPlan[] = [
    {
      id: 'keto-advanced',
      title: 'Advanced Keto Plan',
      description: 'Scientifically formulated ketogenic diet plan with personalized macros, meal variations, and metabolic adaptation tracking.',
      imageUrl: 'https://images.unsplash.com/photo-1600335895229-6e75511892c8',
      benefits: [
        'Custom macronutrient calculator',
        'Weekly meal plans with shopping lists',
        'Ketosis tracking tools',
        'Fat-adapted recipes library',
        'Intermittent fasting integration'
      ],
      isPremium: true,
      price: '$7.99/month',
      isPopular: true
    },
    {
      id: 'plant-based-athlete',
      title: 'Plant-Based Athlete',
      description: 'High-performance vegan nutrition plan designed for athletes with protein optimization and nutrient timing.',
      imageUrl: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd',
      benefits: [
        'Protein-optimized vegan meals',
        'Pre and post-workout nutrition',
        'Performance supplement guide',
        'Recovery-focused recipes',
        'Nutrient deficiency prevention'
      ],
      isPremium: true,
      price: '$5.99/month'
    },
    {
      id: 'personalized-medical',
      title: 'Medical Condition Diet',
      description: 'Specialized nutrition plans tailored for specific medical conditions including diabetes, heart disease, and autoimmune disorders.',
      imageUrl: 'https://images.unsplash.com/photo-1505576399279-565b52d4ac71',
      benefits: [
        'Medical condition-specific plans',
        'Dietitian-reviewed recommendations',
        'Anti-inflammatory food options',
        'Symptom tracking integration',
        'Medication interaction guidance'
      ],
      isPremium: true,
      price: '$9.99/month'
    },
    {
      id: 'basic-healthy',
      title: 'Balanced Nutrition Plan',
      description: 'Our free balanced nutrition plan to help you maintain a healthy diet with proper macronutrient distribution.',
      imageUrl: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061',
      benefits: [
        'Basic macronutrient balance',
        'Simple meal recommendations',
        'Weekly meal ideas',
        'Basic nutritional guidelines'
      ],
      isPremium: false
    }
  ];

  const toggleExpandInfo = (id: string) => {
    setExpandedInfoId(expandedInfoId === id ? null : id);
  };

  const renderDietPlan = (plan: DietPlan) => {
    const isExpanded = expandedInfoId === plan.id;
    const isPlanAccessible = !plan.isPremium || (isUserSubscribed && userTier !== 'basic');
    
    return (
      <View 
        key={plan.id}
        style={[
          styles.planCard,
          { backgroundColor: isDark ? '#1E293B' : '#FFFFFF' }
        ]}
      >
        <TouchableOpacity
          style={styles.planHeader}
          onPress={() => toggleExpandInfo(plan.id)}
          accessibilityRole="button"
          accessibilityLabel={`${plan.title} - ${plan.isPremium ? 'Premium plan' : 'Free plan'}`}
          accessibilityHint="Double tap to expand details"
        >
          <View style={styles.planHeaderContent}>
            <Image 
              source={{ uri: plan.imageUrl }}
              style={styles.planImage}
              accessibilityLabel={`Image for ${plan.title}`}
            />
            
            <View style={styles.planInfo}>
              <View style={styles.planTitleRow}>
                <Text style={[styles.planTitle, { color: colors.text }]}>
                  {plan.title}
                </Text>
                {plan.isPremium && (
                  <Crown size={16} color="#F59E0B" style={styles.crownIcon} />
                )}
              </View>
              
              <Text 
                style={[styles.planDescription, { color: colors.textSecondary }]}
                numberOfLines={isExpanded ? undefined : 2}
              >
                {plan.description}
              </Text>
              
              {plan.isPremium && (
                <Text style={[styles.planPrice, { color: '#F59E0B' }]}>
                  {plan.price}
                </Text>
              )}
            </View>
          </View>
          
          <View style={styles.expandIconContainer}>
            <Feather name="chevron-right" size={20} color={colors.textSecondary} style={[
                styles.expandIcon,
                isExpanded && { transform: [{ rotate: '90deg' }] }
              ]} />
          </View>
        </TouchableOpacity>
        
        {isExpanded && (
          <View style={styles.expandedContent}>
            <View style={[styles.benefitsContainer, { borderTopColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
              <Text style={[styles.benefitsTitle, { color: colors.text }]}>
                Plan Benefits
              </Text>
              
              {plan.benefits.map((benefit, index) => (
                <View key={index} style={styles.benefitItem}>
                  <Feather name="check" size={16} color={colors.primary} style={styles.checkIcon} />
                  <Text style={[styles.benefitText, { color: colors.text }]}>
                    {benefit}
                  </Text>
                </View>
              ))}
            </View>
            
            <View style={styles.actionContainer}>
              {plan.isPremium && !isUserSubscribed ? (
                <TouchableOpacity
                  style={[styles.subscribeButton, { backgroundColor: colors.primary }]}
                  onPress={onSubscribe}
                  accessibilityRole="button"
                  accessibilityLabel="Subscribe to access premium plans"
                >
                  <Crown size={16} color="#FFFFFF" style={styles.buttonIcon} />
                  <Text style={styles.subscribeButtonText}>
                    Subscribe to Access
                  </Text>
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  style={[
                    styles.accessButton,
                    { 
                      backgroundColor: isPlanAccessible ? colors.primary : isDark ? '#334155' : '#F1F5F9',
                      opacity: isPlanAccessible ? 1 : 0.8 
                    }
                  ]}
                  onPress={() => isPlanAccessible ? onSelectPlan(plan) : onSubscribe()}
                  accessibilityRole="button"
                  accessibilityLabel={isPlanAccessible ? `Use ${plan.title}` : `Upgrade to access ${plan.title}`}
                >
                  {!isPlanAccessible && (
                    <Feather name="lock" size={16} color={colors.textSecondary} style={styles.buttonIcon} />
                  )}
                  <Text 
                    style={[
                      styles.accessButtonText, 
                      { color: isPlanAccessible ? '#FFFFFF' : colors.textSecondary }
                    ]}
                  >
                    {isPlanAccessible ? 'Use This Plan' : 'Upgrade to Access'}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        )}
        
        {plan.isPopular && (
          <View style={styles.popularTag}>
            <Text style={styles.popularTagText}>Most Popular</Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Crown size={20} color="#F59E0B" style={styles.headerIcon} />
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Premium Diet Plans
          </Text>
        </View>
        
        <TouchableOpacity
          style={[styles.viewAllButton, { backgroundColor: colors.primary + '15' }]}
          onPress={() => router.push('/(tabs)/dietitian/premium-plans' as any)}
          accessibilityLabel="View all premium plans"
          accessibilityRole="button"
        >
          <Text style={[styles.viewAllText, { color: colors.primary }]}>
            View All
          </Text>
          <Feather name="chevron-right" size={16} color={colors.primary} />
        </TouchableOpacity>
      </View>
      
      {!isUserSubscribed && (
        <TouchableOpacity
          onPress={onSubscribe}
          accessibilityRole="button"
          accessibilityLabel="Unlock all premium diet plans and features"
          accessibilityHint="Double tap to view subscription options"
        >
          <LinearGradient
            colors={isDark ? ['#1E40AF', '#3B82F6'] : ['#3B82F6', '#60A5FA']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.premiumBanner}
          >
            <View style={styles.bannerContent}>
              <View style={styles.bannerTextContainer}>
                <Text style={styles.bannerTitle}>
                  Unlock All Premium Diet Plans
                </Text>
                <Text style={styles.bannerDescription}>
                  Get personalized plans for your specific health goals and dietary needs.
                </Text>
              </View>
              
              <View style={styles.bannerButtonContainer}>
                <TouchableOpacity
                  style={styles.bannerButton}
                  onPress={onSubscribe}
                  accessibilityRole="button"
                  accessibilityLabel="Subscribe now"
                >
                  <Text style={styles.bannerButtonText}>
                    Subscribe
                  </Text>
                  <Feather name="chevron-right" size={16}  color={colors.text} />
                </TouchableOpacity>
              </View>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      )}
      
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
      >
        {dietPlans.map(renderDietPlan)}
        
        <TouchableOpacity
          style={[
            styles.customPlanCard, 
            { 
              backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)',
              borderColor: colors.primary 
            }
          ]}
          onPress={() => router.push('/(tabs)/dietitian/custom-plan' as any)}
          accessibilityLabel="Create custom premium diet plan"
          accessibilityRole="button"
        >
          <Feather name="plus" size={24} color={colors.primary} style={styles.customPlanIcon} />
          <Text style={[styles.customPlanText, { color: colors.text }]}>
            Create Custom Premium Plan
          </Text>
          <Text style={[styles.customPlanDescription, { color: colors.textSecondary }]}>
            Work with our AI to create a fully customized plan for your specific needs
          </Text>
        </TouchableOpacity>
        
        <View style={{ height: 20 }} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  premiumBanner: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  bannerContent: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  bannerTextContainer: {
    flex: 1,
    marginRight: 16,
  },
  bannerTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 4,
  },
  bannerDescription: {
    color: '#FFFFFF',
    fontSize: 12,
    opacity: 0.9,
  },
  bannerButtonContainer: {
  },
  bannerButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  bannerButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginRight: 4,
  },
  scrollView: {
    maxHeight: 600,
  },
  scrollContent: {
    paddingHorizontal: 16,
  },
  planCard: {
    borderRadius: 12,
    marginBottom: 12,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
      web: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
      }
    }),
  },
  planHeader: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  planHeaderContent: {
    flex: 1,
    flexDirection: 'row',
  },
  planImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  planInfo: {
    flex: 1,
  },
  planTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  planTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 6,
  },
  crownIcon: {
    marginTop: 2,
  },
  planDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  planPrice: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 6,
  },
  expandIconContainer: {
    paddingLeft: 12,
  },
  expandIcon: {
  },
  expandedContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  benefitsContainer: {
    borderTopWidth: 1,
    paddingTop: 16,
    marginBottom: 16,
  },
  benefitsTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 12,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  checkIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  benefitText: {
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  actionContainer: {
  },
  subscribeButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  buttonIcon: {
    marginRight: 8,
  },
  subscribeButtonText: {
    color: '#FFFFFF',
    fontSize: 15,
    fontWeight: '600',
  },
  accessButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  accessButtonText: {
    fontSize: 15,
    fontWeight: '600',
  },
  popularTag: {
    position: 'absolute',
    top: 12,
    right: 0,
    backgroundColor: '#F59E0B',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderTopLeftRadius: 4,
    borderBottomLeftRadius: 4,
  },
  popularTagText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  customPlanCard: {
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
  },
  customPlanIcon: {
    marginBottom: 12,
  },
  customPlanText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  customPlanDescription: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default PremiumDietPlansCard; 