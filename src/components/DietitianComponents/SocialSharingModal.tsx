import React, { useState, useEffect , useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Image,
  TextInput,
  Share,
  Platform,
  Alert,
  ScrollView,
  ActivityIndicator
, Dimensions } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { BlurView } from 'expo-blur';
import { Feather } from '@expo/vector-icons';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import ViewShot, { captureRef } from 'react-native-view-shot';
import { LinearGradient } from 'expo-linear-gradient';
import * as MediaLibrary from 'expo-media-library';

// Content types for sharing
type ContentType = 'meal-plan' | 'recipe' | 'achievement';

interface SocialSharingModalProps {
  isVisible: boolean;
  onClose: () => void;
  contentType: ContentType;
  content: any; // The content to share (meal plan, recipe, or achievement)
  appName?: string;
}

interface SharingOption {
  id: string;
  label: string;
  icon: React.ReactNode;
  action: () => void;
}

const SocialSharingModal: React.FC<SocialSharingModalProps> = ({
  isVisible,
  onClose,
  contentType,
  content,
  appName = "HealthApp"
}) => {
  const { colors, isDark } = useTheme();
  const [message, setMessage] = useState<string>('');
  const [isCopied, setIsCopied] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const [contentImage, setContentImage] = useState<string | null>(null);
  const { width: screenWidth } = Dimensions.get('window');
  
  const viewShotRef = useRef<ViewShot>(null);
  
  // Generate default message based on content type
  useEffect(() => {
    if (isVisible) {
      let defaultMessage = '';
      
      switch (contentType) {
        case 'meal-plan':
          defaultMessage = `Check out my meal plan from ${appName}! It's helping me eat healthier. #nutrition #healthyeating`;
          break;
        case 'recipe':
          defaultMessage = `I found this delicious ${content.name} recipe on ${appName}! #recipe #cooking #healthyfood`;
          break;
        case 'achievement':
          defaultMessage = `I just ${content.description} on ${appName}! #healthgoals #progress`;
          break;
      }
      
      setMessage(defaultMessage);
      
      // Generate preview image when modal opens
      generateContentImage();
    }
  }, [isVisible, contentType, content]);

  const generateContentImage = async () => {
    setIsGeneratingImage(true);
    
    // In a real app, you would generate an actual image to share
    // We'll just simulate the delay here
    setTimeout(() => {
      setContentImage(getImageForContent());
      setIsGeneratingImage(false);
    }, 1000);
  };
  
  const getImageForContent = () => {
    // In a real app, you'd use a real image URL or generate one dynamically
    switch (contentType) {
      case 'meal-plan':
        return content.imageUrl || 'https://images.unsplash.com/photo-**********-85f173990554';
      case 'recipe':
        return content.imageUrl || 'https://images.unsplash.com/photo-1542010589005-d1eacc3918f2';
      case 'achievement':
        return content.imageUrl || 'https://images.unsplash.com/photo-1589323935981-a68f3d417c65';
    }
  };

  const getContentTitle = () => {
    switch (contentType) {
      case 'meal-plan':
        return content.title || 'My Meal Plan';
      case 'recipe':
        return content.name || 'Delicious Recipe';
      case 'achievement':
        return content.title || 'My Achievement';
    }
  };

  const getContentDescription = () => {
    switch (contentType) {
      case 'meal-plan':
        return `${content.meals?.length || 0} meals - ${getTotalNutrition()} calories`;
      case 'recipe':
        return `${content.prepTime || 0} min - ${content.nutrition?.calories || 0} calories`;
      case 'achievement':
        return content.description || 'I reached my health goal!';
    }
  };
  
  const getTotalNutrition = () => {
    // Calculate total nutrition for meal plan
    if (contentType === 'meal-plan' && content.meals) {
      return content.meals.reduce((total: number, meal: any) => {
        return total + (meal.nutrition?.calories || 0);
      }, 0);
    }
    return 0;
  };

  const handleCapture = async () => {
    try {
      if (viewShotRef.current) {
        setIsSaving(true);
        const uri = await captureRef(viewShotRef, {
          format: 'jpg',
          quality: 0.8,
        });
        
        // Save to media library on mobile
        if (Platform.OS !== 'web') {
          const permission = await MediaLibrary.requestPermissionsAsync();
          
          if (permission.granted) {
            await MediaLibrary.saveToLibraryAsync(uri);
            Alert.alert('Saved!', 'Image saved to your photo library');
          } else {
            Alert.alert('Permission needed', 'Please grant permission to save the image');
          }
        } else {
          // On web, we'd provide a download link
          Alert.alert('On web', 'On web, users would be able to download the image');
        }
        setIsSaving(false);
      }
    } catch (error) {
      console.error('Error capturing image:', error);
      setIsSaving(false);
      Alert.alert('Error', 'Failed to save the image');
    }
  };

  const handleCopyLink = () => {
    // In a real app, you would generate a shareable URL
    // For now, we'll just simulate copying a link
    const shareableLink = `https://your-app-link.com/share/${contentType}/${content.id}`;
    
    // Copy to clipboard would be implemented here
    setIsCopied(true);
    setTimeout(() => setIsCopied(false), 2000);
    
    // Alert for demo purposes
    Alert.alert('Link Copied', 'Shareable link copied to clipboard!');
  };

  const handleShare = async () => {
    try {
      if (viewShotRef.current) {
        const uri = await captureRef(viewShotRef, {
          format: 'jpg',
          quality: 0.8,
        });
        
        if (Platform.OS === 'web') {
          Alert.alert('Share on Web', 'On web, users would be directed to native sharing APIs');
          return;
        }
        
        const shareOptions = {
          message: message,
          url: uri, // On iOS, this can be a local file URL
          title: getContentTitle()
        };
        
        await Share.share(shareOptions);
      }
    } catch (error) {
      console.error('Error sharing:', error);
      Alert.alert('Error', 'Failed to share');
    }
  };

  const handleSocialShare = (platform: string) => {
    // In a real app, you would implement platform-specific sharing
    // For now, we'll just simulate it with an alert
    Alert.alert(
      `Share to ${platform}`,
      `In a complete app, this would share to ${platform} using their SDK or deep linking.`
    );
  };

  const sharingOptions: SharingOption[] = [
    {
      id: 'copy',
      label: 'Copy Link',
      icon: <Feather name="copy" size={24} color={colors.text} />,
      action: handleCopyLink
    },
    {
      id: 'instagram',
      label: 'Instagram',
      icon: <Instagram size={24} color="#E1306C" />,
      action: () => handleSocialShare('Instagram')
    },
    {
      id: 'twitter',
      label: 'Twitter',
      icon: <Twitter size={24} color="#1DA1F2" />,
      action: () => handleSocialShare('Twitter')
    },
    {
      id: 'facebook',
      label: 'Facebook',
      icon: <Facebook size={24} color="#1877F2" />,
      action: () => handleSocialShare('Facebook')
    }
  ];

  return (
    <Modal
      transparent
      visible={isVisible}
      animationType="slide"
      onRequestClose={onClose}
    >
      <BlurView
        intensity={10}
        tint={isDark ? 'dark' : 'light'}
        style={styles.backdrop}
      >
        <View 
          style={[
            styles.modalContainer, 
            { backgroundColor: isDark ? '#0F172A' : '#FFFFFF' }
          ]}
        >
          <View style={styles.header}>
            <View style={styles.titleContainer}>
              <Feather name="share-2" size={20} color={colors.primary} style={styles.titleIcon} />
              <Text style={[styles.title, { color: colors.text }]}>
                Share {contentType === 'meal-plan' ? 'Meal Plan' : 
                        contentType === 'recipe' ? 'Recipe' : 'Achievement'}
              </Text>
            </View>
            
            <TouchableOpacity 
              style={[styles.closeButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
              onPress={onClose}
              accessibilityLabel="Close sharing modal"
              accessibilityRole="button"
            >
              <Feather name="x" size={20} color={colors.text} />
            </TouchableOpacity>
          </View>
          
          <ScrollView 
            style={styles.content}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            <View style={styles.previewContainer}>
              <Text style={[styles.previewTitle, { color: colors.text }]}>
                Preview
              </Text>
              
              {isGeneratingImage ? (
                <View style={[styles.generatingPreview, { backgroundColor: isDark ? '#1E293B' : '#F1F5F9' }]}>
                  <ActivityIndicator size="large" color={colors.primary} />
                  <Text style={[styles.generatingText, { color: colors.textSecondary }]}>
                    Generating preview...
                  </Text>
                </View>
              ) : (
                <ViewShot 
                  ref={viewShotRef}
                  options={{ format: 'jpg', quality: 0.8 }}
                  style={[
                    styles.shareableContent,
                    { 
                      backgroundColor: isDark ? '#1E293B' : '#FFFFFF',
                      width: screenWidth - 64
                    }
                  ]}
                >
                  <LinearGradient
                    colors={isDark ? ['#0F172A', '#1E293B'] : ['#F9FAFB', '#F1F5F9']}
                    style={styles.contentGradient}
                  >
                    <View style={styles.contentHeader}>
                      <Image 
                        source={{ uri: "https://i.imgur.com/rVLaO2e.png" }} 
                        style={styles.appLogo}
                        resizeMode="contain"
                      />
                      <Text style={[styles.appName, { color: colors.primary }]}>
                        {appName}
                      </Text>
                    </View>
                    
                    {contentImage ? (
                      <Image 
                        source={{ uri: contentImage }}
                        style={styles.contentImage}
                        resizeMode="cover"
                      />
                    ) : (
                      <View style={[styles.placeholderImage, { backgroundColor: isDark ? '#334155' : '#E2E8F0' }]} />
                    )}
                    
                    <View style={styles.contentDetails}>
                      <Text style={[styles.contentTitle, { color: colors.text }]}>
                        {getContentTitle()}
                      </Text>
                      
                      <Text style={[styles.contentDescription, { color: colors.textSecondary }]}>
                        {getContentDescription()}
                      </Text>
                      
                      <Text style={[styles.shareMessage, { color: isDark ? '#94A3B8' : '#64748B' }]}>
                        {message}
                      </Text>
                    </View>
                  </LinearGradient>
                </ViewShot>
              )}
            </View>
            
            <View style={styles.messageContainer}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Message
              </Text>
              
              <TextInput
                style={[
                  styles.messageInput,
                  { 
                    color: colors.text,
                    backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)',
                    borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'
                  }
                ]}
                value={message}
                onChangeText={setMessage}
                multiline
                placeholder="Add a message to your share..."
                placeholderTextColor={colors.textSecondary}
                maxLength={200}
              />
              
              <Text style={[styles.characterCount, { color: colors.textSecondary }]}>
                {message.length}/200
              </Text>
            </View>
            
            <View style={styles.shareOptions}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Share to
              </Text>
              
              <View style={styles.optionsGrid}>
                {sharingOptions.map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    style={[
                      styles.optionButton,
                      { backgroundColor: isDark ? '#1E293B' : '#F1F5F9' }
                    ]}
                    onPress={option.action}
                    accessibilityLabel={`Share to ${option.label}`}
                    accessibilityRole="button"
                  >
                    <View style={styles.optionIcon}>
                      {option.id === 'copy' && isCopied ? (
                        <Feather name="check" size={24}  color={colors.text} />
                      ) : (
                        option.icon
                      )}
                    </View>
                    <Text style={[styles.optionLabel, { color: colors.text }]}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[
                  styles.actionButton,
                  { backgroundColor: isDark ? '#334155' : '#F1F5F9' }
                ]}
                onPress={handleCapture}
                disabled={isSaving || isGeneratingImage}
                accessibilityLabel="Save image"
                accessibilityRole="button"
              >
                {isSaving ? (
                  <ActivityIndicator size="small" color={colors.primary} />
                ) : (
                  <>
                    <Feather name="download" size={20} color={colors.text} style={styles.buttonIcon} />
                    <Text style={[styles.buttonText, { color: colors.text }]}>
                      Save Image
                    </Text>
                  </>
                )}
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.actionButton,
                  { backgroundColor: colors.primary }
                ]}
                onPress={handleShare}
                disabled={isGeneratingImage}
                accessibilityLabel="Share now"
                accessibilityRole="button"
              >
                <Feather name="share-2" size={20} style={styles.buttonIcon} />
                <Text style={styles.shareButtonText}>
                  Share Now
                </Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </BlurView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    height: '90%',
    // Shadow for iOS
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -5 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    // Shadow for Android
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleIcon: {
    marginRight: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  previewContainer: {
    padding: 16,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  generatingPreview: {
    borderRadius: 12,
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  generatingText: {
    marginTop: 16,
    fontSize: 14,
  },
  shareableContent: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  contentGradient: {
    padding: 16,
  },
  contentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  appLogo: {
    width: 24,
    height: 24,
    marginRight: 8,
  },
  appName: {
    fontSize: 14,
    fontWeight: '600',
  },
  contentImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 16,
  },
  placeholderImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 16,
  },
  contentDetails: {
    marginBottom: 16,
  },
  contentTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  contentDescription: {
    fontSize: 14,
    marginBottom: 12,
  },
  shareMessage: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  messageContainer: {
    padding: 16,
    paddingTop: 0,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  messageInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  characterCount: {
    fontSize: 12,
    alignSelf: 'flex-end',
    marginTop: 4,
  },
  shareOptions: {
    padding: 16,
    paddingTop: 0,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  optionButton: {
    width: '48%',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  optionIcon: {
    marginBottom: 8,
    height: 24,
    justifyContent: 'center',
  },
  optionLabel: {
    fontSize: 14,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    paddingTop: 0,
  },
  actionButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    width: '48%',
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  shareButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  }
});

export default SocialSharingModal; 