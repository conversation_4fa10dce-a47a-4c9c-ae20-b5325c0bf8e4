import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  ActivityIndicator,
  Alert,
  Platform
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { BlurView } from 'expo-blur';
import { Feather } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

interface PlanTier {
  id: string;
  name: string;
  price: string;
  period: string;
  description: string;
  features: string[];
  mostPopular?: boolean;
  savePercent?: number;
}

interface SubscriptionModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSubscribe: (plan: PlanTier) => Promise<void>;
}

const SubscriptionModal: React.FC<SubscriptionModalProps> = ({
  isVisible,
  onClose,
  onSubscribe
}) => {
  const { colors, isDark } = useTheme();
  const [selectedPlanId, setSelectedPlanId] = useState<string>('monthly');
  const [isLoading, setIsLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState<'plans' | 'payment'>('plans');
  
  const subscriptionPlans: PlanTier[] = [
    {
      id: 'monthly',
      name: 'Monthly',
      price: '$9.99',
      period: 'per month',
      description: 'Ideal for trying out our premium features',
      features: [
        'All premium diet plans',
        'Weekly personalized meal plans',
        'Access to dietitian community',
        'Advanced nutrition insights'
      ],
      mostPopular: true
    },
    {
      id: 'quarterly',
      name: 'Quarterly',
      price: '$24.99',
      period: 'per 3 months',
      description: 'Great value for committed users',
      features: [
        'All premium diet plans',
        'Weekly personalized meal plans',
        'Access to dietitian community',
        'Advanced nutrition insights',
        'Email nutrition coaching'
      ],
      savePercent: 17
    },
    {
      id: 'annual',
      name: 'Annual',
      price: '$89.99',
      period: 'per year',
      description: 'Best value for long-term health journey',
      features: [
        'All premium diet plans',
        'Weekly personalized meal plans',
        'Access to dietitian community',
        'Advanced nutrition insights',
        'Email nutrition coaching',
        'Monthly dietitian video check-ins',
        'Premium recipe library'
      ],
      savePercent: 25
    }
  ];

  const handleSelectPlan = (planId: string) => {
    setSelectedPlanId(planId);
  };

  const handleContinue = () => {
    setCurrentStep('payment');
  };

  const handleSubscribe = async () => {
    try {
      const selectedPlan = subscriptionPlans.find(plan => plan.id === selectedPlanId);
      if (!selectedPlan) return;
      
      setIsLoading(true);
      await onSubscribe(selectedPlan);
      setIsLoading(false);
      onClose();
    } catch (error) {
      setIsLoading(false);
      Alert.alert(
        'Subscription Failed', 
        'There was an error processing your subscription. Please try again.'
      );
    }
  };
  
  const handleBack = () => {
    setCurrentStep('plans');
  };
  
  const getSelectedPlan = () => {
    return subscriptionPlans.find(plan => plan.id === selectedPlanId);
  };

  const renderPlansStep = () => {
    return (
      <>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={styles.content}
          contentContainerStyle={styles.scrollContent}
        >
          <View style={styles.headerContent}>
            <Crown size={32} color="#F59E0B" />
            <Text style={[styles.headerTitle, { color: colors.text }]}>
              Upgrade to Premium
            </Text>
            <Text style={[styles.headerDescription, { color: colors.textSecondary }]}>
              Unlock all our premium diet plans and get personalized nutrition guidance
            </Text>
          </View>
          
          <View style={styles.plansContainer}>
            {subscriptionPlans.map((plan) => (
              <TouchableOpacity
                key={plan.id}
                style={[
                  styles.planOption,
                  { 
                    backgroundColor: isDark ? '#1E293B' : '#FFFFFF',
                    borderColor: selectedPlanId === plan.id ? colors.primary : isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
                  }
                ]}
                onPress={() => handleSelectPlan(plan.id)}
                accessibilityRole="radio"
                accessibilityState={{ checked: selectedPlanId === plan.id }}
                accessibilityLabel={`${plan.name} plan, ${plan.price} ${plan.period}`}
              >
                <View style={styles.planHeader}>
                  <View>
                    <Text style={[styles.planName, { color: colors.text }]}>
                      {plan.name}
                    </Text>
                    <Text style={[styles.planDescription, { color: colors.textSecondary }]}>
                      {plan.description}
                    </Text>
                  </View>
                  
                  {plan.savePercent && (
                    <View style={styles.saveBadge}>
                      <Text style={styles.saveText}>
                        Save {plan.savePercent}%
                      </Text>
                    </View>
                  )}
                </View>
                
                <View style={styles.planPricing}>
                  <Text style={[styles.planPrice, { color: colors.text }]}>
                    {plan.price}
                  </Text>
                  <Text style={[styles.planPeriod, { color: colors.textSecondary }]}>
                    {plan.period}
                  </Text>
                </View>
                
                <View style={styles.planFeatures}>
                  {plan.features.map((feature, index) => (
                    <View key={index} style={styles.featureItem}>
                      <Feather name="check" size={16} color={colors.primary} style={styles.featureIcon} />
                      <Text style={[styles.featureText, { color: colors.text }]}>
                        {feature}
                      </Text>
                    </View>
                  ))}
                </View>
                
                {selectedPlanId === plan.id && (
                  <View style={[styles.selectedIndicator, { backgroundColor: colors.primary }]}>
                    <Feather name="check" size={16}  color={colors.text} />
                  </View>
                )}
                
                {plan.mostPopular && (
                  <View style={styles.popularBadge}>
                    <Text style={styles.popularText}>Most Popular</Text>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </View>
          
          <View style={styles.guaranteeContainer}>
            <Feather name="shield" size={20} color={colors.primary} style={styles.guaranteeIcon} />
            <Text style={[styles.guaranteeText, { color: colors.textSecondary }]}>
              30-day money-back guarantee. Cancel anytime.
            </Text>
          </View>
        </ScrollView>
        
        <View style={styles.footer}>
          <TouchableOpacity
            style={[styles.continueButton, { backgroundColor: colors.primary }]}
            onPress={handleContinue}
            accessibilityRole="button"
            accessibilityLabel="Continue to payment"
          >
            <Text style={styles.continueButtonText}>
              Continue
            </Text>
            <Feather name="chevron-right" size={20}  color={colors.text} />
          </TouchableOpacity>
        </View>
      </>
    );
  };
  
  const renderPaymentStep = () => {
    const selectedPlan = getSelectedPlan();
    
    if (!selectedPlan) return null;
    
    return (
      <>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={styles.content}
          contentContainerStyle={styles.scrollContent}
        >
          <View style={styles.paymentHeader}>
            <TouchableOpacity
              onPress={handleBack}
              style={styles.backButton}
              accessibilityRole="button"
              accessibilityLabel="Back to plan selection"
            >
              <Feather name="chevron-right" size={20} color={colors.text} style={{ transform: [{ rotate: '180deg' }] }} />
              <Text style={[styles.backButtonText, { color: colors.text }]}>
                Back
              </Text>
            </TouchableOpacity>
            
            <Text style={[styles.paymentTitle, { color: colors.text }]}>
              Complete Your Purchase
            </Text>
          </View>
          
          <View style={[styles.orderSummary, { backgroundColor: isDark ? '#1E293B' : '#F8FAFC' }]}>
            <Text style={[styles.summaryTitle, { color: colors.text }]}>
              Order Summary
            </Text>
            
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryItemName, { color: colors.text }]}>
                {selectedPlan.name} Plan
              </Text>
              <Text style={[styles.summaryItemPrice, { color: colors.text }]}>
                {selectedPlan.price}
              </Text>
            </View>
            
            {selectedPlan.savePercent && (
              <View style={styles.summaryRow}>
                <Text style={[styles.discountText, { color: '#10B981' }]}>
                  {selectedPlan.savePercent}% Discount
                </Text>
                <Text style={[styles.discountAmount, { color: '#10B981' }]}>
                  -${((9.99 * (selectedPlan.id === 'quarterly' ? 3 : 12)) * selectedPlan.savePercent / 100).toFixed(2)}
                </Text>
              </View>
            )}
            
            <View style={[styles.totalRow, { borderTopColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)' }]}>
              <Text style={[styles.totalText, { color: colors.text }]}>
                Total
              </Text>
              <Text style={[styles.totalAmount, { color: colors.text }]}>
                {selectedPlan.price}
              </Text>
            </View>
          </View>
          
          <View style={[styles.paymentMethods, { backgroundColor: isDark ? '#1E293B' : '#F8FAFC' }]}>
            <Text style={[styles.paymentMethodsTitle, { color: colors.text }]}>
              Payment Method
            </Text>
            
            <View style={[styles.paymentMethod, { borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)' }]}>
              <View style={styles.paymentMethodLeft}>
                <Feather name="credit-card" size={24} color={colors.text} style={styles.paymentIcon} />
                <Text style={[styles.paymentMethodText, { color: colors.text }]}>
                  Credit / Debit Card
                </Text>
              </View>
              <View style={[styles.paymentMethodSelected, { backgroundColor: colors.primary }]}>
                <Feather name="check" size={16}  color={colors.text} />
              </View>
            </View>
            
            <Text style={[styles.paymentNote, { color: colors.textSecondary }]}>
              *In the complete app, this would connect to a payment processor like Stripe to handle secure payment information.
            </Text>
          </View>
          
          <View style={styles.disclaimerContainer}>
            <Feather name="alert-circle" size={16} color={colors.textSecondary} style={styles.disclaimerIcon} />
            <Text style={[styles.disclaimerText, { color: colors.textSecondary }]}>
              By subscribing, you agree to our Terms of Service and automatically renew at the end of each billing period until cancellation.
            </Text>
          </View>
        </ScrollView>
        
        <View style={styles.footer}>
          <TouchableOpacity
            style={[styles.subscribeButton, { backgroundColor: colors.primary }]}
            onPress={handleSubscribe}
            disabled={isLoading}
            accessibilityRole="button"
            accessibilityLabel={`Subscribe to ${selectedPlan.name} plan for ${selectedPlan.price} ${selectedPlan.period}`}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <>
                <Text style={styles.subscribeButtonText}>
                  Subscribe for {selectedPlan.price}
                </Text>
                <Feather name="credit-card" size={20} style={styles.subscribeButtonIcon} />
              </>
            )}
          </TouchableOpacity>
        </View>
      </>
    );
  };

  return (
    <Modal
      transparent
      visible={isVisible}
      animationType="slide"
      onRequestClose={onClose}
    >
      <BlurView
        intensity={10}
        tint={isDark ? 'dark' : 'light'}
        style={styles.backdrop}
      >
        <View 
          style={[
            styles.modalContainer, 
            { backgroundColor: colors.background }
          ]}
        >
          <View style={styles.header}>
            <TouchableOpacity 
              style={[styles.closeButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
              onPress={onClose}
              accessibilityLabel="Close subscription modal"
              accessibilityRole="button"
            >
              <Feather name="x" size={20} color={colors.text} />
            </TouchableOpacity>
          </View>
          
          {currentStep === 'plans' ? renderPlansStep() : renderPaymentStep()}
        </View>
      </BlurView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    height: '90%',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -5 },
        shadowOpacity: 0.1,
        shadowRadius: 6,
      },
      android: {
        elevation: 5,
      },
      web: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -5 },
        shadowOpacity: 0.1,
        shadowRadius: 6,
      }
    }),
  },
  header: {
    paddingHorizontal: 16,
    paddingTop: 16,
    alignItems: 'flex-end',
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  headerContent: {
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  headerDescription: {
    fontSize: 16,
    textAlign: 'center',
    marginHorizontal: 24,
  },
  plansContainer: {
    marginBottom: 24,
  },
  planOption: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 2,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
      web: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 4,
      }
    }),
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  planName: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  planDescription: {
    fontSize: 14,
  },
  saveBadge: {
    backgroundColor: '#10B981',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  saveText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  planPricing: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 16,
  },
  planPrice: {
    fontSize: 24,
    fontWeight: '700',
    marginRight: 4,
  },
  planPeriod: {
    fontSize: 14,
  },
  planFeatures: {
    marginTop: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  featureIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  featureText: {
    fontSize: 14,
    flex: 1,
  },
  selectedIndicator: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  popularBadge: {
    position: 'absolute',
    top: -10,
    left: 16,
    backgroundColor: '#F59E0B',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  guaranteeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  guaranteeIcon: {
    marginRight: 8,
  },
  guaranteeText: {
    fontSize: 14,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.05)',
  },
  continueButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 14,
    borderRadius: 12,
  },
  continueButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  paymentHeader: {
    marginTop: 8,
    marginBottom: 24,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  backButtonText: {
    fontSize: 16,
    marginLeft: 4,
  },
  paymentTitle: {
    fontSize: 22,
    fontWeight: '700',
    textAlign: 'center',
  },
  orderSummary: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  summaryItemName: {
    fontSize: 16,
  },
  summaryItemPrice: {
    fontSize: 16,
    fontWeight: '600',
  },
  discountText: {
    fontSize: 16,
  },
  discountAmount: {
    fontSize: 16,
    fontWeight: '600',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 12,
    marginTop: 4,
    borderTopWidth: 1,
  },
  totalText: {
    fontSize: 18,
    fontWeight: '700',
  },
  totalAmount: {
    fontSize: 18,
    fontWeight: '700',
  },
  paymentMethods: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
  },
  paymentMethodsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  paymentMethod: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  paymentMethodLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentIcon: {
    marginRight: 12,
  },
  paymentMethodText: {
    fontSize: 16,
  },
  paymentMethodSelected: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  paymentNote: {
    fontSize: 12,
    fontStyle: 'italic',
    marginTop: 8,
  },
  disclaimerContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  disclaimerIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  disclaimerText: {
    fontSize: 12,
    flex: 1,
  },
  subscribeButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 14,
    borderRadius: 12,
  },
  subscribeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  subscribeButtonIcon: {
  },
});

export default SubscriptionModal; 