import React, { useState, useEffect, useCallback } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, ActivityIndicator, Animated, Platform } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { format } from 'date-fns';
import { generateDailyIntakeSummary, DailyIntakeSummary } from '@/services/dietitianService';
import { NutritionalSummary } from './NutritionalSummary';
import { Feather } from '@expo/vector-icons';
import { useTranslation } from '@/contexts/TranslationContext';
import { PremiumFeature } from './PremiumFeature';
import { LinearGradient } from 'expo-linear-gradient';
import { NutritionCharts } from './NutritionCharts';

interface DietitianSummaryProps {
  selectedDate?: string;
  onDateChange?: (date: string) => void;
  hasSubscription?: boolean;
}

export function DietitianSummary({ 
  selectedDate = format(new Date(), 'yyyy-MM-dd'),
  onDateChange,
  hasSubscription = false
}: DietitianSummaryProps) {
  const { colors, isDark } = useTheme();
  const { t } = useTranslation();
  const [summary, setSummary] = useState<DailyIntakeSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));
  
  // Format date for display
  const displayDate = new Date(selectedDate);
  const isToday = format(new Date(), 'yyyy-MM-dd') === selectedDate;
  
  useEffect(() => {
    fetchSummary();
  }, [selectedDate]);
  
  const fetchSummary = async () => {
    setLoading(true);
    fadeAnim.setValue(0);
    try {
      const data = await generateDailyIntakeSummary(selectedDate);
      setSummary(data);
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true
      }).start();
    } catch (error) {
      console.error('Error fetching summary:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handlePreviousDay = useCallback(() => {
    const prevDate = new Date(selectedDate);
    prevDate.setDate(prevDate.getDate() - 1);
    const formattedDate = format(prevDate, 'yyyy-MM-dd');
    if (onDateChange) {
      onDateChange(formattedDate);
    }
  }, [selectedDate, onDateChange]);
  
  const handleNextDay = useCallback(() => {
    const nextDate = new Date(selectedDate);
    nextDate.setDate(nextDate.getDate() + 1);
    // Don't allow selecting future dates
    if (nextDate <= new Date()) {
      const formattedDate = format(nextDate, 'yyyy-MM-dd');
      if (onDateChange) {
        onDateChange(formattedDate);
      }
    }
  }, [selectedDate, onDateChange]);
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.text }]}>
          Analyzing your nutrition data...
        </Text>
      </View>
    );
  }
  
  return (
    <ScrollView 
      style={styles.container}
      showsVerticalScrollIndicator={false}
    >
      <View style={[styles.dateSelector, {
        backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)',
        borderRadius: 12,
        padding: 8,
        marginBottom: 20,
      }]}>
        <TouchableOpacity 
          onPress={handlePreviousDay} 
          style={[styles.dateButton, {
            backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
          }]}
          accessibilityLabel="Previous day"
          accessibilityRole="button"
        >
          <Feather name="arrow-left" size={20} color={colors.text} />
        </TouchableOpacity>
        
        <View style={styles.dateContainer}>
          <Feather name="calendar" size={16} color={colors.primary} style={{ marginRight: 8 }} />
          <Text style={[styles.dateText, { color: colors.text }]}>
            {format(displayDate, 'MMMM d, yyyy')} {isToday && `(Today)`}
          </Text>
        </View>
        
        <TouchableOpacity 
          onPress={handleNextDay} 
          style={[styles.dateButton, { 
            opacity: isToday ? 0.5 : 1,
            backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
          }]}
          disabled={isToday}
          accessibilityLabel="Next day"
          accessibilityRole="button"
          accessibilityState={{ disabled: isToday }}
        >
          <Feather name="arrow-right" size={20} color={colors.text} />
        </TouchableOpacity>
      </View>
      
      {/* Use Platform-specific styling for better web compatibility */}
      {Platform.OS === 'web' ? (
        <View style={{ opacity: 1 }}>
          {summary ? (
            <>
              <LinearGradient
                colors={isDark 
                  ? ['#1E293B', '#0F172A'] 
                  : ['#EEF2FF', '#E0E7FF']}
                style={[styles.gradientHeader, {
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 8,
                  elevation: 3,
                }]}
              >
                <View style={styles.headerContent}>
                  <View style={styles.headerTextContainer}>
                    <Text style={[styles.headerTitle, { color: isDark ? '#FFFFFF' : '#312E81' }]}>
                      Your Daily Nutrition
                    </Text>
                    <Text style={[styles.headerSubtitle, { color: isDark ? '#94A3B8' : '#4F46E5' }]}>
                      {format(displayDate, 'MMMM d, yyyy')}
                    </Text>
                  </View>
                  <View style={styles.headerStats}>
                    <View style={styles.headerStatItem}>
                      <Text style={[styles.headerStatValue, { color: isDark ? '#FFFFFF' : '#312E81' }]}>
                        {summary.totalNutrition.calories}
                      </Text>
                      <Text style={[styles.headerStatLabel, { color: isDark ? '#94A3B8' : '#4F46E5' }]}>
                        Calories
                      </Text>
                    </View>
                    <View style={[styles.divider, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)' }]} />
                    <View style={styles.headerStatItem}>
                      <Text style={[styles.headerStatValue, { color: isDark ? '#FFFFFF' : '#312E81' }]}>
                        {Math.round(summary.percentOfGoals.calories)}%
                      </Text>
                      <Text style={[styles.headerStatLabel, { color: isDark ? '#94A3B8' : '#4F46E5' }]}>
                        of Goal
                      </Text>
                    </View>
                  </View>
                </View>
              </LinearGradient>
              
              {/* Nutrition Charts */}
              <NutritionCharts 
                nutritionData={summary.totalNutrition}
                dailyGoals={{
                  calories: 2000,
                  protein: 50,
                  carbs: 250,
                  fat: 70
                }}
                title="Nutrition Overview"
              />

              <PremiumFeature 
                featureKey="ai_nutrition_recommendations"
                title="Personalized Nutrition Advice"
                description="Get AI-powered recommendations based on your diet patterns and goals"
                forceAccess={hasSubscription}
              >
                <View style={[styles.recommendationsCard, { 
                  backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(255,255,255,1)',
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 8,
                  elevation: 3,
                }]}>
                  <View style={styles.recommendationsHeader}>
                    <Feather name="award" size={18} color={colors.primary} style={{ marginRight: 8 }} />
                    <Text style={[styles.recommendationsTitle, { color: colors.text }]}>
                      Recommendations
                    </Text>
                  </View>
                  
                  {summary.recommendations.length > 0 ? (
                    summary.recommendations.map((recommendation, index) => (
                      <View key={index} style={styles.recommendationItem}>
                        <View style={[styles.recommendationBullet, { backgroundColor: colors.primary }]} />
                        <Text style={[styles.recommendationText, { color: colors.text }]}>
                          {recommendation}
                        </Text>
                      </View>
                    ))
                  ) : (
                    <Text style={[styles.noDataText, { color: colors.textSecondary }]}>
                      No recommendations available for this day
                    </Text>
                  )}
                </View>
              </PremiumFeature>
              
              <TouchableOpacity style={[styles.mealLogButton, {
                backgroundColor: colors.primary,
                shadowColor: colors.primary,
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.2,
                shadowRadius: 8,
                elevation: 4,
              }]}>
                <Text style={styles.mealLogButtonText}>View Meal Details</Text>
                <Feather name="chevron-right" size={20}  color={colors.text} />
              </TouchableOpacity>
            </>
          ) : (
            <View style={[styles.noDataContainer, {
              backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(255,255,255,1)',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 3,
              borderRadius: 16,
              padding: 20,
            }]}>
              <Feather name="calendar" size={40} color={colors.primary} style={{ marginBottom: 16 }} />
              <Text style={[styles.noDataText, { color: colors.textSecondary }]}>
                No nutrition data available for this day
              </Text>
              <Text style={[styles.noDataSubtext, { color: colors.textSecondary }]}>
                Log your meals to see detailed nutrition analysis
              </Text>
              <TouchableOpacity
                style={[styles.logMealButton, { 
                  backgroundColor: colors.primary,
                  marginTop: 20,
                  paddingVertical: 12,
                  paddingHorizontal: 20,
                  borderRadius: 24,
                  shadowColor: colors.primary,
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.2,
                  shadowRadius: 8,
                  elevation: 4,
                }]}
                accessibilityLabel="Log a meal"
                accessibilityRole="button"
              >
                <Text style={{ color: 'white', fontWeight: '600', fontSize: 16 }}>
                  Log a Meal
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      ) : (
        <Animated.View style={{ opacity: fadeAnim }}>
          {summary ? (
            <>
              <LinearGradient
                colors={isDark 
                  ? ['#1E293B', '#0F172A'] 
                  : ['#EEF2FF', '#E0E7FF']}
                style={[styles.gradientHeader, {
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 8,
                  elevation: 3,
                }]}
              >
                <View style={styles.headerContent}>
                  <View style={styles.headerTextContainer}>
                    <Text style={[styles.headerTitle, { color: isDark ? '#FFFFFF' : '#312E81' }]}>
                      Your Daily Nutrition
                    </Text>
                    <Text style={[styles.headerSubtitle, { color: isDark ? '#94A3B8' : '#4F46E5' }]}>
                      {format(displayDate, 'MMMM d, yyyy')}
                    </Text>
                  </View>
                  <View style={styles.headerStats}>
                    <View style={styles.headerStatItem}>
                      <Text style={[styles.headerStatValue, { color: isDark ? '#FFFFFF' : '#312E81' }]}>
                        {summary.totalNutrition.calories}
                      </Text>
                      <Text style={[styles.headerStatLabel, { color: isDark ? '#94A3B8' : '#4F46E5' }]}>
                        Calories
                      </Text>
                    </View>
                    <View style={[styles.divider, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)' }]} />
                    <View style={styles.headerStatItem}>
                      <Text style={[styles.headerStatValue, { color: isDark ? '#FFFFFF' : '#312E81' }]}>
                        {Math.round(summary.percentOfGoals.calories)}%
                      </Text>
                      <Text style={[styles.headerStatLabel, { color: isDark ? '#94A3B8' : '#4F46E5' }]}>
                        of Goal
                      </Text>
                    </View>
                  </View>
                </View>
              </LinearGradient>
              
              {/* Nutrition Charts */}
              <NutritionCharts 
                nutritionData={summary.totalNutrition}
                dailyGoals={{
                  calories: 2000,
                  protein: 50,
                  carbs: 250,
                  fat: 70
                }}
                title="Nutrition Overview"
              />

              <PremiumFeature 
                featureKey="ai_nutrition_recommendations"
                title="Personalized Nutrition Advice"
                description="Get AI-powered recommendations based on your diet patterns and goals"
                forceAccess={hasSubscription}
              >
                <View style={[styles.recommendationsCard, { 
                  backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(255,255,255,1)',
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 8,
                  elevation: 3,
                }]}>
                  <View style={styles.recommendationsHeader}>
                    <Feather name="award" size={18} color={colors.primary} style={{ marginRight: 8 }} />
                    <Text style={[styles.recommendationsTitle, { color: colors.text }]}>
                      Recommendations
                    </Text>
                  </View>
                  
                  {summary.recommendations.length > 0 ? (
                    summary.recommendations.map((recommendation, index) => (
                      <View key={index} style={styles.recommendationItem}>
                        <View style={[styles.recommendationBullet, { backgroundColor: colors.primary }]} />
                        <Text style={[styles.recommendationText, { color: colors.text }]}>
                          {recommendation}
                        </Text>
                      </View>
                    ))
                  ) : (
                    <Text style={[styles.noDataText, { color: colors.textSecondary }]}>
                      No recommendations available for this day
                    </Text>
                  )}
                </View>
              </PremiumFeature>
              
              <TouchableOpacity style={[styles.mealLogButton, {
                backgroundColor: colors.primary,
                shadowColor: colors.primary,
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.2,
                shadowRadius: 8,
                elevation: 4,
              }]}>
                <Text style={styles.mealLogButtonText}>View Meal Details</Text>
                <Feather name="chevron-right" size={20}  color={colors.text} />
              </TouchableOpacity>
            </>
          ) : (
            <View style={[styles.noDataContainer, {
              backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(255,255,255,1)',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 3,
              borderRadius: 16,
              padding: 20,
            }]}>
              <Feather name="calendar" size={40} color={colors.primary} style={{ marginBottom: 16 }} />
              <Text style={[styles.noDataText, { color: colors.textSecondary }]}>
                No nutrition data available for this day
              </Text>
              <Text style={[styles.noDataSubtext, { color: colors.textSecondary }]}>
                Log your meals to see detailed nutrition analysis
              </Text>
              <TouchableOpacity
                style={[styles.logMealButton, { 
                  backgroundColor: colors.primary,
                  marginTop: 20,
                  paddingVertical: 12,
                  paddingHorizontal: 20,
                  borderRadius: 24,
                  shadowColor: colors.primary,
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.2,
                  shadowRadius: 8,
                  elevation: 4,
                }]}
                accessibilityLabel="Log a meal"
                accessibilityRole="button"
              >
                <Text style={{ color: 'white', fontWeight: '600', fontSize: 16 }}>
                  Log a Meal
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </Animated.View>
      )}
    </ScrollView>
  );
}

// Helper function to determine color based on percentage
function getColorForPercentage(percentage: number, inverseGoal = false): string {
  // For values where higher is better (like protein)
  if (inverseGoal) {
    if (percentage >= 90) return '#10b981'; // Green for good
    if (percentage >= 70) return '#f59e0b'; // Yellow for moderate
    return '#ef4444'; // Red for low
  } 
  // For values where moderation is key (like calories)
  else {
    if (percentage > 110) return '#ef4444'; // Red for over
    if (percentage > 90) return '#10b981'; // Green for on target
    if (percentage > 70) return '#f59e0b'; // Yellow for slightly under
    return '#ef4444'; // Red for way under
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  dateSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateButton: {
    padding: 10,
    borderRadius: 20,
  },
  dateContainer: {
    flex: 1,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  dateText: {
    fontSize: 16,
    fontWeight: '600',
  },
  gradientHeader: {
    borderRadius: 16,
    marginBottom: 20,
    overflow: 'hidden',
  },
  headerContent: {
    padding: 20,
  },
  headerTextContainer: {
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
  },
  headerStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerStatItem: {
    flex: 1,
    alignItems: 'center',
  },
  headerStatValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  headerStatLabel: {
    fontSize: 12,
  },
  divider: {
    width: 1,
    height: 40,
    marginHorizontal: 16,
  },
  recommendationsCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  recommendationsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  recommendationsTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  recommendationBullet: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginTop: 6,
    marginRight: 8,
  },
  recommendationText: {
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  noDataContainer: {
    marginTop: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noDataText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    textAlign: 'center',
  },
  noDataSubtext: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  logMealButton: {},
  mealLogButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  mealLogButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
}); 