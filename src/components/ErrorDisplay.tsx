import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { MaterialIcons , Feather } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

/**
 * Error types for different failure scenarios
 */
export type ErrorType = 
  | 'network' 
  | 'server' 
  | 'api' 
  | 'database' 
  | 'permission' 
  | 'validation' 
  | 'unknown';

/**
 * Properties for the ErrorDisplay component
 */
interface ErrorDisplayProps {
  message: string;
  type?: ErrorType;
  code?: string;
  details?: string;
  onRetry?: () => void;
  onDismiss?: () => void;
  compact?: boolean;
}

/**
 * A standardized error display component
 * Used throughout the app to provide consistent error feedback
 */
export function ErrorDisplay({
  message,
  type = 'unknown',
  code,
  details,
  onRetry,
  onDismiss,
  compact = false,
}: ErrorDisplayProps) {
  // Get appropriate icon based on error type
  const getIcon = () => {
    switch (type) {
      case 'network':
        return <Feather name="wifi-off"  size={20}  color={colors.text} />;
      case 'server':
        return <MaterialIcons name="error"  size={20}  color={colors.text} />;
      case 'database':
        return <Feather name="database"  size={20}  color={colors.text} />;
      case 'api':
        return <MaterialIcons name="swap-vert"  size={20}  color={colors.text} />;
      default:
        return <Feather name="alert-circle"  size={20}  color={colors.text} />;
    }
  };

  // Handle retry with haptic feedback
  const handleRetry = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    if (onRetry) onRetry();
  };

  if (compact) {
    return (
      <View style={styles.compactContainer}>
        {getIcon()}
        <Text style={styles.compactMessage}>{message}</Text>
        {onRetry && (
          <TouchableOpacity onPress={handleRetry} style={styles.compactButton}>
            <Feather name="refresh-cw" size={20}  color={colors.text} />
          </TouchableOpacity>
        )}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>{getIcon()}</View>
      <Text style={styles.message}>{message}</Text>
      {code && <Text style={styles.code}>Error code: {code}</Text>}
      {details && <Text style={styles.details}>{details}</Text>}
      <View style={styles.actionsContainer}>
        {onDismiss && (
          <TouchableOpacity onPress={onDismiss} style={[styles.button, styles.dismissButton]}>
            <Text style={styles.dismissButtonText}>Dismiss</Text>
          </TouchableOpacity>
        )}
        {onRetry && (
          <TouchableOpacity onPress={handleRetry} style={styles.button}>
            <Feather name="refresh-cw" size={18} style={styles.buttonIcon} />
            <Text style={styles.buttonText}>Try Again</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 20,
    margin: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#FFE8E8',
  },
  iconContainer: {
    backgroundColor: '#FFF2F2',
    borderRadius: 40,
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  message: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  code: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  details: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 8,
  },
  button: {
    backgroundColor: '#4287f5',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 120,
  },
  buttonText: {
    color: '#FFF',
    fontWeight: '600',
    fontSize: 14,
  },
  buttonIcon: {
    marginRight: 8,
  },
  dismissButton: {
    backgroundColor: '#f5f5f5',
    marginRight: 10,
  },
  dismissButtonText: {
    color: '#666',
    fontWeight: '600',
    fontSize: 14,
  },
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF2F2',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#FFE8E8',
  },
  compactMessage: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginLeft: 10,
  },
  compactButton: {
    padding: 6,
  },
});

/**
 * Error boundary component to catch errors in child components
 */
interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error to an error reporting service
    console.error('Error boundary caught an error:', error, errorInfo);
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      // Fallback UI when an error occurs
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      return (
        <ErrorDisplay
          message="Something went wrong"
          details={this.state.error?.message}
          onRetry={() => this.setState({ hasError: false })}
        />
      );
    }

    return this.props.children;
  }
} 