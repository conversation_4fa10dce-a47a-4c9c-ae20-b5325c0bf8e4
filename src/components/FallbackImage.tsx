import React, { useState, useEffect } from 'react';
import { View, Image, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';

interface FallbackImageProps {
  source: string | null | undefined;
  width?: number;
  height?: number;
  borderRadius?: number;
  fallbackText?: string;
  isAvatar?: boolean;
  containerStyle?: any;
  imageStyle?: any;
  textStyle?: any;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
}

/**
 * A simplified image component that handles loading failures gracefully
 */
export function FallbackImage({
  source,
  width = 100,
  height = 100,
  borderRadius = 0,
  fallbackText,
  isAvatar = false,
  containerStyle,
  imageStyle,
  textStyle,
  resizeMode = 'cover'
}: FallbackImageProps) {
  const { colors } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);
  
  // Check if source is valid
  const isValidSource = Boolean(source && typeof source === 'string' && source.trim() !== '');
  
  // Reset states when source changes
  useEffect(() => {
    if (isValidSource) {
      setHasError(false);
    }
  }, [source]);
  
  // Get display text for fallback
  const displayText = fallbackText || (isAvatar ? 'User' : '');
  const firstLetter = displayText && displayText.charAt(0).toUpperCase();
  
  const handleLoadStart = () => {
    setIsLoading(true);
  };
  
  const handleLoadSuccess = () => {
    setIsLoading(false);
  };
  
  const handleError = () => {
    console.log('Image failed to load:', source);
    setIsLoading(false);
    setHasError(true);
  };
  
  return (
    <View 
      style={[
        styles.container, 
        { 
          width, 
          height, 
          borderRadius 
        },
        containerStyle
      ]}
    >
      {isValidSource && !hasError ? (
        <>
          <Image
            source={{ uri: source as string }}
            style={[
              styles.image,
              { width, height, borderRadius },
              imageStyle
            ]}
            resizeMode={resizeMode}
            onLoadStart={handleLoadStart}
            onLoad={handleLoadSuccess}
            onError={handleError}
          />
          
          {isLoading && (
            <View style={[styles.loadingContainer, { width, height }]}>
              <ActivityIndicator color={colors.primary} size="small" />
            </View>
          )}
        </>
      ) : (
        <View 
          style={[
            styles.fallbackContainer, 
            { width, height, borderRadius },
            isAvatar && { backgroundColor: colors.primary }
          ]}
        >
          {isAvatar ? (
            <Text style={[styles.avatarText, { color: '#FFFFFF' }, textStyle]}>
              {firstLetter || '?'}
            </Text>
          ) : (
            <Feather name="user" color={colors.textSecondary} />
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.1)',
  },
  fallbackContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
  },
  avatarText: {
    fontSize: 24,
    fontWeight: 'bold',
  }
}); 