import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Switch,
  ScrollView,
  Alert,
  Modal,
  ActivityIndicator
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import {
  FastingType,
  FastingSchedule,
  ActiveFast,
  getPredefinedSchedules,
  getFastingSchedule,
  saveFastingSchedule,
  createCustomSchedule,
  getActiveFast,
  startFast,
  endFast,
  getCurrentFastingStatus,
  calculateFastingWindows
} from '@/services/fastingTrackerService';
import { Feather } from '@expo/vector-icons';
import { formatDistanceToNow } from 'date-fns';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, Easing } from 'react-native-reanimated';

export default function FastingTracker() {
  const { colors, isDark } = useTheme();
  const [loading, setLoading] = useState(true);
  const [fastingSchedule, setFastingSchedule] = useState<FastingSchedule | null>(null);
  const [activeFast, setActiveFast] = useState<ActiveFast | null>(null);
  const [fastingStatus, setFastingStatus] = useState<{ 
    inFastingWindow: boolean; 
    timeRemaining: number; 
    currentWindow: 'fasting' | 'eating';
  } | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  
  // Animated progress values
  const progressValue = useSharedValue(0);
  
  // Fetch data on mount
  useEffect(() => {
    loadData();
    
    // Refresh status every minute
    const interval = setInterval(() => {
      updateStatus();
    }, 60000);
    
    return () => clearInterval(interval);
  }, []);
  
  // Load all necessary data
  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load the fasting schedule
      const schedule = await getFastingSchedule();
      setFastingSchedule(schedule);
      
      // Load active fast if any
      const active = await getActiveFast();
      setActiveFast(active);
      
      // Get current fasting status
      await updateStatus();
    } catch (error) {
      console.error('Error loading fasting data:', error);
      Alert.alert('Error', 'Failed to load fasting data. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Update the fasting status
  const updateStatus = async () => {
    try {
      const status = await getCurrentFastingStatus();
      setFastingStatus(status);
      
      // Update progress animation
      if (activeFast) {
        const startTime = new Date(activeFast.startTime).getTime();
        const endTime = new Date(activeFast.expectedEndTime).getTime();
        const now = Date.now();
        const progress = (now - startTime) / (endTime - startTime);
        
        // Animate to the new progress value
        progressValue.value = withTiming(
          Math.min(1, Math.max(0, progress)),
          { duration: 1000, easing: Easing.bezier(0.25, 0.1, 0.25, 1) }
        );
      } else if (fastingSchedule && status) {
        // Calculate progress based on current window
        let progress = 0;
        if (status.timeRemaining > 0) {
          const totalMinutes = status.currentWindow === 'fasting' ? 
            fastingSchedule.fastingHours * 60 : 
            fastingSchedule.eatingHours * 60;
          
          progress = 1 - (status.timeRemaining / totalMinutes);
        }
        
        // Animate to the new progress value
        progressValue.value = withTiming(
          Math.min(1, Math.max(0, progress)),
          { duration: 1000, easing: Easing.bezier(0.25, 0.1, 0.25, 1) }
        );
      }
    } catch (error) {
      console.error('Error updating fasting status:', error);
    }
  };
  
  // Handle start fasting
  const handleStartFast = async () => {
    try {
      if (!fastingSchedule) {
        Alert.alert('No Schedule', 'Please set up a fasting schedule first.');
        setShowSettings(true);
        return;
      }
      
      const newFast = await startFast(fastingSchedule.type);
      if (newFast) {
        setActiveFast(newFast);
        updateStatus();
      } else {
        throw new Error('Failed to start fast');
      }
    } catch (error) {
      console.error('Error starting fast:', error);
      Alert.alert('Error', 'Failed to start fasting. Please try again.');
    }
  };
  
  // Handle end fasting
  const handleEndFast = async () => {
    try {
      if (!activeFast) return;
      
      // Confirm ending the fast
      Alert.alert(
        'End Fasting',
        'Are you sure you want to end your current fast?',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'End Fast', 
            style: 'destructive',
            onPress: async () => {
              const completedFast = await endFast();
              if (completedFast) {
                setActiveFast(null);
                updateStatus();
              } else {
                throw new Error('Failed to end fast');
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error ending fast:', error);
      Alert.alert('Error', 'Failed to end fasting. Please try again.');
    }
  };
  
  // Handle save schedule
  const handleSaveSchedule = async (schedule: FastingSchedule) => {
    try {
      const success = await saveFastingSchedule(schedule);
      if (success) {
        setFastingSchedule(schedule);
        setShowSettings(false);
        updateStatus();
      } else {
        throw new Error('Failed to save schedule');
      }
    } catch (error) {
      console.error('Error saving schedule:', error);
      Alert.alert('Error', 'Failed to save fasting schedule. Please try again.');
    }
  };
  
  // Format time remaining
  const formatTimeRemaining = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    } else {
      return `${mins}m`;
    }
  };
  
  // Format fasting window time
  const formatScheduleTime = (): string => {
    if (!fastingSchedule) return '';
    
    if (fastingSchedule.type === FastingType.FIVE_TWO) {
      const days = fastingSchedule.fastingDays?.map(day => {
        switch (day) {
          case 0: return 'Sun';
          case 1: return 'Mon';
          case 2: return 'Tue';
          case 3: return 'Wed';
          case 4: return 'Thu';
          case 5: return 'Fri';
          case 6: return 'Sat';
          default: return '';
        }
      }).join(', ');
      
      return `5:2 (${days || 'No days selected'})`;
    } else {
      return `${fastingSchedule.fastingHours}:${fastingSchedule.eatingHours}`;
    }
  };
  
  // Get color based on fasting status
  const getStatusColor = (): string => {
    if (!fastingStatus) return colors.textSecondary;
    
    return fastingStatus.currentWindow === 'fasting' ? 
      colors.primary : 
      colors.success;
  };
  
  // Animated styles for progress
  const progressStyle = useAnimatedStyle(() => {
    return {
      width: `${progressValue.value * 100}%`
    };
  });
  
  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? colors.card : 'white' }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading fasting tracker...
        </Text>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: isDark ? colors.card : 'white' }]}>
      {/* Header with schedule info */}
      <View style={styles.header}>
        <View style={styles.scheduleInfo}>
          <Text style={[styles.scheduleTitle, { color: colors.text }]}>
            {fastingSchedule ? 
              `${fastingSchedule.type} Fasting` : 
              'No Fasting Schedule'
            }
          </Text>
          {fastingSchedule && (
            <Text style={[styles.scheduleSubtitle, { color: colors.textSecondary }]}>
              {formatScheduleTime()}
            </Text>
          )}
        </View>
        
        <TouchableOpacity
          style={styles.settingsButton}
          onPress={() => setShowSettings(true)}
        >
          <Feather name="settings" size={20} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
      
      {/* Current status */}
      <View style={styles.statusContainer}>
        <View style={styles.progressCircle}>
          <View style={[styles.progressCircleInner, { borderColor: getStatusColor() }]}>
            <Text style={[styles.timeDisplay, { color: colors.text }]}>
              {fastingStatus ? 
                formatTimeRemaining(fastingStatus.timeRemaining) : 
                '0h 0m'
              }
            </Text>
            <Text style={[styles.timeLabel, { color: colors.textSecondary }]}>
              {fastingStatus ? 
                `${fastingStatus.currentWindow === 'fasting' ? 'Fasting' : 'Eating'} window` : 
                'Not active'
              }
            </Text>
          </View>
        </View>
        
        {/* Progress bar */}
        <View style={[styles.progressBarContainer, { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }]}>
          <Animated.View
            style={[
              styles.progressBar,
              { backgroundColor: getStatusColor() },
              progressStyle
            ]}
          />
        </View>
        
        {/* Status text */}
        <Text style={[styles.statusText, { color: colors.textSecondary }]}>
          {activeFast ? (
            <>Currently fasting for {formatDistanceToNow(new Date(activeFast.startTime))}</>
          ) : fastingStatus ? (
            <>
              {fastingStatus.currentWindow === 'fasting' ? 
                'Currently in fasting window' : 
                'Currently in eating window'
              }
            </>
          ) : (
            'No active fasting schedule'
          )}
        </Text>
      </View>
      
      {/* Action buttons */}
      <View style={styles.actionContainer}>
        {activeFast ? (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.danger }]}
            onPress={handleEndFast}
          >
            <StopCircle size={24} color="white" style={styles.actionIcon} />
            <Text style={styles.actionButtonText}>End Fast</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.primary }]}
            onPress={handleStartFast}
          >
            <PlayCircle size={24} color="white" style={styles.actionIcon} />
            <Text style={styles.actionButtonText}>Start Fast</Text>
          </TouchableOpacity>
        )}
      </View>
      
      {/* Tips section */}
      <View style={[styles.tipsContainer, { backgroundColor: isDark ? colors.subtle : '#f5f5f5' }]}>
        <View style={styles.tipHeader}>
          <Feather name="info" size={16} color={colors.textSecondary} style={styles.tipIcon} />
          <Text style={[styles.tipTitle, { color: colors.text }]}>Fasting Tips</Text>
        </View>
        <Text style={[styles.tipText, { color: colors.textSecondary }]}>
          Stay hydrated with water, black coffee, or tea during your fasting window. Avoid sweetened beverages that can break your fast.
        </Text>
      </View>
      
      {/* Settings Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={showSettings}
        onRequestClose={() => setShowSettings(false)}
      >
        <ScheduleSettingsModal
          currentSchedule={fastingSchedule}
          onSave={handleSaveSchedule}
          onClose={() => setShowSettings(false)}
        />
      </Modal>
    </View>
  );
}

// Schedule settings modal component
function ScheduleSettingsModal({ 
  currentSchedule, 
  onSave, 
  onClose 
}: { 
  currentSchedule: FastingSchedule | null, 
  onSave: (schedule: FastingSchedule) => void, 
  onClose: () => void 
}) {
  const { colors, isDark } = useTheme();
  const [selectedSchedule, setSelectedSchedule] = useState<FastingSchedule | null>(currentSchedule);
  const [customFastingHours, setCustomFastingHours] = useState(16);
  const [customEatingHours, setCustomEatingHours] = useState(8);
  const [enableNotifications, setEnableNotifications] = useState(currentSchedule?.enableNotifications ?? true);
  
  // Get predefined schedules
  const predefinedSchedules = getPredefinedSchedules();
  
  // Handle schedule selection
  const selectSchedule = (schedule: FastingSchedule) => {
    setSelectedSchedule(schedule);
  };
  
  // Handle save
  const handleSave = () => {
    if (!selectedSchedule) {
      Alert.alert('Error', 'Please select a fasting schedule.');
      return;
    }
    
    // Update notifications setting
    selectedSchedule.enableNotifications = enableNotifications;
    
    onSave(selectedSchedule);
  };
  
  // Create custom schedule
  const createCustomFast = () => {
    if (customFastingHours + customEatingHours !== 24) {
      Alert.alert('Invalid Schedule', 'Fasting and eating hours must add up to 24 hours.');
      return;
    }
    
    const customSchedule = createCustomSchedule(
      customFastingHours,
      customEatingHours,
      '20:00' // Default start time (8 PM)
    );
    
    setSelectedSchedule(customSchedule);
  };
  
  return (
    <View style={[styles.modalContainer, { backgroundColor: 'rgba(0, 0, 0, 0.5)' }]}>
      <View style={[styles.modalContent, { backgroundColor: isDark ? colors.background : 'white' }]}>
        <View style={styles.modalHeader}>
          <Text style={[styles.modalTitle, { color: colors.text }]}>Fasting Schedule</Text>
          <TouchableOpacity style={styles.modalCloseButton} onPress={onClose}>
            <Text style={[styles.modalCloseText, { color: colors.danger }]}>Cancel</Text>
          </TouchableOpacity>
        </View>
        
        <ScrollView style={styles.modalScrollView}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Choose a Schedule</Text>
          
          {/* Predefined schedules */}
          {predefinedSchedules.map((schedule, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.scheduleOption,
                { 
                  backgroundColor: selectedSchedule?.type === schedule.type ? 
                    (isDark ? colors.subtle : '#f0f0f0') : 
                    'transparent' 
                }
              ]}
              onPress={() => selectSchedule(schedule)}
            >
              <View style={styles.scheduleOptionContent}>
                <Text style={[styles.scheduleOptionTitle, { color: colors.text }]}>
                  {schedule.type}
                </Text>
                <Text style={[styles.scheduleOptionDescription, { color: colors.textSecondary }]}>
                  {schedule.type === FastingType.FIVE_TWO ? 
                    '5 days normal eating, 2 days calorie restricted' : 
                    `${schedule.fastingHours} hours fasting, ${schedule.eatingHours} hours eating`
                  }
                </Text>
              </View>
              
              {selectedSchedule?.type === schedule.type && (
                <Feather name="check" size={20} color={colors.primary} />
              )}
            </TouchableOpacity>
          ))}
          
          {/* Custom schedule option */}
          <View style={styles.customScheduleContainer}>
            <Text style={[styles.customScheduleTitle, { color: colors.text }]}>
              Custom Schedule
            </Text>
            
            <View style={styles.customScheduleInputs}>
              <View style={styles.customScheduleInput}>
                <Text style={[styles.customScheduleLabel, { color: colors.textSecondary }]}>
                  Fasting Hours
                </Text>
                <View style={styles.customHoursContainer}>
                  <TouchableOpacity
                    style={[styles.hourButton, { borderColor: colors.border }]}
                    onPress={() => setCustomFastingHours(Math.max(1, customFastingHours - 1))}
                  >
                    <Text style={[styles.hourButtonText, { color: colors.text }]}>-</Text>
                  </TouchableOpacity>
                  <Text style={[styles.hourText, { color: colors.text }]}>
                    {customFastingHours}
                  </Text>
                  <TouchableOpacity
                    style={[styles.hourButton, { borderColor: colors.border }]}
                    onPress={() => setCustomFastingHours(Math.min(23, customFastingHours + 1))}
                  >
                    <Text style={[styles.hourButtonText, { color: colors.text }]}>+</Text>
                  </TouchableOpacity>
                </View>
              </View>
              
              <View style={styles.customScheduleInput}>
                <Text style={[styles.customScheduleLabel, { color: colors.textSecondary }]}>
                  Eating Hours
                </Text>
                <View style={styles.customHoursContainer}>
                  <TouchableOpacity
                    style={[styles.hourButton, { borderColor: colors.border }]}
                    onPress={() => setCustomEatingHours(Math.max(1, customEatingHours - 1))}
                  >
                    <Text style={[styles.hourButtonText, { color: colors.text }]}>-</Text>
                  </TouchableOpacity>
                  <Text style={[styles.hourText, { color: colors.text }]}>
                    {customEatingHours}
                  </Text>
                  <TouchableOpacity
                    style={[styles.hourButton, { borderColor: colors.border }]}
                    onPress={() => setCustomEatingHours(Math.min(23, customEatingHours + 1))}
                  >
                    <Text style={[styles.hourButtonText, { color: colors.text }]}>+</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            
            <TouchableOpacity
              style={[styles.createCustomButton, { backgroundColor: colors.primary }]}
              onPress={createCustomFast}
            >
              <Text style={styles.createCustomButtonText}>Create Custom Schedule</Text>
            </TouchableOpacity>
          </View>
          
          {/* Settings */}
          <View style={styles.settingsSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Settings</Text>
            
            <View style={styles.settingRow}>
              <Text style={[styles.settingLabel, { color: colors.text }]}>
                Enable Notifications
              </Text>
              <Switch
                value={enableNotifications}
                onValueChange={setEnableNotifications}
                trackColor={{ false: '#767577', true: colors.primary }}
                thumbColor="#f4f3f4"
              />
            </View>
          </View>
        </ScrollView>
        
        <View style={styles.modalFooter}>
          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: colors.primary }]}
            onPress={handleSave}
          >
            <Text style={styles.saveButtonText}>Save Schedule</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    overflow: 'hidden',
    marginHorizontal: 16,
    marginVertical: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  loadingText: {
    marginTop: 16,
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  scheduleInfo: {
    flex: 1,
  },
  scheduleTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  scheduleSubtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  settingsButton: {
    padding: 8,
  },
  statusContainer: {
    alignItems: 'center',
    marginVertical: 16,
  },
  progressCircle: {
    width: 160,
    height: 160,
    borderRadius: 80,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 4,
  },
  progressCircleInner: {
    width: '100%',
    height: '100%',
    borderRadius: 76,
    borderWidth: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timeDisplay: {
    fontSize: 28,
    fontWeight: '700',
  },
  timeLabel: {
    fontSize: 14,
    marginTop: 4,
  },
  progressBarContainer: {
    width: '100%',
    height: 8,
    borderRadius: 4,
    marginTop: 24,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
  },
  statusText: {
    marginTop: 12,
    fontSize: 14,
    textAlign: 'center',
  },
  actionContainer: {
    marginVertical: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
  },
  actionIcon: {
    marginRight: 8,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  tipsContainer: {
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  tipHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tipIcon: {
    marginRight: 8,
  },
  tipTitle: {
    fontSize: 15,
    fontWeight: '600',
  },
  tipText: {
    fontSize: 14,
    lineHeight: 20,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingTop: 16,
    height: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalCloseButton: {
    padding: 4,
  },
  modalCloseText: {
    fontSize: 16,
  },
  modalScrollView: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  scheduleOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    marginBottom: 8,
  },
  scheduleOptionContent: {
    flex: 1,
  },
  scheduleOptionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  scheduleOptionDescription: {
    fontSize: 14,
  },
  customScheduleContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  customScheduleTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  customScheduleInputs: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  customScheduleInput: {
    flex: 1,
    alignItems: 'center',
  },
  customScheduleLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  customHoursContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hourButton: {
    width: 32,
    height: 32,
    borderWidth: 1,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  hourButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  hourText: {
    fontSize: 16,
    fontWeight: '600',
    marginHorizontal: 12,
    width: 24,
    textAlign: 'center',
  },
  createCustomButton: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  createCustomButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  settingsSection: {
    marginTop: 24,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  settingLabel: {
    fontSize: 16,
  },
  modalFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  saveButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
}); 