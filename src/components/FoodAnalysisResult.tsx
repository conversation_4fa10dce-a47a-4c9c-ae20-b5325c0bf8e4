import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, View, Text, Image, TouchableOpacity, ScrollView, Platform, StatusBar, SafeAreaView, Modal, TextInput, Alert, Animated, ViewStyle, TextStyle } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { NutritionalSummary } from './NutritionalSummary';
import { FoodItemsList } from './FoodItemsList';
import { explainNutritionalImpact } from '@/services/openaiService';
import { Svg, Circle, Text as SvgText, Line, Path } from 'react-native-svg';
import ARFoodOverlay from './ARFoodOverlay';
import { useTranslation } from '@/contexts/TranslationContext';
import { PremiumFeature } from './PremiumFeature';
import NutritionalExplanation, { NutritionalExplanationSkeleton } from './NutritionalExplanation';
import { Card, Divider } from 'react-native-paper';
import { formatDate } from '../utils/date';

interface FoodItem {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  confidence?: number;
  servingSize?: string;
  cookingMethod?: string;
  isFreshOrProcessed?: 'fresh' | 'processed' | 'mixed' | 'unknown';
  ingredients?: string[];
  estimatedAmount?: string;
  weightGrams?: number;
  volumeCm3?: number;
}

interface FoodAnalysisData {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  description?: string;
  items: FoodItem[];
  confidence?: number;
  totalWeightGrams?: number;
  totalVolumeCm3?: number;
  servingInfo?: {
    servingSize: string;
    totalServings: number;
    caloriesPerServing: number;
    proteinPerServing: number;
    carbsPerServing: number;
    fatPerServing: number;
    fiberPerServing?: number;
    sugarPerServing?: number;
    sodiumPerServing?: number;
    weightPerServing?: number;
  };
  healthHighlights?: {
    positives: string[];
    considerations: string[];
  };
  preparationMethod?: string;
  cuisineType?: string;
  mealType?: string;
  allergens?: string[];
  isPartial?: boolean;
  partialAnalysisMessage?: string;
}

interface AlternativeSource {
  source: string;
  data: FoodAnalysisData;
}

interface FoodAnalysisResultProps {
  data: FoodAnalysisData;
  imageUri: string;
  dailyCalorieGoal: number;
  onGenerateAlternative: (focus: FocusOption) => void;
  onSaveMeal: () => void;
  onScanNew: () => void;
  loading?: boolean;
  hasSubscription?: boolean;
  onDataUpdate?: (updatedData: FoodAnalysisData) => void;
  alternativeSources?: AlternativeSource[];
  onSave?: () => void;
  onShare?: () => void;
  onAddToMealPlan?: () => void;
  isSaved?: boolean;
  onClose?: () => void;
}

// Define available focus options
const FOCUS_OPTIONS = ["Balanced", "Low Carb", "High Protein", "Quick", "Low Fat"] as const;
type FocusOption = typeof FOCUS_OPTIONS[number];

interface MacroNutrientProps {
  protein: number;
  carbs: number;
  fat: number;
  size?: number;
}

function MacronutrientPieChart({ protein, carbs, fat, size = 150 }: MacroNutrientProps) {
  // Ensure values are non-negative numbers
  const safeProtein = Math.max(0, isNaN(protein) ? 0 : protein);
  const safeCarbs = Math.max(0, isNaN(carbs) ? 0 : carbs);
  const safeFat = Math.max(0, isNaN(fat) ? 0 : fat);
  
  const total = safeProtein + safeCarbs + safeFat;
  
  // Don't render if no data
  if (total === 0) {
    // Return empty state
    return (
      <View style={{ alignItems: 'center', marginVertical: 8 }}>
        <Svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
          <Circle cx={size/2} cy={size/2} r={size/2 - 10} fill="#f0f0f0" />
          <SvgText
            x={size/2}
            y={size/2}
            fontSize="14"
            fontWeight="bold"
            textAnchor="middle"
            alignmentBaseline="central"
            fill="#999999"
          >
            No Data
          </SvgText>
        </Svg>
        <Text style={{ fontSize: 12, color: '#999', marginTop: 10 }}>No macronutrient data available</Text>
      </View>
    );
  }

  // Calculate percentages (safely)
  const proteinPercentage = (safeProtein / total) * 100;
  const carbsPercentage = (safeCarbs / total) * 100;
  const fatPercentage = (safeFat / total) * 100;
  
  // Calculate angles for the pie chart
  const proteinAngle = (safeProtein / total) * 360;
  const carbsAngle = (safeCarbs / total) * 360;
  // Ensure the sum of angles doesn't exceed 360 due to rounding errors
  const fatAngle = Math.min(360 - proteinAngle - carbsAngle, (safeFat / total) * 360);
  
  // Calculate start and end angles for each segment
  const proteinStartAngle = 0;
  const proteinEndAngle = proteinStartAngle + proteinAngle;
  
  const carbsStartAngle = proteinEndAngle;
  const carbsEndAngle = carbsStartAngle + carbsAngle;
  
  const fatStartAngle = carbsEndAngle;
  const fatEndAngle = fatStartAngle + fatAngle;
  
  // Function to convert angle to point on the circle
  const polarToCartesian = (centerX: number, centerY: number, radius: number, angleInDegrees: number) => {
    // Ensure angle is a valid number
    const safeAngle = isNaN(angleInDegrees) ? 0 : angleInDegrees;
    const angleInRadians = (safeAngle - 90) * Math.PI / 180.0;
    return {
      x: centerX + (radius * Math.cos(angleInRadians)),
      y: centerY + (radius * Math.sin(angleInRadians))
    };
  };
  
  // Function to create a pie segment path
  const createArc = (centerX: number, centerY: number, radius: number, startAngle: number, endAngle: number) => {
    // Handle case where startAngle equals endAngle (would create invalid arc)
    if (Math.abs(endAngle - startAngle) < 0.5) {
      if (startAngle === 0) {
        return ''; // Don't draw anything for 0-degree arc
      }
      endAngle = startAngle + 0.5; // Add a minimum angle difference
    }
    
    const start = polarToCartesian(centerX, centerY, radius, endAngle);
    const end = polarToCartesian(centerX, centerY, radius, startAngle);
    const largeArcFlag = endAngle - startAngle <= 180 ? 0 : 1;
    
    return [
      `M ${centerX} ${centerY}`,
      `L ${start.x} ${start.y}`,
      `A ${radius} ${radius} 0 ${largeArcFlag} 0 ${end.x} ${end.y}`,
      'Z'
    ].join(' ');
  };
  
  const centerX = size / 2;
  const centerY = size / 2;
  const radius = size / 2 - 10;
  
  // Colors for each macro
  const proteinColor = '#3b82f6'; // blue
  const carbsColor = '#8b5cf6';   // purple
  const fatColor = '#f97316';     // orange
  
  // Format percentages to show on the chart
  const formatPercentage = (value: number) => {
    if (isNaN(value)) return '0%';
    return `${Math.round(value)}%`;
  };
  
  return (
    <View style={{ alignItems: 'center', marginVertical: 8 }}>
      <Svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
        {/* Protein segment */}
        {safeProtein > 0 && proteinAngle > 0.5 && (
          <Path
            d={createArc(centerX, centerY, radius, proteinStartAngle, proteinEndAngle)}
            fill={proteinColor}
          />
        )}
        
        {/* Carbs segment */}
        {safeCarbs > 0 && carbsAngle > 0.5 && (
          <Path
            d={createArc(centerX, centerY, radius, carbsStartAngle, carbsEndAngle)}
            fill={carbsColor}
          />
        )}
        
        {/* Fat segment */}
        {safeFat > 0 && fatAngle > 0.5 && (
          <Path
            d={createArc(centerX, centerY, radius, fatStartAngle, fatEndAngle)}
            fill={fatColor}
          />
        )}
        
        {/* Center circle (empty) */}
        <Circle cx={centerX} cy={centerY} r={radius / 2} fill="white" />
        
        {/* Total in the center */}
        <SvgText
          x={centerX}
          y={centerY}
          fontSize="14"
          fontWeight="bold"
          textAnchor="middle"
          alignmentBaseline="central"
          fill="#333333"
        >
          {total}g
        </SvgText>
      </Svg>
      
      {/* Legend */}
      <View style={{ flexDirection: 'row', justifyContent: 'space-around', width: '100%', marginTop: 10 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: proteinColor, marginRight: 5 }} />
          <Text style={{ fontSize: 12, color: proteinColor }}>Protein: {formatPercentage(proteinPercentage)}</Text>
        </View>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: carbsColor, marginRight: 5 }} />
          <Text style={{ fontSize: 12, color: carbsColor }}>Carbs: {formatPercentage(carbsPercentage)}</Text>
        </View>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: fatColor, marginRight: 5 }} />
          <Text style={{ fontSize: 12, color: fatColor }}>Fat: {formatPercentage(fatPercentage)}</Text>
        </View>
      </View>
    </View>
  );
}

interface MealScoreProps {
  protein: number;
  carbs: number;
  fat: number;
  fiber?: number;
  calories: number;
}

function MealScoreCard({ protein, carbs, fat, fiber = 0, calories }: MealScoreProps) {
  const { colors, isDark } = useTheme();
  
  // Calculate balance score (out of 10)
  const calculateBalanceScore = () => {
    // Ideal macronutrient ratios (approximate ranges)
    const idealProteinPercentage = 0.25; // ~25% from protein
    const idealCarbsPercentage = 0.50;   // ~50% from carbs
    const idealFatPercentage = 0.25;     // ~25% from fat
    
    // Calculate actual percentages
    const total = protein * 4 + carbs * 4 + fat * 9; // Convert to calories
    if (total === 0) return 5; // Default score for no data
    
    const proteinPercentage = (protein * 4) / total;
    const carbsPercentage = (carbs * 4) / total;
    const fatPercentage = (fat * 9) / total;
    
    // Calculate how far each macro is from ideal (as absolute percentage points)
    const proteinDeviation = Math.abs(proteinPercentage - idealProteinPercentage);
    const carbsDeviation = Math.abs(carbsPercentage - idealCarbsPercentage);
    const fatDeviation = Math.abs(fatPercentage - idealFatPercentage);
    
    // Average the deviations and convert to a score out of 10
    // Lower deviation = higher score
    const averageDeviation = (proteinDeviation + carbsDeviation + fatDeviation) / 3;
    const score = 10 - (averageDeviation * 20); // Scale to 0-10
    
    // Ensure score is between 1-10
    return Math.max(1, Math.min(10, Math.round(score)));
  };
  
  // Calculate portion score (out of 10)
  const calculatePortionScore = () => {
    // Assuming average meal should be around 500-700 calories
    if (calories < 200) return 6; // Too small
    if (calories > 1000) return 4; // Too large
    if (calories > 800) return 7; // Slightly large
    if (calories < 400) return 8; // Slightly small
    return 10; // Ideal range
  };
  
  // Calculate nutrient score (out of 10)
  const calculateNutrientScore = () => {
    // Check protein content
    const proteinScore = protein < 15 ? 4 : protein < 25 ? 7 : 10;
    
    // Check fiber content
    const fiberScore = fiber < 3 ? 4 : fiber < 6 ? 7 : 10;
    
    // Average the scores
    return Math.round((proteinScore + fiberScore) / 2);
  };
  
  // Calculate overall score
  const balanceScore = calculateBalanceScore();
  const portionScore = calculatePortionScore();
  const nutrientScore = calculateNutrientScore();
  
  const overallScore = Math.round((balanceScore + portionScore + nutrientScore) / 3);
  
  // Determine color based on score
  const getScoreColor = (score: number) => {
    if (score >= 8) return '#10b981'; // green
    if (score >= 6) return '#f59e0b'; // amber
    return '#ef4444'; // red
  };
  
  const scoreColor = getScoreColor(overallScore);
  
  // Get score message
  const getScoreMessage = () => {
    if (overallScore >= 9) return "Excellent balance of nutrients";
    if (overallScore >= 7) return "Good nutritional profile";
    if (overallScore >= 5) return "Average nutritional balance";
    return "Could be more nutritionally balanced";
  };
  
  // Function to determine text color to ensure contrast against background
  const getTextColor = (backgroundColor: string) => {
    // Convert hex to RGB
    const r = parseInt(backgroundColor.substring(1, 3), 16);
    const g = parseInt(backgroundColor.substring(3, 5), 16);
    const b = parseInt(backgroundColor.substring(5, 7), 16);
    
    // Calculate brightness (HSP formula)
    const brightness = Math.sqrt(
      0.299 * (r * r) + 
      0.587 * (g * g) + 
      0.114 * (b * b)
    );
    
    // Return white for dark backgrounds, black for light
    return brightness < 128 ? 'white' : 'black';
  };
  
  return (
    <View style={[styles.scoreCard, { 
      backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)',
      borderColor: colors.border 
    }]}>
      <View style={styles.scoreHeader}>
        <Text style={[styles.scoreTitle, { color: colors.text }]}>Meal Score</Text>
        <View style={[styles.scoreCircle, { backgroundColor: scoreColor }]}>
          <Text style={[styles.scoreValue, { color: getTextColor(scoreColor) }]}>{overallScore}</Text>
          <Text style={styles.scoreMax}>/10</Text>
        </View>
      </View>
      
      <Text style={[styles.scoreMessage, { color: colors.text }]}>
        {getScoreMessage()}
      </Text>
      
      <View style={styles.scoreBreakdown}>
        <View style={styles.scoreItemRow}>
          <Text style={[styles.scoreItemLabel, { color: colors.textSecondary }]}>
            Macro Balance
          </Text>
          <View style={styles.scoreItemValue}>
            {[...Array(10)].map((_, i) => (
              <View 
                key={i} 
                style={[
                  styles.scoreUnit, 
                  { 
                    backgroundColor: i < balanceScore ? scoreColor : isDark ? '#444' : '#e0e0e0' 
                  }
                ]} 
              />
            ))}
          </View>
        </View>
        
        <View style={styles.scoreItemRow}>
          <Text style={[styles.scoreItemLabel, { color: colors.textSecondary }]}>
            Portion Size
          </Text>
          <View style={styles.scoreItemValue}>
            {[...Array(10)].map((_, i) => (
              <View 
                key={i} 
                style={[
                  styles.scoreUnit, 
                  { 
                    backgroundColor: i < portionScore ? scoreColor : isDark ? '#444' : '#e0e0e0' 
                  }
                ]} 
              />
            ))}
          </View>
        </View>
        
        <View style={styles.scoreItemRow}>
          <Text style={[styles.scoreItemLabel, { color: colors.textSecondary }]}>
            Nutrient Content
          </Text>
          <View style={styles.scoreItemValue}>
            {[...Array(10)].map((_, i) => (
              <View 
                key={i} 
                style={[
                  styles.scoreUnit, 
                  { 
                    backgroundColor: i < nutrientScore ? scoreColor : isDark ? '#444' : '#e0e0e0' 
                  }
                ]} 
              />
            ))}
          </View>
        </View>
      </View>
    </View>
  );
}

interface NutritionTimelineProps {
  calories: number;
  dailyGoal: number;
  mealType: string;
}

function NutritionTimeline({ calories, dailyGoal, mealType = 'Meal' }: NutritionTimelineProps) {
  const { colors, isDark } = useTheme();
  
  // Estimate typical meal distribution throughout the day
  const mealDistribution = {
    'Breakfast': 0.25,
    'Lunch': 0.30,
    'Dinner': 0.35,
    'Snack': 0.10,
    'Meal': 0.30 // Default if mealType is unknown
  };
  
  // Get the expected percentage for this meal type
  const expectedPercentage = mealDistribution[mealType as keyof typeof mealDistribution] || mealDistribution['Meal'];
  const expectedCalories = dailyGoal * expectedPercentage;
  
  // Calculate the actual percentage of daily calories
  const actualPercentage = calories / dailyGoal;
  
  // Determine if the meal is within reasonable range (±20% of expected)
  const isReasonable = calories <= expectedCalories * 1.2 && calories >= expectedCalories * 0.8;
  
  // Format percentages for display
  const formatPercentage = (value: number) => `${Math.round(value * 100)}%`;
  
  // Determine the color based on whether the meal is reasonable
  const getStatusColor = () => {
    if (actualPercentage > expectedPercentage * 1.5) return '#ef4444'; // Significantly over
    if (actualPercentage > expectedPercentage * 1.2) return '#f59e0b'; // Moderately over
    if (actualPercentage < expectedPercentage * 0.7) return '#3b82f6'; // Under
    return '#10b981'; // Within reasonable range
  };
  
  const statusColor = getStatusColor();
  
  // Calculate remaining calories for the day
  const remainingCalories = dailyGoal - calories;
  const remainingPercentage = remainingCalories / dailyGoal;
  
  return (
    <View style={styles.timelineContainer}>
      <Text style={[styles.timelineTitle, { color: colors.text }]}>
        Daily Calorie Balance
      </Text>
      
      <View style={styles.timelineRow}>
        <View style={styles.timelineInfo}>
          <Text style={[styles.timelineLabel, { color: colors.textSecondary }]}>
            This {mealType}
          </Text>
          <Text style={[styles.timelineValue, { color: statusColor }]}>
            {calories} cal
          </Text>
          <Text style={[styles.timelineSubtext, { color: colors.textSecondary }]}>
            {formatPercentage(actualPercentage)} of daily goal
          </Text>
        </View>
        
        <View style={[styles.timelineBar, { backgroundColor: isDark ? '#333' : '#e5e5e5' }]}>
          <View 
            style={[
              styles.timelineFill, 
              { 
                backgroundColor: statusColor,
                width: `${Math.min(actualPercentage * 100, 100)}%` 
              }
            ]} 
          />
          <View 
            style={[
              styles.timelineExpected, 
              { 
                backgroundColor: isDark ? '#666' : '#bbb',
                left: `${expectedPercentage * 100}%` 
              }
            ]} 
          />
        </View>
      </View>
      
      <View style={styles.timelineRow}>
        <View style={styles.timelineInfo}>
          <Text style={[styles.timelineLabel, { color: colors.textSecondary }]}>
            Remaining
          </Text>
          <Text style={[styles.timelineValue, { color: colors.text }]}>
            {remainingCalories} cal
          </Text>
          <Text style={[styles.timelineSubtext, { color: colors.textSecondary }]}>
            {formatPercentage(remainingPercentage)} left for today
          </Text>
        </View>
        
        <View style={[styles.timelineBar, { backgroundColor: isDark ? '#333' : '#e5e5e5' }]}>
          <View 
            style={[
              styles.timelineFill, 
              { 
                backgroundColor: isDark ? 'rgba(255,255,255,0.15)' : 'rgba(0,0,0,0.05)',
                width: `${Math.min(remainingPercentage * 100, 100)}%` 
              }
            ]} 
          />
        </View>
      </View>
      
      <View style={styles.timelineTips}>
        {!isReasonable && actualPercentage > expectedPercentage && (
          <Text style={[styles.timelineTip, { color: colors.textSecondary }]}>
            💡 This {mealType.toLowerCase()} is higher in calories than typical. Consider balancing with lighter meals later.
          </Text>
        )}
        
        {!isReasonable && actualPercentage < expectedPercentage * 0.7 && (
          <Text style={[styles.timelineTip, { color: colors.textSecondary }]}>
            💡 This {mealType.toLowerCase()} is lighter than usual. You have plenty of calories left for other meals today.
          </Text>
        )}
      </View>
    </View>
  );
}

// Add the new Weight Information component
interface WeightInformationProps {
  totalWeightGrams?: number;
  totalVolumeCm3?: number;
  items?: FoodItem[];
}

function WeightInformation({ totalWeightGrams, totalVolumeCm3, items }: WeightInformationProps) {
  const { colors, isDark } = useTheme();
  
  // If no weight information is available
  if (!totalWeightGrams && !items?.some(item => item.weightGrams)) {
    return null;
  }
  
  const calculatedTotalWeight = items?.reduce((sum, item) => sum + (item.weightGrams || 0), 0) || 0;
  const displayWeight = totalWeightGrams || calculatedTotalWeight;
  
  return (
    <View style={[styles.weightInfoContainer, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)', borderColor: colors.border }]}>
      <View style={styles.weightInfoHeader}>
        <MaterialIcons name="fitness-center" size={22} color={colors.primary} />
        <Text style={[styles.weightInfoTitle, { color: colors.text }]}>
          Weight Analysis
        </Text>
      </View>
      
      <View style={styles.weightInfoContent}>
        {displayWeight > 0 && (
          <View style={styles.weightInfoRow}>
            <Text style={[styles.weightInfoLabel, { color: colors.textSecondary }]}>
              Total Weight:
            </Text>
            <Text style={[styles.weightInfoValue, { color: colors.text }]}>
              {displayWeight}g
            </Text>
          </View>
        )}
        
        {totalVolumeCm3 && totalVolumeCm3 > 0 && (
          <View style={styles.weightInfoRow}>
            <Text style={[styles.weightInfoLabel, { color: colors.textSecondary }]}>
              Total Volume:
            </Text>
            <Text style={[styles.weightInfoValue, { color: colors.text }]}>
              {totalVolumeCm3.toFixed(1)} cm³
            </Text>
          </View>
        )}
        
        {/* Show weight for each item if available */}
        {items?.some(item => item.weightGrams) && (
          <View style={styles.weightInfoItemsContainer}>
            <Text style={[styles.weightInfoSectionTitle, { color: colors.text }]}>
              Component Weights
            </Text>
            {items.filter(item => item.weightGrams).map((item, index) => (
              <View key={`weight-${index}`} style={styles.weightInfoItem}>
                <Text style={[styles.weightInfoItemName, { color: colors.text }]}>
                  {item.name}:
                </Text>
                <Text style={[styles.weightInfoItemWeight, { color: colors.primary }]}>
                  {item.weightGrams}g
                </Text>
              </View>
            ))}
          </View>
        )}
      </View>
    </View>
  );
}

export function FoodAnalysisResult({ 
  data, 
  imageUri, 
  dailyCalorieGoal, 
  onGenerateAlternative, 
  onSaveMeal,
  onScanNew,
  loading = false,
  hasSubscription = false,
  onDataUpdate,
  alternativeSources = [],
  onSave,
  onShare,
  onAddToMealPlan,
  isSaved = false,
  onClose,
}: FoodAnalysisResultProps) {
  const { colors, isDark } = useTheme();
  const { t } = useTranslation();
  const [showAR, setShowAR] = useState(false);
  const [expandDescription, setExpandDescription] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [activeSource, setActiveSource] = useState('primary');
  const [dataSourceModalVisible, setDataSourceModalVisible] = useState(false);
  const [nutritionalExplanation, setNutritionalExplanation] = useState<any>(null);
  const [loadingExplanation, setLoadingExplanation] = useState(false);
  const [editedName, setEditedName] = useState(data.name);
  const [selectedFocus, setSelectedFocus] = useState<FocusOption>("Balanced");
  const [expandedAltSource, setExpandedAltSource] = useState<string | null>(null);
  
  // Animation values
  const scaleAnim = useState(new Animated.Value(1))[0];
  const opacityAnim = useState(new Animated.Value(1))[0];
  
  if (!data) return null;
  
  // Handle transition to AR mode with animation
  const handleARToggle = () => {
    if (!showAR) {
      // Animate out current view
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 1.05,
          duration: 300,
          useNativeDriver: true
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true
        })
      ]).start(() => {
        // Once animation completes, show AR overlay
        setShowAR(true);
      });
    } else {
      // Just hide AR overlay, component handles its own exit animation
      setShowAR(false);
      
      // Reset animations for next time
      scaleAnim.setValue(1);
      opacityAnim.setValue(1);
    }
  };
  
  // Reset animations when AR overlay is closed
  useEffect(() => {
    if (!showAR) {
      // Animate view back in
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true
        })
      ]).start();
    }
  }, [showAR]);
  
  // Calculate percentage of daily goal
  const caloriePercentage = Math.round((data.calories / dailyCalorieGoal) * 100);
  
  const handleOverride = async () => {
    if (!editedName.trim()) {
      Alert.alert("Missing Information", "Please enter a name for the food");
      return;
    }
    
    try {
      // Create the updated data structure locally
      const updatedData = {
        ...data, // Spread the existing data
        name: editedName // Override the name
      };

      // Update the parent component with the new data
      if (onDataUpdate) {
        onDataUpdate(updatedData);
      }
      
      // Close the modal
      setShowEditModal(false);
      
      // Success feedback
      Alert.alert(
        "Food Updated", 
        `Successfully updated to: ${editedName}`,
        [{ text: "OK" }]
      );
    } catch (error) {
      Alert.alert(
        "Update Failed", 
        "There was an error updating the food information. Please try again."
      );
      console.error('Error overriding food data:', error);
    }
  };

  // Function to handle requesting nutritional explanation
  const handleExplainNutrition = async () => {
    // If we already have an explanation, just display it
    if (nutritionalExplanation) {
      return;
    }
    
    setLoadingExplanation(true);
    
    try {
      const result = await explainNutritionalImpact({
        name: data.name,
        calories: data.calories,
        protein: data.protein,
        carbs: data.carbs,
        fat: data.fat,
        fiber: data.servingInfo?.fiberPerServing,
        items: data.items
      });
      
      if (result.success && result.explanation) {
        setNutritionalExplanation(result.explanation);
        
        // Show a small toast notification if from cache
        if (result.fromCache) {
          // You could use a toast library here, but for simplicity
          // we'll just log it to console
          console.log('Nutritional explanation loaded from cache');
        }
      } else {
        throw new Error(result.error || 'Failed to generate nutritional explanation');
      }
    } catch (error) {
      console.error('Error generating nutritional explanation:', error);
      // Show a simple explanation for fallback
      setNutritionalExplanation({
        summary: `${data.name} provides ${data.calories} calories with ${data.protein}g protein, ${data.carbs}g carbs, and ${data.fat}g fat.`,
        healthBenefits: ['Provides energy and nutrients'],
        considerations: ['Balance with other meals throughout the day'],
        dietaryImplications: ['Supports your daily nutritional needs'],
        balanceAssessment: 'Check your overall macronutrient balance for the day.'
      });
    } finally {
      setLoadingExplanation(false);
    }
  };

  // Add this function to render alternative sources
  const renderAlternativeSources = () => {
    if (alternativeSources.length === 0) return null;
    
    return (
      <View style={[styles.section, { borderColor: colors.border }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Alternative Nutrition Data
        </Text>
        
        {alternativeSources.map((source, index) => (
          <View 
            key={`source-${index}`} 
            style={[
              // @ts-ignore - Known issue with TypeScript not recognizing these styles
              styles.alternativeSourceCard, 
              { 
                backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)',
                borderColor: colors.border
              }
            ]}
          >
            <TouchableOpacity
              // @ts-ignore - Known issue with TypeScript not recognizing these styles
              style={styles.alternativeSourceHeader}
              onPress={() => setExpandedAltSource(expandedAltSource === source.source ? null : source.source)}
            >
              <View 
                // @ts-ignore - Known issue with TypeScript not recognizing these styles
                style={styles.alternativeSourceInfo}>
                <Text 
                  // @ts-ignore - Known issue with TypeScript not recognizing these styles
                  style={[styles.alternativeSourceName, { color: colors.text }]}>
                  {source.source}
                </Text>
                <Text 
                  // @ts-ignore - Known issue with TypeScript not recognizing these styles
                  style={[styles.alternativeSourceSummary, { color: colors.textSecondary }]}>
                  {source.data.calories} calories · {source.data.protein}g protein
                </Text>
              </View>
              <Feather name="chevron-down" size={18} color={expandedAltSource === source.source ? colors.primary : colors.text} />
            </TouchableOpacity>
            
            {expandedAltSource === source.source && (
              <View 
                // @ts-ignore - Known issue with TypeScript not recognizing these styles
                style={styles.alternativeSourceDetails}>
                <Text style={{ fontSize: 14, color: colors.text }}>
                  This alternative option has {Math.abs(source.data.calories - data.calories)} fewer calories
                  and {Math.abs(source.data.protein - data.protein)}g {source.data.protein > data.protein ? 'more' : 'less'} protein.
                </Text>
                
                <View 
                  // @ts-ignore - Known issue with TypeScript not recognizing these styles
                  style={styles.alternativeSourceItems}>
                  <Text 
                    // @ts-ignore - Known issue with TypeScript not recognizing these styles
                    style={[styles.alternativeSourceItemsTitle, { color: colors.text }]}>
                    Food Items:
                  </Text>
                  {source.data.items.map((item, itemIndex) => (
                    <View 
                      // @ts-ignore - Known issue with TypeScript not recognizing these styles
                      key={`item-${itemIndex}`} 
                      style={styles.alternativeSourceItem}>
                      <Text 
                        // @ts-ignore - Known issue with TypeScript not recognizing these styles
                        style={[styles.alternativeSourceItemName, { color: colors.text }]}>
                        {item.name}
                      </Text>
                      <Text 
                        // @ts-ignore - Known issue with TypeScript not recognizing these styles
                        style={[styles.alternativeSourceItemCalories, { color: colors.textSecondary }]}>
                        {item.calories} cal
                      </Text>
                    </View>
                  ))}
                </View>
                
                <TouchableOpacity
                  // @ts-ignore - Known issue with TypeScript not recognizing these styles
                  style={[styles.alternativeSourceUseButton, { backgroundColor: colors.primary }]}
                  onPress={() => {
                    if (onDataUpdate) {
                      onDataUpdate(source.data);
                    }
                  }}
                >
                  {/* @ts-ignore - Known issue with TypeScript not recognizing these styles */}
                  <Text style={styles.alternativeSourceUseButtonText}>
                    Use This Alternative
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        ))}
      </View>
    );
  };

  const renderConfidenceIndicator = () => {
    if (data.confidence === undefined) return null;
    
    const confidencePercentage = Math.round(data.confidence * 100);
    const getConfidenceColor = () => {
      if (confidencePercentage >= 80) return "#10b981"; // green
      if (confidencePercentage >= 65) return "#f59e0b"; // amber
      return "#ef4444"; // red
    };
    
    return (
      <View style={[styles.confidenceContainer, { borderColor: colors.border }]}>
        <Text style={[styles.confidenceLabel, { color: colors.textSecondary }]}>
          Analysis Confidence
        </Text>
        <View style={styles.confidenceRow}>
          <View style={[styles.confidenceBar, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
            <View 
              style={[
                styles.confidenceFill, 
                { 
                  backgroundColor: getConfidenceColor(),
                  width: `${confidencePercentage}%`
                }
              ]} 
            />
          </View>
          <Text style={[styles.confidenceValue, { color: getConfidenceColor() }]}>
            {confidencePercentage}%
          </Text>
        </View>
        
        {confidencePercentage < 65 && (
          <Text style={[styles.confidenceWarning, { color: colors.warning }]}>
            Low confidence result. Consider retaking the photo with better lighting or clearer view of food.
          </Text>
        )}
      </View>
    );
  };

  const renderEnhancedFoodItem = (item: FoodItem, index: number) => {
    return (
      <View key={`fooditem-${index}`} style={[styles.foodItem, { borderBottomColor: colors.border }]}>
        <View style={styles.foodItemHeader}>
          <Text style={[styles.foodItemName, { color: colors.text }]}>
            {item.name} {item.estimatedAmount && <Text style={{ fontWeight: 'normal' }}>({item.estimatedAmount})</Text>}
          </Text>
          <View style={styles.foodItemMetrics}>
            {/* Show weight if available */}
            {item.weightGrams && (
              <Text style={[styles.foodItemWeight, { color: colors.primary }]}>
                {item.weightGrams}g
              </Text>
            )}
            <Text style={[styles.foodItemCalories, { color: colors.primary }]}>
              {item.calories} cal
            </Text>
          </View>
        </View>
        
        <View style={styles.foodItemDetails}>
          <View style={styles.macroValues}>
            <Text style={[styles.macroValue, { color: colors.text }]}>
              {item.protein}g protein
            </Text>
            <Text style={[styles.macroValue, { color: colors.text }]}>
              {item.carbs}g carbs
            </Text>
            <Text style={[styles.macroValue, { color: colors.text }]}>
              {item.fat}g fat
            </Text>
          </View>
          
          {/* Add volume if available */}
          {item.volumeCm3 && (
            <Text style={[styles.volumeText, { color: colors.textSecondary }]}>
              Volume: {item.volumeCm3.toFixed(1)} cm³
            </Text>
          )}
          
          {(item.cookingMethod || item.isFreshOrProcessed) && (
            <View style={styles.foodAttributes}>
              {item.cookingMethod && (
                <View style={[styles.attributeTag, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
                  <Text style={[styles.attributeText, { color: colors.text }]}>
                    {item.cookingMethod.charAt(0).toUpperCase() + item.cookingMethod.slice(1)}
                  </Text>
                </View>
              )}
              
              {item.isFreshOrProcessed && (
                <View style={[
                  styles.attributeTag, 
                  { 
                    backgroundColor: 
                      item.isFreshOrProcessed === 'fresh' ? 'rgba(16, 185, 129, 0.2)' :
                      item.isFreshOrProcessed === 'processed' ? 'rgba(239, 68, 68, 0.2)' :
                      'rgba(245, 158, 11, 0.2)'
                  }
                ]}>
                  <Text style={[
                    styles.attributeText, 
                    { 
                      color: 
                        item.isFreshOrProcessed === 'fresh' ? '#10b981' :
                        item.isFreshOrProcessed === 'processed' ? '#ef4444' :
                        '#f59e0b'
                    }
                  ]}>
                    {item.isFreshOrProcessed.charAt(0).toUpperCase() + item.isFreshOrProcessed.slice(1)}
                  </Text>
                </View>
              )}
              
              {item.confidence !== undefined && (
                <View style={[styles.attributeTag, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
                  <Text style={[styles.attributeText, { color: colors.text }]}>
                    {Math.round(item.confidence * 100)}% confidence
                  </Text>
                </View>
              )}
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderInfo = () => {
    // Keep the original description from AI
    const fullDescription = data.description;

    return (
      <View style={styles.infoContainer}>
        {/* Add confidence indicator */}
        {renderConfidenceIndicator()}
        
        {/* Add Weight Information component */}
        <WeightInformation 
          totalWeightGrams={data.totalWeightGrams} 
          totalVolumeCm3={data.totalVolumeCm3}
          items={data.items}
        />
        
        {/* *** Add check for partial data *** */}
        {data.isPartial && (
          <View style={styles.partialDataWarning}>
            <Feather name="info" size={16} color={colors.warning} style={{ marginRight: 8 }} />
            <Text style={[styles.partialDataText, { color: colors.warning }]}>
              {data.partialAnalysisMessage || "Analysis is still processing. Results may be refined."}
            </Text>
          </View>
        )}
        
        {/* Nutritional Summary */}
        <NutritionalSummary
          calories={data.calories}
          protein={data.protein}
          carbs={data.carbs}
          fat={data.fat}
          dailyGoal={dailyCalorieGoal}
        />
        
        {/* Macronutrient breakdown */}
        <Text style={[styles.sectionTitle, { color: colors.text, marginTop: 16 }]}>
          {t('macronutrients')}
        </Text>
        <MacronutrientPieChart
          protein={data.protein}
          carbs={data.carbs}
          fat={data.fat}
          size={180} // Larger pie chart
        />
        
        {/* Meal score card */}
        <MealScoreCard
          protein={data.protein}
          carbs={data.carbs}
          fat={data.fat}
          fiber={data.servingInfo?.fiberPerServing}
          calories={data.calories}
        />
        
        {/* Daily timeline */}
        <NutritionTimeline
          calories={data.calories}
          dailyGoal={dailyCalorieGoal}
          mealType={data.mealType || 'Meal'}
        />
        
        {/* Nutritional Explanation Button */}
        {!nutritionalExplanation && !loadingExplanation && (
          <TouchableOpacity 
            style={[styles.explanationButton, { backgroundColor: colors.primary }]}
            onPress={handleExplainNutrition}
          >
            <Feather name="info" size={18} style={{ marginRight: 8 }} />
            <Text style={styles.explanationButtonText}>
              Explain Health Impact
            </Text>
          </TouchableOpacity>
        )}
        
        {/* Show skeleton while loading */}
        {loadingExplanation && <NutritionalExplanationSkeleton />}
        
        {/* Show explanation when available */}
        {nutritionalExplanation && <NutritionalExplanation explanation={nutritionalExplanation} />}
        
        {/* Description section, if available */}
        {fullDescription && (
          <View style={styles.descriptionContainer}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              {t('about')}
            </Text>
            <Text style={[styles.description, { color: colors.text }]}>
              {expandDescription ? fullDescription : (fullDescription.length > 150 ? fullDescription.substring(0, 150) + '...' : fullDescription)}
            </Text>
            
            {fullDescription.length > 150 && (
              <TouchableOpacity 
                onPress={() => setExpandDescription(!expandDescription)}
                style={styles.expandButton}
              >
                <Text style={[styles.expandButtonText, { color: colors.primary }]}>
                  {expandDescription ? t('showLess') : t('readMore')}
                </Text>
                {expandDescription ? (
                  <Feather name="chevron-up" size={16} color={colors.primary} />
                ) : (
                  <Feather name="chevron-down" size={16} color={colors.primary} />
                )}
              </TouchableOpacity>
            )}
          </View>
        )}
        
        {/* Food items list - Use enhanced component */}
        {data.items && data.items.length > 0 && (
          <View style={{ marginTop: 20 }}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              {t('components')}
            </Text>
            <View style={styles.foodItemsList}>
              {data.items.map((item, index) => renderEnhancedFoodItem(item, index))}
            </View>
          </View>
        )}
        
        {/* Health highlights */}
        {data.healthHighlights && (
          <View style={{ marginTop: 20 }}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              {t('healthHighlights')}
            </Text>
            
            {data.healthHighlights.positives && data.healthHighlights.positives.length > 0 && (
              <View style={styles.highlightsSection}>
                <Text style={[styles.highlightsSectionTitle, { color: colors.text }]}>
                  {t('positives')}
                </Text>
                {data.healthHighlights.positives.map((positive, index) => (
                  <View key={index} style={styles.benefitItem}>
                    <View style={[styles.bullet, { backgroundColor: "#22c55e" }]} />
                    <Text style={[styles.benefitText, { color: colors.text }]}>
                      {positive}
                    </Text>
                  </View>
                ))}
              </View>
            )}
            
            {data.healthHighlights.considerations && data.healthHighlights.considerations.length > 0 && (
              <View style={styles.highlightsSection}>
                <Text style={[styles.highlightsSectionTitle, { color: colors.text }]}>
                  {t('considerations')}
                </Text>
                {data.healthHighlights.considerations.map((consideration, index) => (
                  <View key={index} style={styles.benefitItem}>
                    <View style={[styles.bullet, { backgroundColor: "#f59e0b" }]} />
                    <Text style={[styles.benefitText, { color: colors.text }]}>
                      {consideration}
                    </Text>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}
        
        {/* Serving information */}
        {data.servingInfo && (
          <View style={{ marginTop: 20 }}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              {t('servingInfo')}
            </Text>
            <View style={[styles.servingInfoCard, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)' }]}>
              <Text style={[styles.servingInfoText, { color: colors.text }]}>
                {t('servingSize')}: {data.servingInfo.servingSize}
              </Text>
              <Text style={[styles.servingInfoText, { color: colors.text }]}>
                {t('totalServings')}: {data.servingInfo.totalServings}
              </Text>
              <Text style={[styles.servingInfoText, { color: colors.text }]}>
                {t('perServing')}: {data.servingInfo.caloriesPerServing} {t('calories')}, {data.servingInfo.proteinPerServing}g {t('protein')}, {data.servingInfo.carbsPerServing}g {t('carbs')}, {data.servingInfo.fatPerServing}g {t('fat')}
              </Text>
              
              {data.servingInfo.fiberPerServing !== undefined && (
                <Text style={[styles.servingInfoText, { color: colors.text }]}>
                  {t('fiber')}: {data.servingInfo.fiberPerServing}g
                </Text>
              )}
              
              {data.servingInfo.sugarPerServing !== undefined && (
                <Text style={[styles.servingInfoText, { color: colors.text }]}>
                  {t('sugar')}: {data.servingInfo.sugarPerServing}g
                </Text>
              )}
              
              {data.servingInfo.sodiumPerServing !== undefined && (
                <Text style={[styles.servingInfoText, { color: colors.text }]}>
                  {t('sodium')}: {data.servingInfo.sodiumPerServing}mg
                </Text>
              )}
            </View>
          </View>
        )}
      </View>
    );
  };

  const renderEditModal = () => {
    return (
      <Modal
        visible={showEditModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowEditModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Edit Food Information
            </Text>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowEditModal(false)}
            >
              <Feather name="x" size={24} color={colors.text} />
            </TouchableOpacity>
            <View 
              // @ts-ignore - Known issue with TypeScript not recognizing these styles
              style={styles.editModalContent}>
              <Text 
                // @ts-ignore - Known issue with TypeScript not recognizing these styles
                style={[styles.editModalLabel, { color: colors.text }]}>Food Name</Text>
              <TextInput
                style={[
                  // @ts-ignore - Known issue with TypeScript not recognizing these styles
                  styles.editModalInput,
                  {
                    backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                    color: colors.text,
                    borderColor: colors.border
                  }
                ]}
                value={editedName}
                onChangeText={setEditedName}
                placeholder="Enter food name"
                placeholderTextColor={colors.textSecondary}
              />
              <TouchableOpacity
                // @ts-ignore - Known issue with TypeScript not recognizing these styles
                style={[styles.editModalButton, { backgroundColor: colors.primary }]}
                onPress={handleOverride}
              >
                {/* @ts-ignore - Known issue with TypeScript not recognizing these styles */}
                <Text style={styles.editModalButtonText}>Update Food Info</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  const handleGenerateAlternative = () => {
    onGenerateAlternative(selectedFocus);
  };

  const renderActions = () => {
    return (
      <View style={styles.actionsContainer}>
        <View style={styles.buttonsRow}>
          <TouchableOpacity 
            style={[
              styles.primaryButton, 
              { 
                backgroundColor: colors.primary,
                opacity: loading || data.isPartial ? 0.6 : 1
              }
            ]} 
            onPress={onSaveMeal}
            disabled={loading || data.isPartial}
          >
            <CircleCheck size={20} color="white" />
            <Text style={styles.primaryButtonText}>{t('foodAnalysis.saveMeal')}</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.buttonsRow}>
          <TouchableOpacity 
            style={[
              styles.secondaryButton, 
              { 
                borderColor: colors.border,
                backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'white',
                opacity: loading || data.isPartial ? 0.6 : 1 
              }
            ]}
            onPress={() => onGenerateAlternative("Balanced")}
            disabled={loading || data.isPartial}
          >
            <Sparkles size={18} color={colors.primary} />
            <Text style={[styles.secondaryButtonText, { color: colors.text }]}>
              Healthier Alternative
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // Handle close action
  const handleClose = () => {
    // Use onClose if provided, otherwise fallback to onScanNew
    if (onClose) {
      onClose();
    } else {
      onScanNew();
    }
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} backgroundColor={colors.background} />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Close button */}
        <TouchableOpacity 
          style={[styles.closeButton, { backgroundColor: 'rgba(0,0,0,0.5)' }]}
          onPress={handleClose}
        >
          <Feather name="x" size={20}  color={colors.text} />
        </TouchableOpacity>
        
        <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
          {/* Food image at the top with larger height */}
          <View style={styles.imageContainer}>
            <Image 
              source={{ uri: imageUri }}
              style={styles.foodImage}
              resizeMode="cover"
            />
            {/* AR button as an overlay on the image */}
            <TouchableOpacity 
              style={[styles.arButtonOverlay, { backgroundColor: showAR ? colors.primary : 'rgba(0,0,0,0.6)' }]}
              onPress={handleARToggle}
            >
              <MaterialIcons name="view-in-ar" size={20} color="#fff" />
              <Text style={styles.arButtonOverlayText}>AR View</Text>
            </TouchableOpacity>

            {/* Save button overlay on the image */}
            <TouchableOpacity 
              style={[styles.saveButtonOverlay, { backgroundColor: isSaved ? colors.primary : 'rgba(0,0,0,0.6)' }]}
              onPress={onSaveMeal}
              disabled={loading || data.isPartial}
            >
              <Feather name="save" size={20}  color={colors.text} />
            </TouchableOpacity>
          </View>
          
          {/* Food name and details with edit button */}
          <View style={styles.titleSection}>
            <View style={styles.titleRow}>
              <Text style={[styles.foodTitle, { color: colors.text }]}>{data.name}</Text>
              <TouchableOpacity 
                style={styles.editButton}
                onPress={() => {
                  setEditedName(data.name);
                  setShowEditModal(true);
                }}
              >
                <Feather name="edit-2" size={20} color={colors.primary} />
              </TouchableOpacity>
            </View>
            <Text style={[styles.calorieDisplay, { color: colors.primary }]}>
              {data.calories} calories
            </Text>
            {/* Add macro nutrients summary directly in title section */}
            <View style={styles.macroSummary}>
              <Text style={[styles.macroSummaryText, { color: colors.text }]}>
                {data.protein}g protein
              </Text>
              <Text style={styles.macroSeparator}>•</Text>
              <Text style={[styles.macroSummaryText, { color: colors.text }]}>
                {data.carbs}g carbs
              </Text>
              <Text style={styles.macroSeparator}>•</Text>
              <Text style={[styles.macroSummaryText, { color: colors.text }]}>
                {data.fat}g fat
              </Text>
            </View>
          </View>
          
          {renderInfo()}
          
          {/* Simple action buttons at the bottom */}
          <View style={styles.actionsContainer}>
            <TouchableOpacity 
              style={[
                styles.secondaryButton, 
                { 
                  borderColor: colors.border,
                  backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'white',
                  opacity: loading || data.isPartial ? 0.6 : 1 
                }
              ]}
              onPress={() => onGenerateAlternative("Balanced")}
              disabled={loading || data.isPartial}
            >
              <Sparkles size={18} color={colors.primary} />
              <Text style={[styles.secondaryButtonText, { color: colors.text }]}>
                Healthier Alternative
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
        
        {renderEditModal()}
        
        {showAR && (
          <ARFoodOverlay
            foodData={{
              name: data.name,
              calories: data.calories,
              protein: data.protein,
              carbs: data.carbs,
              fat: data.fat,
              items: data.items
            }}
            onClose={() => setShowAR(false)}
          />
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  infoContainer: {
    padding: 16,
  },
  imageContainer: {
    width: '100%',
    height: 400,
    overflow: 'hidden',
    position: 'relative',
  },
  foodImage: {
    width: '100%',
    height: '100%',
  },
  arButtonOverlay: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  arButtonOverlayText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 8,
  },
  saveButtonOverlay: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  titleSection: {
    padding: 16,
    paddingTop: 20,
    paddingBottom: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  foodTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
    flex: 1,
  },
  editButton: {
    padding: 8,
    marginLeft: 10,
  },
  calorieDisplay: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 10,
  },
  macroSummary: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  macroSummaryText: {
    fontSize: 16,
    fontWeight: '500',
  },
  macroSeparator: {
    marginHorizontal: 8,
    fontSize: 14,
    opacity: 0.5,
  },
  macroCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  macroTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    textAlign: 'center',
  },
  itemsContainer: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    borderRadius: 16,
    padding: 20,
    position: 'relative',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  modalCloseButton: {
    position: 'absolute',
    top: 16,
    right: 16,
  },
  actionsContainer: {
    padding: 16,
    marginBottom: 30,
  },
  buttonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 8,
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 10,
    marginVertical: 8,
    width: '100%',
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    marginLeft: 8,
  },
  secondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 10,
    borderWidth: 1,
    marginVertical: 8,
    width: '100%',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  timelineContainer: {
    marginBottom: 20,
  },
  timelineRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  timelineInfo: {
    flex: 1,
  },
  timelineLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  timelineValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  timelineSubtext: {
    fontSize: 12,
    fontWeight: '500',
  },
  timelineBar: {
    flex: 1,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#e5e5e5',
    marginHorizontal: 10,
  },
  timelineFill: {
    height: '100%',
    borderRadius: 10,
    backgroundColor: '#10b981',
  },
  timelineExpected: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    borderRadius: 10,
    backgroundColor: '#666',
  },
  timelineTips: {
    marginTop: 10,
  },
  timelineTip: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  timelineTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  scoreCard: {
    borderRadius: 16,
    padding: 16,
    marginVertical: 16,
    borderWidth: 1,
  },
  scoreHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  scoreTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  scoreCircle: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  scoreValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  scoreMax: {
    fontSize: 12,
    color: 'white',
    marginTop: 2,
  },
  scoreMessage: {
    fontSize: 14,
    marginBottom: 16,
  },
  scoreBreakdown: {
    marginTop: 8,
  },
  scoreItemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  scoreItemLabel: {
    fontSize: 14,
    flex: 1,
  },
  scoreItemValue: {
    flexDirection: 'row',
    flex: 2,
  },
  scoreUnit: {
    width: 14,
    height: 8,
    marginHorizontal: 2,
    borderRadius: 4,
  },
  explanationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginVertical: 16,
  },
  explanationButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  descriptionContainer: {
    marginTop: 16,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  seeMore: {
    fontSize: 14,
    fontWeight: '500',
  },
  expandButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  expandButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  highlightsSection: {
    marginVertical: 8,
  },
  highlightsSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  bullet: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginTop: 6,
    marginRight: 8,
  },
  benefitText: {
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  foodName: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
  },
  editIconButton: {
    padding: 4,
  },
  calorieText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 16,
  },
  servingInfoCard: {
    padding: 16,
    borderRadius: 12,
    marginTop: 8,
  },
  servingInfoText: {
    fontSize: 14,
    marginBottom: 4,
  },
  foodItemsList: {
    marginTop: 8,
  },
  foodItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  foodItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  foodItemName: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  foodItemCalories: {
    fontSize: 16,
    fontWeight: '600',
  },
  foodItemDetails: {
    marginTop: 4,
  },
  macroValues: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  macroValue: {
    fontSize: 14,
    marginRight: 16,
  },
  foodAttributes: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  attributeTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 4,
  },
  attributeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  nutritionHighlights: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  weightBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(37, 99, 235, 0.1)',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
    marginLeft: 10,
  },
  weightText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 4,
  },
  weightInfoContainer: {
    borderRadius: 12,
    borderWidth: 1,
    marginVertical: 12,
    overflow: 'hidden',
  },
  weightInfoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  weightInfoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  weightInfoContent: {
    padding: 12,
  },
  weightInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  weightInfoLabel: {
    fontSize: 14,
  },
  weightInfoValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  weightInfoItemsContainer: {
    marginTop: 8,
  },
  weightInfoSectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  weightInfoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  weightInfoItemName: {
    fontSize: 14,
  },
  weightInfoItemWeight: {
    fontSize: 14,
    fontWeight: '500',
  },
  foodItemMetrics: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  foodItemWeight: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 10,
  },
  volumeText: {
    fontSize: 13,
    marginTop: 4,
  },
  confidenceContainer: {
    marginVertical: 12,
    borderWidth: 1,
    borderRadius: 12,
    padding: 12,
  },
  confidenceLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  confidenceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  confidenceBar: {
    flex: 1,
    height: 10,
    borderRadius: 5,
    marginRight: 10,
  },
  confidenceFill: {
    height: '100%',
    borderRadius: 5,
  },
  confidenceValue: {
    fontSize: 16,
    fontWeight: '700',
    width: 50,
    textAlign: 'right',
  },
  confidenceWarning: {
    fontSize: 12,
    marginTop: 8,
  },
  partialDataWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 8,
    backgroundColor: 'rgba(245, 158, 11, 0.1)',
    marginVertical: 10,
  },
  partialDataText: {
    flex: 1,
    fontSize: 13,
    fontWeight: '500',
  },
  section: {
    marginVertical: 16,
    borderTopWidth: 1,
    paddingTop: 16,
  },
  alternativeSourceCard: {
    borderRadius: 12,
    borderWidth: 1,
    marginVertical: 8,
    overflow: 'hidden',
  },
  alternativeSourceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
  },
  alternativeSourceInfo: {
    flex: 1,
  },
  alternativeSourceName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  alternativeSourceSummary: {
    fontSize: 14,
  },
  alternativeSourceDetails: {
    padding: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  alternativeSourceItems: {
    marginTop: 12,
  },
  alternativeSourceItemsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  alternativeSourceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 6,
  },
  alternativeSourceItemName: {
    fontSize: 14,
  },
  alternativeSourceItemCalories: {
    fontSize: 14,
  },
  alternativeSourceUseButton: {
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 16,
    alignItems: 'center',
    marginTop: 12,
  },
  alternativeSourceUseButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  editModalContent: {
    marginTop: 8,
  },
  editModalLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  editModalInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    marginBottom: 16,
  },
  editModalButton: {
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  editModalButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 10,
    padding: 8,
    borderRadius: 20,
  },
});