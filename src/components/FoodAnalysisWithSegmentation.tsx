import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Text, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import FoodSegmentationDisplay from './widgets/FoodSegmentationDisplay';
import FoodSegmentAnalysis from './widgets/FoodSegmentAnalysis';
import { FoodSegment, segmentFoodItems } from '@/services/vision/segmentationService';
import { enhancedFoodAnalysis } from '@/services/vision/enhancedVisionService';
import { AnalyzeImageResponse } from '@/services/vision/types';
import { Feather } from '@expo/vector-icons';

interface FoodAnalysisWithSegmentationProps {
  imageUri: string;
  initialAnalysisData?: AnalyzeImageResponse;
  onBack: () => void;
  onSave: (data: AnalyzeImageResponse) => void;
}

export function FoodAnalysisWithSegmentation({
  imageUri,
  initialAnalysisData,
  onBack,
  onSave
}: FoodAnalysisWithSegmentationProps) {
  const { colors, isDark } = useTheme();
  const [segmentationData, setSegmentationData] = useState<FoodSegment[]>([]);
  const [selectedSegment, setSelectedSegment] = useState<FoodSegment | null>(null);
  const [analysisData, setAnalysisData] = useState<AnalyzeImageResponse | undefined>(initialAnalysisData);
  const [isLoading, setIsLoading] = useState(!initialAnalysisData);
  const [isSegmenting, setIsSegmenting] = useState(false);
  const [combinedItems, setCombinedItems] = useState<{
    segment?: FoodSegment;
    nutritionData?: {
      name: string;
      calories: number;
      protein: number;
      carbs: number;
      fat: number;
      fiber?: number;
      estimatedAmount?: string;
    };
  }[]>([]);

  // Load segmentation data when component mounts
  useEffect(() => {
    runSegmentation();
    
    // If we don't have initial analysis data, run the analysis
    if (!initialAnalysisData) {
      runAnalysis();
    }
  }, [imageUri]);

  // When either segmentation or analysis data changes, combine them
  useEffect(() => {
    if (analysisData?.data && segmentationData.length > 0) {
      combineDataSources();
    }
  }, [analysisData, segmentationData]);

  const runSegmentation = async () => {
    setIsSegmenting(true);
    try {
      const result = await segmentFoodItems(imageUri);
      if (result.success && result.segments) {
        setSegmentationData(result.segments as any);
        if (result.segments.length > 0) {
          setSelectedSegment(result.segments[0] as any);
        }
      }
    } catch (error) {
      console.error('Error segmenting image:', error);
    } finally {
      setIsSegmenting(false);
    }
  };

  const runAnalysis = async () => {
    setIsLoading(true);
    try {
      const result = await enhancedFoodAnalysis(imageUri);
      setAnalysisData(result);
    } catch (error) {
      console.error('Error analyzing image:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const combineDataSources = () => {
    const combined: {
      segment?: FoodSegment;
      nutritionData?: any;
    }[] = [];

    // Create a copy of segmentationData to work with
    let remainingSegments = [...segmentationData];

    // If we have items from analysis, try to match them with segments
    if (analysisData?.data?.items && analysisData.data.items.length > 0) {
      // First, add items with matching segments
      for (const item of analysisData.data.items) {
        // Try to find a matching segment
        const matchingSegmentIndex = remainingSegments.findIndex(segment => {
          // If the item has a bounding box, check for overlap
          if (item.boundingBox) {
            // Check if centers are near each other
            const itemBox = item.boundingBox;
            let itemCenterX: number, itemCenterY: number;
            
            if ('topLeft' in itemBox) {
              // Convert from the alternative boundingBox format
              itemCenterX = (itemBox.topLeft.x + itemBox.bottomRight.x) / 2;
              itemCenterY = (itemBox.topLeft.y + itemBox.bottomRight.y) / 2;
            } else {
              itemCenterX = itemBox.x + itemBox.width / 2;
              itemCenterY = itemBox.y + itemBox.height / 2;
            }
            
            const segCenterX = segment.boundingBox.x + segment.boundingBox.width / 2;
            const segCenterY = segment.boundingBox.y + segment.boundingBox.height / 2;
            
            // Check if centers are close (within 20% of image width/height)
            const isClose = Math.abs(itemCenterX - segCenterX) < 0.2 && 
                            Math.abs(itemCenterY - segCenterY) < 0.2;
                            
            return isClose;
          }
          
          // If no bounding box, can't match
          return false;
        });
        
        if (matchingSegmentIndex !== -1) {
          // Get the matching segment
          const matchingSegment = remainingSegments[matchingSegmentIndex];
          
          // Update segment with label
          const updatedSegment = {
            ...matchingSegment,
            labeledAs: item.name
          };
          
          combined.push({
            segment: updatedSegment,
            nutritionData: {
              name: item.name,
              calories: item.calories,
              protein: item.protein,
              carbs: item.carbs,
              fat: item.fat,
              fiber: item.fiber,
              estimatedAmount: item.estimatedAmount
            }
          });
          
          // Remove this segment from our consideration
          remainingSegments.splice(matchingSegmentIndex, 1);
        } else {
          // No matching segment, just add the item
          combined.push({
            nutritionData: {
              name: item.name,
              calories: item.calories,
              protein: item.protein,
              carbs: item.carbs,
              fat: item.fat,
              fiber: item.fiber,
              estimatedAmount: item.estimatedAmount
            }
          });
        }
      }
    }
    
    // Add any remaining segments that didn't match an item
    for (const segment of remainingSegments) {
      combined.push({ segment });
    }
    
    setCombinedItems(combined);
  };

  const handleSegmentPress = (segment: FoodSegment) => {
    setSelectedSegment(segment);
  };

  const getSelectedSegmentNutrition = () => {
    if (!selectedSegment) return undefined;
    
    // Find the combined item that matches the selected segment
    const matchingItem = combinedItems.find(item => 
      item.segment && item.segment.id === selectedSegment.id
    );
    
    return matchingItem?.nutritionData;
  };

  const handleSave = () => {
    // Update the analysis data with segmentation info
    if (analysisData && segmentationData.length > 0) {
      const updatedData: AnalyzeImageResponse = {
        ...analysisData,
        data: {
          ...analysisData.data!,
          isMultiItem: segmentationData.length > 1,
          segmentation: {
            segments: segmentationData as unknown as { 
              id: string; 
              boundingBox: { x: number; y: number; width: number; height: number; }; 
              score: number; 
              maskUri?: string | undefined; 
            }[]
          }
        }
      };
      
      onSave(updatedData);
    } else if (analysisData) {
      onSave(analysisData);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Feather name="chevron-left" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Food Segmentation Analysis
        </Text>
      </View>
      
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            Analyzing your food...
          </Text>
        </View>
      ) : (
        <ScrollView style={styles.scrollContainer}>
          <View style={styles.content}>
            <View style={styles.segmentationContainer}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Food Detection
              </Text>
              
              {isSegmenting ? (
                <View style={styles.segmentationLoading}>
                  <ActivityIndicator size="small" color={colors.primary} />
                  <Text style={[styles.segmentationLoadingText, { color: colors.textSecondary }]}>
                    Identifying food items...
                  </Text>
                </View>
              ) : (
                <>
                  {segmentationData.length > 0 ? (
                    <>
                      <FoodSegmentationDisplay
                        imageUri={imageUri}
                        segments={segmentationData}
                        onSegmentPress={handleSegmentPress}
                        selectedSegmentId={selectedSegment?.id}
                      />
                      
                      <View style={styles.segmentCountContainer}>
                        <Text style={[styles.segmentCount, { color: colors.textSecondary }]}>
                          {segmentationData.length} food item{segmentationData.length !== 1 ? 's' : ''} detected
                        </Text>
                        <TouchableOpacity style={styles.infoButton}>
                          <Feather name="info" size={16} color={colors.textSecondary} />
                        </TouchableOpacity>
                      </View>
                    </>
                  ) : (
                    <View style={[styles.noSegmentsContainer, { borderColor: colors.border }]}>
                      <Text style={[styles.noSegmentsText, { color: colors.text }]}>
                        No distinct food items detected
                      </Text>
                      <Text style={[styles.noSegmentsSubtext, { color: colors.textSecondary }]}>
                        Try taking a clearer photo with better lighting or separate your food items
                      </Text>
                    </View>
                  )}
                </>
              )}
            </View>
            
            {selectedSegment && (
              <View style={[styles.analysisContainer, { borderColor: colors.border }]}>
                <FoodSegmentAnalysis
                  segment={selectedSegment}
                  nutritionData={getSelectedSegmentNutrition()}
                  onClose={() => setSelectedSegment(null)}
                />
              </View>
            )}
            
            {combinedItems.length > 0 && (
              <View style={styles.itemsContainer}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  Detected Items
                </Text>
                
                {combinedItems.map((item, index) => (
                  <TouchableOpacity 
                    key={`item-${index}`}
                    style={[styles.itemCard, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)', borderColor: colors.border }]}
                    onPress={() => item.segment && setSelectedSegment(item.segment)}
                  >
                    <View style={styles.itemInfo}>
                      <Text style={[styles.itemName, { color: colors.text }]}>
                        {item.nutritionData?.name || item.segment?.labeledAs || `Food Item ${index + 1}`}
                      </Text>
                      {item.nutritionData && (
                        <Text style={[styles.itemNutrition, { color: colors.textSecondary }]}>
                          {item.nutritionData.calories} cal • {item.nutritionData.protein}g protein
                        </Text>
                      )}
                    </View>
                    {item.segment && (
                      <View 
                        style={[
                          styles.segmentIndicator, 
                          { 
                            backgroundColor: selectedSegment?.id === item.segment.id 
                              ? colors.primary
                              : isDark ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.1)'
                          }
                        ]} 
                      />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            )}
            
            <TouchableOpacity 
              style={[styles.saveButton, { backgroundColor: colors.primary }]}
              onPress={handleSave}
            >
              <Text style={styles.saveButtonText}>Save Analysis</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  segmentationContainer: {
    marginBottom: 24,
  },
  segmentationLoading: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 200,
  },
  segmentationLoadingText: {
    marginTop: 12,
    fontSize: 14,
  },
  segmentCountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
  },
  segmentCount: {
    fontSize: 14,
  },
  infoButton: {
    marginLeft: 8,
    padding: 4,
  },
  noSegmentsContainer: {
    padding: 24,
    borderWidth: 1,
    borderRadius: 12,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 16,
  },
  noSegmentsText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    textAlign: 'center',
  },
  noSegmentsSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  analysisContainer: {
    marginBottom: 24,
    borderWidth: 1,
    borderRadius: 12,
  },
  itemsContainer: {
    marginBottom: 24,
  },
  itemCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  itemNutrition: {
    fontSize: 14,
  },
  segmentIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginLeft: 8,
  },
  saveButton: {
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
    marginBottom: 36,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default FoodAnalysisWithSegmentation; 