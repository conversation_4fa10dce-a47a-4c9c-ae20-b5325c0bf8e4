import React from 'react';
import { StyleSheet, View, Text, ScrollView, Image, TouchableOpacity } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';

interface FoodItem {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber?: number;
}

interface FoodData {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber?: number;
  items: FoodItem[];
  mealType?: string;
  cuisineType?: string;
  preparationMethod?: string;
  imageUri: string;
  healthHighlights?: {
    positives: string[];
    considerations: string[];
  };
}

interface FoodComparisonProps {
  foodA: FoodData;
  foodB: FoodData;
  onClose: () => void;
}

export function FoodComparison({ foodA, foodB, onClose }: FoodComparisonProps) {
  const { colors, isDark } = useTheme();

  // Calculate differences between meals
  const differences = {
    calories: foodB.calories - foodA.calories,
    protein: foodB.protein - foodA.protein,
    carbs: foodB.carbs - foodA.carbs,
    fat: foodB.fat - foodA.fat,
    fiber: (foodB.fiber ?? 0) - (foodA.fiber ?? 0)
  };

  // Helper function to format differences with + or - sign
  const formatDiff = (value: number, unit: string = '') => {
    return value > 0 ? `+${value}${unit}` : `${value}${unit}`;
  };

  // Helper function to get color for difference values
  const getDiffColor = (value: number, inverse: boolean = false) => {
    // For metrics where lower is better (calories, fat), we invert the color logic
    if (inverse) {
      return value < 0 ? colors.success : value > 0 ? colors.error : colors.textSecondary;
    }
    // For metrics where higher is better (protein, fiber)
    return value > 0 ? colors.success : value < 0 ? colors.error : colors.textSecondary;
  };

  // Calculate percentage differences for macronutrients
  const calculatePercentage = (total: number, value: number) => {
    return total > 0 ? Math.round((value / total) * 100) : 0;
  };

  // Calculate macronutrient distribution
  const foodAMacros = {
    protein: calculatePercentage(foodA.calories, foodA.protein * 4),
    carbs: calculatePercentage(foodA.calories, foodA.carbs * 4),
    fat: calculatePercentage(foodA.calories, foodA.fat * 9)
  };

  const foodBMacros = {
    protein: calculatePercentage(foodB.calories, foodB.protein * 4),
    carbs: calculatePercentage(foodB.calories, foodB.carbs * 4),
    fat: calculatePercentage(foodB.calories, foodB.fat * 9)
  };

  // Calculate protein per calorie ratio (higher is better)
  const proteinPerCalorieA = foodA.calories > 0 ? (foodA.protein / foodA.calories).toFixed(3) : '0';
  const proteinPerCalorieB = foodB.calories > 0 ? (foodB.protein / foodB.calories).toFixed(3) : '0';

  // Calculate fiber per calorie ratio (higher is better)
  const fiberPerCalorieA = foodA.calories > 0 && foodA.fiber ? (foodA.fiber / foodA.calories).toFixed(3) : '0';
  const fiberPerCalorieB = foodB.calories > 0 && foodB.fiber ? (foodB.fiber / foodB.calories).toFixed(3) : '0';

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>Food Comparison</Text>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Feather name="x" size={20} color={colors.text} />
        </TouchableOpacity>
      </View>

      {/* Food Names and Images */}
      <View style={styles.foodsContainer}>
        <View style={styles.foodColumn}>
          <Text style={[styles.foodName, { color: colors.text }]}>{foodA.name}</Text>
          <View style={[styles.imageContainer, { borderColor: colors.border }]}>
            <Image source={{ uri: foodA.imageUri }} style={styles.foodImage} />
          </View>
          <Text style={[styles.foodDetails, { color: colors.textSecondary }]}>
            {foodA.mealType}{foodA.cuisineType ? ` • ${foodA.cuisineType}` : ''}
          </Text>
        </View>

        <View style={styles.vsContainer}>
          <View style={[styles.vsCircle, { backgroundColor: colors.primary }]}>
            <Text style={styles.vsText}>VS</Text>
          </View>
        </View>

        <View style={styles.foodColumn}>
          <Text style={[styles.foodName, { color: colors.text }]}>{foodB.name}</Text>
          <View style={[styles.imageContainer, { borderColor: colors.border }]}>
            <Image source={{ uri: foodB.imageUri }} style={styles.foodImage} />
          </View>
          <Text style={[styles.foodDetails, { color: colors.textSecondary }]}>
            {foodB.mealType}{foodB.cuisineType ? ` • ${foodB.cuisineType}` : ''}
          </Text>
        </View>
      </View>

      {/* Nutrition Comparison */}
      <View style={[styles.comparisonCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Nutritional Comparison</Text>

        {/* Calories */}
        <View style={styles.comparisonRow}>
          <View style={styles.metricColumn}>
            <Text style={[styles.metricValue, { color: colors.text }]}>{foodA.calories}</Text>
            <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>Calories</Text>
          </View>
          
          <View style={styles.diffColumn}>
            <View style={[styles.diffPill, { backgroundColor: isDark ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.05)' }]}>
              <Text style={[styles.diffText, { color: getDiffColor(differences.calories, true) }]}>
                {formatDiff(differences.calories)}
              </Text>
              <Feather name="arrow-right" size={14} color={getDiffColor(differences.calories, true)} />
            </View>
          </View>
          
          <View style={styles.metricColumn}>
            <Text style={[styles.metricValue, { color: colors.text }]}>{foodB.calories}</Text>
            <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>Calories</Text>
          </View>
        </View>

        {/* Protein */}
        <View style={styles.comparisonRow}>
          <View style={styles.metricColumn}>
            <Text style={[styles.metricValue, { color: colors.text }]}>{foodA.protein}g</Text>
            <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>Protein</Text>
            <Text style={[styles.metricPercentage, { color: colors.textSecondary }]}>
              {foodAMacros.protein}% of cals
            </Text>
          </View>
          
          <View style={styles.diffColumn}>
            <View style={[styles.diffPill, { backgroundColor: isDark ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.05)' }]}>
              <Text style={[styles.diffText, { color: getDiffColor(differences.protein) }]}>
                {formatDiff(differences.protein, 'g')}
              </Text>
              <Feather name="arrow-right" size={14} color={getDiffColor(differences.protein)} />
            </View>
          </View>
          
          <View style={styles.metricColumn}>
            <Text style={[styles.metricValue, { color: colors.text }]}>{foodB.protein}g</Text>
            <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>Protein</Text>
            <Text style={[styles.metricPercentage, { color: colors.textSecondary }]}>
              {foodBMacros.protein}% of cals
            </Text>
          </View>
        </View>

        {/* Carbs */}
        <View style={styles.comparisonRow}>
          <View style={styles.metricColumn}>
            <Text style={[styles.metricValue, { color: colors.text }]}>{foodA.carbs}g</Text>
            <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>Carbs</Text>
            <Text style={[styles.metricPercentage, { color: colors.textSecondary }]}>
              {foodAMacros.carbs}% of cals
            </Text>
          </View>
          
          <View style={styles.diffColumn}>
            <View style={[styles.diffPill, { backgroundColor: isDark ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.05)' }]}>
              <Text style={[styles.diffText, { color: getDiffColor(differences.carbs, true) }]}>
                {formatDiff(differences.carbs, 'g')}
              </Text>
              <Feather name="arrow-right" size={14} color={getDiffColor(differences.carbs, true)} />
            </View>
          </View>
          
          <View style={styles.metricColumn}>
            <Text style={[styles.metricValue, { color: colors.text }]}>{foodB.carbs}g</Text>
            <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>Carbs</Text>
            <Text style={[styles.metricPercentage, { color: colors.textSecondary }]}>
              {foodBMacros.carbs}% of cals
            </Text>
          </View>
        </View>

        {/* Fat */}
        <View style={styles.comparisonRow}>
          <View style={styles.metricColumn}>
            <Text style={[styles.metricValue, { color: colors.text }]}>{foodA.fat}g</Text>
            <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>Fat</Text>
            <Text style={[styles.metricPercentage, { color: colors.textSecondary }]}>
              {foodAMacros.fat}% of cals
            </Text>
          </View>
          
          <View style={styles.diffColumn}>
            <View style={[styles.diffPill, { backgroundColor: isDark ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.05)' }]}>
              <Text style={[styles.diffText, { color: getDiffColor(differences.fat, true) }]}>
                {formatDiff(differences.fat, 'g')}
              </Text>
              <Feather name="arrow-right" size={14} color={getDiffColor(differences.fat, true)} />
            </View>
          </View>
          
          <View style={styles.metricColumn}>
            <Text style={[styles.metricValue, { color: colors.text }]}>{foodB.fat}g</Text>
            <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>Fat</Text>
            <Text style={[styles.metricPercentage, { color: colors.textSecondary }]}>
              {foodBMacros.fat}% of cals
            </Text>
          </View>
        </View>

        {/* Fiber */}
        {(foodA.fiber !== undefined || foodB.fiber !== undefined) && (
          <View style={styles.comparisonRow}>
            <View style={styles.metricColumn}>
              <Text style={[styles.metricValue, { color: colors.text }]}>{foodA.fiber ?? 0}g</Text>
              <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>Fiber</Text>
            </View>
            
            <View style={styles.diffColumn}>
              <View style={[styles.diffPill, { backgroundColor: isDark ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.05)' }]}>
                <Text style={[styles.diffText, { color: getDiffColor(differences.fiber) }]}>
                  {formatDiff(differences.fiber, 'g')}
                </Text>
                <Feather name="arrow-right" size={14} color={getDiffColor(differences.fiber)} />
              </View>
            </View>
            
            <View style={styles.metricColumn}>
              <Text style={[styles.metricValue, { color: colors.text }]}>{foodB.fiber ?? 0}g</Text>
              <Text style={[styles.metricLabel, { color: colors.textSecondary }]}>Fiber</Text>
            </View>
          </View>
        )}
      </View>

      {/* Nutritional Quality Metrics */}
      <View style={[styles.comparisonCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Nutritional Quality</Text>

        {/* Protein-to-Calorie Ratio */}
        <View style={styles.qualityRow}>
          <View style={styles.qualityLabelColumn}>
            <Text style={[styles.qualityLabel, { color: colors.text }]}>Protein per Calorie</Text>
            <View style={styles.infoIcon}>
              <Feather name="info" size={14} color={colors.textSecondary} />
            </View>
          </View>
          
          <View style={styles.qualityValueColumn}>
            <Text style={[styles.qualityValue, { color: colors.text }]}>{proteinPerCalorieA}</Text>
          </View>
          
          <View style={styles.qualityValueColumn}>
            <Text 
              style={[
                styles.qualityValue, 
                { 
                  color: Number(proteinPerCalorieB) > Number(proteinPerCalorieA) ? 
                    colors.success : 
                    Number(proteinPerCalorieB) < Number(proteinPerCalorieA) ? 
                      colors.error : 
                      colors.text 
                }
              ]}
            >
              {proteinPerCalorieB}
            </Text>
          </View>
        </View>

        {/* Fiber-to-Calorie Ratio */}
        {(foodA.fiber !== undefined || foodB.fiber !== undefined) && (
          <View style={styles.qualityRow}>
            <View style={styles.qualityLabelColumn}>
              <Text style={[styles.qualityLabel, { color: colors.text }]}>Fiber per Calorie</Text>
              <View style={styles.infoIcon}>
                <Feather name="info" size={14} color={colors.textSecondary} />
              </View>
            </View>
            
            <View style={styles.qualityValueColumn}>
              <Text style={[styles.qualityValue, { color: colors.text }]}>{fiberPerCalorieA}</Text>
            </View>
            
            <View style={styles.qualityValueColumn}>
              <Text 
                style={[
                  styles.qualityValue, 
                  { 
                    color: Number(fiberPerCalorieB) > Number(fiberPerCalorieA) ? 
                      colors.success : 
                      Number(fiberPerCalorieB) < Number(fiberPerCalorieA) ? 
                        colors.error : 
                        colors.text 
                  }
                ]}
              >
                {fiberPerCalorieB}
              </Text>
            </View>
          </View>
        )}

        {/* Macro Balance */}
        <View style={styles.qualityRow}>
          <View style={styles.qualityLabelColumn}>
            <Text style={[styles.qualityLabel, { color: colors.text }]}>Macro Balance</Text>
            <View style={styles.infoIcon}>
              <Feather name="info" size={14} color={colors.textSecondary} />
            </View>
          </View>
          
          <View style={styles.qualityValueColumn}>
            <View style={styles.macroDistribution}>
              <View style={[styles.macroBar, { backgroundColor: '#3b82f6', width: `${foodAMacros.protein}%` }]} />
              <View style={[styles.macroBar, { backgroundColor: '#8b5cf6', width: `${foodAMacros.carbs}%` }]} />
              <View style={[styles.macroBar, { backgroundColor: '#f97316', width: `${foodAMacros.fat}%` }]} />
            </View>
          </View>
          
          <View style={styles.qualityValueColumn}>
            <View style={styles.macroDistribution}>
              <View style={[styles.macroBar, { backgroundColor: '#3b82f6', width: `${foodBMacros.protein}%` }]} />
              <View style={[styles.macroBar, { backgroundColor: '#8b5cf6', width: `${foodBMacros.carbs}%` }]} />
              <View style={[styles.macroBar, { backgroundColor: '#f97316', width: `${foodBMacros.fat}%` }]} />
            </View>
          </View>
        </View>
      </View>

      {/* Health Highlights Comparison */}
      {(foodA.healthHighlights || foodB.healthHighlights) && (
        <View style={[styles.comparisonCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Health Highlights</Text>

          <View style={styles.highlightsContainer}>
            <View style={styles.highlightsColumn}>
              <Text style={[styles.highlightsTitle, { color: colors.text }]}>{foodA.name}</Text>
              {foodA.healthHighlights?.positives.map((item, index) => (
                <View key={`a-pos-${index}`} style={styles.highlightItem}>
                  <View style={[styles.highlightDot, { backgroundColor: colors.success }]} />
                  <Text style={[styles.highlightText, { color: colors.text }]}>{item}</Text>
                </View>
              ))}
              {foodA.healthHighlights?.considerations.map((item, index) => (
                <View key={`a-con-${index}`} style={styles.highlightItem}>
                  <View style={[styles.highlightDot, { backgroundColor: colors.warning }]} />
                  <Text style={[styles.highlightText, { color: colors.text }]}>{item}</Text>
                </View>
              ))}
              {(!foodA.healthHighlights || 
                (foodA.healthHighlights.positives.length === 0 && 
                 foodA.healthHighlights.considerations.length === 0)) && (
                <Text style={[styles.noHighlights, { color: colors.textSecondary }]}>
                  No health highlights available
                </Text>
              )}
            </View>

            <View style={styles.divider} />

            <View style={styles.highlightsColumn}>
              <Text style={[styles.highlightsTitle, { color: colors.text }]}>{foodB.name}</Text>
              {foodB.healthHighlights?.positives.map((item, index) => (
                <View key={`b-pos-${index}`} style={styles.highlightItem}>
                  <View style={[styles.highlightDot, { backgroundColor: colors.success }]} />
                  <Text style={[styles.highlightText, { color: colors.text }]}>{item}</Text>
                </View>
              ))}
              {foodB.healthHighlights?.considerations.map((item, index) => (
                <View key={`b-con-${index}`} style={styles.highlightItem}>
                  <View style={[styles.highlightDot, { backgroundColor: colors.warning }]} />
                  <Text style={[styles.highlightText, { color: colors.text }]}>{item}</Text>
                </View>
              ))}
              {(!foodB.healthHighlights || 
                (foodB.healthHighlights.positives.length === 0 && 
                 foodB.healthHighlights.considerations.length === 0)) && (
                <Text style={[styles.noHighlights, { color: colors.textSecondary }]}>
                  No health highlights available
                </Text>
              )}
            </View>
          </View>
        </View>
      )}

      {/* Overall Recommendation */}
      <View style={[styles.recommendationCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Recommendation</Text>
        
        <Text style={[styles.recommendationText, { color: colors.text }]}>
          {foodB.calories < foodA.calories && foodB.protein >= foodA.protein ? 
            `${foodB.name} is a better choice with ${Math.abs(differences.calories)} fewer calories and similar protein content.` :
           foodB.protein > foodA.protein && differences.calories <= 50 ?
            `${foodB.name} offers ${differences.protein}g more protein with only ${differences.calories > 0 ? differences.calories : 'the same'} calories.` :
           foodB.fiber !== undefined && foodA.fiber !== undefined && foodB.fiber > foodA.fiber ?
            `${foodB.name} has ${differences.fiber}g more fiber, which aids digestion and promotes fullness.` :
            `Both meals have their nutritional benefits. Choose based on your dietary goals and preferences.`
          }
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
  },
  closeButton: {
    padding: 8,
  },
  foodsContainer: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  foodColumn: {
    flex: 1,
    alignItems: 'center',
  },
  foodName: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
    height: 40, // Set fixed height for alignment
  },
  imageContainer: {
    width: 120,
    height: 120,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    marginBottom: 8,
  },
  foodImage: {
    width: '100%',
    height: '100%',
  },
  foodDetails: {
    fontSize: 12,
    textAlign: 'center',
  },
  vsContainer: {
    justifyContent: 'center',
    paddingHorizontal: 10,
  },
  vsCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  vsText: {
    color: 'white',
    fontWeight: '700',
    fontSize: 12,
  },
  comparisonCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  comparisonRow: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'center',
  },
  metricColumn: {
    flex: 1,
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 18,
    fontWeight: '700',
  },
  metricLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  metricPercentage: {
    fontSize: 10,
    marginTop: 2,
  },
  diffColumn: {
    paddingHorizontal: 12,
  },
  diffPill: {
    flexDirection: 'row',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 12,
    alignItems: 'center',
  },
  diffText: {
    fontSize: 12,
    fontWeight: '600',
    marginRight: 4,
  },
  qualityRow: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'center',
  },
  qualityLabelColumn: {
    flex: 1.5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  qualityLabel: {
    fontSize: 14,
  },
  infoIcon: {
    marginLeft: 6,
  },
  qualityValueColumn: {
    flex: 1,
    alignItems: 'center',
  },
  qualityValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  macroDistribution: {
    width: '100%',
    height: 12,
    borderRadius: 6,
    flexDirection: 'row',
    overflow: 'hidden',
  },
  macroBar: {
    height: '100%',
  },
  highlightsContainer: {
    flexDirection: 'row',
  },
  highlightsColumn: {
    flex: 1,
  },
  divider: {
    width: 1,
    backgroundColor: 'rgba(0,0,0,0.1)',
    marginHorizontal: 8,
  },
  highlightsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  highlightItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  highlightDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginTop: 5,
    marginRight: 8,
  },
  highlightText: {
    fontSize: 12,
    flex: 1,
  },
  noHighlights: {
    fontSize: 12,
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 8,
  },
  recommendationCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
  },
  recommendationText: {
    fontSize: 16,
    lineHeight: 24,
  },
}); 