import React from 'react';
import { StyleSheet, View, Text, Platform } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , Ionicons } from '@expo/vector-icons';

interface FoodItem {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  estimatedAmount?: string;
  fiber?: number;
  allergens?: string[];
  ingredients?: string[];
}

interface FoodItemsListProps {
  items: FoodItem[];
}

export function FoodItemsList({ items = [] }: FoodItemsListProps) {
  const { colors, isDark } = useTheme();

  if (items.length === 0) {
    return (
      <View style={[
        styles.emptyContainer, 
        { 
          borderColor: colors.border,
          backgroundColor: colors.subtle
        }
      ]}>
        <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
          No food items detected
        </Text>
      </View>
    );
  }

  // Helper function to determine if an item has a specific nutritional quality
  const isHighProtein = (item: FoodItem) => {
    const proteinCalories = item.protein * 4;
    const totalCalories = item.calories > 0 ? item.calories : 1;
    return (proteinCalories / totalCalories) > 0.2;
  };
  const isHighFiber = (item: FoodItem) => {
    return (item.fiber !== undefined && item.fiber > 3);
  };
  const isHealthyFat = (item: FoodItem) => {
    // Healthy fat sources
    const healthyFatSources = [
      'avocado', 'olive', 'olives', 'nuts', 'almond', 'walnut', 'seed',
      'flax', 'chia', 'salmon', 'tuna', 'fish', 'seafood'
    ];
    return item.fat > 5 && 
           healthyFatSources.some(source => item.name.toLowerCase().includes(source));
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {items.map((item, index) => (
        <View 
          key={index} 
          style={[
            styles.itemCard, 
            { 
              backgroundColor: isDark ? colors.card : colors.card,
              borderColor: colors.border,
              shadowColor: colors.shadow,
              marginBottom: index === items.length - 1 ? 0 : 12
            }
          ]}
        >
          <View style={styles.itemHeader}>
            <View style={styles.nameContainer}>
              <Text style={[styles.itemName, { color: colors.text }]} numberOfLines={1}>
                {item.name}
              </Text>
              
              {/* Item badges/indicators */}
              <View style={styles.badgeContainer}>
                {isHighProtein(item) && (
                  <View style={[styles.badge, { backgroundColor: isDark ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)' }]}>
                    <Text style={[styles.badgeText, { color: '#3b82f6' }]}>High Protein</Text>
                  </View>
                )}
                
                {isHighFiber(item) && (
                  <View style={[styles.badge, { backgroundColor: isDark ? 'rgba(16, 185, 129, 0.2)' : 'rgba(16, 185, 129, 0.1)' }]}>
                    <Text style={[styles.badgeText, { color: '#10b981' }]}>Fiber-Rich</Text>
                  </View>
                )}
                
                {isHealthyFat(item) && (
                  <View style={[styles.badge, { backgroundColor: isDark ? 'rgba(245, 158, 11, 0.2)' : 'rgba(245, 158, 11, 0.1)' }]}>
                    <Feather name="heart" size={12} style={styles.badgeIcon} />
                    <Text style={[styles.badgeText, { color: '#f59e0b' }]}>Healthy Fats</Text>
                  </View>
                )}
                
                {item.allergens && item.allergens.length > 0 && (
                  <View style={[styles.badge, { backgroundColor: isDark ? 'rgba(239, 68, 68, 0.2)' : 'rgba(239, 68, 68, 0.1)' }]}>
                    <Text style={[styles.badgeText, { color: '#ef4444' }]}>Allergen</Text>
                  </View>
                )}
              </View>
              
              {item.estimatedAmount && (
                <View style={styles.portionContainer}>
                  <Ionicons name="water" size={14} color={colors.textSecondary} style={styles.portionIcon} />
                  <Text style={[styles.estimatedAmount, { color: colors.textSecondary }]}>
                    {item.estimatedAmount}
                  </Text>
                </View>
              )}
            </View>
            <Text style={[styles.calorieValue, { color: colors.primary }]}>
              {item.calories} cal
            </Text>
          </View>
          
          <View style={styles.macroContainer}>
            <View style={styles.macroItem}>
              <View style={styles.macroHeader}>
                <View style={[styles.macroIndicator, { backgroundColor: '#3b82f6' }]} />
                <Text style={[styles.macroTitle, { color: colors.textSecondary }]}>
                  Protein
                </Text>
              </View>
              <Text style={[styles.macroValue, { color: colors.text }]}>
                {item.protein}g
              </Text>
            </View>
            
            <View style={styles.macroItem}>
              <View style={styles.macroHeader}>
                <View style={[styles.macroIndicator, { backgroundColor: '#8b5cf6' }]} />
                <Text style={[styles.macroTitle, { color: colors.textSecondary }]}>
                  Carbs
                </Text>
              </View>
              <Text style={[styles.macroValue, { color: colors.text }]}>
                {item.carbs}g
              </Text>
            </View>
            
            <View style={styles.macroItem}>
              <View style={styles.macroHeader}>
                <View style={[styles.macroIndicator, { backgroundColor: '#f97316' }]} />
                <Text style={[styles.macroTitle, { color: colors.textSecondary }]}>
                  Fat
                </Text>
              </View>
              <Text style={[styles.macroValue, { color: colors.text }]}>
                {item.fat}g
              </Text>
            </View>
          </View>
          
          {/* Ingredients section */}
          {item.ingredients && item.ingredients.length > 0 && (
            <View style={[styles.ingredientsContainer, { borderTopColor: colors.border }]}>
              <Text style={[styles.ingredientsTitle, { color: colors.textSecondary }]}>
                Ingredients:
              </Text>
              <Text style={[styles.ingredientsText, { color: colors.text }]}>
                {item.ingredients.join(', ')}
              </Text>
            </View>
          )}
        </View>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  emptyContainer: {
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 14,
    fontWeight: '500',
  },
  itemCard: {
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
      web: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  nameContainer: {
    flex: 1,
    marginRight: 12,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
  },
  badgeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
    gap: 4,
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 12,
    marginRight: 4,
  },
  badgeIcon: {
    marginRight: 4,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: '500',
  },
  portionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  portionIcon: {
    marginRight: 4,
  },
  estimatedAmount: {
    fontSize: 13,
  },
  calorieValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  macroContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  macroItem: {
    alignItems: 'center',
    width: '30%',
  },
  macroHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  macroIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  macroTitle: {
    fontSize: 12,
  },
  macroValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  // New styles for ingredients section
  ingredientsContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
  },
  ingredientsTitle: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  ingredientsText: {
    fontSize: 14,
    lineHeight: 20,
  }
});