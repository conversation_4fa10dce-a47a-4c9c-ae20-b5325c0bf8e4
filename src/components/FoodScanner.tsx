import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, ActivityIndicator, ScrollView, Alert , useColorScheme } from 'react-native';
import { CameraView } from 'expo-camera';
import { getFoodProductByBarcode, OpenFoodFactsProduct, extractNutritionFacts } from '../services/food-data/open-food-facts';
import { logFoodFromProduct } from '../services/food-data/food-logging-service';
import Colors from '../constants/Colors';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useBarcodeScanner } from '@/hooks/useBarcodeScanner';

interface FoodScannerProps {
  onProductSelected?: (product: OpenFoodFactsProduct) => void;
}

type MealType = 'breakfast' | 'lunch' | 'dinner' | 'snack';

export function FoodScanner({ onProductSelected }: FoodScannerProps) {
  const [scanned, setScanned] = useState(false);
  const [loading, setLoading] = useState(false);
  const [product, setProduct] = useState<OpenFoodFactsProduct | null>(null);
  const [isLoggingFood, setIsLoggingFood] = useState(false);
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  // Use the new barcode scanner hook
  const {
    hasPermission,
    scanning,
    handleBarcodeScanned,
    startScanning,
    requestPermission
  } = useBarcodeScanner(async (barcodeData) => {
    setScanned(true);
    setLoading(true);
    
    try {
      const productData = await getFoodProductByBarcode(barcodeData.data);
      setProduct(productData);
      
      if (productData && onProductSelected) {
        onProductSelected(productData);
      }
    } catch (error) {
      console.error('Error scanning product:', error);
    } finally {
      setLoading(false);
    }
  });

  const resetScanner = () => {
    setScanned(false);
    setProduct(null);
    setIsLoggingFood(false);
    startScanning();
  };

  const handleLogFood = async (mealType: MealType) => {
    if (!product) return;
    
    try {
      setIsLoggingFood(true);
      await logFoodFromProduct(product, mealType);
      
      Alert.alert(
        "Success!",
        `Added ${product.product_name} to your ${mealType} log.`,
        [{ text: "OK", onPress: resetScanner }]
      );
    } catch (error) {
      console.error('Error logging food:', error);
      Alert.alert("Error", "Failed to log food item.");
    } finally {
      setIsLoggingFood(false);
    }
  };

  if (hasPermission === null) {
    return (
      <View style={styles.container}>
        <Text style={[styles.text, { color: colors.text }]}>Requesting camera permission...</Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={styles.container}>
        <Text style={[styles.text, { color: colors.text }]}>Camera access is required to scan food products.</Text>
        <TouchableOpacity 
          style={[styles.button, { backgroundColor: colors.tint }]}
          onPress={requestPermission}
        >
          <Text style={styles.buttonText}>Grant Permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (loading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color={colors.tint} />
        <Text style={[styles.text, { color: colors.text }]}>Fetching product information...</Text>
      </View>
    );
  }

  if (isLoggingFood) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color={colors.tint} />
        <Text style={[styles.text, { color: colors.text }]}>Logging food item...</Text>
      </View>
    );
  }

  if (product) {
    const nutrition = extractNutritionFacts(product);
    
    return (
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <ScrollView style={styles.productContainer}>
          <View style={styles.productHeader}>
            <Text style={[styles.productName, { color: colors.text }]}>{product.product_name}</Text>
            {product.brands && (
              <Text style={[styles.brandText, { color: colors.text }]}>{product.brands}</Text>
            )}
          </View>
          
          {product.image_url && (
            <Image 
              source={{ uri: product.image_url }} 
              style={styles.productImage}
              resizeMode="contain"
            />
          )}
          
          <View style={styles.nutritionContainer}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Nutrition Facts</Text>
            
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: colors.text }]}>Calories:</Text>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>{nutrition.calories} kcal</Text>
            </View>
            
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: colors.text }]}>Protein:</Text>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>{nutrition.protein}g</Text>
            </View>
            
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: colors.text }]}>Carbs:</Text>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>{nutrition.carbs}g</Text>
            </View>
            
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: colors.text }]}>Fat:</Text>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>{nutrition.fat}g</Text>
            </View>
            
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: colors.text }]}>Sugar:</Text>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>{nutrition.sugar}g</Text>
            </View>
            
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: colors.text }]}>Fiber:</Text>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>{nutrition.fiber}g</Text>
            </View>
            
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: colors.text }]}>Serving Size:</Text>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>{nutrition.servingSize}</Text>
            </View>
            
            {nutrition.nutritionGrade && (
              <View style={styles.nutritionRow}>
                <Text style={[styles.nutritionLabel, { color: colors.text }]}>Nutri-Score:</Text>
                <View style={[
                  styles.nutriScore, 
                  { backgroundColor: getNutriScoreColor(nutrition.nutritionGrade) }
                ]}>
                  <Text style={styles.nutriScoreText}>{nutrition.nutritionGrade.toUpperCase()}</Text>
                </View>
              </View>
            )}
          </View>
          
          {product.ingredients_text && (
            <View style={styles.ingredientsContainer}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Ingredients</Text>
              <Text style={[styles.ingredientsText, { color: colors.text }]}>{product.ingredients_text}</Text>
            </View>
          )}
          
          {/* Food Logging Section */}
          <View style={styles.loggingContainer}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Log Food</Text>
            <Text style={[styles.loggingHint, { color: colors.text }]}>Add this food to your meal log:</Text>
            
            <View style={styles.mealTypeRow}>
              <TouchableOpacity
                style={[styles.mealTypeButton, { backgroundColor: '#FFB74D' }]}
                onPress={() => handleLogFood('breakfast')}
              >
                <View style={styles.mealTypeIconContainer}>
                  <Ionicons name="sunny" size={22} color="white" />
                </View>
                <Text style={styles.mealTypeText}>Breakfast</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.mealTypeButton, { backgroundColor: '#FF7043' }]}
                onPress={() => handleLogFood('lunch')}
              >
                <View style={styles.mealTypeIconContainer}>
                  <Ionicons name="restaurant" size={22} color="white" />
                </View>
                <Text style={styles.mealTypeText}>Lunch</Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.mealTypeRow}>
              <TouchableOpacity
                style={[styles.mealTypeButton, { backgroundColor: '#5E35B1' }]}
                onPress={() => handleLogFood('dinner')}
              >
                <View style={styles.mealTypeIconContainer}>
                  <Ionicons name="moon" size={22} color="white" />
                </View>
                <Text style={styles.mealTypeText}>Dinner</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.mealTypeButton, { backgroundColor: '#43A047' }]}
                onPress={() => handleLogFood('snack')}
              >
                <View style={styles.mealTypeIconContainer}>
                  <Ionicons name="cafe" size={22} color="white" />
                </View>
                <Text style={styles.mealTypeText}>Snack</Text>
              </TouchableOpacity>
            </View>
          </View>
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={[styles.button, { backgroundColor: colors.tint }]}
              onPress={resetScanner}
            >
              <Text style={styles.buttonText}>Scan Another Product</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }

  return (
    <View style={styles.scannerContainer}>
      <CameraView
        style={StyleSheet.absoluteFillObject}
        onBarcodeScanned={scanning ? handleBarcodeScanned : undefined}
        barcodeScannerSettings={{
          barcodeTypes: ['ean13', 'ean8', 'upc_e', 'upc_a'],
        }}
      />
      
      <View style={styles.overlay}>
        <View style={styles.scannerTarget} />
        <Text style={styles.scannerText}>Scan a food product barcode</Text>
      </View>
      
      {scanned && !scanning && (
        <TouchableOpacity 
          style={[styles.button, styles.scanAgainButton, { backgroundColor: colors.tint }]}
          onPress={resetScanner}
        >
          <Text style={styles.buttonText}>Scan Again</Text>
        </TouchableOpacity>
      )}
    </View>
  );
}

function getNutriScoreColor(score: string): string {
  switch (score.toLowerCase()) {
    case 'a': return '#358151'; // Dark green
    case 'b': return '#78BD43'; // Light green
    case 'c': return '#FFC734'; // Yellow
    case 'd': return '#EF8200'; // Orange
    case 'e': return '#EF1D00'; // Red
    default: return '#999999'; // Grey for unknown
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  scannerContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
  },
  text: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#3498db',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
  },
  scanAgainButton: {
    position: 'absolute',
    bottom: 50,
    alignSelf: 'center',
    width: 200,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerTarget: {
    width: 200,
    height: 200,
    borderWidth: 2,
    borderColor: 'white',
    borderRadius: 16,
    backgroundColor: 'transparent',
    marginBottom: 20,
  },
  scannerText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  productContainer: {
    flex: 1,
    width: '100%',
    padding: 16,
  },
  productHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  productName: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  brandText: {
    fontSize: 16,
    marginBottom: 16,
  },
  productImage: {
    width: '100%',
    height: 250,
    marginBottom: 20,
    borderRadius: 8,
  },
  nutritionContainer: {
    marginBottom: 20,
    paddingHorizontal: 10,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  nutritionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#ccc',
  },
  nutritionLabel: {
    fontSize: 16,
  },
  nutritionValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  ingredientsContainer: {
    marginBottom: 20,
    paddingHorizontal: 10,
  },
  ingredientsText: {
    fontSize: 14,
    lineHeight: 20,
  },
  buttonContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  nutriScore: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  nutriScoreText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  loggingContainer: {
    marginBottom: 20,
    paddingHorizontal: 10,
  },
  loggingHint: {
    fontSize: 14,
    marginBottom: 12,
    opacity: 0.8,
  },
  mealTypeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  mealTypeButton: {
    flex: 1,
    borderRadius: 8,
    padding: 12,
    marginHorizontal: 6,
    alignItems: 'center',
  },
  mealTypeIconContainer: {
    marginBottom: 6,
  },
  mealTypeText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
});