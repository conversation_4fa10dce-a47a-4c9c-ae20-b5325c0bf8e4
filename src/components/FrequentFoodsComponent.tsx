import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ActivityIndicator,
  FlatList,
  ScrollView,
  Image
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { FoodItem } from './SavedMealsComponent';

interface MealHistoryEntry {
  id: string;
  date: string;
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  foods: FoodItem[];
}

interface FrequentFood extends FoodItem {
  frequency: number;
  lastUsed: string;
  mealTypes: { [key: string]: number };
}

interface FrequentFoodsComponentProps {
  onAddFood: (food: FoodItem) => void;
  currentMealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
}

export function FrequentFoodsComponent({ onAddFood, currentMealType }: FrequentFoodsComponentProps) {
  const { colors, isDark } = useTheme();
  const [loading, setLoading] = useState(true);
  const [frequentFoods, setFrequentFoods] = useState<FrequentFood[]>([]);
  const [recentFoods, setRecentFoods] = useState<FrequentFood[]>([]);
  const [mealTypeRecommendations, setMealTypeRecommendations] = useState<FrequentFood[]>([]);
  const [activeTab, setActiveTab] = useState<'frequent' | 'recent' | 'recommended'>('recommended');

  useEffect(() => {
    loadFoodHistory();
  }, []);

  useEffect(() => {
    if (frequentFoods.length > 0) {
      // Update meal type recommendations when current meal type changes
      const recommended = frequentFoods
        .filter(food => food.mealTypes[currentMealType] > 0)
        .sort((a, b) => b.mealTypes[currentMealType] - a.mealTypes[currentMealType])
        .slice(0, 15);
      
      setMealTypeRecommendations(recommended);
    }
  }, [currentMealType, frequentFoods]);

  const loadFoodHistory = async () => {
    setLoading(true);
    try {
      // In a real app, we would fetch from a database
      // For this demo, we'll use mock data stored in AsyncStorage
      const storedMeals = await AsyncStorage.getItem('mealHistory');
      
      if (storedMeals) {
        const mealHistory: MealHistoryEntry[] = JSON.parse(storedMeals);
        processMealHistory(mealHistory);
      } else {
        // If no stored meals, create mock data for demonstration
        await createMockMealHistory();
      }
    } catch (error) {
      console.error('Failed to load food history', error);
    } finally {
      setLoading(false);
    }
  };

  const createMockMealHistory = async () => {
    // Similar implementation as in QuickAddComponent
    // This is just for demonstration purposes
    const today = new Date();
    
    // Create array of dates for the past 14 days
    const dates = Array.from({ length: 14 }, (_, i) => {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      return date.toISOString();
    });
    
    // Create some mock foods with different frequencies
    const mockFoodLibrary = [
      {
        id: '1',
        name: 'Oatmeal with Berries',
        calories: 350,
        protein: 12,
        carbs: 60,
        fat: 8,
        servingSize: '1 bowl',
        servings: 1,
      },
      {
        id: '2',
        name: 'Coffee with Milk',
        calories: 50,
        protein: 2,
        carbs: 4,
        fat: 2.5,
        servingSize: '1 cup',
        servings: 1,
      },
      {
        id: '3',
        name: 'Chicken Salad',
        calories: 450,
        protein: 35,
        carbs: 15,
        fat: 28,
        servingSize: '1 plate',
        servings: 1,
      },
      {
        id: '4',
        name: 'Whole Grain Bread',
        calories: 120,
        protein: 4,
        carbs: 22,
        fat: 2,
        servingSize: '1 slice',
        servings: 1,
      },
      {
        id: '5',
        name: 'Grilled Salmon',
        calories: 350,
        protein: 40,
        carbs: 0,
        fat: 18,
        servingSize: '6 oz fillet',
        servings: 1,
      },
      {
        id: '6',
        name: 'Steamed Vegetables',
        calories: 100,
        protein: 3,
        carbs: 20,
        fat: 1,
        servingSize: '1 cup',
        servings: 1,
      },
      {
        id: '7',
        name: 'Brown Rice',
        calories: 220,
        protein: 5,
        carbs: 45,
        fat: 2,
        servingSize: '1 cup',
        servings: 1,
      },
      {
        id: '8',
        name: 'Greek Yogurt',
        calories: 150,
        protein: 15,
        carbs: 8,
        fat: 4,
        servingSize: '170g container',
        servings: 1,
      },
      {
        id: '9',
        name: 'Apple',
        calories: 80,
        protein: 0.5,
        carbs: 21,
        fat: 0.3,
        servingSize: '1 medium',
        servings: 1,
      },
      {
        id: '10',
        name: 'Banana',
        calories: 105,
        protein: 1.3,
        carbs: 27,
        fat: 0.4,
        servingSize: '1 medium',
        servings: 1,
      },
      {
        id: '11',
        name: 'Avocado Toast',
        calories: 230,
        protein: 5,
        carbs: 20,
        fat: 15,
        servingSize: '1 slice',
        servings: 1,
      },
      {
        id: '12',
        name: 'Protein Shake',
        calories: 180,
        protein: 25,
        carbs: 10,
        fat: 3,
        servingSize: '1 shake',
        servings: 1,
      },
      {
        id: '13',
        name: 'Scrambled Eggs',
        calories: 220,
        protein: 14,
        carbs: 2,
        fat: 16,
        servingSize: '2 eggs',
        servings: 1,
      },
      {
        id: '14',
        name: 'Peanut Butter Sandwich',
        calories: 340,
        protein: 12,
        carbs: 35,
        fat: 18,
        servingSize: '1 sandwich',
        servings: 1,
      },
      {
        id: '15',
        name: 'Tuna Salad',
        calories: 290,
        protein: 30,
        carbs: 8,
        fat: 16,
        servingSize: '1 serving',
        servings: 1,
      },
      {
        id: '16',
        name: 'Quinoa Bowl',
        calories: 320,
        protein: 12,
        carbs: 52,
        fat: 8,
        servingSize: '1 bowl',
        servings: 1,
      },
      {
        id: '17',
        name: 'Grilled Chicken Breast',
        calories: 180,
        protein: 35,
        carbs: 0,
        fat: 4,
        servingSize: '4 oz',
        servings: 1,
      },
      {
        id: '18',
        name: 'Sweet Potato',
        calories: 180,
        protein: 2,
        carbs: 41,
        fat: 0.2,
        servingSize: '1 medium',
        servings: 1,
      },
      {
        id: '19',
        name: 'Almond Milk',
        calories: 30,
        protein: 1,
        carbs: 1,
        fat: 2.5,
        servingSize: '1 cup',
        servings: 1,
      },
      {
        id: '20',
        name: 'Turkey Sandwich',
        calories: 320,
        protein: 22,
        carbs: 38,
        fat: 9,
        servingSize: '1 sandwich',
        servings: 1,
      },
    ];
    
    // Assign frequencies to the foods (e.g., how often they appear in different meal types)
    const foodFrequencies = {
      'Oatmeal with Berries': { breakfast: 0.7, lunch: 0.1, dinner: 0, snack: 0.1 },
      'Coffee with Milk': { breakfast: 0.9, lunch: 0.2, dinner: 0, snack: 0.1 },
      'Chicken Salad': { breakfast: 0, lunch: 0.6, dinner: 0.3, snack: 0 },
      'Whole Grain Bread': { breakfast: 0.5, lunch: 0.4, dinner: 0.2, snack: 0.1 },
      'Grilled Salmon': { breakfast: 0, lunch: 0.2, dinner: 0.7, snack: 0 },
      'Steamed Vegetables': { breakfast: 0, lunch: 0.4, dinner: 0.8, snack: 0 },
      'Brown Rice': { breakfast: 0, lunch: 0.5, dinner: 0.6, snack: 0 },
      'Greek Yogurt': { breakfast: 0.4, lunch: 0.1, dinner: 0, snack: 0.6 },
      'Apple': { breakfast: 0.2, lunch: 0.3, dinner: 0.1, snack: 0.7 },
      'Banana': { breakfast: 0.5, lunch: 0.1, dinner: 0, snack: 0.5 },
      'Avocado Toast': { breakfast: 0.8, lunch: 0.2, dinner: 0, snack: 0.1 },
      'Protein Shake': { breakfast: 0.3, lunch: 0.1, dinner: 0.1, snack: 0.5 },
      'Scrambled Eggs': { breakfast: 0.9, lunch: 0.1, dinner: 0.05, snack: 0 },
      'Peanut Butter Sandwich': { breakfast: 0.3, lunch: 0.4, dinner: 0.1, snack: 0.4 },
      'Tuna Salad': { breakfast: 0, lunch: 0.7, dinner: 0.3, snack: 0 },
      'Quinoa Bowl': { breakfast: 0.1, lunch: 0.5, dinner: 0.5, snack: 0 },
      'Grilled Chicken Breast': { breakfast: 0, lunch: 0.5, dinner: 0.8, snack: 0 },
      'Sweet Potato': { breakfast: 0, lunch: 0.3, dinner: 0.6, snack: 0.1 },
      'Almond Milk': { breakfast: 0.6, lunch: 0.1, dinner: 0.1, snack: 0.2 },
      'Turkey Sandwich': { breakfast: 0, lunch: 0.7, dinner: 0.2, snack: 0.1 },
    };
    
    // Create mock meal history
    const mockMealHistory: MealHistoryEntry[] = [];
    
    // For each day in our history
    dates.forEach((date, dayIndex) => {
      // For each meal type
      (['breakfast', 'lunch', 'dinner', 'snack'] as const).forEach(type => {
        const foodsForMeal: FoodItem[] = [];
        
        // Add foods based on their probability for this meal type
        mockFoodLibrary.forEach(food => {
          const frequencyData = foodFrequencies[food.name as keyof typeof foodFrequencies];
          const probability = frequencyData[type];
          
          // Random chance based on probability and some randomization for variety
          if (Math.random() < probability * (1 - 0.1 * (dayIndex % 3))) {
            // Clone the food to avoid reference issues
            foodsForMeal.push({ ...food, id: `${food.id}-${date}-${type}` });
          }
        });
        
        // Only add meals that have foods
        if (foodsForMeal.length > 0) {
          mockMealHistory.push({
            id: `${date}-${type}`,
            date,
            type,
            foods: foodsForMeal,
          });
        }
      });
    });
    
    // Store the mock data
    await AsyncStorage.setItem('mealHistory', JSON.stringify(mockMealHistory));
    
    // Process this data
    processMealHistory(mockMealHistory);
  };

  const processMealHistory = (mealHistory: MealHistoryEntry[]) => {
    // Extract all unique foods and calculate their frequency
    const foodMap = new Map<string, FrequentFood>();
    
    mealHistory.forEach(meal => {
      meal.foods.forEach(food => {
        const baseId = food.id.split('-')[0]; // Get the base food ID without the date/meal suffix
        
        if (foodMap.has(baseId)) {
          // Update existing food entry
          const existingFood = foodMap.get(baseId)!;
          existingFood.frequency += 1;
          
          // Update last used date if this meal is more recent
          if (new Date(meal.date) > new Date(existingFood.lastUsed)) {
            existingFood.lastUsed = meal.date;
          }
          
          // Update meal type counts
          existingFood.mealTypes[meal.type] = (existingFood.mealTypes[meal.type] || 0) + 1;
          
          foodMap.set(baseId, existingFood);
        } else {
          // Create new food entry
          const newFood: FrequentFood = {
            ...food,
            id: baseId, // Use the base ID for this unique food
            frequency: 1,
            lastUsed: meal.date,
            mealTypes: { [meal.type]: 1 },
          };
          
          foodMap.set(baseId, newFood);
        }
      });
    });
    
    // Convert the map to an array
    const allFoods = Array.from(foodMap.values());
    
    // Sort by frequency for frequent foods
    const frequent = [...allFoods].sort((a, b) => b.frequency - a.frequency);
    
    // Sort by lastUsed for recent foods
    const recent = [...allFoods].sort(
      (a, b) => new Date(b.lastUsed).getTime() - new Date(a.lastUsed).getTime()
    );
    
    // Create recommendations based on current meal type
    const recommended = [...allFoods]
      .filter(food => food.mealTypes[currentMealType] > 0)
      .sort((a, b) => b.mealTypes[currentMealType] - a.mealTypes[currentMealType]);
    
    setFrequentFoods(frequent);
    setRecentFoods(recent);
    setMealTypeRecommendations(recommended.slice(0, 15)); // Limit to 15 recommendations
  };

  const getDisplayedFoods = () => {
    switch (activeTab) {
      case 'frequent':
        return frequentFoods;
      case 'recent':
        return recentFoods;
      case 'recommended':
        return mealTypeRecommendations;
      default:
        return mealTypeRecommendations;
    }
  };

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: isDark ? colors.background : '#f9f9f9' }]}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading your food history...
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Tab selector */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'recommended' && [styles.activeTab, { borderColor: colors.primary }]
          ]}
          onPress={() => setActiveTab('recommended')}
        >
          <Feather name="trending-up" size={16} color={activeTab === 'recommended' ? colors.primary : colors.textSecondary} />
          <Text 
            style={[
              styles.tabText, 
              { color: activeTab === 'recommended' ? colors.primary : colors.textSecondary }
            ]}
          >
            Recommended
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'frequent' && [styles.activeTab, { borderColor: colors.primary }]
          ]}
          onPress={() => setActiveTab('frequent')}
        >
          <Feather name="star" size={16} color={activeTab === 'frequent' ? colors.primary : colors.textSecondary} />
          <Text 
            style={[
              styles.tabText, 
              { color: activeTab === 'frequent' ? colors.primary : colors.textSecondary }
            ]}
          >
            Frequent
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'recent' && [styles.activeTab, { borderColor: colors.primary }]
          ]}
          onPress={() => setActiveTab('recent')}
        >
          <Feather name="clock" size={16} color={activeTab === 'recent' ? colors.primary : colors.textSecondary} />
          <Text 
            style={[
              styles.tabText, 
              { color: activeTab === 'recent' ? colors.primary : colors.textSecondary }
            ]}
          >
            Recent
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* Food grid */}
      <FlatList
        data={getDisplayedFoods()}
        keyExtractor={(item) => item.id}
        numColumns={2}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[styles.foodCard, { backgroundColor: isDark ? colors.card : 'white' }]}
            onPress={() => onAddFood(item)}
          >
            <View style={styles.foodCardContent}>
              <Text style={[styles.foodName, { color: colors.text }]} numberOfLines={2}>
                {item.name}
              </Text>
              <Text style={[styles.foodInfo, { color: colors.textSecondary }]}>
                {item.calories} cal • {item.servingSize}
              </Text>
              
              <View style={styles.macroRow}>
                <Text style={[styles.macroText, { color: colors.textSecondary }]}>
                  P: {item.protein}g
                </Text>
                <Text style={[styles.macroText, { color: colors.textSecondary }]}>
                  C: {item.carbs}g
                </Text>
                <Text style={[styles.macroText, { color: colors.textSecondary }]}>
                  F: {item.fat}g
                </Text>
              </View>
            </View>
            
            <TouchableOpacity
              style={[styles.addButton, { backgroundColor: colors.primary }]}
              onPress={() => onAddFood(item)}
            >
              <Feather name="plus" size={16}  color={colors.text} />
            </TouchableOpacity>
          </TouchableOpacity>
        )}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No {activeTab} foods found for {currentMealType}.
            </Text>
          </View>
        )}
        contentContainerStyle={styles.foodGrid}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  foodGrid: {
    paddingBottom: 20,
  },
  foodCard: {
    flex: 1,
    margin: 6,
    borderRadius: 12,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    position: 'relative',
  },
  foodCardContent: {
    minHeight: 100,
  },
  foodName: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 6,
  },
  foodInfo: {
    fontSize: 13,
    marginBottom: 8,
  },
  macroRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  macroText: {
    fontSize: 12,
  },
  addButton: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
});

export default FrequentFoodsComponent; 