import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Modal,
  ScrollView,
  Keyboard,
  ActivityIndicator,
  Dimensions,
  FlatList,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useRouter } from 'expo-router';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Types for search results
export interface SearchResult {
  id: string;
  title: string;
  description?: string;
  type: 'meal' | 'food' | 'recipe' | 'workout' | 'challenge' | 'achievement' | 'setting' | 'article';
  route: string;
  date?: string;
  imageUrl?: string;
  tags?: string[];
  meta?: Record<string, any>;
}

interface GlobalSearchProps {
  /**
   * Whether the search modal is visible
   */
  isVisible: boolean;
  
  /**
   * Callback when the search modal is closed
   */
  onClose: () => void;
  
  /**
   * Initial search query
   */
  initialQuery?: string;
  
  /**
   * Placeholder text for the search input
   */
  placeholder?: string;
  
  /**
   * Whether to show recent searches
   */
  showRecents?: boolean;
  
  /**
   * Number of recent searches to show
   */
  maxRecents?: number;
  
  /**
   * Search function to use for fetching results
   */
  onSearch?: (query: string) => Promise<SearchResult[]>;
}

// Maximum recent searches to store
const MAX_RECENT_SEARCHES = 10;

// Storage key for recent searches
const RECENT_SEARCHES_KEY = 'recent_searches';

/**
 * Global search component that enables searching across all data types
 */
export function GlobalSearch({
  isVisible,
  onClose,
  initialQuery = '',
  placeholder = 'Search meals, workouts, challenges...',
  showRecents = true,
  maxRecents = 5,
  onSearch,
}: GlobalSearchProps) {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const inputRef = useRef<TextInput>(null);
  
  // Search state
  const [query, setQuery] = useState(initialQuery);
  const [isSearching, setIsSearching] = useState(false);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [searchCategories, setSearchCategories] = useState<Record<string, boolean>>({
    meal: true,
    food: true,
    recipe: true,
    workout: true,
    challenge: true,
    achievement: true,
    setting: true,
    article: true,
  });
  
  // Load recent searches on mount
  useEffect(() => {
    loadRecentSearches();
  }, []);
  
  // Focus the input when the modal is shown
  useEffect(() => {
    if (isVisible) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [isVisible]);
  
  // Load recent searches from storage
  const loadRecentSearches = async () => {
    try {
      const storedSearches = await AsyncStorage.getItem(RECENT_SEARCHES_KEY);
      if (storedSearches) {
        setRecentSearches(JSON.parse(storedSearches));
      }
    } catch (error) {
      console.error('Error loading recent searches:', error);
    }
  };
  
  // Save a search to recent searches
  const saveToRecentSearches = async (searchQuery: string) => {
    if (!searchQuery.trim()) return;
    
    try {
      // Add to the beginning and remove duplicates
      const updatedSearches = [
        searchQuery,
        ...recentSearches.filter(s => s !== searchQuery),
      ].slice(0, MAX_RECENT_SEARCHES);
      
      setRecentSearches(updatedSearches);
      await AsyncStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(updatedSearches));
    } catch (error) {
      console.error('Error saving recent search:', error);
    }
  };
  
  // Clear all recent searches
  const clearRecentSearches = async () => {
    try {
      setRecentSearches([]);
      await AsyncStorage.removeItem(RECENT_SEARCHES_KEY);
    } catch (error) {
      console.error('Error clearing recent searches:', error);
    }
  };
  
  // Replace the mock search function with a real implementation that searches the app content
  async function performSearch(query: string): Promise<SearchResult[]> {
    try {
      if (!query || query.trim() === '') {
        return [];
      }

      // Use the searchAllSources function from searchService
      const { searchAllSources } = await import('../services/searchService');
      const results = await searchAllSources(query);
      
      // If we made it this far, save the query to recent searches
      saveToRecentSearches(query);
      
      return results;
    } catch (error) {
      console.error('Search error:', error);
      return [];
    }
  }
  
  // Replace the mock search function call
  const handleSearch = async (text: string) => {
    setQuery(text);
    
    // Clear results if search text is empty
    if (!text || text.trim() === '') {
      setResults([]);
      return;
    }
    
    setIsSearching(true);
    
    try {
      // Use the searchAllSources function from searchService
      const { searchAllSources } = await import('../services/searchService');
      const searchResults = await searchAllSources(text);
      setResults(searchResults);
      
      // Save to recent searches
      if (searchResults.length > 0) {
        saveToRecentSearches(text);
      }
    } catch (error) {
      console.error('Error performing search:', error);
      setResults([]);
    } finally {
      setIsSearching(false);
    }
  };
  
  // Handle result selection
  const handleResultSelect = (result: SearchResult) => {
    onClose();
    // Navigate to the selected result
    router.push(result.route as any);
  };
  
  // Handle recent search selection
  const handleRecentSearchSelect = (searchQuery: string) => {
    setQuery(searchQuery);
    handleSearch(searchQuery);
  };
  
  // Toggle a search category
  const toggleCategory = (category: string) => {
    setSearchCategories(prev => ({
      ...prev,
      [category]: !prev[category],
    }));
  };
  
  // Filter results based on selected categories
  const filteredResults = results.filter(result => 
    searchCategories[result.type]
  );
  
  // Render a search result item
  const renderResultItem = ({ item }: { item: SearchResult }) => (
    <TouchableOpacity
      style={[
        styles.resultItem,
        { borderBottomColor: colors.border }
      ]}
      onPress={() => handleResultSelect(item)}
    >
      <View style={styles.resultContent}>
        {/* Icon based on type */}
        <View
          style={[
            styles.resultTypeIcon,
            { backgroundColor: getTypeColor(item.type, isDark) }
          ]}
        >
          {getTypeIcon(item.type)}
        </View>
        
        <View style={styles.resultTextContainer}>
          <Text
            style={[styles.resultTitle, { color: colors.text }]}
            numberOfLines={1}
          >
            {item.title}
          </Text>
          
          {item.description && (
            <Text
              style={[styles.resultDescription, { color: colors.textSecondary }]}
              numberOfLines={1}
            >
              {item.description}
            </Text>
          )}
          
          {/* Tags */}
          {item.tags && item.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {item.tags.slice(0, 2).map((tag, index) => (
                <View
                  key={index}
                  style={[
                    styles.tag,
                    {
                      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.05)',
                    },
                  ]}
                >
                  <Text style={[styles.tagText, { color: colors.textSecondary }]}>
                    {tag}
                  </Text>
                </View>
              ))}
              
              {item.tags.length > 2 && (
                <Text style={[styles.moreTagsText, { color: colors.textSecondary }]}>
                  +{item.tags.length - 2} more
                </Text>
              )}
            </View>
          )}
        </View>
      </View>
      
      <Feather name="chevron-right" size={16} color={colors.textSecondary} />
    </TouchableOpacity>
  );
  
  // Render a recent search item
  const renderRecentSearchItem = ({ item }: { item: string }) => (
    <TouchableOpacity
      style={[
        styles.recentSearchItem,
        { borderBottomColor: colors.border }
      ]}
      onPress={() => handleRecentSearchSelect(item)}
    >
      <Feather name="clock" size={16} color={colors.textSecondary} style={styles.recentSearchIcon} />
      <Text style={[styles.recentSearchText, { color: colors.text }]}>
        {item}
      </Text>
    </TouchableOpacity>
  );
  
  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View
        style={[
          styles.container,
          {
            backgroundColor: isDark ? 'rgba(0, 0, 0, 0.9)' : 'rgba(255, 255, 255, 0.95)',
            paddingTop: insets.top,
            paddingBottom: insets.bottom,
          },
        ]}
      >
        <View
          style={[
            styles.searchContainer,
            {
              backgroundColor: isDark ? colors.card : '#FFFFFF',
              shadowColor: isDark ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.2)',
            },
          ]}
        >
          {/* Search input */}
          <View
            style={[
              styles.searchInputContainer,
              {
                backgroundColor: isDark ? colors.background : '#F5F5F5',
              },
            ]}
          >
            <Feather name="search" size={18} color={colors.textSecondary} style={styles.searchIcon} />
            
            <TextInput
              ref={inputRef}
              style={[
                styles.searchInput,
                { color: colors.text },
              ]}
              placeholder={placeholder}
              placeholderTextColor={colors.textSecondary}
              value={query}
              onChangeText={handleSearch}
              returnKeyType="search"
              onSubmitEditing={() => handleSearch(query)}
              clearButtonMode="never"
              autoCapitalize="none"
              autoCorrect={false}
              autoComplete="off"
            />
            
            {query.length > 0 && (
              <TouchableOpacity
                onPress={() => {
                  setQuery('');
                  setResults([]);
                  inputRef.current?.focus();
                }}
                style={styles.clearButton}
              >
                <Feather name="x" size={16} color={colors.textSecondary} />
              </TouchableOpacity>
            )}
          </View>
          
          {/* Cancel button */}
          <TouchableOpacity
            onPress={onClose}
            style={styles.cancelButton}
          >
            <Text style={[styles.cancelButtonText, { color: colors.primary }]}>
              Cancel
            </Text>
          </TouchableOpacity>
        </View>
        
        {/* Search results or recent searches */}
        <View style={styles.resultsContainer}>
          {isSearching && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          )}
          
          {!isSearching && (
            <>
              {/* Recent searches */}
              {showRecents && recentSearches.length > 0 && query.length === 0 && (
                <View style={styles.recentSearchesContainer}>
                  <View style={styles.recentSearchesHeader}>
                    <View style={styles.recentSearchesTitleContainer}>
                      <MaterialIcons name="history" size={16} color={colors.textSecondary} style={styles.recentSearchesIcon} />
                      <Text style={[styles.recentSearchesTitle, { color: colors.text }]}>
                        Recent Searches
                      </Text>
                    </View>
                    
                    <TouchableOpacity onPress={clearRecentSearches}>
                      <Text style={[styles.clearRecentText, { color: colors.primary }]}>
                        Clear All
                      </Text>
                    </TouchableOpacity>
                  </View>
                  
                  <FlatList
                    data={recentSearches.slice(0, maxRecents)}
                    renderItem={renderRecentSearchItem}
                    keyExtractor={(item, index) => `recent-${index}-${item}`}
                    showsVerticalScrollIndicator={false}
                  />
                </View>
              )}
              
              {/* Search results */}
              {query.length > 0 && (
                <>
                  {filteredResults.length === 0 ? (
                    <View style={styles.noResultsContainer}>
                      <Text style={[styles.noResultsText, { color: colors.text }]}>
                        No results found for "{query}"
                      </Text>
                      <Text style={[styles.noResultsSubtext, { color: colors.textSecondary }]}>
                        Try adjusting your search or filters
                      </Text>
                    </View>
                  ) : (
                    <FlatList
                      data={filteredResults}
                      renderItem={renderResultItem}
                      keyExtractor={(item) => `result-${item.id}`}
                      showsVerticalScrollIndicator={false}
                      contentContainerStyle={styles.resultsList}
                    />
                  )}
                </>
              )}
            </>
          )}
        </View>
      </View>
    </Modal>
  );
}

// Helper to get icon for result type
function getTypeIcon(type: string) {
  // In a real implementation, you would use different icons for each type
  switch (type) {
    case 'meal':
    case 'food':
    case 'recipe':
      return <Tag size={14} color="#FFFFFF" />;
    default:
      return <Tag size={14} color="#FFFFFF" />;
  }
}

// Helper to get color for result type
function getTypeColor(type: string, isDark: boolean): string {
  switch (type) {
    case 'meal':
      return isDark ? '#065f46' : '#10b981';
    case 'food':
      return isDark ? '#0e7490' : '#06b6d4';
    case 'recipe':
      return isDark ? '#9f1239' : '#e11d48';
    case 'workout':
      return isDark ? '#7e22ce' : '#a855f7';
    case 'challenge':
      return isDark ? '#ea580c' : '#f97316';
    case 'achievement':
      return isDark ? '#facc15' : '#eab308';
    case 'setting':
      return isDark ? '#475569' : '#64748b';
    case 'article':
      return isDark ? '#1d4ed8' : '#3b82f6';
    default:
      return isDark ? '#475569' : '#64748b';
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 10,
    paddingHorizontal: 12,
    height: 44,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    height: 44,
  },
  clearButton: {
    padding: 8,
  },
  cancelButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  resultsContainer: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  resultsList: {
    paddingBottom: 20,
  },
  resultItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  resultContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  resultTypeIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  resultTextContainer: {
    flex: 1,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  resultDescription: {
    fontSize: 14,
  },
  tagsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginTop: 4,
  },
  tag: {
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginRight: 8,
    marginBottom: 4,
  },
  tagText: {
    fontSize: 12,
  },
  moreTagsText: {
    fontSize: 12,
  },
  recentSearchesContainer: {
    marginBottom: 20,
  },
  recentSearchesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  recentSearchesTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  recentSearchesIcon: {
    marginRight: 8,
  },
  recentSearchesTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  clearRecentText: {
    fontSize: 14,
  },
  recentSearchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  recentSearchIcon: {
    marginRight: 12,
  },
  recentSearchText: {
    fontSize: 16,
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 40,
  },
  noResultsText: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 8,
    textAlign: 'center',
  },
  noResultsSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
});

export default GlobalSearch; 