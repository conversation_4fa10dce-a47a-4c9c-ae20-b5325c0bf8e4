import React, { useState } from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import GlobalSearch from './GlobalSearch';

interface GlobalSearchButtonProps {
  /**
   * Style for the button container
   */
  style?: any;
  
  /**
   * Placeholder text for the button
   */
  placeholder?: string;
  
  /**
   * Whether to show the search icon
   */
  showIcon?: boolean;
  
  /**
   * Custom search function
   */
  onSearch?: (query: string) => Promise<any[]>;
}

/**
 * Button component that opens the global search modal
 */
export function GlobalSearchButton({
  style,
  placeholder = 'Search...',
  showIcon = true,
  onSearch,
}: GlobalSearchButtonProps) {
  const { colors, isDark } = useTheme();
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  
  // Open the search modal
  const openSearch = () => {
    setIsSearchVisible(true);
  };
  
  // Close the search modal
  const closeSearch = () => {
    setIsSearchVisible(false);
  };
  
  return (
    <>
      <TouchableOpacity
        style={[
          styles.container,
          {
            backgroundColor: isDark ? colors.card : '#F5F5F5',
          },
          style,
        ]}
        onPress={openSearch}
        accessibilityRole="button"
        accessibilityLabel="Global search"
      >
        {showIcon && (
          <Feather name="search" size={16} color={colors.textSecondary} style={styles.searchIcon} />
        )}
        
        <Text style={[styles.placeholder, { color: colors.textSecondary }]}>
          {placeholder}
        </Text>
      </TouchableOpacity>
      
      <GlobalSearch
        isVisible={isSearchVisible}
        onClose={closeSearch}
        placeholder={placeholder}
        onSearch={onSearch}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  searchIcon: {
    marginRight: 8,
  },
  placeholder: {
    fontSize: 16,
  },
});

export default GlobalSearchButton; 