import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ActivityIndicator, ScrollView, Image } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/contexts/TranslationContext';
import { getGoalBasedRecommendations } from '@/services/goalRecommendationService';
import { Recipe } from '@/services/recipeRecommendationService';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

interface GoalRecommendationsProps {
  onRecipePress?: (recipe: Recipe) => void;
}

export function GoalRecommendations({ onRecipePress }: GoalRecommendationsProps) {
  const { colors, isDark } = useTheme();
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [recommendations, setRecommendations] = useState<{
    title: string;
    description: string;
    actionItems: string[];
    recipes?: Recipe[];
  } | null>(null);
  
  const router = useRouter();
  
  useEffect(() => {
    fetchRecommendations();
  }, []);
  
  const fetchRecommendations = async () => {
    setLoading(true);
    try {
      const data = await getGoalBasedRecommendations();
      setRecommendations(data);
    } catch (error) {
      console.error('Error fetching recommendations:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleRecipePress = (recipe: Recipe) => {
    if (onRecipePress) {
      onRecipePress(recipe);
    } else if (recipe.id) {
      router.push(`/recipe/${recipe.id}`);
    }
  };
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.text }]}>
          Loading recommendations...
        </Text>
      </View>
    );
  }
  
  if (!recommendations) {
    return (
      <View style={styles.errorContainer}>
        <Text style={[styles.errorText, { color: colors.text }]}>
          Unable to load recommendations. Please try again.
        </Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: colors.primary }]}
          onPress={fetchRecommendations}
        >
          <Text style={styles.retryButtonText}>
            Retry
          </Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  return (
    <ScrollView style={styles.container}>
      <View style={[styles.card, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)' }]}>
        <View style={styles.headerRow}>
          <Feather name="trending-up" size={24} color={colors.primary} style={styles.headerIcon} />
          <Text style={[styles.title, { color: colors.text }]}>
            {recommendations.title}
          </Text>
        </View>
        
        <Text style={[styles.description, { color: colors.text }]}>
          {recommendations.description}
        </Text>
        
        <View style={styles.actionItemsContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Action Items
          </Text>
          
          {recommendations.actionItems.map((item, index) => (
            <View key={index} style={styles.actionItem}>
              <MaterialIcons name="check-circle" size={20} color={colors.primary} style={styles.actionItemIcon} />
              <Text style={[styles.actionItemText, { color: colors.text }]}>
                {item}
              </Text>
            </View>
          ))}
        </View>
        
        {recommendations.recipes && recommendations.recipes.length > 0 && (
          <View style={styles.recipesContainer}>
            <View style={styles.recipesHeader}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Recommended Recipes
              </Text>
              <TouchableOpacity onPress={() => router.push('/(tabs)/meal-planner')}>
                <Text style={[styles.seeAllText, { color: colors.primary }]}>
                  See All
                </Text>
              </TouchableOpacity>
            </View>
            
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              style={styles.recipesScrollView}
            >
              {recommendations.recipes.map((recipe, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.recipeCard, 
                    { 
                      backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'white',
                      borderColor: isDark ? 'rgba(255,255,255,0.1)' : colors.border
                    }
                  ]}
                  onPress={() => handleRecipePress(recipe)}
                  activeOpacity={0.7}
                >
                  <View style={styles.recipeImageContainer}>
                    <Image 
                      source={{ uri: recipe.imageUrl }} 
                      style={styles.recipeImage}
                      resizeMode="cover"
                    />
                    <View style={[styles.recipeTypeTag, { backgroundColor: colors.primary }]}>
                      <Text style={styles.recipeTypeText}>
                        {recipe.mealType.charAt(0).toUpperCase() + recipe.mealType.slice(1)}
                      </Text>
                    </View>
                  </View>
                  
                  <View style={styles.recipeInfo}>
                    <Text 
                      style={[styles.recipeName, { color: colors.text }]}
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {recipe.name}
                    </Text>
                    
                    <View style={styles.recipeStats}>
                      <Text style={[styles.recipeStatText, { color: colors.textSecondary }]}>
                        {recipe.nutritionFacts.calories} cal
                      </Text>
                      <Text style={[styles.recipeStatText, { color: colors.textSecondary }]}>
                        {recipe.prepTime + recipe.cookTime}m
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}
      </View>
      
      <TouchableOpacity 
        style={[styles.refreshButton, { backgroundColor: colors.primary }]}
        onPress={fetchRecommendations}
      >
        <Text style={styles.refreshButtonText}>
          Refresh Recommendations
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  card: {
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 16,
    marginTop: 10,
    marginBottom: 16,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerIcon: {
    marginRight: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    flex: 1,
  },
  description: {
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 20,
  },
  actionItemsContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  actionItem: {
    flexDirection: 'row',
    marginBottom: 14,
    alignItems: 'flex-start',
  },
  actionItemIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  actionItemText: {
    fontSize: 15,
    lineHeight: 22,
    flex: 1,
  },
  recipesContainer: {
    marginTop: 10,
  },
  recipesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  seeAllText: {
    fontSize: 14,
    fontWeight: '600',
  },
  recipesScrollView: {
    paddingBottom: 8,
  },
  recipeCard: {
    width: 180,
    borderRadius: 12,
    marginRight: 12,
    overflow: 'hidden',
    borderWidth: 1,
  },
  recipeImageContainer: {
    position: 'relative',
  },
  recipeImage: {
    width: '100%',
    height: 120,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  recipeTypeTag: {
    position: 'absolute',
    top: 8,
    left: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  recipeTypeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  recipeInfo: {
    padding: 10,
  },
  recipeName: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 6,
  },
  recipeStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  recipeStatText: {
    fontSize: 12,
  },
  refreshButton: {
    marginHorizontal: 16,
    marginBottom: 24,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  refreshButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    marginBottom: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  retryButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
}); 