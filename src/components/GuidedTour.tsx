import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  BackHandler,
  Platform,
  ViewStyle,
  LayoutChangeEvent,
  TextStyle,
  StyleProp,
} from 'react-native';
import { BlurView } from 'expo-blur';
import * as Haptics from 'expo-haptics';
import { useTheme } from '../contexts/ThemeContext';
import { useAccessibility } from './AccessibilityProvider';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Types for the tour steps
export interface TourStep {
  /**
   * Unique ID for the step
   */
  id: string;
  
  /**
   * Title of the step
   */
  title: string;
  
  /**
   * Content/description of the step
   */
  content: string;
  
  /**
   * Reference ID to target element
   */
  targetRef?: string;
  
  /**
   * Tooltip position relative to target
   */
  position?: 'top' | 'right' | 'bottom' | 'left' | 'center';
  
  /**
   * Manual position overrides
   */
  targetCoordinates?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  
  /**
   * Optional image to show
   */
  imageUrl?: string;
  
  /**
   * Optional callback to run when step is shown
   */
  onShow?: () => void;
  
  /**
   * Optional custom element for tooltip content
   */
  customContent?: React.ReactNode;
}

export interface GuidedTourProps {
  /**
   * Unique ID for this tour (used for persistence)
   */
  tourId: string;
  
  /**
   * List of steps for the tour
   */
  steps: TourStep[];
  
  /**
   * Whether the tour is visible
   */
  visible: boolean;
  
  /**
   * Callback for when the tour is closed
   */
  onClose: () => void;
  
  /**
   * Callback for when a step changes
   */
  onStepChange?: (stepIndex: number) => void;
  
  /**
   * Whether to show the tour only once
   */
  showOnce?: boolean;
  
  /**
   * Custom style for the tooltip
   */
  tooltipStyle?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for tooltip title
   */
  titleStyle?: StyleProp<TextStyle>;
  
  /**
   * Custom style for tooltip content
   */
  contentStyle?: StyleProp<TextStyle>;
  
  /**
   * Whether to mask (dim) areas outside of the target
   */
  maskEnabled?: boolean;
  
  /**
   * Opacity of the mask (0-1)
   */
  maskOpacity?: number;
  
  /**
   * Text for the "Skip" button
   */
  skipButtonText?: string;
  
  /**
   * Text for the "Next" button
   */
  nextButtonText?: string;
  
  /**
   * Text for the "Back" button
   */
  backButtonText?: string;
  
  /**
   * Text for the "Finish" button
   */
  finishButtonText?: string;
  
  /**
   * Map of element references by ID
   */
  elementRefs?: Record<string, { 
    measure: (callback: (x: number, y: number, width: number, height: number) => void) => void 
  }>;
}

/**
 * A customizable guided tour component that provides step-by-step
 * instructions overlaid on the UI, highlighting specific elements.
 */
export function GuidedTour({
  tourId,
  steps,
  visible,
  onClose,
  onStepChange,
  showOnce = true,
  tooltipStyle,
  titleStyle,
  contentStyle,
  maskEnabled = true,
  maskOpacity = 0.7,
  skipButtonText = 'Skip',
  nextButtonText = 'Next',
  backButtonText = 'Back',
  finishButtonText = 'Finish',
  elementRefs = {},
}: GuidedTourProps) {
  const { colors, isDark } = useTheme();
  const { isReduceMotionEnabled, isScreenReaderEnabled, announceForAccessibility } = useAccessibility();
  
  // State for the current step and targets
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [targetMeasurements, setTargetMeasurements] = useState<{
    x: number, y: number, width: number, height: number
  } | null>(null);
  const [tooltipLayout, setTooltipLayout] = useState({ width: 0, height: 0 });
  const [tourCompleted, setTourCompleted] = useState(false);
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const tooltipAnim = useRef(new Animated.Value(0)).current;
  const spotlightAnim = useRef(new Animated.Value(0)).current;
  
  // Get the current step
  const currentStep = steps[currentStepIndex];
  
  // Handle Android back button press
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (visible) {
        if (currentStepIndex > 0) {
          goToPreviousStep();
        } else {
          handleClose();
        }
        return true;
      }
      return false;
    });
    
    return () => backHandler.remove();
  }, [visible, currentStepIndex]);
  
  // Update target measurements when step changes
  useEffect(() => {
    if (visible && currentStep) {
      measureTarget();
      
      // Run onShow callback if defined
      currentStep.onShow?.();
      
      // Announce for screen readers
      if (isScreenReaderEnabled) {
        announceForAccessibility(`Step ${currentStepIndex + 1} of ${steps.length}: ${currentStep.title}. ${currentStep.content}`);
      }
      
      // Trigger haptic feedback when step changes
      if (Platform.OS !== 'web' && !isReduceMotionEnabled) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
      
      // Notify parent component of step change
      onStepChange?.(currentStepIndex);
    }
  }, [visible, currentStepIndex, currentStep]);
  
  // Animate when visibility changes
  useEffect(() => {
    if (visible) {
      checkIfTourCompleted();
      
      // Animate in
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: isReduceMotionEnabled ? 0 : 300,
          useNativeDriver: true,
        }),
        Animated.timing(tooltipAnim, {
          toValue: 1,
          duration: isReduceMotionEnabled ? 0 : 400,
          useNativeDriver: true,
        }),
        Animated.timing(spotlightAnim, {
          toValue: 1,
          duration: isReduceMotionEnabled ? 0 : 300,
          useNativeDriver: false,
        }),
      ]).start();
    } else {
      // Animate out
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: isReduceMotionEnabled ? 0 : 200,
          useNativeDriver: true,
        }),
        Animated.timing(tooltipAnim, {
          toValue: 0,
          duration: isReduceMotionEnabled ? 0 : 200,
          useNativeDriver: true,
        }),
        Animated.timing(spotlightAnim, {
          toValue: 0,
          duration: isReduceMotionEnabled ? 0 : 200,
          useNativeDriver: false,
        }),
      ]).start();
    }
  }, [visible, isReduceMotionEnabled]);
  
  // Check if this tour has been completed before
  const checkIfTourCompleted = async () => {
    if (showOnce) {
      try {
        const completedTours = await AsyncStorage.getItem('completed_tours');
        if (completedTours) {
          const tours = JSON.parse(completedTours);
          setTourCompleted(tours.includes(tourId));
        }
      } catch (error) {
        console.error('Error checking tour completion status:', error);
      }
    }
  };
  
  // Mark the tour as completed
  const markTourAsCompleted = async () => {
    if (showOnce) {
      try {
        const completedTours = await AsyncStorage.getItem('completed_tours');
        let tours = completedTours ? JSON.parse(completedTours) : [];
        
        if (!tours.includes(tourId)) {
          tours.push(tourId);
          await AsyncStorage.setItem('completed_tours', JSON.stringify(tours));
        }
        
        setTourCompleted(true);
      } catch (error) {
        console.error('Error saving tour completion status:', error);
      }
    }
  };
  
  // Measure the target element
  const measureTarget = () => {
    if (!currentStep) return;
    
    // If we have explicit coordinates, use those
    if (currentStep.targetCoordinates) {
      setTargetMeasurements(currentStep.targetCoordinates);
      return;
    }
    
    // If we have a ref for the target, measure its position
    const ref = currentStep.targetRef ? elementRefs[currentStep.targetRef] : null;
    if (ref) {
      ref.measure((x, y, width, height) => {
        setTargetMeasurements({ x, y, width, height });
      });
    } else {
      // If no target or couldn't measure, center in screen
      const { width, height } = Dimensions.get('window');
      setTargetMeasurements({
        x: width / 2 - 40,
        y: height / 2 - 40,
        width: 80,
        height: 80,
      });
    }
  };
  
  // Handle tooltip layout
  const onTooltipLayout = (event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    setTooltipLayout({ width, height });
  };
  
  // Calculate tooltip position based on target and position preference
  const getTooltipPosition = () => {
    if (!targetMeasurements || !tooltipLayout.width) return { top: 0, left: 0 };
    
    const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
    const { x, y, width, height } = targetMeasurements;
    const { width: tooltipWidth, height: tooltipHeight } = tooltipLayout;
    
    const position = currentStep?.position || 'bottom';
    const padding = 12; // Padding between target and tooltip
    
    switch (position) {
      case 'top':
        return {
          top: Math.max(0, y - tooltipHeight - padding),
          left: Math.max(0, Math.min(screenWidth - tooltipWidth, x + width / 2 - tooltipWidth / 2)),
        };
      case 'right':
        return {
          top: Math.max(0, Math.min(screenHeight - tooltipHeight, y + height / 2 - tooltipHeight / 2)),
          left: Math.min(screenWidth - tooltipWidth, x + width + padding),
        };
      case 'left':
        return {
          top: Math.max(0, Math.min(screenHeight - tooltipHeight, y + height / 2 - tooltipHeight / 2)),
          left: Math.max(0, x - tooltipWidth - padding),
        };
      case 'center':
        return {
          top: screenHeight / 2 - tooltipHeight / 2,
          left: screenWidth / 2 - tooltipWidth / 2,
        };
      case 'bottom':
      default:
        return {
          top: Math.min(screenHeight - tooltipHeight, y + height + padding),
          left: Math.max(0, Math.min(screenWidth - tooltipWidth, x + width / 2 - tooltipWidth / 2)),
        };
    }
  };
  
  // Go to the next step
  const goToNextStep = () => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    } else {
      handleFinish();
    }
  };
  
  // Go to the previous step
  const goToPreviousStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  };
  
  // Handle finishing the tour
  const handleFinish = () => {
    markTourAsCompleted();
    onClose();
  };
  
  // Handle closing the tour
  const handleClose = () => {
    onClose();
  };
  
  // Don't show if the tour has been completed
  if (tourCompleted && showOnce) {
    return null;
  }
  
  // Render the tour if visible
  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={handleClose}
      statusBarTranslucent
    >
      <View style={styles.modalContainer}>
        {/* Background mask */}
        {maskEnabled && (
          <Animated.View 
            style={[
              styles.mask,
              {
                backgroundColor: `rgba(0, 0, 0, ${maskOpacity})`,
                opacity: fadeAnim,
              },
            ]}
          />
        )}
        
        {/* Spotlight effect */}
        {targetMeasurements && (
          <Animated.View
            style={[
              StyleSheet.absoluteFill,
              {
                opacity: spotlightAnim,
                pointerEvents: 'none',
              }
            ]}
          >
            <View
              style={[
                StyleSheet.absoluteFill,
                styles.spotlight,
                {
                  top: targetMeasurements.y - 5,
                  left: targetMeasurements.x - 5,
                  width: targetMeasurements.width + 10,
                  height: targetMeasurements.height + 10,
                  borderRadius: 8,
                },
              ]}
            />
          </Animated.View>
        )}
        
        {/* Tooltip */}
        <Animated.View
          style={[
            styles.tooltipContainer,
            getTooltipPosition(),
            tooltipStyle,
            {
              opacity: tooltipAnim,
              transform: [
                {
                  translateY: tooltipAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [10, 0],
                  }),
                },
              ],
            },
          ]}
          onLayout={onTooltipLayout}
        >
          {currentStep?.customContent || (
            <>
              {/* Title */}
              <View style={styles.tooltipHeader}>
                <Text 
                  style={[
                    styles.tooltipTitle,
                    { color: colors.text },
                    titleStyle,
                  ]}
                >
                  {currentStep?.title}
                </Text>
                
                {/* Step indicator */}
                <Text style={[styles.stepIndicator, { color: colors.textSecondary }]}>
                  {currentStepIndex + 1}/{steps.length}
                </Text>
              </View>
              
              {/* Content */}
              <Text 
                style={[
                  styles.tooltipContent,
                  { color: colors.text },
                  contentStyle,
                ]}
              >
                {currentStep?.content}
              </Text>
              
              {/* Controls */}
              <View style={styles.controls}>
                {/* Skip button */}
                <TouchableOpacity
                  style={[styles.controlButton, styles.skipButton]}
                  onPress={handleClose}
                  accessibilityLabel={skipButtonText}
                  accessibilityRole="button"
                >
                  <Feather name="x" size={16} color={colors.textSecondary} />
                  <Text style={[styles.skipButtonText, { color: colors.textSecondary }]}>
                    {skipButtonText}
                  </Text>
                </TouchableOpacity>
                
                {/* Navigation buttons */}
                <View style={styles.navButtons}>
                  {/* Back button (hide on first step) */}
                  {currentStepIndex > 0 && (
                    <TouchableOpacity
                      style={[
                        styles.navButton,
                        { borderColor: colors.border }
                      ]}
                      onPress={goToPreviousStep}
                      accessibilityLabel={backButtonText}
                      accessibilityRole="button"
                    >
                      <Feather name="arrow-left" size={16} color={colors.text} />
                      <Text style={[styles.navButtonText, { color: colors.text }]}>
                        {backButtonText}
                      </Text>
                    </TouchableOpacity>
                  )}
                  
                  {/* Next/Finish button */}
                  <TouchableOpacity
                    style={[
                      styles.navButton, 
                      styles.primaryButton,
                      { backgroundColor: colors.primary }
                    ]}
                    onPress={goToNextStep}
                    accessibilityLabel={
                      currentStepIndex === steps.length - 1 ? finishButtonText : nextButtonText
                    }
                    accessibilityRole="button"
                  >
                    <Text style={styles.primaryButtonText}>
                      {currentStepIndex === steps.length - 1 ? finishButtonText : nextButtonText}
                    </Text>
                    {currentStepIndex === steps.length - 1 ? (
                      <MaterialIcons name="check-circle" size={16}  color={colors.text} />
                    ) : (
                      <Feather name="arrow-right" size={16}  color={colors.text} />
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            </>
          )}
        </Animated.View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mask: {
    ...StyleSheet.absoluteFillObject,
  },
  spotlight: {
    position: 'absolute',
    borderWidth: 2,
    borderColor: '#3B82F6',
    backgroundColor: 'transparent',
    shadowColor: '#3B82F6',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 10,
    elevation: 5,
  },
  tooltipContainer: {
    position: 'absolute',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    maxWidth: 300,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 10,
  },
  tooltipHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  tooltipTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  stepIndicator: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  tooltipContent: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  controlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  skipButton: {
    marginRight: 'auto',
  },
  skipButtonText: {
    marginLeft: 4,
    fontSize: 14,
  },
  navButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    borderWidth: 1,
    marginLeft: 8,
  },
  primaryButton: {
    borderWidth: 0,
  },
  navButtonText: {
    marginHorizontal: 4,
    fontSize: 14,
    fontWeight: '500',
  },
  primaryButtonText: {
    color: '#FFFFFF',
    marginHorizontal: 4,
    fontSize: 14,
    fontWeight: '500',
  },
});

export default GuidedTour; 