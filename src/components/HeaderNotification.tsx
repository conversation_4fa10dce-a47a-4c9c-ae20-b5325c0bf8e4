import React, { useState, useRef, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Animated, 
  ScrollView,
  Platform,
  Pressable,
  ActivityIndicator,
} from 'react-native';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { useNotifications } from '../contexts/NotificationContext';
import { format } from 'date-fns';
import { canUseNativeDriver } from '../utils/platformUtils';
import { useRouter } from 'expo-router';
import { BlurView } from 'expo-blur';

interface HeaderNotificationProps {
  navigateToNotifications?: () => void;
}

export function HeaderNotification({ navigateToNotifications }: HeaderNotificationProps) {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const { notifications, unreadCount, loading, error, markAsRead, markAllAsRead } = useNotifications();
  const [isOpen, setIsOpen] = useState(false);
  
  // Multiple animation values for more complex effects
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const badgePulse = useRef(new Animated.Value(1)).current;

  // Pulse animation for the badge when new notifications arrive
  useEffect(() => {
    if (unreadCount > 0) {
      Animated.sequence([
        Animated.timing(badgePulse, {
          toValue: 1.2,
          duration: 200,
          useNativeDriver: canUseNativeDriver(),
        }),
        Animated.timing(badgePulse, {
          toValue: 1,
          duration: 200,
          useNativeDriver: canUseNativeDriver(),
        }),
      ]).start();
    }
  }, [unreadCount]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleOutsideClick = () => {
      if (isOpen) toggleDropdown();
    };

    // Only add listener for web platform
    if (Platform.OS === 'web') {
      document.addEventListener('click', handleOutsideClick);
      return () => document.removeEventListener('click', handleOutsideClick);
    }
  }, [isOpen]);

  const toggleDropdown = () => {
    if (isOpen) {
      // Combined animations for a smoother close effect
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: canUseNativeDriver(),
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 200,
          useNativeDriver: canUseNativeDriver(),
        }),
      ]).start(() => setIsOpen(false));
    } else {
      setIsOpen(true);
      // Combined animations for a smoother open effect
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 250,
          useNativeDriver: canUseNativeDriver(),
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: canUseNativeDriver(),
        }),
      ]).start();
    }
  };

  const handleBellClick = (e: any) => {
    // Stop propagation to prevent document click from closing it immediately
    if (Platform.OS === 'web' && e && e.stopPropagation) {
      e.stopPropagation();
    }
    toggleDropdown();
  };

  const handleMarkAllAsRead = async (e: any) => {
    // Stop propagation to prevent document click
    if (Platform.OS === 'web' && e && e.stopPropagation) {
      e.stopPropagation();
    }
    
    try {
      await markAllAsRead();
    } catch (err) {
      console.error('Error marking all as read:', err);
    }
  };

  const handleViewAll = (e: any) => {
    // Stop propagation to prevent document click
    if (Platform.OS === 'web' && e && e.stopPropagation) {
      e.stopPropagation();
    }
    
    // Close dropdown
    toggleDropdown();
    
    // Navigate to notifications screen
    if (navigateToNotifications) {
      navigateToNotifications();
    } else {
      router.push('/profile-screens/notifications');
    }
  };

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <MaterialIcons name="check-circle" size={20} color={colors.success} />;
      case 'error':
        return <AlertOctagon size={20} color={colors.error} />;
      case 'warning':
        return <Feather name="alert-triangle" size={20} color={colors.warning} />;
      case 'info':
      default:
        return <Feather name="info" size={20} color={colors.info} />;
    }
  };

  // Get notification background color based on type (lighter version)
  const getNotificationBackground = (type: string) => {
    switch (type) {
      case 'success':
        return isDark ? 'rgba(34, 197, 94, 0.08)' : 'rgba(34, 197, 94, 0.05)';
      case 'error':
        return isDark ? 'rgba(239, 68, 68, 0.08)' : 'rgba(239, 68, 68, 0.05)';
      case 'warning':
        return isDark ? 'rgba(245, 158, 11, 0.08)' : 'rgba(245, 158, 11, 0.05)';
      case 'info':
      default:
        return isDark ? 'rgba(59, 130, 246, 0.08)' : 'rgba(59, 130, 246, 0.05)';
    }
  };

  // Format relative time (e.g., "2h ago")
  const getRelativeTime = (timestamp: number) => {
    const now = Date.now();
    const diffMs = now - timestamp;
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffSec < 60) return 'Just now';
    if (diffMin < 60) return `${diffMin}m ago`;
    if (diffHour < 24) return `${diffHour}h ago`;
    if (diffDay < 7) return `${diffDay}d ago`;
    return format(timestamp, 'MMM d');
  };

  // Show at most 5 recent notifications in dropdown
  const recentNotifications = notifications.slice(0, 5);

  return (
    <View style={styles.container}>
      {/* Bell icon with unread count */}
      <TouchableOpacity 
        style={[
          styles.iconButton, 
          { 
            backgroundColor: isDark ? 'rgba(255,255,255,0.07)' : 'rgba(0,0,0,0.04)',
            borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.08)',
          }
        ]}
        onPress={handleBellClick}
        accessibilityLabel={`Notifications${unreadCount > 0 ? `, ${unreadCount} unread` : ''}`}
        accessibilityHint="Opens notification list"
        accessibilityRole="button"
      >
        {loading && !isOpen ? (
          <ActivityIndicator size="small" color={colors.primary} />
        ) : (
          <Feather name="bell" size={18} color={colors.text} />
        )}
        
        {unreadCount > 0 && !loading && (
          <Animated.View 
            style={[
              styles.badge,
              { backgroundColor: colors.primary },
              { transform: [{ scale: badgePulse }] }
            ]}
          >
            <Text style={styles.badgeText}>
              {unreadCount > 9 ? '9+' : unreadCount}
            </Text>
          </Animated.View>
        )}
      </TouchableOpacity>

      {/* Dropdown menu */}
      {isOpen && (
        <Animated.View 
          style={[
            styles.dropdown, 
            { 
              backgroundColor: colors.card,
              borderColor: colors.border,
              opacity: fadeAnim,
              transform: [
                { translateY: fadeAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-10, 0]
                  })
                },
                { scale: scaleAnim }
              ],
              shadowColor: colors.shadow,
            }
          ]}
          onStartShouldSetResponder={() => true}
        >
          {/* Glass effect for iOS/MacOS */}
          {(Platform.OS === 'ios' || Platform.OS === 'macos') && (
            <BlurView 
              tint={isDark ? 'dark' : 'light'}
              intensity={35}
              style={StyleSheet.absoluteFillObject}
            />
          )}

          {/* Close button */}
          <Pressable
            style={styles.closeButton}
            onPress={toggleDropdown}
            accessibilityLabel="Close notifications"
            accessibilityRole="button"
          >
            <Feather name="x" size={16} color={colors.textSecondary} />
          </Pressable>

          {/* Header */}
          <View style={[styles.dropdownHeader, { borderBottomColor: colors.border }]}>
            <View style={styles.titleContainer}>
              <Feather name="bell" size={16} color={colors.text} style={styles.titleIcon} />
              <Text style={[styles.dropdownTitle, { color: colors.text }]}>
                Notifications
              </Text>
            </View>
            
            {unreadCount > 0 && !loading && (
              <TouchableOpacity 
                style={[styles.markAllReadButton, { backgroundColor: `${colors.primary}15` }]}
                onPress={handleMarkAllAsRead}
                accessibilityLabel="Mark all as read"
                accessibilityRole="button"
              >
                <CheckCheck size={12} color={colors.primary} />
                <Text style={[styles.markAllReadText, { color: colors.primary }]}>
                  Mark all read
                </Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Loading state */}
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                Loading notifications...
              </Text>
            </View>
          ) : error ? (
            <View style={styles.errorContainer}>
              <AlertOctagon size={32} color={colors.error} style={{ marginBottom: 12 }} />
              <Text style={[styles.errorText, { color: colors.text }]}>
                Couldn't load notifications
              </Text>
              <Text style={[styles.errorSubText, { color: colors.textSecondary }]}>
                Please try again later
              </Text>
              <TouchableOpacity 
                style={[styles.retryButton, { backgroundColor: colors.card, borderColor: colors.border }]}
                onPress={() => router.push('/profile-screens/notifications')}
              >
                <Feather name="refresh-cw" size={14} color={colors.primary} style={{ marginRight: 6 }} />
                <Text style={[styles.retryText, { color: colors.primary }]}>View all notifications</Text>
              </TouchableOpacity>
            </View>
          ) : notifications.length === 0 ? (
            <View style={styles.emptyState}>
              <Feather name="bell" size={32} color={isDark ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.2)'} />
              <Text style={[styles.emptyText, { color: colors.text }]}>
                No notifications yet
              </Text>
              <Text style={[styles.emptyTextSub, { color: isDark ? 'rgba(255,255,255,0.5)' : 'rgba(0,0,0,0.4)' }]}>
                We'll notify you when something happens
              </Text>
            </View>
          ) : (
            <ScrollView 
              style={styles.notificationList}
              contentContainerStyle={styles.notificationListContent}
              showsVerticalScrollIndicator={false}
            >
              {recentNotifications.map(notification => (
                <TouchableOpacity 
                  key={notification.id}
                  style={[
                    styles.notificationItem,
                    { 
                      backgroundColor: !notification.read 
                        ? getNotificationBackground(notification.type)
                        : 'transparent'
                    },
                    { borderBottomColor: colors.border }
                  ]}
                  onPress={() => markAsRead(notification.id)}
                  accessibilityLabel={`${notification.read ? 'Read' : 'Unread'} notification: ${notification.message}`}
                  accessibilityHint="Tap to mark as read"
                  accessibilityRole="button"
                >
                  <View 
                    style={[
                      styles.notificationIcon,
                      { 
                        backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                      }
                    ]}
                  >
                    {getNotificationIcon(notification.type)}
                  </View>
                  <View style={styles.notificationContent}>
                    <Text 
                      style={[
                        styles.notificationMessage, 
                        { color: colors.text },
                        !notification.read && { fontWeight: '600' }
                      ]}
                      numberOfLines={2}
                    >
                      {notification.message}
                    </Text>
                    <Text style={[styles.notificationTime, { color: colors.textSecondary }]}>
                      {getRelativeTime(notification.timestamp)}
                    </Text>
                  </View>
                  {!notification.read && (
                    <TouchableOpacity
                      style={[styles.markReadButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
                      onPress={() => markAsRead(notification.id)}
                      accessibilityLabel="Mark as read"
                    >
                      <Feather name="check" size={14} color={colors.primary} />
                    </TouchableOpacity>
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}

          {/* Footer */}
          {!loading && !error && notifications.length > 0 && (
            <TouchableOpacity 
              style={[styles.viewAllButton, { borderTopColor: colors.border }]}
              onPress={handleViewAll}
              accessibilityLabel="View all notifications"
              accessibilityRole="button"
            >
              <Text style={[styles.viewAllText, { color: colors.primary }]}>
                View all notifications
              </Text>
            </TouchableOpacity>
          )}
        </Animated.View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
  },
  badge: {
    position: 'absolute',
    top: -4,
    right: -4,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
    borderWidth: 2,
    borderColor: 'white',
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '700',
  },
  dropdown: {
    position: 'absolute',
    top: 48,
    right: 0,
    width: 330,
    maxHeight: 420,
    borderRadius: 16,
    borderWidth: 1,
    zIndex: 1000,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 10,
  },
  closeButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    zIndex: 10,
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.1)',
  },
  dropdownHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleIcon: {
    marginRight: 8,
  },
  dropdownTitle: {
    fontSize: 16,
    fontWeight: '700',
  },
  markAllReadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 20,
  },
  markAllReadText: {
    fontSize: 12,
    marginLeft: 4,
    fontWeight: '600',
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
  },
  errorContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 6,
  },
  errorSubText: {
    fontSize: 13,
    marginBottom: 16,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginTop: 8,
  },
  retryText: {
    fontSize: 13,
    fontWeight: '600',
  },
  notificationList: {
    maxHeight: 320,
  },
  notificationListContent: {
    paddingTop: 4,
    paddingBottom: 8,
  },
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    position: 'relative',
  },
  notificationIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  notificationContent: {
    flex: 1,
    paddingRight: 8,
  },
  notificationMessage: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
  notificationTime: {
    fontSize: 12,
  },
  markReadButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  viewAllButton: {
    padding: 14,
    alignItems: 'center',
    borderTopWidth: 1,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
  },
  emptyState: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    marginTop: 12,
    fontSize: 15,
    fontWeight: '600',
  },
  emptyTextSub: {
    marginTop: 6,
    fontSize: 13,
    textAlign: 'center',
  },
});

export default HeaderNotification; 