import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView , Animated, Easing } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { Svg, Circle, Path } from 'react-native-svg';

interface HealthImpactExplainerProps {
  originalCalories: number;
  healthierCalories: number;
  originalFat: number;
  healthierFat: number;
  timesPicked?: number; // How many times user chose healthier options
  onClose?: () => void;
}

/**
 * A component that visualizes and explains the health benefits of consistently
 * choosing healthier alternatives over time
 */
export function HealthImpactExplainer({
  originalCalories,
  healthierCalories,
  originalFat,
  healthierFat,
  timesPicked = 1,
  onClose
}: HealthImpactExplainerProps) {
  const { colors, isDark } = useTheme();
  const [selectedTimeframe, setSelectedTimeframe] = useState<'week' | 'month' | 'year'>('month');
  
  // Calculate differences
  const calorieReduction = originalCalories - healthierCalories;
  const fatReduction = originalFat - healthierFat;
  
  // Convert to percentage reduction
  const caloriePercentReduction = originalCalories > 0 
    ? Math.round((calorieReduction / originalCalories) * 100) 
    : 0;
  
  const fatPercentReduction = originalFat > 0 
    ? Math.round((fatReduction / originalFat) * 100) 
    : 0;
  
  // Calculate projected savings based on selected timeframe and usage frequency
  const calculateProjection = (perMealReduction: number, multiplier = 1) => {
    const usageFrequency = Math.min(timesPicked, 3); // Cap at 3 for projections
    
    switch (selectedTimeframe) {
      case 'week':
        return Math.round(perMealReduction * usageFrequency * 7 * multiplier);
      case 'month':
        return Math.round(perMealReduction * usageFrequency * 30 * multiplier);
      case 'year':
        return Math.round(perMealReduction * usageFrequency * 365 * multiplier);
    }
  };
  
  // Projected calorie savings
  const projectedCalorieSavings = calculateProjection(calorieReduction);
  
  // Convert to pounds (roughly 3500 calories per pound)
  const projectedWeightImpact = (projectedCalorieSavings / 3500).toFixed(1);
  
  // Projected fat reduction
  const projectedFatReduction = calculateProjection(fatReduction);
  
  // Get impressive comparison for calories (e.g., number of donuts)
  const getCalorieComparison = (calories: number) => {
    const donutCalories = 250; // Average donut
    const donutsEquivalent = Math.round(calories / donutCalories);
    
    if (donutsEquivalent > 0) {
      return `That's like skipping ${donutsEquivalent} ${donutsEquivalent === 1 ? 'donut' : 'donuts'}!`;
    }
    return '';
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.card, borderBottomColor: colors.border }]}>
        <TouchableOpacity
          style={[styles.closeButton, { backgroundColor: colors.subtle }]}
          onPress={onClose}
        >
          <Feather name="arrow-left" size={20} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Your Health Impact
        </Text>
        <View style={styles.placeholder} />
      </View>
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.impactCard}>
          <Text style={[styles.impactTitle, { color: colors.text }]}>
            Current Meal Impact
          </Text>
          
          <View style={styles.statRow}>
            <View style={[styles.statItem, { borderRightColor: colors.border, borderRightWidth: 1 }]}>
              <View style={styles.statIconContainer}>
                <MaterialIcons name="local-fire-department" size={20} color={colors.primary} />
              </View>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                Calories Saved
              </Text>
              <Text style={[styles.statValue, { color: colors.success }]}>
                {calorieReduction} cal
              </Text>
              <Text style={[styles.statPercent, { color: colors.success }]}>
                -{caloriePercentReduction}%
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <View style={styles.statIconContainer}>
                <Feather name="trending-down" size={20} color={colors.primary} />
              </View>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                Fat Reduced
              </Text>
              <Text style={[styles.statValue, { color: colors.success }]}>
                {fatReduction}g
              </Text>
              <Text style={[styles.statPercent, { color: colors.success }]}>
                -{fatPercentReduction}%
              </Text>
            </View>
          </View>
        </View>
        
        <View style={styles.timeframeSelector}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Projected Impact
          </Text>
          <Text style={[styles.sectionDescription, { color: colors.textSecondary }]}>
            If you consistently choose healthier alternatives
          </Text>
          
          <View style={styles.tabContainer}>
            <TouchableOpacity
              style={[
                styles.tabButton,
                selectedTimeframe === 'week' && { 
                  backgroundColor: colors.primaryLight,
                  borderColor: colors.primary,
                }
              ]}
              onPress={() => setSelectedTimeframe('week')}
            >
              <Text 
                style={[
                  styles.tabText, 
                  { color: selectedTimeframe === 'week' ? colors.primary : colors.textSecondary }
                ]}
              >
                Weekly
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.tabButton,
                selectedTimeframe === 'month' && { 
                  backgroundColor: colors.primaryLight,
                  borderColor: colors.primary,
                }
              ]}
              onPress={() => setSelectedTimeframe('month')}
            >
              <Text 
                style={[
                  styles.tabText, 
                  { color: selectedTimeframe === 'month' ? colors.primary : colors.textSecondary }
                ]}
              >
                Monthly
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.tabButton,
                selectedTimeframe === 'year' && { 
                  backgroundColor: colors.primaryLight,
                  borderColor: colors.primary,
                }
              ]}
              onPress={() => setSelectedTimeframe('year')}
            >
              <Text 
                style={[
                  styles.tabText, 
                  { color: selectedTimeframe === 'year' ? colors.primary : colors.textSecondary }
                ]}
              >
                Yearly
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={[styles.projectionCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
          <View style={styles.projectionItem}>
            <Feather name="bar-chart" size={22} color={colors.primary} style={styles.projectionIcon} />
            <View>
              <Text style={[styles.projectionLabel, { color: colors.textSecondary }]}>
                Calories saved:
              </Text>
              <Text style={[styles.projectionValue, { color: colors.text }]}>
                {projectedCalorieSavings.toLocaleString()} calories
              </Text>
              <Text style={[styles.projectionDetail, { color: colors.success }]}>
                {getCalorieComparison(projectedCalorieSavings)}
              </Text>
            </View>
          </View>
          
          <View style={styles.projectionItem}>
            <Feather name="trending-down" size={22} color={colors.primary} style={styles.projectionIcon} />
            <View>
              <Text style={[styles.projectionLabel, { color: colors.textSecondary }]}>
                Potential weight impact:
              </Text>
              <Text style={[styles.projectionValue, { color: colors.text }]}>
                {projectedWeightImpact} pounds
              </Text>
              <Text style={[styles.projectionDetail, { color: colors.success }]}>
                Based on calorie reduction only
              </Text>
            </View>
          </View>
          
          <View style={styles.projectionItem}>
            <Feather name="heart" size={22} color={colors.primary} style={styles.projectionIcon} />
            <View>
              <Text style={[styles.projectionLabel, { color: colors.textSecondary }]}>
                Fat reduced:
              </Text>
              <Text style={[styles.projectionValue, { color: colors.text }]}>
                {projectedFatReduction.toLocaleString()}g of fat
              </Text>
              <Text style={[styles.projectionDetail, { color: colors.success }]}>
                Improved heart health markers
              </Text>
            </View>
          </View>
        </View>
        
        <View style={[styles.infoSection, { backgroundColor: colors.primaryLight }]}>
          <View style={styles.infoHeader}>
            <Feather name="award" size={20} color={colors.primary} />
            <Text style={[styles.infoTitle, { color: colors.primary }]}>
              Health Benefits
            </Text>
          </View>
          <Text style={[styles.infoText, { color: colors.text }]}>
            Making healthier food choices consistently can lead to:
          </Text>
          <View style={styles.benefitsList}>
            <View style={styles.benefitItem}>
              <View style={[styles.benefitDot, { backgroundColor: colors.primary }]} />
              <Text style={[styles.benefitText, { color: colors.text }]}>
                Improved energy levels throughout the day
              </Text>
            </View>
            <View style={styles.benefitItem}>
              <View style={[styles.benefitDot, { backgroundColor: colors.primary }]} />
              <Text style={[styles.benefitText, { color: colors.text }]}>
                Better weight management and body composition
              </Text>
            </View>
            <View style={styles.benefitItem}>
              <View style={[styles.benefitDot, { backgroundColor: colors.primary }]} />
              <Text style={[styles.benefitText, { color: colors.text }]}>
                Enhanced heart health and cholesterol levels
              </Text>
            </View>
            <View style={styles.benefitItem}>
              <View style={[styles.benefitDot, { backgroundColor: colors.primary }]} />
              <Text style={[styles.benefitText, { color: colors.text }]}>
                Reduced risk of chronic diseases and inflammation
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
      
      <View style={[styles.footer, { backgroundColor: colors.card, borderTopColor: colors.border }]}>
        <TouchableOpacity
          style={[styles.closeFullButton, { backgroundColor: colors.primary }]}
          onPress={onClose}
        >
          <Text style={styles.closeFullButtonText}>Continue</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  placeholder: {
    width: 36,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom: 100,
  },
  impactCard: {
    marginBottom: 24,
  },
  impactTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  statRow: {
    flexDirection: 'row',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 16,
  },
  statIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
  },
  statPercent: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 4,
  },
  timeframeSelector: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 14,
    marginBottom: 16,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginHorizontal: 4,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  tabText: {
    fontWeight: '500',
  },
  projectionCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
  },
  projectionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  projectionIcon: {
    marginRight: 16,
  },
  projectionLabel: {
    fontSize: 14,
  },
  projectionValue: {
    fontSize: 18,
    fontWeight: '700',
    marginVertical: 2,
  },
  projectionDetail: {
    fontSize: 12,
  },
  infoSection: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginLeft: 8,
  },
  infoText: {
    fontSize: 14,
    marginBottom: 12,
  },
  benefitsList: {
    marginTop: 8,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  benefitDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 8,
  },
  benefitText: {
    fontSize: 14,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    borderTopWidth: 1,
  },
  closeFullButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  closeFullButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
}); 