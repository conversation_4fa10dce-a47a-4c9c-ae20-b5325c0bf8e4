import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useUserPreferences } from '@/contexts/UserPreferencesContext';


interface HealthierAlternativesTabIconProps {
  color: string;
  size?: number;
  focused?: boolean;
}

/**
 * A tab icon component for the healthier alternatives feature
 * Shows a badge when user has preferences enabled
 */
export function HealthierAlternativesTabIcon({ 
  color, 
  size = 24,
  focused = false 
}: HealthierAlternativesTabIconProps) {
  const { dietaryPreferences, healthGoals } = useUserPreferences();
  
  // Determine if user has any preferences set
  const hasPreferences = (
    dietaryPreferences.length > 0 || 
    healthGoals.length > 0
  );
  
  return (
    <View style={styles.container}>
      <Salad size={size} color={color} />
      
      {/* Show badge if user has preferences set */}
      {hasPreferences && (
        <View style={[
          styles.badge, 
          { backgroundColor: focused ? '#4caf50' : '#8bc34a' }
        ]} />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  badge: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 8,
    height: 8,
    borderRadius: 4,
  }
}); 