import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Animated,
  Platform,
  Pressable,
} from 'react-native';
// Temporarily using emoji icons to isolate the issue
import { useTheme } from '@/contexts/ThemeContext';
import { useRouter } from 'expo-router';

interface ActionButtonsProps {
  fadeAnim: Animated.Value;
  translateY: Animated.Value;
  onAddFood?: () => void;
  onAddWater?: () => void;
}

interface ActionButton {
  title: string;
  icon: React.ReactNode;
  gradientColors: [string, string]; // Define as tuple of two strings
  onPress: () => void;
  accessLabel: string;
  accessHint: string;
}

export function ActionButtons({
  fadeAnim,
  translateY,
  onAddFood,
  onAddWater,
}: ActionButtonsProps) {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const [activeButton, setActiveButton] = useState<number | null>(null);

  const buttons: ActionButton[] = [
    {
      title: 'Upload',
      icon: <Text style={{ color: 'white', fontSize: 22 }}>📤</Text>,
      gradientColors: ['#6366f1', '#4f46e5'],
      onPress: () =>
        onAddFood ? onAddFood() : router.push('/(tabs)/food-history'),
      accessLabel: 'Upload',
      accessHint: 'Upload a photo of your meal',
    },
    {
      title: 'Water',
      icon: <Text style={{ color: 'white', fontSize: 22 }}>💧</Text>,
      gradientColors: ['#0ea5e9', '#0284c7'],
      onPress: () =>
        onAddWater ? onAddWater() : router.push('/water-tracker'),
      accessLabel: 'Log Water',
      accessHint: 'Record your water intake',
    },
    {
      title: 'Health',
      icon: <Text style={{ color: 'white', fontSize: 22 }}>❤️</Text>,
      gradientColors: ['#10b981', '#059669'],
      onPress: () => router.push('/profile-screens/health-data'),
      accessLabel: 'Health',
      accessHint: 'View your health data and statistics',
    },
  ];

  return (
    <Animated.View
      style={[
        styles.actionsContainer,
        {
          opacity: fadeAnim,
          transform: [{ translateY: Animated.multiply(translateY, 0.5) }],
        },
      ]}
    >
      {buttons.map((button, index) => {
        const isActive = activeButton === index;

        return (
          <Pressable
            key={`action-${index}`}
            style={[
              styles.buttonWrapper,
              isActive && styles.buttonWrapperActive,
            ]}
            onPress={button.onPress}
            onPressIn={() => setActiveButton(index)}
            onPressOut={() => setActiveButton(null)}
            accessibilityLabel={button.accessLabel}
            accessibilityHint={button.accessHint}
          >
            <View
              style={[
                styles.actionButton,
                { backgroundColor: button.gradientColors[0] },
              ]}
            >
              <View style={styles.buttonContent}>
                <View style={styles.iconContainer}>
                  {button.icon}
                  <View style={styles.iconGlow} />
                </View>
                <Text style={styles.buttonText}>{button.title}</Text>
              </View>
              <Text
                style={[styles.arrowIcon, { color: 'white', fontSize: 16 }]}
              >
                →
              </Text>
            </View>
          </Pressable>
        );
      })}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  actionsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginVertical: 16,
    justifyContent: 'space-between',
    gap: 8,
  },
  buttonWrapper: {
    flex: 1,
    borderRadius: 20,
    transform: [{ scale: 1 }],
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.25,
        shadowRadius: 12,
        shadowColor: '#000',
      },
      android: {
        elevation: 8,
      },
    }),
  },
  buttonWrapperActive: {
    transform: [{ scale: 0.96 }],
  },
  actionButton: {
    padding: 16,
    borderRadius: 20,
    alignItems: 'center',
    position: 'relative',
    overflow: 'hidden',
    minHeight: 100,
  },
  actionButtonActive: {
    // No additional styles needed as the wrapper handles the scale
  },
  buttonContent: {
    alignItems: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.18)',
    marginBottom: 12,
    position: 'relative',
    overflow: 'hidden',
  },
  iconGlow: {
    position: 'absolute',
    bottom: -10,
    left: -10,
    right: -10,
    height: 20,
    backgroundColor: 'rgba(255,255,255,0.4)',
    borderRadius: 10,
    opacity: 0.6,
    transform: [{ scaleX: 1.5 }],
  },
  buttonText: {
    color: 'white',
    fontWeight: '700',
    fontSize: 15,
    letterSpacing: 0.3,
  },
  arrowIcon: {
    position: 'absolute',
    top: 12,
    right: 12,
  },
});
