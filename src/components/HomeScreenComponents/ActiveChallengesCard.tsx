import React, { useEffect, useState, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useRouter } from 'expo-router';
import { FontAwesome , Feather , MaterialCommunityIcons } from '@expo/vector-icons';
import { 
  getUserChallenges, 
  Challenge, 
  ChallengeStatus,
  ChallengeType,
  ChallengeDifficulty
} from '@/services/challengeService';

export default function ActiveChallengesCard() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const [activeChallenges, setActiveChallenges] = useState<Challenge[]>([]);
  const [loading, setLoading] = useState(true);
  const timeoutRef = useRef<number | null>(null);

  useEffect(() => {
    loadChallenges();
    
    // Set a timeout to ensure loading completes even if there's an error
    timeoutRef.current = setTimeout(() => {
      if (loading) {
        console.debug('ActiveChallengesCard: Loading timeout reached, using default challenges');
        setLoading(false);
        setActiveChallenges(getDefaultChallenges());
      }
    }, 5000) as unknown as number;
    
    return () => {
      // Clear timeout on unmount
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const loadChallenges = async () => {
    try {
      console.debug('ActiveChallengesCard: Loading challenges');
      setLoading(true);
      
      // Create a timeout promise to prevent getting stuck
      const challengesPromise = getUserChallenges();
      const timeoutPromise = new Promise<Challenge[]>((resolve) => {
        setTimeout(() => {
          console.debug('ActiveChallengesCard: Service call timed out');
          resolve(getDefaultChallenges());
        }, 3000);
      });
      
      // Use whichever resolves first
      const challenges = await Promise.race([challengesPromise, timeoutPromise]);
      console.debug('ActiveChallengesCard: Challenges loaded, count:', challenges.length);
      
      const inProgressChallenges = challenges.filter(
        challenge => challenge.status === ChallengeStatus.IN_PROGRESS
      );
      setActiveChallenges(inProgressChallenges);
    } catch (error) {
      console.error('Error loading challenges:', error);
      // Use default challenges in case of error
      setActiveChallenges(getDefaultChallenges());
    } finally {
      // Clear the timeout since we've completed loading
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      setLoading(false);
    }
  };

  // Helper function to generate default challenges if the service call fails
  const getDefaultChallenges = (): Challenge[] => {
    return [
      {
        id: 'default-1',
        title: 'Daily Water Goal',
        description: 'Drink at least 2L of water daily',
        type: ChallengeType.WATER_INTAKE,
        goal: 2000,
        progress: 1200,
        status: ChallengeStatus.IN_PROGRESS,
        rewards: {
          points: 50
        },
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        difficulty: ChallengeDifficulty.EASY,
        duration: 7,
        isPublic: true
      },
      {
        id: 'default-2',
        title: 'Protein Champion',
        description: 'Meet your protein goal for 5 days',
        type: ChallengeType.PROTEIN_GOAL,
        goal: 5,
        progress: 3,
        status: ChallengeStatus.IN_PROGRESS,
        rewards: {
          points: 100,
          badge: 'protein_champion'
        },
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
        difficulty: ChallengeDifficulty.MEDIUM,
        duration: 5,
        isPublic: true
      }
    ];
  };

  // Get icon based on challenge type
  const getChallengeIcon = (type: string) => {
    switch (type) {
      case 'water_intake':
        return 'cup-water';
      case 'protein_goal':
        return 'arm-flex';
      case 'calorie_target':
        return 'fire';
      case 'meal_logging':
        return 'silverware-fork-knife';
      case 'streak':
        return 'calendar-check';
      default:
        return 'trophy';
    }
  };

  const goToChallenges = () => {
    // Use the navigation API directly to handle tab navigation
    router.navigate("/(tabs)/challenges" as any);
  };

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? colors.card : '#fff', borderColor: colors.border }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading challenges...</Text>
        </View>
      </View>
    );
  }

  if (activeChallenges.length === 0) {
    return null; // Don't show the card if there are no active challenges
  }

  return (
    <View style={[styles.container, { backgroundColor: isDark ? colors.card : '#fff', borderColor: colors.border }]}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <FontAwesome name="trophy" size={18} color={colors.primary} style={styles.icon} />
          <Text style={[styles.title, { color: colors.text }]}>Active Challenges</Text>
        </View>
        <Text style={[styles.challengeCount, { color: colors.primary }]}>
          {activeChallenges.length} Active
        </Text>
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.challengesContainer}
        contentContainerStyle={styles.challengesList}
      >
        {activeChallenges.map((challenge) => {
          const iconName = getChallengeIcon(challenge.type);
          const progress = challenge.progress !== undefined && challenge.goal 
            ? Math.min((challenge.progress / challenge.goal) * 100, 100) 
            : 0;
            
          return (
            <TouchableOpacity 
              key={challenge.id}
              style={[styles.challengeCard, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)' }]}
              onPress={goToChallenges}
            >
              <View style={[styles.iconContainer, { backgroundColor: `${colors.primary}20` }]}>
                <MaterialCommunityIcons name={iconName} size={20} color={colors.primary} />
              </View>
              <Text 
                style={[styles.challengeName, { color: colors.text }]} 
                numberOfLines={1}
              >
                {challenge.title}
              </Text>
              <View style={[styles.progressBackground, { backgroundColor: colors.border }]}>
                <View 
                  style={[
                    styles.progressFill, 
                    { width: `${progress}%`, backgroundColor: colors.primary }
                  ]} 
                />
              </View>
              <Text style={[styles.progressText, { color: colors.textSecondary }]}>
                {Math.round(progress)}% Complete
              </Text>
            </TouchableOpacity>
          );
        })}
      </ScrollView>

      <TouchableOpacity
        style={[styles.footer, { borderTopColor: colors.border }]}
        onPress={goToChallenges}
      >
        <Text style={[styles.footerText, { color: colors.primary }]}>View all challenges</Text>
        <Feather name="chevron-right" size={16} color={colors.primary} />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    overflow: 'hidden',
    borderWidth: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  challengeCount: {
    fontSize: 14,
    fontWeight: '500',
  },
  challengesContainer: {
    paddingHorizontal: 16,
  },
  challengesList: {
    paddingBottom: 8,
  },
  challengeCard: {
    width: 150,
    padding: 12,
    borderRadius: 12,
    marginRight: 12,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  challengeName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  progressBackground: {
    height: 4,
    borderRadius: 2,
    marginBottom: 6,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderTopWidth: 1,
  },
  footerText: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 14,
  },
}); 