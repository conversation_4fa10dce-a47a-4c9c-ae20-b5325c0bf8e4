import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Image, ScrollView, Animated, Platform, ActivityIndicator, Alert } from 'react-native';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { 
  getMealRecommendations, 
  addRecommendationToMeals, 
  addMealToFavorites, 
  removeMealFromFavorites,
  MealRecommendation,
  FALLBACK_RECOMMENDATIONS
} from '@/services/databaseService';
import { safeRequest } from '@/utils/requestUtils';

interface DailyRecommendationsProps {
  fadeAnim: Animated.Value;
  translateY: Animated.Value;
}

// Add this fallback image URL near the imports
const FALLBACK_IMAGE_URL = 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?q=80&w=1760&auto=format&fit=crop';

export function DailyRecommendations({ fadeAnim, translateY }: DailyRecommendationsProps) {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  
  const [recommendations, setRecommendations] = useState<MealRecommendation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [favorites, setFavorites] = useState<Record<string, boolean>>({});
  const [addingMeal, setAddingMeal] = useState<string | null>(null);
  const [failedImages, setFailedImages] = useState<Record<string, boolean>>({});
  
  useEffect(() => {
    loadRecommendations();
  }, []);
  
  const loadRecommendations = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Fetch recommendations by meal type in parallel with proper timeout handling
      const fetchMealsByType = async (mealType: string) => {
        const { data, error, timedOut, fromCache } = await safeRequest(
          () => getMealRecommendations(mealType, 2),
          {
            timeout: 10000, // Increase timeout to 10 seconds
            useCache: true,
            cacheKey: `meal_recommendations_${mealType}`,
            cacheTTL: 1000 * 60 * 30, // 30 minute cache
            errorMessage: `Failed to fetch ${mealType} recommendations`,
            retryCount: 2, // Increase retry count to 2
            fallbackData: FALLBACK_RECOMMENDATIONS.filter(r => r.type === mealType) // Type-specific fallbacks
          }
        );
        
        if (timedOut) {
          console.log(`${mealType} recommendation request timed out, using cached or fallback data`);
        }
        
        if (fromCache) {
          console.log(`Using cached ${mealType} recommendations`);
        }
        
        // Always return something, even if empty
        return data || FALLBACK_RECOMMENDATIONS.filter(r => r.type === mealType);
      };
      
      try {
        // Fetch all meal types in parallel
        const mealPromises = ['breakfast', 'lunch', 'dinner', 'snack'].map(fetchMealsByType);
        const mealResults = await Promise.all(mealPromises);
        
        // Combine all meal types
        let allRecommendations = mealResults.flat();
        
        if (allRecommendations.length === 0) {
          console.warn('No meal recommendations returned from the database, using fallbacks');
          allRecommendations = FALLBACK_RECOMMENDATIONS;
        }
        
        // Randomize and limit to 8 items
        const shuffled = allRecommendations.sort(() => 0.5 - Math.random());
        const selectedRecommendations = shuffled.slice(0, 8);
        
        // Build favorites map
        const favMap: Record<string, boolean> = {};
        selectedRecommendations.forEach(rec => {
          favMap[rec.id] = rec.is_favorite || false;
        });
        
        setRecommendations(selectedRecommendations);
        setFavorites(favMap);
      } catch (error) {
        console.error('Error processing recommendations:', error);
        useFallbackData();
      }
    } catch (error) {
      console.error('Error loading recommendations:', error);
      setError('Could not load recommendations. Using default suggestions.');
      useFallbackData();
    } finally {
      setLoading(false);
    }
  };
  
  const useFallbackData = () => {
    console.log('DailyRecommendations: Loading timeout reached, using fallback data');
    
    // Create favorites map for fallback data
    const favMap: Record<string, boolean> = {};
    FALLBACK_RECOMMENDATIONS.forEach(rec => {
      favMap[rec.id] = false;
    });
    
    setRecommendations(FALLBACK_RECOMMENDATIONS);
    setFavorites(favMap);
  };
  
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'breakfast':
        return <Coffee size={14} color="white" />;
      case 'lunch':
        return <MaterialIcons name="restaurant" size={14}  color={colors.text} />;
      case 'dinner':
        return <UtensilsCrossed size={14} color="white" />;
      case 'snack':
        return <Feather name="zap" size={14}  color={colors.text} />;
      default:
        return <MaterialIcons name="restaurant" size={14}  color={colors.text} />;
    }
  };
  
  const getMealTypeColor = (type: string) => {
    switch (type) {
      case 'breakfast':
        return ['#60A5FA', '#3B82F6']; // Blue
      case 'lunch':
        return ['#F97316', '#EA580C']; // Orange
      case 'dinner':
        return ['#8B5CF6', '#7C3AED']; // Purple
      case 'snack':
        return ['#10B981', '#059669']; // Green
      default:
        return ['#6B7280', '#4B5563']; // Gray
    }
  };
  
  const getFormattedType = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };
  
  // Add the ImagePlaceholder component here after the helper functions are defined
  const ImagePlaceholder = ({ mealType }: { mealType: string }) => {
    // Get color based on meal type
    const getColor = () => {
      switch (mealType) {
        case 'breakfast': return colors.primary;
        case 'lunch': return '#F97316';
        case 'dinner': return '#8B5CF6';
        case 'snack': return '#10B981';
        default: return colors.primary;
      }
    };
    
    return (
      <View style={[styles.imagePlaceholder, { backgroundColor: isDark ? '#1F2937' : '#F3F4F6' }]}>
        {getTypeIcon(mealType)}
        <Text style={{ color: getColor(), fontSize: 12, marginTop: 8, fontWeight: '600' }}>
          {getFormattedType(mealType)} Image
        </Text>
      </View>
    );
  };
  
  const handleAddToToday = async (recommendation: MealRecommendation) => {
    try {
      setAddingMeal(recommendation.id);
      
      const { data, error, timedOut } = await safeRequest(
        () => addRecommendationToMeals(recommendation),
        {
          timeout: 6000,
          errorMessage: 'Failed to add meal',
        }
      );
      
      const result = data;
      
      if (timedOut) {
        Alert.alert(
          'Connection Issue',
          'The request timed out. The meal might have been added, but we couldn\'t confirm it.',
          [{ text: 'OK' }]
        );
        return;
      }
      
      if (result?.success) {
        Alert.alert(
          'Added to Today',
          `${recommendation.name} has been added to today's meals.`,
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Error',
          `Failed to add meal: ${result?.error || 'Unknown error'}`,
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error adding meal:', error);
      Alert.alert(
        'Error',
        'Something went wrong when adding this meal.',
        [{ text: 'OK' }]
      );
    } finally {
      setAddingMeal(null);
    }
  };
  
  const toggleFavorite = async (recommendation: MealRecommendation) => {
    try {
      // Update state optimistically
      const newFavorites = { ...favorites };
      const isFavorite = !favorites[recommendation.id];
      newFavorites[recommendation.id] = isFavorite;
      setFavorites(newFavorites);
      
      // Call the appropriate API based on new state
      const action = isFavorite ? addMealToFavorites : removeMealFromFavorites;
      
      const { data, error, timedOut } = await safeRequest(
        () => action(recommendation.id),
        {
          timeout: 6000,
          errorMessage: `Failed to ${isFavorite ? 'add to' : 'remove from'} favorites`,
          retryCount: 1
        }
      );
      
      // If request failed or timed out, revert the optimistic update
      if (error || timedOut) {
        const revertedFavorites = { ...favorites };
        revertedFavorites[recommendation.id] = !isFavorite;
        setFavorites(revertedFavorites);
        
        console.error('Error toggling favorite status:', error);
      }
    } catch (error) {
      console.error('Error toggling favorite status:', error);
      
      // Revert on error
      const revertedFavorites = { ...favorites };
      revertedFavorites[recommendation.id] = !favorites[recommendation.id];
      setFavorites(revertedFavorites);
    }
  };
  
  const handleImageError = (mealId: string) => {
    setFailedImages(prev => ({
      ...prev,
      [mealId]: true
    }));
  };
  
  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: isDark ? 'rgba(30,30,40,0.6)' : 'rgba(255,255,255,0.6)' }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading recommendations...</Text>
      </View>
    );
  }
  
  return (
    <Animated.View 
      style={[
        styles.cardSection, 
        { 
          opacity: fadeAnim,
          ...(Platform.OS === 'web' 
            ? { transform: [] } // On web, just use opacity animation to avoid transform issues
            : { transform: [{ translateY: Animated.multiply(translateY, 0.7) }] }
          )
        }
      ]}
    >
      <View style={styles.sectionHeader}>
        <View style={styles.titleContainer}>
          <MaterialIcons name="restaurant" size={18} color={colors.primary} style={styles.sectionIcon} />
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Today's Recommendations</Text>
        </View>
        
        <TouchableOpacity 
          style={[styles.seeAllButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.03)' }]}
          onPress={() => router.push('/meal-planner')}
          accessibilityLabel="View all recommendations"
          accessibilityHint="See all meal recommendations"
        >
          <Text style={[styles.seeAllText, { color: colors.primary }]}>View All</Text>
          <Feather name="chevron-right" size={14} color={colors.primary} />
        </TouchableOpacity>
      </View>
      
      {error && (
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.textSecondary }]}>{error}</Text>
          <TouchableOpacity 
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={loadRecommendations}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      )}
      
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.mealsScrollContainer}
      >
        {recommendations.map((meal, index) => (
          <TouchableOpacity
            key={`meal-${index}`}
            style={[styles.mealCard, { 
              backgroundColor: isDark ? 'rgba(25,25,35,0.9)' : 'rgba(255,255,255,0.95)', 
              borderColor: isDark ? 'rgba(80,80,100,0.3)' : 'rgba(0,0,0,0.04)',
              shadowColor: isDark ? '#000' : 'rgba(0,0,0,0.1)',
            }]}
            onPress={() => router.push({
              pathname: '/recipe/[id]',
              params: { id: meal.id }
            })}
            activeOpacity={0.9}
            accessibilityLabel={`${meal.name}, ${meal.calories} calories`}
          >
            <View style={styles.imageContainer}>
              {failedImages[meal.id] ? (
                <ImagePlaceholder mealType={meal.type} />
              ) : (
                <Image
                  source={{ uri: meal.image_url || FALLBACK_IMAGE_URL }}
                  style={styles.mealImage}
                  resizeMode="cover"
                  accessibilityLabel={`Image of ${meal.name}`}
                  onError={() => handleImageError(meal.id)}
                />
              )}
              <LinearGradient 
                colors={['transparent', 'rgba(0,0,0,0.6)']}
                style={[styles.imageGradient, failedImages[meal.id] ? { opacity: 0.3 } : {}]}
              />
              <View style={[styles.typeChip, { backgroundColor: getMealTypeColor(meal.type)[0] }]}>
                {getTypeIcon(meal.type)}
                <Text style={styles.typeText}>{getFormattedType(meal.type)}</Text>
              </View>
              
              {/* Favorite button */}
              <TouchableOpacity 
                style={styles.favoriteButton}
                onPress={() => toggleFavorite(meal)}
                accessibilityLabel={favorites[meal.id] ? "Remove from favorites" : "Add to favorites"}
              >
                <Feather name="heart" size={20} color={favorites[meal.id] ? "#F43F5E" : "white"} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.mealInfo}>
              <Text style={[styles.mealName, { color: colors.text }]} numberOfLines={2}>
                {meal.name}
              </Text>
              
              <View style={styles.macroInfo}>
                <View style={styles.macroItem}>
                  <Text style={[styles.macroValue, { color: colors.text }]}>{meal.calories}</Text>
                  <Text style={[styles.macroLabel, { color: colors.textSecondary }]}>cal</Text>
                </View>
                
                <View style={styles.macroSeparator} />
                
                <View style={styles.macroItem}>
                  <Text style={[styles.macroValue, { color: colors.text }]}>{meal.protein}g</Text>
                  <Text style={[styles.macroLabel, { color: colors.textSecondary }]}>protein</Text>
                </View>
              </View>
              
              <TouchableOpacity 
                style={[styles.addButton, { 
                  backgroundColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.04)',
                  opacity: addingMeal === meal.id ? 0.7 : 1
                }]}
                onPress={() => handleAddToToday(meal)}
                disabled={addingMeal === meal.id}
              >
                {addingMeal === meal.id ? (
                  <ActivityIndicator size="small" color={colors.primary} />
                ) : (
                  <Text style={[styles.addButtonText, { color: colors.primary }]}>Add to Today</Text>
                )}
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
      
      <TouchableOpacity 
        style={[styles.refreshButton, { backgroundColor: colors.primary + '20', marginTop: 12 }]}
        onPress={loadRecommendations}
      >
        <Text style={[styles.refreshButtonText, { color: colors.primary }]}>Refresh Recommendations</Text>
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  cardSection: {
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIcon: {
    marginRight: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
  },
  seeAllText: {
    fontSize: 13,
    fontWeight: '600',
    marginRight: 2,
  },
  mealsScrollContainer: {
    paddingBottom: 8,
  },
  mealCard: {
    width: 220,
    height: 320,
    borderRadius: 20,
    marginRight: 16,
    borderWidth: 1,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: 160,
  },
  mealImage: {
    width: '100%',
    height: '100%',
  },
  imageGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: 60,
  },
  typeChip: {
    position: 'absolute',
    top: 12,
    left: 12,
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  favoriteButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: 'rgba(0,0,0,0.3)',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  mealInfo: {
    padding: 16,
    flex: 1,
    justifyContent: 'space-between',
  },
  mealName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  macroInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  macroItem: {
    alignItems: 'center',
  },
  macroValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  macroLabel: {
    fontSize: 12,
    marginTop: 2,
  },
  macroSeparator: {
    width: 1,
    height: 24,
    backgroundColor: 'rgba(150,150,150,0.2)',
    marginHorizontal: 16,
  },
  addButton: {
    paddingVertical: 10,
    borderRadius: 12,
    alignItems: 'center',
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  loadingContainer: {
    padding: 40,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    fontWeight: '500',
  },
  errorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    padding: 12,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 0, 0, 0.05)',
  },
  errorText: {
    fontSize: 14,
    marginBottom: 10,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  refreshButton: {
    paddingVertical: 10,
    borderRadius: 12,
    alignItems: 'center',
  },
  refreshButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  imagePlaceholder: {
    width: '100%', 
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
}); 