import React, { useEffect } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Platform, Image } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import HeaderNotification from '@/components/HeaderNotification';

interface HeaderSectionProps {
  username: string;
  formattedDate: string;
}

export function HeaderSection({ username, formattedDate }: HeaderSectionProps) {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  
  // Default avatar or first letter of username
  const getAvatarContent = () => {
    if (username && username.trim().length > 0) {
      return username.charAt(0).toUpperCase();
    }
    return "?";
  };
  
  // For debugging purposes only
  console.log("HeaderSection rendered with username:", username ? `"${username}"` : "empty");
  
  // Extract only the day name and date number from the formatted date
  const getSimpleDate = () => {
    try {
      const parts = formattedDate.split(', ');
      if (parts.length > 1) {
        const dayOfWeek = parts[0];
        const dateParts = parts[1].split(' ');
        const dateNum = dateParts[1];
        return `${dayOfWeek}, ${dateNum} ${dateParts[0]}`;
      }
      return formattedDate;
    } catch (e) {
      return formattedDate;
    }
  };
  
  // Get a properly formatted display name or use "Welcome" if none available
  const getGreeting = () => {
    const timeOfDay = getTimeOfDay();
    if (username && username.trim().length > 0) {
      return `Good ${timeOfDay}, ${username}`;
    }
    return `Good ${timeOfDay}`;
  };
  
  // Navigate to profile function for consistency
  const navigateToProfile = () => {
    try {
      // Navigate directly to profile tab screen instead of the nested route
      router.push('/(tabs)/profile');
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };
  
  return (
    <View style={styles.headerSection}>
      {/* Background gradient */}
      <LinearGradient
        colors={isDark ? 
          ['rgba(99, 102, 241, 0.25)', 'rgba(30, 30, 45, 0)'] : 
          ['rgba(99, 102, 241, 0.15)', 'rgba(255, 255, 255, 0)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        style={styles.headerGradient}
      />
      
      {/* App Logo and Name */}
      <View style={styles.appBrandRow}>
        <View style={styles.logoContainer}>
          <View style={[
            styles.logoIconContainer, 
            { 
              backgroundColor: `${colors.primary}15`,
              shadowColor: colors.primary,
              shadowOffset: { width: 0, height: 0 },
              shadowOpacity: 0.2,
              shadowRadius: 8,
            }
          ]}>
            <Feather name="heart" size={20} color={colors.primary} style={styles.logoIcon} />
          </View>
          <Text style={[styles.logoText, { color: colors.text }]}>
            <Text style={{ color: colors.primary, fontWeight: '800' }}>Choose</Text>
            <Text style={{ fontWeight: '600' }}>Healthy</Text>
          </Text>
        </View>
        
        {/* Notification icon only */}
        <View style={styles.actionIcons}>
          <TouchableOpacity 
            style={[styles.iconButton, { 
              backgroundColor: isDark ? 'rgba(255,255,255,0.07)' : 'rgba(0,0,0,0.04)',
              borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
            }]}
            onPress={() => router.push('/profile-screens/notifications')}
            accessibilityLabel="Notifications"
            accessibilityHint="View your notifications"
          >
            <Feather name="bell" size={18} color={colors.text} />
            <View style={[styles.notificationBadge, { 
              backgroundColor: '#f43f5e',
              borderColor: isDark ? colors.card : 'white'
            }]} />
          </TouchableOpacity>
        </View>
      </View>
      
      {/* User greeting and profile */}
      <View style={styles.profileRow}>
        {/* Left side - Greeting section */}
        <View style={styles.welcomeBlock}>
          <Text style={[styles.nameText, { color: colors.text }]}>
            {getGreeting()}
          </Text>
          <Text style={[styles.dateText, { color: colors.textSecondary }]}>
            {getSimpleDate()}
          </Text>
        </View>
        
        {/* Right side - Avatar button */}
        <View style={styles.actionsRow}>
          {/* Profile button */}
          <TouchableOpacity 
            style={[styles.profileButton, { 
              backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(255,255,255,0.7)',
              borderColor: isDark ? 'rgba(255,255,255,0.1)' : colors.border,
            }]}
            onPress={navigateToProfile}
            accessibilityLabel="Profile"
            accessibilityHint="Go to your profile"
          >
            <LinearGradient
              colors={['#6366f1', '#4338ca']}
              style={styles.avatarCircle}
            >
              <Text style={styles.avatarText}>
                {getAvatarContent()}
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
  
  // Helper function to get time of day greeting
  function getTimeOfDay() {
    const hour = new Date().getHours();
    if (hour < 12) return 'morning';
    if (hour < 18) return 'afternoon';
    return 'evening';
  }
}

const styles = StyleSheet.create({
  headerSection: {
    paddingHorizontal: 16,
    paddingTop: 14,
    paddingBottom: 16,
    overflow: 'hidden',
    position: 'relative',
  },
  headerGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '100%',
  },
  appBrandRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 18,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoIconContainer: {
    width: 38,
    height: 38,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        shadowColor: '#000',
      },
      android: {
        elevation: 2,
      },
    }),
  },
  logoIcon: {
    
  },
  logoText: {
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  actionIcons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  profileRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcomeBlock: {
    flex: 1,
  },
  nameText: {
    fontSize: 24,
    fontWeight: '700',
    letterSpacing: -0.5,
    marginBottom: 4,
  },
  dateText: {
    fontSize: 16,
    fontWeight: '500',
    letterSpacing: 0.2,
  },
  actionsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        shadowColor: '#000',
      },
      android: {
        elevation: 2,
      },
    }),
  },
  notificationBadge: {
    width: 8,
    height: 8,
    borderRadius: 4,
    position: 'absolute',
    top: 10,
    right: 10,
    borderWidth: 1.5,
  },
  profileButton: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        shadowColor: '#000',
      },
      android: {
        elevation: 2,
      },
    }),
  },
  avatarCircle: {
    width: 36,
    height: 36,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
}); 