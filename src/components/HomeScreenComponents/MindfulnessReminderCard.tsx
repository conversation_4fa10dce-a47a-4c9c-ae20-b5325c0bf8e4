import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Animated, Platform, ActivityIndicator, Image } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { getRandomMindfulnessTip, recordMindfulnessSession, MindfulnessTip as BaseMindfulnessTip } from '@/services/databaseService';
import { generateMindfulnessRecommendation, MindfulnessRecommendation } from '@/services/openai/mindfulnessService';
import { useRouter } from 'expo-router';
import { safeRequest } from '@/utils/requestUtils';
import { OPENAI_API_KEY } from '@/utils/config';

interface MindfulnessReminderCardProps {
  fadeAnim: Animated.Value;
  translateY: Animated.Value;
}

// Extend the base MindfulnessTip type with image_url
interface MindfulnessTip extends BaseMindfulnessTip {
  image_url?: string;
}

// Fallback tips in case the database request fails
const FALLBACK_TIPS = [
  {
    id: 'mindful-eating',
    title: 'Mindful Eating',
    message: 'Take a moment to appreciate your food. Eat slowly and savor each bite.',
    icon: 'eating',
    category: 'nutrition',
    gradient_start: '#FF9500',
    gradient_end: '#FF5A5F',
    image_url: 'https://images.pexels.com/photos/1640773/pexels-photo-1640773.jpeg?auto=compress&cs=tinysrgb&w=600',
  },
  {
    id: 'breathing',
    title: 'Deep Breathing',
    message: 'Take 5 deep breaths. Inhale for 4 counts, hold for 4, exhale for 8.',
    icon: 'breathing',
    category: 'stress-relief',
    gradient_start: '#9CECFB',
    gradient_end: '#65C7F7',
    image_url: 'https://images.pexels.com/photos/775417/pexels-photo-775417.jpeg?auto=compress&cs=tinysrgb&w=600',
  },
  {
    id: 'meditation',
    title: 'Quick Meditation',
    message: 'Find a quiet space. Close your eyes and focus on your breath for 3 minutes.',
    icon: 'meditation',
    category: 'mental-health',
    gradient_start: '#B06AB3',
    gradient_end: '#4568DC',
    image_url: 'https://images.pexels.com/photos/3560044/pexels-photo-3560044.jpeg?auto=compress&cs=tinysrgb&w=600',
  },
];

// Default fallback image URL if tips don't have one
const FALLBACK_IMAGE_URL = 'https://images.pexels.com/photos/3560168/pexels-photo-3560168.jpeg?auto=compress&cs=tinysrgb&w=600';

export function MindfulnessReminderCard({ fadeAnim, translateY }: MindfulnessReminderCardProps) {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const [currentTip, setCurrentTip] = useState<MindfulnessTip | null>(null);
  const [dismissed, setDismissed] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [completed, setCompleted] = useState(false);
  const [isAiGenerated, setIsAiGenerated] = useState(false);
  const [imageError, setImageError] = useState(false);
  
  useEffect(() => {
    loadMindfulnessTip();
    
    // Failsafe - if loading takes too long, use a fallback tip
    const timeoutId = setTimeout(() => {
      if (loading) {
        useFallbackTip();
      }
    }, 5000); // 5 second timeout
    
    return () => clearTimeout(timeoutId);
  }, []);
  
  const useFallbackTip = () => {
    console.log('MindfulnessReminderCard: Using fallback tip');
    // Choose a random fallback tip
    const fallbackTip = FALLBACK_TIPS[Math.floor(Math.random() * FALLBACK_TIPS.length)];
    setCurrentTip(fallbackTip as MindfulnessTip);
    setIsAiGenerated(false);
    setLoading(false);
    if (error === null) {
      setError('Could not load personalized tip. Showing a default suggestion.');
    }
  };
  
  const loadMindfulnessTip = async () => {
    try {
      setLoading(true);
      setError(null);
      setImageError(false);
      
      // First try database tip directly - skip AI if likely to fail
      let dbTip: MindfulnessTip | null = null;

      // Improved error handling for database tip fetching
      try {
        const result = await safeRequest<any>(
          () => getRandomMindfulnessTip(),
          {
            timeout: 8000, // Increase timeout to 8 seconds
            useCache: true,
            cacheKey: 'random_mindfulness_tip',
            cacheTTL: 1000 * 60 * 60 * 2, // 2 hour cache for database tips
            errorMessage: 'Error fetching random tip',
            retryCount: 2, // Add retry attempts
            fallbackData: FALLBACK_TIPS[0] // Provide fallback data
          }
        );
        
        dbTip = result.data as MindfulnessTip | null;
        
        if (result.timedOut) {
          console.error('Error fetching random tip: Error: Database request timed out');
        }
        
        if (result.fromCache) {
          console.log('Using cached mindfulness tip');
        }
      } catch (e) {
        console.error('Error in database tip request:', e);
      }
      
      // If we got a valid database tip, use it
      if (dbTip && Object.keys(dbTip).length > 0) {
        console.log('Successfully fetched mindfulness tip from database');
        
        // Add image URL if not present
        if (!dbTip.image_url) {
          dbTip.image_url = getCategoryImage(dbTip.category);
        }
        
        setCurrentTip(dbTip);
        setIsAiGenerated(false);
        setLoading(false);
        return; // Successfully set database tip, exit function
      } else {
        console.log('No valid tip returned from database, trying AI generation');
      }
      
      // Database tip failed, try AI recommendation as fallback
      let aiRecommendation: MindfulnessRecommendation | null = null;
      let aiError: Error | string | null = null;
      let aiTimedOut = false;

      try {
        // Skip OpenAI if we have no API key configured
        if (!OPENAI_API_KEY) {
          console.log('No OpenAI API key found, skipping AI recommendation');
          throw new Error('No OpenAI API key configured');
        }
        
        console.log('Generating AI recommendation with API key:', 
          OPENAI_API_KEY ? `Present (${OPENAI_API_KEY.slice(0, 5)}...)` : 'Missing');
        
        const result = await safeRequest<MindfulnessRecommendation | null>(
          () => generateMindfulnessRecommendation({
            // Add user context data here
            timeOfDay: getTimeOfDay(),
            stressLevel: 4, // Example value, ideally would be from user data
          }),
          {
            timeout: 10000, // Increase timeout for AI generation
            useCache: true,
            cacheKey: 'ai_mindfulness_tip',
            cacheTTL: 1000 * 60 * 60 * 6, // 6 hour cache for AI tips
            errorMessage: 'Error generating AI recommendation',
            retryCount: 1, // Add retry attempt
            fallbackData: null // No specific fallback for AI
          }
        );
        
        console.log('AI recommendation result:', result);
        
        aiRecommendation = result.data;
        aiError = result.error;
        aiTimedOut = result.timedOut;
      } catch (e) {
        console.error('Error in AI recommendation request:', e);
        aiError = 'Error generating AI recommendation';
      }
      
      if (aiTimedOut) {
        console.error('Error generating AI recommendation: Error: OpenAI request timed out');
      }
      
      // If we got a valid AI recommendation, use it
      if (aiRecommendation && aiRecommendation.title && aiRecommendation.message) {
        // Add an image URL to the AI recommendation
        const imageCategory = aiRecommendation.category || 'wellness';
        
        // Convert AI recommendation to the format expected by the component
        const aiTip: MindfulnessTip = {
          id: 'ai-generated-' + Date.now(),
          title: aiRecommendation.title,
          message: aiRecommendation.message,
          icon: aiRecommendation.icon || 'meditation',
          category: aiRecommendation.category || 'wellness',
          gradient_start: aiRecommendation.gradient_start || '#9CECFB',
          gradient_end: aiRecommendation.gradient_end || '#65C7F7',
          image_url: getCategoryImage(imageCategory),
        };
        
        setCurrentTip(aiTip);
        setIsAiGenerated(true);
        setLoading(false);
        return; // Successfully set AI tip, exit function
      }
      
      // If we get here, both database and AI tips failed
      console.warn('Both database and AI tips failed, using fallback');
      useFallbackTip();
      
    } catch (error) {
      console.error('Error loading mindfulness tip:', error);
      setError('Could not load mindfulness tip. Showing a default suggestion.');
      // Use a fallback tip on error
      useFallbackTip();
    } finally {
      // Ensure loading state is set to false in all cases
      setLoading(false);
    }
  };
  
  // Get appropriate image URL for a category
  const getCategoryImage = (category: string): string => {
    switch (category) {
      case 'nutrition':
        return 'https://images.pexels.com/photos/1640773/pexels-photo-1640773.jpeg?auto=compress&cs=tinysrgb&w=600';
      case 'stress-relief':
        return 'https://images.pexels.com/photos/775417/pexels-photo-775417.jpeg?auto=compress&cs=tinysrgb&w=600';
      case 'mental-health':
        return 'https://images.pexels.com/photos/3560044/pexels-photo-3560044.jpeg?auto=compress&cs=tinysrgb&w=600';
      case 'movement':
        return 'https://images.pexels.com/photos/4056723/pexels-photo-4056723.jpeg?auto=compress&cs=tinysrgb&w=600';
      case 'wellness':
        return 'https://images.pexels.com/photos/3560168/pexels-photo-3560168.jpeg?auto=compress&cs=tinysrgb&w=600';
      default:
        return FALLBACK_IMAGE_URL;
    }
  };
  
  // Get time of day: morning, afternoon, evening, night
  const getTimeOfDay = (): string => {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 17) return 'afternoon';
    if (hour >= 17 && hour < 21) return 'evening';
    return 'night';
  };
  
  const handleComplete = async () => {
    if (!currentTip) return;
    
    setCompleted(true);
    
    try {
      const sessionData = {
        tip_id: currentTip.id,
        session_date: new Date().toISOString().split('T')[0],
        completed: true,
        notes: isAiGenerated ? 'AI-generated recommendation' : undefined
      };
      
      // Record the mindfulness session with proper error handling
      const { error, timedOut } = await safeRequest(
        () => recordMindfulnessSession(sessionData),
        {
          timeout: 5000,
          errorMessage: 'Error recording mindfulness session',
        }
      );
      
      if (error || timedOut) {
        console.error('Failed to record mindfulness session:', error || 'Timeout');
        // The user still sees the tip as completed, we just couldn't save it
      }
    } catch (error) {
      console.error('Error recording mindfulness session:', error);
    }
  };
  
  const handleViewMore = () => {
    router.push('/mindfulness');
  };
  
  const handleRefresh = () => {
    console.log('Refreshing mindfulness tip...');
    // Reset states first
    setLoading(true);
    setError(null);
    setCompleted(false);
    setCurrentTip(null); // Clear current tip to ensure UI updates
    
    // Use setTimeout to give UI time to update before loading the new tip
    setTimeout(() => {
      loadMindfulnessTip();
    }, 100);
  };
  
  const handleImageError = () => {
    console.warn('Mindfulness tip image failed to load');
    setImageError(true);
  };
  
  const getIcon = (iconName: string) => {
    switch (iconName) {
      case 'eating':
        return <Feather name="sun" size={18}  color={colors.text} />;
      case 'breathing':
        return <Wind size={18} color="#fff" />;
      case 'hydration':
        return <Droplets size={18} color="#fff" />;
      case 'gratitude':
        return <Feather name="heart" size={18}  color={colors.text} />;
      case 'stretch':
        return <Feather size={18} color="#fff" />;
      case 'meditation':
        return <Feather name="moon" size={18}  color={colors.text} />;
      default:
        return <Feather name="clock" size={18}  color={colors.text} />;
    }
  };
  
  // Skip this component if it's been dismissed
  if (dismissed) {
    return null;
  }
  
  if (loading) {
    return (
      <Animated.View
        style={[
          styles.container,
          {
            opacity: fadeAnim,
            transform: [{ translateY: Animated.multiply(translateY, 0.8) }],
            backgroundColor: isDark ? 'rgba(30,30,40,0.6)' : 'rgba(255,255,255,0.6)',
            borderWidth: isDark ? 1 : 0,
            borderColor: 'rgba(255,255,255,0.08)',
            shadowOpacity: isDark ? 0.2 : 0.1,
            justifyContent: 'center',
            alignItems: 'center',
            padding: 30,
          }
        ]}
      >
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Generating personalized mindfulness tip...
        </Text>
      </Animated.View>
    );
  }
  
  // Always display a fallback tip if no current tip is available
  if (!currentTip) {
    const fallbackTip = FALLBACK_TIPS[0];
    return (
      <Animated.View
        style={[
          styles.container,
          {
            opacity: fadeAnim,
            transform: [{ translateY: Animated.multiply(translateY, 0.8) }],
            backgroundColor: isDark ? 'rgba(30,30,40,0.6)' : 'rgba(255,255,255,0.6)',
            borderWidth: isDark ? 1 : 0,
            borderColor: 'rgba(255,255,255,0.08)',
            shadowOpacity: isDark ? 0.2 : 0.1,
          }
        ]}
      >
        {Platform.OS === 'ios' ? (
          <BlurView
            intensity={isDark ? 40 : 60}
            tint={isDark ? 'dark' : 'light'}
            style={styles.blurContainer}
          />
        ) : null}
        
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.textSecondary }]}>
            Could not load mindfulness tip. Showing a default suggestion.
          </Text>
        </View>
        
        {fallbackTip.image_url && (
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: fallbackTip.image_url }}
              style={styles.backgroundImage}
              resizeMode="cover"
            />
            <View style={[styles.imageDim, { backgroundColor: isDark ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.2)' }]} />
          </View>
        )}
        
        <LinearGradient
          colors={[fallbackTip.gradient_start, fallbackTip.gradient_end]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.iconContainer}
        >
          {getIcon(fallbackTip.icon)}
        </LinearGradient>
        
        <View style={styles.contentContainer}>
          <Text style={[styles.title, { color: colors.text }]}>{fallbackTip.title}</Text>
          <Text style={[styles.message, { color: colors.textSecondary }]}>
            {fallbackTip.message}
          </Text>
          
          <View style={styles.categoryChip}>
            <Text style={styles.categoryText}>{fallbackTip.category}</Text>
          </View>
        </View>
        
        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={[styles.refreshButton, { 
              backgroundColor: isDark ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)',
              borderRadius: 8
            }]}
            onPress={handleRefresh}
            accessibilityLabel="Try again"
            accessibilityHint="Try loading a mindfulness tip again"
          >
            <Text style={[styles.buttonText, { 
              color: isDark ? '#3B82F6' : '#3B82F6',
              fontWeight: '600'
            }]}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  }
  
  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ translateY: Animated.multiply(translateY, 0.8) }],
          backgroundColor: isDark ? 'rgba(30,30,40,0.6)' : 'rgba(255,255,255,0.6)',
          borderWidth: isDark ? 1 : 0,
          borderColor: 'rgba(255,255,255,0.08)',
          shadowOpacity: isDark ? 0.2 : 0.1,
        }
      ]}
    >
      {Platform.OS === 'ios' ? (
        <BlurView
          intensity={isDark ? 40 : 60}
          tint={isDark ? 'dark' : 'light'}
          style={styles.blurContainer}
        />
      ) : null}
      
      {error && (
        <View style={[styles.errorContainer, { backgroundColor: isDark ? 'rgba(255,0,0,0.1)' : 'rgba(255,0,0,0.05)' }]}>
          <Text style={[styles.errorText, { color: colors.textSecondary }]}>{error}</Text>
        </View>
      )}
      
      {currentTip.image_url && !imageError && (
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: currentTip.image_url || FALLBACK_IMAGE_URL }}
            style={styles.backgroundImage}
            onError={handleImageError}
            resizeMode="cover"
          />
          <View style={[styles.imageDim, { backgroundColor: isDark ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.2)' }]} />
        </View>
      )}
      
      <LinearGradient
        colors={[currentTip.gradient_start || '#9CECFB', currentTip.gradient_end || '#65C7F7']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.iconContainer}
      >
        {getIcon(currentTip.icon)}
      </LinearGradient>
      
      <View style={styles.contentContainer}>
        <View style={styles.titleRow}>
          <Text style={[styles.title, { color: colors.text }]}>{currentTip.title}</Text>
          {isAiGenerated && (
            <View style={styles.aiTag}>
              <Text style={styles.aiTagText}>AI</Text>
            </View>
          )}
        </View>
        
        <Text style={[styles.message, { color: colors.textSecondary }]}>
          {currentTip.message}
        </Text>
        
        <View style={styles.categoryChip}>
          <Text style={styles.categoryText}>{currentTip.category}</Text>
        </View>
      </View>
      
      <View style={styles.buttonRow}>
        <TouchableOpacity
          style={[styles.completeButton, completed && styles.disabledButton]}
          onPress={handleComplete}
          disabled={completed}
          accessibilityLabel="Complete mindfulness activity"
          accessibilityHint="Marks this mindfulness activity as completed"
        >
          <Text 
            style={[
              styles.completeButtonText, 
              { color: colors.primary },
              completed && styles.disabledButtonText
            ]}
          >
            {completed ? 'Completed!' : 'Mark as Done'}
          </Text>
        </TouchableOpacity>
        
        <View style={styles.buttonGroup}>
          <TouchableOpacity
            style={[styles.refreshButton, { 
              backgroundColor: isDark ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)',
              borderRadius: 8,
              marginRight: 8
            }]}
            onPress={handleRefresh}
            accessibilityLabel="Refresh mindfulness tip"
            accessibilityHint="Generate a new mindfulness tip"
          >
            <Text style={[styles.buttonText, { 
              color: isDark ? '#3B82F6' : '#3B82F6',
              fontWeight: '600'
            }]}>New Tip</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.dismissButton}
            onPress={handleViewMore}
            accessibilityLabel="View more mindfulness tips"
            accessibilityHint="Opens the mindfulness screen to view more tips"
          >
            <Text style={[styles.dismissText, { color: colors.primary }]}>View More</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    marginVertical: 8,
    overflow: 'hidden',
    position: 'relative',
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 4 },
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  blurContainer: {
    ...StyleSheet.absoluteFillObject,
  },
  iconContainer: {
    position: 'absolute',
    top: 16,
    left: 16,
    width: 36,
    height: 36,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  contentContainer: {
    padding: 16,
    paddingLeft: 68,
    paddingBottom: 60,
    zIndex: 2,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginRight: 8,
  },
  message: {
    fontSize: 15,
    lineHeight: 22,
    opacity: 0.8,
    marginBottom: 12,
  },
  categoryChip: {
    alignSelf: 'flex-start',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: 'rgba(150,150,150,0.15)',
    marginTop: 4,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '500',
    color: 'rgba(150,150,150,0.9)',
    textTransform: 'capitalize',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'absolute',
    bottom: 12,
    right: 16,
    left: 16,
    zIndex: 2,
  },
  buttonGroup: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  completeButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: 'rgba(130,130,150,0.1)',
  },
  completeButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  disabledButton: {
    backgroundColor: 'rgba(130,130,150,0.05)',
  },
  disabledButtonText: {
    opacity: 0.5,
  },
  dismissButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  refreshButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginRight: 4,
    minWidth: 80,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  dismissText: {
    fontSize: 14,
    fontWeight: '500',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
  },
  aiTag: {
    backgroundColor: '#10B981',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  aiTagText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '700',
  },
  imageContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
  },
  backgroundImage: {
    width: '100%',
    height: '100%',
  },
  imageDim: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1,
  },
  errorContainer: {
    margin: 8,
    padding: 8,
    borderRadius: 8,
    zIndex: 3,
  },
  errorText: {
    fontSize: 12,
    textAlign: 'center',
  },
}); 