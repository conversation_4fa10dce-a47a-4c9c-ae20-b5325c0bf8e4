import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Animated, Platform } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

interface NutritionSummaryProps {
  fadeAnim: Animated.Value;
  translateY: Animated.Value;
  dailyStats: {
    calories: number;
    target: number;
    protein: number;
    carbs: number;
    fat: number;
  };
}

export function NutritionSummary({ fadeAnim, translateY, dailyStats }: NutritionSummaryProps) {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  
  // Calculate percentages for macros
  const totalMacros = dailyStats.protein + dailyStats.carbs + dailyStats.fat;
  const proteinPercentage = totalMacros > 0 ? Math.round((dailyStats.protein / totalMacros) * 100) : 0;
  const carbsPercentage = totalMacros > 0 ? Math.round((dailyStats.carbs / totalMacros) * 100) : 0;
  const fatPercentage = totalMacros > 0 ? Math.round((dailyStats.fat / totalMacros) * 100) : 0;
  
  // Color codes for macro nutrients
  const macroColors = {
    protein: '#3B82F6', // Blue
    carbs: '#8B5CF6',   // Purple
    fat: '#F59E0B'      // Amber
  } as const;
  
  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          ...(Platform.OS === 'web' 
            ? { transform: [] } // On web, just use opacity animation to avoid transform issues
            : { transform: [{ translateY: Animated.multiply(translateY, 0.6) }] }
          )
        }
      ]}
    >
      <View style={styles.sectionHeader}>
        <View style={styles.titleWithIcon}>
          <View style={[styles.iconCircle, { backgroundColor: colors.primary + '15' }]}>
            <Feather name="zap" size={16} color={colors.primary} />
          </View>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Nutrition Summary</Text>
        </View>
        
        <TouchableOpacity 
          style={styles.seeAllButton}
          onPress={() => router.push('/(tabs)/history')}
          accessibilityLabel="Nutrition details"
          accessibilityHint="View detailed nutrition information"
        >
          <Text style={[styles.seeAllText, { color: colors.primary }]}>Details</Text>
          <Feather name="chevron-right" size={14} color={colors.primary} />
        </TouchableOpacity>
      </View>
      
      <View style={[styles.nutritionCard, { 
        backgroundColor: isDark ? colors.card : 'white', 
        borderColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.04)',
        shadowColor: isDark ? '#000' : 'rgba(0,0,0,0.1)',
      }]}>
        {/* Calories Section */}
        <View style={styles.calorieSection}>
          <View style={styles.calorieSummary}>
            <Text style={[styles.caloriesLabel, { color: colors.textSecondary }]}>
              Daily calories
            </Text>
            <View style={styles.valueRow}>
              <Text style={[styles.caloriesValue, { color: colors.text }]}>
                {dailyStats.calories}
              </Text>
              <Text style={[styles.caloriesTarget, { color: colors.textSecondary }]}>
                /{dailyStats.target}
              </Text>
            </View>
            <Text style={[styles.caloriesRemaining, { color: isDark ? colors.primary + '90' : colors.primary }]}>
              {dailyStats.target - dailyStats.calories > 0 ? `${dailyStats.target - dailyStats.calories} remaining` : 'Daily goal reached'}
            </Text>
          </View>
          
          {/* Calorie Progress */}
          <View style={styles.calorieProgressContainer}>
            <View style={[styles.progressRing, { 
              borderColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.05)' 
            }]}>
              <View style={[styles.progressFill, { 
                height: `${Math.min(100, Math.round((dailyStats.calories / dailyStats.target) * 100))}%`,
                backgroundColor: colors.primary,
              }]} />
            </View>
            <Text style={[styles.progressPercentage, { color: colors.primary }]}>
              {Math.round((dailyStats.calories / dailyStats.target) * 100)}%
            </Text>
          </View>
        </View>
        
        {/* Divider */}
        <View style={[styles.divider, { backgroundColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.05)' }]} />
        
        {/* Macros Distribution */}
        <View style={styles.macroSection}>
          <Text style={[styles.macroSectionTitle, { color: colors.textSecondary }]}>
            Macro Nutrients
          </Text>
          
          {/* Macro Percentages Bar */}
          <View style={styles.macroBar}>
            <View style={styles.macroBarContainer}>
              <View style={[styles.macroBarSegment, { 
                backgroundColor: macroColors.protein,
                width: `${proteinPercentage}%`,
              }]} />
              <View style={[styles.macroBarSegment, { 
                backgroundColor: macroColors.carbs,
                width: `${carbsPercentage}%`,
              }]} />
              <View style={[styles.macroBarSegment, { 
                backgroundColor: macroColors.fat,
                width: `${fatPercentage}%`,
              }]} />
            </View>
          </View>
          
          {/* Macro Details */}
          <View style={styles.macroDetails}>
            <MacroDetail 
              label="Protein"
              value={`${dailyStats.protein}g`}
              color={macroColors.protein}
              percentage={proteinPercentage}
              textColor={colors.text}
              secondaryColor={colors.textSecondary}
            />
            <MacroDetail 
              label="Carbs"
              value={`${dailyStats.carbs}g`}
              color={macroColors.carbs}
              percentage={carbsPercentage}
              textColor={colors.text}
              secondaryColor={colors.textSecondary}
            />
            <MacroDetail 
              label="Fat"
              value={`${dailyStats.fat}g`}
              color={macroColors.fat}
              percentage={fatPercentage}
              textColor={colors.text}
              secondaryColor={colors.textSecondary}
            />
          </View>
        </View>
      </View>
    </Animated.View>
  );
}

interface MacroDetailProps {
  label: string;
  value: string;
  color: string;
  percentage: number;
  textColor: string;
  secondaryColor: string;
}

// Helper component for each macro nutrient detail
function MacroDetail({ label, value, color, percentage, textColor, secondaryColor }: MacroDetailProps) {
  return (
    <View style={styles.macroDetailItem}>
      <View style={styles.macroDetailHeader}>
        <View style={[styles.macroColorDot, { backgroundColor: color }]} />
        <Text style={[styles.macroLabel, { color: secondaryColor }]}>{label}</Text>
        <Text style={[styles.macroPercentage, { color }]}>{percentage}%</Text>
      </View>
      <Text style={[styles.macroValue, { color: textColor }]}>{value}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 12,
  },
  titleWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconCircle: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.03)',
  },
  seeAllText: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 2,
  },
  nutritionCard: {
    marginHorizontal: 20,
    borderRadius: 20,
    padding: 18,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.12,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  calorieSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  calorieSummary: {
    flex: 1,
  },
  caloriesLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  valueRow: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  caloriesValue: {
    fontSize: 28,
    fontWeight: '700',
    letterSpacing: -0.5,
  },
  caloriesTarget: {
    fontSize: 16,
    marginLeft: 4,
  },
  caloriesRemaining: {
    fontSize: 13,
    fontWeight: '500',
    marginTop: 4,
  },
  calorieProgressContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
    height: 80,
  },
  progressRing: {
    height: 60,
    width: 10,
    borderRadius: 5,
    borderWidth: 1,
    overflow: 'hidden',
    justifyContent: 'flex-end',
  },
  progressFill: {
    width: '100%',
    borderRadius: 4,
  },
  progressPercentage: {
    fontSize: 12,
    fontWeight: '700',
    marginTop: 6,
  },
  divider: {
    height: 1,
    width: '100%',
    marginVertical: 16,
  },
  macroSection: {
    marginTop: 4,
  },
  macroSectionTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 12,
  },
  macroBar: {
    marginBottom: 16,
  },
  macroBarContainer: {
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(0,0,0,0.05)',
    flexDirection: 'row',
    overflow: 'hidden',
  },
  macroBarSegment: {
    height: '100%',
  },
  macroDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  macroDetailItem: {
    marginRight: 16,
  },
  macroDetailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  macroColorDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  macroLabel: {
    fontSize: 13,
    flex: 1,
  },
  macroPercentage: {
    fontSize: 13,
    fontWeight: '700',
    marginLeft: 4,
  },
  macroValue: {
    fontSize: 15,
    fontWeight: '600',
  },
}); 