import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface MealPlan {
  date: string;
  meals: {
    breakfast: Meal;
    lunch: Meal;
    dinner: Meal;
    snacks: Meal[];
  };
  nutritionSummary: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
}

interface Meal {
  name: string;
  nutrition: {
    calories: number;
  };
}

export default function RecentMealPlanCard() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const [todaysPlan, setTodaysPlan] = useState<MealPlan | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadTodaysPlan = async () => {
      try {
        setLoading(true);
        const savedPlans = await AsyncStorage.getItem('mealPlans');
        if (savedPlans) {
          const plans: MealPlan[] = JSON.parse(savedPlans);
          const today = new Date().toISOString().split('T')[0];
          const todayPlan = plans.find(plan => plan.date === today);
          setTodaysPlan(todayPlan || null);
        }
      } catch (error) {
        console.error('Error loading meal plan:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTodaysPlan();
  }, []);

  const goToMealPlanner = () => {
    router.push('/(tabs)/meal-planner');
  };

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? colors.card : '#fff', borderColor: colors.border }]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading meal plan...</Text>
        </View>
      </View>
    );
  }

  if (!todaysPlan) {
    return (
      <TouchableOpacity 
        style={[styles.container, { backgroundColor: isDark ? colors.card : '#fff', borderColor: colors.border }]}
        onPress={goToMealPlanner}
      >
        <View style={styles.emptyContainer}>
          <Feather name="calendar" size={24} color={colors.primary} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>No Meal Plan for Today</Text>
          <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
            Tap to create your personalized AI-generated meal plan
          </Text>
        </View>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity 
      style={[styles.container, { backgroundColor: isDark ? colors.card : '#fff', borderColor: colors.border }]}
      onPress={goToMealPlanner}
    >
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Feather name="calendar" size={18} color={colors.primary} style={styles.icon} />
          <Text style={[styles.title, { color: colors.text }]}>Today's Meal Plan</Text>
        </View>
        <View style={styles.nutritionSummary}>
          <Text style={[styles.calorieText, { color: colors.text }]}>
            {todaysPlan.nutritionSummary.calories} cal
          </Text>
        </View>
      </View>

      <View style={styles.mealsContainer}>
        {todaysPlan.meals.breakfast && (
          <View style={styles.mealItem}>
            <Text style={[styles.mealType, { color: colors.textSecondary }]}>Breakfast</Text>
            <Text style={[styles.mealName, { color: colors.text }]} numberOfLines={1}>
              {todaysPlan.meals.breakfast.name}
            </Text>
          </View>
        )}
        
        {todaysPlan.meals.lunch && (
          <View style={styles.mealItem}>
            <Text style={[styles.mealType, { color: colors.textSecondary }]}>Lunch</Text>
            <Text style={[styles.mealName, { color: colors.text }]} numberOfLines={1}>
              {todaysPlan.meals.lunch.name}
            </Text>
          </View>
        )}
        
        {todaysPlan.meals.dinner && (
          <View style={styles.mealItem}>
            <Text style={[styles.mealType, { color: colors.textSecondary }]}>Dinner</Text>
            <Text style={[styles.mealName, { color: colors.text }]} numberOfLines={1}>
              {todaysPlan.meals.dinner.name}
            </Text>
          </View>
        )}
      </View>

      <View style={[styles.footer, { borderTopColor: colors.border }]}>
        <Text style={[styles.footerText, { color: colors.primary }]}>View full meal plan</Text>
        <Feather name="chevron-right" size={16} color={colors.primary} />
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    overflow: 'hidden',
    borderWidth: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  nutritionSummary: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  calorieText: {
    fontSize: 14,
    fontWeight: '500',
  },
  mealsContainer: {
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  mealItem: {
    marginBottom: 10,
  },
  mealType: {
    fontSize: 12,
    marginBottom: 2,
  },
  mealName: {
    fontSize: 14,
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderTopWidth: 1,
  },
  footerText: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 14,
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 8,
    marginBottom: 4,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 20,
  }
}); 