import React, { useEffect } from 'react';
import { StyleSheet, Text, View, Animated, Platform, Pressable, Dimensions } from 'react-native';
import { Feather , MaterialIcons , Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';

// Get screen dimensions for responsive sizing
const { width: screenWidth } = Dimensions.get('window');
const cardHeight = Math.min(screenWidth * 0.32, 180); // Slightly taller cards

interface StatsSummaryProps {
  fadeAnim: Animated.Value;
  dailyStats: {
    calories: number;
    target: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  waterIntake: number;
  waterGoal: number;
}

export function StatsSummary({ fadeAnim, dailyStats, waterIntake, waterGoal = 2000 }: StatsSummaryProps) {
  const { colors, isDark } = useTheme();
  
  // Calculate percentage for calories
  const caloriePercentage = Math.min(100, Math.round((dailyStats.calories / dailyStats.target) * 100));
  
  // Calculate percentage for water
  const waterPercentage = Math.min(100, Math.round((waterIntake / waterGoal) * 100));
  
  // Get color based on percentage
  const getProgressColor = (percentage: number) => {
    if (percentage < 30) return ['#60A5FA', '#3B82F6']; // Blue
    if (percentage < 80) return ['#10B981', '#059669']; // Green
    if (percentage <= 100) return ['#F59E0B', '#D97706']; // Yellow/orange
    return ['#EF4444', '#DC2626']; // Red for over 100%
  };
  
  const progressColors = getProgressColor(caloriePercentage);
  const waterColors = ['#60A5FA', '#3B82F6']; // Always blue for water
  
  // Calculate protein target based on body weight (default to 50g if can't calculate)
  const proteinTarget = 50; // g
  const proteinPercentage = Math.min(100, Math.round((dailyStats.protein / proteinTarget) * 100));
  
  // Animation values
  const scaleAnim = React.useRef(new Animated.Value(0.95)).current;
  const opacityAnim = React.useRef(new Animated.Value(0.6)).current;
  const proteinWidthAnim = React.useRef(new Animated.Value(0)).current;
  const waterHeightAnim = React.useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    // Animate all elements on mount
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
      Animated.timing(proteinWidthAnim, {
        toValue: proteinPercentage,
        duration: 800,
        useNativeDriver: false,
      }),
      Animated.timing(waterHeightAnim, {
        toValue: waterPercentage,
        duration: 800,
        useNativeDriver: false,
      })
    ]).start();
  }, []);
  
  return (
    <Animated.View style={[styles.statsRow, { opacity: fadeAnim }]}>
      {/* Calories Card */}
      <Pressable style={styles.cardContainer}>
        <Animated.View 
          style={[
            styles.statCard, 
            { 
              backgroundColor: isDark ? 'rgba(25,25,35,0.9)' : 'rgba(255,255,255,0.95)', 
              borderColor: isDark ? 'rgba(80,80,100,0.3)' : 'rgba(0,0,0,0.04)',
              transform: [{ scale: scaleAnim }],
              opacity: opacityAnim,
              height: cardHeight,
            }
          ]}
        >
          <View style={styles.cardContent}>
            {/* Card Header */}
            <View style={styles.cardHeader}>
              <LinearGradient 
                colors={['#3B82F6', '#60A5FA']}
                style={styles.statIconCircle}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <Feather name="zap" size={18}  color={colors.text} />
              </LinearGradient>
              <Text style={[styles.statLabel, { color: colors.text }]}>Calories</Text>
            </View>
            
            {/* Main Value */}
            <View style={styles.valueContainer}>
              <View style={styles.valueRow}>
                <Text style={[styles.statValue, { color: colors.text }]}>{dailyStats.calories}</Text>
                <Text style={[styles.statTarget, { color: colors.textSecondary }]}>/{dailyStats.target}</Text>
              </View>
              
              {/* Percentage */}
              <Text style={[styles.percentageValue, { color: '#3B82F6' }]}>
                {caloriePercentage}%
              </Text>
            </View>
            
            {/* Subtitle */}
            <Text style={[styles.valueLabel, { color: colors.textSecondary }]}>
              daily intake
            </Text>
            
            {/* Bottom text */}
            <Text style={[styles.cardHelperText, { color: colors.textSecondary, marginTop: 'auto' }]}>
              {caloriePercentage < 30 ? 'Just Starting' : 
                caloriePercentage < 80 ? 'Good Progress' : 
                caloriePercentage <= 100 ? 'Almost Done!' : 'Over Limit!'}
            </Text>
          </View>
        </Animated.View>
      </Pressable>
      
      {/* Protein Card */}
      <Pressable style={styles.cardContainer}>
        <Animated.View 
          style={[
            styles.statCard, 
            { 
              backgroundColor: isDark ? 'rgba(25,25,35,0.9)' : 'rgba(255,255,255,0.95)',
              borderColor: isDark ? 'rgba(80,80,100,0.3)' : 'rgba(0,0,0,0.04)',
              transform: [{ scale: scaleAnim }],
              opacity: opacityAnim,
              height: cardHeight,
            }
          ]}
        >
          <View style={styles.cardContent}>
            {/* Card Header */}
            <View style={styles.cardHeader}>
              <LinearGradient 
                colors={['#3B82F6', '#60A5FA']}
                style={styles.statIconCircle}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <MaterialIcons name="restaurant" size={18}  color={colors.text} />
              </LinearGradient>
              <Text style={[styles.statLabel, { color: colors.text }]}>Protein</Text>
            </View>
            
            {/* Main Value */}
            <View style={styles.valueContainer}>
              <View style={styles.valueRow}>
                <Text style={[styles.statValue, { color: colors.text }]}>{dailyStats.protein}</Text>
                <Text style={[styles.proteinUnit, { color: colors.text }]}>g</Text>
                <Text style={[styles.proteinTarget, { color: colors.textSecondary }]}>/{proteinTarget}g</Text>
              </View>
            </View>
            
            {/* Subtitle */}
            <Text style={[styles.valueLabel, { color: colors.textSecondary }]}>
              daily intake
            </Text>
            
            {/* Progress bar */}
            <View style={[styles.linearProgressBg, { 
              backgroundColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.05)',
              marginTop: 'auto',
              marginBottom: 8,
            }]}>
              <Animated.View 
                style={[
                  styles.linearProgressFill, 
                  { 
                    backgroundColor: '#3B82F6',
                    width: proteinWidthAnim.interpolate({
                      inputRange: [0, 100],
                      outputRange: ['0%', '100%']
                    })
                  }
                ]}
              />
            </View>
            
            {/* Percentage */}
            <Text style={[styles.proteinPercentage, { color: '#3B82F6', alignSelf: 'flex-end' }]}>
              {proteinPercentage}%
            </Text>
            
            {/* Bottom text */}
            <Text style={[styles.cardHelperText, { color: colors.textSecondary }]}>
              {proteinPercentage < 30 ? 'Need More Protein' : 
               proteinPercentage < 80 ? 'Building Muscle' : 'Great Job!'}
            </Text>
          </View>
        </Animated.View>
      </Pressable>
      
      {/* Water Card */}
      <Pressable style={styles.cardContainer}>
        <Animated.View 
          style={[
            styles.statCard, 
            { 
              backgroundColor: isDark ? 'rgba(25,25,35,0.9)' : 'rgba(255,255,255,0.95)',
              borderColor: isDark ? 'rgba(80,80,100,0.3)' : 'rgba(0,0,0,0.04)',
              marginRight: 0, // Remove right margin for last card
              transform: [{ scale: scaleAnim }],
              opacity: opacityAnim,
              height: cardHeight,
            }
          ]}
        >
          <View style={styles.cardContent}>
            {/* Card Header */}
            <View style={styles.cardHeader}>
              <LinearGradient 
                colors={['#0EA5E9', '#38BDF8']}
                style={styles.statIconCircle}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <Ionicons name="water" size={18}  color={colors.text} />
              </LinearGradient>
              <Text style={[styles.statLabel, { color: colors.text }]}>Water</Text>
            </View>
            
            {/* Main Value */}
            <View style={styles.valueContainer}>
              <View style={styles.valueRow}>
                <Text style={[styles.statValue, { color: colors.text }]}>
                  {waterIntake >= 1000 ? `${(waterIntake / 1000).toFixed(1)}` : `${waterIntake}ml`}
                </Text>
                {waterIntake >= 1000 && <Text style={[styles.waterUnit, { color: colors.text }]}>L</Text>}
                <Text style={[styles.statTarget, { color: colors.textSecondary }]}>
                  /{waterGoal >= 1000 ? `${(waterGoal / 1000).toFixed(1)}L` : `${waterGoal}ml`}
                </Text>
              </View>
            </View>
            
            {/* Subtitle */}
            <Text style={[styles.valueLabel, { color: colors.textSecondary }]}>
              daily intake
            </Text>
            
            {/* Percentage indicator at bottom-right */}
            <Text style={[styles.waterPercentage, { 
              color: '#0EA5E9', 
              alignSelf: 'flex-end', 
              marginTop: 'auto',
              marginBottom: 8
            }]}>
              {waterPercentage}%
            </Text>
            
            {/* Bottom text */}
            <Text style={[styles.cardHelperText, { color: colors.textSecondary }]}>
              {waterPercentage < 30 ? 'Stay Hydrated!' : 
               waterPercentage < 80 ? 'Keep Drinking' : 'Well Hydrated!'}
            </Text>
          </View>
        </Animated.View>
      </Pressable>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  statsRow: {
    flexDirection: 'row',
    paddingHorizontal: 12,
    marginBottom: 20,
    gap: 8,
  },
  cardContainer: {
    flex: 1,
  },
  statCard: {
    flex: 1,
    borderRadius: 22,
    borderWidth: 1,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
        shadowColor: '#000',
      },
      android: {
        elevation: 6,
      },
    }),
  },
  cardContent: {
    padding: 16,
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  statIconCircle: {
    width: 32,
    height: 32,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  valueContainer: {
    marginBottom: 4,
  },
  valueRow: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  valueLabel: {
    fontSize: 12,
    marginTop: 2,
    opacity: 0.7,
  },
  statValue: {
    fontSize: 28,
    fontWeight: '700',
  },
  statTarget: {
    fontSize: 16,
    marginLeft: 2,
    fontWeight: '500',
  },
  statLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  percentageValue: {
    fontSize: 14,
    fontWeight: '700',
    position: 'absolute',
    right: 0,
    top: 8,
  },
  proteinUnit: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 1,
  },
  proteinTarget: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 2,
  },
  proteinPercentage: {
    fontSize: 14,
    fontWeight: '700',
  },
  waterUnit: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 1,
  },
  linearProgressBg: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  linearProgressFill: {
    height: '100%',
    borderRadius: 4,
  },
  waterPercentage: {
    fontSize: 14,
    fontWeight: '700',
  },
  cardHelperText: {
    fontSize: 12,
    fontWeight: '500',
  }
}); 