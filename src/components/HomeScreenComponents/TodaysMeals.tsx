import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Image, ScrollView, Animated, Platform } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';

interface Meal {
  id: string;
  name: string;
  time: string;
  calories: number;
  image: string;
}

interface TodaysMealsProps {
  fadeAnim: Animated.Value;
  translateY: Animated.Value;
  recentMeals: Meal[];
  onAddFood: () => void;
}

export function TodaysMeals({ fadeAnim, translateY, recentMeals, onAddFood }: TodaysMealsProps) {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  
  return (
    <Animated.View 
      style={[
        styles.cardSection, 
        { 
          opacity: fadeAnim,
          ...(Platform.OS === 'web' 
            ? { transform: [] } // On web, just use opacity animation to avoid transform issues
            : { transform: [{ translateY: Animated.multiply(translateY, 0.7) }] }
          )
        }
      ]}
    >
      <View style={styles.sectionHeader}>
        <View style={styles.titleContainer}>
          <Feather name="calendar" size={18} color={colors.primary} style={styles.sectionIcon} />
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Today's Meals</Text>
        </View>
        <TouchableOpacity 
          style={[styles.seeAllButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.03)' }]}
          onPress={() => router.push('/(tabs)/history')}
          accessibilityLabel="View all meals"
          accessibilityHint="See your complete meal history"
        >
          <Text style={[styles.seeAllText, { color: colors.primary }]}>View All</Text>
          <Feather name="chevron-right" size={14} color={colors.primary} />
        </TouchableOpacity>
      </View>
      
      {recentMeals.length > 0 ? (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.mealsScrollContainer}
        >
          {recentMeals.map((meal, index) => (
            <TouchableOpacity
              key={`meal-${index}`}
              style={[styles.mealCard, { 
                backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'white', 
                borderColor: isDark ? 'rgba(255,255,255,0.1)' : colors.border,
                shadowColor: isDark ? '#000' : 'rgba(0,0,0,0.1)',
              }]}
              onPress={() => router.push({
                pathname: '/recipe/[id]',
                params: { id: meal.id }
              })}
              activeOpacity={0.9}
              accessibilityLabel={`${meal.name}, ${meal.calories} calories`}
            >
              <View style={styles.imageContainer}>
                <Image
                  source={{ uri: meal.image }}
                  style={styles.mealImage}
                  resizeMode="cover"
                  accessibilityLabel={`Image of ${meal.name}`}
                />
                <LinearGradient 
                  colors={['transparent', 'rgba(0,0,0,0.4)']}
                  style={styles.imageGradient}
                />
                <View style={styles.timeChip}>
                  <Feather name="clock" size={12}  color={colors.text} />
                  <Text style={styles.timeText}>{meal.time}</Text>
                </View>
              </View>
              
              <View style={styles.mealInfo}>
                <Text style={[styles.mealName, { color: colors.text }]} numberOfLines={1}>
                  {meal.name}
                </Text>
                
                <View style={styles.mealCalorieWrapper}>
                  <View style={[styles.mealCalorieBlock, { backgroundColor: colors.primary + '15' }]}>
                    <Text style={[styles.mealCalories, { color: colors.primary }]}>
                      {meal.calories}
                    </Text>
                    <Text style={[styles.calorieUnit, { color: colors.primary + 'CC' }]}>kcal</Text>
                  </View>
                </View>
              </View>
            </TouchableOpacity>
          ))}
          
          <TouchableOpacity 
            style={[styles.addMealCard, { 
              backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)', 
              borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
            }]}
            onPress={onAddFood}
            activeOpacity={0.8}
            accessibilityLabel="Add a meal"
            accessibilityHint="Log a new meal"
          >
            <LinearGradient
              colors={['#6366f1', '#4f46e5']}
              style={styles.addMealGradient}
            >
              <Feather name="plus" size={24}  color={colors.text} />
            </LinearGradient>
            <Text style={[styles.addMealText, { color: colors.text }]}>Add Meal</Text>
          </TouchableOpacity>
        </ScrollView>
      ) : (
        <View style={[styles.emptyState, { 
          backgroundColor: isDark ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.02)', 
          borderColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.05)' 
        }]}>
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>No meals logged today</Text>
          <TouchableOpacity 
            style={styles.emptyButton}
            onPress={onAddFood}
            activeOpacity={0.8}
            accessibilityLabel="Add a meal"
            accessibilityHint="Log your first meal today"
          >
            <LinearGradient
              colors={['#6366f1', '#4f46e5']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.emptyButtonGradient}
            >
              <Feather name="plus" size={16} style={{ marginRight: 6 }} />
              <Text style={styles.emptyButtonText}>Add Meal</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  cardSection: {
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIcon: {
    marginRight: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 12,
  },
  seeAllText: {
    fontSize: 13,
    fontWeight: '600',
    marginRight: 2,
  },
  mealsScrollContainer: {
    paddingBottom: 8,
  },
  mealCard: {
    width: 180,
    borderRadius: 16,
    marginRight: 14,
    borderWidth: 1,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: 120,
  },
  mealImage: {
    width: '100%',
    height: '100%',
  },
  imageGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: 40,
  },
  timeChip: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 4,
  },
  mealInfo: {
    padding: 12,
  },
  mealName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  mealCalorieWrapper: {
    flexDirection: 'row',
  },
  mealCalorieBlock: {
    flexDirection: 'row',
    alignItems: 'baseline',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  mealCalories: {
    fontSize: 14,
    fontWeight: '700',
    marginRight: 4,
  },
  calorieUnit: {
    fontSize: 11,
  },
  addMealCard: {
    width: 120,
    height: 212,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 20,
    borderWidth: 1,
    borderStyle: 'dashed',
  },
  addMealGradient: {
    width: 52,
    height: 52,
    borderRadius: 26,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  addMealText: {
    fontSize: 14,
    fontWeight: '600',
  },
  emptyState: {
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
  },
  emptyText: {
    marginBottom: 16,
    fontSize: 14,
  },
  emptyButton: {
    borderRadius: 25,
    overflow: 'hidden',
  },
  emptyButtonGradient: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 10,
    alignItems: 'center',
  },
  emptyButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
}); 