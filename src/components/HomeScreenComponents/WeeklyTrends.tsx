import React from 'react';
import { StyleSheet, Text, View, Animated, Platform } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';

interface WeeklyData {
  day: string;
  calories: number;
  goal: number;
}

interface WeeklyTrendsProps {
  fadeAnim: Animated.Value;
  translateY: Animated.Value;
  weeklyData: WeeklyData[];
  streak: number;
}

export function WeeklyTrends({ fadeAnim, translateY, weeklyData, streak }: WeeklyTrendsProps) {
  const { colors, isDark } = useTheme();
  
  return (
    <Animated.View 
      style={[
        styles.cardSection, 
        { 
          opacity: fadeAnim,
          ...(Platform.OS === 'web' 
            ? { transform: [] } // On web, just use opacity animation to avoid transform issues
            : { transform: [{ translateY: Animated.multiply(translateY, 0.8) }] }
          )
        }
      ]}
    >
      <View style={styles.sectionHeader}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Weekly Trends</Text>
        <View style={[styles.trendBadge, { backgroundColor: colors.primary + '15' }]}>
          <Text style={[styles.trendText, { color: colors.primary }]}>{streak} day streak</Text>
        </View>
      </View>
      
      <View style={[styles.weeklyCard, { 
        backgroundColor: isDark ? colors.card : 'white', 
        borderColor: colors.border,
        shadowColor: isDark ? '#000' : 'rgba(0,0,0,0.1)',
      }]}>
        <View style={styles.chartContainer}>
          {weeklyData.map((day, index) => {
            const isOverGoal = day.calories > day.goal;
            const heightPercentage = Math.min(day.calories / day.goal, 1.5) * 100;
            const barColor = isOverGoal ? '#F59E0B' : colors.primary;
            const isToday = index === weeklyData.length - 1;
            
            return (
              <View key={`day-${index}`} style={styles.chartColumn}>
                <View style={styles.barContainer}>
                  <View 
                    style={[
                      styles.barFill, 
                      { 
                        height: `${heightPercentage}%`,
                        backgroundColor: barColor,
                        opacity: isToday ? 1 : 0.7,
                      }
                    ]}
                  />
                </View>
                <Text style={[
                  styles.dayLabel, 
                  { 
                    color: isToday ? colors.primary : colors.textSecondary,
                    fontWeight: isToday ? '700' : '500'
                  }
                ]}>
                  {day.day}
                </Text>
              </View>
            );
          })}
        </View>
        
        <View style={styles.chartLegend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: colors.primary }]} />
            <Text style={[styles.legendText, { color: colors.textSecondary }]}>Within goal</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: '#F59E0B' }]} />
            <Text style={[styles.legendText, { color: colors.textSecondary }]}>Over goal</Text>
          </View>
        </View>
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  cardSection: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  trendBadge: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 20,
  },
  trendText: {
    fontSize: 12,
    fontWeight: '600',
  },
  weeklyCard: {
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.1,
        shadowRadius: 6,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  chartContainer: {
    flexDirection: 'row',
    height: 150,
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    marginBottom: 16,
  },
  chartColumn: {
    flex: 1,
    alignItems: 'center',
  },
  barContainer: {
    width: '40%',
    height: '85%',
    justifyContent: 'flex-end',
  },
  barFill: {
    width: '100%',
    borderRadius: 4,
    minHeight: 4,
  },
  dayLabel: {
    fontSize: 12,
    marginTop: 8,
  },
  chartLegend: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 12,
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  legendText: {
    fontSize: 12,
  },
}); 