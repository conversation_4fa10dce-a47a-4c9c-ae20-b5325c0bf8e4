import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity, Image, Text, Platform, ActivityIndicator, Alert } from 'react-native';
import * as ImagePickerExpo from 'expo-image-picker';
import { useTheme } from '@/contexts/ThemeContext';


interface ImagePickerProps {
  onImageSelected: (uri: string) => void;
  loading?: boolean;
  error?: string;
}

export function ImagePicker({ onImageSelected, loading = false, error }: ImagePickerProps) {
  const { colors } = useTheme();
  const [image, setImage] = useState<string | null>(null);

  const pickImage = async () => {
    try {
      // Request media library permissions first
      const permissionResult = await ImagePickerExpo.requestMediaLibraryPermissionsAsync();
      
      if (!permissionResult.granted) {
        Alert.alert(
          "Permission Required", 
          "You need to grant permission to access your photo library",
          [{ text: "OK" }]
        );
        return;
      }
      
      // Launch image library
      const result = await ImagePickerExpo.launchImageLibraryAsync({
        mediaTypes: ImagePickerExpo.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled) {
        console.log("Image selected:", result.assets[0].uri);
        setImage(result.assets[0].uri);
        onImageSelected(result.assets[0].uri);
      }
    } catch (error) {
      console.error("Error picking image:", error);
      Alert.alert("Error", "There was a problem selecting your image. Please try again.");
    }
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity 
        style={[
          styles.pickerButton, 
          { 
            backgroundColor: colors.card,
            borderColor: colors.border,
            shadowColor: colors.shadow,
          }
        ]}
        onPress={pickImage}
        disabled={loading}
      >
        {image ? (
          <Image source={{ uri: image }} style={styles.image} />
        ) : (
          <View style={[styles.placeholder, { backgroundColor: colors.subtle }]}>
            <ImagePlus size={32} color={colors.textSecondary} />
            <Text style={[styles.placeholderText, { color: colors.textSecondary }]}>
              Upload from Gallery
            </Text>
          </View>
        )}
        
        {loading && (
          <View style={[styles.loadingOverlay, { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        )}
      </TouchableOpacity>
      
      {error && (
        <View style={styles.errorContainer}>
          <AlertCircle size={16} color={colors.error} style={styles.errorIcon} />
          <Text style={[styles.errorText, { color: colors.error }]}>{error}</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    alignItems: 'center',
  },
  pickerButton: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
      web: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      }
    }),
  },
  placeholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    marginTop: 12,
    fontSize: 16,
    fontWeight: '600',
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    padding: 8,
    borderRadius: 8,
  },
  errorIcon: {
    marginRight: 6,
  },
  errorText: {
    fontSize: 14,
  }
});