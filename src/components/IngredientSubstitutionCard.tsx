import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { IngredientSubstitution, calculateNutritionalImprovements } from '@/services/openai/healthySubstitutions';

interface IngredientSubstitutionCardProps {
  substitution: IngredientSubstitution;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
}

/**
 * A component that displays information about a healthier ingredient substitution
 * with nutritional benefits and cooking tips
 */
export function IngredientSubstitutionCard({
  substitution,
  isExpanded = false,
  onToggleExpand = () => {},
}: IngredientSubstitutionCardProps) {
  const { colors } = useTheme();
  
  // Calculate nutritional improvements if nutrition data is available
  const improvements = substitution.originalNutrition && substitution.substitutionNutrition
    ? calculateNutritionalImprovements(
        substitution.originalIngredient,
        substitution.substitution
      )
    : undefined;

  return (
    <View style={[styles.container, { backgroundColor: colors.card, borderColor: colors.border }]}>
      <TouchableOpacity 
        style={styles.header} 
        onPress={onToggleExpand}
        activeOpacity={0.7}
      >
        <View style={styles.titleContainer}>
          <Text style={[styles.title, { color: colors.text }]}>
            {substitution.originalIngredient} → {substitution.substitution}
          </Text>
          <View style={[styles.ratio, { backgroundColor: colors.primaryLight }]}>
            <Text style={[styles.ratioText, { color: colors.primary }]}>
              {substitution.ratio} ratio
            </Text>
          </View>
        </View>
        {isExpanded ? (
          <Feather name="chevron-up" size={20} color={colors.primary} />
        ) : (
          <Feather name="chevron-down" size={20} color={colors.primary} />
        )}
      </TouchableOpacity>
      
      {/* Summary visible even when collapsed */}
      <View style={styles.summaryContainer}>
        <Feather name="info" size={16} color={colors.primary} style={styles.infoIcon} />
        <Text style={[styles.summary, { color: colors.textSecondary }]}>
          {substitution.benefits}
        </Text>
      </View>
      
      {/* Expanded content */}
      {isExpanded && (
        <View style={styles.expandedContent}>
          {/* Culinary notes */}
          {substitution.culinaryNotes && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Cooking Tips
              </Text>
              <Text style={[styles.sectionContent, { color: colors.textSecondary }]}>
                {substitution.culinaryNotes}
              </Text>
            </View>
          )}
          
          {/* Nutritional improvements */}
          {improvements && Object.keys(improvements).length > 0 && (
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>
                Nutritional Improvements
              </Text>
              <View style={styles.nutritionList}>
                {Object.entries(improvements).map(([key, percentChange]) => {
                  // Skip if the change is not significant
                  if (Math.abs(percentChange) < 1) return null;
                  
                  const isPositive = (key === 'fiber' || key === 'protein') 
                    ? percentChange > 0 
                    : percentChange < 0;
                  
                  const formattedKey = key.replace(/([A-Z])/g, ' $1')
                    .replace(/^./, str => str.toUpperCase());
                  
                  return (
                    <View key={key} style={styles.nutritionItem}>
                      <Text style={[styles.nutritionLabel, { color: colors.text }]}>
                        {formattedKey}:
                      </Text>
                      <Text 
                        style={[
                          styles.nutritionValue, 
                          { 
                            color: isPositive 
                              ? colors.success 
                              : colors.error 
                          }
                        ]}
                      >
                        {percentChange > 0 ? '+' : ''}
                        {Math.round(percentChange)}%
                      </Text>
                    </View>
                  );
                })}
              </View>
            </View>
          )}
          
          {/* Health benefits based on reasons */}
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Health Benefits
            </Text>
            <View style={styles.tagContainer}>
              {substitution.reasons.map((reason, index) => {
                // Convert the reason constant to a readable label
                const reasonLabel = reason
                  .replace(/([A-Z])/g, ' $1')
                  .replace(/^./, str => str.toUpperCase())
                  .replace('Lower', '↓')
                  .replace('Higher', '↑')
                  .replace('Less', '↓');
                
                return (
                  <View 
                    key={index} 
                    style={[
                      styles.tag, 
                      { backgroundColor: colors.subtle }
                    ]}
                  >
                    <Text style={[styles.tagText, { color: colors.text }]}>
                      {reasonLabel}
                    </Text>
                  </View>
                );
              })}
            </View>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  titleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
    flex: 1,
  },
  ratio: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  ratioText: {
    fontSize: 12,
    fontWeight: '500',
  },
  summaryContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingBottom: 16,
    alignItems: 'flex-start',
  },
  infoIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  summary: {
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  expandedContent: {
    padding: 16,
    paddingTop: 0,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 8,
  },
  sectionContent: {
    fontSize: 14,
    lineHeight: 20,
  },
  nutritionList: {
    marginTop: 4,
  },
  nutritionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  nutritionLabel: {
    fontSize: 14,
  },
  nutritionValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
  },
  tag: {
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 12,
    fontWeight: '500',
  },
}); 