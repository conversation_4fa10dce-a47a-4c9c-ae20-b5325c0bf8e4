import React, { useState } from 'react';
import { TouchableOpacity, Text, StyleSheet, Platform } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';

import KeyboardShortcutsHelp from './KeyboardShortcutsHelp';

interface KeyboardShortcutsButtonProps {
  /**
   * Style for the button
   */
  style?: any;
  
  /**
   * Label for the button
   */
  label?: string;
  
  /**
   * Whether to show an icon
   */
  showIcon?: boolean;
}

/**
 * Button that toggles the keyboard shortcuts help modal
 * Only shows on web platform
 */
export function KeyboardShortcutsButton({
  style,
  label = 'Keyboard Shortcuts',
  showIcon = true,
}: KeyboardShortcutsButtonProps) {
  const { colors, isDark } = useTheme();
  const [isHelpVisible, setIsHelpVisible] = useState(false);
  
  // Show help modal
  const showHelp = () => {
    setIsHelpVisible(true);
  };
  
  // Hide help modal
  const hideHelp = () => {
    setIsHelpVisible(false);
  };
  
  // Only render on web platform
  if (Platform.OS !== 'web') {
    return null;
  }
  
  return (
    <>
      <TouchableOpacity
        style={[
          styles.button,
          { backgroundColor: isDark ? colors.card : '#F5F5F5' },
          style,
        ]}
        onPress={showHelp}
        accessibilityRole="button"
        accessibilityLabel="Show keyboard shortcuts"
      >
        {showIcon && (
          <Keyboard
            size={18}
            color={colors.textSecondary}
            style={styles.icon}
          />
        )}
        
        <Text style={[styles.label, { color: colors.text }]}>
          {label}
        </Text>
      </TouchableOpacity>
      
      <KeyboardShortcutsHelp
        isVisible={isHelpVisible}
        onClose={hideHelp}
      />
    </>
  );
}

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  icon: {
    marginRight: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default KeyboardShortcutsButton; 