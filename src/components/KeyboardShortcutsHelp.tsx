import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  ScrollView,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { BlurView } from 'expo-blur';
import { Feather } from '@expo/vector-icons';
import { KeyboardShortcut, standardShortcuts, useKeyboardShortcuts } from '../utils/keyboardNavigation';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface KeyboardShortcutsHelpProps {
  /**
   * Whether the modal is visible
   */
  isVisible: boolean;
  
  /**
   * Callback when the modal is closed
   */
  onClose: () => void;
  
  /**
   * Additional custom shortcuts to display
   */
  additionalShortcuts?: KeyboardShortcut[];
}

/**
 * Component that displays all available keyboard shortcuts in a modal
 * Only relevant for web platform
 */
export function KeyboardShortcutsHelp({
  isVisible,
  onClose,
  additionalShortcuts = [],
}: KeyboardShortcutsHelpProps) {
  const { colors, isDark } = useTheme();
  const insets = useSafeAreaInsets();
  
  // Combined shortcuts (standard + additional)
  const [allShortcuts, setAllShortcuts] = useState<KeyboardShortcut[]>([]);
  
  // Group shortcuts by category
  const [groupedShortcuts, setGroupedShortcuts] = useState<Record<string, KeyboardShortcut[]>>({});
  
  // Create shortcut to close this modal
  useKeyboardShortcuts([
    {
      key: 'Escape',
      action: onClose,
      description: 'Close keyboard shortcuts help',
      category: 'ui',
    },
  ]);
  
  // Process and group shortcuts
  useEffect(() => {
    // Combine standard and additional shortcuts
    const combined = [...standardShortcuts, ...additionalShortcuts];
    setAllShortcuts(combined);
    
    // Group by category
    const grouped = combined.reduce((acc, shortcut) => {
      const category = shortcut.category || 'other';
      
      if (!acc[category]) {
        acc[category] = [];
      }
      
      acc[category].push(shortcut);
      return acc;
    }, {} as Record<string, KeyboardShortcut[]>);
    
    setGroupedShortcuts(grouped);
  }, [additionalShortcuts]);
  
  // Helper to format modifier keys
  const formatModifiers = (modifiers?: KeyboardShortcut['modifiers']) => {
    if (!modifiers) return '';
    
    const keys: string[] = [];
    if (modifiers.ctrl) keys.push('Ctrl');
    if (modifiers.alt) keys.push('Alt');
    if (modifiers.shift) keys.push('Shift');
    if (modifiers.meta) keys.push(Platform.OS === 'macos' ? '⌘' : 'Win');
    
    return keys.join(' + ');
  };
  
  // Get friendly category name
  const getCategoryName = (category: string) => {
    switch (category) {
      case 'navigation':
        return 'Navigation';
      case 'actions':
        return 'Actions';
      case 'ui':
        return 'UI Controls';
      case 'accessibility':
        return 'Accessibility';
      case 'other':
      default:
        return 'Other Shortcuts';
    }
  };
  
  // If not web platform, don't render anything
  if (Platform.OS !== 'web') {
    return null;
  }
  
  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View
        style={[
          styles.overlay,
          {
            backgroundColor: isDark
              ? 'rgba(0, 0, 0, 0.8)'
              : 'rgba(255, 255, 255, 0.8)',
          },
        ]}
      >
        <View
          style={[
            styles.container,
            {
              backgroundColor: colors.card,
              borderColor: colors.border,
              marginTop: insets.top,
              marginBottom: insets.bottom,
            },
          ]}
        >
          {/* Header */}
          <View
            style={[
              styles.header,
              { borderBottomColor: colors.border },
            ]}
          >
            <View style={styles.titleContainer}>
              <Keyboard size={20} color={colors.primary} style={styles.titleIcon} />
              <Text style={[styles.title, { color: colors.text }]}>
                Keyboard Shortcuts
              </Text>
            </View>
            
            <TouchableOpacity
              onPress={onClose}
              style={styles.closeButton}
              accessibilityLabel="Close keyboard shortcuts help"
              accessibilityRole="button"
            >
              <Feather name="x" size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>
          
          {/* Content */}
          <ScrollView style={styles.content}>
            {Object.keys(groupedShortcuts).map(category => (
              <View key={category} style={styles.categorySection}>
                <Text
                  style={[
                    styles.categoryTitle,
                    { color: colors.textSecondary },
                  ]}
                >
                  {getCategoryName(category)}
                </Text>
                
                <View
                  style={[
                    styles.shortcutsList,
                    { borderColor: colors.border },
                  ]}
                >
                  {groupedShortcuts[category].map((shortcut, index) => (
                    <View
                      key={`${shortcut.key}-${index}`}
                      style={[
                        styles.shortcutItem,
                        index < groupedShortcuts[category].length - 1 && {
                          borderBottomWidth: 1,
                          borderBottomColor: colors.border,
                        },
                      ]}
                    >
                      <Text
                        style={[styles.shortcutDescription, { color: colors.text }]}
                      >
                        {shortcut.description}
                      </Text>
                      
                      <View style={styles.keysContainer}>
                        {shortcut.modifiers && (
                          <Text
                            style={[styles.modifierKeys, { color: colors.textSecondary }]}
                          >
                            {formatModifiers(shortcut.modifiers)} +{' '}
                          </Text>
                        )}
                        
                        <View
                          style={[
                            styles.keyPill,
                            { backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)' },
                          ]}
                        >
                          <Text style={[styles.keyText, { color: colors.primary }]}>
                            {shortcut.key}
                          </Text>
                        </View>
                      </View>
                    </View>
                  ))}
                </View>
              </View>
            ))}
            
            {Platform.OS === 'web' && (
              <Text
                style={[
                  styles.webNote,
                  { color: colors.textSecondary },
                ]}
              >
                Note: Keyboard shortcuts are available only on the web version of the app.
              </Text>
            )}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  container: {
    width: '100%',
    maxWidth: 600,
    maxHeight: '90%',
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleIcon: {
    marginRight: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 16,
  },
  categorySection: {
    marginBottom: 20,
  },
  categoryTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    textTransform: 'uppercase',
  },
  shortcutsList: {
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden',
  },
  shortcutItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
  },
  shortcutDescription: {
    fontSize: 15,
    flex: 1,
    paddingRight: 8,
  },
  keysContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modifierKeys: {
    fontSize: 14,
  },
  keyPill: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 4,
  },
  keyText: {
    fontSize: 14,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  webNote: {
    fontSize: 13,
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 20,
    marginBottom: 10,
  },
});

export default KeyboardShortcutsHelp; 