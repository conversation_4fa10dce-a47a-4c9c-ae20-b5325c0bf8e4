import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal, SafeAreaView, FlatList } from 'react-native';
import { useTranslation } from '@/contexts/TranslationContext';
import { Feather } from '@expo/vector-icons';

// Define locally since they're not exported from context
type LanguageCode = string;

interface Language {
  name: string;
  nativeName?: string;
}

const LANGUAGES: Record<LanguageCode, Language> = {
  en: { name: 'English' },
  es: { name: 'Spanish', nativeName: 'Español' },
  fr: { name: 'French', nativeName: 'Français' },
  de: { name: 'German', nativeName: 'Deutsch' },
  zh: { name: 'Chinese', nativeName: '中文' },
  ja: { name: 'Japanese', nativeName: '日本語' },
  // Add other languages as needed
};

interface LanguageSelectorProps {
  minimal?: boolean;
  onDismiss?: () => void;
}

export function LanguageSelector({ minimal = false, onDismiss }: LanguageSelectorProps) {
  const { t, locale, setLocale, availableLanguages } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);

  const languageEntries = Object.entries(LANGUAGES);

  const handleSelectLanguage = async (code: LanguageCode) => {
    await setLocale(code);
    setModalVisible(false);
    onDismiss?.();
  };

  const currentLanguage = LANGUAGES[locale];

  if (minimal) {
    return (
      <TouchableOpacity 
        style={styles.minimalButton} 
        onPress={() => setModalVisible(true)}
        accessibilityLabel={t('profile.language')}
        accessibilityHint={t('profile.language')}
      >
        <Feather name="globe" size={20}  color={colors.text} />
        <Text style={styles.minimalButtonText}>{currentLanguage.name}</Text>
      </TouchableOpacity>
    );
  }

  return (
    <>
      <TouchableOpacity
        style={styles.languageSelector}
        onPress={() => setModalVisible(true)}
        accessibilityLabel={t('profile.language')}
        accessibilityHint={t('profile.language')}
      >
        <View style={styles.leftContent}>
          <Feather name="globe" size={22} style={styles.icon} />
          <View>
            <Text style={styles.label}>{t('profile.language')}</Text>
            <Text style={styles.currentLanguage}>{currentLanguage.name}</Text>
          </View>
        </View>
        <Feather name="arrow-right" size={20}  color={colors.text} />
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{t('profile.language')}</Text>
              <TouchableOpacity 
                onPress={() => setModalVisible(false)}
                accessibilityLabel={t('common.dismiss')}
              >
                <Feather name="x" size={24}  color={colors.text} />
              </TouchableOpacity>
            </View>

            <FlatList
              data={languageEntries}
              keyExtractor={([code]) => code}
              renderItem={({ item: [code, language] }) => (
                <TouchableOpacity
                  style={styles.languageOption}
                  onPress={() => handleSelectLanguage(code as LanguageCode)}
                  accessibilityLabel={language.name}
                  accessibilityRole="radio"
                  accessibilityState={{ checked: code === locale }}
                >
                  <Text style={styles.languageName}>{language.name}</Text>
                  {code === locale && <Feather name="check" size={20}  color={colors.text} />}
                </TouchableOpacity>
              )}
              ItemSeparatorComponent={() => <View style={styles.separator} />}
            />
          </View>
        </SafeAreaView>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  languageSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 20,
    backgroundColor: '#fff',
    borderRadius: 12,
    marginVertical: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  leftContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 16,
  },
  label: {
    fontSize: 16,
    color: '#333',
    marginBottom: 4,
    fontWeight: '500',
  },
  currentLanguage: {
    fontSize: 14,
    color: '#666',
  },
  minimalButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(66, 133, 244, 0.1)',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  minimalButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4285F4',
    marginLeft: 6,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 16,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
    paddingHorizontal: 4,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 4,
  },
  languageName: {
    fontSize: 18,
    color: '#333',
  },
  separator: {
    height: 1,
    backgroundColor: '#eee',
  }
}); 