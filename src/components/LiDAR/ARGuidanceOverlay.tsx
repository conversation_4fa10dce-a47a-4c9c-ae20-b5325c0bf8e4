/**
 * Enhanced AR Guidance Overlay
 * Provides visual guidance for LiDAR scanning process
 */
import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated, Easing, TouchableOpacity, ViewStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { StabilityLevel } from '@/utils/LiDAR/types';

interface ARGuidanceOverlayProps {
  planeDetected: boolean;
  optimalScanningDistance: boolean;
  scanStability: StabilityLevel;
  guidePosition: { x: number; y: number };
  onToggleGuide: () => void;
  scanCompleteness?: number; // New prop for scan completeness
  objectsInView?: number; // New prop for tracked objects
}

export default function ARGuidanceOverlay({
  planeDetected,
  optimalScanningDistance,
  scanStability,
  guidePosition,
  onToggleGuide,
  scanCompleteness = 0,
  objectsInView = 0
}: ARGuidanceOverlayProps) {
  const insets = useSafeAreaInsets();
  
  // Animations
  const guideOpacity = useRef(new Animated.Value(0)).current;
  const guideScale = useRef(new Animated.Value(0.8)).current;
  const pulseAnimation = useRef(new Animated.Value(1)).current;
  const rotateAnimation = useRef(new Animated.Value(0)).current;
  const stabilityAnimation = useRef(new Animated.Value(0)).current;
  
  // Start animations on mount
  useEffect(() => {
    // Fade in animation
    Animated.timing(guideOpacity, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
      easing: Easing.out(Easing.cubic),
    }).start();
    
    // Scale animation
    Animated.timing(guideScale, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
      easing: Easing.elastic(1.2),
    }).start();
    
    // Start pulse animation loop
    startPulseAnimation();
    
    // Start rotation animation loop
    Animated.loop(
      Animated.timing(rotateAnimation, {
        toValue: 1,
        duration: 6000,
        useNativeDriver: true,
        easing: Easing.linear,
      })
    ).start();
  }, []);
  
  // Update stability animation when stability changes
  useEffect(() => {
    let stabilityValue = 0;
    
    if (scanStability === 'stable') stabilityValue = 1;
    else if (scanStability === 'moderate') stabilityValue = 0.5;
    
    Animated.timing(stabilityAnimation, {
      toValue: stabilityValue,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [scanStability]);
  
  // Start pulse animation
  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1.2,
          duration: optimalScanningDistance ? 1500 : 800,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.sin),
        }),
        Animated.timing(pulseAnimation, {
          toValue: 0.9,
          duration: optimalScanningDistance ? 1500 : 800,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.sin),
        }),
      ])
    ).start();
  };
  
  // Get color for guide based on scanning conditions
  const getGuideColor = () => {
    if (!planeDetected) return '#ffeb3b'; // Yellow - looking for plane
    if (!optimalScanningDistance) return '#ff9800'; // Orange - wrong distance
    if (scanStability === 'unstable') return '#ff5722'; // Deep orange - unstable
    if (scanStability === 'moderate') return '#42a5f5'; // Blue - moderate stability
    return '#4caf50'; // Green - good conditions
  };
  
  // Get guide message based on scanning conditions
  const getGuideMessage = () => {
    if (!planeDetected) return 'Move device to find a flat surface';
    if (!optimalScanningDistance) return 'Adjust distance to food';
    if (scanStability === 'unstable') return 'Hold more steady';
    if (scanCompleteness > 0.75) return 'Great coverage! Almost done';
    if (scanCompleteness > 0.5) return 'Keep going, capture more angles';
    if (scanCompleteness > 0.25) return 'Good start, move around food';
    return 'Scanning in progress...';
  };
  
  // Animation styles
  const rotateInterpolation = rotateAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });
  
  const scannerRingStyle = {
    opacity: guideOpacity,
    transform: [
      { scale: guideScale },
      { rotate: rotateInterpolation },
    ],
  };
  
  const innerCircleStyle = {
    transform: [{ scale: pulseAnimation }],
    backgroundColor: getGuideColor(),
  };
  
  const guideContainerStyle = {
    top: guidePosition.y - 50,
    left: guidePosition.x - 50,
  };
  
  // Progress indicator for scan completeness
  const progressBarFill: ViewStyle = {
    width: `${scanCompleteness * 100}%` as any,
    backgroundColor: scanCompleteness > 0.75 ? '#4caf50' : 
                    scanCompleteness > 0.4 ? '#42a5f5' : '#ff9800',
  };
  
  // Get stability indicator width based on stability animation
  const stabilityIndicatorWidth = stabilityAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['33%', '100%'] as any[],
  });
  
  return (
    <View style={styles.container}>
      {/* Close button */}
      <TouchableOpacity
        style={[styles.closeButton, { top: insets.top + 10 }]}
        onPress={onToggleGuide}
      >
        <Ionicons name="close-circle" size={36} color="rgba(255,255,255,0.8)" />
      </TouchableOpacity>
      
      {/* Guidance visualization */}
      <Animated.View style={[styles.guideContainer, guideContainerStyle]}>
        <Animated.View style={[styles.scannerRing, scannerRingStyle]}>
          <View style={styles.ringPattern} />
        </Animated.View>
        <Animated.View style={[styles.innerCircle, innerCircleStyle]} />
      </Animated.View>
      
      {/* Guidance message */}
      <View style={[styles.messageContainer, { bottom: insets.bottom + 140 }]}>
        <Text style={styles.messageText}>{getGuideMessage()}</Text>
        
        {/* Objects counter */}
        {objectsInView > 0 && (
          <View style={styles.objectsCounter}>
            <Ionicons name="cube-outline" size={16} color="#fff" />
            <Text style={styles.objectsCountText}>{objectsInView} objects detected</Text>
          </View>
        )}
      </View>
      
      {/* Status indicators */}
      <View style={[styles.statusContainer, { bottom: insets.bottom + 90 }]}>
        {/* Plane detection indicator */}
        <View style={styles.statusItem}>
          <View style={[styles.statusIndicator, { backgroundColor: planeDetected ? '#4caf50' : '#9e9e9e' }]}>
            <Ionicons name={planeDetected ? "checkmark" : "square-outline"} size={14} color="#fff" />
          </View>
          <Text style={styles.statusText}>Surface</Text>
        </View>
        
        {/* Distance indicator */}
        <View style={styles.statusItem}>
          <View style={[styles.statusIndicator, { backgroundColor: optimalScanningDistance ? '#4caf50' : '#ff9800' }]}>
            <Ionicons name={optimalScanningDistance ? "resize" : "move"} size={14} color="#fff" />
          </View>
          <Text style={styles.statusText}>Distance</Text>
        </View>
        
        {/* Stability indicator */}
        <View style={styles.statusItem}>
          <View style={styles.stabilityIndicatorContainer}>
            <Animated.View style={[styles.stabilityIndicatorFill, { width: stabilityIndicatorWidth }]} />
          </View>
          <Text style={styles.statusText}>Stability</Text>
        </View>
      </View>
      
      {/* Scan completeness progress */}
      <View style={[styles.progressContainer, { bottom: insets.bottom + 50 }]}>
        <Text style={styles.progressText}>Scan Coverage: {Math.round(scanCompleteness * 100)}%</Text>
        <View style={styles.progressBar}>
          <View style={[styles.progressBarFill, progressBarFill]} />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 10,
  },
  guideContainer: {
    position: 'absolute',
    width: 100,
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scannerRing: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  ringPattern: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: 50,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
    borderStyle: 'dashed',
  },
  innerCircle: {
    position: 'absolute',
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#4caf50',
  },
  messageContainer: {
    position: 'absolute',
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    alignItems: 'center',
  },
  messageText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  objectsCounter: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  objectsCountText: {
    color: 'white',
    fontSize: 12,
    marginLeft: 5,
  },
  statusContainer: {
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '90%',
    maxWidth: 400,
  },
  statusItem: {
    alignItems: 'center',
  },
  statusIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#9e9e9e',
    marginBottom: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusText: {
    color: 'white',
    fontSize: 12,
  },
  stabilityIndicatorContainer: {
    width: 60,
    height: 8,
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 5,
  },
  stabilityIndicatorFill: {
    height: '100%',
    backgroundColor: '#4caf50',
  },
  progressContainer: {
    position: 'absolute',
    width: '90%',
    maxWidth: 400,
    alignItems: 'center',
  },
  progressText: {
    color: 'white',
    fontSize: 14,
    marginBottom: 5,
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: '#4caf50',
  },
}); 