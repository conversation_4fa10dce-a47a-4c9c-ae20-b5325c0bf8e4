/**
 * AR Positioning Guides Component
 * 
 * Provides augmented reality guides to help users position
 * food items optimally for accurate weight estimation.
 */
import React, { useEffect, useRef, useState } from 'react';
import { View, StyleSheet, Animated, Easing, Text, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { StabilityLevel } from '@/utils/LiDAR/types';

// Types for AR positioning guides
interface ARPositioningGuidesProps {
  isActive: boolean;
  scanStability: StabilityLevel;
  planeDetected: boolean;
  optimalDistance: boolean;
  cameraAngle?: 'overhead' | 'side' | 'angled' | 'unknown';
  lightingQuality?: 'good' | 'moderate' | 'poor';
  foodInCenter?: boolean;
  scanProgress: number;
  referenceObjectDetected?: boolean;
}

export default function ARPositioningGuides({
  isActive,
  scanStability,
  planeDetected,
  optimalDistance,
  cameraAngle = 'unknown',
  lightingQuality = 'moderate',
  foodInCenter = false,
  scanProgress = 0,
  referenceObjectDetected = false
}: ARPositioningGuidesProps) {
  const insets = useSafeAreaInsets();
  const screenWidth = Dimensions.get('window').width;
  const screenHeight = Dimensions.get('window').height;
  
  // Animation references
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const angleGuideAnim = useRef(new Animated.Value(0)).current;
  const distanceGuideAnim = useRef(new Animated.Value(0)).current;
  const centerGuideAnim = useRef(new Animated.Value(0)).current;
  const referenceGuideAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  
  // Keep track of which guides have been seen already
  const [guidesShown, setGuidesShown] = useState<{
    angle: boolean;
    distance: boolean;
    center: boolean;
    reference: boolean;
  }>({
    angle: false,
    distance: false,
    center: false,
    reference: false
  });
  
  // Show guides when the component becomes active
  useEffect(() => {
    if (isActive) {
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
      
      // Start pulse animation
      startPulseAnimation();
    } else {
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isActive]);
  
  // Trigger specific guides based on detected issues
  useEffect(() => {
    if (!isActive) return;
    
    // Check camera angle
    if (cameraAngle === 'side' && !guidesShown.angle) {
      showAngleGuide();
    }
    
    // Check distance
    if (!optimalDistance && !guidesShown.distance) {
      showDistanceGuide();
    }
    
    // Check centering
    if (!foodInCenter && !guidesShown.center) {
      showCenterGuide();
    }
    
    // Check reference object
    if (!referenceObjectDetected && scanProgress > 0.3 && !guidesShown.reference) {
      showReferenceGuide();
    }
  }, [
    isActive, 
    cameraAngle, 
    optimalDistance, 
    foodInCenter, 
    referenceObjectDetected, 
    scanProgress
  ]);
  
  // Animation for pulse effect
  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.ease),
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.ease),
        }),
      ])
    ).start();
  };
  
  // Show angle guide animation
  const showAngleGuide = () => {
    setGuidesShown(prev => ({ ...prev, angle: true }));
    
    // Animate the guide in
    Animated.sequence([
      Animated.timing(angleGuideAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.delay(4000), // Show for 4 seconds
      Animated.timing(angleGuideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  };
  
  // Show distance guide animation
  const showDistanceGuide = () => {
    setGuidesShown(prev => ({ ...prev, distance: true }));
    
    // Animate the guide in
    Animated.sequence([
      Animated.timing(distanceGuideAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.delay(4000), // Show for 4 seconds
      Animated.timing(distanceGuideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  };
  
  // Show center guide animation
  const showCenterGuide = () => {
    setGuidesShown(prev => ({ ...prev, center: true }));
    
    // Animate the guide in
    Animated.sequence([
      Animated.timing(centerGuideAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.delay(4000), // Show for 4 seconds
      Animated.timing(centerGuideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  };
  
  // Show reference object guide animation
  const showReferenceGuide = () => {
    setGuidesShown(prev => ({ ...prev, reference: true }));
    
    // Animate the guide in
    Animated.sequence([
      Animated.timing(referenceGuideAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.delay(5000), // Show for 5 seconds
      Animated.timing(referenceGuideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  };
  
  // Generate angle guide illustration based on recommended camera angle
  const renderAngleGuide = () => {
    const recommendedAngle = cameraAngle === 'side' ? 'overhead' : 'angled';
    
    return (
      <Animated.View 
        style={[
          styles.guideContainer, 
          styles.angleGuide,
          { 
            opacity: angleGuideAnim,
            transform: [
              { scale: Animated.multiply(pulseAnim, angleGuideAnim) }
            ]
          }
        ]}
      >
        <View style={styles.guideIconContainer}>
          <Ionicons name="camera" size={28} color="#fff" />
          <Ionicons 
            name={recommendedAngle === 'overhead' ? "arrow-down" : "arrow-forward"} 
            size={24} 
            color="#4caf50" 
          />
          <View style={styles.foodIcon} />
        </View>
        
        <Text style={styles.guideTitle}>Adjust Camera Angle</Text>
        <Text style={styles.guideDescription}>
          {recommendedAngle === 'overhead' 
            ? "Position your camera directly above the food for better results"
            : "Try a 45° angle for optimal view of the food's shape"}
        </Text>
      </Animated.View>
    );
  };
  
  // Distance guide illustration
  const renderDistanceGuide = () => {
    return (
      <Animated.View 
        style={[
          styles.guideContainer, 
          styles.distanceGuide,
          { 
            opacity: distanceGuideAnim,
            transform: [
              { scale: Animated.multiply(pulseAnim, distanceGuideAnim) }
            ]
          }
        ]}
      >
        <View style={styles.guideIconContainer}>
          <Ionicons name="camera" size={28} color="#fff" />
          <Text style={styles.distanceText}>
            {optimalDistance ? "Good" : "Move" + (optimalDistance === false ? " closer" : " back")}
          </Text>
          <View style={styles.foodIcon} />
        </View>
        
        <Text style={styles.guideTitle}>Adjust Distance</Text>
        <Text style={styles.guideDescription}>
          {optimalDistance === false 
            ? "Move closer to the food for optimal detail capture" 
            : "Move back to capture the entire food in frame"}
        </Text>
      </Animated.View>
    );
  };
  
  // Centering guide illustration
  const renderCenterGuide = () => {
    return (
      <Animated.View 
        style={[
          styles.guideContainer, 
          styles.centerGuide,
          { 
            opacity: centerGuideAnim,
            transform: [
              { scale: Animated.multiply(pulseAnim, centerGuideAnim) }
            ]
          }
        ]}
      >
        <View style={styles.centerGuideVisual}>
          <View style={styles.frameOutline}>
            <View style={[styles.foodIcon, styles.smallFoodIcon, { top: 20, left: 20 }]} />
            <Animated.View 
              style={[
                styles.targetIndicator, 
                { 
                  transform: [
                    { scale: pulseAnim }
                  ]
                }
              ]}
            >
              <Ionicons name="add" size={24} color="#fff" />
            </Animated.View>
          </View>
        </View>
        
        <Text style={styles.guideTitle}>Center the Food</Text>
        <Text style={styles.guideDescription}>
          Position your food in the center of the frame for best results
        </Text>
      </Animated.View>
    );
  };
  
  // Reference object guide illustration
  const renderReferenceGuide = () => {
    return (
      <Animated.View 
        style={[
          styles.guideContainer, 
          styles.referenceGuide,
          { 
            opacity: referenceGuideAnim,
            transform: [
              { scale: Animated.multiply(pulseAnim, referenceGuideAnim) }
            ]
          }
        ]}
      >
        <View style={styles.referenceGuideVisual}>
          <View style={styles.foodIcon} />
          <Ionicons name="add" size={24} color="#fff" />
          <View style={styles.referenceObjectIcon}>
            <Text style={styles.referenceLabel}>Card</Text>
          </View>
        </View>
        
        <Text style={styles.guideTitle}>Add Reference Object</Text>
        <Text style={styles.guideDescription}>
          Place a credit card, coin, or standard tableware in the frame for more accurate measurements
        </Text>
      </Animated.View>
    );
  };
  
  // Helper indicators that stay on screen while scanning
  const renderPersistentGuides = () => {
    return (
      <View style={styles.persistentGuidesContainer}>
        {/* Distance indicator */}
        <View style={[
          styles.persistentIndicator, 
          { backgroundColor: optimalDistance ? '#4caf50' : '#ff9800' }
        ]}>
          <Ionicons 
            name={optimalDistance ? "resize-outline" : "move-outline"} 
            size={18} 
            color="#fff" 
          />
        </View>
        
        {/* Angle indicator */}
        <View style={[
          styles.persistentIndicator, 
          { backgroundColor: cameraAngle === 'overhead' ? '#4caf50' : '#ff9800' }
        ]}>
          <Ionicons 
            name="camera-outline" 
            size={18} 
            color="#fff" 
          />
        </View>
        
        {/* Reference object indicator */}
        <View style={[
          styles.persistentIndicator, 
          { backgroundColor: referenceObjectDetected ? '#4caf50' : '#ff9800' }
        ]}>
          <Ionicons 
            name="card-outline" 
            size={18} 
            color="#fff" 
          />
        </View>
      </View>
    );
  };
  
  // AR frame guide for optimal positioning
  const renderARFrame = () => {
    return (
      <View style={styles.arFrameContainer}>
        {/* Corner guides */}
        <View style={[styles.cornerGuide, styles.topLeftCorner]} />
        <View style={[styles.cornerGuide, styles.topRightCorner]} />
        <View style={[styles.cornerGuide, styles.bottomLeftCorner]} />
        <View style={[styles.cornerGuide, styles.bottomRightCorner]} />
        
        {/* Center indicator */}
        <Animated.View 
          style={[
            styles.centerIndicator,
            { 
              borderColor: foodInCenter ? '#4caf50' : 'rgba(255,255,255,0.4)',
              transform: [{ scale: pulseAnim }]
            }
          ]}
        />
      </View>
    );
  };
  
  // Don't render anything if not active
  if (!isActive) return null;
  
  return (
    <Animated.View style={[styles.container, { opacity: opacityAnim }]}>
      {/* AR positioning frame */}
      {renderARFrame()}
      
      {/* Guidance overlays */}
      {renderAngleGuide()}
      {renderDistanceGuide()}
      {renderCenterGuide()}
      {renderReferenceGuide()}
      
      {/* Persistent indicators */}
      {renderPersistentGuides()}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    pointerEvents: 'none',
  },
  arFrameContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cornerGuide: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderColor: 'rgba(255,255,255,0.7)',
    borderWidth: 3,
  },
  topLeftCorner: {
    top: 80,
    left: 20,
    borderTopWidth: 3,
    borderLeftWidth: 3,
    borderBottomWidth: 0,
    borderRightWidth: 0,
    borderTopLeftRadius: 10,
  },
  topRightCorner: {
    top: 80,
    right: 20,
    borderTopWidth: 3,
    borderRightWidth: 3,
    borderBottomWidth: 0,
    borderLeftWidth: 0,
    borderTopRightRadius: 10,
  },
  bottomLeftCorner: {
    bottom: 120,
    left: 20,
    borderBottomWidth: 3,
    borderLeftWidth: 3,
    borderTopWidth: 0,
    borderRightWidth: 0,
    borderBottomLeftRadius: 10,
  },
  bottomRightCorner: {
    bottom: 120,
    right: 20,
    borderBottomWidth: 3,
    borderRightWidth: 3,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    borderBottomRightRadius: 10,
  },
  centerIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.4)',
    borderStyle: 'dashed',
  },
  guideContainer: {
    position: 'absolute',
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: 12,
    padding: 20,
    width: '80%',
    maxWidth: 350,
    alignItems: 'center',
  },
  angleGuide: {
    top: '20%',
  },
  distanceGuide: {
    top: '30%',
  },
  centerGuide: {
    top: '40%',
  },
  referenceGuide: {
    bottom: '20%',
  },
  guideTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  guideDescription: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 14,
    marginTop: 4,
    textAlign: 'center',
  },
  guideIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    width: '100%',
  },
  foodIcon: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#ff9800',
    marginHorizontal: 20,
  },
  smallFoodIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginHorizontal: 0,
    position: 'absolute',
  },
  distanceText: {
    color: '#fff',
    fontSize: 16,
    marginHorizontal: 12,
  },
  centerGuideVisual: {
    marginBottom: 16,
    width: 200,
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  frameOutline: {
    width: 150,
    height: 80,
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.5)',
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  targetIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: '#4caf50',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
  },
  referenceGuideVisual: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  referenceObjectIcon: {
    width: 50,
    height: 30,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  referenceLabel: {
    fontSize: 10,
    color: '#333',
    fontWeight: 'bold',
  },
  persistentGuidesContainer: {
    position: 'absolute',
    top: 90,
    right: 20,
    flexDirection: 'column',
  },
  persistentIndicator: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#ff9800',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 5,
  },
}); 