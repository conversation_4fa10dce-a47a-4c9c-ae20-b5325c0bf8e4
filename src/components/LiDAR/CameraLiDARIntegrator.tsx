/**
 * CameraLiDARIntegrator
 * Combines camera and LiDAR capabilities for enhanced food volume estimation
 */
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { View, StyleSheet, TouchableOpacity, Text, Platform, Alert } from 'react-native';
import { CameraView, Camera } from 'expo-camera';
import * as ImageManipulator from 'expo-image-manipulator';
import * as Haptics from 'expo-haptics';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useIsFocused } from '@react-navigation/native';

import useLiDARScanning from '@/hooks/LiDAR/useLiDARScanning';
import ARGuidanceOverlay from './ARGuidanceOverlay';
import { ProcessingStage } from '@/utils/LiDAR/types';
import { DeviceCapability } from '@/utils/volumeEstimator';

// Define simple FlashMode and CameraType objects to avoid TypeScript errors
const FlashMode = {
  on: 'on',
  off: 'off',
  auto: 'auto'
};

const CameraType = {
  back: 'back',
  front: 'front'
};

// Define Camera instance type
interface CameraInstance {
  resumePreview(): Promise<void>;
  autoFocus?(): Promise<void>;
  setExposureOffset(value: number): Promise<void>;
  takePictureAsync(options?: any): Promise<{ uri: string }>;
}

interface CameraLiDARIntegratorProps {
  onCapture: (imageUri: string, volumeData?: any) => void;
  onClose: () => void;
  enableObjectTracking?: boolean;
  scanResolution?: 'high' | 'medium' | 'low';
  showDebugInfo?: boolean;
}

export default function CameraLiDARIntegrator({
  onCapture,
  onClose,
  enableObjectTracking = true,
  scanResolution = 'high',
  showDebugInfo = false,
}: CameraLiDARIntegratorProps) {
  // Hooks and refs
  const insets = useSafeAreaInsets();
  const isFocused = useIsFocused();
  const cameraRef = useRef<any>(null);
  
  // Camera state
  const [hasCameraPermission, setHasCameraPermission] = useState<boolean | null>(null);
  const [isCameraReady, setIsCameraReady] = useState(false);
  const [flashMode, setFlashMode] = useState(FlashMode.off);
  const [isCapturing, setIsCapturing] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(0);
  
  // LiDAR scanning hook
  const lidarScanning = useLiDARScanning({
    enableObjectTracking,
    onScanningFeedback: (quality, stability) => {
      // Provide haptic feedback based on scan quality and stability
      if (quality === 'high' && stability === 'stable') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    }
  });
  
  // UI state
  const [showARGuide, setShowARGuide] = useState(true);
  const [exposureAdjusted, setExposureAdjusted] = useState(false);
  
  // Request camera permissions on mount
  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasCameraPermission(status === 'granted');
      
      if (status !== 'granted') {
        Alert.alert(
          "Camera Permission Required",
          "We need camera access to scan food items. Please enable camera permissions in your device settings."
        );
      }
    })();
  }, []);
  
  // Handle camera focus and blur
  useEffect(() => {
    // When screen comes into focus and camera is ready
    if (isFocused && isCameraReady) {
      // Start LiDAR scanning if device supports it
      if (lidarScanning.deviceSupportsLiDAR === DeviceCapability.LIDAR_SUPPORTED) {
        lidarScanning.startLiDARScanning();
      }
    }
    
    // Cleanup when screen loses focus
    return () => {
      if (lidarScanning.isScanningActive) {
        lidarScanning.stopLiDARScanning();
      }
    };
  }, [isFocused, isCameraReady, lidarScanning]);
  
  // Handle camera ready state
  const handleCameraReady = useCallback(() => {
    setIsCameraReady(true);
    
    // Optimize camera settings for food scanning
    optimizeCameraForFoodScan();
  }, []);
  
  // Optimize camera settings for food scanning
  const optimizeCameraForFoodScan = async () => {
    if (!cameraRef.current || Platform.OS !== 'ios') return;
    
    try {
      // Adjust camera settings for better food detection
      // These settings will help with both visual analysis and LiDAR accuracy
      await cameraRef.current.resumePreview();
      
      // Auto-focus on center (where food is likely to be)
      if (cameraRef.current.autoFocus) {
        await cameraRef.current.autoFocus();
      }
      
      // Adjust exposure for food photography if not already done
      if (!exposureAdjusted) {
        // Slightly increase exposure for better food details
        const currentExposure = 0;
        await cameraRef.current.setExposureOffset(currentExposure + 0.3);
        setExposureAdjusted(true);
      }
    } catch (error) {
      console.error('Error optimizing camera:', error);
    }
  };
  
  // Handle capture button press
  const handleCapture = useCallback(async () => {
    if (!cameraRef.current || isCapturing || !isCameraReady) return;
    
    setIsCapturing(true);
    
    try {
      // Momentary pause to allow user to stabilize
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Trigger haptic feedback
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      
      // Take photo with camera
      const photo = await cameraRef.current.takePictureAsync({
        quality: scanResolution === 'high' ? 1 : scanResolution === 'medium' ? 0.8 : 0.6,
        base64: false,
        skipProcessing: false,
        fixOrientation: true,
      });
      
      // Process the image - resize for consistency
      const processedPhoto = await ImageManipulator.manipulateAsync(
        photo.uri,
        [{ resize: { width: 1200 } }],
        { compress: 0.9, format: ImageManipulator.SaveFormat.JPEG }
      );
      
      // Calculate volume with LiDAR if available
      let volumeData = null;
      if (lidarScanning.deviceSupportsLiDAR === DeviceCapability.LIDAR_SUPPORTED &&
          lidarScanning.isScanningActive) {
        volumeData = await lidarScanning.calculateLiDARVolume();
        await lidarScanning.stopLiDARScanning();
      }
      
      // Call the onCapture callback with results
      onCapture(processedPhoto.uri, volumeData);
    } catch (error) {
      console.error('Error capturing image:', error);
      Alert.alert('Capture Error', 'Failed to capture image. Please try again.');
    } finally {
      setIsCapturing(false);
    }
  }, [cameraRef, isCapturing, isCameraReady, scanResolution, lidarScanning, onCapture]);
  
  // Toggle flash mode
  const toggleFlash = useCallback(() => {
    setFlashMode(
      flashMode === FlashMode.off
        ? FlashMode.on
        : FlashMode.off
    );
  }, [flashMode]);
  
  // Handle close button press
  const handleClose = useCallback(() => {
    if (lidarScanning.isScanningActive) {
      lidarScanning.stopLiDARScanning();
    }
    onClose();
  }, [lidarScanning, onClose]);
  
  // Handle zoom change
  const handleZoomChange = useCallback((newZoomLevel: number) => {
    setZoomLevel(Math.max(0, Math.min(1, newZoomLevel))); // Clamp between 0 and 1
  }, []);
  
  // Show permission denied message if camera access is denied
  if (hasCameraPermission === false) {
    return (
      <View style={[styles.container, styles.centeredContent]}>
        <Text style={styles.errorText}>Camera access is required to scan food.</Text>
        <TouchableOpacity style={styles.button} onPress={handleClose}>
          <Text style={styles.buttonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  // Show loading state while checking permissions
  if (hasCameraPermission === null) {
    return (
      <View style={[styles.container, styles.centeredContent]}>
        <Text style={styles.loadingText}>Requesting camera permission...</Text>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      
      {/* Camera */}
      <CameraView
        ref={cameraRef}
        style={styles.camera}
        facing="back"
      >
        {/* AR Guidance Overlay */}
        {showARGuide && lidarScanning.isScanningActive && (
          <ARGuidanceOverlay
            planeDetected={lidarScanning.planeDetected}
            optimalScanningDistance={lidarScanning.optimalScanningDistance}
            scanStability={lidarScanning.scanStability}
            guidePosition={lidarScanning.guidePosition}
            onToggleGuide={() => setShowARGuide(false)}
            scanCompleteness={lidarScanning.scanCompleteness}
            objectsInView={lidarScanning.objectsInView}
          />
        )}
        
        {/* Food scanning guide */}
        {!lidarScanning.planeDetected && (
          <View style={styles.guidanceContainer}>
            <View style={styles.guidanceBox}>
              <Text style={styles.guidanceText}>
                Place food on a flat surface and point camera at it
              </Text>
            </View>
          </View>
        )}
        
        {/* Close button */}
        <TouchableOpacity 
          style={[styles.closeButton, { top: insets.top + 10 }]} 
          onPress={handleClose}
        >
          <Ionicons name="close-outline" size={32} color="white" />
        </TouchableOpacity>
        
        {/* Controls container */}
        <View style={[styles.controlsContainer, { bottom: insets.bottom + 20 }]}>
          {/* Flash toggle button */}
          <TouchableOpacity style={styles.controlButton} onPress={toggleFlash}>
            <Ionicons 
              name={flashMode === FlashMode.on ? "flash" : "flash-off"} 
              size={24} 
              color="white" 
            />
          </TouchableOpacity>
          
          {/* Capture button */}
          <TouchableOpacity 
            style={[
              styles.captureButton,
              isCapturing && styles.capturingButton
            ]} 
            onPress={handleCapture}
            disabled={isCapturing || !isCameraReady}
          >
            {isCapturing ? (
              <View style={styles.capturingIndicator} />
            ) : (
              <View style={styles.captureButtonInner} />
            )}
          </TouchableOpacity>
          
          {/* LiDAR status indicator */}
          <View style={styles.lidarStatusContainer}>
            <View 
              style={[
                styles.lidarStatusIndicator, 
                { 
                  backgroundColor: lidarScanning.deviceSupportsLiDAR === DeviceCapability.LIDAR_SUPPORTED
                    ? lidarScanning.isScanningActive ? '#4CAF50' : '#2196F3'
                    : '#9E9E9E'
                }
              ]} 
            />
            <Text style={styles.lidarStatusText}>
              {lidarScanning.deviceSupportsLiDAR === DeviceCapability.LIDAR_SUPPORTED
                ? 'LiDAR'
                : '2D Mode'}
            </Text>
          </View>
        </View>
        
        {/* Debug info */}
        {showDebugInfo && (
          <View style={[styles.debugContainer, { top: insets.top + 50 }]}>
            <Text style={styles.debugText}>
              Scanning: {lidarScanning.isScanningActive ? 'Active' : 'Inactive'}{'\n'}
              Quality: {lidarScanning.scanQuality}{'\n'}
              Stability: {lidarScanning.scanStability}{'\n'}
              Mesh Count: {lidarScanning.meshCount}{'\n'}
              Completeness: {Math.round(lidarScanning.scanCompleteness * 100)}%{'\n'}
              Objects: {lidarScanning.objectsInView}
            </Text>
          </View>
        )}
      </CameraView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  centeredContent: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: 'white',
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 20,
  },
  loadingText: {
    color: 'white',
    fontSize: 18,
    textAlign: 'center',
  },
  camera: {
    flex: 1,
  },
  guidanceContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  guidanceBox: {
    width: 240,
    padding: 15,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.7)',
    alignItems: 'center',
  },
  guidanceText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    left: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  controlsContainer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  controlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: 'rgba(255,255,255,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  captureButtonInner: {
    width: 54,
    height: 54,
    borderRadius: 27,
    backgroundColor: 'white',
  },
  capturingButton: {
    backgroundColor: 'rgba(255,59,48,0.3)',
  },
  capturingIndicator: {
    width: 24,
    height: 24,
    borderRadius: 4,
    backgroundColor: '#FF3B30',
  },
  lidarStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  lidarStatusIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 6,
  },
  lidarStatusText: {
    color: 'white',
    fontSize: 12,
  },
  button: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
  },
  debugContainer: {
    position: 'absolute',
    top: 90,
    right: 10,
    backgroundColor: 'rgba(0,0,0,0.7)',
    padding: 8,
    borderRadius: 5,
  },
  debugText: {
    color: 'white',
    fontSize: 10,
  },
}); 