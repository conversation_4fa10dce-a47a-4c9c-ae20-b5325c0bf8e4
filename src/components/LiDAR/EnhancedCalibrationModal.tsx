/**
 * Enhanced Calibration Modal
 * Provides an improved interface for LiDAR calibration with guided workflow
 */
import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Image,
  Platform,
  Dimensions,
  Animated,
  Easing,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { EnhancedCalibrationModalProps, ReferenceObject } from '@/utils/LiDAR/types';

// Reference Object Image component that displays image or fallbacks to icon
const ReferenceObjectImage = ({ 
  object, 
  size = 60, 
  color = '#000' 
}: { 
  object: ReferenceObject; 
  size?: number; 
  color?: string;
}) => {
  if (object.image) {
    return (
      <Image 
        source={object.image} 
        style={{ width: size, height: size }}
        resizeMode="contain"
      />
    );
  }
  
  // Fallback to an icon
  return (
    <View style={[
      styles.customIcon, 
      { 
        width: size, 
        height: size, 
        borderRadius: size / 2,
        backgroundColor: 'rgba(0,0,0,0.1)' 
      }
    ]}>
      <Ionicons 
        name={(object.iconName as any) || "cube"} 
        size={size * 0.5} 
        color={color} 
      />
    </View>
  );
};

// Default reference objects
const DEFAULT_REFERENCE_OBJECTS: ReferenceObject[] = [
  {
    id: 'apple',
    name: 'Medium Apple',
    volume: 182,
    unit: 'cm³',
    iconName: 'nutrition',
  },
  {
    id: 'soda-can',
    name: 'Standard Soda Can',
    volume: 355,
    unit: 'cm³',
    iconName: 'beer',
  },
  {
    id: 'baseball',
    name: 'Baseball',
    volume: 210,
    unit: 'cm³',
    iconName: 'baseball',
  },
  {
    id: 'coffee-cup',
    name: 'Coffee Cup (8oz)',
    volume: 240,
    unit: 'cm³',
    iconName: 'cafe',
  },
];

export default function EnhancedCalibrationModal({
  visible,
  onClose,
  onStartCalibration,
  theme,
  referenceObjects = DEFAULT_REFERENCE_OBJECTS,
}: EnhancedCalibrationModalProps) {
  // State
  const [selectedObject, setSelectedObject] = useState<ReferenceObject | null>(null);
  const [customName, setCustomName] = useState('');
  const [customVolume, setCustomVolume] = useState('');
  const [showCustomForm, setShowCustomForm] = useState(false);
  const [currentStep, setCurrentStep] = useState<'select' | 'guide' | 'custom'>('select');
  const [selectedId, setSelectedId] = useState<string | null>(null);
  
  // Animation values
  const slideAnimation = new Animated.Value(0);
  const opacityAnimation = new Animated.Value(0);
  
  // Reset state when modal opens/closes
  useEffect(() => {
    if (visible) {
      setCurrentStep('select');
      setSelectedObject(null);
      setCustomName('');
      setCustomVolume('');
      setShowCustomForm(false);
      
      // Animate in
      Animated.parallel([
        Animated.timing(slideAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
          easing: Easing.out(Easing.ease),
        }),
        Animated.timing(opacityAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Animate out
      slideAnimation.setValue(0);
      opacityAnimation.setValue(0);
    }
  }, [visible]);
  
  // Handle object selection
  const handleSelectObject = (object: ReferenceObject) => {
    setSelectedId(object.id);
    setSelectedObject(object);
    
    // Animate to next step after a short delay
    setTimeout(() => {
      setCurrentStep('guide');
    }, 300);
  };
  
  // Handle custom object option
  const handleCustomObject = () => {
    setSelectedId('custom');
    setCurrentStep('custom');
    setShowCustomForm(true);
  };
  
  // Handle starting calibration
  const handleStartCalibration = () => {
    if (showCustomForm) {
      if (customName.trim() === '' || customVolume.trim() === '') {
        return; // Don't proceed if fields are empty
      }
      onStartCalibration(customName, customVolume);
    } else if (selectedObject) {
      onStartCalibration(selectedObject.name, selectedObject.volume.toString());
    }
    onClose();
  };
  
  // Handle going back to object selection
  const handleBack = () => {
    setCurrentStep('select');
    setSelectedObject(null);
    setSelectedId(null);
  };
  
  // Animation styles
  const animatedContainer = {
    transform: [
      {
        translateY: slideAnimation.interpolate({
          inputRange: [0, 1],
          outputRange: [300, 0],
        }),
      },
    ],
    opacity: opacityAnimation,
  };
  
  // Render content based on current step
  const renderContent = () => {
    switch (currentStep) {
      case 'select':
        return (
          <>
            <Text style={[styles.title, { color: theme.colors.text }]}>
              Calibrate LiDAR Scanner
            </Text>
            <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
              Select a reference object to calibrate the LiDAR scanner. Choose an object you have available.
            </Text>
            
            <ScrollView style={styles.objectsContainer}>
              {referenceObjects.map((object) => (
                <TouchableOpacity
                  key={object.id}
                  style={[
                    styles.objectItem,
                    { 
                      backgroundColor: theme.colors.card,
                      borderColor: selectedId === object.id ? theme.colors.primary : theme.colors.border 
                    }
                  ]}
                  onPress={() => handleSelectObject(object)}
                >
                  <ReferenceObjectImage 
                    object={object} 
                    color={theme.colors.text} 
                  />
                  <View style={styles.objectInfo}>
                    <Text style={[styles.objectName, { color: theme.colors.text }]}>
                      {object.name}
                    </Text>
                    <Text style={[styles.objectVolume, { color: theme.colors.textSecondary }]}>
                      {object.volume} {object.unit}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
              
              <TouchableOpacity
                style={[
                  styles.objectItem,
                  { 
                    backgroundColor: theme.colors.card,
                    borderColor: selectedId === 'custom' ? theme.colors.primary : theme.colors.border 
                  }
                ]}
                onPress={handleCustomObject}
              >
                <View style={[styles.customIcon, { backgroundColor: theme.colors.border }]}>
                  <Ionicons name="add" size={30} color={theme.colors.text} />
                </View>
                <View style={styles.objectInfo}>
                  <Text style={[styles.objectName, { color: theme.colors.text }]}>
                    Custom Object
                  </Text>
                  <Text style={[styles.objectVolume, { color: theme.colors.textSecondary }]}>
                    Enter your own measurements
                  </Text>
                </View>
              </TouchableOpacity>
            </ScrollView>
          </>
        );
        
      case 'guide':
        return (
          <>
            <TouchableOpacity style={styles.backButton} onPress={handleBack}>
              <Ionicons name="arrow-back" size={24} color={theme.colors.primary} />
            </TouchableOpacity>
            
            <Text style={[styles.title, { color: theme.colors.text }]}>
              Calibration Instructions
            </Text>
            
            {selectedObject && (
              <>
                <View style={styles.selectedObjectContainer}>
                  <ReferenceObjectImage 
                    object={selectedObject}
                    size={100}
                    color={theme.colors.primary}
                  />
                  <Text style={[styles.selectedObjectName, { color: theme.colors.text }]}>
                    {selectedObject.name}
                  </Text>
                  <Text style={[styles.selectedObjectVolume, { color: theme.colors.primary }]}>
                    {selectedObject.volume} {selectedObject.unit}
                  </Text>
                </View>
                
                <View style={styles.instructionsContainer}>
                  <Text style={[styles.instructionTitle, { color: theme.colors.text }]}>
                    How to Calibrate:
                  </Text>
                  <View style={styles.instructionStep}>
                    <View style={[styles.stepNumber, { backgroundColor: theme.colors.primary }]}>
                      <Text style={styles.stepNumberText}>1</Text>
                    </View>
                    <Text style={[styles.instructionText, { color: theme.colors.textSecondary }]}>
                      Place the {selectedObject.name.toLowerCase()} on a flat surface
                    </Text>
                  </View>
                  <View style={styles.instructionStep}>
                    <View style={[styles.stepNumber, { backgroundColor: theme.colors.primary }]}>
                      <Text style={styles.stepNumberText}>2</Text>
                    </View>
                    <Text style={[styles.instructionText, { color: theme.colors.textSecondary }]}>
                      Hold your device about 30cm from the object
                    </Text>
                  </View>
                  <View style={styles.instructionStep}>
                    <View style={[styles.stepNumber, { backgroundColor: theme.colors.primary }]}>
                      <Text style={styles.stepNumberText}>3</Text>
                    </View>
                    <Text style={[styles.instructionText, { color: theme.colors.textSecondary }]}>
                      Move around to scan the entire object (complete 360° view)
                    </Text>
                  </View>
                </View>
                
                <TouchableOpacity
                  style={[styles.startButton, { backgroundColor: theme.colors.primary }]}
                  onPress={handleStartCalibration}
                >
                  <Text style={styles.startButtonText}>Start Calibration</Text>
                </TouchableOpacity>
              </>
            )}
          </>
        );
        
      case 'custom':
        return (
          <>
            <TouchableOpacity style={styles.backButton} onPress={handleBack}>
              <Ionicons name="arrow-back" size={24} color={theme.colors.primary} />
            </TouchableOpacity>
            
            <Text style={[styles.title, { color: theme.colors.text }]}>
              Custom Calibration Object
            </Text>
            <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
              Enter details for your custom calibration object.
              The volume should be in cubic centimeters (cm³).
            </Text>
            
            <View style={styles.formContainer}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Object Name</Text>
              <TextInput
                style={[
                  styles.textInput,
                  { 
                    backgroundColor: theme.colors.card,
                    color: theme.colors.text,
                    borderColor: theme.colors.border
                  }
                ]}
                placeholder="e.g., Coffee Mug"
                placeholderTextColor={theme.colors.textTertiary}
                value={customName}
                onChangeText={setCustomName}
              />
              
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
                Volume (cm³)
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  { 
                    backgroundColor: theme.colors.card,
                    color: theme.colors.text,
                    borderColor: theme.colors.border
                  }
                ]}
                placeholder="e.g., 330"
                placeholderTextColor={theme.colors.textTertiary}
                value={customVolume}
                onChangeText={(text) => setCustomVolume(text.replace(/[^0-9.]/g, ''))}
                keyboardType="decimal-pad"
              />
              
              <Text style={[styles.helpText, { color: theme.colors.textTertiary }]}>
                Common volumes:
                • Standard can (355 cm³)
                • Coffee mug (250-350 cm³)
                • Tennis ball (125 cm³)
                • Golf ball (40 cm³)
              </Text>
            </View>
            
            <TouchableOpacity
              style={[
                styles.startButton, 
                { 
                  backgroundColor: 
                    customName.trim() !== '' && customVolume.trim() !== '' 
                      ? theme.colors.primary 
                      : theme.colors.border
                }
              ]}
              onPress={handleStartCalibration}
              disabled={customName.trim() === '' || customVolume.trim() === ''}
            >
              <Text style={styles.startButtonText}>Start Custom Calibration</Text>
            </TouchableOpacity>
          </>
        );
    }
  };
  
  if (!visible) return null;
  
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={[styles.overlay, { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
        <Animated.View
          style={[
            styles.container,
            { backgroundColor: theme.colors.background },
            animatedContainer
          ]}
        >
          {/* Close button */}
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          
          {/* Content */}
          {renderContent()}
        </Animated.View>
      </View>
    </Modal>
  );
}

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  container: {
    width: width,
    height: height * 0.8,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    paddingTop: 30,
  },
  closeButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    zIndex: 10,
  },
  backButton: {
    position: 'absolute',
    top: 20,
    left: 20,
    zIndex: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    marginBottom: 25,
    textAlign: 'center',
    marginHorizontal: 20,
  },
  objectsContainer: {
    marginTop: 10,
    flex: 1,
  },
  objectItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 12,
    marginBottom: 15,
    borderWidth: 2,
  },
  objectImage: {
    width: 60,
    height: 60,
    marginRight: 15,
  },
  objectInfo: {
    flex: 1,
  },
  objectName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  objectVolume: {
    fontSize: 14,
  },
  customIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  formContainer: {
    marginTop: 20,
    width: '100%',
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    marginTop: 15,
  },
  textInput: {
    height: 50,
    borderWidth: 1,
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
  },
  startButton: {
    width: '100%',
    height: 54,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 30,
    marginBottom: Platform.OS === 'ios' ? 30 : 20,
  },
  startButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  selectedObjectContainer: {
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  largeObjectImage: {
    width: 100,
    height: 100,
    marginBottom: 10,
  },
  selectedObjectName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  selectedObjectVolume: {
    fontSize: 18,
    fontWeight: '500',
  },
  instructionsContainer: {
    marginTop: 10,
    paddingHorizontal: 10,
  },
  instructionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  instructionStep: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  stepNumber: {
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  stepNumberText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  instructionText: {
    fontSize: 16,
    flex: 1,
  },
  helpText: {
    fontSize: 12,
    marginTop: 8,
    lineHeight: 20,
  },
}); 