/**
 * ScannerWithLiDAR component with LiDAR volume estimation
 * Supports iOS devices with LiDAR (iPhone 12 Pro and newer)
 * Falls back to 2D bounding box estimation for other devices
 */
import React, { useState, useRef, useCallback, useMemo, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
  ScrollView,
} from 'react-native';
import { CameraView, useCameraPermissions } from 'expo-camera';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as ImageManipulator from 'expo-image-manipulator';
import { useTheme } from '@/hooks/useTheme';
import * as MealHistory from '@/utils/mealHistoryManager';
import useLiDARScanning from '@/hooks/LiDAR/useLiDARScanning';
import { ProcessingStage, ScannerWithLiDARProps } from '@/utils/LiDAR/types';
import { getScanQualityColor, getConfidenceColor, extractCalories, generateTagsFromFoodItems, guessMealTypeFromTime } from '@/utils/LiDAR/helpers';
import { getMultiFoodVolumeEstimates, detectContainers, DeviceCapability } from '@/utils/volumeEstimator';
import { calculateFoodWeight } from '@/utils/foodDensityMap';
import { classifyFoodImage } from '@/services/classificationService';
import { analyzeNutrition } from '@/services/openaiNutritionService';
import { processFoodScan } from '@/services/lidarScanningService';
import { useAuth } from '@/contexts/AuthContext';
import { segmentFoodImage, distributeLiDARVolume } from '@/services/foodSegmentationService';

// Import subcomponents
import VolumeVisualizer from './VolumeVisualizer';
import EnhancedCalibrationModal from './EnhancedCalibrationModal';
import ARGuidanceOverlay from './ARGuidanceOverlay';
import ARPositioningGuides from './ARPositioningGuides';

export default function ScannerWithLiDAR({ onScanComplete, onClose }: ScannerWithLiDARProps) {
  const { colors, isDark } = useTheme();
  const insets = useSafeAreaInsets();
  const [permission, requestPermission] = useCameraPermissions();
  const { user } = useAuth();
  
  // Refs
  const cameraRef = useRef<any>(null);
  
  // State
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [isCapturing, setIsCapturing] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [classificationResults, setClassificationResults] = useState<any[]>([]);
  const [multiFoodVolumeData, setMultiFoodVolumeData] = useState<any | null>(null);
  const [weightEstimates, setWeightEstimates] = useState<{[id: string]: number}>({});
  const [nutritionData, setNutritionData] = useState<any>(null);
  
  // State for portion adjustment
  const [adjustedWeights, setAdjustedWeights] = useState<{[id: string]: number}>({});
  const [isAdjusting, setIsAdjusting] = useState(false);
  
  // State for UI mode and calibration
  const [showCalibrationModal, setShowCalibrationModal] = useState(false);
  const [calibrationObject, setCalibrationObject] = useState('');
  const [calibrationVolume, setCalibrationVolume] = useState('');
  const [useEnhancedCalibration, setUseEnhancedCalibration] = useState(true);
  const [showARGuide, setShowARGuide] = useState(true);
  
  // State for AR positioning guides
  const [showARPositioningGuides, setShowARPositioningGuides] = useState(true);
  const [cameraAngle, setCameraAngle] = useState<'overhead' | 'side' | 'angled' | 'unknown'>('unknown');
  const [foodInCenter, setFoodInCenter] = useState(false);
  const [referenceObjectDetected, setReferenceObjectDetected] = useState(false);
  
  // Use LiDAR scanning hook
  const lidarScanning = useLiDARScanning();
  
  // Check camera permissions
  React.useEffect(() => {
    (async () => {
      await requestPermission();
      setHasPermission(permission?.granted ?? false);
    })();
  }, [requestPermission]);
  
  // Use device motion to determine camera angle
  useEffect(() => {
    // This would ideally use DeviceMotion to track device orientation
    // For now, we'll simulate this with a timer to change angles for demo purposes
    let timer: ReturnType<typeof setInterval>;
    
    if (lidarScanning.processingStage === 'scanning') {
      timer = setInterval(() => {
        // Simulate different camera angles during scanning
        const angles: ('overhead' | 'side' | 'angled')[] = ['overhead', 'side', 'angled'];
        const randomAngle = angles[Math.floor(Math.random() * angles.length)];
        setCameraAngle(randomAngle);
        
        // Simulate food centering detection
        setFoodInCenter(Math.random() > 0.3); // 70% chance of being centered
        
        // Simulate reference object detection based on scan progress
        if (lidarScanning.scanProgress > 0.5) {
          setReferenceObjectDetected(Math.random() > 0.5);
        }
      }, 3000);
    }
    
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [lidarScanning.processingStage, lidarScanning.scanProgress]);
  
  // Capture image for food classification
  const captureImage = useCallback(async () => {
    if (!cameraRef.current || isCapturing) return;
    
    setIsCapturing(true);
    lidarScanning.setProcessingStage('analyzing');
    
    try {
      // First pause for a moment to allow user to hold still
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Capture photo
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        skipProcessing: false,
      });
      
      // Resize for faster processing
      const resizedPhoto = await ImageManipulator.manipulateAsync(
        photo.uri,
        [{ resize: { width: 800 } }],
        { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
      );
      
      setCapturedImage(resizedPhoto.uri);
      
      // Calculate volume using LiDAR if available
      let volumeResult = null;
      if (lidarScanning.deviceSupportsLiDAR === DeviceCapability.LIDAR_SUPPORTED) {
        volumeResult = await lidarScanning.calculateLiDARVolume();
        await lidarScanning.stopLiDARScanning();
      }
      
      // Process the image using the service
      await processImage(resizedPhoto.uri, volumeResult);
    } catch (error) {
      console.error("Failed to capture or process image:", error);
      Alert.alert(
        "Processing Error",
        "Could not capture or process the image. Please try again."
      );
      setIsCapturing(false);
      lidarScanning.setProcessingStage('idle');
    }
  }, [isCapturing, lidarScanning]);
  
  // Process the captured image with multi-food support
  const processImage = useCallback(async (imageUri: string, volumeData: any | null) => {
    try {
      // Set processing stage to classifying
      lidarScanning.setProcessingStage('classifying');
      
      // Process food scan with our lidarScanningService
      const scanResult = await processFoodScan({
        imageUri,
        volumeData,
        deviceHasLidar: lidarScanning.deviceSupportsLiDAR === DeviceCapability.LIDAR_SUPPORTED,
        calibrationFactor: lidarScanning.calibrationFactor,
        userId: user?.id
      });
      
      if (!scanResult || !scanResult.foods.length) {
        throw new Error("No food items detected");
      }
      
      // Update state with processed data
      setClassificationResults(scanResult.foods.map((food, index) => ({
        id: `food-${index}`,
        name: food.name,
        confidence: 0.8 // Default confidence
      })));
      
      // If multiple food items, use advanced segmentation
      if (scanResult.foods.length > 1 && lidarScanning.deviceSupportsLiDAR === DeviceCapability.LIDAR_SUPPORTED) {
        try {
          
          const segmentationResult = await segmentFoodImage(imageUri, {
            includeMasks: true,
            refineEdges: true,
            useEnhancedModel: true,
            userId: user?.id
          });
          
          // Distribute LiDAR volume among segments
          if (segmentationResult.segments.length > 0 && volumeData) {
            const segmentsWithVolume = await distributeLiDARVolume(
              volumeData.volumeCm3,
              segmentationResult.segments,
              user?.id
            );
            
            // Create multiFoodVolumeData from segments
            setMultiFoodVolumeData({
              items: segmentsWithVolume.map((segment, index) => ({
                id: segment.id,
                name: scanResult.foods[index % scanResult.foods.length].name,
                volumeEstimation: {
                  volumeCm3: segment.estimatedVolumeCm3 || 0,
                  method: 'lidar',
                  confidence: segment.confidence
                }
              })),
              totalVolumeCm3: volumeData.volumeCm3
            });
            
            // Set weight estimates based on volumes
            const weights: {[id: string]: number} = {};
            segmentsWithVolume.forEach((segment, index) => {
              const foodName = scanResult.foods[index % scanResult.foods.length].name;
              weights[segment.id] = calculateFoodWeight(
                foodName,
                segment.estimatedVolumeCm3 || 0
              );
            });
            setWeightEstimates(weights);
          } else {
            // Fallback to standard distribution if segmentation failed
            useStandardDistribution(scanResult);
          }
        } catch (error) {
          console.error("Advanced segmentation failed, using standard distribution:", error);
          useStandardDistribution(scanResult);
        }
      } else {
        // Use standard distribution for single food or non-LiDAR devices
        useStandardDistribution(scanResult);
      }
      
      // Set nutrition data
      setNutritionData(scanResult.nutritionAnalysis);
      
      // Update processing stage
      lidarScanning.setProcessingStage('complete');
      
      // Call onScanComplete callback if provided
      if (onScanComplete) {
        onScanComplete(scanResult);
      }
      
    } catch (error) {
      console.error("Error processing image:", error);
      Alert.alert(
        "Processing Error",
        "Could not analyze the food. Please try again."
      );
      setIsCapturing(false);
      lidarScanning.setProcessingStage('idle');
    }
  }, [lidarScanning, onScanComplete, user]);
  
  // Helper function to use standard volume distribution
  const useStandardDistribution = useCallback((scanResult: any) => {
    setMultiFoodVolumeData({
      items: scanResult.foods.map((food, index) => ({
        id: `food-${index}`,
        name: food.name,
        volumeEstimation: {
          volumeCm3: food.volumeCm3,
          method: lidarScanning.deviceSupportsLiDAR === DeviceCapability.LIDAR_SUPPORTED ? 'lidar' : '2d',
          confidence: 0.8
        }
      })),
      totalVolumeCm3: scanResult.totalVolumeCm3
    });
    
    // Set weight estimates
    const weights: {[id: string]: number} = {};
    scanResult.foods.forEach((food, index) => {
      weights[`food-${index}`] = food.weightGrams;
    });
    setWeightEstimates(weights);
  }, [lidarScanning.deviceSupportsLiDAR]);
  
  // Handle scan button press
  const handleScanPress = useCallback(async () => {
    if (lidarScanning.processingStage === 'idle') {
      // Start LiDAR scanning if supported
      if (lidarScanning.deviceSupportsLiDAR === DeviceCapability.LIDAR_SUPPORTED) {
        await lidarScanning.startLiDARScanning();
        
        // Wait a moment to initialize scanning before capturing
        setTimeout(() => {
          captureImage();
        }, 1000);
      } else {
        // Just capture image on non-LiDAR devices
        captureImage();
      }
    }
  }, [lidarScanning, captureImage]);
  
  // Handle close button press
  const handleClosePress = useCallback(() => {
    // Stop scanning if active
    if (lidarScanning.isScanningActive) {
      lidarScanning.stopLiDARScanning();
    }
    
    // Call onClose callback if provided
    if (onClose) {
      onClose();
    }
  }, [lidarScanning, onClose]);
  
  // Handle retry button press
  const handleRetryPress = useCallback(() => {
    // Reset state
    setCapturedImage(null);
    setClassificationResults([]);
    setMultiFoodVolumeData(null);
    setWeightEstimates({});
    lidarScanning.setProcessingStage('idle');
  }, [lidarScanning]);
  
  // Portion adjustment handlers
  const toggleAdjustmentMode = useCallback(() => {
    if (!isAdjusting) {
      // Initialize adjusted weights with current weights
      setAdjustedWeights({ ...weightEstimates });
    }
    setIsAdjusting(!isAdjusting);
  }, [isAdjusting, weightEstimates]);
  
  const handleAdjustWeight = useCallback((id: string, value: number) => {
    setAdjustedWeights(prev => ({
      ...prev,
      [id]: value
    }));
  }, []);
  
  // Move updateNutritionWithAdjustedWeights declaration up before it's used
  const updateNutritionWithAdjustedWeights = useCallback(async () => {
    if (!multiFoodVolumeData) return;
    
    // Build a prompt that includes all items with their adjusted weights
    const foodItemsDescription = multiFoodVolumeData.items.map(item => 
      `${Math.round(adjustedWeights[item.id] || weightEstimates[item.id])} grams of ${item.name}`
    ).join(", ");
    
    const enhancedPrompt = `A user ate a meal containing ${foodItemsDescription}. 
    Provide detailed nutrition information including total calories, macronutrients (protein, fat, carbs), 
    and a brief health assessment. Also include individual calorie counts for each food item.`;
    
    // Show loading indicator
    setNutritionData(null);
    
    try {
      const nutritionAnalysis = await analyzeNutrition(enhancedPrompt);
      setNutritionData(nutritionAnalysis);
    } catch (error) {
      console.error("Error updating nutrition with adjusted weights:", error);
      Alert.alert("Error", "Failed to update nutrition information");
    }
  }, [adjustedWeights, multiFoodVolumeData, weightEstimates]);
  
  const applyAdjustedWeights = useCallback(async () => {
    // Update weight estimates with adjusted values
    setWeightEstimates(adjustedWeights);
    
    // Recalculate nutrition with adjusted weights
    if (multiFoodVolumeData) {
      await updateNutritionWithAdjustedWeights();
    }
    
    setIsAdjusting(false);
  }, [adjustedWeights, multiFoodVolumeData, updateNutritionWithAdjustedWeights]);
  
  // Calibration handlers
  const handleStartCalibration = useCallback((name: string, volume: string) => {
    setCalibrationObject(name);
    setCalibrationVolume(volume);
    setShowCalibrationModal(false);
    lidarScanning.startCalibrationScanning();
  }, [lidarScanning]);
  
  const handlePerformCalibration = useCallback(() => {
    lidarScanning.performCalibration(calibrationObject, calibrationVolume);
  }, [lidarScanning, calibrationObject, calibrationVolume]);
  
  // Render calibration modal
  const calibrationModal = useMemo(() => {
    if (useEnhancedCalibration) {
      return (
        <EnhancedCalibrationModal 
          visible={showCalibrationModal}
          onClose={() => setShowCalibrationModal(false)}
          onStartCalibration={handleStartCalibration}
          theme={{ colors, isDark }}
        />
      );
    }
    
    // Basic calibration modal as fallback
    return null;
  }, [showCalibrationModal, handleStartCalibration, colors, isDark, useEnhancedCalibration]);
  
  // Render calibration UI when in calibration mode
  const renderCalibrationUI = useCallback(() => {
    return (
      <View style={[styles.calibrationContainer, { backgroundColor: 'rgba(0,0,0,0.7)' }]}>
        <Text style={styles.calibrationTitle}>Calibration Mode</Text>
        <Text style={styles.calibrationInstructions}>
          Place {calibrationObject} on a flat surface and scan from multiple angles
        </Text>
        
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { width: `${lidarScanning.scanProgress * 100}%` }
              ]} 
            />
          </View>
          <Text style={styles.progressText}>
            {lidarScanning.planeDetected ? "Surface detected! Scanning object..." : "Looking for a flat surface..."}
          </Text>
        </View>
        
        <View style={styles.calibrationButtons}>
          <TouchableOpacity
            style={[styles.calibrationButton, { backgroundColor: '#f44336' }]}
            onPress={lidarScanning.cancelCalibration}
          >
            <Text style={styles.buttonText}>Cancel</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.calibrationButton, { backgroundColor: '#4CAF50' }]}
            onPress={handlePerformCalibration}
            disabled={!lidarScanning.planeDetected || lidarScanning.scanProgress < 0.5}
          >
            <Text style={styles.buttonText}>Complete</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }, [lidarScanning.planeDetected, lidarScanning.scanProgress, lidarScanning.cancelCalibration, handlePerformCalibration]);
  
  // Render loading state with scanner feedback
  const loadingState = useMemo(() => {
    let message = "";
    
    switch (lidarScanning.processingStage) {
      case 'scanning':
        message = "Scanning with LiDAR...";
        break;
      case 'analyzing':
        message = "Processing image...";
        break;
      case 'classifying':
        message = "Identifying food items...";
        break;
      case 'estimating':
        message = "Calculating nutrition...";
        break;
      default:
        message = "Processing...";
    }
    
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.card }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.text }]}>{message}</Text>
        
        {lidarScanning.processingStage === 'scanning' && (
          <View style={styles.progressContainer}>
            <View 
              style={[
                styles.progressBar, 
                { 
                  backgroundColor: colors.border,
                }
              ]}
            >
              <View 
                style={[
                  styles.progressFill, 
                  { 
                    width: `${lidarScanning.scanProgress * 100}%`,
                    backgroundColor: getScanQualityColor(lidarScanning.scanQuality) 
                  }
                ]} 
              />
            </View>
            
            <View style={styles.scanQualityIndicator}>
              <Text style={[styles.scanQualityText, { color: colors.textSecondary }]}>
                Scan Quality: {lidarScanning.scanQuality.toUpperCase()}
              </Text>
              
              <View style={styles.qualityDots}>
                <View style={[
                  styles.qualityDot, 
                  { backgroundColor: lidarScanning.scanQuality === 'low' ? '#f44336' : '#e0e0e0' }
                ]} />
                <View style={[
                  styles.qualityDot, 
                  { backgroundColor: lidarScanning.scanQuality === 'medium' ? '#ffeb3b' : '#e0e0e0' }
                ]} />
                <View style={[
                  styles.qualityDot, 
                  { backgroundColor: lidarScanning.scanQuality === 'high' ? '#4caf50' : '#e0e0e0' }
                ]} />
              </View>
            </View>
            
            <Text style={[styles.progressText, { color: colors.textSecondary }]}>
              {lidarScanning.planeDetected 
                ? `Surface detected! Capturing mesh (${lidarScanning.meshCount} fragments)` 
                : "Looking for a flat surface..."}
            </Text>
            
            {/* Scanning tips */}
            {lidarScanning.scanningTips.length > 0 && (
              <View style={styles.tipsContainer}>
                <Text style={[styles.tipsTitle, { color: colors.text }]}>Tips:</Text>
                {lidarScanning.scanningTips.slice(-2).map((tip, index) => (
                  <Text key={index} style={[styles.tipText, { color: colors.textSecondary }]}>
                    • {tip}
                  </Text>
                ))}
              </View>
            )}
          </View>
        )}
      </View>
    );
  }, [lidarScanning.processingStage, lidarScanning.scanProgress, lidarScanning.scanQuality, colors, lidarScanning.planeDetected, lidarScanning.meshCount, lidarScanning.scanningTips]);

  // Render results after scanning
  const renderResults = useCallback(() => {
    if (!multiFoodVolumeData || !capturedImage) return null;
    
    const foods = multiFoodVolumeData.items;
    const totalWeight = Object.values(weightEstimates).reduce((sum, weight) => sum + weight, 0);
    
    return (
      <View style={[styles.resultsContainer, { backgroundColor: colors.background }]}>
        <Image source={{ uri: capturedImage }} style={styles.resultImage} />
        
        <View style={[styles.resultCard, { backgroundColor: colors.card }]}>
          <Text style={[styles.foodTitle, { color: colors.text }]}>
            {foods.length > 1 ? "Multiple Foods Detected" : foods[0].name}
          </Text>
          
          <View style={styles.divider} />
          
          {/* Multiple food items */}
          {foods.length > 1 ? (
            <ScrollView style={styles.foodItemsContainer}>
              {foods.map((item) => (
                <View key={item.id} style={styles.foodItemRow}>
                  <View style={styles.foodItemInfo}>
                    <Text style={[styles.foodItemName, { color: colors.text }]}>
                      {item.name}
                    </Text>
                    <Text style={[styles.foodItemDetails, { color: colors.textSecondary }]}>
                      {Math.round(item.volumeEstimation.volumeCm3)} cm³ • {Math.round(weightEstimates[item.id])}g
                    </Text>
                  </View>
                  <View style={[styles.confidenceBadge, { 
                    backgroundColor: getConfidenceColor(item.volumeEstimation.confidence) 
                  }]}>
                    <Text style={styles.confidenceBadgeText}>
                      {Math.round(item.volumeEstimation.confidence * 100)}%
                    </Text>
                  </View>
                </View>
              ))}
            </ScrollView>
          ) : (
            // Single food item display (original style)
            <View style={styles.infoRow}>
              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  Volume
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {Math.round(multiFoodVolumeData.items[0].volumeEstimation.volumeCm3)} cm³
                </Text>
                <Text style={[styles.infoMethod, { color: colors.textTertiary }]}>
                  {lidarScanning.deviceSupportsLiDAR === DeviceCapability.LIDAR_SUPPORTED
                    ? "LiDAR scan"
                    : "Estimated"}
                </Text>
              </View>
              
              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  Weight
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {Object.values(weightEstimates)[0] ? `${Math.round(Object.values(weightEstimates)[0])} g` : "N/A"}
                </Text>
                <Text style={[styles.infoMethod, { color: colors.textTertiary }]}>
                  Based on density
                </Text>
              </View>
              
              <View style={styles.infoItem}>
                <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>
                  Confidence
                </Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {Math.round(multiFoodVolumeData.items[0].volumeEstimation.confidence * 100)}%
                </Text>
                <Text style={[styles.infoMethod, { color: colors.textTertiary }]}>
                  Scan quality
                </Text>
              </View>
            </View>
          )}
          
          {/* Visual comparison of food volumes */}
          {foods.length > 0 && (
            <View style={styles.visualComparisonContainer}>
              <Text style={[styles.visualComparisonTitle, { color: colors.text }]}>
                Volume Comparison
              </Text>
              <ScrollView horizontal style={styles.visualizerScroll} contentContainerStyle={styles.visualizerScrollContent}>
                {foods.map(item => (
                  <VolumeVisualizer 
                    key={item.id}
                    volume={item.volumeEstimation.volumeCm3}
                    maxVolume={Math.max(...foods.map(i => i.volumeEstimation.volumeCm3)) * 1.2} // Add margin
                    foodName={item.name}
                    style={{ marginRight: 15 }}
                  />
                ))}
              </ScrollView>
            </View>
          )}
          
          {/* Total information for multiple foods */}
          {foods.length > 1 && (
            <View style={styles.totalSection}>
              <Text style={[styles.totalTitle, { color: colors.textSecondary }]}>
                Total
              </Text>
              <View style={styles.totalRow}>
                <Text style={[styles.totalLabel, { color: colors.textSecondary }]}>
                  Volume:
                </Text>
                <Text style={[styles.totalValue, { color: colors.text }]}>
                  {Math.round(multiFoodVolumeData.totalVolumeCm3)} cm³
                </Text>
              </View>
              <View style={styles.totalRow}>
                <Text style={[styles.totalLabel, { color: colors.textSecondary }]}>
                  Weight:
                </Text>
                <Text style={[styles.totalValue, { color: colors.text }]}>
                  {Math.round(totalWeight)} g
                </Text>
              </View>
            </View>
          )}
          
          <View style={styles.divider} />
          
          {nutritionData ? (
            <View style={styles.nutritionContainer}>
              <Text style={[styles.nutritionTitle, { color: colors.text }]}>
                Nutrition Analysis
              </Text>
              <Text style={[styles.nutritionText, { color: colors.textSecondary }]}>
                {nutritionData.analysis}
              </Text>
            </View>
          ) : (
            <ActivityIndicator size="small" color={colors.primary} />
          )}
          
          <TouchableOpacity
            style={[styles.button, { backgroundColor: colors.card, borderWidth: 1, borderColor: colors.border, marginBottom: 10 }]}
            onPress={toggleAdjustmentMode}
          >
            <Text style={[styles.buttonText, { color: colors.text }]}>
              {isAdjusting ? "Cancel Adjustments" : "Adjust Portions"}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, { backgroundColor: colors.primary }]}
            onPress={handleRetryPress}
          >
            <Text style={styles.buttonText}>Scan Again</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }, [multiFoodVolumeData, capturedImage, weightEstimates, colors, isAdjusting, toggleAdjustmentMode, handleRetryPress, nutritionData]);

  // If we don't have permission yet, show a permission request message
  if (hasPermission === null) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Text style={[styles.permissionText, { color: colors.text }]}>
          Requesting camera permission...
        </Text>
      </View>
    );
  }
  
  if (hasPermission === false) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Text style={[styles.permissionText, { color: colors.text }]}>
          Camera permission is required to use the scanner.
        </Text>
        <TouchableOpacity
          style={[styles.button, { backgroundColor: colors.primary }]}
          onPress={handleClosePress}
        >
          <Text style={styles.buttonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      
      {/* Camera view or results */}
      {lidarScanning.processingStage === 'complete' ? (
        renderResults()
      ) : (
        <CameraView 
          ref={cameraRef}
          style={styles.camera}
          facing="back"
        >
          {/* Close button */}
          <TouchableOpacity
            style={[styles.closeButton, { top: insets.top + 10 }]}
            onPress={handleClosePress}
          >
            <Ionicons name="close" size={30} color="#fff" />
          </TouchableOpacity>
          
          {/* Scanning guide overlay */}
          {lidarScanning.processingStage === 'idle' && (
            <View style={styles.guideOverlay}>
              <View style={styles.guideBox}>
                <Text style={styles.guideText}>
                  Center the food and tap Scan
                </Text>
              </View>
            </View>
          )}
          
          {/* Scan button */}
          {lidarScanning.processingStage === 'idle' && (
            <View style={{ position: 'absolute', bottom: insets.bottom + 20, width: '100%', alignItems: 'center' }}>
              <TouchableOpacity
                style={styles.scanButton}
                onPress={handleScanPress}
              >
                <Text style={styles.scanButtonText}>
                  {lidarScanning.deviceSupportsLiDAR === DeviceCapability.LIDAR_SUPPORTED
                    ? "Scan with LiDAR"
                    : "Scan Food"}
                </Text>
              </TouchableOpacity>
              
              {/* Calibration button - only show on LiDAR devices */}
              {lidarScanning.deviceSupportsLiDAR === DeviceCapability.LIDAR_SUPPORTED && (
                <TouchableOpacity
                  style={[styles.calibrateButton, { marginTop: 10 }]}
                  onPress={() => setShowCalibrationModal(true)}
                >
                  <Text style={styles.calibrateButtonText}>Calibrate Scanner</Text>
                </TouchableOpacity>
              )}
            </View>
          )}
          
          {/* LiDAR support info */}
          {lidarScanning.processingStage === 'idle' && (
            <View style={[styles.deviceInfo, { bottom: insets.bottom + 160 }]}>
              <Text style={styles.deviceInfoText}>
                {lidarScanning.deviceSupportsLiDAR === DeviceCapability.LIDAR_SUPPORTED
                  ? lidarScanning.calibrationFactor !== 1.0 
                    ? `LiDAR Scanning Enabled (Calibrated: ${lidarScanning.calibrationFactor.toFixed(2)}x)`
                    : "LiDAR Scanning Enabled"
                  : lidarScanning.deviceSupportsLiDAR === DeviceCapability.NO_LIDAR
                  ? "Using Standard Camera (No LiDAR)"
                  : "Checking device capabilities..."}
              </Text>
            </View>
          )}
          
          {/* Existing AR guidance overlay */}
          {showARGuide && !showCalibrationModal && lidarScanning.processingStage === 'scanning' && (
            <ARGuidanceOverlay
              planeDetected={lidarScanning.planeDetected}
              optimalScanningDistance={lidarScanning.optimalScanningDistance}
              scanStability={lidarScanning.scanStability}
              guidePosition={lidarScanning.guidePosition}
              onToggleGuide={() => setShowARGuide(false)}
              scanCompleteness={lidarScanning.scanProgress}
              objectsInView={lidarScanning.meshCount}
            />
          )}
          
          {/* New AR positioning guides */}
          {showARPositioningGuides && !showCalibrationModal && lidarScanning.processingStage === 'scanning' && (
            <ARPositioningGuides
              isActive={true}
              scanStability={lidarScanning.scanStability}
              planeDetected={lidarScanning.planeDetected}
              optimalDistance={lidarScanning.optimalScanningDistance}
              cameraAngle={cameraAngle}
              foodInCenter={foodInCenter}
              scanProgress={lidarScanning.scanProgress}
              referenceObjectDetected={referenceObjectDetected}
            />
          )}
        </CameraView>
      )}
      
      {/* Calibration modal */}
      {calibrationModal}
      
      {/* Calibration UI */}
      {lidarScanning.processingStage === 'calibrating' && renderCalibrationUI()}
      
      {/* Loading overlay */}
      {(lidarScanning.processingStage !== 'idle' && 
        lidarScanning.processingStage !== 'complete' && 
        lidarScanning.processingStage !== 'calibrating') && loadingState}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    left: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  guideOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  guideBox: {
    width: 200,
    height: 200,
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.8)',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  guideText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
    padding: 10,
  },
  scanButton: {
    paddingHorizontal: 30,
    paddingVertical: 15,
    backgroundColor: '#2196F3',
    borderRadius: 30,
  },
  scanButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  deviceInfo: {
    position: 'absolute',
    bottom: 100,
    alignSelf: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  deviceInfoText: {
    color: '#fff',
    fontSize: 14,
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
  },
  loadingText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 20,
    color: '#fff',
  },
  progressContainer: {
    width: '80%',
    marginTop: 30,
  },
  progressBar: {
    height: 10,
    width: '100%',
    backgroundColor: '#444',
    borderRadius: 5,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#2196F3',
  },
  progressText: {
    fontSize: 14,
    marginTop: 10,
    textAlign: 'center',
    color: '#ccc',
  },
  scanQualityIndicator: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 5,
  },
  scanQualityText: {
    fontSize: 14,
  },
  qualityDots: {
    flexDirection: 'row',
  },
  qualityDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginHorizontal: 2,
  },
  tipsContainer: {
    marginTop: 20,
    alignSelf: 'flex-start',
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  tipText: {
    fontSize: 14,
    marginBottom: 5,
  },
  resultsContainer: {
    flex: 1,
  },
  resultImage: {
    width: '100%',
    height: '40%',
  },
  resultCard: {
    flex: 1,
    marginTop: -20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  foodTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  divider: {
    height: 1,
    backgroundColor: 'rgba(150,150,150,0.2)',
    marginVertical: 15,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  infoItem: {
    flex: 1,
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 14,
    marginBottom: 5,
  },
  infoValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  infoMethod: {
    fontSize: 12,
    marginTop: 5,
  },
  foodItemsContainer: {
    maxHeight: 150,
  },
  foodItemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(150,150,150,0.1)',
  },
  foodItemInfo: {
    flex: 1,
  },
  foodItemName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  foodItemDetails: {
    fontSize: 14,
  },
  confidenceBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 40,
    alignItems: 'center',
  },
  confidenceBadgeText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  totalSection: {
    marginTop: 15,
    backgroundColor: 'rgba(0,0,0,0.05)',
    padding: 10,
    borderRadius: 8,
  },
  totalTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 2,
  },
  totalLabel: {
    fontSize: 14,
  },
  totalValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  visualComparisonContainer: {
    marginTop: 20,
    marginBottom: 10,
  },
  visualComparisonTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  visualizerScroll: {
    marginVertical: 10,
  },
  visualizerScrollContent: {
    paddingVertical: 5,
    paddingHorizontal: 5,
  },
  nutritionContainer: {
    marginBottom: 20,
  },
  nutritionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  nutritionText: {
    fontSize: 16,
    lineHeight: 24,
  },
  button: {
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 20,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  permissionText: {
    fontSize: 18,
    textAlign: 'center',
    margin: 20,
  },
  calibrationContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  calibrationTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 10,
  },
  calibrationInstructions: {
    fontSize: 16,
    color: 'white',
    textAlign: 'center',
    marginBottom: 30,
  },
  calibrationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 30,
  },
  calibrationButton: {
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 10,
    minWidth: 120,
    alignItems: 'center',
  },
  calibrateButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: 'rgba(255,255,255,0.25)',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'white',
  },
  calibrateButtonText: {
    color: 'white',
    fontSize: 14,
  },
}); 