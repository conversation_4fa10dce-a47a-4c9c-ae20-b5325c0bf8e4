/**
 * Enhanced Volume Visualizer Component
 * Provides rich 3D visualization of scanned food volume data
 */
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { View, Text, StyleSheet, Animated, Easing, TouchableOpacity, Platform } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { Circle, Rect, Path, G, Text as SvgText } from 'react-native-svg';
import { VolumeVisualizerProps } from '@/utils/LiDAR/types';
import { getFoodColor, getShapeForFood, getStandardServing, getPortionPercentage } from '@/utils/LiDAR/helpers';
import { Canvas, useThree, useFrame } from '@react-three/fiber/native';
import { Mesh, MeshStandardMaterial } from 'three';
import { useAnimatedReaction } from 'react-native-reanimated';

// 3D Food Visualization Component using Three.js
const FoodMesh: React.FC<{
  volume: number;
  maxVolume: number;
  foodName: string;
  animate: boolean;
}> = ({ volume, maxVolume, foodName, animate }) => {
  const meshRef = useRef<Mesh>(null);
  const materialRef = useRef<MeshStandardMaterial>(null);
  const foodColor = getFoodColor(foodName);
  const shape = getShapeForFood(foodName);
  
  // Scale based on volume
  const scale = useMemo(() => {
    // Calculate relative size based on cube root of volume ratio
    // This preserves the volume relationship while making the visualization more reasonable
    return Math.pow(volume / maxVolume, 1/3) * 1.5;
  }, [volume, maxVolume]);
  
  // Animation state
  const [rotation, setRotation] = useState(0);
  const [pulseScale, setPulseScale] = useState(1);
  
  // Set up light and camera
  const { camera, gl } = useThree();
  useEffect(() => {
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      camera.position.set(0, 0, 4);
      gl.setClearColor('#f0f0f0', 0);
    }
  }, [camera, gl]);
  
  // Animation loop
  useFrame((state, delta) => {
    if (meshRef.current && animate) {
      // Rotate the object
      meshRef.current.rotation.y += delta * 0.5;
      
      // Add subtle breathing animation
      const pulse = Math.sin(state.clock.elapsedTime * 2) * 0.05 + 1;
      meshRef.current.scale.set(scale * pulse, scale * pulse, scale * pulse);
      
      // Material effects
      if (materialRef.current) {
        materialRef.current.emissiveIntensity = 0.2 + Math.sin(state.clock.elapsedTime) * 0.1;
      }
    }
  });
  
  return (
    <>
      {/* Ambient and point lights for better shading */}
      <ambientLight intensity={0.6} />
      <pointLight position={[10, 10, 10]} intensity={0.8} />
      <pointLight position={[-10, -10, -10]} intensity={0.4} color="#ffffff" />
      
      {/* Food mesh - either sphere or cube based on shape */}
      {shape === 'circle' ? (
        <mesh ref={meshRef}>
          <sphereGeometry args={[1, 32, 32]} />
          <meshStandardMaterial 
            ref={materialRef}
            color={foodColor}
            metalness={0.1}
            roughness={0.7}
            emissive={foodColor}
            emissiveIntensity={0.2}
          />
        </mesh>
      ) : (
        <mesh ref={meshRef}>
          <boxGeometry args={[1, 1, 1]} />
          <meshStandardMaterial 
            ref={materialRef}
            color={foodColor}
            metalness={0.1}
            roughness={0.7}
            emissive={foodColor}
            emissiveIntensity={0.2}
          />
        </mesh>
      )}
      
      {/* Grid/floor for reference */}
      <gridHelper 
        args={[6, 10, '#cccccc', '#e0e0e0']} 
        position={[0, -1.5, 0]} 
        rotation={[0, 0, 0]}
      />
    </>
  );
};

// Simple 2D Fallback Visualization
const SimpleFoodVisualization: React.FC<{
  volume: number;
  maxVolume: number;
  foodName: string;
  style?: any;
}> = ({ volume, maxVolume, foodName, style }) => {
  const foodColor = getFoodColor(foodName);
  const shape = getShapeForFood(foodName);
  
  // Calculate relative size based on area for 2D
  const relativeSizePercentage = Math.min(100, Math.pow(volume / maxVolume, 1/2) * 100);
  
  const size = relativeSizePercentage;
  const maxSize = 100;
  
  // Animation
  const animatedValue = useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    Animated.sequence([
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
        easing: Easing.elastic(1),
      }),
      Animated.loop(
        Animated.sequence([
          Animated.timing(animatedValue, {
            toValue: 1.05,
            duration: 1500,
            useNativeDriver: true,
            easing: Easing.inOut(Easing.sin),
          }),
          Animated.timing(animatedValue, {
            toValue: 0.95,
            duration: 1500,
            useNativeDriver: true,
            easing: Easing.inOut(Easing.sin),
          }),
        ])
      ),
    ]).start();
  }, []);
  
  const animatedStyle = {
    transform: [
      { scale: animatedValue },
    ],
  };
  
  return (
    <View style={[styles.simpleContainer, style]}>
      <Text style={styles.label}>{foodName}</Text>
      
      <View style={styles.visualContainer}>
        <View style={styles.maxSizeIndicator} />
        
        <Animated.View style={animatedStyle}>
          {shape === 'circle' ? (
            <View
              style={[
                styles.circleShape,
                {
                  width: size,
                  height: size,
                  borderRadius: size / 2,
                  backgroundColor: foodColor,
                },
              ]}
            />
          ) : (
            <View
              style={[
                styles.rectangleShape,
                {
                  width: size,
                  height: size,
                  backgroundColor: foodColor,
                },
              ]}
            />
          )}
        </Animated.View>
      </View>
      
      <Text style={styles.volumeText}>{Math.round(volume)} cm³</Text>
    </View>
  );
};

// Main Component
export default function VolumeVisualizer({
  volume,
  maxVolume = 500,
  foodName,
  style,
}: VolumeVisualizerProps) {
  const [use3D, setUse3D] = useState(true);
  const [animate, setAnimate] = useState(true);
  
  // Get standard serving information
  const standardServing = getStandardServing(foodName);
  const portionPercentage = getPortionPercentage(foodName, volume);
  
  // Toggle between 3D and 2D visualizations
  const toggleVisualization = () => {
    setUse3D(!use3D);
  };
  
  // Create a more detailed visual representation based on food properties
  return (
    <View style={[styles.container, style]}>
      <Text style={styles.foodName}>{foodName}</Text>
      
      <TouchableOpacity 
        activeOpacity={0.8}
        onPress={toggleVisualization}
        onLongPress={() => setAnimate(!animate)}
        style={styles.visualizerContainer}
      >
        {Platform.OS !== 'web' && use3D ? (
          <View style={styles.canvas3DContainer}>
            <Canvas style={styles.canvas}>
              <FoodMesh 
                volume={volume} 
                maxVolume={maxVolume} 
                foodName={foodName}
                animate={animate}
              />
            </Canvas>
            <View style={styles.canvas3DOverlay}>
              <Text style={styles.overlay3DText}>{Math.round(volume)} cm³</Text>
            </View>
          </View>
        ) : (
          <SimpleFoodVisualization
            volume={volume}
            maxVolume={maxVolume}
            foodName={foodName}
          />
        )}
      </TouchableOpacity>
      
      <View style={styles.infoContainer}>
        <View style={styles.volumeContainer}>
          <Text style={styles.volumeLabel}>Volume</Text>
          <Text style={styles.volumeValue}>{Math.round(volume)} cm³</Text>
        </View>
        
        {portionPercentage !== null && (
          <View style={styles.portionContainer}>
            <Text style={styles.portionLabel}>Portion</Text>
            <LinearGradient
              colors={portionPercentage > 100 ? ['#ff9800', '#f44336'] : ['#4caf50', '#8bc34a']}
              start={{ x: 0, y: 0.5 }}
              end={{ x: 1, y: 0.5 }}
              style={styles.portionGradient}
            >
              <Text style={styles.portionValue}>{portionPercentage}%</Text>
            </LinearGradient>
            <Text style={styles.standardServing}>{standardServing}</Text>
          </View>
        )}
      </View>
      
      {/* Visualization mode indicator */}
      <TouchableOpacity 
        style={styles.visualModeButton} 
        onPress={toggleVisualization}
      >
        <Text style={styles.visualModeText}>
          {use3D ? '3D' : '2D'} View
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: 180,
    padding: 10,
    borderRadius: 15,
    backgroundColor: '#f9f9f9',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    alignItems: 'center',
  },
  foodName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  visualizerContainer: {
    width: 150,
    height: 150,
    marginVertical: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  canvas3DContainer: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
    overflow: 'hidden',
    backgroundColor: '#f0f0f0',
  },
  canvas: {
    flex: 1,
  },
  canvas3DOverlay: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    backgroundColor: 'rgba(0,0,0,0.6)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 10,
  },
  overlay3DText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  simpleContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 5,
  },
  visualContainer: {
    width: 100,
    height: 100,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  maxSizeIndicator: {
    position: 'absolute',
    width: 100,
    height: 100,
    borderWidth: 1,
    borderColor: '#ccc',
    borderStyle: 'dashed',
    borderRadius: 5,
  },
  circleShape: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  rectangleShape: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 4,
  },
  volumeText: {
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 10,
  },
  infoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 10,
  },
  volumeContainer: {
    alignItems: 'center',
    flex: 1,
  },
  volumeLabel: {
    fontSize: 12,
    color: '#666',
  },
  volumeValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  portionContainer: {
    alignItems: 'center',
    flex: 1,
  },
  portionLabel: {
    fontSize: 12,
    color: '#666',
  },
  portionGradient: {
    borderRadius: 10,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginVertical: 4,
  },
  portionValue: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  standardServing: {
    fontSize: 8,
    color: '#888',
    textAlign: 'center',
  },
  visualModeButton: {
    marginTop: 10,
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#e0e0e0',
    borderRadius: 15,
  },
  visualModeText: {
    fontSize: 12,
    fontWeight: '600',
  },
}); 