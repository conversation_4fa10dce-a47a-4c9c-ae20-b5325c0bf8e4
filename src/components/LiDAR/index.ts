/**
 * LiDAR component exports
 * A modular architecture for the LiDAR scanning functionality
 */

/**
 * Main scanner component that provides LiDAR-based food scanning
 * Supports volume estimation, nutritional analysis, and portion adjustment
 */
export { default as ScannerWithLiDAR } from './ScannerWithLiDAR';

/**
 * Visualizes food volume in 3D with interactive rotation and comparison tools
 * Provides visual representation of scanned food items
 */
export { default as VolumeVisualizer } from './VolumeVisualizer';

/**
 * Modal for scanner calibration using reference objects of known volume
 * Improves accuracy of LiDAR measurements with custom calibration factors
 */
export { default as EnhancedCalibrationModal } from './EnhancedCalibrationModal';

/**
 * Provides real-time AR guidance during scanning process
 * Helps users position device optimally for accurate scanning
 */
export { default as ARGuidanceOverlay } from './ARGuidanceOverlay'; 