import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { View, Text, StyleSheet, Image, ActivityIndicator, Dimensions } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SafeAreaView } from 'react-native-safe-area-context';

// Key for maintenance mode in AsyncStorage
const MAINTENANCE_MODE_KEY = 'app_maintenance_mode';

// Context type definition
interface MaintenanceModeContextType {
  isInMaintenanceMode: boolean;
  setMaintenanceMode: (enabled: boolean, message?: string) => Promise<void>;
  maintenanceMessage: string;
}

// Default maintenance message
const DEFAULT_MAINTENANCE_MESSAGE = 
  "We're currently updating our database to provide you with better service. " +
  "The app will be back online shortly. Thank you for your patience!";

// Create context with default values
const MaintenanceModeContext = createContext<MaintenanceModeContextType>({
  isInMaintenanceMode: false,
  setMaintenanceMode: async () => {},
  maintenanceMessage: DEFAULT_MAINTENANCE_MESSAGE
});

// Hook to use the maintenance mode context
export const useMaintenanceMode = () => useContext(MaintenanceModeContext);

// Provider component to wrap the app
export function MaintenanceModeProvider({ children }: { children: ReactNode }) {
  const [isInMaintenanceMode, setIsInMaintenanceMode] = useState(false);
  const [maintenanceMessage, setMaintenanceMessage] = useState(DEFAULT_MAINTENANCE_MESSAGE);
  const [isLoading, setIsLoading] = useState(true);
  
  // Check maintenance mode on mount
  useEffect(() => {
    const checkMaintenanceMode = async () => {
      try {
        const storedValue = await AsyncStorage.getItem(MAINTENANCE_MODE_KEY);
        
        if (storedValue) {
          const { enabled, message } = JSON.parse(storedValue);
          setIsInMaintenanceMode(enabled);
          if (message) {
            setMaintenanceMessage(message);
          }
        }
      } catch (error) {
        console.error('Error checking maintenance mode:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkMaintenanceMode();
  }, []);
  
  // Function to enable/disable maintenance mode
  const setMaintenanceMode = async (enabled: boolean, message?: string) => {
    try {
      const value = {
        enabled,
        message: message || DEFAULT_MAINTENANCE_MESSAGE,
        timestamp: new Date().toISOString()
      };
      
      await AsyncStorage.setItem(MAINTENANCE_MODE_KEY, JSON.stringify(value));
      
      setIsInMaintenanceMode(enabled);
      if (message) {
        setMaintenanceMessage(message);
      }
    } catch (error) {
      console.error('Error setting maintenance mode:', error);
    }
  };
  
  // If still loading, return nothing
  if (isLoading) {
    return null;
  }
  
  // If in maintenance mode, show maintenance screen
  if (isInMaintenanceMode) {
    return <MaintenanceScreen message={maintenanceMessage} />;
  }
  
  // Otherwise, render children as normal
  return (
    <MaintenanceModeContext.Provider 
      value={{ 
        isInMaintenanceMode, 
        setMaintenanceMode, 
        maintenanceMessage 
      }}
    >
      {children}
    </MaintenanceModeContext.Provider>
  );
}

// Maintenance screen component
function MaintenanceScreen({ message }: { message: string }) {
  const windowWidth = Dimensions.get('window').width;
  const imageSize = Math.min(windowWidth * 0.7, 300);
  
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Image
          source={require('@/assets/images/maintenance.png')}
          style={[styles.image, { width: imageSize, height: imageSize }]}
          resizeMode="contain"
        />
        
        <Text style={styles.title}>Under Maintenance</Text>
        
        <Text style={styles.message}>{message}</Text>
        
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0066CC" />
          <Text style={styles.loadingText}>We'll be back soon</Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  image: {
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
    color: '#666',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 24,
  },
  loadingText: {
    marginLeft: 12,
    fontSize: 16,
    color: '#0066CC',
  },
}); 