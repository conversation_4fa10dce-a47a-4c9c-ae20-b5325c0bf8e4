import React, { useState, useEffect, JSX } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';

interface Ingredient {
  id: string;
  name: string;
  quantity: string;
  unit: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
}

interface RecipeNutrition {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  servings: number;
  caloriesPerServing: number;
  proteinPerServing: number;
  carbsPerServing: number;
  fatPerServing: number;
}

interface Recipe {
  id?: string;
  title: string;
  description: string;
  ingredients: Ingredient[];
  instructions: string[];
  prepTime: string;
  cookTime: string;
  servings: number;
  nutrition: RecipeNutrition;
  tags: string[];
}

interface ManualRecipeBuilderComponentProps {
  initialRecipe?: Recipe;
  onSave?: (recipe: Recipe) => void;
}

export function ManualRecipeBuilderComponent({
  initialRecipe,
  onSave
}: ManualRecipeBuilderComponentProps) {
  const { colors, isDark } = useTheme();
  
  const defaultRecipe: Recipe = {
    title: '',
    description: '',
    ingredients: [],
    instructions: [],
    prepTime: '',
    cookTime: '',
    servings: 1,
    nutrition: {
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0,
      servings: 1,
      caloriesPerServing: 0,
      proteinPerServing: 0,
      carbsPerServing: 0,
      fatPerServing: 0
    },
    tags: []
  };

  const [recipe, setRecipe] = useState<Recipe>(initialRecipe || defaultRecipe);
  const [newIngredient, setNewIngredient] = useState<Ingredient>({
    id: Date.now().toString(),
    name: '',
    quantity: '',
    unit: 'g',
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0
  });
  const [newInstruction, setNewInstruction] = useState('');
  const [newTag, setNewTag] = useState('');
  const [activeSection, setActiveSection] = useState<'ingredients' | 'instructions' | 'nutrition' | 'details' | 'all'>('all');
  const [editingIngredientId, setEditingIngredientId] = useState<string | null>(null);

  useEffect(() => {
    calculateNutrition();
  }, [recipe.ingredients, recipe.servings]);

  const calculateNutrition = () => {
    const totalNutrition = recipe.ingredients.reduce(
      (total, ingredient) => {
        return {
          calories: total.calories + ingredient.calories,
          protein: total.protein + ingredient.protein,
          carbs: total.carbs + ingredient.carbs,
          fat: total.fat + ingredient.fat
        };
      },
      { calories: 0, protein: 0, carbs: 0, fat: 0 }
    );

    const servings = recipe.servings > 0 ? recipe.servings : 1;

    setRecipe(prev => ({
      ...prev,
      nutrition: {
        ...totalNutrition,
        servings,
        caloriesPerServing: Math.round(totalNutrition.calories / servings),
        proteinPerServing: Math.round(totalNutrition.protein / servings * 10) / 10,
        carbsPerServing: Math.round(totalNutrition.carbs / servings * 10) / 10,
        fatPerServing: Math.round(totalNutrition.fat / servings * 10) / 10
      }
    }));
  };

  const handleAddIngredient = () => {
    if (!newIngredient.name.trim()) {
      Alert.alert('Missing Information', 'Please enter an ingredient name');
      return;
    }

    // Convert string inputs to numbers
    const ingredient = {
      ...newIngredient,
      calories: Number(newIngredient.calories) || 0,
      protein: Number(newIngredient.protein) || 0,
      carbs: Number(newIngredient.carbs) || 0,
      fat: Number(newIngredient.fat) || 0
    };

    setRecipe(prev => ({
      ...prev,
      ingredients: [...prev.ingredients, ingredient]
    }));

    // Reset form
    setNewIngredient({
      id: Date.now().toString(),
      name: '',
      quantity: '',
      unit: 'g',
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0
    });
  };

  const handleUpdateIngredient = () => {
    if (!editingIngredientId) return;
    if (!newIngredient.name.trim()) {
      Alert.alert('Missing Information', 'Please enter an ingredient name');
      return;
    }

    // Convert string inputs to numbers
    const updatedIngredient = {
      ...newIngredient,
      calories: Number(newIngredient.calories) || 0,
      protein: Number(newIngredient.protein) || 0,
      carbs: Number(newIngredient.carbs) || 0,
      fat: Number(newIngredient.fat) || 0
    };

    setRecipe(prev => ({
      ...prev,
      ingredients: prev.ingredients.map(ingredient => 
        ingredient.id === editingIngredientId ? updatedIngredient : ingredient
      )
    }));

    // Reset form
    setNewIngredient({
      id: Date.now().toString(),
      name: '',
      quantity: '',
      unit: 'g',
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0
    });
    setEditingIngredientId(null);
  };

  const handleEditIngredient = (id: string) => {
    const ingredient = recipe.ingredients.find(item => item.id === id);
    if (ingredient) {
      setNewIngredient(ingredient);
      setEditingIngredientId(id);
    }
  };

  const handleRemoveIngredient = (id: string) => {
    setRecipe(prev => ({
      ...prev,
      ingredients: prev.ingredients.filter(ingredient => ingredient.id !== id)
    }));

    if (editingIngredientId === id) {
      setEditingIngredientId(null);
      setNewIngredient({
        id: Date.now().toString(),
        name: '',
        quantity: '',
        unit: 'g',
        calories: 0,
        protein: 0,
        carbs: 0,
        fat: 0
      });
    }
  };

  const handleAddInstruction = () => {
    if (!newInstruction.trim()) {
      Alert.alert('Missing Information', 'Please enter an instruction');
      return;
    }

    setRecipe(prev => ({
      ...prev,
      instructions: [...prev.instructions, newInstruction.trim()]
    }));

    setNewInstruction('');
  };

  const handleRemoveInstruction = (index: number) => {
    setRecipe(prev => ({
      ...prev,
      instructions: prev.instructions.filter((_, i) => i !== index)
    }));
  };

  const handleAddTag = () => {
    if (!newTag.trim()) return;

    if (!recipe.tags.includes(newTag.trim().toLowerCase())) {
      setRecipe(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim().toLowerCase()]
      }));
    }

    setNewTag('');
  };

  const handleRemoveTag = (tag: string) => {
    setRecipe(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }));
  };

  const handleSaveRecipe = () => {
    if (!recipe.title.trim()) {
      Alert.alert('Missing Information', 'Please enter a recipe title');
      return;
    }

    if (recipe.ingredients.length === 0) {
      Alert.alert('Missing Information', 'Please add at least one ingredient');
      return;
    }

    if (recipe.instructions.length === 0) {
      Alert.alert('Missing Information', 'Please add at least one instruction');
      return;
    }

    if (onSave) {
      onSave(recipe);
    }
  };

  const toggleSection = (section: 'ingredients' | 'instructions' | 'nutrition' | 'details' | 'all') => {
    setActiveSection(prev => prev === section ? 'all' : section);
  };

  const renderSectionHeader = (
    title: string, 
    section: 'ingredients' | 'instructions' | 'nutrition' | 'details',
    icon: JSX.Element
  ) => (
    <TouchableOpacity 
      style={[
        styles.sectionHeader, 
        { borderBottomColor: colors.border }
      ]}
      onPress={() => toggleSection(section)}
    >
      <View style={styles.sectionHeaderContent}>
        {icon}
        <Text style={[styles.sectionHeaderText, { color: colors.text }]}>
          {title}
        </Text>
      </View>
      {activeSection === section || activeSection === 'all' ? (
        <Feather name="chevron-up" size={20} color={colors.text} />
      ) : (
        <Feather name="chevron-down" size={20} color={colors.text} />
      )}
    </TouchableOpacity>
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView style={styles.scrollContainer}>
        <View style={[styles.card, { backgroundColor: isDark ? colors.card : 'white' }]}>
          {/* Recipe Basic Details Section */}
          <View style={styles.titleContainer}>
            <TextInput
              style={[styles.titleInput, { color: colors.text, borderBottomColor: colors.border }]}
              placeholder="Recipe Title"
              placeholderTextColor={colors.textSecondary}
              value={recipe.title}
              onChangeText={(text) => setRecipe(prev => ({ ...prev, title: text }))}
            />
          </View>

          <TextInput
            style={[styles.descriptionInput, { color: colors.text, borderBottomColor: colors.border }]}
            placeholder="Brief description of your recipe"
            placeholderTextColor={colors.textSecondary}
            multiline
            value={recipe.description}
            onChangeText={(text) => setRecipe(prev => ({ ...prev, description: text }))}
          />

          <View style={styles.basicInfo}>
            <View style={styles.infoInput}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Prep Time</Text>
              <TextInput
                style={[styles.infoValue, { color: colors.text, borderBottomColor: colors.border }]}
                placeholder="e.g. 15 minutes"
                placeholderTextColor={colors.textSecondary}
                value={recipe.prepTime}
                onChangeText={(text) => setRecipe(prev => ({ ...prev, prepTime: text }))}
              />
            </View>

            <View style={styles.infoInput}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Cook Time</Text>
              <TextInput
                style={[styles.infoValue, { color: colors.text, borderBottomColor: colors.border }]}
                placeholder="e.g. 30 minutes"
                placeholderTextColor={colors.textSecondary}
                value={recipe.cookTime}
                onChangeText={(text) => setRecipe(prev => ({ ...prev, cookTime: text }))}
              />
            </View>

            <View style={styles.infoInput}>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Servings</Text>
              <TextInput
                style={[styles.infoValue, { color: colors.text, borderBottomColor: colors.border }]}
                placeholder="e.g. 4"
                placeholderTextColor={colors.textSecondary}
                value={recipe.servings.toString()}
                onChangeText={(text) => {
                  const servings = parseInt(text) || 1;
                  setRecipe(prev => ({ ...prev, servings }));
                }}
                keyboardType="number-pad"
              />
            </View>
          </View>

          {/* Tags Section */}
          <View style={styles.tagsContainer}>
            <Text style={[styles.tagsLabel, { color: colors.textSecondary }]}>Tags</Text>
            <View style={styles.tagsInputContainer}>
              <TextInput
                style={[styles.tagInput, { color: colors.text, borderColor: colors.border }]}
                placeholder="Add tag (e.g. vegan, quick, dessert)"
                placeholderTextColor={colors.textSecondary}
                value={newTag}
                onChangeText={setNewTag}
                onSubmitEditing={handleAddTag}
                returnKeyType="done"
              />
              <TouchableOpacity
                style={[styles.addTagButton, { backgroundColor: colors.primary }]}
                onPress={handleAddTag}
              >
                <Feather name="plus" size={16}  color={colors.text} />
              </TouchableOpacity>
            </View>

            <View style={styles.tagsList}>
              {recipe.tags.map(tag => (
                <View 
                  key={tag} 
                  style={[styles.tagChip, { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }]}
                >
                  <Text style={[styles.tagText, { color: colors.text }]}>
                    {tag}
                  </Text>
                  <TouchableOpacity
                    style={styles.removeTagButton}
                    onPress={() => handleRemoveTag(tag)}
                  >
                    <Feather name="x" size={14} color={colors.textSecondary} />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          </View>

          {/* Ingredients Section */}
          {renderSectionHeader(
            'Ingredients', 
            'ingredients',
            <Feather name="plus" size={20} color={colors.primary} style={styles.sectionHeaderIcon} />
          )}

          {(activeSection === 'ingredients' || activeSection === 'all') && (
            <View style={styles.ingredientsContainer}>
              {recipe.ingredients.length > 0 && (
                <View style={styles.ingredientsList}>
                  {recipe.ingredients.map(ingredient => (
                    <View 
                      key={ingredient.id} 
                      style={[
                        styles.ingredientItem, 
                        { backgroundColor: isDark ? colors.subtle : '#f5f5f5' }
                      ]}
                    >
                      <View style={styles.ingredientItemContent}>
                        <Text style={[styles.ingredientName, { color: colors.text }]}>
                          {ingredient.name}
                        </Text>
                        <Text style={[styles.ingredientQuantity, { color: colors.textSecondary }]}>
                          {ingredient.quantity} {ingredient.unit}
                        </Text>
                        <Text style={[styles.ingredientNutrition, { color: colors.textSecondary }]}>
                          {ingredient.calories} cal | P: {ingredient.protein}g | C: {ingredient.carbs}g | F: {ingredient.fat}g
                        </Text>
                      </View>
                      <View style={styles.ingredientActions}>
                        <TouchableOpacity
                          style={styles.ingredientActionButton}
                          onPress={() => handleEditIngredient(ingredient.id)}
                        >
                          <Feather name="edit-2" size={16} color={colors.primary} />
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={styles.ingredientActionButton}
                          onPress={() => handleRemoveIngredient(ingredient.id)}
                        >
                          <Feather name="trash-2" size={16} color={colors.danger} />
                        </TouchableOpacity>
                      </View>
                    </View>
                  ))}
                </View>
              )}

              <View style={styles.addIngredientForm}>
                <View style={styles.ingredientFormRow}>
                  <TextInput
                    style={[
                      styles.ingredientNameInput, 
                      { color: colors.text, borderColor: colors.border }
                    ]}
                    placeholder="Ingredient name"
                    placeholderTextColor={colors.textSecondary}
                    value={newIngredient.name}
                    onChangeText={(text) => setNewIngredient(prev => ({ ...prev, name: text }))}
                  />
                  <View style={styles.quantityUnitContainer}>
                    <TextInput
                      style={[
                        styles.quantityInput, 
                        { color: colors.text, borderColor: colors.border }
                      ]}
                      placeholder="Qty"
                      placeholderTextColor={colors.textSecondary}
                      value={newIngredient.quantity}
                      onChangeText={(text) => setNewIngredient(prev => ({ ...prev, quantity: text }))}
                      keyboardType="numeric"
                    />
                    <TextInput
                      style={[
                        styles.unitInput, 
                        { color: colors.text, borderColor: colors.border }
                      ]}
                      placeholder="Unit"
                      placeholderTextColor={colors.textSecondary}
                      value={newIngredient.unit}
                      onChangeText={(text) => setNewIngredient(prev => ({ ...prev, unit: text }))}
                    />
                  </View>
                </View>

                <View style={styles.nutritionInputsContainer}>
                  <Text style={[styles.nutritionInputsLabel, { color: colors.textSecondary }]}>
                    Nutrition Information
                  </Text>
                  <View style={styles.nutritionInputs}>
                    <View style={styles.nutritionInputGroup}>
                      <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                        Calories
                      </Text>
                      <TextInput
                        style={[
                          styles.nutritionInput, 
                          { color: colors.text, borderColor: colors.border }
                        ]}
                        placeholder="0"
                        placeholderTextColor={colors.textSecondary}
                        value={newIngredient.calories.toString()}
                        onChangeText={(text) => setNewIngredient(prev => ({ 
                          ...prev, 
                          calories: text === '' ? 0 : Number(text) 
                        }))}
                        keyboardType="numeric"
                      />
                    </View>

                    <View style={styles.nutritionInputGroup}>
                      <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                        Protein (g)
                      </Text>
                      <TextInput
                        style={[
                          styles.nutritionInput, 
                          { color: colors.text, borderColor: colors.border }
                        ]}
                        placeholder="0"
                        placeholderTextColor={colors.textSecondary}
                        value={newIngredient.protein.toString()}
                        onChangeText={(text) => setNewIngredient(prev => ({ 
                          ...prev, 
                          protein: text === '' ? 0 : Number(text) 
                        }))}
                        keyboardType="numeric"
                      />
                    </View>

                    <View style={styles.nutritionInputGroup}>
                      <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                        Carbs (g)
                      </Text>
                      <TextInput
                        style={[
                          styles.nutritionInput, 
                          { color: colors.text, borderColor: colors.border }
                        ]}
                        placeholder="0"
                        placeholderTextColor={colors.textSecondary}
                        value={newIngredient.carbs.toString()}
                        onChangeText={(text) => setNewIngredient(prev => ({ 
                          ...prev, 
                          carbs: text === '' ? 0 : Number(text) 
                        }))}
                        keyboardType="numeric"
                      />
                    </View>

                    <View style={styles.nutritionInputGroup}>
                      <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
                        Fat (g)
                      </Text>
                      <TextInput
                        style={[
                          styles.nutritionInput, 
                          { color: colors.text, borderColor: colors.border }
                        ]}
                        placeholder="0"
                        placeholderTextColor={colors.textSecondary}
                        value={newIngredient.fat.toString()}
                        onChangeText={(text) => setNewIngredient(prev => ({ 
                          ...prev, 
                          fat: text === '' ? 0 : Number(text) 
                        }))}
                        keyboardType="numeric"
                      />
                    </View>
                  </View>
                </View>

                <TouchableOpacity
                  style={[
                    styles.addIngredientButton,
                    { backgroundColor: colors.primary }
                  ]}
                  onPress={editingIngredientId ? handleUpdateIngredient : handleAddIngredient}
                >
                  {editingIngredientId ? (
                    <>
                      <Feather name="save" size={18} style={styles.addButtonIcon} />
                      <Text style={styles.addButtonText}>Update Ingredient</Text>
                    </>
                  ) : (
                    <>
                      <Feather name="plus" size={18} style={styles.addButtonIcon} />
                      <Text style={styles.addButtonText}>Add Ingredient</Text>
                    </>
                  )}
                </TouchableOpacity>

                {editingIngredientId && (
                  <TouchableOpacity
                    style={[
                      styles.cancelEditButton,
                      { backgroundColor: colors.subtle }
                    ]}
                    onPress={() => {
                      setEditingIngredientId(null);
                      setNewIngredient({
                        id: Date.now().toString(),
                        name: '',
                        quantity: '',
                        unit: 'g',
                        calories: 0,
                        protein: 0,
                        carbs: 0,
                        fat: 0
                      });
                    }}
                  >
                    <Feather name="x" size={18} color={colors.text} style={styles.addButtonIcon} />
                    <Text style={[styles.cancelButtonText, { color: colors.text }]}>
                      Cancel Edit
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          )}

          {/* Instructions Section */}
          {renderSectionHeader(
            'Instructions', 
            'instructions',
            <Feather name="edit-2" size={20} color={colors.primary} style={styles.sectionHeaderIcon} />
          )}

          {(activeSection === 'instructions' || activeSection === 'all') && (
            <View style={styles.instructionsContainer}>
              {recipe.instructions.length > 0 && (
                <View style={styles.instructionsList}>
                  {recipe.instructions.map((instruction, index) => (
                    <View 
                      key={index} 
                      style={[
                        styles.instructionItem, 
                        { backgroundColor: isDark ? colors.subtle : '#f5f5f5' }
                      ]}
                    >
                      <View style={styles.instructionNumber}>
                        <Text style={[styles.instructionNumberText, { color: colors.primary }]}>
                          {index + 1}
                        </Text>
                      </View>
                      <View style={styles.instructionContent}>
                        <Text style={[styles.instructionText, { color: colors.text }]}>
                          {instruction}
                        </Text>
                      </View>
                      <TouchableOpacity
                        style={styles.removeInstructionButton}
                        onPress={() => handleRemoveInstruction(index)}
                      >
                        <Feather name="x" size={16} color={colors.danger} />
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              )}

              <View style={styles.addInstructionForm}>
                <TextInput
                  style={[
                    styles.instructionInput, 
                    { color: colors.text, borderColor: colors.border }
                  ]}
                  placeholder="Add a step to your recipe..."
                  placeholderTextColor={colors.textSecondary}
                  multiline
                  value={newInstruction}
                  onChangeText={setNewInstruction}
                />
                <TouchableOpacity
                  style={[
                    styles.addInstructionButton,
                    { backgroundColor: colors.primary }
                  ]}
                  onPress={handleAddInstruction}
                >
                  <Feather name="plus" size={18} style={styles.addButtonIcon} />
                  <Text style={styles.addButtonText}>Add Step</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}

          {/* Nutrition Summary Section */}
          {renderSectionHeader(
            'Nutrition Summary', 
            'nutrition',
            <Feather name="calculator" size={20} color={colors.primary} style={styles.sectionHeaderIcon} />
          )}

          {(activeSection === 'nutrition' || activeSection === 'all') && (
            <View style={styles.nutritionSummaryContainer}>
              <View style={[styles.nutritionSummaryCard, { backgroundColor: isDark ? colors.subtle : '#f5f5f5' }]}>
                <View style={styles.nutritionHeaderRow}>
                  <Text style={[styles.nutritionSummaryTitle, { color: colors.text }]}>
                    Total Recipe Nutrition
                  </Text>
                  <InfoIcon size={16} color={colors.textSecondary} />
                </View>

                <View style={styles.nutritionRow}>
                  <View style={styles.nutritionSummaryItem}>
                    <Text style={[styles.nutritionSummaryValue, { color: colors.text }]}>
                      {recipe.nutrition.calories}
                    </Text>
                    <Text style={[styles.nutritionSummaryLabel, { color: colors.textSecondary }]}>
                      Calories
                    </Text>
                  </View>
                  
                  <View style={styles.nutritionSummaryItem}>
                    <Text style={[styles.nutritionSummaryValue, { color: colors.text }]}>
                      {recipe.nutrition.protein}g
                    </Text>
                    <Text style={[styles.nutritionSummaryLabel, { color: colors.textSecondary }]}>
                      Protein
                    </Text>
                  </View>
                  
                  <View style={styles.nutritionSummaryItem}>
                    <Text style={[styles.nutritionSummaryValue, { color: colors.text }]}>
                      {recipe.nutrition.carbs}g
                    </Text>
                    <Text style={[styles.nutritionSummaryLabel, { color: colors.textSecondary }]}>
                      Carbs
                    </Text>
                  </View>
                  
                  <View style={styles.nutritionSummaryItem}>
                    <Text style={[styles.nutritionSummaryValue, { color: colors.text }]}>
                      {recipe.nutrition.fat}g
                    </Text>
                    <Text style={[styles.nutritionSummaryLabel, { color: colors.textSecondary }]}>
                      Fat
                    </Text>
                  </View>
                </View>
              </View>

              <View style={[styles.nutritionSummaryCard, { backgroundColor: isDark ? colors.subtle : '#f5f5f5' }]}>
                <View style={styles.nutritionHeaderRow}>
                  <Text style={[styles.nutritionSummaryTitle, { color: colors.text }]}>
                    Per Serving ({recipe.servings} servings)
                  </Text>
                  <View style={styles.servingAdjuster}>
                    <TouchableOpacity
                      style={[styles.servingButton, { borderColor: colors.border }]}
                      onPress={() => {
                        if (recipe.servings > 1) {
                          setRecipe(prev => ({ ...prev, servings: prev.servings - 1 }));
                        }
                      }}
                    >
                      <Text style={[styles.servingButtonText, { color: colors.text }]}>-</Text>
                    </TouchableOpacity>
                    <Text style={[styles.servingCount, { color: colors.text }]}>
                      {recipe.servings}
                    </Text>
                    <TouchableOpacity
                      style={[styles.servingButton, { borderColor: colors.border }]}
                      onPress={() => setRecipe(prev => ({ ...prev, servings: prev.servings + 1 }))}
                    >
                      <Text style={[styles.servingButtonText, { color: colors.text }]}>+</Text>
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.nutritionRow}>
                  <View style={styles.nutritionSummaryItem}>
                    <Text style={[styles.nutritionSummaryValue, { color: colors.text }]}>
                      {recipe.nutrition.caloriesPerServing}
                    </Text>
                    <Text style={[styles.nutritionSummaryLabel, { color: colors.textSecondary }]}>
                      Calories
                    </Text>
                  </View>
                  
                  <View style={styles.nutritionSummaryItem}>
                    <Text style={[styles.nutritionSummaryValue, { color: colors.text }]}>
                      {recipe.nutrition.proteinPerServing}g
                    </Text>
                    <Text style={[styles.nutritionSummaryLabel, { color: colors.textSecondary }]}>
                      Protein
                    </Text>
                  </View>
                  
                  <View style={styles.nutritionSummaryItem}>
                    <Text style={[styles.nutritionSummaryValue, { color: colors.text }]}>
                      {recipe.nutrition.carbsPerServing}g
                    </Text>
                    <Text style={[styles.nutritionSummaryLabel, { color: colors.textSecondary }]}>
                      Carbs
                    </Text>
                  </View>
                  
                  <View style={styles.nutritionSummaryItem}>
                    <Text style={[styles.nutritionSummaryValue, { color: colors.text }]}>
                      {recipe.nutrition.fatPerServing}g
                    </Text>
                    <Text style={[styles.nutritionSummaryLabel, { color: colors.textSecondary }]}>
                      Fat
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          )}

          {/* Save Button */}
          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: colors.primary }]}
            onPress={handleSaveRecipe}
          >
            <Feather name="save" size={20} style={styles.saveButtonIcon} />
            <Text style={styles.saveButtonText}>Save Recipe</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    margin: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  titleContainer: {
    marginBottom: 12,
  },
  titleInput: {
    fontSize: 24,
    fontWeight: '700',
    paddingVertical: 8,
    borderBottomWidth: 1,
  },
  descriptionInput: {
    fontSize: 16,
    paddingVertical: 8,
    marginBottom: 16,
    borderBottomWidth: 1,
    textAlignVertical: 'top',
    minHeight: 60,
  },
  basicInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  infoInput: {
    flex: 1,
    marginHorizontal: 4,
  },
  infoLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 14,
    borderBottomWidth: 1,
    paddingVertical: 4,
  },
  tagsContainer: {
    marginBottom: 20,
  },
  tagsLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  tagsInputContainer: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  tagInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 8,
  },
  addTagButton: {
    width: 40,
    height: 40,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  tagsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tagChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 14,
  },
  removeTagButton: {
    marginLeft: 4,
    padding: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    marginBottom: 16,
  },
  sectionHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionHeaderIcon: {
    marginRight: 8,
  },
  sectionHeaderText: {
    fontSize: 18,
    fontWeight: '600',
  },
  ingredientsContainer: {
    marginBottom: 24,
  },
  ingredientsList: {
    marginBottom: 16,
  },
  ingredientItem: {
    flexDirection: 'row',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  ingredientItemContent: {
    flex: 1,
  },
  ingredientName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  ingredientQuantity: {
    fontSize: 14,
    marginBottom: 2,
  },
  ingredientNutrition: {
    fontSize: 12,
  },
  ingredientActions: {
    flexDirection: 'row',
  },
  ingredientActionButton: {
    padding: 6,
    marginLeft: 4,
  },
  addIngredientForm: {
    marginBottom: 8,
  },
  ingredientFormRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  ingredientNameInput: {
    flex: 2,
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 8,
  },
  quantityUnitContainer: {
    flex: 1,
    flexDirection: 'row',
  },
  quantityInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 8,
  },
  unitInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  nutritionInputsContainer: {
    marginBottom: 12,
  },
  nutritionInputsLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  nutritionInputs: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  nutritionInputGroup: {
    width: '48%',
    marginBottom: 12,
  },
  nutritionLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  nutritionInput: {
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  addIngredientButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 44,
    borderRadius: 8,
    marginBottom: 8,
  },
  cancelEditButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 44,
    borderRadius: 8,
  },
  addButtonIcon: {
    marginRight: 8,
  },
  addButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  cancelButtonText: {
    fontWeight: '600',
    fontSize: 16,
  },
  instructionsContainer: {
    marginBottom: 24,
  },
  instructionsList: {
    marginBottom: 16,
  },
  instructionItem: {
    flexDirection: 'row',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  instructionNumber: {
    width: 24,
    alignItems: 'center',
    marginRight: 8,
  },
  instructionNumberText: {
    fontSize: 16,
    fontWeight: '700',
  },
  instructionContent: {
    flex: 1,
  },
  instructionText: {
    fontSize: 15,
    lineHeight: 22,
  },
  removeInstructionButton: {
    padding: 4,
  },
  addInstructionForm: {
    marginBottom: 8,
  },
  instructionInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingTop: 12,
    paddingBottom: 12,
    marginBottom: 12,
    textAlignVertical: 'top',
    minHeight: 80,
  },
  addInstructionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 44,
    borderRadius: 8,
  },
  nutritionSummaryContainer: {
    marginBottom: 24,
  },
  nutritionSummaryCard: {
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  nutritionHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  nutritionSummaryTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  nutritionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nutritionSummaryItem: {
    alignItems: 'center',
    flex: 1,
  },
  nutritionSummaryValue: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  nutritionSummaryLabel: {
    fontSize: 12,
  },
  servingAdjuster: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  servingButton: {
    width: 26,
    height: 26,
    borderRadius: 13,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  servingButtonText: {
    fontSize: 16,
    fontWeight: '700',
  },
  servingCount: {
    fontSize: 16,
    fontWeight: '500',
    marginHorizontal: 8,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 54,
    borderRadius: 8,
    marginTop: 8,
  },
  saveButtonIcon: {
    marginRight: 8,
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 18,
  },
});

export default ManualRecipeBuilderComponent; 