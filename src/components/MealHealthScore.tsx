import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { generateMealHealthScore } from '@/services/mealHealthScoreService';
import { Meal, FoodItem } from '@/types/food';
import { Feather , FontAwesome } from '@expo/vector-icons';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming,
  interpolateColor
} from 'react-native-reanimated';

interface MealHealthScoreProps {
  meal: Meal | { 
    id: string; 
    name: string; 
    items: FoodItem[]; 
    totalCalories: number;
    totalProtein: number;
    totalCarbs: number;
    totalFat: number;
  };
}

export default function MealHealthScore({ meal }: MealHealthScoreProps) {
  const { colors, isDark } = useTheme();
  const [loading, setLoading] = useState(true);
  const [expanded, setExpanded] = useState(false);
  const [healthScore, setHealthScore] = useState<any>(null);
  
  // Animated values
  const scoreValue = useSharedValue(0);
  const expandHeight = useSharedValue(0);
  
  useEffect(() => {
    loadHealthScore();
  }, [meal.id]);
  
  const loadHealthScore = async () => {
    try {
      setLoading(true);
      const score = await generateMealHealthScore(meal);
      setHealthScore(score);
      
      // Animate the score value
      scoreValue.value = withTiming(score.score, { duration: 1000 });
    } catch (error) {
      console.error('Error loading health score:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const toggleExpanded = () => {
    setExpanded(!expanded);
    expandHeight.value = withTiming(expanded ? 0 : 1, { duration: 300 });
  };
  
  // Color interpolation for score
  const scoreColorStyle = useAnimatedStyle(() => {
    const backgroundColor = interpolateColor(
      scoreValue.value,
      [0, 30, 50, 70, 100],
      [
        colors.danger,
        '#FFA500', // Orange
        '#FFCC00', // Yellow
        '#90EE90', // Light green
        colors.success
      ]
    );
    
    return {
      backgroundColor
    };
  });
  
  // Expanded details animation
  const expandedStyle = useAnimatedStyle(() => {
    return {
      maxHeight: expandHeight.value === 0 ? 0 : 500,
      opacity: expandHeight.value
    };
  });
  
  // Determine macro level emoji
  const getMacroEmoji = (level: string) => {
    switch (level.toLowerCase()) {
      case 'high':
        return '⬆️';
      case 'low':
        return '⬇️';
      default:
        return '➡️';
    }
  };
  
  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? colors.card : '#f5f5f5' }]}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Analyzing meal health...
        </Text>
      </View>
    );
  }
  
  if (!healthScore) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? colors.card : '#f5f5f5' }]}>
        <Text style={{ color: colors.text }}>Unable to analyze this meal</Text>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: isDark ? colors.card : '#f5f5f5' }]}>
      <TouchableOpacity style={styles.header} onPress={toggleExpanded}>
        <View style={styles.scoreSection}>
          <View style={styles.scoreContainer}>
            <Animated.View 
              style={[
                styles.scoreCircle, 
                scoreColorStyle,
                { borderColor: isDark ? colors.background : 'white' }
              ]}
            >
              <Text style={styles.scoreText}>{healthScore.score}</Text>
            </Animated.View>
          </View>
          <View style={styles.explanationContainer}>
            <Text style={[styles.scoreTitle, { color: colors.text }]}>Health Score</Text>
            <Text style={[styles.explanation, { color: colors.textSecondary }]}>
              {healthScore.explanation}
            </Text>
          </View>
        </View>
        <View style={styles.expandIcon}>
          {expanded ? (
            <Feather name="chevron-up" size={20} color={colors.textSecondary} />
          ) : (
            <Feather name="chevron-down" size={20} color={colors.textSecondary} />
          )}
        </View>
      </TouchableOpacity>
      
      <Animated.View style={[styles.detailsContainer, expandedStyle]}>
        <View style={styles.macroContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Macronutrient Analysis</Text>
          <View style={styles.macroGrid}>
            <View style={styles.macroItem}>
              <Text style={[styles.macroEmoji]}>{getMacroEmoji(healthScore.macroAnalysis.protein)}</Text>
              <Text style={[styles.macroValue, { color: colors.text }]}>Protein</Text>
              <Text style={[styles.macroLabel, { color: colors.textSecondary }]}>
                {healthScore.macroAnalysis.protein}
              </Text>
            </View>
            
            <View style={styles.macroItem}>
              <Text style={[styles.macroEmoji]}>{getMacroEmoji(healthScore.macroAnalysis.carbs)}</Text>
              <Text style={[styles.macroValue, { color: colors.text }]}>Carbs</Text>
              <Text style={[styles.macroLabel, { color: colors.textSecondary }]}>
                {healthScore.macroAnalysis.carbs}
              </Text>
            </View>
            
            <View style={styles.macroItem}>
              <Text style={[styles.macroEmoji]}>{getMacroEmoji(healthScore.macroAnalysis.fat)}</Text>
              <Text style={[styles.macroValue, { color: colors.text }]}>Fat</Text>
              <Text style={[styles.macroLabel, { color: colors.textSecondary }]}>
                {healthScore.macroAnalysis.fat}
              </Text>
            </View>
            
            <View style={styles.macroItem}>
              <Text style={[styles.macroEmoji]}>{getMacroEmoji(healthScore.macroAnalysis.fiber)}</Text>
              <Text style={[styles.macroValue, { color: colors.text }]}>Fiber</Text>
              <Text style={[styles.macroLabel, { color: colors.textSecondary }]}>
                {healthScore.macroAnalysis.fiber}
              </Text>
            </View>
          </View>
        </View>
        
        <View style={styles.strengthsContainer}>
          <View style={styles.strengthsHeader}>
            <FontAwesome name="trophy" size={16} color={colors.success} />
            <Text style={[styles.strengthsTitle, { color: colors.text }]}>Strengths</Text>
          </View>
          {healthScore.strengths.map((strength: string, index: number) => (
            <View key={`strength-${index}`} style={styles.bulletItem}>
              <Text style={[styles.bullet, { color: colors.success }]}>•</Text>
              <Text style={[styles.bulletText, { color: colors.text }]}>{strength}</Text>
            </View>
          ))}
        </View>
        
        <View style={styles.improvementsContainer}>
          <View style={styles.improvementsHeader}>
            <Feather name="alert-circle" size={16} color={colors.warning} />
            <Text style={[styles.improvementsTitle, { color: colors.text }]}>Improvements</Text>
          </View>
          {healthScore.improvements.map((improvement: string, index: number) => (
            <View key={`improvement-${index}`} style={styles.bulletItem}>
              <Text style={[styles.bullet, { color: colors.warning }]}>•</Text>
              <Text style={[styles.bulletText, { color: colors.text }]}>{improvement}</Text>
            </View>
          ))}
        </View>
        
        <View style={styles.goalContainer}>
          <View style={styles.goalHeader}>
            <Feather name="info" size={16} color={colors.primary} />
            <Text style={[styles.goalTitle, { color: colors.text }]}>Goal Alignment</Text>
          </View>
          <Text style={[styles.goalText, { color: colors.textSecondary }]}>
            {healthScore.goalAlignment}
          </Text>
        </View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    margin: 16,
    padding: 16,
    overflow: 'hidden',
  },
  loadingText: {
    marginTop: 8,
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  scoreSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  scoreContainer: {
    marginRight: 12,
  },
  scoreCircle: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
  },
  scoreText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  explanationContainer: {
    flex: 1,
  },
  scoreTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  explanation: {
    fontSize: 14,
    lineHeight: 20,
  },
  expandIcon: {
    paddingLeft: 8,
  },
  detailsContainer: {
    marginTop: 16,
    overflow: 'hidden',
  },
  macroContainer: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 8,
  },
  macroGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  macroItem: {
    alignItems: 'center',
    width: '25%',
  },
  macroEmoji: {
    fontSize: 18,
    marginBottom: 4,
  },
  macroValue: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  macroLabel: {
    fontSize: 12,
    textTransform: 'capitalize',
  },
  strengthsContainer: {
    marginBottom: 16,
  },
  strengthsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  strengthsTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginLeft: 8,
  },
  bulletItem: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  bullet: {
    fontSize: 16,
    marginRight: 8,
    lineHeight: 22,
  },
  bulletText: {
    fontSize: 14,
    lineHeight: 22,
    flex: 1,
  },
  improvementsContainer: {
    marginBottom: 16,
  },
  improvementsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  improvementsTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginLeft: 8,
  },
  goalContainer: {
    marginBottom: 8,
  },
  goalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  goalTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginLeft: 8,
  },
  goalText: {
    fontSize: 14,
    lineHeight: 20,
  },
}); 