import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  ActivityIndicator,
  Image
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { FoodItem } from './SavedMealsComponent';

interface HealthScore {
  score: number; // 0-100
  rating: 'excellent' | 'good' | 'average' | 'needs improvement' | 'poor';
  positives: string[];
  negatives: string[];
  tips: string[];
}

interface MealData {
  id: string;
  name: string;
  date: string;
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  foods: FoodItem[];
  healthScore?: HealthScore;
}

interface MealHealthScoreComponentProps {
  meal?: MealData;
  onRefreshScore?: () => void;
}

export function MealHealthScoreComponent({ 
  meal,
  onRefreshScore
}: MealHealthScoreComponentProps) {
  const { colors, isDark } = useTheme();
  const [loading, setLoading] = useState(false);
  const [expandedSection, setExpandedSection] = useState<'positives' | 'negatives' | 'tips' | null>(null);

  useEffect(() => {
    if (meal && !meal.healthScore) {
      generateMockHealthScore();
    }
  }, [meal]);

  const generateMockHealthScore = () => {
    setLoading(true);
    
    // Simulate API call to generate health score
    setTimeout(() => {
      setLoading(false);
      // This would be replaced by actual AI analysis in a real app
    }, 1500);
  };

  const handleRefreshScore = () => {
    setLoading(true);
    
    // Simulate API call to refresh health score
    setTimeout(() => {
      setLoading(false);
      if (onRefreshScore) onRefreshScore();
    }, 2000);
  };

  const getMockHealthScore = (): HealthScore => {
    // This is mock data that would be generated by AI in a real app
    return {
      score: 78,
      rating: 'good',
      positives: [
        'Good protein content for muscle maintenance and recovery',
        'Contains fiber to support digestive health',
        'Includes healthy unsaturated fats',
        'Provides essential vitamins and minerals'
      ],
      negatives: [
        'Slightly high in sodium which may affect blood pressure',
        'Could include more variety of vegetables for broader nutrient profile',
        'Contains some processed ingredients with added sugars'
      ],
      tips: [
        'Try adding leafy greens for more vitamins K, A, and folate',
        'Consider swapping refined grains for whole grains',
        'Add more colorful vegetables for antioxidants',
        'Reduce added salt by using herbs and spices for flavor instead',
        'Include a source of omega-3 fatty acids like walnuts or flaxseeds'
      ]
    };
  };

  const getScoreColor = (score: number) => {
    if (score >= 85) return colors.success;
    if (score >= 70) return colors.primary;
    if (score >= 50) return colors.warning;
    return colors.danger;
  };

  const getRatingText = (rating: string) => {
    return rating.charAt(0).toUpperCase() + rating.slice(1);
  };

  const renderScoreCircle = (score: number) => {
    const scoreColor = getScoreColor(score);
    
    return (
      <View style={styles.scoreCircleContainer}>
        <View style={[styles.scoreCircle, { borderColor: scoreColor }]}>
          <Text style={[styles.scoreText, { color: scoreColor }]}>{score}</Text>
        </View>
      </View>
    );
  };

  if (!meal) {
    return (
      <View style={[styles.emptyContainer, { backgroundColor: isDark ? colors.card : 'white' }]}>
        <Text style={[styles.emptyText, { color: colors.text }]}>
          No meal data available to analyze.
        </Text>
      </View>
    );
  }

  const healthScore = meal.healthScore || getMockHealthScore();
  
  return (
    <View style={styles.container}>
      <View style={[styles.scoreCard, { backgroundColor: isDark ? colors.card : 'white' }]}>
        <View style={styles.scoreCardHeader}>
          <View style={styles.scoreCardInfo}>
            <View style={styles.titleContainer}>
              <Feather name="heart" size={18} color={colors.primary} style={styles.titleIcon} />
              <Text style={[styles.scoreCardTitle, { color: colors.text }]}>
                Meal Health Score
              </Text>
            </View>
            
            <Text style={[styles.scoreCardSubtitle, { color: colors.textSecondary }]}>
              AI-powered nutritional analysis
            </Text>
          </View>
          
          <TouchableOpacity
            style={[styles.refreshButton, loading && styles.refreshingButton]}
            onPress={handleRefreshScore}
            disabled={loading}
          >
            <Feather name="refresh-cw" size={16} color={colors.primary} style={loading ? styles.spinningIcon : undefined} />
          </TouchableOpacity>
        </View>
        
        <View style={styles.scoreContent}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} style={styles.loader} />
              <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                Analyzing nutritional profile...
              </Text>
            </View>
          ) : (
            <>
              <View style={styles.scoreRow}>
                {renderScoreCircle(healthScore.score)}
                
                <View style={styles.ratingContainer}>
                  <Text style={[styles.ratingLabel, { color: colors.textSecondary }]}>
                    Rating
                  </Text>
                  <Text style={[
                    styles.ratingText, 
                    { color: getScoreColor(healthScore.score) }
                  ]}>
                    {getRatingText(healthScore.rating)}
                  </Text>
                  <Text style={[styles.ratingDescription, { color: colors.text }]}>
                    This meal provides good nutrition but has some areas for improvement.
                  </Text>
                </View>
              </View>
              
              <View style={styles.insightsSections}>
                <TouchableOpacity
                  style={[
                    styles.insightSection,
                    expandedSection === 'positives' && { 
                      backgroundColor: isDark ? colors.subtle : 'rgba(0, 128, 0, 0.05)' 
                    }
                  ]}
                  onPress={() => setExpandedSection(
                    expandedSection === 'positives' ? null : 'positives'
                  )}
                >
                  <View style={styles.insightHeader}>
                    <View style={styles.insightTitleContainer}>
                      <Feather name="award" size={16} color={colors.success} style={styles.insightIcon} />
                      <Text style={[styles.insightTitle, { color: colors.text }]}>
                        Positives
                      </Text>
                    </View>
                    
                    <Feather name="arrow-right" size={16} color={colors.textSecondary} style={[
                        styles.arrowIcon,
                        expandedSection === 'positives' && styles.arrowExpanded
                      ]} />
                  </View>
                  
                  {expandedSection === 'positives' && (
                    <View style={styles.insightContent}>
                      {healthScore.positives.map((positive, index) => (
                        <View key={index} style={styles.insightItem}>
                          <View style={[styles.bulletPoint, { backgroundColor: colors.success }]} />
                          <Text style={[styles.insightText, { color: colors.text }]}>
                            {positive}
                          </Text>
                        </View>
                      ))}
                    </View>
                  )}
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[
                    styles.insightSection,
                    expandedSection === 'negatives' && { 
                      backgroundColor: isDark ? colors.subtle : 'rgba(220, 53, 69, 0.05)' 
                    }
                  ]}
                  onPress={() => setExpandedSection(
                    expandedSection === 'negatives' ? null : 'negatives'
                  )}
                >
                  <View style={styles.insightHeader}>
                    <View style={styles.insightTitleContainer}>
                      <MaterialIcons name="local-fire-department" size={16} color={colors.danger} style={styles.insightIcon} />
                      <Text style={[styles.insightTitle, { color: colors.text }]}>
                        Areas for Improvement
                      </Text>
                    </View>
                    
                    <Feather name="arrow-right" size={16} color={colors.textSecondary} style={[
                        styles.arrowIcon,
                        expandedSection === 'negatives' && styles.arrowExpanded
                      ]} />
                  </View>
                  
                  {expandedSection === 'negatives' && (
                    <View style={styles.insightContent}>
                      {healthScore.negatives.map((negative, index) => (
                        <View key={index} style={styles.insightItem}>
                          <View style={[styles.bulletPoint, { backgroundColor: colors.danger }]} />
                          <Text style={[styles.insightText, { color: colors.text }]}>
                            {negative}
                          </Text>
                        </View>
                      ))}
                    </View>
                  )}
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[
                    styles.insightSection,
                    expandedSection === 'tips' && { 
                      backgroundColor: isDark ? colors.subtle : 'rgba(13, 110, 253, 0.05)' 
                    }
                  ]}
                  onPress={() => setExpandedSection(
                    expandedSection === 'tips' ? null : 'tips'
                  )}
                >
                  <View style={styles.insightHeader}>
                    <View style={styles.insightTitleContainer}>
                      <Lightbulb size={16} color={colors.primary} style={styles.insightIcon} />
                      <Text style={[styles.insightTitle, { color: colors.text }]}>
                        Improvement Tips
                      </Text>
                    </View>
                    
                    <Feather name="arrow-right" size={16} color={colors.textSecondary} style={[
                        styles.arrowIcon,
                        expandedSection === 'tips' && styles.arrowExpanded
                      ]} />
                  </View>
                  
                  {expandedSection === 'tips' && (
                    <View style={styles.insightContent}>
                      {healthScore.tips.map((tip, index) => (
                        <View key={index} style={styles.insightItem}>
                          <View style={[styles.bulletPoint, { backgroundColor: colors.primary }]} />
                          <Text style={[styles.insightText, { color: colors.text }]}>
                            {tip}
                          </Text>
                        </View>
                      ))}
                    </View>
                  )}
                </TouchableOpacity>
              </View>
              
              <View style={styles.disclaimerContainer}>
                <Text style={[styles.disclaimerText, { color: colors.textSecondary }]}>
                  This score is generated by AI based on general nutritional guidelines and may not account for individual dietary needs or restrictions.
                </Text>
              </View>
            </>
          )}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  emptyContainer: {
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    height: 200,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  scoreCard: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  scoreCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  scoreCardInfo: {
    flex: 1,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  titleIcon: {
    marginRight: 8,
  },
  scoreCardTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  scoreCardSubtitle: {
    fontSize: 14,
  },
  refreshButton: {
    padding: 8,
    borderRadius: 20,
  },
  refreshingButton: {
    opacity: 0.7,
  },
  spinningIcon: {
    // In a real app, we would use Animated API for rotation
  },
  scoreContent: {
    padding: 16,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  loader: {
    marginBottom: 16,
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
  },
  scoreRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  scoreCircleContainer: {
    marginRight: 16,
  },
  scoreCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scoreText: {
    fontSize: 24,
    fontWeight: '700',
  },
  ratingContainer: {
    flex: 1,
  },
  ratingLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  ratingText: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },
  ratingDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  insightsSections: {
    marginBottom: 16,
  },
  insightSection: {
    borderRadius: 12,
    marginBottom: 12,
    padding: 16,
  },
  insightHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  insightTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  insightIcon: {
    marginRight: 8,
  },
  insightTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  arrowIcon: {
    // No transition property in React Native
  },
  arrowExpanded: {
    transform: [{ rotate: '90deg' }],
  },
  insightContent: {
    marginTop: 12,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  bulletPoint: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginTop: 7,
    marginRight: 8,
  },
  insightText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  disclaimerContainer: {
    marginTop: 8,
  },
  disclaimerText: {
    fontSize: 12,
    fontStyle: 'italic',
    textAlign: 'center',
  },
});

export default MealHealthScoreComponent; 