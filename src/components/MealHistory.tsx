import React, { useState } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, FlatList } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';

// Sample data structure for a meal record
interface MealRecord {
  id: string;
  name: string;
  date: string; // ISO format date string
  mealType: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  imageUri?: string;
}

interface MealHistoryProps {
  meals: MealRecord[];
  onSelectMeal?: (meal: MealRecord) => void;
}

export function MealHistory({ meals, onSelectMeal }: MealHistoryProps) {
  const { colors, isDark } = useTheme();
  const [view, setView] = useState<'calendar' | 'list' | 'stats'>('calendar');
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [currentMonth, setCurrentMonth] = useState<number>(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState<number>(new Date().getFullYear());

  // Helper function to generate dates for calendar view
  const generateCalendarDays = () => {
    const firstDay = new Date(currentYear, currentMonth, 1);
    const lastDay = new Date(currentYear, currentMonth + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();
    
    const days = [];
    
    // Add padding for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push({ day: 0, date: '', meals: [] });
    }
    
    // Add the days of the month
    for (let i = 1; i <= daysInMonth; i++) {
      const date = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
      const dayMeals = meals.filter(meal => meal.date.startsWith(date));
      days.push({
        day: i,
        date,
        meals: dayMeals,
      });
    }
    
    return days;
  };

  // Switch to previous month
  const prevMonth = () => {
    if (currentMonth === 0) {
      setCurrentMonth(11);
      setCurrentYear(currentYear - 1);
    } else {
      setCurrentMonth(currentMonth - 1);
    }
  };

  // Switch to next month
  const nextMonth = () => {
    if (currentMonth === 11) {
      setCurrentMonth(0);
      setCurrentYear(currentYear + 1);
    } else {
      setCurrentMonth(currentMonth + 1);
    }
  };

  // Get month name
  const getMonthName = (month: number) => {
    return new Date(0, month).toLocaleString('default', { month: 'long' });
  };

  // Calculate daily nutrition totals
  const calculateDailyTotals = (date: string) => {
    const dayMeals = meals.filter(meal => meal.date.startsWith(date));
    
    return {
      calories: dayMeals.reduce((sum, meal) => sum + meal.calories, 0),
      protein: dayMeals.reduce((sum, meal) => sum + meal.protein, 0),
      carbs: dayMeals.reduce((sum, meal) => sum + meal.carbs, 0),
      fat: dayMeals.reduce((sum, meal) => sum + meal.fat, 0),
      mealCount: dayMeals.length
    };
  };

  // Get the color for calories based on the value
  const getCalorieColor = (calories: number) => {
    if (calories < 1200) return '#3b82f6'; // blue - under
    if (calories > 2400) return '#ef4444'; // red - over
    return '#10b981'; // green - good range
  };

  // Render calendar view
  const renderCalendarView = () => {
    const calendarDays = generateCalendarDays();
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    
    return (
      <View style={styles.calendarContainer}>
        <View style={styles.monthSelector}>
          <TouchableOpacity onPress={prevMonth} style={styles.monthNavButton}>
            <Feather name="arrow-left" size={20} color={colors.textSecondary} />
          </TouchableOpacity>
          <Text style={[styles.monthYearText, { color: colors.text }]}>
            {getMonthName(currentMonth)} {currentYear}
          </Text>
          <TouchableOpacity onPress={nextMonth} style={styles.monthNavButton}>
            <Feather name="arrow-right" size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
        
        <View style={styles.dayLabelsRow}>
          {days.map(day => (
            <Text key={day} style={[styles.dayLabel, { color: colors.textSecondary }]}>
              {day}
            </Text>
          ))}
        </View>
        
        <View style={styles.calendarGrid}>
          {calendarDays.map((day, index) => {
            if (day.day === 0) {
              return <View key={`empty-${index}`} style={styles.emptyDay} />;
            }
            
            const isSelected = day.date === selectedDate;
            const totals = calculateDailyTotals(day.date);
            const hasMeals = totals.mealCount > 0;
            
            return (
              <TouchableOpacity 
                key={day.date} 
                style={[
                  styles.calendarDay,
                  isSelected && { borderColor: colors.primary, backgroundColor: colors.primaryLight },
                  { borderWidth: 1, borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }
                ]}
                onPress={() => setSelectedDate(day.date)}
              >
                <Text style={[
                  styles.dayNumber, 
                  { color: isSelected ? colors.primary : colors.text }
                ]}>
                  {day.day}
                </Text>
                
                {hasMeals && (
                  <View style={[
                    styles.calorieIndicator, 
                    { backgroundColor: getCalorieColor(totals.calories) }
                  ]}>
                    <Text style={styles.calorieText}>
                      {totals.calories}
                    </Text>
                  </View>
                )}
                
                {totals.mealCount > 1 && (
                  <View style={styles.mealCountBadge}>
                    <Text style={styles.mealCountText}>
                      {totals.mealCount}
                    </Text>
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </View>
        
        {/* Show selected day's meals */}
        <View style={styles.selectedDayMeals}>
          <Text style={[styles.selectedDayText, { color: colors.text }]}>
            {new Date(selectedDate).toLocaleDateString(undefined, { 
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </Text>
          
          <View style={styles.dailyNutrition}>
            {renderDailyNutrition(selectedDate)}
          </View>
          
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Meals
          </Text>
          
          {renderDayMealsList(selectedDate)}
        </View>
      </View>
    );
  };

  // Render list view of all meals
  const renderListView = () => {
    // Sort meals by date (newest first)
    const sortedMeals = [...meals].sort((a, b) => 
      new Date(b.date).getTime() - new Date(a.date).getTime()
    );
    
    return (
      <View style={styles.listContainer}>
        <FlatList
          data={sortedMeals}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity 
              style={[
                styles.mealListItem,
                { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)' }
              ]}
              onPress={() => onSelectMeal && onSelectMeal(item)}
            >
              <View style={styles.mealInfo}>
                <Text style={[styles.mealName, { color: colors.text }]}>{item.name}</Text>
                <Text style={[styles.mealDate, { color: colors.textSecondary }]}>
                  {new Date(item.date).toLocaleDateString()} • {item.mealType}
                </Text>
              </View>
              <View style={styles.mealMacros}>
                <Text style={[styles.mealCalories, { color: colors.text }]}>
                  {item.calories} cal
                </Text>
                <Text style={[styles.mealMacroText, { color: colors.textSecondary }]}>
                  P: {item.protein}g • C: {item.carbs}g • F: {item.fat}g
                </Text>
              </View>
            </TouchableOpacity>
          )}
          contentContainerStyle={styles.mealsList}
        />
      </View>
    );
  };

  // Render statistics view
  const renderStatsView = () => {
    // Calculate weekly averages
    const now = new Date();
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    
    const lastWeekMeals = meals.filter(meal => {
      const mealDate = new Date(meal.date);
      return mealDate >= oneWeekAgo && mealDate <= now;
    });
    
    const weeklyAvg = {
      calories: Math.round(lastWeekMeals.reduce((sum, meal) => sum + meal.calories, 0) / 7),
      protein: Math.round(lastWeekMeals.reduce((sum, meal) => sum + meal.protein, 0) / 7),
      carbs: Math.round(lastWeekMeals.reduce((sum, meal) => sum + meal.carbs, 0) / 7),
      fat: Math.round(lastWeekMeals.reduce((sum, meal) => sum + meal.fat, 0) / 7),
    };
    
    // Group meals by type
    const mealsByType: Record<string, number> = {};
    meals.forEach(meal => {
      if (!mealsByType[meal.mealType]) {
        mealsByType[meal.mealType] = 0;
      }
      mealsByType[meal.mealType]++;
    });
    
    return (
      <ScrollView style={styles.statsContainer}>
        <View style={[styles.statCard, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)' }]}>
          <Text style={[styles.statCardTitle, { color: colors.text }]}>
            7-Day Averages
          </Text>
          
          <View style={styles.averagesGrid}>
            <View style={styles.avgItem}>
              <Text style={[styles.avgValue, { color: colors.primary }]}>{weeklyAvg.calories}</Text>
              <Text style={[styles.avgLabel, { color: colors.textSecondary }]}>Calories</Text>
            </View>
            <View style={styles.avgItem}>
              <Text style={[styles.avgValue, { color: '#3b82f6' }]}>{weeklyAvg.protein}g</Text>
              <Text style={[styles.avgLabel, { color: colors.textSecondary }]}>Protein</Text>
            </View>
            <View style={styles.avgItem}>
              <Text style={[styles.avgValue, { color: '#8b5cf6' }]}>{weeklyAvg.carbs}g</Text>
              <Text style={[styles.avgLabel, { color: colors.textSecondary }]}>Carbs</Text>
            </View>
            <View style={styles.avgItem}>
              <Text style={[styles.avgValue, { color: '#f97316' }]}>{weeklyAvg.fat}g</Text>
              <Text style={[styles.avgLabel, { color: colors.textSecondary }]}>Fat</Text>
            </View>
          </View>
        </View>
        
        <View style={[styles.statCard, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)' }]}>
          <Text style={[styles.statCardTitle, { color: colors.text }]}>
            Meal Type Distribution
          </Text>
          
          <View style={styles.mealTypesContainer}>
            {Object.entries(mealsByType).map(([type, count]) => (
              <View key={type} style={styles.mealTypeRow}>
                <Text style={[styles.mealTypeText, { color: colors.text }]}>{type}</Text>
                <View style={styles.mealTypeBarContainer}>
                  <View 
                    style={[
                      styles.mealTypeBar, 
                      { 
                        backgroundColor: type === 'Breakfast' ? '#3b82f6' : 
                                         type === 'Lunch' ? '#8b5cf6' : 
                                         type === 'Dinner' ? '#f97316' : '#10b981',
                        width: `${(count / meals.length) * 100}%`
                      }
                    ]} 
                  />
                </View>
                <Text style={[styles.mealTypeCount, { color: colors.textSecondary }]}>
                  {count}
                </Text>
              </View>
            ))}
          </View>
        </View>
        
        <View style={[styles.statCard, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)' }]}>
          <Text style={[styles.statCardTitle, { color: colors.text }]}>
            Recent Trends
          </Text>
          
          <Text style={[styles.trendText, { color: colors.textSecondary }]}>
            • Your protein intake has increased by 15% in the last month
          </Text>
          <Text style={[styles.trendText, { color: colors.textSecondary }]}>
            • You've been consistent with logging meals 5 days per week
          </Text>
          <Text style={[styles.trendText, { color: colors.textSecondary }]}>
            • Your average calorie intake is within 10% of your goal
          </Text>
        </View>
      </ScrollView>
    );
  };

  // Render daily nutrition summary
  const renderDailyNutrition = (date: string) => {
    const totals = calculateDailyTotals(date);
    
    if (totals.mealCount === 0) {
      return (
        <Text style={[styles.noMealsText, { color: colors.textSecondary }]}>
          No meals recorded for this day
        </Text>
      );
    }
    
    return (
      <View style={styles.nutrientRow}>
        <View style={styles.nutrientItem}>
          <Text style={[styles.nutrientValue, { color: colors.text }]}>{totals.calories}</Text>
          <Text style={[styles.nutrientLabel, { color: colors.textSecondary }]}>Calories</Text>
        </View>
        <View style={styles.nutrientItem}>
          <Text style={[styles.nutrientValue, { color: colors.text }]}>{totals.protein}g</Text>
          <Text style={[styles.nutrientLabel, { color: colors.textSecondary }]}>Protein</Text>
        </View>
        <View style={styles.nutrientItem}>
          <Text style={[styles.nutrientValue, { color: colors.text }]}>{totals.carbs}g</Text>
          <Text style={[styles.nutrientLabel, { color: colors.textSecondary }]}>Carbs</Text>
        </View>
        <View style={styles.nutrientItem}>
          <Text style={[styles.nutrientValue, { color: colors.text }]}>{totals.fat}g</Text>
          <Text style={[styles.nutrientLabel, { color: colors.textSecondary }]}>Fat</Text>
        </View>
      </View>
    );
  };

  // Render list of meals for a specific day
  const renderDayMealsList = (date: string) => {
    const dayMeals = meals.filter(meal => meal.date.startsWith(date));
    
    if (dayMeals.length === 0) {
      return (
        <Text style={[styles.noMealsText, { color: colors.textSecondary }]}>
          No meals recorded
        </Text>
      );
    }
    
    return (
      <View style={styles.dayMealsList}>
        {dayMeals.map(meal => (
          <TouchableOpacity 
            key={meal.id} 
            style={[
              styles.dayMealItem,
              { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)' }
            ]}
            onPress={() => onSelectMeal && onSelectMeal(meal)}
          >
            <View style={styles.mealInfo}>
              <Text style={[styles.mealName, { color: colors.text }]}>{meal.name}</Text>
              <Text style={[styles.mealType, { color: colors.textSecondary }]}>{meal.mealType}</Text>
            </View>
            <View style={styles.mealMacros}>
              <Text style={[styles.dayMealCalories, { color: colors.text }]}>
                {meal.calories} cal
              </Text>
              <Text style={[styles.mealMacroText, { color: colors.textSecondary }]}>
                P: {meal.protein}g • C: {meal.carbs}g • F: {meal.fat}g
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.viewToggle}>
        <TouchableOpacity 
          style={[
            styles.viewButton, 
            view === 'calendar' && [styles.activeViewButton, { backgroundColor: colors.primaryLight }]
          ]}
          onPress={() => setView('calendar')}
        >
          <CalendarDays 
            size={18} 
            color={view === 'calendar' ? colors.primary : colors.textSecondary} 
          />
          <Text style={[
            styles.viewButtonText,
            { color: view === 'calendar' ? colors.primary : colors.textSecondary }
          ]}>
            Calendar
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[
            styles.viewButton, 
            view === 'list' && [styles.activeViewButton, { backgroundColor: colors.primaryLight }]
          ]}
          onPress={() => setView('list')}
        >
          <Feather name="arrow-left" size={18} color={view === 'list' ? colors.primary : colors.textSecondary} />
          <Text style={[
            styles.viewButtonText,
            { color: view === 'list' ? colors.primary : colors.textSecondary }
          ]}>
            List
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[
            styles.viewButton, 
            view === 'stats' && [styles.activeViewButton, { backgroundColor: colors.primaryLight }]
          ]}
          onPress={() => setView('stats')}
        >
          <BarChart3 
            size={18} 
            color={view === 'stats' ? colors.primary : colors.textSecondary} 
          />
          <Text style={[
            styles.viewButtonText,
            { color: view === 'stats' ? colors.primary : colors.textSecondary }
          ]}>
            Stats
          </Text>
        </TouchableOpacity>
      </View>
      
      {view === 'calendar' && renderCalendarView()}
      {view === 'list' && renderListView()}
      {view === 'stats' && renderStatsView()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  viewToggle: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
  },
  activeViewButton: {
    borderRadius: 20,
  },
  viewButtonText: {
    marginLeft: 6,
    fontWeight: '600',
  },
  
  // Calendar View
  calendarContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  monthSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  monthNavButton: {
    padding: 8,
  },
  monthYearText: {
    fontSize: 18,
    fontWeight: '700',
  },
  dayLabelsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 8,
  },
  dayLabel: {
    width: 40,
    textAlign: 'center',
    fontSize: 12,
    fontWeight: '600',
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  calendarDay: {
    width: '14.28%', // 7 days per row
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    marginBottom: 4,
  },
  emptyDay: {
    width: '14.28%',
    aspectRatio: 1,
  },
  dayNumber: {
    fontSize: 14,
    fontWeight: '600',
  },
  calorieIndicator: {
    marginTop: 4,
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
  },
  calorieText: {
    color: 'white',
    fontSize: 9,
    fontWeight: '700',
  },
  mealCountBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(0,0,0,0.3)',
    width: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mealCountText: {
    color: 'white',
    fontSize: 9,
    fontWeight: '700',
  },
  selectedDayMeals: {
    marginTop: 16,
  },
  selectedDayText: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  dailyNutrition: {
    marginBottom: 16,
  },
  noMealsText: {
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
    marginVertical: 16,
  },
  nutrientRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nutrientItem: {
    alignItems: 'center',
  },
  nutrientValue: {
    fontSize: 18,
    fontWeight: '700',
  },
  nutrientLabel: {
    fontSize: 12,
  },
  dayMealsList: {
    gap: 8,
  },
  dayMealItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
    borderRadius: 8,
  },
  dayMealCalories: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'right',
  },
  
  // List View
  listContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  mealsList: {
    paddingBottom: 16,
  },
  mealListItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  mealInfo: {
    flex: 1,
  },
  mealName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  mealDate: {
    fontSize: 12,
  },
  mealType: {
    fontSize: 12,
  },
  mealMacros: {
    alignItems: 'flex-end',
  },
  mealCalories: {
    fontSize: 16,
    fontWeight: '600',
  },
  mealMacroText: {
    fontSize: 12,
    marginTop: 4,
  },
  
  // Stats View
  statsContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  statCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  statCardTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  averagesGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  avgItem: {
    alignItems: 'center',
    flex: 1,
  },
  avgValue: {
    fontSize: 20,
    fontWeight: '700',
  },
  avgLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  mealTypesContainer: {
    gap: 12,
  },
  mealTypeRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  mealTypeText: {
    width: 80,
    fontSize: 14,
  },
  mealTypeBarContainer: {
    flex: 1,
    height: 16,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
    overflow: 'hidden',
    marginHorizontal: 12,
  },
  mealTypeBar: {
    height: '100%',
    borderRadius: 8,
  },
  mealTypeCount: {
    width: 30,
    textAlign: 'right',
    fontSize: 14,
  },
  trendText: {
    fontSize: 14,
    lineHeight: 24,
  },
}); 