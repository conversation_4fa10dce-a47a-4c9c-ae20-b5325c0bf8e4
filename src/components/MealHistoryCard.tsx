import React, { useState, useRef } from 'react';
import { StyleSheet, Text, View, Image, TouchableOpacity, Platform, Animated } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { getMealColor, formatTimeForDisplay } from '@/utils/mealUtils';
import { LinearGradient } from 'expo-linear-gradient';

interface MealItemProps {
  id: string;
  name: string;
  calories: number;
  protein?: number;
  carbs?: number;
  fat?: number;
}

interface MealProps {
  id: string;
  name: string;
  time: string;
  items: MealItemProps[];
  totalCalories: number;
  image: string;
}

interface MealHistoryCardProps {
  meal: MealProps;
  onPress?: () => void;
}

export function MealHistoryCard({ meal, onPress }: MealHistoryCardProps) {
  const { colors, isDark } = useTheme();
  const [expanded, setExpanded] = useState(false);
  const heightAnim = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  
  const toggleExpanded = () => {
    setExpanded(!expanded);
    
    Animated.parallel([
      Animated.timing(heightAnim, {
        toValue: expanded ? 0 : 1,
        duration: 300,
        useNativeDriver: false,
      }),
      Animated.timing(rotateAnim, {
        toValue: expanded ? 0 : 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: expanded ? 0 : 1,
        duration: 400,
        useNativeDriver: true,
      }),
    ]).start();
  };
  
  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.98,
      speed: 20,
      useNativeDriver: true,
    }).start();
  };
  
  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      speed: 16,
      useNativeDriver: true,
    }).start();
  };
  
  const rotateStyle = {
    transform: [
      {
        rotate: rotateAnim.interpolate({
          inputRange: [0, 1],
          outputRange: ['0deg', '90deg'],
        }),
      },
    ],
  };
  
  const maxHeight = heightAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 500], // Adjust max height as needed
  });
  
  // Calculate macros for percentage display
  const calculateMacroPercentage = (items: MealItemProps[]) => {
    if (!items || items.length === 0) {
      return [];
    }
    
    const total = items.reduce((sum, item) => sum + item.calories, 0);
    const percentages = items.map(item => ({
      ...item,
      percentage: Math.round((item.calories / total) * 100),
    }));
    
    return percentages.sort((a, b) => b.percentage - a.percentage);
  };
  
  // Calculate total macros if available
  const calculateTotalMacros = (items: MealItemProps[]) => {
    if (!items || items.length === 0) {
      return { protein: 0, carbs: 0, fat: 0 };
    }
    
    return {
      protein: items.reduce((sum, item) => sum + (item.protein || 0), 0),
      carbs: items.reduce((sum, item) => sum + (item.carbs || 0), 0),
      fat: items.reduce((sum, item) => sum + (item.fat || 0), 0),
    };
  };
  
  const sortedItems = calculateMacroPercentage(meal.items || []);
  const totalMacros = calculateTotalMacros(meal.items || []);
  
  // Format the time for display
  const displayTime = meal.time.includes('AM') || meal.time.includes('PM') 
    ? meal.time 
    : formatTimeForDisplay(meal.time);
  
  // Get the appropriate badge color based on meal name
  const mealType = meal.name.toLowerCase();
  const [primaryColor, secondaryColor] = mealType.includes('breakfast') 
    ? ['#F59E0B', '#D97706'] // Amber gradient
    : mealType.includes('lunch')
    ? ['#10B981', '#059669'] // Green gradient
    : mealType.includes('dinner')
    ? ['#3B82F6', '#2563EB'] // Blue gradient
    : ['#8B5CF6', '#7C3AED']; // Purple gradient for snacks or others
  
  // Get tag background color for items
  const getTagBackground = (index: number): [string, string] => {
    const colorPairs: [string, string][] = [
      ['rgba(59, 130, 246, 0.15)', 'rgba(37, 99, 235, 0.25)'],   // Blue
      ['rgba(139, 92, 246, 0.15)', 'rgba(124, 58, 237, 0.25)'],  // Purple
      ['rgba(249, 115, 22, 0.15)', 'rgba(234, 88, 12, 0.25)'],   // Orange
      ['rgba(16, 185, 129, 0.15)', 'rgba(5, 150, 105, 0.25)'],   // Green
      ['rgba(236, 72, 153, 0.15)', 'rgba(219, 39, 119, 0.25)'],  // Pink
    ];
    
    return colorPairs[index % colorPairs.length];
  };
  
  return (
    <Animated.View style={{
      transform: [{ scale: scaleAnim }],
      shadowOpacity: expanded ? 0.25 : 0.1,
      shadowRadius: expanded ? 12 : 6,
    }}>
      <LinearGradient
        colors={isDark ? 
          [colors.card, 'rgba(30, 41, 59, 0.9)'] : 
          [colors.card, '#F8FAFC']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={[
          styles.card, 
          { 
            borderColor: expanded ? primaryColor : colors.border,
          }
        ]}
      >
        <TouchableOpacity 
          style={styles.header}
          onPress={toggleExpanded}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={0.7}
        >
          <View style={styles.imageWrapper}>
            <View style={styles.imageContainer}>
              <Image 
                source={{ uri: meal.image }} 
                style={styles.image} 
                resizeMode="cover"
              />
              {/* Meal type badge with gradient */}
              <LinearGradient
                colors={[primaryColor, secondaryColor]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.mealBadge}
              >
                <Text style={styles.mealBadgeText}>{meal.name}</Text>
              </LinearGradient>
            </View>
          </View>
          
          <View style={styles.headerContent}>
            <View style={styles.headerTextContainer}>
              <Text style={[styles.name, { color: colors.text }]}>{meal.name}</Text>
              <View style={styles.timeContainer}>
                <Feather name="clock" size={14} color={colors.textSecondary} style={styles.timeIcon} />
                <Text style={[styles.time, { color: colors.textSecondary }]}>{displayTime}</Text>
              </View>
              
              {/* Item previews with better styling */}
              <View style={styles.itemPreviews}>
                {sortedItems.slice(0, 2).map((item, index) => (
                  <LinearGradient
                    key={index}
                    colors={getTagBackground(index)}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.itemPreview}
                  >
                    <Text 
                      style={[styles.itemPreviewText, { 
                        color: isDark ? colors.text : colors.textSecondary
                      }]}
                      numberOfLines={1}
                    >
                      {item.name}
                    </Text>
                  </LinearGradient>
                ))}
                {meal.items && meal.items.length > 2 && (
                  <LinearGradient
                    colors={['rgba(100, 116, 139, 0.15)', 'rgba(71, 85, 105, 0.25)']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.itemPreview}
                  >
                    <Text 
                      style={[styles.itemPreviewText, { 
                        color: isDark ? colors.text : colors.textSecondary
                      }]}
                    >
                      +{meal.items.length - 2}
                    </Text>
                  </LinearGradient>
                )}
              </View>
            </View>
            
            <View style={styles.calorieContainer}>
              <LinearGradient
                colors={[primaryColor, secondaryColor]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.calorieGradient}
              >
                <Text style={styles.calories}>{meal.totalCalories}</Text>
                <Text style={styles.calorieLabel}>cal</Text>
              </LinearGradient>
            </View>
          </View>
          
          <Animated.View style={[
            styles.expandIcon, 
            { 
              backgroundColor: expanded ? `${primaryColor}20` : colors.subtle,
              borderColor: expanded ? `${primaryColor}40` : 'transparent',
            },
            rotateStyle
          ]}>
            <Feather name="chevron-right" size={18} color={expanded ? primaryColor : colors.textSecondary} />
          </Animated.View>
        </TouchableOpacity>
        
        {/* Expanded Details Section */}
        <Animated.View 
          style={[
            styles.details, 
            { 
              borderTopColor: colors.border,
              maxHeight,
              opacity: opacityAnim
            }
          ]}
        >
          {/* Macros visual chart */}
          {meal.items && meal.items.length > 0 && (
            <View style={styles.macrosContainer}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.macroSectionTitle, { color: colors.textSecondary }]}>
                  Calories Breakdown
                </Text>
                <Text style={[styles.macroTotalText, { color: colors.textSecondary }]}>
                  {meal.totalCalories} cal total
                </Text>
              </View>
              
              {/* Calories bar chart */}
              <View style={[styles.macrosChart, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)' }]}>
                {sortedItems.map((item, index) => (
                  <LinearGradient
                    key={index}
                    colors={[getSegmentColor(index, colors), getSegmentColor(index, colors)]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={[
                      styles.macrosChartSegment,
                      { 
                        width: `${item.percentage}%`,
                      }
                    ]}
                  />
                ))}
              </View>
              
              {/* Legend with better visualization */}
              <View style={styles.macrosLegend}>
                {sortedItems.slice(0, 4).map((item, index) => (
                  <View key={index} style={styles.macrosLegendItem}>
                    <View 
                      style={[
                        styles.macrosLegendColor, 
                        { backgroundColor: getSegmentColor(index, colors) }
                      ]} 
                    />
                    <Text 
                      style={[styles.macrosLegendText, { color: colors.textSecondary }]}
                      numberOfLines={1}
                    >
                      {item.name}
                    </Text>
                    <Text style={[styles.macroPercentageText, { color: getSegmentColor(index, colors) }]}>
                      {item.percentage}%
                    </Text>
                  </View>
                ))}
              </View>
              
              {/* Nutrition facts if available */}
              {(totalMacros.protein > 0 || totalMacros.carbs > 0 || totalMacros.fat > 0) && (
                <View style={styles.nutritionFacts}>
                  <View style={styles.macroNutrientRow}>
                    <View style={styles.macroNutrient}>
                      <LinearGradient
                        colors={['#3B82F6', '#2563EB']}
                        style={styles.macroNutrientIcon}
                      >
                        <Text style={styles.macroNutrientShort}>P</Text>
                      </LinearGradient>
                      <View>
                        <Text style={[styles.macroNutrientValue, { color: colors.text }]}>
                          {totalMacros.protein}g
                        </Text>
                        <Text style={[styles.macroNutrientLabel, { color: colors.textSecondary }]}>
                          Protein
                        </Text>
                      </View>
                    </View>
                    
                    <View style={styles.macroNutrient}>
                      <LinearGradient
                        colors={['#8B5CF6', '#7C3AED']}
                        style={styles.macroNutrientIcon}
                      >
                        <Text style={styles.macroNutrientShort}>C</Text>
                      </LinearGradient>
                      <View>
                        <Text style={[styles.macroNutrientValue, { color: colors.text }]}>
                          {totalMacros.carbs}g
                        </Text>
                        <Text style={[styles.macroNutrientLabel, { color: colors.textSecondary }]}>
                          Carbs
                        </Text>
                      </View>
                    </View>
                    
                    <View style={styles.macroNutrient}>
                      <LinearGradient
                        colors={['#F97316', '#EA580C']}
                        style={styles.macroNutrientIcon}
                      >
                        <Text style={styles.macroNutrientShort}>F</Text>
                      </LinearGradient>
                      <View>
                        <Text style={[styles.macroNutrientValue, { color: colors.text }]}>
                          {totalMacros.fat}g
                        </Text>
                        <Text style={[styles.macroNutrientLabel, { color: colors.textSecondary }]}>
                          Fat
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>
              )}
            </View>
          )}
          
          {/* Detailed items list */}
          <View style={styles.itemSection}>
            <Text style={[styles.itemsSectionTitle, { color: colors.textSecondary }]}>
              Meal Items
            </Text>
            
            {meal.items && meal.items.length > 0 ? (
              <View style={styles.itemList}>
                {meal.items.map((item, index) => (
                  <View key={item.id} style={[
                    styles.itemRow,
                    index < meal.items.length - 1 && { 
                      borderBottomWidth: 1,
                      borderBottomColor: isDark ? 'rgba(255,255,255,0.06)' : 'rgba(0,0,0,0.04)'
                    }
                  ]}>
                    <View style={styles.itemNameContainer}>
                      <LinearGradient
                        colors={getTagBackground(index)}
                        style={styles.itemIconContainer}
                      >
                        <CheckCircle2 size={14} color={isDark ? '#fff' : '#475569'} />
                      </LinearGradient>
                      <Text style={[styles.itemName, { color: colors.text }]}>{item.name}</Text>
                    </View>
                    <View style={styles.itemCaloriesContainer}>
                      <Text style={[styles.itemCalories, { color: colors.text }]}>{item.calories}</Text>
                      <Text style={[styles.itemCaloriesUnit, { color: colors.textSecondary }]}>cal</Text>
                    </View>
                  </View>
                ))}
              </View>
            ) : (
              <View style={styles.emptyItemsMessage}>
                <Text style={[styles.emptyItemsText, { color: colors.textSecondary }]}>
                  No detailed items available
                </Text>
              </View>
            )}
          </View>
          
          {/* Action buttons */}
          <View style={[styles.actionRow, { borderTopColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.05)' }]}>
            {/* Edit button */}
            <TouchableOpacity 
              style={[
                styles.actionButton,
                { 
                  backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)',
                  borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.07)',
                }
              ]}
              activeOpacity={0.7}
            >
              <Feather name="edit-3" size={16} color={colors.textSecondary} style={styles.actionIcon} />
              <Text style={[styles.actionText, { color: colors.textSecondary }]}>Edit Meal</Text>
            </TouchableOpacity>
            
            {/* Add similar button */}
            <TouchableOpacity 
              style={[
                styles.actionButton,
                styles.primaryButton, 
                { 
                  borderColor: `${primaryColor}40`,
                }
              ]}
              activeOpacity={0.7}
            >
              <LinearGradient
                colors={[primaryColor, secondaryColor]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.primaryButtonGradient}
              >
                <Feather name="plus" size={16} style={styles.actionIcon} />
                <Text style={styles.primaryText}>Add Similar</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </LinearGradient>
    </Animated.View>
  );
}

// Helper function to get segment colors
function getSegmentColor(index: number, colors: any): string {
  const colorPalette = [
    '#3B82F6', // blue
    '#8B5CF6', // purple
    '#F97316', // orange
    '#10B981', // green
    '#EC4899', // pink
    '#F59E0B', // amber
    '#6366F1', // indigo
  ];
  
  return colorPalette[index % colorPalette.length];
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 20,
    marginBottom: 16,
    overflow: 'hidden',
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: {
          width: 0,
          height: 4,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
      web: {
        shadowOffset: {
          width: 0,
          height: 4,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      }
    }),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  imageWrapper: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  imageContainer: {
    borderRadius: 16,
    overflow: 'hidden',
    width: 80,
    height: 80,
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  mealBadge: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingVertical: 3,
    alignItems: 'center',
    justifyContent: 'center',
  },
  mealBadgeText: {
    color: 'white',
    fontWeight: '700',
    fontSize: 10,
    textTransform: 'uppercase',
  },
  headerContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingLeft: 16,
  },
  headerTextContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  name: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 5,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  timeIcon: {
    marginRight: 4,
  },
  time: {
    fontSize: 14,
    fontWeight: '500',
  },
  itemPreviews: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 2,
  },
  itemPreview: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 6,
    marginBottom: 4,
  },
  itemPreviewText: {
    fontSize: 12,
    fontWeight: '600',
  },
  calorieContainer: {
    justifyContent: 'center',
    alignItems: 'flex-end',
    marginRight: 12,
  },
  calorieGradient: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 14,
    alignItems: 'center',
  },
  calories: {
    fontSize: 20,
    fontWeight: '800',
    color: 'white',
  },
  calorieLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
    opacity: 0.9,
  },
  expandIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  details: {
    padding: 20,
    borderTopWidth: 1,
    overflow: 'hidden',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  macrosContainer: {
    marginBottom: 24,
  },
  macroSectionTitle: {
    fontSize: 15,
    fontWeight: '600',
  },
  macroTotalText: {
    fontSize: 13,
    fontWeight: '500',
  },
  macrosChart: {
    height: 14,
    borderRadius: 7,
    flexDirection: 'row',
    overflow: 'hidden',
    marginBottom: 12,
  },
  macrosChartSegment: {
    height: '100%',
  },
  macrosLegend: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  macrosLegendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 14,
    marginBottom: 6,
    minWidth: '45%',
  },
  macrosLegendColor: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  macrosLegendText: {
    fontSize: 13,
    flex: 1,
  },
  macroPercentageText: {
    fontSize: 13,
    fontWeight: '600',
    marginLeft: 4,
  },
  nutritionFacts: {
    marginTop: 8,
    marginBottom: 8,
  },
  macroNutrientRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  macroNutrient: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  macroNutrientIcon: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  macroNutrientShort: {
    color: 'white',
    fontWeight: '700',
    fontSize: 14,
  },
  macroNutrientValue: {
    fontWeight: '700',
    fontSize: 15,
  },
  macroNutrientLabel: {
    fontSize: 12,
  },
  itemSection: {
    marginBottom: 20,
  },
  itemsSectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 12,
  },
  itemList: {
    backgroundColor: 'transparent',
    borderRadius: 16,
    overflow: 'hidden',
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  itemNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  itemIconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  itemName: {
    flex: 1,
    fontSize: 15,
    fontWeight: '500',
  },
  itemCaloriesContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  itemCalories: {
    fontSize: 16,
    fontWeight: '700',
    marginRight: 4,
  },
  itemCaloriesUnit: {
    fontSize: 12,
    fontWeight: '500',
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    paddingTop: 20,
    borderTopWidth: 1,
  },
  actionButton: {
    height: 44,
    flex: 1,
    borderRadius: 22,
    borderWidth: 1,
  },
  actionIcon: {
    marginRight: 6,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  primaryButton: {
    marginLeft: 12,
    overflow: 'hidden',
  },
  primaryButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    borderRadius: 22,
  },
  primaryText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
  },
  emptyItemsMessage: {
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyItemsText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
});