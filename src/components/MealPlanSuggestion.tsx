import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, ScrollView, Image } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { format } from 'date-fns';
import { generateFitnessGoalMealPlan } from '@/services/dietitianService';
import { useTranslation } from '@/contexts/TranslationContext';
import { Card } from 'react-native-paper';
import { MaterialIcons , Feather } from '@expo/vector-icons';
import { router } from 'expo-router';

// Meal plan suggestion component displays an AI-generated meal plan based on user's fitness goals
export default function MealPlanSuggestion({ selectedDate, hasSubscription }: { selectedDate?: string, hasSubscription?: boolean }) {
  const { colors, isDark } = useTheme();
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const [mealPlan, setMealPlan] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const timeoutRef = useRef<number | null>(null);

  useEffect(() => {
    loadMealPlan();
    
    // Set a timeout to ensure we don't get stuck loading
    timeoutRef.current = setTimeout(() => {
      if (isLoading) {
        console.debug('MealPlanSuggestion: Timeout reached, using default meal plan');
        setIsLoading(false);
        setMealPlan(generateDefaultMealPlan());
      }
    }, 5000) as unknown as number;
    
    return () => {
      // Clear timeout on unmount
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [selectedDate]);

  // Load AI-generated meal plan
  const loadMealPlan = async () => {
    try {
      console.debug('MealPlanSuggestion: Loading meal plan data');
      setIsLoading(true);
      setError(null);
      
      // Use selectedDate if provided, otherwise use today's date
      const date = selectedDate || format(new Date(), 'yyyy-MM-dd');
      
      // Get meal plan with a Promise.race for timeout
      const mealPlanPromise = generateFitnessGoalMealPlan(date);
      const timeoutPromise = new Promise<any>((resolve) => {
        setTimeout(() => {
          console.debug('MealPlanSuggestion: Service call timed out');
          resolve(generateDefaultMealPlan());
        }, 3000);
      });
      
      // Use whichever resolves first
      const mealPlanData = await Promise.race([mealPlanPromise, timeoutPromise]);
      console.debug('MealPlanSuggestion: Meal plan data received');
      
      // Set meal plan data
      setMealPlan(mealPlanData);
    } catch (err) {
      console.error('Error loading meal plan:', err);
      setError('Failed to load your personalized meal plan');
      setMealPlan(generateDefaultMealPlan());
    } finally {
      // Clear the timeout since we've completed loading
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      setIsLoading(false);
    }
  };

  // Generate a default meal plan when service fails
  const generateDefaultMealPlan = () => {
    return {
      date: format(new Date(), 'yyyy-MM-dd'),
      meals: {
        breakfast: {
          name: "Greek Yogurt with Berries",
          description: "Creamy Greek yogurt with fresh berries",
          ingredients: ["Greek yogurt", "Mixed berries", "Honey"],
          nutrition: {
            calories: 300,
            protein: 20,
            carbs: 35,
            fat: 8
          },
          preparation: "Mix yogurt with berries and honey",
          mealType: "breakfast"
        },
        lunch: {
          name: "Grilled Chicken Salad",
          description: "Fresh salad with grilled chicken",
          ingredients: ["Chicken breast", "Mixed greens", "Tomatoes", "Cucumber"],
          nutrition: {
            calories: 400,
            protein: 35,
            carbs: 15,
            fat: 18
          },
          preparation: "Grill chicken and mix with vegetables",
          mealType: "lunch"
        },
        dinner: {
          name: "Baked Salmon with Vegetables",
          description: "Baked salmon fillet with roasted vegetables",
          ingredients: ["Salmon", "Broccoli", "Carrots", "Olive oil"],
          nutrition: {
            calories: 450,
            protein: 30,
            carbs: 25,
            fat: 20
          },
          preparation: "Bake salmon and vegetables at 400°F for 20 minutes",
          mealType: "dinner"
        },
        snacks: [
          {
            name: "Apple with Almond Butter",
            description: "Apple slices with almond butter",
            ingredients: ["Apple", "Almond butter"],
            nutrition: {
              calories: 200,
              protein: 5,
              carbs: 25,
              fat: 10
            },
            preparation: "Slice apple and serve with almond butter",
            mealType: "snack"
          }
        ]
      },
      nutritionSummary: {
        calories: 1350,
        protein: 90,
        carbs: 100,
        fat: 56
      }
    };
  };

  // Handle when user wants to view full meal plan
  const handleViewMealPlan = () => {
    router.push('/meal-planner');
  };

  // Background gradient style for card
  const cardStyle = {
    ...styles.card,
    backgroundColor: isDark ? colors.card : '#F9F9F9',
    borderColor: colors.border
  };

  // Show loading indicator
  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: colors.card }]}>
        <Text style={[styles.title, { color: colors.text }]}>{t('mealPlan.title')}</Text>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>
            {t('common.loading')}
          </Text>
        </View>
      </View>
    );
  }

  // Show error state
  if (error || !mealPlan) {
    return (
      <View style={[styles.container, { backgroundColor: colors.card }]}>
        <Text style={[styles.title, { color: colors.text }]}>{t('mealPlan.title')}</Text>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text }]}>
            {error || 'Unable to load meal plan'}
          </Text>
          <TouchableOpacity 
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={loadMealPlan}
          >
            <Text style={[styles.retryButtonText, { color: colors.background }]}>
              {t('common.retry')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Show meal plan
  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>{t('mealPlan.title')}</Text>
        <TouchableOpacity onPress={handleViewMealPlan}>
          <Text style={[styles.viewAllButton, { color: colors.primary }]}>
            {t('dashboard.viewAll')}
          </Text>
        </TouchableOpacity>
      </View>
      
      <Text style={[styles.subtitle, { color: colors.text }]}>
        {t('mealPlan.basedOnGoals')}
      </Text>
      
      {/* Daily Nutrition Summary */}
      <Card style={cardStyle}>
        <Card.Content>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {t('mealPlan.dailyNutrition')}
          </Text>
          <View style={styles.nutritionContainer}>
            <View style={styles.nutritionItem}>
              <Text style={[styles.nutritionValue, { color: colors.primary }]}>
                1700
              </Text>
              <Text style={[styles.nutritionLabel, { color: colors.text }]}>
                {t('nutrition.calories')}
              </Text>
            </View>
            <View style={styles.nutritionItem}>
              <Text style={[styles.nutritionValue, { color: colors.primary }]}>
                112g
              </Text>
              <Text style={[styles.nutritionLabel, { color: colors.text }]}>
                {t('nutrition.protein')}
              </Text>
            </View>
            <View style={styles.nutritionItem}>
              <Text style={[styles.nutritionValue, { color: colors.primary }]}>
                144g
              </Text>
              <Text style={[styles.nutritionLabel, { color: colors.text }]}>
                {t('nutrition.carbs')}
              </Text>
            </View>
            <View style={styles.nutritionItem}>
              <Text style={[styles.nutritionValue, { color: colors.primary }]}>
                80g
              </Text>
              <Text style={[styles.nutritionLabel, { color: colors.text }]}>
                {t('nutrition.fat')}
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Meal Options */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.mealsScrollContent}
      >
        {/* Breakfast Card */}
        <MealCard
          title={t('mealPlan.breakfast')}
          meal={mealPlan.meals.breakfast}
          colors={colors}
        />
        
        {/* Lunch Card */}
        <MealCard
          title={t('mealPlan.lunch')}
          meal={mealPlan.meals.lunch}
          colors={colors}
        />
        
        {/* Dinner Card */}
        <MealCard
          title={t('mealPlan.dinner')}
          meal={mealPlan.meals.dinner}
          colors={colors}
        />
        
        {/* Show snacks if available */}
        {mealPlan.meals.snacks && mealPlan.meals.snacks.length > 0 && (
          <MealCard
            title={t('mealPlan.snacks')}
            meal={mealPlan.meals.snacks[0]}
            colors={colors}
          />
        )}
      </ScrollView>
    </View>
  );
}

// Component for displaying a meal card
function MealCard({ title, meal, colors }: { title: string, meal: any, colors: any }) {
  return (
    <Card style={[styles.mealCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
      <Card.Content>
        <Text style={[styles.mealTypeLabel, { color: colors.primary }]}>{title}</Text>
        <Text style={[styles.mealName, { color: colors.text }]}>{meal.name}</Text>
        
        <View style={styles.mealDetailsContainer}>
          <View style={styles.mealDetail}>
            <MaterialIcons name="restaurant" size={14} color={colors.text} style={styles.mealDetailIcon} />
            <Text style={[styles.mealDetailText, { color: colors.text }]}>
              {meal.ingredients.length} ingredients
            </Text>
          </View>
          {meal.preparation && (
            <View style={styles.mealDetail}>
              <Feather name="clock" size={14} color={colors.text} style={styles.mealDetailIcon} />
              <Text style={[styles.mealDetailText, { color: colors.text }]}>
                {meal.preparation.length > 30 
                  ? `${meal.preparation.substring(0, 30)}...`
                  : meal.preparation}
              </Text>
            </View>
          )}
        </View>
        
        <View style={styles.mealNutrition}>
          <Text style={[styles.mealNutritionItem, { color: colors.text }]}>
            {meal.nutrition.calories} cal
          </Text>
          <Text style={[styles.mealNutritionItem, { color: colors.text }]}>
            {meal.nutrition.protein}g protein
          </Text>
        </View>
      </Card.Content>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 14,
    marginBottom: 16,
    opacity: 0.7,
  },
  viewAllButton: {
    fontSize: 14,
    fontWeight: '600',
  },
  card: {
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  nutritionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    alignItems: 'center',
  },
  nutritionValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  nutritionLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  mealsScrollContent: {
    paddingRight: 16,
  },
  mealCard: {
    width: 220,
    marginRight: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
  mealTypeLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
    textTransform: 'uppercase',
  },
  mealName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  mealDetailsContainer: {
    marginBottom: 12,
  },
  mealDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  mealDetailIcon: {
    marginRight: 6,
  },
  mealDetailText: {
    fontSize: 12,
  },
  mealNutrition: {
    flexDirection: 'row',
    marginTop: 8,
  },
  mealNutritionItem: {
    fontSize: 12,
    marginRight: 12,
  },
  loadingContainer: {
    paddingVertical: 40,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
}); 