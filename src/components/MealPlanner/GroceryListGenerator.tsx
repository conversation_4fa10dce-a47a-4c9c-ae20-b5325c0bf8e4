import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  TextInput,
  Modal,
  ActivityIndicator,
  Share,
  Alert,
  Platform
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/contexts/TranslationContext';
import { format, startOfWeek, endOfWeek, addDays } from 'date-fns';
import { Feather } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SavedGroceryLists, { SavedGroceryList } from './SavedGroceryLists';
import { parseIngredient, processRecipeIngredients, ParsedIngredient } from '@/utils/ingredientParser';

export interface GroceryItem {
  id: string;
  name: string;
  quantity: string;
  category: string;
  checked: boolean;
}

// Example categories for grocery items
const CATEGORIES = [
  'Produce',
  'Meat & Seafood',
  'Dairy & Eggs',
  'Bakery',
  'Pantry',
  'Frozen',
  'Beverages',
  'Other'
];

export interface MealPlanItem {
  date: string;
  meals: {
    name: string;
    ingredients: string[];
    mealType: string;
  }[];
}

interface GroceryListGeneratorProps {
  visible: boolean;
  mealPlan: MealPlanItem[];
  startDate?: Date;
  endDate?: Date;
  onSave?: (list: GroceryItem[]) => void;
  onClose: () => void;
}

const GroceryListGenerator: React.FC<GroceryListGeneratorProps> = ({
  visible,
  mealPlan,
  startDate = new Date(),
  endDate,
  onSave,
  onClose
}) => {
  const { colors, isDark } = useTheme();
  const { t } = useTranslation();
  
  // Default to current week if no end date provided
  const effectiveStartDate = startDate || startOfWeek(new Date(), { weekStartsOn: 1 });
  const effectiveEndDate = endDate || endOfWeek(new Date(), { weekStartsOn: 1 });
  
  const [groceryList, setGroceryList] = useState<GroceryItem[]>([]);
  const [newItemName, setNewItemName] = useState('');
  const [newItemQuantity, setNewItemQuantity] = useState('');
  const [newItemCategory, setNewItemCategory] = useState(CATEGORIES[0]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [isGenerating, setIsGenerating] = useState(true);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [listName, setListName] = useState('');
  const [showSavedLists, setShowSavedLists] = useState(false);
  
  // Reset state when modal opens
  useEffect(() => {
    if (visible) {
      generateGroceryList();
      
      // Default list name to date range
      const startFormatted = format(effectiveStartDate, 'MMM d');
      const endFormatted = format(effectiveEndDate, 'MMM d');
      setListName(`Shopping List ${startFormatted}-${endFormatted}`);
    }
  }, [visible, mealPlan]);
  
  // Generate the grocery list based on meals
  const generateGroceryList = () => {
    setIsGenerating(true);
    
    // Simulate API call or processing time
    setTimeout(() => {
      // Extract all ingredients from the meal plan
      const allIngredients = mealPlan.flatMap(day => 
        day.meals.flatMap(meal => meal.ingredients)
      );
      
      // Process ingredients
      const processedItems = ingredientsToGroceryItems(allIngredients);
      setGroceryList(processedItems);
      setIsGenerating(false);
    }, 1000);
  };
  
  // Convert ingredient strings to grocery items using the parser
  const ingredientsToGroceryItems = (ingredients: string[]): GroceryItem[] => {
    // Use the new ingredient parser
    const parsedIngredients = processRecipeIngredients(ingredients);
    
    // Convert to grocery items
    const groceryItems: GroceryItem[] = parsedIngredients.map((parsed, index) => ({
      id: `ingredient-${index}`,
      name: parsed.name,
      quantity: parsed.quantity && parsed.unit 
        ? `${parsed.quantity} ${parsed.unit}`.trim() 
        : parsed.quantity || '1',
      category: parsed.category,
      checked: false
    }));
    
    // Include some standard items that people might forget
    const standardItems: GroceryItem[] = [
      { id: 'standard-1', name: 'Salt', quantity: 'as needed', category: 'Pantry', checked: false },
      { id: 'standard-2', name: 'Pepper', quantity: 'as needed', category: 'Pantry', checked: false },
      { id: 'standard-3', name: 'Olive oil', quantity: 'as needed', category: 'Pantry', checked: false },
    ];
    
    // Only add standard items if they're not already included
    const allItems = [...groceryItems];
    standardItems.forEach(item => {
      if (!allItems.some(existing => 
        existing.name.toLowerCase() === item.name.toLowerCase()
      )) {
        allItems.push(item);
      }
    });
    
    // Sort by category
    return allItems.sort((a, b) => a.category.localeCompare(b.category));
  };
  
  // Add a new item to the list
  const handleAddItem = () => {
    if (!newItemName.trim()) return;
    
    // Parse the new item to get category and proper format
    const parsedIngredient = parseIngredient(`${newItemQuantity} ${newItemName}`);
    
    const newItem: GroceryItem = {
      id: Date.now().toString(),
      name: parsedIngredient.name || newItemName.trim(),
      quantity: (parsedIngredient.quantity && parsedIngredient.unit)
        ? `${parsedIngredient.quantity} ${parsedIngredient.unit}`.trim() 
        : newItemQuantity.trim() || '1',
      category: newItemCategory,
      checked: false,
    };
    
    setGroceryList(prevList => {
      const updatedList = [...prevList, newItem];
      // Sort by category
      return updatedList.sort((a, b) => a.category.localeCompare(b.category));
    });
    
    setNewItemName('');
    setNewItemQuantity('');
    setShowAddModal(false);
  };
  
  // Toggle item checked status
  const toggleItemChecked = (id: string) => {
    setGroceryList(prevList => 
      prevList.map(item => 
        item.id === id ? { ...item, checked: !item.checked } : item
      )
    );
  };
  
  // Remove an item from the list
  const removeItem = (id: string) => {
    setGroceryList(prevList => prevList.filter(item => item.id !== id));
  };
  
  // Share grocery list as text
  const handleShareList = async () => {
    try {
      // Create a formatted string from the grocery list
      let listText = `Grocery List - ${format(new Date(), 'MMM d, yyyy')}\n\n`;
      
      // Group items by category
      const itemsByCategory = groceryList.reduce<Record<string, GroceryItem[]>>((acc, item) => {
        if (!acc[item.category]) {
          acc[item.category] = [];
        }
        acc[item.category].push(item);
        return acc;
      }, {});
      
      // Generate text section for each category
      Object.entries(itemsByCategory).forEach(([category, items]) => {
        listText += `${category}:\n`;
        items.forEach(item => {
          const checkMark = item.checked ? '✓ ' : '☐ ';
          listText += `${checkMark}${item.name} (${item.quantity})\n`;
        });
        listText += '\n';
      });
      
      // Share the text
      await Share.share({
        message: listText,
        title: 'My Grocery List'
      });
    } catch (error) {
      console.error('Error sharing list:', error);
      Alert.alert(
        t('groceryList.shareError'),
        t('groceryList.shareErrorMessage')
      );
    }
  };
  
  // Save the current grocery list
  const handleSaveList = async () => {
    if (!listName.trim()) {
      Alert.alert(
        t('groceryList.saveError'),
        t('groceryList.enterListName')
      );
      return;
    }
    
    try {
      // Create a date range string
      const dateRange = `${format(effectiveStartDate, 'MMM d')} - ${format(effectiveEndDate, 'MMM d, yyyy')}`;
      
      // Create the saved list object
      const listToSave: SavedGroceryList = {
        id: Date.now().toString(),
        name: listName.trim(),
        dateCreated: new Date().toISOString(),
        dateRange,
        items: groceryList
      };
      
      // Get existing saved lists
      const savedLists: SavedGroceryList[] = [];
      const storedLists = await AsyncStorage.getItem('savedGroceryLists');
      if (storedLists) {
        savedLists.push(...JSON.parse(storedLists));
      }
      
      // Add new list and save back to storage
      savedLists.push(listToSave);
      await AsyncStorage.setItem('savedGroceryLists', JSON.stringify(savedLists));
      
      // Close save modal and show success message
      setShowSaveModal(false);
      Alert.alert(
        t('groceryList.saveSuccess'),
        t('groceryList.saveSuccessMessage', { name: listName })
      );
    } catch (error) {
      console.error('Error saving grocery list:', error);
      Alert.alert(
        t('groceryList.saveError'),
        t('groceryList.saveErrorMessage')
      );
    }
  };
  
  // Load a saved grocery list
  const handleLoadList = (savedList: SavedGroceryList) => {
    setGroceryList(savedList.items);
    setShowSavedLists(false);
  };
  
  // Group items by category
  const itemsByCategory = groceryList.reduce<Record<string, GroceryItem[]>>((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = [];
    }
    acc[item.category].push(item);
    return acc;
  }, {});
  
  // Get unchecked item count
  const uncheckedCount = groceryList.filter(item => !item.checked).length;
  
  return (
    <Modal
      visible={visible}
      transparent={false}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: colors.border }]}>
          <View style={styles.headerTitleContainer}>
            <Text style={[styles.headerTitle, { color: colors.text }]}>
              {t('groceryList.title')}
            </Text>
            <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
              {`${format(effectiveStartDate, 'MMM d')} - ${format(effectiveEndDate, 'MMM d')}`}
            </Text>
          </View>
          
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Feather name="x" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>
        
        {/* Progress indicator */}
        <View style={[styles.progressContainer, { borderBottomColor: colors.border }]}>
          <View style={styles.progressTextContainer}>
            <Text style={[styles.progressText, { color: colors.text }]}>
              {uncheckedCount} {t('groceryList.itemsRemaining')}
            </Text>
          </View>
          
          <View style={[styles.progressBar, { backgroundColor: isDark ? '#333' : '#eee' }]}>
            <View 
              style={[
                styles.progressFill, 
                { 
                  backgroundColor: colors.primary,
                  width: `${Math.max(0, (groceryList.length - uncheckedCount) / Math.max(1, groceryList.length) * 100)}%`
                }
              ]} 
            />
          </View>
        </View>
        
        {/* Loading state */}
        {isGenerating ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.text }]}>
              {t('groceryList.generatingList')}
            </Text>
          </View>
        ) : (
          <>
            {/* Grocery list */}
            <ScrollView style={styles.listContainer}>
              {Object.keys(itemsByCategory).map(category => (
                <View key={category} style={styles.categorySection}>
                  <Text style={[styles.categoryTitle, { color: colors.text }]}>
                    {category}
                  </Text>
                  
                  {itemsByCategory[category].map(item => (
                    <View 
                      key={item.id} 
                      style={[
                        styles.itemRow,
                        { 
                          backgroundColor: item.checked 
                            ? (isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)')
                            : colors.card,
                          borderColor: colors.border
                        }
                      ]}
                    >
                      <TouchableOpacity
                        style={[
                          styles.checkBox,
                          { 
                            borderColor: item.checked ? colors.primary : colors.border,
                            backgroundColor: item.checked ? colors.primary : 'transparent'
                          }
                        ]}
                        onPress={() => toggleItemChecked(item.id)}
                      >
                        {item.checked && <Feather name="check" size={14}  color={colors.text} />}
                      </TouchableOpacity>
                      
                      <View style={styles.itemDetails}>
                        <Text 
                          style={[
                            styles.itemName, 
                            { 
                              color: colors.text,
                              textDecorationLine: item.checked ? 'line-through' : 'none',
                              opacity: item.checked ? 0.6 : 1
                            }
                          ]}
                        >
                          {item.name}
                        </Text>
                        
                        <Text 
                          style={[
                            styles.itemQuantity, 
                            { 
                              color: colors.textSecondary,
                              opacity: item.checked ? 0.6 : 1
                            }
                          ]}
                        >
                          {item.quantity}
                        </Text>
                      </View>
                      
                      <TouchableOpacity
                        style={styles.deleteButton}
                        onPress={() => removeItem(item.id)}
                      >
                        <Feather name="trash-2" size={16} color={colors.error || '#ff6b6b'} />
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              ))}
            </ScrollView>
            
            {/* Action buttons */}
            <View style={[styles.actionsContainer, { borderTopColor: colors.border }]}>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: colors.background, borderColor: colors.border }]}
                onPress={() => setShowAddModal(true)}
              >
                <Feather name="plus" size={20} color={colors.text} />
                <Text style={[styles.actionButtonText, { color: colors.text }]}>
                  {t('groceryList.addItem')}
                </Text>
              </TouchableOpacity>
              
              <View style={styles.actionButtonsRight}>
                <TouchableOpacity
                  style={[styles.iconButton, { backgroundColor: colors.background, borderColor: colors.border }]}
                  onPress={() => setShowSavedLists(true)}
                >
                  <Feather name="bookmark" size={20} color={colors.text} />
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.iconButton, { backgroundColor: colors.background, borderColor: colors.border }]}
                  onPress={handleShareList}
                >
                  <ShareIcon size={20} color={colors.text} />
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.iconButton, { backgroundColor: colors.primary }]}
                  onPress={() => setShowSaveModal(true)}
                >
                  <Feather name="save" size={20}  color={colors.text} />
                </TouchableOpacity>
              </View>
            </View>
            
            {/* Add item modal */}
            <Modal
              visible={showAddModal}
              transparent
              animationType="slide"
              onRequestClose={() => setShowAddModal(false)}
            >
              <View style={styles.modalOverlay}>
                <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
                  <Text style={[styles.modalTitle, { color: colors.text }]}>
                    {t('groceryList.addGroceryItem')}
                  </Text>
                  
                  <Text style={[styles.inputLabel, { color: colors.text }]}>
                    {t('groceryList.itemName')}
                  </Text>
                  <TextInput
                    style={[
                      styles.textInput,
                      { 
                        backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                        color: colors.text
                      }
                    ]}
                    value={newItemName}
                    onChangeText={setNewItemName}
                    placeholder={t('groceryList.enterItemName')}
                    placeholderTextColor={colors.textSecondary}
                  />
                  
                  <Text style={[styles.inputLabel, { color: colors.text }]}>
                    {t('groceryList.quantity')}
                  </Text>
                  <TextInput
                    style={[
                      styles.textInput,
                      { 
                        backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                        color: colors.text
                      }
                    ]}
                    value={newItemQuantity}
                    onChangeText={setNewItemQuantity}
                    placeholder={t('groceryList.enterQuantity')}
                    placeholderTextColor={colors.textSecondary}
                  />
                  
                  <Text style={[styles.inputLabel, { color: colors.text }]}>
                    {t('groceryList.category')}
                  </Text>
                  <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categorySelector}>
                    {CATEGORIES.map(category => (
                      <TouchableOpacity
                        key={category}
                        style={[
                          styles.categoryChip,
                          { 
                            backgroundColor: category === newItemCategory 
                              ? colors.primary 
                              : isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
                          }
                        ]}
                        onPress={() => setNewItemCategory(category)}
                      >
                        <Text 
                          style={{ 
                            color: category === newItemCategory 
                              ? 'white' 
                              : colors.text
                          }}
                        >
                          {category}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                  
                  <View style={styles.modalActions}>
                    <TouchableOpacity
                      style={[styles.modalButton, { backgroundColor: isDark ? '#333' : '#eee' }]}
                      onPress={() => setShowAddModal(false)}
                    >
                      <Text style={[styles.modalButtonText, { color: colors.text }]}>
                        {t('common.cancel')}
                      </Text>
                    </TouchableOpacity>
                    
                    <TouchableOpacity
                      style={[
                        styles.modalButton, 
                        { 
                          backgroundColor: colors.primary,
                          opacity: !newItemName.trim() ? 0.6 : 1
                        }
                      ]}
                      onPress={handleAddItem}
                      disabled={!newItemName.trim()}
                    >
                      <Text style={styles.modalButtonText}>
                        {t('common.add')}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </Modal>
            
            {/* Save List Modal */}
            <Modal
              visible={showSaveModal}
              transparent
              animationType="fade"
              onRequestClose={() => setShowSaveModal(false)}
            >
              <View style={styles.modalOverlay}>
                <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
                  <Text style={[styles.modalTitle, { color: colors.text }]}>
                    {t('groceryList.saveList')}
                  </Text>
                  
                  <Text style={[styles.inputLabel, { color: colors.text }]}>
                    {t('groceryList.listName')}
                  </Text>
                  <TextInput
                    style={[
                      styles.textInput,
                      { 
                        backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                        color: colors.text,
                        borderColor: colors.border
                      }
                    ]}
                    value={listName}
                    onChangeText={setListName}
                    placeholder={t('groceryList.enterListName')}
                    placeholderTextColor={colors.textSecondary}
                  />
                  
                  <View style={styles.modalActions}>
                    <TouchableOpacity
                      style={[styles.modalButton, { backgroundColor: isDark ? '#333' : '#eee' }]}
                      onPress={() => setShowSaveModal(false)}
                    >
                      <Text style={[styles.modalButtonText, { color: colors.text }]}>
                        {t('common.cancel')}
                      </Text>
                    </TouchableOpacity>
                    
                    <TouchableOpacity
                      style={[
                        styles.modalButton, 
                        { 
                          backgroundColor: colors.primary,
                          opacity: !listName.trim() ? 0.6 : 1
                        }
                      ]}
                      onPress={handleSaveList}
                      disabled={!listName.trim()}
                    >
                      <Text style={styles.modalButtonText}>
                        {t('common.save')}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </Modal>
            
            {/* Saved Lists Modal */}
            <SavedGroceryLists
              visible={showSavedLists}
              onClose={() => setShowSavedLists(false)}
              onLoadList={handleLoadList}
            />
          </>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerTitleContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  closeButton: {
    padding: 4,
  },
  progressContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  progressTextContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  listContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  categorySection: {
    marginTop: 16,
    marginBottom: 8,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
  },
  checkBox: {
    width: 22,
    height: 22,
    borderRadius: 4,
    borderWidth: 2,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
  },
  itemQuantity: {
    fontSize: 14,
    marginTop: 2,
  },
  deleteButton: {
    padding: 8,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
  },
  actionButtonText: {
    fontWeight: '500',
    marginLeft: 8,
  },
  actionButtonsRight: {
    flexDirection: 'row',
  },
  iconButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    marginLeft: 8,
    borderWidth: 1,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 24,
    paddingBottom: 40,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  textInput: {
    height: 50,
    borderRadius: 8,
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  categorySelector: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  categoryChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    marginRight: 8,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  modalButton: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  modalButtonText: {
    fontWeight: '600',
    color: 'white',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
});

export default GroceryListGenerator; 