import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  useWindowDimensions,
 useColorScheme } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

import {
  FoodLogEntry,
  PatternResult,
  generateContextualSuggestions,
  analyzeRecurringMeals,
} from '@/utils/intelligentDefaults';

interface IntelligentFoodDefaultsProps {
  userHistory: FoodLogEntry[];
  onSelectSuggestion: (suggestion: PatternResult<FoodLogEntry>) => void;
  maxSuggestions?: number;
  showConfidence?: boolean;
}

export function IntelligentFoodDefaults({
  userHistory,
  onSelectSuggestion,
  maxSuggestions = 3,
  showConfidence = false,
}: IntelligentFoodDefaultsProps) {
  const [suggestions, setSuggestions] = useState<PatternResult<FoodLogEntry>[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { width } = useWindowDimensions();
  
  // Get the current time to determine mealtime context
  const currentHour = new Date().getHours();
  let currentMealContext: 'breakfast' | 'lunch' | 'dinner' | 'snack' = 'snack';
  
  if (currentHour >= 5 && currentHour < 10) {
    currentMealContext = 'breakfast';
  } else if (currentHour >= 10 && currentHour < 15) {
    currentMealContext = 'lunch';
  } else if (currentHour >= 17 && currentHour < 22) {
    currentMealContext = 'dinner';
  }

  useEffect(() => {
    if (userHistory.length === 0) {
      setIsLoading(false);
      return;
    }

    // Analyze user history to find patterns
    const patterns = analyzeRecurringMeals(userHistory);
    
    if (patterns.length === 0) {
      setIsLoading(false);
      return;
    }
    
    // Get contextual suggestions based on current time
    const contextualSuggestions = generateContextualSuggestions(
      'food',
      patterns,
      new Date(),
      maxSuggestions
    );
    
    setSuggestions(contextualSuggestions);
    setIsLoading(false);
  }, [userHistory, maxSuggestions]);

  const handleSelectSuggestion = (suggestion: PatternResult<FoodLogEntry>) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onSelectSuggestion(suggestion);
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={[styles.loadingText, { color: isDark ? '#999' : '#666' }]}>
          Analyzing your meal patterns...
        </Text>
      </View>
    );
  }

  if (suggestions.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={[styles.emptyText, { color: isDark ? '#999' : '#666' }]}>
          No meal patterns detected yet. Keep logging your meals!
        </Text>
      </View>
    );
  }

  // For wide screens, display in grid
  const isWideScreen = width > 500;

  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: isDark ? '#fff' : '#000' }]}>
        Suggested for {currentMealContext}
      </Text>
      
      <Text style={[styles.subtitle, { color: isDark ? '#bbb' : '#666' }]}>
        Based on your usual habits
      </Text>
      
      {isWideScreen ? (
        // Grid layout for wide screens
        <View style={styles.gridContainer}>
          {suggestions.map((suggestion, index) => (
            <SuggestionCard
              key={`${suggestion.pattern.foods?.[0]?.name}-${index}`}
              suggestion={suggestion}
              onSelect={() => handleSelectSuggestion(suggestion)}
              isDark={isDark}
              showConfidence={showConfidence}
              style={{ width: width / Math.min(suggestions.length, 3) - 16 }}
            />
          ))}
        </View>
      ) : (
        // Horizontal scroll for narrow screens
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {suggestions.map((suggestion, index) => (
            <SuggestionCard
              key={`${suggestion.pattern.foods?.[0]?.name}-${index}`}
              suggestion={suggestion}
              onSelect={() => handleSelectSuggestion(suggestion)}
              isDark={isDark}
              showConfidence={showConfidence}
            />
          ))}
        </ScrollView>
      )}
    </View>
  );
}

interface SuggestionCardProps {
  suggestion: PatternResult<FoodLogEntry>;
  onSelect: () => void;
  isDark: boolean;
  showConfidence: boolean;
  style?: object;
}

function SuggestionCard({
  suggestion,
  onSelect,
  isDark,
  showConfidence,
  style = {},
}: SuggestionCardProps) {
  const food = suggestion.pattern.foods?.[0];
  if (!food) return null;

  // Get day context if available
  const dayContext = suggestion.suggestedTime?.dayOfWeek;
  let dayLabel = '';
  
  if (dayContext && dayContext.length > 0) {
    if (dayContext.length === 7) {
      dayLabel = 'Every day';
    } else if (dayContext.includes(1) && dayContext.includes(2) && dayContext.includes(3) && 
               dayContext.includes(4) && dayContext.includes(5) && dayContext.length === 5) {
      dayLabel = 'Weekdays';
    } else if (dayContext.includes(0) && dayContext.includes(6) && dayContext.length === 2) {
      dayLabel = 'Weekends';
    } else {
      const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
      dayLabel = dayContext.map(day => dayNames[day]).join(', ');
    }
  }

  return (
    <TouchableOpacity
      style={[
        styles.card,
        { backgroundColor: isDark ? '#333' : '#f5f5f5' },
        style,
      ]}
      onPress={onSelect}
      activeOpacity={0.7}
      accessibilityRole="button"
      accessibilityLabel={`Suggested meal: ${food.name} ${food.quantity || ''} ${food.unit || ''} for ${food.mealTime}`}
    >
      <View style={styles.cardHeader}>
        <Text style={[styles.foodName, { color: isDark ? '#fff' : '#000' }]}>
          {food.name}
        </Text>
        <Ionicons 
          name="time-outline" 
          size={18} 
          color={isDark ? '#bbb' : '#666'} 
        />
      </View>
      
      <View style={styles.detailsContainer}>
        {food.quantity && (
          <Text style={[styles.quantity, { color: isDark ? '#ddd' : '#333' }]}>
            {food.quantity} {food.unit}
          </Text>
        )}
        
        {dayLabel && (
          <Text style={[styles.dayPattern, { color: isDark ? '#bbb' : '#666' }]}>
            {dayLabel}
          </Text>
        )}
        
        {food.mealTime && (
          <View style={styles.mealTimeTag}>
            <Text style={styles.mealTimeText}>
              {food.mealTime}
            </Text>
          </View>
        )}
        
        {showConfidence && (
          <View style={styles.confidenceContainer}>
            <View style={styles.confidenceBar}>
              <View 
                style={[
                  styles.confidenceFill, 
                  {
                    width: `${suggestion.confidence * 100}%`,
                    backgroundColor: suggestion.confidence > 0.8 ? '#4CAF50' : 
                                    suggestion.confidence > 0.6 ? '#8BC34A' : 
                                    suggestion.confidence > 0.4 ? '#FFC107' : '#FF9800'
                  }
                ]} 
              />
            </View>
            <Text style={[styles.confidenceText, { color: isDark ? '#bbb' : '#666' }]}>
              {Math.round(suggestion.confidence * 100)}% match
            </Text>
          </View>
        )}
      </View>
      
      <View style={styles.cardFooter}>
        <Text style={[styles.tapLabel, { color: isDark ? '#bbb' : '#666' }]}>
          Tap to log
        </Text>
        <Ionicons 
          name="add-circle-outline" 
          size={20} 
          color={isDark ? '#bbb' : '#666'} 
        />
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
    paddingHorizontal: 16,
  },
  subtitle: {
    fontSize: 14,
    marginBottom: 12,
    paddingHorizontal: 16,
  },
  scrollContent: {
    paddingHorizontal: 12,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 14,
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.05)',
    margin: 16,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
  },
  card: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 4,
    marginBottom: 8,
    minWidth: 180,
    maxWidth: 300,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  foodName: {
    fontSize: 16,
    fontWeight: '600',
  },
  detailsContainer: {
    marginBottom: 12,
  },
  quantity: {
    fontSize: 14,
    marginBottom: 4,
  },
  dayPattern: {
    fontSize: 12,
    marginBottom: 8,
  },
  mealTimeTag: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 4,
    alignSelf: 'flex-start',
    marginBottom: 8,
  },
  mealTimeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  confidenceContainer: {
    marginTop: 8,
  },
  confidenceBar: {
    height: 4,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 4,
  },
  confidenceFill: {
    height: '100%',
    borderRadius: 2,
  },
  confidenceText: {
    fontSize: 10,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
    paddingTop: 8,
  },
  tapLabel: {
    fontSize: 12,
  },
});

export default IntelligentFoodDefaults; 