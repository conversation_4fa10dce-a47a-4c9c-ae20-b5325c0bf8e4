import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Alert,
  Image
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/contexts/TranslationContext';
import { Feather } from '@expo/vector-icons';
import RecipeSelector from './RecipeSelector';
import { Recipe } from '@/services/recipeRecommendationService';

// Get recipe meal categories
export const getMealOptions = () => [
  { value: 'breakfast', label: 'Breakfast' },
  { value: 'lunch', label: 'Lunch' },
  { value: 'dinner', label: 'Dinner' },
  { value: 'snack', label: 'Snack' }
];

interface MealItem {
  name: string;
  description?: string;
  ingredients: string[];
  nutrition: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  preparation?: string;
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  imageUrl?: string;
  recipeId?: string;
}

interface MealPlanRecipeIntegrationProps {
  meal: MealItem;
  onUpdateMeal: (updatedMeal: MealItem) => void;
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
}

export default function MealPlanRecipeIntegration({
  meal,
  onUpdateMeal,
  mealType
}: MealPlanRecipeIntegrationProps) {
  const { colors, isDark } = useTheme();
  const { t } = useTranslation();
  
  const [showRecipeSelector, setShowRecipeSelector] = useState(false);
  const [isLinkedToRecipe, setIsLinkedToRecipe] = useState(false);
  
  useEffect(() => {
    // Check if this meal is linked to a recipe
    setIsLinkedToRecipe(!!meal.recipeId);
  }, [meal]);
  
  const handleSelectRecipe = (recipe: Recipe) => {
    // Convert recipe to meal format
    const updatedMeal: MealItem = {
      name: recipe.name,
      description: recipe.description,
      ingredients: recipe.ingredients.map(i => `${i.amount} ${i.unit} ${i.name}`),
      nutrition: {
        calories: recipe.nutritionFacts.calories,
        protein: recipe.nutritionFacts.protein,
        carbs: recipe.nutritionFacts.carbs,
        fat: recipe.nutritionFacts.fat
      },
      preparation: recipe.instructions.join('\n'),
      mealType,
      imageUrl: recipe.imageUrl,
      recipeId: recipe.id
    };
    
    onUpdateMeal(updatedMeal);
  };
  
  const handleChangeRecipe = () => {
    setShowRecipeSelector(true);
  };
  
  const handleRemoveRecipe = () => {
    Alert.alert(
      t('mealPlanner.confirmRemoveRecipe'),
      t('mealPlanner.confirmRemoveRecipeMessage'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel'
        },
        {
          text: t('common.remove'),
          style: 'destructive',
          onPress: () => {
            // Keep the meal but remove the recipe association
            const updatedMeal = { ...meal };
            delete updatedMeal.recipeId;
            delete updatedMeal.imageUrl;
            onUpdateMeal(updatedMeal);
          }
        }
      ]
    );
  };
  
  return (
    <View style={styles.container}>
      {isLinkedToRecipe ? (
        <View style={[styles.recipeCard, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)' }]}>
          {meal.imageUrl && (
            <Image 
              source={{ uri: meal.imageUrl }} 
              style={styles.recipeImage}
              resizeMode="cover"
            />
          )}
          
          <View style={styles.recipeDetails}>
            <Text style={[styles.recipeLink, { color: colors.primary }]}>
              {t('mealPlanner.linkedRecipe')}
            </Text>
            <Text style={[styles.recipeName, { color: colors.text }]}>
              {meal.name}
            </Text>
            
            <View style={styles.recipeActions}>
              <TouchableOpacity
                style={[styles.recipeActionButton, { backgroundColor: colors.primary }]}
                onPress={handleChangeRecipe}
              >
                <Feather name="refresh-cw" size={14} style={styles.actionIcon} />
                <Text style={styles.actionButtonText}>
                  {t('mealPlanner.changeRecipe')}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.recipeActionButton, { backgroundColor: isDark ? '#333' : '#eee' }]}
                onPress={handleRemoveRecipe}
              >
                <Text style={[styles.actionButtonText, { color: colors.text }]}>
                  {t('mealPlanner.removeRecipe')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      ) : (
        <TouchableOpacity
          style={[styles.addRecipeButton, { borderColor: colors.border }]}
          onPress={() => setShowRecipeSelector(true)}
        >
          <View style={styles.addRecipeContent}>
            <Feather name="plus" size={24} color={colors.primary} style={styles.addIcon} />
            <Text style={[styles.addRecipeText, { color: colors.text }]}>
              {t('mealPlanner.addRecipe')}
            </Text>
            <Text style={[styles.addRecipeSubtext, { color: colors.textSecondary }]}>
              {t('mealPlanner.replaceWithRecipe')}
            </Text>
          </View>
        </TouchableOpacity>
      )}
      
      <RecipeSelector
        visible={showRecipeSelector}
        onClose={() => setShowRecipeSelector(false)}
        onSelectRecipe={handleSelectRecipe}
        mealType={mealType}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 12,
    marginBottom: 16,
  },
  addRecipeButton: {
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addRecipeContent: {
    alignItems: 'center',
  },
  addIcon: {
    marginBottom: 8,
  },
  addRecipeText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  addRecipeSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  recipeCard: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  recipeImage: {
    width: '100%',
    height: 120,
  },
  recipeDetails: {
    padding: 16,
  },
  recipeLink: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  recipeName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  recipeActions: {
    flexDirection: 'row',
  },
  recipeActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginRight: 8,
  },
  actionIcon: {
    marginRight: 4,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: 'white',
  },
}); 