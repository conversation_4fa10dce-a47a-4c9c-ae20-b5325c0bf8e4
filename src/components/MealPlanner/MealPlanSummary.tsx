import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/contexts/TranslationContext';
import { getUserProfile } from '@/services/nutritionGoalService';

interface MealPlanSummaryProps {
  title?: string;
  basedOnGoals?: boolean;
}

export default function MealPlanSummary({ 
  title = "mealPlan.title", 
  basedOnGoals = true 
}: MealPlanSummaryProps) {
  const { colors, isDark } = useTheme();
  const { t } = useTranslation();
  const [nutritionGoals, setNutritionGoals] = React.useState({
    calories: 1700,
    protein: 112,
    carbs: 144,
    fat: 80
  });
  
  React.useEffect(() => {
    const loadNutritionGoals = async () => {
      try {
        const profile = await getUserProfile();
        if (profile) {
          setNutritionGoals({
            calories: profile.calorieGoal,
            protein: profile.proteinGoal,
            carbs: profile.carbsGoal,
            fat: profile.fatGoal
          });
        }
      } catch (error) {
        console.error('Error loading nutrition goals:', error);
      }
    };
    
    loadNutritionGoals();
  }, []);
  
  return (
    <View style={[styles.container, { backgroundColor: isDark ? colors.card : '#fff', borderColor: colors.border }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>{t(title)}</Text>
        {basedOnGoals && (
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            {t('mealPlan.basedOnGoals')}
          </Text>
        )}
      </View>
      
      <View style={[styles.nutritionCard, { backgroundColor: isDark ? colors.subtle : 'rgba(0,0,0,0.03)' }]}>
        <Text style={[styles.nutritionTitle, { color: colors.text }]}>{t('mealPlan.dailyNutrition')}</Text>
        
        <View style={styles.nutritionRow}>
          <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>{t('nutrition.calories')}:</Text>
          <Text style={[styles.nutritionValue, { color: colors.text }]}>{nutritionGoals.calories}</Text>
        </View>
        
        <View style={styles.nutritionRow}>
          <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>{t('nutrition.protein')}:</Text>
          <Text style={[styles.nutritionValue, { color: colors.text }]}>{nutritionGoals.protein}g</Text>
        </View>
        
        <View style={styles.nutritionRow}>
          <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>{t('nutrition.carbs')}:</Text>
          <Text style={[styles.nutritionValue, { color: colors.text }]}>{nutritionGoals.carbs}g</Text>
        </View>
        
        <View style={styles.nutritionRow}>
          <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>{t('nutrition.fat')}:</Text>
          <Text style={[styles.nutritionValue, { color: colors.text }]}>{nutritionGoals.fat}g</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    padding: 16,
    borderWidth: 1,
  },
  header: {
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
  },
  nutritionCard: {
    borderRadius: 12,
    padding: 16,
  },
  nutritionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  nutritionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  nutritionLabel: {
    fontSize: 15,
  },
  nutritionValue: {
    fontSize: 15,
    fontWeight: '500',
  },
}); 