import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, ActivityIndicator, Alert } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/contexts/TranslationContext';
import { MaterialCommunityIcons , Feather } from '@expo/vector-icons';
import MealPlannerCalendar from './MealPlannerCalendar';
import MealPlanRecipeIntegration from './MealPlanRecipeIntegration';
import GroceryListGenerator from './GroceryListGenerator';
import SavedGroceryLists, { SavedGroceryList } from './SavedGroceryLists';
import { useMealPlannerService } from '@/services/mealPlannerService';
import { PremiumFeature } from '../PremiumFeature';
import AsyncStorage from '@react-native-async-storage/async-storage';
import MealPlanSummary from './MealPlanSummary';

interface NutritionGoal {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  dietary?: string[];
}

interface MealPlan {
  date: string;
  meals: {
    breakfast: Meal;
    lunch: Meal;
    dinner: Meal;
    snacks: Meal[];
  };
  nutritionSummary: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
}

interface Meal {
  name: string;
  description?: string;
  ingredients: string[];
  nutrition: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  preparation?: string;
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  imageUrl?: string;
  recipeId?: string;
}

export default function MealPlanner() {
  const { colors, isDark } = useTheme();
  const { t } = useTranslation();
  const { generateMealPlan } = useMealPlannerService();
  const [loading, setLoading] = useState(false);
  const [mealPlans, setMealPlans] = useState<MealPlan[]>([]);
  const [nutritionGoal, setNutritionGoal] = useState<NutritionGoal>({
    calories: 2000,
    protein: 100,
    carbs: 250,
    fat: 65,
    dietary: []
  });
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [showGroceryList, setShowGroceryList] = useState(false);
  const [showSavedGroceryLists, setShowSavedGroceryLists] = useState(false);

  // Load saved meal plans from AsyncStorage
  useEffect(() => {
    const loadMealPlans = async () => {
      try {
        const savedPlans = await AsyncStorage.getItem('mealPlans');
        if (savedPlans) {
          setMealPlans(JSON.parse(savedPlans));
        }
        
        const savedGoals = await AsyncStorage.getItem('nutritionGoals');
        if (savedGoals) {
          setNutritionGoal(JSON.parse(savedGoals));
        }
      } catch (error) {
        console.error('Error loading saved meal plans:', error);
      }
    };
    
    loadMealPlans();
  }, []);

  // Save meal plans to AsyncStorage whenever they change
  useEffect(() => {
    const saveMealPlans = async () => {
      try {
        if (mealPlans.length > 0) {
          await AsyncStorage.setItem('mealPlans', JSON.stringify(mealPlans));
        }
      } catch (error) {
        console.error('Error saving meal plans:', error);
      }
    };
    
    saveMealPlans();
  }, [mealPlans]);

  // Generate a meal plan for the selected date using AI
  const handleGenerateMealPlan = useCallback(async () => {
    setLoading(true);
    try {
      const newPlan = await generateMealPlan(selectedDate, nutritionGoal);
      
      // Update meal plans state with the new plan
      setMealPlans(prevPlans => {
        // Remove any existing plan for the selected date
        const filteredPlans = prevPlans.filter(plan => plan.date !== selectedDate);
        // Add the new plan
        return [...filteredPlans, newPlan];
      });
      
      Alert.alert(
        t('mealPlanner.planGenerated'),
        t('mealPlanner.planGeneratedMessage'),
        [{ text: t('common.ok') }]
      );
    } catch (error) {
      Alert.alert(
        t('mealPlanner.generationError'),
        t('mealPlanner.generationErrorMessage'),
        [{ text: t('common.ok') }]
      );
      console.error('Error generating meal plan:', error);
    } finally {
      setLoading(false);
    }
  }, [selectedDate, nutritionGoal, t]);

  // Get the meal plan for the selected date
  const getSelectedDayPlan = (): MealPlan | undefined => {
    return mealPlans.find(plan => plan.date === selectedDate);
  };

  // Update a meal in the meal plan
  const handleUpdateMeal = (updatedMeal: Meal, mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack', snackIndex?: number) => {
    setMealPlans(prevPlans => {
      return prevPlans.map(plan => {
        if (plan.date === selectedDate) {
          // Create a deep copy of the plan
          const updatedPlan = { ...plan, meals: { ...plan.meals } };
          
          // Update the appropriate meal
          if (mealType === 'snack' && snackIndex !== undefined) {
            // Update a specific snack
            const updatedSnacks = [...updatedPlan.meals.snacks];
            updatedSnacks[snackIndex] = updatedMeal;
            updatedPlan.meals.snacks = updatedSnacks;
          } else {
            // Update a main meal (breakfast, lunch, dinner)
            updatedPlan.meals[mealType] = updatedMeal;
          }
          
          // Recalculate nutrition summary
          updatedPlan.nutritionSummary = {
            calories: updatedPlan.meals.breakfast.nutrition.calories + 
                      updatedPlan.meals.lunch.nutrition.calories + 
                      updatedPlan.meals.dinner.nutrition.calories + 
                      updatedPlan.meals.snacks.reduce((sum, snack) => sum + snack.nutrition.calories, 0),
            protein: updatedPlan.meals.breakfast.nutrition.protein + 
                     updatedPlan.meals.lunch.nutrition.protein + 
                     updatedPlan.meals.dinner.nutrition.protein + 
                     updatedPlan.meals.snacks.reduce((sum, snack) => sum + snack.nutrition.protein, 0),
            carbs: updatedPlan.meals.breakfast.nutrition.carbs + 
                   updatedPlan.meals.lunch.nutrition.carbs + 
                   updatedPlan.meals.dinner.nutrition.carbs + 
                   updatedPlan.meals.snacks.reduce((sum, snack) => sum + snack.nutrition.carbs, 0),
            fat: updatedPlan.meals.breakfast.nutrition.fat + 
                 updatedPlan.meals.lunch.nutrition.fat + 
                 updatedPlan.meals.dinner.nutrition.fat + 
                 updatedPlan.meals.snacks.reduce((sum, snack) => sum + snack.nutrition.fat, 0),
          };
          
          return updatedPlan;
        }
        return plan;
      });
    });
  };

  // Render a meal card
  const renderMealCard = (meal: Meal, mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack', snackIndex?: number) => {
    return (
      <View 
        key={snackIndex !== undefined ? `${meal.mealType}-${snackIndex}` : meal.mealType} 
        style={[
          styles.mealCard, 
          { 
            backgroundColor: isDark ? colors.card : '#fff',
            borderColor: colors.border
          }
        ]}
      >
        <Text style={[styles.mealTitle, { color: colors.text }]}>
          {meal.name}
        </Text>
        
        {meal.description && (
          <Text style={[styles.mealDescription, { color: colors.textSecondary }]}>
            {meal.description}
          </Text>
        )}
        
        <View style={styles.nutritionContainer}>
          <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
            {t('common.nutrition')}:
          </Text>
          <Text style={[styles.nutritionValue, { color: colors.text }]}>
            {meal.nutrition.calories} {t('common.calories')} • {meal.nutrition.protein}g {t('common.protein')} • 
            {meal.nutrition.carbs}g {t('common.carbs')} • {meal.nutrition.fat}g {t('common.fat')}
          </Text>
        </View>
        
        <View style={styles.ingredientsContainer}>
          <Text style={[styles.ingredientsLabel, { color: colors.textSecondary }]}>
            {t('common.ingredients')}:
          </Text>
          <Text style={[styles.ingredientsValue, { color: colors.text }]}>
            {meal.ingredients.join(', ')}
          </Text>
        </View>
        
        {/* Add Recipe Integration Component */}
        <MealPlanRecipeIntegration
          meal={meal}
          mealType={meal.mealType}
          onUpdateMeal={(updatedMeal) => handleUpdateMeal(updatedMeal, mealType, snackIndex)}
        />
      </View>
    );
  };

  const selectedDayPlan = getSelectedDayPlan();

  // Handle loading a saved grocery list
  const handleLoadSavedGroceryList = (list: SavedGroceryList) => {
    setShowSavedGroceryLists(false);
    // Set a small delay to allow the saved lists modal to close before opening the grocery list
    setTimeout(() => {
      // Here you might want to do something with the loaded list
      // For now, we'll just show the grocery list UI
      setShowGroceryList(true);
    }, 300);
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <MealPlannerCalendar
        selectedDate={selectedDate}
        onSelectDate={setSelectedDate}
        mealPlans={mealPlans}
        colors={colors}
      />
      
      <View style={[styles.topActions, { borderBottomColor: colors.border }]}>
        <TouchableOpacity
          style={[styles.manageListsButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.05)' }]}
          onPress={() => setShowSavedGroceryLists(true)}
        >
          <Feather name="bookmark" size={16} color={colors.text} style={styles.manageListsIcon} />
          <Text style={[styles.manageListsText, { color: colors.text }]}>
            {t('groceryList.manageLists')}
          </Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView 
        style={styles.content} 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 80 }}
      >
        <MealPlanSummary 
          title="mealPlan.title"
          basedOnGoals={true}
        />
        
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {selectedDate === new Date().toISOString().split('T')[0]
              ? t('mealPlanner.todayPlan')
              : t('mealPlanner.selectedDatePlan')}
          </Text>
          
          <View style={styles.headerButtons}>
            {selectedDayPlan && (
              <TouchableOpacity
                style={[styles.groceryButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}
                onPress={() => setShowGroceryList(true)}
              >
                <Feather name="shopping-cart" size={18} color={colors.primary} />
                <Text style={[styles.groceryButtonText, { color: colors.primary }]}>
                  {t('mealPlanner.groceryList')}
                </Text>
              </TouchableOpacity>
            )}
            
            <PremiumFeature
              featureKey="ai_meal_planning"
              title={t('premium.aiMealPlanningTitle')}
              description={t('premium.aiMealPlanningDesc')}
            >
              <TouchableOpacity
                style={[
                  styles.generateButton,
                  { backgroundColor: loading ? colors.border : colors.primary }
                ]}
                onPress={handleGenerateMealPlan}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <MaterialCommunityIcons name="silverware-fork-knife" size={18} color="#fff" />
                    <Text style={styles.generateButtonText}>
                      {t('mealPlanner.generatePlan')}
                    </Text>
                  </>
                )}
              </TouchableOpacity>
            </PremiumFeature>
          </View>
        </View>
        
        {selectedDayPlan ? (
          <View style={styles.planContainer}>
            <View style={styles.nutritionSummaryContainer}>
              <Text style={[styles.nutritionSummaryTitle, { color: colors.text }]}>
                {t('mealPlanner.dailySummary')}
              </Text>
              <View style={[styles.nutritionSummaryCard, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)' }]}>
                <View style={styles.nutritionSummaryRow}>
                  <Text style={[styles.nutritionSummaryLabel, { color: colors.textSecondary }]}>
                    {t('common.calories')}:
                  </Text>
                  <Text style={[styles.nutritionSummaryValue, { color: colors.text }]}>
                    {selectedDayPlan.nutritionSummary.calories} / {nutritionGoal.calories}
                  </Text>
                </View>
                <View style={styles.nutritionSummaryRow}>
                  <Text style={[styles.nutritionSummaryLabel, { color: colors.textSecondary }]}>
                    {t('common.protein')}:
                  </Text>
                  <Text style={[styles.nutritionSummaryValue, { color: colors.text }]}>
                    {selectedDayPlan.nutritionSummary.protein}g / {nutritionGoal.protein}g
                  </Text>
                </View>
                <View style={styles.nutritionSummaryRow}>
                  <Text style={[styles.nutritionSummaryLabel, { color: colors.textSecondary }]}>
                    {t('common.carbs')}:
                  </Text>
                  <Text style={[styles.nutritionSummaryValue, { color: colors.text }]}>
                    {selectedDayPlan.nutritionSummary.carbs}g / {nutritionGoal.carbs}g
                  </Text>
                </View>
                <View style={styles.nutritionSummaryRow}>
                  <Text style={[styles.nutritionSummaryLabel, { color: colors.textSecondary }]}>
                    {t('common.fat')}:
                  </Text>
                  <Text style={[styles.nutritionSummaryValue, { color: colors.text }]}>
                    {selectedDayPlan.nutritionSummary.fat}g / {nutritionGoal.fat}g
                  </Text>
                </View>
              </View>
            </View>
            
            <View style={styles.mealsContainer}>
              <Text style={[styles.mealTypeTitle, { color: colors.text }]}>
                {t('mealPlanner.breakfast')}
              </Text>
              {renderMealCard(selectedDayPlan.meals.breakfast, 'breakfast')}
              
              <Text style={[styles.mealTypeTitle, { color: colors.text }]}>
                {t('mealPlanner.lunch')}
              </Text>
              {renderMealCard(selectedDayPlan.meals.lunch, 'lunch')}
              
              <Text style={[styles.mealTypeTitle, { color: colors.text }]}>
                {t('mealPlanner.dinner')}
              </Text>
              {renderMealCard(selectedDayPlan.meals.dinner, 'dinner')}
              
              {selectedDayPlan.meals.snacks.length > 0 && (
                <>
                  <Text style={[styles.mealTypeTitle, { color: colors.text }]}>
                    {t('mealPlanner.snacks')}
                  </Text>
                  {selectedDayPlan.meals.snacks.map((snack, index) => 
                    renderMealCard(snack, 'snack', index)
                  )}
                </>
              )}
            </View>
          </View>
        ) : (
          <View style={styles.emptyState}>
            <MaterialCommunityIcons 
              name="food-variant-off" 
              size={64} 
              color={colors.textSecondary} 
            />
            <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
              {t('mealPlanner.noPlanMessage')}
            </Text>
            <Text style={[styles.emptyStateSubText, { color: colors.textSecondary }]}>
              {t('mealPlanner.noPlanSubMessage')}
            </Text>
          </View>
        )}
      </ScrollView>
      
      {/* Grocery List Modal */}
      {selectedDayPlan && showGroceryList && (
        <GroceryListGenerator 
          visible={true}
          mealPlan={[
            {
              date: selectedDate,
              meals: [
                selectedDayPlan.meals.breakfast,
                selectedDayPlan.meals.lunch,
                selectedDayPlan.meals.dinner,
                ...selectedDayPlan.meals.snacks
              ]
            }
          ]}
          onClose={() => setShowGroceryList(false)}
        />
      )}
      
      {/* Saved Grocery Lists Modal */}
      <SavedGroceryLists
        visible={showSavedGroceryLists}
        onClose={() => setShowSavedGroceryLists(false)}
        onLoadList={handleLoadSavedGroceryList}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  topActions: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  manageListsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  manageListsIcon: {
    marginRight: 6,
  },
  manageListsText: {
    fontSize: 13,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  generateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  generateButtonText: {
    color: '#fff',
    fontWeight: '600',
    marginLeft: 8,
  },
  groceryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginRight: 10,
  },
  groceryButtonText: {
    fontWeight: '600',
    marginLeft: 6,
    fontSize: 13,
  },
  planContainer: {
    marginBottom: 60,
  },
  nutritionSummaryContainer: {
    marginBottom: 20,
  },
  nutritionSummaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  nutritionSummaryCard: {
    borderRadius: 12,
    padding: 12,
  },
  nutritionSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  nutritionSummaryLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  nutritionSummaryValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  mealsContainer: {
    marginBottom: 20,
  },
  mealTypeTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  mealCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
  },
  mealTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  mealDescription: {
    fontSize: 14,
    marginBottom: 8,
  },
  nutritionContainer: {
    marginBottom: 8,
  },
  nutritionLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  nutritionValue: {
    fontSize: 14,
  },
  ingredientsContainer: {
    marginBottom: 8,
  },
  ingredientsLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  ingredientsValue: {
    fontSize: 14,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 16,
  },
  emptyStateSubText: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
  },
}); 