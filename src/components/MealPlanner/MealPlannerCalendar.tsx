import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Dimensions 
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { format, addDays, startOfWeek, endOfWeek, addWeeks, subWeeks, isToday, isSameDay } from 'date-fns';

interface MealPlannerCalendarProps {
  selectedDate: string;
  onSelectDate: (date: string) => void;
  mealPlans: {
    date: string;
    nutritionSummary: {
      calories: number;
    }
  }[];
  colors: any;
}

export default function MealPlannerCalendar({ 
  selectedDate, 
  onSelectDate,
  mealPlans,
  colors
}: MealPlannerCalendarProps) {
  // Convert string date to Date object
  const selectedDateObj = new Date(selectedDate);
  const [weekStart, setWeekStart] = useState(startOfWeek(new Date(), { weekStartsOn: 1 }));
  
  // Generate array of dates for the current week view
  const weekDates = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));
  
  // Handle day selection
  const handleSelectDay = (date: Date) => {
    onSelectDate(date.toISOString().split('T')[0]);
  };
  
  // Navigate to previous week
  const goToPreviousWeek = () => {
    setWeekStart(subWeeks(weekStart, 1));
  };
  
  // Navigate to next week
  const goToNextWeek = () => {
    setWeekStart(addWeeks(weekStart, 1));
  };
  
  // Count meals for a specific day
  const hasMealPlanForDay = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    return mealPlans.some(plan => plan.date === dateStr);
  };
  
  // Get total calories for a specific day
  const getCaloriesForDay = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    const plan = mealPlans.find(plan => plan.date === dateStr);
    return plan ? plan.nutritionSummary.calories : 0;
  };
  
  const screenWidth = Dimensions.get('window').width;
  
  return (
    <View style={styles.container}>
      {/* Calendar Header */}
      <View style={[styles.calendarHeader, { borderBottomColor: colors.border }]}>
        <TouchableOpacity onPress={goToPreviousWeek} style={styles.navigationButton}>
          <Feather name="chevron-left" size={20} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.monthText, { color: colors.text }]}>
          {format(weekStart, 'MMMM yyyy')}
        </Text>
        <TouchableOpacity onPress={goToNextWeek} style={styles.navigationButton}>
          <Feather name="chevron-right" size={20} color={colors.text} />
        </TouchableOpacity>
      </View>
      
      {/* Calendar Days */}
      <View style={styles.daysContainer}>
        {weekDates.map((date) => {
          const isSelected = isSameDay(date, selectedDateObj);
          const isCurrentDay = isToday(date);
          const hasMealPlan = hasMealPlanForDay(date);
          const dateStr = date.toISOString().split('T')[0];
          
          return (
            <TouchableOpacity
              key={dateStr}
              style={[styles.dayButton, isSelected && { borderColor: colors.primary }]}
              onPress={() => handleSelectDay(date)}
            >
              <Text style={[
                styles.dayName, 
                { color: isSelected ? colors.primary : colors.textSecondary }
              ]}>
                {format(date, 'EEE')}
              </Text>
              <View style={[
                styles.dayCircle,
                isCurrentDay && { borderColor: colors.primary },
                isSelected && { backgroundColor: colors.primary }
              ]}>
                <Text style={[
                  styles.dayNumber, 
                  { color: isSelected ? 'white' : isCurrentDay ? colors.primary : colors.text }
                ]}>
                  {format(date, 'd')}
                </Text>
              </View>
              {hasMealPlan && (
                <View style={[styles.mealIndicator, { backgroundColor: colors.primary }]} />
              )}
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  navigationButton: {
    padding: 4,
  },
  monthText: {
    fontSize: 18,
    fontWeight: '600',
  },
  daysContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
    paddingVertical: 12,
  },
  dayButton: {
    alignItems: 'center',
    width: (Dimensions.get('window').width) / 7 - 10,
    borderRadius: 8,
    paddingVertical: 8,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  dayName: {
    fontSize: 12,
    marginBottom: 4,
  },
  dayCircle: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  dayNumber: {
    fontSize: 16,
    fontWeight: '500',
  },
  mealIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginTop: 4,
  }
}); 