import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  Text,
  StyleSheet,
  FlatList,
  Keyboard,
  ActivityIndicator,
  Platform,
 useColorScheme } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { debounce } from 'lodash';

import { 
  parseFoodEntry, 
  FoodEntity, 
  getNutritionInfo, 
  FoodNutritionData 
} from '@/utils/naturalLanguageProcessing';

interface NaturalLanguageFoodInputProps {
  onFoodItemsSelected: (items: (FoodEntity & { nutritionInfo?: FoodNutritionData })[], rawText: string) => void;
  placeholder?: string;
  initialValue?: string;
  autoFocus?: boolean;
}

export function NaturalLanguageFoodInput({
  onFoodItemsSelected,
  placeholder = "Enter what you ate, e.g., '2 eggs and toast for breakfast'",
  initialValue = '',
  autoFocus = false,
}: NaturalLanguageFoodInputProps) {
  const [inputText, setInputText] = useState(initialValue);
  const [isProcessing, setIsProcessing] = useState(false);
  const [parsedItems, setParsedItems] = useState<FoodEntity[]>([]);
  const [selectedItems, setSelectedItems] = useState<FoodEntity[]>([]);
  const [suggestion, setSuggestion] = useState<string | undefined>(undefined);
  const [showResults, setShowResults] = useState(false);
  const inputRef = useRef<TextInput>(null);
  const colorScheme = useColorScheme();
  const insets = useSafeAreaInsets();
  const isDark = colorScheme === 'dark';

  // Process the input text with debounce to avoid excessive processing
  const processInput = useCallback(
    debounce(async (text: string) => {
      if (!text.trim()) {
        setParsedItems([]);
        setSuggestion(undefined);
        setShowResults(false);
        return;
      }

      setIsProcessing(true);
      try {
        const result = await parseFoodEntry(text);
        setParsedItems(result.entities);
        setSuggestion(result.suggestedCorrection);
        setShowResults(true);
      } catch (error) {
        console.error('Error parsing food entry:', error);
      } finally {
        setIsProcessing(false);
      }
    }, 500),
    []
  );

  const handleTextChange = (text: string) => {
    setInputText(text);
    processInput(text);
  };

  const handleItemSelect = (item: FoodEntity) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    // Toggle selection
    setSelectedItems(prev => {
      const isAlreadySelected = prev.some(selectedItem => 
        selectedItem.name === item.name && 
        selectedItem.quantity === item.quantity &&
        selectedItem.unit === item.unit
      );

      if (isAlreadySelected) {
        return prev.filter(selectedItem => 
          !(selectedItem.name === item.name && 
            selectedItem.quantity === item.quantity &&
            selectedItem.unit === item.unit)
        );
      } else {
        return [...prev, item];
      }
    });
  };

  const handleDone = async () => {
    Keyboard.dismiss();
    // If no items are explicitly selected, use all parsed items
    const itemsToUse = selectedItems.length > 0 ? selectedItems : parsedItems;
    
    // Enrich with nutrition info
    const enrichedItems = await Promise.all(itemsToUse.map(async (item) => {
      const nutritionInfo = await getNutritionInfo(item);
      // Only include nutritionInfo if it's not null
      return { 
        ...item, 
        nutritionInfo: nutritionInfo || undefined
      };
    }));
    
    onFoodItemsSelected(enrichedItems, inputText);
    setShowResults(false);
  };

  const handleClear = () => {
    setInputText('');
    setParsedItems([]);
    setSuggestion(undefined);
    setSelectedItems([]);
    setShowResults(false);
    inputRef.current?.focus();
  };

  const useSuggestion = () => {
    if (suggestion) {
      // Extract the actual suggestion text
      const suggestedText = suggestion.replace('Did you mean: ', '').replace('?', '');
      setInputText(suggestedText);
      processInput(suggestedText);
    }
  };

  // Render each parsed food item
  const renderFoodItem = ({ item }: { item: FoodEntity }) => {
    const isSelected = selectedItems.some(selectedItem => 
      selectedItem.name === item.name && 
      selectedItem.quantity === item.quantity &&
      selectedItem.unit === item.unit
    );
    
    const [nutritionInfo, setNutritionInfo] = useState<FoodNutritionData | null>(null);
    const [isLoadingNutrition, setIsLoadingNutrition] = useState(false);
    
    useEffect(() => {
      let isMounted = true;
      setIsLoadingNutrition(true);
      
      getNutritionInfo(item).then(data => {
        if (isMounted) {
          setNutritionInfo(data);
          setIsLoadingNutrition(false);
        }
      }).catch(err => {
        console.error('Error fetching nutrition info:', err);
        if (isMounted) setIsLoadingNutrition(false);
      });
      
      return () => { isMounted = false; };
    }, [item.name, item.quantity, item.unit]);
    
    return (
      <TouchableOpacity
        style={[
          styles.foodItem,
          isSelected && styles.selectedItem,
          { backgroundColor: isDark ? '#333' : '#f5f5f5' }
        ]}
        onPress={() => handleItemSelect(item)}
        activeOpacity={0.7}
        accessibilityRole="checkbox"
        accessibilityState={{ checked: isSelected }}
        accessibilityLabel={`${item.name}, ${item.quantity || ''} ${item.unit || ''}, confidence: ${Math.round(item.confidence * 100)}%, ${isSelected ? 'selected' : 'not selected'}`}
      >
        <View style={styles.foodItemContent}>
          <View style={styles.foodItemHeader}>
            <Text style={[styles.foodName, { color: isDark ? '#fff' : '#000' }]}>
              {item.name}
              {item.quantity && <Text> ({item.quantity} {item.unit})</Text>}
            </Text>
            {item.mealTime && (
              <Text style={[styles.mealTime, { color: isDark ? '#ccc' : '#666' }]}>
                for {item.mealTime}
              </Text>
            )}
          </View>
          
          {isLoadingNutrition ? (
            <ActivityIndicator size="small" style={{ marginTop: 4 }} />
          ) : nutritionInfo && (
            <Text style={[styles.nutritionInfo, { color: isDark ? '#bbb' : '#777' }]}>
              {nutritionInfo.calories} cal · {nutritionInfo.protein}g protein · {nutritionInfo.carbs}g carbs · {nutritionInfo.fat}g fat
            </Text>
          )}
          
          <View style={styles.confidenceBar}>
            <View 
              style={[
                styles.confidenceFill, 
                { 
                  width: `${item.confidence * 100}%`,
                  backgroundColor: item.confidence > 0.7 ? '#4CAF50' : item.confidence > 0.5 ? '#FFC107' : '#F44336'
                }
              ]} 
            />
          </View>
          
          {nutritionInfo ? (
            <Text style={styles.knownFood}>Known food</Text>
          ) : isLoadingNutrition ? null : (
            <Text style={styles.unknownFood}>Unknown food</Text>
          )}
        </View>

        <Ionicons
          name={isSelected ? 'checkmark-circle' : 'ellipse-outline'}
          size={24}
          color={isSelected ? '#4CAF50' : isDark ? '#ddd' : '#999'}
        />
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      <View style={styles.inputContainer}>
        <TextInput
          ref={inputRef}
          style={[
            styles.input,
            { color: isDark ? '#fff' : '#000', backgroundColor: isDark ? '#333' : '#f5f5f5' }
          ]}
          placeholder={placeholder}
          placeholderTextColor={isDark ? '#aaa' : '#999'}
          value={inputText}
          onChangeText={handleTextChange}
          multiline
          autoCapitalize="none"
          autoCorrect={true}
          autoFocus={autoFocus}
          returnKeyType="done"
          onSubmitEditing={() => parsedItems.length > 0 && handleDone()}
          accessibilityLabel="Natural language food input"
          accessibilityHint="Type what you ate in natural language, like '2 eggs and toast for breakfast'"
        />
        
        {inputText.length > 0 && (
          <TouchableOpacity 
            style={styles.clearButton}
            onPress={handleClear}
            accessibilityRole="button"
            accessibilityLabel="Clear input"
          >
            <Ionicons name="close-circle" size={20} color={isDark ? '#ddd' : '#999'} />
          </TouchableOpacity>
        )}
        
        {isProcessing && (
          <ActivityIndicator 
            style={styles.activityIndicator} 
            color={isDark ? '#fff' : '#000'} 
            size="small" 
          />
        )}
      </View>
      
      {suggestion && (
        <TouchableOpacity 
          style={[styles.suggestionContainer, { backgroundColor: isDark ? '#2d2d2d' : '#e9f5ff' }]}
          onPress={useSuggestion}
          accessibilityRole="button"
          accessibilityLabel="Use suggested correction"
        >
          <Text style={[styles.suggestionText, { color: isDark ? '#fff' : '#007AFF' }]}>
            {suggestion}
          </Text>
        </TouchableOpacity>
      )}
      
      {showResults && parsedItems.length > 0 && (
        <View style={[styles.resultsContainer, { backgroundColor: isDark ? '#1c1c1e' : '#fff' }]}>
          <Text style={[styles.resultsTitle, { color: isDark ? '#fff' : '#000' }]}>
            Recognized Food Items
          </Text>
          
          <Text style={[styles.resultsSubtitle, { color: isDark ? '#bbb' : '#666' }]}>
            {selectedItems.length > 0 
              ? `${selectedItems.length} selected` 
              : 'Tap items to select/deselect'}
          </Text>
          
          <FlatList
            data={parsedItems}
            renderItem={renderFoodItem}
            keyExtractor={(item, index) => `${item.name}-${index}`}
            style={styles.foodList}
            showsVerticalScrollIndicator={Platform.OS === 'web'}
            contentContainerStyle={styles.foodListContent}
          />
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton, { backgroundColor: isDark ? '#333' : '#f2f2f2' }]}
              onPress={() => setShowResults(false)}
              accessibilityRole="button"
              accessibilityLabel="Cancel"
            >
              <Text style={[styles.buttonText, styles.cancelButtonText, { color: isDark ? '#fff' : '#000' }]}>
                Cancel
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.button, styles.doneButton, { backgroundColor: '#007AFF' }]}
              onPress={handleDone}
              disabled={parsedItems.length === 0}
              accessibilityRole="button"
              accessibilityLabel="Done"
            >
              <Text style={[styles.buttonText, styles.doneButtonText, { color: '#fff' }]}>
                Done
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  input: {
    flex: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  clearButton: {
    position: 'absolute',
    right: 12,
    top: 16,
    padding: 4,
    zIndex: 2,
  },
  activityIndicator: {
    position: 'absolute',
    right: 12,
    bottom: 12,
  },
  suggestionContainer: {
    marginTop: 8,
    padding: 12,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  suggestionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  resultsContainer: {
    marginTop: 16,
    borderRadius: 16,
    overflow: 'hidden',
    maxHeight: 400,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  resultsSubtitle: {
    fontSize: 14,
    marginBottom: 8,
    paddingHorizontal: 16,
  },
  foodList: {
    maxHeight: 300,
  },
  foodListContent: {
    paddingHorizontal: 16,
  },
  foodItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    marginVertical: 4,
    borderRadius: 12,
  },
  selectedItem: {
    borderWidth: 2,
    borderColor: '#4CAF50',
  },
  foodItemContent: {
    flex: 1,
    marginRight: 8,
  },
  foodItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  foodName: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  mealTime: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  nutritionInfo: {
    fontSize: 12,
    marginTop: 4,
  },
  confidenceBar: {
    height: 4,
    backgroundColor: '#e0e0e0',
    borderRadius: 2,
    marginTop: 6,
    overflow: 'hidden',
  },
  confidenceFill: {
    height: '100%',
  },
  knownFood: {
    fontSize: 11,
    color: '#4CAF50',
    marginTop: 4,
  },
  unknownFood: {
    fontSize: 11,
    color: '#F44336',
    marginTop: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
  },
  button: {
    flex: 1,
    borderRadius: 12,
    padding: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    marginRight: 8,
  },
  doneButton: {
    marginLeft: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButtonText: {},
  doneButtonText: {
    color: '#fff',
  },
});

export default NaturalLanguageFoodInput; 