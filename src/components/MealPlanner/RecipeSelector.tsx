import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity, 
  TextInput, 
  Image,
  ActivityIndicator,
  Modal
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/contexts/TranslationContext';
import { Feather } from '@expo/vector-icons';
import { Recipe, RecipeSearchFilters, searchRecipes } from '@/services/recipeRecommendationService';

interface RecipeSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelectRecipe: (recipe: Recipe) => void;
  mealType?: 'breakfast' | 'lunch' | 'dinner' | 'snack';
}

export default function RecipeSelector({
  visible,
  onClose,
  onSelectRecipe,
  mealType
}: RecipeSelectorProps) {
  const { colors, isDark } = useTheme();
  const { t } = useTranslation();
  
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [filters, setFilters] = useState<RecipeSearchFilters>({
    mealType
  });
  const [showFilters, setShowFilters] = useState(false);
  
  useEffect(() => {
    if (visible) {
      loadRecipes();
    }
  }, [visible, filters]);
  
  const loadRecipes = async () => {
    try {
      setLoading(true);
      const searchFilters: RecipeSearchFilters = {
        ...filters,
        query: searchQuery.length > 0 ? searchQuery : undefined
      };
      
      const results = await searchRecipes(searchFilters);
      setRecipes(results);
    } catch (error) {
      console.error('Error loading recipes:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleSearch = () => {
    loadRecipes();
  };
  
  const handleSelectRecipe = (recipe: Recipe) => {
    onSelectRecipe(recipe);
    onClose();
  };
  
  const renderRecipeItem = ({ item }: { item: Recipe }) => (
    <TouchableOpacity
      style={[styles.recipeCard, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}
      onPress={() => handleSelectRecipe(item)}
      activeOpacity={0.7}
    >
      <Image
        source={{ uri: item.imageUrl }}
        style={styles.recipeImage}
        resizeMode="cover"
      />
      
      <View style={styles.recipeInfo}>
        <Text style={[styles.recipeName, { color: colors.text }]} numberOfLines={1}>
          {item.name}
        </Text>
        
        <Text style={[styles.recipeDescription, { color: colors.textSecondary }]} numberOfLines={2}>
          {item.description}
        </Text>
        
        <View style={styles.recipeMetadata}>
          <View style={styles.metadataItem}>
            <Feather name="clock" size={14} color={colors.textSecondary} style={styles.metadataIcon} />
            <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
              {item.prepTime + item.cookTime} min
            </Text>
          </View>
          
          <View style={styles.metadataItem}>
            <ChefHat size={14} color={colors.textSecondary} style={styles.metadataIcon} />
            <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
              {item.difficulty}
            </Text>
          </View>
          
          <View style={styles.calorieChip}>
            <Text style={styles.calorieText}>{item.nutritionFacts.calories} cal</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
  
  return (
    <Modal
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={[styles.header, { borderBottomColor: colors.border }]}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Feather name="x" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            {t('recipesSelector.selectRecipe')}
          </Text>
          <View style={styles.headerRight} />
        </View>
        
        <View style={[styles.searchContainer, { borderBottomColor: colors.border }]}>
          <View style={[styles.searchInputContainer, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
            <Feather name="search" size={20} color={colors.textSecondary} style={styles.searchIcon} />
            <TextInput
              style={[styles.searchInput, { color: colors.text }]}
              placeholder={t('recipesSelector.searchPlaceholder')}
              placeholderTextColor={colors.textSecondary}
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSubmitEditing={handleSearch}
              returnKeyType="search"
            />
          </View>
          
          <TouchableOpacity 
            style={[styles.filterButton, { borderColor: colors.border }]}
            onPress={() => setShowFilters(!showFilters)}
          >
            <Feather name="filter" size={20} color={colors.text} />
          </TouchableOpacity>
        </View>
        
        {mealType && (
          <View style={styles.mealTypeContainer}>
            <Text style={[styles.mealTypeLabel, { color: colors.textSecondary }]}>
              {t('common.mealType')}:
            </Text>
            <View style={[styles.mealTypeChip, { backgroundColor: colors.primary }]}>
              <Text style={styles.mealTypeChipText}>
                {t(`common.${mealType}`)}
              </Text>
            </View>
          </View>
        )}
        
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
              {t('recipesSelector.loading')}
            </Text>
          </View>
        ) : recipes.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, { color: colors.text }]}>
              {t('recipesSelector.noRecipesFound')}
            </Text>
            <Text style={[styles.emptySubText, { color: colors.textSecondary }]}>
              {t('recipesSelector.tryDifferentSearch')}
            </Text>
          </View>
        ) : (
          <FlatList
            data={recipes}
            renderItem={renderRecipeItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.recipeList}
            showsVerticalScrollIndicator={false}
            initialNumToRender={10}
            maxToRenderPerBatch={5}
            ItemSeparatorComponent={() => <View style={{ height: 12 }} />}
          />
        )}
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    height: 56,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
  },
  headerRight: {
    width: 40,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 40,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    borderWidth: 1,
  },
  mealTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  mealTypeLabel: {
    fontSize: 14,
    marginRight: 8,
  },
  mealTypeChip: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
  },
  mealTypeChipText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  emptySubText: {
    fontSize: 14,
    textAlign: 'center',
  },
  recipeList: {
    padding: 16,
  },
  recipeCard: {
    flexDirection: 'row',
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    height: 120,
  },
  recipeImage: {
    width: 120,
    height: '100%',
  },
  recipeInfo: {
    flex: 1,
    padding: 12,
    justifyContent: 'space-between',
  },
  recipeName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  recipeDescription: {
    fontSize: 13,
    lineHeight: 18,
    flex: 1,
  },
  recipeMetadata: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metadataItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  metadataIcon: {
    marginRight: 4,
  },
  metadataText: {
    fontSize: 12,
  },
  calorieChip: {
    backgroundColor: '#10B981',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 10,
    marginLeft: 'auto',
  },
  calorieText: {
    color: 'white',
    fontSize: 11,
    fontWeight: '600',
  },
}); 