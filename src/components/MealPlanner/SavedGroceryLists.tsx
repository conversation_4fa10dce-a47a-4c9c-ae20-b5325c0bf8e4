import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  FlatList, 
  Modal,
  TextInput,
  Alert
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/contexts/TranslationContext';
import { Feather } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { format } from 'date-fns';

export interface GroceryItem {
  id: string;
  name: string;
  quantity: string;
  category: string;
  checked: boolean;
}

export interface SavedGroceryList {
  id: string;
  name: string;
  dateCreated: string;
  dateRange?: string;
  items: GroceryItem[];
}

interface SavedGroceryListsProps {
  visible: boolean;
  onClose: () => void;
  onLoadList: (list: SavedGroceryList) => void;
}

export default function SavedGroceryLists({
  visible,
  onClose,
  onLoadList
}: SavedGroceryListsProps) {
  const { colors, isDark } = useTheme();
  const { t } = useTranslation();
  
  const [savedLists, setSavedLists] = useState<SavedGroceryList[]>([]);
  const [showNewListModal, setShowNewListModal] = useState(false);
  const [newListName, setNewListName] = useState('');
  const [editingList, setEditingList] = useState<SavedGroceryList | null>(null);
  
  // Load saved lists from storage
  useEffect(() => {
    if (visible) {
      loadSavedLists();
    }
  }, [visible]);
  
  const loadSavedLists = async () => {
    try {
      const storedLists = await AsyncStorage.getItem('savedGroceryLists');
      if (storedLists) {
        setSavedLists(JSON.parse(storedLists));
      }
    } catch (error) {
      console.error('Error loading saved grocery lists:', error);
    }
  };
  
  const handleSaveList = async (newList: SavedGroceryList) => {
    try {
      // If editing, remove the old version first
      let updatedLists = [...savedLists];
      if (editingList) {
        updatedLists = updatedLists.filter(list => list.id !== editingList.id);
      }
      
      // Add the new/updated list
      updatedLists = [...updatedLists, newList];
      
      // Save to storage
      await AsyncStorage.setItem('savedGroceryLists', JSON.stringify(updatedLists));
      
      // Update state
      setSavedLists(updatedLists);
      setShowNewListModal(false);
      setNewListName('');
      setEditingList(null);
    } catch (error) {
      console.error('Error saving grocery list:', error);
      Alert.alert(
        t('groceryList.saveError'),
        t('groceryList.saveErrorMessage')
      );
    }
  };
  
  const handleDeleteList = async (listId: string) => {
    try {
      const updatedLists = savedLists.filter(list => list.id !== listId);
      await AsyncStorage.setItem('savedGroceryLists', JSON.stringify(updatedLists));
      setSavedLists(updatedLists);
    } catch (error) {
      console.error('Error deleting grocery list:', error);
      Alert.alert(
        t('groceryList.deleteError'),
        t('groceryList.deleteErrorMessage')
      );
    }
  };
  
  const confirmDeleteList = (list: SavedGroceryList) => {
    Alert.alert(
      t('groceryList.confirmDelete'),
      t('groceryList.confirmDeleteMessage', { name: list.name }),
      [
        {
          text: t('common.cancel'),
          style: 'cancel'
        },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: () => handleDeleteList(list.id)
        }
      ]
    );
  };
  
  const handleEditList = (list: SavedGroceryList) => {
    setEditingList(list);
    setNewListName(list.name);
    setShowNewListModal(true);
  };
  
  const handleCreateNewList = () => {
    setEditingList(null);
    setNewListName('');
    setShowNewListModal(true);
  };
  
  const renderListItem = ({ item }: { item: SavedGroceryList }) => {
    const itemCount = item.items.length;
    const uncheckedCount = item.items.filter(i => !i.checked).length;
    
    return (
      <View 
        style={[
          styles.listCard, 
          { 
            backgroundColor: isDark ? colors.card : 'white',
            borderColor: colors.border
          }
        ]}
      >
        <View style={styles.listCardHeader}>
          <Text style={[styles.listName, { color: colors.text }]}>{item.name}</Text>
          <Text style={[styles.listDate, { color: colors.textSecondary }]}>
            {format(new Date(item.dateCreated), 'MMM d, yyyy')}
          </Text>
        </View>
        
        <View style={styles.listCardBody}>
          {item.dateRange && (
            <View style={styles.dateRangeContainer}>
              <Feather name="calendar" size={14} color={colors.textSecondary} />
              <Text style={[styles.dateRangeText, { color: colors.textSecondary }]}>
                {item.dateRange}
              </Text>
            </View>
          )}
          
          <Text style={[styles.itemCountText, { color: colors.textSecondary }]}>
            {t('groceryList.itemCount', { count: itemCount })} • 
            {uncheckedCount > 0 
              ? t('groceryList.remainingItems', { count: uncheckedCount })
              : t('groceryList.allItemsChecked')}
          </Text>
        </View>
        
        <View style={styles.listCardActions}>
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: colors.primary }]}
            onPress={() => onLoadList(item)}
          >
            <Text style={styles.actionButtonText}>{t('groceryList.load')}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => handleEditList(item)}
          >
            <Feather name="edit" size={20} color={colors.text} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => confirmDeleteList(item)}
          >
            <Feather name="trash-2" size={20} color={colors.error || '#ff6b6b'} />
          </TouchableOpacity>
        </View>
      </View>
    );
  };
  
  return (
    <Modal
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={[styles.header, { borderBottomColor: colors.border }]}>
          <View style={styles.headerTitleContainer}>
            <Text style={[styles.headerTitle, { color: colors.text }]}>
              {t('groceryList.savedLists')}
            </Text>
          </View>
          
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Feather name="x" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>
        
        {savedLists.length > 0 ? (
          <FlatList
            data={savedLists}
            renderItem={renderListItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View style={styles.emptyState}>
            <Feather name="shopping-cart" size={64} color={colors.textSecondary} />
            <Text style={[styles.emptyStateText, { color: colors.text }]}>
              {t('groceryList.noSavedLists')}
            </Text>
            <Text style={[styles.emptyStateSubText, { color: colors.textSecondary }]}>
              {t('groceryList.saveListsHint')}
            </Text>
          </View>
        )}
        
        <TouchableOpacity
          style={[styles.fabButton, { backgroundColor: colors.primary }]}
          onPress={handleCreateNewList}
        >
          <Feather name="plus" size={24}  color={colors.text} />
        </TouchableOpacity>
        
        {/* New/Edit List Modal */}
        <Modal
          visible={showNewListModal}
          transparent
          animationType="fade"
          onRequestClose={() => setShowNewListModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                {editingList ? t('groceryList.editList') : t('groceryList.newList')}
              </Text>
              
              <Text style={[styles.inputLabel, { color: colors.text }]}>
                {t('groceryList.listName')}
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  { 
                    backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                    color: colors.text,
                    borderColor: colors.border
                  }
                ]}
                value={newListName}
                onChangeText={setNewListName}
                placeholder={t('groceryList.listNamePlaceholder')}
                placeholderTextColor={colors.textSecondary}
              />
              
              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={[styles.modalButton, { backgroundColor: isDark ? '#333' : '#eee' }]}
                  onPress={() => setShowNewListModal(false)}
                >
                  <Text style={[styles.modalButtonText, { color: colors.text }]}>
                    {t('common.cancel')}
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[
                    styles.modalButton, 
                    { 
                      backgroundColor: colors.primary,
                      opacity: !newListName.trim() ? 0.6 : 1
                    }
                  ]}
                  onPress={() => {
                    if (!newListName.trim()) return;
                    
                    const list: SavedGroceryList = editingList ? {
                      ...editingList,
                      name: newListName.trim()
                    } : {
                      id: Date.now().toString(),
                      name: newListName.trim(),
                      dateCreated: new Date().toISOString(),
                      items: []
                    };
                    
                    handleSaveList(list);
                  }}
                  disabled={!newListName.trim()}
                >
                  <Text style={styles.modalButtonText}>
                    {editingList ? t('common.save') : t('common.create')}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerTitleContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 4,
  },
  listContainer: {
    padding: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 16,
  },
  emptyStateSubText: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
    marginHorizontal: 40,
  },
  listCard: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
    padding: 16,
  },
  listCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  listName: {
    fontSize: 16,
    fontWeight: '600',
  },
  listDate: {
    fontSize: 12,
  },
  listCardBody: {
    marginBottom: 16,
  },
  dateRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  dateRangeText: {
    fontSize: 12,
    marginLeft: 4,
  },
  itemCountText: {
    fontSize: 13,
  },
  listCardActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  actionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginRight: 12,
  },
  actionButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  iconButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  fabButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    borderRadius: 12,
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  textInput: {
    height: 50,
    borderRadius: 8,
    paddingHorizontal: 16,
    marginBottom: 20,
    borderWidth: 1,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    padding: 14,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  modalButtonText: {
    fontWeight: '600',
    color: 'white',
  },
}); 