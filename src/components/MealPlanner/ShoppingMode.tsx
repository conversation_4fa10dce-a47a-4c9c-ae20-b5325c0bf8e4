import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  SectionList,
  ScrollView,
  StatusBar,
  SafeAreaView,
  Animated,
  Dimensions
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/contexts/TranslationContext';
import { Feather } from '@expo/vector-icons';
import { GroceryItem } from './SavedGroceryLists';

interface ShoppingModeProps {
  visible: boolean;
  groceryList: GroceryItem[];
  onToggleItem: (id: string) => void;
  onClose: () => void;
  onComplete: () => void;
}

const { width, height } = Dimensions.get('window');

export default function ShoppingMode({
  visible,
  groceryList,
  onToggleItem,
  onClose,
  onComplete
}: ShoppingModeProps) {
  const { colors, isDark } = useTheme();
  const { t } = useTranslation();
  
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const scrollRef = useRef<ScrollView>(null);
  const sectionListRef = useRef<SectionList>(null);
  const progressAnim = useRef(new Animated.Value(0)).current;
  
  // Group items by category
  const itemsByCategory = groceryList.reduce<Record<string, GroceryItem[]>>((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = [];
    }
    acc[item.category].push(item);
    return acc;
  }, {});
  
  // Create sections for SectionList
  const sections = Object.entries(itemsByCategory).map(([category, items]) => ({
    title: category,
    data: items,
  }));
  
  // Set initial active category
  useEffect(() => {
    if (sections.length > 0 && !activeCategory) {
      setActiveCategory(sections[0].title);
    }
  }, [sections, activeCategory]);
  
  // Calculate progress
  const totalItems = groceryList.length;
  const checkedItems = groceryList.filter(item => item.checked).length;
  const progress = totalItems > 0 ? checkedItems / totalItems : 0;
  
  // Animate progress bar
  useEffect(() => {
    Animated.timing(progressAnim, {
      toValue: progress,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [progress, progressAnim]);
  
  // Scroll to category
  const scrollToCategory = (category: string) => {
    const sectionIndex = sections.findIndex(section => section.title === category);
    if (sectionIndex !== -1 && sectionListRef.current) {
      sectionListRef.current.scrollToLocation({
        sectionIndex,
        itemIndex: 0,
        animated: true,
        viewOffset: 0,
      });
      setActiveCategory(category);
    }
  };
  
  // Render category tabs
  const renderCategoryTabs = () => (
    <ScrollView
      ref={scrollRef}
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.categoriesContainer}
      contentContainerStyle={styles.categoriesContent}
    >
      {sections.map((section) => (
        <TouchableOpacity
          key={section.title}
          style={[
            styles.categoryTab,
            activeCategory === section.title && { backgroundColor: colors.primary },
          ]}
          onPress={() => scrollToCategory(section.title)}
        >
          <Text
            style={[
              styles.categoryTabText,
              {
                color:
                  activeCategory === section.title ? 'white' : colors.text,
              },
            ]}
          >
            {section.title}
          </Text>
          <View style={styles.categoryCount}>
            <Text style={styles.categoryCountText}>
              {itemsByCategory[section.title].filter(i => !i.checked).length}
            </Text>
          </View>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
  
  // Render section header
  const renderSectionHeader = ({ section }: any) => (
    <View
      style={[
        styles.sectionHeader,
        { backgroundColor: isDark ? colors.background : '#f0f0f0' },
      ]}
    >
      <Text style={[styles.sectionHeaderText, { color: colors.text }]}>
        {section.title}
      </Text>
    </View>
  );
  
  // Render item
  const renderItem = ({ item }: { item: GroceryItem }) => (
    <TouchableOpacity
      style={[
        styles.itemRow,
        {
          backgroundColor: isDark
            ? item.checked
              ? 'rgba(255,255,255,0.05)'
              : colors.card
            : item.checked
            ? 'rgba(0,0,0,0.05)'
            : 'white',
        },
      ]}
      onPress={() => onToggleItem(item.id)}
      activeOpacity={0.7}
    >
      <View
        style={[
          styles.checkbox,
          {
            borderColor: item.checked ? colors.primary : colors.border,
            backgroundColor: item.checked ? colors.primary : 'transparent',
          },
        ]}
      >
        {item.checked && <Feather name="check" size={18}  color={colors.text} />}
      </View>
      
      <View style={styles.itemContent}>
        <Text
          style={[
            styles.itemName,
            {
              color: colors.text,
              textDecorationLine: item.checked ? 'line-through' : 'none',
              opacity: item.checked ? 0.6 : 1,
            },
          ]}
        >
          {item.name}
        </Text>
        <Text
          style={[
            styles.itemQuantity,
            {
              color: colors.textSecondary,
              opacity: item.checked ? 0.6 : 1,
            },
          ]}
        >
          {item.quantity}
        </Text>
      </View>
    </TouchableOpacity>
  );
  
  // Handle section change on scroll
  const onViewableItemsChanged = useRef(({ viewableItems }: any) => {
    if (viewableItems.length > 0) {
      const firstItem = viewableItems[0];
      if (firstItem.section && firstItem.section.title !== activeCategory) {
        setActiveCategory(firstItem.section.title);
        
        // Scroll category tab into view
        if (scrollRef.current) {
          const tabs = sections.map(s => s.title);
          const index = tabs.indexOf(firstItem.section.title);
          if (index !== -1) {
            scrollRef.current.scrollTo({
              x: index * 100 - 50, // approximate width of tab
              animated: true,
            });
          }
        }
      }
    }
  }).current;
  
  const viewabilityConfigCallbackPairs = useRef([
    { viewabilityConfig: { itemVisiblePercentThreshold: 50 }, onViewableItemsChanged },
  ]).current;
  
  // Calculate if shopping is complete
  const isShoppingComplete = checkedItems === totalItems && totalItems > 0;
  
  return (
    visible && (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <StatusBar barStyle={isDark ? 'light-content' : 'dark-content'} />
        
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: colors.border }]}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Feather name="chevron-left" size={24} color={colors.text} />
          </TouchableOpacity>
          
          <View style={styles.headerCenter}>
            <Text style={[styles.headerTitle, { color: colors.text }]}>
              {t('shoppingMode.title')}
            </Text>
            <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
              {checkedItems}/{totalItems} {t('shoppingMode.itemsCollected')}
            </Text>
          </View>
          
          <TouchableOpacity 
            style={[
              styles.doneButton, 
              { opacity: isShoppingComplete ? 1 : 0.5 }
            ]} 
            onPress={onComplete}
            disabled={!isShoppingComplete}
          >
            <Text style={[styles.doneButtonText, { color: colors.primary }]}>
              {t('shoppingMode.done')}
            </Text>
          </TouchableOpacity>
        </View>
        
        {/* Progress Bar */}
        <View style={[styles.progressContainer, { backgroundColor: isDark ? '#333' : '#eee' }]}>
          <Animated.View
            style={[
              styles.progressBar,
              {
                backgroundColor: colors.primary,
                width: progressAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0%', '100%'],
                }),
              },
            ]}
          />
        </View>
        
        {/* Category Tabs */}
        {renderCategoryTabs()}
        
        {/* Main List */}
        {groceryList.length > 0 ? (
          <SectionList
            ref={sectionListRef}
            sections={sections}
            keyExtractor={(item) => item.id}
            renderItem={renderItem}
            renderSectionHeader={renderSectionHeader}
            stickySectionHeadersEnabled={true}
            viewabilityConfigCallbackPairs={viewabilityConfigCallbackPairs}
            style={styles.list}
            contentContainerStyle={styles.listContent}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <Feather name="shopping-cart" size={64} color={colors.textSecondary} />
            <Text style={[styles.emptyText, { color: colors.text }]}>
              {t('shoppingMode.noItems')}
            </Text>
          </View>
        )}
        
        {/* Floating complete button for easy access */}
        {isShoppingComplete && (
          <TouchableOpacity 
            style={[styles.completeButton, { backgroundColor: colors.primary }]}
            onPress={onComplete}
          >
            <Feather name="check" size={24}  color={colors.text} />
          </TouchableOpacity>
        )}
      </SafeAreaView>
    )
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 999,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  closeButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  headerSubtitle: {
    fontSize: 12,
    marginTop: 2,
  },
  doneButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
  },
  doneButtonText: {
    fontWeight: '600',
    fontSize: 14,
  },
  progressContainer: {
    height: 4,
    width: '100%',
  },
  progressBar: {
    height: '100%',
  },
  categoriesContainer: {
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  categoriesContent: {
    paddingHorizontal: 8,
  },
  categoryTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginHorizontal: 4,
    borderRadius: 20,
  },
  categoryTabText: {
    fontSize: 14,
    fontWeight: '500',
  },
  categoryCount: {
    marginLeft: 4,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: '#ff6b6b',
    alignItems: 'center',
    justifyContent: 'center',
  },
  categoryCountText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  list: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 80,
  },
  sectionHeader: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  sectionHeaderText: {
    fontSize: 16,
    fontWeight: '600',
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  checkbox: {
    width: 28,
    height: 28,
    borderRadius: 14,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  itemContent: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  itemQuantity: {
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    marginTop: 16,
  },
  completeButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
}); 