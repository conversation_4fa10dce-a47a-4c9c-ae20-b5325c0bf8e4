import React from 'react';
import { StyleSheet, Text, View, Image, TouchableOpacity, Platform, Animated } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { getMealColor, formatTimeForDisplay } from '@/utils/mealUtils';
import { canUseNativeDriver } from '@/utils/platformUtils';

interface MealProps {
  id: string;
  name: string;
  time: string;
  calories: number;
  image: string;
}

interface MealSummaryCardProps {
  meal: MealProps;
  onPress?: () => void;
}

// Helper function to get a clean meal type for the badge
function getMealTypeLabel(mealName: string): string {
  const mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'];
  const lowerName = mealName.toLowerCase();
  
  for (const type of mealTypes) {
    if (lowerName.includes(type)) {
      return type.toUpperCase();
    }
  }
  
  return 'MEAL';
}

// Helper function to format the meal name for display
function formatMealName(mealName: string): string {
  // Capitalize first letter of each word and clean up meal type labels
  return mealName
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

export function MealSummaryCard({ meal, onPress }: MealSummaryCardProps) {
  const { colors } = useTheme();
  const [pressed, setPressed] = React.useState(false);
  const scaleAnim = React.useRef(new Animated.Value(1)).current;
  
  const handlePressIn = () => {
    setPressed(true);
    Animated.spring(scaleAnim, {
      toValue: 0.97,
      speed: 20,
      useNativeDriver: canUseNativeDriver(),
    }).start();
  };
  
  const handlePressOut = () => {
    setPressed(false);
    Animated.spring(scaleAnim, {
      toValue: 1,
      speed: 20,
      useNativeDriver: canUseNativeDriver(),
    }).start();
  };
  
  // Format the time for display
  const displayTime = formatTimeForDisplay(meal.time);
  
  // Get the appropriate badge color based on meal name
  const badgeColor = getMealColor(meal.name);
  
  return (
    <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
      <TouchableOpacity 
        style={[
          styles.card, 
          { 
            backgroundColor: colors.card,
            shadowColor: colors.shadow,
            borderColor: colors.border,
          },
          pressed && styles.cardPressed
        ]}
        activeOpacity={0.9}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onPress={onPress}
        accessibilityLabel={`Meal: ${meal.name}`}
        accessibilityHint="View meal details"
      >
        <View style={styles.imageContainer}>
          <Image source={{ uri: meal.image }} style={styles.image} resizeMode="cover" />
          <View style={[styles.imageBadge, { backgroundColor: badgeColor }]}>
            <Text style={styles.imageBadgeText}>{getMealTypeLabel(meal.name)}</Text>
          </View>
        </View>
        
        <View style={styles.content}>
          <View style={styles.textContainer}>
            <Text style={[styles.name, { color: colors.text }]}>{formatMealName(meal.name)}</Text>
            <View style={styles.timeContainer}>
              <Feather name="clock" size={14} color={colors.textSecondary} style={styles.timeIcon} />
              <Text style={[styles.time, { color: colors.textSecondary }]}>{displayTime}</Text>
            </View>
          </View>
          <View style={styles.rightContent}>
            <View style={[styles.calorieContainer, { backgroundColor: colors.primaryLight }]}>
              <Text style={[styles.calories, { color: colors.primary }]}>{meal.calories}</Text>
              <Text style={[styles.calorieLabel, { color: colors.primary }]}>cal</Text>
            </View>
            <Feather name="chevron-right" size={16} color={colors.textSecondary} style={styles.chevron} />
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    borderRadius: 16,
    marginBottom: 16,
    overflow: 'hidden',
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 6,
      },
      android: {
        elevation: 3,
      },
      web: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 6,
      }
    }),
  },
  cardPressed: {
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: {
          width: 0,
          height: 1,
        },
        shadowOpacity: 0.05,
        shadowRadius: 2,
      },
      android: {
        elevation: 1,
      },
      web: {
        shadowOffset: {
          width: 0,
          height: 1,
        },
        shadowOpacity: 0.05,
        shadowRadius: 2,
      }
    }),
  },
  imageContainer: {
    width: 110,
    height: 110,
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imageBadge: {
    position: 'absolute',
    top: 8,
    left: 0,
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderTopRightRadius: 10,
    borderBottomRightRadius: 10,
  },
  imageBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '700',
    textTransform: 'uppercase',
  },
  content: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 12,
  },
  textContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  name: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 6,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeIcon: {
    marginRight: 4,
  },
  time: {
    fontSize: 14,
    fontWeight: '500',
  },
  rightContent: {
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  calorieContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 12,
    marginBottom: 12,
  },
  calories: {
    fontSize: 20,
    fontWeight: '800',
  },
  calorieLabel: {
    fontSize: 12,
    fontWeight: '600',
  },
  chevron: {
    opacity: 0.7,
  },
});