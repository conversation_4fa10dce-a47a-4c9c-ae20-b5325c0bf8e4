import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, ActivityIndicator, Dimensions } from 'react-native';
import { format, addDays, subDays, eachDayOfInterval, startOfWeek, endOfWeek } from 'date-fns';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/contexts/TranslationContext';
import { getMealsForDate, MealData } from '@/services/dietitianService';
import { LineChart } from 'react-native-chart-kit';
import { Feather } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

interface MealTimelineProps {
  onDateSelect?: (date: string) => void;
  initialDateRange?: {
    startDate: string;
    endDate: string;
  };
}

export function MealTimeline({ 
  onDateSelect, 
  initialDateRange 
}: MealTimelineProps) {
  const { colors, isDark } = useTheme();
  const { t } = useTranslation();
  
  const today = new Date();
  const [selectedDate, setSelectedDate] = useState(format(today, 'yyyy-MM-dd'));
  const [dateRange, setDateRange] = useState({
    startDate: initialDateRange?.startDate || format(startOfWeek(today), 'yyyy-MM-dd'),
    endDate: initialDateRange?.endDate || format(endOfWeek(today), 'yyyy-MM-dd')
  });
  const [timelineData, setTimelineData] = useState<{
    dates: string[];
    calories: number[];
    protein: number[];
    carbs: number[];
    fat: number[];
  }>({
    dates: [],
    calories: [],
    protein: [],
    carbs: [],
    fat: []
  });
  const [loading, setLoading] = useState(false);
  const [activeChart, setActiveChart] = useState<'calories' | 'macros'>('calories');
  
  useEffect(() => {
    fetchTimelineData();
  }, [dateRange]);
  
  const fetchTimelineData = async () => {
    setLoading(true);
    
    try {
      // Generate array of dates in the range
      const start = new Date(dateRange.startDate);
      const end = new Date(dateRange.endDate);
      
      const dateArray = eachDayOfInterval({ start, end });
      const formattedDates = dateArray.map(date => format(date, 'yyyy-MM-dd'));
      
      // Initialize data arrays
      const caloriesData: number[] = [];
      const proteinData: number[] = [];
      const carbsData: number[] = [];
      const fatData: number[] = [];
      
      // Fetch meal data for each date
      for (const date of formattedDates) {
        const meals = await getMealsForDate(date);
        
        // Calculate totals for the day
        const dayTotals = meals.reduce(
          (totals, meal) => ({
            calories: totals.calories + meal.nutrition.calories,
            protein: totals.protein + meal.nutrition.protein,
            carbs: totals.carbs + meal.nutrition.carbs,
            fat: totals.fat + meal.nutrition.fat
          }),
          { calories: 0, protein: 0, carbs: 0, fat: 0 }
        );
        
        // Add to arrays
        caloriesData.push(dayTotals.calories);
        proteinData.push(dayTotals.protein);
        carbsData.push(dayTotals.carbs);
        fatData.push(dayTotals.fat);
      }
      
      // Update state with the fetched data
      setTimelineData({
        dates: formattedDates,
        calories: caloriesData,
        protein: proteinData,
        carbs: carbsData,
        fat: fatData
      });
    } catch (error) {
      console.error('Error fetching timeline data:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handlePreviousWeek = () => {
    const start = new Date(dateRange.startDate);
    const end = new Date(dateRange.endDate);
    
    setDateRange({
      startDate: format(subDays(start, 7), 'yyyy-MM-dd'),
      endDate: format(subDays(end, 7), 'yyyy-MM-dd')
    });
  };
  
  const handleNextWeek = () => {
    const start = new Date(dateRange.startDate);
    const end = new Date(dateRange.endDate);
    
    // Don't allow selecting future dates beyond today
    if (new Date(format(addDays(end, 7), 'yyyy-MM-dd')) <= today) {
      setDateRange({
        startDate: format(addDays(start, 7), 'yyyy-MM-dd'),
        endDate: format(addDays(end, 7), 'yyyy-MM-dd')
      });
    }
  };
  
  const handleDateSelect = (date: string) => {
    setSelectedDate(date);
    if (onDateSelect) {
      onDateSelect(date);
    }
  };
  
  // Format dates for display on the chart
  const chartLabels = timelineData.dates.map(date => {
    const d = new Date(date);
    return format(d, 'd MMM');
  });
  
  // Set up chart data for calories
  const caloriesChartData = {
    labels: chartLabels,
    datasets: [
      {
        data: timelineData.calories.length ? timelineData.calories : [0],
        color: () => '#10B981', // Assuming green color for calories
        strokeWidth: 2
      }
    ],
    legend: ["Calories"]
  };
  
  // Set up chart data for macros
  const macrosChartData = {
    labels: chartLabels,
    datasets: [
      {
        data: timelineData.protein.length ? timelineData.protein : [0],
        color: () => '#2563EB', // Blue for protein
        strokeWidth: 2
      },
      {
        data: timelineData.carbs.length ? timelineData.carbs : [0],
        color: () => '#F59E0B', // Yellow for carbs
        strokeWidth: 2
      },
      {
        data: timelineData.fat.length ? timelineData.fat : [0],
        color: () => '#EF4444', // Red for fat
        strokeWidth: 2
      }
    ],
    legend: ["Protein", "Carbs", "Fat"]
  };
  
  const chartConfig = {
    backgroundGradientFrom: isDark ? '#121212' : '#FFFFFF',
    backgroundGradientTo: isDark ? '#121212' : '#FFFFFF',
    decimalPlaces: 0,
    color: () => isDark ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)',
    labelColor: () => isDark ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)',
    style: {
      borderRadius: 16
    },
    propsForDots: {
      r: "4",
    }
  };
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.text }]}>
          Loading data...
        </Text>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      {/* Date Range Selector */}
      <View style={styles.dateSelector}>
        <TouchableOpacity onPress={handlePreviousWeek} style={styles.dateButton}>
          <Feather name="chevron-left" size={20} color={colors.text} />
        </TouchableOpacity>
        
        <View style={styles.dateContainer}>
          <Text style={[styles.dateText, { color: colors.text }]}>
            {format(new Date(dateRange.startDate), 'MMM d')} - {format(new Date(dateRange.endDate), 'MMM d, yyyy')}
          </Text>
        </View>
        
        <TouchableOpacity 
          onPress={handleNextWeek} 
          style={[styles.dateButton, { opacity: format(new Date(dateRange.endDate), 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd') ? 0.5 : 1 }]}
          disabled={format(new Date(dateRange.endDate), 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd')}
        >
          <Feather name="chevron-right" size={20} color={colors.text} />
        </TouchableOpacity>
      </View>
      
      {/* Chart Type Selector */}
      <View style={[styles.chartTypeSelector, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)' }]}>
        <TouchableOpacity 
          style={[
            styles.chartTypeButton, 
            activeChart === 'calories' && { 
              backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.08)',
              borderColor: colors.primary
            }
          ]}
          onPress={() => setActiveChart('calories')}
        >
          <Text style={[
            styles.chartTypeText, 
            { color: colors.text },
            activeChart === 'calories' && { color: colors.primary }
          ]}>
            Calories
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[
            styles.chartTypeButton, 
            activeChart === 'macros' && { 
              backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.08)',
              borderColor: colors.primary
            }
          ]}
          onPress={() => setActiveChart('macros')}
        >
          <Text style={[
            styles.chartTypeText, 
            { color: colors.text },
            activeChart === 'macros' && { color: colors.primary }
          ]}>
            Macros
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* Chart */}
      <View style={[styles.chartContainer, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)' }]}>
        {timelineData.dates.length > 0 ? (
          <LineChart
            data={activeChart === 'calories' ? caloriesChartData : macrosChartData}
            width={width - 40}
            height={220}
            chartConfig={chartConfig}
            bezier
            style={styles.chart}
          />
        ) : (
          <View style={styles.noDataContainer}>
            <Text style={[styles.noDataText, { color: colors.textSecondary }]}>
              No data available for this time period
            </Text>
          </View>
        )}
      </View>
      
      {/* Date Selector */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.daySelector}>
        {timelineData.dates.map((date, index) => {
          const isSelected = date === selectedDate;
          const dateObj = new Date(date);
          const isToday = format(dateObj, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd');
          
          return (
            <TouchableOpacity
              key={date}
              style={[
                styles.dayButton,
                { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)' },
                isSelected && { backgroundColor: colors.primary }
              ]}
              onPress={() => handleDateSelect(date)}
            >
              <Text style={[
                styles.dayName,
                { color: colors.textSecondary },
                isSelected && { color: 'white' }
              ]}>
                {format(dateObj, 'EEE')}
              </Text>
              <Text style={[
                styles.dayNumber,
                { color: colors.text },
                isSelected && { color: 'white' }
              ]}>
                {format(dateObj, 'd')}
              </Text>
              {isToday && (
                <View style={[
                  styles.todayIndicator,
                  { backgroundColor: isSelected ? 'white' : colors.primary }
                ]} />
              )}
            </TouchableOpacity>
          );
        })}
      </ScrollView>
      
      {/* Daily Nutrition Summary */}
      {timelineData.dates.length > 0 && (
        <View style={[styles.summaryContainer, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)' }]}>
          <Text style={[styles.summaryTitle, { color: colors.text }]}>
            {format(new Date(selectedDate), 'MMMM d, yyyy')}
          </Text>
          
          <View style={styles.summaryRow}>
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                Calories
              </Text>
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {timelineData.calories[timelineData.dates.indexOf(selectedDate)] || 0}
              </Text>
            </View>
            
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                Protein
              </Text>
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {timelineData.protein[timelineData.dates.indexOf(selectedDate)] || 0}g
              </Text>
            </View>
            
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                Carbs
              </Text>
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {timelineData.carbs[timelineData.dates.indexOf(selectedDate)] || 0}g
              </Text>
            </View>
            
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                Fat
              </Text>
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {timelineData.fat[timelineData.dates.indexOf(selectedDate)] || 0}g
              </Text>
            </View>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  dateSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  dateButton: {
    padding: 10,
  },
  dateContainer: {
    flex: 1,
    alignItems: 'center',
  },
  dateText: {
    fontSize: 16,
    fontWeight: '600',
  },
  chartTypeSelector: {
    flexDirection: 'row',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
  },
  chartTypeButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    margin: 4,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  chartTypeText: {
    fontSize: 14,
    fontWeight: '500',
  },
  chartContainer: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  daySelector: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  dayButton: {
    width: 60,
    height: 70,
    borderRadius: 12,
    marginRight: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dayName: {
    fontSize: 12,
    marginBottom: 4,
  },
  dayNumber: {
    fontSize: 18,
    fontWeight: '600',
  },
  todayIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginTop: 4,
  },
  summaryContainer: {
    borderRadius: 16,
    padding: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
  },
  noDataContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  noDataText: {
    fontSize: 14,
    textAlign: 'center',
  },
}); 