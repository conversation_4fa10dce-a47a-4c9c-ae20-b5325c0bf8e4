import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  ActivityIndicator 
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';

interface MicronutrientData {
  name: string;
  value: number;
  unit: string;
  target: number;
  category: 'minerals' | 'vitamins' | 'fiber' | 'other';
  isEssential: boolean;
}

interface MicronutrientTrackingComponentProps {
  date?: string; // ISO date string, defaults to today
  onInfoPress?: (nutrient: MicronutrientData) => void;
}

export function MicronutrientTrackingComponent({ 
  date, 
  onInfoPress 
}: MicronutrientTrackingComponentProps) {
  const { colors, isDark } = useTheme();
  const [loading, setLoading] = useState(true);
  const [micronutrients, setMicronutrients] = useState<MicronutrientData[]>([]);
  const [activeCategory, setActiveCategory] = useState<'all' | 'minerals' | 'vitamins' | 'fiber' | 'other'>('all');

  useEffect(() => {
    loadMicronutrientData();
  }, [date]);

  const loadMicronutrientData = async () => {
    setLoading(true);
    try {
      // In a real app, fetch from API or database
      // For demo, using mock data
      setTimeout(() => {
        setMicronutrients(getMockMicronutrientData());
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Failed to load micronutrient data', error);
      setLoading(false);
    }
  };

  const getMockMicronutrientData = (): MicronutrientData[] => {
    return [
      // Minerals
      { name: 'Iron', value: 10.5, unit: 'mg', target: 18, category: 'minerals', isEssential: true },
      { name: 'Calcium', value: 765, unit: 'mg', target: 1000, category: 'minerals', isEssential: true },
      { name: 'Potassium', value: 2800, unit: 'mg', target: 3500, category: 'minerals', isEssential: true },
      { name: 'Magnesium', value: 320, unit: 'mg', target: 400, category: 'minerals', isEssential: true },
      { name: 'Zinc', value: 8, unit: 'mg', target: 11, category: 'minerals', isEssential: true },
      { name: 'Selenium', value: 40, unit: 'μg', target: 55, category: 'minerals', isEssential: false },
      
      // Vitamins
      { name: 'Vitamin A', value: 750, unit: 'μg', target: 900, category: 'vitamins', isEssential: true },
      { name: 'Vitamin C', value: 65, unit: 'mg', target: 90, category: 'vitamins', isEssential: true },
      { name: 'Vitamin D', value: 15, unit: 'μg', target: 20, category: 'vitamins', isEssential: true },
      { name: 'Vitamin E', value: 12, unit: 'mg', target: 15, category: 'vitamins', isEssential: true },
      { name: 'Vitamin B12', value: 3.5, unit: 'μg', target: 2.4, category: 'vitamins', isEssential: true },
      { name: 'Folate', value: 320, unit: 'μg', target: 400, category: 'vitamins', isEssential: true },
      
      // Fiber
      { name: 'Soluble Fiber', value: 8, unit: 'g', target: 10, category: 'fiber', isEssential: true },
      { name: 'Insoluble Fiber', value: 12, unit: 'g', target: 15, category: 'fiber', isEssential: true },
      
      // Other
      { name: 'Omega-3', value: 1.2, unit: 'g', target: 1.6, category: 'other', isEssential: true },
      { name: 'Omega-6', value: 11, unit: 'g', target: 12, category: 'other', isEssential: false },
      { name: 'Choline', value: 300, unit: 'mg', target: 425, category: 'other', isEssential: false },
    ];
  };

  const getFilteredNutrients = () => {
    if (activeCategory === 'all') {
      return micronutrients;
    }
    return micronutrients.filter(nutrient => nutrient.category === activeCategory);
  };

  const calculateProgress = (value: number, target: number) => {
    const progress = (value / target) * 100;
    return Math.min(progress, 100); // Cap at 100%
  };

  const getProgressColor = (progress: number) => {
    if (progress < 50) {
      return colors.danger;
    }
    if (progress < 80) {
      return colors.warning;
    }
    return colors.success;
  };

  const getFormattedDate = () => {
    if (!date) {
      return 'Today';
    }
    
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'short',
      day: 'numeric'
    });
  };

  const renderCategoryTabs = () => {
    const categories = [
      { id: 'all', label: 'All' },
      { id: 'minerals', label: 'Minerals' },
      { id: 'vitamins', label: 'Vitamins' },
      { id: 'fiber', label: 'Fiber' },
      { id: 'other', label: 'Other' }
    ];

    return (
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.tabsContainer}
      >
        {categories.map(category => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.tabButton,
              activeCategory === category.id && { 
                backgroundColor: colors.primary,
                borderColor: colors.primary 
              }
            ]}
            onPress={() => setActiveCategory(category.id as any)}
          >
            <Text 
              style={[
                styles.tabButtonText,
                { color: activeCategory === category.id ? 'white' : colors.text }
              ]}
            >
              {category.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  const renderNutrientItem = (nutrient: MicronutrientData) => {
    const progress = calculateProgress(nutrient.value, nutrient.target);
    const progressColor = getProgressColor(progress);
    
    return (
      <View 
        key={nutrient.name} 
        style={[styles.nutrientItem, { backgroundColor: isDark ? colors.card : 'white' }]}
      >
        <View style={styles.nutrientHeader}>
          <View style={styles.nutrientTitleContainer}>
            {nutrient.isEssential && (
              <Circle 
                size={8} 
                fill={progressColor} 
                color={progressColor} 
                style={styles.essentialDot} 
              />
            )}
            <Text style={[styles.nutrientName, { color: colors.text }]}>
              {nutrient.name}
            </Text>
          </View>
          
          {onInfoPress && (
            <TouchableOpacity 
              style={styles.infoButton}
              onPress={() => onInfoPress(nutrient)}
            >
              <Feather name="info" size={16} color={colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
        
        <View style={styles.progressContainer}>
          <View 
            style={[
              styles.progressBackground, 
              { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }
            ]}
          >
            <View
              style={[
                styles.progressFill,
                { 
                  backgroundColor: progressColor,
                  width: `${progress}%` 
                }
              ]}
            />
          </View>
          
          <View style={styles.progressLabels}>
            <Text style={[styles.progressValue, { color: colors.text }]}>
              {nutrient.value} {nutrient.unit}
            </Text>
            <Text style={[styles.progressTarget, { color: colors.textSecondary }]}>
              / {nutrient.target} {nutrient.unit}
            </Text>
          </View>
        </View>
      </View>
    );
  };
  
  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: isDark ? colors.background : '#f9f9f9' }]}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading micronutrient data...
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.dateText, { color: colors.textSecondary }]}>
          Micronutrients for {getFormattedDate()}
        </Text>
        
        <View style={[styles.summaryCard, { backgroundColor: isDark ? colors.card : 'white' }]}>
          <Text style={[styles.summaryTitle, { color: colors.text }]}>
            Tracking {micronutrients.length} essential nutrients
          </Text>
          <Text style={[styles.summaryDescription, { color: colors.textSecondary }]}>
            Micronutrients are essential for your body's function and health. Track your intake to ensure you're meeting your daily needs.
          </Text>
        </View>
      </View>
      
      {renderCategoryTabs()}
      
      <ScrollView style={styles.nutrientsList}>
        {getFilteredNutrients().map(nutrient => renderNutrientItem(nutrient))}
        
        {getFilteredNutrients().length === 0 && (
          <View style={styles.emptyState}>
            <Feather name="alert-circle" size={24} color={colors.textSecondary} />
            <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
              No {activeCategory} data available for this day.
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    marginBottom: 16,
  },
  dateText: {
    fontSize: 14,
    marginBottom: 12,
  },
  summaryCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  summaryDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  tabsContainer: {
    paddingHorizontal: 8,
    paddingBottom: 16,
  },
  tabButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  tabButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
  },
  nutrientsList: {
    flex: 1,
  },
  nutrientItem: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  nutrientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  nutrientTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  essentialDot: {
    marginRight: 6,
  },
  nutrientName: {
    fontSize: 16,
    fontWeight: '500',
  },
  infoButton: {
    padding: 4,
  },
  progressContainer: {
    marginBottom: 4,
  },
  progressBackground: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
  },
  progressFill: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    borderRadius: 4,
  },
  progressLabels: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressTarget: {
    fontSize: 14,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyStateText: {
    fontSize: 16,
    marginTop: 12,
    textAlign: 'center',
  },
});

export default MicronutrientTrackingComponent; 