import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Modal, 
  TouchableOpacity, 
  FlatList,
  TextInput,
  Alert,
  ActivityIndicator
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { FoodItem } from './SavedMealsComponent';

interface MultiFoodLoggingModalProps {
  visible: boolean;
  onClose: () => void;
  onAddFoods: (foods: FoodItem[]) => void;
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
}

export function MultiFoodLoggingModal({ 
  visible, 
  onClose, 
  onAddFoods, 
  mealType
}: MultiFoodLoggingModalProps) {
  const { colors, isDark } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [searching, setSearching] = useState(false);
  const [selectedFoods, setSelectedFoods] = useState<(FoodItem & { servingOptions?: string[] })[]>([]);
  const [loading, setLoading] = useState(false);
  
  // Mock food database
  const FOOD_DATABASE: (FoodItem & { servingOptions?: string[] })[] = [
    {
      id: '1',
      name: 'Banana',
      calories: 105,
      protein: 1.3,
      carbs: 27,
      fat: 0.4,
      servingSize: '1 medium (118g)',
      servings: 1,
      servingOptions: ['1 small (101g)', '1 medium (118g)', '1 large (136g)']
    },
    {
      id: '2',
      name: 'Greek Yogurt, Plain',
      calories: 100,
      protein: 18,
      carbs: 5,
      fat: 0.4,
      servingSize: '170g container',
      servings: 1,
      servingOptions: ['100g', '170g container', '200g']
    },
    {
      id: '3',
      name: 'Oatmeal, cooked',
      calories: 166,
      protein: 5.9,
      carbs: 28,
      fat: 3.6,
      servingSize: '1 cup (234g)',
      servings: 1,
      servingOptions: ['1/2 cup (117g)', '1 cup (234g)', '1.5 cups (351g)']
    },
    {
      id: '4',
      name: 'Chicken Breast, grilled',
      calories: 165,
      protein: 31,
      carbs: 0,
      fat: 3.6,
      servingSize: '3 oz (85g)',
      servings: 1,
      servingOptions: ['3 oz (85g)', '4 oz (113g)', '6 oz (170g)']
    },
    {
      id: '5',
      name: 'Brown Rice, cooked',
      calories: 216,
      protein: 5,
      carbs: 45,
      fat: 1.8,
      servingSize: '1 cup (195g)',
      servings: 1,
      servingOptions: ['1/2 cup (97.5g)', '1 cup (195g)', '1.5 cups (292.5g)']
    },
    {
      id: '6',
      name: 'Avocado',
      calories: 234,
      protein: 2.9,
      carbs: 12.5,
      fat: 21.4,
      servingSize: '1 whole (201g)',
      servings: 1,
      servingOptions: ['1/4 avocado (50g)', '1/2 avocado (100g)', '1 whole (201g)']
    },
    {
      id: '7',
      name: 'Egg, large',
      calories: 72,
      protein: 6.3,
      carbs: 0.4,
      fat: 5,
      servingSize: '1 large (50g)',
      servings: 1,
      servingOptions: ['1 small (38g)', '1 medium (44g)', '1 large (50g)', '1 extra large (56g)']
    },
    {
      id: '8',
      name: 'Apple',
      calories: 95,
      protein: 0.5,
      carbs: 25,
      fat: 0.3,
      servingSize: '1 medium (182g)',
      servings: 1,
      servingOptions: ['1 small (149g)', '1 medium (182g)', '1 large (223g)']
    },
    {
      id: '9',
      name: 'Salmon, baked',
      calories: 175,
      protein: 19,
      carbs: 0,
      fat: 10.5,
      servingSize: '3 oz (85g)',
      servings: 1,
      servingOptions: ['3 oz (85g)', '4 oz (113g)', '6 oz (170g)']
    },
    {
      id: '10',
      name: 'Almond Milk, unsweetened',
      calories: 30,
      protein: 1.2,
      carbs: 1,
      fat: 2.5,
      servingSize: '1 cup (240ml)',
      servings: 1,
      servingOptions: ['1/2 cup (120ml)', '1 cup (240ml)', '2 cups (480ml)']
    }
  ];
  
  // Reset selected foods when modal visibility changes
  useEffect(() => {
    if (!visible) {
      setSelectedFoods([]);
      setSearchQuery('');
    }
  }, [visible]);
  
  const handleSearch = () => {
    if (!searchQuery.trim()) return;
    
    setSearching(true);
    
    // Simulate API call
    setTimeout(() => {
      const results = FOOD_DATABASE.filter(food => 
        food.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      
      // Add results that aren't already selected
      const newSelectedFoods = [...selectedFoods];
      results.forEach(result => {
        if (!selectedFoods.some(food => food.id === result.id)) {
          newSelectedFoods.push({ ...result });
        }
      });
      
      setSelectedFoods(newSelectedFoods);
      setSearching(false);
      setSearchQuery('');
    }, 500);
  };
  
  const handleAddFood = () => {
    const food = FOOD_DATABASE[Math.floor(Math.random() * FOOD_DATABASE.length)];
    setSelectedFoods(prev => [...prev, { ...food }]);
  };
  
  const handleRemoveFood = (id: string) => {
    setSelectedFoods(prev => prev.filter(food => food.id !== id));
  };
  
  const handleUpdateServings = (id: string, servings: number) => {
    setSelectedFoods(prev => prev.map(food => 
      food.id === id ? { ...food, servings } : food
    ));
  };
  
  const handleUpdateServingSize = (id: string, servingSize: string) => {
    setSelectedFoods(prev => prev.map(food => 
      food.id === id ? { ...food, servingSize } : food
    ));
  };
  
  const handleAddAllFoods = () => {
    if (selectedFoods.length === 0) {
      Alert.alert('No Foods Selected', 'Please add at least one food to continue.');
      return;
    }
    
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      // Clone foods to avoid reference issues
      const foodsToAdd = selectedFoods.map(food => ({
        id: food.id,
        name: food.name,
        calories: food.calories,
        protein: food.protein,
        carbs: food.carbs,
        fat: food.fat,
        servingSize: food.servingSize,
        servings: food.servings
      }));
      
      onAddFoods(foodsToAdd);
      setLoading(false);
      onClose();
    }, 500);
  };
  
  const calculateTotals = () => {
    return selectedFoods.reduce(
      (acc, food) => {
        const multiplier = food.servings;
        return {
          calories: acc.calories + food.calories * multiplier,
          protein: acc.protein + food.protein * multiplier,
          carbs: acc.carbs + food.carbs * multiplier,
          fat: acc.fat + food.fat * multiplier,
        };
      },
      { calories: 0, protein: 0, carbs: 0, fat: 0 }
    );
  };
  
  const totals = calculateTotals();
  
  const renderFoodItem = ({ item }: { item: FoodItem & { servingOptions?: string[] } }) => {
    return (
      <View style={[styles.foodItem, { backgroundColor: isDark ? colors.card : 'white' }]}>
        <View style={styles.foodItemHeader}>
          <Text style={[styles.foodName, { color: colors.text }]}>{item.name}</Text>
          <TouchableOpacity
            style={styles.removeButton}
            onPress={() => handleRemoveFood(item.id)}
          >
            <Feather name="x" size={16} color={colors.danger} />
          </TouchableOpacity>
        </View>
        
        <View style={styles.servingControls}>
          <View style={styles.servingCountContainer}>
            <Text style={[styles.servingLabel, { color: colors.textSecondary }]}>Servings</Text>
            <View style={styles.servingCounter}>
              <TouchableOpacity 
                style={[styles.countButton, { borderColor: colors.border }]}
                onPress={() => item.servings > 0.5 && handleUpdateServings(item.id, item.servings - 0.5)}
              >
                <Text style={[styles.countButtonText, { color: colors.text }]}>−</Text>
              </TouchableOpacity>
              
              <TextInput
                style={[styles.servingInput, { color: colors.text, borderColor: colors.border }]}
                value={item.servings.toString()}
                onChangeText={(text) => {
                  const value = parseFloat(text);
                  if (!isNaN(value) && value > 0) {
                    handleUpdateServings(item.id, value);
                  }
                }}
                keyboardType="decimal-pad"
              />
              
              <TouchableOpacity 
                style={[styles.countButton, { borderColor: colors.border }]}
                onPress={() => handleUpdateServings(item.id, item.servings + 0.5)}
              >
                <Text style={[styles.countButtonText, { color: colors.text }]}>+</Text>
              </TouchableOpacity>
            </View>
          </View>
          
          {item.servingOptions && (
            <View style={styles.servingSizeContainer}>
              <Text style={[styles.servingLabel, { color: colors.textSecondary }]}>Serving Size</Text>
              <View style={[styles.servingSizeSelector, { borderColor: colors.border }]}>
                <FlatList
                  data={item.servingOptions}
                  keyExtractor={(size) => size}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  renderItem={({ item: size }) => (
                    <TouchableOpacity
                      style={[
                        styles.servingSizeOption,
                        size === item.servingSize && { 
                          backgroundColor: colors.primary,
                          borderColor: colors.primary 
                        }
                      ]}
                      onPress={() => handleUpdateServingSize(item.id, size)}
                    >
                      <Text 
                        style={[
                          styles.servingSizeText,
                          { color: size === item.servingSize ? 'white' : colors.text }
                        ]}
                      >
                        {size}
                      </Text>
                    </TouchableOpacity>
                  )}
                />
              </View>
            </View>
          )}
        </View>
        
        <View style={styles.nutritionInfo}>
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {Math.round(item.calories * item.servings)}
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>cal</Text>
          </View>
          
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {(item.protein * item.servings).toFixed(1)}g
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>protein</Text>
          </View>
          
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {(item.carbs * item.servings).toFixed(1)}g
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>carbs</Text>
          </View>
          
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {(item.fat * item.servings).toFixed(1)}g
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>fat</Text>
          </View>
        </View>
      </View>
    );
  };
  
  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: isDark ? colors.background : '#f9f9f9' }]}>
          <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Add Multiple Foods
            </Text>
            
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Feather name="x" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.searchContainer}>
            <View style={[styles.searchInputContainer, { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }]}>
              <Feather name="search" size={18} color={colors.textSecondary} style={styles.searchIcon} />
              <TextInput
                style={[styles.searchInput, { color: colors.text }]}
                placeholder="Search for multiple foods..."
                placeholderTextColor={colors.textSecondary}
                value={searchQuery}
                onChangeText={setSearchQuery}
                onSubmitEditing={handleSearch}
                returnKeyType="search"
              />
            </View>
            
            <TouchableOpacity 
              style={[styles.addButton, { backgroundColor: colors.primary }]}
              onPress={handleSearch}
            >
              <Feather name="plus" size={18}  color={colors.text} />
            </TouchableOpacity>
          </View>
          
          {searching && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={colors.primary} />
              <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Searching...</Text>
            </View>
          )}
          
          {selectedFoods.length === 0 && !searching && (
            <View style={styles.emptyContainer}>
              <Feather name="alert-circle" size={32} color={colors.textSecondary} />
              <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                Search for foods above or tap below to randomly add food
              </Text>
              <TouchableOpacity
                style={[styles.emptyAddButton, { backgroundColor: colors.primary }]}
                onPress={handleAddFood}
              >
                <Feather name="plus" size={18}  color={colors.text} />
                <Text style={styles.emptyAddButtonText}>Add Random Food</Text>
              </TouchableOpacity>
            </View>
          )}
          
          <FlatList
            data={selectedFoods}
            keyExtractor={(item) => item.id}
            renderItem={renderFoodItem}
            contentContainerStyle={styles.foodsList}
          />
          
          {selectedFoods.length > 0 && (
            <View style={[styles.footer, { backgroundColor: isDark ? colors.card : 'white', borderTopColor: colors.border }]}>
              <View style={styles.totalsContainer}>
                <View style={styles.totalItems}>
                  <Text style={[styles.totalItemsText, { color: colors.textSecondary }]}>
                    {selectedFoods.length} items
                  </Text>
                </View>
                
                <View style={styles.totalMacros}>
                  <Text style={[styles.totalMacrosText, { color: colors.text }]}>
                    {Math.round(totals.calories)} cal
                  </Text>
                  <Text style={[styles.totalMacrosDetail, { color: colors.textSecondary }]}>
                    P: {totals.protein.toFixed(1)}g • C: {totals.carbs.toFixed(1)}g • F: {totals.fat.toFixed(1)}g
                  </Text>
                </View>
              </View>
              
              <TouchableOpacity
                style={[styles.addAllButton, { backgroundColor: colors.primary }]}
                onPress={handleAddAllFoods}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <Text style={styles.addAllButtonText}>
                    Add All to {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    flex: 1,
    marginTop: 50,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    height: 44,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 16,
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  emptyAddButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  emptyAddButtonText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 8,
  },
  foodsList: {
    padding: 16,
  },
  foodItem: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  foodItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  foodName: {
    fontSize: 16,
    fontWeight: '600',
  },
  removeButton: {
    padding: 4,
  },
  servingControls: {
    marginBottom: 12,
  },
  servingCountContainer: {
    marginBottom: 12,
  },
  servingLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  servingCounter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  countButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  countButtonText: {
    fontSize: 18,
    fontWeight: '600',
  },
  servingInput: {
    height: 36,
    width: 60,
    borderWidth: 1,
    borderRadius: 8,
    textAlign: 'center',
    marginHorizontal: 12,
    fontSize: 16,
  },
  servingSizeContainer: {
    marginBottom: 8,
  },
  servingSizeSelector: {
    borderRadius: 8,
    borderWidth: 1,
    paddingVertical: 4,
    paddingHorizontal: 4,
  },
  servingSizeOption: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
    margin: 4,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  servingSizeText: {
    fontSize: 14,
  },
  nutritionInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    alignItems: 'center',
  },
  nutritionValue: {
    fontSize: 15,
    fontWeight: '600',
  },
  nutritionLabel: {
    fontSize: 12,
    marginTop: 2,
  },
  footer: {
    borderTopWidth: 1,
    padding: 16,
  },
  totalsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  totalItems: {},
  totalItemsText: {
    fontSize: 15,
  },
  totalMacros: {
    alignItems: 'flex-end',
  },
  totalMacrosText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  totalMacrosDetail: {
    fontSize: 13,
  },
  addAllButton: {
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addAllButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default MultiFoodLoggingModal; 