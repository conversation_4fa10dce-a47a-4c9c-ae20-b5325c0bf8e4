import React, { useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
  ViewStyle,
  TextStyle,
  StyleProp
} from 'react-native';
import { usePathname, useRouter } from 'expo-router';
import { useTheme } from '../contexts/ThemeContext';
import { useNavigationGuard } from './NavigationGuard';
import { Feather } from '@expo/vector-icons';
import { useAccessibility } from './AccessibilityProvider';

// Special segments to handle specially or ignore in breadcrumbs
const SPECIAL_SEGMENTS = [
  '(tabs)',
  '(auth)',
  '(onboarding)',
  '_layout',
  'index'
];

// Map route segments to human-readable names
const ROUTE_DISPLAY_NAMES: Record<string, string> = {
  // Tab routes
  'profile': 'Profile',
  'history': 'History',
  'scan': 'Scan',
  'challenges': 'Challenges',
  'database': 'Database',
  
  // Profile screens
  'profile-screens': 'Profile',
  'nutrition-goals': 'Nutrition Goals',
  'health-settings': 'Health Settings',
  'billing': 'Billing',
  'health-data': 'Health Data',
  'connected-devices': 'Connected Devices',
  'invite-friends': 'Invite Friends',
  'help-support': 'Help & Support',
  'privacy': 'Privacy',
  'achievements': 'Achievements',
  'more': 'More',
  
  // Recipe routes
  'recipe': 'Recipe',
  'lidar-portion-scanner': 'Portion Scanner',
  
  // Other main routes
  'food-logger': 'Food Logger',
  'nutrition-timeline': 'Nutrition Timeline',
  'goal-recommendations': 'Goal Recommendations',
  'water-tracker': 'Water Tracker',
  'fasting-tracker': 'Fasting Tracker',
  'dietitian': 'Dietitian',
  'sleep-tracker': 'Sleep Tracker',
  'workout-planner': 'Workout Planner',
  'subscription-plans': 'Subscription Plans'
};

interface NavigationBreadcrumbProps {
  /**
   * Max number of breadcrumb items to display (from right)
   */
  maxItems?: number;
  
  /**
   * Whether to always show home as the first item
   */
  showHome?: boolean;
  
  /**
   * Custom style for the container
   */
  style?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for the breadcrumb items
   */
  itemStyle?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for the breadcrumb text
   */
  textStyle?: StyleProp<TextStyle>;
  
  /**
   * Custom separator component
   */
  separator?: React.ReactNode;
  
  /**
   * Whether to enable horizontal scrolling if breadcrumbs don't fit
   */
  scrollable?: boolean;
  
  /**
   * Optional click handler for breadcrumb items
   */
  onItemPress?: (path: string, index: number) => void;
}

/**
 * A component that displays the current navigation path as breadcrumbs,
 * allowing users to see their current position in the app's hierarchy
 * and easily navigate back to previous levels.
 */
export function NavigationBreadcrumb({
  maxItems = 3,
  showHome = true,
  style,
  itemStyle,
  textStyle,
  separator,
  scrollable = true,
  onItemPress,
}: NavigationBreadcrumbProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { colors, isDark } = useTheme();
  const { preferLargeText } = useAccessibility();
  
  // Get path segments for the breadcrumb
  const breadcrumbItems = useMemo(() => {
    if (!pathname) return [];
    
    // Process path segments
    const segments = pathname
      .split('/')
      .filter(s => s && !SPECIAL_SEGMENTS.includes(s));
      
    // Extract segments and create breadcrumb items
    const items: { label: string; path: string; isActive: boolean }[] = [];
    
    // Add home breadcrumb if configured to show
    if (showHome) {
      items.push({
        label: 'Home',
        path: '/',
        isActive: segments.length === 0
      });
    }
    
    // Process each segment
    let currentPath = '';
    segments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      
      // Handle dynamic segments (e.g., [id]) - try to get a more friendly name
      let label = segment;
      
      // Replace dynamic segment placeholders like [id] with a friendlier name
      if (segment.startsWith('[') && segment.endsWith(']')) {
        const paramName = segment.substring(1, segment.length - 1);
        label = paramName.charAt(0).toUpperCase() + paramName.slice(1);
      } 
      // Use display name mapping if available
      else if (ROUTE_DISPLAY_NAMES[segment]) {
        label = ROUTE_DISPLAY_NAMES[segment];
      } 
      // If no mapping exists, capitalize and format with spaces
      else {
        label = segment
          .replace(/[-_]/g, ' ')
          .replace(/([A-Z])/g, ' $1')
          .replace(/\w\S*/g, txt => txt.charAt(0).toUpperCase() + txt.substr(1));
      }
      
      items.push({
        label,
        path: currentPath,
        isActive: index === segments.length - 1
      });
    });
    
    // If there are more items than maxItems, slice to show the last `maxItems`
    return items.length > maxItems && !scrollable
      ? items.slice(-maxItems)
      : items;
  }, [pathname, maxItems, showHome, scrollable]);
  
  // Handle breadcrumb item press
  const handleItemPress = (path: string, index: number) => {
    if (onItemPress) {
      onItemPress(path, index);
    } else {
      router.push(path as any);
    }
  };
  
  // If there are no breadcrumb items, don't render anything
  if (breadcrumbItems.length <= 1 && breadcrumbItems[0]?.path === '/') {
    return null;
  }
  
  // Define the wrapper component based on scrollable prop
  const Wrapper = scrollable ? ScrollView : View;
  const wrapperProps = scrollable ? {
    horizontal: true,
    showsHorizontalScrollIndicator: false,
    contentContainerStyle: styles.scrollContent
  } : {};
  
  return (
    <View style={[styles.container, style]}>
      <Wrapper {...wrapperProps}>
        {breadcrumbItems.map((item, index) => (
          <React.Fragment key={item.path}>
            {/* Render separator if not the first item */}
            {index > 0 && (
              <View style={styles.separatorContainer}>
                {separator || (
                  <Feather name="chevron-right" size={16} color={colors.textSecondary} />
                )}
              </View>
            )}
            
            {/* Render breadcrumb item */}
            <TouchableOpacity
              style={[
                styles.breadcrumbItem,
                item.isActive && { backgroundColor: `${colors.primary}15` },
                itemStyle
              ]}
              onPress={() => handleItemPress(item.path, index)}
              disabled={item.isActive}
              accessibilityLabel={`Navigate to ${item.label}`}
              accessibilityRole="button"
              accessibilityState={{ disabled: item.isActive }}
            >
              <Text
                style={[
                  styles.breadcrumbText,
                  { 
                    color: item.isActive ? colors.primary : colors.text,
                    fontSize: preferLargeText ? 16 : 14
                  },
                  textStyle
                ]}
                numberOfLines={1}
              >
                {item.label}
              </Text>
            </TouchableOpacity>
          </React.Fragment>
        ))}
      </Wrapper>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  scrollContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  breadcrumbItem: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  breadcrumbText: {
    fontWeight: '500',
  },
  separatorContainer: {
    paddingHorizontal: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default NavigationBreadcrumb; 