import React, { useEffect, useRef, createContext, useContext, ReactNode } from 'react';
import { Alert, Platform, AppState, AppStateStatus } from 'react-native';
import { usePathname, useRouter, Stack } from 'expo-router';

// The maximum allowed navigation depth
const MAX_NAVIGATION_DEPTH = 3;

// Navigation path segments that count as a level
// Empty string means root, so we need to filter them out
const isValidPathSegment = (segment: string) => segment !== '' && segment !== '/';

// Special segments that don't count towards navigation depth
// These are usually implementation details, not actual navigation levels
const NON_COUNTING_SEGMENTS = [
  '(tabs)',   // Tab container doesn't count as a level
  '(auth)',   // Auth container doesn't count as a level
  '(onboarding)', // Onboarding container doesn't count as a level
  '_layout',  // Layout files don't count as a level
  'index',    // Index routes don't count as a level
];

// Define context type for navigation history
interface NavigationContextType {
  // The current navigation history
  history: string[];
  
  // Current navigation depth
  currentDepth: number;
  
  // Go back to the previous screen
  goBack: () => void;
  
  // Go to a specific route, managing depth
  navigateTo: (route: string) => void;
  
  // Check if the navigation depth limit would be exceeded by navigating to a route
  wouldExceedDepthLimit: (route: string) => boolean;
  
  // Reset navigation history
  resetNavigation: () => void;
}

// Create the navigation context
const NavigationContext = createContext<NavigationContextType>({
  history: [],
  currentDepth: 0,
  goBack: () => {},
  navigateTo: () => {},
  wouldExceedDepthLimit: () => false,
  resetNavigation: () => {},
});

// Hook to use the navigation context
export const useNavigationGuard = () => useContext(NavigationContext);

interface NavigationGuardProps {
  children: ReactNode;
  
  // Optional: Customize the maximum navigation depth (defaults to 3)
  maxDepth?: number;
  
  // Optional: Custom handler for when navigation depth is exceeded
  onDepthExceeded?: (
    currentPath: string, 
    targetPath: string, 
    currentDepth: number, 
    maxDepth: number
  ) => void;
  
  // Optional: Whether to show alerts when navigation depth is exceeded
  showAlerts?: boolean;

  // Optional: In development, allows bypassing auth for faster testing
  bypassAuthInDev?: boolean;
}

/**
 * NavigationGuard component monitors and enforces navigation depth limits.
 * It tracks the current navigation depth and prevents navigation beyond the specified limit.
 */
export function NavigationGuard({
  children,
  maxDepth = MAX_NAVIGATION_DEPTH,
  onDepthExceeded,
  showAlerts = true,
  bypassAuthInDev = false,
}: NavigationGuardProps) {
  const pathname = usePathname();
  const router = useRouter();
  const historyRef = useRef<string[]>([]);
  const appState = useRef(AppState.currentState);
  
  // Track whether the app is in the foreground
  const isActive = useRef(true);
  
  // Flag to indicate if auth is being bypassed for development
  const bypassingAuth = useRef(false);

  // Check if we should bypass auth checks (only in development)
  useEffect(() => {
    if (__DEV__ && bypassAuthInDev) {
      console.log('NavigationGuard: Auth checks bypassed for development');
      bypassingAuth.current = true;
    } else {
      bypassingAuth.current = false;
    }
  }, [bypassAuthInDev]);

  // Parse a route into segments that count towards navigation depth
  const parseRouteDepth = (route: string): string[] => {
    if (!route) return [];
    
    const segments = route
      .split('/')
      .filter(isValidPathSegment)
      .filter(segment => !NON_COUNTING_SEGMENTS.includes(segment));
    
    // Handle dynamic segments (e.g., [id]) - these still count as levels
    return segments;
  };
  
  // Calculate navigation depth of a route
  const calculateRouteDepth = (route: string): number => {
    return parseRouteDepth(route).length;
  };
  
  // Navigate to a specific route, managing depth
  const navigateTo = (route: string) => {
    const newDepth = calculateRouteDepth(route);
    
    if (newDepth > maxDepth) {
      handleDepthExceeded(pathname, route, newDepth, maxDepth);
      return;
    }
    
    // Use any to bypass type checking since expo-router has strict route typing
    router.push(route as any);
  };
  
  // Check if navigating to a route would exceed the depth limit
  const wouldExceedDepthLimit = (route: string): boolean => {
    const newDepth = calculateRouteDepth(route);
    return newDepth > maxDepth;
  };
  
  // Handle when navigation depth is exceeded
  const handleDepthExceeded = (
    currentPath: string, 
    targetPath: string, 
    currentDepth: number, 
    maxDepth: number
  ) => {
    // Log the issue
    console.warn(
      `Navigation depth limit exceeded! Current: ${currentDepth}, Max: ${maxDepth}`,
      `Current path: ${currentPath}`,
      `Target path: ${targetPath}`
    );
    
    // Call the custom handler if provided
    if (onDepthExceeded) {
      onDepthExceeded(currentPath, targetPath, currentDepth, maxDepth);
    }
    
    // Show an alert if enabled
    if (showAlerts && __DEV__) {
      Alert.alert(
        'Navigation Depth Limit',
        `Navigation depth exceeds the maximum of ${maxDepth} levels. Consider restructuring your navigation to avoid deep nesting.`,
        [{ text: 'OK' }]
      );
    }
  };
  
  // Go back to the previous screen in history
  const goBack = () => {
    const history = historyRef.current;
    
    if (history.length > 1) {
      // Remove the current route
      history.pop();
      
      // Navigate to the previous route
      const previousRoute = history[history.length - 1];
      router.replace(previousRoute as any);
    } else {
      // No previous route, go to the home screen
      router.replace('/');
    }
  };
  
  // Reset navigation history
  const resetNavigation = () => {
    historyRef.current = [];
  };
  
  // Handle app state changes to monitor when the app goes to background/foreground
  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (
      appState.current.match(/inactive|background/) && 
      nextAppState === 'active'
    ) {
      // App has come to the foreground
      isActive.current = true;
    } else if (
      appState.current === 'active' && 
      nextAppState.match(/inactive|background/)
    ) {
      // App has gone to the background
      isActive.current = false;
    }
    
    appState.current = nextAppState;
  };
  
  // Track navigation history and enforce depth limit
  useEffect(() => {
    if (!pathname || !isActive.current) return;
    
    const currentDepth = calculateRouteDepth(pathname);
    
    // Check if exceeding max depth
    if (currentDepth > maxDepth) {
      handleDepthExceeded(pathname, pathname, currentDepth, maxDepth);
      
      // If in development, navigate to the previous path if available
      if (__DEV__ && historyRef.current.length > 1) {
        const previousPath = historyRef.current[historyRef.current.length - 2];
        router.replace(previousPath as any);
        return;
      }
    }
    
    // Update history
    if (
      historyRef.current.length === 0 || 
      historyRef.current[historyRef.current.length - 1] !== pathname
    ) {
      historyRef.current.push(pathname);
    }
  }, [pathname, maxDepth]);
  
  // Set up AppState listener to monitor when app goes to background/foreground
  useEffect(() => {
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription.remove();
    };
  }, []);
  
  // Calculate the current navigation depth based on the current path
  const currentDepth = calculateRouteDepth(pathname);
  
  // Get current user status, accounting for dev bypass
  const getAuthStatus = () => {
    // If bypassing auth in development mode, consider the user authenticated
    if (__DEV__ && bypassingAuth.current) {
      return true;
    }
    
    // Otherwise, check actual auth state
    // This would typically interface with your auth context
    // For this implementation, we just return true to allow navigation
    return true;
  };
  
  // Provide the navigation context to children
  return (
    <NavigationContext.Provider
      value={{
        history: historyRef.current,
        currentDepth,
        goBack,
        navigateTo,
        wouldExceedDepthLimit,
        resetNavigation,
      }}
    >
      {children}
    </NavigationContext.Provider>
  );
}

export default NavigationGuard; 