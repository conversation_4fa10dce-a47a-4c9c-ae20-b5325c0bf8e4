import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, Linking } from 'react-native';

import { useTheme } from '@/contexts/ThemeContext';

interface NoPermissionViewProps {
  type: 'camera' | 'photos';
  onRequestPermission?: () => void;
}

export function NoPermissionView({ type, onRequestPermission }: NoPermissionViewProps) {
  const { colors } = useTheme();
  
  const title = type === 'camera' ? 'Camera Access Required' : 'Photo Library Access Required';
  const message = type === 'camera' 
    ? 'To analyze your food, we need permission to use your camera. Please enable camera access in your device settings.'
    : 'To analyze your food from your photos, we need permission to access your photo library. Please enable photo library access in your device settings.';
  
  const Icon = type === 'camera' ? CameraOff : ImageOff;
  
  const openSettings = () => {
    // Open settings if platform supports it
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      Linking.openSettings();
    }
  };
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <Icon size={64} color={colors.textSecondary} style={styles.icon} />
        
        <Text style={[styles.title, { color: colors.text }]}>
          {title}
        </Text>
        
        <Text style={[styles.message, { color: colors.textSecondary }]}>
          {message}
        </Text>
        
        <View style={styles.buttonContainer}>
          {onRequestPermission && (
            <TouchableOpacity
              style={[styles.button, styles.requestButton, { backgroundColor: colors.primary }]}
              onPress={onRequestPermission}
            >
              <Text style={styles.buttonText}>Request Permission</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[styles.button, { backgroundColor: colors.primary }]}
            onPress={openSettings}
          >
            <Text style={styles.buttonText}>Open Settings</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    alignItems: 'center',
    maxWidth: 400,
  },
  icon: {
    marginBottom: 24,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    marginBottom: 12,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
    gap: 12,
  },
  button: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  requestButton: {
    marginRight: 12,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
}); 