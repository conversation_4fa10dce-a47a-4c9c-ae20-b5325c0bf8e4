import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, Dimensions } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';

interface NoScanViewProps {
  error?: string;
  onRetry?: () => void;
}

export function NoScanView({ error, onRetry }: NoScanViewProps) {
  const { colors, isDark } = useTheme();
  const currentTime = new Date().getHours();
  const windowWidth = Dimensions.get('window').width;
  
  // Determine the meal time suggestion based on current time
  let mealSuggestion = 'a meal';
  if (currentTime >= 5 && currentTime < 11) {
    mealSuggestion = 'breakfast';
  } else if (currentTime >= 11 && currentTime < 14) {
    mealSuggestion = 'lunch';
  } else if (currentTime >= 14 && currentTime < 17) {
    mealSuggestion = 'a snack';
  } else if (currentTime >= 17 && currentTime < 21) {
    mealSuggestion = 'dinner';
  } else {
    mealSuggestion = 'a late meal';
  }
  
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={
          error 
            ? (isDark ? ['#881337', '#500724'] : ['#FEE2E2', '#FECACA']) 
            : (isDark ? ['#0F172A', '#1E293B'] : ['#F0F9FF', '#E0F2FE'])
        }
        style={[styles.infoCard, { borderColor: colors.border }]}
      >
        <View style={[
          styles.iconContainer, 
          { 
            backgroundColor: error 
              ? (isDark ? 'rgba(255,255,255,0.1)' : 'rgba(220, 38, 38, 0.1)') 
              : (isDark ? 'rgba(255,255,255,0.1)' : 'rgba(59, 130, 246, 0.1)')
          }
        ]}>
          {error ? (
            <Feather name="alert-triangle" size={40} color={isDark ? '#FCA5A5' : '#DC2626'} />
          ) : (
            <CameraIcon size={40} color={isDark ? '#93C5FD' : '#3B82F6'} />
          )}
        </View>
        
        <Text style={[
          styles.title, 
          { 
            color: error 
              ? (isDark ? '#FCA5A5' : '#B91C1C') 
              : colors.text 
          }
        ]}>
          {error ? 'Something went wrong' : 'Analyze Your Food'}
        </Text>
        
        <Text style={[
          styles.description, 
          { 
            color: error 
              ? (isDark ? '#FCA5A5' : '#B91C1C') 
              : colors.textSecondary 
          }
        ]}>
          {error ? error : `Take a photo of ${mealSuggestion} to get nutritional information and track your calories.`}
        </Text>
        
        {onRetry && (
          <TouchableOpacity 
            style={[
              styles.retryButton, 
              { 
                backgroundColor: error 
                  ? (isDark ? '#B91C1C' : '#DC2626') 
                  : colors.primary 
              }
            ]}
            onPress={onRetry}
          >
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        )}
        
        {!error && (
          <View style={styles.benefitsContainer}>
            <View style={styles.benefitItem}>
              <View style={[styles.benefitDot, { backgroundColor: isDark ? '#93C5FD' : '#3B82F6' }]} />
              <Text style={[styles.benefitText, { color: colors.textSecondary }]}>
                Track calories and macronutrients
              </Text>
            </View>
            <View style={styles.benefitItem}>
              <View style={[styles.benefitDot, { backgroundColor: isDark ? '#93C5FD' : '#3B82F6' }]} />
              <Text style={[styles.benefitText, { color: colors.textSecondary }]}>
                Get healthier meal alternatives
              </Text>
            </View>
            <View style={styles.benefitItem}>
              <View style={[styles.benefitDot, { backgroundColor: isDark ? '#93C5FD' : '#3B82F6' }]} />
              <Text style={[styles.benefitText, { color: colors.textSecondary }]}>
                Build a history of your meals
              </Text>
            </View>
          </View>
        )}
      </LinearGradient>
      
      <Image 
        source={{ 
          uri: error 
            ? 'https://images.pexels.com/photos/5792641/pexels-photo-5792641.jpeg?auto=compress&cs=tinysrgb&w=600'
            : 'https://images.pexels.com/photos/1640774/pexels-photo-1640774.jpeg?auto=compress&cs=tinysrgb&w=600'
        }} 
        style={[
          styles.backgroundImage,
          { opacity: isDark ? 0.3 : 0.1 }
        ]}
        resizeMode="cover"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  infoCard: {
    width: '100%',
    maxWidth: 400,
    padding: 28,
    borderRadius: 20,
    alignItems: 'center',
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    zIndex: 1,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 12,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  retryButton: {
    paddingHorizontal: 28,
    paddingVertical: 14,
    borderRadius: 12,
    marginTop: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  backgroundImage: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: 0,
  },
  benefitsContainer: {
    alignSelf: 'stretch',
    marginTop: 16,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  benefitDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 12,
  },
  benefitText: {
    fontSize: 14,
    fontWeight: '500',
  }
}); 