import React from 'react';
import { View, Text, StyleSheet, Dimensions, useWindowDimensions, Platform } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';

// Helper function to merge styles for web compatibility
const mergeStyles = (...styles) => {
  if (Platform.OS === 'web') {
    return Object.assign({}, ...styles);
  }
  return styles;
};

// Simple Pie Chart component with access to theme
const MacronutrientPieChart = ({ 
  proteinPercentage, 
  carbsPercentage, 
  fatPercentage 
}) => {
  const { colors, isDark } = useTheme();
  const pieChartStyle = {
    ...styles.pieChartSimple,
    backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
  };
  
  return (
    <View style={pieChartStyle}>
      {/* Labels inside the pie chart */}
      <View style={styles.pieLabels}>
        <Text style={{ color: isDark ? 'white' : '#333', fontSize: 12, fontWeight: 'bold' }}>
          {Math.round(proteinPercentage)}%
        </Text>
      </View>
      
      {/* Colored segments at the bottom of the chart */}
      <View style={styles.pieSegments}>
        <View style={{
          ...styles.pieSegment,
          flex: proteinPercentage / 100, 
          backgroundColor: '#3B82F6'
        }} />
        <View style={{
          ...styles.pieSegment,
          flex: carbsPercentage / 100, 
          backgroundColor: '#10B981'
        }} />
        <View style={{
          ...styles.pieSegment,
          flex: fatPercentage / 100, 
          backgroundColor: '#F59E0B'
        }} />
      </View>
    </View>
  );
};

interface NutritionData {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber?: number;
}

interface NutritionChartsProps {
  nutritionData: NutritionData;
  dailyGoals: NutritionData;
  weeklyData?: NutritionData[];
  title?: string;
}

export function NutritionCharts({ 
  nutritionData, 
  dailyGoals,
  weeklyData,
  title = "Nutrition Overview"
}: NutritionChartsProps) {
  const { colors, isDark } = useTheme();
  const { width } = useWindowDimensions();
  const chartWidth = width - 48;
  
  // Calculate percentages for macros
  const totalCalories = nutritionData.calories || 1;
  const proteinCalories = nutritionData.protein * 4;
  const carbsCalories = nutritionData.carbs * 4;
  const fatCalories = nutritionData.fat * 9;
  
  const proteinPercentage = (proteinCalories / totalCalories) * 100;
  const carbsPercentage = (carbsCalories / totalCalories) * 100;
  const fatPercentage = (fatCalories / totalCalories) * 100;
  
  // Calculate goal percentages
  const caloriePercentage = Math.min((nutritionData.calories / (dailyGoals.calories || 2000)) * 100, 100);
  const proteinPercentageGoal = Math.min((nutritionData.protein / (dailyGoals.protein || 50)) * 100, 100);
  const carbsPercentageGoal = Math.min((nutritionData.carbs / (dailyGoals.carbs || 250)) * 100, 100);
  const fatPercentageGoal = Math.min((nutritionData.fat / (dailyGoals.fat || 70)) * 100, 100);
  
  // Prepare styles with platform-specific adjustments
  const titleStyle = {
    ...styles.title,
    color: colors.text
  };
  
  const chartContainerStyle = {
    ...styles.chartContainer,
    backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(255,255,255,1)',
    ...(Platform.OS === 'web' ? {
      boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.1)'
    } : {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 3,
    })
  };
  
  const chartTitleStyle = {
    ...styles.chartTitle,
    color: colors.text
  };
  
  return (
    <View style={styles.container}>
      <Text style={titleStyle}>{title}</Text>
      
      {/* Macronutrient Distribution Chart */}
      <View style={chartContainerStyle}>
        <Text style={chartTitleStyle}>Macronutrient Distribution</Text>
        
        <View style={styles.macroDistributionChart}>
          <View style={styles.pieChartContainer}>
            <MacronutrientPieChart 
              proteinPercentage={proteinPercentage}
              carbsPercentage={carbsPercentage}
              fatPercentage={fatPercentage}
            />
          </View>
          
          <View style={styles.macroLegend}>
            <View style={styles.legendItem}>
              <View style={{...styles.legendColor, backgroundColor: '#3B82F6'}} />
              <View>
                <Text style={{...styles.legendText, color: colors.text}}>Protein</Text>
                <Text style={{...styles.legendPercentage, color: colors.textSecondary}}>
                  {proteinPercentage.toFixed(1)}% ({nutritionData.protein}g)
                </Text>
              </View>
            </View>
            
            <View style={styles.legendItem}>
              <View style={{...styles.legendColor, backgroundColor: '#10B981'}} />
              <View>
                <Text style={{...styles.legendText, color: colors.text}}>Carbs</Text>
                <Text style={{...styles.legendPercentage, color: colors.textSecondary}}>
                  {carbsPercentage.toFixed(1)}% ({nutritionData.carbs}g)
                </Text>
              </View>
            </View>
            
            <View style={styles.legendItem}>
              <View style={{...styles.legendColor, backgroundColor: '#F59E0B'}} />
              <View>
                <Text style={{...styles.legendText, color: colors.text}}>Fat</Text>
                <Text style={{...styles.legendPercentage, color: colors.textSecondary}}>
                  {fatPercentage.toFixed(1)}% ({nutritionData.fat}g)
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>
      
      {/* Goal Progress Bar Chart */}
      <View style={chartContainerStyle}>
        <Text style={chartTitleStyle}>Goal Progress</Text>
        
        <View style={styles.barChartContainer}>
          <View style={styles.barGroup}>
            <Text style={{...styles.barLabel, color: colors.textSecondary}}>Calories</Text>
            <View style={styles.barWrapper}>
              <View style={{
                ...styles.barBackground, 
                backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
              }}>
                <View 
                  style={{
                    ...styles.barFill, 
                    width: `${caloriePercentage}%`,
                    backgroundColor: '#3B82F6'
                  }} 
                />
              </View>
              <Text style={{...styles.barValue, color: '#3B82F6'}}>
                {nutritionData.calories} / {dailyGoals.calories || 2000} cal ({caloriePercentage.toFixed(0)}%)
              </Text>
            </View>
          </View>
          
          <View style={styles.barGroup}>
            <Text style={{...styles.barLabel, color: colors.textSecondary}}>Protein</Text>
            <View style={styles.barWrapper}>
              <View style={{
                ...styles.barBackground, 
                backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
              }}>
                <View 
                  style={{
                    ...styles.barFill, 
                    width: `${proteinPercentageGoal}%`,
                    backgroundColor: '#10B981'
                  }} 
                />
              </View>
              <Text style={{...styles.barValue, color: '#10B981'}}>
                {nutritionData.protein} / {dailyGoals.protein || 50}g ({proteinPercentageGoal.toFixed(0)}%)
              </Text>
            </View>
          </View>
          
          <View style={styles.barGroup}>
            <Text style={{...styles.barLabel, color: colors.textSecondary}}>Carbs</Text>
            <View style={styles.barWrapper}>
              <View style={{
                ...styles.barBackground, 
                backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
              }}>
                <View 
                  style={{
                    ...styles.barFill, 
                    width: `${carbsPercentageGoal}%`,
                    backgroundColor: '#F59E0B'
                  }} 
                />
              </View>
              <Text style={{...styles.barValue, color: '#F59E0B'}}>
                {nutritionData.carbs} / {dailyGoals.carbs || 250}g ({carbsPercentageGoal.toFixed(0)}%)
              </Text>
            </View>
          </View>
          
          <View style={styles.barGroup}>
            <Text style={{...styles.barLabel, color: colors.textSecondary}}>Fat</Text>
            <View style={styles.barWrapper}>
              <View style={{
                ...styles.barBackground, 
                backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
              }}>
                <View 
                  style={{
                    ...styles.barFill, 
                    width: `${fatPercentageGoal}%`,
                    backgroundColor: '#EF4444'
                  }} 
                />
              </View>
              <Text style={{...styles.barValue, color: '#EF4444'}}>
                {nutritionData.fat} / {dailyGoals.fat || 70}g ({fatPercentageGoal.toFixed(0)}%)
              </Text>
            </View>
          </View>
        </View>
      </View>
      
      {/* Nutrition Stats Cards */}
      <View style={styles.statsContainer}>
        <View style={{
          ...styles.statCard, 
          backgroundColor: isDark ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)',
          borderColor: '#3B82F6'
        }}>
          <Text style={{...styles.statValue, color: '#3B82F6'}}>{nutritionData.calories}</Text>
          <Text style={{...styles.statLabel, color: colors.text}}>Calories</Text>
        </View>
        
        <View style={{
          ...styles.statCard, 
          backgroundColor: isDark ? 'rgba(16, 185, 129, 0.2)' : 'rgba(16, 185, 129, 0.1)',
          borderColor: '#10B981'
        }}>
          <Text style={{...styles.statValue, color: '#10B981'}}>{nutritionData.protein}g</Text>
          <Text style={{...styles.statLabel, color: colors.text}}>Protein</Text>
        </View>
        
        <View style={{
          ...styles.statCard, 
          backgroundColor: isDark ? 'rgba(245, 158, 11, 0.2)' : 'rgba(245, 158, 11, 0.1)',
          borderColor: '#F59E0B'
        }}>
          <Text style={{...styles.statValue, color: '#F59E0B'}}>{nutritionData.carbs}g</Text>
          <Text style={{...styles.statLabel, color: colors.text}}>Carbs</Text>
        </View>
        
        <View style={{
          ...styles.statCard, 
          backgroundColor: isDark ? 'rgba(239, 68, 68, 0.2)' : 'rgba(239, 68, 68, 0.1)',
          borderColor: '#EF4444'
        }}>
          <Text style={{...styles.statValue, color: '#EF4444'}}>{nutritionData.fat}g</Text>
          <Text style={{...styles.statLabel, color: colors.text}}>Fat</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  chartContainer: {
    marginBottom: 24,
    padding: 16,
    borderRadius: 16,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  macroDistributionChart: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  pieChartContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 140,
  },
  pieChartSimple: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    overflow: 'hidden',
  },
  pieLabels: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  pieSegments: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 8,
    flexDirection: 'row',
    zIndex: 1,
  },
  pieSegment: {
    height: '100%',
  },
  macroLegend: {
    flex: 1,
    paddingLeft: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  legendColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  legendText: {
    fontSize: 14,
    fontWeight: '500',
  },
  legendPercentage: {
    fontSize: 12,
  },
  barChartContainer: {
    marginTop: 8,
  },
  barGroup: {
    marginBottom: 16,
  },
  barLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  barWrapper: {
    position: 'relative',
  },
  barBackground: {
    height: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  barFill: {
    height: '100%',
    borderRadius: 8,
  },
  barValue: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 4,
    textAlign: 'right',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    width: '48%',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    alignItems: 'center',
    borderLeftWidth: 4,
  },
  statValue: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
  }
}); 