import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  ActivityIndicator,
  Dimensions 
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { FoodItem } from './SavedMealsComponent';

interface NutritionDay {
  date: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  goalCalories?: number;
  goalProtein?: number;
  goalCarbs?: number;
  goalFat?: number;
}

interface NutritionChartsComponentProps {
  startDate?: string; // ISO date string, defaults to 7 days ago
  endDate?: string; // ISO date string, defaults to today
  onDateRangeChange?: (startDate: string, endDate: string) => void;
}

export function NutritionChartsComponent({
  startDate,
  endDate,
  onDateRangeChange
}: NutritionChartsComponentProps) {
  const { colors, isDark } = useTheme();
  const [loading, setLoading] = useState(true);
  const [nutritionData, setNutritionData] = useState<NutritionDay[]>([]);
  const [activePeriod, setActivePeriod] = useState<'7days' | '30days' | 'custom'>('7days');
  const [activeChart, setActiveChart] = useState<'calories' | 'macros' | 'distribution'>('calories');
  const [periodDropdownOpen, setPeriodDropdownOpen] = useState(false);
  
  const windowWidth = Dimensions.get('window').width;
  const chartWidth = windowWidth - 40; // Accounting for padding
  
  useEffect(() => {
    loadNutritionData();
  }, [startDate, endDate, activePeriod]);
  
  const loadNutritionData = async () => {
    setLoading(true);
    
    try {
      // In a real app, this would fetch from an API or database
      // For this demo, we'll use mock data
      setTimeout(() => {
        const mockData = generateMockData();
        setNutritionData(mockData);
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Failed to load nutrition data', error);
      setLoading(false);
    }
  };
  
  const generateMockData = (): NutritionDay[] => {
    // Generate some realistic mock data for the selected period
    let days = 7;
    if (activePeriod === '30days') days = 30;
    
    const endDateObj = endDate ? new Date(endDate) : new Date();
    const data: NutritionDay[] = [];
    
    for (let i = 0; i < days; i++) {
      const date = new Date(endDateObj);
      date.setDate(date.getDate() - i);
      
      // Create realistic variations in the data
      const dayOfWeek = date.getDay();
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
      
      // Base values
      let calories = 1800 + Math.random() * 600;
      let protein = 90 + Math.random() * 40;
      let carbs = 180 + Math.random() * 70;
      let fat = 60 + Math.random() * 30;
      
      // Weekend adjustment (people tend to eat more on weekends)
      if (isWeekend) {
        calories += 200 + Math.random() * 300;
        carbs += 30 + Math.random() * 20;
        fat += 15 + Math.random() * 10;
      }
      
      // Occasional low days
      if (Math.random() > 0.8) {
        calories *= 0.7;
        protein *= 0.7;
        carbs *= 0.6;
        fat *= 0.7;
      }
      
      // Round values
      calories = Math.round(calories);
      protein = Math.round(protein);
      carbs = Math.round(carbs);
      fat = Math.round(fat);
      
      data.push({
        date: date.toISOString(),
        calories,
        protein,
        carbs,
        fat,
        goalCalories: 2200,
        goalProtein: 120,
        goalCarbs: 220,
        goalFat: 70
      });
    }
    
    // Sort by date, oldest first
    return data.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  };
  
  const getFormattedDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };
  
  const getAverages = () => {
    if (nutritionData.length === 0) return null;
    
    const total = nutritionData.reduce(
      (acc, day) => {
        return {
          calories: acc.calories + day.calories,
          protein: acc.protein + day.protein,
          carbs: acc.carbs + day.carbs,
          fat: acc.fat + day.fat
        };
      },
      { calories: 0, protein: 0, carbs: 0, fat: 0 }
    );
    
    return {
      calories: Math.round(total.calories / nutritionData.length),
      protein: Math.round(total.protein / nutritionData.length),
      carbs: Math.round(total.carbs / nutritionData.length),
      fat: Math.round(total.fat / nutritionData.length)
    };
  };
  
  const getTotals = () => {
    if (nutritionData.length === 0) return null;
    
    return nutritionData.reduce(
      (acc, day) => {
        return {
          calories: acc.calories + day.calories,
          protein: acc.protein + day.protein,
          carbs: acc.carbs + day.carbs,
          fat: acc.fat + day.fat
        };
      },
      { calories: 0, protein: 0, carbs: 0, fat: 0 }
    );
  };
  
  const getGoalPercentages = () => {
    if (nutritionData.length === 0) return null;
    
    const averages = getAverages();
    if (!averages) return null;
    
    const goalDay = nutritionData[0]; // Use the first day's goals
    
    return {
      calories: Math.round((averages.calories / (goalDay.goalCalories || 2000)) * 100),
      protein: Math.round((averages.protein / (goalDay.goalProtein || 100)) * 100),
      carbs: Math.round((averages.carbs / (goalDay.goalCarbs || 200)) * 100),
      fat: Math.round((averages.fat / (goalDay.goalFat || 60)) * 100)
    };
  };
  
  const renderCaloriesChart = () => {
    if (nutritionData.length === 0) return null;
    
    const barMaxHeight = 200;
    const maxCalories = Math.max(...nutritionData.map(day => day.calories));
    const goalLine = nutritionData[0].goalCalories || 2000;
    
    return (
      <View style={styles.chartContainer}>
        <Text style={[styles.chartTitle, { color: colors.text }]}>
          Daily Calories
        </Text>
        
        <View style={styles.caloriesChart}>
          <View style={styles.chartBars}>
            {nutritionData.map((day, index) => {
              const barHeight = (day.calories / maxCalories) * barMaxHeight;
              const isOverGoal = day.calories > goalLine;
              
              return (
                <View key={index} style={styles.barContainer}>
                  <View style={styles.barLabelContainer}>
                    <Text style={[styles.barValue, { color: colors.text }]}>
                      {day.calories}
                    </Text>
                  </View>
                  
                  <View style={styles.bar}>
                    <View 
                      style={[
                        styles.barFill, 
                        { 
                          height: barHeight, 
                          backgroundColor: isOverGoal ? colors.warning : colors.primary 
                        }
                      ]} 
                    />
                  </View>
                  
                  <Text style={[styles.barDate, { color: colors.textSecondary }]}>
                    {getFormattedDate(day.date)}
                  </Text>
                </View>
              );
            })}
          </View>
          
          {/* Goal line */}
          <View 
            style={[
              styles.goalLine, 
              { 
                top: barMaxHeight - ((goalLine / maxCalories) * barMaxHeight),
                borderColor: colors.textSecondary 
              }
            ]}
          >
            <Text style={[styles.goalLabel, { color: colors.textSecondary, backgroundColor: isDark ? colors.background : 'white' }]}>
              Goal: {goalLine}
            </Text>
          </View>
        </View>
      </View>
    );
  };
  
  const renderMacrosChart = () => {
    if (nutritionData.length === 0) return null;
    
    // Chart dimensions
    const chartHeight = 200;
    const barWidth = Math.min(50, (chartWidth - 80) / (nutritionData.length * 3));
    const barGroupWidth = barWidth * 3 + 10;
    
    // Find max values for scaling
    const maxProtein = Math.max(...nutritionData.map(day => day.protein));
    const maxCarbs = Math.max(...nutritionData.map(day => day.carbs));
    const maxFat = Math.max(...nutritionData.map(day => day.fat));
    const maxValue = Math.max(maxProtein, maxCarbs, maxFat);
    
    return (
      <View style={styles.chartContainer}>
        <Text style={[styles.chartTitle, { color: colors.text }]}>
          Macronutrient Breakdown
        </Text>
        
        <View style={styles.legendContainer}>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: colors.primary }]} />
            <Text style={[styles.legendText, { color: colors.text }]}>Protein</Text>
          </View>
          
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: colors.warning }]} />
            <Text style={[styles.legendText, { color: colors.text }]}>Carbs</Text>
          </View>
          
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: colors.danger }]} />
            <Text style={[styles.legendText, { color: colors.text }]}>Fat</Text>
          </View>
        </View>
        
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.macrosChartContainer}
        >
          {nutritionData.map((day, index) => {
            const proteinHeight = (day.protein / maxValue) * chartHeight;
            const carbsHeight = (day.carbs / maxValue) * chartHeight;
            const fatHeight = (day.fat / maxValue) * chartHeight;
            
            return (
              <View key={index} style={[styles.barGroup, { width: barGroupWidth }]}>
                <View style={styles.macroBarContainer}>
                  <Text style={[styles.macroBarValue, { color: colors.textSecondary }]}>
                    {day.protein}g
                  </Text>
                  <View style={styles.macroBar}>
                    <View 
                      style={[
                        styles.macroBarFill, 
                        { 
                          height: proteinHeight, 
                          backgroundColor: colors.primary,
                          width: barWidth 
                        }
                      ]} 
                    />
                  </View>
                </View>
                
                <View style={styles.macroBarContainer}>
                  <Text style={[styles.macroBarValue, { color: colors.textSecondary }]}>
                    {day.carbs}g
                  </Text>
                  <View style={styles.macroBar}>
                    <View 
                      style={[
                        styles.macroBarFill, 
                        { 
                          height: carbsHeight, 
                          backgroundColor: colors.warning,
                          width: barWidth 
                        }
                      ]} 
                    />
                  </View>
                </View>
                
                <View style={styles.macroBarContainer}>
                  <Text style={[styles.macroBarValue, { color: colors.textSecondary }]}>
                    {day.fat}g
                  </Text>
                  <View style={styles.macroBar}>
                    <View 
                      style={[
                        styles.macroBarFill, 
                        { 
                          height: fatHeight, 
                          backgroundColor: colors.danger,
                          width: barWidth 
                        }
                      ]} 
                    />
                  </View>
                </View>
                
                <Text style={[styles.barGroupDate, { color: colors.textSecondary }]}>
                  {getFormattedDate(day.date)}
                </Text>
              </View>
            );
          })}
        </ScrollView>
      </View>
    );
  };
  
  const renderDistributionChart = () => {
    if (nutritionData.length === 0) return null;
    
    const averages = getAverages();
    if (!averages) return null;
    
    // Calculate macronutrient calories
    const proteinCalories = averages.protein * 4;
    const carbsCalories = averages.carbs * 4;
    const fatCalories = averages.fat * 9;
    const totalCalories = proteinCalories + carbsCalories + fatCalories;
    
    // Calculate percentages
    const proteinPercentage = Math.round((proteinCalories / totalCalories) * 100);
    const carbsPercentage = Math.round((carbsCalories / totalCalories) * 100);
    const fatPercentage = Math.round((fatCalories / totalCalories) * 100);
    
    // For a simple visualization, we'll use a horizontal bar
    return (
      <View style={styles.chartContainer}>
        <Text style={[styles.chartTitle, { color: colors.text }]}>
          Average Macro Distribution
        </Text>
        
        <View style={styles.distributionContainer}>
          <View style={styles.distributionChart}>
            <View 
              style={[
                styles.distributionSegment, 
                { 
                  backgroundColor: colors.primary,
                  width: `${proteinPercentage}%` 
                }
              ]} 
            />
            <View 
              style={[
                styles.distributionSegment, 
                { 
                  backgroundColor: colors.warning,
                  width: `${carbsPercentage}%` 
                }
              ]} 
            />
            <View 
              style={[
                styles.distributionSegment, 
                { 
                  backgroundColor: colors.danger,
                  width: `${fatPercentage}%` 
                }
              ]} 
            />
          </View>
          
          <View style={styles.distributionLabels}>
            <View style={styles.distributionLabel}>
              <View style={[styles.legendColor, { backgroundColor: colors.primary }]} />
              <Text style={[styles.distributionLabelText, { color: colors.text }]}>
                Protein: {proteinPercentage}% ({averages.protein}g)
              </Text>
            </View>
            
            <View style={styles.distributionLabel}>
              <View style={[styles.legendColor, { backgroundColor: colors.warning }]} />
              <Text style={[styles.distributionLabelText, { color: colors.text }]}>
                Carbs: {carbsPercentage}% ({averages.carbs}g)
              </Text>
            </View>
            
            <View style={styles.distributionLabel}>
              <View style={[styles.legendColor, { backgroundColor: colors.danger }]} />
              <Text style={[styles.distributionLabelText, { color: colors.text }]}>
                Fat: {fatPercentage}% ({averages.fat}g)
              </Text>
            </View>
          </View>
          
          <View style={[styles.summaryCard, { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }]}>
            <Text style={[styles.summaryTitle, { color: colors.text }]}>
              Daily Averages
            </Text>
            
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                Calories:
              </Text>
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {averages.calories} kcal
              </Text>
            </View>
            
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                Protein:
              </Text>
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {averages.protein}g
              </Text>
            </View>
            
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                Carbs:
              </Text>
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {averages.carbs}g
              </Text>
            </View>
            
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                Fat:
              </Text>
              <Text style={[styles.summaryValue, { color: colors.text }]}>
                {averages.fat}g
              </Text>
            </View>
          </View>
        </View>
      </View>
    );
  };
  
  const renderPeriodSelector = () => {
    return (
      <View style={styles.periodSelector}>
        <TouchableOpacity
          style={styles.periodButton}
          onPress={() => setPeriodDropdownOpen(!periodDropdownOpen)}
        >
          <Feather name="calendar" size={16} color={colors.text} style={styles.periodIcon} />
          <Text style={[styles.periodButtonText, { color: colors.text }]}>
            {activePeriod === '7days' ? 'Last 7 Days' : 
             activePeriod === '30days' ? 'Last 30 Days' : 'Custom Range'}
          </Text>
          <Feather name="chevron-down" size={16} color={colors.text} />
        </TouchableOpacity>
        
        {periodDropdownOpen && (
          <View style={[styles.periodDropdown, { backgroundColor: isDark ? colors.card : 'white' }]}>
            <TouchableOpacity
              style={[
                styles.periodOption,
                activePeriod === '7days' && { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }
              ]}
              onPress={() => {
                setActivePeriod('7days');
                setPeriodDropdownOpen(false);
              }}
            >
              <Text style={[styles.periodOptionText, { color: colors.text }]}>
                Last 7 Days
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.periodOption,
                activePeriod === '30days' && { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }
              ]}
              onPress={() => {
                setActivePeriod('30days');
                setPeriodDropdownOpen(false);
              }}
            >
              <Text style={[styles.periodOptionText, { color: colors.text }]}>
                Last 30 Days
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.periodOption,
                activePeriod === 'custom' && { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }
              ]}
              onPress={() => {
                setActivePeriod('custom');
                setPeriodDropdownOpen(false);
                
                // In a real app, this would open a date picker
                // For now, we'll just simulate it
                if (onDateRangeChange) {
                  const end = new Date();
                  const start = new Date();
                  start.setDate(start.getDate() - 14); // Custom 14 days
                  onDateRangeChange(start.toISOString(), end.toISOString());
                }
              }}
            >
              <Text style={[styles.periodOptionText, { color: colors.text }]}>
                Custom Range
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };
  
  const renderChartTypeSelector = () => {
    return (
      <View style={styles.chartTypeSelector}>
        <TouchableOpacity
          style={[
            styles.chartTypeButton,
            activeChart === 'calories' && { backgroundColor: colors.primary }
          ]}
          onPress={() => setActiveChart('calories')}
        >
          <BarChart2 
            size={16} 
            color={activeChart === 'calories' ? 'white' : colors.text} 
            style={styles.chartTypeIcon} 
          />
          <Text 
            style={[
              styles.chartTypeText, 
              { color: activeChart === 'calories' ? 'white' : colors.text }
            ]}
          >
            Calories
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.chartTypeButton,
            activeChart === 'macros' && { backgroundColor: colors.primary }
          ]}
          onPress={() => setActiveChart('macros')}
        >
          <Feather name="trending-up" size={16} color={activeChart === 'macros' ? 'white' : colors.text} style={styles.chartTypeIcon} />
          <Text 
            style={[
              styles.chartTypeText, 
              { color: activeChart === 'macros' ? 'white' : colors.text }
            ]}
          >
            Macros
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.chartTypeButton,
            activeChart === 'distribution' && { backgroundColor: colors.primary }
          ]}
          onPress={() => setActiveChart('distribution')}
        >
          <Feather name="pie-chart" size={16} color={activeChart === 'distribution' ? 'white' : colors.text} style={styles.chartTypeIcon} />
          <Text 
            style={[
              styles.chartTypeText, 
              { color: activeChart === 'distribution' ? 'white' : colors.text }
            ]}
          >
            Distribution
          </Text>
        </TouchableOpacity>
      </View>
    );
  };
  
  return (
    <View style={styles.container}>
      {renderPeriodSelector()}
      
      {renderChartTypeSelector()}
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading nutrition data...
          </Text>
        </View>
      ) : (
        <ScrollView contentContainerStyle={styles.chartScroll}>
          {activeChart === 'calories' && renderCaloriesChart()}
          {activeChart === 'macros' && renderMacrosChart()}
          {activeChart === 'distribution' && renderDistributionChart()}
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  periodSelector: {
    position: 'relative',
    zIndex: 10,
    marginBottom: 16,
  },
  periodButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  periodIcon: {
    marginRight: 8,
  },
  periodButtonText: {
    flex: 1,
    fontSize: 16,
  },
  periodDropdown: {
    position: 'absolute',
    top: 45,
    left: 0,
    right: 0,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 10,
  },
  periodOption: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  periodOptionText: {
    fontSize: 16,
  },
  chartTypeSelector: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  chartTypeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 8,
    marginHorizontal: 4,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  chartTypeIcon: {
    marginRight: 6,
  },
  chartTypeText: {
    fontSize: 14,
    fontWeight: '500',
  },
  chartScroll: {
    paddingBottom: 20,
  },
  chartContainer: {
    marginBottom: 24,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  caloriesChart: {
    height: 260,
    position: 'relative',
  },
  chartBars: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 240,
  },
  barContainer: {
    alignItems: 'center',
  },
  barLabelContainer: {
    height: 20,
  },
  barValue: {
    fontSize: 12,
    fontWeight: '500',
  },
  bar: {
    width: 24,
    height: 200,
    justifyContent: 'flex-end',
    marginVertical: 8,
  },
  barFill: {
    width: '100%',
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
  },
  barDate: {
    fontSize: 10,
    width: 40,
    textAlign: 'center',
  },
  goalLine: {
    position: 'absolute',
    borderTopWidth: 1,
    borderStyle: 'dashed',
    left: 0,
    right: 0,
  },
  goalLabel: {
    position: 'absolute',
    right: 0,
    top: -10,
    fontSize: 10,
    paddingHorizontal: 4,
  },
  legendContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 4,
  },
  legendText: {
    fontSize: 12,
  },
  macrosChartContainer: {
    flexDirection: 'row',
    paddingBottom: 20,
  },
  barGroup: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  macroBarContainer: {
    alignItems: 'center',
    marginHorizontal: 2,
  },
  macroBarValue: {
    fontSize: 10,
    marginBottom: 4,
  },
  macroBar: {
    height: 200,
    justifyContent: 'flex-end',
  },
  macroBarFill: {
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
  },
  barGroupDate: {
    position: 'absolute',
    bottom: 0,
    fontSize: 10,
    textAlign: 'center',
    width: '100%',
  },
  distributionContainer: {
    paddingBottom: 10,
  },
  distributionChart: {
    height: 30,
    flexDirection: 'row',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 16,
  },
  distributionSegment: {
    height: '100%',
  },
  distributionLabels: {
    marginBottom: 24,
  },
  distributionLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  distributionLabelText: {
    fontSize: 14,
  },
  summaryCard: {
    borderRadius: 8,
    padding: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default NutritionChartsComponent; 