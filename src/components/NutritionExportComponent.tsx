import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Platform,
  ScrollView,
  Switch,
  Modal
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';

interface ExportSettings {
  includeCalories: boolean;
  includeMacros: boolean;
  includeMicronutrients: boolean;
  includeMealBreakdown: boolean;
  includeWater: boolean;
  includeExercise: boolean;
  includeNotes: boolean;
  dateRange: 'all' | 'custom' | 'week' | 'month' | 'year';
  startDate?: string; // ISO date string
  endDate?: string; // ISO date string
  format: 'pdf' | 'csv' | 'xlsx';
}

interface NutritionExportComponentProps {
  onShare?: (fileUri: string, fileType: string) => void;
  onDateRangeSelect?: (startDate: string, endDate: string) => void;
}

export function NutritionExportComponent({
  onShare,
  onDateRangeSelect
}: NutritionExportComponentProps) {
  const { colors, isDark } = useTheme();
  
  const [settings, setSettings] = useState<ExportSettings>({
    includeCalories: true,
    includeMacros: true,
    includeMicronutrients: false,
    includeMealBreakdown: true,
    includeWater: false,
    includeExercise: false,
    includeNotes: false,
    dateRange: 'month',
    format: 'pdf'
  });
  
  const [isExporting, setIsExporting] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [lastExportedFile, setLastExportedFile] = useState<string | null>(null);
  
  const toggleSetting = (setting: keyof ExportSettings, value?: any) => {
    setSettings(prev => ({
      ...prev,
      [setting]: value !== undefined ? value : !prev[setting]
    }));
  };
  
  const getDateRangeLabel = () => {
    switch (settings.dateRange) {
      case 'week':
        return 'Last 7 days';
      case 'month':
        return 'Last 30 days';
      case 'year':
        return 'Last 12 months';
      case 'all':
        return 'All time';
      case 'custom':
        return 'Custom range';
      default:
        return 'Select date range';
    }
  };
  
  const getFormatLabel = () => {
    switch (settings.format) {
      case 'pdf':
        return 'PDF Document';
      case 'csv':
        return 'CSV Spreadsheet';
      case 'xlsx':
        return 'Excel Spreadsheet';
      default:
        return 'Select format';
    }
  };
  
  const getFormatIcon = () => {
    switch (settings.format) {
      case 'pdf':
        return <Feather name="file-text" size={24} color={colors.danger} />;
      case 'csv':
        return <Feather name="file-text" size={24} color={colors.success} />;
      case 'xlsx':
        return <Feather name="file-text" size={24} color={colors.primary} />;
      default:
        return <Feather name="file-text" size={24} color={colors.text} />;
    }
  };
  
  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      // In a real app, this would generate an actual file
      // For this demo, we'll simulate file creation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Generate fake file path based on format
      const fileName = `nutrition_export_${new Date().getTime()}.${settings.format}`;
      const filePath = `${FileSystem.documentDirectory}${fileName}`;
      
      // In a real app, we would write to this file
      // For demo purposes, we'll just log it
      console.log(`Exported file to: ${filePath}`);
      
      setLastExportedFile(filePath);
      
      Alert.alert(
        'Export Complete',
        `Your nutrition data has been exported as a ${settings.format.toUpperCase()} file.`,
        [
          {
            text: 'Share',
            onPress: () => handleShare(filePath)
          },
          {
            text: 'OK',
            style: 'default'
          }
        ]
      );
    } catch (error) {
      console.error('Export failed:', error);
      Alert.alert('Export Failed', 'There was an error exporting your nutrition data.');
    } finally {
      setIsExporting(false);
    }
  };
  
  const handleShare = async (filePath: string) => {
    try {
      // In a real app, we would check if the file exists
      // For this demo, we'll simulate sharing
      if (Platform.OS === 'web') {
        Alert.alert(
          'Sharing not available',
          'File sharing is not available on web platforms.'
        );
        return;
      }
      
      const isAvailable = await Sharing.isAvailableAsync();
      
      if (isAvailable) {
        await Sharing.shareAsync(filePath);
      } else {
        Alert.alert(
          'Sharing not available',
          'Sharing is not available on this device.'
        );
      }
      
      if (onShare) {
        onShare(filePath, settings.format);
      }
    } catch (error) {
      console.error('Sharing failed:', error);
      Alert.alert('Sharing Failed', 'There was an error sharing your file.');
    }
  };
  
  const handleCustomDateRange = () => {
    // In a real app, this would open a date picker
    // For this demo, we'll simulate it with a fixed range
    const end = new Date();
    const start = new Date();
    start.setDate(start.getDate() - 14); // Two weeks
    
    if (onDateRangeSelect) {
      onDateRangeSelect(start.toISOString(), end.toISOString());
    }
    
    setSettings(prev => ({
      ...prev,
      dateRange: 'custom',
      startDate: start.toISOString(),
      endDate: end.toISOString()
    }));
  };
  
  const renderSettingsModal = () => {
    return (
      <Modal
        visible={showSettingsModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowSettingsModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: isDark ? colors.card : 'white' }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Export Settings
              </Text>
              
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowSettingsModal(false)}
              >
                <Feather name="x" size={20} color={colors.text} />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.settingsScroll}>
              <View style={styles.settingSection}>
                <Text style={[styles.settingSectionTitle, { color: colors.text }]}>
                  Date Range
                </Text>
                
                <View style={styles.radioGroup}>
                  {(['week', 'month', 'year', 'all', 'custom'] as const).map((option) => (
                    <TouchableOpacity
                      key={option}
                      style={styles.radioOption}
                      onPress={() => {
                        toggleSetting('dateRange', option);
                        if (option === 'custom') {
                          handleCustomDateRange();
                        }
                      }}
                    >
                      <View style={[
                        styles.radioButton,
                        settings.dateRange === option && { borderColor: colors.primary }
                      ]}>
                        {settings.dateRange === option && (
                          <View style={[styles.radioButtonInner, { backgroundColor: colors.primary }]} />
                        )}
                      </View>
                      <Text style={[styles.radioLabel, { color: colors.text }]}>
                        {option === 'week' ? 'Last 7 days' :
                         option === 'month' ? 'Last 30 days' :
                         option === 'year' ? 'Last 12 months' :
                         option === 'all' ? 'All time' : 'Custom range'}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
              
              <View style={styles.settingSection}>
                <Text style={[styles.settingSectionTitle, { color: colors.text }]}>
                  Export Format
                </Text>
                
                <View style={styles.radioGroup}>
                  {(['pdf', 'csv', 'xlsx'] as const).map((option) => (
                    <TouchableOpacity
                      key={option}
                      style={styles.radioOption}
                      onPress={() => toggleSetting('format', option)}
                    >
                      <View style={[
                        styles.radioButton,
                        settings.format === option && { borderColor: colors.primary }
                      ]}>
                        {settings.format === option && (
                          <View style={[styles.radioButtonInner, { backgroundColor: colors.primary }]} />
                        )}
                      </View>
                      <Text style={[styles.radioLabel, { color: colors.text }]}>
                        {option.toUpperCase()}
                        {option === 'pdf' ? ' Document' :
                         option === 'csv' ? ' Spreadsheet' :
                         option === 'xlsx' ? ' Excel' : ''}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
              
              <View style={styles.settingSection}>
                <Text style={[styles.settingSectionTitle, { color: colors.text }]}>
                  Data to Include
                </Text>
                
                <View style={styles.switchGroup}>
                  <View style={styles.switchOption}>
                    <Text style={[styles.switchLabel, { color: colors.text }]}>
                      Calories
                    </Text>
                    <Switch
                      value={settings.includeCalories}
                      onValueChange={(value) => toggleSetting('includeCalories', value)}
                      trackColor={{ false: colors.subtle, true: colors.primary }}
                      thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
                      ios_backgroundColor={colors.subtle}
                    />
                  </View>
                  
                  <View style={styles.switchOption}>
                    <Text style={[styles.switchLabel, { color: colors.text }]}>
                      Macronutrients (protein, carbs, fat)
                    </Text>
                    <Switch
                      value={settings.includeMacros}
                      onValueChange={(value) => toggleSetting('includeMacros', value)}
                      trackColor={{ false: colors.subtle, true: colors.primary }}
                      thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
                      ios_backgroundColor={colors.subtle}
                    />
                  </View>
                  
                  <View style={styles.switchOption}>
                    <Text style={[styles.switchLabel, { color: colors.text }]}>
                      Micronutrients (vitamins, minerals)
                    </Text>
                    <Switch
                      value={settings.includeMicronutrients}
                      onValueChange={(value) => toggleSetting('includeMicronutrients', value)}
                      trackColor={{ false: colors.subtle, true: colors.primary }}
                      thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
                      ios_backgroundColor={colors.subtle}
                    />
                  </View>
                  
                  <View style={styles.switchOption}>
                    <Text style={[styles.switchLabel, { color: colors.text }]}>
                      Meal breakdown
                    </Text>
                    <Switch
                      value={settings.includeMealBreakdown}
                      onValueChange={(value) => toggleSetting('includeMealBreakdown', value)}
                      trackColor={{ false: colors.subtle, true: colors.primary }}
                      thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
                      ios_backgroundColor={colors.subtle}
                    />
                  </View>
                  
                  <View style={styles.switchOption}>
                    <Text style={[styles.switchLabel, { color: colors.text }]}>
                      Water intake
                    </Text>
                    <Switch
                      value={settings.includeWater}
                      onValueChange={(value) => toggleSetting('includeWater', value)}
                      trackColor={{ false: colors.subtle, true: colors.primary }}
                      thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
                      ios_backgroundColor={colors.subtle}
                    />
                  </View>
                  
                  <View style={styles.switchOption}>
                    <Text style={[styles.switchLabel, { color: colors.text }]}>
                      Exercise data
                    </Text>
                    <Switch
                      value={settings.includeExercise}
                      onValueChange={(value) => toggleSetting('includeExercise', value)}
                      trackColor={{ false: colors.subtle, true: colors.primary }}
                      thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
                      ios_backgroundColor={colors.subtle}
                    />
                  </View>
                  
                  <View style={styles.switchOption}>
                    <Text style={[styles.switchLabel, { color: colors.text }]}>
                      Notes and comments
                    </Text>
                    <Switch
                      value={settings.includeNotes}
                      onValueChange={(value) => toggleSetting('includeNotes', value)}
                      trackColor={{ false: colors.subtle, true: colors.primary }}
                      thumbColor={Platform.OS === 'ios' ? undefined : 'white'}
                      ios_backgroundColor={colors.subtle}
                    />
                  </View>
                </View>
              </View>
            </ScrollView>
            
            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.closeModalButton, { backgroundColor: colors.subtle }]}
                onPress={() => setShowSettingsModal(false)}
              >
                <Text style={[styles.buttonText, { color: colors.text }]}>
                  Cancel
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.applyButton, { backgroundColor: colors.primary }]}
                onPress={() => setShowSettingsModal(false)}
              >
                <MaterialIcons name="check-circle" size={16} style={styles.buttonIcon} />
                <Text style={[styles.buttonText, { color: 'white' }]}>
                  Apply Settings
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };
  
  return (
    <View style={styles.container}>
      <View style={[styles.exportCard, { backgroundColor: isDark ? colors.card : 'white' }]}>
        <View style={styles.exportHeader}>
          <View style={styles.exportHeaderContent}>
            <Text style={[styles.exportTitle, { color: colors.text }]}>
              Export Nutrition Data
            </Text>
            <Text style={[styles.exportDescription, { color: colors.textSecondary }]}>
              Generate reports for personal tracking or share with your dietitian
            </Text>
          </View>
          
          <TouchableOpacity
            style={styles.settingsButton}
            onPress={() => setShowSettingsModal(true)}
          >
            <Feather name="settings" size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
        
        <View style={styles.optionsSection}>
          <View style={[styles.optionItem, { borderBottomColor: colors.border }]}>
            <View style={styles.optionIconContainer}>
              <Feather name="calendar" size={24} color={colors.primary} />
            </View>
            
            <View style={styles.optionContent}>
              <Text style={[styles.optionLabel, { color: colors.textSecondary }]}>
                Date Range
              </Text>
              <Text style={[styles.optionValue, { color: colors.text }]}>
                {getDateRangeLabel()}
              </Text>
            </View>
            
            <TouchableOpacity
              style={styles.optionAction}
              onPress={() => handleCustomDateRange()}
            >
              <Text style={[styles.optionActionText, { color: colors.primary }]}>
                Change
              </Text>
            </TouchableOpacity>
          </View>
          
          <View style={[styles.optionItem, { borderBottomColor: colors.border }]}>
            <View style={styles.optionIconContainer}>
              {getFormatIcon()}
            </View>
            
            <View style={styles.optionContent}>
              <Text style={[styles.optionLabel, { color: colors.textSecondary }]}>
                File Format
              </Text>
              <Text style={[styles.optionValue, { color: colors.text }]}>
                {getFormatLabel()}
              </Text>
            </View>
            
            <TouchableOpacity
              style={styles.optionAction}
              onPress={() => setShowSettingsModal(true)}
            >
              <Text style={[styles.optionActionText, { color: colors.primary }]}>
                Change
              </Text>
            </TouchableOpacity>
          </View>
          
          <View style={[styles.optionItem, { borderBottomWidth: 0 }]}>
            <View style={styles.optionIconContainer}>
              <Feather name="file-text" size={24} color={colors.text} />
            </View>
            
            <View style={styles.optionContent}>
              <Text style={[styles.optionLabel, { color: colors.textSecondary }]}>
                Data Included
              </Text>
              <Text style={[styles.optionValue, { color: colors.text }]}>
                {[
                  settings.includeCalories && 'Calories',
                  settings.includeMacros && 'Macros',
                  settings.includeMicronutrients && 'Micronutrients',
                  settings.includeMealBreakdown && 'Meals',
                  settings.includeWater && 'Water',
                  settings.includeExercise && 'Exercise',
                  settings.includeNotes && 'Notes'
                ].filter(Boolean).join(', ')}
              </Text>
            </View>
            
            <TouchableOpacity
              style={styles.optionAction}
              onPress={() => setShowSettingsModal(true)}
            >
              <Text style={[styles.optionActionText, { color: colors.primary }]}>
                Change
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.buttonContainer}>
          {lastExportedFile && (
            <TouchableOpacity
              style={[styles.shareButton, { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }]}
              onPress={() => handleShare(lastExportedFile)}
            >
              <Feather name="share-2" size={18} color={colors.text} style={styles.buttonIcon} />
              <Text style={[styles.buttonText, { color: colors.text }]}>
                Share Last Export
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[
              styles.exportButton, 
              { backgroundColor: colors.primary },
              isExporting && { opacity: 0.7 }
            ]}
            onPress={handleExport}
            disabled={isExporting}
          >
            {isExporting ? (
              <ActivityIndicator size="small" color="white" style={styles.buttonIcon} />
            ) : (
              <Feather name="download" size={18} style={styles.buttonIcon} />
            )}
            <Text style={[styles.buttonText, { color: 'white' }]}>
              {isExporting ? 'Exporting...' : 'Export Data'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      
      {renderSettingsModal()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  exportCard: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  exportHeader: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  exportHeaderContent: {
    flex: 1,
  },
  exportTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  exportDescription: {
    fontSize: 14,
  },
  settingsButton: {
    padding: 8,
  },
  optionsSection: {
    paddingHorizontal: 16,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  optionIconContainer: {
    width: 40,
    alignItems: 'center',
    marginRight: 12,
  },
  optionContent: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  optionValue: {
    fontSize: 16,
  },
  optionAction: {
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  optionActionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: 16,
    justifyContent: 'flex-end',
  },
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
    justifyContent: 'center',
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  settingsScroll: {
    padding: 16,
    maxHeight: '60%',
  },
  settingSection: {
    marginBottom: 24,
  },
  settingSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  radioGroup: {
    marginBottom: 8,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: 'rgba(0, 0, 0, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  radioLabel: {
    fontSize: 16,
  },
  switchGroup: {
    marginBottom: 8,
  },
  switchOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchLabel: {
    fontSize: 16,
    flex: 1,
    paddingRight: 16,
  },
  modalFooter: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  closeModalButton: {
    flex: 1,
    height: 50,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  applyButton: {
    flex: 2,
    flexDirection: 'row',
    height: 50,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default NutritionExportComponent; 