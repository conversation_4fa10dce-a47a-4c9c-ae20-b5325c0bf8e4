import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Switch,
  ScrollView,
  Alert,
  Platform
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { exportNutritionSummary, shareNutritionSummary } from '@/services/pdfExportService';
import { Meal } from '@/types/food';
import { Feather } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';

interface NutritionExportModalProps {
  meals: Meal[];
  onClose: () => void;
}

export default function NutritionExportModal({ meals, onClose }: NutritionExportModalProps) {
  const { colors, isDark } = useTheme();
  const [loading, setLoading] = useState(false);
  const [includeRecipes, setIncludeRecipes] = useState(true);
  const [includeMealBreakdown, setIncludeMealBreakdown] = useState(true);
  const [includeGoalComparison, setIncludeGoalComparison] = useState(true);
  
  // Date selection
  const [startDate, setStartDate] = useState(() => {
    // Default to 7 days ago
    const date = new Date();
    date.setDate(date.getDate() - 7);
    return date;
  });
  const [endDate, setEndDate] = useState(new Date()); // Default to today
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  
  const handleExport = async () => {
    try {
      setLoading(true);
      
      // Check if there are meals in the specified date range
      const mealsInRange = meals.filter(meal => {
        const mealDate = new Date(meal.date);
        return mealDate >= startDate && mealDate <= endDate;
      });
      
      if (mealsInRange.length === 0) {
        Alert.alert('No Data', 'There are no meals in the selected date range.');
        setLoading(false);
        return;
      }
      
      const exportOptions = {
        startDate,
        endDate,
        includeRecipes,
        includeMealBreakdown,
        includeGoalComparison,
        title: 'Nutrition Summary'
      };
      
      const pdfPath = await exportNutritionSummary(meals, exportOptions);
      
      if (!pdfPath) {
        throw new Error('Failed to generate PDF');
      }
      
      await shareNutritionSummary(pdfPath);
      
      Alert.alert('Success', 'Nutrition summary has been exported successfully.');
    } catch (error) {
      console.error('Error exporting nutrition summary:', error);
      Alert.alert('Export Failed', 'An error occurred while exporting your nutrition summary.');
    } finally {
      setLoading(false);
    }
  };
  
  const handleStartDateChange = (event: any, selectedDate?: Date) => {
    setShowStartDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setStartDate(selectedDate);
      
      // Adjust end date if start date is after end date
      if (selectedDate > endDate) {
        setEndDate(selectedDate);
      }
    }
  };
  
  const handleEndDateChange = (event: any, selectedDate?: Date) => {
    setShowEndDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setEndDate(selectedDate);
      
      // Adjust start date if end date is before start date
      if (selectedDate < startDate) {
        setStartDate(selectedDate);
      }
    }
  };
  
  return (
    <View style={[styles.container, { backgroundColor: isDark ? colors.background : '#f9f9f9' }]}>
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Export Nutrition Summary</Text>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Feather name="x" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>
      
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
      >
        <View style={[styles.card, { backgroundColor: isDark ? colors.card : 'white' }]}>
          <Text style={[styles.cardTitle, { color: colors.text }]}>Date Range</Text>
          
          <View style={styles.dateSection}>
            <Text style={[styles.dateLabel, { color: colors.textSecondary }]}>Start Date:</Text>
            <TouchableOpacity
              style={[styles.dateButton, { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }]}
              onPress={() => setShowStartDatePicker(true)}
            >
              <Feather name="calendar" size={16} color={colors.primary} style={styles.dateIcon} />
              <Text style={[styles.dateText, { color: colors.text }]}>
                {startDate.toLocaleDateString()}
              </Text>
            </TouchableOpacity>
            
            {showStartDatePicker && (
              <DateTimePicker
                value={startDate}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={handleStartDateChange}
                maximumDate={new Date()}
              />
            )}
          </View>
          
          <View style={styles.dateSection}>
            <Text style={[styles.dateLabel, { color: colors.textSecondary }]}>End Date:</Text>
            <TouchableOpacity
              style={[styles.dateButton, { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }]}
              onPress={() => setShowEndDatePicker(true)}
            >
              <Feather name="calendar" size={16} color={colors.primary} style={styles.dateIcon} />
              <Text style={[styles.dateText, { color: colors.text }]}>
                {endDate.toLocaleDateString()}
              </Text>
            </TouchableOpacity>
            
            {showEndDatePicker && (
              <DateTimePicker
                value={endDate}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={handleEndDateChange}
                maximumDate={new Date()}
                minimumDate={startDate}
              />
            )}
          </View>
        </View>
        
        <View style={[styles.card, { backgroundColor: isDark ? colors.card : 'white' }]}>
          <Text style={[styles.cardTitle, { color: colors.text }]}>Content Options</Text>
          
          <View style={styles.optionRow}>
            <Text style={[styles.optionText, { color: colors.text }]}>Include Recipe Details</Text>
            <Switch
              value={includeRecipes}
              onValueChange={setIncludeRecipes}
              trackColor={{ false: '#767577', true: colors.primary }}
              thumbColor="#f4f3f4"
            />
          </View>
          
          <View style={styles.optionRow}>
            <Text style={[styles.optionText, { color: colors.text }]}>Include Daily Breakdown</Text>
            <Switch
              value={includeMealBreakdown}
              onValueChange={setIncludeMealBreakdown}
              trackColor={{ false: '#767577', true: colors.primary }}
              thumbColor="#f4f3f4"
            />
          </View>
          
          <View style={styles.optionRow}>
            <Text style={[styles.optionText, { color: colors.text }]}>Include Goal Comparison</Text>
            <Switch
              value={includeGoalComparison}
              onValueChange={setIncludeGoalComparison}
              trackColor={{ false: '#767577', true: colors.primary }}
              thumbColor="#f4f3f4"
            />
          </View>
        </View>
        
        <View style={styles.previewSection}>
          <Text style={[styles.previewText, { color: colors.textSecondary }]}>
            Your nutrition summary will include data from {startDate.toLocaleDateString()} to {endDate.toLocaleDateString()}.
            {'\n\n'}
            This report will help you track your nutritional progress and can be shared with your healthcare provider or fitness coach.
          </Text>
        </View>
      </ScrollView>
      
      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.cancelButton, { borderColor: colors.border }]}
          onPress={onClose}
          disabled={loading}
        >
          <Text style={[styles.cancelButtonText, { color: colors.text }]}>Cancel</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.exportButton, { backgroundColor: colors.primary }]}
          onPress={handleExport}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <>
              <Feather name="file-text" size={18} style={styles.exportIcon} />
              <Text style={styles.exportButtonText}>Export PDF</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 60,
    paddingHorizontal: 16,
    position: 'relative',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    position: 'absolute',
    right: 16,
    padding: 8,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: 16,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  dateSection: {
    marginBottom: 16,
  },
  dateLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
  },
  dateIcon: {
    marginRight: 8,
  },
  dateText: {
    fontSize: 16,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
  },
  optionText: {
    fontSize: 16,
  },
  previewSection: {
    padding: 16,
    marginBottom: 16,
  },
  previewText: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  cancelButton: {
    flex: 1,
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  exportButton: {
    flex: 2,
    flexDirection: 'row',
    height: 50,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  exportIcon: {
    marginRight: 8,
  },
  exportButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
}); 