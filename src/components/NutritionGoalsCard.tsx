import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { getUserProfile, getAdjustedNutritionGoals } from '@/services/nutritionGoalService';

export default function NutritionGoalsCard() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  
  const [loading, setLoading] = useState(true);
  const [goals, setGoals] = useState({
    calorieGoal: 0,
    proteinGoal: 0,
    carbsGoal: 0,
    fatGoal: 0,
    waterGoal: 0,
  });
  const [baseGoals, setBaseGoals] = useState({
    calorieGoal: 0,
    proteinGoal: 0,
    carbsGoal: 0,
    fatGoal: 0,
    waterGoal: 0,
  });
  const [adjustmentReason, setAdjustmentReason] = useState<string | undefined>(undefined);
  const [showingAdjusted, setShowingAdjusted] = useState(true);
  
  useEffect(() => {
    loadGoals();
  }, []);
  
  const loadGoals = async () => {
    try {
      setLoading(true);
      
      // Load base profile goals
      const profile = await getUserProfile();
      
      if (profile) {
        const baseProfileGoals = {
          calorieGoal: profile.calorieGoal,
          proteinGoal: profile.proteinGoal,
          carbsGoal: profile.carbsGoal,
          fatGoal: profile.fatGoal,
          waterGoal: profile.waterGoal,
        };
        
        setBaseGoals(baseProfileGoals);
        setGoals(baseProfileGoals);
        
        // Load adjusted goals
        const adjustedGoals = await getAdjustedNutritionGoals();
        
        if (adjustedGoals) {
          setGoals(adjustedGoals);
          setAdjustmentReason(adjustedGoals.adjustmentReason);
        }
      }
    } catch (error) {
      console.error('Error loading nutrition goals:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const toggleGoalsView = () => {
    if (showingAdjusted) {
      // Switch to base goals
      setGoals(baseGoals);
    } else {
      // Get adjusted goals from state
      loadGoals();
    }
    
    setShowingAdjusted(!showingAdjusted);
  };
  
  const handleEditGoals = () => {
    router.push('/profile-screens/nutrition-goals');
  };
  
  // Calculate macronutrient percentages
  const calculateMacroPercentages = () => {
    const proteinCalories = goals.proteinGoal * 4; // 4 calories per gram
    const carbsCalories = goals.carbsGoal * 4; // 4 calories per gram
    const fatCalories = goals.fatGoal * 9; // 9 calories per gram
    const totalCalories = proteinCalories + carbsCalories + fatCalories;
    
    return {
      protein: Math.round((proteinCalories / totalCalories) * 100),
      carbs: Math.round((carbsCalories / totalCalories) * 100),
      fat: Math.round((fatCalories / totalCalories) * 100),
    };
  };
  
  const macroPercentages = calculateMacroPercentages();
  
  // Get color for macro bars
  const getColorForMacro = (macro: 'protein' | 'carbs' | 'fat') => {
    switch (macro) {
      case 'protein':
        return '#3B82F6'; // blue
      case 'carbs':
        return '#F59E0B'; // amber
      case 'fat':
        return '#10B981'; // green
      default:
        return colors.primary;
    }
  };
  
  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading your nutrition goals...</Text>
        </View>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Sliders size={20} color={colors.primary} style={styles.titleIcon} />
          <Text style={[styles.title, { color: colors.text }]}>Nutrition Goals</Text>
        </View>
        
        <View style={styles.headerActions}>
          {adjustmentReason && (
            <TouchableOpacity 
              style={[
                styles.toggleButton, 
                { 
                  backgroundColor: showingAdjusted ? colors.primary + '20' : 'transparent',
                  borderColor: colors.primary
                }
              ]}
              onPress={toggleGoalsView}
            >
              <Feather name="zap" size={16} color={colors.primary} style={styles.toggleIcon} />
              <Text style={[styles.toggleText, { color: colors.primary }]}>
                {showingAdjusted ? 'Adjusted' : 'Base'}
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity 
            style={[styles.editButton, { backgroundColor: colors.subtle }]}
            onPress={handleEditGoals}
          >
            <Feather name="edit" size={16} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>
      
      {adjustmentReason && showingAdjusted && (
        <View style={[styles.adjustmentBanner, { backgroundColor: colors.primary + '15' }]}>
          <Feather name="info" size={14} color={colors.primary} style={styles.adjustmentIcon} />
          <Text style={[styles.adjustmentText, { color: colors.text }]}>{adjustmentReason}</Text>
        </View>
      )}
      
      <View style={styles.goalsGrid}>
        <View style={styles.goalItem}>
          <Text style={[styles.goalValue, { color: colors.text }]}>{goals.calorieGoal}</Text>
          <Text style={[styles.goalLabel, { color: colors.textSecondary }]}>Calories</Text>
        </View>
        
        <View style={styles.goalItem}>
          <Text style={[styles.goalValue, { color: colors.text }]}>{goals.proteinGoal}g</Text>
          <Text style={[styles.goalLabel, { color: colors.textSecondary }]}>Protein</Text>
        </View>
        
        <View style={styles.goalItem}>
          <Text style={[styles.goalValue, { color: colors.text }]}>{goals.carbsGoal}g</Text>
          <Text style={[styles.goalLabel, { color: colors.textSecondary }]}>Carbs</Text>
        </View>
        
        <View style={styles.goalItem}>
          <Text style={[styles.goalValue, { color: colors.text }]}>{goals.fatGoal}g</Text>
          <Text style={[styles.goalLabel, { color: colors.textSecondary }]}>Fat</Text>
        </View>
      </View>
      
      <View style={styles.macroRatioContainer}>
        <Text style={[styles.macroRatioTitle, { color: colors.textSecondary }]}>Macro Ratio</Text>
        
        <View style={styles.macroRatioBar}>
          <View 
            style={[
              styles.macroRatioSegment, 
              { 
                backgroundColor: getColorForMacro('protein'),
                width: `${macroPercentages.protein}%`
              }
            ]} 
          />
          <View 
            style={[
              styles.macroRatioSegment, 
              { 
                backgroundColor: getColorForMacro('carbs'),
                width: `${macroPercentages.carbs}%`
              }
            ]} 
          />
          <View 
            style={[
              styles.macroRatioSegment, 
              { 
                backgroundColor: getColorForMacro('fat'),
                width: `${macroPercentages.fat}%`
              }
            ]} 
          />
        </View>
        
        <View style={styles.macroLegend}>
          <View style={styles.macroLegendItem}>
            <View style={[styles.macroLegendDot, { backgroundColor: getColorForMacro('protein') }]} />
            <Text style={[styles.macroLegendText, { color: colors.textSecondary }]}>
              Protein {macroPercentages.protein}%
            </Text>
          </View>
          
          <View style={styles.macroLegendItem}>
            <View style={[styles.macroLegendDot, { backgroundColor: getColorForMacro('carbs') }]} />
            <Text style={[styles.macroLegendText, { color: colors.textSecondary }]}>
              Carbs {macroPercentages.carbs}%
            </Text>
          </View>
          
          <View style={styles.macroLegendItem}>
            <View style={[styles.macroLegendDot, { backgroundColor: getColorForMacro('fat') }]} />
            <Text style={[styles.macroLegendText, { color: colors.textSecondary }]}>
              Fat {macroPercentages.fat}%
            </Text>
          </View>
        </View>
      </View>
      
      <View style={styles.waterGoalContainer}>
        <Text style={[styles.waterGoalLabel, { color: colors.textSecondary }]}>Daily Water Goal</Text>
        <Text style={[styles.waterGoalValue, { color: colors.text }]}>
          {goals.waterGoal >= 1000 ? `${goals.waterGoal / 1000}L` : `${goals.waterGoal}ml`}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    borderWidth: 1,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 16,
  },
  loadingContainer: {
    paddingVertical: 30,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleIcon: {
    marginRight: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  toggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    marginRight: 8,
  },
  toggleIcon: {
    marginRight: 4,
  },
  toggleText: {
    fontSize: 13,
    fontWeight: '500',
  },
  editButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  adjustmentBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderRadius: 8,
    marginBottom: 16,
  },
  adjustmentIcon: {
    marginRight: 8,
  },
  adjustmentText: {
    fontSize: 13,
    flex: 1,
  },
  goalsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  goalItem: {
    alignItems: 'center',
  },
  goalValue: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  goalLabel: {
    fontSize: 13,
  },
  macroRatioContainer: {
    marginBottom: 16,
  },
  macroRatioTitle: {
    fontSize: 14,
    marginBottom: 8,
  },
  macroRatioBar: {
    height: 16,
    borderRadius: 8,
    flexDirection: 'row',
    overflow: 'hidden',
    marginBottom: 12,
  },
  macroRatioSegment: {
    height: '100%',
  },
  macroLegend: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  macroLegendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  macroLegendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  macroLegendText: {
    fontSize: 12,
  },
  waterGoalContainer: {
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 16,
  },
  waterGoalLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  waterGoalValue: {
    fontSize: 18,
    fontWeight: '600',
  },
}); 