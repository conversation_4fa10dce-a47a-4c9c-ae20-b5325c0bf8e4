import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, TouchableOpacity, Platform, Animated } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { getAdjustedNutritionGoals } from '@/services/nutritionGoalService';
import { Feather } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

interface NutritionProgressProps {
  dailyIntake?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    water: number;
  };
  onAddFood?: () => void;
  onAddWater?: () => void;
}

export default function NutritionProgressCard({ 
  dailyIntake,
  onAddFood,
  onAddWater 
}: NutritionProgressProps) {
  const { colors, isDark } = useTheme();
  
  const [loading, setLoading] = useState(true);
  const [goals, setGoals] = useState({
    calorieGoal: 0,
    proteinGoal: 0,
    carbsGoal: 0,
    fatGoal: 0,
    waterGoal: 0,
  });
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const progressAnim = useRef({
    calories: new Animated.Value(0),
    protein: new Animated.Value(0),
    carbs: new Animated.Value(0),
    fat: new Animated.Value(0),
    water: new Animated.Value(0),
  }).current;
  
  // Default daily intake if not provided (for testing)
  const defaultIntake = {
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    water: 0,
  };
  
  // Use provided intake or default
  const intake = dailyIntake || defaultIntake;
  
  useEffect(() => {
    console.debug('NutritionProgressCard: Component mounted');
    loadGoals();
  }, []);
  
  const loadGoals = async () => {
    console.debug('NutritionProgressCard: Starting to load nutrition goals');
    try {
      setLoading(true);
      
      // Set a timeout to ensure loading state is updated even if there's an error
      const timeoutId = setTimeout(() => {
        console.debug('NutritionProgressCard: Timeout reached, using default goals');
        setGoals({
          calorieGoal: 2000,
          proteinGoal: 150,
          carbsGoal: 200,
          fatGoal: 70,
          waterGoal: 2500,
        });
        setLoading(false);
        startAnimations();
      }, 3000);
      
      // Get adjusted goals based on activity
      console.debug('NutritionProgressCard: Fetching adjusted goals');
      const adjustedGoals = await getAdjustedNutritionGoals();
      console.debug('NutritionProgressCard: Adjusted goals received', !!adjustedGoals);
      
      // Clear timeout since we got a response
      clearTimeout(timeoutId);
      
      if (adjustedGoals) {
        console.debug('NutritionProgressCard: Setting adjusted goals');
        setGoals(adjustedGoals);
      } else {
        // Fallback to default goals if no goals are returned
        console.debug('NutritionProgressCard: No goals returned, using default goals');
        setGoals({
          calorieGoal: 2000,
          proteinGoal: 150,
          carbsGoal: 200,
          fatGoal: 70,
          waterGoal: 2500,
        });
      }
      
      setLoading(false);
      startAnimations();
    } catch (error) {
      console.error('Error loading nutrition goals:', error);
      // Set default goals in case of error
      setGoals({
        calorieGoal: 2000,
        proteinGoal: 150,
        carbsGoal: 200,
        fatGoal: 70,
        waterGoal: 2500,
      });
      setLoading(false);
      startAnimations();
    }
  };
  
  const startAnimations = () => {
    // Calculate progress percentages
    const calorieProgress = calculateProgress(intake.calories, goals.calorieGoal);
    const proteinProgress = calculateProgress(intake.protein, goals.proteinGoal);
    const carbsProgress = calculateProgress(intake.carbs, goals.carbsGoal);
    const fatProgress = calculateProgress(intake.fat, goals.fatGoal);
    const waterProgress = calculateProgress(intake.water, goals.waterGoal);
    
    // Fade in and scale up the card
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: Platform.OS !== 'web',
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: Platform.OS !== 'web',
      }),
      // Animate progress bars
      Animated.timing(progressAnim.calories, {
        toValue: calorieProgress,
        duration: 1000,
        useNativeDriver: false, // Progress width can't use native driver
      }),
      Animated.timing(progressAnim.protein, {
        toValue: proteinProgress,
        duration: 1200,
        useNativeDriver: false,
      }),
      Animated.timing(progressAnim.carbs, {
        toValue: carbsProgress,
        duration: 1200,
        useNativeDriver: false,
      }),
      Animated.timing(progressAnim.fat, {
        toValue: fatProgress,
        duration: 1200,
        useNativeDriver: false,
      }),
      Animated.timing(progressAnim.water, {
        toValue: waterProgress,
        duration: 1200,
        useNativeDriver: false,
      }),
    ]).start();
  };
  
  // Calculate progress percentages (capped at 100%)
  const calculateProgress = (current: number, target: number) => {
    if (target === 0) return 0;
    return Math.min(Math.round((current / target) * 100), 100);
  };
  
  const progressData = {
    calories: calculateProgress(intake.calories, goals.calorieGoal),
    protein: calculateProgress(intake.protein, goals.proteinGoal),
    carbs: calculateProgress(intake.carbs, goals.carbsGoal),
    fat: calculateProgress(intake.fat, goals.fatGoal),
    water: calculateProgress(intake.water, goals.waterGoal),
  };
  
  // Get color based on progress percentage
  const getProgressColor = (percentage: number) => {
    if (percentage < 30) return colors.warning;
    if (percentage < 70) return colors.info;
    if (percentage < 95) return colors.success;
    return colors.primary;
  };

  // Get gradient colors for the calorie circle
  const getCalorieGradient = (percentage: number) => {
    if (percentage < 30) return [colors.warning, colors.warning + '80'] as const;
    if (percentage < 70) return [colors.info, colors.info + '80'] as const;
    if (percentage < 95) return [colors.success, colors.success + '80'] as const;
    return [colors.primary, colors.primary + '80'] as const;
  };
  
  // Get animated interpolated percentages for width
  const animatedWidths = {
    calories: progressAnim.calories.interpolate({
      inputRange: [0, 100],
      outputRange: ['0%', '100%'],
    }),
    protein: progressAnim.protein.interpolate({
      inputRange: [0, 100],
      outputRange: ['0%', '100%'],
    }),
    carbs: progressAnim.carbs.interpolate({
      inputRange: [0, 100],
      outputRange: ['0%', '100%'],
    }),
    fat: progressAnim.fat.interpolate({
      inputRange: [0, 100],
      outputRange: ['0%', '100%'],
    }),
    water: progressAnim.water.interpolate({
      inputRange: [0, 100],
      outputRange: ['0%', '100%'],
    }),
  };
  
  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading nutrition data...</Text>
        </View>
      </View>
    );
  }
  
  return (
    <Animated.View 
      style={[
        styles.container, 
        { 
          backgroundColor: isDark ? colors.card : 'white', 
          borderColor: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.06)',
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
          ...(Platform.OS === 'web' 
            ? { boxShadow: `0px 8px 20px ${isDark ? 'rgba(0, 0, 0, 0.4)' : 'rgba(0, 0, 0, 0.1)'}` } 
            : Platform.OS === 'ios' 
              ? {
                  shadowColor: isDark ? '#000' : 'rgba(0,0,0,0.1)',
                  shadowOffset: { width: 0, height: 8 },
                  shadowOpacity: isDark ? 0.4 : 0.1,
                  shadowRadius: 20,
                }
              : { elevation: 8 })
        }
      ]}
    >
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <View style={[styles.iconBg, { backgroundColor: colors.primary + '20' }]}>
            <Feather name="pie-chart" size={16} color={colors.primary} />
          </View>
          <Text style={[styles.title, { color: colors.text }]}>Today's Nutrition</Text>
        </View>
        
        <TouchableOpacity 
          style={[styles.addButton, { backgroundColor: colors.primary }]}
          onPress={onAddFood}
          activeOpacity={0.8}
          accessibilityLabel="Add food to your log"
          accessibilityHint="Record what you've eaten today"
        >
          <Feather name="plus" size={14} style={{ marginRight: 4 }} />
          <Text style={styles.addButtonText}>Add Food</Text>
        </TouchableOpacity>
      </View>

      <View style={[styles.calorieSection, { 
        backgroundColor: isDark ? colors.subtle : 'rgba(245, 247, 250, 0.85)',
        borderColor: isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.03)',
      }]}>
        <View style={styles.calorieInfo}>
          <Text style={[styles.calorieTitle, { color: colors.textSecondary }]}>
            Calories
          </Text>
          <View style={styles.calorieValues}>
            <Text style={[styles.calorieValue, { color: colors.text }]}>
              {intake.calories}
            </Text>
            <Text style={[styles.calorieGoal, { color: colors.textSecondary }]}>
              /{goals.calorieGoal}
            </Text>
          </View>
          <Text style={[styles.calorieRemaining, { color: colors.textSecondary }]}>
            {Math.max(0, goals.calorieGoal - intake.calories)} remaining
          </Text>
        </View>
        
        <View style={styles.progressCircleContainer}>
          <View style={[
            styles.progressCircle, 
            { borderColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)' }
          ]}>
            <LinearGradient
              colors={getCalorieGradient(progressData.calories)}
              style={[
                styles.gradientCircle, 
                {
                  width: 70,
                  height: 70,
                  borderRadius: 35,
                  opacity: progressData.calories > 0 ? 1 : 0,
                  transform: [
                    { 
                      scale: progressAnim.calories.interpolate({
                        inputRange: [0, 100],
                        outputRange: [0, 1],
                      }) 
                    }
                  ]
                }
              ]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            />
            <View style={styles.percentageContainer}>
              <Text style={[styles.percentageText, { 
                color: getProgressColor(progressData.calories),
                fontSize: 18,
                fontWeight: '700'
              }]}>
                {progressData.calories}%
              </Text>
            </View>
          </View>
        </View>
      </View>
      
      <View style={styles.macroSection}>
        <Text style={[styles.macroSectionTitle, { color: colors.text }]}>
          Macronutrients
        </Text>
        
        <View style={styles.macroGrid}>
          <MacroProgressItem 
            label="Protein"
            current={intake.protein}
            goal={goals.proteinGoal}
            percentage={progressData.protein}
            color="#3B82F6"
            isDark={isDark}
            colors={colors}
            animatedWidth={animatedWidths.protein}
          />
          
          <MacroProgressItem 
            label="Carbs"
            current={intake.carbs}
            goal={goals.carbsGoal}
            percentage={progressData.carbs}
            color="#10B981"
            isDark={isDark}
            colors={colors}
            animatedWidth={animatedWidths.carbs}
          />
          
          <MacroProgressItem 
            label="Fat"
            current={intake.fat}
            goal={goals.fatGoal}
            percentage={progressData.fat}
            color="#F59E0B"
            isDark={isDark}
            colors={colors}
            animatedWidth={animatedWidths.fat}
          />
        </View>
      </View>
      
      <View style={[styles.waterSection, { borderTopColor: isDark ? 'rgba(255, 255, 255, 0.06)' : 'rgba(0, 0, 0, 0.05)' }]}>
        <View style={styles.waterHeader}>
          <View style={styles.waterTitleContainer}>
            <View style={[styles.iconBg, { backgroundColor: '#60A5FA30' }]}>
              <Droplets size={16} color="#60A5FA" />
            </View>
            <Text style={[styles.waterTitle, { color: colors.text }]}>Water Intake</Text>
          </View>
          
          <TouchableOpacity 
            style={[styles.addWaterButton, { backgroundColor: '#60A5FA30' }]}
            onPress={onAddWater}
            activeOpacity={0.8}
            accessibilityLabel="Add water intake"
            accessibilityHint="Record how much water you've consumed"
          >
            <Feather name="plus" size={14} style={{ marginRight: 2 }} />
            <Text style={[styles.addWaterText, { color: '#60A5FA' }]}>Add</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.waterProgressContainer}>
          <View style={[
            styles.waterProgressBar, 
            { backgroundColor: isDark ? colors.subtle : 'rgba(240, 240, 240, 0.8)' }
          ]}>
            <Animated.View style={{ width: animatedWidths.water, overflow: 'hidden' }}>
              <LinearGradient
                colors={['#60A5FA', '#3B82F6']}
                style={styles.waterProgressFill}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              />
            </Animated.View>
            
            <View style={[styles.waterLevels, { opacity: progressData.water < 30 ? 0 : 1 }]}>
              {Array.from({ length: 5 }).map((_, i) => (
                <View 
                  key={i} 
                  style={[
                    styles.waterIndicator,
                    { backgroundColor: 'rgba(255, 255, 255, 0.3)' }
                  ]} 
                />
              ))}
            </View>
          </View>
          
          <View style={styles.waterInfo}>
            <Text style={[styles.waterValues, { color: colors.text, fontWeight: '600' }]}>
              {intake.water >= 1000 ? `${(intake.water / 1000).toFixed(1)}L` : `${intake.water}ml`}
            </Text>
            <Text style={[styles.waterGoal, { color: colors.textSecondary }]}>
              of {goals.waterGoal >= 1000 ? `${(goals.waterGoal / 1000).toFixed(1)}L` : `${goals.waterGoal}ml`}
            </Text>
          </View>
        </View>
      </View>
      
      {/* Details button */}
      <TouchableOpacity 
        style={[styles.detailsButton, { borderTopColor: isDark ? 'rgba(255, 255, 255, 0.06)' : 'rgba(0, 0, 0, 0.05)' }]}
        activeOpacity={0.7}
      >
        <Text style={[styles.detailsButtonText, { color: colors.primary }]}>
          View Details
        </Text>
        <Feather name="chevron-right" size={16} color={colors.primary} />
      </TouchableOpacity>
    </Animated.View>
  );
}

// Helper component for macro progress items
interface MacroProgressItemProps {
  label: string;
  current: number;
  goal: number;
  percentage: number;
  color: string;
  isDark: boolean;
  colors: any;
  animatedWidth: Animated.AnimatedInterpolation<string>;
}

function MacroProgressItem({ label, current, goal, percentage, color, isDark, colors, animatedWidth }: MacroProgressItemProps) {
  return (
    <View style={styles.macroItem}>
      <View style={styles.macroLabelContainer}>
        <View style={[styles.macroColorDot, { backgroundColor: color }]} />
        <Text style={[styles.macroName, { color: colors.text }]}>{label}</Text>
        <Text style={[styles.macroPercentage, { color }]}>{percentage}%</Text>
      </View>
      
      <View style={[
        styles.progressBar, 
        { backgroundColor: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.05)' }
      ]}>
        <Animated.View style={{ width: animatedWidth, overflow: 'hidden' }}>
          <LinearGradient
            colors={[color, color + '90']}
            style={styles.progressFill}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          />
        </Animated.View>
      </View>
      
      <View style={styles.macroValueContainer}>
        <Text style={[styles.macroValue, { color: colors.text }]}>
          {current}g
        </Text>
        <Text style={[styles.macroGoal, { color: colors.textSecondary }]}>
          of {goal}g
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 24,
    borderWidth: 1,
    padding: 20,
    marginHorizontal: 16,
    marginBottom: 16,
    overflow: 'hidden',
  },
  loadingContainer: {
    paddingVertical: 40,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconBg: {
    width: 32,
    height: 32,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
  },
  addButton: {
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  addButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  calorieSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    marginBottom: 24,
    borderWidth: 1,
  },
  calorieInfo: {
    flex: 1,
  },
  calorieTitle: {
    fontSize: 15,
    fontWeight: '500',
    marginBottom: 6,
  },
  calorieValues: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  calorieValue: {
    fontSize: 28,
    fontWeight: '800',
  },
  calorieGoal: {
    fontSize: 18,
    marginLeft: 2,
  },
  calorieRemaining: {
    fontSize: 14,
    marginTop: 4,
  },
  progressCircleContainer: {
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressCircle: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    backgroundColor: 'transparent',
  },
  gradientCircle: {
    position: 'absolute',
    opacity: 0.85,
  },
  percentageContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  percentageText: {
    fontWeight: '700',
  },
  macroSection: {
    marginBottom: 20,
  },
  macroSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  macroGrid: {
    gap: 16,
  },
  macroItem: {
    marginBottom: 4,
  },
  macroLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  macroColorDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  macroName: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
  },
  macroPercentage: {
    fontSize: 13,
    fontWeight: '700',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  macroValueContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  macroValue: {
    fontSize: 15,
    fontWeight: '700',
    marginRight: 4,
  },
  macroGoal: {
    fontSize: 13,
  },
  waterSection: {
    paddingTop: 20,
    borderTopWidth: 1,
    marginTop: 4,
  },
  waterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 14,
  },
  waterTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  waterTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  addWaterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 14,
    flexDirection: 'row',
    alignItems: 'center',
  },
  addWaterText: {
    fontSize: 13,
    fontWeight: '600',
  },
  waterProgressContainer: {
    marginBottom: 4,
  },
  waterProgressBar: {
    height: 14,
    borderRadius: 7,
    marginBottom: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  waterProgressFill: {
    height: '100%',
    width: '100%',
    borderRadius: 7,
  },
  waterLevels: {
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    width: '100%',
    height: '100%',
    alignItems: 'center',
  },
  waterIndicator: {
    width: 2,
    height: '30%',
    borderRadius: 1,
  },
  waterInfo: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  waterValues: {
    fontSize: 15,
    marginRight: 4,
  },
  waterGoal: {
    fontSize: 13,
  },
  detailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
  },
  detailsButtonText: {
    fontSize: 15,
    fontWeight: '600',
    marginRight: 4,
  },
}); 