import React, { useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ActivityIndicator, ScrollView } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , MaterialIcons } from '@expo/vector-icons';

interface NutritionalExplanationProps {
  explanation: {
    summary: string;
    healthBenefits: string[];
    considerations: string[];
    dietaryImplications: string[];
    balanceAssessment: string;
  };
}

export default function NutritionalExplanation({ explanation }: NutritionalExplanationProps) {
  const { colors, isDark } = useTheme();
  const [expandedSection, setExpandedSection] = useState<string | null>('summary');

  const toggleSection = (section: string) => {
    if (expandedSection === section) {
      setExpandedSection(null);
    } else {
      setExpandedSection(section);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: isDark ? colors.card : '#f8f9fa' }]}>
      <View style={[styles.section, { borderColor: colors.border }]}>
        <TouchableOpacity 
          style={styles.sectionHeader} 
          onPress={() => toggleSection('summary')}
          activeOpacity={0.7}
        >
          <View style={styles.sectionTitle}>
            <Feather name="info" size={20} color={colors.primary} style={styles.sectionIcon} />
            <Text style={[styles.sectionTitleText, { color: colors.text }]}>
              Nutrition Summary
            </Text>
          </View>
          {expandedSection === 'summary' ? 
            <Feather name="chevron-up" size={20} color={colors.text} /> : 
            <Feather name="chevron-down" size={20} color={colors.text} />
          }
        </TouchableOpacity>
        
        {expandedSection === 'summary' && (
          <View style={styles.sectionContent}>
            <Text style={[styles.summaryText, { color: colors.text }]}>
              {explanation.summary}
            </Text>
          </View>
        )}
      </View>

      <View style={[styles.section, { borderColor: colors.border }]}>
        <TouchableOpacity 
          style={styles.sectionHeader} 
          onPress={() => toggleSection('benefits')}
          activeOpacity={0.7}
        >
          <View style={styles.sectionTitle}>
            <MaterialIcons name="check-circle" size={20} style={styles.sectionIcon} />
            <Text style={[styles.sectionTitleText, { color: colors.text }]}>
              Health Benefits
            </Text>
          </View>
          {expandedSection === 'benefits' ? 
            <Feather name="chevron-up" size={20} color={colors.text} /> : 
            <Feather name="chevron-down" size={20} color={colors.text} />
          }
        </TouchableOpacity>
        
        {expandedSection === 'benefits' && (
          <View style={styles.sectionContent}>
            {explanation.healthBenefits.map((benefit, index) => (
              <View key={index} style={styles.listItem}>
                <View style={[styles.bulletPoint, { backgroundColor: "#22c55e" }]} />
                <Text style={[styles.listItemText, { color: colors.text }]}>
                  {benefit}
                </Text>
              </View>
            ))}
          </View>
        )}
      </View>

      <View style={[styles.section, { borderColor: colors.border }]}>
        <TouchableOpacity 
          style={styles.sectionHeader} 
          onPress={() => toggleSection('considerations')}
          activeOpacity={0.7}
        >
          <View style={styles.sectionTitle}>
            <Feather name="alert-circle" size={20} style={styles.sectionIcon} />
            <Text style={[styles.sectionTitleText, { color: colors.text }]}>
              Considerations
            </Text>
          </View>
          {expandedSection === 'considerations' ? 
            <Feather name="chevron-up" size={20} color={colors.text} /> : 
            <Feather name="chevron-down" size={20} color={colors.text} />
          }
        </TouchableOpacity>
        
        {expandedSection === 'considerations' && (
          <View style={styles.sectionContent}>
            {explanation.considerations.map((consideration, index) => (
              <View key={index} style={styles.listItem}>
                <View style={[styles.bulletPoint, { backgroundColor: "#f59e0b" }]} />
                <Text style={[styles.listItemText, { color: colors.text }]}>
                  {consideration}
                </Text>
              </View>
            ))}
          </View>
        )}
      </View>

      <View style={[styles.section, { borderColor: colors.border }]}>
        <TouchableOpacity 
          style={styles.sectionHeader} 
          onPress={() => toggleSection('implications')}
          activeOpacity={0.7}
        >
          <View style={styles.sectionTitle}>
            <Feather name="activity" size={20} style={styles.sectionIcon} />
            <Text style={[styles.sectionTitleText, { color: colors.text }]}>
              Dietary Implications
            </Text>
          </View>
          {expandedSection === 'implications' ? 
            <Feather name="chevron-up" size={20} color={colors.text} /> : 
            <Feather name="chevron-down" size={20} color={colors.text} />
          }
        </TouchableOpacity>
        
        {expandedSection === 'implications' && (
          <View style={styles.sectionContent}>
            {explanation.dietaryImplications.map((implication, index) => (
              <View key={index} style={styles.listItem}>
                <View style={[styles.bulletPoint, { backgroundColor: "#3b82f6" }]} />
                <Text style={[styles.listItemText, { color: colors.text }]}>
                  {implication}
                </Text>
              </View>
            ))}
          </View>
        )}
      </View>

      <View style={[styles.section, { borderColor: colors.border, borderBottomWidth: 0 }]}>
        <TouchableOpacity 
          style={styles.sectionHeader} 
          onPress={() => toggleSection('balance')}
          activeOpacity={0.7}
        >
          <View style={styles.sectionTitle}>
            <Feather name="info" size={20} color={colors.primary} style={styles.sectionIcon} />
            <Text style={[styles.sectionTitleText, { color: colors.text }]}>
              Macronutrient Balance
            </Text>
          </View>
          {expandedSection === 'balance' ? 
            <Feather name="chevron-up" size={20} color={colors.text} /> : 
            <Feather name="chevron-down" size={20} color={colors.text} />
          }
        </TouchableOpacity>
        
        {expandedSection === 'balance' && (
          <View style={styles.sectionContent}>
            <Text style={[styles.summaryText, { color: colors.text }]}>
              {explanation.balanceAssessment}
            </Text>
          </View>
        )}
      </View>
    </View>
  );
}

// Component for when the data is loading
export function NutritionalExplanationSkeleton() {
  const { colors, isDark } = useTheme();
  
  return (
    <View style={[styles.container, { backgroundColor: isDark ? colors.card : '#f8f9fa' }]}>
      <View style={[styles.loadingContainer, { backgroundColor: isDark ? colors.card : '#f8f9fa' }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.text }]}>
          Analyzing nutritional impact...
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    marginVertical: 10,
  },
  section: {
    borderBottomWidth: 1,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionIcon: {
    marginRight: 12,
  },
  sectionTitleText: {
    fontSize: 16,
    fontWeight: '600',
  },
  sectionContent: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingBottom: 16,
  },
  summaryText: {
    fontSize: 15,
    lineHeight: 22,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  bulletPoint: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginTop: 6,
    marginRight: 10,
  },
  listItemText: {
    fontSize: 15,
    lineHeight: 20,
    flex: 1,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
}); 