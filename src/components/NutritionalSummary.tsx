import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';

interface NutritionalSummaryProps {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  dailyGoal: number;
  fiber?: number;
  sugar?: number;
  sodium?: number;
}

export function NutritionalSummary({ 
  calories, 
  protein, 
  carbs, 
  fat, 
  dailyGoal, 
  fiber,
  sugar,
  sodium
}: NutritionalSummaryProps) {
  const { colors, isDark } = useTheme();

  // Calculate percentages of daily goals
  const caloriePercentage = Math.round((calories / dailyGoal) * 100);
  
  // Standard daily values for macronutrients (based on 2000 calorie diet)
  const dvProtein = Math.round((protein / 50) * 100); // 50g is typical DV
  const dvCarbs = Math.round((carbs / 275) * 100);    // 275g is typical DV
  const dvFat = Math.round((fat / 78) * 100);         // 78g is typical DV
  const dvFiber = fiber ? Math.round((fiber / 28) * 100) : undefined; // 28g fiber
  
  // Calculate macro distribution (protein, carbs, fat)
  const totalMacroCalories = (protein * 4) + (carbs * 4) + (fat * 9);
  const proteinPercentage = Math.round((protein * 4 / totalMacroCalories) * 100) || 0;
  const carbsPercentage = Math.round((carbs * 4 / totalMacroCalories) * 100) || 0;
  const fatPercentage = Math.round((fat * 9 / totalMacroCalories) * 100) || 0;
  
  // Calculate calories from each macro
  const proteinCalories = protein * 4;
  const carbsCalories = carbs * 4;
  const fatCalories = fat * 9;
  
  // Helper function to get color based on value
  const getPercentageColor = (percentage: number, inverse = false) => {
    if (inverse) {
      // For values where lower is better
      if (percentage <= 50) return colors.success;
      if (percentage <= 100) return colors.warning;
      return colors.error;
    } else {
      // For values where higher might be concerning
      if (percentage > 120) return colors.error;
      if (percentage > 100) return colors.warning;
      return colors.success;
    }
  };
  
  // Determine progress bar widths as percentages
  const getProgressWidth = (percentage: number): number => {
    return Math.min(percentage, 100);
  };
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Calories */}
      <View style={styles.summaryRow}>
        <View style={styles.labelContainer}>
          <Text style={[styles.macroLabel, { color: colors.text }]}>Calories</Text>
          <Text style={[styles.macroValue, { color: colors.text }]}>{calories}</Text>
        </View>
        <View style={styles.progressContainer}>
          <View style={[styles.progressBar, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
            <View 
              style={[
                styles.progressFill, 
                { 
                  backgroundColor: getPercentageColor(caloriePercentage), 
                  width: `${getProgressWidth(caloriePercentage)}%` 
                }
              ]} 
            />
          </View>
          <Text style={[styles.percentageText, { color: getPercentageColor(caloriePercentage) }]}>
            {caloriePercentage}% of daily goal
          </Text>
        </View>
      </View>
      
      {/* Protein */}
      <View style={styles.summaryRow}>
        <View style={styles.labelContainer}>
          <Text style={[styles.macroLabel, { color: colors.text }]}>Protein</Text>
          <View style={styles.macroDetails}>
            <Text style={[styles.macroValue, { color: colors.text }]}>{protein}g</Text>
            <Text style={[styles.calorieDetail, { color: colors.textSecondary }]}>
              {proteinCalories} cal ({proteinPercentage}%)
            </Text>
          </View>
        </View>
        <View style={styles.progressContainer}>
          <View style={[styles.progressBar, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
            <View 
              style={[
                styles.progressFill, 
                { 
                  backgroundColor: '#3b82f6', // blue for protein
                  width: `${getProgressWidth(dvProtein)}%` 
                }
              ]} 
            />
          </View>
          <Text style={[styles.percentageText, { color: '#3b82f6' }]}>
            {dvProtein}% of daily value
          </Text>
        </View>
      </View>
      
      {/* Carbs */}
      <View style={styles.summaryRow}>
        <View style={styles.labelContainer}>
          <Text style={[styles.macroLabel, { color: colors.text }]}>Carbs</Text>
          <View style={styles.macroDetails}>
            <Text style={[styles.macroValue, { color: colors.text }]}>{carbs}g</Text>
            <Text style={[styles.calorieDetail, { color: colors.textSecondary }]}>
              {carbsCalories} cal ({carbsPercentage}%)
            </Text>
          </View>
        </View>
        <View style={styles.progressContainer}>
          <View style={[styles.progressBar, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
            <View 
              style={[
                styles.progressFill, 
                { 
                  backgroundColor: '#8b5cf6', // purple for carbs
                  width: `${getProgressWidth(dvCarbs)}%` 
                }
              ]} 
            />
          </View>
          <Text style={[styles.percentageText, { color: '#8b5cf6' }]}>
            {dvCarbs}% of daily value
          </Text>
        </View>
      </View>
      
      {/* Fat */}
      <View style={styles.summaryRow}>
        <View style={styles.labelContainer}>
          <Text style={[styles.macroLabel, { color: colors.text }]}>Fat</Text>
          <View style={styles.macroDetails}>
            <Text style={[styles.macroValue, { color: colors.text }]}>{fat}g</Text>
            <Text style={[styles.calorieDetail, { color: colors.textSecondary }]}>
              {fatCalories} cal ({fatPercentage}%)
            </Text>
          </View>
        </View>
        <View style={styles.progressContainer}>
          <View style={[styles.progressBar, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
            <View 
              style={[
                styles.progressFill, 
                { 
                  backgroundColor: '#f97316', // orange for fat
                  width: `${getProgressWidth(dvFat)}%` 
                }
              ]} 
            />
          </View>
          <Text style={[styles.percentageText, { color: '#f97316' }]}>
            {dvFat}% of daily value
          </Text>
        </View>
      </View>
      
      {/* Fiber (optional) */}
      {fiber !== undefined && (
        <View style={styles.summaryRow}>
          <View style={styles.labelContainer}>
            <Text style={[styles.macroLabel, { color: colors.text }]}>Fiber</Text>
            <Text style={[styles.macroValue, { color: colors.text }]}>{fiber}g</Text>
          </View>
          <View style={styles.progressContainer}>
            <View style={[styles.progressBar, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
              <View 
                style={[
                  styles.progressFill, 
                  { 
                    backgroundColor: '#10b981', // green for fiber
                    width: `${getProgressWidth(dvFiber || 0)}%` 
                  }
                ]} 
              />
            </View>
            <Text style={[styles.percentageText, { color: '#10b981' }]}>
              {dvFiber}% of daily value
            </Text>
          </View>
        </View>
      )}
      
      {/* Additional details can be added for sugar, sodium, etc. */}
      
      {/* Macro distribution summary */}
      <View style={[styles.distributionContainer, { borderTopColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)' }]}>
        <Text style={[styles.distributionTitle, { color: colors.textSecondary }]}>
          Macronutrient Distribution
        </Text>
        <View style={styles.distributionBar}>
          <View 
            style={[
              styles.distributionSegment,
              { 
                backgroundColor: '#3b82f6', // blue for protein
                width: `${proteinPercentage}%` 
              }
            ]}
          />
          <View 
            style={[
              styles.distributionSegment,
              { 
                backgroundColor: '#8b5cf6', // purple for carbs
                width: `${carbsPercentage}%` 
              }
            ]}
          />
          <View 
            style={[
              styles.distributionSegment,
              { 
                backgroundColor: '#f97316', // orange for fat
                width: `${fatPercentage}%` 
              }
            ]}
          />
        </View>
        <View style={styles.distributionLabels}>
          <Text style={[styles.distributionLabel, { color: '#3b82f6' }]}>
            Protein {proteinPercentage}%
          </Text>
          <Text style={[styles.distributionLabel, { color: '#8b5cf6' }]}>
            Carbs {carbsPercentage}%
          </Text>
          <Text style={[styles.distributionLabel, { color: '#f97316' }]}>
            Fat {fatPercentage}%
          </Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 10,
  },
  summaryRow: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'center',
  },
  labelContainer: {
    width: '35%',
  },
  macroLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  macroValue: {
    fontSize: 16,
    fontWeight: '700',
  },
  macroDetails: {
    flexDirection: 'column',
  },
  calorieDetail: {
    fontSize: 12,
    marginTop: 2,
  },
  progressContainer: {
    flex: 1,
    marginLeft: 10,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  percentageText: {
    fontSize: 12,
    fontWeight: '500',
  },
  distributionContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
  },
  distributionTitle: {
    fontSize: 14,
    marginBottom: 8,
  },
  distributionBar: {
    height: 16,
    borderRadius: 8,
    flexDirection: 'row',
    overflow: 'hidden',
    marginBottom: 8,
  },
  distributionSegment: {
    height: '100%',
  },
  distributionLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  distributionLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
});