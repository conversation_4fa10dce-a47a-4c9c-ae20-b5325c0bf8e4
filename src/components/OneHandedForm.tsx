import React from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, Keyboard, ScrollView } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useOneHandedMode } from './OneHandedModeProvider';
import { OneHandedWrapper } from './OneHandedWrapper';

interface FormField {
  id: string;
  label: string;
  placeholder?: string;
  type?: 'text' | 'number' | 'email' | 'password';
  required?: boolean;
  value: string;
}

interface OneHandedFormProps {
  /**
   * Title of the form
   */
  title: string;
  
  /**
   * Array of form field definitions
   */
  fields: FormField[];
  
  /**
   * Submit button text
   */
  submitText?: string;
  
  /**
   * Cancel button text
   */
  cancelText?: string;
  
  /**
   * Function to handle form submission
   */
  onSubmit: (values: Record<string, string>) => void;
  
  /**
   * Function to handle form cancellation
   */
  onCancel?: () => void;
  
  /**
   * Initial values for the form fields
   */
  initialValues?: Record<string, string>;
}

/**
 * A form component optimized for one-handed operation by positioning
 * input fields and controls within thumb reach when one-handed mode is active.
 */
export function OneHandedForm({
  title,
  fields,
  submitText = 'Submit',
  cancelText = 'Cancel',
  onSubmit,
  onCancel,
  initialValues = {},
}: OneHandedFormProps) {
  const { colors } = useTheme();
  const { isOneHandedModeEnabled } = useOneHandedMode();
  
  // Create state for form field values
  const [values, setValues] = React.useState<Record<string, string>>(
    // Initialize with provided initial values or empty strings
    fields.reduce((acc, field) => {
      acc[field.id] = initialValues[field.id] || '';
      return acc;
    }, {} as Record<string, string>)
  );
  
  // Handle field changes
  const handleChange = (id: string, value: string) => {
    setValues(prev => ({
      ...prev,
      [id]: value,
    }));
  };
  
  // Handle form submission
  const handleSubmit = () => {
    // Dismiss keyboard
    Keyboard.dismiss();
    onSubmit(values);
  };
  
  // Handle form cancellation
  const handleCancel = () => {
    // Dismiss keyboard
    Keyboard.dismiss();
    onCancel?.();
  };
  
  // Get keyboard type based on field type
  const getKeyboardType = (type?: string) => {
    switch (type) {
      case 'number':
        return 'numeric';
      case 'email':
        return 'email-address';
      default:
        return 'default';
    }
  };
  
  return (
    <ScrollView 
      style={styles.scrollContainer}
      contentContainerStyle={styles.scrollContent}
      keyboardShouldPersistTaps="handled"
    >
      <View style={styles.container}>
        <Text style={[styles.title, { color: colors.text }]}>
          {title}
        </Text>
        
        {/* Form fields - wrapped to support one-handed mode */}
        <OneHandedWrapper
          style={styles.fieldsContainer}
          oneHandedStyle={styles.oneHandedFields}
        >
          {fields.map((field) => (
            <View key={field.id} style={styles.fieldContainer}>
              <Text style={[styles.label, { color: colors.text }]}>
                {field.label}
                {field.required && <Text style={{ color: colors.error }}> *</Text>}
              </Text>
              <TextInput
                style={[
                  styles.input,
                  { 
                    backgroundColor: colors.background,
                    color: colors.text,
                    borderColor: colors.border,
                  },
                ]}
                value={values[field.id]}
                onChangeText={(value) => handleChange(field.id, value)}
                placeholder={field.placeholder}
                placeholderTextColor={colors.textSecondary}
                secureTextEntry={field.type === 'password'}
                keyboardType={getKeyboardType(field.type) as any}
              />
            </View>
          ))}
        </OneHandedWrapper>
        
        {/* Action buttons - positioned at bottom for one-handed access */}
        <OneHandedWrapper
          style={styles.buttonsContainer}
          oneHandedStyle={styles.oneHandedButtons}
        >
          {onCancel && (
            <TouchableOpacity
              style={[
                styles.button,
                styles.cancelButton,
                { borderColor: colors.border },
              ]}
              onPress={handleCancel}
              accessibilityLabel={cancelText}
              accessibilityRole="button"
            >
              <Text style={[styles.buttonText, { color: colors.text }]}>
                {cancelText}
              </Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[
              styles.button,
              styles.submitButton,
              { backgroundColor: colors.primary },
            ]}
            onPress={handleSubmit}
            accessibilityLabel={submitText}
            accessibilityRole="button"
          >
            <Text style={[styles.buttonText, styles.submitButtonText]}>
              {submitText}
            </Text>
          </TouchableOpacity>
        </OneHandedWrapper>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
  },
  fieldsContainer: {
    flex: 1,
  },
  oneHandedFields: {
    // When in one-handed mode, adjust spacing
    justifyContent: 'flex-end',
    paddingBottom: 20,
  },
  fieldContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    minHeight: 48, // Ensure minimum touch target size
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  oneHandedButtons: {
    // When in one-handed mode, position buttons differently
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    padding: 16,
    backgroundColor: 'transparent',
  },
  button: {
    flex: 1,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  cancelButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
  },
  submitButton: {
    // Primary button styles applied through props
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  submitButtonText: {
    color: '#FFFFFF',
  },
});

export default OneHandedForm; 