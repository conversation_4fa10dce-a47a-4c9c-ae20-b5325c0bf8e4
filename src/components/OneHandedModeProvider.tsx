import React, { createContext, useContext, useState, useEffect } from 'react';
import { View, Dimensions, StyleSheet, Animated, TouchableOpacity } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAccessibility } from './AccessibilityProvider';

// Storage key for one-handed mode preference
const ONE_HANDED_MODE_KEY = 'one_handed_mode_enabled';

// Context type definition
interface OneHandedModeContextType {
  isOneHandedModeEnabled: boolean;
  enableOneHandedMode: () => void;
  disableOneHandedMode: () => void;
  toggleOneHandedMode: () => void;
}

// Create context with default values
const OneHandedModeContext = createContext<OneHandedModeContextType>({
  isOneHandedModeEnabled: false,
  enableOneHandedMode: () => {},
  disableOneHandedMode: () => {},
  toggleOneHandedMode: () => {},
});

// Hook to use the context
export function useOneHandedMode() {
  return useContext(OneHandedModeContext);
}

interface OneHandedModeProviderProps {
  children: React.ReactNode;
}

/**
 * Provider component that makes one-handed mode functionality available
 * to the entire app, with persistence between app launches.
 */
export function OneHandedModeProvider({ children }: OneHandedModeProviderProps) {
  const [isOneHandedModeEnabled, setIsOneHandedModeEnabled] = useState(false);
  const [contentOffset, setContentOffset] = useState(new Animated.Value(0));
  const { colors } = useTheme();
  const { isReduceMotionEnabled } = useAccessibility();

  // Load saved preference on mount
  useEffect(() => {
    loadSavedPreference();
  }, []);

  // Save preference when it changes
  useEffect(() => {
    savePreference();
  }, [isOneHandedModeEnabled]);

  // Animate content position when one-handed mode changes
  useEffect(() => {
    if (isOneHandedModeEnabled) {
      // Move content down to be more accessible with one hand
      Animated.timing(contentOffset, {
        toValue: 1,
        duration: isReduceMotionEnabled ? 0 : 300,
        useNativeDriver: true,
      }).start();
    } else {
      // Move content back to normal position
      Animated.timing(contentOffset, {
        toValue: 0,
        duration: isReduceMotionEnabled ? 0 : 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isOneHandedModeEnabled, isReduceMotionEnabled]);

  // Load saved preference
  const loadSavedPreference = async () => {
    try {
      const savedPreference = await AsyncStorage.getItem(ONE_HANDED_MODE_KEY);
      if (savedPreference !== null) {
        setIsOneHandedModeEnabled(savedPreference === 'true');
      }
    } catch (error) {
      console.error('Error loading one-handed mode preference:', error);
    }
  };

  // Save current preference
  const savePreference = async () => {
    try {
      await AsyncStorage.setItem(ONE_HANDED_MODE_KEY, String(isOneHandedModeEnabled));
    } catch (error) {
      console.error('Error saving one-handed mode preference:', error);
    }
  };

  // Enable one-handed mode
  const enableOneHandedMode = () => {
    setIsOneHandedModeEnabled(true);
  };

  // Disable one-handed mode
  const disableOneHandedMode = () => {
    setIsOneHandedModeEnabled(false);
  };

  // Toggle one-handed mode
  const toggleOneHandedMode = () => {
    setIsOneHandedModeEnabled(prev => !prev);
  };

  // Calculate content transformation based on screen height
  const screenHeight = Dimensions.get('window').height;
  const transformValue = contentOffset.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -screenHeight * 0.2], // Move content 20% up screen
  });

  return (
    <OneHandedModeContext.Provider
      value={{
        isOneHandedModeEnabled,
        enableOneHandedMode,
        disableOneHandedMode,
        toggleOneHandedMode,
      }}
    >
      <View style={styles.container}>
        <Animated.View
          style={[
            styles.contentContainer,
            {
              transform: [{ translateY: transformValue }],
            },
          ]}
        >
          {children}
        </Animated.View>

        {isOneHandedModeEnabled && (
          <TouchableOpacity
            style={[
              styles.floatingButton,
              { backgroundColor: colors.primary },
            ]}
            onPress={toggleOneHandedMode}
            accessibilityLabel="Disable one-handed mode"
            accessibilityRole="button"
          >
            <Feather name="chevron-down" size={24}  color={colors.text} />
          </TouchableOpacity>
        )}

        {!isOneHandedModeEnabled && (
          <TouchableOpacity
            style={[
              styles.oneHandedModeToggle,
              { backgroundColor: colors.primaryLight },
            ]}
            onPress={toggleOneHandedMode}
            accessibilityLabel="Enable one-handed mode"
            accessibilityRole="button"
          >
            <Feather name="chevron-up" size={20} color={colors.primary} />
          </TouchableOpacity>
        )}
      </View>
    </OneHandedModeContext.Provider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
  },
  floatingButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  oneHandedModeToggle: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
});

export default OneHandedModeProvider; 