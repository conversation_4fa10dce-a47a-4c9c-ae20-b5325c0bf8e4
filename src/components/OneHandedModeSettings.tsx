import React from 'react';
import { View, Text, StyleSheet, Switch, TouchableOpacity, ScrollView } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useOneHandedMode } from './OneHandedModeProvider';
import * as Haptics from 'expo-haptics';
import { Feather } from '@expo/vector-icons';
import ContextualHelp from './ContextualHelp';

interface OneHandedModeSettingsProps {
  /**
   * Optional header title
   */
  title?: string;
}

/**
 * A settings component that allows users to enable, disable, and
 * configure one-handed mode for the app.
 */
export function OneHandedModeSettings({
  title = 'One-Handed Mode',
}: OneHandedModeSettingsProps) {
  const { colors, isDark } = useTheme();
  const { 
    isOneHandedModeEnabled, 
    enableOneHandedMode, 
    disableOneHandedMode, 
    toggleOneHandedMode 
  } = useOneHandedMode();
  
  // Toggle one-handed mode with haptic feedback
  const handleToggle = (value: boolean) => {
    if (value) {
      enableOneHandedMode();
    } else {
      disableOneHandedMode();
    }
    
    // Provide haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };
  
  return (
    <ScrollView style={styles.scrollContainer}>
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.headerContainer}>
          <Text style={[styles.title, { color: colors.text }]}>
            {title}
          </Text>
          
          {/* Help tooltip */}
          <ContextualHelp 
            content={{
              title: 'One-Handed Mode',
              description: 'One-Handed Mode optimizes the app interface for use with a single hand by moving important controls to the bottom half of the screen and adjusting layouts for thumb accessibility.',
              examples: [
                'Moves content and controls to the bottom of the screen',
                'Adds a floating button to toggle the mode',
                'Repositions form elements for easier access',
              ],
            }}
            position="top"
            iconColor={colors.primary}
          />
        </View>
        
        {/* Main toggle */}
        <View style={[styles.settingRow, { borderBottomColor: colors.border }]}>
          <View style={styles.settingTextContainer}>
            <Text style={[styles.settingLabel, { color: colors.text }]}>
              Enable One-Handed Mode
            </Text>
            <Text style={[styles.settingDescription, { color: colors.textSecondary }]}>
              Optimize the interface for single hand use
            </Text>
          </View>
          <Switch
            value={isOneHandedModeEnabled}
            onValueChange={handleToggle}
            trackColor={{ false: '#767577', true: colors.primaryLight }}
            thumbColor={isOneHandedModeEnabled ? colors.primary : '#f4f3f4'}
            ios_backgroundColor="#767577"
            accessibilityLabel="Toggle one-handed mode"
            accessibilityRole="switch"
          />
        </View>
        
        {/* Demonstration button - visible only when one-handed mode is enabled */}
        {isOneHandedModeEnabled && (
          <View style={styles.demoContainer}>
            <Text style={[styles.demoText, { color: colors.textSecondary }]}>
              One-Handed Mode is active. The interface has been optimized for thumb accessibility.
            </Text>
            
            <TouchableOpacity
              style={[
                styles.demoButton,
                { backgroundColor: colors.primaryLight }
              ]}
              onPress={toggleOneHandedMode}
              accessibilityLabel="Toggle one-handed mode demo"
              accessibilityRole="button"
            >
              <Text style={[styles.demoButtonText, { color: colors.primary }]}>
                Toggle One-Handed Mode
              </Text>
            </TouchableOpacity>
          </View>
        )}
        
        {/* Instructions */}
        <View style={styles.instructionsContainer}>
          <Text style={[styles.instructionsTitle, { color: colors.text }]}>
            How to use One-Handed Mode
          </Text>
          
          <View style={styles.instructionItem}>
            <Text style={[styles.instructionNumber, { color: colors.primary }]}>
              1
            </Text>
            <Text style={[styles.instructionText, { color: colors.text }]}>
              Enable One-Handed Mode with the toggle above
            </Text>
          </View>
          
          <View style={styles.instructionItem}>
            <Text style={[styles.instructionNumber, { color: colors.primary }]}>
              2
            </Text>
            <Text style={[styles.instructionText, { color: colors.text }]}>
              Interface elements will shift to be within comfortable thumb reach
            </Text>
          </View>
          
          <View style={styles.instructionItem}>
            <Text style={[styles.instructionNumber, { color: colors.primary }]}>
              3
            </Text>
            <Text style={[styles.instructionText, { color: colors.text }]}>
              Use the floating button to quickly toggle the mode on/off
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 16,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  settingTextContainer: {
    flex: 1,
    marginRight: 16,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
  },
  demoContainer: {
    marginTop: 24,
    padding: 16,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  demoText: {
    fontSize: 14,
    marginBottom: 16,
    lineHeight: 20,
  },
  demoButton: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  demoButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  instructionsContainer: {
    marginTop: 32,
  },
  instructionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  instructionNumber: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    textAlign: 'center',
    lineHeight: 28,
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 12,
  },
  instructionText: {
    fontSize: 14,
    flex: 1,
  },
});

export default OneHandedModeSettings; 