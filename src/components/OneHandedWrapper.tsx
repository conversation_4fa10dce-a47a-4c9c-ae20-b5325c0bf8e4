import React from 'react';
import { View, StyleSheet, ViewStyle, StyleProp } from 'react-native';
import { useOneHandedMode } from './OneHandedModeProvider';

interface OneHandedWrapperProps {
  /**
   * Child components to render
   */
  children: React.ReactNode;
  
  /**
   * Custom style for the container
   */
  style?: StyleProp<ViewStyle>;
  
  /**
   * Style to apply when one-handed mode is active
   */
  oneHandedStyle?: StyleProp<ViewStyle>;
  
  /**
   * Whether to disable one-handed mode for this component
   */
  disableOneHandedMode?: boolean;
}

/**
 * A wrapper component that adjusts UI element positions to be more
 * accessible for one-handed use when one-handed mode is active.
 */
export function OneHandedWrapper({
  children,
  style,
  oneHandedStyle,
  disableOneHandedMode = false,
}: OneHandedWrapperProps) {
  const { isOneHandedModeEnabled } = useOneHandedMode();
  
  // Apply one-handed mode styles only if enabled and not disabled for this component
  const shouldApplyOneHandedStyles = isOneHandedModeEnabled && !disableOneHandedMode;
  
  return (
    <View 
      style={[
        styles.container,
        style,
        shouldApplyOneHandedStyles && styles.oneHandedDefault,
        shouldApplyOneHandedStyles && oneHandedStyle,
      ]}
    >
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    // Base styles
  },
  oneHandedDefault: {
    // Default transformations for one-handed mode
    marginTop: 'auto', // Push content to bottom of container
    paddingBottom: 16, // Add padding to avoid being too close to screen edge
  },
});

export default OneHandedWrapper; 