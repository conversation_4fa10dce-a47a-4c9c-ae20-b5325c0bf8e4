import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  LayoutAnimation,
  Platform,
  UIManager,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import * as Progress from 'react-native-progress';
import { useOptimisticUpdates } from '../utils/optimisticUpdates';
import { useToast } from './Toast';

// Enable LayoutAnimation on Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

interface OptimisticWaterIntakeTrackerProps {
  /**
   * Current water intake in milliliters
   */
  currentIntake: number;
  
  /**
   * Goal intake in milliliters
   */
  goalIntake: number;
  
  /**
   * Callback for updating water intake
   */
  onAddWater: (amount: number) => Promise<void>;
  
  /**
   * Default amount to add/subtract in milliliters
   */
  incrementAmount?: number;
}

/**
 * Water intake tracker component that uses optimistic updates
 * to provide immediate feedback when adding or removing water intake.
 */
export function OptimisticWaterIntakeTracker({
  currentIntake: initialIntake,
  goalIntake,
  onAddWater,
  incrementAmount = 250,
}: OptimisticWaterIntakeTrackerProps) {
  const { colors, isDark } = useTheme();
  const toast = useToast();
  
  // Set up optimistic updates
  const {
    data: intakeData,
    isLoading,
    performOptimisticUpdate,
  } = useOptimisticUpdates({
    initialData: { currentIntake: initialIntake },
    onError: (error) => {
      // Show error toast when update fails
      toast.showToast({
        message: 'Failed to update water intake. Please try again.',
        type: 'error',
      });
    },
  });
  
  // Current intake from optimistic state
  const { currentIntake } = intakeData;
  
  // Calculate progress percentage
  const progressPercentage = Math.min(1, currentIntake / goalIntake);
  
  // Format intake values
  const formatIntake = (ml: number) => {
    if (ml >= 1000) {
      return `${(ml / 1000).toFixed(1)}L`;
    }
    return `${ml}ml`;
  };
  
  // Calculate glasses based on 250ml per glass
  const glassSize = incrementAmount;
  const glasses = Math.floor(currentIntake / glassSize);
  const totalGlasses = Math.ceil(goalIntake / glassSize);
  
  // Handle adding water with optimistic update
  const handleAddWater = async () => {
    try {
      // Use layout animation for smooth transition
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
      
      // Perform optimistic update
      await performOptimisticUpdate(
        // Optimistic update function that runs immediately
        (data) => ({
          ...data,
          currentIntake: data.currentIntake + incrementAmount,
        }),
        // Server update function that runs in the background
        async () => {
          // Call the actual API
          await onAddWater(currentIntake + incrementAmount);
        }
      );
      
      // Show success toast
      toast.showToast({
        message: 'Water intake updated',
        type: 'success',
      });
    } catch (error) {
      // Error handling is done in the onError callback
      console.error('Error updating water intake:', error);
    }
  };
  
  // Handle removing water with optimistic update
  const handleRemoveWater = async () => {
    // Don't allow negative values
    if (currentIntake < incrementAmount) return;
    
    try {
      // Use layout animation for smooth transition
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
      
      // Perform optimistic update
      await performOptimisticUpdate(
        // Optimistic update function that runs immediately
        (data) => ({
          ...data,
          currentIntake: data.currentIntake - incrementAmount,
        }),
        // Server update function that runs in the background
        async () => {
          // Call the actual API
          await onAddWater(currentIntake - incrementAmount);
        }
      );
      
      // Show success toast
      toast.showToast({
        message: 'Water intake updated',
        type: 'success',
      });
    } catch (error) {
      // Error handling is done in the onError callback
      console.error('Error updating water intake:', error);
    }
  };
  
  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDark ? colors.card : '#FFFFFF' },
      ]}
    >
      <View style={styles.headerRow}>
        <View style={styles.iconAndTitle}>
          <View style={[styles.iconContainer, { backgroundColor: colors.primaryLight }]}>
            <Droplets size={20} color={colors.primary} />
          </View>
          <Text style={[styles.title, { color: colors.text }]}>Water Intake</Text>
        </View>
        
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          {formatIntake(currentIntake)} of {formatIntake(goalIntake)}
        </Text>
      </View>
      
      <View style={styles.progressContainer}>
        <Progress.Bar
          progress={progressPercentage}
          width={null}
          height={12}
          color={colors.primary}
          unfilledColor={isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'}
          borderWidth={0}
          borderRadius={6}
          style={styles.progressBar}
        />
      </View>
      
      <View style={styles.glassesContainer}>
        <Text style={[styles.glassesText, { color: colors.text }]}>
          {glasses} of {totalGlasses} glasses
        </Text>
      </View>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[
            styles.button,
            { backgroundColor: colors.primaryLight },
            currentIntake < incrementAmount && styles.disabledButton,
          ]}
          onPress={handleRemoveWater}
          disabled={currentIntake < incrementAmount || isLoading}
        >
          <Feather name="minus" size={20} color={colors.primary} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.button,
            styles.addButton,
            { backgroundColor: colors.primary },
            isLoading && styles.disabledButton,
          ]}
          onPress={handleAddWater}
          disabled={isLoading}
        >
          <Feather name="plus" size={20}  color={colors.text} />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconAndTitle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  subtitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressContainer: {
    marginBottom: 12,
  },
  progressBar: {
    width: '100%',
  },
  glassesContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  glassesText: {
    fontSize: 14,
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  button: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  addButton: {
    width: 54,
    height: 54,
    borderRadius: 27,
  },
  disabledButton: {
    opacity: 0.5,
  },
});

export default OptimisticWaterIntakeTracker; 