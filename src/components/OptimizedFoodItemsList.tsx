import React, { useCallback, useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Pressable,
  Image,
  ActivityIndicator,
  Dimensions,
  Platform,
 useWindowDimensions } from 'react-native';
import { FoodItem } from '../types/api-responses';
import { InlineLoading, SkeletonLoading } from './LoadingIndicator';
import { ErrorDisplay } from './ErrorDisplay';
import * as Haptics from 'expo-haptics';
// Import FlashList conditionally to avoid errors on platforms where it's not available
const FlashList = Platform.OS !== 'web' 
  ? require('@shopify/flash-list').FlashList 
  : null;

const { width } = Dimensions.get('window');

interface OptimizedFoodItemsListProps {
  items: FoodItem[];
  onItemPress?: (item: FoodItem) => void;
  isLoading?: boolean;
  error?: string;
  onRetry?: () => void;
  emptyMessage?: string;
  showThumbnails?: boolean;
  searchQuery?: string;
}

/**
 * Optimized food items list component that implements virtualization for better performance
 * Uses FlashList on native platforms for improved performance
 */
export function OptimizedFoodItemsList({
  items,
  onItemPress,
  isLoading = false,
  error,
  onRetry,
  emptyMessage = 'No food items available',
  showThumbnails = true,
  searchQuery = '',
}: OptimizedFoodItemsListProps) {
  const { width, height } = useWindowDimensions();
  const [visibleItems, setVisibleItems] = useState<FoodItem[]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  
  // Filter items based on search query
  useEffect(() => {
    if (!searchQuery) {
      setVisibleItems(items);
      return;
    }
    
    const lowercaseQuery = searchQuery.toLowerCase();
    const filtered = items.filter(item => 
      item.name.toLowerCase().includes(lowercaseQuery)
    );
    setVisibleItems(filtered);
  }, [items, searchQuery]);
  
  // End initial loading state after first render
  useEffect(() => {
    if (items.length > 0 && isInitialLoad) {
      setIsInitialLoad(false);
    }
  }, [items, isInitialLoad]);
  
  // Render skeleton loading during initial load
  if (isLoading && isInitialLoad) {
    return (
      <View style={styles.container}>
        {Array.from({ length: 8 }).map((_, index) => (
          <View key={`skeleton-${index}`} style={styles.skeletonContainer}>
            {showThumbnails && (
              <SkeletonLoading
                style={styles.skeletonImage}
                isLoading={true}
              />
            )}
            <View style={styles.skeletonTextContainer}>
              <SkeletonLoading
                style={styles.skeletonTitle}
                isLoading={true}
              />
              <SkeletonLoading
                style={styles.skeletonSubtitle}
                isLoading={true}
              />
            </View>
          </View>
        ))}
      </View>
    );
  }
  
  // Show error message if there's an error
  if (error) {
    return (
      <ErrorDisplay
        message={error}
        type="api"
        onRetry={onRetry}
      />
    );
  }
  
  // Show empty message if there are no items
  if (visibleItems.length === 0 && !isLoading) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyMessage}>{emptyMessage}</Text>
      </View>
    );
  }

  // Handle item press with haptic feedback
  const handleItemPress = useCallback((item: FoodItem) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    if (onItemPress) {
      onItemPress(item);
    }
  }, [onItemPress]);
  
  // Render individual food item
  const renderItem = useCallback(({ item }: { item: FoodItem }) => (
    <Pressable
      style={({ pressed }) => [
        styles.itemContainer,
        pressed && styles.itemPressed,
      ]}
      onPress={() => handleItemPress(item)}
    >
      {showThumbnails && (
        <Image
          source={{ uri: `https://nutrition-images.example.com/${encodeURIComponent(item.name)}.jpg` }}
          style={styles.thumbnail}
          defaultSource={require('../assets/images/food-placeholder.png')}
          progressiveRenderingEnabled={true}
          fadeDuration={300}
        />
      )}
      
      <View style={styles.itemDetails}>
        <Text style={styles.itemName}>{item.name}</Text>
        <Text style={styles.itemCalories}>{item.calories} calories</Text>
        <Text style={styles.itemNutrition}>
          P: {item.nutrition.protein}g | C: {item.nutrition.carbs}g | F: {item.nutrition.fat}g
        </Text>
      </View>
    </Pressable>
  ), [handleItemPress, showThumbnails]);
  
  // Use best list component based on platform
  const ListComponent = Platform.OS !== 'web' && FlashList ? FlashList : FlatList;
  
  // Function to extract keys from items
  const keyExtractor = useCallback((item: FoodItem) => 
    item.id ? item.id.toString() : item.name
  , []);
  
  // Type-safe function for getItemType
  const getItemType = useCallback((item: FoodItem) => 'food', []);
  
  return (
    <View style={styles.container}>
      {isLoading && !isInitialLoad && (
        <View style={styles.loadingOverlay}>
          <InlineLoading message="Loading more items..." />
        </View>
      )}
      
      <ListComponent
        data={visibleItems}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        contentContainerStyle={styles.listContent}
        estimatedItemSize={80} // For FlashList
        getItemType={getItemType} // For FlashList optimization
        removeClippedSubviews={true} // Optimize memory usage
        windowSize={7} // Optimize rendering window
        maxToRenderPerBatch={10}
        initialNumToRender={8}
        updateCellsBatchingPeriod={50}
        ListFooterComponent={isLoading && !isInitialLoad ? <ActivityIndicator style={styles.footer} /> : null}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  listContent: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  itemContainer: {
    flexDirection: 'row',
    padding: 12,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E0E0E0',
    backgroundColor: '#fff',
    borderRadius: 8,
    marginVertical: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
  },
  itemPressed: {
    backgroundColor: '#F5F5F5',
  },
  thumbnail: {
    width: 60,
    height: 60,
    borderRadius: 6,
    backgroundColor: '#F0F0F0',
  },
  itemDetails: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'center',
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  itemCalories: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  itemNutrition: {
    fontSize: 12,
    color: '#888',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  footer: {
    paddingVertical: 16,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    padding: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    zIndex: 1,
    alignItems: 'center',
  },
  skeletonContainer: {
    flexDirection: 'row',
    padding: 12,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E0E0E0',
    marginVertical: 4,
  },
  skeletonImage: {
    width: 60,
    height: 60,
    borderRadius: 6,
  },
  skeletonTextContainer: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'center',
  },
  skeletonTitle: {
    height: 18,
    marginBottom: 8,
    width: '80%',
  },
  skeletonSubtitle: {
    height: 14,
    width: '60%',
  },
}); 