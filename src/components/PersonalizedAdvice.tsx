import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/contexts/TranslationContext';
import { getCustomizedNutritionAdvice } from '@/services/dietitianService';
import { Card } from 'react-native-paper';
import { Feather } from '@expo/vector-icons';

/**
 * PersonalizedAdvice displays AI-generated nutrition advice based on user's profile and meal history
 */
export default function PersonalizedAdvice() {
  const { colors, isDark } = useTheme();
  const { t } = useTranslation();
  const [advice, setAdvice] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const timeoutRef = useRef<number | null>(null);

  useEffect(() => {
    loadAdvice();
    
    // Set a timeout to ensure we don't get stuck loading
    timeoutRef.current = setTimeout(() => {
      if (isLoading) {
        console.debug('PersonalizedAdvice: Timeout reached, using default advice');
        setIsLoading(false);
        setAdvice([
          "Balance your diet with proteins, carbs, and healthy fats",
          "Stay hydrated by drinking water throughout the day",
          "Include a variety of fruits and vegetables in your meals",
          "Try to limit processed foods and added sugars"
        ]);
      }
    }, 5000) as unknown as number;
    
    return () => {
      // Clear timeout on unmount
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Load personalized advice from service
  const loadAdvice = async () => {
    try {
      console.debug('PersonalizedAdvice: Loading advice data');
      setIsLoading(true);
      setError(null);
      
      // Get advice from service with a Promise.race for timeout
      const advicePromise = getCustomizedNutritionAdvice();
      const timeoutPromise = new Promise<string[]>((resolve) => {
        setTimeout(() => {
          console.debug('PersonalizedAdvice: Service call timed out');
          resolve([
            "Balance your diet with proteins, carbs, and healthy fats",
            "Stay hydrated by drinking water throughout the day",
            "Include a variety of fruits and vegetables in your meals",
            "Try to limit processed foods and added sugars"
          ]);
        }, 3000);
      });
      
      // Use whichever resolves first
      const adviceData = await Promise.race([advicePromise, timeoutPromise]);
      console.debug('PersonalizedAdvice: Received advice data');
      
      setAdvice(adviceData);
    } catch (err) {
      console.error('Error loading personalized advice:', err);
      setError('Failed to load your personalized advice');
      setAdvice([
        "Balance your diet with proteins, carbs, and healthy fats",
        "Stay hydrated by drinking water throughout the day",
        "Include a variety of fruits and vegetables in your meals",
        "Try to limit processed foods and added sugars"
      ]);
    } finally {
      // Clear the timeout since we've completed loading
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      setIsLoading(false);
    }
  };

  // Handle refresh request
  const handleRefresh = () => {
    loadAdvice();
  };

  // Show loading indicator
  if (isLoading) {
    return (
      <Card style={[styles.container, { backgroundColor: isDark ? colors.card : '#F9F9F9' }]}>
        <Card.Content>
          <View style={styles.titleContainer}>
            <Lightbulb size={18} color={colors.primary} style={styles.icon} />
            <Text style={[styles.title, { color: colors.text }]}>
              Personalized Advice
            </Text>
          </View>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.text }]}>
              {t('common.loading')}
            </Text>
          </View>
        </Card.Content>
      </Card>
    );
  }

  // Show error state
  if (error) {
    return (
      <Card style={[styles.container, { backgroundColor: isDark ? colors.card : '#F9F9F9' }]}>
        <Card.Content>
          <View style={styles.titleContainer}>
            <Lightbulb size={18} color={colors.primary} style={styles.icon} />
            <Text style={[styles.title, { color: colors.text }]}>
              Personalized Advice
            </Text>
          </View>
          <Text style={[styles.errorText, { color: colors.text }]}>
            {error}
          </Text>
          <TouchableOpacity 
            style={[styles.refreshButton, { backgroundColor: colors.primary }]}
            onPress={handleRefresh}
          >
            <Feather name="refresh-cw" size={16} style={styles.refreshIcon} />
            <Text style={styles.refreshButtonText}>
              {t('common.retry')}
            </Text>
          </TouchableOpacity>
        </Card.Content>
      </Card>
    );
  }

  return (
    <Card style={[styles.container, { backgroundColor: isDark ? colors.card : '#F9F9F9' }]}>
      <Card.Content>
        <View style={styles.titleContainer}>
          <Lightbulb size={18} color={colors.primary} style={styles.icon} />
          <Text style={[styles.title, { color: colors.text }]}>
            Personalized Advice
          </Text>
          <TouchableOpacity 
            style={styles.refreshIconContainer}
            onPress={handleRefresh}
          >
            <Feather name="refresh-cw" size={16} color={colors.primary} />
          </TouchableOpacity>
        </View>
        
        <View style={styles.adviceContainer}>
          {advice.map((tip, index) => (
            <View key={index} style={styles.tipContainer}>
              <View style={[styles.bulletPoint, { backgroundColor: colors.primary }]} />
              <Text style={[styles.tipText, { color: colors.text }]}>
                {tip}
              </Text>
            </View>
          ))}
        </View>
      </Card.Content>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  icon: {
    marginRight: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  refreshIconContainer: {
    padding: 4,
  },
  adviceContainer: {
    marginBottom: 8,
  },
  tipContainer: {
    flexDirection: 'row',
    marginBottom: 12,
    alignItems: 'flex-start',
  },
  bulletPoint: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginTop: 7,
    marginRight: 10,
  },
  tipText: {
    fontSize: 14,
    lineHeight: 20,
    flex: 1,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignSelf: 'center',
  },
  refreshIcon: {
    marginRight: 6,
  },
  refreshButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
}); 