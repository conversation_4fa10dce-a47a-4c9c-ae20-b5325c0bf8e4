import { isWeb } from '../utils/platform';
import React from 'react';
import { View, Text } from 'react-native';

// Direct import for native platforms
const expoCameraModule = !isWeb ? require('expo-camera') : null;

// Simple web camera component fallback
const WebCamera = (props: any) => (
  <View style={{ flex: 1, backgroundColor: 'black', justifyContent: 'center', alignItems: 'center' }}>
    <Text style={{ color: 'white' }}>Camera not available on web</Text>
    {props.children}
  </View>
);

// Export the Camera component
export const CameraView = !isWeb ? expoCameraModule.Camera : WebCamera;

// Export camera permissions hook
export function useCameraPermissions() {
  const [permission, setPermission] = React.useState<boolean | null>(null);
  
  const requestPermission = async () => {
    if (isWeb) {
      return { granted: false };
    }
    
    try {
      const { status } = await expoCameraModule.Camera.requestCameraPermissionsAsync();
      const granted = status === 'granted';
      setPermission(granted);
      return { granted };
    } catch (error) {
      console.error('Error requesting camera permission:', error);
      return { granted: false };
    }
  };
  
  React.useEffect(() => {
    if (isWeb) {
      setPermission(false);
      return;
    }
    
    (async () => {
      try {
        const { status } = await expoCameraModule.Camera.getCameraPermissionsAsync();
        setPermission(status === 'granted');
      } catch (error) {
        console.error('Error checking camera permission:', error);
        setPermission(false);
      }
    })();
  }, []);
  
  return [{ granted: permission }, requestPermission];
} 