import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Modal
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , Ionicons } from '@expo/vector-icons';

interface DietPlan {
  id: string;
  name: string;
  description: string;
  imageUrl?: string;
  benefits: string[];
  restrictions: string[];
  popularFoods: string[];
  sampleMeals: {
    breakfast: string[];
    lunch: string[];
    dinner: string[];
    snacks: string[];
  };
  macroSplit: {
    protein: number;
    carbs: number;
    fat: number;
  };
  difficultyLevel: 1 | 2 | 3 | 4 | 5;
  tags: string[];
}

interface PrebuiltDietPlansComponentProps {
  onSelectPlan?: (plan: DietPlan) => void;
}

export function PrebuiltDietPlansComponent({ onSelectPlan }: PrebuiltDietPlansComponentProps) {
  const { colors, isDark } = useTheme();
  const [selectedPlan, setSelectedPlan] = useState<DietPlan | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  const dietPlans: DietPlan[] = [
    {
      id: 'keto',
      name: 'Ketogenic Diet',
      description: 'A high-fat, low-carb diet that forces your body to burn fats rather than carbohydrates by putting you in a state of ketosis.',
      benefits: [
        'Weight loss',
        'Reduced blood sugar and insulin levels',
        'May improve heart disease risk factors',
        'Potential brain health benefits'
      ],
      restrictions: [
        'Very low carbohydrate intake (typically 20-50g per day)',
        'Requires careful monitoring of macronutrients',
        'May be difficult to sustain long-term',
        'Can cause initial "keto flu" symptoms'
      ],
      popularFoods: [
        'Avocados',
        'Eggs',
        'Fatty fish',
        'Nuts and seeds',
        'Full-fat dairy',
        'Olive oil',
        'Low-carb vegetables'
      ],
      sampleMeals: {
        breakfast: [
          'Bacon and eggs with avocado',
          'Keto smoothie with almond milk, berries, and MCT oil',
          'Cream cheese pancakes'
        ],
        lunch: [
          'Tuna salad with olive oil and greens',
          'Cauliflower crust pizza with high-fat toppings',
          'Lettuce wrap with deli meats and cheese'
        ],
        dinner: [
          'Salmon with asparagus and butter sauce',
          'Ribeye steak with garlic butter and broccoli',
          'Chicken thighs with spinach and cream sauce'
        ],
        snacks: [
          'Cheese crisps',
          'Macadamia nuts',
          'Olives',
          'Pepperoni slices',
          'Hard-boiled eggs'
        ]
      },
      macroSplit: {
        protein: 20,
        carbs: 5,
        fat: 75
      },
      difficultyLevel: 4,
      tags: ['weight loss', 'low carb', 'high fat', 'ketosis']
    },
    {
      id: 'mediterranean',
      name: 'Mediterranean Diet',
      description: 'Based on traditional foods eaten in countries like Greece and Italy, focuses on plant-based foods, healthy fats, and moderate protein.',
      benefits: [
        'Heart health',
        'Reduced risk of certain cancers',
        'Brain health and cognitive function',
        'Weight management',
        'Longevity'
      ],
      restrictions: [
        'Limited red meat',
        'Limited added sugars',
        'Limited processed foods'
      ],
      popularFoods: [
        'Olive oil',
        'Fish',
        'Nuts and seeds',
        'Legumes',
        'Whole grains',
        'Fresh fruits and vegetables',
        'Herbs and spices'
      ],
      sampleMeals: {
        breakfast: [
          'Greek yogurt with honey and walnuts',
          'Whole grain toast with avocado and tomato',
          'Vegetable omelet with feta cheese'
        ],
        lunch: [
          'Mediterranean quinoa bowl with chickpeas',
          'Greek salad with olive oil dressing',
          'Lentil soup with whole grain bread'
        ],
        dinner: [
          'Grilled fish with roasted vegetables',
          'Bean and vegetable stew',
          'Chicken souvlaki with tzatziki and salad'
        ],
        snacks: [
          'Hummus with vegetables',
          'Handful of mixed nuts',
          'Fresh fruit',
          'Olives'
        ]
      },
      macroSplit: {
        protein: 15,
        carbs: 50,
        fat: 35
      },
      difficultyLevel: 2,
      tags: ['heart health', 'anti-inflammatory', 'longevity', 'balanced']
    },
    {
      id: 'muscle-gain',
      name: 'Muscle Gain Diet',
      description: 'High-protein diet designed to support muscle growth and strength training, with a caloric surplus to fuel workouts and recovery.',
      benefits: [
        'Supports muscle growth and repair',
        'Improves workout performance',
        'Faster recovery between workouts',
        'Strength gains'
      ],
      restrictions: [
        'Requires consistent caloric surplus',
        'Meal timing may be important',
        'Needs to be combined with resistance training',
        'May require tracking macros carefully'
      ],
      popularFoods: [
        'Lean meats (chicken, turkey)',
        'Eggs',
        'Fish and seafood',
        'Dairy (Greek yogurt, cottage cheese)',
        'Legumes and beans',
        'Whole grains',
        'Nuts and seeds',
        'Protein supplements'
      ],
      sampleMeals: {
        breakfast: [
          'Protein oatmeal with fruit and nut butter',
          'Egg white omelet with vegetables and toast',
          'Protein smoothie with banana, oats, and whey'
        ],
        lunch: [
          'Chicken breast with brown rice and vegetables',
          'Tuna wrap with whole grain tortilla',
          'Turkey and sweet potato bowl'
        ],
        dinner: [
          'Salmon with quinoa and roasted vegetables',
          'Lean beef stir-fry with rice',
          'Protein pasta with turkey meatballs'
        ],
        snacks: [
          'Protein shake',
          'Greek yogurt with berries',
          'Cottage cheese with pineapple',
          'Protein bar',
          'Tuna on crackers'
        ]
      },
      macroSplit: {
        protein: 30,
        carbs: 40,
        fat: 30
      },
      difficultyLevel: 3,
      tags: ['strength training', 'high protein', 'muscle building', 'performance']
    },
    {
      id: 'vegan',
      name: 'Vegan Diet',
      description: 'Plant-based diet that excludes all animal products including meat, dairy, eggs, and honey. Focuses on fruits, vegetables, legumes, grains, nuts, and seeds.',
      benefits: [
        'Reduced risk of heart disease',
        'Lower blood pressure',
        'Weight management',
        'May reduce risk of certain cancers',
        'Lower carbon footprint'
      ],
      restrictions: [
        'No animal products of any kind',
        'Requires careful planning for certain nutrients (B12, iron, zinc)',
        'May need supplementation',
        'Limited restaurant options in some areas'
      ],
      popularFoods: [
        'Tofu and tempeh',
        'Legumes (beans, lentils, chickpeas)',
        'Nutritional yeast',
        'Plant-based milks',
        'Whole grains',
        'Nuts and seeds',
        'Fruits and vegetables',
        'Plant-based protein powders'
      ],
      sampleMeals: {
        breakfast: [
          'Tofu scramble with vegetables',
          'Overnight oats with plant milk and fruit',
          'Smoothie bowl with plant protein and berries'
        ],
        lunch: [
          'Buddha bowl with quinoa, roasted veggies, and tahini',
          'Lentil soup with whole grain bread',
          'Chickpea salad sandwich'
        ],
        dinner: [
          'Vegetable stir-fry with tofu and brown rice',
          'Bean and vegetable chili',
          'Vegan pasta with lentil bolognese'
        ],
        snacks: [
          'Hummus with vegetables',
          'Energy balls (dates, nuts, cocoa)',
          'Roasted chickpeas',
          'Fruit with nut butter',
          'Edamame'
        ]
      },
      macroSplit: {
        protein: 15,
        carbs: 60,
        fat: 25
      },
      difficultyLevel: 3,
      tags: ['plant-based', 'ethical', 'sustainable', 'dairy-free']
    },
    {
      id: 'paleo',
      name: 'Paleo Diet',
      description: 'Based on foods similar to what might have been eaten during the Paleolithic era, focusing on whole foods like lean meats, fish, fruits, vegetables, nuts, and seeds.',
      benefits: [
        'Weight loss',
        'Improved insulin sensitivity',
        'Better satiety',
        'Reduced inflammation',
        'Better blood lipids'
      ],
      restrictions: [
        'No grains or legumes',
        'No dairy products',
        'No refined sugar or salt',
        'No processed foods',
        'Limited alcohol'
      ],
      popularFoods: [
        'Lean meats (grass-fed preferred)',
        'Wild-caught fish and seafood',
        'Fresh fruits and vegetables',
        'Nuts and seeds',
        'Eggs',
        'Herbs and spices',
        'Unrefined oils (olive, coconut, avocado)'
      ],
      sampleMeals: {
        breakfast: [
          'Sweet potato hash with eggs and avocado',
          'Berry smoothie with coconut milk',
          'Breakfast meat patties with fruit'
        ],
        lunch: [
          'Large salad with grilled chicken and olive oil',
          'Lettuce wraps with lean ground beef',
          'Zucchini noodles with shrimp and pesto'
        ],
        dinner: [
          'Grilled salmon with roasted vegetables',
          'Bison burger (no bun) with steamed vegetables',
          'Roast chicken with sweet potatoes and broccoli'
        ],
        snacks: [
          'Beef jerky (no added sugar)',
          'Mixed nuts',
          'Fresh fruit',
          'Hard-boiled eggs',
          'Vegetable sticks with guacamole'
        ]
      },
      macroSplit: {
        protein: 30,
        carbs: 30,
        fat: 40
      },
      difficultyLevel: 4,
      tags: ['ancestral', 'whole foods', 'grain-free', 'dairy-free']
    }
  ];

  const handlePlanPress = (plan: DietPlan) => {
    setSelectedPlan(plan);
    setModalVisible(true);
  };

  const handleCloseModal = () => {
    setModalVisible(false);
    // Wait for the modal animation to finish before clearing the selected plan
    setTimeout(() => setSelectedPlan(null), 300);
  };

  const handleSelectPlan = () => {
    if (selectedPlan && onSelectPlan) {
      onSelectPlan(selectedPlan);
      setModalVisible(false);
      setTimeout(() => setSelectedPlan(null), 300);
    }
  };

  const renderDifficultyStars = (level: number) => {
    const stars: React.ReactElement[] = [];
    for (let i = 0; i < 5; i++) {
      stars.push(
        <Feather name="star" size={14} color={i < level ? colors.warning : colors.border} style={styles.starIcon} />
      );
    }
    return <View style={styles.starsContainer}>{stars}</View>;
  };

  const renderMacroCircle = (plan: DietPlan) => {
    const { protein, carbs, fat } = plan.macroSplit;
    
    return (
      <View style={styles.macroCircleContainer}>
        <View style={styles.macroLegend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: colors.primary }]} />
            <Text style={[styles.legendText, { color: colors.text }]}>
              Protein {protein}%
            </Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: colors.success }]} />
            <Text style={[styles.legendText, { color: colors.text }]}>
              Carbs {carbs}%
            </Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: colors.warning }]} />
            <Text style={[styles.legendText, { color: colors.text }]}>
              Fat {fat}%
            </Text>
          </View>
        </View>
        <View style={styles.circleGraph}>
          <View style={[styles.proteinSegment, { 
            backgroundColor: colors.primary,
            width: `${protein}%`
          }]} />
          <View style={[styles.carbsSegment, { 
            backgroundColor: colors.success,
            width: `${carbs}%`
          }]} />
          <View style={[styles.fatSegment, { 
            backgroundColor: colors.warning,
            width: `${fat}%`
          }]} />
        </View>
      </View>
    );
  };

  const renderMealList = (mealType: string, meals: string[]) => {
    return (
      <View style={styles.mealSection}>
        <Text style={[styles.mealTypeTitle, { color: colors.text }]}>
          {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
        </Text>
        {meals.map((meal, index) => (
          <View key={index} style={styles.mealItem}>
            <View style={[styles.mealBullet, { backgroundColor: colors.primary }]} />
            <Text style={[styles.mealText, { color: colors.text }]}>
              {meal}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  const renderPlanDetail = () => {
    if (!selectedPlan) return null;

    return (
      <Modal
        visible={modalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={handleCloseModal}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, { backgroundColor: isDark ? colors.card : 'white' }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                {selectedPlan.name}
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={handleCloseModal}
              >
                <Feather name="x" size={20} color={colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent}>
              <Text style={[styles.planDescription, { color: colors.text }]}>
                {selectedPlan.description}
              </Text>

              {/* Macro Split */}
              <View style={styles.sectionContainer}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  Macronutrient Split
                </Text>
                {renderMacroCircle(selectedPlan)}
              </View>

              {/* Benefits */}
              <View style={styles.sectionContainer}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  Benefits
                </Text>
                {selectedPlan.benefits.map((benefit, index) => (
                  <View key={index} style={styles.listItem}>
                    <Feather name="check" size={16} color={colors.success} style={styles.listIcon} />
                    <Text style={[styles.listText, { color: colors.text }]}>
                      {benefit}
                    </Text>
                  </View>
                ))}
              </View>

              {/* Restrictions */}
              <View style={styles.sectionContainer}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  Restrictions & Considerations
                </Text>
                {selectedPlan.restrictions.map((restriction, index) => (
                  <View key={index} style={styles.listItem}>
                    <Feather name="info" size={16} color={colors.warning} style={styles.listIcon} />
                    <Text style={[styles.listText, { color: colors.text }]}>
                      {restriction}
                    </Text>
                  </View>
                ))}
              </View>

              {/* Popular Foods */}
              <View style={styles.sectionContainer}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  Foods to Include
                </Text>
                <View style={styles.foodTagsContainer}>
                  {selectedPlan.popularFoods.map((food, index) => (
                    <View 
                      key={index} 
                      style={[styles.foodTag, { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }]}
                    >
                      <Text style={[styles.foodTagText, { color: colors.text }]}>
                        {food}
                      </Text>
                    </View>
                  ))}
                </View>
              </View>

              {/* Sample Meals */}
              <View style={styles.sectionContainer}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  Sample Meal Plan
                </Text>
                
                {renderMealList('breakfast', selectedPlan.sampleMeals.breakfast)}
                {renderMealList('lunch', selectedPlan.sampleMeals.lunch)}
                {renderMealList('dinner', selectedPlan.sampleMeals.dinner)}
                {renderMealList('snacks', selectedPlan.sampleMeals.snacks)}
              </View>

              {/* Difficulty Level */}
              <View style={styles.sectionContainer}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  Difficulty Level
                </Text>
                <View style={styles.difficultyContainer}>
                  {renderDifficultyStars(selectedPlan.difficultyLevel)}
                  <Text style={[styles.difficultyText, { color: colors.textSecondary }]}>
                    {selectedPlan.difficultyLevel === 1 ? 'Very Easy' :
                     selectedPlan.difficultyLevel === 2 ? 'Easy' :
                     selectedPlan.difficultyLevel === 3 ? 'Moderate' :
                     selectedPlan.difficultyLevel === 4 ? 'Challenging' :
                     'Very Challenging'}
                  </Text>
                </View>
              </View>

              {/* Tags */}
              <View style={styles.sectionContainer}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  Categories
                </Text>
                <View style={styles.tagsContainer}>
                  {selectedPlan.tags.map((tag, index) => (
                    <View 
                      key={index} 
                      style={[styles.tagItem, { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }]}
                    >
                      <Text style={[styles.tagText, { color: colors.text }]}>
                        {tag}
                      </Text>
                    </View>
                  ))}
                </View>
              </View>
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.selectPlanButton, { backgroundColor: colors.primary }]}
                onPress={handleSelectPlan}
              >
                <Text style={styles.selectButtonText}>
                  Select This Plan
                </Text>
                <Feather name="arrow-right" size={18} style={styles.buttonIcon} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {dietPlans.map(plan => (
          <TouchableOpacity
            key={plan.id}
            style={[styles.planCard, { backgroundColor: isDark ? colors.card : 'white' }]}
            onPress={() => handlePlanPress(plan)}
          >
            <View style={styles.planHeader}>
              <View style={styles.planInfo}>
                <Text style={[styles.planName, { color: colors.text }]}>
                  {plan.name}
                </Text>
                <Text 
                  style={[styles.planShortDescription, { color: colors.textSecondary }]}
                  numberOfLines={2}
                >
                  {plan.description}
                </Text>
                <View style={styles.planMeta}>
                  {renderDifficultyStars(plan.difficultyLevel)}
                  <View style={styles.macroSplit}>
                    <Text style={[styles.macroText, { color: colors.text }]}>
                      P:{plan.macroSplit.protein}% C:{plan.macroSplit.carbs}% F:{plan.macroSplit.fat}%
                    </Text>
                  </View>
                </View>
              </View>
              <Feather name="chevron-right" size={20} color={colors.textSecondary} />
            </View>
            
            <View style={styles.planTags}>
              {plan.tags.slice(0, 3).map((tag, index) => (
                <View 
                  key={index} 
                  style={[styles.tagBadge, { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }]}
                >
                  <Text style={[styles.tagBadgeText, { color: colors.textSecondary }]}>
                    {tag}
                  </Text>
                </View>
              ))}
              {plan.tags.length > 3 && (
                <Text style={[styles.moreTags, { color: colors.textSecondary }]}>
                  +{plan.tags.length - 3} more
                </Text>
              )}
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
      
      {renderPlanDetail()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  planCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  planInfo: {
    flex: 1,
  },
  planName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 6,
  },
  planShortDescription: {
    fontSize: 14,
    marginBottom: 8,
  },
  planMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  starsContainer: {
    flexDirection: 'row',
  },
  starIcon: {
    marginRight: 2,
  },
  macroSplit: {
    marginLeft: 'auto',
  },
  macroText: {
    fontSize: 12,
    fontWeight: '500',
  },
  planTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  tagBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
    marginRight: 8,
  },
  tagBadgeText: {
    fontSize: 12,
  },
  moreTags: {
    fontSize: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    height: '90%',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  planDescription: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 20,
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  macroCircleContainer: {
    marginVertical: 8,
  },
  macroLegend: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 12,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 6,
  },
  legendText: {
    fontSize: 14,
  },
  circleGraph: {
    height: 24,
    flexDirection: 'row',
    borderRadius: 12,
    overflow: 'hidden',
  },
  proteinSegment: {
    height: '100%',
  },
  carbsSegment: {
    height: '100%',
  },
  fatSegment: {
    height: '100%',
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  listIcon: {
    marginRight: 8,
    marginTop: 3,
  },
  listText: {
    flex: 1,
    fontSize: 15,
    lineHeight: 22,
  },
  foodTagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  foodTag: {
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  foodTagText: {
    fontSize: 14,
  },
  mealSection: {
    marginBottom: 16,
  },
  mealTypeTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  mealItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
  },
  mealBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginTop: 8,
    marginRight: 8,
  },
  mealText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  difficultyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  difficultyText: {
    marginLeft: 8,
    fontSize: 14,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tagItem: {
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 14,
  },
  modalFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  selectPlanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 54,
    borderRadius: 10,
  },
  selectButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  buttonIcon: {
    marginLeft: 8,
  },
});

export default PrebuiltDietPlansComponent; 