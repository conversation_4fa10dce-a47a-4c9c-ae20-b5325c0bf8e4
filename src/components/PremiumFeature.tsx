import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, Platform } from 'react-native';
import { useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { canAccessFeature } from '@/services/stripeService';
import { LinearGradient } from 'expo-linear-gradient';

// Development flag to unlock all premium features
const UNLOCK_ALL_PREMIUM_FEATURES = true;

interface PremiumFeatureProps {
  /**
   * Unique key identifying this premium feature
   */
  featureKey: string;
  
  /**
   * Title to show in the upgrade modal
   */
  title?: string;
  
  /**
   * Description to show in the upgrade modal
   */
  description?: string;
  
  /**
   * The content to display if the user has access
   */
  children: React.ReactNode;
  
  /**
   * Optional placeholder to show instead of the children when locked
   * If not provided, the children will be shown with an overlay
   */
  placeholder?: React.ReactNode;

  /**
   * Override to force access to this feature (for testing)
   */
  forceAccess?: boolean;
}

export function PremiumFeature({ 
  featureKey, 
  title = 'Premium Feature', 
  description = 'Upgrade to access this premium feature and more!',
  children,
  placeholder,
  forceAccess = false
}: PremiumFeatureProps) {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const checkAccess = async () => {
      setLoading(true);
      try {
        // If dev flag is on or forceAccess prop is true, grant access to this feature
        if (UNLOCK_ALL_PREMIUM_FEATURES || forceAccess) {
          setHasAccess(true);
        } else {
          const result = await canAccessFeature(featureKey);
          setHasAccess(result);
        }
      } catch (error) {
        console.error(`Error checking access to ${featureKey}:`, error);
        // In development mode, still grant access even if check fails
        setHasAccess(UNLOCK_ALL_PREMIUM_FEATURES || forceAccess);
      } finally {
        setLoading(false);
      }
    };
    
    checkAccess();
  }, [featureKey, forceAccess]);
  
  const handleUpgrade = () => {
    setShowModal(false);
    router.push('/subscription-plans');
  };
  
  // While loading or if user has access, just render the children
  if (loading || hasAccess) {
    return <>{children}</>;
  }
  
  // If placeholder is provided and user doesn't have access, show placeholder
  if (placeholder && !hasAccess) {
    return (
      <TouchableOpacity 
        style={styles.placeholderContainer}
        onPress={() => setShowModal(true)}
        activeOpacity={0.7}
      >
        {placeholder}
      </TouchableOpacity>
    );
  }
  
  // Otherwise, show children with a lock overlay
  return (
    <>
      <TouchableOpacity 
        style={styles.container}
        onPress={() => setShowModal(true)}
        activeOpacity={0.7}
      >
        <View style={styles.contentContainer}>
          {children}
        </View>
        <View style={[styles.overlay, { backgroundColor: isDark ? 'rgba(0, 0, 0, 0.5)' : 'rgba(255, 255, 255, 0.7)' }]}>
          <View style={[styles.lockContainer, { backgroundColor: isDark ? 'rgba(30, 41, 59, 0.8)' : 'rgba(255, 255, 255, 0.9)' }]}>
            <Feather name="lock" size={20} color={colors.primary} />
            <Text style={[styles.lockText, { color: colors.primary }]}>Premium</Text>
          </View>
        </View>
      </TouchableOpacity>
      
      <Modal
        visible={showModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={[styles.modalContent, { backgroundColor: colors.card, borderColor: colors.border }]}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowModal(false)}
            >
              <Feather name="x" size={24} color={colors.text} />
            </TouchableOpacity>
            
            <View style={styles.modalHeader}>
              <LinearGradient
                colors={['#3B82F6', '#2563EB']}
                style={styles.iconContainer}
              >
                <Sparkles size={24} color="white" />
              </LinearGradient>
              <Text style={[styles.modalTitle, { color: colors.text }]}>{title}</Text>
            </View>
            
            <Text style={[styles.modalDescription, { color: colors.textSecondary }]}>
              {description}
            </Text>
            
            <TouchableOpacity
              style={[styles.upgradeButton, { backgroundColor: colors.primary }]}
              onPress={handleUpgrade}
            >
              <Text style={styles.upgradeButtonText}>View Plans</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
    borderRadius: 8,
  },
  contentContainer: {
    opacity: 0.7,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
  },
  lockContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  lockText: {
    marginLeft: 6,
    fontWeight: '600',
    fontSize: 14,
  },
  placeholderContainer: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 12,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: '700',
  },
  modalDescription: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 24,
  },
  upgradeButton: {
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  upgradeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
  },
}); 