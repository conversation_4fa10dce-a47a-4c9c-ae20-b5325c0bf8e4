import React from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@/contexts/ThemeContext';

// Define an extended profile interface that includes phone
interface ExtendedProfile {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  phone?: string;
  height?: number;
  weight?: number;
  daily_calorie_goal?: number;
  daily_water_goal?: number;
  activity_level?: string;
  created_at?: string;
  updated_at?: string;
}

interface PersonalInfoSectionProps {
  profile: ExtendedProfile | null;
  user: any;
  loadingProfile: boolean;
  editingField: string | null;
  editValue: string;
  setEditValue: (value: string) => void;
  isSaving: boolean;
  handleStartEdit: (field: string, value: string) => void;
  handleSaveEdit: () => void;
}

export function PersonalInfoSection({
  profile,
  user,
  loadingProfile,
  editingField,
  editValue,
  setEditValue,
  isSaving,
  handleStartEdit,
  handleSaveEdit
}: PersonalInfoSectionProps) {
  const { colors, isDark } = useTheme();
  
  return (
    <View style={styles.sectionContainer}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>Personal Information</Text>
      <View style={[
        styles.infoCard, 
        { 
          backgroundColor: colors.card,
          shadowColor: colors.shadow,
          borderColor: colors.border,
        }
      ]}>
        <TouchableOpacity 
          style={[styles.infoRow, { borderBottomColor: colors.border }]}
          activeOpacity={0.7}
          onPress={() => handleStartEdit('full_name', profile?.full_name || '')}
        >
          <View style={styles.infoRowLeft}>
            <LinearGradient
              colors={['#3B82F6', '#2563EB'] as const}
              style={[styles.infoIconContainer, styles.infoIconShadow]}
            >
              <Feather name="user" size={18}  color={colors.text} />
            </LinearGradient>
            <View>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Full Name</Text>
              {editingField === 'full_name' ? (
                <TextInput
                  style={[styles.infoInput, { color: colors.text, borderBottomColor: colors.primary }]}
                  value={editValue}
                  onChangeText={setEditValue}
                  autoFocus
                  onBlur={handleSaveEdit}
                  onSubmitEditing={handleSaveEdit}
                />
              ) : (
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {loadingProfile ? 'Loading...' : (profile?.full_name || 'Add your name')}
                </Text>
              )}
            </View>
          </View>
          <View style={[styles.infoRowRight, styles.editButtonContainer]}>
            {isSaving && editingField === 'full_name' ? (
              <ActivityIndicator size="small" color={colors.primary} />
            ) : (
              <Feather name="edit" size={16} color={colors.textSecondary} />
            )}
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.infoRow, { borderBottomColor: colors.border }]}
          activeOpacity={0.7}
          onPress={() => handleStartEdit('email', user?.email || '')}
        >
          <View style={styles.infoRowLeft}>
            <LinearGradient
              colors={['#8B5CF6', '#7C3AED'] as const}
              style={[styles.infoIconContainer, styles.infoIconShadow]}
            >
              <Feather name="mail" size={18}  color={colors.text} />
            </LinearGradient>
            <View>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Email</Text>
              {editingField === 'email' ? (
                <TextInput
                  style={[styles.infoInput, { color: colors.text, borderBottomColor: colors.primary }]}
                  value={editValue}
                  onChangeText={setEditValue}
                  autoFocus
                  keyboardType="email-address"
                  onBlur={handleSaveEdit}
                  onSubmitEditing={handleSaveEdit}
                />
              ) : (
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {user?.email || 'No email provided'}
                </Text>
              )}
            </View>
          </View>
          <View style={[styles.infoRowRight, styles.editButtonContainer]}>
            {isSaving && editingField === 'email' ? (
              <ActivityIndicator size="small" color={colors.primary} />
            ) : (
              <Feather name="edit" size={16} color={colors.textSecondary} />
            )}
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.infoRow}
          activeOpacity={0.7}
          onPress={() => handleStartEdit('phone', profile?.phone as string || '+****************')}
        >
          <View style={styles.infoRowLeft}>
            <LinearGradient
              colors={['#EC4899', '#DB2777'] as const}
              style={[styles.infoIconContainer, styles.infoIconShadow]}
            >
              <Feather name="phone" size={18}  color={colors.text} />
            </LinearGradient>
            <View>
              <Text style={[styles.infoLabel, { color: colors.textSecondary }]}>Phone</Text>
              {editingField === 'phone' ? (
                <TextInput
                  style={[styles.infoInput, { color: colors.text, borderBottomColor: colors.primary }]}
                  value={editValue}
                  onChangeText={setEditValue}
                  autoFocus
                  keyboardType="phone-pad"
                  onBlur={handleSaveEdit}
                  onSubmitEditing={handleSaveEdit}
                />
              ) : (
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {(profile?.phone as string) || '+****************'}
                </Text>
              )}
            </View>
          </View>
          <View style={[styles.infoRowRight, styles.editButtonContainer]}>
            {isSaving && editingField === 'phone' ? (
              <ActivityIndicator size="small" color={colors.primary} />
            ) : (
              <Feather name="edit" size={16} color={colors.textSecondary} />
            )}
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  sectionContainer: {
    paddingHorizontal: 20,
    paddingTop: 30,
  },
  sectionTitle: {
    fontSize: 19,
    fontWeight: '700',
    marginBottom: 16,
  },
  infoCard: {
    borderRadius: 16,
    borderWidth: 1,
    overflow: 'hidden',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  infoRowLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
  },
  infoIconShadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  infoLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 15,
    fontWeight: '600',
  },
  infoRowRight: {
    
  },
  editButtonContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(100,116,139,0.1)',
  },
  infoInput: {
    fontSize: 15,
    fontWeight: '600',
    borderBottomWidth: 1,
    paddingVertical: 2,
    minWidth: 150,
  },
}); 