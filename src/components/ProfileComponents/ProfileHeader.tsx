import React, { useRef, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, Animated } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@/contexts/ThemeContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Profile } from '@/services/databaseService';
import { canUseNativeDriver } from '@/utils/platformUtils';
import { ProfileImageUploader } from './ProfileImageUploader';

interface ProfileHeaderProps {
  profile: Profile | null;
  user: any;
  loadingProfile: boolean;
  scrollY: Animated.Value;
  router: any;
  onAvatarChange?: (url: string) => void;
}

export function ProfileHeader({
  profile,
  user,
  loadingProfile,
  scrollY,
  router,
  onAvatarChange = () => {}
}: ProfileHeaderProps) {
  const { colors, isDark } = useTheme();
  const insets = useSafeAreaInsets();
  
  // Animation values
  const baseHeaderHeight = 280;
  const minHeaderHeight = 140;
  const topInset = insets.top || (Platform.OS === 'ios' ? 50 : 20);

  const headerHeight = scrollY.interpolate({
    inputRange: [0, 150],
    outputRange: [baseHeaderHeight + topInset, minHeaderHeight + topInset],
    extrapolate: 'clamp',
  });
  
  const imageOpacity = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [1, 0],
    extrapolate: 'clamp',
  });
  
  const titleTranslate = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [0, -30],
    extrapolate: 'clamp',
  });
  
  const titleScale = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [1, 0.8],
    extrapolate: 'clamp',
  });
  
  const headerRadius = scrollY.interpolate({
    inputRange: [0, 150],
    outputRange: [0, 35],
    extrapolate: 'clamp',
  });

  // Define gradient colors for the profile header
  const headerGradientColors = isDark 
    ? ['#1E293B', '#0F172A'] as const
    : ['#3B82F6', '#1D4ED8'] as const;

  const handleAvatarChange = (url: string) => {
    onAvatarChange(url);
  };
    
  return (
    <Animated.View 
      style={[
        styles.profileHeader,
        { 
          height: headerHeight,
          borderBottomLeftRadius: headerRadius,
          borderBottomRightRadius: headerRadius,
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 5 },
          shadowOpacity: 0.1,
          shadowRadius: 10,
          elevation: 8,
        }
      ]}
    >
      <LinearGradient
        colors={headerGradientColors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={[styles.headerGradient, {
          borderBottomLeftRadius: headerRadius,
          borderBottomRightRadius: headerRadius,
        }]}
      >
        <View style={[styles.headerTopRow, { paddingTop: insets.top || (Platform.OS === 'ios' ? 50 : 20), paddingBottom: 10 }]}>
          <Text style={[styles.screenTitle, { color: 'white' }]}>Profile</Text>
          <TouchableOpacity 
            style={[styles.settingsButton, { 
              backgroundColor: 'rgba(255,255,255,0.2)',
              borderRadius: 20,
            }]}
            onPress={() => router.push('/(tabs)/profile-screens/settings')}
          >
            <Feather name="settings" size={20}  color={colors.text} />
          </TouchableOpacity>
        </View>
        
        <Animated.View 
          style={[
            styles.profileImageContainer,
            {
              opacity: imageOpacity,
              transform: [{
                scale: scrollY.interpolate({
                  inputRange: [0, 150],
                  outputRange: [1, 0.5],
                  extrapolate: 'clamp',
                })
              }]
            }
          ]}
        >
          <ProfileImageUploader 
            avatarUrl={profile?.avatar_url || null}
            onAvatarChange={handleAvatarChange}
            size={140}
            disabled={loadingProfile}
          />
        </Animated.View>
        
        <Animated.View 
          style={[
            styles.profileNameContainer,
            {
              transform: [
                { translateY: titleTranslate },
                { scale: titleScale }
              ]
            }
          ]}
        >
          <Text style={[styles.profileName, { color: 'white' }]}>
            {loadingProfile ? 'Loading Profile...' : (profile?.full_name || user?.email?.split('@')[0] || 'User')}
          </Text>
          <Text style={[styles.profileEmail, { color: 'rgba(255,255,255,0.8)' }]}>
            {user?.email || 'No email'}
          </Text>
        </Animated.View>
      </LinearGradient>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  profileHeader: {
    overflow: 'hidden',
    zIndex: 10,
  },
  headerGradient: {
    flex: 1,
    width: '100%',
  },
  headerTopRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  screenTitle: {
    fontSize: 22,
    fontWeight: '700',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImageContainer: {
    alignSelf: 'center',
    marginTop: 20,
    marginBottom: 10,
    position: 'relative',
  },
  profileNameContainer: {
    alignItems: 'center',
    marginTop: 10,
    paddingHorizontal: 20,
  },
  profileName: {
    fontSize: 22,
    fontWeight: '700',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 16,
    opacity: 0.9,
  },
}); 