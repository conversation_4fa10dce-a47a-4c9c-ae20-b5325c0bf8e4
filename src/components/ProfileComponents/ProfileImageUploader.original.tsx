import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, StyleSheet, Alert, Text, ActivityIndicator, Platform } from 'react-native';
import { Feather } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { FallbackImage } from '@/components/FallbackImage';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { app } from '@/lib/firebase';
import { getStorage, ref, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import { getFirestore, doc, updateDoc } from 'firebase/firestore';
import { getAuth, updateProfile } from 'firebase/auth';

// Initialize Firebase services
const storage = getStorage(app);
const db = getFirestore(app);

interface ProfileImageUploaderProps {
  avatarUrl: string | null;
  onAvatarChange: (url: string) => void;
  size?: number;
  disabled?: boolean;
}

export function ProfileImageUploader({
  avatarUrl,
  onAvatarChange,
  size = 140,
  disabled = false
}: ProfileImageUploaderProps) {
  const { colors, isDark } = useTheme();
  const { user } = useAuth();
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [localAvatarUrl, setLocalAvatarUrl] = useState<string | null>(null);
  
  // Use the prop or local state
  const displayAvatarUrl = avatarUrl || localAvatarUrl;
  
  // Reset success state after 3 seconds
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        setSuccess(false);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [success]);

  const handleSelectImage = async () => {
    // Clear previous states
    setError(null);
    setSuccess(false);
    
    try {
      // Request permissions - wrapped in try/catch
      try {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert(
            "Permission Required",
            "Please allow access to your photo library to change your profile picture.",
            [{ text: "OK" }]
          );
          return;
        }
      } catch (error) {
        console.error('Error requesting permission:', error);
        Alert.alert(
          "Error",
          "Could not request camera roll permissions. Please check your settings."
        );
        return;
      }
      
      // Launch image picker - wrapped in try/catch
      try {
        const result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsEditing: true,
          aspect: [1, 1],
          quality: 0.5, // Lower quality to reduce memory usage
          exif: false, // Don't need exif data
        });
        
        if (result.canceled || !result.assets || result.assets.length === 0) {
          // User canceled or no assets selected
          return;
        }
        
        // Begin upload process
        await uploadImage(result.assets[0].uri);
      } catch (error) {
        console.error('Image picker error:', error);
        Alert.alert(
          "Error",
          "Failed to select image. Please try again."
        );
      }
    } catch (error) {
      console.error('Uncaught error in select image:', error);
      setError('An unexpected error occurred. Please try again.');
    }
  };
  
  const uploadImage = async (uri: string) => {
    if (!user?.id) {
      setError('You must be logged in to upload a profile picture');
      return;
    }
    
    setUploading(true);
    
    try {
      // Create a blob from the URI - this is more reliable than base64 conversions
      const fetchResponse = await fetch(uri);
      const imageBlob = await fetchResponse.blob();
      
      // Create a unique filename with timestamp to prevent cache issues
      const timestamp = Date.now();
      const filename = `profile_${timestamp}`;
      const filePath = `avatars/${user.id}/${filename}.jpg`;
      
      // Create storage reference
      const storageRef = ref(storage, filePath);
      
      console.log('Uploading image to Firebase Storage:', filePath);
      
      // Upload directly using the blob (more reliable than base64)
      const uploadTask = uploadBytesResumable(storageRef, imageBlob);
      
      // Monitor upload progress and handle completion
      uploadTask.on(
        'state_changed',
        (snapshot) => {
          // Progress monitoring if needed
          const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          console.log(`Upload is ${progress}% done`);
        },
        (error) => {
          // Handle upload errors
          console.error('Upload error:', error);
          setError('Failed to upload image. Please try again.');
          setUploading(false);
        },
        async () => {
          // Upload completed successfully
          try {
            // Get download URL
            const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
            console.log('Image uploaded successfully. URL:', downloadURL);
            
            // Update profile in Firestore with retry logic
            const maxRetries = 3;
            let success = false;
            
            for (let attempt = 0; attempt < maxRetries; attempt++) {
              try {
                // Update profile in Firestore
                const profileRef = doc(db, 'profiles', user.id);
                await updateDoc(profileRef, { 
                  photoURL: downloadURL,
                  avatar_url: downloadURL, // Add both field names for compatibility
                  updated_at: new Date().toISOString()
                });
                
                console.log('Profile document updated with new image URL');
                success = true;
                break; // Exit retry loop if successful
              } catch (error) {
                console.error(`Error updating profile (attempt ${attempt + 1}):`, error);
                if (attempt === maxRetries - 1) {
                  throw error; // Re-throw on final attempt
                }
                // Wait before retrying
                await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, attempt)));
              }
            }
            
            if (!success) {
              throw new Error('Failed to update profile after multiple attempts');
            }
            
            // Update local state and trigger callback
            setLocalAvatarUrl(downloadURL);
            onAvatarChange(downloadURL);
            setSuccess(true);
            
            // Also update auth profile if possible
            try {
              const auth = getAuth(app);
              if (auth.currentUser) {
                await updateProfile(auth.currentUser, { photoURL: downloadURL });
                console.log('Auth user profile also updated with new image');
              }
            } catch (authError) {
              console.warn('Could not update auth profile, but Firestore was updated:', authError);
            }
          } catch (error) {
            console.error('Error after upload:', error);
            setError('Upload completed but failed to update profile.');
          } finally {
            setUploading(false);
          }
        }
      );
    } catch (error) {
      console.error('Error in upload process:', error);
      setError('Failed to process or upload image');
      setUploading(false);
    }
  };
  
  const handleRetry = () => {
    setError(null);
    setSuccess(false);
  };
  
  const renderOverlay = () => {
    if (uploading) {
      return (
        <View style={[styles.overlay, { backgroundColor: isDark ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)' }]}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={[styles.overlayText, { color: colors.text }]}>Uploading...</Text>
        </View>
      );
    }
    
    if (error) {
      return (
        <View style={[styles.overlay, { backgroundColor: isDark ? 'rgba(127, 29, 29, 0.8)' : 'rgba(254, 226, 226, 0.8)' }]}>
          <Text style={[styles.errorText, { color: isDark ? '#fca5a5' : '#dc2626' }]} numberOfLines={2}>
            {error}
          </Text>
          <TouchableOpacity 
            style={[styles.retryButton, { backgroundColor: isDark ? 'rgba(185, 28, 28, 0.8)' : 'rgba(220, 38, 38, 0.1)' }]}
            onPress={handleRetry}
          >
            <Feather name="refresh-cw" size={16} color={isDark ? 'white' : '#dc2626'} />
            <Text style={{ color: isDark ? 'white' : '#dc2626', marginLeft: 4, fontSize: 12 }}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }
    
    if (success) {
      return (
        <View style={[styles.overlay, { backgroundColor: isDark ? 'rgba(22, 101, 52, 0.8)' : 'rgba(220, 252, 231, 0.8)' }]}>
          <Text style={[styles.successText, { color: isDark ? '#86efac' : '#16a34a' }]}>
            Upload successful
          </Text>
        </View>
      );
    }
    
    return null;
  };
  
  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <TouchableOpacity
        style={[
          styles.imageContainer,
          { 
            width: size,
            height: size,
            borderRadius: size / 2,
            borderColor: isDark ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.1)', 
          }
        ]}
        onPress={handleSelectImage}
        disabled={disabled || uploading}
      >
        <FallbackImage
          source={displayAvatarUrl}
          width={size}
          height={size}
          borderRadius={size / 2}
          isAvatar={true}
          fallbackText={user?.email?.split('@')[0] || 'User'}
          containerStyle={styles.image}
          resizeMode="cover"
        />
        
        {renderOverlay()}
        
        <View style={styles.cameraIconContainer}>
          <Feather name="camera"  size={20}  color={colors.text} />
        </View>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  imageContainer: {
    borderWidth: 4,
    overflow: 'hidden',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
  },
  overlayText: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
  },
  errorText: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  successText: {
    fontSize: 14,
    fontWeight: '600',
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    marginTop: 8,
  },
  cameraIconContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: 'rgba(59, 130, 246, 0.9)',
    borderRadius: 50,
    padding: 6,
    margin: 4,
  },
}); 