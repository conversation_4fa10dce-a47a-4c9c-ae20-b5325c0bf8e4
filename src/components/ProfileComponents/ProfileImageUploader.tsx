import React, { useEffect, useState } from 'react';
import { View, TouchableOpacity, StyleSheet, Text, ActivityIndicator } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { FallbackImage } from '@/components/FallbackImage';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { useProfileImage } from '@/hooks/useProfileImage';

interface ProfileImageUploaderProps {
  avatarUrl: string | null;
  onAvatarChange: (url: string) => void;
  size?: number;
  disabled?: boolean;
}

export function ProfileImageUploader({
  avatarUrl,
  onAvatarChange,
  size = 140,
  disabled = false
}: ProfileImageUploaderProps) {
  const { colors, isDark } = useTheme();
  const { user } = useAuth();
  const [localAvatarUrl, setLocalAvatarUrl] = useState<string | null>(null);
  
  // Use the hook for all upload logic
  const { 
    uploading, 
    uploadProgress, 
    error, 
    success, 
    selectAndUploadImage 
  } = useProfileImage((url) => {
    setLocalAvatarUrl(url);
    onAvatarChange(url);
  });
  
  // Use the prop or local state
  const displayAvatarUrl = avatarUrl || localAvatarUrl;
  
  // Reset success state after 3 seconds (handled in hook)
  
  const handleRetry = () => {
    // Simply trigger the upload flow again
    selectAndUploadImage();
  };
  
  const renderOverlay = () => {
    if (uploading) {
      return (
        <View style={[styles.overlay, { backgroundColor: isDark ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)' }]}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={[styles.overlayText, { color: colors.text }]}>
            {uploadProgress > 0 ? `${Math.round(uploadProgress)}%` : 'Uploading...'}
          </Text>
        </View>
      );
    }
    
    if (error) {
      return (
        <View style={[styles.overlay, { backgroundColor: isDark ? 'rgba(127, 29, 29, 0.8)' : 'rgba(254, 226, 226, 0.8)' }]}>
          <Text style={[styles.errorText, { color: isDark ? '#fca5a5' : '#dc2626' }]} numberOfLines={2}>
            {error}
          </Text>
          <TouchableOpacity 
            style={[styles.retryButton, { backgroundColor: isDark ? 'rgba(185, 28, 28, 0.8)' : 'rgba(220, 38, 38, 0.1)' }]}
            onPress={handleRetry}
          >
            <Feather name="refresh-cw" size={16} color={isDark ? 'white' : '#dc2626'} />
            <Text style={{ color: isDark ? 'white' : '#dc2626', marginLeft: 4, fontSize: 12 }}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }
    
    if (success) {
      return (
        <View style={[styles.overlay, { backgroundColor: isDark ? 'rgba(22, 101, 52, 0.8)' : 'rgba(220, 252, 231, 0.8)' }]}>
          <Text style={[styles.successText, { color: isDark ? '#86efac' : '#16a34a' }]}>
            Upload successful
          </Text>
        </View>
      );
    }
    
    return null;
  };
  
  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <TouchableOpacity
        style={[
          styles.imageContainer,
          { 
            width: size,
            height: size,
            borderRadius: size / 2,
            borderColor: isDark ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.1)', 
          }
        ]}
        onPress={selectAndUploadImage}
        disabled={disabled || uploading}
      >
        <FallbackImage
          source={displayAvatarUrl}
          width={size}
          height={size}
          borderRadius={size / 2}
          isAvatar={true}
          fallbackText={user?.email?.split('@')[0] || 'User'}
          containerStyle={styles.image}
          resizeMode="cover"
        />
        
        {renderOverlay()}
        
        <View style={styles.cameraIconContainer}>
          <Feather name="camera"  size={20}  color={colors.text} />
        </View>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  imageContainer: {
    borderWidth: 4,
    overflow: 'hidden',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
  },
  overlayText: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
  },
  errorText: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  successText: {
    fontSize: 14,
    fontWeight: '600',
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    marginTop: 8,
  },
  cameraIconContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: 'rgba(59, 130, 246, 0.9)',
    borderRadius: 50,
    padding: 6,
    margin: 4,
  },
});