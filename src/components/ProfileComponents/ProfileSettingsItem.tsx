import React from 'react';
import { StyleSheet, TouchableOpacity, View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';

interface ProfileSettingsItemProps {
  label: string;
  icon: string;
  onPress: () => void;
  tint?: string;
  rightText?: string;
  showChevron?: boolean;
}

export function ProfileSettingsItem({
  label,
  icon,
  onPress,
  tint,
  rightText,
  showChevron = true,
}: ProfileSettingsItemProps) {
  const { colors, isDark } = useTheme();

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { backgroundColor: isDark ? colors.card : colors.background }
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.leftSection}>
        <View style={[styles.iconContainer, { backgroundColor: tint || colors.primary + '20' }]}>
          <Ionicons name={icon as any} size={20} color={tint || colors.primary} />
        </View>
        <Text style={[styles.label, { color: colors.text }]}>{label}</Text>
      </View>
      
      <View style={styles.rightSection}>
        {rightText && (
          <Text style={[styles.rightText, { color: colors.text }]}>{rightText}</Text>
        )}
        {showChevron && (
          <Ionicons name="chevron-forward" size={18} color={colors.text + '80'} />
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightText: {
    fontSize: 14,
    marginRight: 8,
    opacity: 0.7,
  },
}); 