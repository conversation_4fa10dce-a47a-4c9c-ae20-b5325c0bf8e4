import React from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Profile } from '@/services/databaseService';

interface StatsCardProps {
  profile: Profile | null;
  loadingProfile: boolean;
  editingField: string | null;
  editValue: string;
  setEditValue: (value: string) => void;
  isSaving: boolean;
  handleEditMeasurement: (field: string, value: string | number) => void;
  handleSaveMeasurement: () => void;
}

export function StatsCard({
  profile,
  loadingProfile,
  editingField,
  editValue,
  setEditValue,
  isSaving,
  handleEditMeasurement,
  handleSaveMeasurement
}: StatsCardProps) {
  const { colors, isDark } = useTheme();
  
  return (
    <View style={[
      styles.statsContainer, 
      { 
        backgroundColor: isDark ? colors.card : 'white',
        shadowColor: colors.shadow,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 10,
        elevation: 6,
        borderColor: colors.border,
        marginBottom: 5,
      }
    ]}>
      <View style={styles.statsRow}>
        <TouchableOpacity 
          style={styles.statItem}
          onPress={() => handleEditMeasurement('weight', profile?.weight || '0')}
        >
          {editingField === 'weight' ? (
            <View style={styles.statEditContainer}>
              <TextInput
                style={[styles.statEditInput, { color: colors.text }]}
                value={editValue}
                onChangeText={setEditValue}
                keyboardType="numeric"
                autoFocus
                onSubmitEditing={handleSaveMeasurement}
              />
              {isSaving ? (
                <ActivityIndicator size="small" color={colors.primary} style={{ marginTop: 4 }} />
              ) : (
                <TouchableOpacity 
                  style={styles.saveButton}
                  onPress={handleSaveMeasurement}
                >
                  <Text style={[styles.saveButtonText, { color: colors.primary }]}>Save</Text>
                </TouchableOpacity>
              )}
            </View>
          ) : (
            <>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {loadingProfile ? '-' : (profile?.weight || '0')}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>lbs</Text>
            </>
          )}
        </TouchableOpacity>
        
        <View style={[styles.statDivider, { backgroundColor: colors.border }]} />
        
        <TouchableOpacity 
          style={styles.statItem}
          onPress={() => handleEditMeasurement('height', profile?.height || '0"')}
        >
          {editingField === 'height' ? (
            <View style={styles.statEditContainer}>
              <TextInput
                style={[styles.statEditInput, { color: colors.text }]}
                value={editValue}
                onChangeText={setEditValue}
                autoFocus
                onSubmitEditing={handleSaveMeasurement}
              />
              {isSaving ? (
                <ActivityIndicator size="small" color={colors.primary} style={{ marginTop: 4 }} />
              ) : (
                <TouchableOpacity 
                  style={styles.saveButton}
                  onPress={handleSaveMeasurement}
                >
                  <Text style={[styles.saveButtonText, { color: colors.primary }]}>Save</Text>
                </TouchableOpacity>
              )}
            </View>
          ) : (
            <>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {loadingProfile ? '-' : (profile?.height || '0"')}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>height</Text>
            </>
          )}
        </TouchableOpacity>
        
        <View style={[styles.statDivider, { backgroundColor: colors.border }]} />
        
        <TouchableOpacity 
          style={styles.statItem}
          onPress={() => handleEditMeasurement('daily_calorie_goal', profile?.daily_calorie_goal || '2000')}
        >
          {editingField === 'daily_calorie_goal' ? (
            <View style={styles.statEditContainer}>
              <TextInput
                style={[styles.statEditInput, { color: colors.text }]}
                value={editValue}
                onChangeText={setEditValue}
                keyboardType="numeric"
                autoFocus
                onSubmitEditing={handleSaveMeasurement}
              />
              {isSaving ? (
                <ActivityIndicator size="small" color={colors.primary} style={{ marginTop: 4 }} />
              ) : (
                <TouchableOpacity 
                  style={styles.saveButton}
                  onPress={handleSaveMeasurement}
                >
                  <Text style={[styles.saveButtonText, { color: colors.primary }]}>Save</Text>
                </TouchableOpacity>
              )}
            </View>
          ) : (
            <>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {loadingProfile ? '-' : (profile?.daily_calorie_goal || '2,000')}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>cal/day</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  statsContainer: {
    marginHorizontal: 0,
    borderRadius: 16,
    borderWidth: 1,
    paddingVertical: 8,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    paddingHorizontal: 20,
    paddingVertical: 18,
  },
  statItem: {
    alignItems: 'center',
    minWidth: 70,
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
  },
  statLabel: {
    fontSize: 13,
    marginTop: 3,
  },
  statDivider: {
    width: 1,
    height: 30,
    marginHorizontal: 5,
  },
  statEditContainer: {
    alignItems: 'center',
  },
  statEditInput: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#3B82F6',
    paddingBottom: 2,
    minWidth: 60,
  },
  saveButton: {
    marginTop: 4,
    paddingVertical: 2,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  saveButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
}); 