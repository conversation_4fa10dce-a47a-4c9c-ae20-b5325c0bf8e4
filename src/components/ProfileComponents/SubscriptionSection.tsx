import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, Linking, Platform } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@/contexts/ThemeContext';

interface SubscriptionSectionProps {
  subscription: any;
  loadingSubscription: boolean;
  loading: boolean;
  handleSubscribe: () => void;
  handleManageSubscription: () => void;
  isSubscriptionActive: boolean;
}

export function SubscriptionSection({
  subscription,
  loadingSubscription,
  loading,
  handleSubscribe,
  handleManageSubscription,
  isSubscriptionActive
}: SubscriptionSectionProps) {
  const { colors, isDark } = useTheme();
  
  // Check if this is an admin subscription
  const isAdminSubscription = !!subscription?.is_admin;
  
  // Define gradient colors for subscription
  const subscriptionGradientColors = isDark
    ? isAdminSubscription 
      ? ['#7e22ce', '#4c1d95'] as const // Purple for admin
      : ['#312E81', '#1E40AF'] as const
    : isAdminSubscription
      ? ['#a855f7', '#7e22ce'] as const // Purple for admin
      : ['#60A5FA', '#3B82F6'] as const;
    
  return (
    <View style={styles.sectionContainer}>
      <Text style={[styles.sectionTitle, { color: colors.text }]}>Subscription</Text>
      <View style={[
        styles.subscriptionCard, 
        { 
          backgroundColor: colors.card,
          shadowColor: colors.shadow,
          borderColor: colors.border,
        }
      ]}>
        {loadingSubscription ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
              Loading subscription details...
            </Text>
          </View>
        ) : isSubscriptionActive ? (
          <>
            <View style={styles.subscriptionHeader}>
              <LinearGradient
                colors={subscriptionGradientColors}
                style={[styles.subscriptionIconContainer, styles.subscriptionIconShadow]}
              >
                <Feather name="zap" size={20}  color={colors.text} />
              </LinearGradient>
              <View style={styles.subscriptionInfo}>
                <Text style={[styles.subscriptionName, { color: colors.text }]}>
                  {isAdminSubscription ? 'Admin Access' : 'AI Friend'}
                </Text>
                <View style={styles.activeStatusContainer}>
                  <View style={[styles.activeStatusDot, isAdminSubscription && { backgroundColor: '#a855f7' }]} />
                  <Text style={[styles.subscriptionStatus, isAdminSubscription && { color: '#a855f7' }]}>
                    {isAdminSubscription ? 'Admin' : 'Active'}
                  </Text>
                </View>
              </View>
            </View>
            
            <View style={[styles.subscriptionDetails, { borderTopColor: colors.border }]}>
              <Text style={[styles.subscriptionDetail, { color: colors.textSecondary }]}>
                {isAdminSubscription 
                  ? 'You have unlimited admin access to all premium features.' 
                  : `Your subscription renews on ${new Date(subscription.current_period_end * 1000).toLocaleDateString()}`
                }
              </Text>
              
              {!isAdminSubscription && (
                <TouchableOpacity 
                  style={[styles.manageButton, { 
                    borderColor: colors.border,
                    backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)'
                  }]}
                  onPress={handleManageSubscription}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator size="small" color={colors.primary} />
                  ) : (
                    <Text style={[styles.manageButtonText, { color: colors.text }]}>
                      Manage Subscription
                    </Text>
                  )}
                </TouchableOpacity>
              )}
            </View>
          </>
        ) : (
          <>
            <View style={styles.subscriptionHeader}>
              <LinearGradient
                colors={subscriptionGradientColors}
                style={[styles.subscriptionIconContainer, styles.subscriptionIconShadow]}
              >
                <Feather name="zap" size={20}  color={colors.text} />
              </LinearGradient>
              <View style={styles.subscriptionInfo}>
                <Text style={[styles.subscriptionName, { color: colors.text }]}>
                  AI Friend
                </Text>
                <Text style={[styles.subscriptionPrice, { color: colors.textSecondary }]}>
                  C$5.00/month
                </Text>
              </View>
            </View>
            
            <Text style={[styles.subscriptionDescription, { color: colors.textSecondary }]}>
              Get personalized nutrition advice and meal recommendations from our AI assistant
            </Text>
            
            <TouchableOpacity
              onPress={handleSubscribe}
              disabled={loading}
              activeOpacity={0.9}
            >
              <LinearGradient
                colors={['#3B82F6', '#2563EB'] as const}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.subscribeButton}
              >
                {loading ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <Text style={styles.subscribeButtonText}>Subscribe Now</Text>
                )}
              </LinearGradient>
            </TouchableOpacity>
          </>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  sectionContainer: {
    paddingHorizontal: 20,
    paddingTop: 30,
  },
  sectionTitle: {
    fontSize: 19,
    fontWeight: '700',
    marginBottom: 16,
  },
  subscriptionCard: {
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
  },
  subscriptionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  subscriptionIconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  subscriptionIconShadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  subscriptionInfo: {
    flex: 1,
  },
  subscriptionName: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  subscriptionPrice: {
    fontSize: 15,
    fontWeight: '500',
  },
  activeStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activeStatusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#10B981',
    marginRight: 6,
  },
  subscriptionStatus: {
    fontSize: 14,
    fontWeight: '600',
    color: '#10B981',
  },
  subscriptionDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  subscribeButton: {
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  subscribeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  subscriptionDetails: {
    paddingTop: 12,
    borderTopWidth: 1,
  },
  subscriptionDetail: {
    fontSize: 14,
    marginBottom: 12,
  },
  manageButton: {
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
  },
  manageButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
}); 