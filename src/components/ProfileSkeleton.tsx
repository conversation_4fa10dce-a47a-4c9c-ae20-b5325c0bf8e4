import React from 'react';
import { View, StyleSheet, ViewStyle, StyleProp, Dimensions } from 'react-native';
import SkeletonLoader from './SkeletonLoader';
import { useTheme } from '../contexts/ThemeContext';

interface ProfileSkeletonProps {
  /**
   * Whether to include a large avatar
   */
  hasAvatar?: boolean;
  
  /**
   * Whether to include stats section
   */
  hasStats?: boolean;
  
  /**
   * Number of stats to show
   */
  statsCount?: number;
  
  /**
   * Whether to include action buttons
   */
  hasActions?: boolean;
  
  /**
   * Number of action buttons to show
   */
  actionsCount?: number;
  
  /**
   * Whether to include sections below stats
   */
  hasSections?: boolean;
  
  /**
   * Number of sections to show
   */
  sectionsCount?: number;
  
  /**
   * Custom styles for the container
   */
  style?: StyleProp<ViewStyle>;
  
  /**
   * Whether the content is loading
   */
  isLoading?: boolean;
}

/**
 * A profile skeleton component that renders a placeholder
 * profile screen with avatar, stats, and sections.
 */
export function ProfileSkeleton({
  hasAvatar = true,
  hasStats = true,
  statsCount = 3,
  hasActions = true,
  actionsCount = 2,
  hasSections = true,
  sectionsCount = 3,
  style,
  isLoading = true,
}: ProfileSkeletonProps) {
  const { colors, isDark } = useTheme();
  const screenWidth = Dimensions.get('window').width;
  
  // If not loading, don't render
  if (!isLoading) {
    return null;
  }
  
  // Calculate stats width based on count
  const statWidth = (screenWidth - 48) / statsCount;
  
  return (
    <View style={[styles.container, style]}>
      {/* Header area with avatar and name */}
      {hasAvatar && (
        <View style={styles.header}>
          <SkeletonLoader
            variant="circle"
            width={80}
            height={80}
            style={styles.avatar}
          />
          
          <View style={styles.nameContainer}>
            <SkeletonLoader
              variant="text"
              width={160}
              height={20}
              style={styles.name}
            />
            <SkeletonLoader
              variant="text"
              width={120}
              height={16}
            />
          </View>
        </View>
      )}
      
      {/* Stats row */}
      {hasStats && (
        <View style={styles.statsContainer}>
          {Array.from({ length: statsCount }).map((_, index) => (
            <View key={`stat-${index}`} style={[styles.stat, { width: statWidth }]}>
              <SkeletonLoader
                variant="text"
                width="60%"
                height={24}
                style={styles.statValue}
              />
              <SkeletonLoader
                variant="text"
                width="80%"
                height={14}
              />
            </View>
          ))}
        </View>
      )}
      
      {/* Action buttons */}
      {hasActions && (
        <View style={styles.actionsContainer}>
          {Array.from({ length: actionsCount }).map((_, index) => (
            <SkeletonLoader
              key={`action-${index}`}
              variant="pill"
              width={120}
              height={40}
              style={styles.actionButton}
            />
          ))}
        </View>
      )}
      
      {/* Content sections */}
      {hasSections && (
        <View style={styles.sectionsContainer}>
          {Array.from({ length: sectionsCount }).map((_, index) => (
            <View 
              key={`section-${index}`} 
              style={[
                styles.section,
                { borderColor: colors.border },
                index === sectionsCount - 1 && styles.lastSection,
              ]}
            >
              <SkeletonLoader
                variant="text"
                width="50%"
                height={18}
                style={styles.sectionTitle}
              />
              
              <View style={styles.sectionContent}>
                {Array.from({ length: 2 }).map((_, itemIndex) => (
                  <View key={`item-${index}-${itemIndex}`} style={styles.sectionItem}>
                    <SkeletonLoader
                      variant="rectangle"
                      width={32}
                      height={32}
                      style={styles.sectionItemIcon}
                    />
                    <View style={styles.sectionItemText}>
                      <SkeletonLoader
                        variant="text"
                        width="70%"
                        height={16}
                        style={styles.itemTitle}
                      />
                      <SkeletonLoader
                        variant="text"
                        width="40%"
                        height={14}
                      />
                    </View>
                  </View>
                ))}
              </View>
            </View>
          ))}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  avatar: {
    marginRight: 16,
  },
  nameContainer: {
    flex: 1,
  },
  name: {
    marginBottom: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  stat: {
    alignItems: 'center',
  },
  statValue: {
    marginBottom: 4,
  },
  actionsContainer: {
    flexDirection: 'row',
    marginBottom: 32,
  },
  actionButton: {
    marginRight: 12,
  },
  sectionsContainer: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
    borderBottomWidth: 1,
    paddingBottom: 16,
  },
  lastSection: {
    borderBottomWidth: 0,
    marginBottom: 0,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  sectionContent: {
    gap: 8,
  },
  sectionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionItemIcon: {
    marginRight: 12,
  },
  sectionItemText: {
    flex: 1,
  },
  itemTitle: {
    marginBottom: 4,
  },
});

export default ProfileSkeleton; 