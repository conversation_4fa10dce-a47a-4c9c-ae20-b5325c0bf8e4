import React from 'react';
import { 
  StyleSheet, 
  RefreshControl, 
  ViewStyle,
  RefreshControlProps,
  Platform,
  ColorValue
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useAccessibility } from './AccessibilityProvider';

interface PullToRefreshIndicatorProps {
  /**
   * Whether refreshing is in progress
   */
  refreshing: boolean;
  
  /**
   * Callback for when refreshing is triggered
   */
  onRefresh: () => void;
  
  /**
   * Optional custom colors for the refresh indicator
   */
  colors?: string[];
  
  /**
   * Optional tint color for iOS
   */
  tintColor?: ColorValue;
  
  /**
   * Optional title for iOS
   */
  title?: string;
  
  /**
   * Optional: Inform the user that the refresh is happening (for screen readers)
   */
  refreshingLabel?: string;
  
  /**
   * Optional: Background color for pull indicator
   */
  backgroundColor?: ColorValue;
  
  /**
   * Optional: Style for the refresh control
   */
  style?: ViewStyle;
  
  /**
   * Optional: Additional props for the RefreshControl
   */
  refreshControlProps?: Partial<RefreshControlProps>;
}

/**
 * A reusable pull-to-refresh indicator component with built-in accessibility features.
 * This component enhances the standard RefreshControl with proper theming, labels, and visual feedback.
 */
export function PullToRefreshIndicator({
  refreshing,
  onRefresh,
  colors,
  tintColor,
  title,
  refreshingLabel = 'Refreshing content',
  backgroundColor,
  style,
  refreshControlProps,
}: PullToRefreshIndicatorProps) {
  const { colors: themeColors } = useTheme();
  const { isScreenReaderEnabled, announceForAccessibility } = useAccessibility();
  
  // Announce refresh status for screen readers
  React.useEffect(() => {
    if (isScreenReaderEnabled) {
      if (refreshing) {
        announceForAccessibility(refreshingLabel);
      }
    }
  }, [refreshing, refreshingLabel, isScreenReaderEnabled, announceForAccessibility]);
  
  // Default colors based on theme
  const defaultColors = [themeColors.primary, themeColors.text];
  const defaultTintColor = themeColors.primary;
  
  return (
    <RefreshControl
      refreshing={refreshing}
      onRefresh={() => {
        // Provide immediate feedback for screen readers
        if (isScreenReaderEnabled) {
          announceForAccessibility('Refreshing content');
        }
        onRefresh();
      }}
      colors={colors || defaultColors}
      tintColor={tintColor || defaultTintColor}
      title={title}
      titleColor={themeColors.text}
      progressBackgroundColor={backgroundColor || (Platform.OS === 'android' ? themeColors.card : undefined)}
      progressViewOffset={Platform.OS === 'android' ? 10 : 0}
      style={style}
      accessible={true}
      accessibilityLabel={refreshing ? refreshingLabel : 'Pull down to refresh'}
      accessibilityHint="Swipe down to refresh the content"
      {...refreshControlProps}
    />
  );
}

const styles = StyleSheet.create({
  // Styles will be added as needed for customization
});

export default PullToRefreshIndicator; 