import React, { useEffect, useRef } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Animated, Platform, Dimensions } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

interface QuickActionsProps {
  fadeAnim: Animated.Value;
  translateY: Animated.Value;
  onTimelinePress?: () => void;
}

export function QuickActions({ fadeAnim, translateY, onTimelinePress }: QuickActionsProps) {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  
  // Animation refs for each button
  const scaleAnims = useRef([
    new Animated.Value(0.95),
    new Animated.Value(0.95),
    new Animated.Value(0.95),
    new Animated.Value(0.95)
  ]).current;
  
  // Stagger animations on mount
  useEffect(() => {
    const animations = scaleAnims.map((anim, index) => 
      Animated.spring(anim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        delay: 100 + (index * 70), // Stagger the animations
        useNativeDriver: Platform.OS !== 'web',
      })
    );
    
    Animated.parallel(animations).start();
    
    return () => {
      // Reset animations when component unmounts
      scaleAnims.forEach(anim => anim.setValue(0.95));
    };
  }, []);
  
  const actionItems = [
    {
      title: "Scan Food",
      icon: <Feather name="camera" size={22}  color={colors.text} />,
      onPress: () => router.push('/(tabs)/scan'),
      accessibilityLabel: "Scan Food",
      accessibilityHint: "Take a photo of your food to log it",
      colors: isDark ? ['#2563EB', '#1E40AF'] : ['#3B82F6', '#2563EB'],
      scale: scaleAnims[0],
    },
    {
      title: "Timeline",
      icon: <Feather name="file-text" size={22}  color={colors.text} />,
      onPress: onTimelinePress,
      accessibilityLabel: "Timeline",
      accessibilityHint: "View your nutrition timeline",
      colors: isDark ? ['#059669', '#047857'] : ['#10B981', '#059669'],
      scale: scaleAnims[1],
    },
    {
      title: "History",
      icon: <HistoryIcon size={22} color="white" />,
      onPress: () => router.push('/(tabs)/history'),
      accessibilityLabel: "History",
      accessibilityHint: "View your meal history",
      colors: isDark ? ['#BE185D', '#9D174D'] : ['#EC4899', '#DB2777'],
      scale: scaleAnims[2],
    },
    {
      title: "Activity",
      icon: <Feather name="activity" size={22}  color={colors.text} />,
      onPress: () => router.push('/'),
      accessibilityLabel: "Activity",
      accessibilityHint: "Track your physical activity",
      colors: isDark ? ['#B45309', '#92400E'] : ['#F59E0B', '#D97706'],
      scale: scaleAnims[3],
    },
  ];
  
  // Function to handle button press animation
  const handlePressIn = (scale: Animated.Value) => {
    Animated.spring(scale, {
      toValue: 0.92,
      friction: 5,
      tension: 300,
      useNativeDriver: Platform.OS !== 'web',
    }).start();
  };
  
  const handlePressOut = (scale: Animated.Value) => {
    Animated.spring(scale, {
      toValue: 1,
      friction: 3,
      tension: 40,
      useNativeDriver: Platform.OS !== 'web',
    }).start();
  };
  
  return (
    <Animated.View
      style={[
        styles.quickActions,
        {
          opacity: fadeAnim,
          transform: [{ translateY }]
        }
      ]}
    >
      {actionItems.map((item, index) => (
        <Animated.View 
          key={`action-${index}`}
          style={[
            styles.actionButtonWrapper,
            {
              transform: [{ scale: item.scale }],
            }
          ]}
        >
          <TouchableOpacity 
            onPress={item.onPress}
            onPressIn={() => handlePressIn(item.scale)}
            onPressOut={() => handlePressOut(item.scale)}
            accessibilityLabel={item.accessibilityLabel}
            accessibilityHint={item.accessibilityHint}
            accessibilityRole="button"
            activeOpacity={0.9}
            style={styles.touchableArea}
          >
            <LinearGradient
              colors={item.colors as [string, string]} 
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={[styles.actionButton, { 
                borderColor: isDark ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.05)',
              }]}
            >
              <View style={styles.actionIconContainer}>
                <View style={[styles.actionIcon, { backgroundColor: 'rgba(255,255,255,0.18)' }]}>
                  {item.icon}
                  <ArrowUpRight size={12} color="white" style={styles.arrowIcon} />
                </View>
                <Text style={styles.actionText}>{item.title}</Text>
              </View>
              
              {/* Add enhanced decoration elements */}
              <View style={[styles.decorationDot, styles.topRight, { backgroundColor: 'rgba(255,255,255,0.25)' }]} />
              <View style={[styles.decorationDot, styles.bottomLeft, { backgroundColor: 'rgba(255,255,255,0.2)' }]} />
              <View style={[styles.decorationLine, { backgroundColor: 'rgba(255,255,255,0.15)' }]} />
            </LinearGradient>
          </TouchableOpacity>
        </Animated.View>
      ))}
    </Animated.View>
  );
}

const { width } = Dimensions.get('window');
const itemWidth = (width - 48) / 4; // Adjusted for 4 items

const styles = StyleSheet.create({
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 12,
    paddingHorizontal: 12,
  },
  actionButtonWrapper: {
    width: itemWidth,
    marginHorizontal: 6,
  },
  touchableArea: {
    width: '100%',
    height: '100%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
    borderRadius: 16,
    ...(Platform.OS === 'web' ? {
      boxShadow: '0px 6px 12px rgba(0, 0, 0, 0.25)'
    } : {})
  },
  actionButton: {
    borderRadius: 16,
    padding: 14,
    alignItems: 'center',
    borderWidth: 1,
    overflow: 'hidden',
    position: 'relative',
    height: 110, // Fixed height for all buttons
  },
  actionIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    zIndex: 2,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    position: 'relative',
  },
  arrowIcon: {
    position: 'absolute',
    right: 4,
    top: 4,
  },
  actionText: {
    fontSize: 13,
    fontWeight: '700',
    letterSpacing: 0.2,
    color: 'white',
    textAlign: 'center',
  },
  decorationDot: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    opacity: 0.7,
  },
  topRight: {
    top: 12,
    right: 12,
  },
  bottomLeft: {
    bottom: 12,
    left: 12,
  },
  decorationLine: {
    position: 'absolute',
    width: '100%',
    height: 1,
    bottom: 36,
    left: 0,
    opacity: 0.5,
  },
}); 