import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ActivityIndicator,
  FlatList,
  Alert
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { FoodItem } from './SavedMealsComponent';

interface MealEntry {
  id: string;
  date: string;
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  foods: FoodItem[];
}

interface QuickAddComponentProps {
  onAddFoods: (foods: FoodItem[], mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack') => void;
  currentMealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
}

export function QuickAddComponent({ onAddFoods, currentMealType }: QuickAddComponentProps) {
  const { colors, isDark } = useTheme();
  const [recentMeals, setRecentMeals] = useState<MealEntry[]>([]);
  const [yesterdayMeals, setYesterdayMeals] = useState<MealEntry[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadRecentMeals();
  }, []);

  const loadRecentMeals = async () => {
    setLoading(true);
    try {
      // In a real app, we would fetch from a database
      // For this demo, we'll use mock data stored in AsyncStorage
      const storedMeals = await AsyncStorage.getItem('mealHistory');
      if (storedMeals) {
        const mealHistory: MealEntry[] = JSON.parse(storedMeals);
        
        // Get today's date
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        
        // Format dates as YYYY-MM-DD for comparison
        const yesterdayFormatted = yesterday.toISOString().split('T')[0];
        
        // Find yesterday's meals
        const yesterdayEntries = mealHistory.filter(
          meal => meal.date.split('T')[0] === yesterdayFormatted
        );
        
        // Get recent meals (last 7 days, excluding yesterday)
        const recentEntries = mealHistory.filter(
          meal => meal.date.split('T')[0] !== yesterdayFormatted
        ).slice(0, 10); // Limit to 10 entries
        
        setYesterdayMeals(yesterdayEntries);
        setRecentMeals(recentEntries);
      } else {
        // If no stored meals, create mock data for demonstration
        createMockMealHistory();
      }
    } catch (error) {
      console.error('Failed to load recent meals', error);
    } finally {
      setLoading(false);
    }
  };

  const createMockMealHistory = async () => {
    // Create some mock meal history data
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    // Mock foods
    const mockFoods: {[key: string]: FoodItem[]} = {
      breakfast: [
        {
          id: '1',
          name: 'Oatmeal with Berries',
          calories: 350,
          protein: 12,
          carbs: 60,
          fat: 8,
          servingSize: '1 bowl',
          servings: 1,
        },
        {
          id: '2',
          name: 'Coffee with Milk',
          calories: 50,
          protein: 2,
          carbs: 4,
          fat: 2.5,
          servingSize: '1 cup',
          servings: 1,
        },
      ],
      lunch: [
        {
          id: '3',
          name: 'Chicken Salad',
          calories: 450,
          protein: 35,
          carbs: 15,
          fat: 28,
          servingSize: '1 plate',
          servings: 1,
        },
        {
          id: '4',
          name: 'Whole Grain Bread',
          calories: 120,
          protein: 4,
          carbs: 22,
          fat: 2,
          servingSize: '1 slice',
          servings: 1,
        },
      ],
      dinner: [
        {
          id: '5',
          name: 'Grilled Salmon',
          calories: 350,
          protein: 40,
          carbs: 0,
          fat: 18,
          servingSize: '6 oz fillet',
          servings: 1,
        },
        {
          id: '6',
          name: 'Steamed Vegetables',
          calories: 100,
          protein: 3,
          carbs: 20,
          fat: 1,
          servingSize: '1 cup',
          servings: 1,
        },
        {
          id: '7',
          name: 'Brown Rice',
          calories: 220,
          protein: 5,
          carbs: 45,
          fat: 2,
          servingSize: '1 cup',
          servings: 1,
        },
      ],
      snack: [
        {
          id: '8',
          name: 'Greek Yogurt',
          calories: 150,
          protein: 15,
          carbs: 8,
          fat: 4,
          servingSize: '170g container',
          servings: 1,
        },
        {
          id: '9',
          name: 'Apple',
          calories: 80,
          protein: 0.5,
          carbs: 21,
          fat: 0.3,
          servingSize: '1 medium',
          servings: 1,
        },
      ],
    };
    
    // Create mock meal entries
    const mockMealHistory: MealEntry[] = [];
    
    // Yesterday's meals
    (['breakfast', 'lunch', 'dinner', 'snack'] as const).forEach(type => {
      mockMealHistory.push({
        id: `yesterday-${type}`,
        date: yesterday.toISOString(),
        type,
        foods: mockFoods[type],
      });
    });
    
    // Previous days' meals (for recent history)
    for (let i = 2; i <= 5; i++) {
      const pastDate = new Date(today);
      pastDate.setDate(pastDate.getDate() - i);
      
      (['breakfast', 'lunch', 'dinner', 'snack'] as const).forEach(type => {
        // Add some variety to the mock meals
        const foods = [...mockFoods[type]];
        if (i % 2 === 0 && type === 'breakfast') {
          foods.push({
            id: '10',
            name: 'Banana',
            calories: 105,
            protein: 1.3,
            carbs: 27,
            fat: 0.4,
            servingSize: '1 medium',
            servings: 1,
          });
        }
        
        mockMealHistory.push({
          id: `day-${i}-${type}`,
          date: pastDate.toISOString(),
          type,
          foods,
        });
      });
    }
    
    // Store the mock data
    await AsyncStorage.setItem('mealHistory', JSON.stringify(mockMealHistory));
    
    // Update state
    const yesterdayFormatted = yesterday.toISOString().split('T')[0];
    setYesterdayMeals(mockMealHistory.filter(
      meal => meal.date.split('T')[0] === yesterdayFormatted
    ));
    setRecentMeals(mockMealHistory.filter(
      meal => meal.date.split('T')[0] !== yesterdayFormatted
    ).slice(0, 10));
  };

  const handleCopyYesterdaysMeal = () => {
    const yesterdayMeal = yesterdayMeals.find(meal => meal.type === currentMealType);
    
    if (yesterdayMeal && yesterdayMeal.foods.length > 0) {
      onAddFoods(yesterdayMeal.foods, currentMealType);
      Alert.alert(
        'Meal Copied',
        `Yesterday's ${currentMealType} has been added.`
      );
    } else {
      Alert.alert(
        'No Meal Found',
        `No ${currentMealType} was recorded yesterday.`
      );
    }
  };

  const handleCopyMeal = (meal: MealEntry) => {
    if (meal.foods.length > 0) {
      onAddFoods(meal.foods, meal.type);
      Alert.alert(
        'Meal Copied',
        `The ${meal.type} has been added.`
      );
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  const calculateMealCalories = (foods: FoodItem[]) => {
    return Math.round(
      foods.reduce((total, food) => total + food.calories * food.servings, 0)
    );
  };

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: isDark ? colors.background : '#f9f9f9' }]}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading recent meals...
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Yesterday's meals section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Yesterday's Meals
        </Text>
        
        {yesterdayMeals.length > 0 ? (
          <>
            <TouchableOpacity
              style={[styles.quickCopyButton, { backgroundColor: colors.primary }]}
              onPress={handleCopyYesterdaysMeal}
            >
              <Feather name="copy" size={16}  color={colors.text} />
              <Text style={styles.quickCopyButtonText}>
                Copy Yesterday's {currentMealType.charAt(0).toUpperCase() + currentMealType.slice(1)}
              </Text>
            </TouchableOpacity>
            
            <FlatList
              data={yesterdayMeals}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.horizontalList}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[styles.mealCard, { backgroundColor: isDark ? colors.card : 'white' }]}
                  onPress={() => handleCopyMeal(item)}
                >
                  <View style={[styles.mealTypeIndicator, { backgroundColor: colors.primary }]}>
                    <Text style={styles.mealTypeText}>
                      {item.type.charAt(0).toUpperCase()}
                    </Text>
                  </View>
                  
                  <View style={styles.mealCardContent}>
                    <Text style={[styles.mealCardTitle, { color: colors.text }]}>
                      {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                    </Text>
                    <Text style={[styles.mealCardDetails, { color: colors.textSecondary }]}>
                      {item.foods.length} items • {calculateMealCalories(item.foods)} cal
                    </Text>
                  </View>
                  
                  <Feather name="chevron-right" size={16} color={colors.textSecondary} />
                </TouchableOpacity>
              )}
            />
          </>
        ) : (
          <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
            No meals recorded yesterday.
          </Text>
        )}
      </View>
      
      {/* Recent meals section */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Recent Meals
        </Text>
        
        {recentMeals.length > 0 ? (
          <FlatList
            data={recentMeals}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.verticalList}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[styles.recentMealCard, { backgroundColor: isDark ? colors.card : 'white' }]}
                onPress={() => handleCopyMeal(item)}
              >
                <View style={styles.recentMealLeft}>
                  <View style={[styles.mealTypeIndicator, { backgroundColor: colors.primary }]}>
                    <Text style={styles.mealTypeText}>
                      {item.type.charAt(0).toUpperCase()}
                    </Text>
                  </View>
                  
                  <View style={styles.recentMealInfo}>
                    <Text style={[styles.recentMealTitle, { color: colors.text }]}>
                      {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                    </Text>
                    <Text style={[styles.recentMealDate, { color: colors.textSecondary }]}>
                      {formatDate(item.date)}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.recentMealRight}>
                  <Text style={[styles.recentMealCalories, { color: colors.text }]}>
                    {calculateMealCalories(item.foods)} cal
                  </Text>
                  <Text style={[styles.recentMealItems, { color: colors.textSecondary }]}>
                    {item.foods.length} items
                  </Text>
                </View>
              </TouchableOpacity>
            )}
          />
        ) : (
          <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
            No recent meal history found.
          </Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  quickCopyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  quickCopyButtonText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 8,
  },
  horizontalList: {
    paddingBottom: 8,
  },
  verticalList: {
    paddingBottom: 20,
  },
  mealCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    marginRight: 12,
    width: 220,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  mealTypeIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  mealTypeText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  mealCardContent: {
    flex: 1,
  },
  mealCardTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  mealCardDetails: {
    fontSize: 14,
  },
  recentMealCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  recentMealLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  recentMealInfo: {
    marginLeft: 12,
  },
  recentMealTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  recentMealDate: {
    fontSize: 14,
  },
  recentMealRight: {
    alignItems: 'flex-end',
  },
  recentMealCalories: {
    fontWeight: '500',
    fontSize: 16,
  },
  recentMealItems: {
    fontSize: 14,
  },
  emptyStateText: {
    textAlign: 'center',
    paddingVertical: 20,
    fontSize: 14,
  },
});

export default QuickAddComponent; 