# Bulk Edit Components

This directory contains components for implementing bulk editing functionality in lists. These components allow users to select multiple items and perform batch operations like delete, update, etc.

## Components Overview

1. **BulkEditProvider**: Context provider component that manages bulk edit state
2. **BulkEditToolbar**: Toolbar component that appears when bulk edit mode is active
3. **SelectableListItem**: List item wrapper component that supports selection in bulk edit mode
4. **BulkEditToggle**: Button component that toggles bulk edit mode

## Usage Example

Here's a simple example of how to use these components:

```tsx
import React, { useState } from 'react';
import { View, FlatList } from 'react-native';
import BulkEditProvider from './BulkEditProvider';
import BulkEditToolbar from './BulkEditToolbar';
import SelectableListItem from './SelectableListItem';
import BulkEditToggle from './BulkEditToggle';

interface Item {
  id: string;
  name: string;
}

function MyListComponent() {
  const [items, setItems] = useState<Item[]>([
    { id: '1', name: 'Item 1' },
    { id: '2', name: 'Item 2' },
    { id: '3', name: 'Item 3' },
  ]);
  
  // Function to delete selected items
  const handleDelete = async (selectedItems: Item[]) => {
    const selectedIds = selectedItems.map(item => item.id);
    setItems(prevItems => prevItems.filter(item => !selectedIds.includes(item.id)));
  };
  
  // Function to update selected items
  const handleUpdate = () => {
    // Implement your update logic here
    console.log('Update selected items');
  };
  
  return (
    <BulkEditProvider<Item>>
      <View style={{ flex: 1 }}>
        {/* Header with toggle button */}
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', padding: 16 }}>
          <Text>My List</Text>
          <BulkEditToggle />
        </View>
        
        {/* List of items */}
        <FlatList
          data={items}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <SelectableListItem<Item>
              item={item}
              onPress={() => console.log(`Pressed ${item.name}`)}
            >
              <Text>{item.name}</Text>
            </SelectableListItem>
          )}
        />
        
        {/* Toolbar for bulk actions */}
        <BulkEditToolbar<Item>
          onDelete={() => handleDelete([])} // Actual selected items are passed internally
          onUpdate={handleUpdate}
        />
      </View>
    </BulkEditProvider>
  );
}
```

## Component API

### BulkEditProvider

```tsx
interface BulkEditProviderProps<T> {
  children: ReactNode;
  getItemId?: (item: T) => string | number; // Function to get unique ID from item
}
```

### BulkEditToggle

```tsx
interface BulkEditToggleProps {
  style?: any;
  label?: string;
  showIcon?: boolean;
  activeLabel?: string;
}
```

### SelectableListItem

```tsx
interface SelectableListItemProps<T> {
  item: T;
  onPress?: () => void;
  onLongPress?: () => void;
  children: ReactNode;
  style?: StyleProp<ViewStyle>;
  selectableContainerStyle?: StyleProp<ViewStyle>;
  selectable?: boolean;
  checkboxComponent?: ReactNode;
  alwaysShowIndicator?: boolean;
}
```

### BulkEditToolbar

```tsx
interface BulkEditToolbarProps {
  title?: string;
  actions?: BulkEditAction[];
  onDelete?: () => Promise<void>;
  onUpdate?: () => void;
  showDelete?: boolean;
  showUpdate?: boolean;
}

interface BulkEditAction {
  id: string;
  label: string;
  icon: ReactNode;
  onPress: () => void;
  destructive?: boolean;
}
```

## Customization

You can customize the appearance and behavior of the bulk edit components by:

1. Passing custom styles to the components
2. Creating custom action buttons for the toolbar
3. Implementing custom selection indicators
4. Defining your own edit operations

## Hook API

The `useBulkEdit` hook provides the following functionality:

```tsx
interface BulkEditContextType<T> {
  isActive: boolean;                // Whether bulk edit mode is active
  toggleBulkEdit: () => void;       // Toggle bulk edit mode
  selectedItems: T[];               // Currently selected items
  toggleItemSelection: (item: T) => void; // Toggle selection of an item
  isItemSelected: (item: T) => boolean;   // Check if an item is selected
  selectAll: (items: T[]) => void;  // Select all items
  deselectAll: () => void;          // Deselect all items
  deleteSelected: (onDelete: (items: T[]) => Promise<void> | void) => Promise<void>;
  updateSelected: (onUpdate: (items: T[]) => Promise<void> | void) => Promise<void>;
  selectedCount: number;            // Number of selected items
}
```

## Best Practices

1. Always wrap your list with `BulkEditProvider`
2. Use `SelectableListItem` for each list item
3. Implement appropriate UI feedback for selection
4. Provide clear actions in the toolbar
5. Add confirmation for destructive operations
6. Allow users to easily exit bulk edit mode 