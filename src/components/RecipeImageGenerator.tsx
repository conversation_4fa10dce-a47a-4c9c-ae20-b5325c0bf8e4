import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  TextInput, 
  Image, 
  ActivityIndicator, 
  ScrollView 
} from 'react-native';
import { generateRecipeImage } from '@/services/imageGenerationService';
import { useTheme } from '@/contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

interface RecipeImageGeneratorProps {
  recipeName?: string;
  ingredients?: string[];
  onImageGenerated?: (imageUrl: string) => void;
}

export function RecipeImageGenerator({
  recipeName = '',
  ingredients = [],
  onImageGenerated
}: RecipeImageGeneratorProps) {
  const { colors, isDark } = useTheme();
  const [name, setName] = useState(recipeName);
  const [ingredientsList, setIngredientsList] = useState<string[]>(ingredients);
  const [newIngredient, setNewIngredient] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Add a new ingredient to the list
  const addIngredient = () => {
    if (newIngredient.trim() === '') return;
    
    setIngredientsList(prev => [...prev, newIngredient.trim()]);
    setNewIngredient('');
  };

  // Remove an ingredient from the list
  const removeIngredient = (index: number) => {
    setIngredientsList(prev => prev.filter((_, i) => i !== index));
  };

  // Generate the recipe image
  const handleGenerateImage = async () => {
    if (name.trim() === '') {
      setError('Please enter a recipe name');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      const url = await generateRecipeImage(name, ingredientsList);
      
      if (!url) {
        setError('Failed to generate image. Please try again.');
        return;
      }
      
      setImageUrl(url);
      
      // Call the callback if provided
      if (onImageGenerated) {
        onImageGenerated(url);
      }
    } catch (err) {
      console.error('Error generating image:', err);
      setError('An error occurred while generating the image');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={[styles.title, { color: colors.text }]}>
        Recipe Image Generator
      </Text>
      
      {/* Recipe name input */}
      <Text style={[styles.label, { color: colors.textSecondary }]}>Recipe Name</Text>
      <TextInput
        style={[
          styles.input,
          { 
            backgroundColor: isDark ? '#333' : '#f5f5f5',
            color: colors.text,
            borderColor: colors.border
          }
        ]}
        value={name}
        onChangeText={setName}
        placeholder="Enter recipe name"
        placeholderTextColor={isDark ? '#999' : '#aaa'}
      />
      
      {/* Ingredients list */}
      <Text style={[styles.label, { color: colors.textSecondary }]}>Main Ingredients</Text>
      <View style={styles.ingredientsContainer}>
        {ingredientsList.map((ingredient, index) => (
          <View 
            key={`${ingredient}-${index}`}
            style={[
              styles.ingredientChip,
              { backgroundColor: isDark ? '#444' : '#e0e0e0' }
            ]}
          >
            <Text style={[styles.ingredientText, { color: colors.text }]}>
              {ingredient}
            </Text>
            <TouchableOpacity 
              onPress={() => removeIngredient(index)}
              style={styles.removeButton}
            >
              <Ionicons name="close-circle" size={16} color={isDark ? '#ccc' : '#777'} />
            </TouchableOpacity>
          </View>
        ))}
      </View>
      
      {/* Add ingredient input */}
      <View style={styles.addIngredientRow}>
        <TextInput
          style={[
            styles.ingredientInput,
            { 
              backgroundColor: isDark ? '#333' : '#f5f5f5',
              color: colors.text,
              borderColor: colors.border
            }
          ]}
          value={newIngredient}
          onChangeText={setNewIngredient}
          placeholder="Add an ingredient"
          placeholderTextColor={isDark ? '#999' : '#aaa'}
          onSubmitEditing={addIngredient}
          returnKeyType="done"
        />
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: colors.primary }]}
          onPress={addIngredient}
        >
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>
      
      {/* Error message */}
      {error && (
        <Text style={[styles.errorText, { color: '#f44336' }]}>
          {error}
        </Text>
      )}
      
      {/* Generate button */}
      <TouchableOpacity
        style={[
          styles.generateButton,
          { backgroundColor: colors.primary },
          isLoading && { opacity: 0.7 }
        ]}
        onPress={handleGenerateImage}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator color="white" size="small" />
        ) : (
          <Text style={styles.generateButtonText}>
            Generate Image with DALL-E 3
          </Text>
        )}
      </TouchableOpacity>
      
      {/* Image preview */}
      {imageUrl && (
        <View style={styles.imageContainer}>
          <Text style={[styles.previewLabel, { color: colors.textSecondary }]}>
            Generated Image
          </Text>
          <Image
            source={{ uri: imageUrl }}
            style={styles.generatedImage}
            resizeMode="cover"
          />
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    textAlign: 'center',
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
  },
  ingredientsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  ingredientChip: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  ingredientText: {
    fontSize: 14,
    marginRight: 4,
  },
  removeButton: {
    padding: 2,
  },
  addIngredientRow: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  ingredientInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginRight: 8,
  },
  addButton: {
    borderRadius: 8,
    padding: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  generateButton: {
    borderRadius: 8,
    padding: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  generateButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  errorText: {
    fontSize: 14,
    marginBottom: 16,
    textAlign: 'center',
  },
  imageContainer: {
    marginBottom: 24,
  },
  previewLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    textAlign: 'center',
  },
  generatedImage: {
    width: '100%',
    height: 300,
    borderRadius: 12,
  },
});

export default RecipeImageGenerator; 