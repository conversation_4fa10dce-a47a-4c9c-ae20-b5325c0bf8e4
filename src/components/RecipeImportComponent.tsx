import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , MaterialIcons } from '@expo/vector-icons';

interface Ingredient {
  name: string;
  quantity: string;
  unit: string;
}

interface Recipe {
  title: string;
  description: string;
  sourceUrl: string;
  ingredients: Ingredient[];
  instructions: string[];
  prepTime?: string;
  cookTime?: string;
  servings?: number;
  calories?: number;
  protein?: number;
  carbs?: number;
  fat?: number;
  image?: string;
}

interface RecipeImportComponentProps {
  onRecipeImported?: (recipe: Recipe) => void;
}

export function RecipeImportComponent({ onRecipeImported }: RecipeImportComponentProps) {
  const { colors, isDark } = useTheme();
  const [url, setUrl] = useState('');
  const [isImporting, setIsImporting] = useState(false);
  const [importedRecipe, setImportedRecipe] = useState<Recipe | null>(null);
  const [importError, setImportError] = useState<string | null>(null);

  const handleImport = async () => {
    if (!url.trim()) {
      Alert.alert('Invalid URL', 'Please enter a valid recipe URL');
      return;
    }

    setIsImporting(true);
    setImportError(null);

    try {
      // Here we would call an API to parse the recipe from the URL
      // For demo purposes, we'll simulate an API call with a timeout
      setTimeout(() => {
        // Check if URL is roughly valid
        if (!url.startsWith('http')) {
          throw new Error('Please enter a valid URL starting with http:// or https://');
        }

        // Mock successful response with sample data
        const mockRecipe: Recipe = {
          title: 'Healthy Banana Oatmeal Pancakes',
          description: 'Delicious and nutritious pancakes made with oats and bananas. Perfect for a healthy breakfast!',
          sourceUrl: url,
          ingredients: [
            { name: 'banana', quantity: '1', unit: 'large' },
            { name: 'rolled oats', quantity: '1/2', unit: 'cup' },
            { name: 'egg', quantity: '1', unit: 'large' },
            { name: 'almond milk', quantity: '1/4', unit: 'cup' },
            { name: 'cinnamon', quantity: '1/2', unit: 'tsp' },
            { name: 'baking powder', quantity: '1/2', unit: 'tsp' },
            { name: 'vanilla extract', quantity: '1/2', unit: 'tsp' },
            { name: 'salt', quantity: '1/8', unit: 'tsp' },
          ],
          instructions: [
            'Blend all ingredients until smooth',
            'Heat a non-stick pan over medium heat',
            'Pour 1/4 cup of batter for each pancake',
            'Cook for 2-3 minutes until bubbles form on the surface',
            'Flip and cook for another 2 minutes',
            'Serve with fresh fruits and maple syrup'
          ],
          prepTime: '5 minutes',
          cookTime: '10 minutes',
          servings: 2,
          calories: 220,
          protein: 8,
          carbs: 35,
          fat: 6
        };

        setImportedRecipe(mockRecipe);
        setIsImporting(false);

      }, 2000);
    } catch (error) {
      setImportError(error instanceof Error ? error.message : 'Failed to import recipe');
      setIsImporting(false);
    }
  };

  const handleSaveRecipe = () => {
    if (importedRecipe && onRecipeImported) {
      onRecipeImported(importedRecipe);
      Alert.alert('Success', 'Recipe has been saved to your collection');
      
      // Reset the form
      setUrl('');
      setImportedRecipe(null);
    }
  };

  const handleTryAgain = () => {
    setImportError(null);
    setUrl('');
  };

  const renderIngredientList = () => {
    if (!importedRecipe) return null;

    return (
      <View style={styles.ingredientList}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Ingredients</Text>
        {importedRecipe.ingredients.map((ingredient, index) => (
          <View key={index} style={styles.ingredientItem}>
            <View style={[styles.bulletPoint, { backgroundColor: colors.primary }]} />
            <Text style={[styles.ingredientText, { color: colors.text }]}>
              {ingredient.quantity} {ingredient.unit} {ingredient.name}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  const renderInstructionsList = () => {
    if (!importedRecipe) return null;

    return (
      <View style={styles.instructionsList}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Instructions</Text>
        {importedRecipe.instructions.map((instruction, index) => (
          <View key={index} style={styles.instructionItem}>
            <Text style={[styles.instructionNumber, { color: colors.primary }]}>
              {index + 1}.
            </Text>
            <Text style={[styles.instructionText, { color: colors.text }]}>
              {instruction}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  const renderNutritionalInfo = () => {
    if (!importedRecipe) return null;

    return (
      <View style={[styles.nutritionCard, { backgroundColor: isDark ? colors.subtle : '#f5f5f5' }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Nutritional Information</Text>
        <View style={styles.nutritionGrid}>
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>{importedRecipe.calories}</Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Calories</Text>
          </View>
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>{importedRecipe.protein}g</Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Protein</Text>
          </View>
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>{importedRecipe.carbs}g</Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Carbs</Text>
          </View>
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>{importedRecipe.fat}g</Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Fat</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderPreparationInfo = () => {
    if (!importedRecipe) return null;

    return (
      <View style={styles.prepInfo}>
        <View style={styles.prepItem}>
          <Text style={[styles.prepLabel, { color: colors.textSecondary }]}>Prep Time:</Text>
          <Text style={[styles.prepValue, { color: colors.text }]}>{importedRecipe.prepTime}</Text>
        </View>
        <View style={styles.prepItem}>
          <Text style={[styles.prepLabel, { color: colors.textSecondary }]}>Cook Time:</Text>
          <Text style={[styles.prepValue, { color: colors.text }]}>{importedRecipe.cookTime}</Text>
        </View>
        <View style={styles.prepItem}>
          <Text style={[styles.prepLabel, { color: colors.textSecondary }]}>Servings:</Text>
          <Text style={[styles.prepValue, { color: colors.text }]}>{importedRecipe.servings}</Text>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {!importedRecipe ? (
        <View style={[styles.importCard, { backgroundColor: isDark ? colors.card : 'white' }]}>
          <View style={styles.importHeader}>
            <Feather name="link" size={24} color={colors.primary} style={styles.importIcon} />
            <Text style={[styles.importTitle, { color: colors.text }]}>
              Import Recipe from URL
            </Text>
          </View>

          <Text style={[styles.importDescription, { color: colors.textSecondary }]}>
            Paste a link to a recipe page and we'll automatically extract the ingredients, instructions, and nutritional information.
          </Text>

          <View style={[styles.inputContainer, { borderColor: colors.border }]}>
            <TextInput
              style={[styles.urlInput, { color: colors.text }]}
              placeholder="Paste recipe URL here"
              placeholderTextColor={colors.textSecondary}
              value={url}
              onChangeText={setUrl}
              autoCapitalize="none"
              keyboardType="url"
            />
            <TouchableOpacity
              style={[styles.clearButton, url.length > 0 ? styles.clearButtonVisible : {}]}
              onPress={() => setUrl('')}
            >
              <Feather name="x" size={16} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>

          {importError && (
            <View style={[styles.errorContainer, { backgroundColor: isDark ? 'rgba(220, 53, 69, 0.1)' : 'rgba(220, 53, 69, 0.1)' }]}>
              <Text style={[styles.errorText, { color: colors.danger }]}>
                {importError}
              </Text>
              <TouchableOpacity
                style={[styles.tryAgainButton, { backgroundColor: colors.danger }]}
                onPress={handleTryAgain}
              >
                <Text style={styles.tryAgainText}>Try Again</Text>
              </TouchableOpacity>
            </View>
          )}

          <TouchableOpacity
            style={[
              styles.importButton,
              { backgroundColor: colors.primary },
              isImporting && { opacity: 0.7 }
            ]}
            onPress={handleImport}
            disabled={isImporting || url.trim().length === 0}
          >
            {isImporting ? (
              <ActivityIndicator size="small" color="white" style={styles.buttonIcon} />
            ) : (
              <Import size={18} color="white" style={styles.buttonIcon} />
            )}
            <Text style={styles.importButtonText}>
              {isImporting ? 'Importing...' : 'Import Recipe'}
            </Text>
          </TouchableOpacity>

          <View style={styles.supportedSites}>
            <Text style={[styles.supportedTitle, { color: colors.textSecondary }]}>
              Supported Sites
            </Text>
            <Text style={[styles.supportedText, { color: colors.text }]}>
              AllRecipes, Food Network, Epicurious, Bon Appétit, Simply Recipes, and most major recipe websites.
            </Text>
          </View>
        </View>
      ) : (
        <ScrollView style={styles.recipeContainer}>
          <View style={[styles.recipeCard, { backgroundColor: isDark ? colors.card : 'white' }]}>
            <View style={styles.recipeHeader}>
              <Text style={[styles.recipeTitle, { color: colors.text }]}>
                {importedRecipe.title}
              </Text>
              <Text style={[styles.recipeDescription, { color: colors.textSecondary }]}>
                {importedRecipe.description}
              </Text>
            </View>

            {renderPreparationInfo()}
            {renderNutritionalInfo()}
            {renderIngredientList()}
            {renderInstructionsList()}

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.cancelButton, { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }]}
                onPress={() => {
                  setImportedRecipe(null);
                  setUrl('');
                }}
              >
                <Feather name="x" size={18} color={colors.text} style={styles.buttonIcon} />
                <Text style={[styles.buttonText, { color: colors.text }]}>
                  Cancel
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.saveButton, { backgroundColor: colors.primary }]}
                onPress={handleSaveRecipe}
              >
                <MaterialIcons name="check-circle" size={18} style={styles.buttonIcon} />
                <Text style={[styles.buttonText, { color: 'white' }]}>
                  Save Recipe
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  importCard: {
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  importHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  importIcon: {
    marginRight: 12,
  },
  importTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  importDescription: {
    fontSize: 14,
    marginBottom: 20,
    lineHeight: 20,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 20,
  },
  urlInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  clearButton: {
    padding: 8,
    opacity: 0,
  },
  clearButtonVisible: {
    opacity: 1,
  },
  importButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
    borderRadius: 8,
    marginBottom: 16,
  },
  buttonIcon: {
    marginRight: 8,
  },
  importButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
  },
  supportedSites: {
    marginTop: 8,
  },
  supportedTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  supportedText: {
    fontSize: 13,
    lineHeight: 18,
  },
  errorContainer: {
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  errorText: {
    fontSize: 14,
    flex: 1,
  },
  tryAgainButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    marginLeft: 8,
  },
  tryAgainText: {
    color: 'white',
    fontSize: 13,
    fontWeight: '500',
  },
  recipeContainer: {
    flex: 1,
  },
  recipeCard: {
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  recipeHeader: {
    marginBottom: 16,
  },
  recipeTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 8,
  },
  recipeDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  prepInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  prepItem: {
    flexDirection: 'row',
    marginRight: 16,
    marginBottom: 8,
  },
  prepLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  prepValue: {
    fontSize: 14,
  },
  nutritionCard: {
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  nutritionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    alignItems: 'center',
  },
  nutritionValue: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  nutritionLabel: {
    fontSize: 12,
  },
  ingredientList: {
    marginBottom: 20,
  },
  ingredientItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  bulletPoint: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginTop: 7,
    marginRight: 10,
  },
  ingredientText: {
    fontSize: 15,
    lineHeight: 20,
    flex: 1,
  },
  instructionsList: {
    marginBottom: 20,
  },
  instructionItem: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  instructionNumber: {
    fontSize: 15,
    fontWeight: '600',
    marginRight: 8,
    width: 20,
  },
  instructionText: {
    fontSize: 15,
    lineHeight: 22,
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    marginTop: 8,
  },
  cancelButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
    borderRadius: 8,
    marginRight: 8,
  },
  saveButton: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
    borderRadius: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default RecipeImportComponent; 