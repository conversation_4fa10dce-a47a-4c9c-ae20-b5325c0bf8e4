import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Image,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { importRecipeFromUrl, saveImportedRecipe } from '@/services/recipeImporterService';
import { Feather } from '@expo/vector-icons';

export default function RecipeImporter({ onComplete, onClose }: { onComplete: (recipeId: string) => void, onClose: () => void }) {
  const { colors, isDark } = useTheme();
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [importedRecipe, setImportedRecipe] = useState<any>(null);
  
  const handleImport = async () => {
    // Validate the URL
    if (!url) {
      setErrorMessage('Please enter a URL');
      return;
    }
    
    try {
      setLoading(true);
      setErrorMessage('');
      
      const recipe = await importRecipeFromUrl(url);
      
      if (!recipe) {
        setErrorMessage('Unable to import recipe from this URL. Please check the URL and try again.');
        return;
      }
      
      setImportedRecipe(recipe);
    } catch (error) {
      console.error('Error importing recipe:', error);
      setErrorMessage('An error occurred while importing the recipe. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  const handleSave = async () => {
    try {
      setLoading(true);
      
      if (!importedRecipe) {
        setErrorMessage('No recipe to save');
        return;
      }
      
      const savedRecipe = await saveImportedRecipe(importedRecipe);
      
      Alert.alert(
        'Recipe Saved',
        `"${savedRecipe.name}" has been added to your recipes`,
        [{ text: 'OK' }]
      );
      
      onComplete(savedRecipe.id);
    } catch (error) {
      console.error('Error saving recipe:', error);
      setErrorMessage('An error occurred while saving the recipe. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={[styles.container, { backgroundColor: isDark ? colors.background : '#f9f9f9' }]}
    >
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Import Recipe</Text>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Feather name="x" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.inputContainer}>
          <View style={[styles.urlInputContainer, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
            <Feather name="link" size={20} color={colors.textSecondary} style={styles.urlIcon} />
            <TextInput
              style={[styles.urlInput, { color: colors.text }]}
              placeholder="Paste recipe URL here"
              placeholderTextColor={colors.textSecondary}
              value={url}
              onChangeText={setUrl}
              autoCapitalize="none"
              autoCorrect={false}
              keyboardType="url"
              returnKeyType="go"
              onSubmitEditing={handleImport}
            />
            {url ? (
              <TouchableOpacity style={styles.clearButton} onPress={() => setUrl('')}>
                <Feather name="x" size={16} color={colors.textSecondary} />
              </TouchableOpacity>
            ) : null}
          </View>
          
          <TouchableOpacity
            style={[styles.importButton, { backgroundColor: colors.primary }]}
            onPress={handleImport}
            disabled={loading || !url}
          >
            {loading && !importedRecipe ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <>
                <Import size={18} color="white" style={styles.importIcon} />
                <Text style={styles.importButtonText}>Import Recipe</Text>
              </>
            )}
          </TouchableOpacity>
          
          {errorMessage ? (
            <View style={styles.errorContainer}>
              <Feather name="alert-circle" size={16} color={colors.danger} style={styles.errorIcon} />
              <Text style={[styles.errorText, { color: colors.danger }]}>{errorMessage}</Text>
            </View>
          ) : null}
        </View>
        
        {importedRecipe ? (
          <View style={[styles.previewContainer, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
            <Text style={[styles.previewTitle, { color: colors.text }]}>Recipe Preview</Text>
            
            <Text style={[styles.recipeName, { color: colors.text }]}>{importedRecipe.name}</Text>
            
            {importedRecipe.imageUrl ? (
              <Image 
                source={{ uri: importedRecipe.imageUrl }} 
                style={styles.recipeImage}
                resizeMode="cover"
              />
            ) : null}
            
            <View style={styles.metadataContainer}>
              {importedRecipe.prepTime || importedRecipe.cookTime ? (
                <View style={styles.metadataItem}>
                  <Feather name="clock" size={16} color={colors.primary} style={styles.metadataIcon} />
                  <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
                    {importedRecipe.prepTime ? `Prep: ${importedRecipe.prepTime}min` : ''}
                    {importedRecipe.prepTime && importedRecipe.cookTime ? ' · ' : ''}
                    {importedRecipe.cookTime ? `Cook: ${importedRecipe.cookTime}min` : ''}
                  </Text>
                </View>
              ) : null}
              
              {importedRecipe.servings ? (
                <View style={styles.metadataItem}>
                  <Feather name="users" size={16} color={colors.primary} style={styles.metadataIcon} />
                  <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
                    Serves: {importedRecipe.servings}
                  </Text>
                </View>
              ) : null}
            </View>
            
            <View style={styles.sectionContainer}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Ingredients</Text>
              {importedRecipe.ingredients.map((ingredient: any, index: number) => (
                <View key={`ingredient-${index}`} style={styles.ingredientItem}>
                  <View style={[styles.bulletPoint, { backgroundColor: colors.primary }]} />
                  <Text style={[styles.ingredientText, { color: colors.text }]}>
                    {ingredient.amount && ingredient.unit 
                      ? `${ingredient.amount} ${ingredient.unit} ${ingredient.name}`
                      : ingredient.amount 
                        ? `${ingredient.amount} ${ingredient.name}`
                        : ingredient.name
                    }
                  </Text>
                </View>
              ))}
            </View>
            
            {importedRecipe.nutritionFacts ? (
              <View style={[styles.nutritionContainer, { backgroundColor: isDark ? colors.subtle : '#f5f5f5', borderColor: colors.border }]}>
                <Text style={[styles.nutritionTitle, { color: colors.text }]}>Nutrition (per serving)</Text>
                <View style={styles.nutritionGrid}>
                  <View style={styles.nutritionItem}>
                    <Text style={[styles.nutritionValue, { color: colors.text }]}>
                      {importedRecipe.nutritionFacts.calories}
                    </Text>
                    <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Calories</Text>
                  </View>
                  
                  <View style={styles.nutritionItem}>
                    <Text style={[styles.nutritionValue, { color: colors.text }]}>
                      {importedRecipe.nutritionFacts.protein}g
                    </Text>
                    <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Protein</Text>
                  </View>
                  
                  <View style={styles.nutritionItem}>
                    <Text style={[styles.nutritionValue, { color: colors.text }]}>
                      {importedRecipe.nutritionFacts.carbs}g
                    </Text>
                    <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Carbs</Text>
                  </View>
                  
                  <View style={styles.nutritionItem}>
                    <Text style={[styles.nutritionValue, { color: colors.text }]}>
                      {importedRecipe.nutritionFacts.fat}g
                    </Text>
                    <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>Fat</Text>
                  </View>
                </View>
              </View>
            ) : null}
            
            <View style={styles.actionsContainer}>
              <TouchableOpacity
                style={[styles.saveButton, { backgroundColor: colors.success }]}
                onPress={handleSave}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <>
                    <Feather name="check" size={18} style={styles.saveIcon} />
                    <Text style={styles.saveButtonText}>Save Recipe</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          <View style={styles.placeholderContainer}>
            <Text style={[styles.placeholderText, { color: colors.textSecondary }]}>
              Paste a URL from your favorite recipe website and click Import to add it to your collection.
            </Text>
          </View>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 60,
    paddingHorizontal: 16,
    position: 'relative',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    position: 'absolute',
    right: 16,
    padding: 8,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  inputContainer: {
    marginTop: 16,
    marginBottom: 24,
  },
  urlInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 16,
    height: 50,
  },
  urlIcon: {
    marginRight: 8,
  },
  urlInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  clearButton: {
    padding: 8,
  },
  importButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
    borderRadius: 8,
  },
  importIcon: {
    marginRight: 8,
  },
  importButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
  },
  errorIcon: {
    marginRight: 8,
  },
  errorText: {
    fontSize: 14,
  },
  previewContainer: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  previewTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 16,
  },
  recipeName: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 16,
  },
  recipeImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 16,
  },
  metadataContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  metadataItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 8,
  },
  metadataIcon: {
    marginRight: 6,
  },
  metadataText: {
    fontSize: 14,
  },
  sectionContainer: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  ingredientItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  bulletPoint: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginTop: 8,
    marginRight: 8,
  },
  ingredientText: {
    fontSize: 15,
    lineHeight: 22,
    flex: 1,
  },
  nutritionContainer: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  nutritionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
  },
  nutritionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    alignItems: 'center',
  },
  nutritionValue: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 4,
  },
  nutritionLabel: {
    fontSize: 12,
  },
  actionsContainer: {
    marginTop: 8,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
    borderRadius: 8,
  },
  saveIcon: {
    marginRight: 8,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  placeholderContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  placeholderText: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
}); 