import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Image, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { getRecommendedRecipes, Recipe } from '@/services/recipeRecommendationService';

interface RecipeRecommendationCardProps {
  title?: string;
  mealType?: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  limit?: number;
}

export default function RecipeRecommendationCard({ 
  title = 'Recommended Recipes', 
  mealType, 
  limit = 5 
}: RecipeRecommendationCardProps) {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  
  const [loading, setLoading] = useState(true);
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    loadRecipes();
  }, [mealType]);
  
  const loadRecipes = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await getRecommendedRecipes(mealType, limit);
      if (result.error) {
        setError(result.error);
        setRecipes([]);
      } else {
        setRecipes(result.recipes);
      }
    } catch (err) {
      console.error("Unexpected error fetching recipes:", err);
      setError('Could not load recommendations.');
      setRecipes([]);
    } finally {
      setLoading(false);
    }
  };
  
  const handleRecipePress = (recipe: Recipe) => {
    // For now, just log the action until we resolve the routing issue
    console.log('Navigate to recipe detail:', recipe.id);
    
    // This approach should work with proper path segments
    try {
      router.push(`/recipe/${recipe.id}` as any);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };
  
  const renderRecipeItem = ({ item }: { item: Recipe }) => (
    <TouchableOpacity 
      style={[styles.recipeCard, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}
      onPress={() => handleRecipePress(item)}
      activeOpacity={0.7}
    >
      <Image 
        source={{ uri: item.imageUrl }} 
        style={styles.recipeImage} 
        resizeMode="cover"
      />
      
      <View style={styles.recipeInfo}>
        <Text style={[styles.recipeName, { color: colors.text }]} numberOfLines={1}>
          {item.name}
        </Text>
        
        <Text style={[styles.recipeDescription, { color: colors.textSecondary }]} numberOfLines={2}>
          {item.description}
        </Text>
        
        <View style={styles.recipeMetadata}>
          <View style={styles.metadataItem}>
            <Feather name="clock" size={14} color={colors.textSecondary} style={styles.metadataIcon} />
            <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
              {item.prepTime + item.cookTime} min
            </Text>
          </View>
          
          <View style={styles.metadataItem}>
            <ChefHat size={14} color={colors.textSecondary} style={styles.metadataIcon} />
            <Text style={[styles.metadataText, { color: colors.textSecondary }]}>
              {item.difficulty}
            </Text>
          </View>
          
          <View style={styles.calorieChip}>
            <Text style={styles.calorieText}>{item.nutritionFacts.calories} cal</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
  
  if (error) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
        </View>
        <View style={styles.emptyContainer}> 
          <Text style={[styles.errorText, { color: colors.danger }]}>
            {error.includes('402') ? 'Could not load recipes (API limit reached).' : error}
          </Text>
           <TouchableOpacity onPress={loadRecipes} style={styles.retryButton}>
             <Text style={[styles.retryButtonText, { color: colors.primary }]}>Retry</Text>
           </TouchableOpacity>
        </View>
      </View>
    );
  }
  
  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Finding the perfect recipes for you...</Text>
        </View>
      </View>
    );
  }
  
  if (recipes.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
        </View>
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            No recipes found. Try adjusting your nutrition goals or dietary preferences.
          </Text>
        </View>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: isDark ? colors.card : 'white', borderColor: colors.border }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
        <TouchableOpacity 
          style={[styles.seeAllButton, { borderColor: colors.border }]}
          onPress={() => console.log('Navigate to all recipes')}
        >
          <Text style={[styles.seeAllText, { color: colors.primary }]}>See All</Text>
        </TouchableOpacity>
      </View>
      
      <FlatList
        data={recipes}
        renderItem={renderRecipeItem}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.recipeList}
        ItemSeparatorComponent={() => <View style={{ width: 16 }} />}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    borderWidth: 1,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  seeAllButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
  },
  seeAllText: {
    fontSize: 12,
    fontWeight: '500',
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 30,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 30,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
  },
  recipeList: {
    paddingRight: 16,
  },
  recipeCard: {
    width: 280,
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
  },
  recipeImage: {
    width: '100%',
    height: 150,
  },
  recipeInfo: {
    padding: 12,
  },
  recipeName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  recipeDescription: {
    fontSize: 13,
    lineHeight: 18,
    marginBottom: 12,
  },
  recipeMetadata: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metadataItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  metadataIcon: {
    marginRight: 4,
  },
  metadataText: {
    fontSize: 12,
  },
  calorieChip: {
    backgroundColor: '#10B981',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 10,
    marginLeft: 'auto',
  },
  calorieText: {
    color: 'white',
    fontSize: 11,
    fontWeight: '600',
  },
  errorText: {
    textAlign: 'center',
    fontSize: 14,
    marginBottom: 10,
  },
  retryButton: {
    marginTop: 10,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '600',
  }
}); 