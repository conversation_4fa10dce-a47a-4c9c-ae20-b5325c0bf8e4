import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView,
  Modal,
  TextInput,
  Alert,
  FlatList
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface SavedMeal {
  id: string;
  name: string;
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  foods: FoodItem[];
  isFavorite: boolean;
  dateCreated: string;
  lastUsed?: string;
}

export interface FoodItem {
  id: string;
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  servingSize: string;
  servings: number;
}

interface SavedMealsComponentProps {
  currentMealType?: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  onSelectMeal: (meal: SavedMeal) => void;
  currentFoods?: FoodItem[];
}

export function SavedMealsComponent({ 
  currentMealType, 
  onSelectMeal,
  currentFoods
}: SavedMealsComponentProps) {
  const { colors, isDark } = useTheme();
  const [savedMeals, setSavedMeals] = useState<SavedMeal[]>([]);
  const [filteredMeals, setFilteredMeals] = useState<SavedMeal[]>([]);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [newMealName, setNewMealName] = useState('');
  const [editingMeal, setEditingMeal] = useState<SavedMeal | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [mealToDelete, setMealToDelete] = useState<SavedMeal | null>(null);

  useEffect(() => {
    loadSavedMeals();
  }, []);

  useEffect(() => {
    if (currentMealType) {
      setFilteredMeals(savedMeals.filter(meal => 
        meal.type === currentMealType &&
        (searchQuery ? meal.name.toLowerCase().includes(searchQuery.toLowerCase()) : true)
      ));
    } else {
      setFilteredMeals(savedMeals.filter(meal => 
        searchQuery ? meal.name.toLowerCase().includes(searchQuery.toLowerCase()) : true
      ));
    }
  }, [savedMeals, currentMealType, searchQuery]);

  const loadSavedMeals = async () => {
    try {
      const savedMealsJson = await AsyncStorage.getItem('savedMeals');
      if (savedMealsJson) {
        setSavedMeals(JSON.parse(savedMealsJson));
      }
    } catch (error) {
      console.error('Failed to load saved meals', error);
    }
  };

  const saveMeals = async (meals: SavedMeal[]) => {
    try {
      await AsyncStorage.setItem('savedMeals', JSON.stringify(meals));
    } catch (error) {
      console.error('Failed to save meals', error);
    }
  };

  const handleSaveMeal = () => {
    if (!currentFoods || currentFoods.length === 0) {
      Alert.alert('No Foods Added', 'Please add at least one food to save as a meal');
      return;
    }

    setNewMealName('');
    setShowSaveModal(true);
  };

  const handleConfirmSave = () => {
    if (!newMealName.trim()) {
      Alert.alert('Name Required', 'Please enter a name for this meal');
      return;
    }

    if (!currentMealType || !currentFoods) {
      return;
    }

    if (editingMeal) {
      // Update existing meal
      const updatedMeals = savedMeals.map(meal => 
        meal.id === editingMeal.id 
          ? {
              ...meal,
              name: newMealName,
              foods: [...currentFoods],
              type: currentMealType,
            }
          : meal
      );
      
      setSavedMeals(updatedMeals);
      saveMeals(updatedMeals);
      setEditingMeal(null);
    } else {
      // Create new meal
      const newMeal: SavedMeal = {
        id: Date.now().toString(),
        name: newMealName,
        type: currentMealType,
        foods: [...currentFoods],
        isFavorite: false,
        dateCreated: new Date().toISOString(),
      };
      
      const updatedMeals = [...savedMeals, newMeal];
      setSavedMeals(updatedMeals);
      saveMeals(updatedMeals);
    }
    
    setShowSaveModal(false);
  };

  const handleToggleFavorite = (meal: SavedMeal) => {
    const updatedMeals = savedMeals.map(m => 
      m.id === meal.id ? { ...m, isFavorite: !m.isFavorite } : m
    );
    
    setSavedMeals(updatedMeals);
    saveMeals(updatedMeals);
  };

  const handleDeleteMeal = (meal: SavedMeal) => {
    setMealToDelete(meal);
    setShowDeleteConfirmation(true);
  };

  const confirmDeleteMeal = () => {
    if (!mealToDelete) return;
    
    const updatedMeals = savedMeals.filter(m => m.id !== mealToDelete.id);
    setSavedMeals(updatedMeals);
    saveMeals(updatedMeals);
    setShowDeleteConfirmation(false);
    setMealToDelete(null);
  };

  const handleEditMeal = (meal: SavedMeal) => {
    setEditingMeal(meal);
    setNewMealName(meal.name);
    setShowSaveModal(true);
  };

  const handleSelectMeal = (meal: SavedMeal) => {
    // Update last used timestamp
    const updatedMeals = savedMeals.map(m => 
      m.id === meal.id ? { ...m, lastUsed: new Date().toISOString() } : m
    );
    
    setSavedMeals(updatedMeals);
    saveMeals(updatedMeals);
    
    onSelectMeal(meal);
  };

  const calculateMealNutrition = (foods: FoodItem[]) => {
    return foods.reduce(
      (acc, food) => {
        const multiplier = food.servings;
        return {
          calories: acc.calories + food.calories * multiplier,
          protein: acc.protein + food.protein * multiplier,
          carbs: acc.carbs + food.carbs * multiplier,
          fat: acc.fat + food.fat * multiplier,
        };
      },
      { calories: 0, protein: 0, carbs: 0, fat: 0 }
    );
  };

  const renderMealItem = ({ item }: { item: SavedMeal }) => {
    const nutrition = calculateMealNutrition(item.foods);
    
    return (
      <TouchableOpacity
        style={[
          styles.mealCard,
          { backgroundColor: isDark ? colors.card : 'white' }
        ]}
        onPress={() => handleSelectMeal(item)}
      >
        <View style={styles.mealCardHeader}>
          <View style={styles.mealInfo}>
            <Text style={[styles.mealName, { color: colors.text }]}>
              {item.name}
            </Text>
            <Text style={[styles.mealType, { color: colors.textSecondary }]}>
              {item.type.charAt(0).toUpperCase() + item.type.slice(1)} • {item.foods.length} items
            </Text>
          </View>
          
          <View style={styles.mealActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleToggleFavorite(item)}
            >
              {item.isFavorite ? (
                <Feather name="star" size={18} color={colors.primary} />
              ) : (
                <StarOff size={18} color={colors.textSecondary} />
              )}
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleEditMeal(item)}
            >
              <Feather name="edit" size={18} color={colors.textSecondary} />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleDeleteMeal(item)}
            >
              <Feather name="trash" size={18} color={colors.danger} />
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.nutritionInfo}>
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {Math.round(nutrition.calories)}
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>cal</Text>
          </View>
          
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {nutrition.protein.toFixed(1)}g
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>protein</Text>
          </View>
          
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {nutrition.carbs.toFixed(1)}g
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>carbs</Text>
          </View>
          
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {nutrition.fat.toFixed(1)}g
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>fat</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {currentFoods && currentFoods.length > 0 && (
        <TouchableOpacity
          style={[styles.saveCurrentButton, { backgroundColor: colors.primary }]}
          onPress={handleSaveMeal}
        >
          <Feather name="save" size={16}  color={colors.text} />
          <Text style={styles.saveCurrentButtonText}>Save current meal</Text>
        </TouchableOpacity>
      )}
      
      {filteredMeals.length > 0 ? (
        <FlatList
          data={filteredMeals}
          keyExtractor={(item) => item.id}
          renderItem={renderMealItem}
          contentContainerStyle={styles.mealsList}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={styles.emptyState}>
          <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
            {searchQuery 
              ? `No saved meals found for "${searchQuery}"`
              : currentMealType 
                ? `No saved ${currentMealType} meals found`
                : 'No saved meals yet. Save your first meal!'
            }
          </Text>
        </View>
      )}
      
      {/* Save Meal Modal */}
      <Modal
        visible={showSaveModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowSaveModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: isDark ? colors.card : 'white' }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              {editingMeal ? 'Edit Saved Meal' : 'Save Meal'}
            </Text>
            
            <TextInput
              style={[
                styles.nameInput,
                { 
                  backgroundColor: isDark ? colors.subtle : '#f5f5f5',
                  color: colors.text,
                  borderColor: colors.border
                }
              ]}
              placeholder="Meal name"
              placeholderTextColor={colors.textSecondary}
              value={newMealName}
              onChangeText={setNewMealName}
              autoFocus
            />
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.subtle }]}
                onPress={() => {
                  setShowSaveModal(false);
                  setEditingMeal(null);
                }}
              >
                <Text style={[styles.modalButtonText, { color: colors.text }]}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.primary }]}
                onPress={handleConfirmSave}
              >
                <Text style={[styles.modalButtonText, { color: 'white' }]}>
                  {editingMeal ? 'Update' : 'Save'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      
      {/* Delete Confirmation Modal */}
      <Modal
        visible={showDeleteConfirmation}
        transparent
        animationType="fade"
        onRequestClose={() => setShowDeleteConfirmation(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: isDark ? colors.card : 'white' }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Delete Saved Meal
            </Text>
            
            <Text style={[styles.modalMessage, { color: colors.textSecondary }]}>
              Are you sure you want to delete "{mealToDelete?.name}"? This action cannot be undone.
            </Text>
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.subtle }]}
                onPress={() => setShowDeleteConfirmation(false)}
              >
                <Text style={[styles.modalButtonText, { color: colors.text }]}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.danger }]}
                onPress={confirmDeleteMeal}
              >
                <Text style={[styles.modalButtonText, { color: 'white' }]}>Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  saveCurrentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  saveCurrentButtonText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 8,
  },
  searchInput: {
    height: 48,
    borderRadius: 8,
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  mealsList: {
    paddingBottom: 20,
  },
  mealCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  mealCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  mealInfo: {
    flex: 1,
  },
  mealName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  mealType: {
    fontSize: 14,
  },
  mealActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 4,
    marginLeft: 8,
  },
  nutritionInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    alignItems: 'center',
  },
  nutritionValue: {
    fontSize: 15,
    fontWeight: '600',
  },
  nutritionLabel: {
    fontSize: 12,
    marginTop: 2,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    borderRadius: 12,
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  modalMessage: {
    fontSize: 16,
    marginBottom: 20,
  },
  nameInput: {
    height: 50,
    borderRadius: 8,
    paddingHorizontal: 16,
    borderWidth: 1,
    marginBottom: 20,
    fontSize: 16,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  modalButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginLeft: 12,
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SavedMealsComponent; 