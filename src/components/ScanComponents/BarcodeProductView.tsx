import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Image, 
  ScrollView, 
  TouchableOpacity,
  Linking
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { OpenFoodFactsProduct, convertToNutritionalInfo } from '@/services/food-data/open-food-facts';

// Meal type options
const MEAL_TYPES = [
  { id: 'breakfast', label: 'Breakfast', icon: 'sunny-outline' },
  { id: 'lunch', label: 'Lunch', icon: 'cafe-outline' },
  { id: 'dinner', label: 'Dinner', icon: 'moon-outline' },
  { id: 'snack', label: 'Snack', icon: 'nutrition-outline' }
];

export interface BarcodeProductViewProps {
  product: OpenFoodFactsProduct;
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  onMealTypeChange: (mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack') => void;
  onSave: () => void;
  onScanAgain: () => void;
}

export function BarcodeProductView({ 
  product, 
  mealType, 
  onMealTypeChange, 
  onSave, 
  onScanAgain 
}: BarcodeProductViewProps) {
  const { colors } = useTheme();
  
  // Convert product data to our format for easier use
  const nutritionalInfo = convertToNutritionalInfo(product);
  
  // Format categories
  const categories = product.categories?.split(',').map(c => c.trim()) || [];
  
  return (
    <ScrollView 
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
    >
      {/* Product Image */}
      <View style={styles.imageContainer}>
        {product.image_url ? (
          <Image 
            source={{ uri: product.image_url }} 
            style={styles.productImage}
            resizeMode="contain"
          />
        ) : (
          <View style={[styles.imagePlaceholder, { backgroundColor: colors.border }]}>
            <Ionicons name="image-outline" size={48} color={colors.primary} />
          </View>
        )}
      </View>
      
      {/* Product Name and Details */}
      <View style={styles.productInfo}>
        <Text style={[styles.productName, { color: colors.text }]}>
          {product.product_name || 'Unknown Product'}
        </Text>
        
        {product.brands && (
          <Text style={[styles.productBrand, { color: colors.textSecondary }]}>
            {product.brands}
          </Text>
        )}
        
        {product.quantity && (
          <Text style={[styles.productQuantity, { color: colors.textSecondary }]}>
            {product.quantity}
          </Text>
        )}
      </View>
      
      {/* Nutritional Information */}
      <View style={[styles.card, { backgroundColor: colors.card }]}>
        <View style={styles.cardHeader}>
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            Nutritional Information
          </Text>
          
          {product.serving_size && (
            <Text style={[styles.servingSize, { color: colors.textSecondary }]}>
              Serving size: {product.serving_size}
            </Text>
          )}
        </View>
        
        <View style={styles.nutritionGrid}>
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {nutritionalInfo.calories}
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
              Calories
            </Text>
          </View>
          
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {nutritionalInfo.protein}g
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
              Protein
            </Text>
          </View>
          
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {nutritionalInfo.carbs}g
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
              Carbs
            </Text>
          </View>
          
          <View style={styles.nutritionItem}>
            <Text style={[styles.nutritionValue, { color: colors.text }]}>
              {nutritionalInfo.fat}g
            </Text>
            <Text style={[styles.nutritionLabel, { color: colors.textSecondary }]}>
              Fat
            </Text>
          </View>
        </View>
        
        {/* Additional Nutritional Info */}
        {(nutritionalInfo.additionalInfo?.fiber !== undefined || 
          nutritionalInfo.additionalInfo?.sugars !== undefined || 
          nutritionalInfo.additionalInfo?.sodium !== undefined) && (
          <View style={styles.additionalNutrition}>
            {nutritionalInfo.additionalInfo?.fiber !== undefined && (
              <View style={styles.additionalItem}>
                <Text style={[styles.additionalLabel, { color: colors.textSecondary }]}>
                  Fiber:
                </Text>
                <Text style={[styles.additionalValue, { color: colors.text }]}>
                  {nutritionalInfo.additionalInfo.fiber}g
                </Text>
              </View>
            )}
            
            {nutritionalInfo.additionalInfo?.sugars !== undefined && (
              <View style={styles.additionalItem}>
                <Text style={[styles.additionalLabel, { color: colors.textSecondary }]}>
                  Sugars:
                </Text>
                <Text style={[styles.additionalValue, { color: colors.text }]}>
                  {nutritionalInfo.additionalInfo.sugars}g
                </Text>
              </View>
            )}
            
            {nutritionalInfo.additionalInfo?.sodium !== undefined && (
              <View style={styles.additionalItem}>
                <Text style={[styles.additionalLabel, { color: colors.textSecondary }]}>
                  Sodium:
                </Text>
                <Text style={[styles.additionalValue, { color: colors.text }]}>
                  {nutritionalInfo.additionalInfo.sodium}mg
                </Text>
              </View>
            )}
          </View>
        )}
      </View>
      
      {/* Ingredients */}
      {product.ingredients_text && (
        <View style={[styles.card, { backgroundColor: colors.card }]}>
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            Ingredients
          </Text>
          <Text style={[styles.ingredients, { color: colors.textSecondary }]}>
            {product.ingredients_text}
          </Text>
        </View>
      )}
      
      {/* Allergens */}
      {product.allergens_tags && product.allergens_tags.length > 0 && (
        <View style={[styles.card, { backgroundColor: colors.card }]}>
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            Allergens
          </Text>
          <View style={styles.allergensList}>
            {product.allergens_tags.map((allergen, index) => (
              <View 
                key={index} 
                style={[styles.allergenTag, { backgroundColor: `${colors.error}20` }]}
              >
                <Text style={[styles.allergenText, { color: colors.error }]}>
                  {allergen.replace('en:', '').replace(/-/g, ' ')}
                </Text>
              </View>
            ))}
          </View>
        </View>
      )}
      
      {/* Categories */}
      {categories.length > 0 && (
        <View style={[styles.card, { backgroundColor: colors.card }]}>
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            Categories
          </Text>
          <View style={styles.categoriesList}>
            {categories.map((category, index) => (
              <View 
                key={index} 
                style={[styles.categoryTag, { backgroundColor: `${colors.primary}15` }]}
              >
                <Text style={[styles.categoryText, { color: colors.primary }]}>
                  {category}
                </Text>
              </View>
            ))}
          </View>
        </View>
      )}
      
      {/* Data Source */}
      <View style={[styles.card, { backgroundColor: colors.card }]}>
        <Text style={[styles.cardTitle, { color: colors.text }]}>
          Data Source
        </Text>
        <Text style={[styles.sourceText, { color: colors.textSecondary }]}>
          Data provided by Open Food Facts
        </Text>
        <TouchableOpacity
          onPress={() => Linking.openURL(`https://world.openfoodfacts.org/product/${product.code}`)}
          style={styles.sourceLink}
        >
          <Text style={{ color: colors.primary }}>
            View on Open Food Facts
          </Text>
          <Ionicons name="open-outline" size={16} color={colors.primary} style={{ marginLeft: 4 }} />
        </TouchableOpacity>
      </View>
      
      {/* Add to Food Log */}
      <View style={[styles.card, { backgroundColor: colors.card }]}>
        <Text style={[styles.cardTitle, { color: colors.text }]}>
          Add to Food Log
        </Text>
        
        <Text style={[styles.sectionLabel, { color: colors.textSecondary }]}>
          Select meal type:
        </Text>
        
        <View style={styles.mealTypeSelector}>
          {MEAL_TYPES.map(type => (
            <TouchableOpacity
              key={type.id}
              style={[
                styles.mealTypeOption,
                mealType === type.id && { 
                  backgroundColor: colors.primary + '20',
                  borderColor: colors.primary 
                },
                { borderColor: colors.border }
              ]}
              onPress={() => onMealTypeChange(type.id as any)}
            >
              <Ionicons 
                name={type.icon as any} 
                size={20} 
                color={mealType === type.id ? colors.primary : colors.text} 
              />
              <Text 
                style={[
                  styles.mealTypeOptionText, 
                  { color: mealType === type.id ? colors.primary : colors.text }
                ]}
              >
                {type.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity 
          style={[styles.scanAgainButton, { borderColor: colors.border }]}
          onPress={onScanAgain}
        >
          <Ionicons name="scan-outline" size={20} color={colors.text} />
          <Text style={[styles.buttonText, { color: colors.text }]}>
            Scan Again
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.saveButton, { backgroundColor: colors.primary }]}
          onPress={onSave}
        >
          <Ionicons name="save-outline" size={20} color="white" />
          <Text style={[styles.buttonText, { color: 'white' }]}>
            Save to Log
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 30,
  },
  imageContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  productImage: {
    width: 200,
    height: 200,
    borderRadius: 8,
  },
  imagePlaceholder: {
    width: 200,
    height: 200,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  productInfo: {
    marginBottom: 20,
  },
  productName: {
    fontSize: 22,
    fontWeight: '700',
    marginBottom: 4,
  },
  productBrand: {
    fontSize: 16,
    marginBottom: 4,
  },
  productQuantity: {
    fontSize: 14,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  cardHeader: {
    marginBottom: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  servingSize: {
    fontSize: 14,
  },
  nutritionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nutritionItem: {
    alignItems: 'center',
  },
  nutritionValue: {
    fontSize: 18,
    fontWeight: '700',
  },
  nutritionLabel: {
    fontSize: 14,
    marginTop: 4,
  },
  additionalNutrition: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
  },
  additionalItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  additionalLabel: {
    fontSize: 14,
  },
  additionalValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  ingredients: {
    fontSize: 14,
    lineHeight: 20,
  },
  allergensList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  allergenTag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  allergenText: {
    fontSize: 14,
    fontWeight: '500',
  },
  categoriesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  categoryTag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  categoryText: {
    fontSize: 14,
  },
  sourceText: {
    fontSize: 14,
    marginBottom: 8,
  },
  sourceLink: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionLabel: {
    fontSize: 14,
    marginBottom: 12,
  },
  mealTypeSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  mealTypeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
  },
  mealTypeOptionText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  scanAgainButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    flex: 1,
    marginRight: 8,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    flex: 1,
    marginLeft: 8,
  },
  buttonText: {
    fontSize: 15,
    fontWeight: '600',
    marginLeft: 8,
  },
}); 