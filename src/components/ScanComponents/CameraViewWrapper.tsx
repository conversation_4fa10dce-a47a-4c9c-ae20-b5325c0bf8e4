import React, { useState, useRef, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, Alert, Dimensions, StatusBar, Switch } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { CameraView, Camera as ExpoCamera, CameraType } from 'expo-camera';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import { useTheme } from '@/contexts/ThemeContext';
import * as Haptics from 'expo-haptics';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { BlurView } from 'expo-blur';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { processFrameForFoodDetection, resetFrameProcessor, RealTimeFoodDetectionResult } from '@/services/vision/realTimeVisionProcessor';
import { RealTimeFoodDetectionOverlay } from './RealTimeFoodDetectionOverlay';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

type CameraDirection = 'front' | 'back';

interface CameraViewWrapperProps {
  cameraType: CameraDirection;
  scanMode: 'food' | 'barcode';
  multiItemMode: boolean;
  onCapture: (uri: string) => void;
  onBarCodeScanned?: (data: { type: string; data: string }) => void;
  onClose: () => void;
  onToggleCameraType: () => void;
  onToggleScanMode: () => void;
}

// Camera permissions hook that uses expo-camera
export function useCameraPermissions() {
  const [permission, setPermission] = React.useState<boolean | null>(null);
  
  const requestPermission = async () => {
    try {
      const { status } = await ExpoCamera.requestCameraPermissionsAsync();
      const granted = status === 'granted';
      setPermission(granted);
      return { granted };
    } catch (error) {
      console.error('Error requesting camera permission:', error);
      return { granted: false };
    }
  };
  
  return [{ granted: permission }, requestPermission];
}

export function CameraViewWrapper({
  cameraType,
  scanMode,
  multiItemMode,
  onCapture,
  onBarCodeScanned,
  onClose,
  onToggleCameraType,
  onToggleScanMode
}: CameraViewWrapperProps) {
  const { colors, isDark } = useTheme();
  const [isProcessing, setIsProcessing] = useState(false);
  const [hasCameraPermission, setHasCameraPermission] = useState<boolean | null>(null);
  const cameraRef = useRef<any>(null); // Use 'any' to avoid TypeScript errors with the camera reference
  const [isCameraReady, setIsCameraReady] = useState(false);
  const [flashMode, setFlashMode] = useState<'off' | 'on' | 'auto'>('off');
  const [recentlyTook, setRecentlyTook] = useState(false);
  const insets = useSafeAreaInsets();
  
  // Real-time detection state
  const [isRealTimeDetectionEnabled, setIsRealTimeDetectionEnabled] = useState(false);
  const [detectionResult, setDetectionResult] = useState<RealTimeFoodDetectionResult | null>(null);
  const frameProcessorActive = useRef(false);
  
  // Add these state variables for capture progress near the top of the component
  const [captureProgress, setCaptureProgress] = useState(0);
  const [processingMessage, setProcessingMessage] = useState('');
  
  // Show status bar when unmounting
  useEffect(() => {
    StatusBar.setHidden(true);
    return () => {
      StatusBar.setHidden(false);
    };
  }, []);
  
  // Request camera permissions when component mounts
  useEffect(() => {
    // Disabling automatic camera permission request on mount
    // Previously, this automatically requested camera permissions which would open the permission prompt
    console.log('Camera component mounted - not automatically requesting permissions');
    
    // (async () => {
    //   console.log('Requesting camera permissions...');
    //   const { status } = await ExpoCamera.requestCameraPermissionsAsync();
    //   console.log('Camera permission status:', status);
    //   setHasCameraPermission(status === 'granted');
    //   
    //   if (status !== 'granted') {
    //     Alert.alert(
    //       "Camera Permission Required",
    //       "We need camera access to scan food items. Please enable camera permissions in your device settings."
    //     );
    //   }
    // })();
  }, []);
  
  // Clean up frame processor when component unmounts
  useEffect(() => {
    return () => {
      console.log('Cleaning up camera resources');
      resetFrameProcessor();
      frameProcessorActive.current = false;
    };
  }, []);
  
  // Effect to handle real-time detection
  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    
    if (isRealTimeDetectionEnabled && scanMode === 'food' && isCameraReady && cameraRef.current) {
      frameProcessorActive.current = true;
      
      // Start capturing frames
      intervalId = setInterval(async () => {
        if (!frameProcessorActive.current || !cameraRef.current) return;
        
        try {
          // Capture frame
          const frame = await cameraRef.current.takePictureAsync({
            quality: 0.5,
            skipProcessing: true,
            exif: false,
          });
          
          // Process frame
          const result = await processFrameForFoodDetection(frame.uri);
          setDetectionResult(result);
        } catch (error) {
          console.warn('Error in frame capture:', error);
        }
      }, 2000); // Process every 2 seconds
    } else {
      frameProcessorActive.current = false;
    }
    
    return () => {
      clearInterval(intervalId);
    };
  }, [isRealTimeDetectionEnabled, scanMode, isCameraReady]);
  
  // Toggle real-time detection mode
  const toggleRealTimeDetection = useCallback(() => {
    if (scanMode !== 'food') {
      // Only allow real-time detection in food scanning mode
      setIsRealTimeDetectionEnabled(false);
      return;
    }
    
    // Toggle state
    setIsRealTimeDetectionEnabled(prevState => {
      const newState = !prevState;
      
      // Reset detection results when turning off
      if (!newState) {
        setDetectionResult(null);
      }
      
      // Provide haptic feedback
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      
      return newState;
    });
  }, [scanMode]);
  
  // Toggle flash mode
  const toggleFlash = () => {
    setFlashMode(prev => {
      if (prev === 'off') return 'on';
      if (prev === 'on') return 'auto';
      return 'off';
    });
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };
  
  // Add improved camera initialization with more safety checks
  useEffect(() => {
    // Additional check to ensure camera is properly initialized
    const checkCameraRef = () => {
      if (!cameraRef.current) {
        console.log('Camera not initialized yet, retrying...');
        // Try to recreate the camera after a short delay
        setTimeout(() => {
          setIsCameraReady(false);
          // Force a refresh using a different approach
          toggleFlash(); // Toggle flash as an alternative way to refresh camera
          setTimeout(() => toggleFlash(), 100); // Toggle back
        }, 500);
      } else {
        console.log('Camera reference exists');
      }
    };
    
    // Check camera after component mounts
    setTimeout(checkCameraRef, 1000);
    
    return () => {
      // Clear any remaining timeouts
    };
  }, []);
  
  // Update the takePicture function to first request permissions explicitly
  const takePicture = async () => {
    // Track if we've already sent the error to prevent duplicate alerts
    let errorAlreadySent = false;

    // First, explicitly request camera permissions if needed
    if (hasCameraPermission === null || hasCameraPermission === false) {
      try {
        const { status } = await ExpoCamera.requestCameraPermissionsAsync();
        setHasCameraPermission(status === 'granted');
        
        if (status !== 'granted') {
          Alert.alert('Camera Permission Required', 
            'We need camera access to take photos. Please enable camera permissions.');
          return;
        }
        
        // Wait a moment for camera to initialize after permissions granted
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.error('Error requesting camera permission:', error);
        Alert.alert('Permission Error', 'Could not request camera permissions');
        return;
      }
    }

    // Immediate safety check for camera ref
    if (!cameraRef.current) {
      console.log('Camera ref is null - calling onClose to exit camera view');
      onClose();
      return;
    }

    // First, check if the camera is ready and has a valid reference
    if (!isCameraReady) {
      console.log('Camera not ready - setting timeout to retry');
      
      // Wait briefly and check again
      setTimeout(() => {
        if (cameraRef.current && isCameraReady) {
          console.log('Camera became ready, trying again');
          takePicture();
        } else {
          console.log('Camera still not ready after delay, exiting');
          if (!errorAlreadySent) {
            errorAlreadySent = true;
            Alert.alert('Camera Error', 'Could not access camera. Please try using the gallery instead.');
            onClose();
          }
        }
      }, 500);
      return;
    }
    
    try {
      setIsProcessing(true);
      setCaptureProgress(0); // Reset progress
      setProcessingMessage('Preparing camera...');
      
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      setRecentlyTook(true);
      
      // Temporarily disable frame processor
      frameProcessorActive.current = false;
      
      // Add a safety delay before taking picture
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Show initial progress
      setCaptureProgress(10);
      setProcessingMessage('Initializing camera...');
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Final safety check before taking picture
      if (!cameraRef.current) {
        throw new Error('Camera reference lost during capture process');
      }
      
      setCaptureProgress(20);
      setProcessingMessage('Capturing photo...');
      
      console.log('Attempting to take picture with camera...');
      
      // Add security timeout - if takePictureAsync takes too long, we'll move on
      let photoPromise;
      let timeoutPromise;
      
      try {
        const timeoutMs = 5000; // 5 seconds timeout
        photoPromise = cameraRef.current.takePictureAsync({
          quality: 0.8,
          skipProcessing: false,
        });
        
        timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Camera operation timed out')), timeoutMs);
        });
        
        // Race the photo promise against the timeout
        const photo = await Promise.race([photoPromise, timeoutPromise]);
        
        console.log('Picture taken successfully, URI:', photo.uri);
        
        // Update progress
        setCaptureProgress(50);
        setProcessingMessage('Optimizing image...');
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Process the captured image
        const manipulatedImage = await manipulateAsync(
          photo.uri,
          [{ resize: { width: 1080 } }],
          { format: SaveFormat.JPEG, compress: 0.8 }
        );
        
        // Update progress
        setCaptureProgress(80);
        setProcessingMessage('Finalizing...');
        
        // Short delay for visual feedback
        setTimeout(() => {
          setCaptureProgress(100);
          setProcessingMessage('Complete!');
          
          setTimeout(() => {
            setRecentlyTook(false);
            const uri = manipulatedImage.uri;
            onCapture(uri);
          }, 300);
        }, 300);
      } catch (innerError: any) {
        console.error('takePictureAsync inner error:', innerError);
        throw new Error(`Failed to capture photo: ${innerError.message}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : '';
      console.error('Error taking picture:', errorMessage);
      console.error('Error stack:', errorStack);
      
      setIsProcessing(false);
      setRecentlyTook(false);
      setCaptureProgress(0);
      
      // Avoid triggering the error more than once
      if (!errorAlreadySent) {
        errorAlreadySent = true;
        Alert.alert('Camera Error', `Could not take photo. Please try using the gallery instead.`);
        
        // Close the camera view on error
        onClose();
      }
    } finally {
      // Re-enable frame processor if real-time detection is on
      if (isRealTimeDetectionEnabled) {
        frameProcessorActive.current = true;
      }
    }
  };
  
  const handleCameraReady = useCallback(() => {
    console.log('Camera is ready, ref exists:', !!cameraRef.current);
    // Add a small delay before setting camera as ready to avoid race conditions
    setTimeout(() => {
      setIsCameraReady(true);
    }, 300);
  }, []);
  
  // Handle barcode scanning if in barcode mode
  const handleBarcodeScanned = (scanData: { type: string; data: string }) => {
    if (scanMode === 'barcode' && onBarCodeScanned) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      onBarCodeScanned(scanData);
    }
  };
  
  // Handle scan mode toggle
  const handleToggleScanMode = () => {
    // Disable real-time detection when switching to barcode mode
    if (scanMode === 'food') {
      setIsRealTimeDetectionEnabled(false);
      setDetectionResult(null);
    }
    
    onToggleScanMode();
  };
  
  if (hasCameraPermission === null) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={styles.processingText}>Camera permissions needed</Text>
        <TouchableOpacity 
          style={styles.fallbackButton}
          onPress={async () => {
            try {
              const { status } = await ExpoCamera.requestCameraPermissionsAsync();
              setHasCameraPermission(status === 'granted');
            } catch (error) {
              console.error('Error requesting permissions:', error);
              setHasCameraPermission(false);
            }
          }}
        >
          <Text style={styles.fallbackButtonText}>Allow Camera Access</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  if (hasCameraPermission === false) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <BlurView intensity={80} style={StyleSheet.absoluteFill} tint={isDark ? 'dark' : 'light'} />
        <Ionicons name="camera-outline" size={50} color="white" style={{ marginBottom: 20 }} />
        <Text style={[styles.processingText, { marginBottom: 20 }]}>Camera Access Required</Text>
        <TouchableOpacity 
          style={styles.fallbackButton}
          onPress={() => ExpoCamera.requestCameraPermissionsAsync()}
        >
          <Text style={styles.fallbackButtonText}>Grant Permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (isProcessing) {
    return (
      <View style={styles.container}>
        <BlurView intensity={70} style={StyleSheet.absoluteFill} tint="dark" />
        <View style={styles.processingContainer}>
          <Animated.View entering={FadeIn} exiting={FadeOut}>
            {recentlyTook ? (
              <>
                <Ionicons name="scan-outline" size={60} color="white" />
                <Text style={styles.processingText}>
                  {processingMessage || 'Processing Image...'}
                </Text>
                
                {/* Progress bar */}
                <View style={styles.progressBarContainer}>
                  <View style={[styles.progressBar, { width: `${captureProgress}%` }]} />
                </View>
                <Text style={styles.progressText}>{captureProgress}%</Text>
              </>
            ) : (
              <>
                <Ionicons name="scan-outline" size={60} color="white" />
                <Text style={styles.processingText}>
                  Processing...
                </Text>
              </>
            )}
          </Animated.View>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar hidden />
      
      {/* Only render the camera if it's ready and has permission */}
      {isCameraReady && hasCameraPermission === true ? (
        <CameraView
          ref={cameraRef}
          style={styles.camera}
          facing={cameraType}
          flash={flashMode}
          onCameraReady={handleCameraReady}
          onBarcodeScanned={scanMode === 'barcode' ? handleBarcodeScanned : undefined}
          barcodeScannerSettings={{
            barcodeTypes: scanMode === 'barcode' ? 
              ['ean13', 'ean8', 'upc_e', 'upc_a', 'code128', 'code39', 'code93', 'codabar', 'itf14', 'qr'] : [],
          }}
        />
      ) : (
        // Show a placeholder for the camera
        <View style={[styles.camera, { backgroundColor: 'black' }]} />
      )}
      
      {/* Real-time food detection overlay */}
      <RealTimeFoodDetectionOverlay 
        detectionResult={detectionResult}
        isEnabled={isRealTimeDetectionEnabled && scanMode === 'food'}
      />
      
      {/* Scan guide overlay - MOVED HIGHER */}
      <View style={styles.guideContainer}>
        <View style={[
          styles.guideBox,
          scanMode === 'barcode' && styles.barcodeGuideBox
        ]} />
      </View>
      
      {/* Scan mode indicator - SEPARATE FROM TOP CONTROLS */}
      <View style={styles.scanModeContainer}>
        <View style={styles.modeIndicator}>
          <Text style={styles.modeText}>
            {scanMode === 'food' 
              ? (multiItemMode ? 'Multi-Food Scan' : 'Food Scan') 
              : 'Barcode Scan'}
          </Text>
        </View>
      </View>
      
      {/* Top header controls - ONLY CLOSE AND FLASH BUTTONS */}
      <View style={styles.topControls}>
        <TouchableOpacity 
          style={styles.iconButton} 
          onPress={onClose}
          activeOpacity={0.7}
        >
          <Ionicons name="close" size={28} color="white" />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.iconButton} 
          onPress={toggleFlash}
          activeOpacity={0.7}
        >
          <Ionicons 
            name={
              flashMode === 'off' ? 'flash-off' : 
              flashMode === 'on' ? 'flash' : 'flash-outline'
            }
            size={28} 
            color="white" 
          />
        </TouchableOpacity>
      </View>
      
      {/* Real-time detection toggle (only for food mode) */}
      {scanMode === 'food' && (
        <View style={styles.realTimeToggleContainer}>
          <Text style={styles.realTimeToggleText}>Real-time detection</Text>
          <Switch
            value={isRealTimeDetectionEnabled}
            onValueChange={toggleRealTimeDetection}
            trackColor={{ false: '#767577', true: '#4caf50' }}
            thumbColor={isRealTimeDetectionEnabled ? '#f4f3f4' : '#f4f3f4'}
          />
        </View>
      )}
      
      {/* CENTERED PROMINENT CONTROLS - TAKE PHOTO MOVED DOWN */}
      <View style={styles.centeredControlsContainer}>
        <View style={styles.centeredControlsRow}>
          <TouchableOpacity 
            style={styles.largeControlButton}
            onPress={handleToggleScanMode}
            activeOpacity={0.7}
          >
            <Ionicons 
              name={scanMode === 'food' ? 'barcode-outline' : 'fast-food-outline'} 
              size={40} 
              color="#FFFFFF" 
            />
            <Text style={styles.largeControlText}>
              {scanMode === 'food' ? 'Barcode' : 'Food'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.largeCaptureButton}
            onPress={takePicture}
            disabled={!isCameraReady}
            activeOpacity={0.7}
          >
            <View style={styles.largeCaptureOuter}>
              <View style={styles.largeCaptureInner} />
            </View>
            <Text style={styles.largeCaptureText}>TAKE PHOTO</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.largeControlButton}
            onPress={onToggleCameraType}
            activeOpacity={0.7}
          >
            <Ionicons name="camera-reverse-outline" size={40} color="#FFFFFF" />
            <Text style={styles.largeControlText}>
              Flip
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  camera: {
    ...StyleSheet.absoluteFillObject,
  },
  processingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  processingText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    marginTop: 10,
    textAlign: 'center',
  },
  fallbackButton: {
    backgroundColor: '#4caf50',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 30,
    marginTop: 20,
  },
  fallbackButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  
  // Guide overlay styles - MOVED HIGHER
  guideContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    // Move guide higher but not too high
    transform: [{ translateY: -SCREEN_HEIGHT * 0.12 }],
  },
  guideBox: {
    width: SCREEN_WIDTH * 0.75,
    height: SCREEN_WIDTH * 0.75,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 20,
    borderStyle: 'dashed',
  },
  barcodeGuideBox: {
    width: SCREEN_WIDTH * 0.8,
    height: 120,
    borderColor: 'rgba(76, 175, 80, 0.8)',
    borderRadius: 12,
    borderStyle: 'solid',
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
  },
  
  // Separate scan mode container MOVED HIGHER ABOVE SQUARE
  scanModeContainer: {
    position: 'absolute',
    top: SCREEN_HEIGHT * 0.06, // Move up significantly 
    left: 0,
    right: 0,
    alignItems: 'center',
    paddingVertical: 10,
  },
  
  // Top controls styles - MOVED HIGHER
  topControls: {
    position: 'absolute',
    top: SCREEN_HEIGHT * 0.03, // Move up significantly
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  
  // Icon button style
  iconButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 8,
  },
  
  // Mode indicator styles
  modeIndicator: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  modeText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  
  // MOVED CONTROLS DOWN SLIGHTLY
  centeredControlsContainer: {
    position: 'absolute',
    // Move controls down a bit
    bottom: SCREEN_HEIGHT * 0.25, // Adjusted from 0.30 to 0.25
    left: 0,
    right: 0,
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingVertical: 15,
    borderTopWidth: 3,
    borderBottomWidth: 3,
    borderColor: '#FF9800',
  },
  centeredControlsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 10,
  },
  largeControlButton: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 15,
    padding: 12,
    borderWidth: 2,
    borderColor: '#FFFFFF',
    minWidth: 100,
  },
  largeControlText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 8,
    textAlign: 'center',
  },
  largeCaptureButton: {
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 15,
  },
  largeCaptureOuter: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 4,
    borderColor: '#FF9800',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  largeCaptureInner: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#FF9800',
  },
  largeCaptureText: {
    color: '#FF9800',
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 8,
    textAlign: 'center',
  },
  
  // Add new styles for real-time detection mode
  realTimeToggleContainer: {
    position: 'absolute',
    top: SCREEN_HEIGHT * 0.13, // Position below scan mode indicator
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 8,
    borderRadius: 20,
  },
  realTimeToggleText: {
    color: 'white',
    marginRight: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  
  // Add these styles for progress bar
  progressBarContainer: {
    width: '80%',
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    marginTop: 20,
    marginBottom: 10,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
    backgroundColor: '#4caf50',
  },
  progressText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 5,
  },
}); 