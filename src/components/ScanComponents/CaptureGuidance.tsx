import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Animated , useColorScheme } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '@/constants/Colors';

interface CaptureGuidanceProps {
  isVisible: boolean;
  lightLevel?: 'low' | 'good' | 'high' | null;
  hasReferenceObject?: boolean;
  isDistanceOptimal?: boolean;
  isAngleOptimal?: boolean;
}

/**
 * Component that provides real-time feedback to users during food photo capture
 * Offers advice on lighting, distance, angle, and inclusion of reference objects
 */
export function CaptureGuidance({
  isVisible,
  lightLevel = null,
  hasReferenceObject = false,
  isDistanceOptimal = false,
  isAngleOptimal = false
}: CaptureGuidanceProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme || 'light'];
  
  const [opacity] = useState(new Animated.Value(0));
  
  // Animate visibility changes
  useEffect(() => {
    Animated.timing(opacity, {
      toValue: isVisible ? 1 : 0,
      duration: 200,
      useNativeDriver: true
    }).start();
  }, [isVisible]);
  
  // Generate tips based on current conditions
  const getTips = () => {
    const tips: { icon: string; text: string; status: 'good' | 'warning' | 'error' }[] = [];
    
    // Lighting tip
    if (lightLevel === 'low') {
      tips.push({
        icon: 'sunny-outline',
        text: 'Move to a brighter area for better recognition',
        status: 'warning'
      });
    } else if (lightLevel === 'high') {
      tips.push({
        icon: 'contrast-outline',
        text: 'Too much glare, try adjusting angle or reducing light',
        status: 'warning'
      });
    } else if (lightLevel === 'good') {
      tips.push({
        icon: 'sunny',
        text: 'Good lighting',
        status: 'good'
      });
    }
    
    // Reference object tip
    tips.push({
      icon: hasReferenceObject ? 'card' : 'card-outline',
      text: hasReferenceObject 
        ? 'Reference object detected' 
        : 'Include a reference item like a credit card for better size estimation',
      status: hasReferenceObject ? 'good' : 'warning'
    });
    
    // Distance tip
    tips.push({
      icon: isDistanceOptimal ? 'resize' : 'resize-outline',
      text: isDistanceOptimal 
        ? 'Good distance from food' 
        : 'Position 8-12 inches away from food for best results',
      status: isDistanceOptimal ? 'good' : 'warning'
    });
    
    // Angle tip
    tips.push({
      icon: isAngleOptimal ? 'camera' : 'camera-outline',
      text: isAngleOptimal 
        ? 'Good camera angle' 
        : 'Try shooting from directly above for more accurate analysis',
      status: isAngleOptimal ? 'good' : 'warning'
    });
    
    return tips;
  };
  
  const getStatusColor = (status: 'good' | 'warning' | 'error') => {
    switch (status) {
      case 'good': return '#4CAF50';
      case 'warning': return '#FFC107';
      case 'error': return '#F44336';
      default: return colors.text;
    }
  };
  
  const tips = getTips();
  
  return (
    <Animated.View style={[
      styles.container,
      { 
        opacity,
        backgroundColor: colorScheme === 'dark' 
          ? 'rgba(0,0,0,0.7)' 
          : 'rgba(255,255,255,0.8)' 
      }
    ]}>
      <Text style={[styles.heading, { color: colors.text }]}>
        Capture Tips
      </Text>
      
      {tips.map((tip, index) => (
        <View key={index} style={styles.tipRow}>
          <Ionicons 
            name={tip.icon as any} 
            size={24} 
            color={getStatusColor(tip.status)} 
          />
          <Text style={[styles.tipText, { color: colors.text }]}>
            {tip.text}
          </Text>
        </View>
      ))}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    padding: 15,
    borderRadius: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
  },
  heading: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center'
  },
  tipRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
  },
  tipText: {
    marginLeft: 10,
    fontSize: 14,
    flex: 1
  }
}); 