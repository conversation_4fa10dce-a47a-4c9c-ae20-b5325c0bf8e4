import React, { useState, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, ScrollView, Dimensions, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import * as Haptics from 'expo-haptics';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface FoodItem {
  id: string;
  name: string;
  calories: number;
  protein?: number;
  carbs?: number;
  fat?: number;
  boundingBox?: {
    topLeft: { x: number, y: number },
    bottomRight: { x: number, y: number }
  };
  maskUri?: string;
  confidence?: number;
}

interface MultiItemFoodDisplayProps {
  imageUri: string;
  foodItems: FoodItem[];
  onItemSelect: (item: FoodItem) => void;
  onCombineItems: (items: FoodItem[]) => void;
}

export function MultiItemFoodDisplay({
  imageUri,
  foodItems,
  onItemSelect,
  onCombineItems
}: MultiItemFoodDisplayProps) {
  const { colors, isDark } = useTheme();
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [showCombineOption, setShowCombineOption] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Toggle food item selection
  const toggleItemSelection = (itemId: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    setSelectedItems(prev => {
      const newSelectedItems = prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId];
      
      // Show combine option if multiple items are selected
      const shouldShowCombine = newSelectedItems.length > 1;
      if (shouldShowCombine !== showCombineOption) {
        setShowCombineOption(shouldShowCombine);
        
        // Animate the combine option
        Animated.timing(fadeAnim, {
          toValue: shouldShowCombine ? 1 : 0,
          duration: 300,
          useNativeDriver: true
        }).start();
      }
      
      return newSelectedItems;
    });
  };

  // Handle combining selected items
  const handleCombineItems = () => {
    if (selectedItems.length < 2) return;
    
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    // Get the selected food items
    const itemsToCombine = foodItems.filter(item => selectedItems.includes(item.id));
    
    // Call the combine callback
    onCombineItems(itemsToCombine);
    
    // Clear selected items
    setSelectedItems([]);
    setShowCombineOption(false);
  };

  return (
    <View style={styles.container}>
      <Text style={[styles.title, { color: colors.text }]}>
        Multiple Food Items Detected
      </Text>
      
      <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
        {foodItems.length} items found. Tap to view details or select multiple items to combine.
      </Text>
      
      {/* Food Image with Bounding Boxes */}
      <View style={styles.imageContainer}>
        <Image source={{ uri: imageUri }} style={styles.foodImage} />
        
        {/* Overlay bounding boxes */}
        {foodItems.map((item, index) => {
          if (!item.boundingBox) return null;
          
          const { topLeft, bottomRight } = item.boundingBox;
          const isSelected = selectedItems.includes(item.id);
          
          return (
            <TouchableOpacity
              key={`box-${item.id}`}
              style={[
                styles.boundingBox,
                {
                  left: topLeft.x * SCREEN_WIDTH * 0.9, // Adjust for container width
                  top: topLeft.y * SCREEN_WIDTH * 0.9, // Maintain aspect ratio
                  width: (bottomRight.x - topLeft.x) * SCREEN_WIDTH * 0.9,
                  height: (bottomRight.y - topLeft.y) * SCREEN_WIDTH * 0.9,
                  borderColor: isSelected ? '#3B82F6' : 'rgba(255, 255, 255, 0.8)',
                  backgroundColor: isSelected ? 'rgba(59, 130, 246, 0.3)' : 'transparent'
                }
              ]}
              onPress={() => toggleItemSelection(item.id)}
              activeOpacity={0.8}
            >
              <View style={[
                styles.itemNumberBadge,
                { backgroundColor: isSelected ? '#3B82F6' : 'rgba(0, 0, 0, 0.7)' }
              ]}>
                <Text style={styles.itemNumberText}>{index + 1}</Text>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
      
      {/* Food Items List */}
      <ScrollView style={styles.itemListContainer} horizontal showsHorizontalScrollIndicator={false}>
        {foodItems.map((item, index) => {
          const isSelected = selectedItems.includes(item.id);
          
          return (
            <TouchableOpacity
              key={`item-${item.id}`}
              style={[
                styles.foodItemCard,
                { 
                  backgroundColor: isSelected 
                    ? isDark ? 'rgba(59, 130, 246, 0.3)' : '#DBEAFE' 
                    : isDark ? colors.card : colors.background,
                  borderColor: isSelected ? '#3B82F6' : colors.border
                }
              ]}
              onPress={() => toggleItemSelection(item.id)}
              onLongPress={() => onItemSelect(item)}
            >
              <View style={styles.itemNumberContainer}>
                <View style={[
                  styles.itemNumberCircle,
                  { backgroundColor: isSelected ? '#3B82F6' : colors.primary }
                ]}>
                  <Text style={styles.itemNumber}>{index + 1}</Text>
                </View>
              </View>
              
              <View style={styles.foodItemInfo}>
                <Text style={[styles.foodItemName, { color: colors.text }]} numberOfLines={1}>
                  {item.name}
                </Text>
                
                <Text style={[styles.foodItemCalories, { color: colors.textSecondary }]}>
                  {item.calories} cal
                </Text>
                
                {item.protein && item.carbs && item.fat && (
                  <View style={styles.macroContainer}>
                    <Text style={[styles.macroText, { color: colors.textSecondary }]}>
                      P: {Math.round(item.protein)}g
                    </Text>
                    <Text style={[styles.macroText, { color: colors.textSecondary }]}>
                      C: {Math.round(item.carbs)}g
                    </Text>
                    <Text style={[styles.macroText, { color: colors.textSecondary }]}>
                      F: {Math.round(item.fat)}g
                    </Text>
                  </View>
                )}
                
                <TouchableOpacity
                  style={[styles.viewDetailsButton, { backgroundColor: isSelected ? '#3B82F6' : colors.primary }]}
                  onPress={() => onItemSelect(item)}
                >
                  <Text style={styles.viewDetailsText}>View Details</Text>
                </TouchableOpacity>
              </View>
              
              <TouchableOpacity
                style={[
                  styles.selectButton,
                  { backgroundColor: isSelected ? '#3B82F6' : 'rgba(0, 0, 0, 0.1)' }
                ]}
                onPress={() => toggleItemSelection(item.id)}
              >
                <Ionicons
                  name={isSelected ? 'checkmark-circle' : 'ellipse-outline'}
                  size={24}
                  color={isSelected ? 'white' : isDark ? 'white' : 'black'}
                />
              </TouchableOpacity>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
      
      {/* Combine Selected Items Button */}
      <Animated.View 
        style={[
          styles.combineContainer,
          {
            opacity: fadeAnim,
            transform: [{
              translateY: fadeAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [20, 0]
              })
            }],
            pointerEvents: showCombineOption ? 'auto' : 'none'
          }
        ]}
      >
        <TouchableOpacity
          style={[styles.combineButton, { backgroundColor: colors.primary }]}
          onPress={handleCombineItems}
          disabled={selectedItems.length < 2}
        >
          <Ionicons name="git-merge-outline" size={22} color="white" style={styles.combineIcon} />
          <Text style={styles.combineText}>
            Combine {selectedItems.length} Items
          </Text>
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    padding: 16,
    borderRadius: 12,
    marginVertical: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    marginBottom: 16,
  },
  imageContainer: {
    position: 'relative',
    width: '90%',
    aspectRatio: 1,
    alignSelf: 'center',
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  foodImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  boundingBox: {
    position: 'absolute',
    borderWidth: 2,
    borderStyle: 'dashed',
  },
  itemNumberBadge: {
    position: 'absolute',
    top: 4,
    left: 4,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemNumberText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  itemListContainer: {
    flexDirection: 'row',
    marginTop: 16,
  },
  foodItemCard: {
    width: 200,
    marginRight: 12,
    borderRadius: 12,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
  },
  itemNumberContainer: {
    marginRight: 12,
  },
  itemNumberCircle: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemNumber: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  foodItemInfo: {
    flex: 1,
  },
  foodItemName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  foodItemCalories: {
    fontSize: 14,
    marginBottom: 4,
  },
  macroContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  macroText: {
    fontSize: 12,
    marginRight: 8,
  },
  viewDetailsButton: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  viewDetailsText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  selectButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  combineContainer: {
    marginTop: 20,
    alignItems: 'center',
  },
  combineButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 25,
    elevation: 2,
  },
  combineIcon: {
    marginRight: 8,
  },
  combineText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
}); 