import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, ScrollView } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { DetectedReferenceObject } from '@/services/vision/referenceObjectDetection';

interface PortionSizeEstimationViewProps {
  imageUri: string;
  foodItems: {
    id: string;
    name: string;
    boundingBox?: {
      topLeft: { x: number, y: number };
      bottomRight: { x: number, y: number };
    };
    estimatedSize?: {
      width: number;
      height: number;
      depth: number;
      volume: number;
      referenceObject: {
        name: string;
        width: number;
        height: number;
      };
    } | null;
  }[];
  referenceObjects: DetectedReferenceObject[];
  imageWidth: number;
  imageHeight: number;
}

export function PortionSizeEstimationView({
  imageUri,
  foodItems,
  referenceObjects,
  imageWidth,
  imageHeight
}: PortionSizeEstimationViewProps) {
  const { colors, isDark } = useTheme();
  const [showDetails, setShowDetails] = useState(false);
  
  // Filter items that have size estimation
  const itemsWithSize = foodItems.filter(item => item.estimatedSize !== null && item.estimatedSize !== undefined);
  
  // Check if we have reference objects and size estimations
  const hasReferenceObjects = referenceObjects.length > 0;
  const hasSizeEstimations = itemsWithSize.length > 0;
  
  if (!hasReferenceObjects) {
    return (
      <View style={[styles.container, { backgroundColor: colors.card }]}>
        <Text style={[styles.title, { color: colors.text }]}>Portion Size Estimation</Text>
        <View style={styles.noReferenceContainer}>
          <Ionicons name="resize-outline" size={48} color={colors.primary} style={styles.icon} />
          <Text style={[styles.noReferenceText, { color: colors.textSecondary }]}>
            No reference objects detected in the image. Include common objects like a credit card, 
            coin, or smartphone in your photo for better portion size estimation.
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <Text style={[styles.title, { color: colors.text }]}>Portion Size Estimation</Text>
      
      {/* Image with bounding boxes */}
      <View style={styles.imageContainer}>
        <Image source={{ uri: imageUri }} style={styles.image} />
        
        {/* Overlay reference objects */}
        {referenceObjects.map((obj, index) => {
          const left = obj.boundingBox.topLeft.x * 100;
          const top = obj.boundingBox.topLeft.y * 100;
          const width = (obj.boundingBox.bottomRight.x - obj.boundingBox.topLeft.x) * 100;
          const height = (obj.boundingBox.bottomRight.y - obj.boundingBox.topLeft.y) * 100;
          
          return (
            <View
              key={`ref-${index}`}
              style={[
                styles.boundingBox,
                {
                  left: `${left}%`,
                  top: `${top}%`,
                  width: `${width}%`,
                  height: `${height}%`,
                  borderColor: '#4caf50'
                }
              ]}
            >
              <View style={[styles.refLabel, { backgroundColor: '#4caf50' }]}>
                <Text style={styles.refLabelText}>{obj.name}</Text>
              </View>
            </View>
          );
        })}
        
        {/* Overlay food items with size estimation */}
        {itemsWithSize.map((item, index) => {
          if (!item.boundingBox) return null;
          
          const left = item.boundingBox.topLeft.x * 100;
          const top = item.boundingBox.topLeft.y * 100;
          const width = (item.boundingBox.bottomRight.x - item.boundingBox.topLeft.x) * 100;
          const height = (item.boundingBox.bottomRight.y - item.boundingBox.topLeft.y) * 100;
          
          return (
            <View
              key={`food-${index}`}
              style={[
                styles.boundingBox,
                {
                  left: `${left}%`,
                  top: `${top}%`,
                  width: `${width}%`,
                  height: `${height}%`,
                  borderColor: '#f57c00'
                }
              ]}
            >
              <View style={[styles.foodLabel, { backgroundColor: '#f57c00' }]}>
                <Text style={styles.foodLabelText}>{item.name}</Text>
              </View>
            </View>
          );
        })}
      </View>
      
      {/* Reference objects list */}
      <View style={styles.sectionContainer}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Detected Reference Objects</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.referencesScroll}>
          {referenceObjects.map((obj, index) => (
            <View key={`ref-item-${index}`} style={[styles.referenceItem, { backgroundColor: isDark ? colors.card : colors.subtle }]}>
              <Text style={[styles.referenceName, { color: colors.text }]}>{obj.name}</Text>
              <Text style={[styles.referenceSize, { color: colors.textSecondary }]}>
                {obj.width.toFixed(1)} × {obj.height.toFixed(1)} mm
              </Text>
            </View>
          ))}
        </ScrollView>
      </View>
      
      {/* Estimated size information */}
      {hasSizeEstimations && (
        <View style={styles.sectionContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Estimated Portion Sizes</Text>
          <View style={styles.sizesContainer}>
            {itemsWithSize.map((item, index) => {
              if (!item.estimatedSize) return null;
              
              const volume = item.estimatedSize.volume;
              // Convert volume to appropriate units and rounded format
              let volumeDisplay = '';
              if (volume < 1) {
                volumeDisplay = `${(volume * 1000).toFixed(0)} ml`;
              } else if (volume < 10) {
                volumeDisplay = `${volume.toFixed(1)} cm³`;
              } else {
                volumeDisplay = `${Math.round(volume)} cm³`;
              }
              
              // Estimate weight (using average density of 1g/cm³)
              const weight = volume;
              let weightDisplay = '';
              if (weight < 1) {
                weightDisplay = `${(weight * 1000).toFixed(0)} g`;
              } else if (weight < 10) {
                weightDisplay = `${weight.toFixed(1)} g`;
              } else {
                weightDisplay = `${Math.round(weight)} g`;
              }
              
              return (
                <View key={`size-${index}`} style={[styles.sizeItem, { backgroundColor: isDark ? colors.card : colors.subtle }]}>
                  <View style={styles.sizeItemHeader}>
                    <Text style={[styles.sizeItemName, { color: colors.text }]}>{item.name}</Text>
                    <View style={[styles.sizeTag, { backgroundColor: colors.primary }]}>
                      <Text style={styles.sizeTagText}>Estimated</Text>
                    </View>
                  </View>
                  
                  <View style={styles.sizeDetails}>
                    <View style={styles.sizeRow}>
                      <Text style={[styles.sizeLabel, { color: colors.textSecondary }]}>Volume:</Text>
                      <Text style={[styles.sizeValue, { color: colors.text }]}>{volumeDisplay}</Text>
                    </View>
                    <View style={styles.sizeRow}>
                      <Text style={[styles.sizeLabel, { color: colors.textSecondary }]}>Est. Weight:</Text>
                      <Text style={[styles.sizeValue, { color: colors.text }]}>{weightDisplay}</Text>
                    </View>
                    
                    {showDetails && (
                      <>
                        <View style={styles.sizeRow}>
                          <Text style={[styles.sizeLabel, { color: colors.textSecondary }]}>Dimensions:</Text>
                          <Text style={[styles.sizeValue, { color: colors.text }]}>
                            {Math.round(item.estimatedSize.width)} × {Math.round(item.estimatedSize.height)} × {Math.round(item.estimatedSize.depth)} mm
                          </Text>
                        </View>
                        <View style={styles.sizeRow}>
                          <Text style={[styles.sizeLabel, { color: colors.textSecondary }]}>Reference:</Text>
                          <Text style={[styles.sizeValue, { color: colors.text }]}>
                            {item.estimatedSize.referenceObject.name}
                          </Text>
                        </View>
                      </>
                    )}
                  </View>
                </View>
              );
            })}
          </View>
          
          <TouchableOpacity 
            style={[styles.detailsButton, { backgroundColor: colors.subtle }]}
            onPress={() => setShowDetails(!showDetails)}
          >
            <Text style={[styles.detailsButtonText, { color: colors.primary }]}>
              {showDetails ? 'Hide' : 'Show'} Calculation Details
            </Text>
            <Ionicons name={showDetails ? 'chevron-up' : 'chevron-down'} size={16} color={colors.primary} />
          </TouchableOpacity>
        </View>
      )}
      
      {/* Accuracy note */}
      <View style={styles.accuracyNote}>
        <Ionicons name="information-circle-outline" size={16} color={colors.textSecondary} style={styles.infoIcon} />
        <Text style={[styles.accuracyText, { color: colors.textSecondary }]}>
          Portion sizes are estimates based on detected reference objects. 
          Results may vary based on camera angle and object placement.
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 12,
    borderRadius: 12,
    overflow: 'hidden',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    padding: 16,
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    aspectRatio: 1,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  boundingBox: {
    position: 'absolute',
    borderWidth: 2,
    borderStyle: 'dashed',
  },
  refLabel: {
    position: 'absolute',
    top: -18,
    left: 0,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  refLabelText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  foodLabel: {
    position: 'absolute',
    bottom: -18,
    right: 0,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  foodLabelText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  sectionContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  referencesScroll: {
    flexDirection: 'row',
  },
  referenceItem: {
    padding: 10,
    marginRight: 8,
    borderRadius: 8,
    minWidth: 120,
  },
  referenceName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  referenceSize: {
    fontSize: 12,
  },
  sizesContainer: {
    gap: 8,
  },
  sizeItem: {
    padding: 12,
    borderRadius: 8,
  },
  sizeItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  sizeItemName: {
    fontSize: 16,
    fontWeight: '600',
  },
  sizeTag: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  sizeTagText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  sizeDetails: {
    gap: 4,
  },
  sizeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  sizeLabel: {
    fontSize: 14,
  },
  sizeValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  detailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 8,
    marginTop: 8,
  },
  detailsButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  accuracyNote: {
    flexDirection: 'row',
    padding: 16,
    paddingTop: 0,
  },
  infoIcon: {
    marginRight: 4,
    marginTop: 2,
  },
  accuracyText: {
    fontSize: 12,
    flex: 1,
  },
  noReferenceContainer: {
    padding: 16,
    alignItems: 'center',
  },
  icon: {
    marginBottom: 16,
  },
  noReferenceText: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
}); 