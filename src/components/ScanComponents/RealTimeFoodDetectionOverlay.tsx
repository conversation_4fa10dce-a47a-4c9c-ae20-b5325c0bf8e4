import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { RealTimeFoodDetectionResult } from '@/services/vision/realTimeVisionProcessor';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface RealTimeFoodDetectionOverlayProps {
  detectionResult: RealTimeFoodDetectionResult | null;
  isEnabled: boolean;
}

/**
 * Overlay component to display real-time food detection results
 * Shows bounding boxes and labels for detected food items
 */
export function RealTimeFoodDetectionOverlay({ 
  detectionResult, 
  isEnabled 
}: RealTimeFoodDetectionOverlayProps) {
  if (!isEnabled || !detectionResult || !detectionResult.success || detectionResult.foodItems.length === 0) {
    return null;
  }

  return (
    <View style={styles.overlayContainer}>
      {detectionResult.foodItems.map((item, index) => {
        // If bounding box is available, position the label accordingly
        if (item.boundingBox) {
          const { topLeft, bottomRight } = item.boundingBox;
          
          // Convert normalized coordinates to screen pixels
          const left = topLeft.x * SCREEN_WIDTH;
          const top = topLeft.y * SCREEN_HEIGHT;
          const width = (bottomRight.x - topLeft.x) * SCREEN_WIDTH;
          const height = (bottomRight.y - topLeft.y) * SCREEN_HEIGHT;
          
          const confidence = Math.round(item.score * 100);
          
          return (
            <React.Fragment key={`item-${index}`}>
              {/* Bounding box */}
              <Animated.View 
                entering={FadeIn.duration(300)}
                exiting={FadeOut.duration(200)}
                style={[
                  styles.boundingBox,
                  {
                    left,
                    top,
                    width,
                    height,
                    borderColor: getConfidenceColor(item.score)
                  }
                ]}
              />
              
              {/* Label */}
              <Animated.View 
                entering={FadeIn.duration(300)}
                exiting={FadeOut.duration(200)}
                style={[
                  styles.labelContainer,
                  {
                    left,
                    top: top - 30, // Position label above the bounding box
                    backgroundColor: getConfidenceColor(item.score, 0.8)
                  }
                ]}
              >
                <Text style={styles.labelText}>
                  {item.name} ({confidence}%)
                </Text>
              </Animated.View>
            </React.Fragment>
          );
        } else {
          // If no bounding box, show label at the top
          return (
            <Animated.View 
              key={`item-${index}`}
              entering={FadeIn.duration(300)}
              exiting={FadeOut.duration(200)}
              style={[
                styles.centeredLabelContainer,
                {
                  top: 80 + (index * 40),
                  backgroundColor: getConfidenceColor(item.score, 0.8)
                }
              ]}
            >
              <Text style={styles.labelText}>
                {item.name} ({Math.round(item.score * 100)}%)
              </Text>
            </Animated.View>
          );
        }
      })}
      
      {/* Status indicator */}
      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>
          {detectionResult.foodItems.length} items detected
        </Text>
      </View>
    </View>
  );
}

// Helper to get color based on confidence score
function getConfidenceColor(score: number, alpha: number = 0.5): string {
  if (score >= 0.8) {
    return `rgba(76, 175, 80, ${alpha})`; // Green for high confidence
  } else if (score >= 0.6) {
    return `rgba(255, 152, 0, ${alpha})`; // Orange for medium confidence  
  } else {
    return `rgba(244, 67, 54, ${alpha})`; // Red for low confidence
  }
}

const styles = StyleSheet.create({
  overlayContainer: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 10,
    pointerEvents: 'none', // Allow touches to pass through to camera controls
  },
  boundingBox: {
    position: 'absolute',
    borderWidth: 2,
    borderStyle: 'dashed',
    backgroundColor: 'transparent',
  },
  labelContainer: {
    position: 'absolute',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    elevation: 3,
  },
  centeredLabelContainer: {
    position: 'absolute',
    alignSelf: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    elevation: 3,
  },
  labelText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  statusContainer: {
    position: 'absolute',
    bottom: 160,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 20,
    alignItems: 'center',
  },
  statusText: {
    color: 'white',
    fontSize: 12,
  },
}); 