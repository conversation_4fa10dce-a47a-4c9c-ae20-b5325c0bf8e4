import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { RecentScan } from '@/types/scan.types';
import { SafeView, SafeText } from '@/components/ui';
import { Feather } from '@expo/vector-icons';

interface RecentScansProps {
  scans: RecentScan[];
  onSelectScan: (uri: string) => void;
  onClose?: () => void;
}

export function RecentScans({ scans, onSelectScan, onClose }: RecentScansProps) {
  const { colors, isDark } = useTheme();

  if (scans.length === 0) {
    return null;
  }

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const isToday = 
      date.getDate() === now.getDate() &&
      date.getMonth() === now.getMonth() &&
      date.getFullYear() === now.getFullYear();

    if (isToday) {
      return `Today, ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    }

    return date.toLocaleDateString([], { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit', 
      minute: '2-digit'
    });
  };

  return (
    <SafeView style={styles.recentScansContainer}>
      <View style={styles.headerContainer}>
        <SafeText style={[styles.recentScansTitle, { color: colors.text }]}>
          Recent Scans
        </SafeText>
        {onClose && (
          <TouchableOpacity 
            style={[styles.closeButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.1)' }]} 
            onPress={onClose}
            accessibilityLabel="Close recent scans"
          >
            <Feather name="x" size={16} color={isDark ? '#fff' : '#000'} />
          </TouchableOpacity>
        )}
      </View>
      <ScrollView
        style={styles.recentScansScroll}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingRight: 20, paddingBottom: 20 }}
      >
        {scans.map(scan => (
          <TouchableOpacity 
            key={scan.id} 
            style={[styles.recentScanItem, { backgroundColor: colors.card }]}
            onPress={() => onSelectScan(scan.imageUri)}
          >
            <Image 
              source={{ uri: scan.imageUri }} 
              style={styles.recentScanImage} 
              resizeMode="cover"
            />
            <SafeView style={[styles.recentScanInfo, { backgroundColor: colors.card }]}>
              <SafeText 
                style={[styles.recentScanName, { color: colors.text }]}
                numberOfLines={1}
              >
                {scan.foodName}
              </SafeText>
              <SafeText style={[styles.recentScanCalories, { color: colors.primary }]}>
                {scan.calories} calories
              </SafeText>
              <SafeText style={[styles.recentScanTime, { color: colors.textSecondary }]}>
                {formatTimestamp(scan.timestamp)}
              </SafeText>
            </SafeView>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </SafeView>
  );
}

const styles = StyleSheet.create({
  recentScansContainer: {
    marginTop: 16,
    marginBottom: 24,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 12,
  },
  recentScansTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  closeButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recentScansScroll: {
    paddingLeft: 20,
  },
  recentScanItem: {
    marginRight: 16,
    borderRadius: 12,
    overflow: 'hidden',
    width: 130,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  recentScanImage: {
    width: 130,
    height: 130,
    borderRadius: 12,
  },
  recentScanInfo: {
    padding: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.03)',
  },
  recentScanName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  recentScanCalories: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  recentScanTime: {
    fontSize: 10,
  }
}); 