import React from 'react';
import { StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import * as MediaLibrary from 'expo-media-library';
import { SafeView, SafeText } from '@/components/ui';

interface SaveImageButtonProps {
  imageUri: string | null;
  position?: 'bottomRight' | 'bottomLeft' | 'custom';
  customStyle?: any;
}

export function SaveImageButton({ 
  imageUri, 
  position = 'bottomRight',
  customStyle 
}: SaveImageButtonProps) {
  const { isDark } = useTheme();

  const saveImageToGallery = async (uri: string) => {
    try {
      // Request permission to access media library
      const { status } = await MediaLibrary.requestPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Unable to save image without media library permission');
        return;
      }
      
      // Create asset from the image URI
      const asset = await MediaLibrary.createAssetAsync(uri);
      
      // Create an album for the app if it doesn't exist
      let album = await MediaLibrary.getAlbumAsync('Health App Foods');
      
      if (album === null) {
        // Create new album for the app
        await MediaLibrary.createAlbumAsync('Health App Foods', asset, false);
      } else {
        // Add to existing album
        await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
      }
      
      Alert.alert('Success', 'Image saved to your photo library');
    } catch (error) {
      console.error('Error saving image to gallery:', error);
      Alert.alert('Error', 'Failed to save image to your photo library');
    }
  };

  if (!imageUri) return null;

  // Determine position styling
  const positionStyle = position === 'bottomRight' 
    ? styles.bottomRight 
    : position === 'bottomLeft' 
      ? styles.bottomLeft 
      : {};

  return (
    <SafeView style={[
      styles.saveButton,
      positionStyle,
      { backgroundColor: isDark ? 'rgba(16, 185, 129, 0.8)' : '#10B981' },
      customStyle
    ]}>
      <TouchableOpacity
        style={styles.saveButtonContent}
        onPress={() => saveImageToGallery(imageUri)}
      >
        <Ionicons name="save-outline" size={20} color="white" />
        <SafeText style={styles.saveButtonText}>Save</SafeText>
      </TouchableOpacity>
    </SafeView>
  );
}

const styles = StyleSheet.create({
  saveButton: {
    position: 'absolute',
    borderRadius: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  bottomRight: {
    right: 20,
    bottom: 40,
  },
  bottomLeft: {
    left: 20,
    bottom: 40,
  },
  saveButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
    marginLeft: 6,
  },
}); 