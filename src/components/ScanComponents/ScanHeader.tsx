import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Platform } from 'react-native';

import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { router } from 'expo-router';

interface ScanHeaderProps {
  isPremium: boolean;
  headerAnim: Animated.Value;
  pulseAnim: Animated.Value;
  rotateAnim: Animated.Value;
  onScanOptions: () => void;
  onOpenGallery: () => void;
  onGoBack: () => void;
  onBarcodeMode: () => void;
}

export function ScanHeader({
  isPremium,
  headerAnim,
  pulseAnim,
  rotateAnim,
  onScanOptions,
  onOpenGallery,
  onGoBack,
  onBarcodeMode
}: ScanHeaderProps) {
  const { colors, isDark } = useTheme();

  const handleHistoryPress = () => {
    // Navigate to history page directly - fixed path
    router.push("/(tabs)/food-history");
  };

  return (
    <Animated.View style={[
      styles.customHeaderContainer,
      {
        opacity: headerAnim,
        transform: [
          {
            translateY: headerAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [-20, 0]
            })
          }
        ]
      }
    ]}>
      <LinearGradient
        colors={isDark ? 
          ['#0F172A', '#1E293B'] : 
          ['#EFF6FF', '#DBEAFE']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.headerGradient}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerTitleContainer}>
            <Animated.View style={{
              transform: [
                {
                  scale: pulseAnim
                }
              ]
            }}>
              <Scan 
                size={28} 
                color={isDark ? '#3B82F6' : '#2563EB'} 
                style={styles.headerIcon} 
              />
            </Animated.View>
            
            <View>
              <Text style={[styles.headerTitle, { color: colors.text }]}>
                AI Food Scanner
              </Text>
              <Text style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
                {isPremium ? 'Premium Access' : 'Analyze your meals'}
              </Text>
            </View>
          </View>
          
          {isPremium && (
            <Animated.View style={{
              transform: [
                {
                  rotate: rotateAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0deg', '360deg']
                  })
                }
              ]
            }}>
              <View style={styles.premiumBadge}>
                <Sparkles size={16} color="#F59E0B" />
              </View>
            </Animated.View>
          )}
        </View>
        
        {/* Quick action buttons */}
        <View style={styles.quickActions}>
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : '#DBEAFE' }]}
            onPress={onScanOptions}
          >
            <ScanLine size={20} color={isDark ? '#3B82F6' : '#2563EB'} />
            <Text style={[styles.actionText, { color: isDark ? '#3B82F6' : '#2563EB' }]}>Scan Options</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: isDark ? 'rgba(139, 92, 246, 0.1)' : '#EDE9FE' }]}
            onPress={() => {
              console.log('Gallery button clicked in header');
              onOpenGallery();
            }}
          >
            <FileImage size={20} color={isDark ? '#8B5CF6' : '#7C3AED'} />
            <Text style={[styles.actionText, { color: isDark ? '#8B5CF6' : '#7C3AED' }]}>Gallery</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: isDark ? 'rgba(16, 185, 129, 0.1)' : '#ECFDF5' }]}
            onPress={handleHistoryPress}
          >
            <HistoryIcon size={20} color={isDark ? '#10B981' : '#059669'} />
            <Text style={[styles.actionText, { color: isDark ? '#10B981' : '#059669' }]}>History</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: isDark ? 'rgba(244, 63, 94, 0.1)' : '#FEE2E2' }]}
            onPress={onBarcodeMode}
            activeOpacity={0.7}
          >
            <Ionicons name="barcode-outline" size={20} color={isDark ? '#F43F5E' : '#E11D48'} />
            <Text style={[styles.actionText, { color: isDark ? '#F43F5E' : '#E11D48' }]}>Scan Barcode</Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  customHeaderContainer: {
    width: '100%',
    overflow: 'hidden',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 10,
      },
      android: {
        elevation: 6,
      },
      web: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 10,
      }
    }),
  },
  headerGradient: {
    paddingTop: 16,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: '700',
  },
  headerSubtitle: {
    fontSize: 13,
    fontWeight: '500',
  },
  premiumBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(245, 158, 11, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(245, 158, 11, 0.3)',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    paddingHorizontal: 8,
    marginBottom: 8,
  },
  actionButton: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderRadius: 12,
    width: '23%', // Adjust width to fit 4 buttons
  },
  actionText: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 6,
    textAlign: 'center',
  },
}); 