import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, Animated } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';

interface ScanLoadingProps {
  scanMode?: 'food' | 'barcode';
  message?: string;
  progress?: number; // Value between 0-100 to show progress
  steps?: string[]; // Optional array of steps to show
  showPercentage?: boolean; // Whether to display the percentage
}

export function ScanLoading({
  scanMode = 'food',
  message,
  progress = 0,
  steps = [],
  showPercentage = true
}: ScanLoadingProps) {
  const { colors, isDark } = useTheme();
  const [animatedProgress] = useState(new Animated.Value(0));
  const [currentStep, setCurrentStep] = useState(0);
  const [showCompleteMessage, setShowCompleteMessage] = useState(false);

  // Default steps for food scanning
  const defaultFoodSteps = [
    'Uploading image...',
    'Analyzing food items...',
    'Identifying ingredients...',
    'Calculating nutrition data...',
    'Finalizing analysis...'
  ];

  // Default steps for barcode scanning
  const defaultBarcodeSteps = [
    'Reading barcode...',
    'Searching product database...',
    'Retrieving product information...',
    'Fetching nutrition data...'
  ];

  // Use provided steps or default based on scan mode
  const stepsToUse = steps.length > 0 
    ? steps 
    : scanMode === 'barcode' 
      ? defaultBarcodeSteps 
      : defaultFoodSteps;
  
  // Update animated value when progress changes
  useEffect(() => {
    Animated.timing(animatedProgress, {
      toValue: progress / 100,
      duration: 300,
      useNativeDriver: false
    }).start();
    
    // Update current step based on progress
    const stepIndex = Math.min(
      Math.floor((progress / 100) * stepsToUse.length),
      stepsToUse.length - 1
    );
    setCurrentStep(stepIndex);

    // If progress is 100%, show complete message
    if (progress >= 100) {
      console.log('Progress at 100%, showing complete message');
      setShowCompleteMessage(true);
    } else {
      setShowCompleteMessage(false);
    }
  }, [progress, stepsToUse.length]);

  const defaultMessage = scanMode === 'barcode' 
    ? 'Looking up product information...' 
    : 'Analyzing your food...';

  // Display complete message if at 100%
  const displayMessage = showCompleteMessage 
    ? 'Analysis complete!' 
    : (message || defaultMessage);

  return (
    <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
      <ActivityIndicator size="large" color={colors.primary} />
      
      <Text style={[styles.loadingText, { color: colors.text }]}>
        {displayMessage}
      </Text>
      
      {/* Progress bar */}
      <View style={[styles.progressBarContainer, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)' }]}>
        <Animated.View 
          style={[
            styles.progressBar, 
            { 
              backgroundColor: showCompleteMessage ? '#4CAF50' : colors.primary,
              width: animatedProgress.interpolate({
                inputRange: [0, 1],
                outputRange: ['0%', '100%']
              }) 
            }
          ]} 
        />
      </View>
      
      {/* Percentage text */}
      {showPercentage && (
        <Text style={[styles.percentageText, { color: showCompleteMessage ? '#4CAF50' : colors.textSecondary }]}>
          {showCompleteMessage ? 'Done!' : `${Math.round(progress)}%`}
        </Text>
      )}
      
      {/* Current step */}
      <Text style={[styles.stepText, { color: colors.textSecondary }]}>
        {showCompleteMessage ? 'Displaying results...' : stepsToUse[currentStep]}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    marginBottom: 24,
    fontSize: 18,
    fontWeight: '600',
  },
  progressBarContainer: {
    width: '80%',
    height: 12,
    borderRadius: 6,
    overflow: 'hidden',
    marginVertical: 8,
  },
  progressBar: {
    height: '100%',
    borderRadius: 6,
  },
  percentageText: {
    fontSize: 16,
    fontWeight: '600',
    marginVertical: 8,
  },
  stepText: {
    fontSize: 14,
    marginTop: 8,
    fontStyle: 'italic',
  }
}); 