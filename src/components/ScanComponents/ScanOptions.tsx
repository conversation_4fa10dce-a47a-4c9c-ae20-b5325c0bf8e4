import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Switch } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useNavigation } from '@react-navigation/native';
import { SafeView, SafeText } from '@/components/ui';

interface ScanOptionsProps {
  onOpenCamera: () => void;
  onOpenGallery: () => void;
  suggestedMeal?: string;
  autoDetectMultiple?: boolean;
  onToggleAutoDetect?: () => void;
}

export function ScanOptionsView({
  onOpenCamera,
  onOpenGallery,
  suggestedMeal = 'a meal',
  autoDetectMultiple = true,
  onToggleAutoDetect
}: ScanOptionsProps) {
  const { colors, isDark } = useTheme();
  const navigation = useNavigation();

  const handleBarcodeNavigation = () => {
    // @ts-ignore - Navigate to the barcode scanner screen
    navigation.navigate('barcode-scanner');
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <SafeView style={styles.headerContainer}>
        <SafeText style={[styles.headerTitle, { color: colors.text }]}>
          How would you like to scan your food?
        </SafeText>
        <SafeText style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
          Take a photo of {suggestedMeal} or choose an existing image
        </SafeText>
      </SafeView>

      <SafeView style={styles.optionsContainer}>
        <TouchableOpacity
          style={[styles.optionButton, { backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : '#DBEAFE' }]}
          onPress={onOpenCamera}
        >
          <SafeView style={styles.optionContent}>
            <SafeView style={[styles.optionIconContainer, { backgroundColor: isDark ? 'rgba(59, 130, 246, 0.2)' : '#93C5FD' }]}>
              <Ionicons name="camera-outline" size={32} color={isDark ? '#3B82F6' : '#2563EB'} />
            </SafeView>
            <SafeView style={styles.optionTextContainer}>
              <SafeText style={[styles.optionTitle, { color: colors.text }]}>Take a Photo</SafeText>
              <SafeText style={[styles.optionDescription, { color: colors.textSecondary }]}>
                Use your camera to scan food
              </SafeText>
            </SafeView>
          </SafeView>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.optionButton, { backgroundColor: isDark ? 'rgba(59, 130, 246, 0.1)' : '#DBEAFE', marginTop: 12 }]}
          onPress={() => {
            console.log('Gallery option pressed in ScanOptions');
            onOpenGallery();
          }}
        >
          <SafeView style={styles.optionContent}>
            <SafeView style={[styles.optionIconContainer, { backgroundColor: isDark ? 'rgba(59, 130, 246, 0.2)' : '#93C5FD' }]}>
              <Ionicons name="image-outline" size={32} color={isDark ? '#3B82F6' : '#2563EB'} />
            </SafeView>
            <SafeView style={styles.optionTextContainer}>
              <SafeText style={[styles.optionTitle, { color: colors.text }]}>Gallery</SafeText>
              <SafeText style={[styles.optionDescription, { color: colors.textSecondary }]}>
                Select a photo from your gallery
              </SafeText>
            </SafeView>
          </SafeView>
        </TouchableOpacity>
        
        {/* Barcode Scanner Option */}
        <TouchableOpacity
          style={[styles.optionButton, { backgroundColor: isDark ? 'rgba(16, 185, 129, 0.1)' : '#D1FAE5', marginTop: 12 }]}
          onPress={handleBarcodeNavigation}
        >
          <SafeView style={styles.optionContent}>
            <SafeView style={[styles.optionIconContainer, { backgroundColor: isDark ? 'rgba(16, 185, 129, 0.2)' : '#6EE7B7' }]}>
              <Ionicons name="barcode-outline" size={32} color={isDark ? '#10B981' : '#059669'} />
            </SafeView>
            <SafeView style={styles.optionTextContainer}>
              <SafeText style={[styles.optionTitle, { color: colors.text }]}>Barcode Scanner</SafeText>
              <SafeText style={[styles.optionDescription, { color: colors.textSecondary }]}>
                Scan packaged food barcodes
              </SafeText>
            </SafeView>
          </SafeView>
        </TouchableOpacity>
        
        {/* Auto-detect multiple items toggle */}
        {onToggleAutoDetect && (
          <SafeView style={[styles.settingOption, { backgroundColor: isDark ? 'rgba(59, 130, 246, 0.05)' : '#F0F7FF', marginTop: 24 }]}>
            <SafeView style={styles.settingTextContainer}>
              <SafeText style={[styles.settingTitle, { color: colors.text }]}>
                Auto-detect multiple items
              </SafeText>
              <SafeText style={[styles.settingDescription, { color: colors.textSecondary }]}>
                Automatically detect and analyze multiple food items in one photo
              </SafeText>
            </SafeView>
            <Switch
              value={autoDetectMultiple}
              onValueChange={onToggleAutoDetect}
              trackColor={{ false: '#767577', true: isDark ? '#2563EB' : '#3B82F6' }}
              thumbColor={autoDetectMultiple ? '#FFFFFF' : '#f4f3f4'}
              ios_backgroundColor="#3e3e3e"
            />
          </SafeView>
        )}
      </SafeView>

      <SafeView style={styles.tipsContainer}>
        <SafeText style={[styles.tipsTitle, { color: colors.text }]}>Tips for better results:</SafeText>
        <SafeView style={styles.tipItem}>
          <Ionicons name="checkmark-circle" size={16} color={isDark ? '#10B981' : '#059669'} style={styles.tipIcon} />
          <SafeText style={[styles.tipText, { color: colors.textSecondary }]}>
            Make sure the food is clearly visible
          </SafeText>
        </SafeView>
        <SafeView style={styles.tipItem}>
          <Ionicons name="checkmark-circle" size={16} color={isDark ? '#10B981' : '#059669'} style={styles.tipIcon} />
          <SafeText style={[styles.tipText, { color: colors.textSecondary }]}>
            Good lighting helps with accurate recognition
          </SafeText>
        </SafeView>
        <SafeView style={styles.tipItem}>
          <Ionicons name="checkmark-circle" size={16} color={isDark ? '#10B981' : '#059669'} style={styles.tipIcon} />
          <SafeText style={[styles.tipText, { color: colors.textSecondary }]}>
            Include the entire meal in the frame
          </SafeText>
        </SafeView>
      </SafeView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 24,
  },
  headerContainer: {
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: '700',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    lineHeight: 22,
  },
  optionsContainer: {
    marginBottom: 32,
  },
  optionButton: {
    borderRadius: 16,
    padding: 16,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  optionTextContainer: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  // New styles for settings
  settingOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 16,
    padding: 16,
  },
  settingTextContainer: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 13,
    lineHeight: 18,
  },
  tipsContainer: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    borderStyle: 'dashed',
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tipIcon: {
    marginRight: 8,
  },
  tipText: {
    fontSize: 14,
    lineHeight: 20,
  },
}); 