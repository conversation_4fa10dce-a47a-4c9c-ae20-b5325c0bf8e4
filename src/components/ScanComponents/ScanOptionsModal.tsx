import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Pressable } from 'react-native';

import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';

interface ScanOptionsModalProps {
  onClose: () => void;
  onSelectSingleFood: () => void;
  onSelectMultiFood: () => void;
  onSelectBarcode: () => void;
}

export function ScanOptionsModal({
  onClose,
  onSelectSingleFood,
  onSelectMultiFood,
  onSelectBarcode
}: ScanOptionsModalProps) {
  const { colors, isDark } = useTheme();

  return (
    <TouchableOpacity 
      style={styles.modalOverlay}
      activeOpacity={1}
      onPress={onClose}
    >
      <BlurView 
        intensity={60} 
        tint={isDark ? "dark" : "light"} 
        style={styles.blurViewContainer}
      >
        <Pressable 
          style={[styles.modalContent, { backgroundColor: colors.card }]} 
          onPress={(e) => e.stopPropagation()}
        >
          <Text style={[styles.modalTitle, { color: colors.text }]}>Scan Options</Text>
          
          <TouchableOpacity 
            style={[styles.modalOption, { borderBottomColor: colors.border, borderBottomWidth: 1 }]}
            onPress={onSelectSingleFood}
          >
            <View style={[styles.modalOptionIcon, { backgroundColor: '#3B82F6' }]}>
              <Scan size={20} color="#fff" />
            </View>
            <View style={styles.modalOptionText}>
              <Text style={[styles.modalOptionTitle, { color: colors.text }]}>Single Food Scan</Text>
              <Text style={[styles.modalOptionDesc, { color: colors.textSecondary }]}>
                Analyze one meal or food item
              </Text>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.modalOption, { borderBottomColor: colors.border, borderBottomWidth: 1 }]}
            onPress={onSelectMultiFood}
          >
            <View style={[styles.modalOptionIcon, { backgroundColor: '#8B5CF6' }]}>
              <ScanLine size={20} color="#fff" />
            </View>
            <View style={styles.modalOptionText}>
              <Text style={[styles.modalOptionTitle, { color: colors.text }]}>Multi-Food Scan</Text>
              <Text style={[styles.modalOptionDesc, { color: colors.textSecondary }]}>
                Detect multiple food items in one image
              </Text>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.modalOption}
            onPress={onSelectBarcode}
          >
            <View style={[styles.modalOptionIcon, { backgroundColor: '#10B981' }]}>
              <Ionicons name="barcode-outline" size={20} color="#fff" />
            </View>
            <View style={styles.modalOptionText}>
              <Text style={[styles.modalOptionTitle, { color: colors.text }]}>Barcode Scan</Text>
              <Text style={[styles.modalOptionDesc, { color: colors.textSecondary }]}>
                Scan packaged food barcodes
              </Text>
            </View>
          </TouchableOpacity>
        </Pressable>
      </BlurView>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  blurViewContainer: {
    width: '85%',
    borderRadius: 16,
    overflow: 'hidden',
  },
  modalContent: {
    width: '100%',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 20,
    textAlign: 'center',
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
  },
  modalOptionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  modalOptionText: {
    flex: 1,
    marginLeft: 12,
    marginRight: 8,
  },
  modalOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  modalOptionDesc: {
    fontSize: 13,
  },
}); 