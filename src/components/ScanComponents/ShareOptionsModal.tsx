import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Modal, 
  TouchableOpacity, 
  Switch,
  ScrollView
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { FoodSharingPrivacyOptions, defaultPrivacyOptions } from '@/services/sharing/foodSharingService';

interface ShareOptionsModalProps {
  visible: boolean;
  onClose: () => void;
  onShareAsText: (options: FoodSharingPrivacyOptions) => void;
  onShareAsImage: (options: FoodSharingPrivacyOptions) => void;
}

export function ShareOptionsModal({
  visible,
  onClose,
  onShareAsText,
  onShareAsImage
}: ShareOptionsModalProps) {
  const { colors, isDark } = useTheme();
  const [privacyOptions, setPrivacyOptions] = useState<FoodSharingPrivacyOptions>({...defaultPrivacyOptions});
  
  const handleToggleOption = (option: keyof FoodSharingPrivacyOptions) => {
    setPrivacyOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };
  
  const resetOptions = () => {
    setPrivacyOptions({...defaultPrivacyOptions});
  };
  
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <BlurView
        intensity={90}
        tint={isDark ? 'dark' : 'light'}
        style={styles.overlay}
      >
        <View style={[styles.container, { backgroundColor: colors.card }]}>
          <View style={styles.header}>
            <Text style={[styles.title, { color: colors.text }]}>
              Sharing Options
            </Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>
          
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            Choose what information to include when sharing
          </Text>
          
          <ScrollView style={styles.optionsContainer}>
            <PrivacyOptionItem
              label="Include Image"
              icon="image-outline"
              value={privacyOptions.includeImage}
              onToggle={() => handleToggleOption('includeImage')}
              colors={colors}
            />
            
            <PrivacyOptionItem
              label="Include Nutritional Info"
              icon="nutrition-outline"
              value={privacyOptions.includeNutritionalInfo}
              onToggle={() => handleToggleOption('includeNutritionalInfo')}
              colors={colors}
            />
            
            <PrivacyOptionItem
              label="Include Meal Type"
              icon="time-outline"
              value={privacyOptions.includeMealType}
              onToggle={() => handleToggleOption('includeMealType')}
              colors={colors}
            />
            
            <PrivacyOptionItem
              label="Include Ingredients"
              icon="list-outline"
              value={privacyOptions.includeIngredients}
              onToggle={() => handleToggleOption('includeIngredients')}
              colors={colors}
            />
            
            <PrivacyOptionItem
              label="Include Date & Time"
              icon="calendar-outline"
              value={privacyOptions.includeDateTime}
              onToggle={() => handleToggleOption('includeDateTime')}
              colors={colors}
            />
            
            <PrivacyOptionItem
              label="Include Health Highlights"
              icon="heart-outline"
              value={privacyOptions.includeHealthHighlights}
              onToggle={() => handleToggleOption('includeHealthHighlights')}
              colors={colors}
            />
          </ScrollView>
          
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.resetButton, { borderColor: colors.border }]}
              onPress={resetOptions}
            >
              <Text style={[styles.resetButtonText, { color: colors.textSecondary }]}>
                Reset Options
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.shareButton, { backgroundColor: colors.primary }]}
              onPress={() => onShareAsText(privacyOptions)}
            >
              <Ionicons name="share-outline" size={18} color="white" />
              <Text style={styles.shareButtonText}>
                Share as Text
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.shareButton, { backgroundColor: colors.primary }]}
              onPress={() => onShareAsImage(privacyOptions)}
            >
              <Ionicons name="image-outline" size={18} color="white" />
              <Text style={styles.shareButtonText}>
                Share as Image
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </BlurView>
    </Modal>
  );
}

interface PrivacyOptionItemProps {
  label: string;
  icon: string;
  value: boolean;
  onToggle: () => void;
  colors: any;
}

function PrivacyOptionItem({ label, icon, value, onToggle, colors }: PrivacyOptionItemProps) {
  const getIconName = () => {
    switch(icon) {
      case 'image-outline': return 'image-outline' as const;
      case 'nutrition-outline': return 'nutrition-outline' as const;
      case 'time-outline': return 'time-outline' as const;
      case 'list-outline': return 'list-outline' as const;
      case 'calendar-outline': return 'calendar-outline' as const;
      case 'heart-outline': return 'heart-outline' as const;
      default: return 'settings-outline' as const;
    }
  };

  return (
    <View style={[styles.optionItem, { borderBottomColor: colors.border }]}>
      <View style={styles.optionLabelContainer}>
        <Ionicons name={getIconName()} size={22} color={colors.primary} style={styles.optionIcon} />
        <Text style={[styles.optionLabel, { color: colors.text }]}>
          {label}
        </Text>
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        trackColor={{ false: '#767577', true: colors.primary + '80' }}
        thumbColor={value ? colors.primary : '#f4f3f4'}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  container: {
    width: '100%',
    maxWidth: 500,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
  },
  closeButton: {
    padding: 4,
  },
  subtitle: {
    fontSize: 14,
    marginBottom: 20,
  },
  optionsContainer: {
    maxHeight: 300,
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  optionLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionIcon: {
    marginRight: 12,
  },
  optionLabel: {
    fontSize: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  resetButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    marginRight: 8,
  },
  resetButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  shareButton: {
    flex: 2,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginLeft: 8,
  },
  shareButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
}); 