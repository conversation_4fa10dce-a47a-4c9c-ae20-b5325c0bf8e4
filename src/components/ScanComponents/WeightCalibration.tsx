import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Platform,
  Image
} from 'react-native';
import { Camera } from 'expo-camera';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { 
  DEFAULT_WEIGHT_CALIBRATION, 
  WeightCalibrationData,
  FOOD_CATEGORIES
} from '@/utils/foodDensityMap';
import { syncCalibrationData, saveCalibrationToCloud } from '@/services/lidarScanningService';
import { getReferenceObjects } from '@/services/weightAnalysisService';

interface WeightCalibrationProps {
  onComplete: () => void;
  onClose: () => void;
}

export function WeightCalibration({ onComplete, onClose }: WeightCalibrationProps) {
  const { colors, isDark } = useTheme();
  const { user } = useAuth();
  
  // State for calibration data
  const [calibrationData, setCalibrationData] = useState<WeightCalibrationData>(
    { ...DEFAULT_WEIGHT_CALIBRATION, userId: user?.id || '' }
  );
  
  // State for UI
  const [activeTab, setActiveTab] = useState<'global' | 'category' | 'specific'>('global');
  const [selectedCategory, setSelectedCategory] = useState(FOOD_CATEGORIES.FRUITS);
  const [referenceObjects, setReferenceObjects] = useState(getReferenceObjects());
  const [selectedReference, setSelectedReference] = useState<string | null>(null);
  const [knownWeight, setKnownWeight] = useState('');
  const [actualWeight, setActualWeight] = useState('');
  const [calibrationMode, setCalibrationMode] = useState<'reference' | 'manual'>('reference');
  const [specificFoodName, setSpecificFoodName] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  
  // Load calibration data on mount
  useEffect(() => {
    loadCalibrationData();
  }, []);
  
  const loadCalibrationData = async () => {
    try {
      // Try to load from local storage first
      const localData = await AsyncStorage.getItem('weightCalibrationData');
      if (localData) {
        setCalibrationData(JSON.parse(localData));
      } 
      
      // If user is logged in, sync with cloud
      if (user?.id) {
        const cloudData = await syncCalibrationData(user.id);
        if (cloudData && typeof cloudData === 'object') {
          // Convert from cloud format to our format
          const formattedData: WeightCalibrationData = {
            userId: user.id,
            calibrationFactors: {
              global: cloudData.global || 1.0,
              ...Object.keys(FOOD_CATEGORIES).reduce((acc, key) => {
                const category = FOOD_CATEGORIES[key as keyof typeof FOOD_CATEGORIES];
                acc[category] = cloudData[category] || 1.0;
                return acc;
              }, {} as Record<string, number>)
            },
            itemSpecificFactors: (cloudData.items as unknown as Record<string, number>) || {},
            lastUpdated: Date.now()
          };
          
          setCalibrationData(formattedData);
          
          // Save to local storage
          await AsyncStorage.setItem('weightCalibrationData', JSON.stringify(formattedData));
        }
      }
    } catch (error) {
      console.error('Error loading calibration data:', error);
      Alert.alert('Error', 'Failed to load calibration data');
    }
  };
  
  const saveCalibrationData = async () => {
    try {
      setIsSaving(true);
      
      // Save to local storage
      await AsyncStorage.setItem('weightCalibrationData', JSON.stringify(calibrationData));
      
      // If user is logged in, save to cloud
      if (user?.id) {
        // Convert to cloud format
        const cloudFormat: Record<string, any> = {
          global: calibrationData.calibrationFactors.global
        };
        
        // Add all category factors
        Object.entries(calibrationData.calibrationFactors)
          .filter(([key]) => key !== 'global')
          .forEach(([key, value]) => {
            cloudFormat[key] = value;
          });
        
        // Add items separately to avoid type errors
        cloudFormat.items = calibrationData.itemSpecificFactors;
        
        await saveCalibrationToCloud(user.id, cloudFormat as unknown as Record<string, number>);
      }
      
      setIsSaving(false);
      Alert.alert('Success', 'Calibration data saved successfully');
      onComplete();
    } catch (error) {
      setIsSaving(false);
      console.error('Error saving calibration data:', error);
      Alert.alert('Error', 'Failed to save calibration data');
    }
  };
  
  const updateGlobalCalibration = useCallback((value: string) => {
    const numValue = parseFloat(value);
    if (!isNaN(numValue) && numValue > 0) {
      setCalibrationData(prev => ({
        ...prev,
        calibrationFactors: {
          ...prev.calibrationFactors,
          global: numValue
        },
        lastUpdated: Date.now()
      }));
    }
  }, []);
  
  const updateCategoryCalibration = useCallback((category: string, value: string) => {
    const numValue = parseFloat(value);
    if (!isNaN(numValue) && numValue > 0) {
      setCalibrationData(prev => ({
        ...prev,
        calibrationFactors: {
          ...prev.calibrationFactors,
          [category]: numValue
        },
        lastUpdated: Date.now()
      }));
    }
  }, []);
  
  const updateSpecificItemCalibration = useCallback(() => {
    if (!specificFoodName.trim()) {
      Alert.alert('Error', 'Please enter a food name');
      return;
    }
    
    // Calculate calibration factor
    if (calibrationMode === 'reference') {
      if (!selectedReference) {
        Alert.alert('Error', 'Please select a reference object');
        return;
      }
      
      const reference = referenceObjects.find(r => r.name === selectedReference);
      if (!reference) return;
      
      const knownWeightValue = parseFloat(knownWeight);
      if (isNaN(knownWeightValue) || knownWeightValue <= 0) {
        Alert.alert('Error', 'Please enter a valid known weight');
        return;
      }
      
      // Calculate calibration factor (actual/expected)
      const calibrationFactor = reference.weightGrams / knownWeightValue;
      
      setCalibrationData(prev => ({
        ...prev,
        itemSpecificFactors: {
          ...prev.itemSpecificFactors,
          [specificFoodName.toLowerCase().trim()]: calibrationFactor
        },
        lastUpdated: Date.now()
      }));
      
      // Reset fields
      setSpecificFoodName('');
      setSelectedReference(null);
      setKnownWeight('');
      
      Alert.alert('Success', `Calibration for ${specificFoodName} has been added`);
    } else {
      // Manual mode
      const knownWeightValue = parseFloat(knownWeight);
      const actualWeightValue = parseFloat(actualWeight);
      
      if (isNaN(knownWeightValue) || knownWeightValue <= 0 || 
          isNaN(actualWeightValue) || actualWeightValue <= 0) {
        Alert.alert('Error', 'Please enter valid weights');
        return;
      }
      
      // Calculate calibration factor (actual/expected)
      const calibrationFactor = actualWeightValue / knownWeightValue;
      
      setCalibrationData(prev => ({
        ...prev,
        itemSpecificFactors: {
          ...prev.itemSpecificFactors,
          [specificFoodName.toLowerCase().trim()]: calibrationFactor
        },
        lastUpdated: Date.now()
      }));
      
      // Reset fields
      setSpecificFoodName('');
      setKnownWeight('');
      setActualWeight('');
      
      Alert.alert('Success', `Calibration for ${specificFoodName} has been added`);
    }
  }, [specificFoodName, selectedReference, knownWeight, actualWeight, calibrationMode, referenceObjects]);
  
  const resetCalibration = useCallback(() => {
    Alert.alert(
      'Confirm Reset',
      'Are you sure you want to reset all calibration data? This cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Reset', 
          style: 'destructive',
          onPress: () => {
            setCalibrationData({ 
              ...DEFAULT_WEIGHT_CALIBRATION,
              userId: user?.id || '' 
            });
          }
        }
      ]
    );
  }, [user]);
  
  const deleteSpecificCalibration = useCallback((foodName: string) => {
    Alert.alert(
      'Confirm Delete',
      `Are you sure you want to delete calibration for "${foodName}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: () => {
            setCalibrationData(prev => {
              const newItemFactors = { ...prev.itemSpecificFactors };
              delete newItemFactors[foodName];
              
              return {
                ...prev,
                itemSpecificFactors: newItemFactors,
                lastUpdated: Date.now()
              };
            });
          }
        }
      ]
    );
  }, []);
  
  const renderGlobalTabContent = () => (
    <View style={styles.tabContent}>
      <Text style={[styles.heading, { color: colors.text }]}>Global Weight Calibration</Text>
      <Text style={[styles.description, { color: colors.text }]}>
        Adjust the global calibration factor to improve overall weight accuracy.
        A value greater than 1.0 increases estimated weights, while a value less than 1.0 decreases them.
      </Text>
      
      <View style={styles.calibrationControl}>
        <Text style={[styles.label, { color: colors.text }]}>Global Calibration Factor:</Text>
        <TextInput
          style={[styles.input, { 
            color: colors.text, 
            borderColor: colors.border,
            backgroundColor: colors.background
          }]}
          value={calibrationData.calibrationFactors.global.toString()}
          onChangeText={updateGlobalCalibration}
          keyboardType="numeric"
          placeholder="1.0"
          placeholderTextColor={colors.textSecondary}
        />
      </View>
      
      <TouchableOpacity
        style={[styles.resetButton, { backgroundColor: colors.error }]}
        onPress={resetCalibration}
      >
        <Text style={styles.resetButtonText}>Reset All Calibrations</Text>
      </TouchableOpacity>
    </View>
  );
  
  const renderCategoryTabContent = () => (
    <View style={styles.tabContent}>
      <Text style={[styles.heading, { color: colors.text }]}>Category Calibration</Text>
      <Text style={[styles.description, { color: colors.text }]}>
        Adjust calibration factors for specific food categories.
      </Text>
      
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categorySelector}>
        {Object.values(FOOD_CATEGORIES).map(category => (
          <TouchableOpacity
            key={category}
            style={[
              styles.categoryButton,
              selectedCategory === category && { 
                backgroundColor: colors.primary,
                borderColor: colors.primary 
              },
              { borderColor: colors.border }
            ]}
            onPress={() => setSelectedCategory(category)}
          >
            <Text
              style={[
                styles.categoryButtonText,
                selectedCategory === category ? 
                  { color: 'white' } : 
                  { color: colors.text }
              ]}
            >
              {category.charAt(0).toUpperCase() + category.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
      
      <View style={styles.calibrationControl}>
        <Text style={[styles.label, { color: colors.text }]}>
          Calibration Factor for {selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)}:
        </Text>
        <TextInput
          style={[styles.input, { 
            color: colors.text, 
            borderColor: colors.border,
            backgroundColor: colors.background
          }]}
          value={(calibrationData.calibrationFactors[selectedCategory] || 1.0).toString()}
          onChangeText={(value) => updateCategoryCalibration(selectedCategory, value)}
          keyboardType="numeric"
          placeholder="1.0"
          placeholderTextColor={colors.textSecondary}
        />
      </View>
    </View>
  );
  
  const renderSpecificTabContent = () => (
    <View style={styles.tabContent}>
      <Text style={[styles.heading, { color: colors.text }]}>Specific Food Calibration</Text>
      <Text style={[styles.description, { color: colors.text }]}>
        Add calibration factors for specific food items for the best accuracy.
      </Text>
      
      <View style={styles.calibrationModeSelector}>
        <TouchableOpacity
          style={[
            styles.modeButton,
            calibrationMode === 'reference' && { 
              backgroundColor: colors.primary,
              borderColor: colors.primary 
            },
            { borderColor: colors.border }
          ]}
          onPress={() => setCalibrationMode('reference')}
        >
          <Text
            style={[
              styles.modeButtonText,
              calibrationMode === 'reference' ? 
                { color: 'white' } : 
                { color: colors.text }
            ]}
          >
            Use Reference Object
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.modeButton,
            calibrationMode === 'manual' && { 
              backgroundColor: colors.primary,
              borderColor: colors.primary 
            },
            { borderColor: colors.border }
          ]}
          onPress={() => setCalibrationMode('manual')}
        >
          <Text
            style={[
              styles.modeButtonText,
              calibrationMode === 'manual' ? 
                { color: 'white' } : 
                { color: colors.text }
            ]}
          >
            Manual Entry
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.calibrationControl}>
        <Text style={[styles.label, { color: colors.text }]}>Food Name:</Text>
        <TextInput
          style={[styles.input, { 
            color: colors.text, 
            borderColor: colors.border,
            backgroundColor: colors.background
          }]}
          value={specificFoodName}
          onChangeText={setSpecificFoodName}
          placeholder="e.g. Banana, Apple Slice"
          placeholderTextColor={colors.textSecondary}
        />
      </View>
      
      {calibrationMode === 'reference' ? (
        <>
          <Text style={[styles.subheading, { color: colors.text }]}>Select Reference Object</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.referenceSelector}>
            {referenceObjects.map(obj => (
              <TouchableOpacity
                key={obj.name}
                style={[
                  styles.referenceButton,
                  selectedReference === obj.name && { 
                    backgroundColor: colors.primary,
                    borderColor: colors.primary 
                  },
                  { borderColor: colors.border }
                ]}
                onPress={() => setSelectedReference(obj.name)}
              >
                <Text
                  style={[
                    styles.referenceButtonText,
                    selectedReference === obj.name ? 
                      { color: 'white' } : 
                      { color: colors.text }
                  ]}
                >
                  {obj.name} ({obj.weightGrams}g)
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
          
          <View style={styles.calibrationControl}>
            <Text style={[styles.label, { color: colors.text }]}>
              Your Food's Weight (grams):
            </Text>
            <TextInput
              style={[styles.input, { 
                color: colors.text, 
                borderColor: colors.border,
                backgroundColor: colors.background
              }]}
              value={knownWeight}
              onChangeText={setKnownWeight}
              keyboardType="numeric"
              placeholder="Enter weight"
              placeholderTextColor={colors.textSecondary}
            />
          </View>
        </>
      ) : (
        <>
          <View style={styles.calibrationControl}>
            <Text style={[styles.label, { color: colors.text }]}>
              Estimated Weight by App (grams):
            </Text>
            <TextInput
              style={[styles.input, { 
                color: colors.text, 
                borderColor: colors.border,
                backgroundColor: colors.background
              }]}
              value={knownWeight}
              onChangeText={setKnownWeight}
              keyboardType="numeric"
              placeholder="Enter estimated weight"
              placeholderTextColor={colors.textSecondary}
            />
          </View>
          
          <View style={styles.calibrationControl}>
            <Text style={[styles.label, { color: colors.text }]}>
              Actual Weight (grams):
            </Text>
            <TextInput
              style={[styles.input, { 
                color: colors.text, 
                borderColor: colors.border,
                backgroundColor: colors.background
              }]}
              value={actualWeight}
              onChangeText={setActualWeight}
              keyboardType="numeric"
              placeholder="Enter actual weight"
              placeholderTextColor={colors.textSecondary}
            />
          </View>
        </>
      )}
      
      <TouchableOpacity
        style={[styles.addButton, { backgroundColor: colors.primary }]}
        onPress={updateSpecificItemCalibration}
      >
        <Text style={styles.addButtonText}>Add Calibration</Text>
      </TouchableOpacity>
      
      {/* List of specific calibrations */}
      {Object.keys(calibrationData.itemSpecificFactors).length > 0 && (
        <>
          <Text style={[styles.subheading, { color: colors.text, marginTop: 20 }]}>
            Current Specific Calibrations
          </Text>
          <ScrollView style={styles.calibrationList}>
            {Object.entries(calibrationData.itemSpecificFactors).map(([foodName, factor]) => (
              <View key={foodName} style={[styles.calibrationItem, { borderColor: colors.border }]}>
                <View style={styles.calibrationItemInfo}>
                  <Text style={[styles.calibrationItemName, { color: colors.text }]}>
                    {foodName}
                  </Text>
                  <Text style={[styles.calibrationItemFactor, { color: colors.textSecondary }]}>
                    Factor: {factor.toFixed(2)}
                  </Text>
                </View>
                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={() => deleteSpecificCalibration(foodName)}
                >
                  <Ionicons name="trash-outline" size={20} color={colors.error} />
                </TouchableOpacity>
              </View>
            ))}
          </ScrollView>
        </>
      )}
    </View>
  );
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Ionicons name="close-outline" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Weight Calibration</Text>
        <TouchableOpacity 
          style={[styles.saveButton, isSaving && { opacity: 0.7 }]} 
          onPress={saveCalibrationData}
          disabled={isSaving}
        >
          <Text style={[styles.saveButtonText, { color: colors.primary }]}>
            {isSaving ? 'Saving...' : 'Save'}
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.tabBar}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'global' && { 
              borderBottomColor: colors.primary,
              borderBottomWidth: 2 
            }
          ]}
          onPress={() => setActiveTab('global')}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'global' ? 
                { color: colors.primary } : 
                { color: colors.textSecondary }
            ]}
          >
            Global
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'category' && { 
              borderBottomColor: colors.primary,
              borderBottomWidth: 2 
            }
          ]}
          onPress={() => setActiveTab('category')}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'category' ? 
                { color: colors.primary } : 
                { color: colors.textSecondary }
            ]}
          >
            Category
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'specific' && { 
              borderBottomColor: colors.primary,
              borderBottomWidth: 2 
            }
          ]}
          onPress={() => setActiveTab('specific')}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'specific' ? 
                { color: colors.primary } : 
                { color: colors.textSecondary }
            ]}
          >
            Specific
          </Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.content}>
        {activeTab === 'global' && renderGlobalTabContent()}
        {activeTab === 'category' && renderCategoryTabContent()}
        {activeTab === 'specific' && renderSpecificTabContent()}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 8,
  },
  saveButton: {
    padding: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  tabBar: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  tabText: {
    fontSize: 15,
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  tabContent: {
    padding: 16,
  },
  heading: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
  },
  subheading: {
    fontSize: 17,
    fontWeight: '600',
    marginBottom: 12,
    marginTop: 8,
  },
  description: {
    fontSize: 14,
    marginBottom: 20,
    lineHeight: 20,
  },
  calibrationControl: {
    marginBottom: 16,
  },
  label: {
    fontSize: 15,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    height: 44,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  categorySelector: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  referenceSelector: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  referenceButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginRight: 8,
    borderWidth: 1,
  },
  referenceButtonText: {
    fontSize: 13,
    fontWeight: '500',
  },
  addButton: {
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  resetButton: {
    alignItems: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 24,
  },
  resetButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  calibrationModeSelector: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  modeButton: {
    flex: 1,
    paddingVertical: 10,
    borderRadius: 8,
    marginHorizontal: 4,
    borderWidth: 1,
    alignItems: 'center',
  },
  modeButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  calibrationList: {
    maxHeight: 200,
  },
  calibrationItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderBottomWidth: 1,
  },
  calibrationItemInfo: {
    flex: 1,
  },
  calibrationItemName: {
    fontSize: 15,
    fontWeight: '500',
  },
  calibrationItemFactor: {
    fontSize: 13,
    marginTop: 2,
  },
  deleteButton: {
    padding: 8,
  },
}); 