import React from 'react';
import { View, Text, StyleSheet , useColorScheme } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '@/constants/Colors';

interface WeightConfidenceIndicatorProps {
  confidence: number;  // 0-1 scale
  verbose?: boolean;   // Show detailed text
  size?: 'small' | 'medium' | 'large';
}

/**
 * Component that visually displays the confidence level for a weight estimate
 */
export function WeightConfidenceIndicator({
  confidence,
  verbose = false,
  size = 'medium'
}: WeightConfidenceIndicatorProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme || 'light'];
  
  // Determine confidence level category
  const getConfidenceLevel = (): 'high' | 'medium' | 'low' => {
    if (confidence >= 0.8) return 'high';
    if (confidence >= 0.5) return 'medium';
    return 'low';
  };
  
  // Get appropriate color based on confidence level
  const getConfidenceColor = (): string => {
    switch (getConfidenceLevel()) {
      case 'high': return '#4CAF50';     // Green
      case 'medium': return '#FFC107';   // Amber
      case 'low': return '#F44336';      // Red
      default: return '#757575';         // Gray (fallback)
    }
  };
  
  // Get appropriate icon based on confidence level
  const getConfidenceIcon = () => {
    switch (getConfidenceLevel()) {
      case 'high': return 'checkmark-circle-outline';
      case 'medium': return 'alert-circle-outline';
      case 'low': return 'warning-outline';
      default: return 'help-circle-outline';
    }
  };
  
  // Get size values based on size prop
  const getSizeValues = () => {
    switch (size) {
      case 'small': return { icon: 16, text: 12, height: 4 };
      case 'large': return { icon: 28, text: 16, height: 8 };
      default: return { icon: 22, text: 14, height: 6 };  // medium
    }
  };
  
  const sizeValues = getSizeValues();
  const confidenceColor = getConfidenceColor();
  const confidenceLevel = getConfidenceLevel();
  const confidenceIcon = getConfidenceIcon();
  
  // Calculate filled width percentage (0-100)
  const filledPercentage = Math.round(confidence * 100);
  
  return (
    <View style={styles.container}>
      {/* Icon indicator */}
      <Ionicons 
        name={confidenceIcon} 
        size={sizeValues.icon} 
        color={confidenceColor} 
      />
      
      {/* Progress bar */}
      <View style={[styles.progressContainer, { height: sizeValues.height }]}>
        <View 
          style={[
            styles.progressFill, 
            { 
              width: `${filledPercentage}%`, 
              backgroundColor: confidenceColor,
              height: sizeValues.height
            }
          ]} 
        />
      </View>
      
      {/* Percentage text */}
      <Text style={[styles.percentageText, { fontSize: sizeValues.text, color: colors.text }]}>
        {filledPercentage}%
      </Text>
      
      {/* Verbose description (optional) */}
      {verbose && (
        <Text style={[styles.verboseText, { fontSize: sizeValues.text, color: colors.text }]}>
          {confidenceLevel === 'high' && 'High confidence'}
          {confidenceLevel === 'medium' && 'Medium confidence'}
          {confidenceLevel === 'low' && 'Low confidence'}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4
  },
  progressContainer: {
    flex: 1,
    backgroundColor: '#E0E0E0',
    marginHorizontal: 8,
    borderRadius: 4,
    overflow: 'hidden'
  },
  progressFill: {
    borderRadius: 4
  },
  percentageText: {
    fontWeight: 'bold',
    marginLeft: 4
  },
  verboseText: {
    marginLeft: 8
  }
}); 