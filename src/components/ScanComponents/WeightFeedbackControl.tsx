import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput, Modal , useColorScheme } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '@/constants/Colors';

interface WeightFeedbackControlProps {
  foodName: string;
  estimatedWeight: number;
  confidence: number;
  onFeedbackSubmit: (feedbackData: {
    foodName: string;
    estimatedWeight: number;
    actualWeight?: number;
    feedbackType: 'accurate' | 'too-high' | 'too-low' | 'way-off';
  }) => void;
}

/**
 * Component that allows users to provide quick feedback on weight estimation accuracy
 */
export function WeightFeedbackControl({
  foodName,
  estimatedWeight,
  confidence,
  onFeedbackSubmit
}: WeightFeedbackControlProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme || 'light'];
  
  const [showExactWeightInput, setShowExactWeightInput] = useState(false);
  const [actualWeight, setActualWeight] = useState('');
  
  // Handle thumbs up feedback
  const handleAccurateFeedback = () => {
    onFeedbackSubmit({
      foodName,
      estimatedWeight,
      feedbackType: 'accurate'
    });
  };
  
  // Handle thumbs down feedback
  const handleInaccurateFeedback = (type: 'too-high' | 'too-low' | 'way-off') => {
    onFeedbackSubmit({
      foodName,
      estimatedWeight,
      feedbackType: type
    });
  };
  
  // Handle submission of actual weight
  const handleExactWeightSubmit = () => {
    const parsedWeight = parseFloat(actualWeight);
    
    if (isNaN(parsedWeight) || parsedWeight <= 0) {
      // Invalid weight, don't submit
      setShowExactWeightInput(false);
      return;
    }
    
    // Determine feedback type based on difference percentage
    const percentDiff = Math.abs((estimatedWeight - parsedWeight) / parsedWeight);
    let feedbackType: 'accurate' | 'too-high' | 'too-low' | 'way-off';
    
    if (percentDiff <= 0.1) {
      feedbackType = 'accurate'; // Within 10%
    } else if (estimatedWeight > parsedWeight) {
      feedbackType = percentDiff > 0.3 ? 'way-off' : 'too-high';
    } else {
      feedbackType = percentDiff > 0.3 ? 'way-off' : 'too-low';
    }
    
    onFeedbackSubmit({
      foodName,
      estimatedWeight,
      actualWeight: parsedWeight,
      feedbackType
    });
    
    // Reset and close input
    setActualWeight('');
    setShowExactWeightInput(false);
  };
  
  // Get confidence level color
  const getConfidenceColor = () => {
    if (confidence >= 0.8) return '#4CAF50'; // High confidence: green
    if (confidence >= 0.5) return '#FFC107'; // Medium confidence: amber
    return '#F44336'; // Low confidence: red
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.estimateContainer}>
        <Text style={[styles.estimateLabel, { color: colors.text }]}>
          Estimated weight:
        </Text>
        <Text style={[styles.estimateValue, { color: colors.text }]}>
          {Math.round(estimatedWeight)}g
        </Text>
        <View 
          style={[
            styles.confidenceIndicator, 
            { backgroundColor: getConfidenceColor() }
          ]} 
        />
      </View>
      
      <Text style={[styles.feedbackPrompt, { color: colors.text }]}>
        Is this estimate accurate?
      </Text>
      
      <View style={styles.feedbackControls}>
        <TouchableOpacity 
          style={styles.feedbackButton} 
          onPress={handleAccurateFeedback}
        >
          <Ionicons name="thumbs-up-outline" size={24} color={colors.tint} />
          <Text style={[styles.buttonText, { color: colors.text }]}>Yes</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.feedbackButton} 
          onPress={() => handleInaccurateFeedback('too-high')}
        >
          <Ionicons name="arrow-down-outline" size={24} color="#F44336" />
          <Text style={[styles.buttonText, { color: colors.text }]}>Too high</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.feedbackButton} 
          onPress={() => handleInaccurateFeedback('too-low')}
        >
          <Ionicons name="arrow-up-outline" size={24} color="#4CAF50" />
          <Text style={[styles.buttonText, { color: colors.text }]}>Too low</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.feedbackButton} 
          onPress={() => setShowExactWeightInput(true)}
        >
          <Ionicons name="create-outline" size={24} color={colors.tint} />
          <Text style={[styles.buttonText, { color: colors.text }]}>
            Enter exact
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* Modal for entering exact weight */}
      <Modal
        visible={showExactWeightInput}
        transparent={true}
        animationType="fade"
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.background }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Enter Actual Weight
            </Text>
            
            <TextInput
              style={[styles.weightInput, { color: colors.text, borderColor: colors.tint }]}
              placeholder="Weight in grams"
              placeholderTextColor="#888"
              keyboardType="numeric"
              value={actualWeight}
              onChangeText={setActualWeight}
              autoFocus
            />
            
            <View style={styles.modalButtons}>
              <TouchableOpacity 
                style={[styles.modalButton, styles.cancelButton]} 
                onPress={() => {
                  setActualWeight('');
                  setShowExactWeightInput(false);
                }}
              >
                <Text style={styles.buttonText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.modalButton, styles.submitButton]} 
                onPress={handleExactWeightSubmit}
              >
                <Text style={[styles.buttonText, { color: '#FFF' }]}>Submit</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    padding: 15,
    marginVertical: 10,
    borderRadius: 12,
  },
  estimateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  estimateLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  estimateValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  confidenceIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginLeft: 8,
  },
  feedbackPrompt: {
    fontSize: 15,
    fontWeight: '500',
    marginBottom: 10,
  },
  feedbackControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  feedbackButton: {
    alignItems: 'center',
    padding: 10,
  },
  buttonText: {
    marginTop: 5,
    fontSize: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    padding: 20,
    borderRadius: 12,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  weightInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    padding: 12,
    borderRadius: 8,
    width: '45%',
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#E0E0E0',
  },
  submitButton: {
    backgroundColor: '#007AFF',
  },
}); 