import React, { useRef, useEffect } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Platform, Animated, Dimensions, ColorValue, Easing } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { canUseNativeDriver } from '@/utils/platformUtils';

interface SuggestedMeal {
  mealType: string;
  suggestedTime: string;
}

interface ScanOptionsProps {
  onOpenCamera: () => void;
  onOpenGallery: () => void;
  disabled?: boolean;
  suggestedMeal?: SuggestedMeal;
}

export function ScanOptions({ 
  onOpenCamera, 
  onOpenGallery, 
  disabled = false,
  suggestedMeal
}: ScanOptionsProps) {
  const { colors, isDark } = useTheme();
  const windowWidth = Dimensions.get('window').width;
  
  // Animation values
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const slideUpAnim = useRef(new Animated.Value(30)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const shineAnim = useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    // Animate content fade in and slide up
    Animated.parallel([
      Animated.timing(fadeInAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: canUseNativeDriver(),
        easing: Easing.out(Easing.quad),
      }),
      Animated.timing(slideUpAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: canUseNativeDriver(),
        easing: Easing.out(Easing.quad),
      })
    ]).start();
    
    // Start pulse animation for the meal card
    const startPulseAnimation = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.03,
          duration: 1500,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: canUseNativeDriver(),
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1500,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: canUseNativeDriver(),
        })
      ]).start(() => startPulseAnimation());
    };
    
    // Start shine animation effect
    const startShineAnimation = () => {
      Animated.timing(shineAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: canUseNativeDriver(),
      }).start(() => {
        shineAnim.setValue(0);
        // Restart with delay
        setTimeout(startShineAnimation, 3000);
      });
    };
    
    startPulseAnimation();
    startShineAnimation();
  }, []);
  
  // Mapping meal types to gradients for visual appeal
  const getMealTypeGradient = (mealType: string): readonly [ColorValue, ColorValue] => {
    const mealLower = mealType.toLowerCase();
    
    if (mealLower.includes('breakfast')) {
      return ['#F59E0B', '#D97706'] as const; // Amber
    } else if (mealLower.includes('lunch')) {
      return ['#10B981', '#059669'] as const; // Green
    } else if (mealLower.includes('dinner')) {
      return ['#3B82F6', '#2563EB'] as const; // Blue
    } else if (mealLower.includes('snack')) {
      return ['#8B5CF6', '#7C3AED'] as const; // Purple
    }
    
    // Default gradient
    return ['#EC4899', '#DB2777'] as const; // Pink
  };
  
  return (
    <Animated.View 
      style={[
        styles.container, 
        {
          opacity: fadeInAnim,
          transform: [{ translateY: slideUpAnim }]
        }
      ]}
    >
      <Text style={[styles.title, { color: colors.text }]}>
        Food Scanner
      </Text>
      <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
        Analyze your meals with AI for accurate nutrition tracking
      </Text>
      
      {suggestedMeal && (
        <Animated.View style={{
          transform: [{ scale: pulseAnim }]
        }}>
          <LinearGradient
            colors={getMealTypeGradient(suggestedMeal.mealType)}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.suggestedMealContainer}
          >
            {/* Animated shine effect */}
            <Animated.View 
              style={[
                styles.shineEffect,
                {
                  transform: [
                    {
                      translateX: shineAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [-windowWidth, windowWidth]
                      })
                    }
                  ]
                }
              ]}
            />
            
            <View style={styles.suggestedMealContent}>
              <View style={styles.suggestedMealIcon}>
                <Feather name="zap" size={20}  color={colors.text} />
              </View>
              
              <View style={styles.suggestedMealTextContainer}>
                <Text style={styles.suggestedMealLabel}>
                  Recommended Meal
                </Text>
                <Text style={styles.suggestedMealType}>
                  {suggestedMeal.mealType} • {suggestedMeal.suggestedTime}
                </Text>
              </View>
              
              <View style={styles.suggestedMealArrow}>
                <Feather name="chevron-right" size={20} style={{opacity: 0.7}} />
              </View>
            </View>
          </LinearGradient>
        </Animated.View>
      )}
      
      <View style={styles.optionsContainer}>
        <TouchableOpacity
          style={[
            styles.optionCard,
            { 
              backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : colors.card,
              borderColor: colors.border,
              shadowColor: colors.shadow,
              opacity: disabled ? 0.7 : 1
            }
          ]}
          onPress={onOpenCamera}
          disabled={disabled}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={['#3B82F6', '#2563EB']}
            style={styles.iconContainer}
          >
            <Feather name="camera" size={28}  color={colors.text} />
          </LinearGradient>
          <Text style={[styles.optionTitle, { color: colors.text }]}>
            Take Photo
          </Text>
          <Text style={[styles.optionDescription, { color: colors.textSecondary }]}>
            Use your camera to snap a picture of your meal
          </Text>
          
          <View style={[styles.optionTag, { backgroundColor: '#EFF6FF' }]}>
            <Text style={{ color: '#2563EB', fontSize: 11, fontWeight: '600' }}>Fast</Text>
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.optionCard,
            { 
              backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : colors.card,
              borderColor: colors.border,
              shadowColor: colors.shadow,
              opacity: disabled ? 0.7 : 1
            }
          ]}
          onPress={onOpenGallery}
          disabled={disabled}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={['#8B5CF6', '#7C3AED']}
            style={styles.iconContainer}
          >
            <ScanLine size={28} color="white" />
          </LinearGradient>
          <Text style={[styles.optionTitle, { color: colors.text }]}>
            Upload Photo
          </Text>
          <Text style={[styles.optionDescription, { color: colors.textSecondary }]}>
            Select an image from your photo gallery
          </Text>
          
          <View style={[styles.optionTag, { backgroundColor: '#F5F3FF' }]}>
            <Text style={{ color: '#7C3AED', fontSize: 11, fontWeight: '600' }}>Easy</Text>
          </View>
        </TouchableOpacity>
      </View>
      
      <View style={[styles.divider, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]} />
      
      <View style={styles.historySection}>
        <TouchableOpacity 
          style={[
            styles.historyButton, 
            { 
              backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : colors.subtle,
              borderColor: colors.border
            }
          ]}
          activeOpacity={0.8}
        >
          <Feather name="clock" size={16} color={colors.primary} style={styles.historyIcon} />
          <Text style={[styles.historyText, { color: colors.text }]}>
            Recent Scans
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[
            styles.historyButton, 
            { 
              backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : colors.subtle,
              borderColor: colors.border
            }
          ]}
          activeOpacity={0.8}
        >
          <MaterialIcons name="history" size={16} color={colors.primary} style={styles.historyIcon} />
          <Text style={[styles.historyText, { color: colors.text }]}>
            Meal History
          </Text>
        </TouchableOpacity>
      </View>
      
      <Text style={[styles.tipText, { color: colors.textSecondary }]}>
        Tip: Place food in good lighting for better results
      </Text>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: '800',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    paddingHorizontal: 20,
    lineHeight: 22,
  },
  suggestedMealContainer: {
    marginBottom: 24,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    position: 'relative',
  },
  shineEffect: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    width: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    transform: [{ skewX: '-20deg' }]
  },
  suggestedMealContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  suggestedMealIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  suggestedMealTextContainer: {
    flex: 1,
  },
  suggestedMealLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 4,
  },
  suggestedMealType: {
    fontSize: 16,
    fontWeight: '700',
    color: 'white',
  },
  suggestedMealArrow: {
    padding: 8,
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  optionCard: {
    width: '48%',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    borderWidth: 1,
    ...Platform.select({
      ios: {
        shadowOffset: {
          width: 0,
          height: 4,
        },
        shadowOpacity: 0.15,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
      web: {
        shadowOffset: {
          width: 0,
          height: 4,
        },
        shadowOpacity: 0.15,
        shadowRadius: 8,
      }
    }),
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
  },
  optionDescription: {
    fontSize: 13,
    textAlign: 'center',
    lineHeight: 18,
  },
  divider: {
    height: 1,
    backgroundColor: 'rgba(0,0,0,0.1)',
    marginBottom: 24,
  },
  historySection: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    marginBottom: 24,
  },
  historyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 24,
    borderWidth: 1,
  },
  historyIcon: {
    marginRight: 8,
  },
  historyText: {
    fontSize: 14,
    fontWeight: '600',
  },
  tipText: {
    fontSize: 13,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  optionTag: {
    position: 'absolute',
    top: 10,
    right: 10,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
});