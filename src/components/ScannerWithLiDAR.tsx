/**
 * ScannerWithLiDAR - Forward to modular implementation
 * This component acts as a bridge to our modular LiDAR implementation
 */
import React from 'react';
import { ScannerWithLiDAR as ModularScanner } from './LiDAR';
import { ScannerWithLiDARProps } from '../utils/LiDAR/types';

export default function ScannerWithLiDAR(props: ScannerWithLiDARProps) {
  // Common household reference objects with known volumes
  const referenceObjects = [
    { id: 'soda_can', name: 'Soda Can (12 oz)', volume: 355, unit: 'ml' },
    { id: 'tennis_ball', name: 'Tennis Ball', volume: 138, unit: 'cm³' },
    { id: 'iphone', name: 'iPhone', volume: 146, unit: 'cm³' },
    { id: 'custom', name: 'Custom Object', volume: 0, unit: 'cm³' }
  ];

  return <ModularScanner {...props} referenceObjects={referenceObjects} />;
}