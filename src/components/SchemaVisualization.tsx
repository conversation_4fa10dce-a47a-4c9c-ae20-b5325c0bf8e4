import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Animated, Dimensions, Platform, TextInput } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

interface TableField {
  name: string;
  type: string;
  isPrimary: boolean;
  isForeign: boolean;
  isNullable: boolean;
  hasDefault: boolean;
  defaultValue?: string;
}

interface TableRelation {
  table: string;
  field: string;
  referencedTable: string;
  referencedField: string;
  onDelete?: string;
}

interface TableProps {
  name: string;
  fields: TableField[];
  relations: TableRelation[];
  hasRLS: boolean;
}

// TableCard component to display a single table
const TableCard = ({ table, onSelect, isSelected }: { 
  table: TableProps; 
  onSelect: () => void; 
  isSelected: boolean;
}) => {
  const { colors } = useTheme();
  const [expanded, setExpanded] = useState(false);
  const heightAnim = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    Animated.parallel([
      Animated.timing(heightAnim, {
        toValue: expanded ? 1 : 0,
        duration: 300,
        useNativeDriver: false,
      }),
      Animated.timing(rotateAnim, {
        toValue: expanded ? 1 : 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, [expanded]);
  
  const maxHeight = heightAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 500], // Max height for expanded view
  });
  
  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '90deg'],
  });
  
  const toggleExpand = () => {
    setExpanded(!expanded);
  };
  
  // Get primary and foreign key counts
  const primaryKeys = table.fields.filter(f => f.isPrimary).length;
  const foreignKeys = table.fields.filter(f => f.isForeign).length;
  
  return (
    <Animated.View style={{
      opacity: isSelected ? 1 : 0.95,
      transform: [{scale: isSelected ? 1 : 0.99}]
    }}>
      <TouchableOpacity
        style={[
          styles.tableCard, 
          { 
            backgroundColor: colors.card,
            borderColor: isSelected ? colors.primary : colors.border,
            borderLeftWidth: isSelected ? 4 : 1,
            shadowColor: colors.shadow,
          }
        ]}
        onPress={onSelect}
        activeOpacity={0.8}
      >
        <View style={styles.tableHeader}>
          <View style={styles.tableNameContainer}>
            <View style={[
              styles.tableIconContainer,
              { backgroundColor: isSelected ? colors.primaryLight : colors.subtle }
            ]}>
              <Table2 
                size={18} 
                color={isSelected ? colors.primary : colors.textSecondary} 
              />
            </View>
            <View>
              <Text style={[styles.tableName, { 
                color: colors.text,
                fontWeight: isSelected ? '700' : '600'
              }]}>
                {table.name}
              </Text>
              <Text style={[styles.tableDetails, { color: colors.textSecondary }]}>
                {table.fields.length} fields • {primaryKeys} PK • {foreignKeys} FK
              </Text>
            </View>
          </View>
          
          {table.hasRLS && (
            <View style={[styles.rlsBadge, { backgroundColor: colors.primaryLight }]}>
              <Feather name="shield" size={12} color={colors.primary} />
              <Text style={[styles.rlsText, { color: colors.primary }]}>RLS</Text>
            </View>
          )}
          
          <TouchableOpacity 
            style={[styles.expandButton, { backgroundColor: colors.subtle }]}
            onPress={toggleExpand}
          >
            <Animated.View style={{ transform: [{ rotate }] }}>
              <Feather name="chevron-right" size={18} color={colors.textSecondary} />
            </Animated.View>
          </TouchableOpacity>
        </View>
        
        <Animated.View 
          style={[
            styles.tableDetailsContainer, 
            { 
              maxHeight,
              borderTopColor: colors.border,
              borderTopWidth: expanded ? 1 : 0,
            }
          ]}
        >
          <View style={[styles.fieldsHeader, { borderBottomColor: colors.border }]}>
            <Text style={[styles.fieldsHeaderText, { color: colors.textSecondary }]}>
              Fields
            </Text>
            <View style={styles.fieldCountBadge}>
              <Text style={[styles.fieldCount, { color: colors.textSecondary }]}>
                {table.fields.length}
              </Text>
            </View>
          </View>
          
          <ScrollView style={styles.fieldsScrollView} showsVerticalScrollIndicator={false}>
            {table.fields.map((field, index) => (
              <View 
                key={field.name} 
                style={[
                  styles.fieldItem, 
                  index !== table.fields.length - 1 && { borderBottomColor: colors.border, borderBottomWidth: 0.5 }
                ]}
              >
                <View style={styles.fieldNameContainer}>
                  <View style={styles.keyIcons}>
                    {field.isPrimary && (
                      <View style={[styles.keyIconContainer, { backgroundColor: '#FEF3C7' }]}>
                        <KeyRound size={12} color="#F59E0B" />
                      </View>
                    )}
                    {field.isForeign && (
                      <View style={[styles.keyIconContainer, { backgroundColor: '#DBEAFE' }]}>
                        <KeySquare size={12} color="#3B82F6" />
                      </View>
                    )}
                    {!field.isPrimary && !field.isForeign && (
                      <View style={styles.placeholderIcon} />
                    )}
                  </View>
                  <Text 
                    style={[
                      styles.fieldName, 
                      { color: colors.text },
                      field.isPrimary && { fontWeight: '700' }
                    ]}
                  >
                    {field.name}
                  </Text>
                </View>
                <View style={styles.fieldTypeContainer}>
                  <View style={[styles.typeTag, { backgroundColor: colors.subtle }]}>
                    <Text style={[styles.fieldType, { color: colors.text }]}>
                      {field.type}
                    </Text>
                  </View>
                  {field.isNullable && (
                    <View style={[styles.nullableTag, { backgroundColor: colors.border }]}>
                      <Text style={[styles.nullableText, { color: colors.textSecondary }]}>
                        null
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            ))}
          </ScrollView>
          
          {table.relations.length > 0 && (
            <>
              <View style={[styles.fieldsHeader, { borderBottomColor: colors.border, borderTopColor: colors.border, borderTopWidth: 1 }]}>
                <Text style={[styles.fieldsHeaderText, { color: colors.textSecondary }]}>
                  Relations
                </Text>
                <View style={styles.fieldCountBadge}>
                  <Text style={[styles.fieldCount, { color: colors.textSecondary }]}>
                    {table.relations.length}
                  </Text>
                </View>
              </View>
              
              <ScrollView style={styles.relationsScrollView} showsVerticalScrollIndicator={false}>
                {table.relations.map((relation, index) => (
                  <View 
                    key={index}
                    style={[
                      styles.relationItem,
                      index !== table.relations.length - 1 && { borderBottomColor: colors.border, borderBottomWidth: 0.5 }
                    ]}
                  >
                    <View style={styles.relationHeader}>
                      <Text style={[styles.relationField, { color: colors.text }]}>
                        {relation.field}
                      </Text>
                      <View style={[styles.relationTypeTag, { backgroundColor: colors.primaryLight }]}>
                        <Text style={[styles.relationTypeText, { color: colors.primary }]}>
                          {relation.onDelete || 'FK'}
                        </Text>
                      </View>
                    </View>
                    <View style={styles.relationTarget}>
                      <Text style={[styles.relationText, { color: colors.textSecondary }]}>
                        References{' '}
                        <Text style={{ color: colors.primary, fontWeight: '600' }}>{relation.referencedTable}</Text>
                        .{relation.referencedField}
                      </Text>
                    </View>
                  </View>
                ))}
              </ScrollView>
            </>
          )}
          
          {table.hasRLS && (
            <View style={[styles.rlsContainer, { backgroundColor: colors.primaryLight }]}>
              <Feather name="lock" size={14} color={colors.primary} style={{ marginRight: 6 }} />
              <Text style={[styles.rlsInfoText, { color: colors.primary }]}>
                Row Level Security is enabled on this table
              </Text>
            </View>
          )}
        </Animated.View>
      </TouchableOpacity>
    </Animated.View>
  );
};

export function SchemaVisualization({ schema }: { schema: any }) {
  const { colors, isDark } = useTheme();
  const [selectedTable, setSelectedTable] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'fields'>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [filterRLS, setFilterRLS] = useState(false);
  
  const fadeAnim = useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true
    }).start();
  }, []);
  
  // Process schema data to format needed by the visualization
  const tables = Object.keys(schema.tables).map(tableName => {
    const table = schema.tables[tableName];
    
    // Extract fields
    const fields = table.columns.map(column => ({
      name: column.name,
      type: column.type,
      isPrimary: table.constraints.some(c => c.type === 'p' && c.definition.includes(column.name)),
      isForeign: table.foreign_keys.some(fk => fk.definition.includes(`FOREIGN KEY (${column.name})`)),
      isNullable: column.is_nullable,
      hasDefault: column.has_default,
      defaultValue: column.default
    }));
    
    // Extract relations
    const relations = table.foreign_keys.map(fk => {
      // Extract table and field info from definition string
      const matches = fk.definition.match(/FOREIGN KEY \(([^)]+)\) REFERENCES ([^(]+)\(([^)]+)\)( ON DELETE ([A-Z]+))?/);
      if (matches) {
        return {
          table: tableName,
          field: matches[1].trim(),
          referencedTable: matches[2].trim(),
          referencedField: matches[3].trim(),
          onDelete: matches[5] || undefined
        };
      }
      return null;
    }).filter(Boolean);
    
    return {
      name: tableName,
      fields,
      relations,
      hasRLS: table.is_rls_enabled
    };
  });
  
  // Filter tables based on search query and RLS filter
  const filteredTables = tables.filter(table => {
    const matchesSearch = table.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                        table.fields.some(field => field.name.toLowerCase().includes(searchQuery.toLowerCase()));
                        
    const matchesRLSFilter = !filterRLS || table.hasRLS;
    
    return matchesSearch && matchesRLSFilter;
  });
  
  // Sort tables
  const sortedTables = [...filteredTables].sort((a, b) => {
    if (sortBy === 'name') {
      return sortDirection === 'asc' 
        ? a.name.localeCompare(b.name)
        : b.name.localeCompare(a.name);
    } else {
      return sortDirection === 'asc'
        ? a.fields.length - b.fields.length
        : b.fields.length - a.fields.length;
    }
  });
  
  const toggleSort = (field: 'name' | 'fields') => {
    if (sortBy === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortDirection('asc');
    }
  };
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Feather name="database" size={24} color={colors.primary} style={styles.titleIcon} />
          <Text style={[styles.title, { color: colors.text }]}>Database Schema</Text>
        </View>
        <TouchableOpacity 
          style={[styles.settingsButton, { backgroundColor: colors.subtle }]}
          activeOpacity={0.7}
        >
          <Feather name="settings" size={20} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
      
      <View style={[styles.statsContainer, { backgroundColor: colors.card, borderColor: colors.border }]}>
        <View style={styles.statItem}>
          <Text style={[styles.statValue, { color: colors.text }]}>{tables.length}</Text>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Tables</Text>
        </View>
        <View style={[styles.statDivider, { backgroundColor: colors.border }]} />
        <View style={styles.statItem}>
          <Text style={[styles.statValue, { color: colors.text }]}>
            {tables.reduce((sum, table) => sum + table.fields.length, 0)}
          </Text>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Columns</Text>
        </View>
        <View style={[styles.statDivider, { backgroundColor: colors.border }]} />
        <View style={styles.statItem}>
          <Text style={[styles.statValue, { color: colors.text }]}>
            {tables.filter(table => table.hasRLS).length}
          </Text>
          <Text style={[styles.statLabel, { color: colors.textSecondary }]}>RLS Tables</Text>
        </View>
      </View>
      
      <View style={styles.toolbarContainer}>
        <View style={[styles.searchBar, { 
          backgroundColor: colors.subtle,
          borderColor: colors.border
        }]}>
          <Feather name="search" size={16} color={colors.textSecondary} style={styles.searchIcon} />
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="Search tables or fields..."
            placeholderTextColor={colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery !== '' && (
            <TouchableOpacity 
              style={styles.clearSearch} 
              onPress={() => setSearchQuery('')}
            >
              <View style={[styles.clearButton, { backgroundColor: colors.border }]}>
                <Text style={{ color: colors.textSecondary, fontSize: 10 }}>✕</Text>
              </View>
            </TouchableOpacity>
          )}
        </View>
        
        <View style={styles.toolbarButtons}>
          <TouchableOpacity 
            style={[styles.toolbarButton, filterRLS && { backgroundColor: colors.primaryLight }]}
            onPress={() => setFilterRLS(!filterRLS)}
          >
            <Feather name="filter" size={18} color={filterRLS ? colors.primary : colors.textSecondary} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.toolbarButton}
            onPress={() => toggleSort('name')}
          >
            {sortBy === 'name' ? (
              sortDirection === 'asc' ? (
                <SortAsc size={18} color={colors.primary} />
              ) : (
                <SortDesc size={18} color={colors.primary} />
              )
            ) : (
              <SortAsc size={18} color={colors.textSecondary} />
            )}
          </TouchableOpacity>
        </View>
      </View>
      
      {filterRLS && (
        <View style={[styles.filterBadge, { backgroundColor: colors.primaryLight }]}>
          <Feather name="shield" size={14} color={colors.primary} />
          <Text style={[styles.filterText, { color: colors.primary }]}>
            Showing only tables with RLS enabled
          </Text>
          <TouchableOpacity onPress={() => setFilterRLS(false)}>
            <View style={[styles.clearFilterButton, { backgroundColor: colors.primary }]}>
              <Text style={{ color: 'white', fontSize: 10 }}>✕</Text>
            </View>
          </TouchableOpacity>
        </View>
      )}
      
      <Animated.View style={{
        flex: 1,
        opacity: fadeAnim
      }}>
        <ScrollView 
          style={styles.tablesContainer}
          contentContainerStyle={styles.tablesContent}
          showsVerticalScrollIndicator={false}
        >
          {sortedTables.length > 0 ? (
            sortedTables.map(table => (
              <TableCard 
                key={table.name}
                table={table}
                onSelect={() => setSelectedTable(table.name === selectedTable ? null : table.name)}
                isSelected={table.name === selectedTable}
              />
            ))
          ) : (
            <View style={[styles.emptyState, { borderColor: colors.border }]}>
              <Feather name="database" size={36} color={colors.textSecondary} style={{ marginBottom: 12 }} />
              <Text style={[styles.emptyStateTitle, { color: colors.text }]}>
                No tables found
              </Text>
              <Text style={[styles.emptyStateMessage, { color: colors.textSecondary }]}>
                {searchQuery
                  ? `No tables matching "${searchQuery}" were found.`
                  : filterRLS
                    ? "No tables with Row Level Security enabled."
                    : "No tables available in the current schema."
                }
              </Text>
              {(searchQuery || filterRLS) && (
                <TouchableOpacity 
                  style={[styles.resetButton, { backgroundColor: colors.primaryLight }]}
                  onPress={() => {
                    setSearchQuery('');
                    setFilterRLS(false);
                  }}
                >
                  <Text style={[styles.resetButtonText, { color: colors.primary }]}>
                    Reset Filters
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          )}
        </ScrollView>
      </Animated.View>
      
      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <View style={[styles.keyIconContainer, { backgroundColor: '#FEF3C7' }]}>
            <KeyRound size={12} color="#F59E0B" />
          </View>
          <Text style={[styles.legendText, { color: colors.textSecondary }]}>Primary Key</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.keyIconContainer, { backgroundColor: '#DBEAFE' }]}>
            <KeySquare size={12} color="#3B82F6" />
          </View>
          <Text style={[styles.legendText, { color: colors.textSecondary }]}>Foreign Key</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.rlsBadge, { backgroundColor: colors.primaryLight, padding: 3 }]}>
            <Feather name="shield" size={10} color={colors.primary} />
            <Text style={[styles.rlsText, { color: colors.primary, fontSize: 8 }]}>RLS</Text>
          </View>
          <Text style={[styles.legendText, { color: colors.textSecondary }]}>Row Level Security</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 10 : 10,
    paddingBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleIcon: {
    marginRight: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginBottom: 20,
    padding: 16,
    borderRadius: 16,
    borderWidth: 1,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  statDivider: {
    width: 1,
    height: 36,
    marginHorizontal: 10,
  },
  toolbarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: 12,
    height: 48,
    borderRadius: 12,
    borderWidth: 1,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 16,
  },
  clearSearch: {
    padding: 4,
  },
  clearButton: {
    width: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
  },
  toolbarButtons: {
    flexDirection: 'row',
    marginLeft: 12,
  },
  toolbarButton: {
    width: 44,
    height: 44,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
    marginLeft: 8,
  },
  filterBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 12,
    padding: 10,
    borderRadius: 10,
  },
  filterText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  clearFilterButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tablesContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  tablesContent: {
    paddingBottom: 100,
  },
  tableCard: {
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  tableHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  tableNameContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  tableIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  tableName: {
    fontSize: 16,
  },
  tableDetails: {
    fontSize: 12,
    marginTop: 2,
  },
  tableDetailsContainer: {
    overflow: 'hidden',
  },
  rlsBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  rlsText: {
    fontSize: 10,
    fontWeight: '700',
    marginLeft: 4,
  },
  expandButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fieldsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  fieldsHeaderText: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  fieldCountBadge: {
    backgroundColor: 'rgba(0,0,0,0.05)',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
  },
  fieldCount: {
    fontSize: 11,
    fontWeight: '600',
  },
  fieldsScrollView: {
    maxHeight: 220,
  },
  relationsScrollView: {
    maxHeight: 150,
  },
  fieldItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  fieldNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  keyIcons: {
    width: 20,
    marginRight: 8,
  },
  keyIconContainer: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderIcon: {
    width: 20,
    height: 20,
  },
  fieldName: {
    fontSize: 14,
  },
  fieldTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeTag: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 6,
  },
  fieldType: {
    fontSize: 11,
    fontWeight: '500',
  },
  nullableTag: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  nullableText: {
    fontSize: 10,
    fontWeight: '500',
  },
  relationItem: {
    padding: 12,
  },
  relationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  relationField: {
    fontSize: 14,
    fontWeight: '600',
  },
  relationTypeTag: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  relationTypeText: {
    fontSize: 10,
    fontWeight: '700',
  },
  relationTarget: {
    marginLeft: 4,
  },
  relationText: {
    fontSize: 13,
    lineHeight: 18,
  },
  onDeleteTag: {
    fontSize: 10,
    marginTop: 4,
    fontStyle: 'italic',
  },
  rlsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    margin: 16,
  },
  rlsInfoText: {
    fontSize: 12,
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: 16,
    marginVertical: 20,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 8,
  },
  emptyStateMessage: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
    lineHeight: 20,
  },
  resetButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  resetButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 12,
    marginBottom: Platform.OS === 'ios' ? 20 : 10,
    flexWrap: 'wrap',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
    marginBottom: 8,
  },
  legendText: {
    fontSize: 12,
    marginLeft: 4,
  }
});