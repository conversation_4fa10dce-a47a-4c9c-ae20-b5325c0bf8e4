import React, { useEffect, useState, useRef } from 'react';
import { View, StyleSheet, ViewStyle, StyleProp, ScrollView } from 'react-native';
import SkeletonLoader from './SkeletonLoader';
import { useTheme } from '../contexts/ThemeContext';

interface ScreenSkeletonProps {
  /**
   * Whether to include a header section
   */
  hasHeader?: boolean;
  
  /**
   * Configuration for the header
   */
  headerConfig?: {
    height?: number;
    hasTitle?: boolean;
    hasSubtitle?: boolean;
    hasIcon?: boolean;
  };
  
  /**
   * Array of section configurations
   */
  sections?: {
    type: 'card' | 'list' | 'grid' | 'text';
    height?: number;
    itemCount?: number;
  }[];
  
  /**
   * Whether to allow scrolling
   */
  scrollable?: boolean;
  
  /**
   * Whether to include a bottom action area
   */
  hasBottomAction?: boolean;
  
  /**
   * Configuration for the bottom action
   */
  bottomActionConfig?: {
    height?: number;
    buttonCount?: number;
  };
  
  /**
   * Custom styles for the container
   */
  style?: StyleProp<ViewStyle>;
  
  /**
   * Whether the content is loading
   */
  isLoading?: boolean;
  
  /**
   * Maximum time to show skeleton (in ms)
   */
  maxLoadingTime?: number;
}

/**
 * A screen skeleton component that renders a placeholder screen
 * with configurable header, sections, and bottom actions.
 */
export function ScreenSkeleton({
  hasHeader = true,
  headerConfig = {
    height: 60,
    hasTitle: true,
    hasSubtitle: false,
    hasIcon: true,
  },
  sections = [
    { type: 'card', height: 200, itemCount: 1 },
    { type: 'list', height: 300, itemCount: 3 },
  ],
  scrollable = true,
  hasBottomAction = false,
  bottomActionConfig = {
    height: 80,
    buttonCount: 1,
  },
  style,
  isLoading = true,
  maxLoadingTime = 10000, // Default maximum loading time: 10 seconds
}: ScreenSkeletonProps) {
  const { colors, isDark } = useTheme();
  const [showSkeleton, setShowSkeleton] = useState(isLoading);
  const timeoutRef = useRef<number | null>(null);
  
  // Set up loading timeout
  useEffect(() => {
    // Update showSkeleton when isLoading changes
    setShowSkeleton(isLoading);
    
    // If loading, set a timeout to stop showing skeleton after maxLoadingTime
    if (isLoading) {
      console.debug('ScreenSkeleton: Starting loading timeout of', maxLoadingTime, 'ms');
      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      // Set new timeout
      timeoutRef.current = setTimeout(() => {
        console.debug('ScreenSkeleton: Loading timeout reached, hiding skeleton');
        setShowSkeleton(false);
      }, maxLoadingTime) as unknown as number;
    } else if (timeoutRef.current) {
      // Clear timeout if no longer loading
      clearTimeout(timeoutRef.current);
    }
    
    // Cleanup on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isLoading, maxLoadingTime]);
  
  // If not loading, don't render
  if (!showSkeleton) {
    return null;
  }

  // Make sure sections is an array (fallback if not provided)
  const sectionsToRender = sections || [];
  
  const screenContent = (
    <>
      {/* Header section */}
      {hasHeader && (
        <View style={[
          styles.header,
          { height: headerConfig.height, backgroundColor: colors.card }
        ]}>
          {headerConfig.hasIcon && (
            <SkeletonLoader
              variant="circle"
              width={32}
              height={32}
              style={styles.headerIcon}
            />
          )}
          
          <View style={styles.headerContent}>
            {headerConfig.hasTitle && (
              <SkeletonLoader
                variant="text"
                width={150}
                height={20}
                style={styles.headerTitle}
              />
            )}
            
            {headerConfig.hasSubtitle && (
              <SkeletonLoader
                variant="text"
                width={100}
                height={16}
              />
            )}
          </View>
          
          <SkeletonLoader
            variant="circle"
            width={32}
            height={32}
          />
        </View>
      )}
      
      {/* Content sections */}
      {sectionsToRender.map((section, index) => (
        <View
          key={`section-${index}`}
          style={[
            styles.section,
            { minHeight: section.height, marginBottom: 16 }
          ]}
        >
          {/* Section header */}
          <View style={styles.sectionHeader}>
            <SkeletonLoader
              variant="text"
              width={120}
              height={18}
            />
            <SkeletonLoader
              variant="text"
              width={60}
              height={14}
              style={styles.seeAll}
            />
          </View>
          
          {/* Section content */}
          <View style={styles.sectionContent}>
            {section.type === 'card' && (
              <SkeletonLoader
                variant="rectangle"
                height={section.height ? section.height - 50 : 150}
                style={styles.card}
              />
            )}
            
            {section.type === 'list' && (
              <View style={styles.list}>
                {Array.from({ length: section.itemCount || 3 }).map((_, itemIndex) => (
                  <View
                    key={`list-item-${index}-${itemIndex}`}
                    style={[
                      styles.listItem,
                      { borderBottomColor: colors.border },
                      itemIndex === (section.itemCount || 3) - 1 && styles.lastListItem,
                    ]}
                  >
                    <SkeletonLoader
                      variant="circle"
                      width={40}
                      height={40}
                      style={styles.listItemIcon}
                    />
                    <View style={styles.listItemContent}>
                      <SkeletonLoader
                        variant="text"
                        width="70%"
                        height={16}
                        style={styles.listItemTitle}
                      />
                      <SkeletonLoader
                        variant="text"
                        width="50%"
                        height={14}
                      />
                    </View>
                  </View>
                ))}
              </View>
            )}
            
            {section.type === 'grid' && (
              <View style={styles.grid}>
                {Array.from({ length: section.itemCount || 4 }).map((_, itemIndex) => (
                  <SkeletonLoader
                    key={`grid-item-${index}-${itemIndex}`}
                    variant="rectangle"
                    width="48%"
                    height={120}
                    style={styles.gridItem}
                  />
                ))}
              </View>
            )}
            
            {section.type === 'text' && (
              <View style={styles.textContent}>
                <SkeletonLoader
                  variant="text"
                  width="100%"
                  height={16}
                  style={styles.textLine}
                />
                <SkeletonLoader
                  variant="text"
                  width="90%"
                  height={16}
                  style={styles.textLine}
                />
                <SkeletonLoader
                  variant="text"
                  width="95%"
                  height={16}
                  style={styles.textLine}
                />
                <SkeletonLoader
                  variant="text"
                  width="85%"
                  height={16}
                  style={styles.textLine}
                />
              </View>
            )}
          </View>
        </View>
      ))}
      
      {/* Fallback if no sections are provided */}
      {sectionsToRender.length === 0 && (
        <View style={styles.fallbackSection}>
          <SkeletonLoader
            variant="rectangle"
            height={200}
            style={styles.card}
          />
        </View>
      )}
    </>
  );
  
  return (
    <View style={[styles.container, style, {backgroundColor: colors.background}]}>
      {scrollable ? (
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {screenContent}
        </ScrollView>
      ) : (
        <View style={styles.fixedContent}>
          {screenContent}
        </View>
      )}
      
      {/* Bottom action area */}
      {hasBottomAction && (
        <View style={[
          styles.bottomAction,
          { height: bottomActionConfig.height, backgroundColor: colors.card }
        ]}>
          <View style={styles.bottomActionContent}>
            {Array.from({ length: bottomActionConfig.buttonCount || 1 }).map((_, index) => (
              <SkeletonLoader
                key={`action-button-${index}`}
                variant="pill"
                width={index === 0 ? '100%' : '48%'}
                height={48}
                style={styles.actionButton}
              />
            ))}
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 16,
  },
  fixedContent: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  headerIcon: {
    marginRight: 12,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    marginBottom: 4,
  },
  section: {
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  seeAll: {
    alignSelf: 'flex-end',
  },
  sectionContent: {
    flex: 1,
  },
  card: {
    width: '100%',
    borderRadius: 12,
  },
  list: {
    width: '100%',
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  lastListItem: {
    borderBottomWidth: 0,
  },
  listItemIcon: {
    marginRight: 12,
  },
  listItemContent: {
    flex: 1,
  },
  listItemTitle: {
    marginBottom: 4,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  gridItem: {
    marginBottom: 12,
    borderRadius: 8,
  },
  textContent: {
    width: '100%',
  },
  textLine: {
    marginBottom: 8,
  },
  bottomAction: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  bottomActionContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
  },
  fallbackSection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ScreenSkeleton; 