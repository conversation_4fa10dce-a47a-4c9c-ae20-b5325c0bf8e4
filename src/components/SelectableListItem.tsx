import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Platform,
  StyleProp,
  ViewStyle,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useBulkEdit } from './BulkEditProvider';
import { Feather } from '@expo/vector-icons';

interface SelectableListItemProps<T> {
  /**
   * The item data
   */
  item: T;
  
  /**
   * Callback when item is pressed in normal mode
   */
  onPress?: () => void;
  
  /**
   * Callback when item is long pressed
   */
  onLongPress?: () => void;
  
  /**
   * Children to render
   */
  children: React.ReactNode;
  
  /**
   * Style for the container
   */
  style?: StyleProp<ViewStyle>;
  
  /**
   * Style for the selectable container
   */
  selectableContainerStyle?: StyleProp<ViewStyle>;
  
  /**
   * Whether to allow selecting this item
   */
  selectable?: boolean;
  
  /**
   * Custom checkbox component
   */
  checkboxComponent?: React.ReactNode;
  
  /**
   * Whether to show the selection indicator even when not in bulk edit mode
   */
  alwaysShowIndicator?: boolean;
}

/**
 * A list item component that can be selected in bulk edit mode
 */
export function SelectableListItem<T>({
  item,
  onPress,
  onLongPress,
  children,
  style,
  selectableContainerStyle,
  selectable = true,
  checkboxComponent,
  alwaysShowIndicator = false,
}: SelectableListItemProps<T>) {
  const { colors, isDark } = useTheme();
  const { isActive, toggleItemSelection, isItemSelected } = useBulkEdit<T>();
  
  // Check if this item is selected
  const selected = isItemSelected(item);
  
  // Handle item press
  const handlePress = () => {
    if (isActive && selectable) {
      // In bulk edit mode, toggle selection
      toggleItemSelection(item);
    } else {
      // In normal mode, call onPress
      onPress?.();
    }
  };
  
  // Handle long press to enter bulk edit mode
  const handleLongPress = () => {
    if (onLongPress) {
      // If a custom long press handler is provided, use it
      onLongPress();
    } else if (selectable) {
      // Otherwise, toggle selection (which will activate bulk edit mode if needed)
      toggleItemSelection(item);
    }
  };
  
  // Determine if we should show the selection indicator
  const showIndicator = (isActive || alwaysShowIndicator) && selectable;
  
  return (
    <TouchableOpacity
      style={[
        styles.container,
        style,
      ]}
      onPress={handlePress}
      onLongPress={handleLongPress}
      activeOpacity={0.7}
      delayLongPress={300}
    >
      {/* Selection indicator */}
      {showIndicator && (
        <View
          style={[
            styles.selectableContainer,
            {
              backgroundColor: selected 
                ? colors.primary
                : isDark 
                  ? 'rgba(255, 255, 255, 0.1)' 
                  : 'rgba(0, 0, 0, 0.05)',
            },
            selectableContainerStyle,
          ]}
        >
          {checkboxComponent || (
            selected && <Feather name="check" size={16}  color={colors.text} />
          )}
        </View>
      )}
      
      {/* Custom children components */}
      <View
        style={[
          styles.content,
          showIndicator && styles.contentWithIndicator,
        ]}
      >
        {children}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
  selectableContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  contentWithIndicator: {
    marginLeft: 0,
  },
});

export default SelectableListItem; 