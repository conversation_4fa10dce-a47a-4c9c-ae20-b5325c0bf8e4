import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  ViewStyle,
  StatusBar,
  StyleProp,
  TextStyle,
} from 'react-native';
import { useRouter, usePathname } from 'expo-router';
import { useTheme } from '../contexts/ThemeContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Feather } from '@expo/vector-icons';
import NavigationBreadcrumb from './NavigationBreadcrumb';
import { useNavigationGuard } from './NavigationGuard';
import { BlurView } from 'expo-blur';

interface SharedHeaderProps {
  /**
   * Title to display in the header
   */
  title?: string;
  
  /**
   * Subtitle to display below the title
   */
  subtitle?: string;
  
  /**
   * Whether to show a back button
   */
  showBackButton?: boolean;
  
  /**
   * Whether to show a home button
   */
  showHomeButton?: boolean;
  
  /**
   * Whether to show breadcrumbs for navigation context
   */
  showBreadcrumbs?: boolean;
  
  /**
   * Whether to show the search button
   */
  showSearchButton?: boolean;
  
  /**
   * Whether to show the more options button
   */
  showMoreButton?: boolean;
  
  /**
   * Handler for when the back button is pressed
   */
  onBackPress?: () => void;
  
  /**
   * Handler for when the home button is pressed
   */
  onHomePress?: () => void;
  
  /**
   * Handler for when the search button is pressed
   */
  onSearchPress?: () => void;
  
  /**
   * Handler for when the more button is pressed
   */
  onMorePress?: () => void;
  
  /**
   * Optional right component to render in the header
   */
  rightComponent?: React.ReactNode;
  
  /**
   * Optional center component to render in the header (replaces title)
   */
  centerComponent?: React.ReactNode;
  
  /**
   * Custom style for the header container
   */
  style?: StyleProp<ViewStyle>;
  
  /**
   * Custom style for the title text
   */
  titleStyle?: StyleProp<TextStyle>;
  
  /**
   * Whether to use a blur effect for the header background
   */
  blurBackground?: boolean;
  
  /**
   * Whether the header is translucent
   */
  translucent?: boolean;
  
  /**
   * Whether to show a border at the bottom of the header
   */
  showBorder?: boolean;
  
  /**
   * Whether to show the status bar
   */
  hideStatusBar?: boolean;
}

/**
 * A shared header component that provides a consistent header across the app,
 * with navigation context via breadcrumbs, back/home buttons, and other actions.
 */
export function SharedHeader({
  title,
  subtitle,
  showBackButton = true,
  showHomeButton = false,
  showBreadcrumbs = true,
  showSearchButton = false,
  showMoreButton = false,
  onBackPress,
  onHomePress,
  onSearchPress,
  onMorePress,
  rightComponent,
  centerComponent,
  style,
  titleStyle,
  blurBackground = false,
  translucent = false,
  showBorder = true,
  hideStatusBar = false,
}: SharedHeaderProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { colors, isDark } = useTheme();
  const insets = useSafeAreaInsets();
  const { currentDepth, history, goBack } = useNavigationGuard();
  
  // Handle back button press
  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      goBack();
    }
  };
  
  // Handle home button press
  const handleHomePress = () => {
    if (onHomePress) {
      onHomePress();
    } else {
      router.push('/');
    }
  };
  
  // Determine if we should show the back button based on navigation depth
  const shouldShowBackButton = showBackButton && (history.length > 1 || currentDepth > 0);
  
  // Header content - varies based on provided props
  const renderHeaderContent = () => {
    // If a custom center component is provided, render it
    if (centerComponent) {
      return centerComponent;
    }
    
    // Otherwise, render title and optional breadcrumbs
    return (
      <View style={styles.titleContainer}>
        {title ? (
          <>
            <View style={styles.titleWrapper}>
              {!shouldShowBackButton && !showHomeButton && (
                <Feather name="heart" size={18} color={colors.primary} style={styles.logoIcon} />
              )}
              <Text
                style={[
                  styles.title,
                  { color: colors.text },
                  titleStyle
                ]}
                numberOfLines={1}
              >
                {title}
              </Text>
            </View>
          </>
        ) : (
          <View style={styles.titleWrapper}>
            <Feather name="heart" size={18} color={colors.primary} style={styles.logoIcon} />
            <Text
              style={[
                styles.title,
                { color: colors.text },
                titleStyle
              ]}
              numberOfLines={1}
            >
              Choose Healthy
            </Text>
          </View>
        )}
        
        {subtitle && (
          <Text
            style={[
              styles.subtitle,
              { color: colors.textSecondary }
            ]}
            numberOfLines={1}
          >
            {subtitle}
          </Text>
        )}
        
        {/* Show breadcrumbs for navigation context if enabled */}
        {showBreadcrumbs && currentDepth > 0 && (
          <NavigationBreadcrumb
            maxItems={3}
            showHome={false}
            style={styles.breadcrumbs}
          />
        )}
      </View>
    );
  };
  
  // Render right side buttons/components
  const renderRightComponents = () => {
    // If a custom right component is provided, render it
    if (rightComponent) {
      return rightComponent;
    }
    
    return (
      <View style={styles.rightContainer}>
        {/* Search button */}
        {showSearchButton && (
          <TouchableOpacity
            style={styles.headerButton}
            onPress={onSearchPress}
            accessibilityLabel="Search"
            accessibilityRole="button"
          >
            <Feather name="search" size={22} color={colors.text} />
          </TouchableOpacity>
        )}
        
        {/* More options button */}
        {showMoreButton && (
          <TouchableOpacity
            style={styles.headerButton}
            onPress={onMorePress}
            accessibilityLabel="More options"
            accessibilityRole="button"
          >
            <Feather name="more-vertical" size={22} color={colors.text} />
          </TouchableOpacity>
        )}
      </View>
    );
  };
  
  // Main header component
  const renderHeader = () => (
    <View
      style={[
        styles.headerContent,
        { 
          paddingTop: translucent ? insets.top : 0,
          borderBottomColor: colors.border,
          borderBottomWidth: showBorder ? StyleSheet.hairlineWidth : 0,
        },
      ]}
    >
      {/* Left side - Back button or Home button */}
      <View style={styles.leftContainer}>
        {shouldShowBackButton && (
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleBackPress}
            accessibilityLabel="Back"
            accessibilityRole="button"
          >
            <Feather name="arrow-left" size={22} color={colors.text} />
          </TouchableOpacity>
        )}
        
        {showHomeButton && !shouldShowBackButton && (
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleHomePress}
            accessibilityLabel="Home"
            accessibilityRole="button"
          >
            <Feather name="home" size={22} color={colors.text} />
          </TouchableOpacity>
        )}
      </View>
      
      {/* Center - Title and/or breadcrumbs */}
      {renderHeaderContent()}
      
      {/* Right side - Action buttons */}
      {renderRightComponents()}
    </View>
  );
  
  // Specify header background based on the blurBackground prop
  if (blurBackground) {
    return (
      <View
        style={[
          styles.container,
          { minHeight: translucent ? 44 + insets.top : 44 },
          style,
        ]}
      >
        {!hideStatusBar && (
          <StatusBar
            translucent={translucent}
            backgroundColor="transparent"
            barStyle={isDark ? 'light-content' : 'dark-content'}
          />
        )}
        <BlurView
          tint={isDark ? 'dark' : 'light'}
          intensity={80}
          style={[
            StyleSheet.absoluteFill,
            translucent && { paddingTop: insets.top },
          ]}
        />
        {renderHeader()}
      </View>
    );
  }
  
  return (
    <View
      style={[
        styles.container,
        { 
          backgroundColor: colors.background,
          minHeight: translucent ? 44 + insets.top : 44 
        },
        style,
      ]}
    >
      {!hideStatusBar && (
        <StatusBar
          translucent={translucent}
          backgroundColor={translucent ? 'transparent' : colors.background}
          barStyle={isDark ? 'light-content' : 'dark-content'}
        />
      )}
      {renderHeader()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    zIndex: 10,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  headerContent: {
    height: 56,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  leftContainer: {
    flex: 0.2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  titleContainer: {
    flex: 0.6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  titleWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoIcon: {
    marginRight: 8,
  },
  rightContainer: {
    flex: 0.2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 2,
  },
  breadcrumbs: {
    marginTop: 4,
    paddingHorizontal: 0,
  },
});

export default SharedHeader; 