import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { contextualNavigation, ShortcutSuggestion } from '@/utils/contextualNavigation';

interface ShortcutSuggestionsProps {
  maxSuggestions?: number;
  onShortcutPress?: (shortcut: ShortcutSuggestion) => void;
}

export function ShortcutSuggestions({ 
  maxSuggestions = 3,
  onShortcutPress
}: ShortcutSuggestionsProps) {
  const { colors, isDark } = useTheme();
  const [shortcuts, setShortcuts] = useState<ShortcutSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // Load shortcut suggestions on mount
  useEffect(() => {
    loadShortcuts();
  }, []);
  
  // Load suggestions from contextual navigation
  const loadShortcuts = async () => {
    setIsLoading(true);
    try {
      // Get dynamic suggestions based on usage patterns
      const dynamicShortcuts = contextualNavigation.getShortcutSuggestions(maxSuggestions);
      
      // Get any saved custom shortcuts
      const customShortcuts = await contextualNavigation.getCustomShortcuts();
      
      // Combine shortcuts, prioritizing custom ones
      const customShortcutScreens = new Set(customShortcuts.map(s => s.screen));
      const allShortcuts = [
        ...customShortcuts,
        ...dynamicShortcuts.filter(s => !customShortcutScreens.has(s.screen))
      ].slice(0, maxSuggestions);
      
      setShortcuts(allShortcuts);
    } catch (error) {
      console.error('Failed to load shortcuts:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle shortcut press
  const handleShortcutPress = (shortcut: ShortcutSuggestion) => {
    // Navigate to the screen
    router.push(shortcut.screen as any);
    
    // Call the callback if provided
    if (onShortcutPress) {
      onShortcutPress(shortcut);
    }
  };
  
  // If no shortcuts and not loading, don't render anything
  if (!isLoading && shortcuts.length === 0) {
    return null;
  }
  
  return (
    <View style={styles.container}>
      <View style={[styles.header, { borderBottomColor: isDark ? '#333' : '#eee' }]}>
        <Text style={[styles.title, { color: colors.text }]}>
          Suggested For You
        </Text>
        <TouchableOpacity 
          onPress={loadShortcuts}
          style={styles.refreshButton}
        >
          <Ionicons name="refresh" size={18} color={colors.primary} />
        </TouchableOpacity>
      </View>
      
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator color={colors.primary} size="small" />
        </View>
      ) : (
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.shortcutsContainer}
        >
          {shortcuts.map((shortcut, index) => (
            <TouchableOpacity
              key={`${shortcut.screen}-${index}`}
              style={[
                styles.shortcutButton,
                { 
                  backgroundColor: isDark ? '#333' : '#f5f5f5',
                  borderColor: isDark ? '#444' : '#ddd'
                }
              ]}
              onPress={() => handleShortcutPress(shortcut)}
            >
              <Ionicons 
                name={shortcut.icon as any} 
                size={20} 
                color={colors.primary} 
                style={styles.shortcutIcon} 
              />
              <Text style={[styles.shortcutText, { color: colors.text }]}>
                {shortcut.title}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 8,
    marginBottom: 8,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  refreshButton: {
    padding: 4,
  },
  loadingContainer: {
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  shortcutsContainer: {
    paddingHorizontal: 12,
  },
  shortcutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    marginHorizontal: 4,
    borderWidth: 1,
  },
  shortcutIcon: {
    marginRight: 8,
  },
  shortcutText: {
    fontWeight: '500',
    fontSize: 14,
  },
});

export default ShortcutSuggestions; 