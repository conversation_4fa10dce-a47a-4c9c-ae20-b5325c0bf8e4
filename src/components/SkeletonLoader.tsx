import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Animated,
  StyleSheet,
  ViewStyle,
  StyleProp,
  Easing,
  Platform,
  DimensionValue,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useAccessibility } from './AccessibilityProvider';

interface SkeletonLoaderProps {
  /**
   * Width of the skeleton element
   */
  width?: DimensionValue;
  
  /**
   * Height of the skeleton element
   */
  height?: DimensionValue;
  
  /**
   * Shape of the skeleton element
   */
  variant?: 'rectangle' | 'circle' | 'text' | 'pill';
  
  /**
   * Custom styles for the container
   */
  style?: StyleProp<ViewStyle>;
  
  /**
   * Whether the skeleton is currently loading
   */
  isLoading?: boolean;
  
  /**
   * Content to show when not loading
   */
  children?: React.ReactNode;
  
  /**
   * Border radius for the skeleton
   */
  borderRadius?: number;
  
  /**
   * Whether to show the animation
   */
  animate?: boolean;
  
  /**
   * Maximum time to show skeleton before hiding (ms)
   */
  maxLoadingTime?: number;
}

/**
 * A skeleton loader component that shows animated placeholders
 * while content is loading, providing a better user experience
 * than traditional loading spinners.
 */
export function SkeletonLoader({
  width,
  height,
  variant = 'rectangle',
  style,
  isLoading = true,
  children,
  borderRadius,
  animate = true,
  maxLoadingTime = 10000, // Default to 10 seconds
}: SkeletonLoaderProps) {
  const { colors, isDark } = useTheme();
  const { isReduceMotionEnabled } = useAccessibility();
  const [showSkeleton, setShowSkeleton] = useState(isLoading);
  
  // Animation value for the shimmer effect
  const shimmerAnimation = useRef(new Animated.Value(0)).current;
  const animationRef = useRef<Animated.CompositeAnimation | null>(null);
  const timeoutRef = useRef<number | null>(null);
  
  // Set up timeout to stop showing skeleton after maxLoadingTime
  useEffect(() => {
    // Update state based on isLoading prop
    setShowSkeleton(isLoading);
    
    if (isLoading) {
      // Set a timeout to force stop showing the skeleton
      timeoutRef.current = setTimeout(() => {
        setShowSkeleton(false);
      }, maxLoadingTime) as unknown as number;
    } else if (timeoutRef.current) {
      // Clear timeout if not loading
      clearTimeout(timeoutRef.current);
    }
    
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isLoading, maxLoadingTime]);
  
  // Start the animation when the component mounts
  useEffect(() => {
    // Clean up any previous animation
    if (animationRef.current) {
      animationRef.current.stop();
    }
    
    if (showSkeleton && animate && !isReduceMotionEnabled) {
      // Create a looping animation for the shimmer effect
      animationRef.current = Animated.loop(
        Animated.timing(shimmerAnimation, {
          toValue: 1,
          duration: 1500,
          easing: Easing.ease,
          useNativeDriver: Platform.OS !== 'web',
        })
      );
      
      animationRef.current.start();
    } else {
      // Reset animation when not loading
      shimmerAnimation.setValue(0);
    }
    
    // Cleanup animation when component unmounts or dependencies change
    return () => {
      if (animationRef.current) {
        animationRef.current.stop();
      }
    };
  }, [showSkeleton, animate, isReduceMotionEnabled, shimmerAnimation]);
  
  // Get the appropriate border radius based on variant
  const getBorderRadius = () => {
    if (borderRadius !== undefined) return borderRadius;
    
    switch (variant) {
      case 'circle':
        return 9999;
      case 'pill':
        return 9999;
      case 'text':
        return 4;
      case 'rectangle':
      default:
        return 8;
    }
  };
  
  // Calculate the appropriate height based on variant
  const getDefaultHeight = () => {
    switch (variant) {
      case 'text':
        return 16;
      case 'pill':
        return 24;
      case 'circle':
        return width || 48;
      case 'rectangle':
      default:
        return 48;
    }
  };
  
  // If not loading, render children
  if (!showSkeleton && children) {
    return <>{children}</>;
  }
  
  // Don't render anything if not loading and no children
  if (!showSkeleton && !children) {
    return null;
  }
  
  // Colors for the shimmer effect
  const baseColor = isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.06)';
  const highlightColor = isDark ? 'rgba(255, 255, 255, 0.14)' : 'rgba(0, 0, 0, 0.12)';
  
  // Interpolate the animation value to create the shimmer gradient
  const shimmerTranslate = shimmerAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [-300, 300],
  });
  
  return (
    <View
      style={[
        styles.container,
        {
          width: width,
          height: height || getDefaultHeight(),
          borderRadius: getBorderRadius(),
          backgroundColor: baseColor,
        },
        style,
      ]}
      accessibilityLabel="Loading content"
      accessibilityRole="progressbar"
    >
      {animate && !isReduceMotionEnabled && (
        <Animated.View
          style={[
            styles.shimmer,
            {
              transform: [{ translateX: shimmerTranslate }],
              backgroundColor: highlightColor,
            },
          ]}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    position: 'relative',
  },
  shimmer: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '60%',
    height: '100%',
    opacity: 0.5,
  },
});

export default SkeletonLoader; 