import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Switch,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Alert,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format, parse } from 'date-fns';
import { getSleepGoals, setSleepGoals, SleepGoals } from '@/services/sleepTrackingService';

// This is a simplified version that doesn't directly use the notification system
// due to some TypeScript compatibility issues
interface SleepReminderSettingsProps {
  onSave?: () => void;
  colors: {
    text: string;
    background: string;
    card: string;
    primary: string;
    textSecondary: string;
  };
}

export default function SleepReminderSettings({ onSave, colors }: SleepReminderSettingsProps) {
  const [loading, setLoading] = useState(true);
  const [sleepGoals, setSleepGoalsState] = useState<SleepGoals | null>(null);
  const [showBedTimePicker, setShowBedTimePicker] = useState(false);
  const [showWakeTimePicker, setShowWakeTimePicker] = useState(false);
  const [showReminderPicker, setShowReminderPicker] = useState(false);
  const [tempBedTime, setTempBedTime] = useState(new Date());
  const [tempWakeTime, setTempWakeTime] = useState(new Date());
  const [tempReminderMinutes, setTempReminderMinutes] = useState(30);

  // Load sleep goals
  useEffect(() => {
    loadSleepGoals();
  }, []);

  const loadSleepGoals = async () => {
    try {
      setLoading(true);
      const result = await getSleepGoals();
      if (result.success && result.data) {
        setSleepGoalsState(result.data);
        
        // Set temp times from the loaded goals
        if (result.data.bedTimeTarget) {
          const today = new Date();
          const timeString = `${format(today, 'yyyy-MM-dd')} ${result.data.bedTimeTarget}`;
          setTempBedTime(parse(timeString, 'yyyy-MM-dd HH:mm', new Date()));
        }
        
        if (result.data.wakeTimeTarget) {
          const today = new Date();
          const timeString = `${format(today, 'yyyy-MM-dd')} ${result.data.wakeTimeTarget}`;
          setTempWakeTime(parse(timeString, 'yyyy-MM-dd HH:mm', new Date()));
        }
        
        if (result.data.bedTimeReminderMinutes) {
          setTempReminderMinutes(result.data.bedTimeReminderMinutes);
        }
      }
    } catch (error) {
      console.error('Error loading sleep goals:', error);
      Alert.alert('Error', 'Failed to load sleep settings');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleReminders = async (enabled: boolean) => {
    if (!sleepGoals) return;
    
    try {
      const updatedGoals = {
        ...sleepGoals,
        remindersEnabled: enabled,
      };
      
      const result = await setSleepGoals(updatedGoals);
      if (result.success) {
        setSleepGoalsState(updatedGoals);
        if (onSave) onSave();
      }
    } catch (error) {
      console.error('Error toggling reminders:', error);
      Alert.alert('Error', 'Failed to update reminder settings');
    }
  };

  const handleSaveBedTime = async () => {
    if (!sleepGoals) return;
    
    try {
      const bedTimeStr = format(tempBedTime, 'HH:mm');
      
      const updatedGoals = {
        ...sleepGoals,
        bedTimeTarget: bedTimeStr,
      };
      
      const result = await setSleepGoals(updatedGoals);
      if (result.success) {
        setSleepGoalsState(updatedGoals);
        if (onSave) onSave();
      }
    } catch (error) {
      console.error('Error saving bed time:', error);
      Alert.alert('Error', 'Failed to update bed time');
    }
  };

  const handleSaveWakeTime = async () => {
    if (!sleepGoals) return;
    
    try {
      const wakeTimeStr = format(tempWakeTime, 'HH:mm');
      
      const updatedGoals = {
        ...sleepGoals,
        wakeTimeTarget: wakeTimeStr,
      };
      
      const result = await setSleepGoals(updatedGoals);
      if (result.success) {
        setSleepGoalsState(updatedGoals);
        if (onSave) onSave();
      }
    } catch (error) {
      console.error('Error saving wake time:', error);
      Alert.alert('Error', 'Failed to update wake time');
    }
  };

  const handleSaveReminderTime = async () => {
    if (!sleepGoals) return;
    
    try {
      const updatedGoals = {
        ...sleepGoals,
        bedTimeReminderMinutes: tempReminderMinutes,
      };
      
      const result = await setSleepGoals(updatedGoals);
      if (result.success) {
        setSleepGoalsState(updatedGoals);
        if (onSave) onSave();
      }
    } catch (error) {
      console.error('Error saving reminder time:', error);
      Alert.alert('Error', 'Failed to update reminder time');
    }
  };

  if (loading || !sleepGoals) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Text style={[styles.loadingText, { color: colors.text }]}>Loading sleep settings...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.card, { backgroundColor: colors.card }]}>
        <Text style={[styles.title, { color: colors.text }]}>Sleep Reminders</Text>
        
        <View style={styles.settingRow}>
          <View style={styles.settingLabelContainer}>
            <MaterialCommunityIcons name="bell" size={20} color={colors.primary} />
            <Text style={[styles.settingLabel, { color: colors.text }]}>Enable Reminders</Text>
          </View>
          <Switch
            value={sleepGoals.remindersEnabled}
            onValueChange={handleToggleReminders}
            trackColor={{ false: '#767577', true: colors.primary }}
            thumbColor={Platform.OS === 'ios' ? undefined : '#f4f3f4'}
          />
        </View>
        
        {sleepGoals.remindersEnabled && (
          <>
            <View style={styles.settingRow}>
              <View style={styles.settingLabelContainer}>
                <MaterialCommunityIcons name="sleep" size={20} color={colors.primary} />
                <Text style={[styles.settingLabel, { color: colors.text }]}>Bedtime</Text>
              </View>
              <TouchableOpacity
                style={[styles.timeButton, { backgroundColor: colors.background }]}
                onPress={() => setShowBedTimePicker(true)}
              >
                <Text style={[styles.timeText, { color: colors.text }]}>
                  {sleepGoals.bedTimeTarget}
                </Text>
              </TouchableOpacity>
              
              {showBedTimePicker && (
                <DateTimePicker
                  value={tempBedTime}
                  mode="time"
                  display="default"
                  onChange={(event, selectedDate) => {
                    setShowBedTimePicker(false);
                    if (selectedDate) {
                      setTempBedTime(selectedDate);
                      handleSaveBedTime();
                    }
                  }}
                />
              )}
            </View>
            
            <View style={styles.settingRow}>
              <View style={styles.settingLabelContainer}>
                <MaterialCommunityIcons name="weather-sunny" size={20} color={colors.primary} />
                <Text style={[styles.settingLabel, { color: colors.text }]}>Wake Time</Text>
              </View>
              <TouchableOpacity
                style={[styles.timeButton, { backgroundColor: colors.background }]}
                onPress={() => setShowWakeTimePicker(true)}
              >
                <Text style={[styles.timeText, { color: colors.text }]}>
                  {sleepGoals.wakeTimeTarget}
                </Text>
              </TouchableOpacity>
              
              {showWakeTimePicker && (
                <DateTimePicker
                  value={tempWakeTime}
                  mode="time"
                  display="default"
                  onChange={(event, selectedDate) => {
                    setShowWakeTimePicker(false);
                    if (selectedDate) {
                      setTempWakeTime(selectedDate);
                      handleSaveWakeTime();
                    }
                  }}
                />
              )}
            </View>
            
            <View style={styles.settingRow}>
              <View style={styles.settingLabelContainer}>
                <MaterialCommunityIcons name="clock-time-five-outline" size={20} color={colors.primary} />
                <Text style={[styles.settingLabel, { color: colors.text }]}>Reminder Before Bed</Text>
              </View>
              <TouchableOpacity
                style={[styles.timeButton, { backgroundColor: colors.background }]}
                onPress={() => {
                  Alert.alert(
                    'Reminder Time',
                    'How many minutes before bedtime?',
                    [
                      { text: '15 minutes', onPress: () => { setTempReminderMinutes(15); handleSaveReminderTime(); } },
                      { text: '30 minutes', onPress: () => { setTempReminderMinutes(30); handleSaveReminderTime(); } },
                      { text: '45 minutes', onPress: () => { setTempReminderMinutes(45); handleSaveReminderTime(); } },
                      { text: '60 minutes', onPress: () => { setTempReminderMinutes(60); handleSaveReminderTime(); } },
                      { text: 'Cancel', style: 'cancel' },
                    ]
                  );
                }}
              >
                <Text style={[styles.timeText, { color: colors.text }]}>
                  {sleepGoals.bedTimeReminderMinutes || 30} minutes
                </Text>
              </TouchableOpacity>
            </View>
            
            <Text style={[styles.infoText, { color: colors.textSecondary }]}>
              You'll receive a reminder {sleepGoals.bedTimeReminderMinutes || 30} minutes before your target bedtime.
            </Text>
          </>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
    marginBottom: 20,
  },
  card: {
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  settingLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingLabel: {
    fontSize: 16,
    marginLeft: 8,
  },
  timeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  timeText: {
    fontSize: 15,
    fontWeight: '500',
  },
  loadingText: {
    textAlign: 'center',
    fontSize: 16,
    marginTop: 20,
  },
  infoText: {
    fontSize: 14,
    marginTop: -8,
    marginBottom: 8,
  },
}); 