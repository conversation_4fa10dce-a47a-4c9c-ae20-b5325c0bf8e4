import React from 'react';
import { View, Text, StyleSheet, useWindowDimensions } from 'react-native';
import Svg, { Rect, Line, Text as SvgText, Circle } from 'react-native-svg';
import { format, parseISO, differenceInDays } from 'date-fns';
import { SleepRecord, SleepQuality, SleepStatistics } from '@/services/sleepTrackingService';

interface SleepStatisticsChartProps {
  records: SleepRecord[];
  statistics?: SleepStatistics;
  colors: {
    text: string;
    background: string;
    primary: string;
    textSecondary: string;
    card: string;
  };
}

export default function SleepStatisticsChart({ 
  records, 
  statistics, 
  colors 
}: SleepStatisticsChartProps) {
  const { width } = useWindowDimensions();
  const chartWidth = width - 48; // Account for padding
  const chartHeight = 180;
  
  // Sort records by date (oldest first for the chart)
  const sortedRecords = [...records].sort((a, b) => 
    new Date(a.date).getTime() - new Date(b.date).getTime()
  );
  
  // Calculate chart parameters
  const maxDuration = Math.max(...records.map(r => r.duration / 60), 9); // At least 9 hours max for scale
  const maxHours = Math.ceil(maxDuration);
  const hourHeight = chartHeight / maxHours;
  
  // If we have less than 7 days of data, calculate the width differently
  const daysSpan = records.length > 1 
    ? differenceInDays(
        parseISO(records[records.length - 1].date),
        parseISO(records[0].date)
      ) + 1
    : 1;
    
  const dayWidth = chartWidth / Math.min(7, daysSpan);
  
  // Quality color mapping
  const getQualityColor = (quality: SleepQuality) => {
    switch (quality) {
      case SleepQuality.EXCELLENT:
        return '#4CAF50';
      case SleepQuality.GOOD:
        return '#8BC34A';
      case SleepQuality.FAIR:
        return '#FFC107';
      case SleepQuality.POOR:
        return '#FF5722';
      default:
        return '#9E9E9E';
    }
  };
  
  // If no records, show empty state
  if (records.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: colors.card }]}>
        <Text style={[styles.title, { color: colors.text }]}>Sleep Patterns</Text>
        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            No sleep data available yet. Start tracking your sleep to see patterns.
          </Text>
        </View>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <Text style={[styles.title, { color: colors.text }]}>Sleep Patterns</Text>
      
      {statistics && (
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: colors.primary }]}>
              {Math.floor(statistics.averageDuration / 60)}h {statistics.averageDuration % 60}m
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Avg Duration</Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: colors.primary }]}>
              {statistics.daysTracked}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Days Tracked</Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: colors.primary }]}>
              {statistics.sleepDebt > 0
                ? `-${Math.floor(statistics.sleepDebt / 60)}h`
                : `+${Math.floor(Math.abs(statistics.sleepDebt) / 60)}h`}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Sleep Balance</Text>
          </View>
        </View>
      )}
      
      <View style={styles.chartContainer}>
        <Svg width={chartWidth} height={chartHeight}>
          {/* Hour markers */}
          {Array.from({ length: maxHours + 1 }).map((_, i) => (
            <React.Fragment key={`hour-${i}`}>
              <Line
                x1={0}
                y1={chartHeight - i * hourHeight}
                x2={chartWidth}
                y2={chartHeight - i * hourHeight}
                stroke={colors.textSecondary}
                strokeWidth={i === 0 ? 1 : 0.5}
                strokeOpacity={i === 0 ? 0.5 : 0.2}
              />
              {i > 0 && i % 2 === 0 && (
                <SvgText
                  x={5}
                  y={chartHeight - i * hourHeight - 5}
                  fontSize={10}
                  fill={colors.textSecondary}
                >
                  {i}h
                </SvgText>
              )}
            </React.Fragment>
          ))}
          
          {/* Sleep duration bars */}
          {sortedRecords.slice(-7).map((record, index) => {
            const barX = index * dayWidth;
            const duration = record.duration / 60; // Convert to hours
            const barHeight = duration * hourHeight;
            
            return (
              <React.Fragment key={record.id}>
                {/* Sleep bar */}
                <Rect
                  x={barX + 8}
                  y={chartHeight - barHeight}
                  width={dayWidth - 16}
                  height={barHeight}
                  fill={getQualityColor(record.quality)}
                  rx={4}
                />
                
                {/* Day label */}
                <SvgText
                  x={barX + dayWidth / 2}
                  y={chartHeight + 15}
                  fontSize={10}
                  fill={colors.text}
                  textAnchor="middle"
                >
                  {format(parseISO(record.date), 'EEE')}
                </SvgText>
              </React.Fragment>
            );
          })}
          
          {/* Target line (if statistics available) */}
          {statistics && (
            <Line
              x1={0}
              y1={chartHeight - 8 * hourHeight}
              x2={chartWidth}
              y2={chartHeight - 8 * hourHeight}
              stroke={colors.primary}
              strokeWidth={1}
              strokeDasharray="5,5"
            />
          )}
        </Svg>
      </View>
      
      <View style={styles.legendContainer}>
        {Object.values(SleepQuality).map(quality => (
          <View key={quality} style={styles.legendItem}>
            <View 
              style={[
                styles.legendColor, 
                { backgroundColor: getQualityColor(quality) }
              ]} 
            />
            <Text style={[styles.legendText, { color: colors.text }]}>
              {quality.charAt(0).toUpperCase() + quality.slice(1)}
            </Text>
          </View>
        ))}
        
        {statistics && (
          <View style={styles.legendItem}>
            <View style={styles.legendLine}>
              <View 
                style={[styles.legendDash, { backgroundColor: colors.primary }]} 
              />
              <View 
                style={[styles.legendDash, { backgroundColor: colors.primary }]} 
              />
            </View>
            <Text style={[styles.legendText, { color: colors.text }]}>
              Target
            </Text>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 16,
    marginVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  chartContainer: {
    marginVertical: 8,
    alignItems: 'center',
    height: 200, // Including space for labels
  },
  legendContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
    justifyContent: 'center',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 4,
  },
  legendLine: {
    flexDirection: 'row',
    marginRight: 4,
  },
  legendDash: {
    width: 5,
    height: 2,
    marginRight: 2,
  },
  legendText: {
    fontSize: 12,
  },
  emptyContainer: {
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 14,
  },
}); 