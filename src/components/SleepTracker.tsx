import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Alert,
 useColorScheme , Dimensions , useWindowDimensions } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format, subDays, isAfter, parseISO, differenceInMinutes } from 'date-fns';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import Svg, { Circle } from 'react-native-svg';
import { getAuth } from 'firebase/auth';

import Colors from '@/constants/Colors';
import {
  SleepQuality,
  addSleepRecord,
  useSleepTrackingService,
  SleepRecord,
  SleepStatistics,
  generateSleepInsights
} from '@/services/sleepTrackingService';
import SleepReminderSettings from '@/components/SleepReminderSettings';
import SleepStatisticsChart from '@/components/SleepStatisticsChart';

// Enhanced color theme with additional properties
interface ExtendedTheme {
  text: string;
  background: string;
  tint: string;
  tabIconDefault: string;
  tabIconSelected: string;
  primary: string;
  card: string;
  textSecondary: string;
}

export default function SleepTracker() {
  const colorScheme = useColorScheme() || 'light';
  // Create extended theme with required properties
  const baseColors = Colors[colorScheme];
  const colors: ExtendedTheme = {
    ...baseColors,
    primary: colorScheme === 'dark' ? '#8080ff' : '#6200ee',
    card: colorScheme === 'dark' ? '#2a2a2a' : '#ffffff',
    textSecondary: colorScheme === 'dark' ? '#a0a0a0' : '#666666',
  };

  const { width } = useWindowDimensions();
  
  // State
  const [isLoading, setIsLoading] = useState(false);
  const [isAddingRecord, setIsAddingRecord] = useState(false);
  const [bedTime, setBedTime] = useState(new Date());
  const [wakeTime, setWakeTime] = useState(new Date());
  const [sleepQuality, setSleepQuality] = useState<SleepQuality>(SleepQuality.GOOD);
  const [interruptions, setInterruptions] = useState(0);
  const [sleepNotes, setSleepNotes] = useState('');
  const [showBedTimePicker, setShowBedTimePicker] = useState(false);
  const [showWakeTimePicker, setShowWakeTimePicker] = useState(false);
  const [recentRecords, setRecentRecords] = useState<SleepRecord[]>([]);
  const [statistics, setStatistics] = useState<SleepStatistics | null>(null);
  const [insights, setInsights] = useState<string[]>([]);

  const { getSleepRecordsForDateRange, getSleepSummary } = useSleepTrackingService();

  // Set initial times
  useEffect(() => {
    const now = new Date();
    
    // Default bedtime to last night at 10:30 PM
    const defaultBedTime = new Date(now);
    defaultBedTime.setDate(defaultBedTime.getDate() - 1);
    defaultBedTime.setHours(22, 30, 0, 0);
    
    // Default wake time to today at 6:30 AM
    const defaultWakeTime = new Date(now);
    defaultWakeTime.setHours(6, 30, 0, 0);
    
    setBedTime(defaultBedTime);
    setWakeTime(defaultWakeTime);
    
    // Load recent sleep data
    loadSleepData();
  }, []);

  // Fetch sleep data
  const loadSleepData = async () => {
    try {
      setIsLoading(true);
      
      // Get the current user
      const auth = getAuth();
      const user = auth.currentUser;
      
      if (!user) {
        console.error('User not authenticated');
        setIsLoading(false);
        return;
      }
      
      // Get records for the past week
      const endDate = format(new Date(), 'yyyy-MM-dd');
      const startDate = format(subDays(new Date(), 7), 'yyyy-MM-dd');
      
      try {
        // Use the hook function with correct parameters
        const records = await getSleepRecordsForDateRange(user.uid, startDate, endDate);
        setRecentRecords(records || []);
        
        // Get statistics for the past month
        const monthStartDate = format(subDays(new Date(), 30), 'yyyy-MM-dd');
        
        if (getSleepSummary) {
          const stats = await getSleepSummary(user.uid, monthStartDate, endDate);
          
          if (stats) {
            // Convert to SleepStatistics format
            const sleepStats: SleepStatistics = {
              averageDuration: stats.averageDuration || 0,
              averageQuality: (typeof stats.averageQuality === 'number' ? 
                               SleepQuality.GOOD : // Default if number
                               stats.averageQuality as SleepQuality), // Use as-is if string
              averageSleepScore: 0, // Default value
              averageBedTime: stats.averageBedtime || '00:00',
              averageWakeTime: stats.averageWakeTime || '00:00',
              sleepDebt: 0, // Default value
              consistencyScore: 0, // Default value
              daysTracked: stats.totalSleepDays || 0,
              sleepEfficiency: 0, // Default value
              totalSleepTime: stats.totalSleepMinutes || 0,
              interruptions: 0 // Default value
            };
            
            setStatistics(sleepStats);
            
            // Generate insights based on statistics
            const sleepInsights = generateSleepInsights(sleepStats);
            setInsights(sleepInsights);
          }
        }
      } catch (error) {
        console.error('Error loading sleep data:', error);
      }
    } catch (error) {
      console.error('Error loading sleep data:', error);
      Alert.alert('Error', 'Failed to load sleep data');
    } finally {
      setIsLoading(false);
    }
  };

  // Add a new sleep record
  const handleAddSleepRecord = async () => {
    try {
      // Validate times
      if (!isAfter(wakeTime, bedTime)) {
        Alert.alert('Invalid Times', 'Wake time must be after bed time');
        return;
      }
      
      setIsLoading(true);
      
      const result = await addSleepRecord(
        bedTime,
        wakeTime,
        sleepQuality,
        interruptions,
        sleepNotes
      );
      
      if (result.success) {
        Alert.alert('Success', 'Sleep record added successfully');
        setIsAddingRecord(false);
        
        // Reset form
        const now = new Date();
        const defaultBedTime = new Date(now);
        defaultBedTime.setDate(defaultBedTime.getDate() - 1);
        defaultBedTime.setHours(22, 30, 0, 0);
        
        const defaultWakeTime = new Date(now);
        defaultWakeTime.setHours(6, 30, 0, 0);
        
        setBedTime(defaultBedTime);
        setWakeTime(defaultWakeTime);
        setSleepQuality(SleepQuality.GOOD);
        setInterruptions(0);
        setSleepNotes('');
        
        // Reload data
        loadSleepData();
      } else {
        Alert.alert('Error', result.error || 'Failed to add sleep record');
      }
    } catch (error) {
      console.error('Error adding sleep record:', error);
      Alert.alert('Error', 'Failed to add sleep record');
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate sleep duration in hours and minutes for display
  const calculateSleepDuration = () => {
    const minutes = differenceInMinutes(wakeTime, bedTime);
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  // Handle quality selection
  const handleQualitySelection = (quality: SleepQuality) => {
    setSleepQuality(quality);
  };

  // Handle interruption adjustment
  const handleInterruptionAdjustment = (adjustment: number) => {
    const newValue = interruptions + adjustment;
    if (newValue >= 0) {
      setInterruptions(newValue);
    }
  };

  // Render loading state
  if (isLoading && !isAddingRecord) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.text }]}>Loading sleep data...</Text>
      </View>
    );
  }

  // Render sleep record form
  if (isAddingRecord) {
    return (
      <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
        <Text style={[styles.title, { color: colors.text }]}>Record Your Sleep</Text>
        
        {/* Bed Time Picker */}
        <View style={styles.timePickerContainer}>
          <Text style={[styles.label, { color: colors.text }]}>Bed Time</Text>
          <TouchableOpacity
            style={[styles.timeButton, { backgroundColor: colors.card }]}
            onPress={() => setShowBedTimePicker(true)}
          >
            <Text style={[styles.timeButtonText, { color: colors.text }]}>
              {format(bedTime, 'h:mm a, MMM d')}
            </Text>
            <MaterialCommunityIcons name="clock-outline" size={24} color={colors.primary} />
          </TouchableOpacity>
          
          {showBedTimePicker && (
            <DateTimePicker
              value={bedTime}
              mode="datetime"
              display="default"
              onChange={(event, selectedDate) => {
                setShowBedTimePicker(false);
                if (selectedDate) {
                  setBedTime(selectedDate);
                }
              }}
            />
          )}
        </View>
        
        {/* Wake Time Picker */}
        <View style={styles.timePickerContainer}>
          <Text style={[styles.label, { color: colors.text }]}>Wake Time</Text>
          <TouchableOpacity
            style={[styles.timeButton, { backgroundColor: colors.card }]}
            onPress={() => setShowWakeTimePicker(true)}
          >
            <Text style={[styles.timeButtonText, { color: colors.text }]}>
              {format(wakeTime, 'h:mm a, MMM d')}
            </Text>
            <MaterialCommunityIcons name="clock-outline" size={24} color={colors.primary} />
          </TouchableOpacity>
          
          {showWakeTimePicker && (
            <DateTimePicker
              value={wakeTime}
              mode="datetime"
              display="default"
              onChange={(event, selectedDate) => {
                setShowWakeTimePicker(false);
                if (selectedDate) {
                  setWakeTime(selectedDate);
                }
              }}
            />
          )}
        </View>
        
        {/* Sleep Duration */}
        <View style={styles.durationContainer}>
          <Text style={[styles.label, { color: colors.text }]}>Sleep Duration</Text>
          <Text style={[styles.durationText, { color: colors.primary }]}>
            {calculateSleepDuration()}
          </Text>
        </View>
        
        {/* Sleep Quality */}
        <View style={styles.qualityContainer}>
          <Text style={[styles.label, { color: colors.text }]}>Sleep Quality</Text>
          <View style={styles.qualityButtons}>
            {Object.values(SleepQuality).map((quality) => (
              <TouchableOpacity
                key={quality}
                style={[
                  styles.qualityButton,
                  {
                    backgroundColor: sleepQuality === quality ? colors.primary : colors.card,
                  },
                ]}
                onPress={() => handleQualitySelection(quality)}
              >
                <Text
                  style={[
                    styles.qualityButtonText,
                    {
                      color: sleepQuality === quality ? '#fff' : colors.text,
                    },
                  ]}
                >
                  {quality.charAt(0).toUpperCase() + quality.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        {/* Interruptions */}
        <View style={styles.interruptionsContainer}>
          <Text style={[styles.label, { color: colors.text }]}>Interruptions</Text>
          <View style={styles.interruptionsAdjuster}>
            <TouchableOpacity
              style={[styles.interruptionButton, { backgroundColor: colors.card }]}
              onPress={() => handleInterruptionAdjustment(-1)}
            >
              <Text style={[styles.interruptionButtonText, { color: colors.text }]}>-</Text>
            </TouchableOpacity>
            
            <Text style={[styles.interruptionValue, { color: colors.text }]}>
              {interruptions}
            </Text>
            
            <TouchableOpacity
              style={[styles.interruptionButton, { backgroundColor: colors.card }]}
              onPress={() => handleInterruptionAdjustment(1)}
            >
              <Text style={[styles.interruptionButtonText, { color: colors.text }]}>+</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.cancelButton, { backgroundColor: colors.card }]}
            onPress={() => setIsAddingRecord(false)}
          >
            <Text style={[styles.buttonText, { color: colors.text }]}>Cancel</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.saveButton, { backgroundColor: colors.primary }]}
            onPress={handleAddSleepRecord}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={[styles.buttonText, { color: '#fff' }]}>Save</Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    );
  }

  // Main sleep tracking dashboard
  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>Sleep Tracking</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: colors.primary }]}
          onPress={() => setIsAddingRecord(true)}
        >
          <MaterialCommunityIcons name="plus" size={24} color="#fff" />
          <Text style={styles.addButtonText}>Add Sleep</Text>
        </TouchableOpacity>
      </View>

      {/* Sleep Score */}
      {statistics && (
        <View style={[styles.scoreCard, { backgroundColor: colors.card }]}>
          <View style={styles.scoreHeader}>
            <Text style={[styles.scoreTitle, { color: colors.text }]}>Sleep Score</Text>
          </View>
          
          <View style={styles.scoreContent}>
            <SleepScoreIndicator score={statistics.averageSleepScore} />
            <View style={styles.scoreDetails}>
              <Text style={[styles.scoreValue, { color: colors.primary }]}>
                {statistics.averageSleepScore}
              </Text>
              <Text style={[styles.scoreLabel, { color: colors.text }]}>Average</Text>
            </View>
          </View>
          
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <MaterialCommunityIcons name="clock-outline" size={20} color={colors.primary} />
              <Text style={[styles.statValue, { color: colors.text }]}>
                {Math.floor(statistics.averageDuration / 60)}h {statistics.averageDuration % 60}m
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Avg. Duration</Text>
            </View>
            
            <View style={styles.statItem}>
              <MaterialCommunityIcons name="weather-night" size={20} color={colors.primary} />
              <Text style={[styles.statValue, { color: colors.text }]}>
                {statistics.averageBedTime}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Avg. Bedtime</Text>
            </View>
            
            <View style={styles.statItem}>
              <MaterialCommunityIcons name="weather-sunny" size={20} color={colors.primary} />
              <Text style={[styles.statValue, { color: colors.text }]}>
                {statistics.averageWakeTime}
              </Text>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>Avg. Wake</Text>
            </View>
          </View>
        </View>
      )}

      {/* Weekly Sleep Duration Bar Chart */}
      {recentRecords.length > 0 && (
        <View style={[styles.chartCard, { backgroundColor: colors.card }]}>
          <Text style={[styles.chartTitle, { color: colors.text }]}>Past Week</Text>
          
          {/* Custom chart visualization instead of using react-native-chart-kit */}
          <View style={styles.customChart}>
            {recentRecords.slice(0, 7).map((record, index) => {
              const durationHours = record.durationMinutes / 60;
              const maxHeight = 180;
              const barHeight = Math.min(durationHours * 20, maxHeight);
              
              return (
                <View key={index} style={styles.barContainer}>
                  <View style={styles.barLabel}>
                    <Text style={{ color: colors.textSecondary }}>
                      {durationHours.toFixed(1)}h
                    </Text>
                  </View>
                  <View 
                    style={[
                      styles.bar, 
                      { 
                        height: barHeight,
                        backgroundColor: colors.primary 
                      }
                    ]} 
                  />
                  <Text style={[styles.barDay, { color: colors.text }]}>
                    {format(parseISO(record.date), 'EEE')}
                  </Text>
                </View>
              );
            })}
          </View>
        </View>
      )}

      {/* Advanced Sleep Statistics Chart */}
      {recentRecords.length > 0 && statistics && (
        <SleepStatisticsChart
          records={recentRecords}
          statistics={statistics}
          colors={colors}
        />
      )}

      {/* Sleep Insights */}
      {insights.length > 0 && (
        <View style={[styles.insightsCard, { backgroundColor: colors.card }]}>
          <Text style={[styles.insightsTitle, { color: colors.text }]}>Sleep Insights</Text>
          {insights.map((insight, index) => (
            <View key={index} style={styles.insightItem}>
              <MaterialCommunityIcons name="lightbulb-outline" size={20} color={colors.primary} />
              <Text style={[styles.insightText, { color: colors.text }]}>{insight}</Text>
            </View>
          ))}
        </View>
      )}

      {/* Recent Records */}
      {recentRecords.length > 0 && (
        <View style={[styles.recentCard, { backgroundColor: colors.card }]}>
          <Text style={[styles.recentTitle, { color: colors.text }]}>Recent Records</Text>
          {recentRecords.slice(0, 3).map((record, index) => (
            <View key={index} style={styles.recordItem}>
              <View style={styles.recordDate}>
                <Text style={[styles.recordDay, { color: colors.text }]}>
                  {format(parseISO(record.date), 'EEE')}
                </Text>
                <Text style={[styles.recordDateFull, { color: colors.textSecondary }]}>
                  {format(parseISO(record.date), 'MMM d')}
                </Text>
              </View>
              
              <View style={styles.recordDetails}>
                <Text style={[styles.recordTime, { color: colors.text }]}>
                  {format(parseISO(`${record.date}T${record.startTime}`), 'h:mm a')} - {format(parseISO(`${record.date}T${record.endTime}`), 'h:mm a')}
                </Text>
                <Text style={[styles.recordDuration, { color: colors.textSecondary }]}>
                  {Math.floor(record.durationMinutes / 60)}h {record.durationMinutes % 60}m • {typeof record.quality === 'string' ? record.quality : SleepQuality[record.quality as number]}
                </Text>
              </View>
              
              <View style={styles.recordScore}>
                <View style={[styles.scoreCircle, { backgroundColor: getSleepScoreColor(record.sleepScore || 0) }]}>
                  <Text style={styles.scoreCircleText}>{record.sleepScore}</Text>
                </View>
              </View>
            </View>
          ))}
        </View>
      )}
      
      {/* Sleep Reminder Settings */}
      <SleepReminderSettings 
        colors={colors} 
        onSave={loadSleepData}
      />
      
    </ScrollView>
  );
}

// Sleep Score Indicator component
function SleepScoreIndicator({ score }: { score: number }) {
  const radius = 40;
  const strokeWidth = 10;
  const normalizedScore = Math.min(100, Math.max(0, score));
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (normalizedScore / 100) * circumference;
  
  const scoreColor = getSleepScoreColor(score);
  
  return (
    <View style={styles.scoreIndicator}>
      <Svg width={radius * 2 + strokeWidth} height={radius * 2 + strokeWidth}>
        {/* Background Circle */}
        <Circle
          cx={radius + strokeWidth / 2}
          cy={radius + strokeWidth / 2}
          r={radius}
          fill="transparent"
          stroke="#e6e6e6"
          strokeWidth={strokeWidth}
        />
        
        {/* Score Circle */}
        <Circle
          cx={radius + strokeWidth / 2}
          cy={radius + strokeWidth / 2}
          r={radius}
          fill="transparent"
          stroke={scoreColor}
          strokeWidth={strokeWidth}
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          transform={`rotate(-90, ${radius + strokeWidth / 2}, ${radius + strokeWidth / 2})`}
        />
      </Svg>
    </View>
  );
}

// Helper function for score colors
function getSleepScoreColor(score: number): string {
  if (score >= 80) return '#4CAF50'; // Good
  if (score >= 60) return '#FFC107'; // Fair
  if (score >= 40) return '#FF9800'; // Poor
  return '#F44336'; // Very poor
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 24,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  addButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 4,
  },
  scoreCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  scoreHeader: {
    marginBottom: 16,
  },
  scoreTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  scoreContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  scoreIndicator: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  scoreDetails: {
    alignItems: 'center',
  },
  scoreValue: {
    fontSize: 42,
    fontWeight: 'bold',
  },
  scoreLabel: {
    fontSize: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 4,
  },
  statLabel: {
    fontSize: 12,
    marginTop: 2,
  },
  chartCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  customChart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-around',
    height: 220,
    paddingTop: 20,
    paddingBottom: 20,
  },
  barContainer: {
    alignItems: 'center',
    flex: 1,
  },
  barLabel: {
    marginBottom: 5,
  },
  bar: {
    width: 20,
    borderRadius: 10,
  },
  barDay: {
    marginTop: 5,
    fontSize: 12,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  insightsCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  insightsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  insightText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 15,
    lineHeight: 22,
  },
  recentCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  recentTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  recordItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  recordDate: {
    width: 50,
    alignItems: 'center',
  },
  recordDay: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  recordDateFull: {
    fontSize: 12,
  },
  recordDetails: {
    flex: 1,
    paddingHorizontal: 12,
  },
  recordTime: {
    fontSize: 16,
    fontWeight: '500',
  },
  recordDuration: {
    fontSize: 14,
    marginTop: 2,
  },
  recordScore: {
    width: 40,
    alignItems: 'center',
  },
  scoreCircle: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scoreCircleText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  loadingText: {
    marginTop: 20,
    fontSize: 16,
  },
  // Add sleep form styles
  timePickerContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  timeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderRadius: 8,
  },
  timeButtonText: {
    fontSize: 16,
  },
  durationContainer: {
    marginBottom: 16,
    alignItems: 'flex-start',
  },
  durationText: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  qualityContainer: {
    marginBottom: 16,
  },
  qualityButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  qualityButton: {
    flex: 1,
    padding: 10,
    borderRadius: 8,
    marginHorizontal: 4,
    alignItems: 'center',
  },
  qualityButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  interruptionsContainer: {
    marginBottom: 24,
  },
  interruptionsAdjuster: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  interruptionButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
  interruptionButtonText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  interruptionValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginHorizontal: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButton: {
    flex: 1,
    marginLeft: 8,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
}); 