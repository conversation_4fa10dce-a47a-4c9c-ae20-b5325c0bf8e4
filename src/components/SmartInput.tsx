import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  KeyboardTypeOptions,
  ViewStyle,
  TextStyle,
  StyleProp,
  TextInputProps,
  NativeSyntheticEvent,
  TextInputFocusEventData,
  Keyboard,
  InputAccessoryView,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { useAccessibility } from './AccessibilityProvider';

// Input types supported by this component
export type InputType = 
  | 'text' 
  | 'email' 
  | 'phone' 
  | 'number' 
  | 'decimal' 
  | 'password' 
  | 'url' 
  | 'search'
  | 'username'
  | 'postalCode'
  | 'creditCard';

interface ValidationError {
  type: string;
  message: string;
}

interface SmartInputProps extends Omit<TextInputProps, 'style'> {
  /**
   * Label text to display above the input
   */
  label?: string;
  
  /**
   * Type of input which determines keyboard type and validation
   */
  inputType?: InputType;
  
  /**
   * Optional helper text to display below the input
   */
  helperText?: string;
  
  /**
   * Whether the input is required
   */
  required?: boolean;
  
  /**
   * Optional placeholder text
   */
  placeholder?: string;
  
  /**
   * Optional error message to display
   */
  error?: string;
  
  /**
   * Optional callback for when the value changes
   */
  onChangeText?: (text: string) => void;
  
  /**
   * Optional callback for when the validation status changes
   */
  onValidationChange?: (isValid: boolean) => void;
  
  /**
   * Optional validation pattern (regex)
   */
  validationPattern?: RegExp;
  
  /**
   * Optional custom validation function
   */
  customValidation?: (value: string) => ValidationError | null;
  
  /**
   * Optional validation message to show when validation fails
   */
  validationMessage?: string;
  
  /**
   * Whether to show the clear button
   */
  showClearButton?: boolean;
  
  /**
   * Style for the container
   */
  containerStyle?: StyleProp<ViewStyle>;
  
  /**
   * Style for the input
   */
  inputStyle?: StyleProp<TextStyle>;
  
  /**
   * Style for the label
   */
  labelStyle?: StyleProp<TextStyle>;
  
  /**
   * Style for the helper text
   */
  helperTextStyle?: StyleProp<TextStyle>;
  
  /**
   * Optional leading icon name
   */
  leadingIcon?: string;
  
  /**
   * Optional trailing icon name
   */
  trailingIcon?: string;
  
  /**
   * Optional callback for when the trailing icon is pressed
   */
  onTrailingIconPress?: () => void;
  
  /**
   * Whether to auto-capitalize the input
   */
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  
  /**
   * Whether to auto-correct the input
   */
  autoCorrect?: boolean;
  
  /**
   * Whether the input is disabled
   */
  disabled?: boolean;
  
  /**
   * Maximum length of the input
   */
  maxLength?: number;
  
  /**
   * Whether the input should be multiline
   */
  multiline?: boolean;
  
  /**
   * Whether to show a "Done" button in the input accessory view (iOS only)
   */
  showDoneButton?: boolean;
}

/**
 * A smart input component that automatically selects the appropriate keyboard type,
 * provides validation, and offers a consistent user experience across the app.
 */
export function SmartInput({
  label,
  inputType = 'text',
  helperText,
  required = false,
  placeholder,
  error,
  onChangeText,
  onValidationChange,
  validationPattern,
  customValidation,
  validationMessage,
  showClearButton = true,
  containerStyle,
  inputStyle,
  labelStyle,
  helperTextStyle,
  leadingIcon,
  trailingIcon,
  onTrailingIconPress,
  autoCapitalize,
  autoCorrect,
  disabled = false,
  maxLength,
  multiline = false,
  showDoneButton = true,
  defaultValue = '',
  value,
  secureTextEntry: propSecureTextEntry,
  onFocus,
  onBlur,
  ...rest
}: SmartInputProps) {
  const { colors, isDark } = useTheme();
  const { isScreenReaderEnabled, preferLargeText } = useAccessibility();
  
  // State
  const [inputValue, setInputValue] = useState(defaultValue);
  const [isFocused, setIsFocused] = useState(false);
  const [validationError, setValidationError] = useState<ValidationError | null>(null);
  const [secureTextEntry, setSecureTextEntry] = useState(
    propSecureTextEntry !== undefined ? propSecureTextEntry : inputType === 'password'
  );
  
  const inputRef = useRef<TextInput>(null);
  
  // The input accessory view ID for iOS
  const inputAccessoryViewID = `${label || 'input'}_accessory_view`;
  
  // Determine keyboard type based on input type
  const getKeyboardType = (): KeyboardTypeOptions => {
    switch (inputType) {
      case 'email':
        return 'email-address';
      case 'phone':
        return 'phone-pad';
      case 'number':
        return 'number-pad';
      case 'decimal':
        return 'decimal-pad';
      case 'url':
        return 'url';
      case 'search':
        return 'web-search';
      case 'postalCode':
        return 'number-pad';
      case 'creditCard':
        return 'number-pad';
      default:
        return 'default';
    }
  };
  
  // Get auto capitalization based on input type
  const getAutoCapitalize = () => {
    if (autoCapitalize !== undefined) return autoCapitalize;
    
    switch (inputType) {
      case 'email':
      case 'password':
      case 'url':
      case 'username':
        return 'none';
      case 'text':
        return 'sentences';
      default:
        return 'none';
    }
  };
  
  // Get auto correct based on input type
  const getAutoCorrect = () => {
    if (autoCorrect !== undefined) return autoCorrect;
    
    switch (inputType) {
      case 'email':
      case 'password':
      case 'url':
      case 'username':
      case 'phone':
      case 'number':
      case 'decimal':
      case 'postalCode':
      case 'creditCard':
        return false;
      default:
        return true;
    }
  };
  
  // Get validation pattern based on input type
  const getValidationPattern = () => {
    if (validationPattern) return validationPattern;
    
    switch (inputType) {
      case 'email':
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      case 'phone':
        return /^\+?[0-9]{10,15}$/;
      case 'url':
        return /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/;
      case 'postalCode':
        return /^[0-9]{5}(-[0-9]{4})?$/;
      case 'creditCard':
        return /^[0-9]{13,19}$/;
      default:
        return null;
    }
  };
  
  // Validate the input value
  const validateInput = (text: string) => {
    // Skip validation if field is empty and not required
    if (text === '' && !required) {
      setValidationError(null);
      onValidationChange?.(true);
      return;
    }
    
    // Check if required field is empty
    if (text === '' && required) {
      const error = {
        type: 'required',
        message: 'This field is required'
      };
      setValidationError(error);
      onValidationChange?.(false);
      return;
    }
    
    // Custom validation
    if (customValidation) {
      const error = customValidation(text);
      setValidationError(error);
      onValidationChange?.(error === null);
      return;
    }
    
    // Pattern validation
    const pattern = getValidationPattern();
    if (pattern && !pattern.test(text)) {
      const error = {
        type: 'pattern',
        message: validationMessage || `Invalid ${inputType} format`
      };
      setValidationError(error);
      onValidationChange?.(false);
      return;
    }
    
    // If we've reached here, the input is valid
    setValidationError(null);
    onValidationChange?.(true);
  };
  
  // Handle text change
  const handleTextChange = (text: string) => {
    // Format input based on type
    let formattedText = text;
    
    if (inputType === 'creditCard') {
      // Remove non-digits and limit to 19 digits
      formattedText = text.replace(/\D/g, '').slice(0, 19);
    } else if (inputType === 'phone') {
      // Remove non-digits
      formattedText = text.replace(/\D/g, '');
    }
    
    setInputValue(formattedText);
    validateInput(formattedText);
    onChangeText?.(formattedText);
  };
  
  // Handle focus event
  const handleFocus = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
    setIsFocused(true);
    onFocus?.(e);
  };
  
  // Handle blur event
  const handleBlur = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
    setIsFocused(false);
    // Validate on blur to handle required fields
    validateInput(value ?? inputValue);
    onBlur?.(e);
  };
  
  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setSecureTextEntry(prev => !prev);
  };
  
  // Clear input value
  const clearInput = () => {
    setInputValue('');
    onChangeText?.('');
    validateInput('');
    inputRef.current?.focus();
  };
  
  // Dismiss the keyboard (for iOS "Done" button)
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };
  
  // Update internal state when value prop changes
  useEffect(() => {
    if (value !== undefined && value !== inputValue) {
      setInputValue(value);
      validateInput(value);
    }
  }, [value]);
  
  // Get display value based on prop or internal state
  const displayValue = value !== undefined ? value : inputValue;
  
  // Font size adjustments for accessibility
  const getFontSize = (baseSize: number) => {
    return preferLargeText ? baseSize * 1.3 : baseSize;
  };
  
  return (
    <View style={[styles.container, containerStyle]}>
      {/* Label */}
      {label && (
        <View style={styles.labelContainer}>
          <Text 
            style={[
              styles.label, 
              { 
                color: validationError ? colors.error : colors.text,
                fontSize: getFontSize(16)
              }, 
              labelStyle
            ]}
            accessibilityRole="text"
          >
            {label}
            {required && <Text style={{ color: colors.error }}> *</Text>}
          </Text>
        </View>
      )}
      
      {/* Input container */}
      <View 
        style={[
          styles.inputContainer, 
          { 
            borderColor: validationError 
              ? colors.error 
              : isFocused 
                ? colors.primary 
                : colors.border,
            backgroundColor: isDark ? colors.subtle : '#F5F5F5',
          },
          disabled && styles.disabledInput
        ]}
      >
        {/* Leading icon */}
        {leadingIcon && (
          <Feather 
            name={leadingIcon as any} 
            size={20} 
            color={colors.textSecondary} 
            style={styles.leadingIcon} 
          />
        )}
        
        {/* TextInput */}
        <TextInput
          ref={inputRef}
          style={[
            styles.input, 
            { 
              color: colors.text,
              fontSize: getFontSize(16)
            },
            multiline && styles.multilineInput,
            inputStyle
          ]}
          placeholder={placeholder}
          placeholderTextColor={colors.textSecondary}
          value={displayValue}
          onChangeText={handleTextChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          keyboardType={getKeyboardType()}
          autoCapitalize={getAutoCapitalize()}
          autoCorrect={getAutoCorrect()}
          secureTextEntry={secureTextEntry}
          editable={!disabled}
          maxLength={maxLength}
          multiline={multiline}
          textAlignVertical={multiline ? 'top' : 'center'}
          inputAccessoryViewID={Platform.OS === 'ios' ? inputAccessoryViewID : undefined}
          accessible={true}
          accessibilityLabel={label || placeholder}
          accessibilityHint={helperText}
          accessibilityRole="text"
          {...rest}
        />
        
        {/* Clear button */}
        {showClearButton && displayValue !== '' && !disabled && (
          <TouchableOpacity
            style={styles.iconButton}
            onPress={clearInput}
            accessibilityLabel="Clear input"
            accessibilityRole="button"
          >
            <Feather name="x" size={18} color={colors.textSecondary} />
          </TouchableOpacity>
        )}
        
        {/* Password visibility toggle */}
        {inputType === 'password' && !disabled && (
          <TouchableOpacity
            style={styles.iconButton}
            onPress={togglePasswordVisibility}
            accessibilityLabel={secureTextEntry ? "Show password" : "Hide password"}
            accessibilityRole="button"
          >
            <Feather 
              name={secureTextEntry ? "eye" : "eye-off"} 
              size={18} 
              color={colors.textSecondary} 
            />
          </TouchableOpacity>
        )}
        
        {/* Trailing icon */}
        {trailingIcon && !disabled && (
          <TouchableOpacity
            style={styles.iconButton}
            onPress={onTrailingIconPress}
            accessibilityLabel="Additional action"
            accessibilityRole="button"
            disabled={!onTrailingIconPress}
          >
            <Feather 
              name={trailingIcon as any} 
              size={18} 
              color={colors.textSecondary} 
            />
          </TouchableOpacity>
        )}
      </View>
      
      {/* Error message or helper text */}
      {(validationError || helperText) && (
        <Text 
          style={[
            styles.helperText, 
            { 
              color: validationError ? colors.error : colors.textSecondary,
              fontSize: getFontSize(14)
            }, 
            helperTextStyle
          ]}
          accessibilityRole="text"
        >
          {validationError ? validationError.message : helperText}
        </Text>
      )}
      
      {/* Input accessory view (iOS only) */}
      {Platform.OS === 'ios' && showDoneButton && (
        <InputAccessoryView nativeID={inputAccessoryViewID}>
          <View style={[styles.accessoryContainer, { backgroundColor: isDark ? colors.card : '#F0F0F0' }]}>
            <TouchableOpacity
              style={styles.doneButton}
              onPress={dismissKeyboard}
              accessibilityLabel="Done"
              accessibilityRole="button"
            >
              <Text style={[styles.doneButtonText, { color: colors.primary }]}>Done</Text>
            </TouchableOpacity>
          </View>
        </InputAccessoryView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  labelContainer: {
    marginBottom: 6,
  },
  label: {
    fontWeight: '500',
    marginBottom: 4,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 48,
  },
  input: {
    flex: 1,
    height: '100%',
    paddingVertical: 8,
  },
  multilineInput: {
    minHeight: 100,
    textAlignVertical: 'top',
    paddingTop: 12,
  },
  iconButton: {
    padding: 8,
    marginLeft: 4,
  },
  helperText: {
    marginTop: 4,
    paddingHorizontal: 4,
  },
  leadingIcon: {
    marginRight: 8,
  },
  disabledInput: {
    opacity: 0.6,
  },
  accessoryContainer: {
    height: 44,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: 16,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: 'rgba(0,0,0,0.2)',
  },
  doneButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  doneButtonText: {
    fontWeight: '600',
    fontSize: 16,
  },
});

export default SmartInput; 