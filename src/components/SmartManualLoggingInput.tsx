import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TextInput, 
  TouchableOpacity, 
  ActivityIndicator,
  Keyboard
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';

import { FoodItem } from './SavedMealsComponent';

interface SmartManualLoggingInputProps {
  onFoodsAdded: (foods: FoodItem[]) => void;
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
}

// Mock database of common foods with nutrition data
const FOOD_DATABASE: Record<string, Omit<FoodItem, 'id' | 'servings'>> = {
  'egg': {
    name: 'Egg',
    calories: 70,
    protein: 6,
    carbs: 0.6,
    fat: 5,
    servingSize: '1 large',
  },
  'boiled egg': {
    name: 'Boiled Egg',
    calories: 70,
    protein: 6.3,
    carbs: 0.6,
    fat: 4.8,
    servingSize: '1 large',
  },
  'fried egg': {
    name: 'Fried Egg',
    calories: 85,
    protein: 5.5,
    carbs: 0.6,
    fat: 6.5,
    servingSize: '1 large',
  },
  'scrambled egg': {
    name: 'Scrambled Egg',
    calories: 90,
    protein: 6,
    carbs: 1,
    fat: 7,
    servingSize: '1 large',
  },
  'toast': {
    name: 'Toast, White Bread',
    calories: 80,
    protein: 2,
    carbs: 15,
    fat: 1,
    servingSize: '1 slice',
  },
  'whole wheat toast': {
    name: 'Toast, Whole Wheat',
    calories: 90,
    protein: 3.5,
    carbs: 17,
    fat: 1,
    servingSize: '1 slice',
  },
  'banana': {
    name: 'Banana',
    calories: 105,
    protein: 1.3,
    carbs: 27,
    fat: 0.4,
    servingSize: '1 medium',
  },
  'apple': {
    name: 'Apple',
    calories: 95,
    protein: 0.5,
    carbs: 25,
    fat: 0.3,
    servingSize: '1 medium',
  },
  'orange': {
    name: 'Orange',
    calories: 65,
    protein: 1.3,
    carbs: 16,
    fat: 0.2,
    servingSize: '1 medium',
  },
  'coffee': {
    name: 'Coffee, Black',
    calories: 5,
    protein: 0.3,
    carbs: 0,
    fat: 0,
    servingSize: '1 cup',
  },
  'milk': {
    name: 'Milk, 2%',
    calories: 120,
    protein: 8,
    carbs: 12,
    fat: 4.5,
    servingSize: '1 cup',
  },
  'almond milk': {
    name: 'Almond Milk, Unsweetened',
    calories: 30,
    protein: 1.2,
    carbs: 1,
    fat: 2.5,
    servingSize: '1 cup',
  },
  'oatmeal': {
    name: 'Oatmeal, Plain',
    calories: 150,
    protein: 5,
    carbs: 27,
    fat: 2.5,
    servingSize: '1 cup cooked',
  },
  'chicken breast': {
    name: 'Chicken Breast, Grilled',
    calories: 165,
    protein: 31,
    carbs: 0,
    fat: 3.6,
    servingSize: '3 oz',
  },
  'salmon': {
    name: 'Salmon, Baked',
    calories: 175,
    protein: 19,
    carbs: 0,
    fat: 10.5,
    servingSize: '3 oz',
  },
  'rice': {
    name: 'White Rice, Cooked',
    calories: 205,
    protein: 4.3,
    carbs: 45,
    fat: 0.4,
    servingSize: '1 cup',
  },
  'brown rice': {
    name: 'Brown Rice, Cooked',
    calories: 215,
    protein: 5,
    carbs: 45,
    fat: 1.8,
    servingSize: '1 cup',
  },
  'avocado': {
    name: 'Avocado',
    calories: 235,
    protein: 3,
    carbs: 12,
    fat: 21,
    servingSize: '1 whole',
  },
  'peanut butter': {
    name: 'Peanut Butter',
    calories: 190,
    protein: 7,
    carbs: 7,
    fat: 16,
    servingSize: '2 tbsp',
  },
  'greek yogurt': {
    name: 'Greek Yogurt, Plain',
    calories: 130,
    protein: 17,
    carbs: 6,
    fat: 4,
    servingSize: '3/4 cup',
  },
  'spinach': {
    name: 'Spinach, Raw',
    calories: 7,
    protein: 0.9,
    carbs: 1.1,
    fat: 0.1,
    servingSize: '1 cup',
  },
};

export function SmartManualLoggingInput({ onFoodsAdded, mealType }: SmartManualLoggingInputProps) {
  const { colors, isDark } = useTheme();
  const [inputText, setInputText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  
  const handleSubmitText = () => {
    if (!inputText.trim()) return;
    
    setIsProcessing(true);
    Keyboard.dismiss();
    
    // In a real app, this would call an AI-powered API
    // For now, we'll use a simple parsing algorithm
    setTimeout(() => {
      const foods = parseInput(inputText);
      if (foods.length > 0) {
        onFoodsAdded(foods);
        setInputText('');
      }
      setIsProcessing(false);
    }, 1500);
  };
  
  const parseInput = (input: string): FoodItem[] => {
    // This is a simplified parser that looks for patterns like:
    // - 2 eggs
    // - 1 slice of toast + black coffee
    // In a real app, this would use NLP and a more sophisticated food database
    
    // Normalize input
    const normalizedInput = input.toLowerCase()
      .replace(/\s+/g, ' ')
      .replace(/,/g, '')
      .trim();
    
    // Split input by delimiters
    const foodStrings = normalizedInput.split(/\+|and|with/);
    
    const parsedFoods: FoodItem[] = [];
    
    for (const foodString of foodStrings) {
      const trimmed = foodString.trim();
      if (!trimmed) continue;
      
      // Try to identify quantity and food
      // Patterns: "2 eggs", "a banana", "an apple", "3 slices of bread", etc.
      let quantity = 1;
      let foodName = trimmed;
      
      // Check for numeric quantity
      const numericMatch = trimmed.match(/^(\d+(\.\d+)?)/);
      if (numericMatch) {
        quantity = parseFloat(numericMatch[1]);
        foodName = trimmed.substring(numericMatch[0].length).trim();
      } 
      // Check for 'a' or 'an'
      else if (trimmed.startsWith('a ')) {
        quantity = 1;
        foodName = trimmed.substring(2).trim();
      } else if (trimmed.startsWith('an ')) {
        quantity = 1;
        foodName = trimmed.substring(3).trim();
      }
      
      // Handle "slices of bread" type patterns
      const unitMatch = foodName.match(/^(cup|slice|piece|bowl)s?\s+of\s+(.+)$/);
      if (unitMatch) {
        // For simplicity, we'll treat 1 slice/cup/etc as 1 serving
        // In a real app, we'd convert units properly
        foodName = unitMatch[2].trim();
      }
      
      // Find closest match in our database
      const match = findBestMatch(foodName);
      
      if (match) {
        parsedFoods.push({
          id: `parsed-${Date.now()}-${parsedFoods.length}`,
          name: match.name,
          calories: match.calories,
          protein: match.protein,
          carbs: match.carbs,
          fat: match.fat,
          servingSize: match.servingSize,
          servings: quantity,
        });
      }
    }
    
    return parsedFoods;
  };
  
  const findBestMatch = (foodName: string): (Omit<FoodItem, 'id' | 'servings'> | null) => {
    // This is a simple exact/partial match
    // A real implementation would use fuzzy matching or ML
    
    // Direct match
    if (FOOD_DATABASE[foodName]) {
      return FOOD_DATABASE[foodName];
    }
    
    // Find any key that contains our input as a substring
    for (const key of Object.keys(FOOD_DATABASE)) {
      if (key.includes(foodName) || foodName.includes(key)) {
        return FOOD_DATABASE[key];
      }
    }
    
    // No match found
    return null;
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.inputRow}>
        <View 
          style={[
            styles.inputContainer, 
            { 
              backgroundColor: isDark ? colors.subtle : '#f5f5f5',
              borderColor: colors.border 
            }
          ]}
        >
          <Sparkles size={18} color={colors.textSecondary} style={styles.inputIcon} />
          <TextInput
            style={[styles.input, { color: colors.text }]}
            value={inputText}
            onChangeText={setInputText}
            placeholder="Type food like '2 eggs + toast'"
            placeholderTextColor={colors.textSecondary}
            onSubmitEditing={handleSubmitText}
            returnKeyType="done"
            editable={!isProcessing}
          />
        </View>
        
        <TouchableOpacity 
          style={[
            styles.submitButton, 
            { backgroundColor: colors.primary },
            (!inputText.trim() || isProcessing) && { opacity: 0.6 }
          ]}
          onPress={handleSubmitText}
          disabled={!inputText.trim() || isProcessing}
        >
          {isProcessing ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Send size={18} color="white" />
          )}
        </TouchableOpacity>
      </View>
      
      <View style={styles.helperTextContainer}>
        <Text style={[styles.helperText, { color: colors.textSecondary }]}>
          Try: "2 boiled eggs + toast" or "bowl of oatmeal with banana"
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    height: 50,
    borderRadius: 8,
    borderWidth: 1,
    paddingHorizontal: 12,
    marginRight: 12,
  },
  inputIcon: {
    marginRight: 8,
  },
  input: {
    flex: 1,
    height: '100%',
    fontSize: 16,
  },
  submitButton: {
    width: 50,
    height: 50,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  helperTextContainer: {
    marginTop: 8,
    paddingHorizontal: 4,
  },
  helperText: {
    fontSize: 13,
    fontStyle: 'italic',
  },
});

export default SmartManualLoggingInput; 