import React, { useEffect, useState, useCallback } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView,
  Animated,
  Platform,
  Dimensions
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { contextualNavigation, ShortcutSuggestion } from '@/utils/contextualNavigation';
import { LinearGradient } from 'expo-linear-gradient';
import { useFocusEffect } from '@react-navigation/native';

interface SmartNavigationCardProps {
  title?: string;
  subtitle?: string;
  maxItems?: number;
  onNavigate?: (screen: string) => void;
}

export function SmartNavigationCard({
  title = "Quick Access",
  subtitle = "Based on your usage",
  maxItems = 6,
  onNavigate
}: SmartNavigationCardProps) {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const [suggestions, setSuggestions] = useState<ShortcutSuggestion[]>([]);
  const [recentScreens, setRecentScreens] = useState<ShortcutSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [fadeAnim] = useState(new Animated.Value(0));
  const [scaleAnim] = useState(new Animated.Value(0.97));

  // Load navigation suggestions when the component mounts or when it regains focus
  useFocusEffect(
    useCallback(() => {
      loadSuggestions();
      
      // Combined animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 400,
          useNativeDriver: Platform.OS !== 'web',
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 8,
          useNativeDriver: Platform.OS !== 'web',
        })
      ]).start();
      
      return () => {
        // Reset animations when component loses focus
        fadeAnim.setValue(0);
        scaleAnim.setValue(0.97);
      };
    }, [])
  );

  // Function to load navigation suggestions
  const loadSuggestions = async () => {
    setIsLoading(true);
    try {
      // Get personalized suggestions based on usage patterns
      const dynamicSuggestions = contextualNavigation.getShortcutSuggestions(maxItems);
      
      // Get recently visited screens
      const recentHistory = contextualNavigation.getRecentScreens(3);
      
      // Filter out duplicates (screens that appear in both lists)
      const suggestionScreens = new Set(dynamicSuggestions.map(s => s.screen));
      const uniqueRecent = recentHistory.filter(s => !suggestionScreens.has(s.screen));
      
      setSuggestions(dynamicSuggestions);
      setRecentScreens(uniqueRecent);
    } catch (error) {
      console.error('Failed to load navigation suggestions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle navigation to a screen
  const handleNavigate = (screen: string) => {
    if (onNavigate) {
      onNavigate(screen);
    } else {
      try {
        // @ts-ignore - Allow dynamic navigation
        router.push(screen);
      } catch (error) {
        console.error('Navigation error:', error);
      }
    }
  };

  // Generate colors for each suggestion item
  const getItemColor = (index: number, isSuggestion: boolean = true): string[] => {
    const suggestionGradients = [
      isDark ? ['#4f46e5', '#3730a3'] : ['#6366f1', '#4f46e5'], // indigo
      isDark ? ['#0284c7', '#0369a1'] : ['#0ea5e9', '#0284c7'], // sky
      isDark ? ['#0891b2', '#0e7490'] : ['#06b6d4', '#0891b2'], // cyan 
      isDark ? ['#0d9488', '#0f766e'] : ['#14b8a6', '#0d9488'], // teal
      isDark ? ['#059669', '#047857'] : ['#10b981', '#059669'], // emerald
      isDark ? ['#65a30d', '#4d7c0f'] : ['#84cc16', '#65a30d'], // lime
    ];
    
    const recentGradients = [
      isDark ? ['#9333ea', '#7e22ce'] : ['#a855f7', '#9333ea'], // purple
      isDark ? ['#c026d3', '#a21caf'] : ['#d946ef', '#c026d3'], // fuchsia 
      isDark ? ['#db2777', '#be185d'] : ['#ec4899', '#db2777'], // pink
    ];
    
    const colors = isSuggestion ? suggestionGradients : recentGradients;
    return colors[index % colors.length];
  };

  // If there are no suggestions and no recent screens, don't render anything
  if (!isLoading && suggestions.length === 0 && recentScreens.length === 0) {
    return null;
  }

  return (
    <Animated.View 
      style={[
        styles.container,
        { 
          backgroundColor: isDark ? colors.card : '#ffffff',
          borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }]
        }
      ]}
    >
      <View style={styles.header}>
        <View>
          <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>{subtitle}</Text>
        </View>
        <TouchableOpacity 
          style={[styles.refreshButton, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)' }]}
          onPress={loadSuggestions}
          accessibilityLabel="Refresh quick access"
          accessibilityHint="Refresh your personalized quick access shortcuts"
        >
          <Ionicons name="refresh" size={16} color={colors.primary} />
        </TouchableOpacity>
      </View>
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {suggestions.length > 0 && (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>Suggested</Text>
            <View style={styles.grid}>
              {suggestions.map((item, index) => {
                const gradientColors = getItemColor(index);
                return (
                  <TouchableOpacity
                    key={`suggestion-${item.screen}`}
                    style={styles.gridItem}
                    onPress={() => handleNavigate(item.screen)}
                    activeOpacity={0.8}
                    accessibilityLabel={item.title}
                    accessibilityHint={`Navigate to ${item.title}`}
                    accessibilityRole="button"
                  >
                    <LinearGradient 
                      colors={gradientColors as any}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 1 }}
                      style={styles.iconContainerGradient}
                    >
                      <View style={styles.iconContainer}>
                        <Ionicons name={item.icon as any} size={24} color="#ffffff" />
                      </View>
                    </LinearGradient>
                    <Text 
                      style={[styles.itemTitle, { color: colors.text }]}
                      numberOfLines={1}
                    >
                      {item.title}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
        )}
        
        {recentScreens.length > 0 && (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>Recent</Text>
            <View style={styles.recentList}>
              {recentScreens.map((item, index) => {
                const gradientColors = getItemColor(index, false);
                return (
                  <TouchableOpacity
                    key={`recent-${item.screen}`}
                    style={[
                      styles.recentItem,
                      { 
                        backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)',
                        borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                      }
                    ]}
                    onPress={() => handleNavigate(item.screen)}
                    activeOpacity={0.7}
                    accessibilityLabel={`Recent: ${item.title}`}
                    accessibilityHint={`Navigate to recently visited ${item.title}`}
                    accessibilityRole="button"
                  >
                    <LinearGradient
                      colors={gradientColors as any}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 1 }}
                      style={styles.recentIconContainerGradient}
                    >
                      <Ionicons name={item.icon as any} size={18} color="#ffffff" />
                    </LinearGradient>
                    <Text style={[styles.recentTitle, { color: colors.text }]}>{item.title}</Text>
                    <View style={[styles.recentArrow, { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }]}>
                      <Ionicons name="chevron-forward" size={16} color={colors.textSecondary} />
                    </View>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
        )}
      </ScrollView>
    </Animated.View>
  );
}

const { width } = Dimensions.get('window');
const gridItemWidth = (width - 32 - 24) / 3; // 32px for container padding, 24px for grid items padding

const styles = StyleSheet.create({
  container: {
    borderRadius: 20,
    overflow: 'hidden',
    marginHorizontal: 16,
    marginVertical: 8,
    borderWidth: 1,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 12,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
  },
  subtitle: {
    fontSize: 14,
    marginTop: 4,
    opacity: 0.8,
  },
  refreshButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    maxHeight: 350,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  section: {
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 12,
    marginLeft: 4,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  gridItem: {
    width: gridItemWidth,
    paddingHorizontal: 4,
    marginBottom: 16,
    alignItems: 'center',
  },
  iconContainerGradient: {
    width: 64,
    height: 64,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    padding: 2,
  },
  iconContainer: {
    flex: 1,
    width: '100%',
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  itemTitle: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  recentList: {
    marginTop: 4,
  },
  recentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 14,
    borderRadius: 14,
    marginBottom: 8,
    borderWidth: 1,
  },
  recentIconContainerGradient: {
    width: 38,
    height: 38,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  recentTitle: {
    flex: 1,
    fontSize: 15,
    fontWeight: '500',
  },
  recentArrow: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  }
});

export default SmartNavigationCard; 