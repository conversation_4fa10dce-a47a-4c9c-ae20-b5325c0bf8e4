import React, { useState, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, Share, Platform, Alert } from 'react-native';
import { <PERSON><PERSON>, <PERSON>, Divider, Icon } from 'react-native-paper';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { useTranslation } from '@/contexts/TranslationContext';
import { socialSharingService, SocialPlatform, MealShareData, ProgressShareData } from '../services/socialSharingService';

interface SocialShareCardProps {
  type: 'meal' | 'achievement';
  data: MealShareData | ProgressShareData;
  onShare?: () => void;
}

export function SocialShareCard({ type, data, onShare }: SocialShareCardProps) {
  const { t } = useTranslation();
  const [isSharing, setIsSharing] = useState(false);
  const cardRef = useRef(null);

  const handleShare = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      setIsSharing(true);

      let success = false;
      
      if (type === 'meal' && 'mealName' in data) {
        // It's a meal
        success = await socialSharingService.shareMeal(data as MealShareData);
      } else if (type === 'achievement' && 'title' in data) {
        // It's an achievement
        success = await socialSharingService.shareProgress(data as ProgressShareData);
      }
      
      if (success && onShare) {
        onShare();
      }
    } catch (error) {
      console.error('Error sharing content:', error);
      Alert.alert(
        t('errors.shareError'),
        t('errors.shareErrorMessage')
      );
    } finally {
      setIsSharing(false);
    }
  };

  const handleCaptureAndShare = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      setIsSharing(true);
      
      const caption = type === 'meal' 
        ? `${t('sharing.checkOutMeal')} ${(data as MealShareData).mealName}!` 
        : `${t('sharing.achievement')}: ${(data as ProgressShareData).title}`;
      
      const success = await socialSharingService.captureAndShareComponent(cardRef, caption);
      
      if (success && onShare) {
        onShare();
      }
    } catch (error) {
      console.error('Error capturing and sharing component:', error);
      Alert.alert(
        t('errors.shareError'),
        t('errors.shareErrorMessage')
      );
    } finally {
      setIsSharing(false);
    }
  };

  const renderMealContent = (mealData: MealShareData) => (
    <View style={styles.contentContainer}>
      {mealData.imageUri && (
        <Image 
          source={{ uri: mealData.imageUri }} 
          style={styles.image} 
          resizeMode="cover"
        />
      )}
      
      <View style={styles.textContainer}>
        <Text style={styles.title}>{mealData.mealName}</Text>
        <Text style={styles.date}>{new Date(mealData.date).toLocaleDateString()}</Text>
        <View style={styles.nutritionContainer}>
          <View style={styles.nutritionItem}>
            <Text style={styles.nutritionValue}>{mealData.nutritionData.calories}</Text>
            <Text style={styles.nutritionLabel}>{t('nutrition.calories')}</Text>
          </View>
          <View style={styles.nutritionItem}>
            <Text style={styles.nutritionValue}>{mealData.nutritionData.protein}g</Text>
            <Text style={styles.nutritionLabel}>{t('nutrition.protein')}</Text>
          </View>
          <View style={styles.nutritionItem}>
            <Text style={styles.nutritionValue}>{mealData.nutritionData.carbs}g</Text>
            <Text style={styles.nutritionLabel}>{t('nutrition.carbs')}</Text>
          </View>
          <View style={styles.nutritionItem}>
            <Text style={styles.nutritionValue}>{mealData.nutritionData.fat}g</Text>
            <Text style={styles.nutritionLabel}>{t('nutrition.fat')}</Text>
          </View>
        </View>
        {mealData.description && (
          <Text style={styles.description}>{mealData.description}</Text>
        )}
      </View>
    </View>
  );

  const renderAchievementContent = (achievementData: ProgressShareData) => (
    <View style={styles.contentContainer}>
      <LinearGradient
        colors={['#4F80E2', '#42B3D5']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.achievementBanner}
      >
        <Icon source="trophy" size={40} color="#FFD700" />
        <Text style={styles.achievementTitle}>{achievementData.title}</Text>
      </LinearGradient>
      
      {achievementData.imageUri && (
        <Image 
          source={{ uri: achievementData.imageUri }} 
          style={styles.achievementImage} 
          resizeMode="cover"
        />
      )}
      
      <View style={styles.textContainer}>
        <Text style={styles.date}>{new Date(achievementData.date).toLocaleDateString()}</Text>
        <Text style={styles.description}>{achievementData.description}</Text>
        
        {achievementData.stats && Object.keys(achievementData.stats).length > 0 && (
          <View style={styles.statsContainer}>
            {Object.entries(achievementData.stats).map(([key, value]) => (
              <View key={key} style={styles.statItem}>
                <Text style={styles.statValue}>{value}</Text>
                <Text style={styles.statLabel}>{key}</Text>
              </View>
            ))}
          </View>
        )}
      </View>
    </View>
  );

  return (
    <Card ref={cardRef} style={styles.card}>
      <Card.Content>
        {type === 'meal' && 'mealName' in data ? 
          renderMealContent(data as MealShareData) : 
          renderAchievementContent(data as ProgressShareData)
        }
      </Card.Content>
      
      <Divider />
      
      <Card.Actions style={styles.actions}>
        <Button 
          mode="outlined"
          loading={isSharing}
          onPress={handleShare}
          icon="share"
          style={styles.button}
          labelStyle={styles.buttonLabel}
        >
          {t('actions.share')}
        </Button>
        
        <Button 
          mode="contained"
          loading={isSharing}
          onPress={handleCaptureAndShare}
          icon="camera"
          style={[styles.button, styles.captureButton]}
          labelStyle={styles.captureButtonLabel}
        >
          {t('actions.captureAndShare')}
        </Button>
      </Card.Actions>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    elevation: 4,
  },
  contentContainer: {
    flexDirection: 'column',
  },
  image: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 16,
  },
  textContainer: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  date: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    color: '#333',
    marginVertical: 8,
  },
  nutritionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 12,
    flexWrap: 'wrap',
  },
  nutritionItem: {
    alignItems: 'center',
    minWidth: 80,
  },
  nutritionValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  nutritionLabel: {
    fontSize: 14,
    color: '#666',
  },
  actions: {
    justifyContent: 'space-between',
    paddingHorizontal: 8,
    paddingVertical: 8,
  },
  button: {
    flex: 1,
    marginHorizontal: 4,
  },
  buttonLabel: {
    fontSize: 14,
  },
  captureButton: {
    backgroundColor: '#007AFF',
  },
  captureButtonLabel: {
    fontSize: 14,
    color: 'white',
  },
  achievementBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  achievementTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: 'white',
    marginLeft: 12,
  },
  achievementImage: {
    width: '100%',
    height: 160,
    borderRadius: 8,
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 12,
  },
  statItem: {
    alignItems: 'center',
    minWidth: 80,
    marginVertical: 8,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#28A745',
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
  },
});

export default SocialShareCard; 