import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Pressable,
  KeyboardAvoidingView,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useAccessibilityInput } from './AccessibilityInputProvider';

// For hardware switch devices (typically connected via Bluetooth), we would integrate 
// with platform-specific APIs, which is beyond the scope of this demo

interface SwitchControlProps {
  /**
   * Array of options to scan through
   */
  options: string[];
  
  /**
   * Function called when an option is selected
   */
  onSelect: (option: string) => void;
  
  /**
   * Whether the switch control is active
   */
  active?: boolean;
  
  /**
   * Custom rendering for each option
   */
  renderOption?: (option: string, isActive: boolean) => React.ReactNode;
  
  /**
   * Additional style for the container
   */
  style?: any;
}

/**
 * Component that implements switch access control for users with motor impairments
 * It automatically scans through options and allows selection with a single switch
 */
export function SwitchControl({
  options,
  onSelect,
  active = true,
  renderOption,
  style,
}: SwitchControlProps) {
  const { colors, isDark } = useTheme();
  const { 
    preferences, 
    startSwitchScanning, 
    stopSwitchScanning, 
    triggerSwitchSelection,
    isScanning, 
    currentScanIndex 
  } = useAccessibilityInput();
  
  // Start/stop scanning when active state changes
  useEffect(() => {
    if (active && options.length > 0) {
      // Start scanning
      startSwitchScanning(options, onSelect);
    } else {
      // Stop scanning
      stopSwitchScanning();
    }
    
    // Clean up on unmount
    return () => {
      stopSwitchScanning();
    };
  }, [active, options, onSelect, startSwitchScanning, stopSwitchScanning]);
  
  // Handle keypress events for keyboard-based switches
  useEffect(() => {
    if (Platform.OS !== 'web') return;
    
    const handleKeyDown = (event: KeyboardEvent) => {
      // Space or Enter key triggers selection
      if (event.key === ' ' || event.key === 'Enter') {
        triggerSwitchSelection();
        event.preventDefault();
      }
    };
    
    // Add event listener
    document.addEventListener('keydown', handleKeyDown);
    
    // Clean up
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [triggerSwitchSelection]);
  
  // If not active or no options, render nothing
  if (!active || options.length === 0) {
    return null;
  }
  
  return (
    <KeyboardAvoidingView
      style={[
        styles.container,
        {
          backgroundColor: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
        },
        style,
      ]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Options */}
      <View style={styles.optionsContainer}>
        {options.map((option, index) => {
          const isActive = isScanning && index === currentScanIndex;
          
          // If custom rendering is provided, use it
          if (renderOption) {
            return (
              <View key={index} style={styles.optionContainer}>
                {renderOption(option, isActive)}
              </View>
            );
          }
          
          // Otherwise, use default rendering
          return (
            <View
              key={index}
              style={[
                styles.option,
                {
                  backgroundColor: isActive
                    ? colors.primary
                    : isDark
                      ? colors.card
                      : '#FFFFFF',
                  borderColor: isActive ? colors.primary : colors.border,
                },
              ]}
            >
              <Text
                style={[
                  styles.optionText,
                  {
                    color: isActive ? '#FFFFFF' : colors.text,
                    fontWeight: isActive ? '700' : '400',
                  },
                ]}
              >
                {option}
              </Text>
            </View>
          );
        })}
      </View>
      
      {/* Control button for triggering selection */}
      <TouchableOpacity
        style={[
          styles.controlButton,
          { backgroundColor: colors.primary },
        ]}
        onPress={triggerSwitchSelection}
        accessibilityRole="button"
        accessibilityLabel="Select current option"
      >
        <Text style={styles.controlButtonText}>Select</Text>
      </TouchableOpacity>
      
      {/* Instructions */}
      <Text style={[styles.instructions, { color: colors.textSecondary }]}>
        Press the Select button or Space/Enter key when the desired option is highlighted
      </Text>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    padding: 16,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 10,
    zIndex: 1000,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 16,
  },
  optionContainer: {
    margin: 8,
  },
  option: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 2,
    margin: 8,
    minWidth: 100,
    alignItems: 'center',
  },
  optionText: {
    fontSize: 16,
  },
  controlButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginVertical: 16,
  },
  controlButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  instructions: {
    textAlign: 'center',
    marginTop: 8,
    fontSize: 14,
  },
});

export default SwitchControl; 