import React, { useEffect, useRef } from 'react';
import { useToast } from './Toast';

interface SyncNotificationProps {
  /**
   * When data was last synced, either as a Date object or a string description
   */
  lastSyncedTime: Date | string;
  
  /**
   * Whether to show the notification
   */
  show: boolean;
  
  /**
   * Callback when notification is dismissed
   */
  onDismiss?: () => void;
  
  /**
   * Duration to show the notification in milliseconds
   * @default 2000 (2 seconds)
   */
  duration?: number;
}

/**
 * Component that shows a temporary "Last synced" notification
 */
export function SyncNotification({
  lastSyncedTime,
  show,
  onDismiss,
  duration = 2000,
}: SyncNotificationProps) {
  const toast = useToast();
  const toastIdRef = useRef<string | null>(null);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  
  useEffect(() => {
    // Clear any existing timeouts when component updates
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    
    // When show becomes true, display the toast
    if (show) {
      // Format the time message
      const timeMessage = typeof lastSyncedTime === 'string' 
        ? lastSyncedTime 
        : formatSyncTime(lastSyncedTime);
      
      // Show the toast notification
      toastIdRef.current = toast.showToast({
        message: `Last synced ${timeMessage}`,
        type: 'info',
        duration,
      });
      
      // Call onDismiss callback when toast disappears
      if (onDismiss) {
        timeoutRef.current = setTimeout(() => {
          // Ensure the toast is hidden
          if (toastIdRef.current) {
            toast.hideToast(toastIdRef.current);
            toastIdRef.current = null;
          }
          
          // Call the onDismiss callback
          onDismiss();
        }, duration);
      }
    }
    
    // Cleanup function to clear timeout and hide toast when unmounting
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      if (toastIdRef.current) {
        toast.hideToast(toastIdRef.current);
      }
    };
  }, [show, lastSyncedTime, duration, onDismiss, toast]);
  
  // Component doesn't render anything itself
  return null;
}

/**
 * Formats a date into a human-readable relative time string
 */
function formatSyncTime(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  
  if (diffSec < 10) {
    return 'just now';
  } else if (diffSec < 60) {
    return `${diffSec} seconds ago`;
  } else if (diffMin === 1) {
    return '1 minute ago';
  } else if (diffMin < 60) {
    return `${diffMin} minutes ago`;
  } else if (diffHour === 1) {
    return '1 hour ago';
  } else if (diffHour < 24) {
    return `${diffHour} hours ago`;
  } else {
    // For longer periods, use the date
    return date.toLocaleDateString();
  }
} 