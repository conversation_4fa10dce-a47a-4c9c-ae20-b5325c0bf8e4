import React from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useDatabase } from '@/contexts/DatabaseContext';
import { formatDistanceToNow } from 'date-fns';
import { Feather } from '@expo/vector-icons';

export const SyncStatusIndicator = () => {
  const { colors } = useTheme();
  const { syncing, lastSyncTime } = useDatabase();

  // Only render when actively syncing
  if (!syncing) {
    return null;
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <View style={styles.syncingContainer}>
        <ActivityIndicator size="small" color={colors.primary} style={styles.icon} />
        <Text style={[styles.text, { color: colors.text }]}>Syncing data...</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    margin: 8,
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    position: 'absolute',
    bottom: 16,
    zIndex: 100,
  },
  syncingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  syncedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginRight: 6,
  },
  text: {
    fontSize: 12,
    fontWeight: '500',
  },
});

export default SyncStatusIndicator; 