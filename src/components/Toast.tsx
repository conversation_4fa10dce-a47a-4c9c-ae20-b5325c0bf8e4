import React, { useState, useEffect, useRef, createContext, useContext } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
  Platform,
  Dimensions,
  StyleProp,
  ViewStyle,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useAccessibility } from './AccessibilityProvider';
import { Feather , MaterialIcons } from '@expo/vector-icons';

// Types of toasts
export type ToastType = 'success' | 'error' | 'info' | 'warning';

// Toast position options
export type ToastPosition = 'top' | 'bottom';

// Toast message structure
export interface ToastMessage {
  /**
   * Unique ID for the toast
   */
  id: string;
  
  /**
   * Message to display
   */
  message: string;
  
  /**
   * Type of toast
   */
  type: ToastType;
  
  /**
   * Duration to show the toast in ms (0 for infinite)
   */
  duration?: number;
  
  /**
   * Optional action button text
   */
  actionText?: string;
  
  /**
   * Optional action callback
   */
  onAction?: () => void;
}

// Context for toast management
type ToastContextType = {
  showToast: (toast: Omit<ToastMessage, 'id'>) => string;
  hideToast: (id: string) => void;
  hideAllToasts: () => void;
};

const ToastContext = createContext<ToastContextType>({
  showToast: () => '',
  hideToast: () => {},
  hideAllToasts: () => {},
});

// Props for ToastProvider
interface ToastProviderProps {
  /**
   * Child components
   */
  children: React.ReactNode;
  
  /**
   * Position of toasts
   */
  position?: ToastPosition;
  
  /**
   * Default duration for toasts in ms
   */
  defaultDuration?: number;
  
  /**
   * Maximum number of toasts to show simultaneously
   */
  maxToasts?: number;
  
  /**
   * Offset from the edge of the screen
   */
  offset?: number;
}

/**
 * Toast provider component that manages toast notifications
 */
export function ToastProvider({
  children,
  position = 'bottom',
  defaultDuration = 3000,
  maxToasts = 3,
  offset = 16,
}: ToastProviderProps) {
  // State for toast messages
  const [toasts, setToasts] = useState<ToastMessage[]>([]);
  
  // Show a new toast
  const showToast = (toast: Omit<ToastMessage, 'id'>) => {
    // Generate a unique ID for this toast
    const id = Date.now().toString();
    
    // Create the new toast with defaults
    const newToast: ToastMessage = {
      id,
      duration: defaultDuration,
      ...toast,
    };
    
    // Add to toast list, limiting to max number of toasts
    setToasts(prev => [newToast, ...prev].slice(0, maxToasts));
    
    // Set a timer to auto-dismiss if duration > 0
    if (newToast.duration && newToast.duration > 0) {
      setTimeout(() => {
        hideToast(id);
      }, newToast.duration);
    }
    
    return id;
  };
  
  // Hide a specific toast
  const hideToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };
  
  // Hide all toasts
  const hideAllToasts = () => {
    setToasts([]);
  };
  
  return (
    <ToastContext.Provider value={{ showToast, hideToast, hideAllToasts }}>
      {children}
      <ToastContainer 
        toasts={toasts} 
        position={position} 
        onDismiss={hideToast}
        offset={offset}
      />
    </ToastContext.Provider>
  );
}

// Props for the individual toast component
interface ToastItemProps {
  /**
   * Toast message data
   */
  toast: ToastMessage;
  
  /**
   * Callback for when the toast is dismissed
   */
  onDismiss: (id: string) => void;
  
  /**
   * Style for the toast container
   */
  style?: StyleProp<ViewStyle>;
}

/**
 * Individual toast notification component
 */
function ToastItem({ toast, onDismiss, style }: ToastItemProps) {
  const { colors, isDark } = useTheme();
  const { isReduceMotionEnabled } = useAccessibility();
  
  // Animation value for fade and slide
  const animValue = useRef(new Animated.Value(0)).current;
  
  // Start animation when component mounts
  useEffect(() => {
    // Animate in
    Animated.timing(animValue, {
      toValue: 1,
      duration: isReduceMotionEnabled ? 0 : 300,
      useNativeDriver: true,
    }).start();
    
    // Return cleanup function
    return () => {
      animValue.setValue(0);
    };
  }, []);
  
  // Handle dismissing the toast with animation
  const handleDismiss = () => {
    // Animate out
    Animated.timing(animValue, {
      toValue: 0,
      duration: isReduceMotionEnabled ? 0 : 200,
      useNativeDriver: true,
    }).start(() => {
      // Call the dismiss callback
      onDismiss(toast.id);
    });
  };
  
  // Get the appropriate icon and color based on toast type
  const getIconAndColor = () => {
    switch (toast.type) {
      case 'success':
        return {
          icon: <MaterialIcons name="check-circle" size={20} color={colors.success} />,
          backgroundColor: isDark ? 'rgba(34, 197, 94, 0.2)' : 'rgba(34, 197, 94, 0.1)',
          borderColor: colors.success,
        };
      case 'error':
        return {
          icon: <AlertOctagon size={20} color={colors.error} />,
          backgroundColor: isDark ? 'rgba(239, 68, 68, 0.2)' : 'rgba(239, 68, 68, 0.1)',
          borderColor: colors.error,
        };
      case 'warning':
        return {
          icon: <Feather name="alert-triangle" size={20} color={colors.warning} />,
          backgroundColor: isDark ? 'rgba(245, 158, 11, 0.2)' : 'rgba(245, 158, 11, 0.1)',
          borderColor: colors.warning,
        };
      case 'info':
      default:
        return {
          icon: <Feather name="info" size={20} color={colors.info} />,
          backgroundColor: isDark ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)',
          borderColor: colors.info,
        };
    }
  };
  
  const { icon, backgroundColor, borderColor } = getIconAndColor();
  
  return (
    <Animated.View
      style={[
        styles.toastItem,
        {
          backgroundColor: isDark ? colors.card : '#FFFFFF',
          borderLeftColor: borderColor,
          opacity: animValue,
          transform: [
            {
              translateY: animValue.interpolate({
                inputRange: [0, 1],
                outputRange: [20, 0],
              }),
            },
          ],
          ...(Platform.OS === 'web' ? { cursor: 'pointer' as const } : {}),
        },
        style,
      ]}
      accessibilityLiveRegion="polite"
      accessibilityRole="alert"
    >
      <View style={[styles.iconContainer, { backgroundColor }]}>
        {icon}
      </View>
      
      <Text style={[styles.message, { color: colors.text }]}>
        {toast.message}
      </Text>
      
      <View style={styles.actionsContainer}>
        {toast.actionText && (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => {
              toast.onAction?.();
              handleDismiss();
            }}
            accessibilityRole="button"
            accessibilityLabel={toast.actionText}
          >
            <Text style={[styles.actionText, { color: colors.primary }]}>
              {toast.actionText}
            </Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={styles.closeButton}
          onPress={handleDismiss}
          accessibilityRole="button"
          accessibilityLabel="Dismiss notification"
        >
          <Feather name="x" size={16} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
}

// Props for the toast container component
interface ToastContainerProps {
  /**
   * List of toasts to display
   */
  toasts: ToastMessage[];
  
  /**
   * Position of the toast container
   */
  position: ToastPosition;
  
  /**
   * Callback for when a toast is dismissed
   */
  onDismiss: (id: string) => void;
  
  /**
   * Offset from the edge of the screen
   */
  offset: number;
}

/**
 * Container for all toast notifications
 */
function ToastContainer({ toasts, position, onDismiss, offset }: ToastContainerProps) {
  // Get screen dimensions
  const { width: screenWidth } = Dimensions.get('window');
  
  return (
    <View
      style={[
        styles.container,
        {
          [position]: offset,
          width: screenWidth,
          pointerEvents: 'box-none',
        },
      ]}
    >
      {toasts.map(toast => (
        <ToastItem
          key={toast.id}
          toast={toast}
          onDismiss={onDismiss}
          style={styles.toast}
        />
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    alignItems: 'center',
    zIndex: 9999,
    padding: 16,
  },
  toast: {
    marginBottom: 8,
  },
  toastItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    maxWidth: 500,
    borderRadius: 8,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
    borderLeftWidth: 4,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  message: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
  },
  actionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
  actionButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
});

/**
 * Hook to use the toast functionality
 */
export function useToast() {
  const context = useContext(ToastContext);
  
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  
  return context;
}

export default ToastProvider; 