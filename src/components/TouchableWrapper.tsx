import React from 'react';
import {
  TouchableOpacity,
  TouchableHighlight,
  TouchableWithoutFeedback,
  Pressable,
  StyleSheet,
  View,
  ViewStyle,
  GestureResponderEvent,
  StyleProp,
  Platform,
} from 'react-native';

// Minimum touch target size recommended for accessibility
const MIN_TOUCH_SIZE = 44;

type TouchableType = 'opacity' | 'highlight' | 'withoutFeedback' | 'pressable';

interface TouchableWrapperProps {
  /**
   * The type of touchable component to use
   */
  touchableType?: TouchableType;
  
  /**
   * Optional style for the wrapper
   */
  style?: StyleProp<ViewStyle>;
  
  /**
   * Handler for press events
   */
  onPress?: (event: GestureResponderEvent) => void;
  
  /**
   * Optional handler for long press events
   */
  onLongPress?: (event: GestureResponderEvent) => void;
  
  /**
   * Optional accessibility label
   */
  accessibilityLabel?: string;
  
  /**
   * Optional accessibility hint
   */
  accessibilityHint?: string;
  
  /**
   * Optional accessibility role
   */
  accessibilityRole?: 'button' | 'link' | 'checkbox' | 'radio' | 'menuitem' | 'switch' | 'tab' | 'none';
  
  /**
   * Optional handler for press in events (TouchableHighlight)
   */
  onPressIn?: (event: GestureResponderEvent) => void;
  
  /**
   * Optional handler for press out events (TouchableHighlight)
   */
  onPressOut?: (event: GestureResponderEvent) => void;
  
  /**
   * Optional active opacity for TouchableOpacity
   */
  activeOpacity?: number;
  
  /**
   * Optional highlight color for TouchableHighlight
   */
  underlayColor?: string;
  
  /**
   * Optional delay for long press detection
   */
  delayLongPress?: number;
  
  /**
   * Whether the component is currently disabled
   */
  disabled?: boolean;
  
  /**
   * Children components
   */
  children: React.ReactNode;
  
  /**
   * Optional width override (will still ensure minimum size)
   */
  width?: number;
  
  /**
   * Optional height override (will still ensure minimum size)
   */
  height?: number;
  
  /**
   * Optional test ID for testing
   */
  testID?: string;
}

/**
 * A wrapper component that ensures any touchable element meets the minimum
 * accessibility size requirements of 44x44 points, while maintaining the
 * visual appearance of the original component.
 */
export function TouchableWrapper({
  touchableType = 'opacity',
  style,
  onPress,
  onLongPress,
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole = 'button',
  onPressIn,
  onPressOut,
  activeOpacity = 0.8,
  underlayColor = '#DDDDDD',
  delayLongPress,
  disabled = false,
  children,
  width,
  height,
  testID,
}: TouchableWrapperProps) {
  // Calculate effective dimensions, ensuring they meet minimum size
  const effectiveWidth = width ? Math.max(width, MIN_TOUCH_SIZE) : MIN_TOUCH_SIZE;
  const effectiveHeight = height ? Math.max(height, MIN_TOUCH_SIZE) : MIN_TOUCH_SIZE;
  
  // Combine user styles with minimum size requirements
  const combinedStyle = [
    styles.touchArea,
    { minWidth: effectiveWidth, minHeight: effectiveHeight },
    style,
  ];
  
  // Platform-specific Halo effect for Android and Web
  const platformSpecificProps = Platform.OS === 'android' || Platform.OS === 'web'
    ? {
        android_ripple: { color: 'rgba(0, 0, 0, 0.1)', borderless: false },
      }
    : {};
  
  // Common accessibility props
  const accessibilityProps = {
    accessible: true,
    accessibilityRole,
    accessibilityLabel,
    accessibilityHint,
    accessibilityState: { disabled },
  };
  
  // Additional common props
  const commonProps = {
    onPress,
    onLongPress,
    onPressIn,
    onPressOut,
    delayLongPress,
    disabled,
    testID,
    ...accessibilityProps,
  };
  
  // Render the appropriate touchable component based on user preference
  switch (touchableType) {
    case 'highlight':
      return (
        <TouchableHighlight
          style={combinedStyle}
          underlayColor={underlayColor}
          {...commonProps}
        >
          <View style={styles.contentContainer}>
            {children}
          </View>
        </TouchableHighlight>
      );
      
    case 'withoutFeedback':
      return (
        <TouchableWithoutFeedback {...commonProps}>
          <View style={[combinedStyle, styles.contentContainer]}>
            {children}
          </View>
        </TouchableWithoutFeedback>
      );
      
    case 'pressable':
      return (
        <Pressable
          style={({ pressed }) => [
            combinedStyle,
            pressed && { opacity: activeOpacity }
          ]}
          {...platformSpecificProps}
          {...commonProps}
        >
          <View style={styles.contentContainer}>
            {children}
          </View>
        </Pressable>
      );
      
    case 'opacity':
    default:
      return (
        <TouchableOpacity
          style={combinedStyle}
          activeOpacity={activeOpacity}
          {...commonProps}
        >
          <View style={styles.contentContainer}>
            {children}
          </View>
        </TouchableOpacity>
      );
  }
}

const styles = StyleSheet.create({
  touchArea: {
    // Minimum touch target size is handled through props
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    // This container preserves the original size of the children
    // while the outer component ensures minimum touch target size
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default TouchableWrapper; 