import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useInteractiveTutorial } from './InteractiveTutorialProvider';
import { Feather } from '@expo/vector-icons';

interface TutorialLauncherButtonProps {
  /**
   * ID of the tutorial to launch
   */
  tutorialId: string;
  
  /**
   * Optional custom label for the button
   */
  label?: string;
  
  /**
   * Optional style for the button
   */
  style?: any;
  
  /**
   * Optional icon position
   */
  iconPosition?: 'left' | 'right';
  
  /**
   * Whether to show the icon
   */
  showIcon?: boolean;
}

/**
 * A button that launches an interactive tutorial when pressed
 */
export function TutorialLauncherButton({
  tutorialId,
  label = 'Show Tutorial',
  style,
  iconPosition = 'left',
  showIcon = true,
}: TutorialLauncherButtonProps) {
  const { colors } = useTheme();
  const { startTutorial, getTutorialById } = useInteractiveTutorial();
  
  // Get tutorial definition to use its name
  const tutorial = getTutorialById(tutorialId);
  
  // Use custom label or generate one based on tutorial name
  const buttonLabel = label || (tutorial ? `Learn ${tutorial.name}` : 'Show Tutorial');
  
  // Launch the tutorial when pressed
  const handlePress = () => {
    startTutorial(tutorialId);
  };
  
  return (
    <TouchableOpacity
      style={[
        styles.button,
        { backgroundColor: colors.primary },
        style,
      ]}
      onPress={handlePress}
      accessibilityRole="button"
      accessibilityLabel={buttonLabel}
    >
      {showIcon && iconPosition === 'left' && (
        <Feather name="help-circle" size={18} style={styles.leftIcon} />
      )}
      
      <Text style={styles.buttonText}>{buttonLabel}</Text>
      
      {showIcon && iconPosition === 'right' && (
        <Feather name="help-circle" size={18} style={styles.rightIcon} />
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  leftIcon: {
    marginRight: 8,
  },
  rightIcon: {
    marginLeft: 8,
  },
});

export default TutorialLauncherButton; 