import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useInteractiveTutorial } from './InteractiveTutorialProvider';
import { Feather } from '@expo/vector-icons';

interface TutorialSuggestionCardProps {
  /**
   * Title for the card
   */
  title?: string;
  
  /**
   * Category to filter tutorials by
   */
  category?: 'nutrition' | 'fitness' | 'sleep' | 'general' | 'settings';
  
  /**
   * Max number of tutorials to show
   */
  maxDisplay?: number;
  
  /**
   * Callback when a tutorial is selected
   */
  onSelect?: (tutorialId: string) => void;
}

/**
 * A card that displays available tutorials and allows the user to start them
 */
export function TutorialSuggestionCard({
  title = 'Learn App Features',
  category,
  maxDisplay = 3,
  onSelect,
}: TutorialSuggestionCardProps) {
  const { colors, isDark } = useTheme();
  const { getTutorialById, startTutorial, hasTutorialBeenCompleted } = useInteractiveTutorial();
  
  // State for available tutorials and completion status
  const [availableTutorials, setAvailableTutorials] = useState<{
    id: string;
    name: string;
    description: string;
    completed: boolean;
  }[]>([]);
  
  // Load available tutorials
  useEffect(() => {
    const loadTutorials = async () => {
      // In a real implementation, you would fetch this from the provider
      // For now, we'll hardcode the tutorial IDs we know exist
      const tutorialIds = ['core-features', 'nutrition-tracking'];
      
      const tutorialsWithStatus = await Promise.all(
        tutorialIds
          .map(async (id) => {
            const tutorial = getTutorialById(id);
            if (!tutorial) return null;
            
            // Only include tutorials of the requested category if specified
            if (category && tutorial.category !== category) return null;
            
            const completed = await hasTutorialBeenCompleted(id);
            
            return {
              id: tutorial.id,
              name: tutorial.name,
              description: tutorial.description,
              completed,
            };
          })
          .filter((tutorial): tutorial is Promise<{
            id: string;
            name: string;
            description: string;
            completed: boolean;
          }> => tutorial !== null)
      );
      
      setAvailableTutorials(tutorialsWithStatus);
    };
    
    loadTutorials();
  }, [category, getTutorialById, hasTutorialBeenCompleted]);
  
  // Start a tutorial
  const handleTutorialSelect = (tutorialId: string) => {
    startTutorial(tutorialId);
    onSelect?.(tutorialId);
  };
  
  // If no tutorials available, don't render
  if (availableTutorials.length === 0) {
    return null;
  }
  
  // Limit the number of tutorials shown
  const displayedTutorials = availableTutorials.slice(0, maxDisplay);
  
  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDark ? colors.card : '#FFFFFF' },
      ]}
    >
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Feather name="help-circle" size={20} color={colors.primary} style={styles.titleIcon} />
          <Text style={[styles.title, { color: colors.text }]}>
            {title}
          </Text>
        </View>
      </View>
      
      <ScrollView style={styles.tutorialList}>
        {displayedTutorials.map((tutorial) => (
          <TouchableOpacity
            key={tutorial.id}
            style={[
              styles.tutorialItem,
              {
                borderBottomColor: colors.border,
              },
            ]}
            onPress={() => handleTutorialSelect(tutorial.id)}
          >
            <View style={styles.tutorialInfo}>
              <Text
                style={[
                  styles.tutorialName,
                  { color: colors.text },
                ]}
              >
                {tutorial.name}
              </Text>
              <Text
                style={[
                  styles.tutorialDescription,
                  { color: colors.textSecondary },
                ]}
                numberOfLines={2}
              >
                {tutorial.description}
              </Text>
            </View>
            
            <View style={styles.statusContainer}>
              {tutorial.completed ? (
                <View
                  style={[
                    styles.completedBadge,
                    { backgroundColor: isDark ? 'rgba(34, 197, 94, 0.2)' : 'rgba(34, 197, 94, 0.1)' },
                  ]}
                >
                  <Feather name="check" size={14} color={colors.success} />
                  <Text
                    style={[
                      styles.completedText,
                      { color: colors.success },
                    ]}
                  >
                    Completed
                  </Text>
                </View>
              ) : (
                <Feather name="chevron-right" size={20} color={colors.primary} />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
      
      {availableTutorials.length > maxDisplay && (
        <TouchableOpacity
          style={[
            styles.viewAllButton,
            { borderTopColor: colors.border },
          ]}
        >
          <Text style={[styles.viewAllText, { color: colors.primary }]}>
            View All Tutorials
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginVertical: 8,
    marginHorizontal: 16,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleIcon: {
    marginRight: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  tutorialList: {
    maxHeight: 300,
  },
  tutorialItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    alignItems: 'center',
  },
  tutorialInfo: {
    flex: 1,
    marginRight: 8,
  },
  tutorialName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  tutorialDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  completedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  completedText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  viewAllButton: {
    padding: 16,
    alignItems: 'center',
    borderTopWidth: 1,
  },
  viewAllText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default TutorialSuggestionCard; 