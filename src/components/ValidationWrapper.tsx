import React, { useState, useEffect, ReactNode } from 'react';
import { View, Text, StyleSheet, Animated, Easing, StyleProp, ViewStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAccessibility } from './AccessibilityProvider';

interface ValidationRule {
  /**
   * Test function that validates the input value
   */
  test: (value: string) => boolean;

  /**
   * Error message to display when validation fails
   */
  message: string;
}

interface ValidationWrapperProps {
  /**
   * The input value to validate
   */
  value: string;

  /**
   * Array of validation rules to apply
   */
  validationRules: ValidationRule[];

  /**
   * Children to render inside the wrapper
   */
  children: ReactNode;

  /**
   * When to trigger validation
   * - onChange: validate on every change (default)
   * - onBlur: validate when input loses focus
   * - onSubmit: validate only when explicitly triggered via validateInput
   */
  validateOn?: 'onChange' | 'onBlur' | 'onSubmit';
  
  /**
   * Called when validation state changes
   */
  onValidationChange?: (isValid: boolean, errorMessage: string | null) => void;
  
  /**
   * Additional style for the wrapper container
   */
  style?: StyleProp<ViewStyle>;
  
  /**
   * Whether to always show validation state (success or error)
   */
  alwaysShowValidation?: boolean;

  /**
   * ID for identifying this input in a form
   */
  inputId?: string;

  /**
   * Whether to show success icon when valid
   */
  showSuccessIcon?: boolean;

  /**
   * Whether to show the error state with color and icon
   */
  showErrorState?: boolean;

  /**
   * Whether to show the error message text
   */
  showErrorMessage?: boolean;

  /**
   * Minimum value length before validation starts
   */
  minLengthBeforeValidation?: number;
}

/**
 * ValidationWrapper component
 * 
 * Wraps form inputs with real-time validation and error messages
 */
export function ValidationWrapper({
  value,
  validationRules,
  children,
  validateOn = 'onChange',
  onValidationChange,
  style,
  alwaysShowValidation = false,
  inputId,
  showSuccessIcon = true,
  showErrorState = true,
  showErrorMessage = true,
  minLengthBeforeValidation = 0,
}: ValidationWrapperProps) {
  const [isDirty, setIsDirty] = useState(false);
  const [isValid, setIsValid] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [shouldValidate, setShouldValidate] = useState(validateOn === 'onChange');
  const [shakeAnimation] = useState(new Animated.Value(0));
  const { isScreenReaderEnabled } = useAccessibility();

  // Function to validate the input value
  const validateInput = () => {
    // Don't validate empty inputs unless they're dirty or we always show validation
    if (value.length < minLengthBeforeValidation && !isDirty && !alwaysShowValidation) {
      setIsValid(true);
      setErrorMessage(null);
      return true;
    }

    // Check all validation rules
    for (const rule of validationRules) {
      const passes = rule.test(value);
      if (!passes) {
        setIsValid(false);
        setErrorMessage(rule.message);
        
        // If screen reader is enabled, don't shake the input
        if (showErrorState && !isScreenReaderEnabled) {
          Animated.sequence([
            Animated.timing(shakeAnimation, {
              toValue: 10,
              duration: 100,
              useNativeDriver: true,
              easing: Easing.linear,
            }),
            Animated.timing(shakeAnimation, {
              toValue: -10,
              duration: 100,
              useNativeDriver: true,
              easing: Easing.linear,
            }),
            Animated.timing(shakeAnimation, {
              toValue: 6,
              duration: 100,
              useNativeDriver: true,
              easing: Easing.linear,
            }),
            Animated.timing(shakeAnimation, {
              toValue: -6,
              duration: 100,
              useNativeDriver: true,
              easing: Easing.linear,
            }),
            Animated.timing(shakeAnimation, {
              toValue: 0,
              duration: 100,
              useNativeDriver: true,
              easing: Easing.linear,
            }),
          ]).start();
        }
        
        if (onValidationChange) {
          onValidationChange(false, rule.message);
        }
        return false;
      }
    }

    // All rules passed
    setIsValid(true);
    setErrorMessage(null);
    
    if (onValidationChange) {
      onValidationChange(true, null);
    }
    return true;
  };

  // Validate when value changes, if validateOn is 'onChange'
  useEffect(() => {
    if (shouldValidate || validateOn === 'onChange') {
      validateInput();
    }
    
    if (value.length > 0 && !isDirty) {
      setIsDirty(true);
    }
  }, [value, shouldValidate]);

  // Handle input blur event
  const handleBlur = () => {
    if (validateOn === 'onBlur') {
      setShouldValidate(true);
      validateInput();
    }
    setIsDirty(true);
  };

  // Determine if we should show validation state
  const showValidation = () => {
    return (
      alwaysShowValidation || 
      (isDirty && shouldValidate) || 
      (isDirty && validateOn === 'onChange')
    );
  };

  // Make validation state accessible by screen readers
  const getAccessibilityProps = () => {
    if (isScreenReaderEnabled && errorMessage && showValidation()) {
      return {
        accessibilityLabel: `Input ${errorMessage ? 'has error: ' + errorMessage : 'is valid'}`,
        accessibilityLiveRegion: 'polite' as 'polite',
      };
    }
    return {};
  };

  return (
    <View style={[styles.container, style]}>
      <Animated.View 
        style={[
          styles.inputContainer, 
          showValidation() && !isValid && showErrorState && styles.errorState,
          showValidation() && isValid && styles.validState,
          {transform: [{translateX: shakeAnimation}]}
        ]}
        {...getAccessibilityProps()}
      >
        {children}
        
        {showValidation() && showSuccessIcon && isValid && (
          <View style={styles.iconContainer}>
            <Ionicons name="checkmark-circle" size={24} color="#4BB543" />
          </View>
        )}
        
        {showValidation() && !isValid && showErrorState && (
          <View style={styles.iconContainer}>
            <Ionicons name="alert-circle" size={24} color="#FF3B30" />
          </View>
        )}
      </Animated.View>
      
      {showValidation() && errorMessage && showErrorMessage && (
        <Text style={styles.errorText}>
          {errorMessage}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  inputContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  errorState: {
    borderColor: '#FF3B30',
    backgroundColor: 'rgba(255, 59, 48, 0.05)',
  },
  validState: {
    borderColor: '#4BB543',
  },
  iconContainer: {
    position: 'absolute',
    right: 12,
    top: '50%',
    marginTop: -12,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 12,
    marginTop: 4,
    marginLeft: 8,
  },
});

export default ValidationWrapper; 