import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
  Platform,
  ToastAndroid,
  Alert,
} from 'react-native';
import { Ionicons , Feather } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '@/contexts/ThemeContext';
import { Audio } from 'expo-av';
import * as Speech from 'expo-speech';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import { router } from 'expo-router';

// Define all available voice commands
const VOICE_COMMANDS = {
  NAVIGATION: {
    GO_HOME: ['go home', 'home screen', 'take me home'],
    GO_PROFILE: ['go to profile', 'show profile', 'my profile'],
    GO_MEALS: ['go to meals', 'meal planner', 'show meals'],
    GO_WATER: ['water tracking', 'track water', 'show water'],
    GO_SLEEP: ['go to sleep', 'sleep tracking', 'show sleep'],
    GO_DASHBOARD: ['go to dashboard', 'show dashboard', 'customizable dashboard'],
    GO_BACK: ['go back', 'previous screen', 'back'],
  },
  LOGGING: {
    LOG_WATER: ['log water', 'add water', 'track water intake'],
    LOG_MEAL: ['log meal', 'add meal', 'track food'],
    LOG_SLEEP: ['log sleep', 'add sleep', 'track sleep'],
    LOG_MOOD: ['log mood', 'add mood', 'track mood'],
  },
  DASHBOARD: {
    EDIT_DASHBOARD: ['edit dashboard', 'customize dashboard', 'rearrange widgets'],
    ADD_WIDGET: ['add widget', 'new widget', 'create widget'],
  },
  SYSTEM: {
    HELP: ['help', 'what can I say', 'show commands'],
    STOP_LISTENING: ['stop listening', 'stop voice', 'cancel voice'],
  },
};

interface CustomizableDashboardMethods {
  toggleEditMode: () => void;
}

// Define interface for voice control props
interface VoiceControlProps {
  onCommand?: (command: string, category: string, action: string) => void;
  enabled?: boolean;
  dashboardEditRef?: React.RefObject<CustomizableDashboardMethods>;
}

const STORAGE_KEY = 'voice_control_enabled';

export function VoiceControl({ 
  onCommand, 
  enabled: enabledProp = true,
  dashboardEditRef
}: VoiceControlProps) {
  const { colors, isDark } = useTheme();
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [results, setResults] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [enabled, setEnabled] = useState<boolean>(enabledProp);
  const [showHelp, setShowHelp] = useState(false);
  const [permissionGranted, setPermissionGranted] = useState(false);
  
  // References
  const recognitionSubscription = useRef<any>(null);
  const audioRecording = useRef<Audio.Recording | null>(null);
  
  // Load voice control preference on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const storedEnabled = await AsyncStorage.getItem(STORAGE_KEY);
        if (storedEnabled !== null) {
          setEnabled(JSON.parse(storedEnabled));
        }
      } catch (error) {
        console.error('Failed to load voice control settings:', error);
      }
      
      // Request microphone permissions
      const audioPermission = await Audio.requestPermissionsAsync();
      setPermissionGranted(audioPermission.status === 'granted');
      
      if (audioPermission.status !== 'granted') {
        setError('Microphone permission is required');
      }
    };
    
    loadSettings();
    
    // Clean up on unmount
    return () => {
      stopListening();
    };
  }, []);
  
  // Save voice control preference when it changes
  useEffect(() => {
    const saveSettings = async () => {
      try {
        await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(enabled));
      } catch (error) {
        console.error('Failed to save voice control settings:', error);
      }
    };
    
    saveSettings();
  }, [enabled]);
  
  // Start audio recording and voice recognition
  const startListening = async () => {
    if (!permissionGranted) {
      if (Platform.OS === 'android') {
        ToastAndroid.show('Microphone permission is required', ToastAndroid.SHORT);
      } else {
        Alert.alert('Permission Required', 'Microphone permission is required for voice control');
      }
      return;
    }
    
    setIsListening(true);
    setResults('');
    setError(null);
    
    try {
      // Configure audio recording
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });
      
      // Start recording
      const recording = new Audio.Recording();
      await recording.prepareToRecordAsync({
        android: {
          extension: '.m4a',
          outputFormat: Audio.AndroidOutputFormat.MPEG_4,
          audioEncoder: Audio.AndroidAudioEncoder.AAC,
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
        },
        ios: {
          extension: '.m4a',
          outputFormat: Audio.IOSOutputFormat.MPEG4AAC,
          audioQuality: Audio.IOSAudioQuality.HIGH,
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
          linearPCMBitDepth: 16,
          linearPCMIsBigEndian: false,
          linearPCMIsFloat: false,
        },
        web: {
          mimeType: 'audio/webm',
          bitsPerSecond: 128000,
        },
      });
      await recording.startAsync();
      audioRecording.current = recording;
      
      // In a real implementation, you would use a speech-to-text service here
      // For demonstration, we'll simulate a 5-second recording
      
      // After 5 seconds, stop recording automatically
      setTimeout(() => {
        if (isListening) {
          stopListening();
        }
      }, 5000);
    } catch (err) {
      console.error('Failed to start recording', err);
      setError('Failed to start voice recognition');
      setIsListening(false);
    }
  };
  
  // Stop audio recording
  const stopListening = async () => {
    if (!isListening) return;
    
    setIsListening(false);
    setIsProcessing(true);
    
    try {
      if (audioRecording.current) {
        await audioRecording.current.stopAndUnloadAsync();
        const uri = audioRecording.current.getURI();
        
        // In a real implementation, you would send the audio file to a speech-to-text service
        // For demonstration, we'll process with our simplified implementation
        if (uri) {
          processAudioFile(uri);
        } else {
          processVoiceInput(); // Fallback to simulated processing
        }
      }
    } catch (err) {
      console.error('Failed to stop recording', err);
      setError('Failed to process voice command');
      setIsProcessing(false);
    }
  };
  
  // Process audio file (simulated)
  const processAudioFile = async (uri: string) => {
    // Here you would send the audio file to a speech-to-text service
    // For example, using Google Cloud Speech-to-Text, Microsoft Azure Speech, etc.
    
    console.log('Audio file recorded at:', uri);
    
    // For this demo, we'll randomly select a command
    setTimeout(() => {
      processVoiceInput();
    }, 1000);
  };
  
  // Process the voice input (using real voice recognition would go here)
  const processVoiceInput = async () => {
    setIsProcessing(true);
    
    try {
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // For demo purposes, we're simulating voice recognition
      // In a real app, this would be replaced with actual speech-to-text results
      
      // Generate a random command for demonstration
      const categories = Object.keys(VOICE_COMMANDS);
      const randomCategory = categories[Math.floor(Math.random() * categories.length)];
      const commandTypes = Object.keys(VOICE_COMMANDS[randomCategory as keyof typeof VOICE_COMMANDS]);
      const randomCommandType = commandTypes[Math.floor(Math.random() * commandTypes.length)];
      const commandArray = VOICE_COMMANDS[randomCategory as keyof typeof VOICE_COMMANDS][randomCommandType as any];
      const randomCommand = commandArray[0];
      
      setResults(randomCommand);
      handleCommand(randomCommand);
    } catch (err) {
      console.error('Error processing voice input', err);
      setError('Failed to process voice command');
    } finally {
      setIsProcessing(false);
    }
  };
  
  // Handle recognized command
  const handleCommand = useCallback((text: string) => {
    let commandFound = false;
    const lowerText = text.toLowerCase();
    
    // Check each command category
    Object.entries(VOICE_COMMANDS).forEach(([category, commandGroup]) => {
      Object.entries(commandGroup as Record<string, string[]>).forEach(([action, phrases]) => {
        phrases.forEach(phrase => {
          if (lowerText.includes(phrase.toLowerCase())) {
            commandFound = true;
            
            // Execute the command
            executeCommand(category, action);
            
            // Call the onCommand callback if provided
            if (onCommand) {
              onCommand(phrase, category, action);
            }
          }
        });
      });
    });
    
    if (!commandFound) {
      speakResponse("I didn't understand that command. Try saying 'help' for a list of commands.");
    }
  }, [onCommand]);
  
  // Execute the recognized command
  const executeCommand = useCallback((category: string, action: string) => {
    switch (category) {
      case 'NAVIGATION':
        handleNavigationCommand(action);
        break;
      case 'LOGGING':
        handleLoggingCommand(action);
        break;
      case 'DASHBOARD':
        handleDashboardCommand(action);
        break;
      case 'SYSTEM':
        handleSystemCommand(action);
        break;
    }
  }, []);
  
  // Handle navigation commands
  const handleNavigationCommand = useCallback((action: string) => {
    switch (action) {
      case 'GO_HOME':
        router.replace('/');
        speakResponse("Going to home screen");
        break;
      case 'GO_PROFILE':
        router.push('/profile');
        speakResponse("Going to profile");
        break;
      case 'GO_MEALS':
        router.push('/meals' as any);
        speakResponse("Going to meal planner");
        break;
      case 'GO_WATER':
        router.push('/water' as any);
        speakResponse("Going to water tracking");
        break;
      case 'GO_SLEEP':
        router.push('/sleep' as any);
        speakResponse("Going to sleep tracking");
        break;
      case 'GO_DASHBOARD':
        router.push('/dashboard-demo');
        speakResponse("Going to customizable dashboard");
        break;
      case 'GO_BACK':
        navigation.goBack();
        speakResponse("Going back");
        break;
    }
  }, [navigation]);
  
  // Handle logging commands
  const handleLoggingCommand = useCallback((action: string) => {
    switch (action) {
      case 'LOG_WATER':
        router.push('/water' as any);
        speakResponse("Ready to log water intake");
        break;
      case 'LOG_MEAL':
        router.push('/meals' as any);
        speakResponse("Ready to log a meal");
        break;
      case 'LOG_SLEEP':
        router.push('/sleep' as any);
        speakResponse("Ready to log sleep");
        break;
      case 'LOG_MOOD':
        speakResponse("Ready to log your mood");
        // Navigate to mood tracking screen
        break;
    }
  }, []);
  
  // Handle dashboard commands
  const handleDashboardCommand = useCallback((action: string) => {
    router.push('/dashboard-demo');
    
    switch (action) {
      case 'EDIT_DASHBOARD':
        speakResponse("Opening dashboard edit mode");
        if (dashboardEditRef?.current) {
          // Call toggleEditMode on the dashboard component
          dashboardEditRef.current.toggleEditMode();
        }
        break;
      case 'ADD_WIDGET':
        speakResponse("Ready to add a new widget");
        // First enable edit mode
        if (dashboardEditRef?.current) {
          dashboardEditRef.current.toggleEditMode();
        }
        break;
    }
  }, [dashboardEditRef]);
  
  // Handle system commands
  const handleSystemCommand = useCallback((action: string) => {
    switch (action) {
      case 'HELP':
        setShowHelp(true);
        speakResponse("Showing available commands");
        break;
      case 'STOP_LISTENING':
        stopListening();
        speakResponse("Voice control stopped");
        break;
    }
  }, []);
  
  // Speak response using text-to-speech
  const speakResponse = useCallback((message: string) => {
    Speech.speak(message, {
      language: 'en',
      pitch: 1.0,
      rate: 0.9,
    });
  }, []);
  
  // Toggle voice control enabled/disabled
  const toggleEnabled = () => {
    if (isListening) {
      stopListening();
    }
    setEnabled(prev => !prev);
  };
  
  // Render help modal with available commands
  const renderHelpModal = () => {
    return (
      <Modal
        visible={showHelp}
        transparent
        animationType="slide"
        onRequestClose={() => setShowHelp(false)}
      >
        <View style={[
          styles.modalContainer,
          { paddingTop: insets.top, paddingBottom: insets.bottom }
        ]}>
          <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Voice Commands
              </Text>
              <TouchableOpacity 
                onPress={() => setShowHelp(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.commandsContainer}>
              <CommandCategory 
                title="Navigation" 
                commands={VOICE_COMMANDS.NAVIGATION}
                colors={colors}
              />
              <CommandCategory 
                title="Logging" 
                commands={VOICE_COMMANDS.LOGGING}
                colors={colors}
              />
              <CommandCategory 
                title="Dashboard" 
                commands={VOICE_COMMANDS.DASHBOARD}
                colors={colors}
              />
              <CommandCategory 
                title="System" 
                commands={VOICE_COMMANDS.SYSTEM}
                colors={colors}
              />
            </View>
            
            <TouchableOpacity
              style={[styles.helpButton, { backgroundColor: colors.primary }]}
              onPress={() => setShowHelp(false)}
            >
              <Text style={styles.helpButtonText}>Got it</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  };
  
  // If voice control is disabled, only show the toggle button
  if (!enabled) {
    return (
      <TouchableOpacity
        style={[
          styles.disabledButton,
          { backgroundColor: isDark ? '#333' : '#f2f2f2' }
        ]}
        onPress={toggleEnabled}
      >
        <Ionicons name="mic-off" size={24} color={colors.text} />
        <Text style={[styles.disabledText, { color: colors.text }]}>
          Voice Control Off
        </Text>
      </TouchableOpacity>
    );
  }
  
  return (
    <>
      <View style={styles.container}>
        <TouchableOpacity
          style={[
            styles.micButton,
            {
              backgroundColor: isListening 
                ? '#ef4444' 
                : isProcessing 
                  ? '#f59e0b' 
                  : colors.primary,
            }
          ]}
          onPress={isListening ? stopListening : startListening}
          disabled={isProcessing || !permissionGranted}
        >
          {isProcessing ? (
            <ActivityIndicator color="white" size="small" />
          ) : (
            <Ionicons
              name={isListening ? 'mic' : 'mic-outline'}
              size={24}
              color="white"
            />
          )}
        </TouchableOpacity>
        
        {(isListening || isProcessing || results) && (
          <View style={[styles.resultsContainer, { backgroundColor: colors.card }]}>
            {isListening && (
              <Text style={[styles.listeningText, { color: colors.text }]}>
                Listening...
              </Text>
            )}
            
            {isProcessing && (
              <Text style={[styles.processingText, { color: colors.text }]}>
                Processing...
              </Text>
            )}
            
            {results && !isListening && !isProcessing && (
              <Text style={[styles.resultsText, { color: colors.text }]}>
                "{results}"
              </Text>
            )}
            
            {error && (
              <Text style={styles.errorText}>
                {error}
              </Text>
            )}
          </View>
        )}
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.helpIconButton, { backgroundColor: colors.card }]}
            onPress={() => setShowHelp(true)}
          >
            <Ionicons name="help-circle-outline" size={20} color={colors.text} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.toggleButton, { backgroundColor: colors.card }]}
            onPress={toggleEnabled}
          >
            <Ionicons name="power" size={20} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>
      
      {renderHelpModal()}
    </>
  );
}

// Helper component to display command categories
function CommandCategory({ 
  title, 
  commands, 
  colors 
}: { 
  title: string; 
  commands: Record<string, string[]>;
  colors: any;
}) {
  return (
    <View style={styles.categoryContainer}>
      <Text style={[styles.categoryTitle, { color: colors.primary }]}>
        {title}
      </Text>
      
      {Object.entries(commands).map(([action, phrases]) => (
        <View key={action} style={styles.commandItem}>
          <Text style={[styles.commandAction, { color: colors.text }]}>
            {action.replace(/_/g, ' ')}:
          </Text>
          <Text style={[styles.commandPhrases, { color: colors.text }]}>
            "{phrases[0]}"
          </Text>
        </View>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    alignItems: 'flex-end',
    zIndex: 100,
  },
  micButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#3b82f6', // Blue
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  resultsContainer: {
    marginBottom: 12,
    padding: 12,
    borderRadius: 12,
    maxWidth: 250,
    minHeight: 50,
    justifyContent: 'center',
  },
  listeningText: {
    fontSize: 14,
    textAlign: 'center',
  },
  processingText: {
    fontSize: 14,
    textAlign: 'center',
  },
  resultsText: {
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
    color: '#ef4444',
  },
  disabledButton: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 100,
  },
  disabledText: {
    marginLeft: 8,
    fontSize: 14,
  },
  buttonContainer: {
    flexDirection: 'row',
    marginTop: 8,
  },
  toggleButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  helpIconButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  commandsContainer: {
    flex: 1,
  },
  categoryContainer: {
    marginBottom: 20,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  commandItem: {
    marginBottom: 8,
    paddingLeft: 8,
  },
  commandAction: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  commandPhrases: {
    fontSize: 12,
    fontStyle: 'italic',
    marginLeft: 8,
  },
  helpButton: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  helpButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
});

export default VoiceControl; 