import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { WaterIntakeRecord } from '@/services/waterIntakeService';
import { useTheme } from '@/contexts/ThemeContext';

interface WaterIntakeItemRowProps {
  item: WaterIntakeRecord;
  index: number;
  isDark: boolean;
  colors: any;
  formatAmount: (amount: number) => string;
  onRemove: (record: WaterIntakeRecord) => void;
}

/**
 * A component that renders a single water intake item row
 * Uses graceful error handling that doesn't display database errors to users
 */
export function WaterIntakeItemRow({
  item,
  index,
  isDark,
  colors,
  formatAmount,
  onRemove
}: WaterIntakeItemRowProps) {
  return (
    <LinearGradient
      colors={index % 2 === 0 
        ? (isDark ? ['#2c3e88', '#1e2a5e'] : ['#e1f5fe', '#b3e5fc']) 
        : (isDark ? ['#1e7c85', '#0f3e42'] : ['#e0f7fa', '#b2ebf2'])}
      style={[
        styles.recordItemGradient,
        { marginBottom: 8 }
      ]}
    >
      <View style={styles.recordInfo}>
        <Text style={[styles.recordTime, { color: isDark ? 'white' : colors.text }]}>{item.time}</Text>
        <Text style={[styles.recordAmount, { color: isDark ? 'white' : colors.text }]}>
          {formatAmount(item.amount)}
        </Text>
        <Text style={[styles.recordSource, { color: isDark ? 'rgba(255,255,255,0.7)' : colors.textSecondary }]}>
          {item.source === 'glass' ? 'Glass' : 
           item.source === 'bottle' ? 'Bottle' : 'Custom'}
        </Text>
      </View>
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => onRemove(item)}
      >
        <Feather name="trash-2" size={18} color={isDark ? '#ff8a80' : colors.danger} />
      </TouchableOpacity>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  recordItemGradient: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
  },
  recordInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  recordTime: {
    width: 60,
    fontSize: 14,
    fontWeight: '500',
  },
  recordAmount: {
    width: 80,
    fontSize: 14,
    fontWeight: '600',
  },
  recordSource: {
    fontSize: 14,
  },
  removeButton: {
    padding: 8,
  },
}); 