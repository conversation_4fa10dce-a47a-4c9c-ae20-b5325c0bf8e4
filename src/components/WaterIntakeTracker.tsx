import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  TextInput,
  Modal,
  ScrollView,
  Alert,
  ActivityIndicator
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import {
  addWaterIntake,
  addQuickWaterIntake,
  removeWaterIntake,
  getWaterIntakeForDate,
  getWaterIntakeProgress,
  getWaterIntakeGoals,
  setWaterIntakeGoals,
  WaterIntakeRecord,
  WaterIntakeGoals
} from '@/services/waterIntakeService';
import { Feather } from '@expo/vector-icons';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, Easing } from 'react-native-reanimated';
import { useFocusEffect } from '@react-navigation/native';
import { useSafeLoading } from '@/utils/loadingStateManager';
import ConnectionCheck from '@/components/ConnectionCheck';
import { LinearGradient } from 'expo-linear-gradient';
import { WaterIntakeItemRow } from './WaterIntakeItemRow';

interface WaterIntakeTrackerProps {
  date?: string; // ISO format date YYYY-MM-DD, defaults to today
  onUpdateIntake?: () => void;
  currentIntake?: number;
  goalIntake?: number;
  onAddWater?: (amount: number) => Promise<void>;
}

export function WaterIntakeTracker({ date, onUpdateIntake, currentIntake, goalIntake, onAddWater }: WaterIntakeTrackerProps) {
  const { colors, isDark } = useTheme();
  const isMountedRef = useRef(true);
  const { isLoading, executeWithLoading } = useSafeLoading({
    initialState: true,
    timeoutDuration: 30000, // Increase timeout to 30 seconds
    debugName: 'WaterIntakeTracker',
    onTimeout: () => {
      console.warn('WaterIntakeTracker: Loading timed out, showing fallback data');
      
      // Set up fallback data
      const fallbackTotal = currentIntake !== undefined ? currentIntake : 0;
      const fallbackGoal = goalIntake !== undefined ? goalIntake : 2000;
      const fallbackPercentage = Math.round((fallbackTotal / fallbackGoal) * 100);
      
      setProgress({
        total: fallbackTotal,
        goal: fallbackGoal,
        percentage: fallbackPercentage
      });
      
      // Fill level animation
      if (fillLevel) {
        fillLevel.value = withTiming(fallbackPercentage / 100, {
          duration: 1000,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1)
        });
      }
      
      // Set fallback goals if needed
      if (!goals) {
        const fallbackGoalsData = {
          dailyGoal: 2000,
          standardGlassSize: 250,
          standardBottleSize: 500,
          remindersEnabled: false
        };
        setGoals(fallbackGoalsData);
        setSettingsGoals(fallbackGoalsData);
      }
      
      // Start wave animation if possible
      if (waveOffset) {
        waveOffset.value = 0;
        startWaveAnimation();
      }
    }
  });
  
  // Additional fallback timer in case useSafeLoading fails
  const loadingTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  
  useEffect(() => {
    // Set a safety timeout to prevent infinite loading
    if (isLoading) {
      // Clear any existing timeout
      if (loadingTimerRef.current) {
        clearTimeout(loadingTimerRef.current);
      }
      
      // Set a new timeout
      loadingTimerRef.current = setTimeout(() => {
        if (isLoading) {
          console.warn('Emergency timeout triggered for WaterIntakeTracker');
          
          // Set fallback data
          setProgress({
            total: currentIntake !== undefined ? currentIntake : 0,
            goal: goalIntake !== undefined ? goalIntake : 2000,
            percentage: currentIntake !== undefined && goalIntake !== undefined ? 
              Math.round((currentIntake / goalIntake) * 100) : 0
          });
          
          // Set fallback goals
          const fallbackGoalsData = {
            dailyGoal: 2000,
            standardGlassSize: 250,
            standardBottleSize: 500,
            remindersEnabled: false
          };
          setGoals(fallbackGoalsData);
          setSettingsGoals(fallbackGoalsData);
          
          // Force the component to re-render
          setForceRender(prev => prev + 1);
        }
      }, 20000); // 20 seconds backup timeout
    }
    
    return () => {
      if (loadingTimerRef.current) {
        clearTimeout(loadingTimerRef.current);
      }
    };
  }, [isLoading, currentIntake, goalIntake]);
  
  const [records, setRecords] = useState<WaterIntakeRecord[]>([]);
  const [progress, setProgress] = useState({ total: 0, goal: 2000, percentage: 0 });
  const [goals, setGoals] = useState<WaterIntakeGoals | null>(null);
  const [showCustomInput, setShowCustomInput] = useState(false);
  const [customAmount, setCustomAmount] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [settingsGoals, setSettingsGoals] = useState<WaterIntakeGoals | null>(null);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [forceRender, setForceRender] = useState(0); // Used to force re-render
  
  // Animation values
  const fillLevel = useSharedValue(0);
  const waveOffset = useSharedValue(0);
  
  // Get current date if not provided
  const currentDate = date || new Date().toISOString().split('T')[0];
  
  // Cleanup effect
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);
  
  // Load data
  const loadData = useCallback(async () => {
    return executeWithLoading(async () => {
      try {
        setLoadError(null);
        
        // Load intake records for the day
        try {
          const dailyRecords = await getWaterIntakeForDate(currentDate);
          
          // Deduplicate records in case there are any with the same ID
          const uniqueRecordsMap = new Map();
          dailyRecords.forEach(record => {
            uniqueRecordsMap.set(record.id, record);
          });
          
          const uniqueRecords = Array.from(uniqueRecordsMap.values());
          setRecords(uniqueRecords);
        } catch (error) {
          console.error('Failed to load water intake records:', error);
          setRecords([]);
        }
        
        // Load progress data
        let progressData = { total: 0, goal: 2000, percentage: 0 };
        try {
          progressData = await getWaterIntakeProgress(currentDate);
        } catch (error) {
          console.error('Failed to load water intake progress:', error);
        }
        
        // Use prop values if provided
        const total = currentIntake !== undefined ? currentIntake : progressData.total;
        const goal = goalIntake !== undefined ? goalIntake : progressData.goal;
        const percentage = Math.round((total / goal) * 100);
        
        setProgress({
          total,
          goal,
          percentage
        });
        
        // Animate fill level
        fillLevel.value = withTiming(percentage / 100, {
          duration: 1000,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1)
        });
        
        // Load goals
        try {
          const goalsData = await getWaterIntakeGoals();
          setGoals(goalsData);
          setSettingsGoals(goalsData);
        } catch (error) {
          console.error('Failed to load water intake goals:', error);
          const fallbackGoalsData = {
            dailyGoal: 2000,
            standardGlassSize: 250,
            standardBottleSize: 500,
            remindersEnabled: false
          };
          setGoals(fallbackGoalsData);
          setSettingsGoals(fallbackGoalsData);
        }
        
        // Start wave animation
        waveOffset.value = 0;
        startWaveAnimation();
      } catch (error) {
        console.error('Error loading water intake data:', error);
        setLoadError('Failed to load water intake data. Please try again.');
        
        // Ensure we have fallback data
        setProgress({
          total: currentIntake !== undefined ? currentIntake : 0,
          goal: goalIntake !== undefined ? goalIntake : 2000,
          percentage: currentIntake !== undefined && goalIntake !== undefined ? 
            Math.round((currentIntake / goalIntake) * 100) : 0
        });
      }
    });
  }, [currentDate, currentIntake, goalIntake, executeWithLoading]);
  
  // Load data when the screen comes into focus
  useFocusEffect(
    useCallback(() => {
      loadData();
    }, [loadData])
  );
  
  // Start wave animation
  const startWaveAnimation = useCallback(() => {
    const animationLoop = () => {
      waveOffset.value = withTiming(1, { duration: 2000 }, () => {
        waveOffset.value = 0;
        // Only continue animation if component is still mounted
        if (isMountedRef.current) {
          animationLoop();
        }
      });
    };
    animationLoop();
  }, []); // Empty dependencies, function doesn't change
  
  // Add water (glass or bottle)
  const handleAddWater = async (type: 'glass' | 'bottle') => {
    executeWithLoading(async () => {
      try {
        // Get amount based on type
        const amount = type === 'glass' 
          ? goals?.standardGlassSize || 250 
          : goals?.standardBottleSize || 500;
        
        // Use onAddWater prop if provided
        if (onAddWater) {
          await onAddWater(amount);
        } else {
          const record = await addQuickWaterIntake(type);
          console.log(`Added ${amount}ml of water (${type})`);
          
          // Don't add to local records - just refresh the data
          // to prevent duplicate entries
        }
        
        // Refresh data
        await loadData();
        
        // Notify parent
        if (onUpdateIntake) {
          onUpdateIntake();
        }
      } catch (error) {
        console.error('Error adding water:', error);
        
        // Show retry option
        Alert.alert(
          'Error Adding Water',
          'Failed to add water intake. Would you like to try again?',
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Retry', 
              onPress: () => handleAddWater(type)
            }
          ]
        );
      }
    });
  };
  
  // Add custom amount
  const handleAddCustomAmount = async () => {
    executeWithLoading(async () => {
      try {
        const amount = parseInt(customAmount, 10);
        
        if (isNaN(amount) || amount <= 0) {
          Alert.alert('Invalid Amount', 'Please enter a valid amount greater than 0.');
          return;
        }
        
        // Use onAddWater prop if provided
        if (onAddWater) {
          await onAddWater(amount);
        } else {
          const record = await addWaterIntake(amount, 'other');
          console.log(`Added ${amount}ml of water (custom)`);
          
          // Don't add to local records - just refresh the data
          // to prevent duplicate entries
        }
        
        // Reset and close
        setCustomAmount('');
        setShowCustomInput(false);
        
        // Refresh data
        await loadData();
        
        // Notify parent
        if (onUpdateIntake) {
          onUpdateIntake();
        }
      } catch (error) {
        console.error('Error adding custom water amount:', error);
        
        // Show retry option
        Alert.alert(
          'Error Adding Water',
          'Failed to add water intake. Would you like to try again?',
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Retry', 
              onPress: () => handleAddCustomAmount()
            }
          ]
        );
      }
    });
  };
  
  // Remove water intake record
  const handleRemoveIntake = async (record: WaterIntakeRecord) => {
    try {
      Alert.alert(
        'Remove Intake',
        'Are you sure you want to remove this water intake record?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Remove',
            style: 'destructive',
            onPress: async () => {
              executeWithLoading(async () => {
                try {
                  await removeWaterIntake(record.id, record.date);
                  
                  // Refresh data
                  await loadData();
                  
                  // Notify parent
                  if (onUpdateIntake) {
                    onUpdateIntake();
                  }
                } catch (error) {
                  console.error('Error removing water intake:', error);
                  Alert.alert('Error', 'Failed to remove water intake. Please try again.');
                }
              });
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error removing water intake:', error);
      Alert.alert('Error', 'Failed to remove water intake. Please try again.');
    }
  };
  
  // Save settings
  const handleSaveSettings = async () => {
    executeWithLoading(async () => {
      try {
        if (!settingsGoals) return;
        
        await setWaterIntakeGoals(settingsGoals);
        
        // Close settings
        setShowSettings(false);
        
        // Refresh data
        await loadData();
      } catch (error) {
        console.error('Error saving water intake goals:', error);
        Alert.alert('Error', 'Failed to save settings. Please try again.');
      }
    });
  };
  
  // Create animated styles
  const waterFillStyle = useAnimatedStyle(() => {
    return {
      height: `${Math.min(fillLevel.value * 100, 100)}%`
    };
  });
  
  const waveStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: waveOffset.value * 40 }]
    };
  });
  
  // Format amount for display
  const formatAmount = (ml: number): string => {
    return ml >= 1000 ? `${(ml / 1000).toFixed(1)}L` : `${ml}ml`;
  };
  
  // If loading for over 5 seconds, show loading content with retry button
  const isLongLoading = isLoading && forceRender > 0;
  
  // If loading, show a nicer loading state with partial data if available
  if (isLoading && !records.length && !goals && forceRender === 0) {
    // Loading UI with glass/bottle buttons still visible to allow interaction
    return (
      <View style={[styles.container, { backgroundColor: isDark ? 'rgba(30, 42, 94, 0.8)' : 'white' }]}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: isDark ? 'white' : colors.text }]}>Water Intake</Text>
          <TouchableOpacity
            style={styles.settingsButton}
            onPress={() => setShowSettings(true)}
          >
            <Feather name="settings" size={20} color={isDark ? 'rgba(255,255,255,0.7)' : colors.textSecondary} />
          </TouchableOpacity>
        </View>
        
        {/* Skeleton Water Level Visualization */}
        <View style={styles.waterVisualizationContainer}>
          <View style={[styles.waterBottle, { borderColor: colors.border, backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)' }]}>
            <View style={styles.waterMetrics}>
              <ActivityIndicator color={colors.primary} size="large" />
            </View>
          </View>
          
          <View style={styles.percentageContainer}>
            <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
              Loading data...
            </Text>
            <TouchableOpacity 
              style={[styles.retryButton, { backgroundColor: colors.primary, marginTop: 12 }]}
              onPress={loadData}
            >
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Quick Add Buttons - still functional even during loading */}
        <View style={styles.quickAddContainer}>
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: colors.primary }]}
            onPress={() => handleAddWater('glass')}
          >
            <Feather name="plus" size={18} color="white" style={styles.addIcon} />
            <Text style={styles.addButtonText}>
              Add Glass (250ml)
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: colors.primary }]}
            onPress={() => handleAddWater('bottle')}
          >
            <Feather name="plus" size={18} color="white" style={styles.addIcon} />
            <Text style={styles.addButtonText}>
              Add Bottle (500ml)
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.addButton,
              { backgroundColor: isDark ? colors.subtle : '#f0f0f0' }
            ]}
            onPress={() => setShowCustomInput(true)}
          >
            <Feather 
              name="droplet" 
              size={18} 
              color={colors.textSecondary} 
              style={styles.addIcon} 
            />
            <Text style={[styles.customAddButtonText, { color: colors.text }]}>
              Custom Amount
            </Text>
          </TouchableOpacity>
        </View>
        
        {/* Loading message for records */}
        <View style={styles.emptyRecordsContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={[styles.emptyRecordsText, { color: colors.textSecondary, marginTop: 16 }]}>
            Loading your water intake...
          </Text>
        </View>
      </View>
    );
  }
  
  // If error and no data, show error state with retry button
  if (loadError && !records.length) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: isDark ? colors.card : 'white' }]}>
        <Feather name="alert-circle" size={40} color={colors.danger} />
        <Text style={[styles.errorText, { color: colors.text, marginTop: 16 }]}>
          {loadError}
        </Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: colors.primary, marginTop: 16 }]}
          onPress={loadData}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: isDark ? 'rgba(30, 42, 94, 0.8)' : 'white' }]}>
      {/* Container Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: isDark ? 'white' : colors.text }]}>Water Intake</Text>
        <TouchableOpacity
          style={styles.settingsButton}
          onPress={() => setShowSettings(true)}
        >
          <Feather name="settings" size={20} color={isDark ? 'rgba(255,255,255,0.7)' : colors.textSecondary} />
        </TouchableOpacity>
      </View>
      
      {/* Water Level Visualization */}
      <View style={styles.waterVisualizationContainer}>
        <LinearGradient
          colors={isDark ? ['#2c3e88', '#1e7c85'] : ['#e1f5fe', '#b3e5fc']}
          style={[styles.waterBottle, { borderColor: isDark ? 'rgba(255,255,255,0.2)' : colors.border }]}
        >
          <Animated.View style={[
            styles.waterFill, 
            waterFillStyle, 
            { backgroundColor: isDark ? '#4fc3f7' : '#2196f3' }
          ]}>
            <Animated.View 
              style={[
                styles.waterWave, 
                waveStyle, 
                { backgroundColor: isDark ? 'rgba(255, 255, 255, 0.3)' : 'rgba(255, 255, 255, 0.4)' }
              ]} 
            />
          </Animated.View>
          
          <View style={styles.waterGoalMarker}>
            <Text style={[styles.waterGoalText, { color: isDark ? 'white' : 'rgba(0, 0, 0, 0.6)' }]}>Goal</Text>
          </View>
          
          <View style={styles.waterMetrics}>
            <Text style={[styles.waterAmount, { color: 'white' }]}>
              {formatAmount(progress.total)}
            </Text>
            <Text style={[styles.waterGoal, { color: 'rgba(255, 255, 255, 0.9)' }]}>
              of {formatAmount(progress.goal)}
            </Text>
          </View>
        </LinearGradient>
        
        <View style={styles.percentageContainer}>
          <Text style={[styles.percentageText, { color: isDark ? 'white' : colors.text }]}>
            {progress.percentage}%
          </Text>
          <Text style={[styles.goalText, { color: isDark ? 'rgba(255,255,255,0.7)' : colors.textSecondary }]}>
            of daily goal
          </Text>
        </View>
      </View>
      
      {/* Quick Add Buttons */}
      <View style={styles.quickAddContainer}>
        <LinearGradient
          colors={isDark ? ['#1e88e5', '#0d47a1'] : ['#90caf9', '#42a5f5']}
          style={styles.addButton}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        >
          <TouchableOpacity
            style={styles.addButtonTouchable}
            onPress={() => handleAddWater('glass')}
          >
            <Feather name="plus" size={18} color="white" style={styles.addIcon} />
            <Text style={styles.addButtonText}>
              Add Glass ({formatAmount(goals?.standardGlassSize || 250)})
            </Text>
          </TouchableOpacity>
        </LinearGradient>
        
        <LinearGradient
          colors={isDark ? ['#1e88e5', '#0d47a1'] : ['#90caf9', '#42a5f5']}
          style={styles.addButton}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        >
          <TouchableOpacity
            style={styles.addButtonTouchable}
            onPress={() => handleAddWater('bottle')}
          >
            <Feather name="plus" size={18} color="white" style={styles.addIcon} />
            <Text style={styles.addButtonText}>
              Add Bottle ({formatAmount(goals?.standardBottleSize || 500)})
            </Text>
          </TouchableOpacity>
        </LinearGradient>
        
        <LinearGradient
          colors={isDark ? ['#616161', '#424242'] : ['#f5f5f5', '#e0e0e0']}
          style={styles.addButton}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        >
          <TouchableOpacity
            style={styles.addButtonTouchable}
            onPress={() => setShowCustomInput(true)}
          >
            <Feather 
              name="droplet" 
              size={18} 
              color={isDark ? 'white' : colors.textSecondary} 
              style={styles.addIcon} 
            />
            <Text style={[
              styles.customAddButtonText, 
              { color: isDark ? 'white' : colors.text }
            ]}>
              Custom Amount
            </Text>
          </TouchableOpacity>
        </LinearGradient>
      </View>
      
      {/* Today's Records */}
      {records.length > 0 ? (
        <View style={styles.recordsContainer}>
          <Text style={[styles.recordsTitle, { color: isDark ? 'white' : colors.text }]}>Today's Intake</Text>
          <FlatList
            data={records.sort((a, b) => b.time.localeCompare(a.time))}
            keyExtractor={(item, index) => `${item.id}_${index}`}
            renderItem={({ item, index }) => (
              <WaterIntakeItemRow
                item={item}
                index={index}
                isDark={isDark}
                colors={colors}
                formatAmount={formatAmount}
                onRemove={handleRemoveIntake}
              />
            )}
            style={styles.recordsList}
            contentContainerStyle={styles.recordsListContent}
          />
        </View>
      ) : (
        <View style={styles.emptyRecordsContainer}>
          <Feather 
            name="droplet" 
            size={48} 
            color={isDark ? colors.subtle : '#e0e0e0'} 
          />
          <Text style={[styles.emptyRecordsText, { color: colors.textSecondary }]}>
            No water intake recorded today.
          </Text>
          <Text style={[styles.emptyRecordsSubtext, { color: colors.textSecondary }]}>
            Use the buttons above to add water.
          </Text>
        </View>
      )}
      
      {/* Custom Amount Modal */}
      <Modal
        visible={showCustomInput}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowCustomInput(false)}
      >
        <View style={styles.modalOverlay}>
          <LinearGradient
            colors={isDark ? ['#2c3e88', '#1e2a5e'] : ['#ffffff', '#f5f5f5']}
            style={styles.modalContainer}
          >
            <Text style={[styles.modalTitle, { color: isDark ? 'white' : colors.text }]}>
              Add Custom Amount
            </Text>
            
            <View style={[
              styles.inputContainer,
              { backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : '#f5f5f5' }
            ]}>
              <TextInput
                style={[styles.input, { color: isDark ? 'white' : colors.text }]}
                keyboardType="number-pad"
                onChangeText={setCustomAmount}
                value={customAmount}
                placeholder="Enter amount in ml"
                placeholderTextColor={isDark ? 'rgba(255,255,255,0.5)' : colors.textSecondary}
              />
              <Text style={[styles.inputUnit, { color: isDark ? 'rgba(255,255,255,0.7)' : colors.textSecondary }]}>ml</Text>
            </View>
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[
                  styles.modalButton,
                  styles.cancelButton,
                  { borderColor: isDark ? 'rgba(255,255,255,0.2)' : colors.border }
                ]}
                onPress={() => {
                  setCustomAmount('');
                  setShowCustomInput(false);
                }}
              >
                <Text style={[styles.cancelButtonText, { color: isDark ? 'white' : colors.text }]}>
                  Cancel
                </Text>
              </TouchableOpacity>
              
              <LinearGradient
                colors={isDark ? ['#1e88e5', '#0d47a1'] : ['#90caf9', '#42a5f5']}
                style={[styles.modalButton, styles.addModalButton]}
              >
                <TouchableOpacity
                  style={styles.addModalButtonTouchable}
                  onPress={handleAddCustomAmount}
                >
                  <Text style={styles.addModalButtonText}>Add</Text>
                </TouchableOpacity>
              </LinearGradient>
            </View>
          </LinearGradient>
        </View>
      </Modal>
      
      {/* Settings Modal */}
      <Modal
        visible={showSettings}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowSettings(false)}
      >
        <View style={styles.modalOverlay}>
          <LinearGradient
            colors={isDark ? ['#2c3e88', '#1e2a5e'] : ['#ffffff', '#f5f5f5']}
            style={styles.settingsModalContainer}
          >
            <View style={styles.settingsHeader}>
              <Text style={[styles.settingsTitle, { color: colors.text }]}>
                Water Intake Settings
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowSettings(false)}
              >
                <Feather name="x" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.settingsContent}>
              {settingsGoals && (
                <>
                  <Text style={[styles.settingsSectionTitle, { color: colors.text }]}>
                    Daily Goal
                  </Text>
                  
                  <View style={[
                    styles.settingsInputContainer,
                    { backgroundColor: isDark ? colors.subtle : '#f5f5f5' }
                  ]}>
                    <TextInput
                      style={[styles.settingsInput, { color: colors.text }]}
                      keyboardType="number-pad"
                      value={settingsGoals.dailyGoal.toString()}
                      onChangeText={(value) => {
                        const dailyGoal = parseInt(value || '0', 10);
                        setSettingsGoals({
                          ...settingsGoals,
                          dailyGoal: isNaN(dailyGoal) ? 0 : dailyGoal
                        });
                      }}
                    />
                    <Text style={[styles.settingsInputUnit, { color: colors.textSecondary }]}>
                      ml
                    </Text>
                  </View>
                  
                  <Text style={[styles.settingsDescription, { color: colors.textSecondary }]}>
                    Recommended daily water intake is about 2000-3000 ml for adults.
                  </Text>
                  
                  <Text style={[styles.settingsSectionTitle, { color: colors.text }]}>
                    Container Sizes
                  </Text>
                  
                  <View style={styles.containerSizeRow}>
                    <View style={styles.containerSizeItem}>
                      <Text style={[styles.containerSizeLabel, { color: colors.text }]}>
                        Glass Size
                      </Text>
                      <View style={[
                        styles.settingsInputContainer,
                        { backgroundColor: isDark ? colors.subtle : '#f5f5f5' }
                      ]}>
                        <TextInput
                          style={[styles.settingsInput, { color: colors.text }]}
                          keyboardType="number-pad"
                          value={settingsGoals.standardGlassSize.toString()}
                          onChangeText={(value) => {
                            const glassSize = parseInt(value || '0', 10);
                            setSettingsGoals({
                              ...settingsGoals,
                              standardGlassSize: isNaN(glassSize) ? 0 : glassSize
                            });
                          }}
                        />
                        <Text style={[styles.settingsInputUnit, { color: colors.textSecondary }]}>
                          ml
                        </Text>
                      </View>
                    </View>
                    
                    <View style={styles.containerSizeItem}>
                      <Text style={[styles.containerSizeLabel, { color: colors.text }]}>
                        Bottle Size
                      </Text>
                      <View style={[
                        styles.settingsInputContainer,
                        { backgroundColor: isDark ? colors.subtle : '#f5f5f5' }
                      ]}>
                        <TextInput
                          style={[styles.settingsInput, { color: colors.text }]}
                          keyboardType="number-pad"
                          value={settingsGoals.standardBottleSize.toString()}
                          onChangeText={(value) => {
                            const bottleSize = parseInt(value || '0', 10);
                            setSettingsGoals({
                              ...settingsGoals,
                              standardBottleSize: isNaN(bottleSize) ? 0 : bottleSize
                            });
                          }}
                        />
                        <Text style={[styles.settingsInputUnit, { color: colors.textSecondary }]}>
                          ml
                        </Text>
                      </View>
                    </View>
                  </View>
                </>
              )}
            </ScrollView>
            
            <View style={styles.settingsFooter}>
              <TouchableOpacity
                style={[
                  styles.footerButton,
                  styles.settingsCancelButton,
                  { borderColor: colors.border }
                ]}
                onPress={() => {
                  setSettingsGoals(goals);
                  setShowSettings(false);
                }}
              >
                <Text style={[styles.settingsCancelButtonText, { color: colors.text }]}>
                  Cancel
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.footerButton,
                  styles.settingsSaveButton,
                  { backgroundColor: colors.primary }
                ]}
                onPress={handleSaveSettings}
              >
                <Text style={styles.settingsSaveButtonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </LinearGradient>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    overflow: 'hidden',
    margin: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  loadingContainer: {
    borderRadius: 16,
    margin: 16,
    padding: 40,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
  },
  loadingSubtext: {
    marginTop: 8,
    fontSize: 14,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
    marginTop: 16,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  settingsButton: {
    padding: 8,
  },
  waterVisualizationContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  waterBottle: {
    width: 140,
    height: 200,
    borderRadius: 70,
    borderWidth: 3,
    overflow: 'hidden',
    position: 'relative',
  },
  waterFill: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#3498db',
    overflow: 'hidden',
  },
  waterWave: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  waterGoalMarker: {
    position: 'absolute',
    right: -30,
    top: '50%',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 4,
  },
  waterGoalText: {
    color: 'rgba(0, 0, 0, 0.5)',
    fontSize: 10,
    fontWeight: '500',
  },
  waterMetrics: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    transform: [{ translateY: -20 }],
    alignItems: 'center',
  },
  waterAmount: {
    fontSize: 20,
    fontWeight: '700',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  waterGoal: {
    fontSize: 12,
    fontWeight: '500',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  percentageContainer: {
    marginTop: 8,
    alignItems: 'center',
  },
  percentageText: {
    fontSize: 28,
    fontWeight: '700',
  },
  goalText: {
    fontSize: 14,
  },
  quickAddContainer: {
    marginBottom: 24,
  },
  addButton: {
    borderRadius: 12,
    marginBottom: 8,
    overflow: 'hidden',
  },
  addButtonTouchable: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 48,
    width: '100%',
    paddingHorizontal: 16,
  },
  addIcon: {
    marginRight: 8,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  customAddButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  recordsContainer: {
    flex: 1,
  },
  recordsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  recordsList: {
    flex: 1,
  },
  recordsListContent: {
    paddingBottom: 16,
  },
  recordItemGradient: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
  },
  recordInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  recordTime: {
    width: 60,
    fontSize: 14,
    fontWeight: '500',
  },
  recordAmount: {
    width: 80,
    fontSize: 14,
    fontWeight: '600',
  },
  recordSource: {
    fontSize: 14,
  },
  removeButton: {
    padding: 8,
  },
  emptyRecordsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 30,
  },
  emptyRecordsText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyRecordsSubtext: {
    fontSize: 14,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    width: '85%',
    borderRadius: 20,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 24,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  inputUnit: {
    fontSize: 16,
    marginLeft: 8,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    height: 44,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    marginRight: 8,
    borderWidth: 1,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  addModalButton: {
    marginLeft: 8,
  },
  addModalButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  addModalButtonTouchable: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  settingsModalContainer: {
    width: '90%',
    maxHeight: '85%',
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
  settingsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  settingsTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 4,
  },
  settingsContent: {
    padding: 16,
  },
  settingsSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    marginTop: 8,
  },
  settingsInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 8,
  },
  settingsInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  settingsInputUnit: {
    fontSize: 16,
    marginLeft: 8,
  },
  settingsDescription: {
    fontSize: 14,
    marginBottom: 24,
  },
  containerSizeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  containerSizeItem: {
    flex: 1,
    marginHorizontal: 4,
  },
  containerSizeLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  settingsFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  footerButton: {
    flex: 1,
    height: 44,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  settingsCancelButton: {
    marginRight: 8,
    borderWidth: 1,
  },
  settingsCancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  settingsSaveButton: {
    marginLeft: 8,
  },
  settingsSaveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
});