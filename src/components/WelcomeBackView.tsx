import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ImageBackground,
  Image,
  useColorScheme,
  Dimensions,
  Platform,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAccessibility } from './AccessibilityProvider';

interface WelcomeBackViewProps {
  /**
   * User's name for personalized greeting
   */
  userName: string;
  
  /**
   * Current date string to display
   */
  currentDate?: string;
  
  /**
   * Optional array of stats to display
   */
  stats?: {
    label: string;
    value: string | number;
    icon: string;
    color: string;
  }[];
  
  /**
   * Optional actions to display
   */
  quickActions?: {
    label: string;
    onPress: () => void;
    icon: string;
    color: string;
  }[];
  
  /**
   * Optional streak count
   */
  streakCount?: number;
  
  /**
   * Optional handler for dismissing the welcome screen
   */
  onDismiss?: () => void;
  
  /**
   * Optional handler for viewing more details
   */
  onViewDetails?: () => void;
  
  /**
   * Optional motivation message
   */
  motivationMessage?: string;
  
  /**
   * Optional time since last visit (e.g., "2 days ago")
   */
  lastVisit?: string;
  
  /**
   * Optional upcoming events
   */
  upcomingEvents?: {
    title: string;
    time: string;
    icon: string;
  }[];
  
  /**
   * Optional background image
   */
  backgroundImage?: any;
}

/**
 * WelcomeBackView component
 * 
 * Displays a personalized welcome back experience with relevant statistics and actions
 */
export function WelcomeBackView({
  userName,
  currentDate = new Date().toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' }),
  stats = [],
  quickActions = [],
  streakCount,
  onDismiss,
  onViewDetails,
  motivationMessage,
  lastVisit,
  upcomingEvents = [],
  backgroundImage,
}: WelcomeBackViewProps) {
  const [hasSeenToday, setHasSeenToday] = useState(false);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const { isScreenReaderEnabled, isReduceMotionEnabled } = useAccessibility();
  const { width } = Dimensions.get('window');
  
  // Time-based greeting
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 18) return 'Good Afternoon';
    return 'Good Evening';
  };
  
  const greeting = getGreeting();
  
  // Check if we've displayed the welcome screen today
  useEffect(() => {
    const checkWelcomeStatus = async () => {
      try {
        const today = new Date().toDateString();
        const lastWelcomeDate = await AsyncStorage.getItem('lastWelcomeDate');
        
        if (lastWelcomeDate === today) {
          setHasSeenToday(true);
        } else {
          // Store today's date so we don't show the welcome again today
          await AsyncStorage.setItem('lastWelcomeDate', today);
          
          // Announce to screen reader if enabled
          if (isScreenReaderEnabled) {
            const message = `${greeting}, ${userName}. ${motivationMessage || ''}`;
            // Use the announceForAccessibility method if available
            // For this example, we'll rely on the accessible prop
          }
        }
      } catch (error) {
        console.error('Error checking welcome status:', error);
      }
    };
    
    checkWelcomeStatus();
  }, [greeting, userName, motivationMessage, isScreenReaderEnabled]);
  
  // If we've already shown the welcome today and it's dismissible, don't show it again
  if (hasSeenToday && onDismiss) {
    return null;
  }
  
  const defaultBackgroundImage = require('../assets/images/welcome-back-bg.jpg');
  
  return (
    <ScrollView 
      style={styles.scrollView}
      contentContainerStyle={styles.scrollContent}
      showsVerticalScrollIndicator={false}
    >
      <ImageBackground
        source={backgroundImage || defaultBackgroundImage}
        style={styles.backgroundImage}
        imageStyle={styles.backgroundImageStyle}
      >
        <LinearGradient
          colors={['rgba(0,0,0,0.7)', 'rgba(0,0,0,0.2)', 'rgba(0,0,0,0.7)']}
          style={styles.gradient}
        >
          <View 
            style={styles.container} 
            accessible={true}
            accessibilityRole="header"
            accessibilityLabel={`${greeting}, ${userName}. ${currentDate}. ${
              motivationMessage ? `Motivation: ${motivationMessage}.` : ''
            } ${lastVisit ? `Last visit: ${lastVisit}` : ''}`}
          >
            {onDismiss && (
              <TouchableOpacity 
                style={styles.dismissButton}
                onPress={onDismiss}
                accessibilityLabel="Dismiss welcome screen"
                accessibilityRole="button"
              >
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            )}
            
            <View style={styles.header}>
              <View>
                <Text style={styles.greeting}>{greeting}</Text>
                <Text style={styles.userName}>{userName}</Text>
                <Text style={styles.date}>{currentDate}</Text>
                
                {lastVisit && (
                  <Text style={styles.lastVisit}>Last visit: {lastVisit}</Text>
                )}
              </View>
              
              {streakCount !== undefined && (
                <View style={styles.streakContainer}>
                  <Text style={styles.streakValue}>{streakCount}</Text>
                  <Text style={styles.streakLabel}>Day Streak</Text>
                  <View style={styles.streakIconContainer}>
                    <Ionicons name="flame" size={16} color="#FF9500" />
                  </View>
                </View>
              )}
            </View>
            
            {motivationMessage && (
              <View style={styles.motivationContainer}>
                <Text style={styles.motivationText}>"{motivationMessage}"</Text>
              </View>
            )}
            
            {stats.length > 0 && (
              <View style={styles.statsContainer}>
                {stats.map((stat, index) => (
                  <View 
                    key={`stat-${index}`} 
                    style={styles.statItem}
                    accessible={true}
                    accessibilityLabel={`${stat.label}: ${stat.value}`}
                  >
                    <View style={[styles.statIcon, { backgroundColor: stat.color }]}>
                      <Ionicons name={stat.icon as any} size={16} color="#fff" />
                    </View>
                    <Text style={styles.statValue}>{stat.value}</Text>
                    <Text style={styles.statLabel}>{stat.label}</Text>
                  </View>
                ))}
              </View>
            )}
            
            {quickActions.length > 0 && (
              <View style={styles.quickActionsContainer}>
                <Text style={styles.sectionTitle}>Quick Actions</Text>
                <View style={styles.quickActionsRow}>
                  {quickActions.map((action, index) => (
                    <TouchableOpacity 
                      key={`action-${index}`}
                      style={styles.quickActionButton}
                      onPress={action.onPress}
                      accessibilityLabel={action.label}
                      accessibilityRole="button"
                    >
                      <View style={[styles.quickActionIcon, { backgroundColor: action.color }]}>
                        <Ionicons name={action.icon as any} size={20} color="#fff" />
                      </View>
                      <Text style={styles.quickActionLabel}>{action.label}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}
            
            {upcomingEvents.length > 0 && (
              <View style={styles.eventsContainer}>
                <Text style={styles.sectionTitle}>Coming Up Today</Text>
                {upcomingEvents.map((event, index) => (
                  <View 
                    key={`event-${index}`}
                    style={styles.eventItem}
                    accessible={true}
                    accessibilityLabel={`${event.title} at ${event.time}`}
                  >
                    <View style={styles.eventIconContainer}>
                      <Ionicons name={event.icon as any} size={16} color="#fff" />
                    </View>
                    <View style={styles.eventDetails}>
                      <Text style={styles.eventTitle}>{event.title}</Text>
                      <Text style={styles.eventTime}>{event.time}</Text>
                    </View>
                  </View>
                ))}
              </View>
            )}
            
            {onViewDetails && (
              <TouchableOpacity 
                style={styles.viewDetailsButton}
                onPress={onViewDetails}
                accessibilityLabel="View more details"
                accessibilityRole="button"
              >
                <Text style={styles.viewDetailsText}>View Details</Text>
                <Ionicons name="chevron-forward" size={16} color="#fff" />
              </TouchableOpacity>
            )}
          </View>
        </LinearGradient>
      </ImageBackground>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
  },
  backgroundImageStyle: {
    resizeMode: 'cover',
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  container: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 20,
  },
  dismissButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 10,
    padding: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  greeting: {
    fontSize: 18,
    fontWeight: '500',
    color: '#fff',
    marginBottom: 4,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  date: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
    marginBottom: 4,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  lastVisit: {
    fontSize: 12,
    color: '#fff',
    opacity: 0.8,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  streakContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
    height: 80,
  },
  streakValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  streakLabel: {
    fontSize: 12,
    color: '#fff',
    marginTop: 2,
  },
  streakIconContainer: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#fff',
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  motivationContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  motivationText: {
    color: '#fff',
    fontSize: 16,
    fontStyle: 'italic',
    textAlign: 'center',
    lineHeight: 22,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  statItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4,
  },
  statIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
    color: '#fff',
    opacity: 0.9,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 12,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  quickActionsContainer: {
    marginBottom: 24,
  },
  quickActionsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
  },
  quickActionButton: {
    width: '33.33%',
    padding: 8,
    alignItems: 'center',
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  quickActionLabel: {
    fontSize: 12,
    color: '#fff',
    textAlign: 'center',
    ...(Platform.OS === 'web'
      ? { textShadow: '1px 1px 2px rgba(0, 0, 0, 0.5)' }
      : {
          textShadowColor: 'rgba(0, 0, 0, 0.5)',
          textShadowOffset: { width: 1, height: 1 },
          textShadowRadius: 2,
        }
    ),
  },
  eventsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  eventItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  eventIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  eventDetails: {
    flex: 1,
  },
  eventTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 2,
  },
  eventTime: {
    fontSize: 12,
    color: '#fff',
    opacity: 0.8,
  },
  viewDetailsButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  viewDetailsText: {
    color: '#fff',
    fontWeight: 'bold',
    marginRight: 4,
  },
});

export default WelcomeBackView; 