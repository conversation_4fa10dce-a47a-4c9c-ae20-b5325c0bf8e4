import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { IngredientSubstitutionCard } from './IngredientSubstitutionCard';
import { Feather } from '@expo/vector-icons';
import { IngredientSubstitution, findSubstitutions } from '@/services/openai/healthySubstitutions';

interface WhyThisSwapExplainerProps {
  originalIngredients: string[];
  healthierIngredients: string[];
  dietaryPreferences?: string[];
  userHealthGoals?: string[];
  onClose?: () => void;
}

/**
 * A component that explains why ingredients were swapped in a healthier recipe alternative
 * Shows the health benefits and culinary details of each substitution
 */
export function WhyThisSwapExplainer({
  originalIngredients,
  healthierIngredients,
  dietaryPreferences = [],
  userHealthGoals = [],
  onClose
}: WhyThisSwapExplainerProps) {
  const { colors, isDark } = useTheme();
  const [expandedSubstitutions, setExpandedSubstitutions] = useState<string[]>([]);
  const [substitutions, setSubstitutions] = useState<IngredientSubstitution[]>(() => {
    // Identify potential substitutions by comparing ingredients
    const possibleSubstitutions: IngredientSubstitution[] = [];
    
    // For each original ingredient, check if there's a substitution in the healthier recipe
    originalIngredients.forEach(originalIngredient => {
      // Find potential substitutions from our database
      const availableSubstitutions = findSubstitutions(originalIngredient, dietaryPreferences);
      
      if (availableSubstitutions.length > 0) {
        // For each potential substitution, check if it's in the healthier ingredients list
        availableSubstitutions.forEach(substitution => {
          const substitutionName = substitution.substitution.toLowerCase();
          
          // Check if the healthier ingredients list contains this substitution
          const isUsed = healthierIngredients.some(ingredient => 
            ingredient.toLowerCase().includes(substitutionName)
          );
          
          if (isUsed) {
            possibleSubstitutions.push(substitution);
          }
        });
      }
    });
    
    return possibleSubstitutions;
  });

  // Toggle expanded state of a substitution
  const toggleExpand = (originalIngredient: string, substitutionName: string) => {
    const key = `${originalIngredient}-${substitutionName}`;
    setExpandedSubstitutions(prev => {
      if (prev.includes(key)) {
        return prev.filter(item => item !== key);
      } else {
        return [...prev, key];
      }
    });
  };

  // Check if a substitution is expanded
  const isExpanded = (originalIngredient: string, substitutionName: string) => {
    const key = `${originalIngredient}-${substitutionName}`;
    return expandedSubstitutions.includes(key);
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.card, borderBottomColor: colors.border }]}>
        <TouchableOpacity
          style={[styles.closeButton, { backgroundColor: colors.subtle }]}
          onPress={onClose}
        >
          <Feather name="arrow-left" size={20} color={colors.text} />
        </TouchableOpacity>
        <View style={styles.titleContainer}>
          <Feather name="book-open" size={18} color={colors.primary} style={styles.titleIcon} />
          <Text style={[styles.title, { color: colors.text }]}>
            Why These Swaps?
          </Text>
        </View>
        <View style={styles.placeholder} />
      </View>
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        {substitutions.length > 0 ? (
          <>
            <Text style={[styles.description, { color: colors.textSecondary }]}>
              These ingredient swaps were selected to create a healthier version of the original recipe
              while maintaining similar flavors and textures.
            </Text>
            
            {substitutions.map((substitution, index) => (
              <IngredientSubstitutionCard
                key={`${substitution.originalIngredient}-${substitution.substitution}-${index}`}
                substitution={substitution}
                isExpanded={isExpanded(substitution.originalIngredient, substitution.substitution)}
                onToggleExpand={() => toggleExpand(substitution.originalIngredient, substitution.substitution)}
              />
            ))}
            
            <View style={[styles.tipContainer, { backgroundColor: colors.subtle }]}>
              <Text style={[styles.tipTitle, { color: colors.text }]}>
                Pro Tip
              </Text>
              <Text style={[styles.tipText, { color: colors.textSecondary }]}>
                Small changes add up! By making these substitutions regularly, you can significantly
                improve your overall nutrition without sacrificing taste.
              </Text>
            </View>
          </>
        ) : (
          <View style={styles.emptyState}>
            <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
              No ingredient substitutions were identified for this recipe. This may happen if the
              recipe already uses healthy ingredients or if the substitutions are custom to this recipe.
            </Text>
          </View>
        )}
      </ScrollView>
      
      <View style={[styles.footer, { backgroundColor: colors.card, borderTopColor: colors.border }]}>
        <TouchableOpacity
          style={[styles.closeFullButton, { backgroundColor: colors.primary }]}
          onPress={onClose}
        >
          <Text style={styles.closeFullButtonText}>Return to Recipe</Text>
          <Feather name="arrow-right" size={18}  color={colors.text} />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleIcon: {
    marginRight: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  placeholder: {
    width: 36,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 20,
  },
  emptyState: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  tipContainer: {
    padding: 16,
    borderRadius: 12,
    marginTop: 8,
    marginBottom: 16,
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    lineHeight: 20,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
  },
  closeFullButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  closeFullButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
}); 