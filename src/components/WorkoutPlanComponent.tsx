import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ScrollView,
  ActivityIndicator,
  Image,
  Alert
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import {
  WorkoutPlan,
  WorkoutSession,
  WorkoutDifficulty,
  WorkoutFocus,
  BodyArea,
  fetchWorkoutPlans,
  fetchWorkoutPlan,
  fetchWorkoutHistory,
  WorkoutCompletion
} from '@/services/workoutPlanService';
import { format, addDays, isToday, isPast, isEqual } from 'date-fns';

interface WorkoutPlanComponentProps {
  onSelectSession?: (session: WorkoutSession, plan: WorkoutPlan) => void;
  onCreatePlan?: () => void;
  onViewHistory?: () => void;
}

const WorkoutPlanComponent: React.FC<WorkoutPlanComponentProps> = ({
  onSelectSession,
  onCreatePlan,
  onViewHistory
}) => {
  const { colors, isDark } = useTheme();
  const [loading, setLoading] = useState(true);
  const [plans, setPlans] = useState<WorkoutPlan[]>([]);
  const [activePlan, setActivePlan] = useState<WorkoutPlan | null>(null);
  const [completedSessions, setCompletedSessions] = useState<Record<string, boolean>>({});
  const [upcomingSessions, setUpcomingSessions] = useState<{
    session: WorkoutSession;
    date: Date;
    dayOfWeek: string;
    isToday: boolean;
    isPast: boolean;
  }[]>([]);
  
  // Load workout plans
  const loadWorkoutPlans = useCallback(async () => {
    try {
      setLoading(true);
      
      // Fetch workout plans
      const plansResult = await fetchWorkoutPlans();
      
      if (!plansResult.success || !plansResult.data) {
        console.error('Failed to fetch workout plans:', plansResult.error);
        return;
      }
      
      // Set plans
      setPlans(plansResult.data);
      
      // If there's at least one plan, set it as the active plan
      if (plansResult.data.length > 0) {
        const currentPlan = plansResult.data[0];
        setActivePlan(currentPlan);
        
        // Calculate upcoming sessions based on the current plan
        await updateUpcomingSessions(currentPlan);
      }
    } catch (error) {
      console.error('Error loading workout plans:', error);
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Load workout plans when the screen comes into focus
  useFocusEffect(
    useCallback(() => {
      loadWorkoutPlans();
    }, [loadWorkoutPlans])
  );
  
  // Update upcoming sessions for a plan
  const updateUpcomingSessions = async (plan: WorkoutPlan) => {
    try {
      // Calculate dates from startDate
      const startDate = new Date(plan.startDate);
      const today = new Date();
      
      // Determine what day of the plan we're on (0-based)
      const daysSinceStart = Math.max(0, Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)));
      
      // Calculate weekday indices
      const workoutDays = Array.from({ length: 7 }, (_, i) => i)
        .filter(day => !plan.restDays.includes(day));
      
      // If days per week doesn't match workout days, adjust
      if (workoutDays.length !== plan.daysPerWeek) {
        console.warn('Workout days mismatch. Using plan.daysPerWeek instead.');
      }
      
      // Get upcoming sessions for a 14-day window
      const upcoming: {
        session: WorkoutSession;
        date: Date;
        dayOfWeek: string;
        isToday: boolean;
        isPast: boolean;
      }[] = [];
      
      // Get completed sessions for this plan
      const historyResult = await fetchWorkoutHistory({ planId: plan.id });
      const completions = historyResult.success ? historyResult.data || [] : [];
      
      // Create a map of completed sessions
      const completed: Record<string, boolean> = {};
      completions.forEach(completion => {
        completed[completion.workoutSessionId] = true;
      });
      
      setCompletedSessions(completed);
      
      // Calculate upcoming sessions
      for (let dayOffset = -7; dayOffset < 14; dayOffset++) {
        const date = addDays(today, dayOffset);
        const dayOfWeek = date.getDay(); // 0-6, Sunday to Saturday
        
        // Skip rest days
        if (plan.restDays.includes(dayOfWeek)) {
          continue;
        }
        
        // Calculate which session to show based on the day and plan
        const daysSinceStartWithOffset = daysSinceStart + dayOffset;
        
        // Skip days before the plan starts
        if (daysSinceStartWithOffset < 0) {
          continue;
        }
        
        // Determine which session to show
        // Get session index based on rotation of available sessions
        const sessionIndex = daysSinceStartWithOffset % plan.sessions.length;
        const session = plan.sessions[sessionIndex];
        
        if (session) {
          upcoming.push({
            session,
            date,
            dayOfWeek: format(date, 'EEE'),
            isToday: isToday(date),
            isPast: isPast(date) && !isToday(date)
          });
        }
      }
      
      // Sort by date (past to future)
      upcoming.sort((a, b) => a.date.getTime() - b.date.getTime());
      
      setUpcomingSessions(upcoming);
    } catch (error) {
      console.error('Error updating upcoming sessions:', error);
    }
  };
  
  // Select a workout plan
  const handleSelectPlan = async (plan: WorkoutPlan) => {
    setActivePlan(plan);
    await updateUpcomingSessions(plan);
  };
  
  // Handle session selection
  const handleSelectSession = (session: WorkoutSession) => {
    if (activePlan && onSelectSession) {
      onSelectSession(session, activePlan);
    } else {
      Alert.alert('Error', 'No active workout plan selected.');
    }
  };
  
  // Get color for difficulty
  const getDifficultyColor = (difficulty: WorkoutDifficulty) => {
    switch (difficulty) {
      case WorkoutDifficulty.BEGINNER:
        return '#4CAF50'; // Green
      case WorkoutDifficulty.INTERMEDIATE:
        return '#2196F3'; // Blue
      case WorkoutDifficulty.ADVANCED:
        return '#F44336'; // Red
      default:
        return colors.text;
    }
  };
  
  // Format difficulty for display
  const formatDifficulty = (difficulty: WorkoutDifficulty) => {
    return difficulty.charAt(0).toUpperCase() + difficulty.slice(1);
  };
  
  // Format focus for display
  const formatFocus = (focus: WorkoutFocus[]) => {
    return focus.map(f => f.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')).join(', ');
  };
  
  // Format date for display
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM d, yyyy');
  };
  
  // If loading
  if (loading && plans.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? colors.card : 'white' }]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }
  
  // If no plans
  if (plans.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? colors.card : 'white' }]}>
        <View style={styles.emptyContainer}>
          <Feather name="activity" size={64} color={isDark ? colors.subtle : '#e0e0e0'} />
          <Text style={[styles.emptyText, { color: colors.text }]}>
            No workout plans found
          </Text>
          <Text style={[styles.emptySubtext, { color: colors.textSecondary }]}>
            Create a new workout plan to get started
          </Text>
          <TouchableOpacity
            style={[styles.createButton, { backgroundColor: colors.primary }]}
            onPress={onCreatePlan}
          >
            <Text style={styles.createButtonText}>Create Workout Plan</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: isDark ? colors.card : 'white' }]}>
      {/* Workout Plans Selector */}
      <View style={styles.planSelector}>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.planScrollContent}
        >
          {plans.map((plan) => (
            <TouchableOpacity
              key={plan.id}
              style={[
                styles.planItem,
                activePlan?.id === plan.id && styles.activePlanItem,
                { 
                  backgroundColor: activePlan?.id === plan.id ? 
                    colors.primary + '20' : 
                    isDark ? colors.subtle : '#f0f0f0',
                  borderColor: activePlan?.id === plan.id ? colors.primary : 'transparent'
                }
              ]}
              onPress={() => handleSelectPlan(plan)}
            >
              <Text 
                style={[
                  styles.planName, 
                  { 
                    color: activePlan?.id === plan.id ? 
                      colors.primary : 
                      colors.text,
                    fontWeight: activePlan?.id === plan.id ? '600' : '400'
                  }
                ]}
                numberOfLines={1}
              >
                {plan.name}
              </Text>
              <View style={styles.planMetaRow}>
                <Text 
                  style={[
                    styles.planMeta, 
                    { 
                      color: colors.textSecondary,
                      opacity: activePlan?.id === plan.id ? 1 : 0.7
                    }
                  ]}
                >
                  {plan.daysPerWeek}x/week
                </Text>
                <View 
                  style={[
                    styles.difficultyIndicator, 
                    { backgroundColor: getDifficultyColor(plan.difficulty) }
                  ]}
                />
              </View>
            </TouchableOpacity>
          ))}
          <TouchableOpacity
            style={[
              styles.addPlanButton,
              { 
                backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                borderColor: isDark ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.1)'
              }
            ]}
            onPress={onCreatePlan}
          >
            <Feather name="plus" size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        </ScrollView>
      </View>
      
      {/* Active Plan Details */}
      {activePlan && (
        <View style={styles.activeContainer}>
          <View style={styles.planHeader}>
            <View style={styles.planInfo}>
              <Text style={[styles.activePlanName, { color: colors.text }]}>
                {activePlan.name}
              </Text>
              <Text style={[styles.activePlanDetails, { color: colors.textSecondary }]}>
                {formatFocus(activePlan.focus)} • {formatDifficulty(activePlan.difficulty)}
              </Text>
              <Text style={[styles.activePlanDates, { color: colors.textSecondary }]}>
                {formatDate(activePlan.startDate)} - {formatDate(activePlan.endDate)}
              </Text>
            </View>
            
            <TouchableOpacity
              style={[styles.historyButton, { borderColor: colors.border }]}
              onPress={onViewHistory}
            >
              <Feather name="calendar" size={16} color={colors.textSecondary} />
              <Text style={[styles.historyButtonText, { color: colors.textSecondary }]}>
                History
              </Text>
            </TouchableOpacity>
          </View>
          
          {/* Upcoming Sessions */}
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Schedule
          </Text>
          
          <FlatList
            data={upcomingSessions}
            keyExtractor={(item, index) => `${item.session.id}-${index}`}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[
                  styles.sessionItem,
                  item.isToday && styles.todaySession,
                  completedSessions[item.session.id] && styles.completedSession,
                  item.isPast && !completedSessions[item.session.id] && styles.missedSession,
                  { 
                    backgroundColor: isDark ? 
                      (item.isToday ? colors.primary + '20' : colors.subtle) : 
                      (item.isToday ? colors.primary + '10' : '#f5f5f5'),
                    opacity: item.isPast && !item.isToday ? 0.6 : 1
                  }
                ]}
                onPress={() => handleSelectSession(item.session)}
                disabled={item.isPast && !item.isToday && !completedSessions[item.session.id]}
              >
                <View style={styles.sessionDateContainer}>
                  <Text style={[styles.sessionDay, { color: colors.textSecondary }]}>
                    {item.dayOfWeek}
                  </Text>
                  <Text style={[styles.sessionDate, { color: colors.text }]}>
                    {format(item.date, 'd')}
                  </Text>
                </View>
                
                <View style={styles.sessionInfo}>
                  <Text style={[styles.sessionName, { color: colors.text }]}>
                    {item.session.name}
                  </Text>
                  <Text style={[styles.sessionDetails, { color: colors.textSecondary }]}>
                    {item.session.exercises.length} exercises • {item.session.duration} mins
                  </Text>
                </View>
                
                <View style={styles.sessionStatus}>
                  {completedSessions[item.session.id] ? (
                    <View style={[styles.statusIndicator, { backgroundColor: colors.success }]}>
                      <Feather name="check" size={12} color="white" />
                    </View>
                  ) : item.isToday ? (
                    <View style={[styles.statusIndicator, { backgroundColor: colors.primary }]}>
                      <Feather name="chevron-right" size={12} color="white" />
                    </View>
                  ) : item.isPast ? (
                    <View style={[styles.statusIndicator, { backgroundColor: colors.danger }]}>
                      <Feather name="x" size={12} color="white" />
                    </View>
                  ) : (
                    <View style={[styles.statusIndicator, { backgroundColor: colors.textSecondary }]}>
                      <Feather name="clock" size={12} color="white" />
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            )}
            style={styles.sessionsList}
            contentContainerStyle={styles.sessionsListContent}
            showsVerticalScrollIndicator={false}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
  },
  createButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  planSelector: {
    paddingVertical: 16,
    backgroundColor: 'transparent',
  },
  planScrollContent: {
    paddingHorizontal: 16,
  },
  planItem: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginRight: 8,
    borderWidth: 1,
    minWidth: 120,
  },
  activePlanItem: {
    borderWidth: 1,
  },
  planName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  planMetaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  planMeta: {
    fontSize: 12,
  },
  difficultyIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  addPlanButton: {
    height: 58,
    width: 58,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
  },
  activeContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  planInfo: {
    flex: 1,
  },
  activePlanName: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  activePlanDetails: {
    fontSize: 14,
    marginBottom: 2,
  },
  activePlanDates: {
    fontSize: 12,
  },
  historyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    borderWidth: 1,
  },
  historyButtonText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  sessionsList: {
    flex: 1,
  },
  sessionsListContent: {
    paddingBottom: 16,
  },
  sessionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  todaySession: {
    borderWidth: 1,
    borderColor: '#3498db',
  },
  completedSession: {
    borderWidth: 1,
    borderColor: '#2ecc71',
  },
  missedSession: {
    borderWidth: 0,
  },
  sessionDateContainer: {
    width: 40,
    alignItems: 'center',
  },
  sessionDay: {
    fontSize: 12,
    fontWeight: '500',
  },
  sessionDate: {
    fontSize: 16,
    fontWeight: '700',
  },
  sessionInfo: {
    flex: 1,
    marginLeft: 12,
  },
  sessionName: {
    fontSize: 15,
    fontWeight: '500',
    marginBottom: 2,
  },
  sessionDetails: {
    fontSize: 12,
  },
  sessionStatus: {
    marginLeft: 8,
  },
  statusIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default WorkoutPlanComponent; 