import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  StyleSheet, 
  Alert, 
  Platform,
  ScrollView,
  ActivityIndicator 
} from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { getAuth } from 'firebase/auth';
import { getFirestore, doc, getDoc } from 'firebase/firestore';

export function AuthTestView() {
  const { user, signIn, signInWithGoogle, signUpWithEmail, signOut, loading } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [isSignUp, setIsSignUp] = useState(false);
  const [testStatus, setTestStatus] = useState<string[]>([]);
  const [profileData, setProfileData] = useState<any>(null);
  
  const auth = getAuth();
  const db = getFirestore();

  useEffect(() => {
    addStatus(`Platform: ${Platform.OS}`);
    addStatus(`Environment: ${process.env.EXPO_PUBLIC_ENVIRONMENT || 'development'}`);
    addStatus(`Auth Domain: ${process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN || 'not set'}`);
  }, []);

  useEffect(() => {
    if (user) {
      addStatus(`User logged in: ${user.email}`);
      loadProfile();
    } else {
      addStatus('No user logged in');
      setProfileData(null);
    }
  }, [user]);

  const addStatus = (message: string) => {
    setTestStatus(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const loadProfile = async () => {
    if (!user) return;
    
    try {
      const profileDoc = await getDoc(doc(db, 'profiles', user.id));
      if (profileDoc.exists()) {
        setProfileData(profileDoc.data());
        addStatus('Profile loaded from Firestore');
      } else {
        addStatus('No profile found in Firestore');
      }
    } catch (error) {
      addStatus(`Profile load error: ${error.message}`);
    }
  };

  const handleEmailAuth = async () => {
    try {
      if (isSignUp) {
        addStatus(`Attempting sign up for ${email}`);
        const result = await signUpWithEmail(email, password, name);
        if (result.error) {
          addStatus(`Sign up failed: ${result.error.message}`);
          Alert.alert('Sign Up Failed', result.error.message);
        } else {
          addStatus('Sign up successful!');
          Alert.alert('Success', 'Account created successfully!');
        }
      } else {
        addStatus(`Attempting sign in for ${email}`);
        const result = await signIn(email, password);
        if (!result.success) {
          addStatus(`Sign in failed: ${result.error}`);
          Alert.alert('Sign In Failed', String(result.error));
        } else {
          addStatus('Sign in successful!');
          Alert.alert('Success', 'Signed in successfully!');
        }
      }
    } catch (error) {
      addStatus(`Auth error: ${error.message}`);
      Alert.alert('Error', error.message);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      addStatus('Attempting Google sign in...');
      const result = await signInWithGoogle();
      if (result.success) {
        addStatus('Google sign in successful!');
        Alert.alert('Success', 'Signed in with Google!');
      } else {
        addStatus(`Google sign in failed: ${result.error}`);
        Alert.alert('Google Sign In Failed', result.error || 'Unknown error');
      }
    } catch (error) {
      addStatus(`Google auth error: ${error.message}`);
      Alert.alert('Error', error.message);
    }
  };

  const handleSignOut = async () => {
    try {
      addStatus('Attempting sign out...');
      await signOut();
      addStatus('Sign out successful!');
      Alert.alert('Success', 'Signed out successfully!');
    } catch (error) {
      addStatus(`Sign out error: ${error.message}`);
      Alert.alert('Error', error.message);
    }
  };

  const testNetworkConnectivity = async () => {
    try {
      addStatus('Testing network connectivity...');
      const response = await fetch('https://www.google.com');
      addStatus(`Network test: ${response.ok ? 'Connected' : 'Failed'}`);
    } catch (error) {
      addStatus(`Network test failed: ${error.message}`);
    }
  };

  const clearTestStatus = () => {
    setTestStatus([]);
    addStatus('Status cleared');
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Authentication Test</Text>
        
        {/* Current User Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Current User</Text>
          {user ? (
            <>
              <Text>Email: {user.email}</Text>
              <Text>Name: {user.name || 'Not set'}</Text>
              <Text>ID: {user.id}</Text>
              {profileData && (
                <View style={styles.profileData}>
                  <Text style={styles.profileTitle}>Firestore Profile:</Text>
                  <Text>Full Name: {profileData.full_name || 'Not set'}</Text>
                  <Text>Auth Provider: {profileData.auth_provider || 'email'}</Text>
                </View>
              )}
            </>
          ) : (
            <Text>Not signed in</Text>
          )}
        </View>

        {/* Sign In/Up Form */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {isSignUp ? 'Sign Up' : 'Sign In'}
          </Text>
          
          {isSignUp && (
            <TextInput
              style={styles.input}
              placeholder="Name"
              value={name}
              onChangeText={setName}
              autoCapitalize="words"
            />
          )}
          
          <TextInput
            style={styles.input}
            placeholder="Email"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />
          
          <TextInput
            style={styles.input}
            placeholder="Password"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
          />
          
          <TouchableOpacity 
            style={[styles.button, loading && styles.buttonDisabled]}
            onPress={handleEmailAuth}
            disabled={loading}
          >
            <Text style={styles.buttonText}>
              {loading ? 'Loading...' : (isSignUp ? 'Sign Up' : 'Sign In')}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity onPress={() => setIsSignUp(!isSignUp)}>
            <Text style={styles.switchText}>
              {isSignUp ? 'Already have an account? Sign In' : "Don't have an account? Sign Up"}
            </Text>
          </TouchableOpacity>
        </View>

        {/* OAuth Buttons */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>OAuth Providers</Text>
          
          <TouchableOpacity 
            style={[styles.button, styles.googleButton, loading && styles.buttonDisabled]}
            onPress={handleGoogleSignIn}
            disabled={loading}
          >
            <Text style={styles.buttonText}>Sign in with Google</Text>
          </TouchableOpacity>
        </View>

        {/* Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Actions</Text>
          
          <TouchableOpacity 
            style={[styles.button, styles.signOutButton, (!user || loading) && styles.buttonDisabled]}
            onPress={handleSignOut}
            disabled={!user || loading}
          >
            <Text style={styles.buttonText}>Sign Out</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, styles.testButton]}
            onPress={testNetworkConnectivity}
          >
            <Text style={styles.buttonText}>Test Network</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, styles.clearButton]}
            onPress={clearTestStatus}
          >
            <Text style={styles.buttonText}>Clear Status</Text>
          </TouchableOpacity>
        </View>

        {/* Test Status */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Test Status</Text>
          <View style={styles.statusContainer}>
            {testStatus.map((status, index) => (
              <Text key={index} style={styles.statusText}>{status}</Text>
            ))}
          </View>
        </View>

        {loading && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="large" color="#007bff" />
          </View>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    padding: 20,
    paddingBottom: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  section: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    fontSize: 16,
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 10,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  googleButton: {
    backgroundColor: '#4285f4',
  },
  signOutButton: {
    backgroundColor: '#dc3545',
  },
  testButton: {
    backgroundColor: '#28a745',
  },
  clearButton: {
    backgroundColor: '#6c757d',
  },
  switchText: {
    textAlign: 'center',
    color: '#007bff',
    marginTop: 10,
  },
  profileData: {
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  profileTitle: {
    fontWeight: '600',
    marginBottom: 5,
  },
  statusContainer: {
    maxHeight: 200,
  },
  statusText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 5,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});