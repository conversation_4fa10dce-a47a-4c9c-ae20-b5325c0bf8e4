import React, { ReactNode, useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { getAuth, onAuthStateChanged } from 'firebase/auth';

interface DebugWrapperProps {
  children: ReactNode;
  componentName?: string;
}

/**
 * Wraps any component with debugging tools to help diagnose loading issues
 * Usage: <DebugWrapper componentName="ProfileScreen">{yourComponent}</DebugWrapper>
 */
export default function DebugWrapper({ children, componentName = 'Unknown' }: DebugWrapperProps) {
  const { colors, isDark } = useTheme();
  const [showDebugger, setShowDebugger] = useState(false);
  const [authInfo, setAuthInfo] = useState<{authenticated: boolean; userId?: string}>({
    authenticated: false
  });
  const [loadTimes, setLoadTimes] = useState<{start: number; end?: number}>({
    start: Date.now()
  });

  // Check authentication status
  useEffect(() => {
    const auth = getAuth();
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setAuthInfo({
        authenticated: !!user,
        userId: user?.uid
      });
    });
    
    // Record component load time
    const loadEnd = Date.now();
    setLoadTimes(prev => ({ ...prev, end: loadEnd }));
    
    console.log(`[DEBUG] ${componentName} mounted in ${loadEnd - loadTimes.start}ms`);
    
    return () => unsubscribe();
  }, [componentName]);

  const toggleDebugger = () => {
    setShowDebugger(prev => !prev);
  };

  return (
    <View style={styles.container}>
      {children}
      
      {/* Debug info button */}
      <TouchableOpacity 
        style={[
          styles.debugButton, 
          { backgroundColor: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)' }
        ]}
        onPress={toggleDebugger}
      >
        <Text style={[styles.debugButtonText, { color: colors.primary }]}>
          {showDebugger ? 'Hide Debug' : 'Debug'}
        </Text>
      </TouchableOpacity>
      
      {/* Debug panel */}
      {showDebugger && (
        <View style={[
          styles.debugPanel, 
          { backgroundColor: isDark ? 'rgba(0, 0, 0, 0.9)' : 'rgba(255, 255, 255, 0.95)' }
        ]}>
          <ScrollView>
            <Text style={[styles.debugTitle, { color: colors.text }]}>
              Debug: {componentName}
            </Text>
            
            {/* Auth status */}
            <View style={styles.debugSection}>
              <Text style={[styles.debugSectionTitle, { color: colors.text }]}>Auth Status</Text>
              <Text style={[styles.debugText, { color: colors.text }]}>
                {authInfo.authenticated ? '✓ Authenticated' : '✗ Not Authenticated'}
              </Text>
              {authInfo.userId && (
                <Text style={[styles.debugText, { color: colors.textSecondary }]}>
                  User ID: {authInfo.userId}
                </Text>
              )}
            </View>
            
            {/* Load time */}
            <View style={styles.debugSection}>
              <Text style={[styles.debugSectionTitle, { color: colors.text }]}>Performance</Text>
              <Text style={[styles.debugText, { color: colors.text }]}>
                Component Load Time: {loadTimes.end 
                  ? `${loadTimes.end - loadTimes.start}ms` 
                  : 'Calculating...'}
              </Text>
            </View>
            
            {/* Firebase Diagnostics */}
            <View style={styles.debugSection}>
              <Text style={[styles.debugSectionTitle, { color: colors.text }]}>
                Firebase Diagnostics
              </Text>
              <Text style={[styles.debugText, { color: colors.text }]}>
                Firebase Auth: {authInfo.authenticated ? 'Connected' : 'Not Connected'}
              </Text>
              <Text style={[styles.debugText, { color: colors.text }]}>
                Firestore: {true ? 'Connected' : 'Not Connected'}
              </Text>
              <Text style={[styles.debugText, { color: colors.text }]}>
                Storage: {true ? 'Connected' : 'Not Connected'}
              </Text>
            </View>
          </ScrollView>
          
          <TouchableOpacity
            style={[styles.closeButton, { backgroundColor: colors.primary }]}
            onPress={() => setShowDebugger(false)}
          >
            <Text style={styles.closeButtonText}>Close Debugger</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  debugButton: {
    position: 'absolute',
    top: 50,
    right: 10,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    zIndex: 9999,
  },
  debugButtonText: {
    fontWeight: 'bold',
    fontSize: 12,
  },
  debugPanel: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
    padding: 16,
  },
  debugTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  debugSection: {
    marginBottom: 16,
  },
  debugSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  debugText: {
    fontSize: 14,
    marginBottom: 4,
  },
  closeButton: {
    padding: 12,
    alignItems: 'center',
    borderRadius: 6,
    marginTop: 16,
  },
  closeButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
}); 