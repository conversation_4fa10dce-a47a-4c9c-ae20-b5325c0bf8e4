import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, ScrollView, ActivityIndicator } from 'react-native';
import { analyzeAdvancedFoodImage, UnifiedFoodAnalysisResult } from '@/services';
import { Feather } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { useTheme } from '@/contexts/ThemeContext';

export default function FoodVisionDemo() {
  const { colors } = useTheme();
  const [image, setImage] = useState<string | null>(null);
  const [result, setResult] = useState<UnifiedFoodAnalysisResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      setError('Permission to access media library was denied');
      return;
    }

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled) {
        setImage(result.assets[0].uri);
        setResult(null);
        setError(null);
      }
    } catch (error) {
      setError('Error selecting image: ' + (error as Error).message);
    }
  };

  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      setError('Permission to access camera was denied');
      return;
    }

    try {
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled) {
        setImage(result.assets[0].uri);
        setResult(null);
        setError(null);
      }
    } catch (error) {
      setError('Error taking photo: ' + (error as Error).message);
    }
  };

  const analyzeImage = async () => {
    if (!image) return;

    setLoading(true);
    setError(null);

    try {
      const analysisResult = await analyzeAdvancedFoodImage(image, {
        includeVolumeEstimation: true,
        includeFineGrainedDetails: true,
        includeNutritionalAnalysis: true,
        includeTextualDescription: true,
        modelQuality: 'accurate'
      });

      setResult(analysisResult);
    } catch (error) {
      setError('Error analyzing image: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>Food Vision AI 2025</Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Advanced multimodal food recognition
        </Text>
      </View>

      <View style={styles.imageContainer}>
        {image ? (
          <Image source={{ uri: image }} style={styles.image} />
        ) : (
          <View style={[styles.placeholderImage, { backgroundColor: colors.card }]}>
            <ImageIcon size={64} color={colors.primary} />
            <Text style={[styles.placeholderText, { color: colors.textSecondary }]}>
              Select or take a photo of food
            </Text>
          </View>
        )}
      </View>

      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.button, { backgroundColor: colors.card }]}
          onPress={pickImage}
        >
          <ImageIcon size={24} color={colors.primary} />
          <Text style={[styles.buttonText, { color: colors.text }]}>Gallery</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, { backgroundColor: colors.card }]}
          onPress={takePhoto}
        >
          <Feather name="camera" size={24} color={colors.primary} />
          <Text style={[styles.buttonText, { color: colors.text }]}>Camera</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.analyzeButton,
            { backgroundColor: colors.primary, opacity: image ? 1 : 0.5 }
          ]}
          onPress={analyzeImage}
          disabled={!image || loading}
        >
          {loading ? (
            <ActivityIndicator color="#fff" size="small" />
          ) : (
            <>
              <Feather name="zap" size={24}  color={colors.text} />
              <Text style={styles.analyzeButtonText}>Analyze</Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      {error && (
        <View style={[styles.errorContainer, { backgroundColor: 'rgba(255, 0, 0, 0.1)' }]}>
          <Text style={[styles.errorText, { color: 'rgb(200, 0, 0)' }]}>{error}</Text>
        </View>
      )}

      {result && (
        <View style={[styles.resultContainer, { backgroundColor: colors.card }]}>
          <Text style={[styles.resultTitle, { color: colors.text }]}>Analysis Results</Text>
          
          <View style={styles.resultSection}>
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>Description</Text>
            <Text style={[styles.resultText, { color: colors.text }]}>
              {result.textualAnalysis.description}
            </Text>
          </View>

          <View style={styles.resultSection}>
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>Detected Foods</Text>
            {result.foodItems.map((food, index) => (
              <View key={index} style={styles.foodItem}>
                <Text style={[styles.foodName, { color: colors.text }]}>{food.name}</Text>
                <Text style={[styles.foodConfidence, { color: colors.textSecondary }]}>
                  {Math.round(food.confidence * 100)}% confidence
                </Text>
              </View>
            ))}
          </View>

          <View style={styles.resultSection}>
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>Nutrition</Text>
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: colors.text }]}>Calories:</Text>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>
                {result.nutritionalSummary.totalCalories} kcal
              </Text>
            </View>
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: colors.text }]}>Protein:</Text>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>
                {result.nutritionalSummary.macronutrients.protein}g
              </Text>
            </View>
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: colors.text }]}>Carbs:</Text>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>
                {result.nutritionalSummary.macronutrients.carbs}g
              </Text>
            </View>
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: colors.text }]}>Fat:</Text>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>
                {result.nutritionalSummary.macronutrients.fat}g
              </Text>
            </View>
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: colors.text }]}>Fiber:</Text>
              <Text style={[styles.nutritionValue, { color: colors.text }]}>
                {result.nutritionalSummary.macronutrients.fiber}g
              </Text>
            </View>
          </View>

          <View style={styles.resultSection}>
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>Health Insights</Text>
            {result.textualAnalysis.healthInsights.map((insight, index) => (
              <View key={index} style={styles.insightItem}>
                <Feather name="chevron-right" size={16} color={colors.primary} />
                <Text style={[styles.insightText, { color: colors.text }]}>{insight}</Text>
              </View>
            ))}
          </View>

          <View style={styles.resultSection}>
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>Dietary Categories</Text>
            <View style={styles.categoriesContainer}>
              {result.textualAnalysis.dietaryCategories.length > 0 ? (
                result.textualAnalysis.dietaryCategories.map((category, index) => (
                  <View 
                    key={index} 
                    style={[styles.categoryTag, { backgroundColor: colors.primaryLight }]}
                  >
                    <Text style={[styles.categoryText, { color: colors.primary }]}>
                      {category}
                    </Text>
                  </View>
                ))
              ) : (
                <Text style={[styles.noDataText, { color: colors.textSecondary }]}>
                  No specific dietary categories detected
                </Text>
              )}
            </View>
          </View>

          <View style={styles.resultSection}>
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>Analysis Details</Text>
            <Text style={[styles.detailText, { color: colors.textSecondary }]}>
              Model: {result.meta.modelVersion}
            </Text>
            <Text style={[styles.detailText, { color: colors.textSecondary }]}>
              Processing time: {result.meta.processingTimeMs}ms
            </Text>
            <Text style={[styles.detailText, { color: colors.textSecondary }]}>
              Confidence: {Math.round(result.meta.confidenceScore * 100)}%
            </Text>
          </View>
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
  },
  imageContainer: {
    aspectRatio: 4/3,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
  },
  placeholderText: {
    marginTop: 12,
    fontSize: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  button: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    marginHorizontal: 4,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  buttonText: {
    fontSize: 16,
    marginLeft: 8,
  },
  analyzeButton: {
    flex: 1.5,
    padding: 12,
    borderRadius: 8,
    marginHorizontal: 4,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  analyzeButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  errorContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
  },
  resultContainer: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  resultTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  resultSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  resultText: {
    fontSize: 16,
    lineHeight: 24,
  },
  foodItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  foodName: {
    fontSize: 16,
  },
  foodConfidence: {
    fontSize: 14,
  },
  nutritionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  nutritionLabel: {
    fontSize: 16,
  },
  nutritionValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  insightText: {
    fontSize: 16,
    marginLeft: 8,
    flex: 1,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
  },
  categoryTag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
  },
  noDataText: {
    fontSize: 16,
    fontStyle: 'italic',
  },
  detailText: {
    fontSize: 14,
    marginBottom: 4,
  },
}); 