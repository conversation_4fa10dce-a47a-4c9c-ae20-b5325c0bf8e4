import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, ActivityIndicator, StyleSheet, ScrollView, Switch } from 'react-native';
import { useDatabaseType } from '@/contexts/DatabaseContext';
import { useLidarVolumeService, FoodSegment, DistributeVolumeResponse } from '@/services/lidarVolumeService';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Component to test the LiDAR volume distribution function with both Firebase and Firebase
 */
export default function LidarVolumeTest() {
  const { useFirebase, toggleProvider } = useDatabaseType();
  const { user } = useAuth();
  const { distributeVolume } = useLidarVolumeService();
  
  // Form state
  const [totalVolume, setTotalVolume] = useState('500');
  const [segmentCount, setSegmentCount] = useState(3);
  const [segments, setSegments] = useState<FoodSegment[]>(generateSampleSegments(3));
  
  // Loading and result state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<DistributeVolumeResponse | null>(null);
  
  // When segment count changes, generate new sample segments
  const handleSegmentCountChange = (value: string) => {
    const count = parseInt(value);
    if (!isNaN(count) && count > 0 && count <= 10) {
      setSegmentCount(count);
      setSegments(generateSampleSegments(count));
    }
  };
  
  const handleDistributeVolume = async () => {
    setLoading(true);
    setError(null);
    setResult(null);
    
    try {
      const volumeResult = await distributeVolume({
        totalVolumeCm3: parseFloat(totalVolume),
        segments,
        userId: user?.id
      });
      
      setResult(volumeResult);
    } catch (err) {
      console.error('Error distributing volume:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>LiDAR Volume Distribution Test</Text>
        <View style={styles.providerToggle}>
          <Text>Using: 'Firebase'</Text>
          <Switch
            value={useFirebase}
            onValueChange={toggleProvider}
            thumbColor={useFirebase ? '#4285F4' : '#49E9A6'}
          />
        </View>
      </View>
      
      <View style={styles.formContainer}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Total Volume (cm³):</Text>
          <TextInput
            style={styles.input}
            value={totalVolume}
            onChangeText={setTotalVolume}
            keyboardType="numeric"
            placeholder="Enter total volume"
          />
        </View>
        
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Number of Segments:</Text>
          <TextInput
            style={styles.input}
            value={String(segmentCount)}
            onChangeText={handleSegmentCountChange}
            keyboardType="numeric"
            placeholder="Enter number of segments (1-10)"
          />
        </View>
        
        <View style={styles.segmentsContainer}>
          <Text style={styles.label}>Segments:</Text>
          {segments.map((segment, index) => (
            <View key={segment.id} style={styles.segmentItem}>
              <Text>Segment {index + 1}: {segment.id}</Text>
              <Text>Area: {segment.boundingBox.width * segment.boundingBox.height} px²</Text>
              <Text>Confidence: {(segment.confidence * 100).toFixed(1)}%</Text>
            </View>
          ))}
        </View>
      </View>
      
      <TouchableOpacity 
        style={styles.distributeButton} 
        onPress={handleDistributeVolume}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="white" />
        ) : (
          <Text style={styles.distributeButtonText}>Distribute Volume</Text>
        )}
      </TouchableOpacity>
      
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
      
      {result && (
        <View style={styles.resultContainer}>
          <Text style={styles.sectionHeader}>Volume Distribution Results:</Text>
          
          {result.segments.map((segment, index) => (
            <View key={segment.id} style={styles.resultItem}>
              <Text style={styles.resultTitle}>Segment {index + 1}</Text>
              <Text>ID: {segment.id}</Text>
              <Text>Volume: {segment.estimatedVolumeCm3?.toFixed(2)} cm³</Text>
              <Text>Percentage: {((segment.estimatedVolumeCm3 || 0) / parseFloat(totalVolume) * 100).toFixed(1)}%</Text>
              
              <View style={styles.volumeBar}>
                <View 
                  style={[
                    styles.volumeBarFill, 
                    { width: `${(segment.estimatedVolumeCm3 || 0) / parseFloat(totalVolume) * 100}%` }
                  ]} 
                />
              </View>
            </View>
          ))}
          
          <View style={styles.resultSummary}>
            <Text>Total Volume: {parseFloat(totalVolume).toFixed(2)} cm³</Text>
            <Text>Distributed Volume: {result.segments.reduce((total, segment) => total + (segment.estimatedVolumeCm3 || 0), 0).toFixed(2)} cm³</Text>
          </View>
        </View>
      )}
    </ScrollView>
  );
}

/**
 * Generate sample food segments for testing
 */
function generateSampleSegments(count: number): FoodSegment[] {
  const segments: FoodSegment[] = [];
  
  for (let i = 0; i < count; i++) {
    // Random bounding box dimensions
    const width = Math.floor(Math.random() * 100) + 50;
    const height = Math.floor(Math.random() * 100) + 50;
    const x = Math.floor(Math.random() * (512 - width));
    const y = Math.floor(Math.random() * (512 - height));
    
    // Random confidence between 0.7 and 1.0
    const confidence = 0.7 + Math.random() * 0.3;
    
    segments.push({
      id: `food-${i + 1}`,
      boundingBox: { x, y, width, height },
      confidence,
    });
  }
  
  return segments;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
  },
  providerToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  formContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#eee',
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 10,
    backgroundColor: 'white',
  },
  segmentsContainer: {
    marginTop: 16,
  },
  segmentItem: {
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  distributeButton: {
    backgroundColor: '#4285F4',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 16,
  },
  distributeButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  errorContainer: {
    padding: 16,
    backgroundColor: '#ffebee',
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#ffcdd2',
  },
  errorText: {
    color: '#c62828',
  },
  resultContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#eee',
  },
  sectionHeader: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  resultItem: {
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  resultTitle: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  volumeBar: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    marginTop: 8,
    overflow: 'hidden',
  },
  volumeBarFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 4,
  },
  resultSummary: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#e8f4fd',
    borderRadius: 8,
  },
}); 