import React, { useState } from 'react';
import { View, Text, Button, TextInput, StyleSheet, Alert } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { getAuth } from 'firebase/auth';

export function ProductionAuthTest() {
  const { signIn, signInWithGoogle, signUpWithEmail, signOut } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  
  const auth = getAuth();

  const testEmailSignUp = async () => {
    try {
      setLoading(true);
      await signUpWithEmail(email, password, 'Test User');
      Alert.alert('Success', 'Sign up successful!');
    } catch (error: any) {
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  const testEmailSignIn = async () => {
    try {
      setLoading(true);
      await signIn(email, password);
      Alert.alert('Success', 'Sign in successful!');
    } catch (error: any) {
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  const testGoogleSignIn = async () => {
    try {
      setLoading(true);
      await signInWithGoogle();
      Alert.alert('Success', 'Google sign in successful!');
    } catch (error: any) {
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  const testSignOut = async () => {
    try {
      setLoading(true);
      await signOut();
      Alert.alert('Success', 'Sign out successful!');
    } catch (error: any) {
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Production Auth Test</Text>
      
      <Text style={styles.info}>
        Current User: {auth.currentUser?.email || 'Not signed in'}
      </Text>
      
      <TextInput
        style={styles.input}
        placeholder="Email"
        value={email}
        onChangeText={setEmail}
        autoCapitalize="none"
        keyboardType="email-address"
      />
      
      <TextInput
        style={styles.input}
        placeholder="Password"
        value={password}
        onChangeText={setPassword}
        secureTextEntry
      />
      
      <View style={styles.buttonContainer}>
        <Button
          title="Sign Up"
          onPress={testEmailSignUp}
          disabled={loading || !email || !password}
        />
        
        <Button
          title="Sign In"
          onPress={testEmailSignIn}
          disabled={loading || !email || !password}
        />
        
        <Button
          title="Google Sign In"
          onPress={testGoogleSignIn}
          disabled={loading}
        />
        
        <Button
          title="Sign Out"
          onPress={testSignOut}
          disabled={loading || !auth.currentUser}
        />
      </View>
      
      <Text style={styles.info}>
        Environment: {process.env.EXPO_PUBLIC_ENVIRONMENT || 'development'}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  info: {
    marginBottom: 15,
    textAlign: 'center',
    color: '#666',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 10,
    marginBottom: 15,
  },
  buttonContainer: {
    gap: 10,
  },
});