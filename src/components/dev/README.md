# Development Components

This folder contains components used exclusively for development, testing, and debugging purposes.

## Usage

These components should **NOT** be included in production builds. They are meant for:

- Testing authentication flows
- Testing Firebase functions
- Debugging UI components
- Testing API integrations
- Performance testing
- Development utilities

## Components

### Authentication Testing
- `AuthTestView` - Comprehensive auth testing UI
- `ProductionAuthTest` - Production environment auth testing

### Payment Testing
- `StripeCheckoutTest` - Stripe payment flow testing

### Feature Testing
- `FoodVisionDemo` - Food recognition testing
- `LidarVolumeTest` - LiDAR scanning testing
- `RecipeImageGeneratorTest` - Image generation testing
- `SegmentVisualizationTest` - Food segmentation testing

### Development Utilities
- `DebugWrapper` - Debugging wrapper component
- `SyncIntegrationExample` - Data sync testing
- `SkeletonExample` - Loading skeleton examples

## Import Example

```typescript
// Only import in development screens
import { AuthTestView } from '@/components/dev';

// Use in a development screen
export default function DevScreen() {
  return <AuthTestView />;
}
```

## Production Exclusion

This folder is automatically excluded from production builds through:

### Metro Configuration
The `metro.config.js` automatically excludes dev components in production:
```javascript
if (process.env.NODE_ENV === 'production') {
  defaultConfig.resolver.blockList = [
    /components\/dev\/.*/,
    /app\/dev\/.*/,
    // ... other exclusions
  ];
}
```

### TypeScript Configuration
A production TypeScript config is available at `tsconfig.production.json`

### Build Scripts
Production build scripts set `NODE_ENV=production`:
```json
"build:web": "NODE_ENV=production expo export --platform web",
"build:ios": "NODE_ENV=production eas build --platform ios --profile production",
"build:android": "NODE_ENV=production eas build --platform android --profile production"
```

### Webpack Configuration
Web builds also exclude dev components in production mode.

## Adding New Test Components

When adding new test components:
1. Place them in this folder
2. Export them from `index.ts`
3. Add documentation here
4. Ensure they're not imported in production code