import React, { useState } from 'react';
import { View, Text, TextInput, Image, TouchableOpacity, ActivityIndicator, StyleSheet, ScrollView, Switch } from 'react-native';
import { useDatabaseType } from '@/contexts/DatabaseContext';
import { useRecipeImageService, RecipeImageResult } from '@/services/recipeImageService';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Component to test the recipe image generation function with both Firebase and Firebase
 */
export default function RecipeImageGeneratorTest() {
  const { useFirebase, toggleProvider } = useDatabaseType();
  const { user } = useAuth();
  const { generateRecipeImage } = useRecipeImageService();
  
  // Recipe details form state
  const [recipeName, setRecipeName] = useState('Pasta Carbonara');
  const [ingredients, setIngredients] = useState('pasta, eggs, bacon, cheese, pepper');
  const [cuisine, setCuisine] = useState('Italian');
  const [mealType, setMealType] = useState('dinner');
  const [style, setStyle] = useState<'realistic' | 'illustration' | 'overhead' | 'closeup'>('realistic');
  
  // Loading and result state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<RecipeImageResult | null>(null);
  
  const handleGenerateImage = async () => {
    if (!user) {
      setError("You must be logged in to use this function");
      return;
    }
    
    setLoading(true);
    setError(null);
    setResult(null);
    
    try {
      const ingredientsList = ingredients.split(',').map(i => i.trim()).filter(i => i);
      
      const imageResult = await generateRecipeImage({
        recipeName,
        ingredients: ingredientsList,
        cuisine,
        mealType,
        style,
        saveResult: true
      });
      
      setResult(imageResult);
    } catch (err) {
      console.error('Error generating recipe image:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Recipe Image Generator</Text>
        <View style={styles.providerToggle}>
          <Text>Using: 'Firebase'</Text>
          <Switch
            value={useFirebase}
            onValueChange={toggleProvider}
            thumbColor={useFirebase ? '#4285F4' : '#49E9A6'}
          />
        </View>
      </View>
      
      <View style={styles.formContainer}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Recipe Name:</Text>
          <TextInput
            style={styles.input}
            value={recipeName}
            onChangeText={setRecipeName}
            placeholder="Enter recipe name"
          />
        </View>
        
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Ingredients (comma separated):</Text>
          <TextInput
            style={styles.input}
            value={ingredients}
            onChangeText={setIngredients}
            placeholder="Enter ingredients"
            multiline
          />
        </View>
        
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Cuisine:</Text>
          <TextInput
            style={styles.input}
            value={cuisine}
            onChangeText={setCuisine}
            placeholder="Enter cuisine type"
          />
        </View>
        
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Meal Type:</Text>
          <TextInput
            style={styles.input}
            value={mealType}
            onChangeText={setMealType}
            placeholder="Enter meal type"
          />
        </View>
        
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Image Style:</Text>
          <View style={styles.styleSelector}>
            {(['realistic', 'illustration', 'overhead', 'closeup'] as const).map((styleOption) => (
              <TouchableOpacity
                key={styleOption}
                style={[
                  styles.styleButton,
                  style === styleOption && styles.styleButtonSelected
                ]}
                onPress={() => setStyle(styleOption)}
              >
                <Text style={[
                  styles.styleButtonText,
                  style === styleOption && styles.styleButtonTextSelected
                ]}>
                  {styleOption.charAt(0).toUpperCase() + styleOption.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
      
      <TouchableOpacity 
        style={styles.generateButton} 
        onPress={handleGenerateImage}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="white" />
        ) : (
          <Text style={styles.generateButtonText}>Generate Image</Text>
        )}
      </TouchableOpacity>
      
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
      
      {result && (
        <View style={styles.resultContainer}>
          <Text style={styles.sectionHeader}>Generated Image:</Text>
          <Text style={styles.promptText}>Prompt: {result.prompt}</Text>
          
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: result.imageUrl }}
              style={styles.recipeImage}
              resizeMode="cover"
            />
          </View>
          
          <View style={styles.resultDetails}>
            <Text>Style: {result.style}</Text>
            <Text>Generated on: {new Date(result.createdAt).toLocaleString()}</Text>
            <Text>Processing ID: {result.id}</Text>
          </View>
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
  },
  providerToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  formContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#eee',
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 10,
    backgroundColor: 'white',
  },
  styleSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  styleButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  styleButtonSelected: {
    backgroundColor: '#4285F4',
  },
  styleButtonText: {
    color: '#333',
  },
  styleButtonTextSelected: {
    color: 'white',
  },
  generateButton: {
    backgroundColor: '#4285F4',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 16,
  },
  generateButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  errorContainer: {
    padding: 16,
    backgroundColor: '#ffebee',
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#ffcdd2',
  },
  errorText: {
    color: '#c62828',
  },
  resultContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#eee',
  },
  sectionHeader: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  promptText: {
    fontStyle: 'italic',
    color: '#666',
    marginBottom: 16,
  },
  imageContainer: {
    alignItems: 'center',
    marginVertical: 16,
    backgroundColor: '#f5f5f5',
    padding: 8,
    borderRadius: 8,
  },
  recipeImage: {
    width: '100%',
    height: 300,
    borderRadius: 8,
  },
  resultDetails: {
    marginTop: 16,
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
  },
}); 