import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, ActivityIndicator, Image, StyleSheet, ScrollView, Switch } from 'react-native';
import { useDatabaseType } from '@/contexts/DatabaseContext';
import { useSegmentVisualizationService, FoodSegment, SegmentVisualizationResponse } from '@/services/segmentVisualizationService';
import { useLidarVolumeService } from '@/services/lidarVolumeService';
import { useAuth } from '@/contexts/AuthContext';
import * as Linking from 'expo-linking';

/**
 * Component to test the segment visualization function with both Firebase and Firebase
 */
export default function SegmentVisualizationTest() {
  const { useFirebase, toggleProvider } = useDatabaseType();
  const { user } = useAuth();
  const { generateVisualization } = useSegmentVisualizationService();
  const { distributeVolume } = useLidarVolumeService();
  
  // Form state
  const [imageUrl, setImageUrl] = useState('https://example.com/food-image.jpg');
  const [segmentCount, setSegmentCount] = useState(3);
  const [segments, setSegments] = useState<FoodSegment[]>(generateSampleSegments(3));
  const [highlightMode, setHighlightMode] = useState<'overlay' | 'outline' | 'mask'>('overlay');
  
  // Loading and result state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<SegmentVisualizationResponse | null>(null);
  
  // When segment count changes, generate new sample segments
  const handleSegmentCountChange = (value: string) => {
    const count = parseInt(value);
    if (!isNaN(count) && count > 0 && count <= 10) {
      setSegmentCount(count);
      setSegments(generateSampleSegments(count));
    }
  };
  
  // Distribute volumes among segments as a preprocessing step
  const handleDistributeAndVisualize = async () => {
    setLoading(true);
    setError(null);
    setResult(null);
    
    try {
      // First distribute volumes among segments
      const totalVolumeCm3 = 500; // Default volume in cm³
      const volumeResult = await distributeVolume({
        totalVolumeCm3,
        segments,
        userId: user?.id
      });
      
      // Then generate visualization with the updated segments
      const visualizationResult = await generateVisualization({
        imageUrl,
        segments: volumeResult.segments,
        highlightMode
      });
      
      setResult(visualizationResult);
    } catch (err) {
      console.error('Error generating visualization:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Segment Visualization Test</Text>
        <View style={styles.providerToggle}>
          <Text>Using: 'Firebase'</Text>
          <Switch
            value={useFirebase}
            onValueChange={toggleProvider}
            thumbColor={useFirebase ? '#4285F4' : '#49E9A6'}
          />
        </View>
      </View>
      
      <View style={styles.formContainer}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Image URL:</Text>
          <TextInput
            style={styles.input}
            value={imageUrl}
            onChangeText={setImageUrl}
            placeholder="Enter image URL"
          />
        </View>
        
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Number of Segments:</Text>
          <TextInput
            style={styles.input}
            value={String(segmentCount)}
            onChangeText={handleSegmentCountChange}
            keyboardType="numeric"
            placeholder="Enter number of segments (1-10)"
          />
        </View>
        
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Highlight Mode:</Text>
          <View style={styles.modeSelector}>
            {(['overlay', 'outline', 'mask'] as const).map((mode) => (
              <TouchableOpacity
                key={mode}
                style={[
                  styles.modeButton,
                  highlightMode === mode && styles.modeButtonSelected
                ]}
                onPress={() => setHighlightMode(mode)}
              >
                <Text style={[
                  styles.modeButtonText,
                  highlightMode === mode && styles.modeButtonTextSelected
                ]}>
                  {mode.charAt(0).toUpperCase() + mode.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        <View style={styles.segmentsContainer}>
          <Text style={styles.label}>Segments:</Text>
          {segments.map((segment, index) => (
            <View key={segment.id} style={styles.segmentItem}>
              <Text>Segment {index + 1}: {segment.id}</Text>
              <Text>Area: {segment.boundingBox.width * segment.boundingBox.height} px²</Text>
              <Text>Confidence: {(segment.confidence * 100).toFixed(1)}%</Text>
            </View>
          ))}
        </View>
      </View>
      
      <TouchableOpacity 
        style={styles.visualizeButton} 
        onPress={handleDistributeAndVisualize}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="white" />
        ) : (
          <Text style={styles.visualizeButtonText}>Generate Visualization</Text>
        )}
      </TouchableOpacity>
      
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
      
      {result && (
        <View style={styles.resultContainer}>
          <Text style={styles.sectionHeader}>Visualization Results:</Text>
          
          <View style={styles.resultDetails}>
            <Text>Visualization ID: {result.visualizationId}</Text>
            <Text>Segments Visualized: {result.segmentsVisualized}</Text>
            <Text>Highlight Mode: {highlightMode}</Text>
            
            <TouchableOpacity 
              style={styles.linkButton}
              onPress={() => Linking.openURL(result.visualizedImageUrl)}
            >
              <Text style={styles.linkButtonText}>Open Visualization</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.imageContainer}>
            <Text style={styles.label}>Preview:</Text>
            <Image
              source={{ uri: result.visualizedImageUrl }}
              style={styles.previewImage}
              resizeMode="contain"
            />
            <Text style={styles.caption}>Note: This is a simulated visualization</Text>
          </View>
        </View>
      )}
    </ScrollView>
  );
}

/**
 * Generate sample food segments for testing
 */
function generateSampleSegments(count: number): FoodSegment[] {
  const segments: FoodSegment[] = [];
  
  for (let i = 0; i < count; i++) {
    // Random bounding box dimensions
    const width = Math.floor(Math.random() * 100) + 50;
    const height = Math.floor(Math.random() * 100) + 50;
    const x = Math.floor(Math.random() * (512 - width));
    const y = Math.floor(Math.random() * (512 - height));
    
    // Random confidence between 0.7 and 1.0
    const confidence = 0.7 + Math.random() * 0.3;
    
    segments.push({
      id: `food-${i + 1}`,
      boundingBox: { x, y, width, height },
      confidence,
    });
  }
  
  return segments;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
  },
  providerToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  formContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#eee',
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 10,
    backgroundColor: 'white',
  },
  modeSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  modeButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  modeButtonSelected: {
    backgroundColor: '#4285F4',
  },
  modeButtonText: {
    color: '#333',
  },
  modeButtonTextSelected: {
    color: 'white',
  },
  segmentsContainer: {
    marginTop: 16,
  },
  segmentItem: {
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  visualizeButton: {
    backgroundColor: '#4285F4',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 16,
  },
  visualizeButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  errorContainer: {
    padding: 16,
    backgroundColor: '#ffebee',
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#ffcdd2',
  },
  errorText: {
    color: '#c62828',
  },
  resultContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#eee',
  },
  sectionHeader: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  resultDetails: {
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  linkButton: {
    backgroundColor: '#4CAF50',
    padding: 10,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 12,
  },
  linkButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  imageContainer: {
    marginTop: 16,
  },
  previewImage: {
    width: '100%',
    height: 200,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    marginBottom: 8,
  },
  caption: {
    fontStyle: 'italic',
    color: '#666',
    textAlign: 'center',
  },
}); 