import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Button } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import SkeletonLoader from '../SkeletonLoader';
import CardSkeleton from '../CardSkeleton';
import ListSkeleton from '../ListSkeleton';
import ProfileSkeleton from '../ProfileSkeleton';
import ScreenSkeleton from '../ScreenSkeleton';

/**
 * An example component that demonstrates how to use the various
 * skeleton components while content is loading.
 */
export default function SkeletonExample() {
  const { colors } = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [showProfile, setShowProfile] = useState(false);
  const [showFullScreen, setShowFullScreen] = useState(false);
  
  // Simulate loading content
  useEffect(() => {
    if (!isLoading) return;
    
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 3000); // Show skeletons for 3 seconds
    
    return () => clearTimeout(timer);
  }, [isLoading]);
  
  // Reload the skeletons
  const handleReload = () => {
    setIsLoading(true);
  };
  
  // Toggle between demo types
  const toggleProfileDemo = () => {
    setShowProfile(!showProfile);
  };
  
  const toggleFullScreenDemo = () => {
    setShowFullScreen(!showFullScreen);
  };
  
  // If showing full screen demo
  if (showFullScreen) {
    return (
      <View style={styles.container}>
        <ScreenSkeleton
          isLoading={isLoading}
          hasHeader={true}
          sections={[
            { type: 'card', height: 180, itemCount: 1 },
            { type: 'list', height: 240, itemCount: 4 },
            { type: 'grid', height: 300, itemCount: 4 },
          ]}
          hasBottomAction={true}
        />
        
        {!isLoading && (
          <View style={[styles.controls, { backgroundColor: colors.card }]}>
            <Button title="Reload Skeletons" onPress={handleReload} />
            <Button title="Back to Examples" onPress={toggleFullScreenDemo} />
          </View>
        )}
      </View>
    );
  }
  
  // If showing profile demo
  if (showProfile) {
    return (
      <View style={styles.container}>
        <ProfileSkeleton isLoading={isLoading} />
        
        {!isLoading && (
          <View style={[styles.controls, { backgroundColor: colors.card }]}>
            <Button title="Reload Skeletons" onPress={handleReload} />
            <Button title="Back to Examples" onPress={toggleProfileDemo} />
          </View>
        )}
      </View>
    );
  }
  
  // Main demo with all skeleton types
  return (
    <ScrollView
      style={[styles.container, { backgroundColor: colors.background }]}
      contentContainerStyle={styles.contentContainer}
    >
      <Text style={[styles.title, { color: colors.text }]}>
        Skeleton Loading Examples
      </Text>
      
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Basic Skeleton Loaders
      </Text>
      
      <View style={styles.row}>
        <SkeletonLoader
          variant="rectangle"
          width={100}
          height={100}
          isLoading={isLoading}
        />
        
        <SkeletonLoader
          variant="circle"
          width={100}
          height={100}
          isLoading={isLoading}
        />
        
        <SkeletonLoader
          variant="pill"
          width={100}
          height={40}
          isLoading={isLoading}
        />
      </View>
      
      <View style={styles.textRow}>
        <SkeletonLoader
          variant="text"
          width="80%"
          height={20}
          isLoading={isLoading}
          style={styles.textSkeleton}
        />
        
        <SkeletonLoader
          variant="text"
          width="60%"
          height={16}
          isLoading={isLoading}
          style={styles.textSkeleton}
        />
        
        <SkeletonLoader
          variant="text"
          width="70%"
          height={16}
          isLoading={isLoading}
          style={styles.textSkeleton}
        />
      </View>
      
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        Card Skeleton
      </Text>
      
      <CardSkeleton
        isLoading={isLoading}
        hasImage={true}
        hasContent={true}
        hasActions={true}
        contentLines={3}
      />
      
      <Text style={[styles.sectionTitle, { color: colors.text }]}>
        List Skeleton
      </Text>
      
      <ListSkeleton
        isLoading={isLoading}
        itemCount={3}
        hasAvatar={true}
        hasTitle={true}
        hasSubtitle={true}
        hasAccessory={true}
      />
      
      <View style={styles.buttonRow}>
        <Button
          title="Show Profile Skeleton"
          onPress={toggleProfileDemo}
        />
        
        <Button
          title="Show Full Screen Skeleton"
          onPress={toggleFullScreenDemo}
        />
      </View>
      
      <Button
        title="Reload Skeletons"
        onPress={handleReload}
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 24,
    marginBottom: 16,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  textRow: {
    marginBottom: 24,
  },
  textSkeleton: {
    marginBottom: 8,
  },
  buttonRow: {
    marginTop: 24,
    marginBottom: 16,
  },
  controls: {
    padding: 16,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
}); 