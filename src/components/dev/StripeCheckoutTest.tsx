import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, ActivityIndicator, StyleSheet, ScrollView, Switch } from 'react-native';
import { useDatabaseType } from '@/contexts/DatabaseContext';
import { useStripeService, StripeCheckoutResponse } from '@/services/stripeService';
import * as Linking from 'expo-linking';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Component to test the Stripe checkout function with both Firebase and Firebase
 */
export default function StripeCheckoutTest() {
  const { useFirebase, toggleProvider } = useDatabaseType();
  const { user } = useAuth();
  const { createCheckoutSession } = useStripeService();
  
  // Checkout form state
  const [priceId, setPriceId] = useState('price_1Kw3jfGhTghPoWEZa8JlGgdP'); // Example price ID
  const [mode, setMode] = useState<'payment' | 'subscription'>('payment');
  
  // URLs
  const baseUrl = Linking.createURL('/checkout');
  const successUrl = `${baseUrl}?result=success`;
  const cancelUrl = `${baseUrl}?result=cancel`;
  
  // Loading and result state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<StripeCheckoutResponse | null>(null);
  
  const handleCheckout = async () => {
    if (!user) {
      setError("You must be logged in to use this function");
      return;
    }
    
    setLoading(true);
    setError(null);
    setResult(null);
    
    try {
      const checkoutResult = await createCheckoutSession({
        price_id: priceId,
        success_url: successUrl,
        cancel_url: cancelUrl,
        mode
      });
      
      setResult(checkoutResult);
      
      // Open the checkout URL
      if (checkoutResult.url) {
        Linking.openURL(checkoutResult.url);
      }
    } catch (err) {
      console.error('Error creating checkout session:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Stripe Checkout Test</Text>
        <View style={styles.providerToggle}>
          <Text>Using: 'Firebase'</Text>
          <Switch
            value={useFirebase}
            onValueChange={toggleProvider}
            thumbColor={useFirebase ? '#4285F4' : '#49E9A6'}
          />
        </View>
      </View>
      
      <View style={styles.formContainer}>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Price ID:</Text>
          <TextInput
            style={styles.input}
            value={priceId}
            onChangeText={setPriceId}
            placeholder="Enter Stripe Price ID"
          />
        </View>
        
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Mode:</Text>
          <View style={styles.modeSelector}>
            {(['payment', 'subscription'] as const).map((modeOption) => (
              <TouchableOpacity
                key={modeOption}
                style={[
                  styles.modeButton,
                  mode === modeOption && styles.modeButtonSelected
                ]}
                onPress={() => setMode(modeOption)}
              >
                <Text style={[
                  styles.modeButtonText,
                  mode === modeOption && styles.modeButtonTextSelected
                ]}>
                  {modeOption.charAt(0).toUpperCase() + modeOption.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        <View style={styles.urlsContainer}>
          <Text style={styles.label}>Success URL:</Text>
          <Text style={styles.urlText}>{successUrl}</Text>
          
          <Text style={styles.label}>Cancel URL:</Text>
          <Text style={styles.urlText}>{cancelUrl}</Text>
        </View>
      </View>
      
      <TouchableOpacity 
        style={styles.checkoutButton} 
        onPress={handleCheckout}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="white" />
        ) : (
          <Text style={styles.checkoutButtonText}>Proceed to Checkout</Text>
        )}
      </TouchableOpacity>
      
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
      
      {result && (
        <View style={styles.resultContainer}>
          <Text style={styles.sectionHeader}>Checkout Session Created:</Text>
          
          <View style={styles.resultDetails}>
            <Text>Session ID: {result.sessionId}</Text>
            <TouchableOpacity onPress={() => Linking.openURL(result.url)}>
              <Text style={styles.linkText}>Open Checkout Page</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
  },
  providerToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  formContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#eee',
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 10,
    backgroundColor: 'white',
  },
  modeSelector: {
    flexDirection: 'row',
    gap: 8,
  },
  modeButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  modeButtonSelected: {
    backgroundColor: '#4285F4',
  },
  modeButtonText: {
    color: '#333',
  },
  modeButtonTextSelected: {
    color: 'white',
  },
  urlsContainer: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  urlText: {
    fontFamily: 'monospace',
    fontSize: 12,
    marginBottom: 12,
    color: '#666',
  },
  checkoutButton: {
    backgroundColor: '#4285F4',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 16,
  },
  checkoutButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  errorContainer: {
    padding: 16,
    backgroundColor: '#ffebee',
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#ffcdd2',
  },
  errorText: {
    color: '#c62828',
  },
  resultContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#eee',
  },
  sectionHeader: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  resultDetails: {
    marginTop: 16,
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderRadius: 8,
  },
  linkText: {
    color: '#4285F4',
    textDecorationLine: 'underline',
    marginTop: 8,
  },
}); 