import React from 'react';
import { <PERSON>, But<PERSON>, Text, StyleSheet } from 'react-native';
import { SyncNotification } from '../SyncNotification';
import { useSyncNotification } from '@/hooks/useSyncNotification';
import { ToastProvider } from '../Toast';

/**
 * Example component demonstrating a real-world integration
 * of the sync notification functionality
 */
export function SyncIntegrationExample() {
  // Use our custom hook to manage sync state
  const {
    lastSynced,
    showNotification,
    onSyncStart,
    onSyncComplete,
    onNotificationDismiss,
    isSyncing,
  } = useSyncNotification();
  
  // Function to simulate data synchronization
  const handleSyncData = async () => {
    try {
      // Indicate that sync is starting
      onSyncStart();
      
      // Here you would perform your actual sync logic
      console.log('Syncing data with server...');
      
      // Simulate API call with a delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // When sync is successful, call the onSyncComplete function
      onSyncComplete();
    } catch (error) {
      console.error('Sync failed:', error);
      // Handle sync errors here
    }
  };
  
  return (
    <ToastProvider>
      <View style={styles.container}>
        <Button 
          title={isSyncing ? 'Syncing...' : 'Sync Data'} 
          onPress={handleSyncData}
          disabled={isSyncing}
          accessibilityLabel="Sync data with server"
        />
        
        {lastSynced && (
          <>
            <Text style={styles.lastSyncText}>
              Last synchronized: {lastSynced.toLocaleString()}
            </Text>
            
            <SyncNotification
              lastSyncedTime={lastSynced}
              show={showNotification}
              onDismiss={onNotificationDismiss}
              duration={2000} // 2 seconds as specified in requirements
            />
          </>
        )}
      </View>
    </ToastProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    gap: 12,
  },
  lastSyncText: {
    fontSize: 14,
    opacity: 0.7,
    marginTop: 8,
  },
}); 