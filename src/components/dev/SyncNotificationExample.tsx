import React, { useState } from 'react';
import { View, Button, StyleSheet } from 'react-native';
import { SyncNotification } from '../SyncNotification';
import { ToastProvider } from '../Toast';

/**
 * Example component demonstrating how to use the SyncNotification
 */
export function SyncNotificationExample() {
  // State to track when last sync occurred and whether to show notification
  const [lastSynced, setLastSynced] = useState<Date | null>(null);
  const [showNotification, setShowNotification] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  
  // Function to simulate data synchronization
  const handleSync = async () => {
    try {
      // Set syncing state
      setIsSyncing(true);
      
      // Here you would perform your actual sync logic
      console.log('Syncing data...');
      
      // Simulate a delay for the sync operation
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // After sync completes successfully:
      const syncTime = new Date();
      setLastSynced(syncTime);
      setIsSyncing(false);
      setShowNotification(true);
    } catch (error) {
      console.error('Sync failed:', error);
      setIsSyncing(false);
    }
  };
  
  // Clear the notification flag after it's dismissed
  const handleNotificationDismiss = () => {
    setShowNotification(false);
  };
  
  return (
    <ToastProvider>
      <View style={styles.container}>
        <Button 
          title={isSyncing ? "Syncing..." : "Sync Data"}
          onPress={handleSync}
          disabled={isSyncing}
          accessibilityLabel="Sync data with server"
        />
        
        {lastSynced && (
          <SyncNotification
            lastSyncedTime={lastSynced}
            show={showNotification}
            onDismiss={handleNotificationDismiss}
            duration={2000} // 2 seconds as specified
          />
        )}
      </View>
    </ToastProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
}); 