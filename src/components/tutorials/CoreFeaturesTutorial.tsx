import React from 'react';
import { createGeneralTutorial, TutorialStep } from '../InteractiveTutorial';

// Define steps for the core features tutorial
const coreFeaturesTutorialSteps: TutorialStep[] = [
  // Step 1: Welcome to the Health App
  {
    id: 'welcome',
    title: 'Welcome to Health App',
    content: 'This tutorial will guide you through the core features of the app. Let\'s get started!',
    position: 'center',
  },
  
  // Step 2: Dashboard Overview
  {
    id: 'dashboard',
    title: 'Your Health Dashboard',
    content: 'This is your personalized dashboard that shows your health metrics at a glance.',
    targetId: 'dashboard-container',
    position: 'bottom',
  },
  
  // Step 3: Stats Cards
  {
    id: 'stats-cards',
    title: 'Health Stats',
    content: 'These cards display your key health metrics including steps, calories, sleep, and water intake.',
    targetId: 'stats-grid',
    position: 'top',
  },
  
  // Step 4: Navigation
  {
    id: 'navigation',
    title: 'App Navigation',
    content: 'Use the tab bar to navigate between different sections of the app.',
    targetId: 'tab-bar',
    position: 'top',
  },
  
  // Step 5: Logging Data
  {
    id: 'logging',
    title: 'Recording Activities',
    content: 'Tap the "+" button to log meals, water intake, workouts, and other activities.',
    targetId: 'add-button',
    position: 'bottom',
  },
  
  // Step 6: Profile
  {
    id: 'profile',
    title: 'Your Profile',
    content: 'View and edit your profile, preferences, and connected devices here.',
    targetId: 'profile-tab',
    position: 'top',
  },
  
  // Step 7: One-handed Mode
  {
    id: 'one-handed-mode',
    title: 'One-handed Mode',
    content: 'Enable one-handed mode from settings for easier app usage with just your thumb.',
    targetId: 'accessibility-settings',
    position: 'bottom',
  },
  
  // Step 8: Data Insights
  {
    id: 'insights',
    title: 'Health Insights',
    content: 'Get personalized insights based on your health data and activity patterns.',
    targetId: 'insights-card',
    position: 'bottom',
  },
  
  // Step 9: Conclusion
  {
    id: 'conclusion',
    title: 'You\'re All Set!',
    content: 'You\'ve completed the core features tutorial. Explore the app to discover more!',
    position: 'center',
  },
];

// Create the tutorial definition
export const CoreFeaturesTutorial = createGeneralTutorial(
  'core-features',
  'Core Features',
  coreFeaturesTutorialSteps
);

export default CoreFeaturesTutorial; 