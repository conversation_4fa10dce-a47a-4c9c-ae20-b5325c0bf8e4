import React from 'react';
import { createNutritionTutorial, TutorialStep } from '../InteractiveTutorial';

// Define steps for the nutrition tracking tutorial
const nutritionTrackingTutorialSteps: TutorialStep[] = [
  // Step 1: Introduction to Nutrition Tracking
  {
    id: 'nutrition-intro',
    title: 'Nutrition Tracking',
    content: 'This tutorial will show you how to track your food intake and monitor your nutrition.',
    position: 'center',
  },
  
  // Step 2: Logging Meals
  {
    id: 'logging-meals',
    title: 'Logging Meals',
    content: 'Tap the "+" button to log a meal. You can scan food, search the database, or create a custom entry.',
    targetId: 'add-meal-button',
    position: 'bottom',
  },
  
  // Step 3: Food Scanner
  {
    id: 'food-scanner',
    title: 'Food Scanner',
    content: 'Use the camera to scan packaged food barcodes or take photos of your meals for automatic recognition.',
    targetId: 'scan-food-button',
    position: 'bottom',
  },
  
  // Step 4: Manual Entry
  {
    id: 'manual-entry',
    title: 'Manual Food Entry',
    content: 'Search our extensive food database or enter nutritional information manually.',
    targetId: 'manual-entry-button',
    position: 'bottom',
  },
  
  // Step 5: Saved Meals
  {
    id: 'saved-meals',
    title: 'Saved Meals & Recipes',
    content: 'Save frequently eaten meals and recipes for quick and easy logging in the future.',
    targetId: 'saved-meals-section',
    position: 'top',
  },
  
  // Step 6: Nutrition Dashboard
  {
    id: 'nutrition-dashboard',
    title: 'Nutrition Dashboard',
    content: 'View your daily and weekly nutritional intake, including macros and micronutrients.',
    targetId: 'nutrition-dashboard',
    position: 'top',
  },
  
  // Step 7: Setting Nutrition Goals
  {
    id: 'nutrition-goals',
    title: 'Customizing Nutrition Goals',
    content: 'Set personalized goals for calories, macros, and specific nutrients based on your needs.',
    targetId: 'nutrition-goals-button',
    position: 'bottom',
  },
  
  // Step 8: Understanding Macros
  {
    id: 'understanding-macros',
    title: 'Understanding Macros',
    content: 'Track your protein, carbs, and fats to ensure a balanced diet that supports your goals.',
    targetId: 'macro-chart',
    position: 'bottom',
  },
  
  // Step 9: Meal Planning
  {
    id: 'meal-planning',
    title: 'Meal Planning',
    content: 'Plan your meals in advance to stay on track with your nutrition goals.',
    targetId: 'meal-planning-tab',
    position: 'top',
  },
  
  // Step 10: Conclusion
  {
    id: 'nutrition-conclusion',
    title: 'Ready to Track!',
    content: 'You\'re now ready to start tracking your nutrition. Remember, consistency is key to achieving your health goals!',
    position: 'center',
  },
];

// Create the tutorial definition
export const NutritionTrackingTutorial = createNutritionTutorial(
  'nutrition-tracking',
  'Nutrition Tracking',
  nutritionTrackingTutorialSteps
);

export default NutritionTrackingTutorial; 