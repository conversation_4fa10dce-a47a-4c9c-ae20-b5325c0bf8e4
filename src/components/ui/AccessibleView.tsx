import React, { ReactNode } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, StyleProp, ViewStyle, TextStyle } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';

interface AccessibleViewProps {
  children: ReactNode;
  accessibilityLabel: string;
  accessibilityHint?: string;
  role?: 'none' | 'button' | 'link' | 'search' | 'image' | 'text' | 'header' | 'summary' | 'alert';
  style?: StyleProp<ViewStyle>;
  testID?: string;
  importantForAccessibility?: 'auto' | 'yes' | 'no' | 'no-hide-descendants';
  accessible?: boolean;
  onAccessibilityTap?: () => void;
}

interface AccessibleTextProps {
  children: ReactNode;
  accessibilityLabel?: string;
  accessibilityRole?: 'text' | 'header' | 'link' | 'summary';
  style?: StyleProp<TextStyle>;
  variant?: 'body' | 'header' | 'label' | 'caption';
  testID?: string;
  accessible?: boolean;
  adjustsFontSizeToFit?: boolean;
  numberOfLines?: number;
}

interface AccessibleButtonProps {
  onPress: () => void;
  accessibilityLabel: string;
  accessibilityHint?: string;
  disabled?: boolean;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  children: ReactNode;
  testID?: string;
  highContrast?: boolean;
}

export const AccessibleView: React.FC<AccessibleViewProps> = ({
  children,
  accessibilityLabel,
  accessibilityHint,
  role,
  style,
  testID,
  importantForAccessibility,
  accessible = true,
  onAccessibilityTap,
}) => {
  return (
    <View
      accessible={accessible}
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      accessibilityRole={role}
      style={style}
      testID={testID}
      importantForAccessibility={importantForAccessibility}
      onAccessibilityTap={onAccessibilityTap}
    >
      {children}
    </View>
  );
};

export const AccessibleText: React.FC<AccessibleTextProps> = ({
  children,
  accessibilityLabel,
  accessibilityRole = 'text',
  style,
  variant = 'body',
  testID,
  accessible = true,
  adjustsFontSizeToFit,
  numberOfLines,
}) => {
  const { colors } = useTheme();
  
  // Get appropriate text style based on variant
  const getVariantStyle = () => {
    switch (variant) {
      case 'header':
        return styles.headerText;
      case 'label':
        return styles.labelText;
      case 'caption':
        return styles.captionText;
      case 'body':
      default:
        return styles.bodyText;
    }
  };
  
  return (
    <Text
      accessible={accessible}
      accessibilityLabel={accessibilityLabel || (typeof children === 'string' ? children : undefined)}
      accessibilityRole={accessibilityRole}
      style={[getVariantStyle(), { color: colors.text }, style]}
      testID={testID}
      adjustsFontSizeToFit={adjustsFontSizeToFit}
      numberOfLines={numberOfLines}
    >
      {children}
    </Text>
  );
};

export const AccessibleButton: React.FC<AccessibleButtonProps> = ({
  onPress,
  accessibilityLabel,
  accessibilityHint,
  disabled = false,
  style,
  textStyle,
  children,
  testID,
  highContrast = false,
}) => {
  const { colors, isDark } = useTheme();
  
  // Use higher contrast colors for better accessibility
  const buttonColor = highContrast 
    ? isDark ? '#60A5FA' : '#1E40AF'  // Higher contrast blue
    : colors.primary;
  
  // Calculate text color for contrast
  const textColor = highContrast
    ? isDark ? '#FFFFFF' : '#FFFFFF'  // White text 
    : isDark ? '#FFFFFF' : colors.background;
    
  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled}
      style={[
        styles.button,
        { backgroundColor: buttonColor },
        disabled && { opacity: 0.5 },
        style,
      ]}
      accessible={true}
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      accessibilityRole="button"
      accessibilityState={{ disabled }}
      testID={testID}
    >
      {typeof children === 'string' ? (
        <Text style={[styles.buttonText, { color: textColor }, textStyle]}>
          {children}
        </Text>
      ) : (
        children
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  headerText: {
    fontSize: 24,
    fontWeight: '700',
    letterSpacing: -0.5,
    marginBottom: 8,
  },
  bodyText: {
    fontSize: 16,
    fontWeight: '400',
  },
  labelText: {
    fontSize: 14,
    fontWeight: '500',
  },
  captionText: {
    fontSize: 12,
    fontWeight: '400',
    opacity: 0.7,
  },
});

export default {
  AccessibleView,
  AccessibleText,
  AccessibleButton
}; 