import React from 'react';
import { 
  StyleSheet, 
  View, 
  ImageBackground, 
  StatusBar,
  useWindowDimensions,
  Image
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';

// Default adaptive background color to use instead of an image
const DEFAULT_BACKGROUND_COLOR = {
  light: '#f8f9fa',
  dark: '#121212'
};

// SVG Pattern to provide some texture
const pattern = `<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <path d="M11 18C11 11.3726 16.3726 6 23 6H78C84.6274 6 90 11.3726 90 18V83C90 89.6274 84.6274 95 78 95H23C16.3726 95 11 89.6274 11 83V18Z" stroke="rgba(0,0,0,0.1)" stroke-width="0.5"/>
  <path d="M30 30C30 27.2386 32.2386 25 35 25H65C67.7614 25 70 27.2386 70 30V70C70 72.7614 67.7614 75 65 75H35C32.2386 75 30 72.7614 30 70V30Z" stroke="rgba(0,0,0,0.1)" stroke-width="0.5"/>
  <circle cx="50" cy="50" r="30" stroke="rgba(0,0,0,0.1)" stroke-width="0.5"/>
</svg>`;

const patternDataUri = `data:image/svg+xml;utf8,${pattern}`;

interface AuthBackgroundProps {
  children: React.ReactNode;
}

export default function AuthBackground({ children }: AuthBackgroundProps) {
  const { colors, isDark } = useTheme();
  const { height, width } = useWindowDimensions();
  
  // Adaptive background color with texture
  const backgroundColor = isDark ? DEFAULT_BACKGROUND_COLOR.dark : DEFAULT_BACKGROUND_COLOR.light;

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent
      />
      
      <View style={styles.patternContainer}>
        {/* Top Right Circle */}
        <View style={[styles.decorationCircle, styles.topRightCircle, { 
          backgroundColor: isDark ? colors.primary + '30' : colors.primary + '15'
        }]} />
        
        {/* Bottom Left Circle */}
        <View style={[styles.decorationCircle, styles.bottomLeftCircle, { 
          backgroundColor: isDark ? colors.success + '30' : colors.success + '15' 
        }]} />
      </View>
      
      <View style={[
        styles.content, 
        { backgroundColor: 'transparent' }
      ]}>
        {children}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
    overflow: 'hidden',
  },
  patternContainer: {
    ...StyleSheet.absoluteFillObject,
    opacity: 0.8,
  },
  content: {
    flex: 1,
    width: '100%',
    zIndex: 2,
  },
  decorationCircle: {
    position: 'absolute',
    borderRadius: 500,
  },
  topRightCircle: {
    width: 300,
    height: 300,
    top: -120,
    right: -80,
  },
  bottomLeftCircle: {
    width: 400,
    height: 400,
    bottom: -180,
    left: -130,
  }
}); 