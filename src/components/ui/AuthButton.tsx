import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
  StyleProp,
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';

interface AuthButtonProps {
  onPress: () => void;
  title: string;
  loading?: boolean;
  disabled?: boolean;
  variant?: 'primary' | 'secondary' | 'outline' | 'danger';
  icon?: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  accessibilityLabel?: string;
}

export function AuthButton({
  onPress,
  title,
  loading = false,
  disabled = false,
  variant = 'primary',
  icon,
  style,
  textStyle,
  accessibilityLabel,
}: AuthButtonProps) {
  const { colors, isDark } = useTheme();
  
  const getButtonStyles = () => {
    const baseStyle: ViewStyle = {
      height: 56,
      borderRadius: 14,
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 4,
    };
    
    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          backgroundColor: colors.primary,
        };
      case 'secondary':
        return {
          ...baseStyle,
          backgroundColor: isDark ? colors.card : colors.subtle,
        };
      case 'outline':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: colors.border,
        };
      case 'danger':
        return {
          ...baseStyle,
          backgroundColor: colors.error,
        };
      default:
        return baseStyle;
    }
  };
  
  const getTextStyles = () => {
    const baseStyle: TextStyle = {
      fontSize: 17,
      fontWeight: '700',
      letterSpacing: 0.2,
    };
    
    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          color: '#ffffff',
        };
      case 'secondary':
        return {
          ...baseStyle,
          color: colors.text,
        };
      case 'outline':
        return {
          ...baseStyle,
          color: colors.primary,
        };
      case 'danger':
        return {
          ...baseStyle,
          color: '#ffffff',
        };
      default:
        return baseStyle;
    }
  };
  
  const iconSpacing: TextStyle | null = icon ? { marginLeft: 8 } : null;
  
  return (
    <TouchableOpacity
      style={[
        getButtonStyles(),
        (disabled || loading) && { opacity: 0.7 },
        style,
      ]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
      accessibilityLabel={accessibilityLabel || title}
    >
      {loading ? (
        <ActivityIndicator 
          color={variant === 'outline' ? colors.primary : '#ffffff'} 
          size="small" 
        />
      ) : (
        <>
          {icon && <React.Fragment>{icon}</React.Fragment>}
          <Text style={[getTextStyles(), iconSpacing, textStyle]}>
            {title}
          </Text>
        </>
      )}
    </TouchableOpacity>
  );
}

export default AuthButton; 