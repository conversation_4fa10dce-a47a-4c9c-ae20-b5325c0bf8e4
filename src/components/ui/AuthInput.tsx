import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  TextInputProps,
  StyleProp,
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';

interface AuthInputProps extends TextInputProps {
  label: string;
  icon?: React.ReactNode;
  error?: string;
  containerStyle?: StyleProp<ViewStyle>;
  inputWrapperStyle?: StyleProp<ViewStyle>;
  labelStyle?: StyleProp<TextStyle>;
  inputStyle?: StyleProp<TextStyle>;
  secureTextEntry?: boolean;
  rightElement?: React.ReactNode;
}

export function AuthInput({
  label,
  icon,
  error,
  containerStyle,
  inputWrapperStyle,
  labelStyle,
  inputStyle,
  secureTextEntry,
  rightElement,
  ...props
}: AuthInputProps) {
  const { colors, isDark } = useTheme();
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const iconPadding: TextStyle | null = icon ? { paddingLeft: 0 } : null;

  return (
    <View style={[styles.container, containerStyle]}>
      <Text style={[styles.label, { color: colors.text }, labelStyle]}>
        {label}
      </Text>
      
      <View
        style={[
          styles.inputWrapper,
          {
            backgroundColor: isDark ? colors.card : colors.subtle,
            borderColor: isFocused ? colors.primary + '80' : colors.border,
            borderWidth: isFocused ? 2 : 1,
          },
          error && { borderColor: colors.error + '70', borderWidth: 1 },
          inputWrapperStyle,
        ]}
      >
        {icon && <View style={styles.iconContainer}>{icon}</View>}
        
        <TextInput
          style={[
            styles.input,
            { color: colors.text },
            iconPadding,
            inputStyle,
          ]}
          placeholderTextColor={colors.textSecondary}
          secureTextEntry={secureTextEntry && !isPasswordVisible}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...props}
        />
        
        {secureTextEntry && (
          <TouchableOpacity
            style={styles.eyeIcon}
            onPress={togglePasswordVisibility}
            accessibilityLabel={isPasswordVisible ? "Hide password" : "Show password"}
          >
            {isPasswordVisible ? (
              <Feather name="eye-off" size={20} color={colors.textSecondary} />
            ) : (
              <Feather name="eye" size={20} color={colors.textSecondary} />
            )}
          </TouchableOpacity>
        )}
        
        {rightElement && !secureTextEntry && (
          <View style={styles.rightElement}>{rightElement}</View>
        )}
      </View>
      
      {error && (
        <Text style={[styles.errorText, { color: colors.error }]}>
          {error}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 14,
    height: 56,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
  },
  iconContainer: {
    paddingLeft: 16,
    paddingRight: 8,
  },
  input: {
    flex: 1,
    height: 56,
    fontSize: 16,
    fontWeight: '500',
    paddingHorizontal: 16,
  },
  eyeIcon: {
    padding: 16,
  },
  rightElement: {
    padding: 16,
  },
  errorText: {
    fontSize: 13,
    fontWeight: '500',
    marginTop: 6,
  },
});

export default AuthInput; 