import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  FlatList, 
  ScrollView,
  TextInput,
  ActivityIndicator,
  Switch,
  Modal
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { 
  useUserPreferences, 
  AVAILABLE_DIETARY_CATEGORIES, 
  AVAILABLE_HEALTH_FOCUSES,
  COMMON_ALLERGIES
} from '@/contexts/UserPreferencesContext';

interface DietaryPreference {
  id: string;
  name: string;
  description: string;
}

interface HealthGoal {
  id: string;
  name: string;
  description: string;
}

interface FoodRestriction {
  id: string;
  name: string;
  isAllergy: boolean;
}

// Available dietary preferences
const DIETARY_PREFERENCES: DietaryPreference[] = [
  {
    id: 'vegan',
    name: 'Vegan',
    description: 'No animal products or by-products'
  },
  {
    id: 'vegetarian',
    name: 'Vegetarian',
    description: 'No meat, may include dairy and eggs'
  },
  {
    id: 'pescatarian',
    name: 'Pescatarian',
    description: 'Vegetarian plus seafood'
  },
  {
    id: 'gluten-free',
    name: 'Gluten-Free',
    description: 'No wheat, barley, rye, or triticale'
  },
  {
    id: 'dairy-free',
    name: 'Dairy-Free',
    description: 'No milk products'
  },
  {
    id: 'keto',
    name: 'Keto',
    description: 'High-fat, low-carb diet'
  },
  {
    id: 'paleo',
    name: 'Paleo',
    description: 'Foods similar to what might have been eaten during the Paleolithic era'
  },
  {
    id: 'whole30',
    name: 'Whole30',
    description: 'Whole foods, no added sugar, grains, legumes, or dairy'
  },
  {
    id: 'mediterranean',
    name: 'Mediterranean',
    description: 'Plant-based foods, lean proteins, healthy fats'
  },
  {
    id: 'low-carb',
    name: 'Low-Carb',
    description: 'Reduced intake of carbohydrates'
  }
];

// Health goals
const HEALTH_GOALS: HealthGoal[] = [
  {
    id: 'weight-loss',
    name: 'Weight Loss',
    description: 'Reduce body fat and overall weight'
  },
  {
    id: 'muscle-gain',
    name: 'Muscle Gain',
    description: 'Increase muscle mass and strength'
  },
  {
    id: 'heart-health',
    name: 'Heart Health',
    description: 'Improve cardiovascular health'
  },
  {
    id: 'blood-sugar',
    name: 'Blood Sugar Control',
    description: 'Stabilize blood glucose levels'
  },
  {
    id: 'digestion',
    name: 'Digestive Health',
    description: 'Improve gut health and digestion'
  },
  {
    id: 'energy',
    name: 'Energy Boost',
    description: 'Increase overall energy levels'
  },
  {
    id: 'anti-inflammatory',
    name: 'Reduce Inflammation',
    description: 'Lower chronic inflammation'
  },
  {
    id: 'brain-health',
    name: 'Brain Health',
    description: 'Support cognitive function'
  }
];

// Common food restrictions/allergies
const COMMON_RESTRICTIONS: FoodRestriction[] = [
  { id: 'peanuts', name: 'Peanuts', isAllergy: true },
  { id: 'tree-nuts', name: 'Tree Nuts', isAllergy: true },
  { id: 'shellfish', name: 'Shellfish', isAllergy: true },
  { id: 'dairy', name: 'Dairy', isAllergy: true },
  { id: 'eggs', name: 'Eggs', isAllergy: true },
  { id: 'soy', name: 'Soy', isAllergy: true },
  { id: 'wheat', name: 'Wheat', isAllergy: true },
  { id: 'fish', name: 'Fish', isAllergy: true },
  { id: 'sesame', name: 'Sesame', isAllergy: true },
  { id: 'sulfites', name: 'Sulfites', isAllergy: true },
  { id: 'nightshades', name: 'Nightshades', isAllergy: false },
  { id: 'fodmaps', name: 'FODMAPs', isAllergy: false },
  { id: 'alcohol', name: 'Alcohol', isAllergy: false },
  { id: 'caffeine', name: 'Caffeine', isAllergy: false },
  { id: 'added-sugar', name: 'Added Sugar', isAllergy: false }
];

interface DietaryPreferencesSelectorProps {
  onComplete?: () => void;
}

export function DietaryPreferencesSelector({ onComplete }: DietaryPreferencesSelectorProps) {
  const { colors } = useTheme();
  const { 
    dietaryPreferences, 
    setDietaryPreferences,
    healthGoals,
    setHealthGoals,
    foodRestrictions,
    setFoodRestrictions,
    hasSavedPreferences
  } = useUserPreferences();
  
  // Local state for UI
  const [activeTab, setActiveTab] = useState<'diet' | 'health' | 'allergies'>('diet');
  const [customIngredient, setCustomIngredient] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPreferences, setSelectedPreferences] = useState<string[]>(dietaryPreferences || []);
  const [selectedGoals, setSelectedGoals] = useState<string[]>(healthGoals || []);
  const [selectedRestrictions, setSelectedRestrictions] = useState<string[]>(foodRestrictions || []);
  const [showAddRestrictionModal, setShowAddRestrictionModal] = useState(false);
  const [isCustomAllergy, setIsCustomAllergy] = useState(false);
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  
  // Toggle a dietary preference
  const toggleDietaryPreference = (preference: string) => {
    if (selectedPreferences.includes(preference)) {
      setSelectedPreferences(selectedPreferences.filter(p => p !== preference));
    } else {
      setSelectedPreferences([...selectedPreferences, preference]);
    }
  };
  
  // Toggle a health focus
  const toggleHealthFocus = (focus: string) => {
    if (selectedGoals.includes(focus)) {
      setSelectedGoals(selectedGoals.filter(f => f !== focus));
    } else {
      setSelectedGoals([...selectedGoals, focus]);
    }
  };
  
  // Toggle an allergy
  const toggleAllergy = (allergy: string) => {
    if (selectedRestrictions.includes(allergy)) {
      setSelectedRestrictions(selectedRestrictions.filter(a => a !== allergy));
    } else {
      setSelectedRestrictions([...selectedRestrictions, allergy]);
    }
  };
  
  // Add custom excluded ingredient
  const addExcludedIngredient = () => {
    if (customIngredient.trim() && !foodRestrictions.includes(customIngredient.trim())) {
      setFoodRestrictions([...foodRestrictions, customIngredient.trim()]);
      setCustomIngredient('');
    }
  };
  
  // Remove an excluded ingredient
  const removeExcludedIngredient = (ingredient: string) => {
    setFoodRestrictions(foodRestrictions.filter(i => i !== ingredient));
  };
  
  // Filter items based on search query
  const getFilteredItems = (items: string[]) => {
    if (!searchQuery) return items;
    return items.filter(item => 
      item.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };
  
  // Add custom food restriction
  const addCustomRestriction = () => {
    if (customIngredient.trim() === '') return;
    
    const customId = `custom-${customIngredient.toLowerCase().replace(/\s+/g, '-')}`;
    
    // Add to context (would need to be modified to handle custom restrictions)
    toggleAllergy(customId);
    
    // Reset and close modal
    setCustomIngredient('');
    setIsCustomAllergy(false);
    setShowAddRestrictionModal(false);
  };
  
  // Save preferences to context when done
  const savePreferences = async () => {
    setLoading(true);
    try {
      setDietaryPreferences(selectedPreferences);
      setHealthGoals(selectedGoals);
      setFoodRestrictions(selectedRestrictions);
      
      if (onComplete) {
        await onComplete();
      }
    } catch (error) {
      console.error('Failed to save preferences:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Render option cards for each step
  const renderOptionCards = (options: any[], selectedItems: string[], toggleFn: (id: string) => void) => {
    return options.map(option => (
      <TouchableOpacity
        key={option.id}
        style={[
          styles.optionCard,
          {
            backgroundColor: selectedItems.includes(option.id) ? colors.primaryLight : colors.card,
            borderColor: selectedItems.includes(option.id) ? colors.primary : colors.border
          }
        ]}
        onPress={() => toggleFn(option.id)}
      >
        <View style={styles.optionHeader}>
          <Text style={[
            styles.optionName,
            { color: selectedItems.includes(option.id) ? colors.primary : colors.text }
          ]}>
            {option.name}
          </Text>
          
          {selectedItems.includes(option.id) && (
            <View style={[styles.checkCircle, { backgroundColor: colors.primary }]}>
              <Feather name="check" size={12}  color={colors.text} />
            </View>
          )}
        </View>
        
        <Text style={[
          styles.optionDescription,
          { color: selectedItems.includes(option.id) ? colors.primary : colors.textSecondary }
        ]}>
          {option.description}
        </Text>
      </TouchableOpacity>
    ));
  };
  
  // Render step content
  const renderStepContent = () => {
    switch (step) {
      case 1:
        return (
          <>
            <View style={styles.stepHeader}>
              <Feather name="heart" size={24} color={colors.primary} style={styles.stepIcon} />
              <Text style={[styles.stepTitle, { color: colors.text }]}>
                Dietary Preferences
              </Text>
              <Text style={[styles.stepDescription, { color: colors.textSecondary }]}>
                Select any dietary preferences you follow
              </Text>
            </View>
            
            <View style={styles.optionsContainer}>
              {renderOptionCards(DIETARY_PREFERENCES, selectedPreferences, toggleDietaryPreference)}
            </View>
            
            <View style={styles.navigationButtons}>
              <TouchableOpacity
                style={[styles.skipButton, { borderColor: colors.border }]}
                onPress={() => setStep(step + 1)}
              >
                <Text style={[styles.skipButtonText, { color: colors.textSecondary }]}>
                  Skip
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.nextButton, { backgroundColor: colors.primary }]}
                onPress={() => setStep(step + 1)}
              >
                <Text style={styles.nextButtonText}>Next</Text>
                <Feather name="chevron-right" size={16}  color={colors.text} />
              </TouchableOpacity>
            </View>
          </>
        );
        
      case 2:
        return (
          <>
            <View style={styles.stepHeader}>
              <Feather name="alert-triangle" size={24} color={colors.primary} style={styles.stepIcon} />
              <Text style={[styles.stepTitle, { color: colors.text }]}>
                Health Goals
              </Text>
              <Text style={[styles.stepDescription, { color: colors.textSecondary }]}>
                What health outcomes are you looking for?
              </Text>
            </View>
            
            <View style={styles.optionsContainer}>
              {renderOptionCards(HEALTH_GOALS, selectedGoals, toggleHealthFocus)}
            </View>
            
            <View style={styles.navigationButtons}>
              <TouchableOpacity
                style={[styles.backButton, { borderColor: colors.border }]}
                onPress={() => setStep(step - 1)}
              >
                <Feather name="chevron-right" size={16} color={colors.textSecondary} style={styles.backIcon} />
                <Text style={[styles.backButtonText, { color: colors.textSecondary }]}>
                  Back
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.nextButton, { backgroundColor: colors.primary }]}
                onPress={() => setStep(step + 1)}
              >
                <Text style={styles.nextButtonText}>Next</Text>
                <Feather name="chevron-right" size={16}  color={colors.text} />
              </TouchableOpacity>
            </View>
          </>
        );
        
      case 3:
        return (
          <>
            <View style={styles.stepHeader}>
              <Feather name="alert-triangle" size={24} color={colors.primary} style={styles.stepIcon} />
              <Text style={[styles.stepTitle, { color: colors.text }]}>
                Food Restrictions
              </Text>
              <Text style={[styles.stepDescription, { color: colors.textSecondary }]}>
                Select any foods you want to avoid
              </Text>
            </View>
            
            <View style={styles.optionsContainer}>
              {renderOptionCards(COMMON_RESTRICTIONS, selectedRestrictions, toggleAllergy)}
            </View>
            
            <View style={styles.navigationButtons}>
              <TouchableOpacity
                style={[styles.backButton, { borderColor: colors.border }]}
                onPress={() => setStep(step - 1)}
              >
                <Feather name="chevron-right" size={16} color={colors.textSecondary} style={styles.backIcon} />
                <Text style={[styles.backButtonText, { color: colors.textSecondary }]}>
                  Back
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.nextButton, 
                  { backgroundColor: colors.primary, opacity: loading ? 0.7 : 1 }
                ]}
                onPress={savePreferences}
                disabled={loading}
              >
                <Text style={styles.nextButtonText}>
                  {loading ? 'Saving...' : 'Complete'}
                </Text>
                <Feather name="check" size={16}  color={colors.text} />
              </TouchableOpacity>
            </View>
          </>
        );
        
      default:
        return null;
    }
  };
  
  // Render close button
  const renderCloseButton = () => (
    <TouchableOpacity
      style={[styles.closeButton, { backgroundColor: colors.subtle || '#f0f0f0' }]}
      onPress={onComplete}
    >
      <Feather name="x" size={20} color={colors.text} />
    </TouchableOpacity>
  );
  
  // Render progress indicator
  const renderProgressIndicator = () => (
    <View style={styles.progressIndicator}>
      {[1, 2, 3].map(i => (
        <View
          key={i}
          style={[
            styles.progressDot,
            {
              backgroundColor: i <= step ? colors.primary : colors.border,
              width: i === step ? 24 : 8
            }
          ]}
        />
      ))}
    </View>
  );
  
  if (hasSavedPreferences) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Your preferences have been saved!
        </Text>
      </View>
    );
  }
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Saving your preferences...
        </Text>
      </View>
    );
  }
  
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        {renderCloseButton()}
        {renderProgressIndicator()}
      </View>
      
      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {renderStepContent()}
      </ScrollView>
      
      {/* Custom Restriction Modal */}
      <Modal
        visible={showAddRestrictionModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowAddRestrictionModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, { backgroundColor: colors.card }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Add Custom Restriction
              </Text>
              <TouchableOpacity 
                style={[styles.modalCloseButton, { backgroundColor: colors.subtle }]}
                onPress={() => setShowAddRestrictionModal(false)}
              >
                <Feather name="x" size={20} color={colors.text} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.modalContent}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>
                Food or Ingredient to Avoid
              </Text>
              <TextInput
                style={[styles.textInput, { 
                  backgroundColor: colors.subtle,
                  color: colors.text,
                  borderColor: colors.border
                }]}
                value={customIngredient}
                onChangeText={setCustomIngredient}
                placeholder="e.g., Avocado, Mushrooms, etc."
                placeholderTextColor={colors.textSecondary}
              />
              
              <View style={styles.allergyToggle}>
                <Text style={[styles.allergyToggleLabel, { color: colors.text }]}>
                  This is an allergy (not just a preference)
                </Text>
                <Switch
                  value={isCustomAllergy}
                  onValueChange={setIsCustomAllergy}
                  trackColor={{ false: colors.subtle, true: colors.primaryLight }}
                  thumbColor={isCustomAllergy ? colors.primary : colors.border}
                />
              </View>
              
              {isCustomAllergy && (
                <Text style={[styles.allergyNote, { color: colors.error }]}>
                  Allergies will be strictly avoided in all recommendations
                </Text>
              )}
            </View>
            
            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[styles.cancelButton, { borderColor: colors.border }]}
                onPress={() => setShowAddRestrictionModal(false)}
              >
                <Text style={[styles.cancelButtonText, { color: colors.text }]}>
                  Cancel
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.addButton, 
                  { backgroundColor: customIngredient.trim() ? colors.primary : colors.subtle }
                ]}
                onPress={addCustomRestriction}
                disabled={!customIngredient.trim()}
              >
                <Text style={[
                  styles.addButtonText, 
                  { color: customIngredient.trim() ? 'white' : colors.textSecondary }
                ]}>
                  Add
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressDot: {
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  stepHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  stepIcon: {
    marginBottom: 12,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
  },
  stepDescription: {
    fontSize: 16,
    textAlign: 'center',
    maxWidth: '80%',
  },
  optionsContainer: {
    marginBottom: 24,
  },
  optionCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
  },
  optionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  optionName: {
    fontSize: 16,
    fontWeight: '600',
  },
  checkCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionDescription: {
    fontSize: 14,
  },
  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  skipButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  skipButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  nextButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  backIcon: {
    transform: [{ rotate: '180deg' }],
    marginRight: 8,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '85%',
    borderRadius: 12,
    maxWidth: 400,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalCloseButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    padding: 16,
  },
  inputLabel: {
    fontSize: 16,
    marginBottom: 8,
  },
  textInput: {
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    fontSize: 16,
    marginBottom: 16,
  },
  allergyToggle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  allergyToggleLabel: {
    fontSize: 14,
    flex: 1,
  },
  allergyNote: {
    fontSize: 12,
    fontStyle: 'italic',
    marginBottom: 16,
  },
  modalFooter: {
    flexDirection: 'row',
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: '#ddd',
  },
  cancelButton: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
    borderRightWidth: StyleSheet.hairlineWidth,
    borderRightColor: '#ddd',
  },
  cancelButtonText: {
    fontSize: 16,
  },
  addButton: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
  },
  addButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
}); 