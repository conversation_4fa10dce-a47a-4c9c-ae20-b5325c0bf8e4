import React, { useEffect, useRef } from 'react';
import { StyleSheet, TouchableOpacity, Animated, ViewStyle } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Feather } from '@expo/vector-icons';
import { useFooterVisibility } from '@/contexts/FooterVisibilityContext';
import { useTheme } from '@/contexts/ThemeContext';

interface FooterToggleProps {
  style?: ViewStyle;
}

export function FooterToggle({ style }: FooterToggleProps) {
  const { isFooterVisible, toggleFooterVisibility } = useFooterVisibility();
  const { isDark, colors } = useTheme();
  const insets = useSafeAreaInsets();
  const positionAnim = useRef(new Animated.Value(isFooterVisible ? 1 : 0)).current;
  
  useEffect(() => {
    Animated.spring(positionAnim, {
      toValue: isFooterVisible ? 1 : 0,
      useNativeDriver: true,
      friction: 8, // Adjust for desired "bounciness"
      tension: 40, // Adjust for speed
    }).start();
  }, [isFooterVisible, positionAnim]);
  
  // Calculate bottom position based on footer visibility
  const bottomPosition = positionAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [20, 70 + Math.max(insets.bottom, 8)],
  });
  
  return (
    <Animated.View
      style={[
        styles.container,
        style,
        {
          transform: [{ translateY: 0 }],
          bottom: bottomPosition,
        },
      ]}
    >
      <TouchableOpacity
        style={[
          styles.button,
          {
            backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
          }
        ]}
        onPress={toggleFooterVisibility}
        accessibilityRole="button"
        accessibilityLabel={isFooterVisible ? "Hide footer" : "Show footer"}
      >
        {isFooterVisible ? (
          <Feather name="chevron-down" size={20} color={isDark ? 'white' : 'black'} />
        ) : (
          <Feather name="chevron-up" size={20} color={isDark ? 'white' : 'black'} />
        )}
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    right: 20,
    zIndex: 999,
  },
  button: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});

export default FooterToggle; 