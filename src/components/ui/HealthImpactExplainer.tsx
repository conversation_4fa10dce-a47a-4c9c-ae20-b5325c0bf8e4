import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';

interface HealthImpactExplainerProps {
  nutrientFocus: 'calories' | 'saturatedFat' | 'sugar' | 'fiber' | 'protein';
  frequencyOfSwap: 'occasional' | 'regular' | 'consistent';
}

export function HealthImpactExplainer({ 
  nutrientFocus, 
  frequencyOfSwap 
}: HealthImpactExplainerProps) {
  const { colors } = useTheme();
  
  // Get impact data based on nutrient focus and swap frequency
  const getImpactData = () => {
    // Base health metrics that will be impacted
    const baseMetrics = {
      weight: 'low',
      heartHealth: 'low',
      energy: 'low',
      cognition: 'low',
    };
    
    // Impact levels based on frequency
    const frequencyMultiplier = {
      occasional: 1,
      regular: 2,
      consistent: 3,
    };
    
    // Set impacts based on nutrient focus
    switch (nutrientFocus) {
      case 'calories':
        baseMetrics.weight = 'high';
        baseMetrics.energy = 'medium';
        break;
      case 'saturatedFat':
        baseMetrics.heartHealth = 'high';
        baseMetrics.weight = 'medium';
        break;
      case 'sugar':
        baseMetrics.energy = 'high';
        baseMetrics.cognition = 'medium';
        break;
      case 'fiber':
        baseMetrics.heartHealth = 'medium';
        baseMetrics.weight = 'medium';
        break;
      case 'protein':
        baseMetrics.energy = 'medium';
        baseMetrics.cognition = 'high';
        break;
    }
    
    // Apply frequency multiplier
    const multiplier = frequencyMultiplier[frequencyOfSwap];
    
    // Text explanations for each focus area
    const impactText = {
      calories: "Consistent reduction in caloric intake can lead to gradual weight loss and improved metabolic health. Choosing lower-calorie alternatives helps create a sustainable calorie deficit without feeling deprived.",
      saturatedFat: "Reducing saturated fat intake can positively impact cholesterol levels and cardiovascular health over time. Healthier fat alternatives provide essential nutrients while supporting heart health.",
      sugar: "Lowering added sugar consumption helps stabilize blood glucose levels, reducing energy crashes and supporting long-term metabolic health. This can also contribute to improved dental health and reduced inflammation.",
      fiber: "Increasing fiber intake supports digestive health, helps maintain healthy cholesterol levels, and contributes to feeling fuller longer, which can aid in weight management.",
      protein: "Choosing higher quality protein sources provides essential amino acids while often reducing saturated fat intake, supporting muscle maintenance and overall metabolic health."
    };
    
    return {
      impactText: impactText[nutrientFocus],
      weightImpact: getImpactLevel(baseMetrics.weight, multiplier),
      heartImpact: getImpactLevel(baseMetrics.heartHealth, multiplier),
      energyImpact: getImpactLevel(baseMetrics.energy, multiplier),
      cognitionImpact: getImpactLevel(baseMetrics.cognition, multiplier),
    };
  };
  
  // Helper to calculate impact level
  const getImpactLevel = (baseLevel: string, multiplier: number): number => {
    const baseLevelMap = { low: 1, medium: 2, high: 3 };
    const baseValue = baseLevelMap[baseLevel as keyof typeof baseLevelMap];
    const calculatedValue = baseValue * multiplier;
    // Return a value between 1-5
    return Math.min(Math.max(Math.round(calculatedValue * 5/9), 1), 5);
  };
  
  // Get impact data
  const impactData = getImpactData();
  
  // Render impact bars
  const renderImpactBar = (label: string, level: number, icon: React.ReactNode) => {
    const bars: React.ReactElement[] = [];
    
    for (let i = 1; i <= 5; i++) {
      bars.push(
        <View 
          key={i} 
          style={[
            styles.impactBar, 
            { 
              backgroundColor: i <= level ? colors.primary : colors.border,
              opacity: i <= level ? (0.4 + (i * 0.12)) : 0.3
            }
          ]} 
        />
      );
    }
    
    return (
      <View style={styles.impactItem}>
        <View style={styles.impactLabelContainer}>
          {icon}
          <Text style={[styles.impactLabel, { color: colors.text }]}>{label}</Text>
        </View>
        <View style={styles.barsContainer}>
          {bars}
        </View>
      </View>
    );
  };
  
  return (
    <View style={[styles.container, { backgroundColor: colors.card, borderColor: colors.border }]}>
      <View style={styles.header}>
        <Feather name="trending-up" size={20} color={colors.primary} style={styles.headerIcon} />
        <Text style={[styles.headerText, { color: colors.text }]}>
          Long-Term Health Impact
        </Text>
      </View>
      
      <Text style={[styles.description, { color: colors.textSecondary }]}>
        {impactData.impactText}
      </Text>
      
      <View style={styles.impactsContainer}>
        {renderImpactBar('Weight Management', impactData.weightImpact, 
          <Feather name="trending-up" size={16} color={colors.primary} style={styles.impactIcon} />
        )}
        
        {renderImpactBar('Heart Health', impactData.heartImpact,
          <Feather name="heart" size={16} color={colors.error} style={styles.impactIcon} />
        )}
        
        {renderImpactBar('Energy Levels', impactData.energyImpact,
          <Feather name="clock" size={16} color={colors.warning} style={styles.impactIcon} />
        )}
        
        {renderImpactBar('Cognitive Function', impactData.cognitionImpact,
          <Brain size={16} color={colors.primary} style={styles.impactIcon} />
        )}
      </View>
      
      <Text style={[styles.frequencyNote, { color: colors.textSecondary }]}>
        * Based on {frequencyOfSwap} substitution of healthier alternatives
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    borderWidth: 1,
    marginVertical: 16,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  headerIcon: {
    marginRight: 8,
  },
  headerText: {
    fontSize: 16,
    fontWeight: '600',
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    padding: 16,
    paddingTop: 12,
  },
  impactsContainer: {
    padding: 16,
    paddingTop: 8,
  },
  impactItem: {
    marginBottom: 12,
  },
  impactLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  impactIcon: {
    marginRight: 6,
  },
  impactLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  barsContainer: {
    flexDirection: 'row',
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  impactBar: {
    flex: 1,
    marginRight: 2,
    borderRadius: 2,
  },
  frequencyNote: {
    fontSize: 12,
    fontStyle: 'italic',
    padding: 16,
    paddingTop: 0,
  },
}); 