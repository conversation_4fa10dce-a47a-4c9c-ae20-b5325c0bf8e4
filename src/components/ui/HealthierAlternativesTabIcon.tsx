import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';


interface HealthierAlternativesTabIconProps {
  focused: boolean;
  hasNewAlternatives?: boolean;
  count?: number;
}

export function HealthierAlternativesTabIcon({ 
  focused, 
  hasNewAlternatives = false,
  count = 0
}: HealthierAlternativesTabIconProps) {
  const { colors } = useTheme();
  
  return (
    <View style={styles.container}>
      <Salad 
        size={24} 
        color={focused ? colors.primary : colors.textSecondary} 
      />
      
      {hasNewAlternatives && (
        <View style={[styles.badge, { backgroundColor: colors.notification }]}>
          {count > 0 ? (
            <Text style={styles.badgeText}>{count > 99 ? '99+' : count}</Text>
          ) : (
            <View style={styles.badgeDot} />
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: -2,
    right: -6,
    minWidth: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '700',
  },
  badgeDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: 'white',
  },
}); 