import React from 'react';
import { View, Text, ActivityIndicator, StyleSheet, Pressable } from 'react-native';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';

interface ImageUploadProgressProps {
  isUploading: boolean;
  hasError: boolean;
  errorMessage?: string;
  isSuccess?: boolean;
  onRetry?: () => void;
  onCancel?: () => void;
}

export function ImageUploadProgress({
  isUploading,
  hasError,
  errorMessage = 'Upload failed',
  isSuccess = false,
  onRetry,
  onCancel
}: ImageUploadProgressProps) {
  const { colors, isDark } = useTheme();

  if (!isUploading && !hasError && !isSuccess) {
    return null;
  }

  // Success state
  if (isSuccess) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? 'rgba(22, 101, 52, 0.8)' : 'rgba(220, 252, 231, 0.9)' }]}>
        <MaterialIcons name="check-circle" size={24} color={isDark ? '#86efac' : '#16a34a'} />
        <Text style={[styles.text, { color: isDark ? '#86efac' : '#16a34a' }]}>Upload successful</Text>
      </View>
    );
  }

  // Error state
  if (hasError) {
    return (
      <View style={[styles.container, { backgroundColor: isDark ? 'rgba(127, 29, 29, 0.8)' : 'rgba(254, 226, 226, 0.9)' }]}>
        <Feather name="alert-circle" size={24} color={isDark ? '#fca5a5' : '#dc2626'} />
        <Text style={[styles.text, { color: isDark ? '#fca5a5' : '#dc2626' }]}>{errorMessage}</Text>
        <View style={styles.buttonContainer}>
          {onRetry && (
            <Pressable
              style={[styles.button, { backgroundColor: isDark ? 'rgba(220, 38, 38, 0.7)' : 'rgba(220, 38, 38, 0.1)' }]}
              onPress={onRetry}
            >
              <Text style={{ color: isDark ? 'white' : '#dc2626' }}>Retry</Text>
            </Pressable>
          )}
          {onCancel && (
            <Pressable
              style={[styles.button, { backgroundColor: isDark ? 'rgba(0, 0, 0, 0.4)' : 'rgba(0, 0, 0, 0.05)' }]}
              onPress={onCancel}
            >
              <Text style={{ color: isDark ? '#e5e7eb' : '#4b5563' }}>Cancel</Text>
            </Pressable>
          )}
        </View>
      </View>
    );
  }

  // Loading state
  return (
    <View style={[styles.container, { backgroundColor: isDark ? 'rgba(30, 58, 138, 0.8)' : 'rgba(219, 234, 254, 0.9)' }]}>
      <ActivityIndicator size="small" color={isDark ? '#93c5fd' : '#3b82f6'} />
      <Text style={[styles.text, { color: isDark ? '#93c5fd' : '#3b82f6' }]}>Uploading image...</Text>
      {onCancel && (
        <Pressable
          style={[styles.button, { backgroundColor: isDark ? 'rgba(0, 0, 0, 0.4)' : 'rgba(0, 0, 0, 0.05)' }]}
          onPress={onCancel}
        >
          <Feather name="x-circle" size={18} color={isDark ? '#e5e7eb' : '#4b5563'} />
        </Pressable>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    padding: 12,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 100,
  },
  text: {
    marginLeft: 10,
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
  },
  button: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    marginLeft: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
}); 