import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather, MaterialIcons } from '@expo/vector-icons';
import { WhyThisSwapExplainer } from './WhyThisSwapExplainer';

interface NutritionInfo {
  calories: number;
  protein?: number;
  fat?: number;
  saturatedFat?: number;
  sodium?: number;
  sugar?: number;
  fiber?: number;
  glycemicIndex?: number;
}

export interface Ingredient {
  id: string;
  name: string;
  nutrition: NutritionInfo;
  servingSize: string;
  imageUrl?: string;
  category: string;
}

interface IngredientSwapBenefit {
  title: string;
  description: string;
  icon: 'heart' | 'leaf' | 'info' | 'clock';
}

interface IngredientSubstitutionCardProps {
  originalIngredient: Ingredient;
  substituteIngredient: Ingredient;
  benefits: IngredientSwapBenefit[];
  longTermBenefits?: string;
  preparationNotes?: string;
  onSelect?: () => void;
}

export function IngredientSubstitutionCard({
  originalIngredient,
  substituteIngredient,
  benefits,
  longTermBenefits,
  preparationNotes,
  onSelect
}: IngredientSubstitutionCardProps) {
  const { colors } = useTheme();
  const [showExplainer, setShowExplainer] = useState(false);
  
  // Calculate percentage differences between nutrition values
  const calculateDifference = (substitute: number, original: number) => {
    if (!original || !substitute) return null;
    return Math.round(((substitute - original) / original) * 100);
  };
  
  // Determine if a nutritional difference is positive (healthier)
  const isPositiveDifference = (key: keyof NutritionInfo, diff: number | null) => {
    if (diff === null) return false;
    
    // For these nutrition aspects, less is better (negative diff is positive)
    if (['calories', 'fat', 'saturatedFat', 'sodium', 'sugar', 'glycemicIndex'].includes(key)) {
      return diff < 0;
    }
    
    // For these, more is better (positive diff is positive)
    return diff > 0;
  };
  
  // Placeholder image if none is provided
  const getPlaceholderImage = (category: string) => {
    const categories: Record<string, string> = {
      'fruit': 'https://via.placeholder.com/60x60/FFB6C1/FFFFFF?text=Fruit',
      'vegetable': 'https://via.placeholder.com/60x60/90EE90/FFFFFF?text=Veggie',
      'grain': 'https://via.placeholder.com/60x60/F5DEB3/FFFFFF?text=Grain',
      'protein': 'https://via.placeholder.com/60x60/ADD8E6/FFFFFF?text=Protein',
      'dairy': 'https://via.placeholder.com/60x60/FAFAD2/FFFFFF?text=Dairy',
      'oil': 'https://via.placeholder.com/60x60/FFD700/FFFFFF?text=Oil',
      'condiment': 'https://via.placeholder.com/60x60/DDA0DD/FFFFFF?text=Cond',
      'sweetener': 'https://via.placeholder.com/60x60/FFC0CB/FFFFFF?text=Sweet',
    };
    
    return categories[category.toLowerCase()] || 'https://via.placeholder.com/60x60/CCCCCC/FFFFFF?text=Food';
  };
  
  return (
    <>
      <TouchableOpacity 
        style={[styles.card, { backgroundColor: colors.card, borderColor: colors.border }]}
        onPress={onSelect}
        activeOpacity={0.7}
      >
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            Healthier Alternative
          </Text>
          <TouchableOpacity 
            style={[styles.infoButton, { backgroundColor: colors.primaryLight }]} 
            onPress={() => setShowExplainer(true)}
          >
            <Feather name="info" size={16} color={colors.primary} />
          </TouchableOpacity>
        </View>
        
        <View style={styles.ingredientsRow}>
          {/* Original Ingredient */}
          <View style={styles.ingredientColumn}>
            <Image 
              source={{ uri: originalIngredient.imageUrl || getPlaceholderImage(originalIngredient.category) }}
              style={styles.ingredientImage}
            />
            <Text style={[styles.ingredientName, { color: colors.text }]} numberOfLines={2}>
              {originalIngredient.name}
            </Text>
            <Text style={[styles.servingSize, { color: colors.textSecondary }]}>
              {originalIngredient.servingSize}
            </Text>
          </View>
          
          {/* Arrow */}
          <View style={styles.arrowContainer}>
            <View style={[styles.arrowCircle, { backgroundColor: colors.primaryLight }]}>
              <Feather name="arrow-right" size={18} color={colors.primary} />
            </View>
          </View>
          
          {/* Substitute Ingredient */}
          <View style={styles.ingredientColumn}>
            <View style={styles.substituteImageContainer}>
              <Image 
                source={{ uri: substituteIngredient.imageUrl || getPlaceholderImage(substituteIngredient.category) }}
                style={styles.ingredientImage}
              />
              <View style={[styles.sparklesBadge, { backgroundColor: colors.success }]}>
                <MaterialIcons name="auto-awesome" size={12} color="white"  />
              </View>
            </View>
            <Text style={[styles.ingredientName, styles.substituteIngredientName, { color: colors.success }]} numberOfLines={2}>
              {substituteIngredient.name}
            </Text>
            <Text style={[styles.servingSize, { color: colors.textSecondary }]}>
              {substituteIngredient.servingSize}
            </Text>
          </View>
        </View>
        
        <View style={[styles.nutritionComparisonContainer, { borderTopColor: colors.border }]}>
          <Text style={[styles.comparisonTitle, { color: colors.textSecondary }]}>
            Nutrition Comparison
          </Text>
          
          <View style={styles.nutritionGrid}>
            {/* Calories Comparison */}
            <View style={styles.nutritionRow}>
              <Text style={[styles.nutritionLabel, { color: colors.text }]}>
                Calories
              </Text>
              <View style={styles.nutritionValues}>
                <Text style={[styles.nutritionValue, { color: colors.textSecondary }]}>
                  {originalIngredient.nutrition.calories}
                </Text>
                <Text style={[styles.nutritionValue, { color: colors.text }]}>
                  {substituteIngredient.nutrition.calories}
                </Text>
                
                {calculateDifference(
                  substituteIngredient.nutrition.calories, 
                  originalIngredient.nutrition.calories
                ) !== null && (
                  <View style={styles.differenceContainer}>
                    <Text 
                      style={[
                        styles.differenceValue, 
                        { 
                          color: isPositiveDifference(
                            'calories', 
                            calculateDifference(
                              substituteIngredient.nutrition.calories, 
                              originalIngredient.nutrition.calories
                            )
                          ) ? colors.success : colors.error 
                        }
                      ]}
                    >
                      {calculateDifference(
                        substituteIngredient.nutrition.calories, 
                        originalIngredient.nutrition.calories
                      )}%
                    </Text>
                  </View>
                )}
              </View>
            </View>
            
            {/* Fat Comparison */}
            {originalIngredient.nutrition.fat !== undefined && substituteIngredient.nutrition.fat !== undefined && (
              <View style={styles.nutritionRow}>
                <Text style={[styles.nutritionLabel, { color: colors.text }]}>
                  Fat
                </Text>
                <View style={styles.nutritionValues}>
                  <Text style={[styles.nutritionValue, { color: colors.textSecondary }]}>
                    {originalIngredient.nutrition.fat}g
                  </Text>
                  <Text style={[styles.nutritionValue, { color: colors.text }]}>
                    {substituteIngredient.nutrition.fat}g
                  </Text>
                  
                  {calculateDifference(
                    substituteIngredient.nutrition.fat, 
                    originalIngredient.nutrition.fat
                  ) !== null && (
                    <View style={styles.differenceContainer}>
                      <Text 
                        style={[
                          styles.differenceValue, 
                          { 
                            color: isPositiveDifference(
                              'fat', 
                              calculateDifference(
                                substituteIngredient.nutrition.fat, 
                                originalIngredient.nutrition.fat
                              )
                            ) ? colors.success : colors.error 
                          }
                        ]}
                      >
                        {calculateDifference(
                          substituteIngredient.nutrition.fat, 
                          originalIngredient.nutrition.fat
                        )}%
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            )}
            
            {/* Fiber Comparison */}
            {originalIngredient.nutrition.fiber !== undefined && substituteIngredient.nutrition.fiber !== undefined && (
              <View style={styles.nutritionRow}>
                <Text style={[styles.nutritionLabel, { color: colors.text }]}>
                  Fiber
                </Text>
                <View style={styles.nutritionValues}>
                  <Text style={[styles.nutritionValue, { color: colors.textSecondary }]}>
                    {originalIngredient.nutrition.fiber}g
                  </Text>
                  <Text style={[styles.nutritionValue, { color: colors.text }]}>
                    {substituteIngredient.nutrition.fiber}g
                  </Text>
                  
                  {calculateDifference(
                    substituteIngredient.nutrition.fiber, 
                    originalIngredient.nutrition.fiber
                  ) !== null && (
                    <View style={styles.differenceContainer}>
                      <Text 
                        style={[
                          styles.differenceValue, 
                          { 
                            color: isPositiveDifference(
                              'fiber', 
                              calculateDifference(
                                substituteIngredient.nutrition.fiber, 
                                originalIngredient.nutrition.fiber
                              )
                            ) ? colors.success : colors.error 
                          }
                        ]}
                      >
                        {calculateDifference(
                          substituteIngredient.nutrition.fiber, 
                          originalIngredient.nutrition.fiber
                        )}%
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            )}
            
            {/* Sugar Comparison */}
            {originalIngredient.nutrition.sugar !== undefined && substituteIngredient.nutrition.sugar !== undefined && (
              <View style={styles.nutritionRow}>
                <Text style={[styles.nutritionLabel, { color: colors.text }]}>
                  Sugar
                </Text>
                <View style={styles.nutritionValues}>
                  <Text style={[styles.nutritionValue, { color: colors.textSecondary }]}>
                    {originalIngredient.nutrition.sugar}g
                  </Text>
                  <Text style={[styles.nutritionValue, { color: colors.text }]}>
                    {substituteIngredient.nutrition.sugar}g
                  </Text>
                  
                  {calculateDifference(
                    substituteIngredient.nutrition.sugar, 
                    originalIngredient.nutrition.sugar
                  ) !== null && (
                    <View style={styles.differenceContainer}>
                      <Text 
                        style={[
                          styles.differenceValue, 
                          { 
                            color: isPositiveDifference(
                              'sugar', 
                              calculateDifference(
                                substituteIngredient.nutrition.sugar, 
                                originalIngredient.nutrition.sugar
                              )
                            ) ? colors.success : colors.error 
                          }
                        ]}
                      >
                        {calculateDifference(
                          substituteIngredient.nutrition.sugar, 
                          originalIngredient.nutrition.sugar
                        )}%
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
      
      {showExplainer && (
        <WhyThisSwapExplainer
          originalIngredient={originalIngredient.name}
          substituteIngredient={substituteIngredient.name}
          benefits={benefits.map(benefit => ({
            title: benefit.title,
            shortDescription: benefit.description,
            longDescription: benefit.description,
            icon: benefit.icon
          }))}
        />
      )}
    </>
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
  infoButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  ingredientsRow: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  ingredientColumn: {
    flex: 1,
    alignItems: 'center',
  },
  ingredientImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginBottom: 8,
  },
  substituteImageContainer: {
    position: 'relative',
  },
  sparklesBadge: {
    position: 'absolute',
    bottom: 6,
    right: -6,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  ingredientName: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    marginBottom: 4,
    paddingHorizontal: 4,
  },
  substituteIngredientName: {
    fontWeight: '600',
  },
  servingSize: {
    fontSize: 12,
    textAlign: 'center',
  },
  arrowContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  arrowCircle: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  nutritionComparisonContainer: {
    borderTopWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  comparisonTitle: {
    fontSize: 14,
    marginBottom: 12,
  },
  nutritionGrid: {
    marginBottom: 8,
  },
  nutritionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 6,
  },
  nutritionLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  nutritionValues: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  nutritionValue: {
    fontSize: 14,
    width: 50,
    textAlign: 'right',
    marginLeft: 8,
  },
  differenceContainer: {
    marginLeft: 8,
    width: 50,
  },
  differenceValue: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'right',
  },
}); 