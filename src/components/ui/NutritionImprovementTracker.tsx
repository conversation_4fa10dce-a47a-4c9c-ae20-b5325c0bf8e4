import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';
import { SavedRecipe } from '@/services/recipeCollectionService';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage key for tracking nutrition improvements
const NUTRITION_HISTORY_KEY = 'nutrition_improvement_history';

interface NutritionHistory {
  date: string; // ISO string
  improvements: {
    calories: number;
    fat: number;
    saturatedFat: number;
    sodium: number;
    sugar: number;
    fiber: number;
    protein: number;
  }
}

interface NutritionImprovementTrackerProps {
  latestRecipe?: SavedRecipe;
  initialExpanded?: boolean;
}

export function NutritionImprovementTracker({ 
  latestRecipe, 
  initialExpanded = false 
}: NutritionImprovementTrackerProps) {
  const { colors } = useTheme();
  const [histories, setHistories] = useState<NutritionHistory[]>([]);
  const [period, setPeriod] = useState<'week' | 'month' | 'year'>('week');
  const [isLoading, setIsLoading] = useState(true);
  const [expanded, setExpanded] = useState(initialExpanded);
  const [activeNutrient, setActiveNutrient] = useState<'calories' | 'sugar' | 'saturatedFat' | 'fiber' | 'protein'>('calories');
  
  // Load nutrition history
  useEffect(() => {
    const loadHistory = async () => {
      try {
        setIsLoading(true);
        const historyData = await AsyncStorage.getItem(NUTRITION_HISTORY_KEY);
        
        if (historyData) {
          setHistories(JSON.parse(historyData));
        } else {
          // Initialize with mock data if none exists
          const mockData = generateMockHistory();
          await AsyncStorage.setItem(NUTRITION_HISTORY_KEY, JSON.stringify(mockData));
          setHistories(mockData);
        }
      } catch (error) {
        console.error('Error loading nutrition history:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadHistory();
  }, []);
  
  // Add new recipe to history if provided
  useEffect(() => {
    if (latestRecipe) {
      addRecipeToHistory(latestRecipe);
    }
  }, [latestRecipe]);
  
  // Add recipe to history
  const addRecipeToHistory = async (recipe: SavedRecipe) => {
    try {
      // Calculate improvements based on recipe's nutritionalInfo
      // This is simplified - in a real app we'd compare with original recipe
      const newImprovement: NutritionHistory = {
        date: new Date().toISOString(),
        improvements: {
          calories: recipe.nutritionalInfo.calories < 300 ? 10 : 5,
          fat: recipe.nutritionalInfo.fat ? 15 : 0,
          saturatedFat: recipe.nutritionalInfo.fat ? 20 : 0,
          sodium: 8,
          sugar: recipe.nutritionalInfo.sugar ? 12 : 0,
          fiber: recipe.nutritionalInfo.fiber ? 18 : 0,
          protein: recipe.nutritionalInfo.protein ? 5 : 0,
        }
      };
      
      const updatedHistory = [...histories, newImprovement];
      setHistories(updatedHistory);
      await AsyncStorage.setItem(NUTRITION_HISTORY_KEY, JSON.stringify(updatedHistory));
    } catch (error) {
      console.error('Error adding recipe to history:', error);
    }
  };
  
  // Filter history based on selected period
  const getFilteredHistory = () => {
    const now = new Date();
    const cutoffDate = new Date();
    
    if (period === 'week') {
      cutoffDate.setDate(now.getDate() - 7);
    } else if (period === 'month') {
      cutoffDate.setMonth(now.getMonth() - 1);
    } else {
      cutoffDate.setFullYear(now.getFullYear() - 1);
    }
    
    return histories.filter(h => new Date(h.date) >= cutoffDate);
  };
  
  // Calculate averages for the filtered period
  const calculateAverages = () => {
    const filtered = getFilteredHistory();
    if (filtered.length === 0) return null;
    
    const totals = {
      calories: 0,
      fat: 0,
      saturatedFat: 0,
      sodium: 0,
      sugar: 0,
      fiber: 0,
      protein: 0,
    };
    
    filtered.forEach(h => {
      Object.keys(totals).forEach(key => {
        totals[key as keyof typeof totals] += h.improvements[key as keyof typeof h.improvements];
      });
    });
    
    Object.keys(totals).forEach(key => {
      totals[key as keyof typeof totals] /= filtered.length;
    });
    
    return totals;
  };
  
  const averages = calculateAverages();
  
  const totalSwaps = getFilteredHistory().length;
  
  // Helper to calculate total calorie savings
  const calculateTotalCalorieSavings = () => {
    const filtered = getFilteredHistory();
    const totalCalories = filtered.reduce((total, h) => total + h.improvements.calories, 0);
    // Assuming an average of 50 calories saved per percentage point of improvement
    return totalCalories * 50;
  };
  
  // Calculate streak of consecutive days with improvements
  const calculateStreak = () => {
    if (histories.length === 0) return 0;
    
    const now = new Date();
    now.setHours(0, 0, 0, 0); // Normalize to start of day
    
    let streak = 0;
    let currentDate = new Date(now);
    
    while (true) {
      // Check if there's an entry for this date
      const dateStr = currentDate.toISOString().split('T')[0];
      const hasEntry = histories.some(h => h.date.split('T')[0] === dateStr);
      
      if (hasEntry) {
        streak += 1;
        // Move to previous day
        currentDate.setDate(currentDate.getDate() - 1);
      } else {
        break;
      }
    }
    
    return streak;
  };
  
  const streak = calculateStreak();
  
  // Generate bar height based on average value and maximum expected value
  const getBarHeight = (value: number, max: number) => {
    return Math.min(Math.max((value / max) * 100, 5), 100);
  };
  
  // Calculate percentage change from first to last value
  const calculateChange = (nutrientData: number[]) => {
    if (nutrientData.length < 2) return 0;
    
    const firstValue = nutrientData[0];
    const lastValue = nutrientData[nutrientData.length - 1];
    
    return ((lastValue - firstValue) / firstValue) * 100;
  };
  
  // Get appropriate color based on whether decrease is good (calories, sugar, saturated fat)
  // or increase is good (fiber, protein)
  const getChangeColor = (nutrient: string, changePercent: number) => {
    const decreaseIsGood = ['calories', 'sugar', 'saturatedFat'].includes(nutrient);
    
    if (decreaseIsGood) {
      return changePercent < 0 ? colors.success : colors.error;
    } else {
      return changePercent > 0 ? colors.success : colors.error;
    }
  };
  
  // Format data for display
  const getNutrientData = () => {
    return [
      {
        id: 'calories',
        name: 'Calories',
        unit: 'kcal',
        data: [histories[histories.length - 1].improvements.calories, histories[0].improvements.calories],
        change: calculateChange([histories[histories.length - 1].improvements.calories, histories[0].improvements.calories]),
        goal: 'decrease'
      },
      {
        id: 'sugar',
        name: 'Added Sugar',
        unit: 'g',
        data: [histories[histories.length - 1].improvements.sugar, histories[0].improvements.sugar],
        change: calculateChange([histories[histories.length - 1].improvements.sugar, histories[0].improvements.sugar]),
        goal: 'decrease'
      },
      {
        id: 'saturatedFat',
        name: 'Saturated Fat',
        unit: 'g',
        data: [histories[histories.length - 1].improvements.saturatedFat, histories[0].improvements.saturatedFat],
        change: calculateChange([histories[histories.length - 1].improvements.saturatedFat, histories[0].improvements.saturatedFat]),
        goal: 'decrease'
      },
      {
        id: 'fiber',
        name: 'Fiber',
        unit: 'g',
        data: [histories[histories.length - 1].improvements.fiber, histories[0].improvements.fiber],
        change: calculateChange([histories[histories.length - 1].improvements.fiber, histories[0].improvements.fiber]),
        goal: 'increase'
      },
      {
        id: 'protein',
        name: 'Protein',
        unit: 'g',
        data: [histories[histories.length - 1].improvements.protein, histories[0].improvements.protein],
        change: calculateChange([histories[histories.length - 1].improvements.protein, histories[0].improvements.protein]),
        goal: 'increase'
      }
    ];
  };
  
  const nutrientData = getNutrientData();
  const activeData = nutrientData.find(n => n.id === activeNutrient) || nutrientData[0];
  
  // Render chart for the active nutrient
  const renderChart = () => {
    const data = activeData.data;
    const max = Math.max(...data) * 1.1; // Add 10% padding to the top
    
    return (
      <View style={styles.chartContainer}>
        <View style={styles.yAxisLabels}>
          <Text style={[styles.axisLabel, { color: colors.textSecondary }]}>{Math.round(max)}</Text>
          <Text style={[styles.axisLabel, { color: colors.textSecondary }]}>{Math.round(max / 2)}</Text>
          <Text style={[styles.axisLabel, { color: colors.textSecondary }]}>0</Text>
        </View>
        
        <View style={styles.chartContent}>
          {data.map((value, index) => {
            const barHeight = (value / max) * 150; // Max height 150
            const lastWeeks = data.length - 6;
            const weekLabel = `Week ${index + 1 + lastWeeks}`;
            
            return (
              <View key={index} style={styles.barContainer}>
                <View 
                  style={[
                    styles.bar, 
                    { 
                      height: barHeight, 
                      backgroundColor: getChangeColor(activeData.id, activeData.change),
                      opacity: 0.5 + (index / (data.length * 2))
                    }
                  ]} 
                />
                <Text style={[styles.barLabel, { color: colors.textSecondary }]}>
                  {weekLabel}
                </Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };
  
  // Render nutrient selector buttons
  const renderNutrientSelector = () => {
    return (
      <View style={styles.nutrientSelector}>
        {nutrientData.map(nutrient => (
          <TouchableOpacity
            key={nutrient.id}
            style={[
              styles.nutrientButton,
              activeNutrient === nutrient.id && { 
                backgroundColor: colors.primaryLight,
                borderColor: colors.primary 
              },
              { borderColor: colors.border }
            ]}
            onPress={() => setActiveNutrient(nutrient.id as any)}
          >
            <Text 
              style={[
                styles.nutrientButtonText, 
                { 
                  color: activeNutrient === nutrient.id ? 
                    colors.primary : colors.textSecondary 
                }
              ]}
            >
              {nutrient.name}
            </Text>
            
            {/* Show change percentage badge */}
            <View 
              style={[
                styles.changeBadge, 
                { backgroundColor: getChangeColor(nutrient.id, nutrient.change) }
              ]}
            >
              <Text style={styles.changeBadgeText}>
                {nutrient.change > 0 ? '+' : ''}{nutrient.change.toFixed(1)}%
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    );
  };
  
  return (
    <View style={[styles.container, { backgroundColor: colors.card, borderColor: colors.border }]}>
      <TouchableOpacity 
        style={styles.header}
        onPress={() => setExpanded(!expanded)}
      >
        <View style={styles.headerLeft}>
          <Feather name="bar-chart-2" size={20} color={colors.primary} style={styles.headerIcon}  />
          <Text style={[styles.headerText, { color: colors.text }]}>
            Nutrition Improvement Tracker
          </Text>
        </View>
        
        {expanded ? 
          <Feather name="chevron-up" size={20} color={colors.textSecondary} /> :
          <Feather name="chevron-down" size={20} color={colors.textSecondary} />
        }
      </TouchableOpacity>
      
      {expanded && (
        <View style={styles.content}>
          <View style={styles.summaryContainer}>
            <View style={styles.summaryHeader}>
              <Text style={[styles.summaryTitle, { color: colors.text }]}>
                {activeData.name} Trend
              </Text>
              
              <View style={styles.period}>
                <Text style={[styles.periodText, { color: colors.textSecondary }]}>
                  Past 6 weeks
                </Text>
              </View>
            </View>
            
            <View style={styles.summaryRow}>
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                  Current
                </Text>
                <Text style={[styles.summaryValue, { color: colors.text }]}>
                  {activeData.data[activeData.data.length - 1]} {activeData.unit}
                </Text>
              </View>
              
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                  6 Weeks Ago
                </Text>
                <Text style={[styles.summaryValue, { color: colors.text }]}>
                  {activeData.data[0]} {activeData.unit}
                </Text>
              </View>
              
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryLabel, { color: colors.textSecondary }]}>
                  Change
                </Text>
                <View style={styles.changeValueContainer}>
                  <ArrowUpRight 
                    size={16} 
                    color={getChangeColor(activeData.id, activeData.change)}
                    style={[
                      styles.changeIcon,
                      activeData.change < 0 && { transform: [{ rotate: '90deg' }] }
                    ]}
                  />
                  <Text 
                    style={[
                      styles.changeValue, 
                      { color: getChangeColor(activeData.id, activeData.change) }
                    ]}
                  >
                    {Math.abs(activeData.change).toFixed(1)}%
                  </Text>
                </View>
              </View>
            </View>
          </View>
          
          {renderChart()}
          {renderNutrientSelector()}
          
          <Text style={[styles.disclaimer, { color: colors.textSecondary }]}>
            Based on your food swaps and recipe alternatives over time
          </Text>
        </View>
      )}
    </View>
  );
}

// Helper function to generate mock history data for demo purposes
function generateMockHistory(): NutritionHistory[] {
  const history: NutritionHistory[] = [];
  const now = new Date();
  
  // Generate entries for the past 30 days
  for (let i = 0; i < 30; i++) {
    // Skip some days to simulate non-consecutive usage
    if (i > 3 && i % 6 === 0) continue;
    
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    
    history.push({
      date: date.toISOString(),
      improvements: {
        calories: Math.random() * 15 + 5, // 5-20% improvement
        fat: Math.random() * 18 + 2, // 2-20% improvement
        saturatedFat: Math.random() * 20 + 5, // 5-25% improvement
        sodium: Math.random() * 10 + 5, // 5-15% improvement
        sugar: Math.random() * 15 + 5, // 5-20% improvement
        fiber: Math.random() * 20 + 5, // 5-25% improvement
        protein: Math.random() * 10 + 2, // 2-12% improvement
      }
    });
  }
  
  return history;
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    borderWidth: 1,
    marginVertical: 16,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: 8,
  },
  headerText: {
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    padding: 16,
    paddingTop: 0,
  },
  summaryContainer: {
    marginBottom: 16,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  period: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  periodText: {
    fontSize: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryItem: {
    flex: 1,
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 15,
    fontWeight: '600',
  },
  changeValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  changeIcon: {
    marginRight: 4,
  },
  changeValue: {
    fontSize: 15,
    fontWeight: '600',
  },
  chartContainer: {
    height: 200,
    flexDirection: 'row',
    marginBottom: 16,
  },
  yAxisLabels: {
    width: 30,
    height: 150,
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingRight: 5,
  },
  axisLabel: {
    fontSize: 10,
  },
  chartContent: {
    flex: 1,
    height: 150,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingLeft: 5,
  },
  barContainer: {
    alignItems: 'center',
  },
  bar: {
    width: 16,
    borderTopLeftRadius: 3,
    borderTopRightRadius: 3,
  },
  barLabel: {
    fontSize: 9,
    marginTop: 4,
    transform: [{ rotate: '-45deg' }],
    width: 40,
    textAlign: 'center',
  },
  nutrientSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  nutrientButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    marginRight: 8,
    marginBottom: 8,
  },
  nutrientButtonText: {
    fontSize: 12,
    fontWeight: '500',
    marginRight: 6,
  },
  changeBadge: {
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
  },
  changeBadgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '600',
  },
  disclaimer: {
    fontSize: 12,
    fontStyle: 'italic',
    textAlign: 'center',
  },
}); 