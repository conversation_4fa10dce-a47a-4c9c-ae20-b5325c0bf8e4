# UI Components

This directory contains reusable UI components for the health app.

## Safe Text Rendering Components

### SafeText

A component that safely renders text content. This helps prevent issues when text might appear in unexpected places.

```tsx
import { SafeText } from '@/components/ui';

// Usage
<SafeText>This will always render safely</SafeText>

// With styling
<SafeText style={{ fontSize: 16, color: 'blue' }}>
  Styled text
</SafeText>
```

### SafeView

A wrapper for View that safely handles text nodes by automatically wrapping them in Text components. Use this to prevent the common React Native error: "A text node cannot be a child of a <View>".

```tsx
import { SafeView } from '@/components/ui';

// Usage - this will never cause the text node error
<SafeView>
  {someValue} // Even if this is a string or number, it will be wrapped in a Text component
  <SomeOtherComponent />
</SafeView>
```

## When to use these components

Use `SafeView` in places where:
- You're conditionally rendering content that might include text nodes
- You're rendering dynamic content that might not always be a React component
- You're seeing "A text node cannot be a child of a <View>" errors

Use `SafeText` when:
- You need a simple text component with consistent default styling
- You want to ensure text is safely rendered regardless of its parent component

## Example: Fixing Text Node Errors

Before:
```tsx
<View>
  {condition ? <Component /> : "Some text"} // This causes an error
</View>
```

After:
```tsx
<SafeView>
  {condition ? <Component /> : "Some text"} // This works fine
</SafeView>
```

Or:
```tsx
<View>
  {condition ? <Component /> : <SafeText>Some text</SafeText>}
</View>
``` 