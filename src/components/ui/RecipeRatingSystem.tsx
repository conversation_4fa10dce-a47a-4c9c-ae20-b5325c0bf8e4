import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  TextInput, 
  Modal,
  ActivityIndicator,
  Alert,
  Keyboard,
  TouchableWithoutFeedback
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather , MaterialIcons } from '@expo/vector-icons';
import { SavedRecipe, rateRecipe } from '@/services/recipeCollectionService';

interface RecipeRatingSystemProps {
  recipe: SavedRecipe;
  onRatingComplete?: (newRating: number) => void;
  showPrompt?: boolean;
}

export function RecipeRatingSystem({ 
  recipe, 
  onRatingComplete,
  showPrompt = false 
}: RecipeRatingSystemProps) {
  const { colors } = useTheme();
  const [rating, setRating] = useState<number>(recipe.rating || 0);
  const [feedback, setFeedback] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  
  // Show modal if prompted
  useEffect(() => {
    if (showPrompt && !recipe.rating) {
      setModalVisible(true);
    }
  }, [showPrompt, recipe.rating]);
  
  // Handle star press to set rating
  const handleStarPress = (selectedRating: number) => {
    setRating(selectedRating);
  };
  
  // Submit rating to backend
  const handleSubmitRating = async () => {
    if (rating === 0) {
      Alert.alert('Please Rate', 'Please select a rating before submitting');
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      // Save rating to backend
      const success = await rateRecipe(recipe.id, rating);
      
      // Send feedback if provided (would be implemented in a real app)
      const hasFeedback = feedback.trim().length > 0;
      
      if (success) {
        setShowSuccess(true);
        
        // Notify parent component if needed
        if (onRatingComplete) {
          onRatingComplete(rating);
        }
        
        // Hide success after a delay
        setTimeout(() => {
          setShowSuccess(false);
          setModalVisible(false);
          
          // Clear feedback after submission
          setFeedback('');
        }, 1500);
      } else {
        Alert.alert('Error', 'Failed to submit rating. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting rating:', error);
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Render stars for rating
  const renderStars = (size = 24, interactive = true) => {
    return (
      <View style={styles.starsContainer}>
        {[1, 2, 3, 4, 5].map((i) => (
          <TouchableOpacity
            key={i}
            onPress={() => interactive && handleStarPress(i)}
            disabled={!interactive || isSubmitting}
            style={styles.starButton}
          >
            <Feather name="star" color={colors.primary} />
          </TouchableOpacity>
        ))}
      </View>
    );
  };
  
  // Compact rating display for use in recipe cards
  if (!modalVisible) {
    return (
      <View style={styles.compactContainer}>
        <TouchableOpacity 
          style={[styles.rateButton, { backgroundColor: colors.primaryLight }]}
          onPress={() => setModalVisible(true)}
        >
          <Feather name="star" size={16} color={colors.primary} style={styles.rateIcon} />
          <Text style={[styles.rateText, { color: colors.primary }]}>
            {recipe.rating ? 'Edit Rating' : 'Rate Recipe'}
          </Text>
        </TouchableOpacity>
        
        {(recipe.rating ?? 0) > 0 && (
          <View style={styles.ratingDisplay}>
            <Text style={[styles.ratingText, { color: colors.text }]}>
              {(recipe.rating || 0).toFixed(1)}
            </Text>
            <Feather name="star" size={14} color={colors.primary} />
          </View>
        )}
      </View>
    );
  }
  
  return (
    <Modal
      visible={modalVisible}
      transparent
      animationType="fade"
      onRequestClose={() => setModalVisible(false)}
    >
      <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, { backgroundColor: colors.card }]}>
            {!showSuccess ? (
              <>
                <View style={styles.header}>
                  <Text style={[styles.title, { color: colors.text }]}>
                    Rate This Recipe
                  </Text>
                  
                  <TouchableOpacity 
                    style={[styles.closeButton, { backgroundColor: colors.subtle }]}
                    onPress={() => setModalVisible(false)}
                  >
                    <Feather name="x" size={20} color={colors.text} />
                  </TouchableOpacity>
                </View>
                
                <Text style={[styles.recipeName, { color: colors.text }]}>
                  {recipe.name}
                </Text>
                
                <View style={styles.ratingSection}>
                  <Text style={[styles.ratingLabel, { color: colors.text }]}>
                    How would you rate this healthier alternative?
                  </Text>
                  
                  {renderStars(32)}
                  
                  <Text style={[styles.ratingDescription, { color: colors.textSecondary }]}>
                    {rating === 1 && 'Not great - needs improvement'}
                    {rating === 2 && 'Fair - could be better'}
                    {rating === 3 && 'Good - adequate substitute'}
                    {rating === 4 && 'Very good - nearly as good as original'}
                    {rating === 5 && 'Excellent - better than the original!'}
                  </Text>
                </View>
                
                <View style={styles.feedbackSection}>
                  <Text style={[styles.feedbackLabel, { color: colors.text }]}>
                    Additional Feedback (Optional)
                  </Text>
                  
                  <TextInput
                    style={[styles.feedbackInput, { 
                      backgroundColor: colors.subtle,
                      color: colors.text,
                      borderColor: colors.border
                    }]}
                    placeholder="Tell us more about your experience..."
                    placeholderTextColor={colors.textSecondary}
                    multiline
                    value={feedback}
                    onChangeText={setFeedback}
                    maxLength={250}
                  />
                </View>
                
                <TouchableOpacity
                  style={[styles.submitButton, { 
                    backgroundColor: rating > 0 ? colors.primary : colors.subtle,
                    opacity: isSubmitting ? 0.7 : 1
                  }]}
                  onPress={handleSubmitRating}
                  disabled={isSubmitting || rating === 0}
                >
                  {isSubmitting ? (
                    <ActivityIndicator size="small" color="white" />
                  ) : (
                    <>
                      <Feather name="send" size={18} color="white" style={styles.submitIcon}  />
                      <Text style={styles.submitText}>Submit Rating</Text>
                    </>
                  )}
                </TouchableOpacity>
              </>
            ) : (
              <View style={styles.successContainer}>
                <MaterialIcons name="check-circle" size={64} color={colors.success} style={styles.successIcon} />
                <Text style={[styles.successText, { color: colors.text }]}>
                  Thank you for your feedback!
                </Text>
                <Text style={[styles.successSubtext, { color: colors.textSecondary }]}>
                  Your rating helps improve our recommendations
                </Text>
              </View>
            )}
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
}

const styles = StyleSheet.create({
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  rateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  rateIcon: {
    marginRight: 4,
  },
  rateText: {
    fontSize: 12,
    fontWeight: '500',
  },
  ratingDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '85%',
    borderRadius: 12,
    padding: 20,
    maxWidth: 400,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recipeName: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 20,
    textAlign: 'center',
  },
  ratingSection: {
    alignItems: 'center',
    marginBottom: 24,
  },
  ratingLabel: {
    fontSize: 16,
    marginBottom: 12,
    textAlign: 'center',
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  starButton: {
    padding: 5,
  },
  ratingDescription: {
    fontSize: 14,
    textAlign: 'center',
    minHeight: 20,
  },
  feedbackSection: {
    marginBottom: 24,
  },
  feedbackLabel: {
    fontSize: 16,
    marginBottom: 8,
  },
  feedbackInput: {
    borderRadius: 8,
    borderWidth: 1,
    padding: 12,
    height: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  submitIcon: {
    marginRight: 8,
  },
  submitText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  successContainer: {
    alignItems: 'center',
    padding: 20,
  },
  successIcon: {
    marginBottom: 16,
  },
  successText: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  successSubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
}); 