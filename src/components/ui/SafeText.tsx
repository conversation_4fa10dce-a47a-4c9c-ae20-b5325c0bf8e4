import React, { ReactNode } from 'react';
import { Text, TextProps, StyleSheet } from 'react-native';

interface SafeTextProps extends TextProps {
  children: ReactNode;
}

/**
 * A component that safely renders text content
 * This is useful for preventing "A text node cannot be a child of a <View>" errors
 * by automatically wrapping any potential text nodes in a Text component
 */
export function SafeText({ children, style, ...props }: SafeTextProps) {
  return (
    <Text style={[styles.defaultText, style]} {...props}>
      {children}
    </Text>
  );
}

const styles = StyleSheet.create({
  defaultText: {
    fontSize: 14,
  }
}); 