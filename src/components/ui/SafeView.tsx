import React, { ReactNode } from 'react';
import { View, ViewProps, Text } from 'react-native';

interface SafeViewProps extends ViewProps {
  children: ReactNode;
}

/**
 * A wrapper for View that safely handles text nodes by wrapping them in Text components
 * This helps prevent "A text node cannot be a child of a <View>" errors
 */
export function SafeView({ children, style, ...props }: SafeViewProps) {
  // Process children to wrap any string/number children in Text components
  const processedChildren = React.Children.map(children, child => {
    // If child is a string or number (text node), wrap it in a Text component
    if (typeof child === 'string' || typeof child === 'number') {
      return <Text>{child}</Text>;
    }
    return child;
  });
  
  return (
    <View style={style} {...props}>
      {processedChildren}
    </View>
  );
} 