import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Feather } from '@expo/vector-icons';

// Types for seasonal ingredients
interface SeasonalIngredient {
  id: string;
  name: string;
  peak: string; // e.g., "Early Summer"
  months: number[]; // 1-12 for Jan-Dec
  type: 'fruit' | 'vegetable' | 'herb';
  substitutes: string[]; // What this can replace
  benefits: string[];
  imageUrl?: string;
}

// Seasonal ingredient data
const SEASONAL_INGREDIENTS: SeasonalIngredient[] = [
  {
    id: 'strawberries',
    name: 'Strawberries',
    peak: 'Late Spring',
    months: [4, 5, 6, 7],
    type: 'fruit',
    substitutes: ['refined sugar', 'artificial sweeteners', 'jam'],
    benefits: ['Natural sweetness', 'High in vitamin C', 'Rich in antioxidants'],
    imageUrl: 'https://images.unsplash.com/photo-1464965911861-746a04b4bca6?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=300&q=80'
  },
  {
    id: 'kale',
    name: 'Kale',
    peak: 'Fall to Winter',
    months: [9, 10, 11, 12, 1, 2],
    type: 'vegetable',
    substitutes: ['spinach', 'lettuce', 'cabbage'],
    benefits: ['More nutrient-dense than many lettuces', 'High in vitamins K and C', 'Contains cancer-fighting compounds'],
    imageUrl: 'https://images.unsplash.com/photo-1515192558034-e21e7e033d73?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=300&q=80'
  },
  {
    id: 'blueberries',
    name: 'Blueberries',
    peak: 'Summer',
    months: [6, 7, 8],
    type: 'fruit',
    substitutes: ['sugar', 'chocolate chips', 'candy'],
    benefits: ['Natural sweetness', 'High in antioxidants', 'Support brain health'],
    imageUrl: 'https://images.unsplash.com/photo-1498557850523-fd3d118b962e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=300&q=80'
  },
  {
    id: 'butternut-squash',
    name: 'Butternut Squash',
    peak: 'Fall',
    months: [9, 10, 11, 12],
    type: 'vegetable',
    substitutes: ['sweet potato', 'potato', 'carrot'],
    benefits: ['Rich in vitamins A and C', 'High in fiber', 'Lower glycemic index than potatoes'],
    imageUrl: 'https://images.unsplash.com/photo-**********-ab62050f199c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=300&q=80'
  },
  {
    id: 'asparagus',
    name: 'Asparagus',
    peak: 'Spring',
    months: [3, 4, 5, 6],
    type: 'vegetable',
    substitutes: ['green beans', 'broccoli'],
    benefits: ['High in folate', 'Good source of vitamin K', 'Contains anti-inflammatory compounds'],
    imageUrl: 'https://images.unsplash.com/photo-1522184462610-800d92c656d7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=300&q=80'
  },
  {
    id: 'basil',
    name: 'Fresh Basil',
    peak: 'Summer',
    months: [6, 7, 8, 9],
    type: 'herb',
    substitutes: ['dried herbs', 'salt', 'pepper'],
    benefits: ['Intense natural flavor', 'Contains essential oils with anti-inflammatory benefits', 'Rich in vitamin K'],
    imageUrl: 'https://images.unsplash.com/photo-1619465292683-ef9b40c582cf?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=300&q=80'
  },
  {
    id: 'brussels-sprouts',
    name: 'Brussels Sprouts',
    peak: 'Fall to Winter',
    months: [9, 10, 11, 12, 1, 2],
    type: 'vegetable',
    substitutes: ['cabbage', 'frozen vegetables'],
    benefits: ['High in fiber', 'Rich in vitamins K and C', 'Contains cancer-fighting compounds'],
    imageUrl: 'https://images.unsplash.com/photo-1438118907704-7718ee9a191a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=300&q=80'
  },
  {
    id: 'pumpkin',
    name: 'Pumpkin',
    peak: 'Fall',
    months: [9, 10, 11],
    type: 'vegetable',
    substitutes: ['canned pumpkin', 'sweet potato', 'butternut squash'],
    benefits: ['High in vitamin A', 'Rich in antioxidants', 'Good source of fiber'],
    imageUrl: 'https://images.unsplash.com/photo-1506917728037-b6af01a7d403?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=300&q=80'
  },
  {
    id: 'citrus',
    name: 'Citrus Fruits',
    peak: 'Winter',
    months: [12, 1, 2, 3],
    type: 'fruit',
    substitutes: ['lemon juice', 'vinegar', 'salt'],
    benefits: ['Natural flavor enhancers', 'High in vitamin C', 'Contains antioxidants'],
    imageUrl: 'https://images.unsplash.com/photo-1611080626919-7cf5a9dbab12?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=300&q=80'
  },
  {
    id: 'tomatoes',
    name: 'Fresh Tomatoes',
    peak: 'Summer',
    months: [7, 8, 9],
    type: 'vegetable',
    substitutes: ['canned tomatoes', 'tomato paste', 'sun-dried tomatoes'],
    benefits: ['Rich in lycopene', 'Better flavor than off-season tomatoes', 'Higher vitamin content'],
    imageUrl: 'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=300&q=80'
  }
];

// Component props
interface SeasonalIngredientSuggestionsProps {
  onIngredientSelect?: (ingredient: SeasonalIngredient) => void;
  location?: string; // Could be used for regional seasonality
}

export function SeasonalIngredientSuggestions({
  onIngredientSelect,
  location = 'North America', // Default location
}: SeasonalIngredientSuggestionsProps) {
  const { colors } = useTheme();
  const [currentDate] = useState(new Date());
  const currentMonth = currentDate.getMonth() + 1; // 1-12 for Jan-Dec
  
  // Filter ingredients that are in season now
  const inSeasonNow = SEASONAL_INGREDIENTS.filter(ingredient => 
    ingredient.months.includes(currentMonth)
  );
  
  // Filter ingredients coming into season soon (next month)
  const comingSoon = SEASONAL_INGREDIENTS.filter(ingredient => {
    const nextMonth = currentMonth === 12 ? 1 : currentMonth + 1;
    return ingredient.months.includes(nextMonth) && !ingredient.months.includes(currentMonth);
  });
  
  // Get the current season name
  const getCurrentSeason = () => {
    // Northern hemisphere seasons
    if (currentMonth >= 3 && currentMonth <= 5) return 'spring';
    if (currentMonth >= 6 && currentMonth <= 8) return 'summer';
    if (currentMonth >= 9 && currentMonth <= 11) return 'fall';
    return 'winter';
  };
  
  const currentSeason = getCurrentSeason();
  
  // Get a placeholder image if the ingredient image is not available
  const getPlaceholderImage = (type: string) => {
    if (type === 'fruit') return 'https://via.placeholder.com/100x100/FFB6C1/FFFFFF?text=Fruit';
    if (type === 'vegetable') return 'https://via.placeholder.com/100x100/90EE90/FFFFFF?text=Vegetable';
    return 'https://via.placeholder.com/100x100/ADD8E6/FFFFFF?text=Herb';
  };
  
  const renderIngredientCard = (ingredient: SeasonalIngredient) => (
    <TouchableOpacity 
      key={ingredient.id}
      style={[styles.ingredientCard, { backgroundColor: colors.card, borderColor: colors.border }]}
      onPress={() => onIngredientSelect?.(ingredient)}
    >
      <Image 
        source={{ uri: ingredient.imageUrl || getPlaceholderImage(ingredient.type) }}
        style={styles.ingredientImage}
        resizeMode="cover"
      />
      
      <View style={styles.ingredientInfo}>
        <Text style={[styles.ingredientName, { color: colors.text }]}>
          {ingredient.name}
        </Text>
        
        <View style={styles.seasonBadge}>
          <Feather name="calendar" size={12} color={colors.primary} style={styles.seasonIcon} />
          <Text style={[styles.seasonText, { color: colors.primary }]}>
            {ingredient.peak}
          </Text>
        </View>
        
        <Text style={[styles.substituteText, { color: colors.textSecondary }]} numberOfLines={2}>
          Swap for: {ingredient.substitutes.join(', ')}
        </Text>
      </View>
    </TouchableOpacity>
  );
  
  return (
    <View style={[styles.container, { backgroundColor: colors.card, borderColor: colors.border }]}>
      <View style={styles.header}>
        <View>
          <Text style={[styles.title, { color: colors.text }]}>Seasonal Ingredients</Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            Currently in {currentSeason.charAt(0).toUpperCase() + currentSeason.slice(1)} • {location}
          </Text>
        </View>
        
        <Feather name="leaf" size={20} color={colors.success}  />
      </View>
      
      <View style={styles.seasonBanner}>
        <Feather name="clock" size={16} color={colors.primary} style={styles.bannerIcon} />
        <Text style={[styles.bannerText, { color: colors.text }]}>
          In Season Now ({inSeasonNow.length})
        </Text>
      </View>
      
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {inSeasonNow.length > 0 ? (
          inSeasonNow.map(renderIngredientCard)
        ) : (
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            No ingredients currently in season for your region.
          </Text>
        )}
      </ScrollView>
      
      {comingSoon.length > 0 && (
        <>
          <View style={styles.seasonBanner}>
            <Feather name="refresh-cw" size={16} color={colors.primary} style={styles.bannerIcon} />
            <Text style={[styles.bannerText, { color: colors.text }]}>
              Coming Soon ({comingSoon.length})
            </Text>
          </View>
          
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            {comingSoon.map(renderIngredientCard)}
          </ScrollView>
        </>
      )}
      
      <TouchableOpacity 
        style={[styles.viewAllButton, { borderColor: colors.border }]}
        onPress={() => {/* Navigate to full seasonal guide */}}
      >
        <Text style={[styles.viewAllText, { color: colors.primary }]}>
          View Seasonal Guide
        </Text>
        <Feather name="arrow-right" size={16} color={colors.primary} />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 13,
  },
  seasonBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  bannerIcon: {
    marginRight: 8,
  },
  bannerText: {
    fontSize: 16,
    fontWeight: '500',
  },
  scrollContent: {
    paddingBottom: 8,
    paddingRight: 16,
  },
  ingredientCard: {
    width: 160,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 12,
    overflow: 'hidden',
  },
  ingredientImage: {
    width: '100%',
    height: 100,
  },
  ingredientInfo: {
    padding: 10,
  },
  ingredientName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  seasonBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  seasonIcon: {
    marginRight: 4,
  },
  seasonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  substituteText: {
    fontSize: 12,
  },
  emptyText: {
    fontSize: 14,
    fontStyle: 'italic',
    paddingVertical: 16,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginTop: 8,
    borderTopWidth: 1,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 6,
  },
}); 