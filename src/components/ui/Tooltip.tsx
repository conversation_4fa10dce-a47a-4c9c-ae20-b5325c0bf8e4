import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Animated, Pressable } from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';

interface TooltipProps {
  text: string;
  position: { x: number; y: number };
  onClose: () => void;
  maxWidth?: number;
}

const Tooltip: React.FC<TooltipProps> = ({ 
  text, 
  position, 
  onClose,
  maxWidth = 200
}) => {
  const { colors, isDark } = useTheme();
  const opacity = new Animated.Value(0);
  
  useEffect(() => {
    Animated.timing(opacity, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
    
    // Auto-hide tooltip after 3 seconds
    const timer = setTimeout(() => {
      Animated.timing(opacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start(() => onClose());
    }, 3000);
    
    return () => clearTimeout(timer);
  }, []);
  
  return (
    <Pressable 
      style={StyleSheet.absoluteFill} 
      onPress={onClose}
      accessibilityLabel="Close tooltip"
      accessibilityRole="button"
    >
      <Animated.View 
        style={[
          styles.container,
          {
            opacity,
            backgroundColor: isDark ? '#1E293B' : '#FFFFFF',
            left: position.x,
            top: position.y,
            maxWidth,
            borderColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: isDark ? 0.3 : 0.1,
            shadowRadius: 4,
          }
        ]}
      >
        <View style={styles.arrow} />
        <Text style={[styles.text, { color: colors.text }]}>
          {text}
        </Text>
      </Animated.View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    padding: 10,
    borderRadius: 8,
    borderWidth: 1,
    elevation: 5,
    zIndex: 1000,
  },
  arrow: {
    position: 'absolute',
    top: -8,
    left: '50%',
    marginLeft: -8,
    width: 0,
    height: 0,
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderBottomWidth: 8,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: '#FFFFFF', // Will be overridden by the theme
  },
  text: {
    fontSize: 14,
    lineHeight: 18,
  },
});

export default Tooltip; 