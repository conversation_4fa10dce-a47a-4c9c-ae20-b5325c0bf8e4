import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, ScrollView } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';

interface BenefitExplanation {
  title: string;
  shortDescription: string;
  longDescription: string;
  scientificReference?: string;
}

interface WhyThisSwapExplainerProps {
  originalIngredient: string;
  substituteIngredient: string;
  benefits: BenefitExplanation[];
}

export function WhyThisSwapExplainer({ 
  originalIngredient, 
  substituteIngredient, 
  benefits 
}: WhyThisSwapExplainerProps) {
  const { colors } = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedBenefit, setSelectedBenefit] = useState<BenefitExplanation | null>(null);
  
  const handleOpenBenefitModal = (benefit: BenefitExplanation) => {
    setSelectedBenefit(benefit);
    setModalVisible(true);
  };
  
  return (
    <View style={styles.container}>
      <View style={[styles.header, { backgroundColor: colors.primary }]}>
        <Feather name="info" size={18} style={styles.headerIcon} />
        <Text style={styles.headerText}>Why Swap {originalIngredient} for {substituteIngredient}?</Text>
      </View>
      
      <View style={styles.benefitsContainer}>
        {benefits.map((benefit, index) => (
          <TouchableOpacity 
            key={index} 
            style={[styles.benefitItem, { borderBottomColor: colors.border }]}
            onPress={() => handleOpenBenefitModal(benefit)}
          >
            <Text style={[styles.benefitTitle, { color: colors.text }]}>
              {benefit.title}
            </Text>
            <Text style={[styles.benefitDescription, { color: colors.textSecondary }]}>
              {benefit.shortDescription}
            </Text>
            <Text style={[styles.learnMore, { color: colors.primary }]}>
              Learn more
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: 'rgba(0,0,0,0.5)' }]}>
          <View style={[styles.modalContent, { backgroundColor: colors.background }]}>
            <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                {selectedBenefit?.title}
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Feather name="x" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalBody}>
              <Text style={[styles.modalSubtitle, { color: colors.text }]}>
                Replacing {originalIngredient} with {substituteIngredient}
              </Text>
              
              <Text style={[styles.modalDescription, { color: colors.text }]}>
                {selectedBenefit?.longDescription}
              </Text>
              
              {selectedBenefit?.scientificReference && (
                <View style={[styles.referenceContainer, { backgroundColor: colors.card }]}>
                  <Text style={[styles.referenceTitle, { color: colors.textSecondary }]}>
                    Scientific Reference:
                  </Text>
                  <Text style={[styles.referenceText, { color: colors.textSecondary }]}>
                    {selectedBenefit.scientificReference}
                  </Text>
                </View>
              )}
            </ScrollView>
            
            <TouchableOpacity 
              style={[styles.closeButton, { backgroundColor: colors.primary }]}
              onPress={() => setModalVisible(false)}
            >
              <Text style={styles.closeButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    marginVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  headerIcon: {
    marginRight: 8,
  },
  headerText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  benefitsContainer: {
    padding: 8,
  },
  benefitItem: {
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
  },
  benefitTitle: {
    fontWeight: '600',
    fontSize: 15,
    marginBottom: 4,
  },
  benefitDescription: {
    fontSize: 14,
    marginBottom: 6,
  },
  learnMore: {
    fontSize: 13,
    fontWeight: '500',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxHeight: '80%',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  modalBody: {
    padding: 16,
  },
  modalSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  modalDescription: {
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 20,
  },
  referenceContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  referenceTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  referenceText: {
    fontSize: 13,
    fontStyle: 'italic',
  },
  closeButton: {
    padding: 16,
    alignItems: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
}); 