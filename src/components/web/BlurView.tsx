import React from 'react';
import { View, ViewProps } from 'react-native';

interface BlurViewProps extends ViewProps {
  intensity?: number;
  tint?: 'light' | 'dark' | 'default';
}

export const BlurView: React.FC<BlurViewProps> = ({ 
  intensity = 10, 
  tint = 'default',
  style,
  children,
  ...props 
}) => {
  // Calculate opacity based on intensity (0-100)
  const opacity = Math.min(intensity / 100, 0.8);
  
  // Determine background color based on tint
  let backgroundColor = `rgba(255, 255, 255, ${opacity})`;
  if (tint === 'dark') {
    backgroundColor = `rgba(0, 0, 0, ${opacity})`;
  }

  return (
    <View
      style={[
        {
          backgroundColor,
          backdropFilter: 'blur(10px)',
        },
        style
      ]}
      {...props}
    >
      {children}
    </View>
  );
}; 