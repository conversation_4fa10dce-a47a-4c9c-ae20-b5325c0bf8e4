import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export const CameraType = {
  front: 'front',
  back: 'back',
};

export const useCameraPermissions = () => {
  return [{ granted: false }, () => Promise.resolve(false)];
};

interface CameraViewProps {
  type?: 'front' | 'back';
  style?: any;
  children?: React.ReactNode;
  onMountError?: (error: Error) => void;
}

export const CameraView: React.FC<CameraViewProps> = ({ 
  style, 
  children,
  ...props 
}) => {
  return (
    <View style={[styles.container, style]}>
      <View style={styles.messageContainer}>
        <Text style={styles.title}>Camera Not Available</Text>
        <Text style={styles.message}>
          The camera feature is not available in web view. Please use the mobile app for full functionality.
        </Text>
      </View>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageContainer: {
    padding: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(0,0,0,0.7)',
    width: '80%',
    alignItems: 'center',
  },
  title: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  message: {
    color: 'white',
    textAlign: 'center',
    lineHeight: 20,
  }
}); 