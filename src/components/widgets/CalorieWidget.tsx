import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { ProgressBar } from 'react-native-paper';

interface CalorieWidgetProps {
  consumed: number;
  goal: number;
}

export function CalorieWidget({ consumed, goal }: CalorieWidgetProps) {
  const progress = Math.min(consumed / goal, 1);
  const remaining = goal - consumed;
  
  // Determine color based on consumption level
  const getProgressColor = () => {
    if (progress < 0.5) return '#4CAF50'; // Green
    if (progress < 0.75) return '#FFC107'; // Yellow
    if (progress < 0.9) return '#FF9800'; // Orange
    return '#F44336'; // Red
  };
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Daily Calories</Text>
      
      <View style={styles.calorieInfo}>
        <Text style={styles.calorieValue}>{consumed}</Text>
        <Text style={styles.calorieUnit}>cal</Text>
      </View>
      
      <ProgressBar 
        progress={progress} 
        color={getProgressColor()}
        style={styles.progressBar}
      />
      
      <View style={styles.goalContainer}>
        <Text style={styles.goalText}>
          {remaining > 0 
            ? `${remaining} cal remaining` 
            : 'Daily goal reached'}
        </Text>
        <Text style={styles.totalGoal}>Goal: {goal} cal</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  calorieInfo: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 12,
  },
  calorieValue: {
    fontSize: 32,
    fontWeight: '700',
  },
  calorieUnit: {
    fontSize: 16,
    marginBottom: 4,
    marginLeft: 4,
    opacity: 0.7,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 12,
  },
  goalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  goalText: {
    fontSize: 14,
    fontWeight: '500',
  },
  totalGoal: {
    fontSize: 12,
    opacity: 0.7,
  },
}); 