import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Text,
  useWindowDimensions,
  useColorScheme,
  Platform,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
import { DraggableWidget, WidgetProps } from './DraggableWidget';

// Define widget interface used in the dashboard
export interface Widget extends Omit<WidgetProps, 'onPositionChange' | 'onRemove' | 'isEditMode'> {
  type: string;
  component: React.ReactNode;
}

interface CustomizableDashboardProps {
  availableWidgets: Widget[];
  storageKey?: string;
}

// Type for saved widget layout
interface SavedWidgetLayout {
  id: string;
  type: string;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export function CustomizableDashboard({
  availableWidgets,
  storageKey = 'dashboard_layout',
}: CustomizableDashboardProps) {
  const [activeWidgets, setActiveWidgets] = useState<Widget[]>([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { width: screenWidth, height: screenHeight } = useWindowDimensions();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  // Set default dashboard height for the container
  const dashboardHeight = Math.max(screenHeight * 0.8, 800);
  
  // Load saved dashboard layout
  useEffect(() => {
    const loadLayout = async () => {
      try {
        const savedLayout = await AsyncStorage.getItem(storageKey);
        
        if (savedLayout) {
          const layoutData: SavedWidgetLayout[] = JSON.parse(savedLayout);
          
          // Map saved layout to available widgets
          const widgetsToShow = layoutData.map(layoutItem => {
            // Find the matching widget from available widgets
            const matchingWidget = availableWidgets.find(
              widget => widget.type === layoutItem.type
            );
            
            if (matchingWidget) {
              return {
                ...matchingWidget,
                id: layoutItem.id,
                initialPosition: layoutItem.position,
              };
            }
            return null;
          }).filter(Boolean) as Widget[];
          
          setActiveWidgets(widgetsToShow);
        } else {
          // If no saved layout, use default widgets
          setActiveWidgets(
            availableWidgets.slice(0, 3).map((widget, index) => ({
              ...widget,
              id: `widget-${widget.type}-${index}`,
              initialPosition: {
                x: 20,
                y: index * 220 + 20,
                width: screenWidth > 600 ? 300 : screenWidth - 40,
                height: 200,
              },
            }))
          );
        }
      } catch (error) {
        console.error('Error loading dashboard layout:', error);
        // Fall back to default layout
        setActiveWidgets(
          availableWidgets.slice(0, 3).map((widget, index) => ({
            ...widget,
            id: `widget-${widget.type}-${index}`,
            initialPosition: {
              x: 20,
              y: index * 220 + 20,
              width: screenWidth > 600 ? 300 : screenWidth - 40,
              height: 200,
            },
          }))
        );
      } finally {
        setIsLoading(false);
      }
    };
    
    loadLayout();
  }, [availableWidgets, storageKey, screenWidth]);
  
  // Save dashboard layout
  const saveLayout = useCallback(async () => {
    try {
      const layoutToSave = activeWidgets.map(widget => ({
        id: widget.id,
        type: widget.type,
        position: widget.initialPosition || {
          x: 0,
          y: 0,
          width: widget.minWidth || 150,
          height: widget.minHeight || 100,
        },
      }));
      
      await AsyncStorage.setItem(storageKey, JSON.stringify(layoutToSave));
    } catch (error) {
      console.error('Error saving dashboard layout:', error);
    }
  }, [activeWidgets, storageKey]);
  
  // Save layout when widgets change
  useEffect(() => {
    if (!isLoading) {
      saveLayout();
    }
  }, [activeWidgets, saveLayout, isLoading]);
  
  // Handle widget position changes
  const handlePositionChange = useCallback(
    (id: string, position: { x: number; y: number; width: number; height: number }) => {
      setActiveWidgets(prevWidgets =>
        prevWidgets.map(widget => {
          if (widget.id === id) {
            return {
              ...widget,
              initialPosition: position,
            };
          }
          return widget;
        })
      );
    },
    []
  );
  
  // Handle widget removal
  const handleRemoveWidget = useCallback((id: string) => {
    setActiveWidgets(prevWidgets => prevWidgets.filter(widget => widget.id !== id));
  }, []);
  
  // Add new widget
  const handleAddWidget = useCallback(
    (widgetType: string) => {
      const widgetToAdd = availableWidgets.find(widget => widget.type === widgetType);
      
      if (widgetToAdd) {
        // Find a suitable y position (below the last widget)
        let maxY = 20;
        activeWidgets.forEach(widget => {
          const widgetBottom =
            (widget.initialPosition?.y || 0) + (widget.initialPosition?.height || 200);
          maxY = Math.max(maxY, widgetBottom + 20);
        });
        
        const newWidget: Widget = {
          ...widgetToAdd,
          id: `widget-${widgetType}-${Date.now()}`,
          initialPosition: {
            x: 20,
            y: maxY,
            width: screenWidth > 600 ? 300 : screenWidth - 40,
            height: 200,
          },
        };
        
        setActiveWidgets(prevWidgets => [...prevWidgets, newWidget]);
      }
    },
    [availableWidgets, activeWidgets, screenWidth]
  );
  
  // Toggle edit mode
  const toggleEditMode = () => {
    setIsEditMode(prev => !prev);
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: isDark ? '#fff' : '#000' }]}>Dashboard</Text>
        <TouchableOpacity
          style={[
            styles.editButton,
            { backgroundColor: isEditMode ? '#007AFF' : isDark ? '#333' : '#f5f5f5' },
          ]}
          onPress={toggleEditMode}
          accessibilityRole="button"
          accessibilityLabel={isEditMode ? 'Exit edit mode' : 'Edit dashboard'}
          accessibilityHint={
            isEditMode
              ? 'Saves the current layout and exits edit mode'
              : 'Allows you to resize, move, and remove widgets'
          }
        >
          <Ionicons
            name={isEditMode ? 'checkmark' : 'pencil'}
            size={18}
            color={isEditMode ? '#fff' : isDark ? '#fff' : '#333'}
          />
          <Text
            style={[
              styles.editButtonText,
              {
                color: isEditMode ? '#fff' : isDark ? '#fff' : '#333',
              },
            ]}
          >
            {isEditMode ? 'Done' : 'Edit'}
          </Text>
        </TouchableOpacity>
      </View>
      
      {isEditMode && (
        <ScrollView
          horizontal
          style={styles.widgetPicker}
          contentContainerStyle={styles.widgetPickerContent}
          showsHorizontalScrollIndicator={false}
        >
          {availableWidgets.map(widget => (
            <TouchableOpacity
              key={widget.type}
              style={[
                styles.widgetOption,
                { backgroundColor: isDark ? '#333' : '#f5f5f5' },
              ]}
              onPress={() => handleAddWidget(widget.type)}
              accessibilityRole="button"
              accessibilityLabel={`Add ${widget.title} widget`}
            >
              <Ionicons name="add-circle" size={16} color="#007AFF" />
              <Text style={[styles.widgetOptionText, { color: isDark ? '#fff' : '#000' }]}>
                {widget.title}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      )}
      
      <ScrollView
        style={[styles.dashboardContainer, { height: dashboardHeight }]}
        contentContainerStyle={styles.dashboardContent}
      >
        <View style={[styles.dashboardCanvas, { minHeight: dashboardHeight }]}>
          {activeWidgets.map(widget => (
            <DraggableWidget
              key={widget.id}
              id={widget.id}
              title={widget.title}
              initialPosition={widget.initialPosition}
              minWidth={widget.minWidth}
              minHeight={widget.minHeight}
              onPositionChange={handlePositionChange}
              onRemove={handleRemoveWidget}
              isEditMode={isEditMode}
            >
              {widget.component}
            </DraggableWidget>
          ))}
        </View>
      </ScrollView>
      
      {activeWidgets.length === 0 && (
        <View style={styles.emptyState}>
          <Ionicons 
            name="grid-outline" 
            size={48} 
            color={isDark ? '#555' : '#ccc'} 
          />
          <Text style={[styles.emptyStateText, { color: isDark ? '#bbb' : '#666' }]}>
            {isEditMode 
              ? 'Tap a widget above to add it to your dashboard' 
              : 'Your dashboard is empty. Tap Edit to add widgets'}
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  editButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  widgetPicker: {
    maxHeight: 60,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  widgetPickerContent: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    flexDirection: 'row',
  },
  widgetOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginHorizontal: 4,
  },
  widgetOptionText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  dashboardContainer: {
    flex: 1,
  },
  dashboardContent: {
    flexGrow: 1,
  },
  dashboardCanvas: {
    width: '100%',
    position: 'relative',
  },
  emptyState: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
  },
});

export default CustomizableDashboard; 