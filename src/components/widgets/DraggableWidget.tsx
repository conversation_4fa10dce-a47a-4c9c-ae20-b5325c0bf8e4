import React, { useState, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  PanResponder,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface WidgetPosition {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface WidgetProps {
  id: string;
  title: string;
  initialPosition?: WidgetPosition;
  minWidth?: number;
  minHeight?: number;
  onPositionChange?: (id: string, position: WidgetPosition) => void;
  onRemove?: (id: string) => void;
  isEditMode?: boolean;
  children?: React.ReactNode;
}

export function DraggableWidget({
  id,
  title,
  initialPosition,
  minWidth = 150,
  minHeight = 100,
  onPositionChange,
  onRemove,
  isEditMode = false,
  children,
}: WidgetProps) {
  const screenWidth = Dimensions.get('window').width;
  const initialPos = initialPosition || {
    x: 0,
    y: 0,
    width: minWidth,
    height: minHeight,
  };
  
  // Animation values for position and size
  const pan = useRef(new Animated.ValueXY({ x: initialPos.x, y: initialPos.y })).current;
  const [width, setWidth] = useState(initialPos.width);
  const [height, setHeight] = useState(initialPos.height);
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  
  // Track position for callback
  const positionRef = useRef<WidgetPosition>({
    x: initialPos.x,
    y: initialPos.y,
    width,
    height,
  });

  // Update position ref when position changes
  const updatePositionRef = useCallback((x: number, y: number, w: number, h: number) => {
    positionRef.current = { x, y, width: w, height: h };
    
    // Notify parent about position change
    if (onPositionChange) {
      onPositionChange(id, positionRef.current);
    }
  }, [id, onPositionChange]);
  
  // Reset position animation to match the actual position
  // Needed after layout changes
  const resetAnimation = () => {
    pan.setValue({ x: positionRef.current.x, y: positionRef.current.y });
  };

  // Get current position values from the Animated.ValueXY
  const getPosition = () => {
    const position = { x: 0, y: 0 };
    // Extract values safely
    pan.x.addListener(value => position.x = value.value);
    pan.y.addListener(value => position.y = value.value);
    // Clean up listeners
    pan.x.removeAllListeners();
    pan.y.removeAllListeners();
    return position;
  };

  // Pan responder for dragging
  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => isEditMode,
    onMoveShouldSetPanResponder: () => isEditMode && !isResizing,
    onPanResponderGrant: () => {
      setIsDragging(true);
      pan.setOffset({
        x: getPosition().x,
        y: getPosition().y,
      });
      pan.setValue({ x: 0, y: 0 });
    },
    onPanResponderMove: Animated.event(
      [null, { dx: pan.x, dy: pan.y }],
      { useNativeDriver: false }
    ),
    onPanResponderRelease: (_, gestureState) => {
      pan.flattenOffset();
      setIsDragging(false);
      
      // Calculate new position after drag
      const position = getPosition();
      const x = Math.max(0, Math.min(position.x, screenWidth - width));
      const y = Math.max(0, position.y);
      
      // Animate to valid position
      Animated.spring(pan, {
        toValue: { x, y },
        useNativeDriver: false,
        friction: 5,
      }).start();
      
      updatePositionRef(x, y, width, height);
    },
  });

  // Resize handler
  const resizeResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => isEditMode,
    onMoveShouldSetPanResponder: () => isEditMode,
    onPanResponderGrant: () => {
      setIsResizing(true);
    },
    onPanResponderMove: (_, gestureState) => {
      // Calculate new width and height based on gesture
      const newWidth = Math.max(minWidth, width + gestureState.dx);
      const newHeight = Math.max(minHeight, height + gestureState.dy);
      
      // Update dimensions
      setWidth(newWidth);
      setHeight(newHeight);
    },
    onPanResponderRelease: () => {
      setIsResizing(false);
      
      // Update position ref with new dimensions
      const position = getPosition();
      updatePositionRef(position.x, position.y, width, height);
    },
  });

  // Handle component removal
  const handleRemove = () => {
    if (onRemove) {
      onRemove(id);
    }
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          width,
          height,
          transform: [{ translateX: pan.x }, { translateY: pan.y }],
          borderColor: isDragging || isResizing ? '#007AFF' : 'transparent',
          borderWidth: isEditMode ? 1 : 0,
        },
      ]}
      {...panResponder.panHandlers}
    >
      {/* Widget Header with drag handle */}
      <View
        style={[
          styles.header,
          {
            backgroundColor: isDragging ? 'rgba(0, 122, 255, 0.1)' : 'transparent',
          },
        ]}
      >
        <Text style={styles.title} numberOfLines={1}>
          {title}
        </Text>
        
        {isEditMode && (
          <View style={styles.controls}>
            <TouchableOpacity
              style={styles.removeButton}
              onPress={handleRemove}
              hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
            >
              <Ionicons name="close-circle" size={24} color="#FF3B30" />
            </TouchableOpacity>
            <Ionicons name="grid-outline" size={18} color="#8E8E93" />
          </View>
        )}
      </View>
      
      {/* Widget Content */}
      <View style={styles.content}>{children}</View>
      
      {/* Resize handle */}
      {isEditMode && (
        <View
          style={styles.resizeHandle}
          {...resizeResponder.panHandlers}
        >
          <Ionicons name="resize" size={16} color="#8E8E93" />
        </View>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    backgroundColor: '#fff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  header: {
    height: 44,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  removeButton: {
    marginRight: 8,
  },
  content: {
    flex: 1,
    padding: 12,
  },
  resizeHandle: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderTopLeftRadius: 8,
  },
});

export default DraggableWidget; 