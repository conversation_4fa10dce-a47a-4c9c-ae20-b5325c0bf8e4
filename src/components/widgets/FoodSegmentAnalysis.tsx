import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { FoodSegment } from '@/services/vision/segmentationService';
import Colors from '@/constants/Colors';
import { useTheme } from '@/contexts/ThemeContext';

interface FoodSegmentAnalysisProps {
  segment: FoodSegment;
  nutritionData?: {
    name: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber?: number;
    estimatedAmount?: string;
  };
  onClose?: () => void;
  onApply?: (updatedData: any) => void;
}

/**
 * Component to display detailed analysis for a selected food segment
 * Shows nutritional information and allows corrections
 */
export function FoodSegmentAnalysis({
  segment,
  nutritionData,
  onClose,
  onApply,
}: FoodSegmentAnalysisProps) {
  const displayName = nutritionData?.name || segment.labeledAs || 'Unknown food';
  const { colors, isDark } = useTheme();
  
  // Calculate calorie density category
  const getCalorieDensity = (calories: number) => {
    if (calories <= 100) return { label: 'Low', color: '#22c55e' }; // Green
    if (calories <= 250) return { label: 'Moderate', color: '#eab308' }; // Yellow
    if (calories <= 400) return { label: 'High', color: '#f97316' }; // Orange
    return { label: 'Very High', color: '#ef4444' }; // Red
  };
  
  const calorieDensity = nutritionData ? getCalorieDensity(nutritionData.calories) : null;
  
  // Calculate macronutrient percentages
  const calculateMacroPercentages = () => {
    if (!nutritionData) return null;
    
    const { protein, carbs, fat } = nutritionData;
    const totalCalories = (protein * 4) + (carbs * 4) + (fat * 9);
    
    if (totalCalories === 0) return null;
    
    return {
      proteinPct: Math.round((protein * 4 / totalCalories) * 100),
      carbsPct: Math.round((carbs * 4 / totalCalories) * 100),
      fatPct: Math.round((fat * 9 / totalCalories) * 100),
    };
  };
  
  const macroPercentages = calculateMacroPercentages();
  
  return (
    <View style={[styles.container, { backgroundColor: isDark ? colors.card : '#FFF', borderColor: colors.border }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>{displayName}</Text>
        {onClose && (
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>
        )}
      </View>
      
      <View style={styles.content}>
        {nutritionData ? (
          <>
            {/* Enhanced calorie display */}
            <View style={[styles.calorieCard, { backgroundColor: isDark ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)' }]}>
              <View style={styles.calorieHeader}>
                <Text style={[styles.calorieLabel, { color: colors.text }]}>Calories:</Text>
                <View style={[styles.calorieBadge, { backgroundColor: calorieDensity?.color + '20' }]}>
                  <Text style={[styles.calorieDensity, { color: calorieDensity?.color }]}>
                    {calorieDensity?.label}
                  </Text>
                </View>
            </View>
              <Text style={[styles.calorieValue, { color: calorieDensity?.color }]}>
                {nutritionData.calories} kcal
              </Text>
              
              {/* Calorie context */}
              <Text style={[styles.calorieContext, { color: colors.textSecondary }]}>
                {nutritionData.calories < 150 && "Low calorie option, good for weight management"}
                {nutritionData.calories >= 150 && nutritionData.calories < 300 && "Moderate calories, suitable for a light meal or snack"}
                {nutritionData.calories >= 300 && nutritionData.calories < 500 && "Substantial calories, consider as a main meal component"}
                {nutritionData.calories >= 500 && "High calorie option, consider portion size carefully"}
              </Text>
            </View>
            
            {/* Macronutrient breakdown */}
            {macroPercentages && (
              <View style={styles.macroBreakdown}>
                <Text style={[styles.macroTitle, { color: colors.text }]}>Macronutrient Breakdown</Text>
                <View style={styles.macroBarContainer}>
                  <View style={[styles.macroBar, styles.proteinBar, { width: `${macroPercentages.proteinPct}%` }]} />
                  <View style={[styles.macroBar, styles.carbsBar, { width: `${macroPercentages.carbsPct}%` }]} />
                  <View style={[styles.macroBar, styles.fatBar, { width: `${macroPercentages.fatPct}%` }]} />
                </View>
                <View style={styles.macroLegend}>
                  <View style={styles.macroLegendItem}>
                    <View style={[styles.macroLegendColor, styles.proteinBar]} />
                    <Text style={[styles.macroLegendText, { color: colors.textSecondary }]}>Protein {macroPercentages.proteinPct}%</Text>
                  </View>
                  <View style={styles.macroLegendItem}>
                    <View style={[styles.macroLegendColor, styles.carbsBar]} />
                    <Text style={[styles.macroLegendText, { color: colors.textSecondary }]}>Carbs {macroPercentages.carbsPct}%</Text>
                  </View>
                  <View style={styles.macroLegendItem}>
                    <View style={[styles.macroLegendColor, styles.fatBar]} />
                    <Text style={[styles.macroLegendText, { color: colors.textSecondary }]}>Fat {macroPercentages.fatPct}%</Text>
                  </View>
                </View>
            </View>
            )}
            
            {/* Detailed nutrition info */}
            <View style={styles.nutritionDetails}>
            <View style={styles.nutritionRow}>
                <Text style={[styles.label, { color: colors.text }]}>Protein:</Text>
                <Text style={[styles.value, { color: colors.text }]}>{nutritionData.protein}g</Text>
            </View>
            
              <View style={styles.nutritionRow}>
                <Text style={[styles.label, { color: colors.text }]}>Carbs:</Text>
                <Text style={[styles.value, { color: colors.text }]}>{nutritionData.carbs}g</Text>
              </View>
            
              <View style={styles.nutritionRow}>
                <Text style={[styles.label, { color: colors.text }]}>Fat:</Text>
                <Text style={[styles.value, { color: colors.text }]}>{nutritionData.fat}g</Text>
              </View>
              
              {nutritionData.fiber !== undefined && (
                <View style={styles.nutritionRow}>
                  <Text style={[styles.label, { color: colors.text }]}>Fiber:</Text>
                  <Text style={[styles.value, { color: colors.text }]}>{nutritionData.fiber}g</Text>
                </View>
              )}
            </View>
            
            {onApply && (
              <TouchableOpacity 
                style={[styles.applyButton, { backgroundColor: colors.primary }]} 
                onPress={onApply}
              >
                <Text style={styles.applyButtonText}>Apply Changes</Text>
              </TouchableOpacity>
            )}
          </>
        ) : (
          <View style={styles.noDataContainer}>
            <Text style={[styles.noDataText, { color: colors.textSecondary }]}>
              No nutrition data available for this item
            </Text>
          </View>
        )}
      </View>
      
      <View style={styles.segmentInfo}>
        <Text style={styles.segmentInfoText}>
          Confidence: {((segment.score ?? 0) * 100).toFixed(0)}%
        </Text>
        <Text style={styles.segmentInfoText}>
          Area: {segment.area} px²
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.1)',
  },
  closeButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  content: {
    padding: 16,
  },
  // Enhanced calorie styling
  calorieCard: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  calorieHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  calorieLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  calorieBadge: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
  },
  calorieDensity: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  calorieValue: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  calorieContext: {
    fontSize: 12,
    lineHeight: 16,
  },
  // Macro breakdown
  macroBreakdown: {
    marginBottom: 16,
  },
  macroTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  macroBarContainer: {
    height: 12,
    flexDirection: 'row',
    borderRadius: 6,
    overflow: 'hidden',
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  macroBar: {
    height: '100%',
  },
  proteinBar: {
    backgroundColor: '#3b82f6', // Blue
  },
  carbsBar: {
    backgroundColor: '#8b5cf6', // Purple
  },
  fatBar: {
    backgroundColor: '#f97316', // Orange
  },
  macroLegend: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  macroLegendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  macroLegendColor: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 4,
  },
  macroLegendText: {
    fontSize: 12,
  },
  // Detailed nutrition info
  nutritionDetails: {
    marginBottom: 16,
  },
  nutritionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
  },
  value: {
    fontSize: 14,
  },
  noDataContainer: {
    padding: 20,
    alignItems: 'center',
  },
  noDataText: {
    fontSize: 14,
    textAlign: 'center',
  },
  applyButton: {
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  applyButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
  segmentInfo: {
    marginTop: 12,
    padding: 8,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  segmentInfoText: {
    fontSize: 12,
    color: '#666',
  },
});

export default FoodSegmentAnalysis; 