import React, { useState, useEffect } from 'react';
import { View, Image, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { FoodSegment } from '@/services/vision/segmentationService';
import Colors from '@/constants/Colors';

interface FoodSegmentationDisplayProps {
  imageUri: string;
  segments: FoodSegment[];
  onSegmentPress?: (segment: FoodSegment) => void;
  showLabels?: boolean;
  highlightSelected?: boolean;
  selectedSegmentId?: string;
  style?: any;
}

/**
 * Component to display food segmentation results
 * Shows the original image with overlay of bounding boxes and/or segmentation masks
 */
export function FoodSegmentationDisplay({
  imageUri,
  segments,
  onSegmentPress,
  showLabels = true,
  highlightSelected = true,
  selectedSegmentId,
  style,
}: FoodSegmentationDisplayProps) {
  // Use light theme colors as default
  const colors = Colors.light;
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  
  // Get image dimensions to properly scale the overlay
  useEffect(() => {
    if (imageUri) {
      Image.getSize(imageUri, (width, height) => {
        setImageSize({ width, height });
      }, (error) => {
        console.error('Error getting image size:', error);
      });
    }
  }, [imageUri]);
  
  // Calculate the scale factor to map from image coordinates to display coordinates
  const getScaleFactor = () => {
    if (imageSize.width === 0 || containerSize.width === 0) return { x: 1, y: 1 };
    
    const scaleX = containerSize.width / imageSize.width;
    const scaleY = containerSize.height / imageSize.height;
    return { x: scaleX, y: scaleY };
  };
  
  // Generate a unique color for each segment
  const getSegmentColor = (index: number, isSelected: boolean) => {
    const colors = [
      '#FF6B6B', '#48DBFB', '#1DD1A1', '#FFC048', '#9C88FF',
      '#F368E0', '#FF9F43', '#00D2D3', '#54A0FF', '#5F27CD'
    ];
    
    const baseColor = colors[index % colors.length];
    
    if (isSelected) {
      return `${baseColor}`;
    }
    return `${baseColor}80`; // 50% transparency
  };
  
  const handleLayout = (event: any) => {
    const { width, height } = event.nativeEvent.layout;
    setContainerSize({ width, height });
  };
  
  return (
    <View 
      style={[styles.container, style]} 
      onLayout={handleLayout}
    >
      {/* Original image */}
      <Image
        source={{ uri: imageUri }}
        style={styles.image}
        resizeMode="contain"
      />
      
      {/* Overlay for bounding boxes and masks */}
      <View style={styles.overlay}>
        {segments.map((segment, index) => {
          const isSelected = segment.id === selectedSegmentId;
          const scale = getScaleFactor();
          
          // Calculate the position and size of the bounding box
          const boxStyle = {
            left: segment.boundingBox.x * containerSize.width,
            top: segment.boundingBox.y * containerSize.height,
            width: segment.boundingBox.width * containerSize.width,
            height: segment.boundingBox.height * containerSize.height,
            borderColor: getSegmentColor(index, isSelected),
            backgroundColor: isSelected ? `${getSegmentColor(index, true)}20` : 'transparent'
          };
          
          return (
            <TouchableOpacity
              key={segment.id}
              style={[styles.boundingBox, boxStyle]}
              onPress={() => onSegmentPress && onSegmentPress(segment)}
              activeOpacity={0.8}
            >
              {showLabels && segment.labeledAs && (
                <View style={[
                  styles.label, 
                  { backgroundColor: getSegmentColor(index, isSelected) }
                ]}>
                  <Text style={styles.labelText}>
                    {segment.labeledAs}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    width: '100%',
    aspectRatio: 1,
    overflow: 'hidden',
    borderRadius: 12,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    // pointer-events must be handled in web-specific code if needed
  },
  boundingBox: {
    position: 'absolute',
    borderWidth: 2,
    borderStyle: 'solid',
  },
  label: {
    position: 'absolute',
    top: -25,
    left: 0,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  labelText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  maskOverlay: {
    position: 'absolute',
    left: 0,
    top: 0,
    width: '100%',
    height: '100%',
  }
});

export default FoodSegmentationDisplay; 