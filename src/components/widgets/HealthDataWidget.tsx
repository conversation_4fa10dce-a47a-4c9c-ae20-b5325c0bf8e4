import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import { Feather } from '@expo/vector-icons';

interface HealthDataWidgetProps {
  steps: number;
  heartRate: number;
  lastUpdated: string;
  theme?: 'light' | 'dark';
  size?: 'small' | 'medium' | 'large';
}

/**
 * A widget component for displaying health data on the home screen
 * This serves as a template for both iOS and Android widgets
 */
export default function HealthDataWidget({
  steps,
  heartRate,
  lastUpdated,
  theme = 'light',
  size = 'medium'
}: HealthDataWidgetProps) {
  // Determine colors based on theme
  const colors = {
    background: theme === 'light' ? '#FFFFFF' : '#1F2937',
    text: theme === 'light' ? '#1F2937' : '#F9FAFB',
    textSecondary: theme === 'light' ? '#6B7280' : '#9CA3AF',
    primary: theme === 'light' ? '#3B82F6' : '#60A5FA',
    heartRate: '#EF4444',
    border: theme === 'light' ? '#E5E7EB' : '#374151',
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Determine styles based on size
  const getContainerStyle = () => {
    switch (size) {
      case 'small':
        return { width: 150, height: 150 };
      case 'large':
        return { width: 320, height: 155 };
      default: // medium
        return { width: 300, height: 150 };
    }
  };

  // Layout for small widget
  if (size === 'small') {
    return (
      <View style={[
        styles.container,
        getContainerStyle(),
        { backgroundColor: colors.background, borderColor: colors.border }
      ]}>
        <Text style={[styles.title, { color: colors.text }]}>Health</Text>
        
        <View style={styles.smallMetricRow}>
          <Feather name="activity" size={18} color={colors.primary} />
          <Text style={[styles.metricValue, { color: colors.primary }]}>
            {steps.toLocaleString()}
          </Text>
        </View>
        
        <View style={styles.smallMetricRow}>
          <Feather name="heart" size={18} color={colors.heartRate} />
          <Text style={[styles.metricValue, { color: colors.heartRate }]}>
            {heartRate.toLocaleString()} bpm
          </Text>
        </View>
        
        <Text style={[styles.smallUpdated, { color: colors.textSecondary }]}>
          Updated {formatDate(lastUpdated)}
        </Text>
      </View>
    );
  }

  // Layout for medium and large widgets
  return (
    <View style={[
      styles.container,
      getContainerStyle(),
      { backgroundColor: colors.background, borderColor: colors.border }
    ]}>
      <Text style={[styles.title, { color: colors.text }]}>Health Data</Text>
      
      <View style={styles.metricsContainer}>
        <View style={styles.metricItem}>
          <View style={styles.metricHeader}>
            <Feather name="activity" size={22} color={colors.primary} />
            <Text style={[styles.metricTitle, { color: colors.text }]}>Steps</Text>
          </View>
          <Text style={[styles.metricValue, { color: colors.primary, fontSize: 28 }]}>
            {steps.toLocaleString()}
          </Text>
        </View>
        
        <View style={[
          styles.metricSeparator,
          { backgroundColor: colors.border }
        ]} />
        
        <View style={styles.metricItem}>
          <View style={styles.metricHeader}>
            <Feather name="heart" size={22} color={colors.heartRate} />
            <Text style={[styles.metricTitle, { color: colors.text }]}>Heart Rate</Text>
          </View>
          <Text style={[styles.metricValue, { color: colors.heartRate, fontSize: 28 }]}>
            {heartRate} <Text style={{ fontSize: 16 }}>bpm</Text>
          </Text>
        </View>
      </View>
      
      <Text style={[styles.updatedTime, { color: colors.textSecondary }]}>
        Updated {formatDate(lastUpdated)}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 20,
    borderWidth: 1,
    justifyContent: 'space-between',
    overflow: 'hidden',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  metricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
    marginBottom: 8,
  },
  metricItem: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricTitle: {
    fontSize: 16,
    marginLeft: 8,
    fontWeight: '500',
  },
  metricValue: {
    fontSize: 24,
    fontWeight: '700',
  },
  metricSeparator: {
    width: 1,
    alignSelf: 'stretch',
    marginHorizontal: 16,
  },
  updatedTime: {
    fontSize: 12,
    textAlign: 'center',
  },
  // Small widget styles
  smallMetricRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  smallUpdated: {
    fontSize: 10,
    textAlign: 'center',
    marginTop: 'auto',
  },
}); 