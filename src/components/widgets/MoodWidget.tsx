import React, { ReactElement } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface MoodWidgetProps {
  mood: 'Sad' | 'Okay' | 'Good' | 'Happy' | 'Excited';
}

export function MoodWidget({ mood }: MoodWidgetProps) {
  // Get emoji and color based on mood
  const getMoodDetails = () => {
    switch (mood) {
      case 'Sad':
        return { icon: "sad-outline", color: '#7986CB', emoji: '😔', description: 'Feeling down' };
      case 'Okay':
        return { icon: "remove-outline", color: '#9E9E9E', emoji: '😐', description: 'Just okay' };
      case 'Good':
        return { icon: "happy-outline", color: '#4CAF50', emoji: '🙂', description: 'Feeling good' };
      case 'Happy':
        return { icon: "sunny-outline", color: '#FFC107', emoji: '😊', description: 'Quite happy' };
      case 'Excited':
        return { icon: "star", color: '#FF9800', emoji: '🤩', description: 'Excited!' };
      default:
        return { icon: "happy-outline", color: '#4CAF50', emoji: '🙂', description: 'Feeling good' };
    }
  };
  
  // Render recent mood history (just sample data for now)
  const renderMoodHistory = (): ReactElement[] => {
    const moods = ['Happy', 'Good', 'Good', 'Sad', 'Okay'];
    const history: ReactElement[] = [];
    
    for (let i = 0; i < moods.length; i++) {
      const currentMood = moods[i] as MoodWidgetProps['mood'];
      const { icon, color } = getMoodDetailsForMood(currentMood);
      
      history.push(
        <View key={i} style={styles.historyItem}>
          <Ionicons name={icon as any} size={18} color={color} />
        </View>
      );
    }
    
    return history;
  };
  
  // Helper function to get mood details for history
  const getMoodDetailsForMood = (mood: MoodWidgetProps['mood']) => {
    switch (mood) {
      case 'Sad': return { icon: "sad-outline", color: '#7986CB' };
      case 'Okay': return { icon: "remove-outline", color: '#9E9E9E' };
      case 'Good': return { icon: "happy-outline", color: '#4CAF50' };
      case 'Happy': return { icon: "sunny-outline", color: '#FFC107' };
      case 'Excited': return { icon: "star", color: '#FF9800' };
      default: return { icon: "happy-outline", color: '#4CAF50' };
    }
  };
  
  const { emoji, color, description } = getMoodDetails();
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Mood Tracker</Text>
      
      <View style={styles.moodContainer}>
        <Text style={styles.emoji}>{emoji}</Text>
        <Text style={[styles.moodText, { color }]}>{mood}</Text>
        <Text style={styles.description}>{description}</Text>
      </View>
      
      <View style={styles.historyContainer}>
        <Text style={styles.historyTitle}>Recent Moods:</Text>
        <View style={styles.historyIcons}>
          {renderMoodHistory()}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  moodContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  emoji: {
    fontSize: 40,
    marginBottom: 8,
  },
  moodText: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    opacity: 0.7,
  },
  historyContainer: {
    marginTop: 'auto',
  },
  historyTitle: {
    fontSize: 14,
    marginBottom: 8,
  },
  historyIcons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  historyItem: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    justifyContent: 'center',
    alignItems: 'center',
  },
}); 