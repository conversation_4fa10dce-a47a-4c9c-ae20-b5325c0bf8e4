import React from 'react';
import { View, Text, StyleSheet, useColorScheme } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Utility to get chart width based on container width
const getChartWidth = (containerWidth: number) => {
  // Leave some padding on each side
  return Math.max(containerWidth - 32, 200);
};

interface WidgetContainerProps {
  children: React.ReactNode;
  icon?: string;
  iconColor?: string;
}

// A wrapper component for all widgets to ensure consistent styling
export function WidgetContainer({ children, icon, iconColor = '#007AFF' }: WidgetContainerProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  return (
    <View style={[styles.widgetContainer, { backgroundColor: isDark ? '#1c1c1e' : '#fff' }]}>
      {icon && (
        <View style={styles.iconContainer}>
          <Ionicons name={icon as any} size={20} color={iconColor} />
        </View>
      )}
      {children}
    </View>
  );
}

// A simple stats widget with a number and label
export function StatsWidget({
  value,
  label,
  icon,
  unit,
  iconColor,
}: {
  value: number;
  label: string;
  icon?: string;
  unit?: string;
  iconColor?: string;
}) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  return (
    <WidgetContainer icon={icon} iconColor={iconColor}>
      <View style={styles.statsContent}>
        <Text style={[styles.statsValue, { color: isDark ? '#fff' : '#000' }]}>
          {value}
          {unit && (
            <Text style={[styles.statsUnit, { color: isDark ? '#bbb' : '#666' }]}>{unit}</Text>
          )}
        </Text>
        <Text style={[styles.statsLabel, { color: isDark ? '#bbb' : '#666' }]}>{label}</Text>
      </View>
    </WidgetContainer>
  );
}

// Simple line chart widget using mock visualization
export function LineChartWidget({
  data,
  title,
  containerWidth,
}: {
  data: number[];
  title: string;
  containerWidth: number;
}) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const chartWidth = getChartWidth(containerWidth);
  
  // Find min and max values to normalize height
  const maxValue = Math.max(...data);
  const minValue = Math.min(...data);
  const range = maxValue - minValue;
  
  return (
    <WidgetContainer icon="analytics-outline">
      <Text style={[styles.chartTitle, { color: isDark ? '#fff' : '#000' }]}>{title}</Text>
      <View style={styles.chartContainer}>
        <View style={{ width: chartWidth, height: 130 }}>
          <View style={styles.mockChart}>
            {data.map((value, index) => {
              // Normalize the height from 10% to 90% of chart height
              const normalizedHeight = range === 0 
                ? 50 
                : 10 + ((value - minValue) / range) * 80;
              
              return (
                <View 
                  key={index}
                  style={[
                    styles.mockChartBar,
                    {
                      height: `${normalizedHeight}%`,
                      backgroundColor: '#007AFF',
                    }
                  ]}
                />
              );
            })}
          </View>
        </View>
      </View>
    </WidgetContainer>
  );
}

// Simple bar chart widget using mock visualization
export function BarChartWidget({
  data,
  labels,
  title,
  containerWidth,
}: {
  data: number[];
  labels: string[];
  title: string;
  containerWidth: number;
}) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const chartWidth = getChartWidth(containerWidth);
  
  // Find max value to normalize height
  const maxValue = Math.max(...data);
  
  return (
    <WidgetContainer icon="bar-chart-outline">
      <Text style={[styles.chartTitle, { color: isDark ? '#fff' : '#000' }]}>{title}</Text>
      <View style={styles.chartContainer}>
        <View style={{ width: chartWidth, height: 130 }}>
          <View style={styles.mockBarChart}>
            {data.map((value, index) => {
              const normalizedHeight = maxValue === 0 
                ? 0 
                : (value / maxValue) * 100;
                
              return (
                <View key={index} style={styles.mockBarChartColumn}>
                  <View 
                    style={[
                      styles.mockBarChartBar,
                      {
                        height: `${normalizedHeight}%`,
                        backgroundColor: '#007AFF',
                      }
                    ]}
                  />
                  {labels[index] && (
                    <Text 
                      style={[
                        styles.mockBarChartLabel, 
                        { color: isDark ? '#bbb' : '#666' }
                      ]}
                      numberOfLines={1}
                    >
                      {labels[index]}
                    </Text>
                  )}
                </View>
              );
            })}
          </View>
        </View>
      </View>
    </WidgetContainer>
  );
}

// Simple pie chart widget using mock visualization
export function PieChartWidget({
  data,
  title,
  containerWidth,
}: {
  data: { name: string; value: number; color: string }[];
  title: string;
  containerWidth: number;
}) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const chartWidth = getChartWidth(containerWidth);
  
  // Calculate total for percentages
  const total = data.reduce((sum, item) => sum + item.value, 0);
  
  return (
    <WidgetContainer icon="pie-chart-outline">
      <Text style={[styles.chartTitle, { color: isDark ? '#fff' : '#000' }]}>{title}</Text>
      <View style={styles.chartContainer}>
        <View style={{ width: chartWidth, height: 130 }}>
          <View style={styles.mockPieChart}>
            <View style={styles.mockPieContainer}>
              {data.map((item, index) => {
                const percentage = total === 0 ? 0 : (item.value / total) * 100;
                
                // Skip rendering very small segments
                if (percentage < 1) return null;
                
                return (
                  <View 
                    key={index}
                    style={[
                      styles.mockPieSegment,
                      {
                        backgroundColor: item.color,
                        width: `${percentage}%`,
                      }
                    ]}
                  />
                );
              })}
            </View>
            
            <View style={styles.mockPieLegend}>
              {data.map((item, index) => (
                <View key={index} style={styles.mockPieLegendItem}>
                  <View 
                    style={[
                      styles.mockPieLegendColor,
                      { backgroundColor: item.color }
                    ]}
                  />
                  <Text 
                    style={[
                      styles.mockPieLegendText,
                      { color: isDark ? '#fff' : '#000' }
                    ]}
                    numberOfLines={1}
                  >
                    {item.name}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        </View>
      </View>
    </WidgetContainer>
  );
}

// A list widget for items like tasks, meals, etc.
export function ListWidget<T>({
  items,
  title,
  icon,
  renderItem,
}: {
  items: T[];
  title: string;
  icon?: string;
  renderItem: (item: T, index: number) => React.ReactNode;
}) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  return (
    <WidgetContainer icon={icon}>
      <Text style={[styles.widgetTitle, { color: isDark ? '#fff' : '#000' }]}>{title}</Text>
      <View style={styles.listContainer}>
        {items.length > 0 ? (
          items.slice(0, 5).map((item, index) => (
            <View key={index} style={styles.listItem}>
              {renderItem(item, index)}
            </View>
          ))
        ) : (
          <Text style={[styles.emptyText, { color: isDark ? '#bbb' : '#666' }]}>No items</Text>
        )}
      </View>
    </WidgetContainer>
  );
}

// A progress widget with completion percentage
export function ProgressWidget({
  progress,
  title,
  icon,
  color = '#007AFF',
}: {
  progress: number; // 0 to 1
  title: string;
  icon?: string;
  color?: string;
}) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const percentage = Math.floor(progress * 100);
  
  return (
    <WidgetContainer icon={icon}>
      <Text style={[styles.widgetTitle, { color: isDark ? '#fff' : '#000' }]}>{title}</Text>
      <View style={styles.progressContainer}>
        <View style={[styles.progressBar, { backgroundColor: isDark ? '#333' : '#e0e0e0' }]}>
          <View
            style={[
              styles.progressFill,
              {
                width: `${percentage}%`,
                backgroundColor: color,
              },
            ]}
          />
        </View>
        <Text style={[styles.progressText, { color: isDark ? '#fff' : '#000' }]}>
          {percentage}%
        </Text>
      </View>
    </WidgetContainer>
  );
}

// A calendar/date widget
export function DateWidget({ icon }: { icon?: string }) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const today = new Date();
  const date = today.getDate();
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  const month = monthNames[today.getMonth()];
  const year = today.getFullYear();
  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const day = dayNames[today.getDay()];
  
  return (
    <WidgetContainer icon={icon || 'calendar-outline'}>
      <View style={styles.dateContainer}>
        <Text style={[styles.dayText, { color: isDark ? '#fff' : '#000' }]}>{day}</Text>
        <Text style={[styles.dateNumber, { color: isDark ? '#fff' : '#000' }]}>{date}</Text>
        <Text style={[styles.monthYear, { color: isDark ? '#bbb' : '#666' }]}>
          {month} {year}
        </Text>
      </View>
    </WidgetContainer>
  );
}

// Define styles for the widgets
const styles = StyleSheet.create({
  widgetContainer: {
    flex: 1,
    borderRadius: 12,
    padding: 16,
    position: 'relative',
  },
  iconContainer: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  widgetTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  // Stats widget styles
  statsContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statsValue: {
    fontSize: 32,
    fontWeight: '700',
    textAlign: 'center',
  },
  statsUnit: {
    fontSize: 18,
    fontWeight: '400',
  },
  statsLabel: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
  },
  // Chart widget styles
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  chartContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  // Mock line chart styles
  mockChart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    height: '100%',
    width: '100%',
    paddingBottom: 20,
  },
  mockChartBar: {
    width: 4,
    borderRadius: 2,
    marginHorizontal: 3,
  },
  // Mock bar chart styles
  mockBarChart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-around',
    height: '100%',
    width: '100%',
    paddingBottom: 20,
  },
  mockBarChartColumn: {
    alignItems: 'center',
    justifyContent: 'flex-end',
    height: '100%',
    flex: 1,
  },
  mockBarChartBar: {
    width: '60%',
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
  },
  mockBarChartLabel: {
    fontSize: 10,
    marginTop: 4,
    textAlign: 'center',
  },
  // Mock pie chart styles
  mockPieChart: {
    flexDirection: 'row',
    height: '100%',
    width: '100%',
  },
  mockPieContainer: {
    flexDirection: 'row',
    height: 80,
    width: 80,
    borderRadius: 40,
    overflow: 'hidden',
    alignSelf: 'center',
  },
  mockPieSegment: {
    height: '100%',
  },
  mockPieLegend: {
    flex: 1,
    marginLeft: 16,
    justifyContent: 'center',
  },
  mockPieLegendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  mockPieLegendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 6,
  },
  mockPieLegendText: {
    fontSize: 12,
  },
  // List widget styles
  listContainer: {
    flex: 1,
  },
  listItem: {
    paddingVertical: 6,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 12,
  },
  // Progress widget styles
  progressContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  progressBar: {
    height: 16,
    width: '100%',
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 8,
  },
  progressText: {
    fontSize: 16,
    fontWeight: '600',
  },
  // Date widget styles
  dateContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  dayText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  dateNumber: {
    fontSize: 36,
    fontWeight: '700',
  },
  monthYear: {
    fontSize: 14,
    marginTop: 4,
  },
});

export default {
  StatsWidget,
  LineChartWidget,
  BarChartWidget,
  PieChartWidget,
  ListWidget,
  ProgressWidget,
  DateWidget,
}; 