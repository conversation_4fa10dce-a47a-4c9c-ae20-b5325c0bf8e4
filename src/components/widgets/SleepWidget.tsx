import React, { ReactElement } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface SleepWidgetProps {
  hours: number;
  quality: 'Poor' | 'Fair' | 'Good' | 'Excellent';
}

export function SleepWidget({ hours, quality }: SleepWidgetProps) {
  // Format hours (e.g., 7.5 -> "7h 30m")
  const formatHours = (value: number) => {
    const wholeHours = Math.floor(value);
    const minutes = Math.round((value - wholeHours) * 60);
    return `${wholeHours}h ${minutes > 0 ? `${minutes}m` : ''}`;
  };
  
  // Get color based on quality
  const getQualityColor = () => {
    switch (quality) {
      case 'Poor': return '#F44336'; // Red
      case 'Fair': return '#FF9800'; // Orange
      case 'Good': return '#4CAF50'; // Green
      case 'Excellent': return '#2196F3'; // Blue
      default: return '#4CAF50';
    }
  };
  
  // Get stars based on quality
  const getQualityStars = () => {
    switch (quality) {
      case 'Poor': return 1;
      case 'Fair': return 2;
      case 'Good': return 3;
      case 'Excellent': return 4;
      default: return 3;
    }
  };
  
  // Render quality stars
  const renderStars = (): ReactElement[] => {
    const stars: ReactElement[] = [];
    const qualityStars = getQualityStars();
    
    for (let i = 0; i < 4; i++) {
      stars.push(
        <Ionicons
          key={i}
          name={i < qualityStars ? "star" : "star-outline"}
          size={18}
          color={getQualityColor()}
          style={styles.starIcon}
        />
      );
    }
    
    return stars;
  };
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Sleep</Text>
      
      <View style={styles.sleepInfo}>
        <Text style={styles.sleepValue}>{formatHours(hours)}</Text>
        <Text style={styles.sleepLabel}>last night</Text>
      </View>
      
      <View style={styles.qualityContainer}>
        <Text style={styles.qualityLabel}>Quality:</Text>
        <View style={styles.starsContainer}>
          {renderStars()}
        </View>
      </View>
      
      <View style={styles.qualityTextContainer}>
        <Text style={[styles.qualityText, { color: getQualityColor() }]}>
          {quality}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  sleepInfo: {
    marginBottom: 12,
  },
  sleepValue: {
    fontSize: 34,
    fontWeight: '700',
  },
  sleepLabel: {
    fontSize: 14,
    opacity: 0.7,
  },
  qualityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  qualityLabel: {
    fontSize: 14,
    marginRight: 8,
  },
  starsContainer: {
    flexDirection: 'row',
  },
  starIcon: {
    marginRight: 2,
  },
  qualityTextContainer: {
    alignItems: 'flex-start',
  },
  qualityText: {
    fontSize: 16,
    fontWeight: '600',
  },
}); 