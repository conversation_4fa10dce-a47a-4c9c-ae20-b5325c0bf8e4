import React, { ReactElement } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface WaterIntakeWidgetProps {
  current: number;
  goal: number;
  onAdd?: () => void;
}

export function WaterIntakeWidget({ current, goal, onAdd }: WaterIntakeWidgetProps) {
  const progress = Math.min(current / goal, 1);
  const remaining = goal - current;
  
  // Create glass indicators
  const renderGlasses = (): ReactElement[] => {
    const glasses: ReactElement[] = [];
    for (let i = 0; i < goal; i++) {
      glasses.push(
        <Ionicons
          key={i}
          name={i < current ? "water" : "water-outline"}
          size={22}
          color="#2196F3"
          style={styles.glassIcon}
        />
      );
    }
    return glasses;
  };
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Water Intake</Text>
      
      <View style={styles.counterContainer}>
        <Text style={styles.counterValue}>{current}</Text>
        <Text style={styles.counterUnit}>/ {goal} glasses</Text>
      </View>
      
      <View style={styles.glassesContainer}>
        {renderGlasses()}
      </View>
      
      <View style={styles.actionsContainer}>
        <Text style={styles.remainingText}>
          {remaining > 0 
            ? `${remaining} more to go` 
            : 'Goal completed!'}
        </Text>
        
        {remaining > 0 && onAdd && (
          <TouchableOpacity
            style={styles.addButton}
            onPress={onAdd}
          >
            <Ionicons name="add-circle" size={24} color="#2196F3" />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  counterContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 16,
  },
  counterValue: {
    fontSize: 32,
    fontWeight: '700',
    color: '#2196F3',
  },
  counterUnit: {
    fontSize: 16,
    marginLeft: 4,
    opacity: 0.7,
  },
  glassesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
    marginBottom: 12,
  },
  glassIcon: {
    marginRight: 6,
    marginBottom: 6,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  remainingText: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  addButton: {
    padding: 4,
  },
}); 