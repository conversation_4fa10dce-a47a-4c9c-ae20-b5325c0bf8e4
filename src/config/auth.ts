/**
 * Authentication Configuration
 * Centralized configuration for all auth-related constants and environment variables
 */

export interface AuthConfig {
  firebase: {
    webClientId: string;
  };
  oauth: {
    google: {
      webClientId: string;
      iosClientId?: string;
      androidClientId?: string;
    };
    apple: {
      clientId: string;
      redirectUri: string;
    };
  };
  testAccounts: {
    google: {
      email: string;
      password: string;
      displayName: string;
      photoURL: string;
    };
    apple: {
      email: string;
      password: string;
      displayName: string;
      photoURL: string;
    };
  };
  development: {
    enableTestAccounts: boolean;
    forceTestAccountCleanup: boolean;
    testAccountIds: string[];
  };
}

// Environment variable validation
const requiredEnvVars = {
  EXPO_PUBLIC_FIREBASE_WEB_CLIENT_ID: process.env.EXPO_PUBLIC_FIREBASE_WEB_CLIENT_ID,
  EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID,
  EXPO_PUBLIC_APPLE_CLIENT_ID: process.env.EXPO_PUBLIC_APPLE_CLIENT_ID,
} as const;

// Validate environment variables
const missingEnvVars = Object.entries(requiredEnvVars)
  .filter(([key, value]) => !value)
  .map(([key]) => key);

if (missingEnvVars.length > 0 && __DEV__) {
  console.warn('⚠️  Missing environment variables for auth:', missingEnvVars);
  console.warn('⚠️  Falling back to development defaults');
}

// Auth configuration with environment variables and fallbacks
export const AUTH_CONFIG: AuthConfig = {
  firebase: {
    webClientId: requiredEnvVars.EXPO_PUBLIC_FIREBASE_WEB_CLIENT_ID || 
      '************-0a08n16m8qs3emha4c4uql7f8sd5rk48.apps.googleusercontent.com'
  },
  
  oauth: {
    google: {
      webClientId: requiredEnvVars.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID || 
        '************-0a08n16m8qs3emha4c4uql7f8sd5rk48.apps.googleusercontent.com',
      iosClientId: process.env.EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID,
      androidClientId: process.env.EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID
    },
    apple: {
      clientId: requiredEnvVars.EXPO_PUBLIC_APPLE_CLIENT_ID || 'com.kingly.choosehealthy',
      redirectUri: process.env.EXPO_PUBLIC_APPLE_REDIRECT_URI || 'https://choosehealthy.app/auth/apple'
    }
  },
  
  testAccounts: {
    google: {
      email: process.env.EXPO_PUBLIC_GOOGLE_TEST_EMAIL || '<EMAIL>',
      password: process.env.EXPO_PUBLIC_GOOGLE_TEST_PASSWORD || 'GoogleTest123!',
      displayName: 'Google Test User',
      photoURL: 'https://lh3.googleusercontent.com/a/default-user-avatar'
    },
    apple: {
      email: process.env.EXPO_PUBLIC_APPLE_TEST_EMAIL || '<EMAIL>',
      password: process.env.EXPO_PUBLIC_APPLE_TEST_PASSWORD || 'AppleTest123!',
      displayName: 'Apple Test User',
      photoURL: 'https://appleid.apple.com/static/images/shared/avatar_default.png'
    }
  },
  
  development: {
    enableTestAccounts: process.env.NODE_ENV === 'development' || 
      process.env.EXPO_PUBLIC_ENVIRONMENT === 'development',
    forceTestAccountCleanup: process.env.EXPO_PUBLIC_FORCE_TEST_CLEANUP === 'true',
    testAccountIds: (process.env.EXPO_PUBLIC_TEST_ACCOUNT_IDS || 
      'aCGNX5YBh6WgOi6Wt4cXLvB6PFI3,kzXsQSmnuNVY36HBCgThmlLSsvD3').split(',')
  }
};

// Configuration validation
export const validateAuthConfig = (): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!AUTH_CONFIG.firebase.webClientId) {
    errors.push('Firebase web client ID is required');
  }
  
  if (!AUTH_CONFIG.oauth.google.webClientId) {
    errors.push('Google OAuth web client ID is required');
  }
  
  if (!AUTH_CONFIG.oauth.apple.clientId) {
    errors.push('Apple OAuth client ID is required');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

// Export individual configs for easier access
export const FIREBASE_CONFIG = AUTH_CONFIG.firebase;
export const GOOGLE_CONFIG = AUTH_CONFIG.oauth.google;
export const APPLE_CONFIG = AUTH_CONFIG.oauth.apple;
export const TEST_ACCOUNTS = AUTH_CONFIG.testAccounts;
export const DEV_CONFIG = AUTH_CONFIG.development;

// Log configuration status (development only)
if (__DEV__) {
  const validation = validateAuthConfig();
  if (validation.valid) {
    console.log('✅ Auth configuration loaded successfully');
  } else {
    console.warn('⚠️  Auth configuration issues:', validation.errors);
  }
  
  console.log('🔧 Auth config summary:', {
    hasFirebaseConfig: !!AUTH_CONFIG.firebase.webClientId,
    hasGoogleConfig: !!AUTH_CONFIG.oauth.google.webClientId,
    hasAppleConfig: !!AUTH_CONFIG.oauth.apple.clientId,
    testAccountsEnabled: AUTH_CONFIG.development.enableTestAccounts,
    environment: process.env.EXPO_PUBLIC_ENVIRONMENT || 'development'
  });
}