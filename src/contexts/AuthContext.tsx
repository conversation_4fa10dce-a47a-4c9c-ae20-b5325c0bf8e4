/**
 * AuthContext - Refactored
 * Clean, modular authentication context using new architecture
 */

import React, { createContext, useContext, useEffect, ReactNode, useRef } from 'react';
import { Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import { getAuth, onAuthStateChanged, Unsubscribe } from 'firebase/auth';

// Import our new modular components
import { AuthService } from '@/lib/auth/AuthService';
import { AuthStateManager } from '@/lib/auth/AuthStateManager';
import { AuthErrorHelper } from '@/lib/auth/AuthErrorHelper';
import { 
  AuthContextValue, 
  AuthResult, 
  User, 
  AuthError, 
  PlatformCapabilities 
} from '@/types/auth';
import { AUTH_CONFIG, DEV_CONFIG } from '@/config/auth';

// Create context
const AuthContext = createContext<AuthContextValue | undefined>(undefined);

// Provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  // Core services
  const authService = useRef(new AuthService());
  const stateManager = useRef(new AuthStateManager());
  const errorHelper = useRef(new AuthErrorHelper());
  
  // Component lifecycle
  const isMounted = useRef(true);
  const authListenerRef = useRef<Unsubscribe | null>(null);
  const networkListenerRef = useRef<(() => void) | null>(null);
  
  // Get current state from state manager
  const currentState = stateManager.current.getState();

  // Setup cleanup on unmount
  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
      cleanup();
    };
  }, []);

  // Initialize auth system
  useEffect(() => {
    initializeAuth();
    setupNetworkMonitoring();
    
    return () => {
      cleanup();
    };
  }, []);

  // Subscribe to state changes for re-renders
  useEffect(() => {
    const unsubscribe = stateManager.current.subscribe(() => {
      if (isMounted.current) {
        // Force re-render by updating state
        // React will re-render when context value changes
      }
    });
    
    return unsubscribe;
  }, []);

  // Core auth methods

  const signIn = async (email: string, password: string): Promise<AuthResult> => {
    if (!isMounted.current) return { success: false };
    
    stateManager.current.setLoading(true, 'sign-in');
    
    try {
      const result = await authService.current.signInWithEmail(email, password);
      
      if (result.success && result.user) {
        stateManager.current.setUser(result.user, 'sign-in');
        
        // Set intentional login flag for web
        if (Platform.OS === 'web') {
          sessionStorage.setItem('intentional_login', 'true');
        }
      } else if (result.error) {
        stateManager.current.setError(result.error, 'sign-in');
        errorHelper.current.logError(result.error);
      }
      
      return result;
    } catch (error) {
      const authError = errorHelper.current.createFromFirebaseError(error, 'sign-in');
      stateManager.current.setError(authError, 'sign-in');
      return { success: false, error: authError };
    }
  };

  const signUp = async (email: string, password: string, name: string): Promise<AuthResult> => {
    if (!isMounted.current) return { success: false };
    
    stateManager.current.setLoading(true, 'sign-up');
    
    try {
      const result = await authService.current.signUp(email, password, name);
      
      if (result.success && result.user) {
        stateManager.current.setUser(result.user, 'sign-up');
      } else if (result.error) {
        stateManager.current.setError(result.error, 'sign-up');
        errorHelper.current.logError(result.error);
      }
      
      return result;
    } catch (error) {
      const authError = errorHelper.current.createFromFirebaseError(error, 'sign-up');
      stateManager.current.setError(authError, 'sign-up');
      return { success: false, error: authError };
    }
  };

  const signInWithGoogle = async (): Promise<AuthResult> => {
    if (!isMounted.current) return { success: false };
    
    stateManager.current.setLoading(true, 'google-oauth');
    
    try {
      const result = await authService.current.signInWithGoogle();
      
      if (result.success && result.user) {
        stateManager.current.setUser(result.user, 'google-oauth');
      } else if (result.error) {
        stateManager.current.setError(result.error, 'google-oauth');
        errorHelper.current.logError(result.error);
      }
      
      return result;
    } catch (error) {
      const authError = errorHelper.current.createFromFirebaseError(error, 'google-oauth');
      stateManager.current.setError(authError, 'google-oauth');
      return { success: false, error: authError };
    }
  };

  const signInWithApple = async (): Promise<AuthResult> => {
    if (!isMounted.current) return { success: false };
    
    stateManager.current.setLoading(true, 'apple-oauth');
    
    try {
      const result = await authService.current.signInWithApple();
      
      if (result.success && result.user) {
        stateManager.current.setUser(result.user, 'apple-oauth');
      } else if (result.error) {
        stateManager.current.setError(result.error, 'apple-oauth');
        errorHelper.current.logError(result.error);
      }
      
      return result;
    } catch (error) {
      const authError = errorHelper.current.createFromFirebaseError(error, 'apple-oauth');
      stateManager.current.setError(authError, 'apple-oauth');
      return { success: false, error: authError };
    }
  };

  const signOut = async (): Promise<AuthResult> => {
    if (!isMounted.current) return { success: false };
    
    stateManager.current.setLoading(true, 'sign-out');
    
    try {
      // Clear state immediately for responsive UI
      stateManager.current.setUser(null, 'sign-out');
      
      // Clear web session storage
      if (Platform.OS === 'web') {
        try {
          sessionStorage.removeItem('intentional_login');
          sessionStorage.removeItem('expo-router-state');
          sessionStorage.removeItem('expo-router-origin-state');
        } catch (e) {
          console.warn('AuthContext: Error clearing session storage:', e);
        }
      }
      
      const result = await authService.current.signOut();
      
      if (!result.success && result.error) {
        stateManager.current.setError(result.error, 'sign-out');
        errorHelper.current.logError(result.error);
      }
      
      return result;
    } catch (error) {
      const authError = errorHelper.current.createFromFirebaseError(error, 'sign-out');
      stateManager.current.setError(authError, 'sign-out');
      return { success: false, error: authError };
    }
  };

  // Utility methods

  const refreshSession = async (): Promise<void> => {
    if (!isMounted.current) return;
    
    try {
      const result = await authService.current.refreshSession();
      
      if (result.success && result.user) {
        stateManager.current.setUser(result.user, 'refresh-session');
        stateManager.current.refreshSession();
      } else if (result.error) {
        stateManager.current.setError(result.error, 'refresh-session');
        console.warn('AuthContext: Session refresh failed:', result.error);
      }
    } catch (error) {
      console.error('AuthContext: Session refresh error:', error);
    }
  };

  const getCurrentUser = (): User | null => {
    return authService.current.getCurrentUser();
  };

  const clearError = (): void => {
    stateManager.current.clearError();
  };

  const getCapabilities = (): PlatformCapabilities => {
    return authService.current.getCapabilities();
  };

  // Setup and initialization

  const initializeAuth = async (): Promise<void> => {
    if (!isMounted.current) return;
    
    console.log('AuthContext: Initializing auth system...');
    stateManager.current.setLoading(true);
    
    try {
      // Handle web-specific initialization
      if (Platform.OS === 'web') {
        await handleWebInitialization();
      }
      
      // Set up Firebase auth state listener
      setupAuthStateListener();
      
    } catch (error) {
      console.error('AuthContext: Initialization error:', error);
      const authError = errorHelper.current.createFromFirebaseError(error, 'sign-in');
      stateManager.current.setError(authError);
    } finally {
      if (isMounted.current) {
        stateManager.current.setLoading(false);
      }
    }
  };

  const handleWebInitialization = async (): Promise<void> => {
    try {
      // Clear Firebase IndexedDB
      const { clearFirebaseIndexedDB } = await import('@/lib/firebase');
      await clearFirebaseIndexedDB();
    } catch (e) {
      console.warn('AuthContext: Error clearing Firebase IndexedDB:', e);
    }

    // Handle test account cleanup
    const isFromLogin = sessionStorage.getItem('intentional_login') === 'true';
    const currentUser = getAuth().currentUser;
    const path = window.location.pathname;

    if (currentUser && !isFromLogin && !path.includes('login') && !path.includes('signup')) {
      if (DEV_CONFIG.testAccountIds.includes(currentUser.uid)) {
        console.warn('AuthContext: Test account detected during initialization, forcing sign out');
        await signOut();
        
        if (!path.includes('login')) {
          window.location.href = '/login';
          return;
        }
      }
    }
  };

  const setupAuthStateListener = (): void => {
    if (!isMounted.current) return;
    
    // Clear existing listener
    if (authListenerRef.current) {
      authListenerRef.current();
      authListenerRef.current = null;
    }
    
    try {
      const auth = getAuth();
      const unsubscribe = onAuthStateChanged(
        auth,
        async (firebaseUser) => {
          if (!isMounted.current) return;
          
          console.log('AuthContext: Auth state changed');
          
          if (firebaseUser) {
            // Handle test account detection for web
            if (Platform.OS === 'web' && DEV_CONFIG.testAccountIds.includes(firebaseUser.uid)) {
              const path = window.location.pathname;
              const isFromLogin = sessionStorage.getItem('intentional_login') === 'true';
              
              if (!isFromLogin && !path.includes('login') && !path.includes('signup')) {
                console.warn('AuthContext: Test account auto-signin detected, forcing sign out');
                await signOut();
                window.location.href = '/login';
                return;
              }
            }
            
            console.log('AuthContext: User signed in:', firebaseUser.uid);
            
            // Create user object
            const user: User = {
              id: firebaseUser.uid,
              email: firebaseUser.email || '',
              name: firebaseUser.displayName || '',
              photoURL: firebaseUser.photoURL || undefined,
              provider: getProviderFromFirebaseUser(firebaseUser),
              verified: firebaseUser.emailVerified,
              createdAt: firebaseUser.metadata.creationTime ? new Date(firebaseUser.metadata.creationTime) : undefined,
              lastLoginAt: firebaseUser.metadata.lastSignInTime ? new Date(firebaseUser.metadata.lastSignInTime) : undefined
            };
            
            stateManager.current.setUser(user);
            
          } else {
            console.log('AuthContext: No user signed in');
            stateManager.current.setUser(null);
          }
        },
        (error) => {
          console.error('AuthContext: Auth state listener error:', error);
          const authError = errorHelper.current.createFromFirebaseError(error, 'sign-in');
          stateManager.current.setError(authError);
        }
      );
      
      authListenerRef.current = unsubscribe;
    } catch (error) {
      console.error('AuthContext: Error setting up auth state listener:', error);
      const authError = errorHelper.current.createFromFirebaseError(error, 'sign-in');
      stateManager.current.setError(authError);
    }
  };

  const setupNetworkMonitoring = (): void => {
    // Monitor network changes for session refresh
    const unsubscribe = NetInfo.addEventListener(state => {
      const isConnected = state.isConnected && state.isInternetReachable;
      
      if (isConnected && currentState.user && !currentState.isLoading) {
        console.log('AuthContext: Network reconnected, refreshing session');
        refreshSession();
      }
    });
    
    networkListenerRef.current = unsubscribe;
  };

  const cleanup = (): void => {
    // Clean up auth listener
    if (authListenerRef.current) {
      try {
        authListenerRef.current();
        authListenerRef.current = null;
      } catch (e) {
        console.error('AuthContext: Error cleaning up auth listener:', e);
      }
    }
    
    // Clean up network listener
    if (networkListenerRef.current) {
      try {
        networkListenerRef.current();
        networkListenerRef.current = null;
      } catch (e) {
        console.error('AuthContext: Error cleaning up network listener:', e);
      }
    }
    
    // Dispose state manager
    stateManager.current.dispose();
  };

  // Helper functions

  const getProviderFromFirebaseUser = (firebaseUser: any): any => {
    const providerId = firebaseUser.providerData[0]?.providerId;
    
    switch (providerId) {
      case 'google.com':
        return 'google.com';
      case 'apple.com':
        return 'apple.com';
      default:
        return 'email';
    }
  };

  // Context value
  const contextValue: AuthContextValue = {
    // State from state manager
    user: currentState.user,
    isLoading: currentState.isLoading,
    isAuthenticated: currentState.isAuthenticated,
    error: currentState.error,
    
    // Core methods
    signIn,
    signUp,
    signInWithGoogle,
    signInWithApple,
    signOut,
    
    // Utility methods
    refreshSession,
    getCurrentUser,
    clearError,
    
    // Platform info
    capabilities: getCapabilities()
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook for consuming auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};

// Export auth state manager for advanced usage
export const useAuthState = () => {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuthState must be used within an AuthProvider');
  }
  
  // Return state manager instance for advanced state operations
  return {
    getStateSummary: () => {
      // Access via provider's state manager
      // This would need to be exposed if advanced usage is needed
      return {};
    }
  };
};