import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Alert, Platform } from 'react-native';
import { initDatabase, closeDatabase, fixDatabaseAccessHandleError , syncOfflineDataWithConflictResolution, initConflictsTable, getUnresolvedConflicts, ConflictResolutionStrategy, DataConflict } from '@/services/databaseService';
import NetInfo from '@react-native-community/netinfo';
import { DatabaseProvider as DatabaseProviderInterface } from '@/lib/database/DatabaseProvider';
import { UnifiedFirebaseProvider } from '@/lib/database/UnifiedFirebaseProvider';
import { isFirebaseConfigured } from '@/lib/firebase';

// Helper to check if running on web
const isWeb = Platform.OS === 'web';

// Create the context
export interface DatabaseContextValue {
  db: DatabaseProviderInterface;
}

const DatabaseContext = createContext<DatabaseContextValue | undefined>(undefined);

interface DatabaseProviderProps {
  children: ReactNode;
}

export const DatabaseProviderComponent: React.FC<DatabaseProviderProps> = ({ children }) => {
  const [db] = useState<DatabaseProviderInterface>(new UnifiedFirebaseProvider());
  const [initialized, setInitialized] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  const [syncAttemptCount, setSyncAttemptCount] = useState(0);
  const [hasConflicts, setHasConflicts] = useState(false);

  // Initialize database
  useEffect(() => {
    const setupDatabase = async () => {
      // Skip SQLite initialization on web platform to avoid browser compatibility issues
      if (isWeb) {
        console.log('Web platform detected. Using Firebase directly, skipping SQLite initialization.');
        setInitialized(true);
        return;
      }
      
      try {
        const result = await initDatabase();
        if (result.success) {
          console.log('Database initialized successfully');
          
          // Initialize conflicts table
          await initConflictsTable();
          
          // Check for existing conflicts
          refreshConflicts();
          
          setInitialized(true);
        } else {
          console.error('Failed to initialize database:', result.error);
          
          // If on web and we get an Access Handle error, try to fix it
          if (isWeb && 
              result.error && 
              result.error.includes('createSyncAccessHandle')) {
            console.log('Trying to fix Access Handle error...');
            const fixResult = await fixDatabaseAccessHandleError();
            
            if (fixResult.success) {
              console.log('Successfully fixed database access issue');
              
              // Initialize conflicts table
              await initConflictsTable();
              
              // Check for existing conflicts
              refreshConflicts();
              
              setInitialized(true);
            } else {
              console.error('Failed to fix database access issue:', 
                'error' in fixResult ? fixResult.error : 'Unknown error');
              if (__DEV__) {
                Alert.alert('Database Error', 
                  `Failed to fix database access issue. Try reloading the app: ${
                    'error' in fixResult ? fixResult.error : 'Unknown error'
                  }`);
              }
            }
          } else if (__DEV__) {
            Alert.alert('Database Error', `Failed to initialize database: ${result.error}`);
          }
        }
      } catch (error) {
        console.error('Error initializing database:', error);
        if (__DEV__) {
          Alert.alert('Database Error', 'An unexpected error occurred while initializing the database');
        }
      }
    };

    setupDatabase();
    
    // Cleanup function - close database when context is unmounted
    return () => {
      if (!isWeb) {
        closeDatabase().catch(err => {
          console.error('Error closing database during cleanup:', err);
        });
      }
    };
  }, []);

  // Set up network listeners to trigger sync when coming back online
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      if (state.isConnected && state.isInternetReachable && initialized && !syncing) {
        // Only attempt to sync if we haven't tried too many times
        if (syncAttemptCount < 3) {
          syncData();
          setSyncAttemptCount(prevCount => prevCount + 1);
        }
      }
    });

    return () => {
      unsubscribe();
    };
  }, [initialized, syncing, syncAttemptCount]);

  // Reset sync attempt counter periodically
  useEffect(() => {
    const resetInterval = setInterval(() => {
      setSyncAttemptCount(0);
    }, 3600000); // Reset every hour

    return () => {
      clearInterval(resetInterval);
    };
  }, []);

  // Function to check for unresolved conflicts
  const refreshConflicts = async (): Promise<DataConflict[]> => {
    // Skip on web
    if (isWeb) {
      return [];
    }
    
    try {
      const conflicts = await getUnresolvedConflicts();
      setHasConflicts(conflicts.length > 0);
      return conflicts;
    } catch (error) {
      console.error('Error checking for conflicts:', error);
      return [];
    }
  };

  // Function to manually trigger data sync with conflict resolution
  const syncData = async (strategy?: ConflictResolutionStrategy) => {
    if (!initialized || syncing) return;
    
    // Skip on web
    if (isWeb) {
      return;
    }

    setSyncing(true);
    try {
      const result = await syncOfflineDataWithConflictResolution(
        strategy || ConflictResolutionStrategy.SERVER_WINS
      );
      
      if (result.success) {
        console.log(`Data synced successfully: ${result.syncedCount} operations, ${result.errorCount} errors`);
        
        if (result.conflictCount && result.conflictCount > 0) {
          console.log(`Found ${result.conflictCount} conflicts during sync`);
          setHasConflicts(true);
        } else {
          // If we didn't get conflicts from sync, check if there are any stored ones
          await refreshConflicts();
        }
        
        setLastSyncTime(new Date());
      } else {
        console.warn('Failed to sync data:', result.error);
      }
    } catch (error) {
      console.error('Error syncing data:', error);
    } finally {
      setSyncing(false);
    }
  };
  
  const value: DatabaseContextValue = {
    db
  };

  return (
    <DatabaseContext.Provider value={value}>
      {children}
    </DatabaseContext.Provider>
  );
};

// Export an alias for backward compatibility
export const DatabaseProvider = DatabaseProviderComponent;

// Create the hook
export function useDatabase() {
  const context = useContext(DatabaseContext);
  if (!context) {
    throw new Error('useDatabase must be used within a DatabaseProvider');
  }
  return context;
}

// Create the context for legacy code compatibility
interface DatabaseContextType {
  db: DatabaseProviderInterface;
  useFirebase: boolean;
  toggleProvider: () => void;
  setUseFirebase: (value: boolean) => void;
}

const DatabaseContextType = createContext<DatabaseContextType | undefined>(undefined);

interface DatabaseProviderComponentProps {
  children: ReactNode;
}

// Create a compatibility provider component that always uses Firebase
export function DatabaseTypeProviderComponent({ 
  children
}: DatabaseProviderComponentProps) {
  const [db] = useState<DatabaseProviderInterface>(new UnifiedFirebaseProvider());
  
  // Check if Firebase is properly configured and log a warning if not
  useEffect(() => {
    if (!isFirebaseConfigured()) {
      console.warn('Firebase is not properly configured. Check your environment variables.');
    }
  }, []);
  
  // Stub methods that do nothing (for backward compatibility)
  const toggleProvider = () => {
    console.warn('toggleProvider is deprecated as the app now uses Firebase exclusively');
  };
  
  const setUseFirebase = (value: boolean) => {
    if (!value) {
      
    }
  };
  
  return (
    <DatabaseContextType.Provider value={{ db, useFirebase: true, toggleProvider, setUseFirebase }}>
      {children}
    </DatabaseContextType.Provider>
  );
}

// Create the hook for components to use
export function useDatabaseType() {
  const context = useContext(DatabaseContextType);
  if (!context) {
    throw new Error('useDatabase must be used within a DatabaseTypeProviderComponent');
  }
  return context;
} 