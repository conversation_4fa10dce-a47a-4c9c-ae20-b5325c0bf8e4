import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  getAuth, 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword, 
  signOut, 
  onAuthStateChanged, 
  User,
  signInAnonymously
} from 'firebase/auth';
import { 
  getFirestore, 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc, 
  getDocs, 
  setDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  serverTimestamp,
  WhereFilterOp,
  onSnapshot
} from 'firebase/firestore';

// Import centralized Firebase instances from the single modern source
import { app, auth, firestore as db } from '@/lib/firebase';

// Import offline fallback utilities
import * as offlineDataFallback from '@/utils/offlineDataFallback';

// Add timeout to prevent operations from hanging
const withTimeout = (promise, timeoutMs) => {
  const timeout = new Promise((_, reject) => {
    const id = setTimeout(() => {
      clearTimeout(id);
      reject(new Error(`Operation timed out after ${timeoutMs}ms`));
    }, timeoutMs);
  });
  return Promise.race([promise, timeout]);
};

// Filter type for queries
export interface QueryFilter {
  field: string;
  operator: WhereFilterOp;
  value: any;
}

// Network status state
interface NetworkState {
  isOnline: boolean;
  lastChecked: Date;
}

// Error interface for typing Firebase errors
interface FirebaseError {
  code?: string;
  message: string;
}

// Define the shape of our context
interface FirebaseContextType {
  firebase: any;
  auth: any;
  firestore: any;
  storage: any;
  functions: any;
  isInitialized: boolean;
  isLoading: boolean;
  error: Error | null;
  isOfflineMode: boolean;
  networkState: NetworkState;
  checkConnection: () => Promise<boolean>;
  forceOfflineMode: () => void;
}

// Create the context with defaults
const FirebaseContext = createContext<FirebaseContextType>({
  firebase: null,
  auth: null,
  firestore: null,
  storage: null,
  functions: null,
  isInitialized: false,
  isLoading: true,
  error: null,
  isOfflineMode: false,
  networkState: { isOnline: true, lastChecked: new Date() },
  checkConnection: async () => true,
  forceOfflineMode: () => {}
});

// Context provider props
interface FirebaseProviderProps {
  children: ReactNode;
}

// Provider component that wraps the app and makes Firebase available
export function FirebaseProvider({ children }: FirebaseProviderProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isOfflineMode, setIsOfflineMode] = useState(false);
  const [networkState, setNetworkState] = useState<NetworkState>({
    isOnline: true,
    lastChecked: new Date()
  });
  const [services, setServices] = useState({
    firebase: null as any,
    auth: null as any,
    firestore: null as any,
    storage: null as any,
    functions: null as any,
  });

  // Connection check function
  const checkConnection = async (): Promise<boolean> => {
    if (!services.firestore) return false;
    
    // iOS-specific connection check with anonymous auth
    if (Platform.OS === 'ios') {
      try {
        // First check if we have a valid auth session
        if (!services.auth.currentUser) {
          console.log('📶 iOS - No auth, attempting anonymous sign in');
          try {
            // Try anonymous authentication with timeout to prevent hanging
            await withTimeout(services.auth.signInAnonymously(), 10000);
            console.log('📶 iOS - Anonymous auth successful');
          } catch (authErr) {
            console.warn('📶 iOS - Anonymous auth failed:', authErr);
            
            // Since auth failed, fallback to offline mode
            setIsOfflineMode(true);
            setNetworkState({
              isOnline: false,
              lastChecked: new Date()
            });
            
            // Update offline status in AsyncStorage
            await offlineDataFallback.setOfflineMode(true);
            
            return false;
          }
        }
        
        // Try to read from public collection with timeout
        try {
          const publicDoc = await withTimeout(
            services.firestore.collection('public').doc('app_status').get(),
            5000
          );
          console.log('📶 iOS - Connection check success using public collection');
          
          setIsOfflineMode(false);
          setNetworkState({
            isOnline: true,
            lastChecked: new Date()
          });
          
          // Update offline status in AsyncStorage
          await offlineDataFallback.setOfflineMode(false);
          
          return true;
        } catch (timeoutErr) {
          console.warn('📶 iOS - Public collection read timed out:', timeoutErr);
          
          // Try a more permissive collection specifically for iOS tests
          try {
            await withTimeout(
              services.firestore.collection('ios_connectivity_test').doc('connection_check').set({
                timestamp: new Date().toISOString(),
                platform: 'ios'
              }),
              5000
            );
            
            console.log('📶 iOS - Connection check success using test collection');
            setIsOfflineMode(false);
            setNetworkState({
              isOnline: true,
              lastChecked: new Date()
            });
            
            // Update offline status in AsyncStorage
            await offlineDataFallback.setOfflineMode(false);
            
            return true;
          } catch (iosErr) {
            console.warn('📶 iOS - All connection tests failed, switching to offline mode');
            setIsOfflineMode(true);
            setNetworkState({
              isOnline: false,
              lastChecked: new Date()
            });
            
            // Update offline status in AsyncStorage
            await offlineDataFallback.setOfflineMode(true);
            
            return false;
          }
        }
      } catch (iosErrUnknown) {
        const iosErr = iosErrUnknown as FirebaseError;
        console.warn('📶 iOS - Connection failed:', iosErr.message);
        setIsOfflineMode(true);
        setNetworkState({
          isOnline: false,
          lastChecked: new Date()
        });
        
        // Update offline status in AsyncStorage
        await offlineDataFallback.setOfflineMode(true);
        
        return false;
      }
    }
    
    // For non-iOS platforms, use the standard approach
    // Attempt multiple methods to check connection
    const attemptConnection = async () => {
      // Method 1: Try to read from public collection
      try {
        const publicDoc = await withTimeout(
          services.firestore.collection('public').doc('app_status').get(),
          5000
        );
        console.log('📶 Firebase connection check: ONLINE (via public collection)');
        return true;
      } catch (err1) {
        console.log('📶 Public collection check failed, trying system health...');
        
        // Method 2: Try to write to system health collection
        try {
          await withTimeout(
            services.firestore.collection('_system_health').doc('connection_test').set({
              timestamp: new Date().toISOString(),
              status: 'online',
              platform: Platform.OS
            }),
            5000
          );
          console.log('📶 Firebase connection check: ONLINE (via system health)');
          return true;
        } catch (err2) {
          console.log('📶 System health check failed, trying another approach...');
          
          // Method 3: Try native Firebase connection state (most reliable but requires auth)
          try {
            // @ts-ignore - This is a non-standard but working Firebase method
            const isConnected = await services.firestore.getImmediate().INTERNAL.getComponent('firestore').getOnlineComponentProvider().isPrimaryClient();
            if (isConnected) {
              console.log('📶 Firebase connection check: ONLINE (via internal API)');
              return true;
            }
          } catch (err3) {
            console.log('📶 Internal connection check failed');
          }
          
          // All methods failed, try to check auth as last resort
          if (services.auth && services.auth.currentUser) {
            try {
              // Try to reauthenticate silently with timeout
              await withTimeout(services.auth.currentUser.getIdToken(true), 5000);
              console.log('📶 Firebase connection check: ONLINE (via auth token refresh)');
              return true;
            } catch (authErr) {
              console.log('📶 Auth refresh failed too');
            }
          }
          
          // All methods failed
          return false;
        }
      }
    };
    
    const isOnline = await attemptConnection();
    
    // Update connection state
    setIsOfflineMode(!isOnline);
    setNetworkState({
      isOnline,
      lastChecked: new Date()
    });
    
    // Update offline status in AsyncStorage
    await offlineDataFallback.setOfflineMode(!isOnline);
    
    if (isOnline) {
      console.log('📶 Firebase connection check: ONLINE');
    } else {
      console.log('📶 Firebase connection check: OFFLINE - all methods failed');
    }
    
    return isOnline;
  };

  // Initialize Firebase on component mount
  useEffect(() => {
    async function initializeFirebase() {
      // Skip if already initialized
      if (isInitialized) return;
      
      try {
        // First, check if Firebase is properly configured
        if (isFirebaseConfigured()) {
          console.log('🔄 Firebase Context: Firebase is configured');
          
          // Set a timeout to prevent hanging during initialization
          const initTimeout = setTimeout(() => {
            setError(new Error('Firebase initialization timed out'));
            setIsOfflineMode(true);
            console.error('🔄 Firebase Context: Firebase initialization timed out');
          }, 15000); // 15-second timeout
          
          // iOS-specific authentication approach
          if (Platform.OS === 'ios') {
            try {
              // Ensure anonymous auth finishes before attempting Firestore operations
              const auth = getAuth();
              
              // First check if user is already signed in
              if (!auth.currentUser) {
                console.log('🔄 Firebase Context: iOS needs anonymous auth');
                
                // Create timeout promise for auth
                const authPromise = signInAnonymously(auth);
                const timeoutPromise = new Promise((_, reject) => 
                  setTimeout(() => reject(new Error('Auth timed out')), 10000)
                );
                
                // Wait for authentication with timeout
                await Promise.race([authPromise, timeoutPromise])
                  .then((userCredential) => {
                    console.log('🔄 Firebase Context: iOS anonymous auth successful');
                  })
                  .catch(error => {
                    console.warn('🔄 Firebase Context: iOS anonymous auth failed, will use offline mode:', error);
                    setIsOfflineMode(true);
                  });
                  
                // Wait for auth state to propagate
                await new Promise(resolve => setTimeout(resolve, 500));
              } else {
                console.log('🔄 Firebase Context: iOS already has auth:', auth.currentUser.uid);
              }
              
              // Test write permissions even if we already have a user
              try {
                // First try with public collection
                const db = getFirestore();
                const testRef = doc(db, 'ios_test', 'test_permissions');
                await setDoc(testRef, {
                  timestamp: new Date().toISOString(),
                  platform: 'ios'
                });
                console.log('🔄 Firebase Context: iOS permission test successful');
              } catch (permErr: any) {
                console.warn('🔄 Firebase Context: iOS permission test failed, continuing with caution:', permErr.message);
                // Don't force offline mode yet, let regular operations continue
              }
            } catch (iosError) {
              console.error('🔄 Firebase Context: iOS Firebase setup error:', iosError);
              setIsOfflineMode(true);
            }
          }
          
          // Clear the timeout as we've completed initialization
          clearTimeout(initTimeout);
          
          // Mark as initialized and set status
          setIsInitialized(true);
          setError(null);
          
          // Detect if we're in offline mode
          const firestore = getFirestore();
          if (firestore) {
            try {
              // Setup online/offline detection
              if (Platform.OS === 'web') {
                // For web, use connection state detection
                const connectedRef = doc(firestore, '.info/connected');
                const unsubscribe = onSnapshot(connectedRef, (snap) => {
                  const connected = snap.data()?.connected || false;
                  setIsOfflineMode(!connected);
                  console.log(`🔄 Firebase Context: Online status changed: ${connected ? 'ONLINE' : 'OFFLINE'}`);
                });
                
                // Cleanup function
                return () => unsubscribe();
              } else {
                // For native, we'll do periodic connection tests
                const intervalId = setInterval(async () => {
                  try {
                    // Try to reach the public doc
                    const publicRef = doc(firestore, 'public', 'app_status');
                    await getDoc(publicRef);
                    
                    // If we get here, we're online
                    if (isOfflineMode) {
                      console.log('🔄 Firebase Context: Connection restored, switching to online mode');
                      setIsOfflineMode(false);
                    }
                  } catch (connErr) {
                    // If there's an error, we might be offline
                    if (!isOfflineMode) {
                      console.log('🔄 Firebase Context: Connection lost, switching to offline mode');
                      setIsOfflineMode(true);
                    }
                  }
                }, 60000); // Check once per minute
                
                // Cleanup function
                return () => clearInterval(intervalId);
              }
            } catch (connErr) {
              console.warn('🔄 Firebase Context: Error setting up connection monitoring:', connErr);
            }
          }
        } else {
          console.error('🔄 Firebase Context: Firebase is not configured');
          setError(new Error('Firebase is not configured'));
          setIsOfflineMode(true);
        }
      } catch (error) {
        console.error('🔄 Firebase Context: Initialization error:', error);
        setError(error instanceof Error ? error : new Error(String(error)));
        setIsOfflineMode(true);
      }
    }
    
    // Start the initialization process
    initializeFirebase();
  }, []);

  // Value object that will be provided to consumers
  const value = {
    firebase,
    auth: services.auth,
    firestore: services.firestore,
    db: services.firestore, // Alias for compatibility
    storage: services.storage,
    functions: services.functions,
    isInitialized,
    isLoading,
    error,
    isOfflineMode,
    networkState,
    checkConnection,
    forceOfflineMode: () => {
      setIsOfflineMode(true);
      setNetworkState({
        isOnline: false,
        lastChecked: new Date()
      });
      // Update offline status in AsyncStorage
      offlineDataFallback.setOfflineMode(true);
    }
  };

  return (
    <FirebaseContext.Provider value={value}>
      {children}
    </FirebaseContext.Provider>
  );
}

// Custom hook for using Firebase
export function useFirebase() {
  const context = useContext(FirebaseContext);
  if (context === undefined) {
    throw new Error('useFirebase must be used within a FirebaseProvider');
  }
  return context;
}

// Convenience shortcut hooks
export function useAuth() {
  const { auth } = useFirebase();
  return auth;
}

export function useFirestore() {
  const { firestore } = useFirebase();
  return firestore;
}

export function useStorage() {
  const { storage } = useFirebase();
  return storage;
}

export function useFunctions() {
  const { functions } = useFirebase();
  return functions;
}

// Alias for db
export function useDb() {
  const { firestore } = useFirebase();
  return firestore;
}

// Hook to check if we're in offline mode
export function useOfflineStatus() {
  const { isOfflineMode, networkState, checkConnection } = useFirebase();
  return { isOfflineMode, networkState, checkConnection };
}

export default FirebaseContext;

// Firebase service implementation
const firebaseServiceImpl = {
  // Auth methods
  async signIn(email: string, password: string) {
    try {
      const auth = firebase.auth();
      const userCredential = await auth.signInWithEmailAndPassword(email, password);
      return { success: true, user: userCredential.user };
    } catch (error) {
      console.error('Error signing in:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  async signUp(email: string, password: string) {
    try {
      const auth = firebase.auth();
      const userCredential = await auth.createUserWithEmailAndPassword(email, password);
      return { success: true, user: userCredential.user };
    } catch (error) {
      console.error('Error signing up:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  async signOut() {
    try {
      const auth = firebase.auth();
      await auth.signOut();
      return { success: true };
    } catch (error) {
      console.error('Error signing out:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  getCurrentUser() {
    const auth = firebase.auth();
    return auth.currentUser;
  },

  onAuthStateChanged(callback: (user: User | null) => void) {
    const auth = firebase.auth();
    return auth.onAuthStateChanged(callback);
  },

  // Firestore methods with offline support
  async addDocument(collectionName: string, data: any) {
    try {
      const firestore = firebase.firestore();
      const docRef = firestore.collection(collectionName).doc();
      
      await docRef.set({
        ...data,
        id: docRef.id,
        created_at: firebase.firestore.FieldValue.serverTimestamp()
      });
      
      return { success: true, id: docRef.id };
    } catch (error) {
      console.error(`Error adding document to ${collectionName}:`, error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  async updateDocument(collectionName: string, docId: string, data: any) {
    try {
      const firestore = firebase.firestore();
      const docRef = firestore.collection(collectionName).doc(docId);
      
      await docRef.update({
        ...data,
        updated_at: firebase.firestore.FieldValue.serverTimestamp()
      });
      
      return { success: true };
    } catch (error) {
      console.error(`Error updating document in ${collectionName}:`, error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  async setDocument(collectionName: string, docId: string, data: any, merge = true) {
    try {
      const firestore = firebase.firestore();
      const docRef = firestore.collection(collectionName).doc(docId);
      
      await docRef.set({
        ...data,
        updated_at: firebase.firestore.FieldValue.serverTimestamp()
      }, { merge });
      
      return { success: true };
    } catch (error) {
      console.error(`Error setting document in ${collectionName}:`, error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  async deleteDocument(collectionName: string, docId: string) {
    try {
      const firestore = firebase.firestore();
      const docRef = firestore.collection(collectionName).doc(docId);
      await docRef.delete();
      return { success: true };
    } catch (error) {
      console.error(`Error deleting document from ${collectionName}:`, error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  async getDocument(collectionName: string, docId: string) {
    try {
      const firestore = firebase.firestore();
      const docRef = firestore.collection(collectionName).doc(docId);
      const docSnap = await docRef.get();
      
      if (docSnap.exists) {
        return { success: true, data: { id: docSnap.id, ...docSnap.data() } };
      } else {
        return { success: false, error: 'Document not found' };
      }
    } catch (error) {
      console.error(`Error getting document from ${collectionName}:`, error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  },

  async queryDocuments(collectionName: string, filters: QueryFilter[] = [], orderByField?: string, orderDirection?: 'asc' | 'desc', limitCount?: number) {
    try {
      const firestore = firebase.firestore();
      let queryRef = firestore.collection(collectionName);
      
      // Apply filters
      for (const filter of filters) {
        queryRef = queryRef.where(filter.field, filter.operator, filter.value);
      }
      
      // Apply ordering if provided
      if (orderByField) {
        queryRef = queryRef.orderBy(orderByField, orderDirection || 'asc');
      }
      
      // Apply limit if provided
      if (limitCount) {
        queryRef = queryRef.limit(limitCount);
      }
      
      // Execute query
      const querySnapshot = await queryRef.get();
      
      // Transform results
      const results: any[] = [];
      querySnapshot.forEach((doc) => {
        results.push({
          id: doc.id,
          ...doc.data()
        });
      });
      
      return { success: true, data: results };
    } catch (error) {
      console.error(`Error querying documents from ${collectionName}:`, error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error', data: [] };
    }
  }
};

// Export the Firebase service
export const firebaseService = firebaseServiceImpl; 