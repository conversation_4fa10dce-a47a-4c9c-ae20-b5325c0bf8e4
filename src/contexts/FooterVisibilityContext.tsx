import React, { createContext, useContext, useState } from 'react';

interface FooterVisibilityContextType {
  isFooterVisible: boolean;
  toggleFooterVisibility: () => void;
  setFooterVisibility: (visible: boolean) => void;
  hiddenTabs: string[];
  toggleTabVisibility: (tabKey: string) => void;
  isTabVisible: (tabKey: string) => boolean;
}

const FooterVisibilityContext = createContext<FooterVisibilityContextType | undefined>(undefined);

export function FooterVisibilityProvider({ children }: { children: React.ReactNode }) {
  const [isFooterVisible, setIsFooterVisible] = useState(true);
  // Hide dashboard (index) and food-history tabs
  const [hiddenTabs, setHiddenTabs] = useState<string[]>(['index', 'food-history']);

  const toggleFooterVisibility = () => {
    setIsFooterVisible((prev) => !prev);
  };

  const setFooterVisibility = (visible: boolean) => {
    setIsFooterVisible(visible);
  };

  const toggleTabVisibility = (tabKey: string) => {
    setHiddenTabs(prev => 
      prev.includes(tabKey) 
        ? prev.filter(tab => tab !== tabKey) 
        : [...prev, tabKey]
    );
  };

  const isTabVisible = (tabKey: string) => {
    return !hiddenTabs.includes(tabKey);
  };

  return (
    <FooterVisibilityContext.Provider 
      value={{ 
        isFooterVisible, 
        toggleFooterVisibility, 
        setFooterVisibility,
        hiddenTabs,
        toggleTabVisibility,
        isTabVisible
      }}
    >
      {children}
    </FooterVisibilityContext.Provider>
  );
}

export function useFooterVisibility(): FooterVisibilityContextType {
  const context = useContext(FooterVisibilityContext);
  if (context === undefined) {
    throw new Error('useFooterVisibility must be used within a FooterVisibilityProvider');
  }
  return context;
} 