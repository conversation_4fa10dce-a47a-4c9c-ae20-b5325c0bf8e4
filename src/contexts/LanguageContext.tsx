import React, { createContext, useState, useContext, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Available languages
export const LANGUAGES = {
  ENGLISH: 'en',
  SPANISH: 'es',
  FRENCH: 'fr',
  // Add more languages as needed
};

// Type for the context
interface LanguageContextType {
  language: string;
  setLanguage: (language: string) => Promise<void>;
}

// Create context with default values
const LanguageContext = createContext<LanguageContextType>({
  language: LANGUAGES.ENGLISH,
  setLanguage: async () => {},
});

// Props for provider
interface LanguageProviderProps {
  children: ReactNode;
}

// Provider component
export function LanguageProvider({ children }: LanguageProviderProps) {
  const [language, setLanguageState] = useState<string>(LANGUAGES.ENGLISH);

  // Load language preference from storage on mount
  React.useEffect(() => {
    const loadLanguage = async () => {
      try {
        const storedLanguage = await AsyncStorage.getItem('user_language');
        if (storedLanguage) {
          setLanguageState(storedLanguage);
        }
      } catch (error) {
        console.error('Error loading language preference:', error);
      }
    };

    loadLanguage();
  }, []);

  // Function to change language
  const setLanguage = async (newLanguage: string) => {
    try {
      await AsyncStorage.setItem('user_language', newLanguage);
      setLanguageState(newLanguage);
    } catch (error) {
      console.error('Error saving language preference:', error);
    }
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
}

// Custom hook for using the language context
export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
} 