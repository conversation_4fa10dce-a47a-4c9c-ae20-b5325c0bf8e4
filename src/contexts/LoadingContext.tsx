import React, { createContext, useState, useContext, ReactNode } from 'react';

// Type for the context
interface LoadingContextType {
  isLoading: boolean;
  setLoading: (isLoading: boolean) => void;
  loadingMessage: string;
  setLoadingMessage: (message: string) => void;
}

// Create context with default values
const LoadingContext = createContext<LoadingContextType>({
  isLoading: false,
  setLoading: () => {},
  loadingMessage: '',
  setLoadingMessage: () => {},
});

// Props for provider
interface LoadingProviderProps {
  children: ReactNode;
}

// Provider component
export function LoadingProvider({ children }: LoadingProviderProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [loadingMessage, setLoadingMessage] = useState<string>('');

  // Wrapper for setting loading state to prevent race conditions
  const setLoading = (loading: boolean) => {
    setIsLoading(loading);
    if (!loading) {
      setLoadingMessage(''); // Clear message when not loading
    }
  };

  return (
    <LoadingContext.Provider value={{ 
      isLoading, 
      setLoading, 
      loadingMessage, 
      setLoadingMessage
    }}>
      {children}
    </LoadingContext.Provider>
  );
}

// Custom hook for using the loading context
export function useLoading() {
  const context = useContext(LoadingContext);
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
} 