import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Alert, Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import { useAuthService, User, AuthResult } from '@/services/authService';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<AuthResult>;
  signUp: (email: string, password: string, name: string) => Promise<AuthResult>;
  signOut: () => Promise<void>;
  sendPasswordResetEmail: (email: string) => Promise<AuthResult>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component that wraps your app and makes auth object available to any child component that calls useAuth()
export function NewAuthProvider({ children }: { children: ReactNode }) {
  const authService = useAuthService();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [loading, setLoading] = useState(false);
  const [lastNetworkStatus, setLastNetworkStatus] = useState<boolean | null>(null);

  // Network monitoring
  useEffect(() => {
    // Subscribe to network state changes
    const unsubscribe = NetInfo.addEventListener(state => {
      const isConnected = state.isConnected && state.isInternetReachable;
      
      // Only act if state changed from connected to disconnected or vice versa
      if (lastNetworkStatus !== isConnected) {
        setLastNetworkStatus(isConnected);
        
        if (isConnected) {
          console.log('AuthContext: Network is back online, refreshing user');
          refreshUser(); // Refresh user when network comes back
        } else {
          console.log('AuthContext: Network offline');
        }
      }
    });
    
    return () => {
      unsubscribe();
    };
  }, [lastNetworkStatus]);

  // Function to refresh user data
  const refreshUser = async () => {
    try {
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('Error refreshing user:', error);
    }
  };

  // Set up auth state listener
  useEffect(() => {
    setIsLoading(true);
    
    // Get initial user
    authService.getCurrentUser()
      .then(currentUser => {
        setUser(currentUser);
        setIsLoading(false);
      })
      .catch(error => {
        console.error('Error getting current user:', error);
        setIsLoading(false);
      });
    
    // Subscribe to auth state changes
    const unsubscribe = authService.onAuthStateChanged((newUser) => {
      setUser(newUser);
    });
    
    return unsubscribe;
  }, []);

  // Sign in with email and password
  const signIn = async (email: string, password: string): Promise<AuthResult> => {
    if (!email || !password) {
      return { 
        success: false, 
        error: 'Email and password are required' 
      };
    }
    
    setLoading(true);
    
    try {
      // Check network connectivity first
      const netInfo = await NetInfo.fetch();
      if (!netInfo.isConnected || !netInfo.isInternetReachable) {
        return {
          success: false,
          error: 'Network is unavailable. Please check your internet connection and try again.'
        };
      }
      
      const result = await authService.signIn(email, password);
      
      if (result.success && result.user) {
        setUser(result.user);
      }
      
      return result;
    } catch (error) {
      console.error('Error signing in:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unexpected error occurred' 
      };
    } finally {
      setLoading(false);
    }
  };

  // Sign up with email, password, and name
  const signUp = async (email: string, password: string, name: string): Promise<AuthResult> => {
    if (!email || !password || !name) {
      return { 
        success: false, 
        error: 'Email, password, and name are required' 
      };
    }
    
    setLoading(true);
    
    try {
      // Check network connectivity first
      const netInfo = await NetInfo.fetch();
      if (!netInfo.isConnected || !netInfo.isInternetReachable) {
        return {
          success: false,
          error: 'Network is unavailable. Please check your internet connection and try again.'
        };
      }
      
      const result = await authService.signUp(email, password, name);
      
      if (result.success && result.user) {
        setUser(result.user);
      }
      
      return result;
    } catch (error) {
      console.error('Error signing up:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unexpected error occurred' 
      };
    } finally {
      setLoading(false);
    }
  };

  // Sign out
  const signOut = async (): Promise<void> => {
    setLoading(true);
    
    try {
      const result = await authService.signOut();
      
      if (result.success) {
        setUser(null);
      } else {
        console.error('Error signing out:', result.error);
        Alert.alert('Sign Out Error', result.error || 'Failed to sign out. Please try again.');
      }
    } catch (error) {
      console.error('Error signing out:', error);
      Alert.alert('Sign Out Error', 'An unexpected error occurred while signing out.');
    } finally {
      setLoading(false);
    }
  };

  // Send password reset email
  const sendPasswordResetEmail = async (email: string): Promise<AuthResult> => {
    if (!email) {
      return { 
        success: false, 
        error: 'Email is required' 
      };
    }
    
    setLoading(true);
    
    try {
      const result = await authService.sendPasswordResetEmail(email);
      return result;
    } catch (error) {
      console.error('Error sending password reset email:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unexpected error occurred' 
      };
    } finally {
      setLoading(false);
    }
  };

  // Log auth state when it changes for debugging
  useEffect(() => {
    console.log('AuthContext: Authentication state updated', {
      isAuthenticated: !!user,
      isLoading,
      loading,
    });
  }, [user, isLoading, loading]);

  const value: AuthContextType = {
    user,
    isLoading,
    loading,
    signIn,
    signUp,
    signOut,
    sendPasswordResetEmail
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Hook for easy access to the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 