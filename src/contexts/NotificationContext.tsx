import React, { createContext, useState, useContext, ReactNode, useEffect, useCallback } from 'react';
import { 
  getFirestore, 
  collection, 
  query, 
  where, 
  orderBy, 
  limit, 
  getDocs, 
  doc, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  addDoc, 
  Timestamp, 
  onSnapshot 
} from 'firebase/firestore';
import { getAuth, onAuthStateChanged } from 'firebase/auth';
import { app } from '@/lib/firebase';

// Initialize Firebase services
const db = getFirestore(app);
const auth = getAuth(app);

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  read: boolean;
  timestamp: number;
  userId?: string;
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: Error | null;
  addNotification: (notification: Omit<Notification, 'id' | 'read' | 'timestamp' | 'userId'>) => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  clearNotifications: () => Promise<void>;
}

// Create context with default values
const NotificationContext = createContext<NotificationContextType>({
  notifications: [],
  unreadCount: 0,
  loading: false,
  error: null,
  addNotification: async () => {},
  markAsRead: async () => {},
  markAllAsRead: async () => {},
  clearNotifications: async () => {},
});

// Props for provider
interface NotificationProviderProps {
  children: ReactNode;
}

// Provider component
export function NotificationProvider({ children }: NotificationProviderProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  
  // Auth state listener
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUserId(user?.uid || null);
    });
    
    return () => unsubscribe();
  }, []);
  
  // Fetch notifications when user changes
  useEffect(() => {
    let unsubscribe = () => {};
    
    if (userId) {
      setLoading(true);
      
      try {
        // Create a query for user's notifications
        const notificationsRef = collection(db, 'notifications');
        const userNotificationsQuery = query(
          notificationsRef,
          where('userId', '==', userId),
          orderBy('timestamp', 'desc'),
          limit(50)
        );
        
        // Set up a real-time listener
        unsubscribe = onSnapshot(userNotificationsQuery, 
          (snapshot) => {
            const notificationsList: Notification[] = [];
            snapshot.forEach(doc => {
              const data = doc.data();
              notificationsList.push({
                id: doc.id,
                title: data.title,
                message: data.message,
                type: data.type,
                read: data.read,
                timestamp: data.timestamp?.toMillis() || Date.now(),
                userId: data.userId,
              });
            });
            setNotifications(notificationsList);
            setLoading(false);
          },
          (err) => {
            console.error('Error fetching notifications:', err);
            setError(err as Error);
            setLoading(false);
          }
        );
      } catch (err) {
        console.error('Error setting up notifications listener:', err);
        setError(err as Error);
        setLoading(false);
      }
    } else {
      // Clear notifications when user logs out
      setNotifications([]);
      setLoading(false);
    }
    
    return () => unsubscribe();
  }, [userId]);

  // Calculate unread count
  const unreadCount = notifications.filter(n => !n.read).length;

  // Add a new notification
  const addNotification = async (notification: Omit<Notification, 'id' | 'read' | 'timestamp' | 'userId'>) => {
    try {
      if (!userId) {
        console.warn('Cannot add notification: User not authenticated');
        return;
      }
      
      await addDoc(collection(db, 'notifications'), {
        ...notification,
        read: false,
        timestamp: Timestamp.now(),
        userId: userId,
      });
    } catch (err) {
      console.error('Error adding notification:', err);
      setError(err as Error);
    }
  };

  // Mark a notification as read
  const markAsRead = async (id: string) => {
    try {
      const notificationRef = doc(db, 'notifications', id);
      await updateDoc(notificationRef, {
        read: true
      });
    } catch (err) {
      console.error('Error marking notification as read:', err);
      setError(err as Error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      if (!userId) return;
      
      // Get all unread notifications
      const unreadQuery = query(
        collection(db, 'notifications'),
        where('userId', '==', userId),
        where('read', '==', false)
      );
      
      const snapshot = await getDocs(unreadQuery);
      
      // Update each one (in a real production app, we'd use a batch write)
      const promises = snapshot.docs.map(doc => 
        updateDoc(doc.ref, { read: true })
      );
      
      await Promise.all(promises);
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
      setError(err as Error);
    }
  };

  // Clear all notifications
  const clearNotifications = async () => {
    try {
      if (!userId) return;
      
      // Get all user notifications
      const userNotificationsQuery = query(
        collection(db, 'notifications'),
        where('userId', '==', userId)
      );
      
      const snapshot = await getDocs(userNotificationsQuery);
      
      // Delete each one (in a real production app, we'd use a batch write)
      const promises = snapshot.docs.map(doc => 
        deleteDoc(doc.ref)
      );
      
      await Promise.all(promises);
    } catch (err) {
      console.error('Error clearing notifications:', err);
      setError(err as Error);
    }
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        loading,
        error,
        addNotification,
        markAsRead,
        markAllAsRead,
        clearNotifications,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
}

// Custom hook for using the notification context
export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
} 