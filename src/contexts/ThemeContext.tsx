import React, { createContext, useContext, useState, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Theme type definition
export type ThemeType = 'light' | 'dark' | 'system';

// Theme colors interface
export interface ThemeColors {
  primary: string;
  background: string;
  card: string;
  text: string;
  textSecondary: string;
  border: string;
  notification: string;
  danger: string;
  success: string;
  warning: string;
  info: string;
  buttonText: string;
  shadow: string;
  subtle: string;
  primaryLight: string;
  error: string;
}

// Define light theme colors
export const lightColors: ThemeColors = {
  primary: '#3B82F6',
  background: '#F9FAFB',
  card: '#FFFFFF',
  text: '#1F2937',
  textSecondary: '#6B7280',
  border: '#E5E7EB',
  notification: '#EF4444',
  danger: '#EF4444',
  success: '#10B981',
  warning: '#F59E0B',
  info: '#3B82F6',
  buttonText: '#FFFFFF',
  shadow: 'rgba(0,0,0,0.1)',
  subtle: 'rgba(0,0,0,0.05)',
  primaryLight: 'rgba(59,130,246,0.1)',
  error: '#EF4444'
};

// Define dark theme colors
export const darkColors: ThemeColors = {
  primary: '#60A5FA',
  background: '#111827',
  card: '#1F2937',
  text: '#F9FAFB',
  textSecondary: '#9CA3AF',
  border: '#374151',
  notification: '#EF4444',
  danger: '#F87171',
  success: '#34D399',
  warning: '#FBBF24',
  info: '#60A5FA',
  buttonText: '#FFFFFF',
  shadow: 'rgba(0,0,0,0.2)',
  subtle: 'rgba(255,255,255,0.05)',
  primaryLight: 'rgba(96,165,250,0.15)',
  error: '#F87171'
};

// Theme context interface
interface ThemeContextType {
  theme: ThemeType;
  colors: ThemeColors;
  isDark: boolean;
  setTheme: (theme: ThemeType) => void;
  toggleTheme: () => void;
}

// Create theme context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Theme provider props
interface ThemeProviderProps {
  children: React.ReactNode;
}

// Theme provider component
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const deviceTheme = useColorScheme();
  const [theme, setThemeState] = useState<ThemeType>('system');
  
  // Initialize theme from storage
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem('theme') as ThemeType | null;
        if (savedTheme) {
          setThemeState(savedTheme);
        } else {
          // If no theme is saved, explicitly set to dark theme
          await AsyncStorage.setItem('theme', 'dark');
          setThemeState('dark');
        }
      } catch (error) {
        console.error('Failed to load theme preference', error);
        // If there's an error, default to dark
        setThemeState('dark');
      }
    };
    
    loadTheme();
  }, []);
  
  // Save theme to storage when it changes
  const setTheme = async (newTheme: ThemeType) => {
    try {
      await AsyncStorage.setItem('theme', newTheme);
      setThemeState(newTheme);
    } catch (error) {
      console.error('Failed to save theme preference', error);
    }
  };
  
  // Toggle between light and dark theme
  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
  };
  
  // FORCE dark theme regardless of device or stored preferences
  const isDark = theme === 'system' ? deviceTheme === 'dark' : theme === 'dark';
  
  // Get the appropriate colors based on theme
  const colors = isDark ? darkColors : lightColors;
  
  // Context value
  const contextValue: ThemeContextType = {
    theme,
    colors,
    isDark,
    setTheme,
    toggleTheme,
  };
  
  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// Hook to use theme context
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};