import React, { createContext, useContext, ReactNode, useState, useEffect } from 'react';
import enTranslations from '../../translations/en.json';
import frTranslations from '../../translations/fr.json';
import esTranslations from '../../translations/es.json';

// Simplified translation context
interface TranslationContextValue {
  t: (scope: string, options?: Record<string, any>) => string;
  locale: string;
  setLocale: (locale: string) => Promise<void>;
  availableLanguages: Record<string, { code: string; name: string }>;
  isRTL: boolean;
}

// Translation data type - using any for simplicity since we can't model the exact structure
type TranslationData = Record<string, any>;

// Create context
export const TranslationContext = createContext<TranslationContextValue | undefined>(undefined);

// Provider props
interface TranslationProviderProps {
  children: ReactNode;
}

export function TranslationProvider({ children }: TranslationProviderProps) {
  const [locale, setLocaleState] = useState('en');
  const [translations, setTranslations] = useState<TranslationData>(enTranslations);
  
  // Load translations based on locale
  useEffect(() => {
    switch (locale) {
      case 'fr':
        setTranslations(frTranslations as TranslationData);
        break;
      case 'es':
        setTranslations(esTranslations as TranslationData);
        break;
      default:
        setTranslations(enTranslations as TranslationData);
    }
  }, [locale]);
  
  // Translation function that looks up values from the translation files
  const t = (scope: string, options?: Record<string, any>): string => {
    // Handle default value if provided
    if (options && 'defaultValue' in options) {
      return options.defaultValue as string;
    }
    
    // Handle dot notation for nested keys (e.g., "mealPlan.title")
    const keys = scope.split('.');
    let value: any = translations;
    
    // Navigate through the nested objects
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        // Return scope if translation not found
        return scope;
      }
    }
    
    return typeof value === 'string' ? value : scope;
  };
  
  // Set locale function
  const setLocale = async (newLocale: string) => {
    setLocaleState(newLocale);
  };
  
  // Context value
  const contextValue: TranslationContextValue = {
    t,
    locale,
    setLocale,
    availableLanguages: { 
      en: { code: 'en', name: 'English' },
      fr: { code: 'fr', name: 'Français' },
      es: { code: 'es', name: 'Español' }
    },
    isRTL: false,
  };

  return (
    <TranslationContext.Provider value={contextValue}>
      {children}
    </TranslationContext.Provider>
  );
}

// Hook to use translations
export function useTranslation() {
  const context = useContext(TranslationContext);
  
  if (context === undefined) {
    throw new Error('useTranslation must be used within a TranslationProvider');
  }
  
  return context;
} 