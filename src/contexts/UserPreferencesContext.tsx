import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { DIETARY_CATEGORIES, HEALTH_FOCUSES } from '@/services/healthySubstitutions';

// Define the types for our preferences
interface UserPreferencesState {
  dietaryPreferences: string[];
  healthGoals: string[];
  foodRestrictions: string[];
  hasSavedPreferences: boolean;
}

interface UserPreferencesContextType extends UserPreferencesState {
  setDietaryPreferences: (preferences: string[]) => void;
  setHealthGoals: (goals: string[]) => void;
  setFoodRestrictions: (restrictions: string[]) => void;
  savePreferences: () => Promise<void>;
  resetPreferences: () => void;
}

// Create context with default values
const UserPreferencesContext = createContext<UserPreferencesContextType>({
  dietaryPreferences: [],
  healthGoals: [],
  foodRestrictions: [],
  hasSavedPreferences: false,
  setDietaryPreferences: () => {},
  setHealthGoals: () => {},
  setFoodRestrictions: () => {},
  savePreferences: async () => {},
  resetPreferences: () => {},
});

// Storage keys
const STORAGE_KEYS = {
  DIETARY_PREFERENCES: 'user_dietary_preferences',
  HEALTH_GOALS: 'user_health_goals',
  FOOD_RESTRICTIONS: 'user_food_restrictions',
  HAS_SAVED_PREFERENCES: 'user_has_saved_preferences',
};

// Props for provider
interface UserPreferencesProviderProps {
  children: ReactNode;
}

// Provider component
export function UserPreferencesProvider({ children }: UserPreferencesProviderProps) {
  // State for user preferences
  const [dietaryPreferences, setDietaryPreferences] = useState<string[]>([]);
  const [healthGoals, setHealthGoals] = useState<string[]>([]);
  const [foodRestrictions, setFoodRestrictions] = useState<string[]>([]);
  const [hasSavedPreferences, setHasSavedPreferences] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Load preferences from storage on mount
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        const [
          storedDietaryPrefs,
          storedHealthGoals,
          storedRestrictions,
          storedHasSavedPreferences,
        ] = await Promise.all([
          AsyncStorage.getItem(STORAGE_KEYS.DIETARY_PREFERENCES),
          AsyncStorage.getItem(STORAGE_KEYS.HEALTH_GOALS),
          AsyncStorage.getItem(STORAGE_KEYS.FOOD_RESTRICTIONS),
          AsyncStorage.getItem(STORAGE_KEYS.HAS_SAVED_PREFERENCES),
        ]);

        if (storedDietaryPrefs) {
          setDietaryPreferences(JSON.parse(storedDietaryPrefs));
        }
        
        if (storedHealthGoals) {
          setHealthGoals(JSON.parse(storedHealthGoals));
        }
        
        if (storedRestrictions) {
          setFoodRestrictions(JSON.parse(storedRestrictions));
        }
        
        if (storedHasSavedPreferences) {
          setHasSavedPreferences(JSON.parse(storedHasSavedPreferences));
        }
      } catch (error) {
        console.error('Error loading preferences:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadPreferences();
  }, []);

  // Save preferences to storage
  const savePreferences = async () => {
    try {
      await Promise.all([
        AsyncStorage.setItem(STORAGE_KEYS.DIETARY_PREFERENCES, JSON.stringify(dietaryPreferences)),
        AsyncStorage.setItem(STORAGE_KEYS.HEALTH_GOALS, JSON.stringify(healthGoals)),
        AsyncStorage.setItem(STORAGE_KEYS.FOOD_RESTRICTIONS, JSON.stringify(foodRestrictions)),
        AsyncStorage.setItem(STORAGE_KEYS.HAS_SAVED_PREFERENCES, JSON.stringify(true)),
      ]);
      
      setHasSavedPreferences(true);
    } catch (error) {
      console.error('Error saving preferences:', error);
      throw error;
    }
  };

  // Reset preferences
  const resetPreferences = () => {
    setDietaryPreferences([]);
    setHealthGoals([]);
    setFoodRestrictions([]);
    
    // Clear from storage
    Object.values(STORAGE_KEYS).forEach(key => {
      AsyncStorage.removeItem(key);
    });
    
    setHasSavedPreferences(false);
  };

  // Memoize context value to prevent unnecessary re-renders
  const value = {
    dietaryPreferences,
    setDietaryPreferences,
    healthGoals,
    setHealthGoals,
    foodRestrictions,
    setFoodRestrictions,
    hasSavedPreferences,
    savePreferences,
    resetPreferences,
  };

  // Show loading state or render children
  if (isLoading) {
    return null; // Or a loading indicator
  }

  return (
    <UserPreferencesContext.Provider value={value}>
      {children}
    </UserPreferencesContext.Provider>
  );
}

// Custom hook for using the preferences context
export function useUserPreferences() {
  const context = useContext(UserPreferencesContext);
  if (context === undefined) {
    throw new Error('useUserPreferences must be used within a UserPreferencesProvider');
  }
  return context;
}

// Export available options for type safety and autocomplete
export const AVAILABLE_DIETARY_CATEGORIES = DIETARY_CATEGORIES;
export const AVAILABLE_HEALTH_FOCUSES = HEALTH_FOCUSES;

// Common allergies
export const COMMON_ALLERGIES = [
  'dairy',
  'eggs',
  'peanuts',
  'tree nuts',
  'fish',
  'shellfish',
  'wheat',
  'soy',
  'sesame',
  'gluten',
]; 