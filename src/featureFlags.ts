import { getFeatureFlag } from '@/services/posthogService';

// Feature flag hook
import { useState, useEffect } from 'react';
import { Platform } from 'react-native';

// Comprehensive feature flag keys based on docs/00-ft.md
export const FEATURE_FLAGS = {
  // Core MVP Features
  FOOD_SCANNING: 'food_scanning',
  WATER_TRACKING: 'water_tracking',
  USER_PROFILE: 'user_profile',
  LIDAR_SCANNING: 'lidar_scanning',
  PAYMENTS: 'payments',
  AUTHENTICATION: 'authentication',
  OFFLINE_MODE: 'offline_mode',

  // Storage & Sync Features
  FIREBASE_SYNC: 'firebase_sync',
  IMAGE_STORAGE: 'image_storage',
  PROFILE_PHOTOS: 'profile_photos',
  SCAN_HISTORY: 'scan_history',

  // Hidden Features (Not in MVP navigation)
  DASHBOARD_HOME: 'dashboard_home',
  FOOD_HISTORY: 'food_history',
  MEAL_PLANNER: 'meal_planner',
  SOCIAL_FEATURES: 'social_features',
  DIETITIAN_SERVICES: 'dietitian_services',
  CHALLENGES: 'challenges',
  RECIPES: 'recipes',

  // Advanced Features
  RECIPE_ALTERNATIVES: 'recipe_alternatives',
  RECIPE_GENERATION: 'recipe_generation',
  NUTRITION_TIMELINE: 'nutrition_timeline',
  GOAL_RECOMMENDATIONS: 'goal_recommendations',
  ACHIEVEMENT_SYSTEM: 'achievement_system',

  // Platform-Specific Features
  IOS_LIDAR_SCANNING: 'ios_lidar_scanning',
  IOS_HEALTH_KIT: 'ios_health_kit',
  IOS_ARKIT_OVERLAY: 'ios_arkit_overlay',
  ANDROID_HEALTH_CONNECT: 'android_health_connect',
  ANDROID_2D_ESTIMATION: 'android_2d_estimation',
  WEB_LIMITED_MODE: 'web_limited_mode',

  // Beta/Experimental Features
  AI_MEAL_RECOMMENDATIONS: 'ai_meal_recommendations',
  VOICE_COMMANDS: 'voice_commands',
  WEARABLE_INTEGRATION: 'wearable_integration',
  BARCODE_SCANNING: 'barcode_scanning',
  NUTRITION_COACH: 'nutrition_coach',
  SLEEP_TRACKING: 'sleep_tracking',
  WORKOUT_TRACKING: 'workout_tracking',
  MINDFULNESS_FEATURES: 'mindfulness_features',
  FASTING_TRACKER: 'fasting_tracker',
} as const;

// Default feature states (used as fallback if PostHog is unavailable)
const DEFAULT_STATES = {
  // MVP Features - Enabled by default
  [FEATURE_FLAGS.FOOD_SCANNING]: true,
  [FEATURE_FLAGS.WATER_TRACKING]: true,
  [FEATURE_FLAGS.USER_PROFILE]: true,
  [FEATURE_FLAGS.LIDAR_SCANNING]: true,
  [FEATURE_FLAGS.PAYMENTS]: true,
  [FEATURE_FLAGS.AUTHENTICATION]: true,
  [FEATURE_FLAGS.OFFLINE_MODE]: true,

  // Storage & Sync - Enabled
  [FEATURE_FLAGS.FIREBASE_SYNC]: true,
  [FEATURE_FLAGS.IMAGE_STORAGE]: true,
  [FEATURE_FLAGS.PROFILE_PHOTOS]: true,
  [FEATURE_FLAGS.SCAN_HISTORY]: true,

  // Hidden Features - Disabled
  [FEATURE_FLAGS.DASHBOARD_HOME]: false,
  [FEATURE_FLAGS.FOOD_HISTORY]: false,
  [FEATURE_FLAGS.MEAL_PLANNER]: false,
  [FEATURE_FLAGS.SOCIAL_FEATURES]: false,
  [FEATURE_FLAGS.DIETITIAN_SERVICES]: false,
  [FEATURE_FLAGS.CHALLENGES]: false,
  [FEATURE_FLAGS.RECIPES]: false,

  // Advanced Features - Disabled
  [FEATURE_FLAGS.RECIPE_ALTERNATIVES]: false,
  [FEATURE_FLAGS.RECIPE_GENERATION]: false,
  [FEATURE_FLAGS.NUTRITION_TIMELINE]: false,
  [FEATURE_FLAGS.GOAL_RECOMMENDATIONS]: false,
  [FEATURE_FLAGS.ACHIEVEMENT_SYSTEM]: false,

  // Platform-Specific - Conditional defaults
  [FEATURE_FLAGS.IOS_LIDAR_SCANNING]: true,
  [FEATURE_FLAGS.IOS_HEALTH_KIT]: false,
  [FEATURE_FLAGS.IOS_ARKIT_OVERLAY]: true,
  [FEATURE_FLAGS.ANDROID_HEALTH_CONNECT]: false,
  [FEATURE_FLAGS.ANDROID_2D_ESTIMATION]: true,
  [FEATURE_FLAGS.WEB_LIMITED_MODE]: false,

  // Beta Features - Disabled
  [FEATURE_FLAGS.AI_MEAL_RECOMMENDATIONS]: false,
  [FEATURE_FLAGS.VOICE_COMMANDS]: false,
  [FEATURE_FLAGS.WEARABLE_INTEGRATION]: false,
  [FEATURE_FLAGS.BARCODE_SCANNING]: false,
  [FEATURE_FLAGS.NUTRITION_COACH]: false,
  [FEATURE_FLAGS.SLEEP_TRACKING]: false,
  [FEATURE_FLAGS.WORKOUT_TRACKING]: false,
  [FEATURE_FLAGS.MINDFULNESS_FEATURES]: false,
  [FEATURE_FLAGS.FASTING_TRACKER]: false,
} as const;

// Type for feature flags
export type FeatureFlag = (typeof FEATURE_FLAGS)[keyof typeof FEATURE_FLAGS];

export function useFeatureFlag(flag: FeatureFlag): boolean {
  // Platform-specific defaults
  const getPlatformDefault = (flag: FeatureFlag): boolean => {
    if (Platform.OS === 'ios' && flag.startsWith('android_')) return false;
    if (Platform.OS === 'android' && flag.startsWith('ios_')) return false;
    if (
      Platform.OS === 'web' &&
      (flag.includes('lidar') || flag.includes('arkit'))
    )
      return false;
    return DEFAULT_STATES[flag];
  };

  const [enabled, setEnabled] = useState(getPlatformDefault(flag));

  useEffect(() => {
    // Fetch feature flag from PostHog
    getFeatureFlag(flag)
      .then((isEnabled) => {
        setEnabled(isEnabled);
      })
      .catch(() => {
        // On error, use platform-specific default
        setEnabled(getPlatformDefault(flag));
      });
  }, [flag]);

  return enabled;
}

// Async function to check feature flags
export async function isFeatureEnabled(flag: FeatureFlag): Promise<boolean> {
  try {
    const enabled = await getFeatureFlag(flag);
    return enabled;
  } catch (error) {
    console.warn(
      `Failed to fetch feature flag ${flag}, using default:`,
      DEFAULT_STATES[flag]
    );
    return DEFAULT_STATES[flag];
  }
}

// Check if a feature is enabled synchronously (uses last known state)
export function isFeatureEnabledSync(flag: FeatureFlag): boolean {
  // This returns the default state immediately
  // In a real app, you might cache the last known state
  return DEFAULT_STATES[flag];
}

// Export feature flags interface for components (backwards compatibility)
export const FEATURES = {
  // MVP Features
  FOOD_SCANNING: () => isFeatureEnabledSync(FEATURE_FLAGS.FOOD_SCANNING),
  WATER_TRACKING: () => isFeatureEnabledSync(FEATURE_FLAGS.WATER_TRACKING),
  USER_PROFILE: () => isFeatureEnabledSync(FEATURE_FLAGS.USER_PROFILE),
  LIDAR_SCANNING: () => isFeatureEnabledSync(FEATURE_FLAGS.LIDAR_SCANNING),
  PAYMENTS: () => isFeatureEnabledSync(FEATURE_FLAGS.PAYMENTS),

  // Non-MVP features (kept for compatibility)
  WORKOUT_TRACKING: () => isFeatureEnabledSync(FEATURE_FLAGS.WORKOUT_TRACKING),
  SLEEP_TRACKING: () => isFeatureEnabledSync(FEATURE_FLAGS.SLEEP_TRACKING),
  MEAL_PLANNING: () => isFeatureEnabledSync(FEATURE_FLAGS.MEAL_PLANNER),
  SOCIAL_SHARING: () => isFeatureEnabledSync(FEATURE_FLAGS.SOCIAL_FEATURES),
  WEARABLE_INTEGRATION: () =>
    isFeatureEnabledSync(FEATURE_FLAGS.WEARABLE_INTEGRATION),
  DIETITIAN_FEATURES: () =>
    isFeatureEnabledSync(FEATURE_FLAGS.DIETITIAN_SERVICES),
};

// For simple boolean checks (legacy support)
export const FEATURES_SIMPLE = {
  FOOD_SCANNING: true,
  WATER_TRACKING: true,
  USER_PROFILE: true,
  LIDAR_SCANNING: true,
  PAYMENTS: true,
  WORKOUT_TRACKING: false,
  SLEEP_TRACKING: false,
  MEAL_PLANNING: false,
  SOCIAL_SHARING: false,
  WEARABLE_INTEGRATION: false,
  DIETITIAN_FEATURES: false,
};
