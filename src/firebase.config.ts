import { Platform } from 'react-native';

// Firebase configuration
export const firebaseConfig = {
  apiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.EXPO_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID,
  databaseURL: process.env.EXPO_PUBLIC_FIREBASE_DATABASE_URL
};

// Firestore configuration options for offline support
export const firestoreSettings = {
  cacheSizeBytes: 100 * 1024 * 1024, // 100MB cache size
  experimentalForceLongPolling: true, // Required for React Native
  ignoreUndefinedProperties: true,
};

// Configure cache expiration (in milliseconds)
export const cacheConfig = {
  defaultCacheTTL: 60 * 60 * 1000, // 1 hour default
  profileCacheTTL: 24 * 60 * 60 * 1000, // 24 hours for profile data
  mealsCacheTTL: 30 * 60 * 1000, // 30 minutes for meals
  recommendationsCacheTTL: 2 * 60 * 60 * 1000 // 2 hours for recommendations
};

// Check if Firebase is properly configured
export function isFirebaseConfigured(): boolean {
  return !!firebaseConfig.apiKey && !!firebaseConfig.projectId;
}

// This file now only exports configuration, 
// the actual Firebase initialization happens in lib/firebase.ts 