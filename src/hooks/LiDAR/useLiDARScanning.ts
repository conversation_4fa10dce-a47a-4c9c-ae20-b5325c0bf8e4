/**
 * Custom hook for LiDAR scanning functionality
 */
import { useState, useEffect, useCallback, useRef } from 'react';
import { Platform, NativeModules, NativeEventEmitter, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Haptics from 'expo-haptics';
import { ProcessingStage, ScanQualityLevel, StabilityLevel } from '@/utils/LiDAR/types';
import { checkDeviceLiDARSupport, DeviceCapability } from '@/utils/volumeEstimator';
import { useAuth } from '@/contexts/AuthContext'; // Import auth context for user ID
import { 
  checkLiDARAvailability, 
  syncCalibrationData, 
  saveCalibrationToCloud 
} from '@/services/lidarScanningService';
import {
  getDeviceScalingCorrection,
  getARKitScalingConfiguration,
  applyDeviceCorrectionsToVolumeData,
  hasKnownScalingIssues
} from '@/utils/LiDAR/deviceSpecificScaling';
import {
  getOptimalScanningConfig,
  throttle,
  monitorPerformance,
  shouldDeferHeavyOperation,
  getDeviceCapabilities
} from '@/services/vision/performanceOptimization';
import {
  getOptimizedFeedbackSettings,
  reuseCalculation,
  startLatencyMeasurement,
  endLatencyMeasurement,
  getARRenderingConfig,
  predictMotion,
  shouldSkipFrame
} from '@/utils/LiDAR/latencyOptimization';
// Import advanced latency optimization utilities
import {
  getEnhancedFeedbackSettings,
  processingWithTimeBudget,
  trackDeviceMotion,
  getMotionAdaptiveQuality,
  getSmoothedPosition,
  processBatch,
  prewarmMeshProcessing,
  getAccelerationStructureConfig,
  shouldUseAdaptiveSkipFrame,
  getOptimalRenderingSettings
} from '@/utils/LiDAR/advancedLatencyOptimization';

// Import native ARKit module
const { ARKitVolumeScanner } = NativeModules;

// Create event emitter for ARKit events
const ARKitEventEmitter = new NativeEventEmitter(
  ARKitVolumeScanner as any
);

// Debounce function to limit frequent updates
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      func(...args);
    };
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

interface UseLiDARScanningProps {
  onScanningFeedback?: (quality: ScanQualityLevel, stability: StabilityLevel) => void;
  enableObjectTracking?: boolean;
  enhancedCalibration?: boolean;
}

interface UseLiDARScanningResult {
  deviceSupportsLiDAR: DeviceCapability;
  isScanningActive: boolean;
  processingStage: ProcessingStage;
  planeDetected: boolean;
  scanProgress: number;
  scanQuality: ScanQualityLevel;
  scanStability: StabilityLevel;
  meshCount: number;
  scanningTips: string[];
  edgeDetectionQuality: ScanQualityLevel;
  hasDepthData: boolean;
  optimalScanningDistance: boolean;
  guidePosition: { x: number; y: number };
  calibrationFactor: number;
  savedCalibrations: {[key: string]: number};
  objectsBoundingBoxes: any[]; // New: tracking bounding boxes for multiple objects
  objectsInView: number; // New: number of objects tracked in current scan
  scanCompleteness: number; // New: estimation of scan completeness (0-1)
  averageLatencyMs: number; // New: average AR feedback loop latency
  
  startLiDARScanning: () => Promise<void>;
  stopLiDARScanning: () => Promise<void>;
  calculateLiDARVolume: () => Promise<any>;
  startCalibrationScanning: () => Promise<void>;
  performCalibration: (objectName: string, volumeStr: string) => Promise<void>;
  cancelCalibration: () => Promise<void>;
  setProcessingStage: (stage: ProcessingStage) => void;
  syncCalibrationWithCloud: () => Promise<void>;
  resetScan: () => void; // New: reset current scan data
  toggleObjectTracking: (enabled: boolean) => void; // New: toggle object tracking
}

export default function useLiDARScanning({
  onScanningFeedback,
  enableObjectTracking = true,
  enhancedCalibration = true
}: UseLiDARScanningProps = {}): UseLiDARScanningResult {
  // Auth context for user ID
  const { user } = useAuth();
  
  // State
  const [deviceSupportsLiDAR, setDeviceSupportsLiDAR] = useState<DeviceCapability>(DeviceCapability.UNKNOWN);
  const [isScanningActive, setIsScanningActive] = useState(false);
  const [processingStage, setProcessingStage] = useState<ProcessingStage>('idle');
  const [planeDetected, setPlaneDetected] = useState(false);
  const [scanProgress, setScanProgress] = useState(0);
  const [scanQuality, setScanQuality] = useState<ScanQualityLevel>('medium');
  const [scanStability, setScanStability] = useState<StabilityLevel>('unstable');
  const [meshCount, setMeshCount] = useState(0);
  const [scanningTips, setScanningTips] = useState<string[]>([]);
  const [edgeDetectionQuality, setEdgeDetectionQuality] = useState<ScanQualityLevel>('low');
  const [hasDepthData, setHasDepthData] = useState(false);
  const [optimalScanningDistance, setOptimalScanningDistance] = useState(false);
  const [guidePosition, setGuidePosition] = useState({ x: 0, y: 0 });
  const [calibrationFactor, setCalibrationFactor] = useState(1.0);
  const [savedCalibrations, setSavedCalibrations] = useState<{[key: string]: number}>({});
  const [lastSyncTime, setLastSyncTime] = useState<number>(0);
  
  // New state for enhanced features
  const [objectsBoundingBoxes, setObjectsBoundingBoxes] = useState<any[]>([]);
  const [objectsInView, setObjectsInView] = useState(0);
  const [scanCompleteness, setScanCompleteness] = useState(0);
  const [isObjectTrackingEnabled, setIsObjectTrackingEnabled] = useState(enableObjectTracking);
  const [scanAngles, setScanAngles] = useState<Set<string>>(new Set());
  const [calibrationQuality, setCalibrationQuality] = useState<'low' | 'medium' | 'high'>('medium');
  
  // Performance optimization state
  const [devicePerformanceLevel, setDevicePerformanceLevel] = useState<'low' | 'medium' | 'high'>('medium');
  const [isLowPowerMode, setIsLowPowerMode] = useState(false);
  
  // Latency tracking state
  const [feedbackLoopSettings, setFeedbackLoopSettings] = useState<any>(null);
  const [averageLatencyMs, setAverageLatencyMs] = useState(0);
  const latencyReadingsRef = useRef<number[]>([]);
  const frameIdCounterRef = useRef(0);
  const nextFrameTimeRef = useRef(0);
  
  // New advanced latency optimization state
  const [enhancedFeedbackSettings, setEnhancedFeedbackSettings] = useState<any>(null);
  const motionLevelRef = useRef<'none' | 'low' | 'medium' | 'high'>('none');
  const smoothingEnabledRef = useRef(true);
  const positionKeyRef = useRef('guide-position');
  const renderingSettingsRef = useRef<any>(null);
  
  // Update device performance information and AR settings
  useEffect(() => {
    (async () => {
      try {
        // Get device capabilities
        const capabilities = await getDeviceCapabilities();
        setDevicePerformanceLevel(capabilities.performanceLevel);
        setIsLowPowerMode(capabilities.isLowBatteryMode);
        
        // Get optimized feedback loop settings
        const settings = await getOptimizedFeedbackSettings();
        setFeedbackLoopSettings(settings);
        
        // Get enhanced feedback settings for advanced optimizations
        const enhancedSettings = await getEnhancedFeedbackSettings();
        setEnhancedFeedbackSettings(enhancedSettings);
        
        // Set smoothing enabled based on performance level
        smoothingEnabledRef.current = enhancedSettings.useTemporalSmoothing;
        
        // Get optimal rendering settings
        const renderSettings = getOptimalRenderingSettings();
        renderingSettingsRef.current = renderSettings;
        
        // Apply AR rendering config if supported
        if (Platform.OS === 'ios' && NativeModules.ARKitVolumeScanner) {
          const renderConfig = getARRenderingConfig();
          NativeModules.ARKitVolumeScanner.setRenderingConfig({
            ...renderConfig,
            msaaSamples: renderSettings.msaaSamples,
            useMipMapping: renderSettings.useMipMapping
          });
          
          // Prewarm mesh processing to avoid latency spikes
          prewarmMeshProcessing();
        }
      } catch (error) {
        console.error("Error initializing AR settings:", error);
      }
    })();
  }, []);
  
  // Reset scan data
  const resetScan = useCallback(() => {
    setPlaneDetected(false);
    setScanProgress(0);
    setMeshCount(0);
    setScanningTips([]);
    setEdgeDetectionQuality('low');
    setHasDepthData(false);
    setOptimalScanningDistance(false);
    setObjectsBoundingBoxes([]);
    setObjectsInView(0);
    setScanCompleteness(0);
    setScanAngles(new Set());
    
    // Reset latency measurements
    latencyReadingsRef.current = [];
    setAverageLatencyMs(0);
    frameIdCounterRef.current = 0;
    nextFrameTimeRef.current = 0;
  }, []);
  
  // Toggle object tracking
  const toggleObjectTracking = useCallback((enabled: boolean) => {
    setIsObjectTrackingEnabled(enabled);
    if (isScanningActive && Platform.OS === 'ios') {
      // Update native module config
      ARKitVolumeScanner.updateScanningConfig({
        trackObjects: enabled
      }).catch(console.error);
    }
  }, [isScanningActive]);
  
  // Record latency measurement and update average
  const recordLatency = useCallback((latencyMs: number) => {
    const readings = latencyReadingsRef.current;
    
    // Add new reading
    readings.push(latencyMs);
    
    // Keep only last 10 readings
    if (readings.length > 10) {
      readings.shift();
    }
    
    // Calculate average
    const sum = readings.reduce((acc, val) => acc + val, 0);
    const avg = sum / readings.length;
    
    setAverageLatencyMs(avg);
  }, []);
  
  // Provide scanning feedback
  const provideScanningFeedback = useCallback((quality: ScanQualityLevel, stability: StabilityLevel) => {
    // Only provide feedback when significant changes occur
    if (scanQuality !== quality || scanStability !== stability) {
      setScanQuality(quality);
      setScanStability(stability);
      
      // Only provide haptic feedback if we're not in low power mode to save battery
      if (!isLowPowerMode) {
        // Provide haptic feedback based on scan quality
        if (quality === 'high' && stability === 'stable') {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        } else if (quality === 'medium' || stability === 'moderate') {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        } else {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
      }
      
      // Call external feedback handler if provided
      if (onScanningFeedback) {
        onScanningFeedback(quality, stability);
      }
    }
  }, [scanQuality, scanStability, onScanningFeedback, isLowPowerMode]);
  
  // Generate a unique frame ID
  const getNextFrameId = useCallback(() => {
    const frameId = `frame-${frameIdCounterRef.current++}`;
    return frameId;
  }, []);
  
  // Performance-optimized scan angle processing
  const processScanAngle = throttle(
    (deviceOrientation: { pitch: number; yaw: number }) => {
      // Start latency measurement
      const frameId = getNextFrameId();
      startLatencyMeasurement(frameId);
      
      // Discretize the angle to 45-degree segments (8 segments for a complete 360° view)
      const angleSegment = Math.floor((deviceOrientation.yaw % 360) / 45);
      const pitchSegment = Math.floor((deviceOrientation.pitch + 90) / 60); // 3 segments for pitch (-90 to 90)
      
      const angleKey = `${angleSegment}-${pitchSegment}`;
      
      setScanAngles(prev => {
        const newAngles = new Set(prev);
        newAngles.add(angleKey);
        
        // Calculate completeness based on angle coverage
        // Perfect coverage would be 8 yaw segments x 3 pitch segments = 24 total segments
        const completeness = Math.min(1, newAngles.size / 12); // We only need about 12 segments for good coverage
        setScanCompleteness(completeness);
        
        // Add relevant tips based on scan completeness
        if (completeness > 0.25 && completeness < 0.4) {
          addScanningTip("Try scanning from different angles for better results");
        } else if (completeness >= 0.5 && completeness < 0.75) {
          addScanningTip("Looking good! Move around to get the top and bottom");
        } else if (completeness >= 0.75) {
          addScanningTip("Great scan coverage! You can finalize when ready");
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        }
        
        return newAngles;
      });
      
      // End latency measurement and record
      const latency = endLatencyMeasurement(frameId);
      recordLatency(latency);
    },
    // Throttle based on device performance level
    devicePerformanceLevel === 'high' ? 200 : devicePerformanceLevel === 'medium' ? 500 : 1000
  );
  
  // Check LiDAR support on mount
  useEffect(() => {
    (async () => {
      try {
        // Use the service for better device checking
        const lidarCheck = await checkLiDARAvailability();
        
        if (lidarCheck.available) {
          setDeviceSupportsLiDAR(DeviceCapability.LIDAR_SUPPORTED);
        } else {
          setDeviceSupportsLiDAR(DeviceCapability.NO_LIDAR);
          
          // Show a message if reason is provided
          if (lidarCheck.reason) {
            console.log(`LiDAR not available: ${lidarCheck.reason}`);
          }
        }
      } catch (error) {
        console.error("Error checking LiDAR availability:", error);
        setDeviceSupportsLiDAR(DeviceCapability.NO_LIDAR);
      }
      
      // Load saved calibrations from local storage
      try {
        const savedCalibrationData = await AsyncStorage.getItem('lidar_calibrations');
        if (savedCalibrationData) {
          const parsedData = JSON.parse(savedCalibrationData);
          setSavedCalibrations(parsedData);
          
          // Apply default calibration if present
          if (parsedData.defaultCalibration) {
            setCalibrationFactor(parsedData.defaultCalibration);
            
            // Determine calibration quality based on number of calibrations and recency
            if (Object.keys(parsedData).length > 3 && parsedData.lastCalibrationDate) {
              const lastCalDate = new Date(parsedData.lastCalibrationDate);
              const daysAgo = (Date.now() - lastCalDate.getTime()) / (1000 * 60 * 60 * 24);
              
              if (daysAgo < 7) {
                setCalibrationQuality('high');
              } else if (daysAgo < 30) {
                setCalibrationQuality('medium');
              } else {
                setCalibrationQuality('low');
              }
            }
          }
        }
      } catch (error) {
        console.error('Error loading calibration data:', error);
      }
      
      // Sync with cloud if user is logged in
      if (user?.id) {
        syncCalibrationWithCloud();
      }
    })();
  }, [user?.id]);
  
  // Debounced tip adder to prevent flooding the UI with tips
  const addScanningTip = useCallback(
    debounce((tip: string) => {
      setScanningTips(prev => {
        // Don't duplicate tips
        if (prev.includes(tip)) return prev;
        // Keep only last 3 tips
        const newTips = [...prev, tip];
        return newTips.slice(Math.max(0, newTips.length - 3));
      });
    }, 2000),
    []
  );
  
  // Low-latency scan progress handler using advanced optimization techniques
  const handleScanProgress = useCallback(async (event: any) => {
    // Use adaptive frame skipping for more intelligent frame pacing
    const now = Date.now();
    const frameBudgetMs = enhancedFeedbackSettings?.frameBudgetMs || 16;
    
    if (enhancedFeedbackSettings && now < nextFrameTimeRef.current) {
      return;
    }
    
    // Start latency measurement
    const frameId = getNextFrameId();
    startLatencyMeasurement(frameId);
    
    // Track device motion to adapt processing quality
    let motionLevel = 'medium' as 'none' | 'low' | 'medium' | 'high';
    if (event.deviceMovement) {
      motionLevel = trackDeviceMotion(event.deviceMovement);
      motionLevelRef.current = motionLevel;
    }
    
    // Use time-budgeted processing to ensure we don't block the UI thread
    await processingWithTimeBudget(async () => {
      // Update state with frame data
      setScanProgress(event.progress);
      setMeshCount(event.meshCount || 0);
      
      // Calculate scan quality based on mesh count
      let quality: ScanQualityLevel = 'low';
      let stability: StabilityLevel = 'unstable';
      
      // Use memoization for common calculations
      const qualityCalculation = await reuseCalculation(
        `quality-${event.meshCount}`,
        async () => {
          // This is the calculation we want to cache
          if (event.meshCount < 10) {
            return 'low' as ScanQualityLevel;
          } else if (event.meshCount < 30) {
            return 'medium' as ScanQualityLevel;
          } else {
            return 'high' as ScanQualityLevel;
          }
        }
      );
      
      quality = qualityCalculation;
      
      // Calculate stability
      if (event.deviceMovement) {
        if (event.deviceMovement < 0.02) {
          stability = 'stable';
        } else if (event.deviceMovement < 0.05) {
          stability = 'moderate';
        } else {
          stability = 'unstable';
        }
      }
      
      // Process device orientation data if available
      if (event.deviceOrientation) {
        // Apply adaptive quality based on motion
        const motionQualityFactor = getMotionAdaptiveQuality(
          enhancedFeedbackSettings?.motionScalingFactor || 0.5
        );
        
        // Use higher throttling during rapid motion to maintain responsiveness
        const adaptiveThrottleInterval = 
          motionLevel === 'high' ? 500 :
          motionLevel === 'medium' ? 300 :
          motionLevel === 'low' ? 200 : 100;
        
        const processScanAngleThrottled = throttle(
          processScanAngle,
          adaptiveThrottleInterval
        );
        
        processScanAngleThrottled(event.deviceOrientation);
      }
      
      // Update feedback
      provideScanningFeedback(quality, stability);
    }, frameBudgetMs);
    
    // End latency measurement
    const latency = endLatencyMeasurement(frameId);
    recordLatency(latency);
    
    // Set next frame time based on optimal settings and current motion level
    if (enhancedFeedbackSettings) {
      // Use shorter intervals during high motion for more responsiveness
      const adaptiveFrameInterval = 
        motionLevelRef.current === 'high' ? enhancedFeedbackSettings.frameCaptureInterval * 0.7 :
        motionLevelRef.current === 'medium' ? enhancedFeedbackSettings.frameCaptureInterval * 0.85 :
        enhancedFeedbackSettings.frameCaptureInterval;
      
      nextFrameTimeRef.current = now + adaptiveFrameInterval;
    }
    
    // Use advanced adaptive frame skipping
    if (await shouldUseAdaptiveSkipFrame(latency, frameBudgetMs)) {
      // Add extra delay to next frame to allow system to catch up
      nextFrameTimeRef.current += 50;
    }
  }, [
    enhancedFeedbackSettings, 
    processScanAngle,
    provideScanningFeedback, 
    recordLatency, 
    getNextFrameId
  ]);
  
  // Enhanced ARKit event listeners
  useEffect(() => {
    if (Platform.OS !== 'ios' || deviceSupportsLiDAR !== DeviceCapability.LIDAR_SUPPORTED) return;
    
    // Event listeners
    const planeDetectedSubscription = ARKitEventEmitter.addListener(
      'onPlaneDetected',
      () => {
        setPlaneDetected(true);
        // Add feedback when plane is detected
        addScanningTip("Surface detected! Now scan the food from multiple angles");
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    );
    
    // Create throttled scan progress handler with dynamic interval
    const scanProgressInterval = feedbackLoopSettings?.meshProcessingInterval || 
                                (devicePerformanceLevel === 'high' ? 100 : 
                                devicePerformanceLevel === 'medium' ? 200 : 500);
    
    const throttledScanProgressHandler = throttle(handleScanProgress, scanProgressInterval);
    
    const scanProgressSubscription = ARKitEventEmitter.addListener(
      'onScanProgress',
      throttledScanProgressHandler
    );
    
    const distanceCheckSubscription = ARKitEventEmitter.addListener(
      'onDistanceCheck',
      (event) => {
        // Update optimal distance status
        setOptimalScanningDistance(event.isOptimalDistance);
        
        // Update guide position for AR overlay using advanced smoothing
        if (event.guidePosition) {
          // Get position and velocity from event
          const position: [number, number, number] = [
            event.guidePosition.x,
            event.guidePosition.y,
            0
          ];
          
          const velocity: [number, number, number] | undefined = event.velocity ? [
            event.velocity.x,
            event.velocity.y,
            0
          ] : undefined;
          
          // Apply motion prediction and temporal smoothing for smoother AR positioning
          if (smoothingEnabledRef.current && motionLevelRef.current !== 'high') {
            // Use smoothing for stable or moderate motion to reduce jitter
            const smoothedPosition = getSmoothedPosition(
              positionKeyRef.current,
              position,
              velocity,
              averageLatencyMs,
              // Vary smoothing strength by motion level
              motionLevelRef.current === 'none' ? 0.8 :
              motionLevelRef.current === 'low' ? 0.6 : 0.3
            );
            
            setGuidePosition({
              x: smoothedPosition[0],
              y: smoothedPosition[1]
            });
          } else {
            // During high motion, use prediction without smoothing for responsiveness
            if (velocity && averageLatencyMs > 20) {
              const predictedPosition = predictMotion(
                position,
                velocity,
                averageLatencyMs
              );
              
              setGuidePosition({
                x: predictedPosition[0],
                y: predictedPosition[1]
              });
            } else {
              setGuidePosition({
                x: position[0],
                y: position[1]
              });
            }
          }
        }
        
        // Provide feedback on distance
        if (!optimalScanningDistance && event.isOptimalDistance) {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          addScanningTip("Perfect distance! Hold steady to get the best scan");
        }
      }
    );
    
    // Create throttled mesh update handler
    const meshUpdateInterval = feedbackLoopSettings?.meshProcessingInterval || 
                              (devicePerformanceLevel === 'high' ? 200 : 
                              devicePerformanceLevel === 'medium' ? 500 : 1000);
    
    const handleMeshUpdated = throttle((event: any) => {
      setMeshCount(event.meshCount || 0);
      
      // Provide scanning tips based on progress
      if (event.meshCount === 10) {
        addScanningTip("Try moving around to capture all sides of the food");
      } else if (event.meshCount === 25) {
        addScanningTip("Looking good! A bit more scanning will improve accuracy");
      }
    }, meshUpdateInterval);
    
    const meshUpdatedSubscription = ARKitEventEmitter.addListener(
      'onMeshUpdated',
      handleMeshUpdated
    );
    
    // Enhanced depth analysis event
    const depthAnalysisSubscription = ARKitEventEmitter.addListener(
      'onDepthAnalysis', 
      (event) => {
        // Handle depth discontinuities (edges of objects)
        if (event.edgeConfidence) {
          // Update state with edge detection confidence
          setEdgeDetectionQuality(event.edgeConfidence > 0.7 ? 'high' : event.edgeConfidence > 0.4 ? 'medium' : 'low');
          
          // If we detect strong edges, provide feedback
          if (event.edgeConfidence > 0.7 && event.distinctObjectsCount > 1) {
            addScanningTip(`Detected ${event.distinctObjectsCount} different food items!`);
          }
        }
        
        // Use depth data to improve object separation
        if (event.depthMap && event.depthMap.length > 0) {
          setHasDepthData(true);
        }
      }
    );
    
    // Object tracking event handler - only enable if device can handle it
    let objectTrackingSubscription = { remove: () => {} };
    
    if (isObjectTrackingEnabled && devicePerformanceLevel !== 'low') {
      objectTrackingSubscription = ARKitEventEmitter.addListener(
        'onObjectsTracked',
        (event) => {
          if (event.objects && Array.isArray(event.objects)) {
            setObjectsBoundingBoxes(event.objects);
            setObjectsInView(event.objects.length);
            
            // Provide feedback when multiple objects are detected
            if (event.objects.length > 1 && objectsInView <= 1) {
              addScanningTip(`Now tracking ${event.objects.length} separate objects`);
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
            }
          }
        }
      );
    }
    
    // Clean up subscriptions
    return () => {
      planeDetectedSubscription.remove();
      scanProgressSubscription.remove();
      meshUpdatedSubscription.remove();
      distanceCheckSubscription.remove();
      depthAnalysisSubscription.remove();
      objectTrackingSubscription.remove();
      
      // Stop scanning if active
      if (isScanningActive) {
        ARKitVolumeScanner.stopScanning().catch(console.error);
      }
    };
  }, [
    isScanningActive, 
    deviceSupportsLiDAR, 
    optimalScanningDistance, 
    scanQuality, 
    scanStability, 
    isObjectTrackingEnabled, 
    objectsInView,
    addScanningTip,
    processScanAngle,
    devicePerformanceLevel,
    feedbackLoopSettings,
    averageLatencyMs,
    handleScanProgress
  ]);
  
  // Start LiDAR scanning with enhanced options
  const startLiDARScanning = useCallback(async () => {
    if (
      deviceSupportsLiDAR !== DeviceCapability.LIDAR_SUPPORTED ||
      Platform.OS !== 'ios'
    ) {
      return;
    }
    
    try {
      // Start performance monitoring
      const perfMonitor = monitorPerformance('lidar-scanning');
      perfMonitor.start();
      
      // Reset scan data
      resetScan();
      
      // Get device-specific scaling configuration
      const scalingConfig = await getARKitScalingConfiguration();
      
      // Get optimal scanning config based on device capabilities
      const optimalConfig = await getOptimalScanningConfig();
      
      // Get enhanced feedback loop settings for latency optimization
      const feedbackSettings = await getEnhancedFeedbackSettings();
      
      // Get acceleration structure config for faster point cloud processing
      const accelerationConfig = getAccelerationStructureConfig();
      
      // Enhanced config with all options - adaptive to device capabilities
      await ARKitVolumeScanner.startScanning({
        calibrationFactor,
        trackObjects: isObjectTrackingEnabled && optimalConfig.objectTrackingQuality !== 'low',
        highResolutionScanning: optimalConfig.highResolutionScanning,
        adaptiveQualityMode: optimalConfig.adaptiveQualityMode,
        enableSceneReconstruction: optimalConfig.enableSceneReconstruction,
        environmentTexturing: optimalConfig.environmentTexturing,
        useLowPowerMode: optimalConfig.useLowPowerMode,
        scanningFrequency: optimalConfig.scanningFrequency,
        meshQuality: optimalConfig.meshQuality,
        
        // Add enhanced latency optimization settings
        updateInterval: feedbackSettings.meshProcessingInterval,
        depthResolution: feedbackSettings.depthResolution,
        processingCoverage: feedbackSettings.processingCoverage,
        prioritizeResponseOverAccuracy: feedbackSettings.prioritizeResponseOverAccuracy,
        
        // Add advanced optimization options
        useDoubleBuffering: feedbackSettings.useDoubleBuffering,
        preWarmShaders: feedbackSettings.preWarmShaders,
        useBackgroundProcessing: feedbackSettings.useBackgroundProcessing,
        motionScalingFactor: feedbackSettings.motionScalingFactor,
        
        // Add acceleration structure configuration
        useBVH: accelerationConfig.useBVH,
        useOctree: accelerationConfig.useOctree,
        bvhMaxDepth: accelerationConfig.maxDepth,
        octreeMaxLeafSize: accelerationConfig.maxLeafSize,
        
        // Add device-specific scaling configuration
        ...scalingConfig
      });
      
      setIsScanningActive(true);
      setProcessingStage('scanning');
      
      // End performance monitoring
      perfMonitor.end();
    } catch (error) {
      console.error("Failed to start scanning:", error);
      Alert.alert(
        "Scanning Error",
        "Could not start LiDAR scanning. Please ensure you're in a well-lit environment."
      );
    }
  }, [deviceSupportsLiDAR, calibrationFactor, isObjectTrackingEnabled, resetScan]);
  
  // Calculate volume using LiDAR with enhanced options
  const calculateLiDARVolume = useCallback(async () => {
    if (
      deviceSupportsLiDAR !== DeviceCapability.LIDAR_SUPPORTED ||
      Platform.OS !== 'ios'
    ) {
      return null;
    }
    
    try {
      // Start performance monitoring
      const perfMonitor = monitorPerformance('volume-calculation');
      perfMonitor.start();
      
      // Get optimal scan quality
      const optimalConfig = await getOptimalScanningConfig();
      
      // Adapt options based on device capabilities and detected objects
      const options = {
        useMeshOptimization: true,
        calculateIndividualObjects: objectsInView > 1 && isObjectTrackingEnabled && optimalConfig.objectTrackingQuality !== 'low',
        confidenceThreshold: 0.65,
        includeBoundingBoxes: optimalConfig.objectTrackingQuality !== 'low',
        includePointCloud: optimalConfig.meshQuality === 'high',
        meshDecimationFactor: optimalConfig.meshQuality === 'high' ? 0.1 : 
                              optimalConfig.meshQuality === 'medium' ? 0.3 : 0.5
      };
      
      // Get raw volume data
      const rawVolumeData = await ARKitVolumeScanner.calculateVolume(options);
      
      // Apply device-specific corrections
      const correctedVolumeData = await applyDeviceCorrectionsToVolumeData(rawVolumeData);
      
      // End performance monitoring
      perfMonitor.end();
      
      return correctedVolumeData;
    } catch (error) {
      console.error("Failed to calculate volume:", error);
      Alert.alert(
        "Volume Calculation Error",
        "Could not calculate food volume. Make sure the food is clearly visible on a flat surface."
      );
      return null;
    }
  }, [deviceSupportsLiDAR, objectsInView, isObjectTrackingEnabled]);
  
  // Stop LiDAR scanning
  const stopLiDARScanning = useCallback(async () => {
    if (
      deviceSupportsLiDAR !== DeviceCapability.LIDAR_SUPPORTED ||
      Platform.OS !== 'ios' ||
      !isScanningActive
    ) {
      return;
    }
    
    try {
      await ARKitVolumeScanner.stopScanning();
      setIsScanningActive(false);
    } catch (error) {
      console.error("Failed to stop scanning:", error);
    }
  }, [deviceSupportsLiDAR, isScanningActive]);
  
  // Start LiDAR scanning for enhanced calibration
  const startCalibrationScanning = useCallback(async () => {
    if (
      deviceSupportsLiDAR !== DeviceCapability.LIDAR_SUPPORTED ||
      Platform.OS !== 'ios'
    ) {
      Alert.alert(
        "Calibration Error",
        "Calibration requires a device with LiDAR sensor."
      );
      return;
    }
    
    try {
      // Reset scan data
      resetScan();
      
      // Get optimal scanning config based on device capabilities
      const optimalConfig = await getOptimalScanningConfig();
      
      // Enhanced calibration with visualization - adapted to device capabilities
      await ARKitVolumeScanner.startScanning({
        showDebugVisualization: true,
        calibrationMode: true,
        highResolutionScanning: optimalConfig.meshQuality !== 'low',
        adaptiveQualityMode: true,
        meshingRate: optimalConfig.meshQuality,
        useLowPowerMode: optimalConfig.useLowPowerMode
      });
      
      setIsScanningActive(true);
      setProcessingStage('calibrating');
    } catch (error) {
      console.error("Failed to start calibration scanning:", error);
      Alert.alert(
        "Calibration Error",
        "Could not start calibration scan. Please ensure you're in a well-lit environment."
      );
    }
  }, [deviceSupportsLiDAR, resetScan]);
  
  // Perform enhanced calibration with known volume
  const performCalibration = useCallback(async (objectName: string, volumeStr: string) => {
    if (
      deviceSupportsLiDAR !== DeviceCapability.LIDAR_SUPPORTED ||
      Platform.OS !== 'ios' ||
      !isScanningActive
    ) {
      return;
    }
    
    try {
      const knownVolumeCm3 = parseFloat(volumeStr);
      if (isNaN(knownVolumeCm3) || knownVolumeCm3 <= 0) {
        Alert.alert("Error", "Please enter a valid volume in cm³");
        return;
      }
      
      // Start performance monitoring
      const perfMonitor = monitorPerformance('calibration');
      perfMonitor.start();
      
      // Enhanced calibration with scan quality factoring
      const result = await ARKitVolumeScanner.calibrateMeasurement(knownVolumeCm3, {
        considerScanQuality: true,
        useMultiSampleAveraging: true,
        scanCompleteness: scanCompleteness
      });
      
      const newCalibrationFactor = result.calibrationFactor;
      setCalibrationFactor(newCalibrationFactor);
      
      // Save enhanced calibration to AsyncStorage
      try {
        const currentDate = new Date().toISOString();
        const newCalibrations = {
          ...savedCalibrations,
          defaultCalibration: newCalibrationFactor,
          lastCalibrationDate: currentDate,
          [objectName]: newCalibrationFactor,
          [`${objectName}_date`]: currentDate,
          [`${objectName}_quality`]: scanCompleteness,
        };
        
        await AsyncStorage.setItem('lidar_calibrations', JSON.stringify(newCalibrations));
        setSavedCalibrations(newCalibrations);
        
        // Determine calibration quality based on scan completeness
        if (scanCompleteness > 0.8) {
          setCalibrationQuality('high');
        } else if (scanCompleteness > 0.5) {
          setCalibrationQuality('medium');
        } else {
          setCalibrationQuality('low');
        }
        
        // Save to cloud if user is logged in
        if (user?.id) {
          await saveCalibrationToCloud(user.id, newCalibrations);
        }
      } catch (storageError) {
        console.error('Failed to save calibration:', storageError);
      }
      
      await ARKitVolumeScanner.stopScanning();
      setIsScanningActive(false);
      setProcessingStage('idle');
      
      // End performance monitoring
      perfMonitor.end();
      
      Alert.alert(
        "Calibration Complete",
        `Successfully calibrated with ${objectName} (${knownVolumeCm3} cm³). Calibration factor: ${newCalibrationFactor.toFixed(2)}.`,
        [
          { 
            text: "OK",
            onPress: () => {
              // Provide calibration quality feedback
              if (scanCompleteness < 0.5) {
                setTimeout(() => {
                  Alert.alert(
                    "Calibration Quality",
                    "The calibration scan was incomplete. Consider recalibrating with a more complete scan for better results.",
                    [{ text: "OK" }]
                  );
                }, 500);
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error("Calibration failed:", error);
      Alert.alert("Calibration Failed", "Could not complete calibration. Please try again with a different object.");
      
      setIsScanningActive(false);
      setProcessingStage('idle');
      ARKitVolumeScanner.stopScanning().catch(console.error);
    }
  }, [deviceSupportsLiDAR, isScanningActive, scanCompleteness, savedCalibrations, user?.id]);
  
  // Cancel calibration
  const cancelCalibration = useCallback(async () => {
    if (isScanningActive) {
      try {
        await ARKitVolumeScanner.stopScanning();
      } catch (error) {
        console.error("Error stopping calibration scan:", error);
      }
    }
    
    setIsScanningActive(false);
    setProcessingStage('idle');
  }, [isScanningActive]);
  
  // Enhanced sync calibration data with cloud - throttled to save battery
  const throttledSyncCalibration = throttle(
    async () => {
      // Only sync if user is logged in
      if (!user?.id) {
        return;
      }
      
      try {
        // First try to get cloud data
        const cloudCalibrations = await syncCalibrationData(user.id);
        if (cloudCalibrations && Object.keys(cloudCalibrations).length > 0) {
          // Check which calibrations are newer
          const mergedCalibrations = { ...savedCalibrations };
          
          // For each cloud calibration, check if it's newer than local
          Object.keys(cloudCalibrations).forEach(key => {
            if (key.endsWith('_date')) {
              const baseKey = key.replace('_date', '');
              const cloudDate = new Date(cloudCalibrations[key]).getTime();
              const localDate = savedCalibrations[key] ? new Date(savedCalibrations[key]).getTime() : 0;
              
              // If cloud date is newer, use cloud values
              if (cloudDate > localDate) {
                mergedCalibrations[baseKey] = cloudCalibrations[baseKey];
                mergedCalibrations[key] = cloudCalibrations[key];
                
                // Copy any related fields (_quality, etc.)
                const qualityKey = `${baseKey}_quality`;
                if (cloudCalibrations[qualityKey]) {
                  mergedCalibrations[qualityKey] = cloudCalibrations[qualityKey];
                }
              }
            } else if (!key.includes('_')) {
              // For keys without dates, always take the cloud value if local doesn't exist
              if (!savedCalibrations[key]) {
                mergedCalibrations[key] = cloudCalibrations[key];
              }
            }
          });
          
          // Update local state
          setSavedCalibrations(mergedCalibrations);
          if (mergedCalibrations.defaultCalibration) {
            setCalibrationFactor(mergedCalibrations.defaultCalibration);
          }
          
          // Save to local storage
          await AsyncStorage.setItem('lidar_calibrations', JSON.stringify(mergedCalibrations));
          console.log("Successfully synced calibration data from cloud");
        } else if (Object.keys(savedCalibrations).length > 0) {
          // If we have local calibrations but none in the cloud, upload local ones
          await saveCalibrationToCloud(user.id, savedCalibrations);
          console.log("Uploaded local calibration data to cloud");
        }
        
        // Update last sync time
        setLastSyncTime(Date.now());
      } catch (error) {
        console.error("Error syncing calibration data:", error);
      }
    },
    // Throttle sync based on device performance to save battery
    devicePerformanceLevel === 'high' ? 60000 : 300000 // 1 minute or 5 minutes
  );
  
  // Wrapper to ensure the function always returns a Promise
  const syncCalibrationWithCloud = async (): Promise<void> => {
    await Promise.resolve(throttledSyncCalibration());
    return;
  };
  
  // Add a new effect to check for device-specific scaling issues
  useEffect(() => {
    if (
      deviceSupportsLiDAR === DeviceCapability.LIDAR_SUPPORTED &&
      Platform.OS === 'ios'
    ) {
      (async () => {
        try {
          // Check if this device has known scaling issues
          const deviceHasScalingIssues = await hasKnownScalingIssues();
          
          if (deviceHasScalingIssues) {
            console.log('Device has known LiDAR scaling issues. Applying corrections...');
            
            // Add a scanning tip for the user - but only if not in low power mode
            if (!isLowPowerMode) {
              addScanningTip(
                "Device-specific LiDAR calibration applied for improved accuracy"
              );
            }
          }
        } catch (error) {
          console.error('Error checking device scaling issues:', error);
        }
      })();
    }
  }, [deviceSupportsLiDAR, addScanningTip, isLowPowerMode]);
  
  return {
    deviceSupportsLiDAR,
    isScanningActive,
    processingStage,
    planeDetected,
    scanProgress,
    scanQuality,
    scanStability,
    meshCount,
    scanningTips,
    edgeDetectionQuality,
    hasDepthData,
    optimalScanningDistance,
    guidePosition,
    calibrationFactor,
    savedCalibrations,
    objectsBoundingBoxes,
    objectsInView,
    scanCompleteness,
    averageLatencyMs,
    
    startLiDARScanning,
    stopLiDARScanning,
    calculateLiDARVolume,
    startCalibrationScanning,
    performCalibration,
    cancelCalibration,
    setProcessingStage,
    syncCalibrationWithCloud,
    resetScan,
    toggleObjectTracking
  };
} 