# Archived Hooks

This directory contains hooks that have been deprecated or replaced as part of architecture improvements or library migrations.

## useBarcodeScannerHook.tsx

- **Archived Date**: 2025-01-18
- **Reason**: Migration from deprecated expo-barcode-scanner to expo-camera
- **Replacement**: hooks/useBarcodeScanner.ts

### Original Functionality
- Provided a simple interface for showing/hiding barcode scanner
- Handled product found callback translation to barcode+type format
- Returned scanner component that could be rendered in UI

### Key Features
- Scanner visibility state management
- Automatic closing on successful scan
- Barcode extraction from product ID (handled 'barcode-' prefix)

### Usage Pattern
```typescript
const { scannerComponent, showScanner, hideScanner, isScannerVisible } = useBarcodeScannerHook(
  (barcode, type) => console.log('Scanned:', barcode, type)
);
```