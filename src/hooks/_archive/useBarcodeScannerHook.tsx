import React, { useState } from 'react';
import { FoodItem } from '@/types/food';
import { BarcodeScanner } from '@/components/BarcodeScanner';

/**
 * Custom hook for using the barcode scanner functionality
 * @param onBarcodeScan Callback function to handle scanned barcodes
 * @returns Controls and state for the barcode scanner
 * 
 * @deprecated This hook is deprecated and has been archived. Use the new useBarcodeScanner hook instead.
 * This implementation was part of the old expo-barcode-scanner based architecture.
 * Archived on: 2025-01-18
 */
export function useBarcodeScannerHook(
  onBarcodeScan: (barcode: string, type: string) => void
) {
  const [isScannerVisible, setScannerVisible] = useState(false);
  
  const showScanner = () => setScannerVisible(true);
  const hideScanner = () => setScannerVisible(false);
  
  // Create a handler that adapts from the product format to the barcode+type format
  const handleProductFound = (product: FoodItem) => {
    // Extract the barcode from the product ID if it has a barcode- prefix
    const barcode = product.id.startsWith('barcode-') 
      ? product.id.substring(8) 
      : product.id;
    
    onBarcodeScan(barcode, 'unknown');
    hideScanner();
  };
  
  const scannerComponent = isScannerVisible ? (
    <BarcodeScanner 
      onProductFound={handleProductFound} 
      onClose={hideScanner}
    />
  ) : null;
  
  return {
    scannerComponent,
    showScanner,
    hideScanner,
    isScannerVisible
  };
}