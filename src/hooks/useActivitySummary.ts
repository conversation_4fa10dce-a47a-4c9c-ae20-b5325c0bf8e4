import { useState, useEffect } from 'react';
import { ActivitySummaryService, ActivityHistory } from '@/services/activitySummaryService';

export interface UseActivitySummaryResult {
  activityHistory: ActivityHistory;
  isLoading: boolean;
  error: Error | null;
  timeRange: 'day' | 'week' | 'month';
  setTimeRange: (range: 'day' | 'week' | 'month') => void;
  maxValues: {
    maxCalories: number;
    maxWater: number;
    maxMeals: number;
    maxSteps: number;
  };
  formatDisplayDate: (dateString: string) => string;
  refetchData: () => Promise<void>;
}

export function useActivitySummary(): UseActivitySummaryResult {
  const [timeRange, setTimeRange] = useState<'day' | 'week' | 'month'>('day');
  const [activityHistory, setActivityHistory] = useState<ActivityHistory>({
    calories: [],
    water: [],
    meals: [],
    steps: [],
    dates: []
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  const activityService = new ActivitySummaryService();

  const fetchActivityHistory = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await activityService.loadActivityHistory(timeRange);
      
      if (result.success && result.history) {
        setActivityHistory(result.history);
      } else {
        setError(result.error || new Error('Failed to load activity history'));
      }
    } catch (err) {
      console.error('Error fetching activity history:', err);
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch activity history when timeRange changes
  useEffect(() => {
    fetchActivityHistory();
  }, [timeRange]);

  // Calculate max values for chart scaling
  const maxValues = activityService.calculateMaxValues(activityHistory);

  return {
    activityHistory,
    isLoading,
    error,
    timeRange,
    setTimeRange,
    maxValues,
    formatDisplayDate: activityService.formatDisplayDate.bind(activityService),
    refetchData: fetchActivityHistory
  };
}