import { useState, useEffect, useCallback } from 'react';
import { barcodeScannerService, BarcodeData } from '@/services/barcodeScannerService';
import { BarcodeScanningResult } from 'expo-camera';

export interface UseBarcodeScanner {
  // Permission state
  hasPermission: boolean | null;
  
  // Scanning state
  scanning: boolean;
  scannedData: BarcodeData | null;
  
  // Scanner visibility (for UI display)
  isVisible: boolean;
  
  // Actions
  handleBarcodeScanned: (barcodes: BarcodeScanningResult[]) => void;
  startScanning: () => void;
  stopScanning: () => void;
  resetScanner: () => void;
  showScanner: () => void;
  hideScanner: () => void;
  
  // Utilities
  requestPermission: () => Promise<void>;
  setManualBarcode: (barcode: string, type?: string) => void;
}

/**
 * Custom hook for barcode scanning functionality
 * Implements business logic layer following three-layer architecture
 * 
 * @param onBarcodeScanned - Optional callback when barcode is successfully scanned
 * @returns UseBarcodeScanner interface with states and actions
 */
export function useBarcodeScanner(
  onBarcodeScanned?: (barcodeData: BarcodeData) => void
): UseBarcodeScanner {
  // Permission state
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  
  // Scanning state
  const [scanning, setScanning] = useState(false);
  const [scannedData, setScannedData] = useState<BarcodeData | null>(null);
  
  // UI visibility state
  const [isVisible, setIsVisible] = useState(false);

  // Check permission on mount
  useEffect(() => {
    checkPermission();
  }, []);

  /**
   * Check current camera permission status
   */
  const checkPermission = async () => {
    const status = await barcodeScannerService.getPermissionStatus();
    setHasPermission(status);
  };

  /**
   * Request camera permission
   */
  const requestPermission = async () => {
    const granted = await barcodeScannerService.requestPermission();
    setHasPermission(granted);
  };

  /**
   * Handle barcode scan results from camera
   * This is the main business logic for processing scanned barcodes
   */
  const handleBarcodeScanned = useCallback(async (barcodes: BarcodeScanningResult[]) => {
    if (!scanning || barcodes.length === 0) {
      return;
    }

    // Extract first valid barcode
    const barcodeData = barcodeScannerService.extractFirstBarcode(barcodes);
    
    if (!barcodeData) {
      return;
    }

    // Validate barcode format
    if (!barcodeScannerService.isValidBarcode(barcodeData.data, barcodeData.type)) {
      console.warn('Invalid barcode format:', barcodeData);
      return;
    }

    // Update state
    setScannedData(barcodeData);
    setScanning(false);

    // Call callback if provided
    if (onBarcodeScanned) {
      onBarcodeScanned(barcodeData);
    }

    // Additional business logic could go here:
    // - Analytics tracking
    // - Caching
    // - Duplicate detection
    // - etc.
  }, [scanning, onBarcodeScanned]);

  /**
   * Start scanning process
   */
  const startScanning = useCallback(() => {
    setScanning(true);
    setScannedData(null);
  }, []);

  /**
   * Stop scanning process
   */
  const stopScanning = useCallback(() => {
    setScanning(false);
  }, []);

  /**
   * Reset scanner to initial state
   */
  const resetScanner = useCallback(() => {
    setScanning(false);
    setScannedData(null);
  }, []);

  /**
   * Show scanner UI
   */
  const showScanner = useCallback(() => {
    setIsVisible(true);
    startScanning();
  }, [startScanning]);

  /**
   * Hide scanner UI
   */
  const hideScanner = useCallback(() => {
    setIsVisible(false);
    stopScanning();
    resetScanner();
  }, [stopScanning, resetScanner]);

  /**
   * Manually set barcode data (for manual input)
   */
  const setManualBarcode = useCallback((barcode: string, type: string = 'manual') => {
    const barcodeData = barcodeScannerService.formatBarcode(barcode, type);
    
    if (barcodeScannerService.isValidBarcode(barcode, type)) {
      setScannedData(barcodeData);
      
      if (onBarcodeScanned) {
        onBarcodeScanned(barcodeData);
      }
    }
  }, [onBarcodeScanned]);

  return {
    // States
    hasPermission,
    scanning,
    scannedData,
    isVisible,
    
    // Actions
    handleBarcodeScanned,
    startScanning,
    stopScanning,
    resetScanner,
    showScanner,
    hideScanner,
    requestPermission,
    setManualBarcode,
  };
}