import { useState, useEffect, useCallback, useMemo } from 'react';
import { databaseExplorerService, TableData, QueryOptions } from '@/services/databaseExplorerService';

export interface UseDatabaseExplorerResult {
  // Data
  data: any[];
  columns: string[];
  loading: boolean;
  error: string | null;
  
  // Filtering & Sorting
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  filteredData: any[];
  sortColumn: string | null;
  sortDirection: 'asc' | 'desc';
  handleSort: (column: string) => void;
  
  // Actions
  refreshData: () => Promise<void>;
  exportAsJson: () => Promise<string>;
  exportAsCsv: () => Promise<string>;
  
  // Pagination
  pageSize: number;
  currentPage: number;
  totalPages: number;
  goToPage: (page: number) => void;
}

export function useDatabaseExplorer(
  selectedTable: string | null,
  pageSize: number = 50
): UseDatabaseExplorerResult {
  const [data, setData] = useState<any[]>([]);
  const [columns, setColumns] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Filtering & Sorting
  const [searchQuery, setSearchQuery] = useState('');
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  // Load data when table changes
  useEffect(() => {
    if (selectedTable) {
      loadTableData();
    } else {
      setData([]);
      setColumns([]);
    }
  }, [selectedTable]);

  const loadTableData = async () => {
    if (!selectedTable) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const options: QueryOptions = {
        limitCount: pageSize * 3, // Load more for client-side operations
        orderByField: sortColumn || undefined,
        orderDirection: sortDirection
      };
      
      const result = await databaseExplorerService.loadCollectionData(
        selectedTable,
        options
      );
      
      setData(result.rows);
      setColumns(result.columns);
      setTotalCount(result.totalCount || result.rows.length);
    } catch (err: any) {
      console.error('Error loading data:', err);
      setError(err.message || 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  // Filter data based on search query
  const filteredData = useMemo(() => {
    if (!searchQuery) return data;
    
    const query = searchQuery.toLowerCase();
    return data.filter(row => {
      return Object.values(row).some(value => {
        if (value === null || value === undefined) return false;
        return String(value).toLowerCase().includes(query);
      });
    });
  }, [data, searchQuery]);

  // Sort filtered data
  const sortedData = useMemo(() => {
    if (!sortColumn) return filteredData;
    
    return [...filteredData].sort((a, b) => {
      const aVal = a[sortColumn];
      const bVal = b[sortColumn];
      
      if (aVal === null || aVal === undefined) return 1;
      if (bVal === null || bVal === undefined) return -1;
      
      if (typeof aVal === 'number' && typeof bVal === 'number') {
        return sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
      }
      
      const aStr = String(aVal).toLowerCase();
      const bStr = String(bVal).toLowerCase();
      
      if (sortDirection === 'asc') {
        return aStr.localeCompare(bStr);
      } else {
        return bStr.localeCompare(aStr);
      }
    });
  }, [filteredData, sortColumn, sortDirection]);

  // Paginate sorted data
  const paginatedData = useMemo(() => {
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    return sortedData.slice(start, end);
  }, [sortedData, currentPage, pageSize]);

  const totalPages = Math.ceil(sortedData.length / pageSize);

  const handleSort = useCallback((column: string) => {
    if (sortColumn === column) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
    setCurrentPage(1); // Reset to first page
  }, [sortColumn]);

  const goToPage = useCallback((page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  }, [totalPages]);

  const refreshData = useCallback(async () => {
    await loadTableData();
  }, [selectedTable, sortColumn, sortDirection]);

  const exportAsJson = useCallback(async () => {
    if (!selectedTable) throw new Error('No table selected');
    return databaseExplorerService.exportCollectionAsJson(selectedTable);
  }, [selectedTable]);

  const exportAsCsv = useCallback(async () => {
    if (!selectedTable) throw new Error('No table selected');
    return databaseExplorerService.exportCollectionAsCsv(selectedTable);
  }, [selectedTable]);

  return {
    // Data
    data: paginatedData,
    columns,
    loading,
    error,
    
    // Filtering & Sorting
    searchQuery,
    setSearchQuery,
    filteredData: paginatedData,
    sortColumn,
    sortDirection,
    handleSort,
    
    // Actions
    refreshData,
    exportAsJson,
    exportAsCsv,
    
    // Pagination
    pageSize,
    currentPage,
    totalPages,
    goToPage
  };
}