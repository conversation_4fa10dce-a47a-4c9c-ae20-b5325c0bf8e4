import { useEffect } from 'react';
import { useFeatureFlag, FEATURE_FLAGS } from '@/featureFlags';
import { Analytics } from '../services/analyticsService';
import { trackFeatureError } from '../services/errorTrackingService';

// Custom hook to handle feature flag checks and tracking
export function useFeatureTracking(
  featureFlag: keyof typeof FEATURE_FLAGS,
  screenName: string
) {
  const isEnabled = useFeatureFlag(FEATURE_FLAGS[featureFlag]);
  
  useEffect(() => {
    // Track screen view with feature context
    Analytics.trackScreenView(screenName, {
      feature: featureFlag,
      feature_enabled: isEnabled,
    });
  }, [screenName, featureFlag, isEnabled]);
  
  // Helper to track errors for this feature
  const trackError = (action: string, error: Error, details?: any) => {
    trackFeatureError(featureFlag, action, error, details);
  };
  
  // Helper to track analytics for this feature
  const trackAction = (action: string, properties?: Record<string, any>) => {
    Analytics.trackFeatureUsage(FEATURE_FLAGS[featureFlag], action, properties);
  };
  
  return {
    isEnabled,
    trackError,
    trackAction,
  };
}

// Usage example:
/*
function WaterTrackerScreen() {
  const { isEnabled, trackError, trackAction } = useFeatureTracking('WATER_TRACKING', 'water_tracker');
  
  const handleAddWater = async (amount: number) => {
    try {
      trackAction('add_water_started', { amount });
      await addWaterIntake(amount);
      trackAction('add_water_completed', { amount });
    } catch (error) {
      trackError('add_water', error, { amount });
    }
  };
  
  if (!isEnabled) {
    return <FeatureDisabledView feature="Water Tracking" />;
  }
  
  return <WaterTrackerComponent onAddWater={handleAddWater} />;
}
*/