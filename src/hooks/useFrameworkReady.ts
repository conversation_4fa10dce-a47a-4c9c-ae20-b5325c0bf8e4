import { useEffect } from 'react';
import { Platform } from 'react-native';

// Handle web-specific types
type WebWindow = typeof globalThis & {
  frameworkReady?: () => void;
};

export function useFrameworkReady() {
  useEffect(() => {
    if (Platform.OS === 'web') {
      // Cast global object to our custom web window type
      const webWindow = globalThis as WebWindow;
      webWindow.frameworkReady?.();
    }
  });
}
