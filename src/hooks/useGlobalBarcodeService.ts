import { useEffect, useState } from 'react';
import {
  initGlobalBarcodeService,
  getGlobalBarcodeConfig,
  setGlobalBarcodeConfig,
  getBarcodeStatistics,
  recognizeBarcodeGlobal,
  ProductRegion,
  BarcodeDatabase,
  BarcodeRecognitionResult
} from '@/services/globalBarcodeService';

interface UseGlobalBarcodeServiceProps {
  autoInit?: boolean;
  initialRegion?: ProductRegion;
  onInitialized?: () => void;
}

/**
 * Hook to use the global barcode service
 */
export function useGlobalBarcodeService({
  autoInit = true,
  initialRegion,
  onInitialized
}: UseGlobalBarcodeServiceProps = {}) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [currentRegion, setCurrentRegion] = useState<ProductRegion | null>(null);
  const [statistics, setStatistics] = useState<{
    cachedBarcodes: number;
    databaseCounts: Record<BarcodeDatabase, number>;
    totalLookups: number;
    successRate: number;
  } | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Initialize the service
  useEffect(() => {
    if (autoInit) {
      initService();
    }
  }, [autoInit]);

  /**
   * Initialize the global barcode service
   */
  const initService = async (customConfig?: any) => {
    try {
      setIsInitializing(true);
      setError(null);

      const config = customConfig || {};
      if (initialRegion) {
        config.defaultRegion = initialRegion;
      }

      await initGlobalBarcodeService(config);
      setIsInitialized(true);
      
      // Get the current configuration
      const currentConfig = getGlobalBarcodeConfig();
      setCurrentRegion(currentConfig.defaultRegion);
      
      // Get statistics
      await refreshStatistics();
      
      if (onInitialized) {
        onInitialized();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to initialize barcode service');
      console.error('Error initializing barcode service:', err);
    } finally {
      setIsInitializing(false);
    }
  };

  /**
   * Refresh the barcode statistics
   */
  const refreshStatistics = async () => {
    try {
      const stats = await getBarcodeStatistics();
      setStatistics(stats);
      return stats;
    } catch (err) {
      console.error('Error refreshing barcode statistics:', err);
      return null;
    }
  };

  /**
   * Scan a barcode and get product information
   */
  const scanBarcode = async (
    barcode: string,
    region: ProductRegion = currentRegion || ProductRegion.GLOBAL
  ): Promise<BarcodeRecognitionResult> => {
    try {
      if (!isInitialized) {
        throw new Error('Barcode service not initialized');
      }

      const result = await recognizeBarcodeGlobal(barcode, region);
      
      // Refresh statistics after scan
      refreshStatistics();
      
      return result;
    } catch (err) {
      console.error('Error scanning barcode:', err);
      return {
        product: null,
        success: false,
        error: err instanceof Error ? err.message : 'Unknown error scanning barcode'
      };
    }
  };

  /**
   * Change the current region
   */
  const changeRegion = async (region: ProductRegion): Promise<void> => {
    try {
      if (!isInitialized) {
        throw new Error('Barcode service not initialized');
      }

      await setGlobalBarcodeConfig({ defaultRegion: region });
      setCurrentRegion(region);
    } catch (err) {
      console.error('Error changing region:', err);
      setError(err instanceof Error ? err.message : 'Failed to change region');
    }
  };

  return {
    isInitialized,
    isInitializing,
    currentRegion,
    statistics,
    error,
    initService,
    scanBarcode,
    changeRegion,
    refreshStatistics
  };
} 