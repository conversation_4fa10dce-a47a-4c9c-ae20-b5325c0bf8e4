import { useState, useEffect } from 'react';
import { Alert } from 'react-native';
import { PrivacySettingsService, PrivacySettings } from '@/services/privacySettingsService';
import { useAuth } from '@/contexts/AuthContext';
import * as LocalAuthentication from 'expo-local-authentication';

export interface UsePrivacySettingsResult {
  settings: PrivacySettings;
  loading: boolean;
  error: Error | null;
  hasBiometrics: boolean;
  loadSettings: () => Promise<void>;
  handleToggleSetting: (key: keyof PrivacySettings, value: boolean) => Promise<void>;
  handleAppLockTimeoutChange: (timeout: number) => Promise<void>;
  handleResetAllSettings: () => Promise<void>;
  handleRequestDataExport: () => Promise<void>;
  handleDeleteAllData: () => Promise<void>;
  authenticateWithBiometrics: () => Promise<boolean>;
}

export function usePrivacySettings(): UsePrivacySettingsResult {
  const { user } = useAuth();
  const [settings, setSettings] = useState<PrivacySettings>({
    shareActivityData: false,
    shareHealthData: false,
    shareMealData: false,
    publicProfile: false,
    allowFriendRequests: true,
    dataCollection: true,
    biometricAuth: false,
    appLock: false,
    appLockTimeout: 5,
    emergencyContacts: [],
    user_id: user?.id || '',
    updated_at: new Date().toISOString()
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [hasBiometrics, setHasBiometrics] = useState(false);
  
  const privacyService = new PrivacySettingsService();

  const checkBiometricSupport = async () => {
    try {
      const compatible = await LocalAuthentication.hasHardwareAsync();
      const enrolled = await LocalAuthentication.isEnrolledAsync();
      setHasBiometrics(compatible && enrolled);
    } catch (error) {
      console.error('Error checking biometric support:', error);
      setHasBiometrics(false);
    }
  };

  const authenticateWithBiometrics = async (): Promise<boolean> => {
    try {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to enable app lock',
        fallbackLabel: 'Use passcode',
      });
      return result.success;
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return false;
    }
  };

  const loadSettings = async () => {
    if (!user) {
      setLoading(false);
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await privacyService.loadSettings(user.id);
      
      if (result.success && result.settings) {
        setSettings(result.settings);
      } else {
        setError(result.error || new Error('Failed to load privacy settings'));
      }
    } catch (err) {
      console.error('Error loading privacy settings:', err);
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const handleToggleSetting = async (key: keyof PrivacySettings, value: boolean) => {
    if (!user) return;
    
    // Special case for biometric auth
    if (key === 'biometricAuth' && value && !hasBiometrics) {
      Alert.alert(
        'Biometric Authentication Unavailable',
        'Your device does not support biometric authentication or you haven\'t set it up in your device settings.',
        [{ text: 'OK' }]
      );
      return;
    }
    
    // Special case for app lock
    if (key === 'appLock' && value) {
      const biometricResult = await authenticateWithBiometrics();
      if (!biometricResult) {
        return;
      }
    }
    
    // Update local state optimistically
    setSettings(prev => ({
      ...prev,
      [key]: value,
      updated_at: new Date().toISOString()
    }));
    
    try {
      const result = await privacyService.updateSetting(user.id, key, value);
      
      if (!result.success) {
        // Revert optimistic update
        setSettings(prev => ({
          ...prev,
          [key]: !value
        }));
        throw result.error || new Error('Failed to update setting');
      }
    } catch (error) {
      console.error('Error updating privacy setting:', error);
      Alert.alert('Error', 'Failed to update privacy setting. Please try again.');
    }
  };

  const handleAppLockTimeoutChange = async (timeout: number) => {
    if (!user) return;
    
    // Update local state optimistically
    setSettings(prev => ({
      ...prev,
      appLockTimeout: timeout,
      updated_at: new Date().toISOString()
    }));
    
    try {
      const result = await privacyService.updateAppLockTimeout(user.id, timeout);
      
      if (!result.success) {
        throw result.error || new Error('Failed to update app lock timeout');
      }
    } catch (error) {
      console.error('Error updating app lock timeout:', error);
      Alert.alert('Error', 'Failed to update app lock timeout. Please try again.');
    }
  };

  const handleResetAllSettings = async () => {
    if (!user) return;
    
    Alert.alert(
      'Reset Privacy Settings',
      'Are you sure you want to reset all privacy settings to their default values? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await privacyService.resetAllSettings(user.id);
              
              if (result.success) {
                // Reload settings to get the defaults
                await loadSettings();
                Alert.alert('Success', 'Privacy settings have been reset to defaults.');
              } else {
                throw result.error || new Error('Failed to reset settings');
              }
            } catch (error) {
              console.error('Error resetting privacy settings:', error);
              Alert.alert('Error', 'Failed to reset privacy settings. Please try again.');
            }
          }
        }
      ]
    );
  };

  const handleRequestDataExport = async () => {
    if (!user) return;
    
    Alert.alert(
      'Export Data',
      'We will prepare your data for export and send you a download link via email. This may take a few minutes.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Export',
          onPress: async () => {
            try {
              const result = await privacyService.requestDataExport(user.id);
              
              if (result.success) {
                Alert.alert(
                  'Export Requested',
                  'Your data export has been requested. You will receive an email with download instructions within 24 hours.'
                );
              } else {
                throw result.error || new Error('Failed to request data export');
              }
            } catch (error) {
              console.error('Error requesting data export:', error);
              Alert.alert('Error', 'Failed to request data export. Please try again.');
            }
          }
        }
      ]
    );
  };

  const handleDeleteAllData = async () => {
    if (!user) return;
    
    Alert.alert(
      'Delete All Data',
      'Are you absolutely sure you want to delete ALL your data? This action is permanent and cannot be undone. You will be logged out and your account will be deactivated.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete Forever',
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Final Confirmation',
              'This is your final warning. All your data will be permanently deleted. Type YES to confirm.',
              [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'YES, DELETE EVERYTHING',
                  style: 'destructive',
                  onPress: async () => {
                    try {
                      const result = await privacyService.deleteAllData(user.id);
                      
                      if (result.success) {
                        Alert.alert(
                          'Data Deleted',
                          'All your data has been permanently deleted. You will now be logged out.',
                          [
                            {
                              text: 'OK',
                              onPress: () => {
                                // The auth context should handle logout
                                // router.replace('/(auth)/login');
                              }
                            }
                          ]
                        );
                      } else {
                        throw result.error || new Error('Failed to delete data');
                      }
                    } catch (error) {
                      console.error('Error deleting all data:', error);
                      Alert.alert('Error', 'Failed to delete data. Please contact support.');
                    }
                  }
                }
              ]
            );
          }
        }
      ]
    );
  };

  // Load settings on mount and when user changes
  useEffect(() => {
    loadSettings();
    checkBiometricSupport();
  }, [user]);

  return {
    settings,
    loading,
    error,
    hasBiometrics,
    loadSettings,
    handleToggleSetting,
    handleAppLockTimeoutChange,
    handleResetAllSettings,
    handleRequestDataExport,
    handleDeleteAllData,
    authenticateWithBiometrics
  };
}