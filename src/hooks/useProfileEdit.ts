import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { profileEditService, ProfileData } from '@/services/profileEditService';

export interface UseProfileEditResult {
  profile: ProfileData | null;
  loading: boolean;
  saving: boolean;
  error: string | null;
  formData: ProfileData;
  updateFormData: (field: string, value: any) => void;
  updateNestedFormData: (section: string, field: string, value: any) => void;
  saveProfile: () => Promise<boolean>;
  refreshProfile: () => Promise<void>;
}

export function useProfileEdit(): UseProfileEditResult {
  const { user } = useAuth();
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<ProfileData>({
    full_name: '',
    username: '',
    bio: '',
    location: '',
    birthday: '',
    preferences: {
      notifications: true,
      darkMode: false,
      language: 'en'
    },
    stats: {
      height: 0,
      weight: 0,
      activityLevel: 'moderate'
    },
    health: {
      dietaryRestrictions: [],
      allergies: [],
      conditions: []
    },
    social: {
      instagram: '',
      twitter: '',
      website: ''
    }
  });

  // Load profile on mount
  useEffect(() => {
    if (user?.id) {
      loadProfile();
    }
  }, [user?.id]);

  const loadProfile = async () => {
    if (!user?.id) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const profileData = await profileEditService.getProfile(user.id);
      
      if (profileData) {
        setProfile(profileData);
        // Merge with defaults
        setFormData(prev => ({
          ...prev,
          ...profileData,
          preferences: { ...prev.preferences, ...profileData.preferences },
          stats: { ...prev.stats, ...profileData.stats },
          health: { ...prev.health, ...profileData.health },
          social: { ...prev.social, ...profileData.social }
        }));
      }
    } catch (err: any) {
      console.error('Error loading profile:', err);
      setError(err.message || 'Failed to load profile');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = useCallback((field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  const updateNestedFormData = useCallback((section: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof ProfileData] as any,
        [field]: value
      }
    }));
  }, []);

  const saveProfile = useCallback(async (): Promise<boolean> => {
    if (!user?.id) {
      Alert.alert('Error', 'You must be logged in to save changes');
      return false;
    }

    setSaving(true);
    setError(null);

    try {
      // Validate username if changed
      if (formData.username && formData.username !== profile?.username) {
        const isAvailable = await profileEditService.checkUsernameAvailable(
          formData.username,
          user.id
        );
        
        if (!isAvailable) {
          Alert.alert('Error', 'Username is already taken');
          setSaving(false);
          return false;
        }
      }

      // Save profile
      const result = await profileEditService.updateProfile(user.id, formData);
      
      if (result.success) {
        setProfile(formData);
        Alert.alert('Success', 'Profile updated successfully');
        return true;
      } else {
        throw new Error(result.error);
      }
    } catch (err: any) {
      console.error('Error saving profile:', err);
      Alert.alert('Error', err.message || 'Failed to save profile');
      return false;
    } finally {
      setSaving(false);
    }
  }, [user?.id, formData, profile?.username]);

  const refreshProfile = useCallback(async () => {
    await loadProfile();
  }, [user?.id]);

  return {
    profile,
    loading,
    saving,
    error,
    formData,
    updateFormData,
    updateNestedFormData,
    saveProfile,
    refreshProfile
  };
}