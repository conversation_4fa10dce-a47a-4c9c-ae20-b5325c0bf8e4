import { useState, useCallback } from 'react';
import { Alert } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { useAuth } from '@/contexts/AuthContext';
import { profileImageService, UploadProgress } from '@/services/profileImageService';

export interface UseProfileImageResult {
  uploading: boolean;
  uploadProgress: number;
  error: string | null;
  success: boolean;
  selectAndUploadImage: () => Promise<void>;
}

export function useProfileImage(onAvatarChange?: (url: string) => void): UseProfileImageResult {
  const { user } = useAuth();
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  const handleProgress = useCallback((progress: UploadProgress) => {
    setUploadProgress(progress.percentage);
  }, []);
  
  const selectAndUploadImage = useCallback(async () => {
    // Reset states
    setError(null);
    setSuccess(false);
    setUploadProgress(0);
    
    try {
      // Check if user is logged in
      if (!user?.id) {
        setError('You must be logged in to upload a profile picture');
        return;
      }
      
      // Request permissions
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          "Permission Required",
          "Please allow access to your photo library to change your profile picture.",
          [{ text: "OK" }]
        );
        return;
      }
      
      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.5,
        exif: false,
      });
      
      if (result.canceled || !result.assets || result.assets.length === 0) {
        return;
      }
      
      // Start upload
      setUploading(true);
      const uploadResult = await profileImageService.uploadProfileImage(
        result.assets[0].uri,
        user.id,
        handleProgress
      );
      
      if (uploadResult.success && uploadResult.url) {
        setSuccess(true);
        onAvatarChange?.(uploadResult.url);
        
        // Reset success after 3 seconds
        setTimeout(() => setSuccess(false), 3000);
      } else {
        setError(uploadResult.error || 'Upload failed');
      }
    } catch (err: any) {
      console.error('Image selection error:', err);
      setError(err.message || 'Failed to select image');
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  }, [user, onAvatarChange, handleProgress]);
  
  return {
    uploading,
    uploadProgress,
    error,
    success,
    selectAndUploadImage
  };
}