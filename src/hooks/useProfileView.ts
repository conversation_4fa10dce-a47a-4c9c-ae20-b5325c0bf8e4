import { useState, useEffect, useCallback } from 'react';
import { useFocusEffect } from 'expo-router';
import { ProfileViewService, ProfileData } from '@/services/profileViewService';
import { useDatabaseType } from '@/contexts/DatabaseContext';

export interface UseProfileViewResult {
  profile: ProfileData | null;
  loading: boolean;
  error: Error | null;
  completionPercentage: number;
  daysSinceCreation: number | null;
  loadProfileData: () => Promise<void>;
}

export function useProfileView(): UseProfileViewResult {
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [completionPercentage, setCompletionPercentage] = useState(0);
  const [daysSinceCreation, setDaysSinceCreation] = useState<number | null>(null);
  const { useFirebase } = useDatabaseType();
  
  const profileService = new ProfileViewService();

  const loadProfileData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await profileService.loadProfile();
      
      if (result.success && result.profile) {
        setProfile(result.profile);
        
        // Calculate profile completion percentage
        const completion = profileService.calculateProfileCompletion(result.profile);
        setCompletionPercentage(completion === 0 ? 20 : completion); // Minimum 20% if they have an account
        
        // Calculate days since creation
        const days = profileService.getDaysSinceCreation(result.profile);
        setDaysSinceCreation(days);
      } else {
        setError(result.error || new Error('Failed to load profile'));
      }
    } catch (err) {
      console.error('Error loading profile:', err);
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadProfileData();
  }, [useFirebase]);
  
  // Reload when screen is focused
  useFocusEffect(
    useCallback(() => {
      console.log('Profile screen focused - reloading profile data');
      // Force profile reload whenever the user navigates back to this screen
      setLoading(true);
      loadProfileData();
      return () => {}; // cleanup if needed
    }, [useFirebase])
  );

  return {
    profile,
    loading,
    error,
    completionPercentage,
    daysSinceCreation,
    loadProfileData
  };
}