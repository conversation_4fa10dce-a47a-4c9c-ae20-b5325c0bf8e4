import { useNavigation, useRouter } from 'expo-router';
import { ParamListBase } from '@react-navigation/native';

/**
 * Custom hook for safe navigation that prevents "GO_BACK" action not handled errors.
 * 
 * This hook provides a safe way to navigate back by checking first if there's a 
 * previous screen in the navigation stack. If not, it falls back to safer alternatives.
 */
export function useSafeNavigation() {
  const navigation = useNavigation();
  const router = useRouter();

  /**
   * Safe way to navigate back that will never cause the "GO_BACK action not handled" error
   */
  const safeGoBack = () => {
    try {
      console.log('[Navigation] Attempting to go back safely');
      
      // First check if navigation can go back (preferred method)
      if (navigation.canGoBack?.()) {
        console.log('[Navigation] Navigation stack has previous screens, using navigation.goBack()');
        navigation.goBack();
        return;
      } else {
        console.log('[Navigation] No previous screens in stack, navigation.canGoBack() returned false');
      }

      // Otherwise try router.back() which is safer in expo-router
      try {
        console.log('[Navigation] Attempting router.back() as fallback');
        router.back();
        console.log('[Navigation] router.back() completed successfully');
      } catch (error) {
        console.warn('[Navigation] router.back() failed, replacing with home route', error);
        // If all fails, go to a safe default route
        router.replace('/(tabs)');
      }
    } catch (error) {
      console.error('[Navigation] Critical navigation error, forcing redirect to home screen', error);
      // Final fallback - just go to home screen
      router.replace('/(tabs)');
    }
  };

  return {
    ...navigation,
    safeGoBack,
    // Add more safe navigation methods here if needed
    safeNavigate: (route: any) => {
      try {
        console.log(`[Navigation] Safely navigating to: ${JSON.stringify(route)}`);
        router.push(route);
      } catch (error) {
        console.error('[Navigation] Navigation error during safeNavigate:', error);
      }
    }
  };
} 