import { useState, useCallback } from 'react';
import * as ImagePicker from 'expo-image-picker';
import { Alert } from 'react-native';
import { analyzeFoodImage } from '@/services/vision';
import { AnalyzeImageResponse } from '@/services/vision/types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { generateAlternativeRecipe } from '@/services/openai/recipeGeneration';
import { Recipe } from '@/services/openai/recipeTypes';

interface RecentScan {
  id: string;
  imageUri: string;
  foodName: string;
  timestamp: number;
  calories: number;
}

export function useScanFunctions() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [imageUri, setImageUri] = useState<string>('');
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [photoPermissionDenied, setPhotoPermissionDenied] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  
  // Load recent scans from AsyncStorage
  const loadRecentScans = useCallback(async () => {
    try {
      const recentScansData = await AsyncStorage.getItem('recentScans');
      if (recentScansData) {
        return JSON.parse(recentScansData);
      }
      return [];
    } catch (error) {
      console.error('Error loading recent scans:', error);
      return [];
    }
  }, []);
  
  // Save recent scan to AsyncStorage
  const saveRecentScan = useCallback(async (scan: RecentScan) => {
    try {
      const existingScansData = await AsyncStorage.getItem('recentScans');
      let existingScans: RecentScan[] = existingScansData ? JSON.parse(existingScansData) : [];
      
      // Limit to last 10 scans
      const updatedScans = [scan, ...existingScans].slice(0, 10);
      
      await AsyncStorage.setItem('recentScans', JSON.stringify(updatedScans));
      return updatedScans;
    } catch (error) {
      console.error('Error saving recent scan:', error);
      return [];
    }
  }, []);

  // Handle image selection from gallery
  const handleOpenGallery = useCallback(async (explicitUserRequest = false) => {
    // Safety check - only proceed if explicitly requested by user action
    // This prevents automatic opening on component mount or initialization
    if (!explicitUserRequest) {
      console.log('Gallery open prevented - not explicitly requested by user');
      return null;
    }
    
    // Clear previous data
    setImageUri('');
    setAnalysisResult(null);
    setError(null);
    setProgress(0);
    
    try {
      // Update progress for checking permissions
      setProgress(10);
      setCurrentStep('Checking photo permissions...');
      
      // Check for permissions first
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        setPhotoPermissionDenied(true);
        setLoading(false); // Make sure to reset loading state
        return null;
      }
      
      // Update progress for opening gallery
      setProgress(30);
      setCurrentStep('Opening photo gallery...');
      
      // Launch the image picker when explicitly requested
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        // Update progress for image selection
        setProgress(50);
        setCurrentStep('Image selected, preparing for analysis...');
        
        const selectedUri = result.assets[0].uri;
        console.log('Image selected from gallery:', selectedUri);
        return selectedUri;
      } else {
        // User canceled, reset loading state
        setLoading(false);
      }
    } catch (error) {
      console.error('Error opening gallery:', error);
      setError('Failed to open photo gallery. Please try again.');
      setLoading(false); // Make sure to reset loading state
    }
    
    return null;
  }, [setLoading, setProgress, setCurrentStep, setError, setPhotoPermissionDenied]);
  
  // Helper function to simulate progress updates
  const updateProgress = useCallback((stage: number, total: number, stepText: string) => {
    // Calculate percentage based on current stage out of total stages
    const percentage = Math.floor((stage / total) * 100);
    console.log(`Progress update: ${percentage}% - ${stepText}`);
    setProgress(percentage);
    setCurrentStep(stepText);
  }, []);
  
  // Analyze food image
  const analyzeImage = useCallback(async (uri: string): Promise<AnalyzeImageResponse | undefined> => {
    setLoading(true);
    setAnalysisResult(null);
    setProgress(0);
    setCurrentStep('Starting analysis...');
    
    // Type annotation for the returned analysis results
    let analysisResponse: AnalyzeImageResponse | undefined;
    
    // Progress tracking stages
    const totalStages = 7;
    let currentStage = 0;
    
    // Add a failsafe timeout that will reset the loading state if analysis takes too long
    const loadingTimeout = setTimeout(() => {
      // Check if analysis is still considered loading by the component state
      if (!analysisResult) { // Only set timeout result if no result exists yet
        console.log('Analysis timeout reached - setting placeholder');
        setProgress(100); // Set progress to 100% when timeout occurs
        setCurrentStep('Finalizing results...');
        
        const simpleTimeoutResult = {
          success: true,
          data: {
            name: "Analysis Timed Out",
            description: "Processing took longer than expected.",
            calories: 0, 
            protein: 0, 
            carbs: 0, 
            fat: 0,
            items: [],
            isPartial: true,
            partialAnalysisMessage: "Analysis took longer than usual. Showing placeholder."
          }
        };
        setAnalysisResult(simpleTimeoutResult);
      }
    }, 20000); // 20 seconds timeout
    
    try {
      console.log('Starting image analysis for:', uri);
      
      // Update progress: Initializing
      updateProgress(++currentStage, totalStages, 'Preparing image...');
      await new Promise(resolve => setTimeout(resolve, 300)); // Small delay for visual feedback
      
      // Check if we have a cached result for this image
      updateProgress(++currentStage, totalStages, 'Checking for cached results...');
      const cachedResults = await AsyncStorage.getItem('cachedAnalysis');
      const cachedData = cachedResults ? JSON.parse(cachedResults) : {};
      
      if (cachedData[uri] && false) { // Temporarily disabled caching for testing
        console.log('Using cached analysis result');
        
        // Simulate progress through stages even for cached results
        for (let i = currentStage; i < totalStages - 1; i++) {
          updateProgress(++currentStage, totalStages, 'Retrieving cached data...');
          await new Promise(resolve => setTimeout(resolve, 200)); // Small delay for visual feedback
        }
        
        setAnalysisResult(cachedData[uri]);
        
        // Add to recent scans
        if (cachedData[uri].success && cachedData[uri].data) {
          const { name, calories } = cachedData[uri].data;
          const newScan: RecentScan = {
            id: Date.now().toString(),
            imageUri: uri,
            foodName: name,
            timestamp: Date.now(),
            calories
          };
          await saveRecentScan(newScan);
        }
        
        updateProgress(totalStages, totalStages, 'Analysis complete!');
      } else {
        // Perform the analysis
        // Update progress: Uploading image
        updateProgress(++currentStage, totalStages, 'Uploading image for analysis...');
        await new Promise(resolve => setTimeout(resolve, 500)); // Simulate upload time
        
        // Update progress: Processing image
        updateProgress(++currentStage, totalStages, 'Processing image...');
        
        // Update progress: Detecting food items
        updateProgress(++currentStage, totalStages, 'Identifying food items...');
        
        // Actual analysis call
        const result = await analyzeFoodImage(uri) as AnalyzeImageResponse;
        analysisResponse = result;
        
        // Update progress: Calculating nutrition
        updateProgress(++currentStage, totalStages, 'Calculating nutrition information...');
        
        // Update progress: Finalizing
        updateProgress(totalStages, totalStages, 'Finalizing results...');
        
        setAnalysisResult(result);
        
        // Cache the result
        if (result.success) {
          const updatedCache = { ...cachedData, [uri]: result };
          await AsyncStorage.setItem('cachedAnalysis', JSON.stringify(updatedCache));
          
          // Add to recent scans
          if (result.data) {
            const { name, calories } = result.data;
            const newScan: RecentScan = {
              id: Date.now().toString(),
              imageUri: uri,
              foodName: name,
              timestamp: Date.now(),
              calories
            };
            await saveRecentScan(newScan);
          }
        }
      }
    } catch (error) {
      console.error('Error analyzing image:', error);
      setAnalysisResult({
        success: false,
        error: 'An error occurred while analyzing the image.'
      });
      setProgress(100); // Complete the progress bar even on error
      setCurrentStep('Error analyzing image');
      Alert.alert(
        "Analysis Error",
        "There was a problem analyzing your food. Please try again with a different image.",
        [{ text: "OK" }]
      );
    } finally {
      clearTimeout(loadingTimeout);
      setLoading(false);
      console.log('Analysis completed in original analyzeImage function, loading state set to false');
    }
    
    return analysisResponse;
  }, [saveRecentScan, analysisResult, updateProgress]);

  // Clear all image analysis cache
  const clearImageCache = useCallback(async () => {
    try {
      // Web platform needs a different approach
      const asyncStorageKeys = await AsyncStorage.getAllKeys();
      const analysisCacheKeys = asyncStorageKeys.filter(key => 
        key.startsWith('food_analysis_') || 
        key.startsWith('google_vision_') || 
        key.startsWith('labels_') ||
        key === 'cachedAnalysis'
      );
      
      if (analysisCacheKeys.length > 0) {
        await AsyncStorage.multiRemove(analysisCacheKeys);
        console.log(`Cleared ${analysisCacheKeys.length} cache entries from AsyncStorage`);
      }
      
      Alert.alert(
        "Cache Cleared", 
        "Image analysis cache has been cleared. This should resolve any issues with food recognition.",
        [{ text: "OK" }]
      );
    } catch (error) {
      console.error('Error clearing cache:', error);
      Alert.alert(
        "Cache Error", 
        "Failed to clear cache. Try refreshing the app.",
        [{ text: "OK" }]
      );
    }
  }, []);

  // Generate healthier alternative recipes
  const generateHealthierAlternative = useCallback(async () => {
    if (!analysisResult || !analysisResult.data) {
      Alert.alert('Error', 'No food analysis available to generate alternatives');
      return null;
    }

    try {
      const originalNutrition = {
        calories: analysisResult.data.calories || 0,
        protein: analysisResult.data.protein || 0,
        carbs: analysisResult.data.carbs || 0,
        fat: analysisResult.data.fat || 0
      };

      // Get the original ingredients if available
      const ingredients = analysisResult.data.items || [];
      console.log('Recipe generation with ingredients:', ingredients.length);

      const result = await generateAlternativeRecipe({
        originalFoodName: analysisResult.data.name,
        nutritionalInfo: originalNutrition,
        originalIngredients: ingredients.length > 0 ? ingredients : undefined
      });

      if (!result.success || !result.recipes || result.recipes.length === 0) {
        throw new Error(result.error || 'Failed to generate alternative recipes');
      }

      // Add originalNutrition to each recipe for comparison
      const recipesWithOriginalNutrition = result.recipes.map(recipe => ({
        ...recipe,
        originalNutrition
      }));

      return recipesWithOriginalNutrition;
    } catch (error) {
      console.error('Error generating healthier alternative:', error);
      Alert.alert(
        'Alternative Generation Error',
        'There was a problem generating a healthier alternative. Please try again.',
        [{ text: 'OK' }]
      );
      return null;
    }
  }, [analysisResult]);

  return {
    loading,
    setLoading,
    error,
    setError,
    imageUri,
    setImageUri,
    analysisResult,
    setAnalysisResult,
    photoPermissionDenied,
    setPhotoPermissionDenied,
    loadRecentScans,
    saveRecentScan,
    handleOpenGallery,
    analyzeImage,
    clearImageCache,
    generateHealthierAlternative,
    progress,
    currentStep,
    setProgress,
    setCurrentStep
  };
} 