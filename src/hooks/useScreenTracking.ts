import { useEffect } from 'react';
import { usePathname } from 'expo-router';
import { contextualNavigation } from '@/utils/contextualNavigation';

/**
 * Hook to track screen navigation for contextual suggestions
 * Automatically tracks screen entry and exit
 */
export function useScreenTracking() {
  const pathname = usePathname();
  
  useEffect(() => {
    // When the pathname changes, track screen entry
    contextualNavigation.trackScreenEntry(pathname);
    
    // On unmount or before changing to a new screen, track exit
    return () => {
      contextualNavigation.trackScreenExit();
    };
  }, [pathname]);
}

export default useScreenTracking; 