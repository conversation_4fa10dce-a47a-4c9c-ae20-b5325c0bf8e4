import { useState, useCallback, useRef } from 'react';

interface SyncState {
  /**
   * When the last sync happened
   */
  lastSynced: Date | null;
  
  /**
   * Whether the sync notification should be shown
   */
  showNotification: boolean;
  
  /**
   * Function to call when sync is complete
   */
  onSyncComplete: () => void;
  
  /**
   * Function to call when sync starts
   */
  onSyncStart: () => void;
  
  /**
   * Function to call when notification is dismissed
   */
  onNotificationDismiss: () => void;
  
  /**
   * Whether a sync is currently in progress
   */
  isSyncing: boolean;
}

/**
 * Custom hook to manage sync state and notifications
 * 
 * @returns State and functions for managing sync notifications
 */
export function useSyncNotification(): SyncState {
  const [lastSynced, setLastSynced] = useState<Date | null>(null);
  const [showNotification, setShowNotification] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  
  /**
   * Start the sync process
   */
  const onSyncStart = useCallback(() => {
    setIsSyncing(true);
  }, []);
  
  /**
   * Call this function when synchronization is complete
   */
  const onSyncComplete = useCallback(() => {
    const now = new Date();
    setLastSynced(now);
    setIsSyncing(false);
    // Show the notification
    setShowNotification(true);
  }, []);
  
  /**
   * Call this function when the notification is dismissed
   */
  const onNotificationDismiss = useCallback(() => {
    // Ensure notification is hidden
    setShowNotification(false);
  }, []);
  
  return {
    lastSynced,
    showNotification,
    onSyncComplete,
    onSyncStart,
    onNotificationDismiss,
    isSyncing,
  };
} 