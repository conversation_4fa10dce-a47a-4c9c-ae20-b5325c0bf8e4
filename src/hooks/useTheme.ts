import { useColorScheme } from 'react-native';
import { useMemo } from 'react';

interface ThemeColors {
  primary: string;
  background: string;
  card: string;
  text: string;
  textSecondary: string;
  textTertiary: string;
  border: string;
}

export function useTheme() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const colors = useMemo<ThemeColors>(() => {
    if (isDark) {
      return {
        primary: '#0A84FF',
        background: '#000000',
        card: '#1C1C1E',
        text: '#FFFFFF',
        textSecondary: '#ABABAB',
        textTertiary: '#757575',
        border: '#38383A',
      };
    }
    
    return {
      primary: '#007AFF',
      background: '#F2F2F7',
      card: '#FFFFFF',
      text: '#000000',
      textSecondary: '#6D6D72',
      textTertiary: '#A9A9A9',
      border: '#E5E5EA',
    };
  }, [isDark]);

  return { colors, isDark };
} 