import { useState, useEffect, useCallback } from 'react';
import { Platform } from 'react-native';
import Constants from 'expo-constants';
import * as Device from 'expo-device';
import { 
  initHealthIntegration, 
  getHealthData, 
  writeHealthData, 
  syncHealthData, 
  subscribeToHealthUpdates,
  HealthDataType,
  isWearableSupported
} from '@/services/healthIntegrationService';
import { useAlert } from './useAlert';

export interface HealthMetric {
  date: string;
  value: number;
  sourceType?: string;
  sourceName?: string;
  unit?: string;
}

export interface ConnectedDevice {
  id: string;
  name: string;
  type: 'watch' | 'tracker' | 'other';
  platform: 'apple' | 'google' | 'unknown';
  connected: boolean;
  lastSync?: string;
}

export function useWearableDevices() {
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [isInitializing, setIsInitializing] = useState<boolean>(false);
  const [connectedDevices, setConnectedDevices] = useState<ConnectedDevice[]>([]);
  const [healthData, setHealthData] = useState<Record<HealthDataType, HealthMetric[]>>({} as Record<HealthDataType, HealthMetric[]>);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { showAlert } = useAlert();

  // Initialize health integration
  const initialize = useCallback(async () => {
    if (isInitializing) return;
    
    setIsInitializing(true);
    setError(null);
    
    try {
      const success = await initHealthIntegration();
      
      if (success) {
        setIsInitialized(true);
        
        // Create a placeholder connected device based on the platform
        const deviceInfo = {
          id: '1',
          name: Platform.OS === 'ios' ? 'Apple Watch' : 'Android Wearable',
          type: 'watch',
          platform: Platform.OS === 'ios' ? 'apple' : 'google',
          connected: true,
          lastSync: new Date().toISOString(),
        } as ConnectedDevice;
        
        setConnectedDevices([deviceInfo]);
      } else {
        setError('Failed to initialize health integration');
      }
    } catch (err) {
      setError(`Error initializing health integration: ${err}`);
    } finally {
      setIsInitializing(false);
    }
  }, [isInitializing]);

  // Check if the device supports wearable integration
  const supportsWearable = useCallback(() => {
    if (!isWearableSupported()) {
      return false;
    }

    // Check for specific device capabilities
    if (Platform.OS === 'ios') {
      // iOS supports Apple Watch integration via HealthKit
      return true;
    } else if (Platform.OS === 'android') {
      // For Android, check for Wear OS or fitness integration support
      return true; // Simplified check - in production you might want more specific detection
    }
    
    return false;
  }, []);

  // Fetch health data from connected devices
  const fetchHealthData = useCallback(async (
    dataType: HealthDataType,
    startDate: string = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // Last 30 days
    endDate: string = new Date().toISOString()
  ) => {
    if (!isInitialized) {
      showAlert({
        title: 'Not Connected',
        message: 'Please connect to a wearable device first',
        type: 'warning'
      });
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await getHealthData(dataType, startDate, endDate);
      
      if (result.success && result.data) {
        setHealthData(prevData => ({
          ...prevData,
          [dataType]: result.data || []
        }));
        
        // Update last sync time for connected devices
        setConnectedDevices(prevDevices => 
          prevDevices.map(device => ({
            ...device,
            lastSync: new Date().toISOString()
          }))
        );
        
        return result.data;
      } else {
        setError(result.error || 'Failed to fetch health data');
        return [];
      }
    } catch (err) {
      const errorMsg = `Error fetching health data: ${err}`;
      setError(errorMsg);
      showAlert({
        title: 'Error',
        message: errorMsg,
        type: 'error'
      });
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [isInitialized, showAlert]);

  // Write health data to connected devices
  const writeHealth = useCallback(async (
    dataType: HealthDataType,
    value: number,
    date: string = new Date().toISOString(),
    unit?: string
  ) => {
    if (!isInitialized) {
      showAlert({
        title: 'Not Connected',
        message: 'Please connect to a wearable device first',
        type: 'warning'
      });
      return false;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await writeHealthData(dataType, value, date, unit);
      
      if (result.success) {
        // Update local data after successful write
        const newEntry: HealthMetric = {
          date,
          value,
          unit,
          sourceType: 'app',
          sourceName: Device.deviceName || 'Health App'
        };
        
        setHealthData(prevData => ({
          ...prevData,
          [dataType]: [...(prevData[dataType] || []), newEntry]
        }));
        
        // Update last sync time
        setConnectedDevices(prevDevices => 
          prevDevices.map(device => ({
            ...device,
            lastSync: new Date().toISOString()
          }))
        );
        
        return true;
      } else {
        setError(result.error || 'Failed to write health data');
        return false;
      }
    } catch (err) {
      const errorMsg = `Error writing health data: ${err}`;
      setError(errorMsg);
      showAlert({
        title: 'Error',
        message: errorMsg,
        type: 'error'
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [isInitialized, showAlert]);

  // Sync health data with server
  const syncHealth = useCallback(async (
    dataType: HealthDataType,
    startDate: string = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // Last 7 days
    endDate: string = new Date().toISOString()
  ) => {
    if (!isInitialized) {
      showAlert({
        title: 'Not Connected',
        message: 'Please connect to a wearable device first',
        type: 'warning'
      });
      return false;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await syncHealthData(dataType, startDate, endDate);
      
      if (result.success) {
        // Update last sync time
        setConnectedDevices(prevDevices => 
          prevDevices.map(device => ({
            ...device,
            lastSync: new Date().toISOString()
          }))
        );
        
        showAlert({
          title: 'Sync Complete',
          message: `Successfully synced ${dataType} data`,
          type: 'success'
        });
        
        return true;
      } else {
        setError(result.error || 'Failed to sync health data');
        showAlert({
          title: 'Sync Failed',
          message: result.error || 'Failed to sync health data',
          type: 'error'
        });
        return false;
      }
    } catch (err) {
      const errorMsg = `Error syncing health data: ${err}`;
      setError(errorMsg);
      showAlert({
        title: 'Error',
        message: errorMsg,
        type: 'error'
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [isInitialized, showAlert]);

  // Listen for real-time health updates
  const setupHealthListener = useCallback((
    dataType: HealthDataType,
    callback: (data: HealthMetric) => void
  ) => {
    if (!isInitialized) {
      showAlert({
        title: 'Not Connected',
        message: 'Please connect to a wearable device first',
        type: 'warning'
      });
      return { unsubscribe: () => {} };
    }
    
    return subscribeToHealthUpdates(dataType, callback);
  }, [isInitialized, showAlert]);

  // Disconnect from wearable devices
  const disconnect = useCallback(() => {
    setIsInitialized(false);
    setConnectedDevices([]);
    showAlert({
      title: 'Disconnected',
      message: 'Successfully disconnected from wearable devices',
      type: 'success'
    });
  }, [showAlert]);

  // Get information about supported wearable platforms
  const getSupportedPlatforms = useCallback(() => {
    return {
      apple: Platform.OS === 'ios',
      google: Platform.OS === 'android',
      wear: Platform.OS === 'android',
      isSupportedOnDevice: supportsWearable()
    };
  }, [supportsWearable]);

  // Auto-initialize on mount if possible
  useEffect(() => {
    if (supportsWearable() && !isInitialized && !isInitializing) {
      initialize();
    }
  }, [supportsWearable, isInitialized, isInitializing, initialize]);

  return {
    isInitialized,
    isInitializing,
    initialize,
    connectedDevices,
    healthData,
    isLoading,
    error,
    fetchHealthData,
    writeHealth,
    syncHealth,
    setupHealthListener,
    disconnect,
    supportsWearable: supportsWearable(),
    getSupportedPlatforms
  };
} 