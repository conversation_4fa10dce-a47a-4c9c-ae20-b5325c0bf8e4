import { getAuth, initializeAuth } from 'firebase/auth';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { app } from '@/firebase.config';

// Initialize Firebase Auth based on platform
let auth;

// Function to initialize auth based on the platform
export function initializeFirebaseAuth() {
  try {
    if (Platform.OS === 'web') {
      // Use standard auth initialization for Web
      auth = getAuth(app);
      console.log('Firebase Auth initialized for Web');
    } else {
      // For React Native, we need a different approach
      // First try the standard auth initialization
      auth = getAuth(app);
      
      // Manually handle persistence with AsyncStorage
      // This is a workaround since we can't directly use getReactNativePersistence
      console.log('Firebase Auth initialized for React Native');
      
      // Store the user manually in AsyncStorage when auth state changes
      auth.onAuthStateChanged(async (user) => {
        if (user) {
          // Store minimal user data
          try {
            await AsyncStorage.setItem('auth_user', JSON.stringify({
              uid: user.uid,
              email: user.email,
              displayName: user.displayName
            }));
          } catch (e) {
            console.error('Error storing auth state:', e);
          }
        } else {
          // Clear stored user data on sign out
          try {
            await AsyncStorage.removeItem('auth_user');
          } catch (e) {
            console.error('Error clearing auth state:', e);
          }
        }
      });
    }
    
    return auth;
  } catch (error) {
    console.error('Error initializing Firebase Auth:', error);
    return null;
  }
}

// Function to restore auth state from AsyncStorage (for React Native)
export async function restoreAuthState() {
  if (Platform.OS === 'web') return;
  
  try {
    const userData = await AsyncStorage.getItem('auth_user');
    if (userData) {
      console.log('Restored auth state from AsyncStorage');
      return JSON.parse(userData);
    }
  } catch (e) {
    console.error('Error restoring auth state:', e);
  }
  
  return null;
}

export { auth }; 