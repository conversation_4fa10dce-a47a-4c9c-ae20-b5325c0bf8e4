/**
 * AuthError<PERSON>elper
 * Centralized error handling and formatting for authentication
 */

import { Platform } from 'react-native';
import { 
  AuthError, 
  AuthErrorCode, 
  AuthErrorContext, 
  AuthOperation,
  CreateAuthErrorOptions,
  AuthErrorHelper as I<PERSON>uthError<PERSON>elper 
} from '@/types/auth';

export class AuthErrorHelper implements IAuthErrorHelper {
  
  /**
   * Create a standardized auth error
   */
  createAuthError(options: CreateAuthErrorOptions): AuthError {
    const { code, message, details, context, originalError } = options;
    
    return {
      code,
      message: message || this.getDefaultMessage(code),
      details: details || originalError,
      timestamp: new Date(),
      context: {
        operation: context?.operation || 'sign-in',
        platform: context?.platform || (Platform.OS as 'web' | 'ios' | 'android'),
        provider: context?.provider,
        userId: context?.userId,
        timestamp: context?.timestamp || new Date()
      }
    };
  }

  /**
   * Check if error is network-related
   */
  isNetworkError(error: AuthError): boolean {
    return error.code.startsWith('network/');
  }

  /**
   * Check if error is caused by user action
   */
  isUserError(error: AuthError): boolean {
    const userErrorCodes: AuthErrorCode[] = [
      'auth/wrong-password',
      'auth/invalid-email',
      'auth/weak-password',
      'oauth/google-cancelled',
      'oauth/apple-cancelled',
      'auth/popup-closed-by-user'
    ];
    
    return userErrorCodes.includes(error.code);
  }

  /**
   * Check if error is recoverable (user can retry)
   */
  isRecoverableError(error: AuthError): boolean {
    const recoverableErrorCodes: AuthErrorCode[] = [
      'network/offline',
      'network/timeout',
      'auth/too-many-requests',
      'auth/popup-blocked',
      'platform/service-unavailable',
      'oauth/token-exchange-failed'
    ];
    
    return recoverableErrorCodes.includes(error.code);
  }

  /**
   * Format error message for display to user
   */
  formatErrorMessage(error: AuthError): string {
    // User-friendly messages based on error codes
    const userMessages: Record<AuthErrorCode, string> = {
      // Network errors
      'network/offline': 'You appear to be offline. Please check your internet connection and try again.',
      'network/timeout': 'The request timed out. Please try again.',
      'network/unavailable': 'Network is unavailable. Please check your internet connection.',
      
      // Firebase Auth errors
      'auth/user-not-found': 'No account found with this email address.',
      'auth/wrong-password': 'Invalid email or password. Please try again.',
      'auth/email-already-in-use': 'An account with this email already exists.',
      'auth/weak-password': 'Password should be at least 6 characters long.',
      'auth/invalid-email': 'Please enter a valid email address.',
      'auth/user-disabled': 'This account has been disabled. Please contact support.',
      'auth/too-many-requests': 'Too many failed attempts. Please try again later.',
      'auth/popup-closed-by-user': 'Sign-in was cancelled. Please try again.',
      'auth/popup-blocked': 'Pop-up was blocked. Please allow pop-ups for this site.',
      'auth/cancelled-popup-request': 'Sign-in was cancelled. Please try again.',
      
      // OAuth specific errors
      'oauth/google-cancelled': 'Google sign-in was cancelled.',
      'oauth/apple-cancelled': 'Apple sign-in was cancelled.',
      'oauth/configuration-error': 'Authentication is not properly configured. Please contact support.',
      'oauth/token-exchange-failed': 'Authentication failed. Please try again.',
      
      // Platform errors
      'platform/not-supported': 'This authentication method is not supported on your device.',
      'platform/permission-denied': 'Permission denied. Please allow the required permissions.',
      'platform/service-unavailable': 'Authentication service is temporarily unavailable.',
      
      // Application errors
      'app/initialization-failed': 'Failed to initialize authentication. Please restart the app.',
      'app/profile-creation-failed': 'Account created but failed to set up profile. Please contact support.',
      'app/session-expired': 'Your session has expired. Please sign in again.',
      'app/unknown-error': 'Something went wrong. Please try again.'
    };

    return userMessages[error.code] || error.message || 'An unexpected error occurred.';
  }

  /**
   * Get error message from various error types
   */
  getErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error;
    }
    
    if (error?.message) {
      return error.message;
    }
    
    if (error?.code) {
      return this.getDefaultMessage(error.code);
    }
    
    return 'An unexpected error occurred';
  }

  /**
   * Get default message for error code
   */
  private getDefaultMessage(code: AuthErrorCode): string {
    const defaultMessages: Record<AuthErrorCode, string> = {
      'network/offline': 'Network connection is offline',
      'network/timeout': 'Request timed out',
      'network/unavailable': 'Network is unavailable',
      'auth/user-not-found': 'User not found',
      'auth/wrong-password': 'Invalid credentials',
      'auth/email-already-in-use': 'Email already in use',
      'auth/weak-password': 'Password is too weak',
      'auth/invalid-email': 'Invalid email format',
      'auth/user-disabled': 'User account disabled',
      'auth/too-many-requests': 'Too many requests',
      'auth/popup-closed-by-user': 'Sign-in popup closed',
      'auth/popup-blocked': 'Sign-in popup blocked',
      'auth/cancelled-popup-request': 'Sign-in cancelled',
      'oauth/google-cancelled': 'Google sign-in cancelled',
      'oauth/apple-cancelled': 'Apple sign-in cancelled',
      'oauth/configuration-error': 'OAuth configuration error',
      'oauth/token-exchange-failed': 'Token exchange failed',
      'platform/not-supported': 'Platform not supported',
      'platform/permission-denied': 'Permission denied',
      'platform/service-unavailable': 'Service unavailable',
      'app/initialization-failed': 'Initialization failed',
      'app/profile-creation-failed': 'Profile creation failed',
      'app/session-expired': 'Session expired',
      'app/unknown-error': 'Unknown error occurred'
    };

    return defaultMessages[code] || 'Unknown error';
  }

  /**
   * Log error with appropriate level based on severity
   */
  logError(error: AuthError): void {
    const logData = {
      code: error.code,
      message: error.message,
      context: error.context,
      timestamp: error.timestamp
    };

    if (this.isUserError(error)) {
      // User errors are less critical
      console.warn('Auth User Error:', logData);
    } else if (this.isNetworkError(error)) {
      // Network errors are informational
      console.info('Auth Network Error:', logData);
    } else {
      // System errors are critical
      console.error('Auth System Error:', logData, error.details);
    }
  }

  /**
   * Create auth error from Firebase error
   */
  createFromFirebaseError(firebaseError: any, operation: AuthOperation): AuthError {
    const code = this.mapFirebaseErrorCode(firebaseError?.code);
    
    return this.createAuthError({
      code,
      message: this.getErrorMessage(firebaseError),
      details: firebaseError,
      context: {
        operation,
        platform: Platform.OS as 'web' | 'ios' | 'android',
        timestamp: new Date()
      },
      originalError: firebaseError
    });
  }

  /**
   * Map Firebase error codes to our standard codes
   */
  private mapFirebaseErrorCode(firebaseCode?: string): AuthErrorCode {
    const codeMap: Record<string, AuthErrorCode> = {
      'auth/user-not-found': 'auth/user-not-found',
      'auth/wrong-password': 'auth/wrong-password',
      'auth/invalid-credential': 'auth/wrong-password',
      'auth/email-already-in-use': 'auth/email-already-in-use',
      'auth/weak-password': 'auth/weak-password',
      'auth/invalid-email': 'auth/invalid-email',
      'auth/user-disabled': 'auth/user-disabled',
      'auth/too-many-requests': 'auth/too-many-requests',
      'auth/popup-closed-by-user': 'auth/popup-closed-by-user',
      'auth/popup-blocked': 'auth/popup-blocked',
      'auth/cancelled-popup-request': 'auth/cancelled-popup-request',
      'auth/network-request-failed': 'network/unavailable',
      'auth/timeout': 'network/timeout'
    };

    return codeMap[firebaseCode || ''] || 'app/unknown-error';
  }

  /**
   * Get retry strategy for error
   */
  getRetryStrategy(error: AuthError): { shouldRetry: boolean; delay: number; maxRetries: number } {
    if (!this.isRecoverableError(error)) {
      return { shouldRetry: false, delay: 0, maxRetries: 0 };
    }

    // Network errors: retry with exponential backoff
    if (this.isNetworkError(error)) {
      return { shouldRetry: true, delay: 1000, maxRetries: 3 };
    }

    // Rate limiting: retry with longer delay
    if (error.code === 'auth/too-many-requests') {
      return { shouldRetry: true, delay: 30000, maxRetries: 2 };
    }

    // Service unavailable: retry with moderate delay
    if (error.code === 'platform/service-unavailable') {
      return { shouldRetry: true, delay: 5000, maxRetries: 2 };
    }

    return { shouldRetry: false, delay: 0, maxRetries: 0 };
  }
}