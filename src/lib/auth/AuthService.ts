/**
 * AuthService
 * Core authentication business logic separated from UI concerns
 */

import { Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import { 
  getAuth, 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  updateProfile,
  User as FirebaseUser
} from 'firebase/auth';
import { 
  doc, 
  getDoc, 
  setDoc, 
  Timestamp,
  getFirestore
} from 'firebase/firestore';

import { 
  AuthResult, 
  AuthServiceInterface, 
  User, 
  AuthError, 
  AuthOperation, 
  AuthProvider,
  UserProfile,
  PlatformCapabilities 
} from '@/types/auth';
import { AUTH_CONFIG, TEST_ACCOUNTS, DEV_CONFIG } from '@/config/auth';
import { AuthErrorHelper } from './AuthErrorHelper';
import { PlatformAuthHandler } from './PlatformAuthHandler';

export class AuthService implements AuthServiceInterface {
  private auth = getAuth();
  private db = getFirestore();
  private errorHelper = new AuthErrorHelper();
  private platformHandler = new PlatformAuthHandler();

  /**
   * Sign in with email and password
   */
  async signInWithEmail(email: string, password: string): Promise<AuthResult> {
    const startTime = Date.now();
    
    try {
      console.log('AuthService: Attempting email sign in for:', email);

      // Check network connectivity
      const networkCheck = await this.checkNetworkConnectivity();
      if (!networkCheck.success) {
        return networkCheck;
      }

      // Clear any existing auth state
      await this.clearAuthState();

      // Sign in with Firebase
      const userCredential = await signInWithEmailAndPassword(this.auth, email, password);
      console.log('AuthService: Email sign in successful:', userCredential.user.uid);

      // Create user object
      const user = await this.createUserFromFirebaseUser(userCredential.user);

      // Update profile with last login
      await this.updateUserProfile(userCredential.user.uid, {
        last_login: Timestamp.now()
      });

      return {
        success: true,
        user,
        metadata: {
          duration: Date.now() - startTime,
          provider: 'email',
          operation: 'sign-in'
        }
      };

    } catch (error) {
      console.error('AuthService: Email sign in error:', error);
      return {
        success: false,
        error: this.errorHelper.createAuthError({
          code: this.mapFirebaseErrorCode(error),
          message: this.errorHelper.getErrorMessage(error),
          details: error,
          context: {
            operation: 'sign-in',
            platform: Platform.OS as 'web' | 'ios' | 'android',
            provider: 'email',
            timestamp: new Date()
          }
        }),
        metadata: {
          duration: Date.now() - startTime,
          provider: 'email',
          operation: 'sign-in'
        }
      };
    }
  }

  /**
   * Sign up with email, password, and name
   */
  async signUp(email: string, password: string, name: string): Promise<AuthResult> {
    const startTime = Date.now();
    
    try {
      console.log('AuthService: Attempting sign up for:', email);

      // Check network connectivity
      const networkCheck = await this.checkNetworkConnectivity();
      if (!networkCheck.success) {
        return networkCheck;
      }

      // Clear any existing auth state
      await this.clearAuthState();

      // Create user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);
      const firebaseUser = userCredential.user;

      // Update Firebase profile
      await updateProfile(firebaseUser, {
        displayName: name
      });

      // Create Firestore profile
      await this.createUserProfile(firebaseUser, name);

      // Create user object
      const user = await this.createUserFromFirebaseUser(firebaseUser, name);

      console.log('AuthService: Sign up successful:', firebaseUser.uid);

      return {
        success: true,
        user,
        metadata: {
          duration: Date.now() - startTime,
          provider: 'email',
          operation: 'sign-up'
        }
      };

    } catch (error) {
      console.error('AuthService: Sign up error:', error);
      return {
        success: false,
        error: this.errorHelper.createAuthError({
          code: this.mapFirebaseErrorCode(error),
          message: this.errorHelper.getErrorMessage(error),
          details: error,
          context: {
            operation: 'sign-up',
            platform: Platform.OS as 'web' | 'ios' | 'android',
            provider: 'email',
            timestamp: new Date()
          }
        }),
        metadata: {
          duration: Date.now() - startTime,
          provider: 'email',
          operation: 'sign-up'
        }
      };
    }
  }

  /**
   * Sign in with Google
   */
  async signInWithGoogle(): Promise<AuthResult> {
    const startTime = Date.now();
    
    try {
      console.log('AuthService: Attempting Google sign in');

      // Check network connectivity
      const networkCheck = await this.checkNetworkConnectivity();
      if (!networkCheck.success) {
        return networkCheck;
      }

      // Use platform-specific handler
      const result = await this.platformHandler.signInWithGoogle();
      
      if (result.success && result.user) {
        // Update last login
        await this.updateUserProfile(result.user.id, {
          last_login: Timestamp.now()
        });
      }

      return {
        ...result,
        metadata: {
          duration: Date.now() - startTime,
          provider: 'google.com',
          operation: 'google-oauth',
          ...result.metadata
        }
      };

    } catch (error) {
      console.error('AuthService: Google sign in error:', error);
      return {
        success: false,
        error: this.errorHelper.createAuthError({
          code: 'oauth/google-cancelled',
          message: 'Google sign in failed',
          details: error,
          context: {
            operation: 'google-oauth',
            platform: Platform.OS as 'web' | 'ios' | 'android',
            provider: 'google.com',
            timestamp: new Date()
          }
        }),
        metadata: {
          duration: Date.now() - startTime,
          provider: 'google.com',
          operation: 'google-oauth'
        }
      };
    }
  }

  /**
   * Sign in with Apple
   */
  async signInWithApple(): Promise<AuthResult> {
    const startTime = Date.now();
    
    try {
      console.log('AuthService: Attempting Apple sign in');

      // Check network connectivity
      const networkCheck = await this.checkNetworkConnectivity();
      if (!networkCheck.success) {
        return networkCheck;
      }

      // Use platform-specific handler
      const result = await this.platformHandler.signInWithApple();
      
      if (result.success && result.user) {
        // Update last login
        await this.updateUserProfile(result.user.id, {
          last_login: Timestamp.now()
        });
      }

      return {
        ...result,
        metadata: {
          duration: Date.now() - startTime,
          provider: 'apple.com',
          operation: 'apple-oauth',
          ...result.metadata
        }
      };

    } catch (error) {
      console.error('AuthService: Apple sign in error:', error);
      return {
        success: false,
        error: this.errorHelper.createAuthError({
          code: 'oauth/apple-cancelled',
          message: 'Apple sign in failed',
          details: error,
          context: {
            operation: 'apple-oauth',
            platform: Platform.OS as 'web' | 'ios' | 'android',
            provider: 'apple.com',
            timestamp: new Date()
          }
        }),
        metadata: {
          duration: Date.now() - startTime,
          provider: 'apple.com',
          operation: 'apple-oauth'
        }
      };
    }
  }

  /**
   * Sign out current user
   */
  async signOut(): Promise<AuthResult> {
    const startTime = Date.now();
    
    try {
      console.log('AuthService: Signing out user');

      // Platform-specific cleanup
      await this.platformHandler.cleanup();

      // Firebase sign out
      await firebaseSignOut(this.auth);

      console.log('AuthService: Sign out successful');

      return {
        success: true,
        user: null,
        metadata: {
          duration: Date.now() - startTime,
          provider: 'email', // Provider doesn't matter for signout
          operation: 'sign-out'
        }
      };

    } catch (error) {
      console.error('AuthService: Sign out error:', error);
      return {
        success: false,
        error: this.errorHelper.createAuthError({
          code: 'app/unknown-error',
          message: 'Failed to sign out',
          details: error,
          context: {
            operation: 'sign-out',
            platform: Platform.OS as 'web' | 'ios' | 'android',
            timestamp: new Date()
          }
        }),
        metadata: {
          duration: Date.now() - startTime,
          provider: 'email',
          operation: 'sign-out'
        }
      };
    }
  }

  /**
   * Refresh current session
   */
  async refreshSession(): Promise<AuthResult> {
    const startTime = Date.now();
    
    try {
      const currentUser = this.auth.currentUser;
      
      if (!currentUser) {
        return {
          success: false,
          error: this.errorHelper.createAuthError({
            code: 'app/session-expired',
            message: 'No active session to refresh',
            context: {
              operation: 'refresh-session',
              platform: Platform.OS as 'web' | 'ios' | 'android',
              timestamp: new Date()
            }
          })
        };
      }

      // Refresh token if needed
      await currentUser.getIdToken(true);

      // Create updated user object
      const user = await this.createUserFromFirebaseUser(currentUser);

      return {
        success: true,
        user,
        metadata: {
          duration: Date.now() - startTime,
          provider: this.getProviderFromFirebaseUser(currentUser),
          operation: 'refresh-session'
        }
      };

    } catch (error) {
      console.error('AuthService: Session refresh error:', error);
      return {
        success: false,
        error: this.errorHelper.createAuthError({
          code: 'app/session-expired',
          message: 'Failed to refresh session',
          details: error,
          context: {
            operation: 'refresh-session',
            platform: Platform.OS as 'web' | 'ios' | 'android',
            timestamp: new Date()
          }
        }),
        metadata: {
          duration: Date.now() - startTime,
          provider: 'email',
          operation: 'refresh-session'
        }
      };
    }
  }

  /**
   * Get current user
   */
  getCurrentUser(): User | null {
    const firebaseUser = this.auth.currentUser;
    if (!firebaseUser) return null;

    return {
      id: firebaseUser.uid,
      email: firebaseUser.email || '',
      name: firebaseUser.displayName || '',
      photoURL: firebaseUser.photoURL || undefined,
      provider: this.getProviderFromFirebaseUser(firebaseUser),
      verified: firebaseUser.emailVerified
    };
  }

  /**
   * Get platform capabilities
   */
  getCapabilities(): PlatformCapabilities {
    return this.platformHandler.getCapabilities();
  }

  // Private helper methods

  private async checkNetworkConnectivity(): Promise<AuthResult> {
    const netInfo = await NetInfo.fetch();
    
    if (!netInfo.isConnected || !netInfo.isInternetReachable) {
      return {
        success: false,
        error: this.errorHelper.createAuthError({
          code: 'network/unavailable',
          message: 'Network is unavailable. Please check your internet connection and try again.',
          context: {
            operation: 'sign-in',
            platform: Platform.OS as 'web' | 'ios' | 'android',
            timestamp: new Date()
          }
        })
      };
    }

    return { success: true };
  }

  private async clearAuthState(): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // Import clearAuthState on-demand to avoid circular dependency
        const { clearAuthState } = await import('@/lib/firebase');
        await clearAuthState(true);
      } else {
        // For mobile, just sign out directly
        try {
          await firebaseSignOut(this.auth);
        } catch (e) {
          console.warn('AuthService: Error in pre-signout:', e);
        }
      }
    } catch (error) {
      console.warn('AuthService: Error clearing auth state:', error);
    }
  }

  private async createUserFromFirebaseUser(firebaseUser: FirebaseUser, providedName?: string): Promise<User> {
    let name = providedName || firebaseUser.displayName || '';

    // Try to get name from Firestore profile
    if (!name) {
      try {
        const userDoc = await getDoc(doc(this.db, 'profiles', firebaseUser.uid));
        if (userDoc.exists()) {
          const userData = userDoc.data();
          name = userData.full_name || '';
        }
      } catch (error) {
        console.warn('AuthService: Could not fetch profile name:', error);
      }
    }

    return {
      id: firebaseUser.uid,
      email: firebaseUser.email || '',
      name,
      photoURL: firebaseUser.photoURL || undefined,
      provider: this.getProviderFromFirebaseUser(firebaseUser),
      verified: firebaseUser.emailVerified,
      createdAt: firebaseUser.metadata.creationTime ? new Date(firebaseUser.metadata.creationTime) : undefined,
      lastLoginAt: firebaseUser.metadata.lastSignInTime ? new Date(firebaseUser.metadata.lastSignInTime) : undefined
    };
  }

  private async createUserProfile(firebaseUser: FirebaseUser, name: string): Promise<void> {
    const profileData: Partial<UserProfile> = {
      id: firebaseUser.uid,
      email: firebaseUser.email || '',
      full_name: name,
      created_at: Timestamp.now(),
      updated_at: Timestamp.now(),
      auth_provider: this.getProviderFromFirebaseUser(firebaseUser),
      daily_calorie_goal: 2000
    };

    await setDoc(doc(this.db, 'profiles', firebaseUser.uid), profileData);
  }

  private async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<void> {
    try {
      await setDoc(doc(this.db, 'profiles', userId), {
        ...updates,
        updated_at: Timestamp.now()
      }, { merge: true });
    } catch (error) {
      console.warn('AuthService: Failed to update user profile:', error);
    }
  }

  private getProviderFromFirebaseUser(firebaseUser: FirebaseUser): AuthProvider {
    const providerId = firebaseUser.providerData[0]?.providerId;
    
    switch (providerId) {
      case 'google.com':
        return 'google.com';
      case 'apple.com':
        return 'apple.com';
      default:
        return 'email';
    }
  }

  private mapFirebaseErrorCode(error: any): string {
    const errorCode = error?.code || '';
    
    // Map Firebase error codes to our standard error codes
    switch (errorCode) {
      case 'auth/user-not-found':
      case 'auth/wrong-password':
      case 'auth/invalid-credential':
        return 'auth/wrong-password';
      case 'auth/email-already-in-use':
        return 'auth/email-already-in-use';
      case 'auth/weak-password':
        return 'auth/weak-password';
      case 'auth/invalid-email':
        return 'auth/invalid-email';
      case 'auth/user-disabled':
        return 'auth/user-disabled';
      case 'auth/too-many-requests':
        return 'auth/too-many-requests';
      case 'auth/popup-closed-by-user':
        return 'auth/popup-closed-by-user';
      case 'auth/popup-blocked':
        return 'auth/popup-blocked';
      default:
        return 'app/unknown-error';
    }
  }
}