/**
 * AuthStateManager
 * Centralized state management for authentication
 */

import { 
  AuthState, 
  AuthStateManagerInterface, 
  User, 
  AuthError, 
  AuthOperation,
  SessionInfo,
  AuthStateChangeEvent 
} from '@/types/auth';

type StateListener = (state: AuthState) => void;
type EventListener = (event: AuthStateChangeEvent) => void;

export class AuthStateManager implements AuthStateManagerInterface {
  private state: AuthState;
  private listeners: Set<StateListener> = new Set();
  private eventListeners: Set<EventListener> = new Set();

  constructor() {
    this.state = this.getInitialState();
  }

  /**
   * Get current auth state
   */
  getState(): AuthState {
    return { ...this.state };
  }

  /**
   * Update auth state
   */
  setState(updates: Partial<AuthState>): void {
    const previousState = { ...this.state };
    
    this.state = {
      ...this.state,
      ...updates
    };

    // Derived state updates
    this.state.isAuthenticated = !!this.state.user;

    // Notify listeners if state actually changed
    if (this.hasStateChanged(previousState, this.state)) {
      this.notifyListeners();
      this.emitStateChangeEvent(previousState);
    }
  }

  /**
   * Subscribe to state changes
   */
  subscribe(listener: StateListener): () => void {
    this.listeners.add(listener);
    
    // Immediately call listener with current state
    listener(this.getState());
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Subscribe to auth events
   */
  subscribeToEvents(listener: EventListener): () => void {
    this.eventListeners.add(listener);
    
    return () => {
      this.eventListeners.delete(listener);
    };
  }

  /**
   * Clear all state
   */
  clearState(): void {
    this.setState({
      user: null,
      isLoading: false,
      isAuthenticated: false,
      error: null,
      lastOperation: null,
      sessionInfo: null
    });
  }

  /**
   * Set user and create session info
   */
  setUser(user: User | null, operation?: AuthOperation): void {
    const sessionInfo: SessionInfo | null = user ? {
      sessionId: this.generateSessionId(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      refreshedAt: new Date(),
      platform: this.getPlatform(),
      provider: user.provider
    } : null;

    this.setState({
      user,
      sessionInfo,
      lastOperation: operation || null,
      error: null, // Clear any previous errors
      isLoading: false
    });
  }

  /**
   * Set error state
   */
  setError(error: AuthError | null, operation?: AuthOperation): void {
    this.setState({
      error,
      lastOperation: operation || null,
      isLoading: false
    });
  }

  /**
   * Set loading state
   */
  setLoading(isLoading: boolean, operation?: AuthOperation): void {
    this.setState({
      isLoading,
      lastOperation: operation || null
    });
  }

  /**
   * Clear error
   */
  clearError(): void {
    this.setState({
      error: null
    });
  }

  /**
   * Update session info (for token refresh, etc.)
   */
  refreshSession(): void {
    if (this.state.sessionInfo) {
      this.setState({
        sessionInfo: {
          ...this.state.sessionInfo,
          refreshedAt: new Date()
        }
      });
    }
  }

  /**
   * Check if session is expired
   */
  isSessionExpired(): boolean {
    if (!this.state.sessionInfo) {
      return true;
    }

    return new Date() > this.state.sessionInfo.expiresAt;
  }

  /**
   * Get session time remaining in milliseconds
   */
  getSessionTimeRemaining(): number {
    if (!this.state.sessionInfo) {
      return 0;
    }

    const remaining = this.state.sessionInfo.expiresAt.getTime() - Date.now();
    return Math.max(0, remaining);
  }

  /**
   * Check if user has specific provider
   */
  hasProvider(provider: string): boolean {
    return this.state.user?.provider === provider;
  }

  /**
   * Get user ID safely
   */
  getUserId(): string | null {
    return this.state.user?.id || null;
  }

  /**
   * Get user email safely
   */
  getUserEmail(): string | null {
    return this.state.user?.email || null;
  }

  /**
   * Check if user is verified
   */
  isUserVerified(): boolean {
    return this.state.user?.verified || false;
  }

  /**
   * Get state summary for debugging
   */
  getStateSummary(): object {
    return {
      hasUser: !!this.state.user,
      isAuthenticated: this.state.isAuthenticated,
      isLoading: this.state.isLoading,
      hasError: !!this.state.error,
      errorCode: this.state.error?.code,
      lastOperation: this.state.lastOperation,
      provider: this.state.user?.provider,
      sessionExpired: this.isSessionExpired(),
      sessionTimeRemaining: this.getSessionTimeRemaining()
    };
  }

  // Private methods

  private getInitialState(): AuthState {
    return {
      user: null,
      isLoading: true, // Start with loading true
      isAuthenticated: false,
      error: null,
      lastOperation: null,
      sessionInfo: null
    };
  }

  private hasStateChanged(previous: AuthState, current: AuthState): boolean {
    // Check key fields that matter for notifications
    return (
      previous.user?.id !== current.user?.id ||
      previous.isLoading !== current.isLoading ||
      previous.isAuthenticated !== current.isAuthenticated ||
      previous.error?.code !== current.error?.code ||
      previous.lastOperation !== current.lastOperation
    );
  }

  private notifyListeners(): void {
    const currentState = this.getState();
    this.listeners.forEach(listener => {
      try {
        listener(currentState);
      } catch (error) {
        console.error('AuthStateManager: Error in state listener:', error);
      }
    });
  }

  private emitStateChangeEvent(previousState: AuthState): void {
    let event: AuthStateChangeEvent;

    // Determine event type based on state changes
    if (!previousState.user && this.state.user) {
      event = {
        type: 'user-signed-in',
        user: this.state.user,
        timestamp: new Date(),
        operation: this.state.lastOperation || 'sign-in'
      };
    } else if (previousState.user && !this.state.user) {
      event = {
        type: 'user-signed-out',
        user: null,
        timestamp: new Date(),
        operation: this.state.lastOperation || 'sign-out'
      };
    } else if (previousState.user && this.state.user && 
               previousState.sessionInfo?.refreshedAt !== this.state.sessionInfo?.refreshedAt) {
      event = {
        type: 'session-refreshed',
        user: this.state.user,
        timestamp: new Date(),
        operation: 'refresh-session'
      };
    } else if (!previousState.error && this.state.error) {
      event = {
        type: 'error-occurred',
        error: this.state.error,
        timestamp: new Date(),
        operation: this.state.lastOperation || 'sign-in'
      };
    } else {
      return; // No significant event to emit
    }

    // Notify event listeners
    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('AuthStateManager: Error in event listener:', error);
      }
    });
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
  }

  private getPlatform(): 'web' | 'ios' | 'android' {
    if (typeof window !== 'undefined') {
      return 'web';
    }
    
    // Simple platform detection for React Native
    const { Platform } = require('react-native');
    return Platform.OS as 'ios' | 'android';
  }

  /**
   * Serialize state for persistence (optional)
   */
  serializeState(): string {
    const persistableState = {
      user: this.state.user,
      sessionInfo: this.state.sessionInfo,
      lastOperation: this.state.lastOperation
    };
    
    return JSON.stringify(persistableState);
  }

  /**
   * Deserialize state from persistence (optional)
   */
  deserializeState(serializedState: string): void {
    try {
      const parsed = JSON.parse(serializedState);
      
      // Validate session is not expired
      if (parsed.sessionInfo) {
        const expiresAt = new Date(parsed.sessionInfo.expiresAt);
        if (new Date() > expiresAt) {
          console.log('AuthStateManager: Stored session expired, clearing state');
          return;
        }
      }
      
      this.setState({
        user: parsed.user,
        sessionInfo: parsed.sessionInfo ? {
          ...parsed.sessionInfo,
          expiresAt: new Date(parsed.sessionInfo.expiresAt),
          refreshedAt: new Date(parsed.sessionInfo.refreshedAt)
        } : null,
        lastOperation: parsed.lastOperation,
        isLoading: false
      });
      
    } catch (error) {
      console.warn('AuthStateManager: Failed to deserialize state:', error);
    }
  }

  /**
   * Dispose of all listeners (cleanup)
   */
  dispose(): void {
    this.listeners.clear();
    this.eventListeners.clear();
  }
}