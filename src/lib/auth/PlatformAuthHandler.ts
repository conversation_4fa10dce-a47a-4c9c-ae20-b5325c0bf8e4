/**
 * PlatformAuthHandler
 * Platform-specific authentication logic (Web vs Mobile OAuth)
 */

import { Platform } from 'react-native';
import { 
  getAuth,
  GoogleAuthProvider,
  OAuthProvider,
  signInWithPopup,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  updateProfile,
  User as FirebaseUser
} from 'firebase/auth';
import { 
  doc, 
  setDoc, 
  getDoc,
  Timestamp,
  getFirestore
} from 'firebase/firestore';

import { 
  AuthResult, 
  PlatformAuthHandlerInterface, 
  PlatformCapabilities,
  User,
  UserProfile
} from '@/types/auth';
import { AUTH_CONFIG, GOOGLE_CONFIG, APPLE_CONFIG, TEST_ACCOUNTS, DEV_CONFIG } from '@/config/auth';
import { AuthErrorHelper } from './AuthErrorHelper';

export class PlatformAuthHandler implements PlatformAuthHandlerInterface {
  private auth = getAuth();
  private db = getFirestore();
  private errorHelper = new AuthErrorHelper();

  /**
   * Platform-specific Google OAuth sign-in
   */
  async signInWithGoogle(): Promise<AuthResult> {
    const startTime = Date.now();

    try {
      if (Platform.OS === 'web') {
        return await this.signInWithGoogleWeb();
      } else {
        // For mobile, use real OAuth or test account based on development setting
        if (DEV_CONFIG.enableTestAccounts) {
          return await this.signInWithGoogleTestAccount();
        } else {
          return await this.signInWithGoogleMobile();
        }
      }
    } catch (error) {
      console.error('PlatformAuthHandler: Google sign-in error:', error);
      return {
        success: false,
        error: this.errorHelper.createFromFirebaseError(error, 'google-oauth'),
        metadata: {
          duration: Date.now() - startTime,
          provider: 'google.com',
          operation: 'google-oauth'
        }
      };
    }
  }

  /**
   * Platform-specific Apple OAuth sign-in
   */
  async signInWithApple(): Promise<AuthResult> {
    const startTime = Date.now();

    try {
      if (Platform.OS === 'web') {
        return await this.signInWithAppleWeb();
      } else if (Platform.OS === 'ios') {
        // For iOS, use real Apple Sign-In or test account
        if (DEV_CONFIG.enableTestAccounts) {
          return await this.signInWithAppleTestAccount();
        } else {
          return await this.signInWithAppleIOS();
        }
      } else {
        // Android doesn't support Apple Sign-In
        return {
          success: false,
          error: this.errorHelper.createAuthError({
            code: 'platform/not-supported',
            message: 'Apple Sign-In is not supported on Android',
            context: {
              operation: 'apple-oauth',
              platform: 'android',
              timestamp: new Date()
            }
          })
        };
      }
    } catch (error) {
      console.error('PlatformAuthHandler: Apple sign-in error:', error);
      return {
        success: false,
        error: this.errorHelper.createFromFirebaseError(error, 'apple-oauth'),
        metadata: {
          duration: Date.now() - startTime,
          provider: 'apple.com',
          operation: 'apple-oauth'
        }
      };
    }
  }

  /**
   * Get platform capabilities
   */
  getCapabilities(): PlatformCapabilities {
    return {
      supportsGoogleOAuth: true, // Supported on all platforms
      supportsAppleOAuth: Platform.OS !== 'android', // Not supported on Android
      supportsWebOAuth: Platform.OS === 'web',
      supportsBiometric: Platform.OS !== 'web',
      supportsKeychain: Platform.OS !== 'web'
    };
  }

  /**
   * Platform-specific cleanup
   */
  async cleanup(): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        await this.cleanupWeb();
      } else {
        await this.cleanupMobile();
      }
    } catch (error) {
      console.warn('PlatformAuthHandler: Cleanup error:', error);
    }
  }

  // Private methods for Web OAuth

  private async signInWithGoogleWeb(): Promise<AuthResult> {
    console.log('PlatformAuthHandler: Web Google OAuth');
    
    // Clear any existing session first
    await this.cleanup();
    
    const provider = new GoogleAuthProvider();
    provider.addScope('profile');
    provider.addScope('email');
    
    const result = await signInWithPopup(this.auth, provider);
    console.log('PlatformAuthHandler: Google OAuth successful:', result.user.uid);
    
    // Handle social auth success
    await this.handleSocialAuthSuccess(result.user);
    
    const user = await this.createUserFromFirebaseUser(result.user);
    
    return {
      success: true,
      user,
      metadata: {
        duration: 0, // Will be set by caller
        provider: 'google.com',
        operation: 'google-oauth'
      }
    };
  }

  private async signInWithAppleWeb(): Promise<AuthResult> {
    console.log('PlatformAuthHandler: Web Apple OAuth');
    
    // In development, use test accounts to avoid emulator popup issues
    if (DEV_CONFIG.enableTestAccounts) {
      console.log('PlatformAuthHandler: Using Apple test account for web development');
      return await this.signInWithAppleTestAccount();
    }
    
    // Clear any existing session first
    await this.cleanup();
    
    const provider = new OAuthProvider('apple.com');
    provider.addScope('email');
    provider.addScope('name');
    
    const result = await signInWithPopup(this.auth, provider);
    console.log('PlatformAuthHandler: Apple OAuth successful:', result.user.uid);
    
    // Handle social auth success
    await this.handleSocialAuthSuccess(result.user);
    
    const user = await this.createUserFromFirebaseUser(result.user);
    
    return {
      success: true,
      user,
      metadata: {
        duration: 0, // Will be set by caller
        provider: 'apple.com',
        operation: 'apple-oauth'
      }
    };
  }

  // Private methods for Mobile OAuth

  private async signInWithGoogleMobile(): Promise<AuthResult> {
    console.log('PlatformAuthHandler: Mobile Google OAuth');
    
    // TODO: Implement real Google OAuth for mobile
    // For now, return configuration error to indicate it needs implementation
    return {
      success: false,
      error: this.errorHelper.createAuthError({
        code: 'oauth/configuration-error',
        message: 'Real Google OAuth for mobile not yet implemented. Enable test accounts in development.',
        context: {
          operation: 'google-oauth',
          platform: Platform.OS as 'ios' | 'android',
          timestamp: new Date()
        }
      })
    };
    
    /* Real implementation would look like:
    import * as Google from 'expo-auth-session/providers/google';
    
    const [request, response, promptAsync] = Google.useIdTokenAuthRequest({
      clientId: GOOGLE_CONFIG.iosClientId || GOOGLE_CONFIG.androidClientId,
    });

    const result = await promptAsync();
    if (result.type === 'success') {
      const { id_token } = result.params;
      const credential = GoogleAuthProvider.credential(id_token);
      const userCredential = await signInWithCredential(this.auth, credential);
      // ... handle success
    }
    */
  }

  private async signInWithAppleIOS(): Promise<AuthResult> {
    console.log('PlatformAuthHandler: iOS Apple Sign-In');
    
    // TODO: Implement real Apple Sign-In for iOS
    // For now, return configuration error to indicate it needs implementation
    return {
      success: false,
      error: this.errorHelper.createAuthError({
        code: 'oauth/configuration-error',
        message: 'Real Apple Sign-In for iOS not yet implemented. Enable test accounts in development.',
        context: {
          operation: 'apple-oauth',
          platform: 'ios',
          timestamp: new Date()
        }
      })
    };
    
    /* Real implementation would look like:
    import * as AppleAuthentication from 'expo-apple-authentication';
    
    const credential = await AppleAuthentication.signInAsync({
      requestedScopes: [
        AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
        AppleAuthentication.AppleAuthenticationScope.EMAIL,
      ],
    });

    const provider = new OAuthProvider('apple.com');
    const authCredential = provider.credential({
      idToken: credential.identityToken,
      rawNonce: credential.nonce,
    });
    
    const userCredential = await signInWithCredential(this.auth, authCredential);
    // ... handle success
    */
  }

  // Private methods for Test Accounts (Development)

  private async signInWithGoogleTestAccount(): Promise<AuthResult> {
    console.log('PlatformAuthHandler: Using Google test account for development');
    
    const testAccount = TEST_ACCOUNTS.google;
    
    try {
      // Try to sign in with existing test account
      const userCredential = await signInWithEmailAndPassword(
        this.auth, 
        testAccount.email, 
        testAccount.password
      );
      
      // Ensure profile is complete
      if (!userCredential.user.displayName) {
        await updateProfile(userCredential.user, {
          displayName: testAccount.displayName,
          photoURL: testAccount.photoURL
        });
      }
      
      await this.handleSocialAuthSuccess(userCredential.user);
      const user = await this.createUserFromFirebaseUser(userCredential.user);
      
      return {
        success: true,
        user,
        metadata: {
          duration: 0,
          provider: 'google.com',
          operation: 'google-oauth',
          fallbackUsed: true
        }
      };
      
    } catch (signInError) {
      console.log('PlatformAuthHandler: Creating Google test account');
      
      // Create test account if it doesn't exist
      const userCredential = await createUserWithEmailAndPassword(
        this.auth,
        testAccount.email,
        testAccount.password
      );
      
      // Set up Google-like profile
      await updateProfile(userCredential.user, {
        displayName: testAccount.displayName,
        photoURL: testAccount.photoURL
      });
      
      // Create Firestore profile
      await this.createSocialAuthProfile(userCredential.user, 'google.com');
      
      const user = await this.createUserFromFirebaseUser(userCredential.user);
      
      return {
        success: true,
        user,
        metadata: {
          duration: 0,
          provider: 'google.com',
          operation: 'google-oauth',
          fallbackUsed: true
        }
      };
    }
  }

  private async signInWithAppleTestAccount(): Promise<AuthResult> {
    console.log('PlatformAuthHandler: Using Apple test account for development');
    
    const testAccount = TEST_ACCOUNTS.apple;
    
    try {
      // Try to sign in with existing test account
      const userCredential = await signInWithEmailAndPassword(
        this.auth,
        testAccount.email,
        testAccount.password
      );
      
      // Ensure profile is complete
      if (!userCredential.user.displayName) {
        await updateProfile(userCredential.user, {
          displayName: testAccount.displayName,
          photoURL: testAccount.photoURL
        });
      }
      
      await this.handleSocialAuthSuccess(userCredential.user);
      const user = await this.createUserFromFirebaseUser(userCredential.user);
      
      return {
        success: true,
        user,
        metadata: {
          duration: 0,
          provider: 'apple.com',
          operation: 'apple-oauth',
          fallbackUsed: true
        }
      };
      
    } catch (signInError) {
      console.log('PlatformAuthHandler: Creating Apple test account');
      
      // Create test account if it doesn't exist
      const userCredential = await createUserWithEmailAndPassword(
        this.auth,
        testAccount.email,
        testAccount.password
      );
      
      // Set up Apple-like profile
      await updateProfile(userCredential.user, {
        displayName: testAccount.displayName,
        photoURL: testAccount.photoURL
      });
      
      // Create Firestore profile
      await this.createSocialAuthProfile(userCredential.user, 'apple.com');
      
      const user = await this.createUserFromFirebaseUser(userCredential.user);
      
      return {
        success: true,
        user,
        metadata: {
          duration: 0,
          provider: 'apple.com',
          operation: 'apple-oauth',
          fallbackUsed: true
        }
      };
    }
  }

  // Helper methods

  private async handleSocialAuthSuccess(firebaseUser: FirebaseUser): Promise<void> {
    // Add delay for Firebase permissions to propagate
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    try {
      const userDoc = await getDoc(doc(this.db, 'profiles', firebaseUser.uid));
      
      if (!userDoc.exists()) {
        await this.createSocialAuthProfile(firebaseUser);
      } else {
        // Update existing profile
        await setDoc(doc(this.db, 'profiles', firebaseUser.uid), {
          updated_at: Timestamp.now(),
          last_login: Timestamp.now()
        }, { merge: true });
      }
    } catch (error) {
      console.error('PlatformAuthHandler: Error handling social auth profile:', error);
    }
  }

  private async createSocialAuthProfile(firebaseUser: FirebaseUser, providerOverride?: string): Promise<void> {
    const provider = providerOverride || firebaseUser.providerData[0]?.providerId || 'unknown';
    
    const profileData: Partial<UserProfile> = {
      id: firebaseUser.uid,
      email: firebaseUser.email || '',
      full_name: firebaseUser.displayName || '',
      created_at: Timestamp.now(),
      updated_at: Timestamp.now(),
      auth_provider: provider as any,
      daily_calorie_goal: 2000,
      daily_water_goal: 2000,
      daily_protein_goal: 150,
      daily_carbs_goal: 200,
      daily_fat_goal: 70,
      height: provider === 'apple.com' ? "5'11\"" : "5'10\"",
      weight: provider === 'apple.com' ? 165 : 170,
      activity_level: 'moderate'
    };

    await setDoc(doc(this.db, 'profiles', firebaseUser.uid), profileData);
  }

  private async createUserFromFirebaseUser(firebaseUser: FirebaseUser): Promise<User> {
    return {
      id: firebaseUser.uid,
      email: firebaseUser.email || '',
      name: firebaseUser.displayName || '',
      photoURL: firebaseUser.photoURL || undefined,
      provider: this.getProviderFromFirebaseUser(firebaseUser),
      verified: firebaseUser.emailVerified,
      createdAt: firebaseUser.metadata.creationTime ? new Date(firebaseUser.metadata.creationTime) : undefined,
      lastLoginAt: firebaseUser.metadata.lastSignInTime ? new Date(firebaseUser.metadata.lastSignInTime) : undefined
    };
  }

  private getProviderFromFirebaseUser(firebaseUser: FirebaseUser): any {
    const providerId = firebaseUser.providerData[0]?.providerId;
    
    switch (providerId) {
      case 'google.com':
        return 'google.com';
      case 'apple.com':
        return 'apple.com';
      default:
        return 'email';
    }
  }

  private async cleanupWeb(): Promise<void> {
    try {
      // Import clearAuthState on-demand to avoid circular dependency
      const { clearAuthState } = await import('@/lib/firebase');
      await clearAuthState();
      
      // Clear router state
      sessionStorage.removeItem('expo-router-state');
      sessionStorage.removeItem('expo-router-origin-state');
      sessionStorage.removeItem('intentional_login');
    } catch (error) {
      console.warn('PlatformAuthHandler: Web cleanup error:', error);
    }
  }

  private async cleanupMobile(): Promise<void> {
    // Mobile cleanup is simpler - just Firebase signout
    // Any OAuth-specific cleanup would go here
  }
}