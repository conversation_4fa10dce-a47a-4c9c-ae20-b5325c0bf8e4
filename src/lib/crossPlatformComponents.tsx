import React from 'react';
import { Platform, PressableProps, Pressable, StyleProp, ViewStyle, TextStyle, Text } from 'react-native';

interface TouchableProps extends PressableProps {
  style?: StyleProp<ViewStyle>;
  activeOpacity?: number;
  textStyle?: StyleProp<TextStyle>;
  children: React.ReactNode;
}

/**
 * A cross-platform touchable component that uses Pressable internally.
 * This component can be used as a drop-in replacement for TouchableOpacity
 * and will handle platform-specific differences between web and native.
 */
export function CrossPlatformTouchable({
  style,
  activeOpacity = 0.7,
  onPress,
  children,
  ...rest
}: TouchableProps) {
  return (
    <Pressable
      onPress={onPress}
      style={({ pressed }) => [
        style,
        Platform.OS === 'web' 
          ? { cursor: 'pointer' } 
          : pressed ? { opacity: activeOpacity } : {}
      ]}
      {...rest}
    >
      {children}
    </Pressable>
  );
}

/**
 * A cross-platform button component that uses Pressable internally.
 */
export function CrossPlatformButton({
  style,
  textStyle,
  activeOpacity = 0.7,
  onPress,
  children,
  ...rest
}: TouchableProps) {
  return (
    <Pressable
      onPress={onPress}
      style={({ pressed }) => [
        {
          padding: 12,
          borderRadius: 8,
          backgroundColor: '#007AFF',
          alignItems: 'center',
          justifyContent: 'center',
        },
        style,
        Platform.OS === 'web' 
          ? { cursor: 'pointer' } 
          : pressed ? { opacity: activeOpacity } : {}
      ]}
      {...rest}
    >
      {typeof children === 'string' ? (
        <Text style={[{ color: 'white', fontWeight: '600' }, textStyle]}>
          {children}
        </Text>
      ) : (
        children
      )}
    </Pressable>
  );
} 