import { getFirestore, collection, query, where, getDocs, doc, setDoc } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { initializeApp } from 'firebase/app';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

/**
 * Checks if a collection exists in Firestore
 */
async function checkCollectionExists(collectionName: string): Promise<boolean> {
  try {
    const snapshot = await getDocs(collection(db, collectionName));
    return !snapshot.empty;
  } catch (err) {
    console.error(`Error checking if collection ${collectionName} exists:`, err);
    return false;
  }
}

/**
 * Creates the profiles collection structure if needed
 */
async function createProfilesCollection(): Promise<void> {
  try {
    console.log('Ensuring profiles collection is ready...');
    
    // In Firestore, collections are created automatically when documents are added
    // We don't need to explicitly create the collection structure
    
    console.log('Profiles collection ready');
  } catch (err) {
    console.error('Error setting up profiles collection:', err);
  }
}

/**
 * Creates water intake collections if needed
 */
async function createWaterIntakeCollections(): Promise<void> {
  try {
    console.log('Ensuring water intake collections are ready...');
    
    // Check if current user needs default water settings
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (user) {
      // Check if the user already has water intake goals
      const goalsRef = collection(db, 'water_intake_goals');
      const q = query(goalsRef, where('userId', '==', user.uid));
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        // Create default water intake goals for the user
        await setDoc(doc(db, 'water_intake_goals', user.uid), {
          userId: user.uid,
          dailyGoal: 2000,
          standardGlassSize: 250,
          standardBottleSize: 500,
          remindersEnabled: false,
          updatedAt: new Date()
        });
        
        console.log('Default water intake goals created for the user');
      }
    }
    
    console.log('Water intake collections ready');
  } catch (err) {
    console.error('Error setting up water intake collections:', err);
  }
}

/**
 * Initialize all database collections
 */
export async function initializeDatabase(): Promise<void> {
  try {
    console.log('Initializing database...');
    
    // Check user authentication
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      console.log('No authenticated user, skipping database initialization');
      return;
    }
    
    // Initialize collections
    await createProfilesCollection();
    await createWaterIntakeCollections();
    
    console.log('Database initialization complete');
  } catch (err) {
    console.error('Error initializing database:', err);
  }
} 