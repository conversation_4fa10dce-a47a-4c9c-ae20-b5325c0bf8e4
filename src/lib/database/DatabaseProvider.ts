/**
 * Database Provider Interface
 * 
 * This interface abstracts away the specifics of the database implementation,
 * allowing us to switch between Supabase and Firebase without changing the application code.
 */

export interface DatabaseProvider {
  // Authentication methods
  signIn(email: string, password: string): Promise<any>;
  signUp(email: string, password: string, userData: any): Promise<any>;
  signOut(): Promise<void>;
  getCurrentUser(): Promise<any | null>;
  onAuthStateChanged(callback: (user: any | null) => void): () => void;
  sendPasswordResetEmail(email: string): Promise<any>;
  
  // Data methods
  getDocument(collection: string, id: string): Promise<any>;
  queryDocuments(collection: string, filters: any[]): Promise<any[]>;
  createDocument(collection: string, data: any): Promise<string>;
  updateDocument(collection: string, id: string, data: any): Promise<void>;
  deleteDocument(collection: string, id: string): Promise<void>;
  
  // Storage methods
  uploadFile(path: string, file: any, metadata?: any): Promise<string>;
  getFileUrl(path: string): Promise<string>;
  deleteFile(path: string): Promise<void>;
  
  // Realtime methods
  subscribeToDocument(collection: string, id: string, callback: (data: any) => void): () => void;
  subscribeToQuery(collection: string, filters: any[], callback: (data: any[]) => void): () => void;
} 