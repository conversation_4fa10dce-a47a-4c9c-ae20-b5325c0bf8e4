import { DatabaseProvider } from './DatabaseProvider';
import { 
  getFirestore, collection as firestoreCollection, doc, getDoc, getDocs,
  setDoc, updateDoc, deleteDoc, query, where, onSnapshot,
  DocumentData, WhereFilterOp, Unsubscribe, serverTimestamp,
  addDoc, orderBy, limit
} from 'firebase/firestore';
import { 
  getAuth, signInWithEmailAndPassword, createUserWithEmailAndPassword,
  signOut, onAuthStateChanged, sendPasswordResetEmail, User,
  GoogleAuthProvider, signInWithPopup
} from 'firebase/auth';
import {
  getStorage, ref, uploadBytes, getDownloadURL, deleteObject
} from 'firebase/storage';

// Use centralized Firebase instances
import { auth, db, storage } from '@/lib/firebase';

/**
 * Unified Firebase Database Provider
 * Combines the best features from both existing providers
 * with standardized implementation
 */
export class UnifiedFirebaseProvider implements DatabaseProvider {
  constructor() {
    console.log('Initializing Unified Firebase Database Provider');
  }

  // Authentication methods
  async signIn(email: string, password: string) {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      return { success: true, user: userCredential.user };
    } catch (error: any) {
      console.error('Error signing in:', error);
      return { success: false, error: error.message };
    }
  }

  async signUp(email: string, password: string, additionalData: any = {}) {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      
      // Create user profile document
      await this.createProfile(user.uid, {
        email: user.email,
        ...additionalData,
        created_at: serverTimestamp(),
        updated_at: serverTimestamp()
      });
      
      return { success: true, user };
    } catch (error: any) {
      console.error('Error signing up:', error);
      return { success: false, error: error.message };
    }
  }

  async signOut() {
    try {
      await signOut(auth);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  }

  async getCurrentUser() {
    return auth.currentUser;
  }

  onAuthStateChanged(callback: (user: User | null) => void) {
    return onAuthStateChanged(auth, callback);
  }

  async sendPasswordResetEmail(email: string) {
    try {
      await sendPasswordResetEmail(auth, email);
      return { success: true };
    } catch (error: any) {
      console.error('Error sending password reset email:', error);
      return { success: false, error: error.message };
    }
  }

  async signInWithGoogle() {
    try {
      const provider = new GoogleAuthProvider();
      const result = await signInWithPopup(auth, provider);
      
      // Check if user profile exists, create if not
      const profileExists = await this.getDocument('users', result.user.uid);
      if (!profileExists) {
        await this.createProfile(result.user.uid, {
          email: result.user.email,
          displayName: result.user.displayName,
          photoURL: result.user.photoURL
        });
      }
      
      return { success: true, user: result.user };
    } catch (error: any) {
      console.error('Error signing in with Google:', error);
      return { success: false, error: error.message };
    }
  }

  // Profile management
  async createProfile(userId: string, profileData: any) {
    try {
      const profileDoc = doc(db, 'users', userId);
      await setDoc(profileDoc, {
        ...profileData,
        id: userId,
        created_at: serverTimestamp(),
        updated_at: serverTimestamp()
      });
      return profileDoc.id;
    } catch (error) {
      console.error('Error creating profile:', error);
      throw error;
    }
  }

  async getProfile(userId: string) {
    return this.getDocument('users', userId);
  }

  async updateProfile(userId: string, updates: any) {
    return this.updateDocument('users', userId, updates);
  }

  // Data methods
  async getDocument(collectionName: string, id: string) {
    try {
      const docRef = doc(db, collectionName, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() };
      }
      
      return null;
    } catch (error) {
      console.error(`Error getting document from ${collectionName}/${id}:`, error);
      return null;
    }
  }

  async queryDocuments(
    collectionName: string, 
    filters: {
      field: string;
      operator: WhereFilterOp;
      value: any;
    }[] = [],
    orderByField?: string,
    limitCount?: number
  ) {
    try {
      const collectionRef = firestoreCollection(db, collectionName);
      let queryConstraints: any[] = [];
      
      // Apply filters
      filters.forEach(filter => {
        queryConstraints.push(where(filter.field, filter.operator, filter.value));
      });
      
      // Apply ordering
      if (orderByField) {
        queryConstraints.push(orderBy(orderByField));
      }
      
      // Apply limit
      if (limitCount) {
        queryConstraints.push(limit(limitCount));
      }
      
      const q = query(collectionRef, ...queryConstraints);
      const querySnapshot = await getDocs(q);
      
      const results: any[] = [];
      querySnapshot.forEach((doc) => {
        results.push({ id: doc.id, ...doc.data() });
      });
      
      return results;
    } catch (error) {
      console.error(`Error querying documents from ${collectionName}:`, error);
      return [];
    }
  }

  async createDocument(collectionName: string, data: any, id?: string) {
    try {
      const docData = {
        ...data,
        created_at: serverTimestamp(),
        updated_at: serverTimestamp()
      };

      if (id) {
        // Create with specified ID
        const docRef = doc(db, collectionName, id);
        await setDoc(docRef, docData);
        return id;
      } else {
        // Create with auto-generated ID
        const docRef = await addDoc(firestoreCollection(db, collectionName), docData);
        return docRef.id;
      }
    } catch (error) {
      console.error(`Error creating document in ${collectionName}:`, error);
      throw error;
    }
  }

  async updateDocument(collectionName: string, id: string, updates: any) {
    try {
      const docRef = doc(db, collectionName, id);
      await updateDoc(docRef, {
        ...updates,
        updated_at: serverTimestamp()
      });
    } catch (error) {
      console.error(`Error updating document in ${collectionName}/${id}:`, error);
      throw error;
    }
  }

  async deleteDocument(collectionName: string, id: string) {
    try {
      const docRef = doc(db, collectionName, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error(`Error deleting document from ${collectionName}/${id}:`, error);
      throw error;
    }
  }

  // Storage methods
  async uploadFile(
    path: string, 
    file: File | Blob | string, 
    metadata?: { contentType?: string }
  ): Promise<string> {
    try {
      let fileToUpload: Blob;
      
      // Handle base64 string
      if (typeof file === 'string' && file.startsWith('data:')) {
        const base64Data = file.split(',')[1];
        const binaryString = atob(base64Data);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        fileToUpload = new Blob([bytes], { type: metadata?.contentType || 'image/jpeg' });
      } else if (file instanceof Blob || file instanceof File) {
        fileToUpload = file;
      } else {
        throw new Error('Invalid file type');
      }
      
      const storageRef = ref(storage, path);
      const snapshot = await uploadBytes(storageRef, fileToUpload, metadata);
      const downloadURL = await getDownloadURL(snapshot.ref);
      return downloadURL;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  }

  async deleteFile(path: string): Promise<void> {
    try {
      const storageRef = ref(storage, path);
      await deleteObject(storageRef);
    } catch (error: any) {
      if (error.code !== 'storage/object-not-found') {
        console.error('Error deleting file:', error);
        throw error;
      }
    }
  }

  async getFileUrl(path: string): Promise<string> {
    try {
      const storageRef = ref(storage, path);
      return await getDownloadURL(storageRef);
    } catch (error) {
      console.error('Error getting file URL:', error);
      throw error;
    }
  }

  // Realtime subscriptions
  subscribeToDocument(
    collectionName: string,
    id: string,
    callback: (data: any) => void
  ): Unsubscribe {
    const docRef = doc(db, collectionName, id);
    return onSnapshot(docRef, (snapshot) => {
      if (snapshot.exists()) {
        callback({ id: snapshot.id, ...snapshot.data() });
      } else {
        callback(null);
      }
    }, (error) => {
      console.error(`Error subscribing to ${collectionName}/${id}:`, error);
      callback(null);
    });
  }

  subscribeToQuery(
    collectionName: string,
    filters: {
      field: string;
      operator: WhereFilterOp;
      value: any;
    }[],
    callback: (data: any[]) => void,
    orderByField?: string
  ): Unsubscribe {
    const collectionRef = firestoreCollection(db, collectionName);
    let queryConstraints: any[] = [];
    
    filters.forEach(filter => {
      queryConstraints.push(where(filter.field, filter.operator, filter.value));
    });
    
    if (orderByField) {
      queryConstraints.push(orderBy(orderByField));
    }
    
    const q = query(collectionRef, ...queryConstraints);
    
    return onSnapshot(q, (snapshot) => {
      const results: any[] = [];
      snapshot.forEach((doc) => {
        results.push({ id: doc.id, ...doc.data() });
      });
      callback(results);
    }, (error) => {
      console.error(`Error subscribing to ${collectionName} query:`, error);
      callback([]);
    });
  }

  // Utility method for batch operations
  async batchWrite(operations: {
    type: 'create' | 'update' | 'delete';
    collection: string;
    id?: string;
    data?: any;
  }[]) {
    const batch = db.batch ? db.batch() : null;
    if (!batch) {
      throw new Error('Batch operations not supported');
    }

    for (const op of operations) {
      const docRef = op.id 
        ? doc(db, op.collection, op.id)
        : doc(firestoreCollection(db, op.collection));

      switch (op.type) {
        case 'create':
          batch.set(docRef, {
            ...op.data,
            created_at: serverTimestamp(),
            updated_at: serverTimestamp()
          });
          break;
        case 'update':
          batch.update(docRef, {
            ...op.data,
            updated_at: serverTimestamp()
          });
          break;
        case 'delete':
          batch.delete(docRef);
          break;
      }
    }

    await batch.commit();
  }
}