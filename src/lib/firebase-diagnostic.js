/**
 * Firebase Diagnostic Helper
 *
 * This file provides diagnostic functions to help troubleshoot Firebase initialization issues.
 * Import and call these functions to check the status of Firebase at various points in your app.
 */

import firebase from 'firebase/compat/app';
import 'firebase/compat/auth';
import 'firebase/compat/firestore';
import 'firebase/compat/storage';
import 'firebase/compat/functions';
import { Platform } from 'react-native';

// Emergency initialization if needed
if (firebase.apps.length === 0) {
  try {
    const hardcodedConfig = {
      apiKey: "AIzaSyAVDcjU_1bv3p3C0XTj0HoFq7sv1KezEs8",
      authDomain: "kingly-choosehealthy-dev.firebaseapp.com",
      projectId: "kingly-choosehealthy-dev",
      storageBucket: "kingly-choosehealthy-dev.firebasestorage.app",
      messagingSenderId: "716899221805",
      appId: "1:716899221805:web:26673127dcef3b9b35d3a4",
      measurementId: "G-2GK4YL0GJW"
    };

    firebase.initializeApp(hardcodedConfig);
    console.log('🚒 Emergency Firebase initialization in diagnostic module');
  } catch (error) {
    console.error('🚒 Emergency initialization failed:', error);
  }
}

/**
 * Check Firebase initialization status
 * @returns {Object} Status information about Firebase initialization
 */
export function checkFirebaseStatus() {
  const status = {
    isInitialized: firebase.apps.length > 0,
    appCount: firebase.apps.length,
    platform: Platform.OS,
    timestamp: new Date().toISOString(),
    envVariables: {
      apiKey: !!process.env.EXPO_PUBLIC_FIREBASE_API_KEY,
      projectId: !!process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID,
      authDomain: !!process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN,
      storageBucket: !!process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET,
    }
  };

  console.log('=== Firebase Diagnostic Check ===');
  console.log(JSON.stringify(status, null, 2));
  console.log('===============================');

  return status;
}

/**
 * Check Firebase services availability
 * @returns {Object} Status information about Firebase services
 */
export function checkFirebaseServices() {
  let auth = null;
  let firestore = null;
  let storage = null;
  let functions = null;

  try {
    auth = firebase.auth();
  } catch (e) {
    console.error('Auth service check failed:', e.message);
  }

  try {
    firestore = firebase.firestore();
  } catch (e) {
    console.error('Firestore service check failed:', e.message);
  }

  try {
    storage = firebase.storage();
  } catch (e) {
    console.error('Storage service check failed:', e.message);
  }

  try {
    functions = firebase.functions();
  } catch (e) {
    console.error('Functions service check failed:', e.message);
  }

  const status = {
    services: {
      auth: auth !== null,
      firestore: firestore !== null,
      storage: storage !== null,
      functions: functions !== null
    },
    timestamp: new Date().toISOString()
  };

  console.log('=== Firebase Services Check ===');
  console.log(JSON.stringify(status, null, 2));
  console.log('==============================');

  return status;
}

/**
 * Attempt to fix common Firebase initialization issues
 */
export function attemptFirebaseFix() {
  if (firebase.apps.length === 0) {
    try {
      const config = {
        apiKey: "AIzaSyAVDcjU_1bv3p3C0XTj0HoFq7sv1KezEs8",
        authDomain: "kingly-choosehealthy-dev.firebaseapp.com",
        projectId: "kingly-choosehealthy-dev",
        storageBucket: "kingly-choosehealthy-dev.firebasestorage.app",
        messagingSenderId: "716899221805",
        appId: "1:716899221805:web:26673127dcef3b9b35d3a4",
      };

      firebase.initializeApp(config);
      console.log('📱 Emergency Firebase initialization completed');
      return true;
    } catch (error) {
      console.error('📱 Emergency Firebase initialization failed:', error);
      return false;
    }
  } else {
    console.log('📱 Firebase already initialized, no fix needed');
    return true;
  }
}

export default {
  checkFirebaseStatus,
  checkFirebaseServices,
  attemptFirebaseFix
};