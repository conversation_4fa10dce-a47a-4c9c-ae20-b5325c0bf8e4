import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { getFunctions } from 'firebase/functions';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Firebase configuration
export const firebaseConfig = {
  apiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.EXPO_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID,
  databaseURL: process.env.EXPO_PUBLIC_FIREBASE_DATABASE_URL
};

// Initialize Firebase
let app;
let auth;
let db;
let storage;
let functions;

try {
  // Initialize Firebase
  app = initializeApp(firebaseConfig);
  console.log('Firebase app initialized');

  // Get auth instance - simplified approach
  // Note: We're not using initializeAuth with persistence to avoid the issue
  auth = getAuth(app);
  console.log('Firebase Auth initialized with basic config');

  // Manual persistence with AsyncStorage
  auth.onAuthStateChanged(async (user) => {
    if (user) {
      try {
        // Store minimal user data
        const userData = {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName
        };
        await AsyncStorage.setItem('firebase_auth_user', JSON.stringify(userData));
        console.log('User data stored in AsyncStorage');
      } catch (error) {
        console.error('Error storing user data:', error);
      }
    } else {
      try {
        // Clear user data on sign out
        await AsyncStorage.removeItem('firebase_auth_user');
        console.log('User data cleared from AsyncStorage');
      } catch (error) {
        console.error('Error clearing user data:', error);
      }
    }
  });

  // Initialize Firestore
  db = getFirestore(app);
  console.log('Firebase Firestore initialized');

  // Initialize Storage
  storage = getStorage(app);
  console.log('Firebase Storage initialized');

  // Initialize Functions
  functions = getFunctions(app);
  console.log('Firebase Functions initialized');
} catch (error) {
  console.error('Error initializing Firebase:', error);
}

// Check if Firebase is properly configured
export const isFirebaseConfigured = !!firebaseConfig.apiKey && !!firebaseConfig.projectId;

// Check if Firebase is properly initialized
export const isInitialized = !!app && !!auth && !!db && !!storage && !!functions;

// Export initialized services
export { 
  app, 
  auth, 
  db, 
  storage, 
  functions
};

// Helper function to restore auth state from AsyncStorage
export async function restoreAuthState() {
  try {
    const userData = await AsyncStorage.getItem('firebase_auth_user');
    if (userData) {
      return JSON.parse(userData);
    }
  } catch (error) {
    console.error('Error restoring auth state:', error);
  }
  return null;
} 