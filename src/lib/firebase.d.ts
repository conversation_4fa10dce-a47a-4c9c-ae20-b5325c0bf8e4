import { FirebaseApp } from 'firebase/app';
import { Auth } from 'firebase/auth';
import { Firestore } from 'firebase/firestore';
import { FirebaseStorage } from 'firebase/storage';
import { Functions } from 'firebase/functions';

export const app: FirebaseApp;
export const auth: Auth;
export const db: Firestore;
export const firestore: Firestore;
export const storage: FirebaseStorage;
export const functions: Functions;
export const firebaseConfig: Record<string, string | undefined>;
export function logFirebaseConfigStatus(): boolean;
export const firestoreSettings: {
  cacheSizeBytes: number;
  experimentalForceLongPolling: boolean;
  ignoreUndefinedProperties: boolean;
};
export function clearAuthState(): Promise<{ success: boolean; error: any }>;
export function clearFirebaseIndexedDB(): Promise<{ success: boolean; error: any }>;
export function isFirebaseConfigured(): boolean; 