import { initializeApp, getApps, getApp } from 'firebase/app';
import {
  getFirestore,
  enableIndexedDbPersistence,
  CACHE_SIZE_UNLIMITED,
  initializeFirestore,
  connectFirestoreEmulator,
} from 'firebase/firestore';
import {
  getAuth,
  setPersistence,
  inMemoryPersistence,
  signOut,
  connectAuthEmulator,
} from 'firebase/auth';
import { getStorage, connectStorageEmulator } from 'firebase/storage';
import { getFunctions, connectFunctionsEmulator } from 'firebase/functions';
import { getAnalytics, isSupported, Analytics } from 'firebase/analytics';
import { Platform } from 'react-native';
import Constants from 'expo-constants';
import { firebaseConfig, firestoreSettings } from '@/firebase.config';

// Helper to clear Firebase-related data from IndexedDB
async function clearFirebaseIndexedDB() {
  if (Platform.OS !== 'web' || typeof window === 'undefined') return;

  console.log('Firebase: Clearing browser storage data');

  try {
    // Clear all localStorage and sessionStorage
    try {
      const keysToRemove = [
        'firebase:authUser',
        'firebase:host',
        'firebase:persistence',
        'firebase:session',
        'firebase:pendingRedirect',
        'firebase:redirectEvent',
        'firebase:auth:',
        'firebase:auth.timer',
        'firebase:auth.session',
      ];

      // Clear specific keys
      keysToRemove.forEach((key) => {
        try {
          localStorage.removeItem(key);
          sessionStorage.removeItem(key);
        } catch (e) {
          console.warn(`Firebase: Failed to remove ${key} from storage:`, e);
        }
      });

      // Also try full storage clear for good measure
      localStorage.clear();
      sessionStorage.clear();
    } catch (e) {
      console.warn('Firebase: Error clearing storage:', e);
    }

    // Clear all cookies related to Firebase
    try {
      document.cookie.split(';').forEach((cookie) => {
        const [name] = cookie.trim().split('=');
        if (name && (name.includes('firebase') || name.includes('__session'))) {
          document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/;`;
        }
      });
    } catch (e) {
      console.warn('Firebase: Error clearing cookies:', e);
    }

    // Clear IndexedDB databases
    if ('indexedDB' in window) {
      // Specific Firebase database names to clear - but NOT the main 'firestore' database
      // to avoid deleting it while it's in use
      const idbDatabases = [
        'firebaseLocalStorageDb',
        'firebaseLocalStorage',
        'firebase-installations-database',
        'firebase-messaging-database',
        'firebase-auth-state',
        'firebase-heartbeat-database',
      ];

      for (const dbName of idbDatabases) {
        try {
          window.indexedDB.deleteDatabase(dbName);
          console.log(`Firebase: Cleared IndexedDB database: ${dbName}`);
        } catch (e) {
          console.warn(
            `Firebase: Failed to clear IndexedDB database ${dbName}:`,
            e
          );
        }
      }
    }

    // Clear CacheStorage if available (for service workers)
    if ('caches' in window) {
      try {
        const cacheNames = await window.caches.keys();
        for (const cacheName of cacheNames) {
          if (cacheName.includes('firebase')) {
            await window.caches.delete(cacheName);
            console.log(`Firebase: Cleared cache: ${cacheName}`);
          }
        }
      } catch (e) {
        console.warn('Firebase: Error clearing caches:', e);
      }
    }
  } catch (e) {
    console.error('Firebase: Error clearing browser data:', e);
  }
}

// Initialize or get Firebase app without deleting existing instances
function initializeFirebase() {
  let app;

  try {
    // Check if Firebase is already initialized
    if (!getApps().length) {
      console.log('Firebase: Initializing for the first time');
      app = initializeApp(firebaseConfig);
    } else {
      console.log(
        'Firebase: Already initialized, retrieving existing instance'
      );
      app = getApp();
    }

    // For web platform, always set persistence to inMemory to prevent auto sign-in issues
    if (Platform.OS === 'web') {
      const auth = getAuth(app);
      setPersistence(auth, inMemoryPersistence)
        .then(() =>
          console.log('Firebase: Set auth persistence to inMemoryPersistence')
        )
        .catch((error) =>
          console.error('Firebase: Error setting persistence:', error)
        );

      // Clear storage without deleting the app
      clearFirebaseIndexedDB();
    }

    return app;
  } catch (error) {
    console.error('Firebase: Error initializing:', error);

    // Fall back to existing app if available
    try {
      return getApp();
    } catch (getAppError) {
      console.error(
        'Firebase: Critical error, could not initialize or retrieve app:',
        getAppError
      );
      throw new Error('Firebase initialization failed completely');
    }
  }
}

// Initialize Firebase once
const app = initializeFirebase();

// Initialize Firestore with settings
const db = initializeFirestore(app, firestoreSettings);

// Enable offline persistence for mobile platforms
if (Platform.OS !== 'web') {
  enableIndexedDbPersistence(db)
    .then(() => {
      console.log('Firebase: Firestore offline persistence enabled');
    })
    .catch((err) => {
      if (err.code === 'failed-precondition') {
        // Multiple tabs open, persistence can only be enabled in one tab at a time
        console.warn(
          'Firebase: Firestore persistence failed - multiple tabs open'
        );
      } else if (err.code === 'unimplemented') {
        // The current browser/environment doesn't support persistence
        console.warn(
          'Firebase: Firestore persistence not supported in this environment'
        );
      } else {
        console.error('Firebase: Error enabling Firestore persistence:', err);
      }
    });
}

// Initialize Authentication
const auth = getAuth(app);

// Initialize Storage
const storage = getStorage(app);

// Initialize Cloud Functions
const functions = getFunctions(app);

// Debug environment detection
console.log('Firebase: Environment debug info:', {
  NODE_ENV: process.env.NODE_ENV,
  EXPO_PUBLIC_USE_EMULATORS: process.env.EXPO_PUBLIC_USE_EMULATORS,
  EXPO_PUBLIC_ENVIRONMENT: process.env.EXPO_PUBLIC_ENVIRONMENT,
  expoEnvironment: Constants.expoConfig?.extra?.environment,
  FIREBASE_EMULATOR_AUTH: process.env.FIREBASE_EMULATOR_AUTH,
  FIREBASE_EMULATOR_FIRESTORE: process.env.FIREBASE_EMULATOR_FIRESTORE,
});

// Connect to emulators based on environment variables OR explicit emulator flags
const shouldUseEmulators = 
  process.env.NODE_ENV === 'development' ||
  process.env.EXPO_PUBLIC_USE_EMULATORS === 'true' ||
  process.env.EXPO_PUBLIC_ENVIRONMENT === 'development' ||
  Constants.expoConfig?.extra?.environment === 'development' ||
  process.env.FIREBASE_EMULATOR_FIRESTORE === 'true'; // Force emulators if explicitly set

if (shouldUseEmulators) {
  console.log(
    'Firebase: Development environment detected, checking emulator configuration'
  );

  // Check individual emulator environment variables (fallback to EXPO_PUBLIC_USE_EMULATORS if specific flags not set)
  const useAllEmulators = process.env.EXPO_PUBLIC_USE_EMULATORS === 'true';
  const useAuthEmulator = process.env.FIREBASE_EMULATOR_AUTH === 'true' || useAllEmulators;
  const useFirestoreEmulator = process.env.FIREBASE_EMULATOR_FIRESTORE === 'true' || useAllEmulators;
  const useStorageEmulator = process.env.FIREBASE_EMULATOR_STORAGE === 'true' || useAllEmulators;
  const useFunctionsEmulator = process.env.FIREBASE_EMULATOR_FUNCTIONS === 'true' || useAllEmulators;

  // Connect Auth emulator if enabled
  if (useAuthEmulator) {
    try {
      connectAuthEmulator(auth, 'http://localhost:9099', {
        disableWarnings: true,
      });
      console.log('Firebase: Connected to Auth emulator');
    } catch (e) {
      console.log(
        'Firebase: Auth emulator already connected or error:',
        e.message
      );
    }
  } else {
    console.log(
      'Firebase: Using production Auth (FIREBASE_EMULATOR_AUTH=false)'
    );
  }

  // Connect Firestore emulator if enabled
  if (useFirestoreEmulator) {
    try {
      connectFirestoreEmulator(db, 'localhost', 8080);
      console.log('Firebase: Connected to Firestore emulator');
    } catch (e) {
      console.log(
        'Firebase: Firestore emulator already connected or error:',
        e.message
      );
    }
  } else {
    console.log(
      'Firebase: Using production Firestore (FIREBASE_EMULATOR_FIRESTORE=false)'
    );
  }

  // Connect Storage emulator if enabled
  if (useStorageEmulator) {
    try {
      connectStorageEmulator(storage, 'localhost', 9199);
      console.log('Firebase: Connected to Storage emulator');
    } catch (e) {
      console.log(
        'Firebase: Storage emulator already connected or error:',
        e.message
      );
    }
  } else {
    console.log(
      'Firebase: Using production Storage (FIREBASE_EMULATOR_STORAGE=false)'
    );
  }

  // Connect Functions emulator if enabled
  if (useFunctionsEmulator) {
    try {
      connectFunctionsEmulator(functions, 'localhost', 5001);
      console.log('Firebase: Connected to Functions emulator');
    } catch (e) {
      console.log(
        'Firebase: Functions emulator already connected or error:',
        e.message
      );
    }
  } else {
    console.log(
      'Firebase: Using production Functions (FIREBASE_EMULATOR_FUNCTIONS=false)'
    );
  }

  // Log emulator configuration summary
  console.log('Firebase: Emulator configuration:', {
    auth: useAuthEmulator,
    firestore: useFirestoreEmulator,
    storage: useStorageEmulator,
    functions: useFunctionsEmulator,
  });
}

// Initialize Analytics (only on web platform and not in emulator mode)
let analytics: Analytics | null = null;
if (Platform.OS === 'web' && !shouldUseEmulators) {
  // Analytics is not supported on React Native, only on web
  // Skip analytics when using emulators to avoid 503 errors
  isSupported().then((supported) => {
    if (supported) {
      analytics = getAnalytics(app);
    }
  });
} else {
  console.log('Firebase: Skipping analytics initialization (emulator mode or non-web platform)');
}

// Clean local auth state but don't delete the app
async function clearAuthState(isForSignIn = false) {
  if (Platform.OS === 'web') {
    try {
      // Check if there's an active user first
      const currentAuth = getAuth(app);
      if (currentAuth.currentUser) {
        console.log('Firebase: Signing out current user before auth operation');
        await signOut(currentAuth);
      }

      // For sign-in, use minimal storage clearing to prevent breaking auth
      if (isForSignIn) {
        console.log('Firebase: Performing minimal cleanup for sign-in');

        // Clear just authentication state tokens
        try {
          localStorage.removeItem('firebase:authUser');
          sessionStorage.removeItem('firebase:authUser');
          localStorage.removeItem('firebase:session');
          sessionStorage.removeItem('firebase:session');
        } catch (e) {
          console.warn('Firebase: Error clearing minimal auth storage:', e);
        }
      } else {
        // For sign-out, do a more thorough cleaning
        console.log('Firebase: Performing complete auth state cleanup');
        await clearFirebaseIndexedDB();
      }

      // Reload persistence to ensure clean state
      await setPersistence(currentAuth, inMemoryPersistence);

      console.log('Firebase: Auth state prepared successfully');
    } catch (error) {
      console.error('Firebase: Error clearing auth state:', error);
    }
  }
}

export {
  app,
  db,
  auth,
  storage,
  functions,
  analytics,
  clearFirebaseIndexedDB,
  clearAuthState,
};

// Helper to check if Firebase is properly configured
export function isFirebaseConfigured(): boolean {
  return !!firebaseConfig.apiKey && !!firebaseConfig.projectId;
}

// Helper to log Firebase configuration status
export function logFirebaseConfigStatus(): boolean {
  const isConfigured = isFirebaseConfigured();
  console.log(
    'Firebase Configuration Status:',
    isConfigured ? 'Configured' : 'Not configured'
  );
  return isConfigured;
}
