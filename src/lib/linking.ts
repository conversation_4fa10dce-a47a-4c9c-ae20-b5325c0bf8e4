// Navigation linking configuration for deep linking
export const linking = {
  prefixes: [
    // Add your app's URL schemes here
    'healthapp://',
    'https://healthapp.example.com',
    'exp://'  // Expo development
  ],
  config: {
    // Link configuration based on your navigation structure
    initialRouteName: 'home',
    screens: {
      home: 'home',
      scan: 'scan',
      profile: 'profile',
      // Deep link to specific screens
      'recipe-details': 'recipe/:id',
      'food-details': 'food/:id',
      'meal-plan': 'meal-plan/:date',
      // Auth screens
      login: 'login',
      register: 'register',
      // Settings screens
      settings: 'settings',
      'health-goals': 'settings/health-goals',
      'dietary-preferences': 'settings/dietary-preferences',
      // Nested navigators
      tabs: {
        screens: {
          home: 'home',
          scan: 'scan',
          profile: 'profile',
        },
      },
    },
  },
}; 