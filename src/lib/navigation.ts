import { createNavigationContainerRef } from '@react-navigation/native';

// Create a navigation ref that can be used outside of components
export const navigationRef = createNavigationContainerRef<any>();

// Function to navigate from outside of a React component
export function navigate(name: string, params?: object) {
  if (navigationRef.isReady()) {
    navigationRef.navigate(name, params);
  } else {
    // You might want to save this navigation for when the ref is ready
    console.warn('Navigation attempted before navigationRef was ready');
  }
}

// Function to go back from outside of a React component
export function goBack() {
  if (navigationRef.isReady() && navigationRef.canGoBack()) {
    navigationRef.goBack();
  } else {
    console.warn('Cannot go back - either navigationRef is not ready or there is no screen to go back to');
  }
}

// Function to reset the navigation state
export function reset(state: object) {
  if (navigationRef.isReady()) {
    navigationRef.reset(state as never);
  } else {
    console.warn('Navigation reset attempted before navigationRef was ready');
  }
}

// Get the current route name
export function getCurrentRouteName() {
  if (navigationRef.isReady()) {
    return navigationRef.getCurrentRoute()?.name;
  }
  return null;
} 