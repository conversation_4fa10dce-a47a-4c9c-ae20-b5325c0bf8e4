
import { firestore } from '../lib/firebase';
import { getDoc, doc, collection, query, where, getDocs, addDoc, updateDoc, deleteDoc, orderBy, limit as firestoreLimit } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

export type ActivityType = 'running' | 'walking' | 'cycling' | 'swimming' | 'strength' | 'yoga' | 'hiit' | 'other';

export interface ActivityRecord {
  id: string;
  user_id: string;
  type: ActivityType;
  start_time: string;
  end_time: string;
  duration_minutes: number;
  distance_km?: number;
  calories_burned: number;
  heart_rate_avg?: number;
  heart_rate_max?: number;
  steps?: number;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * Fetches activity records for the current user
 */
export async function fetchActivities(options?: {
  startDate?: string;
  endDate?: string;
  type?: ActivityType;
  limit?: number;
}) {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      console.error('Error getting user: No user found');
      return { success: false, error: 'Authentication error', data: [] };
    }

    // Build the query
    const activitiesRef = collection(firestore, 'activities');
    
    // Create query constraints
    const constraints: any[] = [
      where('user_id', '==', user.uid),
      orderBy('start_time', 'desc')
    ];
    
    // Apply filters
    if (options?.startDate) {
      constraints.push(where('start_time', '>=', options.startDate));
    }
    
    if (options?.endDate) {
      constraints.push(where('start_time', '<=', options.endDate));
    }
    
    if (options?.type) {
      constraints.push(where('type', '==', options.type));
    }
    
    if (options?.limit) {
      constraints.push(firestoreLimit(options.limit));
    }
    
    const q = query(activitiesRef, ...constraints);
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return { success: true, data: [] };
    }

    const data = querySnapshot.docs.map(doc => {
      return { id: doc.id, ...doc.data() } as ActivityRecord;
    });

    return { success: true, data };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in fetchActivities:', errorMessage);
    return { success: false, error: errorMessage, data: [] };
  }
}

/**
 * Fetches activity statistics for the current user
 */
export async function fetchActivityStats(timeframe: 'week' | 'month' | 'year' = 'week') {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      console.error('Error getting user: No user found');
      return { success: false, error: 'Authentication error' };
    }

    // Calculate date range based on timeframe
    const endDate = new Date();
    const startDate = new Date();
    
    if (timeframe === 'week') {
      startDate.setDate(endDate.getDate() - 7);
    } else if (timeframe === 'month') {
      startDate.setMonth(endDate.getMonth() - 1);
    } else if (timeframe === 'year') {
      startDate.setFullYear(endDate.getFullYear() - 1);
    }

    // Query Firebase for activities in date range
    const activitiesRef = collection(firestore, 'activities');
    const q = query(
      activitiesRef,
      where('user_id', '==', user.uid),
      where('start_time', '>=', startDate.toISOString()),
      where('start_time', '<=', endDate.toISOString())
    );
    
    const querySnapshot = await getDocs(q);
    const data = querySnapshot.docs.map(doc => {
      return { id: doc.id, ...doc.data() } as ActivityRecord;
    });

    // Calculate statistics
    const totalActivities = data.length;
    const totalDuration = data.reduce((sum, activity) => sum + (activity.duration_minutes || 0), 0);
    const totalCalories = data.reduce((sum, activity) => sum + (activity.calories_burned || 0), 0);
    const totalDistance = data.reduce((sum, activity) => sum + (activity.distance_km || 0), 0);
    
    // Group by activity type
    const activityBreakdown: Record<string, number> = {};
    data.forEach(activity => {
      const type = activity.type || 'other';
      activityBreakdown[type] = (activityBreakdown[type] || 0) + 1;
    });

    return { 
      success: true, 
      data: {
        totalActivities,
        totalDuration,
        totalCalories,
        totalDistance,
        activityBreakdown,
        timeframe
      }
    };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in fetchActivityStats:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Adds a new activity record
 */
export async function addActivity(activity: Omit<ActivityRecord, 'id' | 'user_id' | 'created_at' | 'updated_at'>) {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Authentication error: Not authenticated' };
    }

    const now = new Date().toISOString();
    const activityData = {
      ...activity,
      user_id: user.uid,
      created_at: now,
      updated_at: now
    };
    
    const docRef = await addDoc(collection(firestore, 'activities'), activityData);
    
    // Return the created activity with its ID
    const data = {
      id: docRef.id,
      ...activityData
    };

    return { success: true, data };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in addActivity:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Updates an existing activity record
 */
export async function updateActivity(activityId: string, updates: Partial<Omit<ActivityRecord, 'id' | 'user_id' | 'created_at' | 'updated_at'>>) {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Authentication error: Not authenticated' };
    }

    // First verify that this activity belongs to the user
    const activityRef = doc(firestore, 'activities', activityId);
    const activityDoc = await getDoc(activityRef);
    
    if (!activityDoc.exists()) {
      return { success: false, error: 'Activity not found' };
    }
    
    const activityData = activityDoc.data();
    if (activityData.user_id !== user.uid) {
      return { success: false, error: 'Permission denied' };
    }
    
    // Update the activity
    const updatedData = {
      ...updates,
      updated_at: new Date().toISOString()
    };
    
    await updateDoc(activityRef, updatedData);
    
    // Get the updated document
    const updatedActivityDoc = await getDoc(activityRef);
    const data = { 
      id: activityId, 
      ...updatedActivityDoc.data() 
    } as ActivityRecord;

    return { success: true, data };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in updateActivity:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Deletes an activity record
 */
export async function deleteActivity(activityId: string) {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Authentication error: Not authenticated' };
    }

    // First verify that this activity belongs to the user
    const activityRef = doc(firestore, 'activities', activityId);
    const activityDoc = await getDoc(activityRef);
    
    if (!activityDoc.exists()) {
      return { success: false, error: 'Activity not found' };
    }
    
    const activityData = activityDoc.data();
    if (activityData.user_id !== user.uid) {
      return { success: false, error: 'Permission denied' };
    }
    
    // Delete the activity
    await deleteDoc(activityRef);

    return { success: true };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in deleteActivity:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Get sample activity data for demo purposes
 */
export function getSampleActivityData(): ActivityRecord[] {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  const twoDaysAgo = new Date(today);
  twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);
  const threeDaysAgo = new Date(today);
  threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
  const fourDaysAgo = new Date(today);
  fourDaysAgo.setDate(fourDaysAgo.getDate() - 4);
  const fiveDaysAgo = new Date(today);
  fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5);
  const sixDaysAgo = new Date(today);
  sixDaysAgo.setDate(sixDaysAgo.getDate() - 6);
  
  return [
    {
      id: '1',
      user_id: 'user123',
      type: 'running',
      start_time: today.toISOString(),
      end_time: new Date(today.getTime() + 45 * 60000).toISOString(),
      duration_minutes: 45,
      distance_km: 5.2,
      calories_burned: 420,
      heart_rate_avg: 145,
      heart_rate_max: 175,
      steps: 7500,
      notes: 'Morning run, felt great!'
    },
    {
      id: '2',
      user_id: 'user123',
      type: 'strength',
      start_time: yesterday.toISOString(),
      end_time: new Date(yesterday.getTime() + 60 * 60000).toISOString(),
      duration_minutes: 60,
      calories_burned: 380,
      heart_rate_avg: 125,
      heart_rate_max: 150,
      notes: 'Upper body workout'
    },
    {
      id: '3',
      user_id: 'user123',
      type: 'yoga',
      start_time: twoDaysAgo.toISOString(),
      end_time: new Date(twoDaysAgo.getTime() + 30 * 60000).toISOString(),
      duration_minutes: 30,
      calories_burned: 120,
      heart_rate_avg: 90,
      heart_rate_max: 105,
      notes: 'Evening yoga session'
    },
    {
      id: '4',
      user_id: 'user123',
      type: 'cycling',
      start_time: threeDaysAgo.toISOString(),
      end_time: new Date(threeDaysAgo.getTime() + 90 * 60000).toISOString(),
      duration_minutes: 90,
      distance_km: 25.3,
      calories_burned: 650,
      heart_rate_avg: 155,
      heart_rate_max: 178,
      notes: 'Long weekend ride'
    },
    {
      id: '5',
      user_id: 'user123',
      type: 'swimming',
      start_time: fourDaysAgo.toISOString(),
      end_time: new Date(fourDaysAgo.getTime() + 40 * 60000).toISOString(),
      duration_minutes: 40,
      distance_km: 1.5,
      calories_burned: 350,
      heart_rate_avg: 130,
      heart_rate_max: 155,
      notes: 'Pool swim, focused on technique'
    },
    {
      id: '6',
      user_id: 'user123',
      type: 'hiit',
      start_time: fiveDaysAgo.toISOString(),
      end_time: new Date(fiveDaysAgo.getTime() + 25 * 60000).toISOString(),
      duration_minutes: 25,
      calories_burned: 320,
      heart_rate_avg: 160,
      heart_rate_max: 185,
      notes: 'Intense session!'
    },
    {
      id: '7',
      user_id: 'user123',
      type: 'walking',
      start_time: sixDaysAgo.toISOString(),
      end_time: new Date(sixDaysAgo.getTime() + 60 * 60000).toISOString(),
      duration_minutes: 60,
      distance_km: 4.8,
      calories_burned: 280,
      heart_rate_avg: 105,
      heart_rate_max: 120,
      steps: 6500,
      notes: 'Leisure walk in the park'
    }
  ];
} 