import { getAuth } from 'firebase/auth';
import { getFirestore, collection, query, where, getDocs, orderBy } from 'firebase/firestore';

export interface ActivityHistory {
  calories: number[];
  water: number[];
  meals: number[];
  steps: number[];
  dates: string[];
}

export interface ActivityData {
  calories: number;
  water: number;
  meals: number;
  steps: number;
  date: string;
}

export interface LoadActivityHistoryResult {
  success: boolean;
  history?: ActivityHistory;
  error?: Error;
}

export class ActivitySummaryService {
  private db = getFirestore();
  private auth = getAuth();

  async loadActivityHistory(timeRange: 'day' | 'week' | 'month'): Promise<LoadActivityHistoryResult> {
    try {
      const user = this.auth.currentUser;
      
      if (!user) {
        console.log('No user logged in');
        return { success: false, error: new Error('No user logged in') };
      }
      
      const userId = user.uid;
      
      // Determine date range based on selected timeRange
      const today = new Date();
      let startDate = new Date();
      
      if (timeRange === 'week') {
        startDate.setDate(today.getDate() - 7);
      } else if (timeRange === 'month') {
        startDate.setMonth(today.getMonth() - 1);
      } else {
        // For 'day', we'll get the last 7 entries to show a weekly trend
        startDate.setDate(today.getDate() - 7);
      }
      
      // Format dates for Firestore query
      const startDateString = startDate.toISOString().split('T')[0];
      const endDateString = today.toISOString().split('T')[0];
      
      // Query activity logs collection
      const activityRef = collection(this.db, 'activity_logs');
      const q = query(
        activityRef,
        where('user_id', '==', userId),
        where('date', '>=', startDateString),
        where('date', '<=', endDateString),
        orderBy('date', 'asc')
      );
      
      const querySnapshot = await getDocs(q);
      
      // Prepare data arrays
      const history: ActivityHistory = {
        calories: [],
        water: [],
        meals: [],
        steps: [],
        dates: []
      };
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        history.calories.push(data.calories || 0);
        history.water.push(data.water || 0);
        history.meals.push(data.meals || 0);
        history.steps.push(data.steps || 0);
        history.dates.push(data.date || '');
      });
      
      // If no data exists, create some mock data for demonstration
      if (history.dates.length === 0) {
        history.calories = [1800, 2100, 1950, 2200, 1850, 2000, 2150];
        history.water = [6, 8, 7, 9, 6, 8, 7];
        history.meals = [3, 4, 3, 5, 3, 4, 4];
        history.steps = [8500, 9200, 7800, 10500, 8100, 9000, 9800];
        history.dates = this.generateDemoDates(7);
      }
      
      return {
        success: true,
        history
      };
    } catch (error) {
      console.error('Error loading activity history:', error);
      return {
        success: false,
        error: error instanceof Error ? error : new Error('Unknown error loading activity history')
      };
    }
  }

  private generateDemoDates(days: number): string[] {
    const dates: string[] = [];
    const today = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      dates.push(date.toISOString().split('T')[0]);
    }
    
    return dates;
  }

  calculateMaxValues(history: ActivityHistory): {
    maxCalories: number;
    maxWater: number;
    maxMeals: number;
    maxSteps: number;
  } {
    return {
      maxCalories: Math.max(...history.calories, 1),
      maxWater: Math.max(...history.water, 1),
      maxMeals: Math.max(...history.meals, 1),
      maxSteps: Math.max(...history.steps, 1)
    };
  }

  formatDisplayDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
  }
}