import { useDatabaseType } from '@/contexts/DatabaseContext';
import { v4 as uuidv4 } from 'uuid';

export interface ActivityRecord {
  id?: string;
  userId: string;
  date: string;
  startTime?: string;
  endTime?: string;
  activityType: string;
  durationMinutes: number;
  caloriesBurned?: number;
  distance?: number;
  steps?: number;
  heartRate?: {
    average?: number;
    max?: number;
    min?: number;
  };
  notes?: string;
  source?: 'manual' | 'google_fit' | 'apple_health' | 'garmin' | 'fitbit' | 'other';
  createdAt?: string;
  updatedAt?: string;
}

export interface ActivityGoal {
  id?: string;
  userId: string;
  activityType?: string;
  weeklyMinutesTarget?: number;
  weeklyCaloriesTarget?: number;
  weeklyDistanceTarget?: number;
  dailyStepsTarget?: number;
  createdAt?: string;
  updatedAt?: string;
}


export function useActivityTrackingService() {
  const { db, useFirebase  } = useDatabaseType();

  /**
   * Add an activity record
   */
  const addActivityRecord = async (activity: Omit<ActivityRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
    try {
      // Add timestamps
      const now = new Date().toISOString();
      const activityData = {
        ...activity,
        createdAt: now,
        updatedAt: now
      };

      // Add to database
      const collection = useFirebase ? 'activity_records' : 'activity_records';
      const id = await db.createDocument(collection, activityData);
      
      return id;
    } catch (error) {
      console.error('Error adding activity record:', error);
      throw error;
    }
  };

  /**
   * Get activity records for a specific date
   */
  const getActivityRecordsForDate = async (userId: string, date: string): Promise<ActivityRecord[]> => {
    try {
      const collection = useFirebase ? 'activity_records' : 'activity_records';
      const filters = [
        { field: 'userId', operator: '==', value: userId },
        { field: 'date', operator: '==', value: date }
      ];
      
      const records = await db.queryDocuments(collection, filters);
      return records as ActivityRecord[];
    } catch (error) {
      console.error(`Error getting activity records for date ${date}:`, error);
      throw error;
    }
  };

  /**
   * Get activity records for a date range
   */
  const getActivityRecordsForDateRange = async (userId: string, startDate: string, endDate: string): Promise<ActivityRecord[]> => {
    try {
      const collection = useFirebase ? 'activity_records' : 'activity_records';
      const filters = [
        { field: 'userId', operator: '==', value: userId },
        { field: 'date', operator: '>=', value: startDate },
        { field: 'date', operator: '<=', value: endDate }
      ];
      
      const records = await db.queryDocuments(collection, filters);
      return records as ActivityRecord[];
    } catch (error) {
      console.error(`Error getting activity records for date range ${startDate} to ${endDate}:`, error);
      throw error;
    }
  };

  /**
   * Update an activity record
   */
  const updateActivityRecord = async (id: string, updates: Partial<ActivityRecord>): Promise<void> => {
    try {
      const collection = useFirebase ? 'activity_records' : 'activity_records';
      const now = new Date().toISOString();
      
      await db.updateDocument(collection, id, {
        ...updates,
        updatedAt: now
      });
    } catch (error) {
      console.error(`Error updating activity record ${id}:`, error);
      throw error;
    }
  };

  /**
   * Delete an activity record
   */
  const deleteActivityRecord = async (id: string): Promise<void> => {
    try {
      const collection = useFirebase ? 'activity_records' : 'activity_records';
      await db.deleteDocument(collection, id);
    } catch (error) {
      console.error(`Error deleting activity record ${id}:`, error);
      throw error;
    }
  };

  /**
   * Get activity summary for a date range
   */
  const getActivitySummary = async (
    userId: string, 
    startDate: string, 
    endDate: string
  ): Promise<{
    totalMinutes: number;
    totalCalories: number;
    totalDistance: number;
    totalSteps: number;
    byActivityType: Record<string, {
      minutes: number;
      calories: number;
      distance: number;
      count: number;
    }>;
  }> => {
    try {
      // Get all activity records for the date range
      const records = await getActivityRecordsForDateRange(userId, startDate, endDate);
      
      // Initialize summary structure
      const summary = {
        totalMinutes: 0,
        totalCalories: 0,
        totalDistance: 0,
        totalSteps: 0,
        byActivityType: {} as Record<string, {
          minutes: number;
          calories: number;
          distance: number;
          count: number;
        }>
      };
      
      // Calculate summary values
      for (const record of records) {
        summary.totalMinutes += record.durationMinutes || 0;
        summary.totalCalories += record.caloriesBurned || 0;
        summary.totalDistance += record.distance || 0;
        summary.totalSteps += record.steps || 0;
        
        // Initialize activity type if not exists
        if (!summary.byActivityType[record.activityType]) {
          summary.byActivityType[record.activityType] = {
            minutes: 0,
            calories: 0,
            distance: 0,
            count: 0
          };
        }
        
        // Add to activity type summary
        const activitySummary = summary.byActivityType[record.activityType];
        activitySummary.minutes += record.durationMinutes || 0;
        activitySummary.calories += record.caloriesBurned || 0;
        activitySummary.distance += record.distance || 0;
        activitySummary.count += 1;
      }
      
      return summary;
    } catch (error) {
      console.error(`Error getting activity summary for ${startDate} to ${endDate}:`, error);
      throw error;
    }
  };

  /**
   * Get activity goal for user
   */
  const getActivityGoal = async (userId: string): Promise<ActivityGoal | null> => {
    try {
      const collection = useFirebase ? 'activity_goals' : 'activity_goals';
      
      // First try to get by userId
      const filters = [
        { field: 'userId', operator: '==', value: userId }
      ];
      
      const goals = await db.queryDocuments(collection, filters);
      
      if (goals && goals.length > 0) {
        return goals[0] as ActivityGoal;
      }
      
      // If no goal found, try to get directly by id (assuming userId is the document id)
      try {
        const goal = await db.getDocument(collection, userId);
        if (goal) {
          return goal as ActivityGoal;
        }
      } catch (docError) {
        console.log('No direct activity goal found with userId as id');
      }
      
      return null;
    } catch (error) {
      console.error(`Error getting activity goal for user ${userId}:`, error);
      throw error;
    }
  };

  /**
   * Set activity goal for user
   */
  const setActivityGoal = async (goal: ActivityGoal): Promise<void> => {
    try {
      const collection = useFirebase ? 'activity_goals' : 'activity_goals';
      const now = new Date().toISOString();
      
      // Check if goal already exists
      const existingGoal = await getActivityGoal(goal.userId);
      
      if (existingGoal) {
        // Update existing goal
        await db.updateDocument(collection, existingGoal.id!, {
          ...goal,
          updatedAt: now
        });
      } else {
        // Create new goal
        await db.createDocument(collection, {
          ...goal,
          createdAt: now,
          updatedAt: now
        });
      }
    } catch (error) {
      console.error('Error setting activity goal:', error);
      throw error;
    }
  };

  /**
   * Check if a user has met their activity goals for a given week
   */
  const checkWeeklyGoalProgress = async (
    userId: string, 
    weekStartDate: string
  ): Promise<{
    hasMinutesGoal: boolean;
    hasCaloriesGoal: boolean;
    hasDistanceGoal: boolean;
    hasStepsGoal: boolean;
    minutesProgress: number;
    caloriesProgress: number;
    distanceProgress: number;
    stepsProgress: number;
    minutesGoalMet: boolean;
    caloriesGoalMet: boolean;
    distanceGoalMet: boolean;
    stepsGoalMet: boolean;
  }> => {
    try {
      // Calculate the end date (6 days from start date)
      const startDateObj = new Date(weekStartDate);
      const endDateObj = new Date(startDateObj);
      endDateObj.setDate(startDateObj.getDate() + 6);
      const weekEndDate = endDateObj.toISOString().split('T')[0];
      
      // Get the user's activity goal
      const goal = await getActivityGoal(userId);
      
      // Get the user's activity summary for the week
      const summary = await getActivitySummary(userId, weekStartDate, weekEndDate);
      
      // Determine if goals exist and if they have been met
      const hasMinutesGoal = goal?.weeklyMinutesTarget != null && goal.weeklyMinutesTarget > 0;
      const hasCaloriesGoal = goal?.weeklyCaloriesTarget != null && goal.weeklyCaloriesTarget > 0;
      const hasDistanceGoal = goal?.weeklyDistanceTarget != null && goal.weeklyDistanceTarget > 0;
      const hasStepsGoal = goal?.dailyStepsTarget != null && goal.dailyStepsTarget > 0;
      
      const minutesProgress = hasMinutesGoal 
        ? Math.min(100, Math.round((summary.totalMinutes / (goal?.weeklyMinutesTarget || 1)) * 100)) 
        : 0;
        
      const caloriesProgress = hasCaloriesGoal 
        ? Math.min(100, Math.round((summary.totalCalories / (goal?.weeklyCaloriesTarget || 1)) * 100)) 
        : 0;
        
      const distanceProgress = hasDistanceGoal 
        ? Math.min(100, Math.round((summary.totalDistance / (goal?.weeklyDistanceTarget || 1)) * 100)) 
        : 0;
        
      // For steps goal, we need to calculate for 7 days
      const dailyStepsTarget = goal?.dailyStepsTarget || 0;
      const weeklyStepsTarget = dailyStepsTarget * 7;
      const stepsProgress = hasStepsGoal 
        ? Math.min(100, Math.round((summary.totalSteps / weeklyStepsTarget) * 100)) 
        : 0;
      
      return {
        hasMinutesGoal,
        hasCaloriesGoal,
        hasDistanceGoal,
        hasStepsGoal,
        minutesProgress,
        caloriesProgress,
        distanceProgress,
        stepsProgress,
        minutesGoalMet: hasMinutesGoal && summary.totalMinutes >= (goal?.weeklyMinutesTarget || 0),
        caloriesGoalMet: hasCaloriesGoal && summary.totalCalories >= (goal?.weeklyCaloriesTarget || 0),
        distanceGoalMet: hasDistanceGoal && summary.totalDistance >= (goal?.weeklyDistanceTarget || 0),
        stepsGoalMet: hasStepsGoal && summary.totalSteps >= weeklyStepsTarget
      };
    } catch (error) {
      console.error(`Error checking weekly goal progress for ${userId}:`, error);
      throw error;
    }
  };

  return {
    addActivityRecord,
    getActivityRecordsForDate,
    getActivityRecordsForDateRange,
    updateActivityRecord,
    deleteActivityRecord,
    getActivitySummary,
    getActivityGoal,
    setActivityGoal,
    checkWeeklyGoalProgress
  };
} 