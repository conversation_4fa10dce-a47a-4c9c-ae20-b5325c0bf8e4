import { trackEvent, trackScreen, identify } from './posthogService';
import { FEATURE_FLAGS, isFeatureEnabled } from '@/featureFlags';
import { Platform } from 'react-native';

// Centralized analytics service for the app
export class AnalyticsService {
  // Screen tracking with feature flag check
  static async trackScreenView(screenName: string, properties?: Record<string, any>) {
    // Check which features are enabled for context
    const featureContext = {
      water_tracking_enabled: await isFeatureEnabled(FEATURE_FLAGS.WATER_TRACKING),
      food_scanning_enabled: await isFeatureEnabled(FEATURE_FLAGS.FOOD_SCANNING),
      lidar_enabled: await isFeatureEnabled(FEATURE_FLAGS.LIDAR_SCANNING),
      payments_enabled: await isFeatureEnabled(FEATURE_FLAGS.PAYMENTS),
    };
    
    trackScreen(screenName, {
      ...properties,
      ...featureContext,
      platform: Platform.OS,
      timestamp: new Date().toISOString(),
    });
  }
  
  // User actions analytics
  static trackUserAction(action: string, category: string, properties?: Record<string, any>) {
    trackEvent(`${category}_${action}`, {
      ...properties,
      category,
      action,
      platform: Platform.OS,
      timestamp: new Date().toISOString(),
    });
  }
  
  // Feature usage tracking
  static async trackFeatureUsage(feature: FEATURE_FLAGS[keyof typeof FEATURE_FLAGS], action: string, properties?: Record<string, any>) {
    const isEnabled = await isFeatureEnabled(feature);
    
    if (!isEnabled) {
      // Track attempts to use disabled features
      trackEvent('feature_disabled_usage_attempt', {
        feature,
        action,
        ...properties,
      });
      return;
    }
    
    trackEvent(`feature_${feature}_${action}`, {
      feature,
      action,
      enabled: isEnabled,
      ...properties,
      platform: Platform.OS,
      timestamp: new Date().toISOString(),
    });
  }
  
  // Specific feature analytics
  static waterTracking = {
    trackIntakeAdded: (amount: number, source: string) => {
      this.trackFeatureUsage(FEATURE_FLAGS.WATER_TRACKING, 'intake_added', {
        amount_ml: amount,
        source,
        time_of_day: new Date().getHours(),
      });
    },
    
    trackGoalUpdated: (newGoal: number, oldGoal: number) => {
      this.trackFeatureUsage(FEATURE_FLAGS.WATER_TRACKING, 'goal_updated', {
        new_goal: newGoal,
        old_goal: oldGoal,
        change_amount: newGoal - oldGoal,
      });
    },
    
    trackSyncCompleted: (success: boolean, recordCount: number) => {
      this.trackFeatureUsage(FEATURE_FLAGS.WATER_TRACKING, 'sync_completed', {
        success,
        record_count: recordCount,
        sync_method: 'firebase',
      });
    },
  };
  
  static foodScanning = {
    trackScanStarted: (method: 'camera' | 'lidar' | 'barcode') => {
      this.trackFeatureUsage(FEATURE_FLAGS.FOOD_SCANNING, 'scan_started', {
        scan_method: method,
        device_has_lidar: Platform.OS === 'ios',
      });
    },
    
    trackScanCompleted: (method: string, success: boolean, analysisTime: number) => {
      this.trackFeatureUsage(FEATURE_FLAGS.FOOD_SCANNING, 'scan_completed', {
        scan_method: method,
        success,
        analysis_time_ms: analysisTime,
      });
    },
    
    trackFoodIdentified: (foodName: string, confidence: number, method: string) => {
      this.trackFeatureUsage(FEATURE_FLAGS.FOOD_SCANNING, 'food_identified', {
        food_name: foodName,
        confidence_score: confidence,
        identification_method: method,
      });
    },
  };
  
  static lidarScanning = {
    trackCalibrationStarted: () => {
      this.trackFeatureUsage(FEATURE_FLAGS.LIDAR_SCANNING, 'calibration_started', {
        device_model: Platform.constants?.Model,
      });
    },
    
    trackCalibrationCompleted: (success: boolean, referenceObject: string) => {
      this.trackFeatureUsage(FEATURE_FLAGS.LIDAR_SCANNING, 'calibration_completed', {
        success,
        reference_object: referenceObject,
      });
    },
    
    trackVolumeEstimated: (volume: number, confidence: number) => {
      this.trackFeatureUsage(FEATURE_FLAGS.LIDAR_SCANNING, 'volume_estimated', {
        volume_ml: volume,
        confidence_score: confidence,
      });
    },
  };
  
  static payments = {
    trackCheckoutStarted: (plan: string, price: number) => {
      this.trackFeatureUsage(FEATURE_FLAGS.PAYMENTS, 'checkout_started', {
        plan_type: plan,
        price_usd: price,
        currency: 'USD',
      });
    },
    
    trackCheckoutCompleted: (plan: string, sessionId: string) => {
      this.trackFeatureUsage(FEATURE_FLAGS.PAYMENTS, 'checkout_completed', {
        plan_type: plan,
        session_id: sessionId,
      });
    },
    
    trackSubscriptionChanged: (oldPlan: string, newPlan: string, action: string) => {
      this.trackFeatureUsage(FEATURE_FLAGS.PAYMENTS, 'subscription_changed', {
        old_plan: oldPlan,
        new_plan: newPlan,
        change_action: action,
      });
    },
  };
  
  static profile = {
    trackProfileUpdated: (fields: string[]) => {
      this.trackFeatureUsage(FEATURE_FLAGS.USER_PROFILE, 'profile_updated', {
        updated_fields: fields,
        field_count: fields.length,
      });
    },
    
    trackPhotoUploaded: (success: boolean, fileSize?: number) => {
      this.trackFeatureUsage(FEATURE_FLAGS.PROFILE_PHOTOS, 'photo_uploaded', {
        success,
        file_size_kb: fileSize ? fileSize / 1024 : undefined,
      });
    },
    
    trackSettingsChanged: (setting: string, value: any) => {
      this.trackFeatureUsage(FEATURE_FLAGS.USER_PROFILE, 'settings_changed', {
        setting_name: setting,
        new_value: value,
      });
    },
  };
  
  // User journey tracking
  static trackUserJourney(milestone: string, properties?: Record<string, any>) {
    trackEvent(`user_journey_${milestone}`, {
      milestone,
      ...properties,
      platform: Platform.OS,
      timestamp: new Date().toISOString(),
    });
  }
  
  // Conversion tracking
  static trackConversion(type: string, value?: number, properties?: Record<string, any>) {
    trackEvent(`conversion_${type}`, {
      conversion_type: type,
      conversion_value: value,
      ...properties,
      platform: Platform.OS,
      timestamp: new Date().toISOString(),
    });
  }
  
  // Performance tracking
  static trackPerformance(metric: string, value: number, unit: string, properties?: Record<string, any>) {
    trackEvent('performance_metric', {
      metric_name: metric,
      metric_value: value,
      metric_unit: unit,
      ...properties,
      platform: Platform.OS,
      timestamp: new Date().toISOString(),
    });
  }
}

// Export convenience functions
export const Analytics = AnalyticsService;

// Export feature-specific analytics
export const WaterAnalytics = AnalyticsService.waterTracking;
export const ScanAnalytics = AnalyticsService.foodScanning;
export const LidarAnalytics = AnalyticsService.lidarScanning;
export const PaymentAnalytics = AnalyticsService.payments;
export const ProfileAnalytics = AnalyticsService.profile;