/**
 * API Client
 * Centralized service for making API calls with consistent error handling,
 * retry logic, and response formatting
 */

import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { BaseApiResponse, ErrorResponse } from '@/types/api-responses';
import { getAuth } from 'firebase/auth';

/**
 * API request configuration
 */
interface ApiRequestConfig {
  baseUrl?: string;
  headers?: Record<string, string>;
  timeout?: number;
  retry?: {
    maxRetries: number;
    delayMs: number;
    backoffFactor: number;
  };
  cache?: {
    enabled: boolean;
    ttlMs?: number; // Time to live in milliseconds
  };
  offline?: {
    enabled: boolean;
    fallbackData?: any;
  };
}

/**
 * Default API request configuration
 */
const DEFAULT_CONFIG: ApiRequestConfig = {
  timeout: 30000, // 30 seconds
  retry: {
    maxRetries: 3,
    delayMs: 1000, // 1 second
    backoffFactor: 2, // Exponential backoff
  },
  cache: {
    enabled: false,
    ttlMs: 5 * 60 * 1000, // 5 minutes
  },
  offline: {
    enabled: false,
  },
};

/**
 * Response metadata
 */
interface ResponseMeta {
  timestamp: number;
  duration: number;
  retries: number;
  cacheHit: boolean;
}

/**
 * Cache storage key generator
 */
const getCacheKey = (url: string, params?: any) => {
  const queryParams = params ? JSON.stringify(params) : '';
  return `api_cache:${url}:${queryParams}`;
};

/**
 * Check if network is available
 */
const isNetworkAvailable = async (): Promise<boolean> => {
  // Basic implementation - in a real app, use NetInfo
  try {
    // Simple network check by making a HEAD request to a reliable endpoint
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000);
    
    try {
      const response = await fetch('https://httpbin.org/head', {
        method: 'HEAD',
        signal: controller.signal as any,
      });
      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      return false;
    } finally {
      clearTimeout(timeoutId);
    }
  } catch (error) {
    return false;
  }
};

/**
 * Generic API client with consistent error handling and retry logic
 */
class ApiClient {
  private config: ApiRequestConfig;

  constructor(config: Partial<ApiRequestConfig> = {}) {
    // Merge with default config
    this.config = {
      ...DEFAULT_CONFIG,
      ...config,
      retry: config.retry ? {
        ...DEFAULT_CONFIG.retry,
        ...config.retry,
      } : DEFAULT_CONFIG.retry,
      cache: config.cache ? {
        ...DEFAULT_CONFIG.cache,
        ...config.cache,
      } : DEFAULT_CONFIG.cache,
      offline: config.offline ? {
        ...DEFAULT_CONFIG.offline,
        ...config.offline,
      } : DEFAULT_CONFIG.offline,
    };
  }

  /**
   * Set default headers for all requests
   */
  setDefaultHeaders(headers: Record<string, string>) {
    this.config.headers = {
      ...this.config.headers,
      ...headers,
    };
  }

  /**
   * Get cached response if available
   */
  private async getCachedResponse<T>(cacheKey: string): Promise<{ data: T; meta: ResponseMeta } | null> {
    try {
      const cached = await AsyncStorage.getItem(cacheKey);
      if (cached) {
        const { data, meta, expiry } = JSON.parse(cached);
        const now = Date.now();
        
        // Check if cache has expired
        if (expiry > now) {
          return { data, meta: { ...meta, cacheHit: true } };
        }
        
        // Cache expired, remove it
        await AsyncStorage.removeItem(cacheKey);
      }
    } catch (error) {
      console.warn('Error reading from cache:', error);
    }
    
    return null;
  }

  /**
   * Cache response for future use
   */
  private async cacheResponse<T>(cacheKey: string, data: T, meta: ResponseMeta) {
    try {
      // Access ttlMs with non-null assertion since we know DEFAULT_CONFIG.cache is defined
      const ttl = this.config.cache?.ttlMs || DEFAULT_CONFIG.cache!.ttlMs!;
      const expiry = Date.now() + ttl;
      
      await AsyncStorage.setItem(
        cacheKey,
        JSON.stringify({ data, meta, expiry })
      );
    } catch (error) {
      console.warn('Error writing to cache:', error);
    }
  }

  /**
   * Make an API request with retry capability
   */
  async request<T = any>(
    url: string,
    options: RequestInit = {},
    config: Partial<ApiRequestConfig> = {}
  ): Promise<{ data: T; meta: ResponseMeta }> {
    // Merge configs for this specific request
    const requestConfig: ApiRequestConfig = {
      ...this.config,
      ...config,
      retry: config.retry ? {
        ...this.config.retry,
        ...config.retry,
      } : this.config.retry,
      cache: config.cache ? {
        ...this.config.cache,
        ...config.cache,
      } : this.config.cache,
      offline: config.offline ? {
        ...this.config.offline,
        ...config.offline,
      } : this.config.offline,
    };
    
    // Prepare headers
    const headers = {
      'Content-Type': 'application/json',
      ...this.config.headers,
      ...options.headers,
    };
    
    // Add baseUrl if provided
    const fullUrl = requestConfig.baseUrl ? `${requestConfig.baseUrl}${url}` : url;
    
    // Check for cached response if caching is enabled
    let cacheKey = '';
    if (requestConfig.cache?.enabled && options.method !== 'POST' && options.method !== 'PUT' && options.method !== 'DELETE') {
      cacheKey = getCacheKey(fullUrl, options.body);
      const cached = await this.getCachedResponse<T>(cacheKey);
      if (cached) {
        return cached;
      }
    }
    
    // Check for network connectivity if offline handling is enabled
    if (requestConfig.offline?.enabled) {
      const networkAvailable = await isNetworkAvailable();
      if (!networkAvailable) {
        // If no network and we have fallback data, return it
        if (requestConfig.offline.fallbackData) {
          return {
            data: requestConfig.offline.fallbackData as T,
            meta: {
              timestamp: Date.now(),
              duration: 0,
              retries: 0,
              cacheHit: false,
            },
          };
        }
        
        // Otherwise, throw a network error
        throw this.createError({
          message: 'Network unavailable',
          code: 'NETWORK_UNAVAILABLE',
        });
      }
    }
    
    // Implement retry logic
    let retries = 0;
    let lastError: any;
    let startTime = Date.now();
    
    while (retries <= (requestConfig.retry?.maxRetries || 0)) {
      try {
        if (retries > 0) {
          // Calculate backoff delay
          const delay = 
            (requestConfig.retry?.delayMs || 1000) * 
            Math.pow(requestConfig.retry?.backoffFactor || 1, retries - 1);
          
          // Wait for backoff period
          await new Promise(resolve => setTimeout(resolve, delay));
          
          // Reset start time for accurate duration measurement
          startTime = Date.now();
        }
        
        // Create abort controller for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
          controller.abort();
        }, requestConfig.timeout);
        
        // Make the request
        const response = await fetch(fullUrl, {
          ...options,
          headers,
          signal: controller.signal as any,
        });
        
        // Clear timeout
        clearTimeout(timeoutId);
        
        // Calculate request duration
        const duration = Date.now() - startTime;
        
        // Process response
        if (response.ok) {
          let responseData: T;
          
          // Parse response based on content type
          const contentType = response.headers.get('Content-Type') || '';
          if (contentType.includes('application/json')) {
            responseData = await response.json() as T;
          } else if (contentType.includes('text/')) {
            responseData = await response.text() as unknown as T;
          } else {
            // For binary data like images, get blob
            responseData = await response.blob() as unknown as T;
          }
          
          // Create metadata
          const meta: ResponseMeta = {
            timestamp: Date.now(),
            duration,
            retries,
            cacheHit: false,
          };
          
          // Cache the response if caching is enabled
          if (requestConfig.cache?.enabled && cacheKey) {
            this.cacheResponse(cacheKey, responseData, meta);
          }
          
          return { data: responseData, meta };
        } else {
          // Handle API error responses
          let errorData: any;
          try {
            errorData = await response.json();
          } catch {
            errorData = { message: response.statusText };
          }
          
          // Should we retry based on status code?
          const shouldRetry = [408, 429, 500, 502, 503, 504].includes(response.status);
          if (shouldRetry && retries < (requestConfig.retry?.maxRetries || 0)) {
            lastError = this.createError({
              message: errorData.message || `Request failed with status ${response.status}`,
              status: response.status,
              data: errorData,
              code: errorData.code || `HTTP_${response.status}`,
            });
            retries++;
            continue;
          }
          
          // If we shouldn't retry or have exhausted retries, throw the error
          throw this.createError({
            message: errorData.message || `Request failed with status ${response.status}`,
            status: response.status,
            data: errorData, 
            code: errorData.code || `HTTP_${response.status}`,
          });
        }
      } catch (error: any) {
        // Handle fetch errors (network, timeout, etc.)
        lastError = error;
        
        // Determine if we should retry based on error type
        const isNetworkError = 
          error.name === 'AbortError' || 
          error.message?.includes('Network request failed') ||
          error.message?.includes('network timeout');
          
        if (isNetworkError && retries < (requestConfig.retry?.maxRetries || 0)) {
          retries++;
          continue;
        }
        
        // Standardize error format
        throw this.createError({
          message: error.message || 'Network request failed',
          code: error.name === 'AbortError' ? 'TIMEOUT' : 'NETWORK_ERROR',
          originalError: error,
        });
      }
    }
    
    // Should only reach here if all retries failed
    throw lastError;
  }

  /**
   * Create standardized error object
   */
  private createError({
    message,
    code = 'API_ERROR',
    status,
    data,
    originalError,
  }: {
    message: string;
    code?: string;
    status?: number;
    data?: any;
    originalError?: Error;
  }): ErrorResponse {
    return {
      success: false,
      error: message,
      code,
      details: {
        status,
        data,
        originalError: originalError?.message,
      },
    };
  }

  /**
   * Convenience method for GET requests
   */
  async get<T = any>(
    url: string,
    params?: Record<string, any>,
    config?: Partial<ApiRequestConfig>
  ): Promise<T> {
    // Build URL with query parameters
    const queryString = params
      ? '?' + new URLSearchParams(params as Record<string, string>).toString()
      : '';
    
    const { data } = await this.request<T>(
      `${url}${queryString}`,
      { method: 'GET' },
      config
    );
    
    return data;
  }

  /**
   * Convenience method for POST requests
   */
  async post<T = any>(
    url: string,
    body?: any,
    config?: Partial<ApiRequestConfig>
  ): Promise<T> {
    const { data } = await this.request<T>(
      url,
      {
        method: 'POST',
        body: body ? JSON.stringify(body) : undefined,
      },
      config
    );
    
    return data;
  }

  /**
   * Convenience method for PUT requests
   */
  async put<T = any>(
    url: string,
    body?: any,
    config?: Partial<ApiRequestConfig>
  ): Promise<T> {
    const { data } = await this.request<T>(
      url,
      {
        method: 'PUT',
        body: body ? JSON.stringify(body) : undefined,
      },
      config
    );
    
    return data;
  }

  /**
   * Convenience method for DELETE requests
   */
  async delete<T = any>(
    url: string,
    config?: Partial<ApiRequestConfig>
  ): Promise<T> {
    const { data } = await this.request<T>(
      url,
      { method: 'DELETE' },
      config
    );
    
    return data;
  }

  /**
   * Upload file to an API endpoint
   */
  async uploadFile<T = any>(
    url: string,
    fileUri: string,
    fieldName: string = 'file',
    additionalFields?: Record<string, string>,
    config?: Partial<ApiRequestConfig>
  ): Promise<T> {
    // Handle platform differences
    if (Platform.OS === 'web') {
      // For web, we need to fetch the file and create a blob
      const response = await fetch(fileUri);
      const blob = await response.blob();
      
      // Create FormData and append file
      const formData = new FormData();
      formData.append(
        fieldName,
        blob,
        fileUri.split('/').pop() || 'file'
      );
      
      // Add any additional fields
      if (additionalFields) {
        Object.entries(additionalFields).forEach(([key, value]) => {
          formData.append(key, value);
        });
      }
      
      // Make the request
      const { data } = await this.request<T>(
        url,
        {
          method: 'POST',
          body: formData as any,
          headers: { 'Content-Type': 'multipart/form-data' },
        },
        config
      );
      
      return data;
    } else {
      // For native platforms, use FileSystem.uploadAsync
      if (!FileSystem.uploadAsync) {
        throw new Error('FileSystem.uploadAsync is not available on this platform');
      }
      
      // Prepare upload options
      const uploadOptions: FileSystem.FileSystemUploadOptions = {
        httpMethod: 'POST',
        uploadType: FileSystem.FileSystemUploadType.MULTIPART,
        fieldName,
      };
      
      // Add additional fields
      if (additionalFields) {
        uploadOptions.parameters = additionalFields;
      }
      
      // Add headers
      uploadOptions.headers = {
        ...this.config.headers,
      };
      
      // Upload file
      const fullUrl = this.config.baseUrl ? `${this.config.baseUrl}${url}` : url;
      const response = await FileSystem.uploadAsync(fullUrl, fileUri, uploadOptions);
      
      if (response.status >= 200 && response.status < 300) {
        let data: T;
        try {
          data = JSON.parse(response.body);
        } catch {
          data = response.body as unknown as T;
        }
        return data;
      } else {
        throw this.createError({
          message: `Upload failed with status ${response.status}`,
          status: response.status,
          data: response.body,
        });
      }
    }
  }
}

// Create a default instance for general use
export const apiClient = new ApiClient();

// Create specialized instances for specific APIs
export const visionApiClient = new ApiClient({
  baseUrl: process.env.EXPO_PUBLIC_VISION_API_URL || 'https://api.calorielens.com/vision',
  retry: {
    maxRetries: 2,
    delayMs: 1000,
    backoffFactor: 1.5,
  },
  cache: {
    enabled: true,
    ttlMs: 30 * 60 * 1000, // 30 minutes
  },
});

export const nutritionApiClient = new ApiClient({
  baseUrl: process.env.EXPO_PUBLIC_NUTRITION_API_URL || 'https://api.calorielens.com/nutrition',
  timeout: 15000, // 15 seconds
  cache: {
    enabled: true,
    ttlMs: 60 * 60 * 1000, // 1 hour
  },
  offline: {
    enabled: true,
  },
});

// Add authentication token to requests when available
async function addAuthTokenToRequest(options: any = {}) {
  try {
    const auth = getAuth();
    const currentUser = auth.currentUser;
    
    if (currentUser) {
      const token = await currentUser.getIdToken();
      options.headers = {
        ...options.headers,
        'Authorization': `Bearer ${token}`
      };
    }
    
    return options;
  } catch (error) {
    console.error('Error adding auth token to request:', error);
    return options;
  }
}

// Helper function to make Firebase requests through API client
export async function makeFirebaseRequest<T = any>(
  endpoint: string,
  options: RequestInit = {},
  config: any = {}
): Promise<T> {
  const apiClient = new ApiClient({
    baseUrl: process.env.EXPO_PUBLIC_FIREBASE_API_URL,
    headers: {
      'Content-Type': 'application/json',
    }
  });
  
  // Add auth token if user is logged in
  const optionsWithAuth = await addAuthTokenToRequest(options);
  
  const response = await apiClient.request<T>(endpoint, optionsWithAuth, config);
  return response.data;
}

export default ApiClient; 