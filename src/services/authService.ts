import { useDatabaseType } from '@/contexts/DatabaseContext';
import { useProfileService } from './profileService';

export interface User {
  id: string;
  email: string;
  name?: string;
  avatarUrl?: string;
}

export interface AuthResult {
  success: boolean;
  user?: User | null;
  error?: string | null;
}


export function useAuthService() {
  const { db, useFirebase } = useDatabaseType();
  const profileService = useProfileService();

  /**
   * Sign in with email and password
   */
  const signIn = async (email: string, password: string): Promise<AuthResult> => {
    try {
      const result = await db.signIn(email, password);

      if (!result.success) {
        return { 
          success: false, 
          error: result.error || 'Authentication failed'
        };
      }

      const authUser = result.user;
      
      if (!authUser) {
        return { 
          success: true, 
          user: null,
          error: 'User authenticated but no user data returned'
        };
      }

      // Get the user profile
      let profile;
      try {
        profile = await profileService.getProfile(authUser.id || authUser.uid);
      } catch (error) {
        console.warn('Error fetching profile after login:', error);
      }

      // Create a standardized user object
      const user: User = {
        id: authUser.id || authUser.uid,
        email: authUser.email || '',
        name: profile?.full_name || profile?.displayName || authUser.email?.split('@')[0] || '',
        avatarUrl: profile?.avatar_url || profile?.avatarUrl || '',
      };

      return { 
        success: true, 
        user,
        error: null
      };
    } catch (error) {
      console.error('Error signing in:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error during sign in'
      };
    }
  };

  /**
   * Sign up with email, password, and name
   */
  const signUp = async (email: string, password: string, name: string): Promise<AuthResult> => {
    try {
      // Prepare user data appropriate for the provider
      const userData = useFirebase 
        ? { displayName: name, email } 
        : { full_name: name, email };
      
      const result = await db.signUp(email, password, userData);

      if (!result.success) {
        return { 
          success: false, 
          error: result.error || 'Registration failed'
        };
      }

      const authUser = result.user;
      
      if (!authUser) {
        return { 
          success: true,
          user: null,
          error: 'User registered but no user data returned'
        };
      }

      // For Firebase, we'll need to manually create the profile since we're not using a trigger
      
      const userId = authUser.id || authUser.uid;
      
      try {
        // Check if profile exists
        let profile = await profileService.getProfile(userId);
        
        // If profile doesn't exist, create it
        if (!profile) {
          console.log('Creating new user profile after registration');
          
          const profileData = useFirebase 
            ? {
                displayName: name,
                email: email,
                createdAt: new Date().toISOString()
              }
            : {
                full_name: name,
                email: email,
                created_at: new Date().toISOString()
              };
              
          await profileService.updateProfile(userId, profileData);
          
          // Get the newly created profile
          profile = await profileService.getProfile(userId);
        }
        
        // Create standardized user object
        const user: User = {
          id: userId,
          email: email,
          name: name,
          avatarUrl: profile?.avatar_url || profile?.avatarUrl || '',
        };
        
        return {
          success: true,
          user,
          error: null
        };
      } catch (profileError) {
        console.error('Error creating profile after registration:', profileError);
        
        // Return success but with a warning about profile creation
        return {
          success: true,
          user: {
            id: userId,
            email: email,
            name: name
          },
          error: 'User registered but profile creation failed'
        };
      }
    } catch (error) {
      console.error('Error signing up:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error during registration'
      };
    }
  };

  /**
   * Sign out the current user
   */
  const signOut = async (): Promise<AuthResult> => {
    try {
      await db.signOut();
      return { success: true };
    } catch (error) {
      console.error('Error signing out:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error during sign out'
      };
    }
  };

  /**
   * Get the current user
   */
  const getCurrentUser = async (): Promise<User | null> => {
    try {
      const authUser = await db.getCurrentUser();
      
      if (!authUser) {
        return null;
      }
      
      // Get the user profile
      let profile;
      try {
        profile = await profileService.getProfile(authUser.id || authUser.uid);
      } catch (profileError) {
        console.warn('Error fetching profile in getCurrentUser:', profileError);
      }
      
      // Create standardized user object
      return {
        id: authUser.id || authUser.uid,
        email: authUser.email || '',
        name: profile?.full_name || profile?.displayName || authUser.email?.split('@')[0] || '',
        avatarUrl: profile?.avatar_url || profile?.avatarUrl || '',
      };
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  };

  /**
   * Set up a listener for authentication state changes
   */
  const onAuthStateChanged = (callback: (user: User | null) => void): (() => void) => {
    return db.onAuthStateChanged(async (authUser) => {
      if (!authUser) {
        callback(null);
        return;
      }
      
      // Get the user profile
      let profile;
      try {
        profile = await profileService.getProfile(authUser.id || authUser.uid);
      } catch (profileError) {
        console.warn('Error fetching profile in auth state change handler:', profileError);
      }
      
      // Create standardized user object
      const user: User = {
        id: authUser.id || authUser.uid,
        email: authUser.email || '',
        name: profile?.full_name || profile?.displayName || authUser.email?.split('@')[0] || '',
        avatarUrl: profile?.avatar_url || profile?.avatarUrl || '',
      };
      
      callback(user);
    });
  };

  /**
   * Send a password reset email
   */
  const sendPasswordResetEmail = async (email: string): Promise<AuthResult> => {
    try {
      const result = await db.sendPasswordResetEmail(email);
      return result;
    } catch (error) {
      console.error('Error sending password reset email:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error sending password reset email'
      };
    }
  };

  return {
    signIn,
    signUp,
    signOut,
    getCurrentUser,
    onAuthStateChanged,
    sendPasswordResetEmail
  };
} 