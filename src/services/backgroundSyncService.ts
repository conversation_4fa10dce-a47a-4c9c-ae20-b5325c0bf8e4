// This is a mock implementation of the background sync service
// The actual implementation has been disabled because the required packages are not available
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// Mock BackgroundFetchResult enumeration
enum BackgroundFetchResult {
  NoData = 0,
  NewData = 1,
  Failed = 2,
}

// Initialize background sync
export const initBackgroundSync = async (): Promise<boolean> => {
  try {
    // Store background sync status in AsyncStorage
    await AsyncStorage.setItem('backgroundSyncEnabled', 'true');
    console.log('[MOCK] Background health sync registered');
    return true;
  } catch (error) {
    console.error('Failed to register background sync:', error);
    return false;
  }
};

// Unregister background sync
export const stopBackgroundSync = async (): Promise<boolean> => {
  try {
    await AsyncStorage.setItem('backgroundSyncEnabled', 'false');
    console.log('[MOCK] Background health sync unregistered');
    return true;
  } catch (error) {
    console.error('Failed to unregister background sync:', error);
    return false;
  }
};

// Check if background sync is registered
export const isBackgroundSyncRegistered = async (): Promise<boolean> => {
  try {
    const value = await AsyncStorage.getItem('backgroundSyncEnabled');
    return value === 'true';
  } catch {
    return false;
  }
};

// Trigger an immediate sync
export const triggerSync = async (): Promise<boolean> => {
  try {
    // Simulate a sync by storing the current time
    await AsyncStorage.setItem('lastHealthDataSync', new Date().toISOString());
    console.log('[MOCK] Manual sync triggered');
    return true;
  } catch (error) {
    console.error('Error triggering manual sync:', error);
    return false;
  }
};

// Get the last sync time
export const getLastSyncTime = async (): Promise<Date | null> => {
  try {
    const lastSync = await AsyncStorage.getItem('lastHealthDataSync');
    return lastSync ? new Date(lastSync) : null;
  } catch (error) {
    console.error('Error getting last sync time:', error);
    return null;
  }
}; 