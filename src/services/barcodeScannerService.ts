import { Camera , BarcodeScanningResult } from 'expo-camera';

export interface BarcodeData {
  data: string;
  type: string;
  timestamp: string;
}

/**
 * Service layer for barcode scanning operations
 * Handles permissions, barcode formatting, and external API lookups
 * Following the three-layer architecture pattern
 */
export const barcodeScannerService = {
  /**
   * Request camera permission for barcode scanning
   * @returns Promise<boolean> - true if permission granted, false otherwise
   */
  async requestPermission(): Promise<boolean> {
    const { status } = await Camera.requestCameraPermissionsAsync();
    return status === 'granted';
  },

  /**
   * Get current camera permission status
   * @returns Promise<boolean | null> - true if granted, false if denied, null if not determined
   */
  async getPermissionStatus(): Promise<boolean | null> {
    const { status } = await Camera.getCameraPermissionsAsync();
    
    switch (status) {
      case 'granted':
        return true;
      case 'denied':
        return false;
      default:
        return null;
    }
  },

  /**
   * Format barcode data into standardized format
   * @param barcode - Raw barcode data string
   * @param type - Barcode type string
   * @returns Formatted barcode data object
   */
  formatBarcode(barcode: string, type: string): BarcodeData {
    return {
      data: barcode,
      type: type,
      timestamp: new Date().toISOString()
    };
  },

  /**
   * Lookup barcode in external databases/APIs
   * This method should handle external API calls only
   * No UI logic should be included here
   * 
   * @param barcode - Barcode to lookup
   * @returns Promise<object | null> - Product data or null if not found
   */
  async lookupBarcode(barcode: string): Promise<any> {
    // This is a placeholder for external API integration
    // In the real implementation, this would call external barcode databases
    // For now, it returns null to indicate not found
    
    // Example of what this might do:
    // const response = await fetch(`https://api.barcodedb.com/lookup/${barcode}`);
    // const data = await response.json();
    // return data;
    
    return null;
  },

  /**
   * Validate barcode format
   * @param barcode - Barcode string to validate
   * @param type - Expected barcode type
   * @returns boolean - true if valid format
   */
  isValidBarcode(barcode: string, type: string): boolean {
    if (!barcode || barcode.trim().length === 0) {
      return false;
    }

    // Basic validation based on barcode type
    switch (type) {
      case 'ean13':
        return /^\d{13}$/.test(barcode);
      case 'ean8':
        return /^\d{8}$/.test(barcode);
      case 'upc_a':
        return /^\d{12}$/.test(barcode);
      case 'upc_e':
        return /^\d{8}$/.test(barcode);
      default:
        // For unknown types, just check if it's not empty
        return barcode.length > 0;
    }
  },

  /**
   * Extract the first barcode from scanning results
   * @param results - Array of barcode scanning results
   * @returns BarcodeData | null - First valid barcode or null
   */
  extractFirstBarcode(results: BarcodeScanningResult[]): BarcodeData | null {
    if (!results || results.length === 0) {
      return null;
    }

    const firstResult = results[0];
    return this.formatBarcode(firstResult.data, firstResult.type);
  }
};