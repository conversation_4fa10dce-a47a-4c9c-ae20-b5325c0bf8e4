import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import * as SQLite from 'expo-sqlite';
import * as Crypto from 'expo-crypto';

// Define the structure of a cached nutritional explanation
export interface CachedNutritionalExplanation {
  id: string;
  mealData: {
    name: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber?: number;
    items?: {name: string}[];
  };
  explanation: {
    summary: string;
    healthBenefits: string[];
    considerations: string[];
    dietaryImplications: string[];
    balanceAssessment: string;
  };
  timestamp: number;
}

// Cache expiration time (7 days in milliseconds)
const CACHE_EXPIRATION = 7 * 24 * 60 * 60 * 1000;

// SQLite database reference
let db: SQLite.SQLiteDatabase;

// Meal health score cache implementation
const MEAL_SCORE_CACHE_PREFIX = 'meal_score_';
const MEAL_SCORE_CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

/**
 * Initialize the cache database
 */
export async function initCacheDatabase() {
  try {
    db = await SQLite.openDatabaseAsync('health_app_cache.db');
    
    // Create the nutritional explanations cache table if it doesn't exist
    await db.execAsync(`
      CREATE TABLE IF NOT EXISTS nutritional_explanations_cache (
        id TEXT PRIMARY KEY,
        meal_data TEXT,
        explanation TEXT,
        timestamp INTEGER
      );
    `);
    
    return { success: true };
  } catch (error) {
    console.error('Error initializing cache database:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error initializing cache database' 
    };
  }
}

/**
 * Generate a unique ID for caching based on meal data
 */
export async function generateCacheId(mealData: {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber?: number;
  items?: {name: string}[];
}): Promise<string> {
  // Create a string representation of the meal data for hashing
  const mealString = JSON.stringify({
    name: mealData.name.toLowerCase(),
    calories: Math.round(mealData.calories), // Round to avoid minor differences
    protein: Math.round(mealData.protein),
    carbs: Math.round(mealData.carbs),
    fat: Math.round(mealData.fat),
    fiber: mealData.fiber ? Math.round(mealData.fiber) : undefined,
    // For items, just use the names to ensure consistency
    items: mealData.items ? mealData.items.map(item => item.name.toLowerCase()).sort() : undefined
  });
  
  // Create a hash from the string
  // Use expo-crypto's digestStringAsync for hashing
  return await Crypto.digestStringAsync(
    Crypto.CryptoDigestAlgorithm.SHA256,
    mealString
  );
}

/**
 * Store a nutritional explanation in the cache
 */
export async function cacheNutritionalExplanation(
  mealData: {
    name: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber?: number;
    items?: {name: string}[];
  },
  explanation: {
    summary: string;
    healthBenefits: string[];
    considerations: string[];
    dietaryImplications: string[];
    balanceAssessment: string;
  }
): Promise<boolean> {
  try {
    if (!db) {
      await initCacheDatabase();
    }
    
    const cacheId = await generateCacheId(mealData);
    const timestamp = Date.now();
    
    await db.runAsync(
      `INSERT OR REPLACE INTO nutritional_explanations_cache (id, meal_data, explanation, timestamp)
       VALUES (?, ?, ?, ?)`,
      [
        cacheId,
        JSON.stringify(mealData),
        JSON.stringify(explanation),
        timestamp
      ]
    );
    
    return true;
  } catch (error) {
    console.error('Error caching nutritional explanation:', error);
    return false;
  }
}

/**
 * Retrieve a cached nutritional explanation
 */
export async function getCachedNutritionalExplanation(
  mealData: {
    name: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber?: number;
    items?: {name: string}[];
  }
): Promise<{
  success: boolean;
  explanation?: {
    summary: string;
    healthBenefits: string[];
    considerations: string[];
    dietaryImplications: string[];
    balanceAssessment: string;
  };
}> {
  try {
    if (!db) {
      await initCacheDatabase();
    }
    
    const cacheId = await generateCacheId(mealData);
    const now = Date.now();
    
    // Get the cached entry
    const cachedEntry = await db.getFirstAsync<{
      id: string;
      meal_data: string;
      explanation: string;
      timestamp: number;
    }>(
      'SELECT * FROM nutritional_explanations_cache WHERE id = ?',
      cacheId
    );
    
    if (!cachedEntry) {
      return { success: false };
    }
    
    // Check if the cache has expired
    if (now - cachedEntry.timestamp > CACHE_EXPIRATION) {
      // Remove expired cache entry
      await db.runAsync('DELETE FROM nutritional_explanations_cache WHERE id = ?', cacheId);
      return { success: false };
    }
    
    // Parse the explanation
    const explanation = JSON.parse(cachedEntry.explanation);
    
    return {
      success: true,
      explanation
    };
  } catch (error) {
    console.error('Error retrieving cached nutritional explanation:', error);
    return { success: false };
  }
}

/**
 * Clear all cached nutritional explanations
 */
export async function clearNutritionalExplanationsCache(): Promise<boolean> {
  try {
    if (!db) {
      await initCacheDatabase();
    }
    
    await db.runAsync('DELETE FROM nutritional_explanations_cache');
    return true;
  } catch (error) {
    console.error('Error clearing nutritional explanations cache:', error);
    return false;
  }
}

/**
 * Remove expired cache entries
 */
export async function cleanupExpiredCache(): Promise<boolean> {
  try {
    if (!db) {
      await initCacheDatabase();
    }
    
    const expirationThreshold = Date.now() - CACHE_EXPIRATION;
    
    await db.runAsync(
      'DELETE FROM nutritional_explanations_cache WHERE timestamp < ?',
      expirationThreshold
    );
    
    return true;
  } catch (error) {
    console.error('Error cleaning up expired cache:', error);
    return false;
  }
}

/**
 * Cache a meal health score
 * @param mealId The ID of the meal
 * @param scoreData The meal health score data to cache
 */
export async function cacheMealScore(mealId: string, scoreData: any): Promise<void> {
  try {
    const cacheKey = `${MEAL_SCORE_CACHE_PREFIX}${mealId}`;
    const cacheData = {
      data: scoreData,
      timestamp: Date.now(),
      expiry: Date.now() + MEAL_SCORE_CACHE_EXPIRY
    };
    
    await AsyncStorage.setItem(cacheKey, JSON.stringify(cacheData));
    console.log(`Cached meal score for meal ${mealId}`);
  } catch (error) {
    console.error('Error caching meal score:', error);
  }
}

/**
 * Get a cached meal health score
 * @param mealId The ID of the meal
 * @returns The cached meal health score or null if not found or expired
 */
export async function getCachedMealScore(mealId: string): Promise<any | null> {
  try {
    const cacheKey = `${MEAL_SCORE_CACHE_PREFIX}${mealId}`;
    const cachedData = await AsyncStorage.getItem(cacheKey);
    
    if (!cachedData) {
      return null;
    }
    
    const { data, expiry } = JSON.parse(cachedData);
    
    // Check if the cached data has expired
    if (Date.now() > expiry) {
      console.log(`Cached meal score for meal ${mealId} has expired`);
      await AsyncStorage.removeItem(cacheKey);
      return null;
    }
    
    console.log(`Retrieved cached meal score for meal ${mealId}`);
    return data;
  } catch (error) {
    console.error('Error retrieving cached meal score:', error);
    return null;
  }
} 