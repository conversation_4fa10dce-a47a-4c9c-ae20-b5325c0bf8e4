import AsyncStorage from '@react-native-async-storage/async-storage';

import { v4 as uuidv4 } from 'uuid';
import { trackChallengeCompletion } from './rewardsService';
import { firestore } from '@/lib/firebase';
import { getDoc, doc, collection, query, where, getDocs, addDoc, updateDoc, setDoc } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

// Challenge types
export enum ChallengeType {
  WATER_INTAKE = 'water_intake',
  PROTEIN_GOAL = 'protein_goal',
  CALORIE_TARGET = 'calorie_target',
  MEAL_LOGGING = 'meal_logging',
  STREAK = 'streak',
  CUSTOM = 'custom',
}

// Challenge difficulty levels
export enum ChallengeDifficulty {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard',
}

// Challenge status
export enum ChallengeStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

// Challenge interface
export interface Challenge {
  id: string;
  title: string;
  description: string;
  type: ChallengeType;
  duration: number; // in days
  difficulty: ChallengeDifficulty;
  goal: number; // target value (e.g., water amount, protein grams)
  unit?: string; // unit for the goal (e.g., ml, g)
  startDate?: string;
  endDate?: string;
  status: ChallengeStatus;
  participants?: string[]; // array of user IDs
  createdBy?: string; // user ID of creator
  progress?: number; // current progress toward goal
  isPublic: boolean;
  imageUrl?: string;
  rewards?: {
    points: number;
    badge?: string;
  };
}

// User challenge progress
interface UserChallengeProgress {
  userId: string;
  challengeId: string;
  currentValue: number;
  completedDays: number;
  lastUpdateDate: string;
  status: ChallengeStatus;
}

// Local cache keys
const CHALLENGES_CACHE_KEY = 'user_challenges';
const CHALLENGE_PROGRESS_KEY = 'challenge_progress';

// Pre-defined challenges
const predefinedChallenges: Omit<Challenge, 'id' | 'status'>[] = [
  {
    title: '30-Day Water Challenge',
    description: 'Drink 2500ml of water every day for 30 days to improve hydration and boost metabolism.',
    type: ChallengeType.WATER_INTAKE,
    duration: 30,
    difficulty: ChallengeDifficulty.MEDIUM,
    goal: 2500, // ml per day
    unit: 'ml',
    isPublic: true,
    imageUrl: 'https://images.unsplash.com/photo-1538300342682-cf57afb97169',
    rewards: {
      points: 300,
      badge: 'hydration_master',
    },
  },
  {
    title: '7-Day Protein Boost',
    description: 'Meet your daily protein goal for 7 consecutive days.',
    type: ChallengeType.PROTEIN_GOAL,
    duration: 7,
    difficulty: ChallengeDifficulty.EASY,
    goal: 0, // Will be set to user's protein goal
    unit: 'g',
    isPublic: true,
    imageUrl: 'https://images.unsplash.com/photo-1533090481720-856c6e3c1fdc',
    rewards: {
      points: 100,
      badge: 'protein_champion',
    },
  },
  {
    title: '21-Day Meal Tracking',
    description: 'Log all your meals for 21 consecutive days to build a consistent habit.',
    type: ChallengeType.MEAL_LOGGING,
    duration: 21,
    difficulty: ChallengeDifficulty.MEDIUM,
    goal: 3, // 3 meals per day
    isPublic: true,
    imageUrl: 'https://images.unsplash.com/photo-1544145945-f90425340c7e',
    rewards: {
      points: 210,
      badge: 'tracking_master',
    },
  },
  {
    title: '14-Day Calorie Goal',
    description: 'Stay within your daily calorie target for 14 days.',
    type: ChallengeType.CALORIE_TARGET,
    duration: 14,
    difficulty: ChallengeDifficulty.HARD,
    goal: 0, // Will be set to user's calorie goal
    unit: 'kcal',
    isPublic: true,
    imageUrl: 'https://images.unsplash.com/photo-1533090161767-e6ffed986c88',
    rewards: {
      points: 280,
      badge: 'calorie_control',
    },
  },
];

/**
 * Get all available challenges for the user
 */
export async function getAvailableChallenges(): Promise<Challenge[]> {
  try {
    // Get user-specific data
    const userProfile = await getUserNutritionGoals();
    
    // Try to fetch from Firebase first
    try {
      const challengesRef = collection(firestore, 'challenges');
      const q = query(challengesRef, where('is_public', '==', true));
      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        const challenges: Challenge[] = [];
        querySnapshot.forEach((doc) => {
          const challengeData = doc.data();
          challenges.push(mapDbChallengeToChallengeObject({
            id: doc.id,
            ...challengeData
          }, userProfile));
        });
        return challenges;
      }
    } catch (dbError) {
      console.error('Error fetching challenges from Firebase:', dbError);
      // Collection might not exist, continue to use predefined challenges
    }
    
    // Use predefined challenges
    return predefinedChallenges.map(challenge => {
      // Generate a unique ID for each challenge
      const id = uuidv4();
      
      // Set goal based on user profile for certain challenges
      let goal = challenge.goal;
      if (challenge.type === ChallengeType.PROTEIN_GOAL && userProfile.proteinGoal) {
        goal = userProfile.proteinGoal;
      } else if (challenge.type === ChallengeType.CALORIE_TARGET && userProfile.calorieGoal) {
        goal = userProfile.calorieGoal;
      }
      
      return {
        ...challenge,
        id,
        goal,
        status: ChallengeStatus.NOT_STARTED,
      };
    });
  } catch (error) {
    console.error('Error getting available challenges:', error);
    return [];
  }
}

/**
 * Get user's active challenges
 */
export async function getUserChallenges(): Promise<Challenge[]> {
  try {
    // Try to load from local storage first
    const cachedChallenges = await AsyncStorage.getItem(CHALLENGES_CACHE_KEY);
    
    if (cachedChallenges) {
      return JSON.parse(cachedChallenges);
    }
    
    // If not in cache, try to fetch from Firebase
    try {
      const userId = await getUserId();
      const userChallengesRef = collection(firestore, 'user_challenges');
      const q = query(userChallengesRef, where('user_id', '==', userId));
      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        const challenges: Challenge[] = [];
        
        // For each user challenge, get the challenge details
        for (const userChallengeDoc of querySnapshot.docs) {
          const userChallengeData = userChallengeDoc.data();
          
          // Get the challenge details
          const challengeDocRef = doc(firestore, 'challenges', userChallengeData.challenge_id);
          const challengeDoc = await getDoc(challengeDocRef);
          
          if (challengeDoc.exists()) {
            const challengeData = challengeDoc.data();
            challenges.push({
              id: challengeDoc.id,
              title: challengeData.title,
              description: challengeData.description,
              type: challengeData.type,
              duration: challengeData.duration,
              difficulty: challengeData.difficulty,
              goal: challengeData.goal,
              unit: challengeData.unit,
              status: userChallengeData.status as ChallengeStatus,
              startDate: userChallengeData.start_date,
              endDate: userChallengeData.end_date,
              progress: userChallengeData.progress,
              isPublic: challengeData.is_public,
              imageUrl: challengeData.image_url,
              createdBy: challengeData.created_by,
              rewards: challengeData.rewards,
            });
          }
        }
        
        // Cache the result
        await AsyncStorage.setItem(CHALLENGES_CACHE_KEY, JSON.stringify(challenges));
        
        return challenges;
      }
    } catch (dbError) {
      console.error('Error fetching user challenges - collection may not exist:', dbError);
      // Fall back to empty array if collection doesn't exist
    }
    
    return [];
  } catch (error) {
    console.error('Error getting user challenges:', error);
    return [];
  }
}

/**
 * Join a challenge
 */
export async function joinChallenge(challenge: Challenge): Promise<boolean> {
  try {
    const userId = await getUserId();
    const now = new Date().toISOString();
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + challenge.duration);
    
    // Update the challenge with start and end dates
    const updatedChallenge: Challenge = {
      ...challenge,
      startDate: now,
      endDate: endDate.toISOString(),
      status: ChallengeStatus.IN_PROGRESS,
      progress: 0,
    };
    
    // Add to user challenges in Firebase
    try {
      await addDoc(collection(firestore, 'user_challenges'), {
        user_id: userId,
        challenge_id: challenge.id,
        status: ChallengeStatus.IN_PROGRESS,
        start_date: now,
        end_date: endDate.toISOString(),
        progress: 0,
      });
    } catch (dbError) {
      console.error('Error joining challenge - collection may not exist:', dbError);
      // Continue with local storage
    }
    
    // Update local cache
    const existingChallenges = await getUserChallenges();
    const updatedChallenges = [...existingChallenges, updatedChallenge];
    await AsyncStorage.setItem(CHALLENGES_CACHE_KEY, JSON.stringify(updatedChallenges));
    
    return true;
  } catch (error) {
    console.error('Error joining challenge:', error);
    return false;
  }
}

/**
 * Create a custom challenge
 */
export async function createCustomChallenge(challenge: Omit<Challenge, 'id' | 'status'>): Promise<Challenge | null> {
  try {
    const userId = await getUserId();
    
    // Prepare challenge data for Firebase
    const challengeData = {
      title: challenge.title,
      description: challenge.description,
      type: challenge.type,
      duration: challenge.duration,
      difficulty: challenge.difficulty,
      goal: challenge.goal,
      unit: challenge.unit,
      is_public: challenge.isPublic,
      image_url: challenge.imageUrl,
      created_by: userId,
      rewards: challenge.rewards,
      created_at: new Date().toISOString()
    };
    
    let newChallenge: Challenge;
    
    // Attempt to add to Firebase
    try {
      const docRef = await addDoc(collection(firestore, 'challenges'), challengeData);
      
      newChallenge = {
        ...challenge,
        id: docRef.id,
        status: ChallengeStatus.NOT_STARTED,
        createdBy: userId,
      };
    } catch (dbError) {
      console.error('Error creating challenge in Firebase:', dbError);
      
      // Create with local ID if Firebase fails
      const id = uuidv4();
      newChallenge = {
        ...challenge,
        id,
        status: ChallengeStatus.NOT_STARTED,
        createdBy: userId,
      };
    }
    
    return newChallenge;
  } catch (error) {
    console.error('Error creating custom challenge:', error);
    return null;
  }
}

/**
 * Update challenge progress
 */
export async function updateChallengeProgress(challengeId: string, value: number): Promise<boolean> {
  try {
    // Get the user's challenges
    const challenges = await getUserChallenges();
    const challenge = challenges.find(c => c.id === challengeId);
    
    if (!challenge) {
      console.error('Challenge not found:', challengeId);
      return false;
    }
    
    // Get the progress data
    let progressData: UserChallengeProgress[] = [];
    const cachedProgress = await AsyncStorage.getItem(CHALLENGE_PROGRESS_KEY);
    
    if (cachedProgress) {
      progressData = JSON.parse(cachedProgress);
    }
    
    const userId = await getUserId();
    const today = new Date().toISOString().split('T')[0];
    
    // Find or create progress entry
    let progressEntry = progressData.find(p => p.challengeId === challengeId && p.userId === userId);
    
    if (!progressEntry) {
      progressEntry = {
        userId,
        challengeId,
        currentValue: 0,
        completedDays: 0,
        lastUpdateDate: today,
        status: ChallengeStatus.IN_PROGRESS,
      };
      progressData.push(progressEntry);
    }
    
    // If it's a new day, reset the current value for daily challenges
    if (progressEntry.lastUpdateDate !== today) {
      progressEntry.currentValue = 0;
      progressEntry.lastUpdateDate = today;
      
      // Increment completed days if yesterday's goal was met
      if (progressEntry.currentValue >= challenge.goal) {
        progressEntry.completedDays += 1;
      }
    }
    
    // Update the progress value
    progressEntry.currentValue += value;
    
    // Check if challenge is completed
    let challengeCompleted = false;
    if (progressEntry.completedDays >= challenge.duration) {
      progressEntry.status = ChallengeStatus.COMPLETED;
      challengeCompleted = true;
      
      // Update challenge status
      const updatedChallenges = challenges.map(c => 
        c.id === challengeId 
          ? { ...c, status: ChallengeStatus.COMPLETED } 
          : c
      );
      
      await AsyncStorage.setItem(CHALLENGES_CACHE_KEY, JSON.stringify(updatedChallenges));
      
      // Update in Firebase
      try {
        // Find the user challenge document
        const userChallengesRef = collection(firestore, 'user_challenges');
        const q = query(
          userChallengesRef, 
          where('user_id', '==', userId),
          where('challenge_id', '==', challengeId)
        );
        
        const querySnapshot = await getDocs(q);
        
        if (!querySnapshot.empty) {
          const userChallengeDoc = querySnapshot.docs[0];
          await updateDoc(userChallengeDoc.ref, {
            status: ChallengeStatus.COMPLETED,
            updated_at: new Date().toISOString()
          });
        }
      } catch (dbError) {
        console.error('Error updating challenge status - collection may not exist:', dbError);
        // Continue with local storage
      }
      
      // Track challenge completion in the rewards system
      if (challengeCompleted) {
        try {
          await trackChallengeCompletion(challenge);
        } catch (error) {
          console.error('Error tracking challenge completion:', error);
        }
      }
    }
    
    // Save progress data
    await AsyncStorage.setItem(CHALLENGE_PROGRESS_KEY, JSON.stringify(progressData));
    
    return true;
  } catch (error) {
    console.error('Error updating challenge progress:', error);
    return false;
  }
}

/**
 * Helper function to get user ID
 */
async function getUserId(): Promise<string> {
  const auth = getAuth();
  const user = auth.currentUser;
  
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  return user.uid;
}

/**
 * Helper function to get user nutrition goals
 */
async function getUserNutritionGoals(): Promise<any> {
  try {
    // Try to get from local storage first
    const profileData = await AsyncStorage.getItem('userProfile');
    
    if (profileData) {
      return JSON.parse(profileData);
    }
    
    // Otherwise, get from Firebase
    const userId = await getUserId();
    const profileDoc = await getDoc(doc(firestore, 'profiles', userId));
    
    if (profileDoc.exists()) {
      const data = profileDoc.data();
      return {
        calorieGoal: data.daily_calorie_goal || 2000,
        proteinGoal: data.daily_protein_goal || 100,
      };
    }
    
    // Return default values if no profile found
    return {
      calorieGoal: 2000,
      proteinGoal: 100,
    };
  } catch (error) {
    console.error('Error getting user nutrition goals:', error);
    return {
      calorieGoal: 2000,
      proteinGoal: 100,
    };
  }
}

/**
 * Helper function to map database challenge to Challenge object
 */
function mapDbChallengeToChallengeObject(dbChallenge: any, userProfile: any): Challenge {
  let goal = dbChallenge.goal;
  
  // Set goal based on user profile for certain challenges
  if (dbChallenge.type === ChallengeType.PROTEIN_GOAL && userProfile.proteinGoal) {
    goal = userProfile.proteinGoal;
  } else if (dbChallenge.type === ChallengeType.CALORIE_TARGET && userProfile.calorieGoal) {
    goal = userProfile.calorieGoal;
  }
  
  return {
    id: dbChallenge.id,
    title: dbChallenge.title,
    description: dbChallenge.description,
    type: dbChallenge.type,
    duration: dbChallenge.duration,
    difficulty: dbChallenge.difficulty,
    goal,
    unit: dbChallenge.unit,
    status: ChallengeStatus.NOT_STARTED,
    isPublic: dbChallenge.is_public,
    imageUrl: dbChallenge.image_url,
    createdBy: dbChallenge.created_by,
    rewards: dbChallenge.rewards,
  };
} 