import { useDatabaseType } from '@/contexts/DatabaseContext';

export interface Challenge {
  id?: string;
  title: string;
  description: string;
  type: 'steps' | 'water' | 'sleep' | 'activity' | 'nutrition' | 'custom';
  target: number; // Target value (e.g., steps count, minutes of activity)
  unit: string; // Unit of measurement (e.g., 'steps', 'ml', 'minutes')
  startDate: string;
  endDate: string;
  isPublic: boolean;
  creatorId: string;
  createdAt?: string;
  updatedAt?: string;
  imageUrl?: string;
  rewardPoints?: number;
  rules?: string;
  participants?: number;
}

export interface UserChallenge {
  id?: string;
  userId: string;
  challengeId: string;
  joinedAt: string;
  currentProgress: number;
  completed: boolean;
  completedAt?: string;
  lastUpdatedAt: string;
}

export interface ChallengeProgress {
  challengeId: string;
  title: string;
  current: number;
  target: number;
  unit: string;
  percentage: number;
  completed: boolean;
  startDate: string;
  endDate: string;
  type: Challenge['type'];
  description: string;
}


export function useChallengesService() {
  const { db, useFirebase  } = useDatabaseType();

  /**
   * Create a new challenge
   */
  const createChallenge = async (challenge: Omit<Challenge, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
    try {
      // Add timestamps
      const now = new Date().toISOString();
      const challengeData = {
        ...challenge,
        createdAt: now,
        updatedAt: now,
        participants: 0 // Initialize with 0 participants
      };

      // Add to database
      const collection = useFirebase ? 'challenges' : 'challenges';
      const id = await db.createDocument(collection, challengeData);
      
      return id;
    } catch (error) {
      console.error('Error creating challenge:', error);
      throw error;
    }
  };

  /**
   * Get all available public challenges
   */
  const getPublicChallenges = async (): Promise<Challenge[]> => {
    try {
      const collection = useFirebase ? 'challenges' : 'challenges';
      const filters = [
        { field: 'isPublic', operator: '==', value: true }
      ];
      
      const challenges = await db.queryDocuments(collection, filters);
      return challenges as Challenge[];
    } catch (error) {
      console.error('Error getting public challenges:', error);
      throw error;
    }
  };

  /**
   * Get challenges created by a user
   */
  const getUserCreatedChallenges = async (userId: string): Promise<Challenge[]> => {
    try {
      const collection = useFirebase ? 'challenges' : 'challenges';
      const filters = [
        { field: 'creatorId', operator: '==', value: userId }
      ];
      
      const challenges = await db.queryDocuments(collection, filters);
      return challenges as Challenge[];
    } catch (error) {
      console.error(`Error getting challenges created by user ${userId}:`, error);
      throw error;
    }
  };

  /**
   * Get a challenge by ID
   */
  const getChallengeById = async (challengeId: string): Promise<Challenge | null> => {
    try {
      const collection = useFirebase ? 'challenges' : 'challenges';
      const challenge = await db.getDocument(collection, challengeId);
      return challenge as Challenge;
    } catch (error) {
      console.error(`Error getting challenge ${challengeId}:`, error);
      return null;
    }
  };

  /**
   * Update a challenge
   */
  const updateChallenge = async (challengeId: string, updates: Partial<Challenge>): Promise<void> => {
    try {
      const collection = useFirebase ? 'challenges' : 'challenges';
      const now = new Date().toISOString();
      
      await db.updateDocument(collection, challengeId, {
        ...updates,
        updatedAt: now
      });
    } catch (error) {
      console.error(`Error updating challenge ${challengeId}:`, error);
      throw error;
    }
  };

  /**
   * Delete a challenge
   */
  const deleteChallenge = async (challengeId: string): Promise<void> => {
    try {
      const collection = useFirebase ? 'challenges' : 'challenges';
      await db.deleteDocument(collection, challengeId);
      
      // Also delete all user challenge entries for this challenge
      // Note: In a real app, you might want to use a transaction or batch write for this
      try {
        const userChallengesCollection = useFirebase ? 'user_challenges' : 'user_challenges';
        const filters = [
          { field: 'challengeId', operator: '==', value: challengeId }
        ];
        
        const userChallenges = await db.queryDocuments(userChallengesCollection, filters);
        
        for (const userChallenge of userChallenges) {
          await db.deleteDocument(userChallengesCollection, userChallenge.id);
        }
      } catch (userChallengeError) {
        console.error(`Error cleaning up user challenges for challenge ${challengeId}:`, userChallengeError);
        // Continue with the deletion even if this part fails
      }
    } catch (error) {
      console.error(`Error deleting challenge ${challengeId}:`, error);
      throw error;
    }
  };

  /**
   * Join a challenge
   */
  const joinChallenge = async (userId: string, challengeId: string): Promise<void> => {
    try {
      // Check if user is already participating in this challenge
      const userChallengesCollection = useFirebase ? 'user_challenges' : 'user_challenges';
      const filters = [
        { field: 'userId', operator: '==', value: userId },
        { field: 'challengeId', operator: '==', value: challengeId }
      ];
      
      const existingEntries = await db.queryDocuments(userChallengesCollection, filters);
      
      if (existingEntries.length > 0) {
        // User is already participating in this challenge
        return;
      }
      
      // Get the challenge to check dates
      const challenge = await getChallengeById(challengeId);
      
      if (!challenge) {
        throw new Error(`Challenge ${challengeId} not found`);
      }
      
      const now = new Date();
      const endDate = new Date(challenge.endDate);
      
      if (now > endDate) {
        throw new Error('This challenge has already ended');
      }
      
      // Create user challenge entry
      const userChallenge: Omit<UserChallenge, 'id'> = {
        userId,
        challengeId,
        joinedAt: now.toISOString(),
        currentProgress: 0,
        completed: false,
        lastUpdatedAt: now.toISOString()
      };
      
      await db.createDocument(userChallengesCollection, userChallenge);
      
      // Increment participant count on the challenge
      const challengesCollection = useFirebase ? 'challenges' : 'challenges';
      await db.updateDocument(challengesCollection, challengeId, {
        participants: (challenge.participants || 0) + 1,
        updatedAt: now.toISOString()
      });
    } catch (error) {
      console.error(`Error joining challenge ${challengeId} for user ${userId}:`, error);
      throw error;
    }
  };

  /**
   * Leave a challenge
   */
  const leaveChallenge = async (userId: string, challengeId: string): Promise<void> => {
    try {
      // Find user challenge entry
      const userChallengesCollection = useFirebase ? 'user_challenges' : 'user_challenges';
      const filters = [
        { field: 'userId', operator: '==', value: userId },
        { field: 'challengeId', operator: '==', value: challengeId }
      ];
      
      const userChallenges = await db.queryDocuments(userChallengesCollection, filters);
      
      if (userChallenges.length === 0) {
        // User is not participating in this challenge
        return;
      }
      
      // Delete the user challenge entry
      await db.deleteDocument(userChallengesCollection, userChallenges[0].id);
      
      // Get the challenge and decrement participant count
      const challenge = await getChallengeById(challengeId);
      
      if (challenge) {
        const challengesCollection = useFirebase ? 'challenges' : 'challenges';
        await db.updateDocument(challengesCollection, challengeId, {
          participants: Math.max(0, (challenge.participants || 1) - 1),
          updatedAt: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error(`Error leaving challenge ${challengeId} for user ${userId}:`, error);
      throw error;
    }
  };

  /**
   * Get challenges a user is participating in
   */
  const getUserActiveChallenges = async (userId: string): Promise<Challenge[]> => {
    try {
      // Find user challenge entries
      const userChallengesCollection = useFirebase ? 'user_challenges' : 'user_challenges';
      const filters = [
        { field: 'userId', operator: '==', value: userId }
      ];
      
      const userChallenges = await db.queryDocuments(userChallengesCollection, filters);
      
      if (userChallenges.length === 0) {
        return [];
      }
      
      // Get the challenge IDs
      const challengeIds = userChallenges.map(uc => uc.challengeId);
      
      // Get the challenges
      const challengesCollection = useFirebase ? 'challenges' : 'challenges';
      const challenges: Challenge[] = [];
      
      for (const challengeId of challengeIds) {
        try {
          const challenge = await db.getDocument(challengesCollection, challengeId);
          
          if (challenge) {
            // Check if challenge is still active
            const now = new Date();
            const endDate = new Date(challenge.endDate);
            
            if (now <= endDate) {
              challenges.push(challenge as Challenge);
            }
          }
        } catch (challengeError) {
          console.error(`Error getting challenge ${challengeId}:`, challengeError);
          // Continue with other challenges
        }
      }
      
      return challenges;
    } catch (error) {
      console.error(`Error getting active challenges for user ${userId}:`, error);
      throw error;
    }
  };

  /**
   * Update challenge progress for a user
   */
  const updateChallengeProgress = async (
    userId: string, 
    challengeId: string, 
    progress: number
  ): Promise<void> => {
    try {
      // Find user challenge entry
      const userChallengesCollection = useFirebase ? 'user_challenges' : 'user_challenges';
      const filters = [
        { field: 'userId', operator: '==', value: userId },
        { field: 'challengeId', operator: '==', value: challengeId }
      ];
      
      const userChallenges = await db.queryDocuments(userChallengesCollection, filters);
      
      if (userChallenges.length === 0) {
        throw new Error(`User ${userId} is not participating in challenge ${challengeId}`);
      }
      
      const userChallenge = userChallenges[0] as UserChallenge;
      
      // Get the challenge to check the target
      const challenge = await getChallengeById(challengeId);
      
      if (!challenge) {
        throw new Error(`Challenge ${challengeId} not found`);
      }
      
      // Update progress
      const now = new Date();
      const completed = progress >= challenge.target;
      const completedAt = completed && !userChallenge.completed ? now.toISOString() : userChallenge.completedAt;
      
      await db.updateDocument(userChallengesCollection, userChallenge.id!, {
        currentProgress: progress,
        completed,
        completedAt,
        lastUpdatedAt: now.toISOString()
      });
    } catch (error) {
      console.error(`Error updating progress for challenge ${challengeId} and user ${userId}:`, error);
      throw error;
    }
  };

  /**
   * Get progress for all active challenges for a user
   */
  const getUserChallengeProgress = async (userId: string): Promise<ChallengeProgress[]> => {
    try {
      // Find user challenge entries
      const userChallengesCollection = useFirebase ? 'user_challenges' : 'user_challenges';
      const filters = [
        { field: 'userId', operator: '==', value: userId }
      ];
      
      const userChallenges = await db.queryDocuments(userChallengesCollection, filters) as UserChallenge[];
      
      if (userChallenges.length === 0) {
        return [];
      }
      
      // Get the challenges and build progress objects
      const challengesCollection = useFirebase ? 'challenges' : 'challenges';
      const progressList: ChallengeProgress[] = [];
      
      for (const userChallenge of userChallenges) {
        try {
          const challenge = await db.getDocument(challengesCollection, userChallenge.challengeId) as Challenge;
          
          if (challenge) {
            const percentage = Math.min(100, Math.round((userChallenge.currentProgress / challenge.target) * 100));
            
            progressList.push({
              challengeId: challenge.id!,
              title: challenge.title,
              current: userChallenge.currentProgress,
              target: challenge.target,
              unit: challenge.unit,
              percentage,
              completed: userChallenge.completed,
              startDate: challenge.startDate,
              endDate: challenge.endDate,
              type: challenge.type,
              description: challenge.description
            });
          }
        } catch (challengeError) {
          console.error(`Error getting challenge ${userChallenge.challengeId}:`, challengeError);
          // Continue with other challenges
        }
      }
      
      return progressList;
    } catch (error) {
      console.error(`Error getting challenge progress for user ${userId}:`, error);
      throw error;
    }
  };

  /**
   * Get all completed challenges for a user
   */
  const getUserCompletedChallenges = async (userId: string): Promise<ChallengeProgress[]> => {
    try {
      // Find user challenge entries that are completed
      const userChallengesCollection = useFirebase ? 'user_challenges' : 'user_challenges';
      const filters = [
        { field: 'userId', operator: '==', value: userId },
        { field: 'completed', operator: '==', value: true }
      ];
      
      const userChallenges = await db.queryDocuments(userChallengesCollection, filters) as UserChallenge[];
      
      if (userChallenges.length === 0) {
        return [];
      }
      
      // Get the challenges and build progress objects
      const challengesCollection = useFirebase ? 'challenges' : 'challenges';
      const progressList: ChallengeProgress[] = [];
      
      for (const userChallenge of userChallenges) {
        try {
          const challenge = await db.getDocument(challengesCollection, userChallenge.challengeId) as Challenge;
          
          if (challenge) {
            progressList.push({
              challengeId: challenge.id!,
              title: challenge.title,
              current: userChallenge.currentProgress,
              target: challenge.target,
              unit: challenge.unit,
              percentage: 100, // Completed challenges are at 100%
              completed: true,
              startDate: challenge.startDate,
              endDate: challenge.endDate,
              type: challenge.type,
              description: challenge.description
            });
          }
        } catch (challengeError) {
          console.error(`Error getting challenge ${userChallenge.challengeId}:`, challengeError);
          // Continue with other challenges
        }
      }
      
      return progressList;
    } catch (error) {
      console.error(`Error getting completed challenges for user ${userId}:`, error);
      throw error;
    }
  };

  return {
    createChallenge,
    getPublicChallenges,
    getUserCreatedChallenges,
    getChallengeById,
    updateChallenge,
    deleteChallenge,
    joinChallenge,
    leaveChallenge,
    getUserActiveChallenges,
    updateChallengeProgress,
    getUserChallengeProgress,
    getUserCompletedChallenges
  };
} 