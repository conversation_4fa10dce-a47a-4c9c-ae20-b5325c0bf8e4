/**
 * Classification service for food image analysis
 */

import { BoundingBox } from '../utils/volumeEstimator';

export interface ClassificationResult {
  label: string;
  confidence: number;
  boundingBox?: BoundingBox;
}

export async function classifyFoodImage(imageUri: string): Promise<ClassificationResult[]> {
  // In a real implementation, this would call the Google Vision API
  console.log('Classifying image:', imageUri);
  
  // Return mock data with multiple food items for testing
  return [
    {
      label: 'apple',
      confidence: 0.95,
      boundingBox: { x: 100, y: 150, width: 200, height: 200 }
    },
    {
      label: 'banana',
      confidence: 0.88,
      boundingBox: { x: 350, y: 200, width: 250, height: 150 }
    },
    // Include a third item for more complex meal testing
    {
      label: 'yogurt',
      confidence: 0.82,
      boundingBox: { x: 200, y: 400, width: 180, height: 180 }
    }
  ];
} 