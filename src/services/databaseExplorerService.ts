import { 
  getFirestore, 
  collection, 
  getDocs, 
  query, 
  limit, 
  where, 
  orderBy,
  QueryConstraint,
  DocumentData,
  startAfter,
  endBefore
} from 'firebase/firestore';
import { app } from '@/lib/firebase';

const db = getFirestore(app);

export interface TableData {
  rows: DocumentData[];
  columns: string[];
  totalCount?: number;
}

export interface QueryOptions {
  limitCount?: number;
  orderByField?: string;
  orderDirection?: 'asc' | 'desc';
  whereConditions?: {
    field: string;
    operator: any;
    value: any;
  }[];
  startAfterDoc?: any;
  endBeforeDoc?: any;
}

class DatabaseExplorerService {
  /**
   * Get all collections (tables) in Firestore
   */
  async getCollections(): Promise<string[]> {
    // Note: This is a limitation - Firestore doesn't provide a way to list collections
    // In production, you'd maintain a metadata collection
    return [
      'profiles',
      'users', 
      'water_intake',
      'food_logs',
      'recipes',
      'meal_plans',
      'challenges',
      'notifications',
      'settings'
    ];
  }

  /**
   * Load data from a collection with optional filters
   */
  async loadCollectionData(
    collectionName: string,
    options: QueryOptions = {}
  ): Promise<TableData> {
    try {
      const constraints: QueryConstraint[] = [];
      
      // Add limit
      if (options.limitCount) {
        constraints.push(limit(options.limitCount));
      }
      
      // Add ordering
      if (options.orderByField) {
        constraints.push(
          orderBy(
            options.orderByField, 
            options.orderDirection || 'asc'
          )
        );
      }
      
      // Add where conditions
      if (options.whereConditions) {
        options.whereConditions.forEach(condition => {
          constraints.push(
            where(condition.field, condition.operator, condition.value)
          );
        });
      }
      
      // Add pagination
      if (options.startAfterDoc) {
        constraints.push(startAfter(options.startAfterDoc));
      }
      if (options.endBeforeDoc) {
        constraints.push(endBefore(options.endBeforeDoc));
      }
      
      // Create and execute query
      const q = query(collection(db, collectionName), ...constraints);
      const snapshot = await getDocs(q);
      
      // Extract data
      const rows: DocumentData[] = [];
      const columnsSet = new Set<string>();
      
      snapshot.forEach(doc => {
        const data = { id: doc.id, ...doc.data() };
        rows.push(data);
        Object.keys(data).forEach(key => columnsSet.add(key));
      });
      
      return {
        rows,
        columns: Array.from(columnsSet),
        totalCount: snapshot.size
      };
    } catch (error: any) {
      console.error('Error loading collection data:', error);
      throw new Error(error.message || 'Failed to load data');
    }
  }

  /**
   * Get estimated document count for a collection
   */
  async getCollectionCount(collectionName: string): Promise<number> {
    try {
      // For large collections, you'd use a counter document
      // This is a simple implementation
      const snapshot = await getDocs(collection(db, collectionName));
      return snapshot.size;
    } catch (error) {
      console.error('Error getting collection count:', error);
      return 0;
    }
  }

  /**
   * Export collection data as JSON
   */
  async exportCollectionAsJson(collectionName: string): Promise<string> {
    try {
      const data = await this.loadCollectionData(collectionName, {
        limitCount: 1000 // Safety limit
      });
      
      return JSON.stringify(data.rows, null, 2);
    } catch (error: any) {
      throw new Error('Failed to export data: ' + error.message);
    }
  }

  /**
   * Export collection data as CSV
   */
  async exportCollectionAsCsv(collectionName: string): Promise<string> {
    try {
      const data = await this.loadCollectionData(collectionName, {
        limitCount: 1000
      });
      
      if (data.rows.length === 0) return '';
      
      // Header
      const headers = data.columns.join(',');
      
      // Rows
      const rows = data.rows.map(row => {
        return data.columns.map(col => {
          const value = row[col];
          // Escape values containing commas or quotes
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value ?? '';
        }).join(',');
      });
      
      return [headers, ...rows].join('\n');
    } catch (error: any) {
      throw new Error('Failed to export CSV: ' + error.message);
    }
  }

  /**
   * Analyze collection schema
   */
  async analyzeCollectionSchema(collectionName: string): Promise<any> {
    try {
      const data = await this.loadCollectionData(collectionName, {
        limitCount: 100 // Sample size
      });
      
      const schema: any = {};
      
      // Analyze field types from sample
      data.rows.forEach(row => {
        Object.entries(row).forEach(([key, value]) => {
          if (!schema[key]) {
            schema[key] = {
              types: new Set(),
              nullable: false,
              samples: []
            };
          }
          
          if (value === null || value === undefined) {
            schema[key].nullable = true;
          } else {
            schema[key].types.add(typeof value);
            if (schema[key].samples.length < 3) {
              schema[key].samples.push(value);
            }
          }
        });
      });
      
      // Convert sets to arrays
      Object.keys(schema).forEach(key => {
        schema[key].types = Array.from(schema[key].types);
      });
      
      return schema;
    } catch (error: any) {
      throw new Error('Failed to analyze schema: ' + error.message);
    }
  }
}

export const databaseExplorerService = new DatabaseExplorerService();