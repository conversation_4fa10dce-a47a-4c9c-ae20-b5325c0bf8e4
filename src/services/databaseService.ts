import 'react-native-url-polyfill/auto';
import { generateRecipeImage } from './imageGenerationService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SQLite from 'expo-sqlite';
import { Platform, Alert } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import { logConfigStatus } from '@/utils/config';
import { initializeApp } from 'firebase/app';
import { 
  getFirestore, 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  getDoc, 
  setDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  deleteDoc, 
  Timestamp,
  updateDoc,
  writeBatch,
  serverTimestamp,
  onSnapshot
} from 'firebase/firestore';
import { 
  getAuth, 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword, 
  signOut as firebaseSignOut, 
  updateProfile as updateUserProfile 
} from 'firebase/auth';
import { getStorage } from 'firebase/storage';
import { firebaseConfig } from '@/firebase.config';

// Helper to check if running on web
const isWeb = Platform.OS === 'web';

// Log configuration status at service initialization
logConfigStatus();

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);
const storage = getStorage(app);

console.log('Firebase initialized successfully');

// Mock data for offline/development use
export const mockProfiles = {
  'user-id-1': {
    id: 'user-id-1',
    email: '<EMAIL>',
    full_name: 'Test User',
    avatar_url: null,
    weight: 165,
    height: "5'10\"",
    daily_calorie_goal: 2000,
    created_at: new Date().toISOString()
  },
};

// Add connection status monitoring
let isConnected = true;
let hasShownOfflineAlert = false;

// Function to check if Firebase is reachable
export async function checkFirebaseConnection(): Promise<boolean> {
  try {
    // Try to use a collection that's both lightweight and has appropriate permissions
    // First try meal_recommendations which all authenticated users can read
    const recommendationsRef = collection(db, 'meal_recommendations');
    const q = query(recommendationsRef, limit(1));
    
    try {
      const snapshot = await getDocs(q);
      
      // Update connection status
      const wasOffline = !isConnected;
      isConnected = true;
      
      // If we're coming back online, show a notification
      if (wasOffline && isConnected) {
        console.log('Database connection restored');
        hasShownOfflineAlert = false;
      }
      
      return isConnected;
    } catch (permissionError) {
      // If we get permission issues, try a different approach - check auth state
      console.log('Permission error on first connection test, trying alternate method');
      
      // If the user is authenticated, we can consider Firebase connected
      if (auth.currentUser) {
        isConnected = true;
        return true;
      }
      
      // If no auth and permission error, we'll fall through to the offline logic
      throw permissionError;
    }
  } catch (error) {
    console.error('Error checking Firebase connection:', error);
    isConnected = false;
    
    // If we just went offline and haven't shown an alert yet, show one
    if (!isConnected && !hasShownOfflineAlert) {
      console.log('Database connection lost, using offline mode');
      hasShownOfflineAlert = true;
      
      if (Platform.OS !== 'web') {
        Alert.alert(
          'Offline Mode',
          'Unable to connect to the server. The app will continue to work with limited functionality.',
          [{ text: 'OK' }]
        );
      }
    }
    
    return false;
  }
}

// Initialize by checking connection on startup
setTimeout(checkFirebaseConnection, 2000);

// Validate the connection immediately
(async () => {
  try {
    console.log('Testing Firebase connection...');
    const startTime = Date.now();
    
    // Use meal_recommendations collection which all signed-in users can read
    const recommendationsRef = collection(db, 'meal_recommendations');
    const q = query(recommendationsRef, limit(1));
    
    try {
      await getDocs(q);
      const elapsed = Date.now() - startTime;
      console.log(`Firebase connection test successful (${elapsed}ms)`);
    } catch (permissionError) {
      console.log('Permission error on initial connection test, checking auth state');
      
      // If the user is authenticated, we can consider Firebase connected even with permission issues
      if (auth.currentUser) {
        console.log('User is authenticated, considering Firebase connected');
      } else {
        console.log('No authenticated user, waiting for auth state to change');
      }
    }
  } catch (err) {
    console.error('Firebase connection test exception:', err);
  }
})();

// Define database types for better type safety
export type Profile = {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  height?: number;
  weight?: number;
  daily_calorie_goal?: number;
  daily_water_goal?: number;
  activity_level?: string;
  created_at?: string;
  updated_at?: string;
};

export type DietaryPreference = {
  id: string;
  user_id: string;
  diet_type: string;
  allergies: string[];
  excluded_foods: string[];
  created_at?: string;
  updated_at?: string;
};

export type Meal = {
  id: string;
  user_id: string;
  name: string;
  date: string;
  time: string;
  total_calories: number;
  total_protein?: number;
  total_carbs?: number;
  total_fat?: number;
  image_url?: string;
  created_at?: string;
  updated_at?: string;
  items?: MealItem[];
};

export type MealItem = {
  id: string;
  meal_id: string;
  name: string;
  calories: number;
  protein?: number;
  carbs?: number;
  fat?: number;
  created_at?: string;
};

export type WaterIntake = {
  id: string;
  user_id: string;
  date: string;
  amount_ml: number;
  created_at?: string;
  updated_at?: string;
};

export type AlternativeRecipe = {
  id: string;
  user_id: string;
  original_meal_id?: string;
  name: string;
  description?: string;
  total_calories: number;
  total_protein?: number;
  total_carbs?: number;
  total_fat?: number;
  preparation_time?: string;
  cooking_time?: string;
  image_prompt?: string;
  image_url?: string;
  image_generation_status?: string;
  last_image_generation_attempt?: string;
  created_at?: string;
  ingredients?: RecipeIngredient[];
  instructions?: RecipeInstruction[];
  health_benefits?: RecipeHealthBenefit[];
};

export type RecipeIngredient = {
  id: string;
  recipe_id: string;
  name: string;
  amount: string;
  calories?: number;
  protein?: number;
  carbs?: number;
  fat?: number;
};

export type RecipeInstruction = {
  id: string;
  recipe_id: string;
  step_number: number;
  instruction: string;
};

export type RecipeHealthBenefit = {
  id: string;
  recipe_id: string;
  benefit: string;
};

// Type for the sync queue table
interface SyncQueueItem {
  id: string;
  table_name: string;
  operation: string;
  data: string;
  created_at: string;
}

// Define the conflict resolution strategies
export enum ConflictResolutionStrategy {
  LOCAL_WINS = 'local_wins',
  SERVER_WINS = 'server_wins',
  MANUAL = 'manual',
}

// Conflict type definition for better type safety
export interface DataConflict {
  id: string;
  tableName: string;
  localData: any;
  serverData: any;
  timestamp: string;
  resolved: boolean;
  strategy?: ConflictResolutionStrategy;
}

// Initialize SQLite database
let sqliteDb: SQLite.SQLiteDatabase;

// Initialize the SQLite database
export async function initDatabase() {
  // On web platform, bypass SQLite initialization to avoid browser compatibility issues
  if (isWeb) {
    console.log('Web platform detected, skipping SQLite initialization');
    return { success: true };
  }
  
  try {
    // Close any existing database connection first
    if (sqliteDb) {
      try {
        await sqliteDb.closeAsync();
        console.log('Closed existing database connection');
      } catch (closeError) {
        console.warn('Error closing existing database connection:', closeError);
      }
    }

    // Open a new connection
    sqliteDb = await SQLite.openDatabaseAsync('health_app.db');
    
    // Execute SQL to create necessary tables
    await sqliteDb.execAsync(`
      CREATE TABLE IF NOT EXISTS meals (
        id TEXT PRIMARY KEY,
        name TEXT,
        date TEXT,
        time TEXT,
        total_calories REAL,
        total_protein REAL,
        total_carbs REAL,
        total_fat REAL,
        image_url TEXT,
        synced INTEGER DEFAULT 0,
        created_at TEXT,
        user_id TEXT
      );
      
      CREATE TABLE IF NOT EXISTS meal_items (
        id TEXT PRIMARY KEY,
        meal_id TEXT,
        name TEXT,
        calories REAL,
        protein REAL,
        carbs REAL,
        fat REAL,
        synced INTEGER DEFAULT 0,
        FOREIGN KEY (meal_id) REFERENCES meals(id)
      );
      
      CREATE TABLE IF NOT EXISTS sync_queue (
        id TEXT PRIMARY KEY,
        table_name TEXT,
        operation TEXT,
        data TEXT,
        created_at TEXT
      );
    `);
    
    console.log('Database initialized successfully');
    return { success: true };
  } catch (error) {
    console.error('Error initializing database:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// Check network status
async function checkNetworkStatus() {
  const state = await NetInfo.fetch();
  return state.isConnected && state.isInternetReachable;
}

// Queue an operation for syncing later
async function queueSyncOperation(tableName: string, operation: string, data: any) {
  if (!sqliteDb) {
    await initDatabase();
  }
  
  const id = Date.now().toString();
  const timestamp = new Date().toISOString();
  
  try {
    const result = await sqliteDb.runAsync(
      `INSERT INTO sync_queue (id, table_name, operation, data, created_at) VALUES (?, ?, ?, ?, ?)`,
      [id, tableName, operation, JSON.stringify(data), timestamp]
    );
    return { success: true, id };
  } catch (error) {
    console.error('Error queueing sync operation:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// Helper to get the user ID
async function getUserId(): Promise<string | null> {
  try {
    const user = auth.currentUser;
    return user?.uid || null;
  } catch (error) {
    console.error('Error getting user ID:', error);
    return null;
  }
}

/**
 * Profile functions
 */
export async function getProfile() {
  console.debug('databaseService: Getting user profile');
  try {
    const user = auth.currentUser;
    if (!user) {
      console.debug('databaseService: No authenticated user found for profile fetch');
      return null;
    }
    
    const profileRef = doc(db, 'profiles', user.uid);
    const profileSnap = await getDoc(profileRef);
    
    if (!profileSnap.exists()) {
      console.error('Profile not found');
      return null;
    }
    
    console.debug('databaseService: Profile fetched successfully');
    return { id: profileSnap.id, ...profileSnap.data() };
  } catch (error) {
    console.error('Error in getProfile:', error);
    return null;
  }
}

export async function updateProfile(profile: Partial<Profile>) {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    const profileRef = doc(db, 'profiles', user.uid);
    await updateDoc(profileRef, {
      ...profile,
      updated_at: new Date().toISOString()
    });

    // Get the updated profile
    const updatedProfileSnap = await getDoc(profileRef);
    
    if (!updatedProfileSnap.exists()) {
      return { success: false, error: 'Failed to retrieve updated profile' };
    }

    return { 
      success: true, 
      data: { id: updatedProfileSnap.id, ...updatedProfileSnap.data() } 
    };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in updateProfile:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Dietary preferences functions
 */
export async function getDietaryPreferences() {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      console.log('No authenticated user found');
      return null;
    }

    // Query dietary preferences for current user
    const preferencesRef = collection(db, 'dietary_preferences');
    const q = query(preferencesRef, where('user_id', '==', user.uid));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return null;
    }
    
    // Return the first match
    const prefDoc = querySnapshot.docs[0];
    return { id: prefDoc.id, ...prefDoc.data() };
  } catch (err) {
    console.error('Unexpected error in getDietaryPreferences:', err);
    return null;
  }
}

export async function updateDietaryPreferences(preferences: Partial<DietaryPreference>) {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    const userId = user.uid;
    
    // Check if preferences exist
    const preferencesRef = collection(db, 'dietary_preferences');
    const q = query(preferencesRef, where('user_id', '==', userId));
    const querySnapshot = await getDocs(q);
    
    let result;
    
    if (!querySnapshot.empty) {
      // Update existing preferences
      const prefDoc = querySnapshot.docs[0];
      const prefRef = doc(db, 'dietary_preferences', prefDoc.id);
      
      await updateDoc(prefRef, {
        ...preferences,
        updated_at: new Date().toISOString(),
      });
      
      // Get updated document
      const updatedPrefSnap = await getDoc(prefRef);
      result = { id: updatedPrefSnap.id, ...updatedPrefSnap.data() };
    } else {
      // Insert new preferences
      const newPrefRef = await addDoc(collection(db, 'dietary_preferences'), {
        ...preferences,
        user_id: userId,
        created_at: new Date().toISOString()
      });
      
      const newPrefSnap = await getDoc(newPrefRef);
      result = { id: newPrefSnap.id, ...newPrefSnap.data() };
    }

    return { success: true, data: result };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in updateDietaryPreferences:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Auth functions
 */
export async function signUp(email: string, password: string) {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    return { success: true, data: userCredential };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in signUp:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

export async function signIn(email: string, password: string) {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return { success: true, data: userCredential };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in signIn:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

export async function signOut() {
  try {
    await firebaseSignOut(auth);
    return { success: true };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in signOut:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

export async function getCurrentUser() {
  try {
    const user = auth.currentUser;
    return user;
  } catch (err) {
    console.error('Unexpected error in getCurrentUser:', err);
    return null;
  }
}

// Add this type assertion to safeFirebaseOperation to fix type issues
export async function safeFirebaseOperation<T = any>(operation: () => Promise<T>): Promise<T> {
  try {
    const result = await operation();
    return result as T;
  } catch (error: any) {
    console.error(`Firebase operation error: ${error.message}`);
    throw error;
  }
}

/**
 * Meal functions
 */
export async function getMeals(date: string) {
  console.debug('databaseService: Getting meals for date:', date);
  try {
    const user = auth.currentUser;
    if (!user) {
      console.debug('databaseService: No authenticated user found for meal fetch');
      return [];
    }
    
    // Cache key for this specific query
    const cacheKey = `meals_${user.uid}_${date}`;
    
    // Try to get from cache first
    try {
      const cachedData = await AsyncStorage.getItem(cacheKey);
      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        const cacheTimestamp = parsedData.timestamp || 0;
        const now = Date.now();
        
        // Use cache if it's less than 5 minutes old
        if (now - cacheTimestamp < 5 * 60 * 1000) {
          console.debug('databaseService: Using cached meals data');
          return parsedData.meals;
        }
      }
    } catch (cacheError) {
      console.warn('databaseService: Cache error:', cacheError);
    }
    
    // Query meals with optimized patterns - set a reasonable limit and order by time
    const mealsRef = collection(db, 'meals');
    const q = query(
      mealsRef, 
      where('user_id', '==', user.uid),
      where('date', '==', date),
      orderBy('time', 'asc'),
      limit(20) // Limit to 20 meals per day which is reasonable
    );
    
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      console.debug('databaseService: No meals found for date:', date);
      return [];
    }
    
    const meals: Meal[] = [];
    querySnapshot.forEach((doc) => {
      meals.push({ id: doc.id, ...doc.data() } as Meal);
    });
    
    console.debug(`databaseService: Found ${meals.length} meals for date:`, date);
    
    // Cache the results
    try {
      await AsyncStorage.setItem(cacheKey, JSON.stringify({
        meals,
        timestamp: Date.now()
      }));
    } catch (cacheError) {
      console.warn('databaseService: Error caching meals data:', cacheError);
    }
    
    return meals;
  } catch (error) {
    console.error('Error in getMeals:', error);
    return [];
  }
}

export async function getMealById(mealId: string) {
  console.debug('databaseService: Getting meal by ID:', mealId);
    
  // Use the new executeWithRetry helper
  const result = await executeWithRetry(
    async () => {
      // Check user authentication
      const user = auth.currentUser;
    if (!user) {
        throw new Error('No authenticated user found');
    }

    // Get the meal document
    const mealRef = doc(db, 'meals', mealId);
    const mealSnap = await getDoc(mealRef);
    
      if (!mealSnap.exists()) {
        throw new Error(`Meal with ID ${mealId} not found`);
      }
      
      const mealData = { id: mealSnap.id, ...mealSnap.data() } as Meal;
      
      // Check if the meal belongs to the current user
      if (mealData.user_id !== user.uid) {
        throw new Error('You do not have permission to access this meal');
      }
      
      // Get meal items
    const itemsRef = collection(db, 'meal_items');
    const q = query(itemsRef, where('meal_id', '==', mealId));
      const itemsSnapshot = await getDocs(q);
      
      const items: MealItem[] = [];
      itemsSnapshot.forEach(doc => {
        items.push({ id: doc.id, ...doc.data() } as MealItem);
      });
      
      // Combine meal with items
      mealData.items = items;
      
      return mealData;
    },
    3, // Max 3 retries
    `Get meal by ID: ${mealId}`
  );
  
  if (result.error) {
    console.error('databaseService: Error getting meal by ID:', result.error);
    return null;
  }
  
  return result.data;
}

/**
 * Utility function to perform batch operations on Firestore
 * @param operations Array of operations to perform in a batch
 */
export async function performBatchOperations(
  operations: {
    type: 'add' | 'update' | 'delete' | 'set';
    collection: string;
    docId?: string;
    data?: any;
    options?: any;
  }[]
): Promise<{ success: boolean; ids?: string[]; error?: string }> {
  try {
    const user = auth.currentUser;
    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }
    
    const batch = writeBatch(db);
    const newIds: string[] = [];
    
    // Process each operation
    for (const op of operations) {
      switch (op.type) {
        case 'add': {
          // For add operations, we need to create a new document reference
          const newDocRef = doc(collection(db, op.collection));
          
          // Add document with user_id and timestamps
          batch.set(newDocRef, {
            ...op.data,
        user_id: user.uid,
            created_at: serverTimestamp(),
            updated_at: serverTimestamp()
          });
          
          newIds.push(newDocRef.id);
          break;
        }
        
        case 'update': {
          if (!op.docId) {
            return { success: false, error: 'Document ID required for update operation' };
          }
          
          const docRef = doc(db, op.collection, op.docId);
          
          // Update with timestamp
          batch.update(docRef, {
            ...op.data,
            updated_at: serverTimestamp()
          });
          break;
        }
        
        case 'set': {
          if (!op.docId) {
            return { success: false, error: 'Document ID required for set operation' };
          }
          
          const docRef = doc(db, op.collection, op.docId);
          
          // Set with options (like merge)
          batch.set(docRef, {
            ...op.data,
            updated_at: serverTimestamp()
          }, op.options || {});
          break;
        }
        
        case 'delete': {
          if (!op.docId) {
            return { success: false, error: 'Document ID required for delete operation' };
          }
          
          const docRef = doc(db, op.collection, op.docId);
          batch.delete(docRef);
          break;
        }
        
        default:
          return { success: false, error: `Unknown operation type: ${op.type}` };
      }
    }
    
    // Execute the batch
    await batch.commit();
    
    return { success: true, ids: newIds };
  } catch (error) {
    console.error('Error in batch operations:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error in batch operation'
    };
  }
}

/**
 * Create a meal with items in a single batch operation
 */
export async function createMeal(
  meal: Omit<Meal, 'id' | 'user_id' | 'created_at' | 'updated_at'>, 
  items: Omit<MealItem, 'id' | 'meal_id' | 'created_at'>[]
) {
  console.debug('databaseService: Creating meal with items');
  
  // Use a batch operation to create both the meal and its items atomically
  const operations = [
    {
      type: 'add' as const,
      collection: 'meals',
      data: meal
    }
  ];
  
  // Execute the batch operation
  const result = await performBatchOperations(operations);
  
  if (!result.success || !result.ids || result.ids.length === 0) {
    console.error('databaseService: Failed to create meal:', result.error);
    return { success: false, error: result.error || 'Failed to create meal' };
  }
  
  const mealId = result.ids[0];
  console.debug('databaseService: Meal created with ID:', mealId);
  
  // Now add the items in a separate batch operation
  if (items.length > 0) {
    const itemOperations = items.map(item => ({
      type: 'add' as const,
      collection: 'meal_items',
      data: {
        ...item,
        meal_id: mealId
      }
    }));
    
    const itemsResult = await performBatchOperations(itemOperations);
    
    if (!itemsResult.success) {
      console.error('databaseService: Failed to create meal items:', itemsResult.error);
      
      // Even though meal creation succeeded, items failed - consider this a partial success
      return { 
        success: true, 
        id: mealId, 
        warning: 'Meal was created but some items may be missing'
      };
    }
  }
  
  return { success: true, id: mealId };
}

export async function updateMeal(mealId: string, mealUpdates: Partial<Meal>, newItems?: Omit<MealItem, 'id' | 'meal_id' | 'created_at'>[]) {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check if meal belongs to user
    const mealRef = doc(db, 'meals', mealId);
    const mealSnap = await getDoc(mealRef);
    
    if (!mealSnap.exists() || mealSnap.data().user_id !== user.uid) {
      return { success: false, error: 'Meal not found or access denied' };
    }

    // Update meal data
    await updateDoc(mealRef, {
      ...mealUpdates,
      updated_at: new Date().toISOString(),
    });

    // If new items are provided, replace all existing items
    if (newItems && newItems.length > 0) {
      // Create a batch for atomic operations
      const batch = writeBatch(db);
      
      // First get existing items to delete them
      const itemsRef = collection(db, 'meal_items');
      const q = query(itemsRef, where('meal_id', '==', mealId));
      const existingItems = await getDocs(q);
      
      // Delete all existing items
      existingItems.forEach(itemDoc => {
        batch.delete(doc(db, 'meal_items', itemDoc.id));
      });
      
      // Add new items
      for (const item of newItems) {
        const newItemRef = doc(collection(db, 'meal_items'));
        batch.set(newItemRef, {
          ...item,
          meal_id: mealId,
          created_at: new Date().toISOString()
        });
      }
      
      // Commit all changes
      await batch.commit();
    }

    // Fetch the updated meal with its items
    const updatedMeal = await getMealById(mealId);
    
    if (!updatedMeal) {
      return { success: true, data: { id: mealId, ...mealUpdates } as any }; // Return partial success
    }

    return { success: true, data: updatedMeal };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in updateMeal:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

export async function deleteMeal(mealId: string) {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check if meal belongs to user
    const mealRef = doc(db, 'meals', mealId);
    const mealSnap = await getDoc(mealRef);
    
    if (!mealSnap.exists() || mealSnap.data().user_id !== user.uid) {
      return { success: false, error: 'Meal not found or access denied' };
    }
    
    // Create a batch to delete meal and its items atomically
    const batch = writeBatch(db);
    
    // Delete meal
    batch.delete(mealRef);
    
    // Delete all related meal items
    const itemsRef = collection(db, 'meal_items');
    const q = query(itemsRef, where('meal_id', '==', mealId));
    const itemsSnap = await getDocs(q);
    
    itemsSnap.forEach(itemDoc => {
      batch.delete(doc(db, 'meal_items', itemDoc.id));
    });
    
    // Commit batch
    await batch.commit();

    return { success: true };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in deleteMeal:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Water Intake functions
 */
export async function getWaterIntake() {
  console.debug('databaseService: Getting water intake');
  try {
    const user = auth.currentUser;
    if (!user) {
      console.debug('databaseService: No authenticated user found for water intake fetch');
      return null;
    }
    
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    
    // Cache key for this specific query
    const cacheKey = `water_${user.uid}_${today}`;
    
    // Try to get from cache first
    try {
      const cachedData = await AsyncStorage.getItem(cacheKey);
      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        const cacheTimestamp = parsedData.timestamp || 0;
        const now = Date.now();
        
        // Use cache if it's less than 5 minutes old
        if (now - cacheTimestamp < 5 * 60 * 1000) {
          console.debug('databaseService: Using cached water intake data');
          return parsedData.waterIntake;
        }
      }
    } catch (cacheError) {
      console.warn('databaseService: Cache error:', cacheError);
    }
    
    // Query with limit(1) to only get the most recent record
    const waterRef = collection(db, 'water_intake');
    const q = query(
      waterRef,
      where('user_id', '==', user.uid),
      where('date', '==', today),
      orderBy('created_at', 'desc'),
      limit(1)
    );
    
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      console.debug('databaseService: No water intake found for today');
      return { amount_ml: 0 };
    }
    
    // Return the first matching record
    const waterDoc = querySnapshot.docs[0];
    const waterIntake = { id: waterDoc.id, ...waterDoc.data() };
    console.debug('databaseService: Water intake fetched successfully');
    
    // Cache the results
    try {
      await AsyncStorage.setItem(cacheKey, JSON.stringify({
        waterIntake,
        timestamp: Date.now()
      }));
    } catch (cacheError) {
      console.warn('databaseService: Error caching water intake data:', cacheError);
    }
    
    return waterIntake;
  } catch (error) {
    console.error('Error in getWaterIntake:', error);
    return { amount_ml: 0 };
  }
}

export async function updateWaterIntake(amount_ml: number, date?: string) {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    const userId = user.uid;
    const recordDate = date || new Date().toISOString().split('T')[0];
    
    // Check if a record exists for this day
    const waterRef = collection(db, 'water_intake');
    const q = query(
      waterRef,
      where('user_id', '==', userId),
      where('date', '==', recordDate)
    );
    
    const querySnapshot = await getDocs(q);
    
    let result;
    
    if (!querySnapshot.empty) {
      // Update existing record
      const waterDoc = querySnapshot.docs[0];
      const waterDocRef = doc(db, 'water_intake', waterDoc.id);
      
      await updateDoc(waterDocRef, {
        amount_ml: amount_ml,
        updated_at: new Date().toISOString(),
      });
      
      // Get updated document
      const updatedWaterSnap = await getDoc(waterDocRef);
      result = { id: updatedWaterSnap.id, ...updatedWaterSnap.data() };
    } else {
      // Insert new record
      const newWaterRef = await addDoc(collection(db, 'water_intake'), {
        user_id: userId,
        date: recordDate,
        amount_ml: amount_ml,
        created_at: new Date().toISOString()
      });
      
      const newWaterSnap = await getDoc(newWaterRef);
      result = { id: newWaterSnap.id, ...newWaterSnap.data() };
    }

    return { success: true, data: result };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in updateWaterIntake:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Alternative recipe functions
 */
export async function getAlternativeRecipes(originalMealId?: string) {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      console.log('No authenticated user found');
      return [];
    }

    // Start building the query
    const recipesRef = collection(db, 'alternative_recipes');
    let q = query(recipesRef, where('user_id', '==', user.uid), orderBy('created_at', 'desc'));
    
    // Add filter by originalMealId if provided
    if (originalMealId) {
      q = query(recipesRef, 
        where('user_id', '==', user.uid), 
        where('original_meal_id', '==', originalMealId),
        orderBy('created_at', 'desc')
      );
    }
    
    const querySnapshot = await getDocs(q);
    const recipes: AlternativeRecipe[] = [];
    
    // For each recipe, gather all its related data
    for (const recipeDoc of querySnapshot.docs) {
      const recipeData = recipeDoc.data();
      
      // Get ingredients
      const ingredientsRef = collection(db, 'recipe_ingredients');
      const ingredientsQuery = query(ingredientsRef, where('recipe_id', '==', recipeDoc.id));
      const ingredientsSnap = await getDocs(ingredientsQuery);
      const ingredients = ingredientsSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as RecipeIngredient));
      
      // Get instructions
      const instructionsRef = collection(db, 'recipe_instructions');
      const instructionsQuery = query(instructionsRef, where('recipe_id', '==', recipeDoc.id));
      const instructionsSnap = await getDocs(instructionsQuery);
      const instructions = instructionsSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as RecipeInstruction));
      
      // Sort instructions by step number
      instructions.sort((a: RecipeInstruction, b: RecipeInstruction) => a.step_number - b.step_number);
      
      // Get health benefits
      const benefitsRef = collection(db, 'recipe_health_benefits');
      const benefitsQuery = query(benefitsRef, where('recipe_id', '==', recipeDoc.id));
      const benefitsSnap = await getDocs(benefitsQuery);
      const healthBenefits = benefitsSnap.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as RecipeHealthBenefit));
      
      // Combine all data
      recipes.push({
        id: recipeDoc.id,
        ...recipeData,
        ingredients,
        instructions,
        health_benefits: healthBenefits
      } as AlternativeRecipe);
    }
    
    return recipes;
  } catch (err) {
    console.error('Unexpected error in getAlternativeRecipes:', err);
    return [];
  }
}

/**
 * Creates an alternative recipe in the database
 * @param recipe Recipe main data
 * @param ingredients Array of ingredient objects
 * @param instructions Array of instruction objects
 * @param healthBenefits Array of health benefit objects
 * @returns Promise with success status and optional created recipe ID
 */
export async function createAlternativeRecipe(
  recipe: Omit<AlternativeRecipe, 'id' | 'created_at' | 'user_id'>,
  ingredients: Omit<RecipeIngredient, 'id' | 'recipe_id'>[],
  instructions: Omit<RecipeInstruction, 'id' | 'recipe_id'>[],
  healthBenefits: Omit<RecipeHealthBenefit, 'id' | 'recipe_id'>[]
): Promise<{ success: boolean; data?: { id: string; image_prompt?: string }; error?: string }> {
  try {
    // Get the current user
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    // Create a batch for atomic operations
    const batch = writeBatch(db);
    
    // Add the recipe document
    const recipeRef = doc(collection(db, 'alternative_recipes'));
    const recipeData = {
      ...recipe,
      user_id: user.uid,
      created_at: new Date().toISOString(),
      image_generation_status: 'pending'
    };
    
    batch.set(recipeRef, recipeData);
    
    // Add ingredients
    if (ingredients.length > 0) {
      for (const ingredient of ingredients) {
        const ingredientRef = doc(collection(db, 'recipe_ingredients'));
        batch.set(ingredientRef, {
          ...ingredient,
          recipe_id: recipeRef.id,
          created_at: new Date().toISOString()
        });
      }
    }
    
    // Add instructions
    if (instructions.length > 0) {
      for (const instruction of instructions) {
        const instructionRef = doc(collection(db, 'recipe_instructions'));
        batch.set(instructionRef, {
          ...instruction,
          recipe_id: recipeRef.id,
          created_at: new Date().toISOString()
        });
      }
    }
    
    // Add health benefits
    if (healthBenefits.length > 0) {
      for (const benefit of healthBenefits) {
        const benefitRef = doc(collection(db, 'recipe_health_benefits'));
        batch.set(benefitRef, {
          ...benefit,
          recipe_id: recipeRef.id,
          created_at: new Date().toISOString()
        });
      }
    }
    
    // Commit all changes
    await batch.commit();
    
    // Generate image immediately if an image prompt was provided
    if (recipe.image_prompt) {
      try {
        // Fire and forget image generation - don't wait for it to complete
        console.log('Starting image generation for recipe:', recipeRef.id);
        generateRecipeImage(recipeRef.id, [recipe.image_prompt])
          .then(result => {
            const typedResult = result as { success?: boolean; error?: string };
            if (typedResult && typedResult.success) {
              console.log('Recipe image generated successfully');
            } else {
              console.error('Recipe image generation failed:', typedResult?.error || 'Unknown error');
            }
          })
          .catch(err => console.error('Error in image generation:', err));
      } catch (imageError) {
        // Log error but don't fail the whole operation
        console.error('Error starting image generation:', imageError);
      }
    }
    
    return {
      success: true,
      data: { 
        id: recipeRef.id,
        image_prompt: recipe.image_prompt 
      }
    };
  } catch (error) {
    console.error('Error creating alternative recipe:', error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error creating recipe'
    };
  }
}

export async function updateRecipeImage(recipeId: string, imageUrl: string) {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check if recipe belongs to user
    const recipeRef = doc(db, 'alternative_recipes', recipeId);
    const recipeSnap = await getDoc(recipeRef);
    
    if (!recipeSnap.exists() || recipeSnap.data().user_id !== user.uid) {
      return { success: false, error: 'Recipe not found or access denied' };
    }
    
    // Update recipe with new image info
    await updateDoc(recipeRef, { 
      image_url: imageUrl,
      image_generation_status: 'completed',
      last_image_generation_attempt: new Date().toISOString()
    });

    return { success: true };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in updateRecipeImage:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

export async function regenerateRecipeImage(recipeId: string) {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Get the recipe to check if we have an image prompt
    const recipeRef = doc(db, 'alternative_recipes', recipeId);
    const recipeSnap = await getDoc(recipeRef);
    
    if (!recipeSnap.exists() || recipeSnap.data().user_id !== user.uid) {
      return { success: false, error: 'Recipe not found or access denied' };
    }
    
    const recipeData = recipeSnap.data();
    if (!recipeData.image_prompt) {
      return { success: false, error: 'No image prompt found' };
    }

    try {
      // Generate new image
      const result = await generateRecipeImage(recipeId, recipeData.image_prompt);
      return result;
    } catch (error) {
      console.error('Error regenerating recipe image:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error regenerating image' 
      };
    }
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in regenerateRecipeImage:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

export async function deleteAlternativeRecipe(recipeId: string) {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check if recipe belongs to user
    const recipeRef = doc(db, 'alternative_recipes', recipeId);
    const recipeSnap = await getDoc(recipeRef);
    
    if (!recipeSnap.exists() || recipeSnap.data().user_id !== user.uid) {
      return { success: false, error: 'Recipe not found or access denied' };
    }
    
    // Create a batch for atomic deletion of all related documents
    const batch = writeBatch(db);
    
    // Delete recipe
    batch.delete(recipeRef);
    
    // Delete ingredients
    const ingredientsRef = collection(db, 'recipe_ingredients');
    const ingredientsQuery = query(ingredientsRef, where('recipe_id', '==', recipeId));
    const ingredientsSnap = await getDocs(ingredientsQuery);
    
    ingredientsSnap.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    // Delete instructions
    const instructionsRef = collection(db, 'recipe_instructions');
    const instructionsQuery = query(instructionsRef, where('recipe_id', '==', recipeId));
    const instructionsSnap = await getDocs(instructionsQuery);
    
    instructionsSnap.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    // Delete health benefits
    const benefitsRef = collection(db, 'recipe_health_benefits');
    const benefitsQuery = query(benefitsRef, where('recipe_id', '==', recipeId));
    const benefitsSnap = await getDocs(benefitsQuery);
    
    benefitsSnap.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    // Commit all deletions
    await batch.commit();

    return { success: true };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in deleteAlternativeRecipe:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

export type MealRecommendation = {
  id: string;
  user_id?: string;
  name: string;
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  image_url: string;
  description?: string;
  ingredients?: string[];
  preparation_time?: string;
  is_favorite?: boolean;
  created_at?: string;
  updated_at?: string;
};

// Type declaration for mindfulness tips
export type MindfulnessTip = {
  id: string;
  title: string;
  message: string;
  icon: string;
  category: string;
  gradient_start: string;
  gradient_end: string;
  created_at?: string;
};

// Type declaration for user mindfulness session
export type MindfulnessSession = {
  id: string;
  user_id: string;
  tip_id?: string;
  session_date: string;
  duration_seconds?: number;
  completed: boolean;
  notes?: string;
  created_at?: string;
};

// Type declaration for mindfulness preferences
export type MindfulnessPreferences = {
  id?: string;
  user_id: string;
  reminder_frequency: string;
  preferred_categories: string[];
  notification_enabled: boolean;
  reminder_time: string;
  created_at?: string;
  updated_at?: string;
};

// Default fallback recommendations
export const FALLBACK_RECOMMENDATIONS: MealRecommendation[] = [
  {
    id: 'breakfast-1',
    name: 'Greek Yogurt with Berries',
    type: 'breakfast' as 'breakfast' | 'lunch' | 'dinner' | 'snack',
    calories: 320,
    protein: 18,
    carbs: 42,
    fat: 8,
    image_url: 'https://images.pexels.com/photos/1099680/pexels-photo-1099680.jpeg?auto=compress&cs=tinysrgb&w=600',
  },
  {
    id: 'lunch-1',
    name: 'Grilled Chicken Salad',
    type: 'lunch' as 'breakfast' | 'lunch' | 'dinner' | 'snack',
    calories: 450,
    protein: 35,
    carbs: 25,
    fat: 22,
    image_url: 'https://images.pexels.com/photos/1410235/pexels-photo-1410235.jpeg?auto=compress&cs=tinysrgb&w=600',
  },
  {
    id: 'dinner-1',
    name: 'Salmon with Roasted Vegetables',
    type: 'dinner' as 'breakfast' | 'lunch' | 'dinner' | 'snack',
    calories: 520,
    protein: 38,
    carbs: 30,
    fat: 25,
    image_url: 'https://images.pexels.com/photos/3763847/pexels-photo-3763847.jpeg?auto=compress&cs=tinysrgb&w=600',
  },
  {
    id: 'snack-1',
    name: 'Apple with Almond Butter',
    type: 'snack' as 'breakfast' | 'lunch' | 'dinner' | 'snack',
    calories: 210,
    protein: 7,
    carbs: 25,
    fat: 12,
    image_url: 'https://images.pexels.com/photos/2105104/pexels-photo-2105104.jpeg?auto=compress&cs=tinysrgb&w=600',
  },
];

/**
 * Meal recommendation functions
 */
export async function getMealRecommendations(mealType?: string, maxItems: number = 10, signal?: AbortSignal) {
  try {
    // Check authentication first
    const user = auth.currentUser;
    
    if (!user) {
      console.log('No authenticated user found for meal recommendations');
      // Return fallback data instead of empty array when no user is found
      return mealType 
        ? FALLBACK_RECOMMENDATIONS.filter(r => r.type === mealType)
        : FALLBACK_RECOMMENDATIONS;
    }

    // For web platform, first check cached recommendations
    if (isWeb) {
      try {
        const cachedData = await AsyncStorage.getItem(`meal_recommendations_${mealType || 'all'}`);
        if (cachedData) {
          const parsedData = JSON.parse(cachedData);
          const timestamp = parsedData.timestamp || 0;
          const recommendations = parsedData.data || [];
          
          // Use cache if it's less than 30 minutes old
          if (recommendations.length > 0 && (Date.now() - timestamp) < 30 * 60 * 1000) {
            console.log(`Using cached ${mealType || 'all'} recommendations`);
            return recommendations;
          }
        }
      } catch (cacheError) {
        console.warn('Error accessing recommendation cache:', cacheError);
      }
    }

    // Ensure we have a valid database reference
    if (!db) {
      return mealType 
        ? FALLBACK_RECOMMENDATIONS.filter(r => r.type === mealType)
        : FALLBACK_RECOMMENDATIONS;
    }

    // Start building the query
    let recommendations: MealRecommendation[] = [];
    try {
      const recommendationsRef = collection(db, 'meal_recommendations');
      let q = query(recommendationsRef, limit(maxItems));
      
      // Apply meal type filter if provided
      if (mealType) {
        q = query(recommendationsRef, where('type', '==', mealType), limit(maxItems));
      }
      
      // Execute the query with timeout protection
      const queryPromise = getDocs(q);
      const timeoutPromise = new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error('Query timeout')), 5000)
      );
      
      const querySnapshot = await Promise.race([queryPromise, timeoutPromise]);
      recommendations = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as MealRecommendation[];
      
      console.log(`Fetched ${recommendations.length} meal recommendations for type: ${mealType || 'all'}`);
    } catch (queryError: unknown) {
      console.error('Error querying meal recommendations:', queryError);
      // If we got a permission error, use fallback data immediately
      if (queryError instanceof Error && 
          (queryError.message.includes('permission') || 
           queryError.message.includes('Missing or insufficient permissions'))) {
        console.log('Permission error when fetching recommendations, using fallbacks');
        return mealType 
          ? FALLBACK_RECOMMENDATIONS.filter(r => r.type === mealType)
          : FALLBACK_RECOMMENDATIONS;
      }
      // Otherwise continue with empty recommendations array and try dietary filtering later
    }
    
    // Continue only if we have recommendations
    if (recommendations.length === 0) {
      console.warn('No meal recommendations found, using fallbacks');
      return mealType 
        ? FALLBACK_RECOMMENDATIONS.filter(r => r.type === mealType)
        : FALLBACK_RECOMMENDATIONS;
    }
    
    // Get dietary preferences to filter appropriate recommendations
    let dietaryPreferences: Record<string, any> | null = null;
    try {
      const preferencesRef = collection(db, 'dietary_preferences');
      const preferencesQuery = query(preferencesRef, where('user_id', '==', user.uid));
      
      try {
        const preferencesSnap = await getDocs(preferencesQuery);
        
        if (!preferencesSnap.empty) {
          dietaryPreferences = preferencesSnap.docs[0].data() as Record<string, any>;
          console.log('Found dietary preferences for filtering');
        } else {
          console.log('No dietary preferences found for this user');
        }
      } catch (permissionError) {
        // If we get a permission error on dietary preferences, just continue without them
        console.warn('Permission error when fetching dietary preferences, continuing without filtering');
      }
    } catch (prefError) {
      console.error('Error fetching dietary preferences:', prefError);
      // Continue without dietary preferences
    }
    
    // If we have dietary preferences, filter the results post-query
    // But only if we have recommendations to filter
    let filteredRecommendations = recommendations;
    
    if (dietaryPreferences && recommendations.length > 0) {
      // Filter out meals based on dietary preferences
      try {
        filteredRecommendations = recommendations.filter(meal => {
          // Type assertion to avoid TypeScript errors
          const typedMeal = meal as any;
          
          // Skip meals that don't match diet type (vegetarian, vegan, etc.)
          if (dietaryPreferences?.diet_type && typedMeal.diet_type && 
              dietaryPreferences.diet_type !== typedMeal.diet_type) {
            return false;
          }
          
          // Skip meals with allergens
          if (dietaryPreferences?.allergies && dietaryPreferences.allergies.length > 0 && 
              typedMeal.allergens) {
            if (typedMeal.allergens.some((allergen: string) => 
              dietaryPreferences?.allergies.includes(allergen))) {
              return false;
            }
          }
          
          // Skip meals with excluded foods
          if (dietaryPreferences?.excluded_foods && dietaryPreferences.excluded_foods.length > 0 && 
              typedMeal.ingredients) {
            if (typedMeal.ingredients.some((ingredient: string) => 
              dietaryPreferences?.excluded_foods.includes(ingredient))) {
              return false;
            }
          }
          
          return true;
        });
        
        console.log(`Filtered recommendations from ${recommendations.length} to ${filteredRecommendations.length} based on dietary preferences`);
        
        // If filtering removed all recommendations, use the unfiltered list
        if (filteredRecommendations.length === 0 && recommendations.length > 0) {
          console.log('Dietary filtering removed all recommendations, using unfiltered list');
          filteredRecommendations = recommendations;
        }
      } catch (filterError) {
        console.error('Error during dietary filtering:', filterError);
        // Use unfiltered recommendations if filtering fails
        filteredRecommendations = recommendations;
      }
    }
    
    // Check if we have recommendations after all processing
    if (filteredRecommendations.length === 0) {
      console.warn('No meal recommendations returned from the database, using fallbacks');
      filteredRecommendations = mealType 
        ? FALLBACK_RECOMMENDATIONS.filter(r => r.type === mealType)
        : FALLBACK_RECOMMENDATIONS;
    }
    
    // Cache the results
    if (isWeb) {
      try {
        await AsyncStorage.setItem(
          `meal_recommendations_${mealType || 'all'}`, 
          JSON.stringify({
            data: filteredRecommendations,
            timestamp: Date.now()
          })
        );
        console.log(`Cached ${filteredRecommendations.length} recommendations for future use`);
      } catch (cacheError) {
        console.warn('Error caching recommendations:', cacheError);
      }
    }
    
    return filteredRecommendations;
  } catch (err) {
    console.error('Unexpected error in getMealRecommendations:', err);
    
    // Try to get from cache as fallback even if error occurred
    if (isWeb) {
      try {
        const cachedData = await AsyncStorage.getItem(`meal_recommendations_${mealType || 'all'}`);
        if (cachedData) {
          const parsedData = JSON.parse(cachedData);
          const recommendations = parsedData.data || [];
          
          if (recommendations.length > 0) {
            console.log(`Using cached ${mealType || 'all'} recommendations after error`);
            return recommendations;
          }
        }
      } catch (cacheError) {
        console.warn('Error accessing recommendation cache during error recovery:', cacheError);
      }
    }
    
    // Return fallback data instead of empty array when everything fails
    return mealType 
      ? FALLBACK_RECOMMENDATIONS.filter(r => r.type === mealType)
      : FALLBACK_RECOMMENDATIONS;
  }
}

export async function addMealToFavorites(mealId: string) {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    // Check if record already exists in user_favorite_meals
    const favoritesRef = collection(db, 'user_favorite_meals');
    const q = query(
      favoritesRef,
      where('user_id', '==', user.uid),
      where('meal_id', '==', mealId)
    );
    
    const querySnapshot = await getDocs(q);
    
    // If it exists, it's already a favorite
    if (!querySnapshot.empty) {
      return { 
        success: true, 
        data: { 
          id: querySnapshot.docs[0].id, 
          is_favorite: true 
        } 
      };
    }
    
    // Otherwise add to favorites
    const newFavoriteRef = await addDoc(collection(db, 'user_favorite_meals'), {
      user_id: user.uid,
      meal_id: mealId,
      created_at: new Date().toISOString()
    });
    
    return { 
      success: true, 
      data: { 
        id: newFavoriteRef.id, 
        is_favorite: true 
      } 
    };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in addMealToFavorites:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

export async function removeMealFromFavorites(mealId: string) {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }
    
    // Find the favorite record
    const favoritesRef = collection(db, 'user_favorite_meals');
    const q = query(
      favoritesRef,
      where('user_id', '==', user.uid),
      where('meal_id', '==', mealId)
    );
    
    const querySnapshot = await getDocs(q);
    
    // Delete all matching records
    const batch = writeBatch(db);
    querySnapshot.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    await batch.commit();
    
    return { success: true, data: { is_favorite: false } };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in removeMealFromFavorites:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

export async function addRecommendationToMeals(recommendation: MealRecommendation) {
  // Create a meal from the recommendation
  const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
  const now = new Date().toTimeString().split(' ')[0].substring(0, 5); // HH:MM

  // Prepare the meal object
  const meal: Omit<Meal, 'id' | 'user_id' | 'created_at' | 'updated_at'> = {
    name: recommendation.name,
    date: today,
    time: now,
    total_calories: recommendation.calories,
    total_protein: recommendation.protein,
    total_carbs: recommendation.carbs,
    total_fat: recommendation.fat,
    image_url: recommendation.image_url
  };

  // Prepare the meal item
  const items: Omit<MealItem, 'id' | 'meal_id' | 'created_at'>[] = [{
    name: recommendation.name,
    calories: recommendation.calories,
    protein: recommendation.protein,
    carbs: recommendation.carbs,
    fat: recommendation.fat
  }];

  // Use the existing createMeal function
  return await createMeal(meal, items);
}

/**
 * Mindfulness functions
 */
export async function getMindfulnessTips(category?: string) {
  try {
    // Start building the query
    const tipsRef = collection(db, 'mindfulness_tips');
    let q = query(tipsRef);
    
    // Filter by category if provided
    if (category) {
      q = query(tipsRef, where('category', '==', category));
    }
    
    const querySnapshot = await getDocs(q);
    const tips = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
    
    return tips;
  } catch (err) {
    console.error('Unexpected error in getMindfulnessTips:', err);
    return [];
  }
}

// Default fallback mindfulness tips
export const FALLBACK_MINDFULNESS_TIPS: MindfulnessTip[] = [
  {
    id: 'tip-1',
    title: 'Morning Breath Awareness',
    message: 'Take 5 deep breaths, focusing on the sensation of air flowing in and out. Notice how your body feels more energized with each breath.',
    category: 'stress-relief',
    icon: 'breathing',
    gradient_start: '#9CECFB',
    gradient_end: '#65C7F7'
  },
  {
    id: 'tip-2',
    title: 'Mindful Hydration',
    message: 'Take a moment to slowly drink a glass of water. Feel the sensation as it travels through your body, bringing hydration and renewal.',
    category: 'nutrition',
    icon: 'hydration',
    gradient_start: '#43CEA2',
    gradient_end: '#185A9D'
  },
  {
    id: 'tip-3',
    title: 'Gratitude Reflection',
    message: 'Pause and name three things you\'re grateful for today. Focusing on gratitude can shift your perspective and enhance wellbeing.',
    category: 'mental-health',
    icon: 'gratitude',
    gradient_start: '#B06AB3',
    gradient_end: '#4568DC'
  },
  {
    id: 'tip-4',
    title: 'Mindful Movement Break',
    message: 'Stand up and stretch your arms overhead. Gently twist your torso from side to side, noticing the sensations in your muscles and joints.',
    category: 'movement',
    icon: 'stretch',
    gradient_start: '#FF9966',
    gradient_end: '#FF5E62'
  }
];

export async function getRandomMindfulnessTip(categories?: string[], signal?: AbortSignal) {
  console.debug('Getting random mindfulness tip', categories);
  
  // Try to get from cache first
  try {
    const cachedData = await AsyncStorage.getItem('cached_mindfulness_tip');
    if (cachedData) {
      const parsedData = JSON.parse(cachedData);
      const cacheTimestamp = parsedData.timestamp || 0;
      const now = Date.now();
      
      // Use cache if it's less than 15 minutes old
      if (now - cacheTimestamp < 15 * 60 * 1000) {
        console.debug('Using cached mindfulness tip');
        return parsedData.tip;
      }
    }
  } catch (cacheError) {
    console.warn('Error accessing tip cache:', cacheError);
  }
  
  try {
    // Check authentication first
    const user = auth.currentUser;
    
    if (!user) {
      console.log('No authenticated user found for mindfulness tips');
      // Return a random fallback tip
      const randomIndex = Math.floor(Math.random() * FALLBACK_MINDFULNESS_TIPS.length);
      const fallbackTip = FALLBACK_MINDFULNESS_TIPS[randomIndex];
      
      // Cache the tip
      try {
        await AsyncStorage.setItem('cached_mindfulness_tip', JSON.stringify({
          tip: fallbackTip,
          timestamp: Date.now()
        }));
      } catch (cacheError) {
        console.warn('Error caching tip:', cacheError);
      }
      
      return fallbackTip;
    }

    // Execute with retry pattern to handle network issues
    const { data: tips, error } = await executeWithRetry(async () => {
    // Start building the query
    const tipsRef = collection(db, 'mindfulness_tips');
    let q = query(tipsRef);
    
    // Filter by categories if provided
    if (categories && categories.length > 0) {
      // Note: Firestore doesn't support direct "IN" array queries in the same way
      
      const querySnapshot = await getDocs(q);
        
        if (querySnapshot.empty) {
          console.warn('No mindfulness tips found in database');
          return [];
        }
        
        return querySnapshot.docs
        .map(doc => ({
          id: doc.id,
          ...doc.data()
          }) as MindfulnessTip)
          .filter(tip => categories.includes(tip.category));
      }
      
      // If no categories specified, get all tips
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        console.warn('No mindfulness tips found in database');
        return [];
      }
      
      return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
      }) as MindfulnessTip);
    }, 3, 'Get mindfulness tips');
    
    if (error || !tips || tips.length === 0) {
      console.error('Error or empty results from mindfulness tips query:', error);
      // Return a random fallback tip
      const randomIndex = Math.floor(Math.random() * FALLBACK_MINDFULNESS_TIPS.length);
      const fallbackTip = FALLBACK_MINDFULNESS_TIPS[randomIndex];
      
      // Cache the tip
      try {
        await AsyncStorage.setItem('cached_mindfulness_tip', JSON.stringify({
          tip: fallbackTip,
          timestamp: Date.now()
        }));
      } catch (cacheError) {
        console.warn('Error caching tip:', cacheError);
      }
      
      return fallbackTip;
    }
    
    // Select a random tip
      const randomIndex = Math.floor(Math.random() * tips.length);
    const tip = tips[randomIndex];
    
    // Cache the tip
    try {
      await AsyncStorage.setItem('cached_mindfulness_tip', JSON.stringify({
        tip,
        timestamp: Date.now()
      }));
    } catch (cacheError) {
      console.warn('Error caching tip:', cacheError);
    }
    
    return tip;
  } catch (err) {
    console.error('Unexpected error in getRandomMindfulnessTip:', err);
    
    // Return a random fallback tip
    const randomIndex = Math.floor(Math.random() * FALLBACK_MINDFULNESS_TIPS.length);
    return FALLBACK_MINDFULNESS_TIPS[randomIndex];
  }
}

export async function recordMindfulnessSession(session: Omit<MindfulnessSession, 'id' | 'user_id' | 'created_at'>) {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }
    
    // Add the session
    const sessionData = {
      ...session,
      user_id: user.uid,
      created_at: new Date().toISOString()
    };
    
    const sessionRef = await addDoc(collection(db, 'user_mindfulness_sessions'), sessionData);
    
    return { success: true, data: { id: sessionRef.id } };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in recordMindfulnessSession:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

export async function getMindfulnessPreferences() {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      console.log('No authenticated user found');
      return null;
    }
    
    const preferencesRef = collection(db, 'user_mindfulness_preferences');
    const q = query(preferencesRef, where('user_id', '==', user.uid));
    
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return null;
    }
    
    const prefDoc = querySnapshot.docs[0];
    return { id: prefDoc.id, ...prefDoc.data() };
  } catch (err) {
    console.error('Unexpected error in getMindfulnessPreferences:', err);
    return null;
  }
}

export async function updateMindfulnessPreferences(preferences: Omit<MindfulnessPreferences, 'id' | 'user_id' | 'created_at' | 'updated_at'>) {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Not authenticated' };
    }

    const userId = user.uid;
    
    // Check if preferences exist
    const preferencesRef = collection(db, 'user_mindfulness_preferences');
    const q = query(preferencesRef, where('user_id', '==', userId));
    
    const querySnapshot = await getDocs(q);
    let result;
    
    if (!querySnapshot.empty) {
      // Update existing preferences
      const prefDoc = querySnapshot.docs[0];
      const prefRef = doc(db, 'user_mindfulness_preferences', prefDoc.id);
      
      await updateDoc(prefRef, {
        ...preferences,
        updated_at: new Date().toISOString(),
      });
      
      // Get updated document
      const updatedPrefSnap = await getDoc(prefRef);
      result = { id: updatedPrefSnap.id, ...updatedPrefSnap.data() };
    } else {
      // Insert new preferences
      const newPrefRef = await addDoc(collection(db, 'user_mindfulness_preferences'), {
        ...preferences,
        user_id: userId,
        created_at: new Date().toISOString()
      });
      
      const newPrefSnap = await getDoc(newPrefRef);
      result = { id: newPrefSnap.id, ...newPrefSnap.data() };
    }

    return { success: true, data: result };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in updateMindfulnessPreferences:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

export async function getUserMindfulnessSessions(startDate?: string, endDate?: string) {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      console.log('No authenticated user found');
      return [];
    }
    
    // Start building the query
    const sessionsRef = collection(db, 'user_mindfulness_sessions');
    let q = query(sessionsRef, where('user_id', '==', user.uid));
    
    // Add date range filters if provided
    if (startDate) {
      q = query(q, where('session_date', '>=', startDate));
    }
    
    if (endDate) {
      q = query(q, where('session_date', '<=', endDate));
    }
    
    // Order by most recent first
    q = query(q, orderBy('session_date', 'desc'));
    
    const querySnapshot = await getDocs(q);
    const sessions: any[] = [];
    
    // For each session, get the associated tip if available
    for (const sessionDoc of querySnapshot.docs) {
      const sessionData = sessionDoc.data();
      let tipData: any = null;
      
      if (sessionData.tip_id) {
        const tipRef = doc(db, 'mindfulness_tips', sessionData.tip_id);
        const tipSnap = await getDoc(tipRef);
        
        if (tipSnap.exists()) {
          tipData = { id: tipSnap.id, ...tipSnap.data() };
        }
      }
      
      sessions.push({
        id: sessionDoc.id,
        ...sessionData,
        tip: tipData
      });
    }
    
    return sessions;
  } catch (err) {
    console.error('Unexpected error in getUserMindfulnessSessions:', err);
    return [];
  }
}

// Get meals from local database
export async function getLocalMeals() {
  if (!sqliteDb) {
    await initDatabase();
  }
  
  try {
    const data = await sqliteDb.getAllAsync('SELECT * FROM meals ORDER BY date DESC, time DESC');
    return { success: true, data };
  } catch (error) {
    console.error('Error getting local meals:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Get meal items from local database
export async function getLocalMealItems(mealId: string) {
  if (!sqliteDb) {
    await initDatabase();
  }
  
  try {
    const data = await sqliteDb.getAllAsync('SELECT * FROM meal_items WHERE meal_id = ?', mealId);
    return { success: true, data };
  } catch (error) {
    console.error('Error getting local meal items:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Enhanced sync function with conflict detection and resolution
export async function syncOfflineDataWithConflictResolution(
  strategy: ConflictResolutionStrategy = ConflictResolutionStrategy.SERVER_WINS
): Promise<{
  success: boolean;
  syncedCount?: number;
  conflictCount?: number;
  errorCount?: number;
  conflicts?: DataConflict[];
  error?: string;
}> {
  if (!sqliteDb) {
    await initDatabase();
  }
  
  if (!(await checkNetworkStatus())) {
    return { success: false, error: 'No internet connection' };
  }
  
  try {
    // Get all pending sync operations
    const operations = await sqliteDb.getAllAsync<SyncQueueItem>('SELECT * FROM sync_queue ORDER BY created_at ASC');
    
    let successCount = 0;
    let errorCount = 0;
    let conflictCount = 0;
    const conflicts: DataConflict[] = [];
    
    for (const op of operations) {
      try {
        const data = JSON.parse(op.data);
        
        if (op.operation === 'INSERT' || op.operation === 'UPDATE') {
          // For inserts and updates, check for conflicts first
          if (op.table_name === 'meals') {
            // If it's an update, check if the server version has changed
            if (op.operation === 'UPDATE' && data.id) {
              const mealRef = doc(db, 'meals', data.id);
              const mealSnap = await getDoc(mealRef);
              
              if (!mealSnap.exists()) {
                // If record doesn't exist on server, it's not a conflict
                // This is fine, no conflict
              } else {
                const serverData = { id: mealSnap.id, ...mealSnap.data() } as any;
                
                // Check if there's a conflict (server data is different)
                const serverTimestamp = new Date(serverData.updated_at || serverData.created_at);
                const localTimestamp = new Date(data.updated_at || data.created_at);
                
                // If server data is newer AND different from local data
                if (serverTimestamp > localTimestamp && JSON.stringify(serverData) !== JSON.stringify(data)) {
                  // We have a conflict
                  conflictCount++;
                  
                  const conflict: DataConflict = {
                    id: data.id,
                    tableName: op.table_name,
                    localData: data,
                    serverData,
                    timestamp: new Date().toISOString(),
                    resolved: false
                  };
                  
                  conflicts.push(conflict);
                  
                  // Apply conflict resolution strategy
                  if (strategy === ConflictResolutionStrategy.LOCAL_WINS) {
                    // Proceed with the operation as normal (local data wins)
                    conflict.resolved = true;
                    conflict.strategy = ConflictResolutionStrategy.LOCAL_WINS;
                  } else if (strategy === ConflictResolutionStrategy.SERVER_WINS) {
                    // Skip this operation and update local DB with server data
                    await sqliteDb.runAsync(
                      `UPDATE meals SET 
                        name = ?, date = ?, time = ?, total_calories = ?, 
                        total_protein = ?, total_carbs = ?, total_fat = ?, 
                        image_url = ?, synced = 1 
                      WHERE id = ?`,
                      [
                        serverData.name,
                        serverData.date,
                        serverData.time,
                        serverData.total_calories,
                        serverData.total_protein || 0,
                        serverData.total_carbs || 0,
                        serverData.total_fat || 0,
                        serverData.image_url || null,
                        data.id
                      ]
                    );
                    
                    // Remove the operation from queue since we've resolved the conflict
                    await sqliteDb.runAsync('DELETE FROM sync_queue WHERE id = ?', op.id);
                    
                    conflict.resolved = true;
                    conflict.strategy = ConflictResolutionStrategy.SERVER_WINS;
                    
                    successCount++;
                    continue; // Skip to the next operation
                  } else {
                    // MANUAL strategy - save conflict for later resolution
                    // Store the conflict in the database for later resolution
                    saveConflict(conflict);
                    continue; // Skip to the next operation
                  }
                }
              }
            }
            
            // No conflict or conflict resolved with LOCAL_WINS strategy, proceed with operation
            if (op.operation === 'INSERT') {
              const mealRef = doc(collection(db, 'meals'));
              await setDoc(mealRef, data);
            } else {
              const mealRef = doc(db, 'meals', data.id);
              await updateDoc(mealRef, data);
            }
            
            // Mark as synced in local database
            await sqliteDb.runAsync('UPDATE meals SET synced = 1 WHERE id = ?', data.id);
          } else if (op.table_name === 'meal_items') {
            // Similar conflict resolution for meal items
            if (op.operation === 'UPDATE' && data.id) {
              const itemRef = doc(db, 'meal_items', data.id);
              const itemSnap = await getDoc(itemRef);
              
              if (!itemSnap.exists()) {
                // If record doesn't exist on server, it's not a conflict
              } else {
                const serverData = { id: itemSnap.id, ...itemSnap.data() } as any;
                
                // Check for conflict with similar logic as meals
                const conflict: DataConflict = {
                  id: data.id,
                  tableName: op.table_name,
                  localData: data,
                  serverData,
                  timestamp: new Date().toISOString(),
                  resolved: false
                };
                
                if (JSON.stringify(serverData) !== JSON.stringify(data)) {
                  conflictCount++;
                  conflicts.push(conflict);
                  
                  if (strategy === ConflictResolutionStrategy.SERVER_WINS) {
                    await sqliteDb.runAsync(
                      `UPDATE meal_items SET 
                        name = ?, calories = ?, protein = ?, 
                        carbs = ?, fat = ?, synced = 1 
                      WHERE id = ?`,
                      [
                        serverData.name,
                        serverData.calories,
                        serverData.protein || 0,
                        serverData.carbs || 0,
                        serverData.fat || 0,
                        data.id
                      ]
                    );
                    
                    await sqliteDb.runAsync('DELETE FROM sync_queue WHERE id = ?', op.id);
                    
                    conflict.resolved = true;
                    conflict.strategy = ConflictResolutionStrategy.SERVER_WINS;
                    
                    successCount++;
                    continue;
                  } else if (strategy === ConflictResolutionStrategy.MANUAL) {
                    saveConflict(conflict);
                    continue;
                  }
                  // For LOCAL_WINS, we continue with the operation as normal
                }
              }
            }
            
            // No conflict or LOCAL_WINS strategy, proceed with operation
            if (op.operation === 'INSERT') {
              const itemRef = doc(collection(db, 'meal_items'));
              await setDoc(itemRef, data);
            } else {
              const itemRef = doc(db, 'meal_items', data.id);
              await updateDoc(itemRef, data);
            }
            
            // Mark as synced in local database
            await sqliteDb.runAsync('UPDATE meal_items SET synced = 1 WHERE id = ?', data.id);
          }
        } else if (op.operation === 'DELETE') {
          // For DELETE operations, just delete from the server
          if (op.table_name === 'meals') {
            const mealRef = doc(db, 'meals', data.id);
            await deleteDoc(mealRef);
          } else if (op.table_name === 'meal_items') {
            const itemRef = doc(db, 'meal_items', data.id);
            await deleteDoc(itemRef);
          }
        }
        
        // Delete the successful operation from queue
        await sqliteDb.runAsync('DELETE FROM sync_queue WHERE id = ?', op.id);
        
        successCount++;
      } catch (error) {
        console.error('Error syncing operation:', error);
        errorCount++;
      }
    }
    
    return {
      success: true,
      syncedCount: successCount,
      conflictCount,
      errorCount,
      conflicts: conflicts.length > 0 ? conflicts : undefined
    };
  } catch (error) {
    console.error('Error in syncOfflineData:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// Initialize conflicts table if it doesn't exist
export async function initConflictsTable() {
  if (!sqliteDb) {
    await initDatabase();
  }
  
  try {
    await sqliteDb.execAsync(`
      CREATE TABLE IF NOT EXISTS data_conflicts (
        id TEXT PRIMARY KEY,
        table_name TEXT,
        local_data TEXT,
        server_data TEXT,
        timestamp TEXT,
        resolved INTEGER DEFAULT 0,
        resolution_strategy TEXT
      );
    `);
    return { success: true };
  } catch (error) {
    console.error('Error creating conflicts table:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// Define the conflict database record type
interface ConflictRecord {
  id: string;
  table_name: string;
  local_data: string;
  server_data: string;
  timestamp: string;
  resolved: number;
  resolution_strategy: string | null;
}

// Save a conflict to the database for later resolution
async function saveConflict(conflict: DataConflict) {
  if (!sqliteDb) {
    await initDatabase();
  }
  
  await initConflictsTable();
  
  try {
    await sqliteDb.runAsync(
      `INSERT OR REPLACE INTO data_conflicts (
        id, table_name, local_data, server_data, timestamp, resolved, resolution_strategy
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        conflict.id,
        conflict.tableName,
        JSON.stringify(conflict.localData),
        JSON.stringify(conflict.serverData),
        conflict.timestamp,
        conflict.resolved ? 1 : 0,
        conflict.strategy || null
      ]
    );
    return { success: true };
  } catch (error) {
    console.error('Error saving conflict:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// Get all unresolved conflicts
export async function getUnresolvedConflicts(): Promise<DataConflict[]> {
  if (!sqliteDb) {
    await initDatabase();
  }
  
  await initConflictsTable();
  
  try {
    const conflicts = await sqliteDb.getAllAsync<ConflictRecord>(
      'SELECT * FROM data_conflicts WHERE resolved = 0 ORDER BY timestamp DESC'
    );
    
    return conflicts.map(c => ({
      id: c.id,
      tableName: c.table_name,
      localData: JSON.parse(c.local_data),
      serverData: JSON.parse(c.server_data),
      timestamp: c.timestamp,
      resolved: c.resolved === 1,
      strategy: c.resolution_strategy as ConflictResolutionStrategy
    }));
  } catch (error) {
    console.error('Error getting unresolved conflicts:', error);
    return [];
  }
}

// Resolve a specific conflict
export async function resolveConflict(
  conflictId: string, 
  strategy: ConflictResolutionStrategy
): Promise<{ success: boolean; error?: string }> {
  if (!sqliteDb) {
    await initDatabase();
  }
  
  try {
    // Get the conflict details
    const conflict = await sqliteDb.getFirstAsync<ConflictRecord>(
      'SELECT * FROM data_conflicts WHERE id = ?',
      conflictId
    );
    
    if (!conflict) {
      return { success: false, error: 'Conflict not found' };
    }
    
    const localData = JSON.parse(conflict.local_data);
    const serverData = JSON.parse(conflict.server_data);
    const tableName = conflict.table_name;
    
    if (strategy === ConflictResolutionStrategy.LOCAL_WINS) {
      // Apply local changes to the server
      if (tableName === 'meals') {
        const mealRef = doc(db, 'meals', conflictId);
        await updateDoc(mealRef, localData);
      } else if (tableName === 'meal_items') {
        const itemRef = doc(db, 'meal_items', conflictId);
        await updateDoc(itemRef, localData);
      }
    } else if (strategy === ConflictResolutionStrategy.SERVER_WINS) {
      // Apply server changes to local DB
      if (tableName === 'meals') {
        await sqliteDb.runAsync(
          `UPDATE meals SET 
            name = ?, date = ?, time = ?, total_calories = ?, 
            total_protein = ?, total_carbs = ?, total_fat = ?, 
            image_url = ?, synced = 1 
          WHERE id = ?`,
          [
            serverData.name,
            serverData.date,
            serverData.time,
            serverData.total_calories,
            serverData.total_protein || 0,
            serverData.total_carbs || 0,
            serverData.total_fat || 0,
            serverData.image_url || null,
            conflictId
          ]
        );
      } else if (tableName === 'meal_items') {
        await sqliteDb.runAsync(
          `UPDATE meal_items SET 
            name = ?, calories = ?, protein = ?, 
            carbs = ?, fat = ?, synced = 1 
          WHERE id = ?`,
          [
            serverData.name,
            serverData.calories,
            serverData.protein || 0,
            serverData.carbs || 0,
            serverData.fat || 0,
            conflictId
          ]
        );
      }
    }
    
    // Mark the conflict as resolved
    await sqliteDb.runAsync(
      'UPDATE data_conflicts SET resolved = 1, resolution_strategy = ? WHERE id = ?',
      [strategy, conflictId]
    );
    
    return { success: true };
  } catch (error) {
    console.error('Error resolving conflict:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// Helper function to listen for real-time updates to a document
export function listenForDocumentChanges<T>(
  collectionName: string, 
  documentId: string, 
  callback: (data: T | null) => void
) {
  const docRef = doc(db, collectionName, documentId);
  return onSnapshot(docRef, (docSnap) => {
    if (docSnap.exists()) {
      callback({ id: docSnap.id, ...docSnap.data() } as T);
    } else {
      callback(null);
    }
  });
}

// Helper function to listen for real-time updates to a collection
export function listenForCollectionChanges<T>(
  collectionName: string,
  queryConstraints: any[] = [],
  callback: (data: T[]) => void
) {
  const collectionRef = collection(db, collectionName);
  const q = query(collectionRef, ...queryConstraints);
  
  return onSnapshot(q, (querySnapshot) => {
    const items = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as T[];
    
    callback(items);
  });
}

// Add this function to properly close the database connection
export async function closeDatabase() {
  // On web platform, bypass SQLite operations
  if (isWeb) {
    return { success: true, message: 'Web platform, no SQLite connection to close' };
  }
  
  try {
    if (sqliteDb) {
      await sqliteDb.closeAsync();
      console.log('Database connection closed successfully');
      // Reset the database reference
      sqliteDb = undefined as unknown as SQLite.SQLiteDatabase;
      return { success: true };
    }
    return { success: true, message: 'No active database connection to close' };
  } catch (error) {
    console.error('Error closing database connection:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// Add a function to handle the Access Handle error specifically for web
export async function fixDatabaseAccessHandleError() {
  if (isWeb) {
    console.log('Using Firebase directly on web platform, skipping SQLite initialization');
    return { success: true, message: 'Web platform, using Firebase directly' };
  }
  
  if (Platform.OS === 'web') {
    try {
      // Close existing connection
      await closeDatabase();
      
      // Wait a moment to ensure resources are released
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Attempt to initialize the database again
      const result = await initDatabase();
      return result;
    } catch (error) {
      console.error('Error fixing database access handle:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
  return { success: true, message: 'Not applicable on non-web platforms' };
}

/**
 * Utility function for robust Firebase operation handling with retries and detailed errors
 * @param operation The database operation to execute
 * @param maxRetries Maximum number of retry attempts
 * @param operationName Name of the operation for logging
 */
export async function executeWithRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  operationName: string = 'Database operation'
): Promise<{ data: T | null; error: Error | null }> {
  let retries = 0;
  let lastError: Error | null = null;
  
  while (retries <= maxRetries) {
    try {
      // Check network connectivity
      const netInfo = await NetInfo.fetch();
      if (!netInfo.isConnected) {
        console.warn(`${operationName}: Network is not connected, retrying (${retries}/${maxRetries})`);
        lastError = new Error('Network connection unavailable');
        retries++;
        
        // Wait longer between retries
        await new Promise(resolve => setTimeout(resolve, 1000 * retries));
        continue;
      }
      
      // Attempt operation
      const result = await operation();
      return { data: result, error: null };
    } catch (err) {
      // Construct standardized error
      const error = err instanceof Error ? err : new Error(String(err));
      
      // Log the error with attempt number
      console.error(`${operationName} failed (attempt ${retries + 1}/${maxRetries + 1}):`, error.message);
      
      // Save for potential return
      lastError = error;
      
      // Determine if we should retry
      const isRetryable = 
        error.message.includes('network') || 
        error.message.includes('timeout') ||
        error.message.includes('unavailable') ||
        error.message.includes('ECONNREFUSED') ||
        error.message.includes('429') || // Rate limiting
        error.message.includes('500') || // Server error
        error.message.includes('503'); // Service unavailable
      
      if (!isRetryable) {
        console.error(`${operationName}: Non-retryable error, aborting retries:`, error.message);
        break;
      }
      
      retries++;
      
      if (retries <= maxRetries) {
        // Exponential backoff: wait longer between successive retries
        const backoffTime = Math.min(1000 * Math.pow(2, retries), 10000); // Cap at 10 seconds
        console.log(`${operationName}: Retrying in ${backoffTime}ms...`);
        await new Promise(resolve => setTimeout(resolve, backoffTime));
      }
    }
  }
  
  return { 
    data: null, 
    error: lastError || new Error(`${operationName} failed after ${maxRetries} retries`)
  };
}