import { NutritionInfo } from '@/types/api-responses';
import { getUserProfile, NutritionGoalType, ActivityLevel, DietaryPreference } from './nutritionGoalService';
import { generateNutritionRecommendations } from './openai/secureApiClient';
import { useMealPlannerService } from './mealPlannerService';
import { format } from 'date-fns';
import { getAuth } from 'firebase/auth';
import { collection, query, where, getDocs, orderBy, Firestore, getFirestore } from 'firebase/firestore';
import { app } from '@/lib/firebase';


// Initialize Firestore
const db = getFirestore(app);

/**
 * Interface for meal data
 */
export interface MealData {
  id: string;
  name: string;
  mealType: string;
  timestamp: string;
  nutrition: NutritionInfo;
  imageUrl?: string;
}

/**
 * Interface for daily intake summary
 */
export interface DailyIntakeSummary {
  date: string;
  totalNutrition: NutritionInfo;
  percentOfGoals: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber?: number;
  };
  meals: MealData[];
  recommendations: string[];
}

/**
 * Interface for weekly intake summary
 */
export interface WeeklyIntakeSummary {
  startDate: string;
  endDate: string;
  averageNutrition: NutritionInfo;
  percentOfGoals: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber?: number;
  };
  dailySummaries: DailyIntakeSummary[];
  trends: {
    calorieIntake: { date: string; value: number }[];
    proteinIntake: { date: string; value: number }[];
    carbsIntake: { date: string; value: number }[];
    fatIntake: { date: string; value: number }[];
  };
  recommendations: string[];
}

// OpenAI interface removed - now using secure API client

/**
 * Get meals for a specific date
 */
export async function getMealsForDate(date: string): Promise<MealData[]> {
  try {
    // Get user from Firebase Auth
    const auth = getAuth(app);
    const user = auth.currentUser;
    
    if (!user) {
      console.error('No user found');
      return [];
    }
    
    // Convert date string to Date object
    const targetDate = new Date(date);
    const formattedDate = targetDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    
    // Get meals from Firestore
    const mealsQuery = query(
      collection(db, 'meals'),
      where('user_id', '==', user.uid),
      where('date', '==', formattedDate),
      orderBy('time', 'asc')
    );
    
    const querySnapshot = await getDocs(mealsQuery);
    
    if (querySnapshot.empty) {
      return [];
    }
    
    // Transform database records to MealData interface
    const meals: MealData[] = [];
    querySnapshot.forEach((doc) => {
      const meal = doc.data();
      meals.push({
        id: doc.id,
        name: meal.name,
        mealType: meal.meal_type || 'meal', // Fallback to 'meal' if meal_type is not available
        timestamp: `${meal.date}T${meal.time}`, // Construct timestamp from date and time
        nutrition: {
          calories: meal.total_calories,
          protein: meal.total_protein,
          carbs: meal.total_carbs,
          fat: meal.total_fat,
          fiber: meal.fiber || 0,
          sugar: meal.sugar || 0,
          sodium: meal.sodium || 0
        },
        imageUrl: meal.image_url
      });
    });
    
    return meals;
  } catch (error) {
    console.error('Error in getMealsForDate:', error);
    return [];
  }
}

/**
 * Generate daily intake summary
 */
export async function generateDailyIntakeSummary(date: string): Promise<DailyIntakeSummary | null> {
  try {
    // Get user profile for nutrition goals
    const userProfile = await getUserProfile();
    if (!userProfile) {
      console.error('No user profile found');
      return null;
    }
    
    // Get meals for the date
    const meals = await getMealsForDate(date);
    
    // Calculate total nutrition
    const totalNutrition: NutritionInfo = meals.reduce(
      (total, meal) => ({
        calories: total.calories + meal.nutrition.calories,
        protein: total.protein + meal.nutrition.protein,
        carbs: total.carbs + meal.nutrition.carbs,
        fat: total.fat + meal.nutrition.fat,
        fiber: (total.fiber || 0) + (meal.nutrition.fiber || 0),
        sugar: (total.sugar || 0) + (meal.nutrition.sugar || 0),
        sodium: (total.sodium || 0) + (meal.nutrition.sodium || 0)
      }),
      { calories: 0, protein: 0, carbs: 0, fat: 0, fiber: 0, sugar: 0, sodium: 0 }
    );
    
    // Calculate percent of daily goals
    const percentOfGoals = {
      calories: (totalNutrition.calories / userProfile.calorieGoal) * 100,
      protein: (totalNutrition.protein / userProfile.proteinGoal) * 100,
      carbs: (totalNutrition.carbs / userProfile.carbsGoal) * 100,
      fat: (totalNutrition.fat / userProfile.fatGoal) * 100,
      fiber: totalNutrition.fiber ? (totalNutrition.fiber / 25) * 100 : undefined // using 25g as standard fiber goal
    };
    
    // Generate AI recommendations if API key is available
    let recommendations: string[] = [];
    if (meals.length > 0) {
      recommendations = await generateRecommendations(totalNutrition, userProfile.calorieGoal, userProfile.proteinGoal, userProfile.carbsGoal, userProfile.fatGoal, userProfile.goalType);
    } else {
      recommendations = ["No meals recorded for this day. Try logging your meals to get personalized recommendations."];
    }
    
    return {
      date,
      totalNutrition,
      percentOfGoals,
      meals,
      recommendations
    };
  } catch (error) {
    console.error('Error generating daily intake summary:', error);
    return null;
  }
}

/**
 * Generate weekly intake summary
 */
export async function generateWeeklyIntakeSummary(startDate: string, endDate: string): Promise<WeeklyIntakeSummary | null> {
  try {
    // Get user profile for nutrition goals
    const userProfile = await getUserProfile();
    if (!userProfile) {
      console.error('No user profile found');
      return null;
    }
    
    // Parse dates
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    // Generate array of dates in the range
    const dates: string[] = [];
    const current = new Date(start);
    
    while (current <= end) {
      dates.push(format(current, 'yyyy-MM-dd'));
      current.setDate(current.getDate() + 1);
    }
    
    // Get daily summaries for each date
    const dailySummaries: DailyIntakeSummary[] = [];
    for (const date of dates) {
      const summary = await generateDailyIntakeSummary(date);
      if (summary) {
        dailySummaries.push(summary);
      }
    }
    
    if (dailySummaries.length === 0) {
      return null;
    }
    
    // Calculate average nutrition
    const averageNutrition: NutritionInfo = {
      calories: Math.round(dailySummaries.reduce((sum, day) => sum + day.totalNutrition.calories, 0) / dailySummaries.length),
      protein: Math.round(dailySummaries.reduce((sum, day) => sum + day.totalNutrition.protein, 0) / dailySummaries.length),
      carbs: Math.round(dailySummaries.reduce((sum, day) => sum + day.totalNutrition.carbs, 0) / dailySummaries.length),
      fat: Math.round(dailySummaries.reduce((sum, day) => sum + day.totalNutrition.fat, 0) / dailySummaries.length),
      fiber: Math.round(dailySummaries.reduce((sum, day) => sum + (day.totalNutrition.fiber || 0), 0) / dailySummaries.length),
      sugar: Math.round(dailySummaries.reduce((sum, day) => sum + (day.totalNutrition.sugar || 0), 0) / dailySummaries.length),
      sodium: Math.round(dailySummaries.reduce((sum, day) => sum + (day.totalNutrition.sodium || 0), 0) / dailySummaries.length)
    };
    
    // Calculate percent of daily goals
    const percentOfGoals = {
      calories: (averageNutrition.calories / userProfile.calorieGoal) * 100,
      protein: (averageNutrition.protein / userProfile.proteinGoal) * 100,
      carbs: (averageNutrition.carbs / userProfile.carbsGoal) * 100,
      fat: (averageNutrition.fat / userProfile.fatGoal) * 100,
      fiber: averageNutrition.fiber ? (averageNutrition.fiber / 25) * 100 : undefined // using 25g as standard fiber goal
    };
    
    // Create trends data
    const trends = {
      calorieIntake: dailySummaries.map(day => ({ date: day.date, value: day.totalNutrition.calories })),
      proteinIntake: dailySummaries.map(day => ({ date: day.date, value: day.totalNutrition.protein })),
      carbsIntake: dailySummaries.map(day => ({ date: day.date, value: day.totalNutrition.carbs })),
      fatIntake: dailySummaries.map(day => ({ date: day.date, value: day.totalNutrition.fat }))
    };
    
    // Generate AI recommendations
    const recommendations = await generateWeeklyRecommendations(
      averageNutrition, 
      userProfile.calorieGoal, 
      userProfile.proteinGoal, 
      userProfile.carbsGoal, 
      userProfile.fatGoal, 
      userProfile.goalType, 
      trends
    );
    
    return {
      startDate,
      endDate,
      averageNutrition,
      percentOfGoals,
      dailySummaries,
      trends,
      recommendations
    };
  } catch (error) {
    console.error('Error generating weekly intake summary:', error);
    return null;
  }
}

/**
 * Generate recommendations based on nutrition data
 */
async function generateRecommendations(
  nutrition: NutritionInfo,
  calorieGoal: number,
  proteinGoal: number,
  carbsGoal: number,
  fatGoal: number,
  goalType: NutritionGoalType
): Promise<string[]> {
  // Create recommendations array
  const recommendations: string[] = [];
  
  // Add calorie goal recommendation
  recommendations.push(`You've consumed ${Math.round((nutrition.calories / calorieGoal) * 100)}% of your daily calorie goal.`);
  
  // Add protein recommendation
  if (nutrition.protein < proteinGoal) {
    recommendations.push("Try to increase your protein intake to meet your daily goal.");
  } else {
    recommendations.push("Great job meeting your protein goal!");
  }
  
  // Add recommendation if over calorie goal
  if (nutrition.calories > calorieGoal * 1.1) {
    recommendations.push("You're over your calorie goal for today. Consider lighter meals for the rest of the day.");
  }
  
  // Add recommendation if significantly under calorie goal
  if (nutrition.calories < calorieGoal * 0.7) {
    recommendations.push("You're significantly under your calorie goal. Make sure you're eating enough to fuel your body.");
  }
  
  // Add fiber recommendation if applicable
  if (nutrition.fiber && nutrition.fiber < 25) {
    recommendations.push("Try to include more fiber-rich foods like fruits, vegetables, and whole grains.");
  }

  // Use AI-generated recommendations via secure Firebase function
  try {
    const response = await generateNutritionRecommendations(
      nutrition,
      calorieGoal,
      proteinGoal,
      carbsGoal,
      fatGoal,
      goalType
    );

    if (response.success && response.recommendations && response.recommendations.length > 0) {
      return response.recommendations;
    } else {
      console.log('Using default recommendations - no AI recommendations available');
      return recommendations;
    }
  } catch (error) {
    console.error('Error generating AI recommendations:', error);
    return recommendations;
  }
}

/**
 * Generate weekly recommendations based on nutrition trends
 */
async function generateWeeklyRecommendations(
  averageNutrition: NutritionInfo,
  calorieGoal: number,
  proteinGoal: number,
  carbsGoal: number,
  fatGoal: number,
  goalType: NutritionGoalType,
  trends: any
): Promise<string[]> {
  // Create default recommendations
  const recommendations: string[] = [];
  
  // Add calorie recommendation
  recommendations.push(`Your weekly average was ${Math.round((averageNutrition.calories / calorieGoal) * 100)}% of your daily calorie goal.`);
  
  // Add protein recommendation
  if (averageNutrition.protein < proteinGoal) {
    recommendations.push("Try to increase your protein intake to better meet your weekly goals.");
  } else {
    recommendations.push("Great job meeting your protein goals consistently!");
  }
  
  // Add calorie trend recommendation
  if (averageNutrition.calories > calorieGoal * 1.1) {
    recommendations.push("You're trending above your calorie goals. Consider adjusting portion sizes or food choices.");
  }
  
  // Add recommendation for undereating
  if (averageNutrition.calories < calorieGoal * 0.8) {
    recommendations.push("You're consistently under your calorie targets. Make sure you're eating enough to support your activity levels.");
  }
  
  // Add fiber recommendation
  if (averageNutrition.fiber && averageNutrition.fiber < 25) {
    recommendations.push("Your fiber intake is below recommendations. Try adding more fruits, vegetables, and whole grains to your diet.");
  }

  // Use AI-generated recommendations via secure Firebase function
  try {
    // TODO: Create a separate function for weekly recommendations
    // For now, use the same function with appropriate parameters
    const response = await generateNutritionRecommendations(
      averageNutrition,
      calorieGoal,
      proteinGoal,
      carbsGoal,
      fatGoal,
      goalType
    );
    
    if (response.success && response.recommendations && response.recommendations.length > 0) {
      return response.recommendations;
    } else {
      console.log('Using default weekly recommendations - no AI recommendations available');
      return recommendations;
    }
  } catch (error) {
    console.error('Error generating AI weekly recommendations:', error);
    return recommendations;
  }
  
  return recommendations;
}

/**
 * Describe a nutrition trend from time series data
 */
function describeNutritionTrend(trendData: { date: string; value: number }[]): string {
  if (!trendData || trendData.length < 2) {
    return "insufficient data";
  }
  
  // Calculate simple linear regression
  const n = trendData.length;
  const xValues = Array.from({ length: n }, (_, i) => i); // Use indices as x values
  const yValues = trendData.map(point => point.value);
  
  const sumX = xValues.reduce((sum, x) => sum + x, 0);
  const sumY = yValues.reduce((sum, y) => sum + y, 0);
  const sumXY = xValues.reduce((sum, x, i) => sum + x * yValues[i], 0);
  const sumXX = xValues.reduce((sum, x) => sum + x * x, 0);
  
  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  
  // Calculate percent change from first to last day
  const firstValue = yValues[0];
  const lastValue = yValues[yValues.length - 1];
  const percentChange = ((lastValue - firstValue) / firstValue) * 100;
  
  // Describe the trend
  if (Math.abs(slope) < 0.5) {
    return "relatively stable";
  } else if (slope > 0) {
    return `increasing (${Math.round(percentChange)}% change)`;
  } else {
    return `decreasing (${Math.round(Math.abs(percentChange))}% change)`;
  }
}

/**
 * Generate a fitness goal-based meal plan
 */
export async function generateFitnessGoalMealPlan(date: string): Promise<any> {
  try {
    // This would normally call an API endpoint, but for now we'll return mock data
    return {
      date,
      meals: {
        breakfast: {
          name: "Greek Yogurt with Berries and Honey",
          description: "Creamy Greek yogurt with fresh berries and a drizzle of honey",
          ingredients: ["Greek yogurt", "Mixed berries", "Honey", "Granola"],
          nutrition: {
            calories: 320,
            protein: 22,
            carbs: 40,
            fat: 8
          },
          preparation: "Mix yogurt with berries and honey, top with granola",
          mealType: "breakfast"
        },
        lunch: {
          name: "Mediterranean Quinoa Bowl",
          description: "Protein-packed quinoa with roasted vegetables and feta cheese",
          ingredients: ["Quinoa", "Bell peppers", "Zucchini", "Red onion", "Feta cheese", "Olive oil", "Lemon juice"],
          nutrition: {
            calories: 450,
            protein: 18,
            carbs: 52,
            fat: 20
          },
          preparation: "Combine cooked quinoa with roasted vegetables, top with feta",
          mealType: "lunch"
        },
        dinner: {
          name: "Baked Salmon with Asparagus",
          description: "Herb-seasoned salmon fillet with roasted asparagus",
          ingredients: ["Salmon fillet", "Asparagus", "Olive oil", "Lemon", "Garlic", "Herbs"],
          nutrition: {
            calories: 420,
            protein: 38,
            carbs: 12,
            fat: 24
          },
          preparation: "Bake salmon and asparagus with olive oil and seasonings",
          mealType: "dinner"
        },
        snacks: [
          {
            name: "Apple with Almond Butter",
            description: "Sliced apple with a tablespoon of almond butter",
            ingredients: ["Apple", "Almond butter"],
            nutrition: {
              calories: 200,
              protein: 5,
              carbs: 25,
              fat: 10
            },
            preparation: "Slice apple and serve with almond butter",
            mealType: "snack"
          }
        ]
      },
      nutritionSummary: {
        calories: 1700,
        protein: 112,
        carbs: 144,
        fat: 80
      }
    };
  } catch (error) {
    console.error('Error generating fitness goal meal plan:', error);
    throw error;
  }
}

/**
 * Generate customized nutrition advice based on user profile and recent intake
 */
export async function getCustomizedNutritionAdvice(): Promise<string[]> {
  console.debug('dietitianService: Starting to get customized nutrition advice');
  try {
    // Get user profile
    const userProfile = await getUserProfile();
    console.debug('dietitianService: User profile obtained:', !!userProfile);
    
    if (!userProfile) {
      console.debug('dietitianService: No user profile found, returning default advice');
      return [
        "Complete your profile to get personalized nutrition advice",
        "Set your nutrition goals to receive tailored recommendations",
        "Stay hydrated by drinking water throughout the day",
        "Include a variety of colorful fruits and vegetables in your meals"
      ];
    }
    
    // Get today's date
    const today = format(new Date(), 'yyyy-MM-dd');
    console.debug('dietitianService: Generating daily intake summary for', today);
    
    try {
      // Generate daily intake summary
      const dailySummary = await generateDailyIntakeSummary(today);
      console.debug('dietitianService: Daily summary generated:', !!dailySummary);
      
      // Default advice if no recent data
      if (!dailySummary || dailySummary.meals.length === 0) {
        console.debug('dietitianService: No meals found, returning profile-based advice');
        return [
          "Track your meals to receive personalized nutrition advice",
          `Based on your profile, your daily targets are ${userProfile.calorieGoal} calories with ${userProfile.proteinGoal}g protein`,
          `Your current goal is ${userProfile.goalType.replace('_', ' ')}`,
          "Try to balance your meals throughout the day for consistent energy"
        ];
      }
      
      console.debug('dietitianService: Returning personalized recommendations');
      // Return recommendations from the daily summary
      return dailySummary.recommendations;
    } catch (summaryError) {
      console.error('Error generating daily intake summary:', summaryError);
      // Return profile-based advice if daily summary fails
      return [
        `Based on your profile, aim for ${userProfile.calorieGoal} calories daily`,
        `Your protein target is ${userProfile.proteinGoal}g to support your ${userProfile.goalType.replace('_', ' ')} goal`,
        "Stay hydrated and include a variety of nutrient-dense foods",
        "Consider tracking your meals for more personalized advice"
      ];
    }
  } catch (error) {
    console.error('Error generating customized nutrition advice:', error);
    // Return generic advice if everything fails
    return [
      "Unable to generate personalized advice at this time",
      "Balance your diet with proteins, carbs, and healthy fats",
      "Stay hydrated by drinking water throughout the day",
      "Include a variety of fruits and vegetables in your meals"
    ];
  }
} 