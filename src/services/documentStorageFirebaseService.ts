/**
 * Document Storage Firebase Service
 * 
 * Implementation of document storage functionality using Firebase Storage.
 */

import { ref, uploadBytes, getDownloadURL, deleteObject, listAll } from 'firebase/storage';
import { collection, addDoc, getDoc, getDocs, query, where, doc, updateDoc, deleteDoc, serverTimestamp } from 'firebase/firestore';
import { storage, firestore } from '@/lib/firebase';

// Types
export interface StoredDocument {
  id: string;
  userId: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  storagePath: string;
  downloadUrl?: string;
  thumbnailUrl?: string;
  metadata?: Record<string, any>;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Upload a document to Firebase Storage
 * 
 * @param userId User ID who uploaded the document
 * @param file Binary file data
 * @param fileName Name of the file
 * @param fileType MIME type of the file
 * @param folder Optional folder path within the user's documents
 * @param metadata Optional metadata for the file
 * @param tags Optional tags for the document
 * @returns Promise with success status and stored document or error
 */
export async function uploadDocument(
  userId: string,
  file: Blob | Uint8Array | ArrayBuffer,
  fileName: string,
  fileType: string,
  folder: string = 'documents',
  metadata: Record<string, any> = {},
  tags: string[] = []
): Promise<{
  success: boolean;
  data?: StoredDocument;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    // Create a unique file path
    const timestamp = new Date().getTime();
    const storagePath = `${userId}/${folder}/${timestamp}_${fileName}`;
    const storageRef = ref(storage, storagePath);
    
    // Upload the file
    await uploadBytes(storageRef, file, {
      contentType: fileType,
      customMetadata: {
        userId,
        uploadedAt: timestamp.toString(),
        ...metadata
      }
    });
    
    // Get the download URL
    const downloadUrl = await getDownloadURL(storageRef);
    
    // Create a document record in Firestore
    const documentRef = collection(firestore, 'documents');
    const docData = {
      userId,
      fileName,
      fileType,
      fileSize: file instanceof Blob ? file.size : file.byteLength,
      storagePath,
      downloadUrl,
      metadata,
      tags,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };
    
    const docRef = await addDoc(documentRef, docData);
    
    // Return the stored document
    const storedDocument: StoredDocument = {
      id: docRef.id,
      userId,
      fileName,
      fileType,
      fileSize: file instanceof Blob ? file.size : file.byteLength,
      storagePath,
      downloadUrl,
      metadata,
      tags,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    return {
      success: true,
      data: storedDocument
    };
  } catch (error) {
    console.error('Error uploading document:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get all documents for a user
 * 
 * @param userId User ID to get documents for
 * @param folder Optional folder to filter by
 * @param fileType Optional file type to filter by
 * @returns Promise with success status and array of stored documents or error
 */
export async function getUserDocuments(
  userId: string,
  folder?: string,
  fileType?: string
): Promise<{
  success: boolean;
  data?: StoredDocument[];
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }
    
    // Build query based on filters
    let documentsQuery = query(
      collection(firestore, 'documents'),
      where('userId', '==', userId)
    );
    
    // Add folder filter if provided
    if (folder) {
      documentsQuery = query(
        documentsQuery,
        where('storagePath', '>=', `${userId}/${folder}/`),
        where('storagePath', '<=', `${userId}/${folder}/\uf8ff`)
      );
    }
    
    // Add file type filter if provided
    if (fileType) {
      documentsQuery = query(
        documentsQuery,
        where('fileType', '==', fileType)
      );
    }
    
    const querySnapshot = await getDocs(documentsQuery);
    
    const documents: StoredDocument[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      documents.push({
        id: doc.id,
        userId: data.userId,
        fileName: data.fileName,
        fileType: data.fileType,
        fileSize: data.fileSize,
        storagePath: data.storagePath,
        downloadUrl: data.downloadUrl,
        thumbnailUrl: data.thumbnailUrl,
        metadata: data.metadata || {},
        tags: data.tags || [],
        createdAt: data.createdAt?.toDate?.() ? data.createdAt.toDate().toISOString() : data.createdAt,
        updatedAt: data.updatedAt?.toDate?.() ? data.updatedAt.toDate().toISOString() : data.updatedAt
      });
    });
    
    return {
      success: true,
      data: documents
    };
  } catch (error) {
    console.error('Error getting user documents:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get a single document by ID
 * 
 * @param documentId Document ID to get
 * @returns Promise with success status and stored document or error
 */
export async function getDocument(
  documentId: string
): Promise<{
  success: boolean;
  data?: StoredDocument;
  error?: string;
}> {
  try {
    const documentRef = doc(firestore, 'documents', documentId);
    const documentSnap = await getDoc(documentRef);
    
    if (!documentSnap.exists()) {
      return {
        success: false,
        error: 'Document not found'
      };
    }
    
    const data = documentSnap.data();
    
    const document: StoredDocument = {
      id: documentSnap.id,
      userId: data.userId,
      fileName: data.fileName,
      fileType: data.fileType,
      fileSize: data.fileSize,
      storagePath: data.storagePath,
      downloadUrl: data.downloadUrl,
      thumbnailUrl: data.thumbnailUrl,
      metadata: data.metadata || {},
      tags: data.tags || [],
      createdAt: data.createdAt?.toDate?.() ? data.createdAt.toDate().toISOString() : data.createdAt,
      updatedAt: data.updatedAt?.toDate?.() ? data.updatedAt.toDate().toISOString() : data.updatedAt
    };
    
    return {
      success: true,
      data: document
    };
  } catch (error) {
    console.error('Error getting document:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Update document metadata
 * 
 * @param documentId Document ID to update
 * @param updates Updates to apply (fileName, metadata, tags)
 * @returns Promise with success status and updated document or error
 */
export async function updateDocument(
  documentId: string,
  updates: {
    fileName?: string;
    metadata?: Record<string, any>;
    tags?: string[];
  }
): Promise<{
  success: boolean;
  data?: StoredDocument;
  error?: string;
}> {
  try {
    const documentRef = doc(firestore, 'documents', documentId);
    const documentSnap = await getDoc(documentRef);
    
    if (!documentSnap.exists()) {
      return {
        success: false,
        error: 'Document not found'
      };
    }
    
    // Build the update object
    const updateData: any = {
      updatedAt: serverTimestamp()
    };
    
    if (updates.fileName) {
      updateData.fileName = updates.fileName;
    }
    
    if (updates.metadata) {
      updateData.metadata = {
        ...documentSnap.data().metadata,
        ...updates.metadata
      };
    }
    
    if (updates.tags) {
      updateData.tags = updates.tags;
    }
    
    // Update the document
    await updateDoc(documentRef, updateData);
    
    // Get the updated document
    const updatedDocSnap = await getDoc(documentRef);
    const data = updatedDocSnap.data();
    
    if (!data) {
      return {
        success: false,
        error: 'Failed to retrieve updated document'
      };
    }
    
    const document: StoredDocument = {
      id: updatedDocSnap.id,
      userId: data.userId,
      fileName: data.fileName,
      fileType: data.fileType,
      fileSize: data.fileSize,
      storagePath: data.storagePath,
      downloadUrl: data.downloadUrl,
      thumbnailUrl: data.thumbnailUrl,
      metadata: data.metadata || {},
      tags: data.tags || [],
      createdAt: data.createdAt?.toDate?.() ? data.createdAt.toDate().toISOString() : data.createdAt,
      updatedAt: data.updatedAt?.toDate?.() ? data.updatedAt.toDate().toISOString() : data.updatedAt
    };
    
    return {
      success: true,
      data: document
    };
  } catch (error) {
    console.error('Error updating document:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Delete a document
 * 
 * @param documentId Document ID to delete
 * @returns Promise with success status or error
 */
export async function deleteDocument(
  documentId: string
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Get the document first to get the storage path
    const documentRef = doc(firestore, 'documents', documentId);
    const documentSnap = await getDoc(documentRef);
    
    if (!documentSnap.exists()) {
      return {
        success: false,
        error: 'Document not found'
      };
    }
    
    const data = documentSnap.data();
    const storagePath = data.storagePath;
    
    // Delete the file from storage
    const storageRef = ref(storage, storagePath);
    await deleteObject(storageRef);
    
    // Delete the document record
    await deleteDoc(documentRef);
    
    return {
      success: true
    };
  } catch (error) {
    console.error('Error deleting document:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Delete all documents in a folder
 * 
 * @param userId User ID who owns the documents
 * @param folder Folder path to delete
 * @returns Promise with success status and count of deleted documents or error
 */
export async function deleteFolderContents(
  userId: string,
  folder: string
): Promise<{
  success: boolean;
  deletedCount?: number;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }
    
    if (!folder) {
      return {
        success: false,
        error: 'Folder path is required'
      };
    }
    
    // Get all documents in the folder
    const folderPath = `${userId}/${folder}/`;
    const documentsQuery = query(
      collection(firestore, 'documents'),
      where('userId', '==', userId),
      where('storagePath', '>=', folderPath),
      where('storagePath', '<=', folderPath + '\uf8ff')
    );
    
    const querySnapshot = await getDocs(documentsQuery);
    
    if (querySnapshot.empty) {
      return {
        success: true,
        deletedCount: 0
      };
    }
    
    // Delete each document
    let deletedCount = 0;
    for (const docSnapshot of querySnapshot.docs) {
      const data = docSnapshot.data();
      const storagePath = data.storagePath;
      
      // Delete the file from storage
      const storageRef = ref(storage, storagePath);
      await deleteObject(storageRef);
      
      // Delete the document record
      await deleteDoc(docSnapshot.ref);
      
      deletedCount++;
    }
    
    return {
      success: true,
      deletedCount
    };
  } catch (error) {
    console.error('Error deleting folder contents:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
} 