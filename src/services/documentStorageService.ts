import * as FirebaseDocumentStorage from './documentStorageFirebaseService';
export type { StoredDocument } from './documentStorageFirebaseService';

export const getUserDocuments = async (
    folder?: string,
    fileType?: string
  ) => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      if (useFirebase) {
        // Use Firebase implementation
        return await FirebaseDocumentStorage.getUserDocuments(
          user.id,
          folder,
          fileType
        );
      } else {
        
        // For now, return an empty array
        return {
          success: true,
          data: []
        };
      }
    } catch (error) {
      console.error('Error getting user documents:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  
  const updateDocument = async (
    documentId: string,
    updates: {
      fileName?: string;
      metadata?: Record<string, any>;
      tags?: string[];
    }
  ) => {
    try {
      if (useFirebase) {
        // Use Firebase implementation
        return await FirebaseDocumentStorage.updateDocument(documentId, updates);
      } else {
        
        // For now, return an error
        return {
          success: false,
          error: 'Document update not implemented for firebase'
        };
      }
    } catch (error) {
      console.error('Error updating document:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  
  const deleteFolderContents = async (folder: string) => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      if (useFirebase) {
        // Use Firebase implementation
        return await FirebaseDocumentStorage.deleteFolderContents(user.id, folder);
      } else {
        
        // For now, return an error
        return {
          success: false,
          error: 'Folder deletion not implemented for firebase'
        };
      }
    } catch (error) {
      console.error('Error deleting folder contents:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  return {
    uploadDocument,
    getUserDocuments,
    getDocument,
    updateDocument,
    deleteDocument,
    deleteFolderContents
  };
}
