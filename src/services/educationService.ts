/**
 * Nutrition Education Service
 * Provides educational content about nutrition topics
 */

// Define the lesson item type
export interface NutritionLessonItem {
  id: string;
  title: string;
  subtitle: string;
  imageUrl?: string;
  duration: string;
  content: string[];
  quiz?: {
    question: string;
    options: string[];
    correctAnswerIndex: number;
    explanation: string;
  }[];
  completed?: boolean;
}

// Sample content
const nutritionLessons: NutritionLessonItem[] = [
  {
    id: 'macronutrients-101',
    title: 'Macronutrients 101',
    subtitle: 'Understanding Proteins, Carbs, and Fats',
    imageUrl: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061',
    duration: '4 min read',
    content: [
      'Macronutrients are the nutrients your body needs in large amounts to function properly: proteins, carbohydrates, and fats.',
      'Proteins are essential for building and repairing tissues. They are made up of amino acids, which are the building blocks of your body. Good sources include meat, fish, eggs, dairy, legumes, and nuts.',
      "Carbohydrates are your body's main source of energy. They break down into glucose, which fuels your brain and muscles. Complex carbs (whole grains, vegetables) are generally healthier than simple carbs (sugars).",
      "Fats are important for hormone production, nutrient absorption, and protecting your organs. Healthy fats include avocados, olive oil, nuts, and fatty fish. Not all fats are created equal – unsaturated fats are generally healthier than saturated or trans fats."
    ],
    quiz: [
      {
        question: 'Which macronutrient is primarily responsible for tissue repair?',
        options: ['Carbohydrates', 'Protein', 'Fat', 'Water'],
        correctAnswerIndex: 1,
        explanation: "Protein is essential for building and repairing tissues in your body. It's made up of amino acids, which are often called the \"building blocks\" of the body."
      },
      {
        question: 'Which of these is NOT a healthy source of fats?',
        options: ['Avocados', 'Trans fats in processed foods', 'Olive oil', 'Fatty fish'],
        correctAnswerIndex: 1,
        explanation: 'Trans fats, often found in processed foods, can raise bad cholesterol levels and lower good cholesterol. They are considered the least healthy type of fat.'
      }
    ]
  },
  {
    id: 'micronutrients-explained',
    title: 'Micronutrients Explained',
    subtitle: 'The Vital Role of Vitamins and Minerals',
    imageUrl: 'https://images.unsplash.com/photo-1610348725531-843dff563e2c',
    duration: '3 min read',
    content: [
      'Micronutrients are essential nutrients that your body needs in smaller amounts, including vitamins and minerals.',
      'Vitamins are organic compounds that help regulate vital bodily processes. There are 13 essential vitamins, including vitamins A, C, D, E, K, and the B vitamins. They support functions like immunity, energy production, and bone health.',
      'Minerals are inorganic elements that support functions like muscle contraction, nerve transmission, and maintaining fluid balance. Key minerals include calcium, iron, potassium, and magnesium.',
      'While your body needs only small amounts of micronutrients, deficiencies can lead to serious health problems. A varied diet rich in fruits, vegetables, lean proteins, and whole grains typically provides adequate micronutrients.'
    ],
    quiz: [
      {
        question: 'Which vitamin is primarily produced by your skin when exposed to sunlight?',
        options: ['Vitamin A', 'Vitamin C', 'Vitamin D', 'Vitamin K'],
        correctAnswerIndex: 2,
        explanation: "Vitamin D is produced in your skin in response to sunlight. It's essential for calcium absorption and bone health."
      },
      {
        question: 'Which mineral is most important for oxygen transport in the blood?',
        options: ['Calcium', 'Iron', 'Potassium', 'Zinc'],
        correctAnswerIndex: 1,
        explanation: 'Iron is a crucial component of hemoglobin, the protein in red blood cells that carries oxygen from your lungs to all parts of your body.'
      }
    ]
  },
  {
    id: 'hydration-importance',
    title: 'The Importance of Hydration',
    subtitle: "Understanding Water's Role in Nutrition",
    imageUrl: 'https://images.unsplash.com/photo-1536939459926-301728717817',
    duration: '2 min read',
    content: [
      "Water is often overlooked as a nutrient, but it's crucial for nearly every bodily function. It makes up about 60% of your body weight.",
      'Water helps regulate body temperature, lubricate joints, remove waste through urination and bowel movements, and transport nutrients to cells.',
      'The classic recommendation of eight 8-ounce glasses per day (about 2 liters) is a reasonable goal, but needs vary based on activity level, climate, and individual factors.',
      "Signs of dehydration include thirst, dark-colored urine, fatigue, dizziness, and dry mouth. Don't wait until you're thirsty to drink water – by then, you're already mildly dehydrated."
    ],
    quiz: [
      {
        question: 'What percentage of the human body is approximately water?',
        options: ['40%', '60%', '75%', '90%'],
        correctAnswerIndex: 1,
        explanation: 'Water makes up about 60% of the adult human body. This percentage is higher in infants (around 75%) and lower in older adults.'
      },
      {
        question: 'Which of these is NOT a function of water in the body?',
        options: ['Regulating body temperature', 'Lubricating joints', 'Producing vitamin D', 'Removing waste products'],
        correctAnswerIndex: 2,
        explanation: 'While water is essential for many bodily functions, vitamin D production happens in the skin when exposed to sunlight and is not directly related to hydration.'
      }
    ]
  }
];

/**
 * Get all nutrition lessons
 */
export function getNutritionLessons(): NutritionLessonItem[] {
  return nutritionLessons;
}

/**
 * Get featured nutrition lessons (limited subset)
 */
export function getFeaturedNutritionLessons(limit: number = 2): NutritionLessonItem[] {
  return nutritionLessons.slice(0, limit);
}

/**
 * Get a specific lesson by ID
 */
export function getNutritionLessonById(id: string): NutritionLessonItem | undefined {
  return nutritionLessons.find(lesson => lesson.id === id);
}

/**
 * Mark a lesson as completed
 */
export function completeLessonById(id: string): void {
  const lesson = nutritionLessons.find(lesson => lesson.id === id);
  if (lesson) {
    lesson.completed = true;
  }
}

/**
 * Get completed lessons
 */
export function getCompletedLessons(): NutritionLessonItem[] {
  return nutritionLessons.filter(lesson => lesson.completed);
}

/**
 * Get progress stats
 */
export function getEducationProgress(): { completed: number, total: number, percentage: number } {
  const completed = nutritionLessons.filter(lesson => lesson.completed).length;
  const total = nutritionLessons.length;
  const percentage = Math.round((completed / total) * 100);
  
  return {
    completed,
    total,
    percentage
  };
} 