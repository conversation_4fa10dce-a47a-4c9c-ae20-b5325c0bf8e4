import AsyncStorage from '@react-native-async-storage/async-storage';
import { FoodAnalysisData } from '@/types/scan.types';
import { Recipe } from '@/services/openai/recipeTypes';

// Constants for error recovery storage
const OPERATION_STATE_KEY = 'operation_state';
const SCAN_RECOVERY_DATA_KEY = 'scan_recovery_data';
const RECIPE_RECOVERY_DATA_KEY = 'recipe_recovery_data';
const MAX_RETRY_ATTEMPTS = 3;
const MAX_RECOVERY_AGE_MS = 24 * 60 * 60 * 1000; // 24 hours

// Operation types that we can recover from
export enum OperationType {
  SCAN = 'scan',
  RECIPE_GENERATION = 'recipe_generation'
}

// Operation states
export enum OperationState {
  IDLE = 'idle',
  IN_PROGRESS = 'in_progress',
  FAILED = 'failed',
  COMPLETED = 'completed'
}

// Error types for different kinds of failures
export enum ErrorType {
  NETWORK = 'network',
  TIMEOUT = 'timeout',
  API_ERROR = 'api_error',
  PERMISSION_DENIED = 'permission_denied',
  UNKNOWN = 'unknown'
}

// Recovery data interface for scan operations
interface ScanRecoveryData {
  timestamp: number;
  imageUri: string;
  partialResults?: Partial<FoodAnalysisData>;
  errorType?: ErrorType;
  retryCount: number;
  retryTimestamp?: number;
}

// Recovery data interface for recipe generation operations
interface RecipeRecoveryData {
  timestamp: number;
  originalFoodName: string;
  nutritionalInfo: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  dietaryPreferences?: string[];
  allergies?: string[];
  partialResults?: Recipe[];
  errorType?: ErrorType;
  retryCount: number;
  retryTimestamp?: number;
}

// Operation state tracking
interface OperationStateData {
  scanOperation: {
    state: OperationState;
    startTime?: number;
    endTime?: number;
  };
  recipeOperation: {
    state: OperationState;
    startTime?: number;
    endTime?: number;
  };
}

/**
 * Initialize the operation state tracking
 * This should be called when the app starts
 */
export async function initErrorRecovery(): Promise<void> {
  try {
    // Check if we already have operation state data
    const stateData = await getOperationState();
    
    // If we have operations in progress, mark them as failed
    // This would happen if the app crashed or was force-closed
    let stateChanged = false;
    
    if (stateData.scanOperation.state === OperationState.IN_PROGRESS) {
      stateData.scanOperation.state = OperationState.FAILED;
      stateData.scanOperation.endTime = Date.now();
      stateChanged = true;
      
      console.log('Detected interrupted scan operation - marked as failed');
    }
    
    if (stateData.recipeOperation.state === OperationState.IN_PROGRESS) {
      stateData.recipeOperation.state = OperationState.FAILED;
      stateData.recipeOperation.endTime = Date.now();
      stateChanged = true;
      
      console.log('Detected interrupted recipe generation operation - marked as failed');
    }
    
    // Save updated state if needed
    if (stateChanged) {
      await saveOperationState(stateData);
    }
    
    // Clean up any recovery data older than 24 hours
    await cleanupOldRecoveryData();
    
    console.log('Error recovery system initialized');
  } catch (error) {
    console.error('Error initializing error recovery:', error);
  }
}

/**
 * Get the current operation state
 * @returns The current operation state data
 */
async function getOperationState(): Promise<OperationStateData> {
  try {
    const stateJson = await AsyncStorage.getItem(OPERATION_STATE_KEY);
    
    if (stateJson) {
      return JSON.parse(stateJson);
    }
  } catch (error) {
    console.error('Error getting operation state:', error);
  }
  
  // Return default state if we couldn't get the saved state
  return {
    scanOperation: {
      state: OperationState.IDLE
    },
    recipeOperation: {
      state: OperationState.IDLE
    }
  };
}

/**
 * Save the operation state
 * @param state The operation state to save
 */
async function saveOperationState(state: OperationStateData): Promise<void> {
  try {
    await AsyncStorage.setItem(OPERATION_STATE_KEY, JSON.stringify(state));
  } catch (error) {
    console.error('Error saving operation state:', error);
  }
}

/**
 * Mark an operation as started
 * @param operationType The type of operation being started
 */
export async function startOperation(operationType: OperationType): Promise<void> {
  try {
    const state = await getOperationState();
    const now = Date.now();
    
    if (operationType === OperationType.SCAN) {
      state.scanOperation = {
        state: OperationState.IN_PROGRESS,
        startTime: now
      };
    } else if (operationType === OperationType.RECIPE_GENERATION) {
      state.recipeOperation = {
        state: OperationState.IN_PROGRESS,
        startTime: now
      };
    }
    
    await saveOperationState(state);
    console.log(`Started ${operationType} operation`);
  } catch (error) {
    console.error(`Error marking ${operationType} operation as started:`, error);
  }
}

/**
 * Mark an operation as completed
 * @param operationType The type of operation being completed
 */
export async function completeOperation(operationType: OperationType): Promise<void> {
  try {
    const state = await getOperationState();
    const now = Date.now();
    
    if (operationType === OperationType.SCAN) {
      state.scanOperation = {
        state: OperationState.COMPLETED,
        startTime: state.scanOperation.startTime,
        endTime: now
      };
      
      // Clear scan recovery data when successfully completed
      await AsyncStorage.removeItem(SCAN_RECOVERY_DATA_KEY);
    } else if (operationType === OperationType.RECIPE_GENERATION) {
      state.recipeOperation = {
        state: OperationState.COMPLETED,
        startTime: state.recipeOperation.startTime,
        endTime: now
      };
      
      // Clear recipe recovery data when successfully completed
      await AsyncStorage.removeItem(RECIPE_RECOVERY_DATA_KEY);
    }
    
    await saveOperationState(state);
    console.log(`Completed ${operationType} operation`);
  } catch (error) {
    console.error(`Error marking ${operationType} operation as completed:`, error);
  }
}

/**
 * Mark an operation as failed and save recovery data
 * @param operationType The type of operation that failed
 * @param errorType The type of error that occurred
 * @param recoveryData Data needed to recover from the failure
 */
export async function failOperation(
  operationType: OperationType,
  errorType: ErrorType,
  recoveryData: ScanRecoveryData | RecipeRecoveryData
): Promise<void> {
  try {
    const state = await getOperationState();
    const now = Date.now();
    
    if (operationType === OperationType.SCAN) {
      state.scanOperation = {
        state: OperationState.FAILED,
        startTime: state.scanOperation.startTime,
        endTime: now
      };
      
      // Save scan recovery data
      const scanData = recoveryData as ScanRecoveryData;
      await saveScanRecoveryData({
        ...scanData,
        errorType,
        timestamp: now,
        retryCount: (scanData.retryCount || 0) + 1
      });
    } else if (operationType === OperationType.RECIPE_GENERATION) {
      state.recipeOperation = {
        state: OperationState.FAILED,
        startTime: state.recipeOperation.startTime,
        endTime: now
      };
      
      // Save recipe recovery data
      const recipeData = recoveryData as RecipeRecoveryData;
      await saveRecipeRecoveryData({
        ...recipeData,
        errorType,
        timestamp: now,
        retryCount: (recipeData.retryCount || 0) + 1
      });
    }
    
    await saveOperationState(state);
    console.log(`Failed ${operationType} operation with error type: ${errorType}`);
  } catch (error) {
    console.error(`Error marking ${operationType} operation as failed:`, error);
  }
}

/**
 * Save scan recovery data
 * @param data The scan recovery data to save
 */
async function saveScanRecoveryData(data: ScanRecoveryData): Promise<void> {
  try {
    await AsyncStorage.setItem(SCAN_RECOVERY_DATA_KEY, JSON.stringify(data));
  } catch (error) {
    console.error('Error saving scan recovery data:', error);
  }
}

/**
 * Save recipe recovery data
 * @param data The recipe recovery data to save
 */
async function saveRecipeRecoveryData(data: RecipeRecoveryData): Promise<void> {
  try {
    await AsyncStorage.setItem(RECIPE_RECOVERY_DATA_KEY, JSON.stringify(data));
  } catch (error) {
    console.error('Error saving recipe recovery data:', error);
  }
}

/**
 * Check if there is a recoverable scan operation
 * @returns Scan recovery data if available, null otherwise
 */
export async function getRecoverableScan(): Promise<ScanRecoveryData | null> {
  try {
    const dataJson = await AsyncStorage.getItem(SCAN_RECOVERY_DATA_KEY);
    
    if (!dataJson) {
      return null;
    }
    
    const data: ScanRecoveryData = JSON.parse(dataJson);
    
    // Check if the recovery data is still valid
    if (Date.now() - data.timestamp > MAX_RECOVERY_AGE_MS) {
      await AsyncStorage.removeItem(SCAN_RECOVERY_DATA_KEY);
      return null;
    }
    
    // Check if we've exceeded the retry count
    if (data.retryCount > MAX_RETRY_ATTEMPTS) {
      console.log('Exceeded maximum retry attempts for scan recovery');
      await AsyncStorage.removeItem(SCAN_RECOVERY_DATA_KEY);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error getting recoverable scan:', error);
    return null;
  }
}

/**
 * Check if there is a recoverable recipe generation operation
 * @returns Recipe recovery data if available, null otherwise
 */
export async function getRecoverableRecipe(): Promise<RecipeRecoveryData | null> {
  try {
    const dataJson = await AsyncStorage.getItem(RECIPE_RECOVERY_DATA_KEY);
    
    if (!dataJson) {
      return null;
    }
    
    const data: RecipeRecoveryData = JSON.parse(dataJson);
    
    // Check if the recovery data is still valid
    if (Date.now() - data.timestamp > MAX_RECOVERY_AGE_MS) {
      await AsyncStorage.removeItem(RECIPE_RECOVERY_DATA_KEY);
      return null;
    }
    
    // Check if we've exceeded the retry count
    if (data.retryCount > MAX_RETRY_ATTEMPTS) {
      console.log('Exceeded maximum retry attempts for recipe recovery');
      await AsyncStorage.removeItem(RECIPE_RECOVERY_DATA_KEY);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error getting recoverable recipe:', error);
    return null;
  }
}

/**
 * Mark a scan recovery as being retried
 */
export async function markScanRetryAttempt(): Promise<void> {
  try {
    const data = await getRecoverableScan();
    
    if (data) {
      await saveScanRecoveryData({
        ...data,
        retryCount: data.retryCount + 1,
        retryTimestamp: Date.now()
      });
    }
  } catch (error) {
    console.error('Error marking scan retry attempt:', error);
  }
}

/**
 * Mark a recipe recovery as being retried
 */
export async function markRecipeRetryAttempt(): Promise<void> {
  try {
    const data = await getRecoverableRecipe();
    
    if (data) {
      await saveRecipeRecoveryData({
        ...data,
        retryCount: data.retryCount + 1,
        retryTimestamp: Date.now()
      });
    }
  } catch (error) {
    console.error('Error marking recipe retry attempt:', error);
  }
}

/**
 * Clean up recovery data that is older than the maximum recovery age
 */
async function cleanupOldRecoveryData(): Promise<void> {
  try {
    // Check scan recovery data
    const scanDataJson = await AsyncStorage.getItem(SCAN_RECOVERY_DATA_KEY);
    
    if (scanDataJson) {
      const scanData: ScanRecoveryData = JSON.parse(scanDataJson);
      
      if (Date.now() - scanData.timestamp > MAX_RECOVERY_AGE_MS) {
        await AsyncStorage.removeItem(SCAN_RECOVERY_DATA_KEY);
        console.log('Cleaned up old scan recovery data');
      }
    }
    
    // Check recipe recovery data
    const recipeDataJson = await AsyncStorage.getItem(RECIPE_RECOVERY_DATA_KEY);
    
    if (recipeDataJson) {
      const recipeData: RecipeRecoveryData = JSON.parse(recipeDataJson);
      
      if (Date.now() - recipeData.timestamp > MAX_RECOVERY_AGE_MS) {
        await AsyncStorage.removeItem(RECIPE_RECOVERY_DATA_KEY);
        console.log('Cleaned up old recipe recovery data');
      }
    }
  } catch (error) {
    console.error('Error cleaning up old recovery data:', error);
  }
}

/**
 * Update partial scan results in the recovery data
 * @param partialResults Partial food analysis results
 */
export async function updateScanPartialResults(partialResults: Partial<FoodAnalysisData>): Promise<void> {
  try {
    const data = await getRecoverableScan();
    
    if (data) {
      await saveScanRecoveryData({
        ...data,
        partialResults: {
          ...data.partialResults,
          ...partialResults
        }
      });
    }
  } catch (error) {
    console.error('Error updating scan partial results:', error);
  }
}

/**
 * Update partial recipe results in the recovery data
 * @param partialResults Partial recipe results
 */
export async function updateRecipePartialResults(partialResults: Recipe[]): Promise<void> {
  try {
    const data = await getRecoverableRecipe();
    
    if (data) {
      await saveRecipeRecoveryData({
        ...data,
        partialResults
      });
    }
  } catch (error) {
    console.error('Error updating recipe partial results:', error);
  }
}

/**
 * Determine the appropriate error type from an error object
 * @param error The error object
 * @returns The error type
 */
export function determineErrorType(error: unknown): ErrorType {
  if (!error) {
    return ErrorType.UNKNOWN;
  }
  
  const errorMessage = error instanceof Error ? error.message.toLowerCase() : String(error).toLowerCase();
  
  if (errorMessage.includes('network') || 
      errorMessage.includes('connection') || 
      errorMessage.includes('offline') || 
      errorMessage.includes('internet')) {
    return ErrorType.NETWORK;
  }
  
  if (errorMessage.includes('timeout') || 
      errorMessage.includes('timed out')) {
    return ErrorType.TIMEOUT;
  }
  
  if (errorMessage.includes('permission') || 
      errorMessage.includes('access denied') || 
      errorMessage.includes('not allowed')) {
    return ErrorType.PERMISSION_DENIED;
  }
  
  if (errorMessage.includes('api') || 
      errorMessage.includes('openai') || 
      errorMessage.includes('rate limit') || 
      errorMessage.includes('status code')) {
    return ErrorType.API_ERROR;
  }
  
  return ErrorType.UNKNOWN;
}

/**
 * Get a user-friendly error message based on the error type
 * @param errorType The type of error
 * @returns A user-friendly error message
 */
export function getErrorMessage(errorType: ErrorType): string {
  switch (errorType) {
    case ErrorType.NETWORK:
      return 'Network connection error. Please check your internet connection and try again.';
    case ErrorType.TIMEOUT:
      return 'The request timed out. This might be due to a slow connection or high server load.';
    case ErrorType.API_ERROR:
      return 'There was an error with the service. We\'re working on it!';
    case ErrorType.PERMISSION_DENIED:
      return 'Permission denied. Please grant the required permissions to use this feature.';
    case ErrorType.UNKNOWN:
    default:
      return 'An unexpected error occurred. Please try again later.';
  }
}

/**
 * Get a suggestion for how to resolve an error
 * @param errorType The type of error
 * @returns A suggestion for resolving the error
 */
export function getErrorResolutionSuggestion(errorType: ErrorType): string {
  switch (errorType) {
    case ErrorType.NETWORK:
      return 'Connect to a stable WiFi network or check your mobile data connection.';
    case ErrorType.TIMEOUT:
      return 'Try again when you have a stronger connection, or try using a smaller image.';
    case ErrorType.API_ERROR:
      return 'Please try again in a few minutes, or contact support if the problem persists.';
    case ErrorType.PERMISSION_DENIED:
      return 'Go to your device settings and ensure this app has the required permissions.';
    case ErrorType.UNKNOWN:
    default:
      return 'Restart the app or your device, or contact support if the problem persists.';
  }
} 