import { trackError } from './posthogService';
import { Platform } from 'react-native';

export class ErrorTracker {
  private static instance: ErrorTracker;
  
  private constructor() {
    this.setupGlobalErrorHandling();
  }
  
  static getInstance(): ErrorTracker {
    if (!ErrorTracker.instance) {
      ErrorTracker.instance = new ErrorTracker();
    }
    return ErrorTracker.instance;
  }
  
  private setupGlobalErrorHandling() {
    if (__DEV__) {
      // In development, let errors bubble up for debugging
      return;
    }
    
    // Handle unhandled promise rejections
    const originalHandler = global.onunhandledrejection;
    global.onunhandledrejection = (event: any) => {
      this.trackError(
        new Error(event.reason?.message || 'Unhandled Promise Rejection'),
        {
          type: 'unhandled_promise_rejection',
          reason: event.reason,
          promise: event.promise
        }
      );
      
      if (originalHandler) {
        originalHandler(event);
      }
    };
    
    // React Native global error handler
    const originalErrorHandler = ErrorUtils.getGlobalHandler();
    ErrorUtils.setGlobalHandler((error: Error, isFatal?: boolean) => {
      this.trackError(error, {
        type: 'global_error',
        isFatal,
        platform: Platform.OS
      });
      
      if (originalErrorHandler) {
        originalErrorHandler(error, isFatal);
      }
    });
  }
  
  trackError(error: Error, context?: Record<string, any>): void {
    try {
      // Sanitize error data
      const sanitizedContext = {
        ...context,
        platform: Platform.OS,
        version: Platform.Version,
        timestamp: new Date().toISOString(),
        // Add app-specific context
        feature: context?.feature || 'unknown',
        action: context?.action || 'unknown'
      };
      
      // Track with PostHog
      trackError(error, sanitizedContext);
      
      // In production, you might also want to:
      // 1. Send to Sentry or similar
      // 2. Log to a remote service
      // 3. Show user-friendly error message
      
    } catch (trackingError) {
      // Don't let error tracking errors crash the app
      console.error('Failed to track error:', trackingError);
    }
  }
  
  // Track specific app errors with context
  trackFeatureError(feature: string, action: string, error: Error, details?: any): void {
    this.trackError(error, {
      feature,
      action,
      details,
      severity: 'error'
    });
  }
  
  // Track warnings that don't crash the app
  trackWarning(message: string, context?: Record<string, any>): void {
    trackError(new Error(message), {
      ...context,
      severity: 'warning',
      isWarning: true
    });
  }
  
  // Track API errors specifically
  trackApiError(endpoint: string, error: Error, statusCode?: number): void {
    this.trackError(error, {
      type: 'api_error',
      endpoint,
      statusCode,
      feature: 'api',
      action: 'request'
    });
  }
  
  // Track user actions that resulted in errors
  trackUserActionError(action: string, error: Error, userInput?: any): void {
    this.trackError(error, {
      type: 'user_action_error',
      action,
      userInput: userInput ? JSON.stringify(userInput) : undefined,
      feature: 'user_interaction'
    });
  }
}

export const errorTracker = ErrorTracker.getInstance();

// Export convenience functions
export const trackFeatureError = (feature: string, action: string, error: Error, details?: any) => 
  errorTracker.trackFeatureError(feature, action, error, details);

export const trackApiError = (endpoint: string, error: Error, statusCode?: number) =>
  errorTracker.trackApiError(endpoint, error, statusCode);

export const trackUserActionError = (action: string, error: Error, userInput?: any) =>
  errorTracker.trackUserActionError(action, error, userInput);

export const trackWarning = (message: string, context?: Record<string, any>) =>
  errorTracker.trackWarning(message, context);