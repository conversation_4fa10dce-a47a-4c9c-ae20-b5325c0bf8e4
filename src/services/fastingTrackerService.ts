import AsyncStorage from '@react-native-async-storage/async-storage';
import { add, differenceInMinutes, format, isAfter, isBefore, parseISO } from 'date-fns';
import * as Notifications from 'expo-notifications';

// Storage keys
const FASTING_SCHEDULE_KEY = 'fasting_schedule';
const ACTIVE_FAST_KEY = 'active_fast';
const FASTING_HISTORY_KEY = 'fasting_history';

// Define fasting types
export enum FastingType {
  SIXTEEN_EIGHT = '16:8', // 16 hours fasting, 8 hours eating
  EIGHTEEN_SIX = '18:6', // 18 hours fasting, 6 hours eating
  FIVE_TWO = '5:2', // 5 days normal eating, 2 days restricted calories
  CUSTOM = 'Custom' // Custom schedule
}

// Define the fasting schedule interface
export interface FastingSchedule {
  type: FastingType;
  fastingHours: number;
  eatingHours: number;
  startTime?: string; // Format: HH:mm - when the fasting period starts each day
  fastingDays?: number[]; // For 5:2, which days to fast (0 = Sunday, 6 = Saturday)
  customSchedule?: {
    fastingStartTime: string; // Format: HH:mm
    fastingEndTime: string; // Format: HH:mm
  }[];
  enableNotifications: boolean;
}

// Define the active fast interface
export interface ActiveFast {
  startTime: string; // ISO string
  expectedEndTime: string; // ISO string
  type: FastingType;
  isCompleted: boolean;
  actualEndTime?: string; // ISO string (if completed)
  notes?: string;
}

// Define the completed fast interface for history
export interface CompletedFast {
  id: string;
  startTime: string; // ISO string
  endTime: string; // ISO string
  type: FastingType;
  duration: number; // in minutes
  targetDuration: number; // in minutes
  wasCompleted: boolean; // Whether the fast was completed as scheduled or ended early
  notes?: string;
}

/**
 * Get the current fasting schedule
 */
export async function getFastingSchedule(): Promise<FastingSchedule | null> {
  try {
    const scheduleJson = await AsyncStorage.getItem(FASTING_SCHEDULE_KEY);
    if (!scheduleJson) return null;
    
    return JSON.parse(scheduleJson) as FastingSchedule;
  } catch (error) {
    console.error('Error retrieving fasting schedule:', error);
    return null;
  }
}

/**
 * Save a fasting schedule
 */
export async function saveFastingSchedule(schedule: FastingSchedule): Promise<boolean> {
  try {
    await AsyncStorage.setItem(FASTING_SCHEDULE_KEY, JSON.stringify(schedule));
    
    // Schedule notifications if enabled
    if (schedule.enableNotifications) {
      await scheduleNotifications(schedule);
    } else {
      await cancelAllNotifications();
    }
    
    return true;
  } catch (error) {
    console.error('Error saving fasting schedule:', error);
    return false;
  }
}

/**
 * Get predefined fasting schedules
 */
export function getPredefinedSchedules(): FastingSchedule[] {
  return [
    {
      type: FastingType.SIXTEEN_EIGHT,
      fastingHours: 16,
      eatingHours: 8,
      startTime: '20:00', // 8 PM
      enableNotifications: true
    },
    {
      type: FastingType.EIGHTEEN_SIX,
      fastingHours: 18,
      eatingHours: 6,
      startTime: '18:00', // 6 PM
      enableNotifications: true
    },
    {
      type: FastingType.FIVE_TWO,
      fastingHours: 24,
      eatingHours: 0, // On fasting days, restricted calories but not technically a time window
      fastingDays: [1, 4], // Monday and Thursday
      enableNotifications: true
    }
  ];
}

/**
 * Create a custom fasting schedule
 */
export function createCustomSchedule(
  fastingHours: number,
  eatingHours: number,
  startTime: string // Format: HH:mm
): FastingSchedule {
  return {
    type: FastingType.CUSTOM,
    fastingHours,
    eatingHours,
    startTime,
    enableNotifications: true
  };
}

/**
 * Get the current active fast
 */
export async function getActiveFast(): Promise<ActiveFast | null> {
  try {
    const fastJson = await AsyncStorage.getItem(ACTIVE_FAST_KEY);
    if (!fastJson) return null;
    
    return JSON.parse(fastJson) as ActiveFast;
  } catch (error) {
    console.error('Error retrieving active fast:', error);
    return null;
  }
}

/**
 * Start a new fast
 */
export async function startFast(type: FastingType): Promise<ActiveFast | null> {
  try {
    // Get the current schedule
    const schedule = await getFastingSchedule();
    if (!schedule) return null;
    
    const now = new Date();
    const fastingHours = schedule.fastingHours;
    
    // Calculate the expected end time
    const expectedEndTime = add(now, { hours: fastingHours });
    
    const newFast: ActiveFast = {
      startTime: now.toISOString(),
      expectedEndTime: expectedEndTime.toISOString(),
      type,
      isCompleted: false
    };
    
    await AsyncStorage.setItem(ACTIVE_FAST_KEY, JSON.stringify(newFast));
    
    // Schedule end fast notification
    if (schedule.enableNotifications) {
      await scheduleEndFastNotification(expectedEndTime);
    }
    
    return newFast;
  } catch (error) {
    console.error('Error starting fast:', error);
    return null;
  }
}

/**
 * End the current fast
 */
export async function endFast(notes?: string): Promise<CompletedFast | null> {
  try {
    const activeFast = await getActiveFast();
    if (!activeFast) return null;
    
    const now = new Date();
    const startTime = parseISO(activeFast.startTime);
    const expectedEndTime = parseISO(activeFast.expectedEndTime);
    
    // Check if the fast was completed as scheduled
    const wasCompleted = isAfter(now, expectedEndTime);
    
    // Update the active fast with completion info
    activeFast.isCompleted = true;
    activeFast.actualEndTime = now.toISOString();
    if (notes) activeFast.notes = notes;
    
    // Save the updated active fast (for temporary reference)
    await AsyncStorage.setItem(ACTIVE_FAST_KEY, JSON.stringify(activeFast));
    
    // Create a completed fast record
    const completedFast: CompletedFast = {
      id: Date.now().toString(),
      startTime: activeFast.startTime,
      endTime: now.toISOString(),
      type: activeFast.type,
      duration: differenceInMinutes(now, startTime),
      targetDuration: differenceInMinutes(expectedEndTime, startTime),
      wasCompleted,
      notes
    };
    
    // Add to fasting history
    await addToFastingHistory(completedFast);
    
    // Clear active fast after a short delay (to allow UI to show completion)
    setTimeout(async () => {
      await AsyncStorage.removeItem(ACTIVE_FAST_KEY);
    }, 3000);
    
    // Cancel the end fast notification
    await cancelAllNotifications();
    
    return completedFast;
  } catch (error) {
    console.error('Error ending fast:', error);
    return null;
  }
}

/**
 * Add a completed fast to the history
 */
async function addToFastingHistory(fast: CompletedFast): Promise<boolean> {
  try {
    const historyJson = await AsyncStorage.getItem(FASTING_HISTORY_KEY);
    let history: CompletedFast[] = [];
    
    if (historyJson) {
      history = JSON.parse(historyJson);
    }
    
    // Add the new fast to the history
    history.push(fast);
    
    // Sort by date (newest first)
    history.sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime());
    
    // Save the updated history
    await AsyncStorage.setItem(FASTING_HISTORY_KEY, JSON.stringify(history));
    
    return true;
  } catch (error) {
    console.error('Error adding to fasting history:', error);
    return false;
  }
}

/**
 * Get the fasting history
 */
export async function getFastingHistory(): Promise<CompletedFast[]> {
  try {
    const historyJson = await AsyncStorage.getItem(FASTING_HISTORY_KEY);
    if (!historyJson) return [];
    
    return JSON.parse(historyJson) as CompletedFast[];
  } catch (error) {
    console.error('Error retrieving fasting history:', error);
    return [];
  }
}

/**
 * Get the fasting history for a specific date range
 */
export async function getFastingHistoryByDateRange(
  startDate: Date,
  endDate: Date
): Promise<CompletedFast[]> {
  try {
    const history = await getFastingHistory();
    
    return history.filter((fast) => {
      const fastDate = parseISO(fast.startTime);
      return isAfter(fastDate, startDate) && isBefore(fastDate, endDate);
    });
  } catch (error) {
    console.error('Error retrieving fasting history by date range:', error);
    return [];
  }
}

/**
 * Clear all fasting data (for testing or reset)
 */
export async function clearAllFastingData(): Promise<boolean> {
  try {
    await AsyncStorage.removeItem(FASTING_SCHEDULE_KEY);
    await AsyncStorage.removeItem(ACTIVE_FAST_KEY);
    await AsyncStorage.removeItem(FASTING_HISTORY_KEY);
    await cancelAllNotifications();
    
    return true;
  } catch (error) {
    console.error('Error clearing fasting data:', error);
    return false;
  }
}

/**
 * Calculate fasting window and eating window based on schedule
 */
export function calculateFastingWindows(schedule: FastingSchedule): {
  fastingWindow: { start: string; end: string };
  eatingWindow: { start: string; end: string };
} {
  if (!schedule.startTime) {
    return {
      fastingWindow: { start: '00:00', end: '00:00' },
      eatingWindow: { start: '00:00', end: '00:00' }
    };
  }
  
  // Parse the start time
  const [startHour, startMinute] = schedule.startTime.split(':').map(Number);
  
  // Calculate fasting end time
  const fastingEndHour = (startHour + schedule.fastingHours) % 24;
  const fastingEndTime = `${fastingEndHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')}`;
  
  // Calculate eating end time (which is the same as fasting start time)
  const eatingEndTime = schedule.startTime;
  
  // Calculate eating start time (which is the same as fasting end time)
  const eatingStartTime = fastingEndTime;
  
  return {
    fastingWindow: {
      start: eatingEndTime,
      end: fastingEndTime
    },
    eatingWindow: {
      start: eatingStartTime,
      end: eatingEndTime
    }
  };
}

/**
 * Check if a fast is due to start based on the schedule
 */
export function shouldStartFast(schedule: FastingSchedule): boolean {
  if (!schedule.startTime) return false;
  
  const now = new Date();
  const currentTimeStr = format(now, 'HH:mm');
  
  // For 5:2 fasting, check if today is a fasting day
  if (schedule.type === FastingType.FIVE_TWO) {
    const currentDay = now.getDay(); // 0 = Sunday, 6 = Saturday
    if (!schedule.fastingDays?.includes(currentDay)) {
      return false;
    }
  }
  
  // Check if current time matches the start time (with 5 minute margin)
  const [currentHour, currentMinute] = currentTimeStr.split(':').map(Number);
  const [scheduleHour, scheduleMinute] = schedule.startTime.split(':').map(Number);
  
  const currentTotalMinutes = currentHour * 60 + currentMinute;
  const scheduleTotalMinutes = scheduleHour * 60 + scheduleMinute;
  
  // Start fasting if within 5 minutes of scheduled time
  return Math.abs(currentTotalMinutes - scheduleTotalMinutes) <= 5;
}

/**
 * Get the current fasting status (in fasting window or eating window)
 */
export async function getCurrentFastingStatus(): Promise<{
  inFastingWindow: boolean;
  timeRemaining: number; // in minutes
  currentWindow: 'fasting' | 'eating';
}> {
  const activeFast = await getActiveFast();
  
  if (activeFast && !activeFast.isCompleted) {
    // We're in an active fast
    const now = new Date();
    const endTime = parseISO(activeFast.expectedEndTime);
    
    // Calculate time remaining in minutes
    const timeRemaining = differenceInMinutes(endTime, now);
    
    return {
      inFastingWindow: true,
      timeRemaining: timeRemaining > 0 ? timeRemaining : 0,
      currentWindow: 'fasting'
    };
  } else {
    // Check if we're in an eating window based on schedule
    const schedule = await getFastingSchedule();
    
    if (!schedule || !schedule.startTime) {
      return {
        inFastingWindow: false,
        timeRemaining: 0,
        currentWindow: 'eating'
      };
    }
    
    const windows = calculateFastingWindows(schedule);
    const now = new Date();
    const currentTimeStr = format(now, 'HH:mm');
    
    // Check if current time is in fasting window
    const isInFastingWindow = isTimeInWindow(
      currentTimeStr,
      windows.fastingWindow.start,
      windows.fastingWindow.end
    );
    
    // Calculate time remaining until the window ends
    const timeRemaining = calculateTimeRemainingInWindow(
      currentTimeStr,
      isInFastingWindow ? windows.fastingWindow.end : windows.eatingWindow.end
    );
    
    return {
      inFastingWindow: isInFastingWindow,
      timeRemaining,
      currentWindow: isInFastingWindow ? 'fasting' : 'eating'
    };
  }
}

/**
 * Check if a time is within a time window
 */
function isTimeInWindow(
  timeToCheck: string,
  windowStart: string,
  windowEnd: string
): boolean {
  // Convert all times to minutes since midnight
  const timeMinutes = convertTimeToMinutes(timeToCheck);
  const startMinutes = convertTimeToMinutes(windowStart);
  const endMinutes = convertTimeToMinutes(windowEnd);
  
  // Handle windows that span midnight
  if (startMinutes > endMinutes) {
    return timeMinutes >= startMinutes || timeMinutes <= endMinutes;
  } else {
    return timeMinutes >= startMinutes && timeMinutes <= endMinutes;
  }
}

/**
 * Calculate time remaining until the window ends
 */
function calculateTimeRemainingInWindow(
  currentTime: string,
  endTime: string
): number {
  // Convert times to minutes
  const currentMinutes = convertTimeToMinutes(currentTime);
  let endMinutes = convertTimeToMinutes(endTime);
  
  // If end time is before current time, it means it's on the next day
  if (endMinutes < currentMinutes) {
    endMinutes += 24 * 60; // Add 24 hours
  }
  
  return endMinutes - currentMinutes;
}

/**
 * Convert time string (HH:mm) to minutes since midnight
 */
function convertTimeToMinutes(time: string): number {
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
}

/**
 * Schedule notifications for fasting schedule
 */
async function scheduleNotifications(schedule: FastingSchedule): Promise<void> {
  await cancelAllNotifications();
  
  if (!schedule.enableNotifications) return;
  
  // Request permissions
  const { status } = await Notifications.requestPermissionsAsync();
  if (status !== 'granted') {
    console.log('Notification permissions not granted');
    return;
  }
  
  // Schedule fasting start notification
  if (schedule.startTime) {
    const [hours, minutes] = schedule.startTime.split(':').map(Number);
    
    // Schedule notification 15 minutes before fasting starts
    await Notifications.scheduleNotificationAsync({
      content: {
        title: 'Fasting Starts Soon',
        body: 'Your fasting window starts in 15 minutes.',
        data: { type: 'fasting_start' }
      },
      identifier: 'fasting-start-reminder',
      trigger: {
        dateComponents: {
          hour: hours === 0 ? 23 : hours - 1,
          minute: minutes + 45 > 59 ? minutes - 15 : minutes + 45,
        },
        repeats: true
      }
    });
    
    // Schedule notification at fasting start time
    await Notifications.scheduleNotificationAsync({
      content: {
        title: 'Fasting Window Started',
        body: 'Your fasting window has started. Stay strong!',
        data: { type: 'fasting_started' }
      },
      identifier: 'fasting-started',
      trigger: {
        dateComponents: {
          hour: hours,
          minute: minutes,
        },
        repeats: true
      }
    });
    
    // Calculate fasting end time
    const windows = calculateFastingWindows(schedule);
    const [endHours, endMinutes] = windows.fastingWindow.end.split(':').map(Number);
    
    // Schedule notification at fasting end time
    await Notifications.scheduleNotificationAsync({
      content: {
        title: 'Fasting Complete!',
        body: `You've completed your ${schedule.fastingHours}-hour fast!`,
        data: { type: 'fasting_complete' }
      },
      identifier: 'fasting-complete',
      trigger: {
        dateComponents: {
          hour: endHours,
          minute: endMinutes,
        },
        repeats: true
      }
    });
  }
}

/**
 * Schedule a notification for the end of an active fast
 */
async function scheduleEndFastNotification(endTime: Date): Promise<void> {
  const { status } = await Notifications.requestPermissionsAsync();
  if (status !== 'granted') {
    console.log('Notification permissions not granted');
    return;
  }
  
  await Notifications.scheduleNotificationAsync({
    content: {
      title: 'Fasting Complete!',
      body: 'You have reached your fasting goal!',
      data: { type: 'fasting_complete' }
    },
    identifier: 'fasting-complete-once',
    trigger: {
      seconds: Math.floor((endTime.getTime() - Date.now()) / 1000)
    }
  });
}

/**
 * Cancel all scheduled notifications
 */
async function cancelAllNotifications(): Promise<void> {
  await Notifications.cancelAllScheduledNotificationsAsync();
} 