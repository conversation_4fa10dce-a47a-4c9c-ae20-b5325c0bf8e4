import { initializeApp } from 'firebase/app';
import { 
  getAuth, 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword, 
  signOut, 
  onAuthStateChanged, 
  User 
} from 'firebase/auth';
import { 
  getFirestore, 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc, 
  getDocs, 
  setDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  serverTimestamp,
  WhereFilterOp,
  Timestamp,
  Firestore,
  QueryConstraint,
  CollectionReference,
  Query,
  DocumentData
} from 'firebase/firestore';
import { app, db, auth } from '@/lib/firebase';

// Initialize Firebase if not already initialized
try {
  initializeApp({
    apiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY,
    authDomain: process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID,
    storageBucket: process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.EXPO_PUBLIC_FIREBASE_APP_ID
  });
} catch (error) {
  console.log('Firebase already initialized');
}

// Get Firebase instances
const authInstance = getAuth();
const dbInstance = getFirestore();

// Filter type for queries
export interface QueryFilter {
  field: string;
  operator: WhereFilterOp;
  value: any;
}

class FirebaseService {
  // Auth methods
  async signIn(email: string, password: string) {
    try {
      const userCredential = await signInWithEmailAndPassword(authInstance, email, password);
      return { success: true, user: userCredential.user };
    } catch (error) {
      console.error('Error signing in:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async signUp(email: string, password: string) {
    try {
      const userCredential = await createUserWithEmailAndPassword(authInstance, email, password);
      return { success: true, user: userCredential.user };
    } catch (error) {
      console.error('Error signing up:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async signOut() {
    try {
      await signOut(authInstance);
      return { success: true };
    } catch (error) {
      console.error('Error signing out:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  getCurrentUser(): User | null {
    return authInstance.currentUser;
  }

  onAuthStateChanged(callback: (user: User | null) => void) {
    return onAuthStateChanged(authInstance, callback);
  }

  // Firestore methods
  getFirestore(): Firestore {
    return dbInstance;
  }

  async addDocument(collectionName: string, data: any) {
    try {
      const docRef = await addDoc(collection(dbInstance, collectionName), {
        ...data,
        createdAt: Timestamp.now()
      });
      
      return docRef.id;
    } catch (error) {
      console.error('Error adding document:', error);
      throw error;
    }
  }

  async updateDocument(collectionName: string, docId: string, data: any) {
    try {
      const docRef = doc(dbInstance, collectionName, docId);
      await updateDoc(docRef, {
        ...data,
        updatedAt: Timestamp.now()
      });
      
      return true;
    } catch (error) {
      console.error('Error updating document:', error);
      throw error;
    }
  }

  async deleteDocument(collectionName: string, docId: string) {
    try {
      const docRef = doc(dbInstance, collectionName, docId);
      await deleteDoc(docRef);
      
      return true;
    } catch (error) {
      console.error('Error deleting document:', error);
      throw error;
    }
  }

  async getDocument(collectionName: string, docId: string) {
    try {
      const docRef = doc(dbInstance, collectionName, docId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        };
      } else {
        return null;
      }
    } catch (error) {
      console.error('Error getting document:', error);
      throw error;
    }
  }

  async setDocument(collectionName: string, docId: string, data: any, merge: boolean = false) {
    try {
      const docRef = doc(dbInstance, collectionName, docId);
      await setDoc(docRef, {
        ...data,
        updatedAt: Timestamp.now()
      }, { merge });
      
      return true;
    } catch (error) {
      console.error('Error setting document:', error);
      throw error;
    }
  }

  async queryDocuments(
    collectionName: string,
    filters: { field: string; operator: string; value: any }[],
    orderByField?: string,
    orderDirection?: 'asc' | 'desc',
    limitCount?: number
  ) {
    try {
      // Create initial query
      let collectionRef = collection(dbInstance, collectionName);
      let results: any[] = [];
      
      // Apply filters
      if (filters && filters.length > 0) {
        let constraints: QueryConstraint[] = [];
        
        for (const filter of filters) {
          constraints.push(where(filter.field, filter.operator as any, filter.value));
        }
        
        // Apply ordering if specified
        if (orderByField) {
          constraints.push(orderBy(orderByField, orderDirection || 'asc'));
        }
        
        // Apply limit if specified
        if (limitCount) {
          constraints.push(limit(limitCount));
        }
        
        const q = query(collectionRef, ...constraints);
        const querySnapshot = await getDocs(q);
        
        querySnapshot.forEach((doc) => {
          results.push({
            id: doc.id,
            ...doc.data()
          });
        });
      } else {
        // If no filters, just get all documents from the collection
        const querySnapshot = await getDocs(collectionRef);
        
        querySnapshot.forEach((doc) => {
          results.push({
            id: doc.id,
            ...doc.data()
          });
        });
      }
      
      return results;
    } catch (error) {
      console.error('Error querying documents:', error);
      throw error;
    }
  }
}

export const firebaseService = new FirebaseService(); 