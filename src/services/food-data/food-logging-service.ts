import AsyncStorage from '@react-native-async-storage/async-storage';
import { OpenFoodFactsProduct, extractNutritionFacts, convertToNutritionalInfo } from './open-food-facts';
import { saveToHistory } from '../foodCacheService';

// Define the structure of a logged food item
export interface LoggedFoodItem {
  id: string;
  name: string;
  brand?: string;
  servingSize?: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  sugar?: number;
  fiber?: number;
  sodium?: number;
  nutritionGrade?: string | null;
  processedFoodGrade?: number | null;
  imageUrl?: string;
  barcode?: string;
  timestamp: number;
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  quantity: number;
}

// Keys for AsyncStorage
const FOOD_LOG_KEY = 'health_app_food_log';
const FOOD_ITEMS_KEY = 'health_app_food_items';

type MealType = 'breakfast' | 'lunch' | 'dinner' | 'snack';

/**
 * Log a scanned product to food history
 * @param product The product data from Open Food Facts
 * @param mealType The type of meal (breakfast, lunch, dinner, snack)
 * @returns Promise that resolves when the food is logged
 */
export async function logFoodFromProduct(
  product: OpenFoodFactsProduct, 
  mealType: MealType
): Promise<void> {
  try {
    // Convert OpenFoodFacts format to our app's format
    const nutritionalInfo = convertToNutritionalInfo(product);
    
    // Prepare data for saving to history
    const foodData = {
      name: product.product_name || 'Unknown Product',
      dishName: product.product_name || 'Unknown Product',
      calories: nutritionalInfo.calories,
      protein: nutritionalInfo.protein,
      carbs: nutritionalInfo.carbs,
      fat: nutritionalInfo.fat,
      description: `${product.brands ? product.brands + ' - ' : ''}${product.quantity || ''}`,
      items: [
        {
          name: product.product_name || 'Unknown Product',
          calories: nutritionalInfo.calories,
          protein: nutritionalInfo.protein,
          carbs: nutritionalInfo.carbs,
          fat: nutritionalInfo.fat,
          estimatedAmount: product.quantity || 'One serving',
          fiber: nutritionalInfo.additionalInfo?.fiber,
          allergens: nutritionalInfo.additionalInfo?.allergens
        }
      ],
      servingInfo: {
        servingSize: product.serving_size || '100g',
        totalServings: 1,
        caloriesPerServing: nutritionalInfo.calories,
        proteinPerServing: nutritionalInfo.protein,
        carbsPerServing: nutritionalInfo.carbs,
        fatPerServing: nutritionalInfo.fat,
        fiberPerServing: nutritionalInfo.additionalInfo?.fiber,
        sugarPerServing: nutritionalInfo.additionalInfo?.sugars,
        sodiumPerServing: nutritionalInfo.additionalInfo?.sodium
      },
      healthHighlights: {
        positives: [] as string[],
        considerations: [] as string[]
      },
      preparationMethod: '',
      cuisineType: product.categories?.split(',')[0] || '',
      mealType: mealType,
      allergens: nutritionalInfo.additionalInfo?.allergens,
    };
    
    // Add some health highlights based on nutritional content
    if (nutritionalInfo.protein >= 15) {
      foodData.healthHighlights.positives.push('High in protein');
    }
    
    if (nutritionalInfo.additionalInfo?.fiber && nutritionalInfo.additionalInfo.fiber >= 5) {
      foodData.healthHighlights.positives.push('Good source of fiber');
    }
    
    if (nutritionalInfo.fat >= 20) {
      foodData.healthHighlights.considerations.push('High in fat');
    }
    
    if (nutritionalInfo.additionalInfo?.sugars && nutritionalInfo.additionalInfo.sugars >= 15) {
      foodData.healthHighlights.considerations.push('High in sugar');
    }
    
    if (nutritionalInfo.additionalInfo?.sodium && nutritionalInfo.additionalInfo.sodium >= 500) {
      foodData.healthHighlights.considerations.push('High in sodium');
    }
    
    // Use product image or placeholder
    const imageUri = product.image_url || 'https://via.placeholder.com/300x300?text=No+Image';
    
    // Save to food history - update to match function signature
    const foodDataWithImageUri = {
      ...foodData,
      imageUri // Adding imageUri here to match the expected structure
    };

    // Now call saveToHistory with the correctly structured object (which doesn't need imageUri as second parameter)
    await saveToHistory(foodDataWithImageUri, imageUri);
  } catch (error) {
    console.error('Error logging food from product:', error);
    throw error;
  }
}

/**
 * Save a food item to storage for future reference
 */
async function saveFoodItem(foodItem: LoggedFoodItem): Promise<void> {
  try {
    // Get existing food items
    const existingItemsJson = await AsyncStorage.getItem(FOOD_ITEMS_KEY);
    let existingItems: Record<string, LoggedFoodItem> = existingItemsJson 
      ? JSON.parse(existingItemsJson) 
      : {};
    
    // Check if a food with the same barcode exists, if so, update it
    if (foodItem.barcode) {
      const existingItemWithBarcode = Object.values(existingItems).find(
        item => item.barcode === foodItem.barcode
      );
      
      if (existingItemWithBarcode) {
        // Update the existing item rather than creating a new one
        existingItems[existingItemWithBarcode.id] = {
          ...existingItemWithBarcode,
          name: foodItem.name,
          brand: foodItem.brand,
          servingSize: foodItem.servingSize,
          calories: foodItem.calories,
          protein: foodItem.protein,
          carbs: foodItem.carbs,
          fat: foodItem.fat,
          sugar: foodItem.sugar,
          fiber: foodItem.fiber,
          sodium: foodItem.sodium,
          nutritionGrade: foodItem.nutritionGrade,
          processedFoodGrade: foodItem.processedFoodGrade,
          imageUrl: foodItem.imageUrl,
        };
        
        await AsyncStorage.setItem(FOOD_ITEMS_KEY, JSON.stringify(existingItems));
        return;
      }
    }
    
    // Add new food item
    existingItems[foodItem.id] = foodItem;
    await AsyncStorage.setItem(FOOD_ITEMS_KEY, JSON.stringify(existingItems));
  } catch (error) {
    console.error('Error saving food item:', error);
    throw error;
  }
}

/**
 * Log a food item consumption to the food log
 */
async function logFoodItem(foodItem: LoggedFoodItem): Promise<void> {
  try {
    // Get existing log
    const existingLogJson = await AsyncStorage.getItem(FOOD_LOG_KEY);
    let existingLog: LoggedFoodItem[] = existingLogJson 
      ? JSON.parse(existingLogJson) 
      : [];
    
    // Add new log entry
    existingLog.push(foodItem);
    
    // Sort by timestamp descending
    existingLog.sort((a, b) => b.timestamp - a.timestamp);
    
    await AsyncStorage.setItem(FOOD_LOG_KEY, JSON.stringify(existingLog));
  } catch (error) {
    console.error('Error logging food item:', error);
    throw error;
  }
}

/**
 * Get all food items in the log
 */
export async function getFoodLog(): Promise<LoggedFoodItem[]> {
  try {
    const logJson = await AsyncStorage.getItem(FOOD_LOG_KEY);
    return logJson ? JSON.parse(logJson) : [];
  } catch (error) {
    console.error('Error getting food log:', error);
    return [];
  }
}

/**
 * Get food log for a specific date
 */
export async function getFoodLogByDate(date: Date): Promise<LoggedFoodItem[]> {
  try {
    const allLogs = await getFoodLog();
    
    // Get the start and end timestamps for the given date
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);
    
    // Filter logs by date
    return allLogs.filter(item => {
      const itemDate = new Date(item.timestamp);
      return itemDate >= startOfDay && itemDate <= endOfDay;
    });
  } catch (error) {
    console.error('Error getting food log by date:', error);
    return [];
  }
}

/**
 * Delete a food item from the log
 */
export async function deleteFoodLogItem(id: string): Promise<boolean> {
  try {
    // Get existing log
    const existingLogJson = await AsyncStorage.getItem(FOOD_LOG_KEY);
    if (!existingLogJson) return false;
    
    const existingLog: LoggedFoodItem[] = JSON.parse(existingLogJson);
    const newLog = existingLog.filter(item => item.id !== id);
    
    if (newLog.length === existingLog.length) {
      // Item not found
      return false;
    }
    
    await AsyncStorage.setItem(FOOD_LOG_KEY, JSON.stringify(newLog));
    return true;
  } catch (error) {
    console.error('Error deleting food log item:', error);
    return false;
  }
}

/**
 * Clear the entire food log
 */
export async function clearFoodLog(): Promise<void> {
  try {
    await AsyncStorage.removeItem(FOOD_LOG_KEY);
  } catch (error) {
    console.error('Error clearing food log:', error);
    throw error;
  }
} 