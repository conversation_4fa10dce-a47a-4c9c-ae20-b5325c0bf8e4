import { Alert } from 'react-native';
import { NutritionalInfo as AppNutritionalInfo } from '@/types/scan.types';

/**
 * Types for the Open Food Facts API response
 */
export interface OpenFoodFactsProduct {
  id?: string;
  code?: string;
  product_name: string;
  product_name_en?: string;
  brands?: string;
  image_url?: string;
  image_small_url?: string;
  image_ingredients_url?: string;
  image_nutrition_url?: string;
  quantity?: string;
  ingredients_text?: string;
  allergens?: string;
  traces?: string;
  serving_size?: string;
  nutriments: {
    energy?: number;
    'energy-kcal'?: number;
    'energy-kj'?: number;
    fat?: number;
    'saturated-fat'?: number;
    carbohydrates?: number;
    sugars?: number;
    fiber?: number;
    proteins?: number;
    salt?: number;
    sodium?: number;
    // Add more nutrients as needed
    [key: string]: number | undefined;
  };
  nutrient_levels?: {
    fat?: string;
    salt?: string;
    'saturated-fat'?: string;
    sugars?: string;
  };
  nutriscore_grade?: string;
  nova_group?: number;
  categories?: string;
  categories_tags?: string[];
  labels?: string;
  labels_tags?: string[];
  ingredients_tags?: string[];
  allergens_tags?: string[];
}

export interface OpenFoodFactsResponse {
  code: string;
  product: OpenFoodFactsProduct;
  status: number;
  status_verbose: string;
}

interface SearchResponse {
  count: number;
  page: number;
  page_count: number;
  page_size: number;
  products: OpenFoodFactsProduct[];
}

const API_URL = 'https://world.openfoodfacts.org/api/v3/product';
const APP_NAME = 'HealthApp';
const CONTACT_INFO = '<EMAIL>'; // Replace with your contact info

/**
 * Fetches food product information by barcode from Open Food Facts API
 */
export async function getFoodProductByBarcode(barcode: string): Promise<OpenFoodFactsProduct | null> {
  try {
    // Validate barcode
    if (!barcode || !/^\d{8,14}$/.test(barcode)) {
      console.error('Invalid barcode format');
      return null;
    }

    const response = await fetch(`${API_URL}/${barcode}.json`, {
      method: 'GET',
      headers: {
        'User-Agent': `${APP_NAME} - ${CONTACT_INFO}`,
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      console.error(`API Error: ${response.status} ${response.statusText}`);
      return null;
    }

    const data = await response.json() as OpenFoodFactsResponse;

    // Check if the product was found
    if (data.status !== 1 || !data.product) {
      console.log('Product not found or API returned an error');
      return null;
    }

    return {
      ...data.product,
      id: barcode,
    };
  } catch (error) {
    console.error('Error fetching food product:', error);
    return null;
  }
}

/**
 * Extracts nutrition facts from a product in standardized format
 */
export function extractNutritionFacts(product: OpenFoodFactsProduct) {
  const { nutriments } = product;
  
  return {
    calories: nutriments['energy-kcal'] || 0,
    protein: nutriments.proteins || 0,
    carbs: nutriments.carbohydrates || 0,
    fat: nutriments.fat || 0,
    sugar: nutriments.sugars || 0,
    fiber: nutriments.fiber || 0,
    sodium: nutriments.sodium || 0,
    servingSize: product.serving_size || 'Unknown',
    nutritionGrade: product.nutriscore_grade || null,
    processedFoodGrade: product.nova_group || null,
  };
}

/**
 * Search for food products by name
 */
export async function searchFoodProducts(query: string, page = 1, pageSize = 20): Promise<OpenFoodFactsProduct[]> {
  try {
    if (!query || query.length < 2) {
      return [];
    }

    const searchUrl = `https://world.openfoodfacts.org/cgi/search.pl?search_terms=${encodeURIComponent(query)}&search_simple=1&action=process&json=1&page=${page}&page_size=${pageSize}`;
    
    const response = await fetch(searchUrl, {
      method: 'GET',
      headers: {
        'User-Agent': `${APP_NAME} - ${CONTACT_INFO}`,
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      console.error(`Search API Error: ${response.status} ${response.statusText}`);
      return [];
    }

    const data = await response.json() as SearchResponse;
    
    if (!data.products || !Array.isArray(data.products)) {
      return [];
    }

    return data.products.map((product) => ({
      ...product,
      id: product.code || String(Math.random()),
    }));
  } catch (error) {
    console.error('Error searching food products:', error);
    return [];
  }
}

/**
 * Handles scanning a barcode and retrieving food information
 */
export async function handleBarcodeScanned(barcode: string): Promise<OpenFoodFactsProduct | null> {
  try {
    const product = await getFoodProductByBarcode(barcode);
    
    if (!product) {
      Alert.alert(
        'Product Not Found',
        'This product was not found in the food database. Would you like to add it?',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Add Product', 
            onPress: () => {
              // Handle adding a new product
              // This could navigate to a form or another screen
              console.log('Navigate to add product form');
            } 
          }
        ]
      );
      return null;
    }
    
    return product;
  } catch (error) {
    console.error('Error handling barcode scan:', error);
    Alert.alert('Error', 'Failed to retrieve product information.');
    return null;
  }
}

export interface NutritionalInfo {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  servingSize: string;
  additionalInfo?: {
    fiber?: number;
    sugars?: number;
    sodium?: number;
    allergens?: string[];
  };
  source: string;
  sourceId: string;
}

/**
 * Convert OpenFoodFacts product data to our app's nutritional info format
 * @param product The product data from OpenFoodFacts
 * @returns Nutritional info in our app format
 */
export function convertToNutritionalInfo(product: OpenFoodFactsProduct): NutritionalInfo {
  // Extract and normalize nutritional data
  const nutriments = product.nutriments || {};
  
  // Default serving size if not specified
  const servingSize = product.serving_size || '100g';
  
  // Energy in kcal, preferring the dedicated field if available
  const calories = nutriments['energy-kcal'] || 
                  (nutriments.energy ? nutriments.energy / 4.184 : 0);
  
  // Extract other macros, defaulting to 0 if not available
  const protein = nutriments.proteins || 0;
  const carbs = nutriments.carbohydrates || 0;
  const fat = nutriments.fat || 0;
  const fiber = nutriments.fiber || 0;
  const sugars = nutriments.sugars || 0;
  const sodium = nutriments.sodium || 0;
  
  // Extract allergens
  const allergens = product.allergens_tags?.map(allergen => 
    allergen.replace('en:', '').replace(/-/g, ' ')
  ) || [];
  
  // Create nutritional info object in our format
  return {
    name: product.product_name || 'Unknown Product',
    calories: Math.round(calories),
    protein: Math.round(protein * 10) / 10,
    carbs: Math.round(carbs * 10) / 10,
    fat: Math.round(fat * 10) / 10,
    servingSize,
    additionalInfo: {
      fiber: Math.round(fiber * 10) / 10,
      sugars: Math.round(sugars * 10) / 10,
      sodium: Math.round(sodium * 10) / 10,
      allergens
    },
    source: 'Open Food Facts',
    sourceId: product.code ?? 'unknown'
  };
} 