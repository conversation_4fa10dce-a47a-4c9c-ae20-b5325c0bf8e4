/**
 * USDA API Service
 * 
 * Provides access to the USDA FoodData Central API for retrieving
 * nutritional and density data for various food items.
 */

import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';
import { getNutritionData } from '@/services/openai/secureApiClient';

// USDA API configuration
// Now using Firebase Functions - no API key needed
const USDA_API_BASE_URL = 'https://api.nal.usda.gov/fdc/v1';
const CACHE_TTL = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

// Cache keys
const DENSITY_CACHE_KEY = 'usda_density_cache';
const NUTRITION_CACHE_KEY = 'usda_nutrition_cache';

/**
 * Food search parameters for USDA API
 */
export interface FoodSearchParams {
  query: string;
  dataType?: ('Foundation' | 'SR Legacy' | 'Survey (FNDDS)' | 'Branded')[];
  pageSize?: number;
  pageNumber?: number;
  sortBy?: 'dataType.keyword' | 'lowercaseDescription.keyword' | 'fdcId' | 'publishedDate';
  sortOrder?: 'asc' | 'desc';
  brandOwner?: string;
}

/**
 * Food details parameters for USDA API
 */
export interface FoodDetailsParams {
  fdcId: string;
  format?: 'abridged' | 'full';
}

/**
 * USDA food density information
 */
export interface FoodDensityInfo {
  fdcId: string;
  description: string;
  density?: number; // g/mL (approximately g/cm³)
  householdServingWeight?: number; // g
  servingSize?: number;
  servingSizeUnit?: string;
  source: 'usda' | 'calculated';
  lastUpdated: number; // timestamp
}

/**
 * USDA nutritional information
 */
export interface FoodNutritionInfo {
  fdcId: string;
  description: string;
  dataType?: string;
  calories?: number; // per 100g
  protein?: number; // g per 100g
  carbohydrates?: number; // g per 100g
  fat?: number; // g per 100g
  fiber?: number; // g per 100g
  sugar?: number; // g per 100g
  sodium?: number; // mg per 100g
  nutrients?: {
    nutrientId: number;
    nutrientName: string;
    nutrientNumber: string;
    unitName: string;
    value: number;
  }[];
  source: 'usda';
  lastUpdated: number; // timestamp
}

/**
 * Search for foods in the USDA database
 * 
 * @param params Search parameters
 * @returns List of matching food items
 */
export async function searchUsdaFoods(
  params: FoodSearchParams
): Promise<any> {
  try {
    const url = `${USDA_API_BASE_URL}/foods/search`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Api-Key': USDA_API_KEY
      },
      body: JSON.stringify({
        query: params.query,
        dataType: params.dataType || ['Foundation', 'SR Legacy'],
        pageSize: params.pageSize || 25,
        pageNumber: params.pageNumber || 1,
        sortBy: params.sortBy || 'lowercaseDescription.keyword',
        sortOrder: params.sortOrder || 'asc',
        brandOwner: params.brandOwner
      })
    });
    
    if (!response.ok) {
      throw new Error(`USDA API error: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error searching USDA foods:', error);
    throw error;
  }
}

/**
 * Get details for a specific food by FDC ID
 * 
 * @param params Food details parameters
 * @returns Detailed food information
 */
export async function getUsdaFoodDetails(
  params: FoodDetailsParams
): Promise<any> {
  try {
    const url = `${USDA_API_BASE_URL}/food/${params.fdcId}?format=${params.format || 'full'}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-Api-Key': USDA_API_KEY
      }
    });
    
    if (!response.ok) {
      throw new Error(`USDA API error: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error getting USDA food details:', error);
    throw error;
  }
}

/**
 * Get density information for a food from USDA data
 * 
 * @param foodName Name of the food
 * @param useCache Whether to use cached data (default: true)
 * @returns Food density information or null if not found
 */
export async function getFoodDensityFromUsda(
  foodName: string,
  useCache: boolean = true
): Promise<FoodDensityInfo | null> {
  try {
    // Normalize food name
    const normalizedName = foodName.toLowerCase().trim();
    
    // Check cache first if enabled
    if (useCache) {
      const densityInfo = await getCachedDensityInfo(normalizedName);
      if (densityInfo) {
        return densityInfo;
      }
    }
    
    // Search for food in USDA database
    const searchResults = await searchUsdaFoods({
      query: normalizedName,
      dataType: ['Foundation', 'SR Legacy', 'Survey (FNDDS)'],
      pageSize: 5
    });
    
    if (!searchResults.foods || searchResults.foods.length === 0) {
      return null;
    }
    
    // Get the best matching food
    const bestMatch = findBestMatchingFood(searchResults.foods, normalizedName);
    if (!bestMatch) {
      return null;
    }
    
    // Get detailed information
    const foodDetails = await getUsdaFoodDetails({
      fdcId: bestMatch.fdcId
    });
    
    // Extract density information
    const densityInfo = extractDensityInfo(foodDetails);
    
    // Cache the result
    if (densityInfo) {
      await cacheDensityInfo(normalizedName, densityInfo);
    }
    
    return densityInfo;
  } catch (error) {
    console.error('Error getting food density from USDA:', error);
    return null;
  }
}

/**
 * Get nutritional information for a food from USDA data
 * 
 * @param foodName Name of the food
 * @param useCache Whether to use cached data (default: true)
 * @returns Food nutritional information or null if not found
 */
export async function getFoodNutritionFromUsda(
  foodName: string,
  useCache: boolean = true
): Promise<FoodNutritionInfo | null> {
  try {
    // Normalize food name
    const normalizedName = foodName.toLowerCase().trim();
    
    // Check cache first if enabled
    if (useCache) {
      const nutritionInfo = await getCachedNutritionInfo(normalizedName);
      if (nutritionInfo) {
        return nutritionInfo;
      }
    }
    
    // Search for food in USDA database
    const searchResults = await searchUsdaFoods({
      query: normalizedName,
      dataType: ['Foundation', 'SR Legacy', 'Survey (FNDDS)'],
      pageSize: 5
    });
    
    if (!searchResults.foods || searchResults.foods.length === 0) {
      return null;
    }
    
    // Get the best matching food
    const bestMatch = findBestMatchingFood(searchResults.foods, normalizedName);
    if (!bestMatch) {
      return null;
    }
    
    // Get detailed information
    const foodDetails = await getUsdaFoodDetails({
      fdcId: bestMatch.fdcId
    });
    
    // Extract nutrition information
    const nutritionInfo = extractNutritionInfo(foodDetails);
    
    // Cache the result
    if (nutritionInfo) {
      await cacheNutritionInfo(normalizedName, nutritionInfo);
    }
    
    return nutritionInfo;
  } catch (error) {
    console.error('Error getting food nutrition from USDA:', error);
    return null;
  }
}

/**
 * Find the best matching food from search results
 * 
 * @param foods List of foods from search results
 * @param query Original search query
 * @returns Best matching food or null if no good match
 */
function findBestMatchingFood(foods: any[], query: string): any | null {
  if (!foods || foods.length === 0) {
    return null;
  }
  
  // Simple scoring system based on how well the description matches the query
  const scoredFoods = foods.map(food => {
    const description = food.description.toLowerCase();
    let score = 0;
    
    // Exact match gets highest score
    if (description === query) {
      score += 100;
    }
    // Starting with the query is also good
    else if (description.startsWith(query)) {
      score += 50;
    }
    // Contains the query is okay
    else if (description.includes(query)) {
      score += 25;
    }
    
    // Prefer Foundation and SR Legacy data
    if (food.dataType === 'Foundation') {
      score += 10;
    } else if (food.dataType === 'SR Legacy') {
      score += 5;
    }
    
    return { food, score };
  });
  
  // Sort by score in descending order
  scoredFoods.sort((a, b) => b.score - a.score);
  
  // Return the highest scoring food if it has a minimum score
  return scoredFoods[0].score >= 25 ? scoredFoods[0].food : null;
}

/**
 * Extract density information from food details
 * 
 * @param foodDetails Detailed food information from USDA API
 * @returns Density information or null if not available
 */
function extractDensityInfo(foodDetails: any): FoodDensityInfo | null {
  if (!foodDetails || !foodDetails.fdcId) {
    return null;
  }
  
  const densityInfo: FoodDensityInfo = {
    fdcId: foodDetails.fdcId,
    description: foodDetails.description,
    source: 'usda',
    lastUpdated: Date.now()
  };
  
  // Try to find density information in serving size data
  if (foodDetails.servingSize && foodDetails.servingSizeUnit) {
    // If serving size is in volume units, we can calculate density
    if (
      foodDetails.servingSizeUnit === 'ml' || 
      foodDetails.servingSizeUnit === 'mL' ||
      foodDetails.servingSizeUnit === 'cup' || 
      foodDetails.servingSizeUnit === 'tbsp' || 
      foodDetails.servingSizeUnit === 'tsp'
    ) {
      let volumeInMl = 0;
      
      // Convert serving size to mL
      if (foodDetails.servingSizeUnit === 'ml' || foodDetails.servingSizeUnit === 'mL') {
        volumeInMl = foodDetails.servingSize;
      } else if (foodDetails.servingSizeUnit === 'cup') {
        volumeInMl = foodDetails.servingSize * 236.588; // 1 cup = 236.588 mL
      } else if (foodDetails.servingSizeUnit === 'tbsp') {
        volumeInMl = foodDetails.servingSize * 14.7868; // 1 tbsp = 14.7868 mL
      } else if (foodDetails.servingSizeUnit === 'tsp') {
        volumeInMl = foodDetails.servingSize * 4.92892; // 1 tsp = 4.92892 mL
      }
      
      // If we have household serving weight in grams, calculate density
      if (foodDetails.householdServingWeight && volumeInMl > 0) {
        densityInfo.density = foodDetails.householdServingWeight / volumeInMl;
        densityInfo.householdServingWeight = foodDetails.householdServingWeight;
      }
    }
    
    densityInfo.servingSize = foodDetails.servingSize;
    densityInfo.servingSizeUnit = foodDetails.servingSizeUnit;
  }
  
  // If we have household serving weight but no calculated density,
  // try to calculate based on portion descriptions
  if (
    !densityInfo.density && 
    foodDetails.foodPortions && 
    Array.isArray(foodDetails.foodPortions)
  ) {
    for (const portion of foodDetails.foodPortions) {
      if (
        portion.gramWeight && 
        portion.amount && 
        portion.measureUnit && 
        (
          portion.measureUnit.toLowerCase() === 'ml' || 
          portion.measureUnit.toLowerCase() === 'milliliter' ||
          portion.measureUnit.toLowerCase() === 'cup' ||
          portion.measureUnit.toLowerCase() === 'tbsp' ||
          portion.measureUnit.toLowerCase() === 'tablespoon' ||
          portion.measureUnit.toLowerCase() === 'tsp' ||
          portion.measureUnit.toLowerCase() === 'teaspoon'
        )
      ) {
        let volumeInMl = 0;
        
        // Convert to mL
        if (portion.measureUnit.toLowerCase() === 'ml' || portion.measureUnit.toLowerCase() === 'milliliter') {
          volumeInMl = portion.amount;
        } else if (portion.measureUnit.toLowerCase() === 'cup') {
          volumeInMl = portion.amount * 236.588; // 1 cup = 236.588 mL
        } else if (portion.measureUnit.toLowerCase() === 'tbsp' || portion.measureUnit.toLowerCase() === 'tablespoon') {
          volumeInMl = portion.amount * 14.7868; // 1 tbsp = 14.7868 mL
        } else if (portion.measureUnit.toLowerCase() === 'tsp' || portion.measureUnit.toLowerCase() === 'teaspoon') {
          volumeInMl = portion.amount * 4.92892; // 1 tsp = 4.92892 mL
        }
        
        if (volumeInMl > 0) {
          densityInfo.density = portion.gramWeight / volumeInMl;
          densityInfo.householdServingWeight = portion.gramWeight;
          break;
        }
      }
    }
  }
  
  // If we still don't have density but have household serving weight,
  // mark it as calculated from secondary data
  if (!densityInfo.density && foodDetails.householdServingWeight) {
    // Estimate density based on food category
    densityInfo.density = estimateDensityFromCategory(foodDetails);
    densityInfo.source = 'calculated';
    densityInfo.householdServingWeight = foodDetails.householdServingWeight;
  }
  
  return densityInfo.density ? densityInfo : null;
}

/**
 * Estimate density from food category when not directly available
 * 
 * @param foodDetails Food details from USDA API
 * @returns Estimated density in g/mL (approximately g/cm³)
 */
function estimateDensityFromCategory(foodDetails: any): number {
  // Default density if we can't determine category
  let defaultDensity = 0.7;
  
  // Check for food categories in foodCategory or foodCategoryId fields
  const category = foodDetails.foodCategory?.toLowerCase() || '';
  
  // These are approximate average densities for common food categories
  if (
    category.includes('fruit') || 
    category.includes('vegetable') || 
    category.includes('produce')
  ) {
    return 0.6; // Fruits and vegetables average
  } else if (
    category.includes('meat') || 
    category.includes('poultry') || 
    category.includes('seafood') ||
    category.includes('beef') ||
    category.includes('pork')
  ) {
    return 1.0; // Meats average
  } else if (
    category.includes('dairy') || 
    category.includes('milk') || 
    category.includes('cheese')
  ) {
    return 0.9; // Dairy products average
  } else if (
    category.includes('grain') || 
    category.includes('cereal') || 
    category.includes('pasta') ||
    category.includes('bread')
  ) {
    return 0.5; // Grains and cereals average
  } else if (
    category.includes('oil') || 
    category.includes('fat')
  ) {
    return 0.92; // Oils and fats average
  } else if (
    category.includes('soup') || 
    category.includes('sauce') ||
    category.includes('beverage') ||
    category.includes('drink')
  ) {
    return 1.05; // Liquids average
  } else if (
    category.includes('snack') || 
    category.includes('sweet') ||
    category.includes('dessert') ||
    category.includes('candy')
  ) {
    return 0.4; // Snacks and desserts average
  }
  
  // Fallback to default density
  return defaultDensity;
}

/**
 * Extract nutrition information from food details
 * 
 * @param foodDetails Detailed food information from USDA API
 * @returns Nutrition information or null if not available
 */
function extractNutritionInfo(foodDetails: any): FoodNutritionInfo | null {
  if (!foodDetails || !foodDetails.fdcId) {
    return null;
  }
  
  const nutritionInfo: FoodNutritionInfo = {
    fdcId: foodDetails.fdcId,
    description: foodDetails.description,
    dataType: foodDetails.dataType,
    source: 'usda',
    lastUpdated: Date.now(),
    nutrients: []
  };
  
  // Extract nutrient information
  if (foodDetails.foodNutrients && Array.isArray(foodDetails.foodNutrients)) {
    for (const nutrient of foodDetails.foodNutrients) {
      if (
        nutrient.nutrient && 
        nutrient.nutrient.id && 
        nutrient.nutrient.name && 
        nutrient.amount
      ) {
        const nutrientData = {
          nutrientId: nutrient.nutrient.id,
          nutrientName: nutrient.nutrient.name,
          nutrientNumber: nutrient.nutrient.number || '',
          unitName: nutrient.nutrient.unitName || 'g',
          value: nutrient.amount
        };
        
        nutritionInfo.nutrients?.push(nutrientData);
        
        // Extract key nutritional values
        switch (nutrient.nutrient.name.toLowerCase()) {
          case 'energy':
            if (nutrient.nutrient.unitName === 'kcal') {
              nutritionInfo.calories = nutrient.amount;
            }
            break;
          case 'protein':
            nutritionInfo.protein = nutrient.amount;
            break;
          case 'carbohydrate, by difference':
          case 'carbohydrates':
          case 'total carbohydrate':
            nutritionInfo.carbohydrates = nutrient.amount;
            break;
          case 'total lipid (fat)':
          case 'fat':
          case 'total fat':
            nutritionInfo.fat = nutrient.amount;
            break;
          case 'fiber, total dietary':
          case 'dietary fiber':
            nutritionInfo.fiber = nutrient.amount;
            break;
          case 'sugars, total including nlea':
          case 'total sugars':
          case 'sugars':
            nutritionInfo.sugar = nutrient.amount;
            break;
          case 'sodium, na':
          case 'sodium':
            nutritionInfo.sodium = nutrient.amount;
            break;
        }
      }
    }
  }
  
  return nutritionInfo;
}

/**
 * Get cached density information
 * 
 * @param foodName Normalized food name
 * @returns Cached density information or null if not found or expired
 */
async function getCachedDensityInfo(foodName: string): Promise<FoodDensityInfo | null> {
  try {
    const cacheString = await AsyncStorage.getItem(DENSITY_CACHE_KEY);
    if (!cacheString) {
      return null;
    }
    
    const cache = JSON.parse(cacheString) as Record<string, FoodDensityInfo>;
    const cachedInfo = cache[foodName];
    
    // Check if cache exists and is not expired
    if (cachedInfo && Date.now() - cachedInfo.lastUpdated < CACHE_TTL) {
      return cachedInfo;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting cached density info:', error);
    return null;
  }
}

/**
 * Cache density information for a food
 * 
 * @param foodName Normalized food name
 * @param densityInfo Density information to cache
 */
async function cacheDensityInfo(foodName: string, densityInfo: FoodDensityInfo): Promise<void> {
  try {
    // Get existing cache
    const cacheString = await AsyncStorage.getItem(DENSITY_CACHE_KEY);
    const cache = cacheString ? JSON.parse(cacheString) as Record<string, FoodDensityInfo> : {};
    
    // Update cache
    cache[foodName] = densityInfo;
    
    // Save updated cache
    await AsyncStorage.setItem(DENSITY_CACHE_KEY, JSON.stringify(cache));
  } catch (error) {
    console.error('Error caching density info:', error);
  }
}

/**
 * Get cached nutrition information
 * 
 * @param foodName Normalized food name
 * @returns Cached nutrition information or null if not found or expired
 */
async function getCachedNutritionInfo(foodName: string): Promise<FoodNutritionInfo | null> {
  try {
    const cacheString = await AsyncStorage.getItem(NUTRITION_CACHE_KEY);
    if (!cacheString) {
      return null;
    }
    
    const cache = JSON.parse(cacheString) as Record<string, FoodNutritionInfo>;
    const cachedInfo = cache[foodName];
    
    // Check if cache exists and is not expired
    if (cachedInfo && Date.now() - cachedInfo.lastUpdated < CACHE_TTL) {
      return cachedInfo;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting cached nutrition info:', error);
    return null;
  }
}

/**
 * Cache nutrition information for a food
 * 
 * @param foodName Normalized food name
 * @param nutritionInfo Nutrition information to cache
 */
async function cacheNutritionInfo(foodName: string, nutritionInfo: FoodNutritionInfo): Promise<void> {
  try {
    // Get existing cache
    const cacheString = await AsyncStorage.getItem(NUTRITION_CACHE_KEY);
    const cache = cacheString ? JSON.parse(cacheString) as Record<string, FoodNutritionInfo> : {};
    
    // Update cache
    cache[foodName] = nutritionInfo;
    
    // Save updated cache
    await AsyncStorage.setItem(NUTRITION_CACHE_KEY, JSON.stringify(cache));
  } catch (error) {
    console.error('Error caching nutrition info:', error);
  }
}

/**
 * Get density for a food, integrating it with our existing density database
 * 
 * @param foodName Name of the food
 * @returns Density in g/cm³
 */
export async function getEnhancedFoodDensity(foodName: string): Promise<number> {
  try {
    // First, try to get density from our local database
    const localDensity = await import('../../utils/foodDensityMap')
      .then(module => module.getFoodDensity(foodName))
      .catch(() => null);
    
    // If we have a local density that's not the default, use it
    if (localDensity && localDensity !== 0.7) {
      return localDensity;
    }
    
    // Otherwise, try to get from USDA API
    const usdaDensityInfo = await getFoodDensityFromUsda(foodName);
    if (usdaDensityInfo?.density) {
      return usdaDensityInfo.density;
    }
    
    // If all else fails, return the local density (default or not)
    return localDensity || 0.7; // Default density
  } catch (error) {
    console.error('Error getting enhanced food density:', error);
    return 0.7; // Default density
  }
} 