import AsyncStorage from '@react-native-async-storage/async-storage';

// Default timeout for AsyncStorage operations (ms)
const ASYNC_STORAGE_TIMEOUT = 5000;

// Helper function to add timeout to promises
function withTimeout<T>(promise: Promise<T>, timeoutMs: number, errorMessage: string): Promise<T> {
  // Create a timeout promise that rejects after specified milliseconds
  const timeoutPromise = new Promise<T>((_, reject) => {
    const timeoutId = setTimeout(() => {
      clearTimeout(timeoutId);
      reject(new Error(`Timeout (${timeoutMs}ms): ${errorMessage}`));
    }, timeoutMs);
  });

  // Race the original promise against the timeout
  return Promise.race([promise, timeoutPromise]);
}

// Define the structure of our food analysis data
interface FoodItem {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  estimatedAmount?: string;
  fiber?: number;
  allergens?: string[];
  ingredients?: string[];
}

export interface FoodAnalysisData {
  id: string;
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  description?: string;
  items: FoodItem[];
  servingInfo?: {
    servingSize: string;
    totalServings: number;
    caloriesPerServing: number;
    proteinPerServing: number;
    carbsPerServing: number;
    fatPerServing: number;
    fiberPerServing?: number;
    sugarPerServing?: number;
    sodiumPerServing?: number;
  };
  healthHighlights?: {
    positives: string[];
    considerations: string[];
  };
  preparationMethod?: string;
  cuisineType?: string;
  mealType?: string;
  allergens?: string[];
  imageUri: string;
  date: string; // ISO date string
  isSaved: boolean;
}

// Cache keys
const FOOD_HISTORY_KEY = 'food_analysis_history';
const FOOD_FAVORITES_KEY = 'food_analysis_favorites';

/**
 * Saves a food analysis result to the history cache
 * @param data Food analysis data
 * @returns Promise that resolves when the data is saved
 */
export async function saveToHistory(data: Omit<FoodAnalysisData, 'id' | 'date' | 'isSaved'>, imageUri: string): Promise<FoodAnalysisData> {
  try {
    // Get existing history
    const history = await getHistory();

    // Create a new entry with ID and date
    const newEntry: FoodAnalysisData = {
      ...data,
      id: generateUniqueId(),
      imageUri,
      date: new Date().toISOString(),
      isSaved: false
    };

    // Add to history (at the beginning)
    const updatedHistory = [newEntry, ...history];

    // Limit history size to 50 items
    const trimmedHistory = updatedHistory.slice(0, 50);

    // Save updated history with timeout
    await withTimeout(
      AsyncStorage.setItem(FOOD_HISTORY_KEY, JSON.stringify(trimmedHistory)),
      ASYNC_STORAGE_TIMEOUT,
      'Saving food to history timed out'
    );

    return newEntry;
  } catch (error) {
    console.error('Error saving food analysis to history:', error);
    throw error;
  }
}

/**
 * Gets all food analysis history
 * @returns Promise that resolves with the history data
 */
export async function getHistory(): Promise<FoodAnalysisData[]> {
  try {
    // Get history with timeout
    const historyJson = await withTimeout(
      AsyncStorage.getItem(FOOD_HISTORY_KEY),
      ASYNC_STORAGE_TIMEOUT,
      'Getting food history timed out'
    );
    
    return historyJson ? JSON.parse(historyJson) : [];
  } catch (error) {
    console.error('Error getting food analysis history:', error);
    // Return empty array instead of throwing to allow app to continue functioning
    return [];
  }
}

/**
 * Gets a food analysis item from history by ID
 * @param id The ID of the food analysis to retrieve
 * @returns Promise that resolves with the history item or null if not found
 */
export async function getHistoryItem(id: string): Promise<FoodAnalysisData | null> {
  try {
    const history = await getHistory();
    const item = history.find(item => item.id === id);
    return item || null;
  } catch (error) {
    console.error('Error getting food analysis history item:', error);
    return null;
  }
}

/**
 * Updates a food analysis item in history
 * @param updatedItem The updated food analysis item
 * @returns Promise that resolves when the data is updated
 */
export async function updateHistoryItem(updatedItem: FoodAnalysisData): Promise<void> {
  try {
    // Get existing history
    const history = await getHistory();
    
    // Find the index of the item to update
    const index = history.findIndex(item => item.id === updatedItem.id);
    
    if (index === -1) {
      throw new Error(`Item with ID ${updatedItem.id} not found in history`);
    }
    
    // Update the item
    history[index] = updatedItem;
    
    // Save updated history with timeout
    await withTimeout(
      AsyncStorage.setItem(FOOD_HISTORY_KEY, JSON.stringify(history)),
      ASYNC_STORAGE_TIMEOUT,
      'Updating history item timed out'
    );
  } catch (error) {
    console.error('Error updating food analysis in history:', error);
    throw error;
  }
}

/**
 * Saves a food analysis to favorites
 * @param data Food analysis data
 * @returns Promise that resolves when the data is saved
 */
export async function saveToFavorites(data: FoodAnalysisData): Promise<void> {
  try {
    // Get existing favorites
    const favorites = await getFavorites();

    // Check if it's already in favorites
    const existingIndex = favorites.findIndex(item => item.id === data.id);

    // If already exists, update it
    if (existingIndex >= 0) {
      favorites[existingIndex] = { ...data, isSaved: true };
    } else {
      // Add to favorites (at the beginning)
      favorites.unshift({ ...data, isSaved: true });
    }

    // Save updated favorites with timeout
    await withTimeout(
      AsyncStorage.setItem(FOOD_FAVORITES_KEY, JSON.stringify(favorites)),
      ASYNC_STORAGE_TIMEOUT,
      'Saving to favorites timed out'
    );

    // Also update the item in history to reflect saved status
    const history = await getHistory();
    const historyIndex = history.findIndex(item => item.id === data.id);
    
    if (historyIndex >= 0) {
      history[historyIndex] = { ...data, isSaved: true };
      await withTimeout(
        AsyncStorage.setItem(FOOD_HISTORY_KEY, JSON.stringify(history)),
        ASYNC_STORAGE_TIMEOUT,
        'Updating history for favorite timed out'
      );
    }
  } catch (error) {
    console.error('Error saving food analysis to favorites:', error);
    throw error;
  }
}

/**
 * Removes a food analysis from favorites
 * @param id The ID of the food analysis to remove
 * @returns Promise that resolves when the data is removed
 */
export async function removeFromFavorites(id: string): Promise<void> {
  try {
    // Get existing favorites
    const favorites = await getFavorites();

    // Remove the item
    const updatedFavorites = favorites.filter(item => item.id !== id);

    // Save updated favorites with timeout
    await withTimeout(
      AsyncStorage.setItem(FOOD_FAVORITES_KEY, JSON.stringify(updatedFavorites)),
      ASYNC_STORAGE_TIMEOUT,
      'Removing from favorites timed out'
    );

    // Also update the item in history to reflect unsaved status
    const history = await getHistory();
    const historyIndex = history.findIndex(item => item.id === id);
    
    if (historyIndex >= 0) {
      history[historyIndex] = { ...history[historyIndex], isSaved: false };
      await withTimeout(
        AsyncStorage.setItem(FOOD_HISTORY_KEY, JSON.stringify(history)),
        ASYNC_STORAGE_TIMEOUT,
        'Updating history for removed favorite timed out'
      );
    }
  } catch (error) {
    console.error('Error removing food analysis from favorites:', error);
    throw error;
  }
}

/**
 * Gets all favorite food analyses
 * @returns Promise that resolves with the favorites data
 */
export async function getFavorites(): Promise<FoodAnalysisData[]> {
  try {
    // Get favorites with timeout
    const favoritesJson = await withTimeout(
      AsyncStorage.getItem(FOOD_FAVORITES_KEY),
      ASYNC_STORAGE_TIMEOUT,
      'Getting favorites timed out'
    );
    
    return favoritesJson ? JSON.parse(favoritesJson) : [];
  } catch (error) {
    console.error('Error getting food analysis favorites:', error);
    // Return empty array instead of throwing to allow app to continue functioning
    return [];
  }
}

/**
 * Deletes a food analysis from history
 * @param id The ID of the food analysis to delete
 * @returns Promise that resolves when the data is deleted
 */
export async function deleteFromHistory(id: string): Promise<void> {
  try {
    // Get existing history
    const history = await getHistory();

    // Remove the item
    const updatedHistory = history.filter(item => item.id !== id);

    // Save updated history with timeout
    await withTimeout(
      AsyncStorage.setItem(FOOD_HISTORY_KEY, JSON.stringify(updatedHistory)),
      ASYNC_STORAGE_TIMEOUT,
      'Deleting from history timed out'
    );
  } catch (error) {
    console.error('Error deleting food analysis from history:', error);
    throw error;
  }
}

/**
 * Clears all food analysis history
 * @returns Promise that resolves when the history is cleared
 */
export async function clearHistory(): Promise<void> {
  try {
    await withTimeout(
      AsyncStorage.removeItem(FOOD_HISTORY_KEY),
      ASYNC_STORAGE_TIMEOUT,
      'Clearing history timed out'
    );
  } catch (error) {
    console.error('Error clearing food analysis history:', error);
    throw error;
  }
}

/**
 * Generates a unique ID for a food analysis entry
 * @returns A unique ID string
 */
function generateUniqueId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
}

/**
 * Gets analyzed foods grouped by date
 * @returns Promise that resolves with foods grouped by date
 */
export async function getFoodsByDate(): Promise<Record<string, FoodAnalysisData[]>> {
  try {
    // Apply timeout to the potentially slow history fetch
    const history = await withTimeout(
      getHistory(),
      ASYNC_STORAGE_TIMEOUT,
      'Getting history for foods by date timed out'
    );
    
    try {
      // Group by date (just the date part, not time)
      return history.reduce((grouped, food) => {
        // Handle potential invalid date strings
        if (!food.date) {
          return grouped;
        }
        
        try {
          const date = food.date.split('T')[0];
          
          if (!grouped[date]) {
            grouped[date] = [];
          }
          
          grouped[date].push(food);
        } catch (dateError) {
          // Skip this item if date parsing fails
          console.error('Error parsing date for food item:', dateError);
        }
        
        return grouped;
      }, {} as Record<string, FoodAnalysisData[]>);
    } catch (groupingError) {
      console.error('Error grouping foods by date:', groupingError);
      return {};
    }
  } catch (error) {
    console.error('Error getting foods by date:', error);
    return {};
  }
}

/**
 * Gets nutrition statistics for a date range
 * @param startDate Start date (ISO string)
 * @param endDate End date (ISO string)
 * @returns Promise that resolves with nutrition statistics
 */
export async function getNutritionStats(startDate: string, endDate: string): Promise<{
  totalCalories: number;
  averageCalories: number;
  totalProtein: number;
  averageProtein: number;
  totalCarbs: number;
  averageCarbs: number;
  totalFat: number;
  averageFat: number;
  mealCounts: Record<string, number>;
}> {
  try {
    // Apply timeout to the potentially slow history fetch
    const history = await withTimeout(
      getHistory(),
      ASYNC_STORAGE_TIMEOUT,
      'Getting history for nutrition stats timed out'
    );
    
    // Filter foods within date range
    const start = new Date(startDate).getTime();
    const end = new Date(endDate).getTime();
    
    const foodsInRange = history.filter(food => {
      const foodDate = new Date(food.date).getTime();
      return foodDate >= start && foodDate <= end;
    });
    
    // Count days in range
    const dayCount = Math.max(1, Math.ceil((end - start) / (1000 * 60 * 60 * 24)));
    
    // Calculate totals - wrap in try/catch to handle any unexpected data issues
    try {
      const totals = foodsInRange.reduce(
        (sums, food) => {
          sums.calories += food.calories || 0;
          sums.protein += food.protein || 0;
          sums.carbs += food.carbs || 0;
          sums.fat += food.fat || 0;
          
          // Count meal types
          if (food.mealType) {
            sums.mealCounts[food.mealType] = (sums.mealCounts[food.mealType] || 0) + 1;
          }
          
          return sums;
        },
        { 
          calories: 0, 
          protein: 0, 
          carbs: 0, 
          fat: 0, 
          mealCounts: {} as Record<string, number> 
        }
      );
      
      return {
        totalCalories: totals.calories,
        averageCalories: Math.round(totals.calories / dayCount),
        totalProtein: totals.protein,
        averageProtein: Math.round(totals.protein / dayCount),
        totalCarbs: totals.carbs,
        averageCarbs: Math.round(totals.carbs / dayCount),
        totalFat: totals.fat,
        averageFat: Math.round(totals.fat / dayCount),
        mealCounts: totals.mealCounts
      };
    } catch (calculationError) {
      console.error('Error calculating nutrition totals:', calculationError);
      // Provide fallback values if calculation fails
      return {
        totalCalories: 0,
        averageCalories: 0,
        totalProtein: 0,
        averageProtein: 0,
        totalCarbs: 0,
        averageCarbs: 0,
        totalFat: 0,
        averageFat: 0,
        mealCounts: {}
      };
    }
  } catch (error) {
    console.error('Error getting nutrition stats:', error);
    return {
      totalCalories: 0,
      averageCalories: 0,
      totalProtein: 0,
      averageProtein: 0,
      totalCarbs: 0,
      averageCarbs: 0,
      totalFat: 0,
      averageFat: 0,
      mealCounts: {}
    };
  }
} 