/**
 * Food History Service
 * Enhanced service for storing, retrieving, and analyzing food history data
 * with both local caching (AsyncStorage) and cloud storage (Firebase)
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { getAuth } from 'firebase/auth';
import { getFirestore, collection, doc, setDoc, getDoc, getDocs, query, where, orderBy, limit, deleteDoc, addDoc, serverTimestamp, Timestamp, writeBatch } from 'firebase/firestore';
import { FoodAnalysisData as BaseFoodAnalysisData, getHistory as getCachedHistory, saveToHistory as saveToCachedHistory, deleteFromHistory as deleteFromCachedHistory, clearHistory as clearCachedHistory } from './foodCacheService';
import { startOfToday, startOfWeek, startOfMonth, subDays, subWeeks, subMonths, isWithinInterval, format } from 'date-fns';
import { useToast } from '@/components/Toast';
import { useState, useCallback, useEffect } from 'react';

// Firebase references
const db = getFirestore();
const auth = getAuth();

// Collection names
const FOOD_HISTORY_COLLECTION = 'food_history';
const NUTRITIONAL_INSIGHTS_COLLECTION = 'nutritional_insights';

// Default timeout for Firebase operations (ms)
const DEFAULT_FIREBASE_TIMEOUT = 15000;

// Helper function to add timeout to promises
function withTimeout<T>(promise: Promise<T>, timeoutMs: number, errorMessage: string): Promise<T> {
  // Create a timeout promise that rejects after specified milliseconds
  const timeoutPromise = new Promise<T>((_, reject) => {
    const timeoutId = setTimeout(() => {
      clearTimeout(timeoutId);
      reject(new Error(`Timeout (${timeoutMs}ms): ${errorMessage}`));
    }, timeoutMs);
  });

  // Race the original promise against the timeout
  return Promise.race([promise, timeoutPromise]);
}

// Extend the FoodAnalysisData to include Firebase-specific fields
export interface FoodAnalysisData extends BaseFoodAnalysisData {
  firebaseId?: string;
  syncedAt?: any; // Firestore timestamp
}

// Additional type for nutritional insights
export interface NutritionalInsight {
  id: string;
  userId: string;
  type: 'daily' | 'weekly' | 'monthly';
  date: string; // ISO date string
  summary: string;
  data: {
    totalCalories: number;
    totalProtein: number;
    totalCarbs: number;
    totalFat: number;
    mealCounts: Record<string, number>;
    topFoods: {
      name: string;
      count: number;
    }[];
    recommendations?: string[];
  };
  created: Date;
}

/**
 * Save a food item to history with toast notifications
 * @param foodData Food analysis data to save
 * @param showToast Optional toast notification function
 * @returns Promise resolving to success status
 */
export async function saveToHistory(
  foodData: FoodAnalysisData, 
  showToast?: (message: string, type: 'success' | 'error' | 'info' | 'warning') => void
): Promise<boolean> {
  try {
    // First save locally for immediate feedback
    await saveToCachedHistory(foodData, "saved");
    const localSuccess = true; // Assume success if no exception thrown
    
    // Try to save to Firestore if user is authenticated
    if (auth.currentUser) {
      try {
        const firestoreData = {
          ...foodData,
          userId: auth.currentUser.uid,
          created: new Date().toISOString(),
          synced: true
        };
        
        // Add to Firestore
        await addDoc(collection(db, FOOD_HISTORY_COLLECTION), firestoreData);
        
        // Show success toast if callback provided
        if (showToast) {
          showToast('Food saved to your history', 'success');
        }
        
        return true;
      } catch (error) {
        console.error('Error saving to Firestore:', error);
        
        // If Firestore fails but local succeeds, still return success but notify user
        if (localSuccess && showToast) {
          showToast('Food saved locally but not synced to cloud', 'warning');
        }
        
        return localSuccess;
      }
    }
    
    // If no user authenticated but local save succeeded
    if (localSuccess && showToast) {
      showToast('Food saved to local history', 'success');
    }
    
    return localSuccess;
  } catch (error) {
    console.error('Error in saveToHistory:', error);
    
    if (showToast) {
      showToast('Failed to save food to history', 'error');
    }
    
    return false;
  }
}

/**
 * Delete a food item from history with toast notifications
 * @param foodId ID of the food item to delete
 * @param showToast Optional toast notification function
 * @returns Promise resolving to success status
 */
export async function deleteFromHistory(
  foodId: string,
  showToast?: (message: string, type: 'success' | 'error' | 'info' | 'warning') => void
): Promise<boolean> {
  try {
    // First delete locally
    await deleteFromCachedHistory(foodId);
    const localSuccess = true; // Assume success if no exception thrown
    
    // Try to delete from Firestore if user is authenticated
    if (auth.currentUser) {
      try {
        // Create query to find the document by local ID
        const q = query(
          collection(db, FOOD_HISTORY_COLLECTION),
          where('userId', '==', auth.currentUser.uid),
          where('id', '==', foodId)
        );
        
        const querySnapshot = await getDocs(q);
        
        if (!querySnapshot.empty) {
          // Delete all matching documents (should be just one)
          const batch = writeBatch(db);
          querySnapshot.forEach((doc) => {
            batch.delete(doc.ref);
          });
          
          await batch.commit();
        }
        
        // Show success toast if callback provided
        if (showToast) {
          showToast('Food removed from history', 'success');
        }
        
        return true;
      } catch (error) {
        console.error('Error deleting from Firestore:', error);
        
        // If Firestore fails but local succeeds, still return success but notify
        if (localSuccess && showToast) {
          showToast('Food removed locally but not from cloud', 'warning');
        }
        
        return localSuccess;
      }
    }
    
    // If no user authenticated but local delete succeeded
    if (localSuccess && showToast) {
      showToast('Food removed from local history', 'success');
    }
    
    return localSuccess;
  } catch (error) {
    console.error('Error in deleteFromHistory:', error);
    
    if (showToast) {
      showToast('Failed to remove food from history', 'error');
    }
    
    return false;
  }
}

/**
 * Get food history
 * @returns Array of food history items
 */
export async function getHistory(): Promise<FoodAnalysisData[]> {
  return getFoodHistory();
}

/**
 * Food history hook for getting food history with notifications
 */
export function useFoodHistory() {
  const toast = useToast();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [foodHistory, setFoodHistory] = useState<FoodAnalysisData[]>([]);
  
  // Get history with better error handling
  const getHistoryWithToast = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const history = await getHistory();
      setFoodHistory(history);
      return history;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);
      
      toast.showToast({
        type: 'error',
        message: 'Failed to load food history',
        duration: 4000
      });
      
      return [];
    } finally {
      setLoading(false);
    }
  }, [toast]);
  
  // Add a food item with toast notification
  const addFoodWithToast = useCallback(async (foodData: FoodAnalysisData) => {
    try {
      const success = await saveToHistory(foodData);
      
      if (success) {
        toast.showToast({
          type: 'success',
          message: 'Food added to your history',
          duration: 3000
        });
        
        // Refresh history
        getHistoryWithToast();
        return true;
      } else {
        toast.showToast({
          type: 'error',
          message: 'Failed to add food to history',
          duration: 4000
        });
        return false;
      }
    } catch (err) {
      console.error('Error adding food with toast:', err);
      
      toast.showToast({
        type: 'error',
        message: 'Error adding food to history',
        duration: 4000
      });
      
      return false;
    }
  }, [toast, getHistoryWithToast]);
  
  // Delete a food item with toast notification
  const deleteFoodWithToast = useCallback(async (foodId: string) => {
    try {
      const success = await deleteFromHistory(foodId);
      
      if (success) {
        toast.showToast({
          type: 'info',
          message: 'Food removed from history',
          duration: 3000
        });
        
        // Refresh history
        getHistoryWithToast();
        return true;
      } else {
        toast.showToast({
          type: 'error',
          message: 'Failed to remove food from history',
          duration: 4000
        });
        return false;
      }
    } catch (err) {
      console.error('Error deleting food with toast:', err);
      
      toast.showToast({
        type: 'error',
        message: 'Error removing food from history',
        duration: 4000
      });
      
      return false;
    }
  }, [toast, getHistoryWithToast]);
  
  // Load history on mount
  useEffect(() => {
    getHistoryWithToast();
  }, [getHistoryWithToast]);
  
  return {
    foodHistory,
    loading,
    error,
    refreshHistory: getHistoryWithToast,
    addFood: addFoodWithToast,
    deleteFood: deleteFoodWithToast
  };
}

/**
 * Get food history with optional filters
 * @param options Filtering options
 * @returns Array of food history items matching the filters
 */
export async function getFoodHistory(options?: {
  mealType?: string; // 'breakfast', 'lunch', 'dinner', 'snack'
  startDate?: Date;
  endDate?: Date;
  searchTerm?: string;
  sortBy?: 'date' | 'calories' | 'protein' | 'carbs' | 'fat';
  sortDirection?: 'asc' | 'desc';
  limit?: number;
  useCloud?: boolean; // Whether to query from Firebase (if authenticated)
}): Promise<FoodAnalysisData[]> {
  try {
    // Set default options
    const defaultOptions = {
      startDate: subMonths(new Date(), 1), // Last month by default
      endDate: new Date(),
      sortBy: 'date' as const,
      sortDirection: 'desc' as const,
      limit: 50,
      useCloud: true
    };
    
    // Merge with provided options
    const mergedOptions = { ...defaultOptions, ...options };
    
    // If we're using cloud and user is authenticated, try to fetch from Firebase
    if (mergedOptions.useCloud && auth.currentUser) {
      try {
        return await getCloudFoodHistory(mergedOptions);
      } catch (error) {
        console.error('Error fetching cloud food history, falling back to local:', error);
        // Fall back to local cache if cloud fetch fails
      }
    }
    
    // Get from local cache
    return getLocalFoodHistory(mergedOptions);
  } catch (error) {
    console.error('Error getting food history:', error);
    return [];
  }
}

/**
 * Get food history from local cache with filters
 */
async function getLocalFoodHistory(options: {
  mealType?: string;
  startDate: Date;
  endDate: Date;
  searchTerm?: string;
  sortBy: 'date' | 'calories' | 'protein' | 'carbs' | 'fat';
  sortDirection: 'asc' | 'desc';
  limit: number;
}): Promise<FoodAnalysisData[]> {
  try {
    let history = await getCachedHistory() as FoodAnalysisData[];
    
    // Apply date filter
    history = history.filter(item => {
      const itemDate = new Date(item.date);
      return isWithinInterval(itemDate, { start: options.startDate, end: options.endDate });
    });
    
    // Apply meal type filter if specified
    if (options.mealType && options.mealType !== 'all') {
      history = history.filter(item => 
        item.mealType?.toLowerCase() === options.mealType?.toLowerCase()
      );
    }
    
    // Apply search term filter if specified
    if (options.searchTerm) {
      const searchTermLower = options.searchTerm.toLowerCase();
      history = history.filter(item => 
        item.name.toLowerCase().includes(searchTermLower) ||
        item.items.some(food => food.name.toLowerCase().includes(searchTermLower))
      );
    }
    
    // Apply sorting
    history.sort((a, b) => {
      let comparison: number;
      
      switch (options.sortBy) {
        case 'calories':
          comparison = (a.calories || 0) - (b.calories || 0);
          break;
        case 'protein':
          comparison = (a.protein || 0) - (b.protein || 0);
          break;
        case 'carbs':
          comparison = (a.carbs || 0) - (b.carbs || 0);
          break;
        case 'fat':
          comparison = (a.fat || 0) - (b.fat || 0);
          break;
        case 'date':
        default:
          comparison = new Date(a.date).getTime() - new Date(b.date).getTime();
          break;
      }
      
      return options.sortDirection === 'asc' ? comparison : -comparison;
    });
    
    // Apply limit
    return history.slice(0, options.limit);
  } catch (error) {
    console.error('Error getting local food history:', error);
    return [];
  }
}

/**
 * Get food history from Firebase with filters
 */
async function getCloudFoodHistory(options: {
  mealType?: string;
  startDate: Date;
  endDate: Date;
  searchTerm?: string;
  sortBy: 'date' | 'calories' | 'protein' | 'carbs' | 'fat';
  sortDirection: 'asc' | 'desc';
  limit: number;
}): Promise<FoodAnalysisData[]> {
  try {
    if (!auth.currentUser) {
      throw new Error('User not authenticated');
    }
    
    const userId = auth.currentUser.uid;
    
    // Start building query
    let q = query(
      collection(db, FOOD_HISTORY_COLLECTION),
      where('userId', '==', userId),
      where('date', '>=', options.startDate.toISOString()),
      where('date', '<=', options.endDate.toISOString())
    );
    
    // Add meal type filter if specified
    if (options.mealType && options.mealType !== 'all') {
      q = query(q, where('mealType', '==', options.mealType));
    }
    
    // Add sorting
    q = query(
      q, 
      orderBy(options.sortBy === 'date' ? 'date' : options.sortBy, 
      options.sortDirection === 'asc' ? 'asc' : 'desc')
    );
    
    // Execute query with timeout
    const snapshot = await withTimeout(
      getDocs(q),
      DEFAULT_FIREBASE_TIMEOUT,
      'Firebase query timed out'
    );
    
    // Convert to FoodAnalysisData objects
    let results = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        ...data,
        id: doc.id,
        date: data.date || new Date().toISOString() // Ensure date exists
      } as FoodAnalysisData;
    });
    
    // Apply text search filter if specified (need to do this client-side for Firestore)
    if (options.searchTerm) {
      const searchTermLower = options.searchTerm.toLowerCase();
      results = results.filter(item => 
        item.name.toLowerCase().includes(searchTermLower) ||
        item.items.some(food => food.name.toLowerCase().includes(searchTermLower))
      );
    }
    
    // Apply limit in JavaScript
    return results.slice(0, options.limit);
  } catch (error) {
    console.error('Error getting cloud food history:', error);
    throw error;
  }
}

/**
 * Clear all food history
 * @param clearFromCloud Whether to also clear from Firebase (if authenticated)
 */
export async function clearFoodHistory(clearFromCloud: boolean = true): Promise<void> {
  try {
    // Clear local cache
    await clearCachedHistory();
    
    // If clearFromCloud is true and user is authenticated, delete all user's items from Firebase
    if (clearFromCloud && auth.currentUser) {
      const userId = auth.currentUser.uid;
      
      // Query for all user's food history items
      const q = query(
        collection(db, FOOD_HISTORY_COLLECTION),
        where('userId', '==', userId)
      );
      
      const snapshot = await getDocs(q);
      
      // Delete each item
      const deletePromises = snapshot.docs.map(doc => 
        deleteDoc(doc.ref)
      );
      
      await Promise.all(deletePromises);
    }
  } catch (error) {
    console.error('Error clearing food history:', error);
    throw error;
  }
}

/**
 * Sync local food history with Firebase
 * @returns Number of items synced
 */
export async function syncFoodHistory(): Promise<number> {
  try {
    if (!auth.currentUser) {
      throw new Error('User not authenticated');
    }
    
    const userId = auth.currentUser.uid;
    
    // Get local history
    const localHistory = await getCachedHistory() as FoodAnalysisData[];
    
    // Filter for items that don't have a Firebase ID
    const unsyncedItems = localHistory.filter(item => !item.firebaseId);
    
    // Upload each unsynced item
    const syncPromises = unsyncedItems.map(async item => {
      // Create a Firestore reference for this item
      const itemRef = doc(collection(db, FOOD_HISTORY_COLLECTION));
      
      // Save to Firestore
      await setDoc(itemRef, {
        ...item,
        firebaseId: itemRef.id,
        userId,
        syncedAt: serverTimestamp(),
      });
      
      // Update local item with Firebase ID
      return {
        ...item,
        firebaseId: itemRef.id
      };
    });
    
    // Wait for all syncs to complete
    const syncedItems = await Promise.all(syncPromises);
    
    // Update local cache with Firebase IDs
    if (syncedItems.length > 0) {
      // Create a map for quick lookups
      const syncedItemMap = new Map(
        syncedItems.map(item => [item.id, item])
      );
      
      // Update local history
      const updatedHistory = localHistory.map(item => 
        syncedItemMap.has(item.id) ? syncedItemMap.get(item.id)! : item
      );
      
      // Save updated history back to AsyncStorage
      await AsyncStorage.setItem('food_analysis_history', JSON.stringify(updatedHistory));
    }
    
    return syncedItems.length;
  } catch (error) {
    console.error('Error syncing food history:', error);
    throw error;
  }
}

/**
 * Generate and save a nutritional insight
 * @param type Type of insight (daily, weekly, monthly)
 * @returns The generated insight
 */
export async function generateNutritionalInsight(
  type: 'daily' | 'weekly' | 'monthly' = 'daily'
): Promise<NutritionalInsight> {
  try {
    if (!auth.currentUser) {
      throw new Error('User not authenticated');
    }
    
    const userId = auth.currentUser.uid;
    
    // Determine date range based on type
    let startDate = new Date();
    const endDate = new Date();
    
    switch (type) {
      case 'daily':
        startDate = startOfToday();
        break;
      case 'weekly':
        startDate = startOfWeek(new Date());
        break;
      case 'monthly':
        startDate = startOfMonth(new Date());
        break;
    }
    
    // Get food history for the period
    const foodHistory = await getFoodHistory({
      startDate,
      endDate,
      useCloud: true
    });
    
    // Calculate totals
    const totals = foodHistory.reduce(
      (sums, food) => {
        sums.calories += food.calories || 0;
        sums.protein += food.protein || 0;
        sums.carbs += food.carbs || 0;
        sums.fat += food.fat || 0;
        
        // Count meal types
        const mealType = food.mealType?.toLowerCase() || 'unknown';
        sums.mealCounts[mealType] = (sums.mealCounts[mealType] || 0) + 1;
        
        // Count food items for top foods
        food.items.forEach(item => {
          const foodName = item.name.toLowerCase();
          sums.foodCounts[foodName] = (sums.foodCounts[foodName] || 0) + 1;
        });
        
        return sums;
      },
      { 
        calories: 0, 
        protein: 0, 
        carbs: 0, 
        fat: 0, 
        mealCounts: {} as Record<string, number>,
        foodCounts: {} as Record<string, number>
      }
    );
    
    // Calculate top foods
    const topFoods = Object.entries(totals.foodCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([name, count]) => ({
        name: name.charAt(0).toUpperCase() + name.slice(1), // Capitalize
        count
      }));
    
    // Generate a human-readable summary
    let summary = '';
    
    switch (type) {
      case 'daily':
        summary = `Today you consumed ${totals.calories} calories, with ${totals.protein}g protein, ${totals.carbs}g carbs, and ${totals.fat}g fat.`;
        break;
      case 'weekly':
        summary = `This week you've consumed ${totals.calories} calories, with ${totals.protein}g protein, ${totals.carbs}g carbs, and ${totals.fat}g fat.`;
        break;
      case 'monthly':
        summary = `This month you've consumed ${totals.calories} calories, with ${totals.protein}g protein, ${totals.carbs}g carbs, and ${totals.fat}g fat.`;
        break;
    }
    
    // Create a nutritional insight object
    const insight: NutritionalInsight = {
      id: Date.now().toString(36) + Math.random().toString(36).substring(2, 7),
      userId,
      type,
      date: new Date().toISOString(),
      summary,
      data: {
        totalCalories: totals.calories,
        totalProtein: totals.protein,
        totalCarbs: totals.carbs,
        totalFat: totals.fat,
        mealCounts: totals.mealCounts,
        topFoods,
      },
      created: new Date()
    };
    
    // Save to Firebase
    const insightRef = await addDoc(collection(db, NUTRITIONAL_INSIGHTS_COLLECTION), {
      ...insight,
      created: serverTimestamp()
    });
    
    // Add the Firebase ID to the insight
    insight.id = insightRef.id;
    
    return insight;
  } catch (error) {
    console.error('Error generating nutritional insight:', error);
    throw error;
  }
}

/**
 * Get nutritional insights
 * @param type Optional type filter (daily, weekly, monthly)
 * @param limit Maximum number of insights to retrieve
 * @returns Array of nutritional insights
 */
export async function getNutritionalInsights(
  type?: 'daily' | 'weekly' | 'monthly',
  limit: number = 10
): Promise<NutritionalInsight[]> {
  try {
    if (!auth.currentUser) {
      throw new Error('User not authenticated');
    }
    
    const userId = auth.currentUser.uid;
    
    // Build query
    let q = query(
      collection(db, NUTRITIONAL_INSIGHTS_COLLECTION),
      where('userId', '==', userId)
    );
    
    // Add type filter if specified
    if (type) {
      q = query(q, where('type', '==', type));
    }
    
    // Add sorting (handle limit later with JavaScript)
    q = query(q, orderBy('created', 'desc'));
    
    // Execute query with timeout
    const snapshot = await withTimeout(
      getDocs(q),
      DEFAULT_FIREBASE_TIMEOUT,
      'Nutritional insights query timed out'
    );
    
    // Convert to NutritionalInsight objects
    const results = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        ...data,
        id: doc.id,
        created: data.created?.toDate() || new Date()
      } as NutritionalInsight;
    });
    
    // Apply limit in JavaScript instead of in the query
    return results.slice(0, limit);
  } catch (error) {
    console.error('Error getting nutritional insights:', error);
    return [];
  }
}

/**
 * Export food history as JSON
 * @returns JSON string of food history
 */
export async function exportFoodHistoryAsJson(): Promise<string> {
  try {
    const history = await getCachedHistory();
    return JSON.stringify(history);
  } catch (error) {
    console.error('Error exporting food history:', error);
    throw error;
  }
}

/**
 * Import food history from JSON
 * @param jsonData JSON string of food history
 * @param overwrite Whether to overwrite existing history (default: false)
 * @returns Number of items imported
 */
export async function importFoodHistoryFromJson(
  jsonData: string,
  overwrite: boolean = false
): Promise<number> {
  try {
    // Parse the JSON data
    const importedHistory = JSON.parse(jsonData) as FoodAnalysisData[];
    
    if (!Array.isArray(importedHistory)) {
      throw new Error('Invalid import data format');
    }
    
    // If overwriting, clear existing history first
    if (overwrite) {
      await clearCachedHistory();
      await AsyncStorage.setItem('food_analysis_history', jsonData);
      return importedHistory.length;
    }
    
    // Otherwise, merge with existing history
    const currentHistory = await getCachedHistory() as FoodAnalysisData[];
    
    // Get IDs of current items
    const existingIds = new Set(currentHistory.map(item => item.id));
    
    // Filter out imported items that already exist
    const newItems = importedHistory.filter(item => !existingIds.has(item.id));
    
    // Merge current and new items
    const mergedHistory = [...currentHistory, ...newItems];
    
    // Save merged history
    await AsyncStorage.setItem('food_analysis_history', JSON.stringify(mergedHistory));
    
    return newItems.length;
  } catch (error) {
    console.error('Error importing food history:', error);
    throw error;
  }
}

/**
 * Get most frequent foods
 * @param limit Maximum number of foods to retrieve
 * @param period Optional time period filter ('week', 'month', 'year', or undefined for all time)
 * @returns Array of foods with counts
 */
export async function getMostFrequentFoods(
  limit: number = 10,
  period?: 'week' | 'month' | 'year'
): Promise<{ name: string; count: number }[]> {
  try {
    // Determine start date based on period
    let startDate = new Date(0); // Beginning of time by default
    
    if (period) {
      const now = new Date();
      switch (period) {
        case 'week':
          startDate = subWeeks(now, 1);
          break;
        case 'month':
          startDate = subMonths(now, 1);
          break;
        case 'year':
          startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
          break;
      }
    }
    
    // Get food history for the period
    const foodHistory = await getFoodHistory({
      startDate,
      endDate: new Date(),
      useCloud: true
    });
    
    // Count food items
    const foodCounts: Record<string, number> = {};
    
    foodHistory.forEach(entry => {
      entry.items.forEach(item => {
        const foodName = item.name.toLowerCase();
        foodCounts[foodName] = (foodCounts[foodName] || 0) + 1;
      });
    });
    
    // Sort by count and take top ones
    return Object.entries(foodCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([name, count]) => ({
        name: name.charAt(0).toUpperCase() + name.slice(1), // Capitalize
        count
      }));
  } catch (error) {
    console.error('Error getting most frequent foods:', error);
    return [];
  }
}

/**
 * Get daily nutritional averages
 * @param period Period for averaging ('week', 'month', or 'year')
 * @returns Daily averages for nutritional values
 */
export async function getDailyNutritionalAverages(
  period: 'week' | 'month' | 'year'
): Promise<{
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
}> {
  try {
    // Determine date range
    const now = new Date();
    let startDate: Date;
    let dayCount: number;
    
    switch (period) {
      case 'week':
        startDate = subWeeks(now, 1);
        dayCount = 7;
        break;
      case 'month':
        startDate = subMonths(now, 1);
        dayCount = 30; // Approximate
        break;
      case 'year':
        startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
        dayCount = 365; // Approximate
        break;
    }
    
    // Get food history for the period
    const foodHistory = await getFoodHistory({
      startDate,
      endDate: now,
      useCloud: true
    });
    
    // Sum nutritional values
    const totals = foodHistory.reduce(
      (sums, food) => {
        sums.calories += food.calories || 0;
        sums.protein += food.protein || 0;
        sums.carbs += food.carbs || 0;
        sums.fat += food.fat || 0;
        return sums;
      },
      { calories: 0, protein: 0, carbs: 0, fat: 0 }
    );
    
    // Calculate daily averages
    return {
      calories: Math.round(totals.calories / dayCount),
      protein: Math.round(totals.protein / dayCount),
      carbs: Math.round(totals.carbs / dayCount),
      fat: Math.round(totals.fat / dayCount)
    };
  } catch (error) {
    console.error('Error getting daily nutritional averages:', error);
    return { calories: 0, protein: 0, carbs: 0, fat: 0 };
  }
}

/**
 * Trigger a notification for food saved to history
 * This should be called after a food is successfully saved to history
 * @param foodData Food data that was saved
 * @param notificationCallback Optional callback function to trigger notification display
 */
export function triggerFoodSavedNotification(
  foodData: FoodAnalysisData,
  notificationCallback?: (notification: { 
    title: string;
    message: string;
    type: 'success' | 'info' | 'warning' | 'error'
  }) => void
) {
  try {
    if (!notificationCallback) return;
    
    const calories = Math.round(foodData.calories || 0);
    const protein = Math.round(foodData.protein || 0);
    
    notificationCallback({
      title: 'Food Added to History',
      message: `${foodData.name} (${calories} calories, ${protein}g protein) has been added to your food history.`,
      type: 'success'
    });
  } catch (error) {
    console.error('Error triggering food saved notification:', error);
    // Don't throw - this is a non-critical function
  }
} 