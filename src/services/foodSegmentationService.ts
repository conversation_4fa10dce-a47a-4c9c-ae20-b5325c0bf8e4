
export async function segmentFoodImage(
  imageUri: string,
  options: SegmentationOptions = {}
): Promise<SegmentationResult> {
  const startTime = Date.now();
  
  try {
    // Preprocess the image to a standard size
    const preprocessedImage = await preprocessImage(imageUri);
    
    
    const imageId = `segment-${Date.now()}`;
    let imageUrl = preprocessedImage;
    
    if (options.userId) {
      
      imageUrl = await uploadImageTofirebase(preprocessedImage, options.userId, imageId);
    }
    
    
    const segments = await performSegmentationWithfirebase(imageUrl, options);
    
    // Generate visualization if requested
    let visualizedImage = preprocessedImage;
    if (options.returnVisualizedImage && segments.length > 0) {
      visualizedImage = await fetchVisualizedImageFromfirebase(imageUrl, segments, options);
    }
    
    
    if (options.userId) {
      await saveSegmentationResultTofirebase(options.userId, imageId, segments);
    }
    
    return {
      segments,
      image: visualizedImage,
      processingTime: Date.now() - startTime
    };
  } catch (error: any) {
    console.error('Food segmentation error:', error);
    throw new Error(`Failed to segment food image: ${error.message}`);
  }
}


async function performSegmentationWithfirebase(
  imageUrl: string,
  options: SegmentationOptions
): Promise<FoodSegment[]> {
  try {
    // Call Firebase Cloud Function for food segmentation
    const functions = getFunctions();
    const foodSegmentationFn = httpsCallable(functions, 'generateSegmentVisualization');
    
    const result = await foodSegmentationFn({ 
      imageUrl, 
      threshold: options.threshold || 0.5,
      refineEdges: options.refineEdges || false,
      enhancedModel: options.useEnhancedModel || false,
      includeMasks: options.includeMasks || false
    });
  
    // No segments detected
    if (!result.data) return [];
    
    // Cast the response to the expected type
    const responseData = result.data as any;
    if (!responseData.segments || !Array.isArray(responseData.segments) || responseData.segments.length === 0) {
      return [];
    }
  
    return responseData.segments as FoodSegment[];
  } catch (error) {
    console.error('Error calling food segmentation function:', error);
    throw error;
  }
}


async function saveSegmentationResultTofirebase(
  userId: string,
  imageId: string,
  segments: FoodSegment[]
): Promise<void> {
  try {
    await addDoc(collection(firestore, 'food_segments'), {
      user_id: userId,
      image_id: imageId,
      segments: segments,
      created_at: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error saving segmentation results:', error);
  }
}


export function useFoodSegmentationService() {
  const { db, useFirebase  } = useDatabaseType();

  /**
   * Process a food image to detect and segment food items
   */
  const processFoodImage = async (
    imageUrl: string,
    options?: FoodSegmentationOptions
  ): Promise<FoodSegmentationResult> => {
    try {
      if (useFirebase) {
        // Use Firebase Cloud Function
        const functions = getFunctions();
        const foodSegmentationFn = httpsCallable<
          { imageUrl: string } & FoodSegmentationOptions,
          FoodSegmentationResult
        >(functions, 'foodSegmentation');

        const result = await foodSegmentationFn({
          imageUrl,
          ...options
        });

        return result.data;
      } else {
        // Use alternative implementation for non-Firebase environment
        const functions = getFunctions();
        const foodSegmentationFn = httpsCallable<
          { imageUrl: string } & FoodSegmentationOptions,
          FoodSegmentationResult
        >(functions, 'foodSegmentation');

        const result = await foodSegmentationFn({
          imageUrl,
          ...options
        });

        return result.data;
      }
    } catch (error) {
      console.error('Error in foodSegmentationService:', error);
      throw error;
    }
  };

  /**
   * Get stored segmentation results for a user
   */
  const getUserSegmentationResults = async (userId: string, limit = 10): Promise<FoodSegmentationResult[]> => {
    try {
      const collection = useFirebase ? 'food_segmentations' : 'food_segmentations';
      const filters = [
        { field: 'userId', operator: '==', value: userId }
      ];
      
      // Adjust the query to match the DatabaseProvider interface
      const results = await db.queryDocuments(collection, filters);
      
      // Apply the limit after fetching
      return (results as FoodSegmentationResult[]).slice(0, limit);
    } catch (error) {
      console.error('Error getting segmentation results:', error);
      throw error;
    }
  };

  /**
   * Get a specific segmentation result
   */
  const getSegmentationResult = async (resultId: string): Promise<FoodSegmentationResult | null> => {
    try {
      const collection = useFirebase ? 'food_segmentations' : 'food_segmentations';
      const result = await db.getDocument(collection, resultId);
      return result as FoodSegmentationResult;
    } catch (error) {
      console.error(`Error getting segmentation result ${resultId}:`, error);
      return null;
    }
  };

  /**
   * Delete a segmentation result
   */
  const deleteSegmentationResult = async (resultId: string): Promise<void> => {
    try {
      const collection = useFirebase ? 'food_segmentations' : 'food_segmentations';
      await db.deleteDocument(collection, resultId);
    } catch (error) {
      console.error(`Error deleting segmentation result ${resultId}:`, error);
      throw error;
    }
  };

  return {
    processFoodImage,
    getUserSegmentationResults,
    getSegmentationResult,
    deleteSegmentationResult
  };
} 