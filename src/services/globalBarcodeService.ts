import AsyncStorage from '@react-native-async-storage/async-storage';
import { FoodItem } from '@/types/food';
import * as Network from 'expo-network';
import { recognizeBarcode } from './vision/barcodeRecognition';
import { db } from '@/lib/firebase';
import { doc, setDoc, getDoc, serverTimestamp } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

// Define the supported barcode databases
export enum BarcodeDatabase {
  OPEN_FOOD_FACTS = 'openfoodfacts',
  USDA = 'usda',
  TESCO = 'tesco',
  AMAZON = 'amazon',
  INTERNAL = 'internal'
}

// Define product regions for region-specific queries
export enum ProductRegion {
  NORTH_AMERICA = 'north_america',
  EUROPE = 'europe',
  ASIA = 'asia',
  AUSTRALIA = 'australia',
  GLOBAL = 'global'
}

// Configuration for the barcode service
interface GlobalBarcodeConfig {
  primaryDatabase: BarcodeDatabase;
  fallbackDatabases: BarcodeDatabase[];
  defaultRegion: ProductRegion;
  cacheExpiration: number; // in milliseconds
  offlineMode: boolean;
  apiKeys: Record<BarcodeDatabase, string>;
}

// Default configuration
const DEFAULT_CONFIG: GlobalBarcodeConfig = {
  primaryDatabase: BarcodeDatabase.OPEN_FOOD_FACTS,
  fallbackDatabases: [BarcodeDatabase.INTERNAL, BarcodeDatabase.USDA],
  defaultRegion: ProductRegion.GLOBAL,
  cacheExpiration: 30 * 24 * 60 * 60 * 1000, // 30 days
  offlineMode: false,
  apiKeys: {
    [BarcodeDatabase.OPEN_FOOD_FACTS]: '',
    [BarcodeDatabase.USDA]: process.env.EXPO_PUBLIC_USDA_API_KEY || '',
    [BarcodeDatabase.TESCO]: process.env.EXPO_PUBLIC_TESCO_API_KEY || '',
    [BarcodeDatabase.AMAZON]: process.env.EXPO_PUBLIC_AMAZON_API_KEY || '',
    [BarcodeDatabase.INTERNAL]: ''
  }
};

// Current configuration
let currentConfig: GlobalBarcodeConfig = { ...DEFAULT_CONFIG };

// Storage keys
const BARCODE_CACHE_KEY = 'barcode_cache_';
const CONFIG_STORAGE_KEY = 'global_barcode_config';

// Product recognition result
export interface BarcodeRecognitionResult {
  product: FoodItem | null;
  success: boolean;
  error?: string;
  source?: BarcodeDatabase;
  cachedResult?: boolean;
}

interface NutritionData {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  servingSize?: string;
  category?: string;
}

// Tesco API response interfaces
interface TescoApiResponse {
  products?: TescoProduct[];
  status?: number;
}

interface TescoProduct {
  description?: string;
  brand?: string;
  superDepartment?: string;
  image?: string;
  calcNutrition?: {
    calories?: number;
    protein?: number;
    carbohydrate?: number;
    fat?: number;
  };
}

// Amazon API response interfaces
interface AmazonApiResponse {
  SearchResult?: {
    Items?: AmazonItem[];
  };
}

interface AmazonItem {
  ItemInfo: {
    Title: {
      DisplayValue: string;
    };
    ByLineInfo?: {
      Brand?: {
        DisplayValue: string;
      };
    };
    Classifications?: {
      ProductGroup?: {
        DisplayValue: string;
      };
    };
  };
  Images?: {
    Primary?: {
      Medium?: {
        URL: string;
      };
    };
  };
}

// Add this with your other interfaces
interface OpenFoodFactsResponse {
  status: number;
  product?: {
    product_name?: string;
    nutriments?: any;
    brands?: string;
    categories_tags?: string[];
    image_url?: string;
    serving_size_g?: number;
  };
}

// Add this with your other interfaces
interface USDAApiResponse {
  foods: {
    fdcId: string;
    description: string;
    foodNutrients: {
      nutrientId: number;
      value: number;
    }[];
    brandName?: string;
    foodCategory?: string;
    servingSize?: number;
  }[];
}

/**
 * Initialize the global barcode service
 */
export async function initGlobalBarcodeService(config?: Partial<GlobalBarcodeConfig>): Promise<void> {
  try {
    // Load stored configuration if exists
    const storedConfigJson = await AsyncStorage.getItem(CONFIG_STORAGE_KEY);
    if (storedConfigJson) {
      const storedConfig = JSON.parse(storedConfigJson);
      currentConfig = { ...currentConfig, ...storedConfig };
    }

    // Override with provided config if any
    if (config) {
      currentConfig = { ...currentConfig, ...config };
      await AsyncStorage.setItem(CONFIG_STORAGE_KEY, JSON.stringify(currentConfig));
    }

    // Check network availability to set offline mode
    const networkState = await Network.getNetworkStateAsync();
    currentConfig.offlineMode = !networkState.isConnected || !networkState.isInternetReachable;
    
    console.log('Global Barcode Service initialized', { 
      offlineMode: currentConfig.offlineMode,
      primaryDatabase: currentConfig.primaryDatabase
    });
  } catch (error) {
    console.error('Error initializing global barcode service:', error);
  }
}

/**
 * Set the global barcode service configuration
 */
export async function setGlobalBarcodeConfig(config: Partial<GlobalBarcodeConfig>): Promise<void> {
  try {
    currentConfig = { ...currentConfig, ...config };
    await AsyncStorage.setItem(CONFIG_STORAGE_KEY, JSON.stringify(currentConfig));
  } catch (error) {
    console.error('Error saving global barcode config:', error);
  }
}

/**
 * Get the current global barcode service configuration
 */
export function getGlobalBarcodeConfig(): GlobalBarcodeConfig {
  return { ...currentConfig };
}

/**
 * Set the preferred region for product lookups
 */
export async function setPreferredRegion(region: ProductRegion): Promise<void> {
  try {
    currentConfig.defaultRegion = region;
    await AsyncStorage.setItem(CONFIG_STORAGE_KEY, JSON.stringify(currentConfig));
  } catch (error) {
    console.error('Error setting preferred region:', error);
  }
}

/**
 * Toggle offline mode
 */
export async function setOfflineMode(enabled: boolean): Promise<void> {
  try {
    currentConfig.offlineMode = enabled;
    await AsyncStorage.setItem(CONFIG_STORAGE_KEY, JSON.stringify(currentConfig));
  } catch (error) {
    console.error('Error setting offline mode:', error);
  }
}

/**
 * Recognize a barcode using global databases with fallback
 */
export async function recognizeBarcodeGlobal(
  barcode: string,
  region: ProductRegion = currentConfig.defaultRegion
): Promise<BarcodeRecognitionResult> {
  try {
    if (!barcode) {
      throw new Error('Barcode is empty');
    }

    // First check the local cache
    const cachedProduct = await getCachedProduct(barcode);
    if (cachedProduct) {
      return {
        product: cachedProduct,
        success: true,
        cachedResult: true,
        source: BarcodeDatabase.INTERNAL
      };
    }

    // If in offline mode, just use the existing barcode recognition
    if (currentConfig.offlineMode) {
      return recognizeBarcode(barcode);
    }

    // Try the primary database first
    const primaryResult = await queryDatabase(currentConfig.primaryDatabase, barcode, region);
    if (primaryResult.success && primaryResult.product) {
      await cacheProduct(barcode, primaryResult.product);
      return primaryResult;
    }

    // Try fallback databases
    for (const database of currentConfig.fallbackDatabases) {
      const fallbackResult = await queryDatabase(database, barcode, region);
      if (fallbackResult.success && fallbackResult.product) {
        await cacheProduct(barcode, fallbackResult.product);
        return fallbackResult;
      }
    }

    // All databases failed, try existing barcode recognition as last resort
    return recognizeBarcode(barcode);
  } catch (error) {
    console.error('Error in global barcode recognition:', error);
    return {
      product: null,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error during barcode recognition'
    };
  }
}

/**
 * Query a specific database for a barcode
 */
async function queryDatabase(
  database: BarcodeDatabase,
  barcode: string,
  region: ProductRegion
): Promise<BarcodeRecognitionResult> {
  try {
    switch (database) {
      case BarcodeDatabase.OPEN_FOOD_FACTS:
        return await queryOpenFoodFacts(barcode, region);
      case BarcodeDatabase.USDA:
        return await queryUSDA(barcode);
      case BarcodeDatabase.TESCO:
        return await queryTesco(barcode);
      case BarcodeDatabase.AMAZON:
        return await queryAmazon(barcode);
      case BarcodeDatabase.INTERNAL:
        return await recognizeBarcode(barcode);
      default:
        throw new Error(`Unsupported database: ${database}`);
    }
  } catch (error) {
    console.error(`Error querying database ${database}:`, error);
    return {
      product: null,
      success: false,
      error: error instanceof Error ? error.message : `Failed to query ${database}`,
      source: database
    };
  }
}

/**
 * Query Open Food Facts database
 */
async function queryOpenFoodFacts(
  barcode: string,
  region: ProductRegion
): Promise<BarcodeRecognitionResult> {
  try {
    // Determine country parameter based on region
    let country = '';
    switch (region) {
      case ProductRegion.NORTH_AMERICA:
        country = 'usa,canada,mexico';
        break;
      case ProductRegion.EUROPE:
        country = 'uk,france,germany,spain,italy';
        break;
      case ProductRegion.ASIA:
        country = 'japan,china,india,korea';
        break;
      case ProductRegion.AUSTRALIA:
        country = 'australia,new-zealand';
        break;
      case ProductRegion.GLOBAL:
        // No country filter
        break;
    }

    // Construct the URL
    let url = `https://world.openfoodfacts.org/api/v0/product/${barcode}.json`;
    
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Open Food Facts API returned ${response.status}: ${response.statusText}`);
    }

    const data = await response.json() as OpenFoodFactsResponse;

    if (!data || data.status !== 1 || !data.product) {
      return {
        product: null,
        success: false,
        error: 'Product not found in Open Food Facts',
        source: BarcodeDatabase.OPEN_FOOD_FACTS
      };
    }

    // Extract nutritional data
    const nutriments = data.product.nutriments || {};
    
    // Map to our FoodItem interface
    const product: FoodItem = {
      id: `off-${barcode}`,
      name: data.product.product_name || 'Unknown Product',
      calories: nutriments.energy_kcal_100g || nutriments.energy_kcal || 0,
      protein: nutriments.proteins_100g || 0,
      carbs: nutriments.carbohydrates_100g || 0,
      fat: nutriments.fat_100g || 0,
      barcode: barcode,
      brand: data.product.brands || '',
      category: data.product.categories_tags ? data.product.categories_tags[0] : '',
      imageUrl: data.product.image_url || '',
      portions: [
        {
          name: 'serving',
          grams: data.product.serving_size_g || 100,
          quantity: 1
        }
      ]
    };

    return {
      product,
      success: true,
      source: BarcodeDatabase.OPEN_FOOD_FACTS
    };
  } catch (error) {
    console.error('Error querying Open Food Facts:', error);
    return {
      product: null,
      success: false,
      error: error instanceof Error ? error.message : 'Error querying Open Food Facts',
      source: BarcodeDatabase.OPEN_FOOD_FACTS
    };
  }
}

/**
 * Query USDA FoodData Central database
 */
async function queryUSDA(barcode: string): Promise<BarcodeRecognitionResult> {
  try {
    const apiKey = currentConfig.apiKeys[BarcodeDatabase.USDA];
    if (!apiKey) {
      throw new Error('USDA API key not configured');
    }

    // First query UPC to FDC ID
    const searchUrl = `https://api.nal.usda.gov/fdc/v1/foods/search?api_key=${apiKey}&query=${barcode}&dataType=Branded`;
    
    const searchResponse = await fetch(searchUrl);
    if (!searchResponse.ok) {
      throw new Error(`USDA API returned ${searchResponse.status}: ${searchResponse.statusText}`);
    }

    const searchData = await searchResponse.json() as USDAApiResponse;
    
    if (!searchData.foods || searchData.foods.length === 0) {
      return {
        product: null,
        success: false,
        error: 'Product not found in USDA database',
        source: BarcodeDatabase.USDA
      };
    }

    // Get the first matching food
    const food = searchData.foods[0];
    
    // Extract nutritional data
    const nutrients = food.foodNutrients || [];
    const getNutrientValue = (id: number) => {
      const nutrient = nutrients.find(n => n.nutrientId === id);
      return nutrient ? nutrient.value : 0;
    };

    // Map to our FoodItem interface
    const product: FoodItem = {
      id: `usda-${food.fdcId}`,
      name: food.description || 'Unknown Product',
      calories: getNutrientValue(1008), // Energy (kcal)
      protein: getNutrientValue(1003), // Protein
      carbs: getNutrientValue(1005), // Carbohydrates
      fat: getNutrientValue(1004), // Total lipid (fat)
      barcode: barcode,
      brand: food.brandName || '',
      category: food.foodCategory || '',
      portions: [
        {
          name: 'serving',
          grams: food.servingSize || 100,
          quantity: 1
        }
      ]
    };

    return {
      product,
      success: true,
      source: BarcodeDatabase.USDA
    };
  } catch (error) {
    console.error('Error querying USDA:', error);
    return {
      product: null,
      success: false,
      error: error instanceof Error ? error.message : 'Error querying USDA',
      source: BarcodeDatabase.USDA
    };
  }
}

/**
 * Query Tesco database (placeholder implementation)
 */
async function queryTesco(barcode: string): Promise<BarcodeRecognitionResult> {
  try {
    const apiKey = currentConfig.apiKeys[BarcodeDatabase.TESCO];
    if (!apiKey) {
      throw new Error('Tesco API key not configured');
    }

    // Tesco's Grocery API endpoint
    const url = `https://dev.tescolabs.com/product/?gtin=${barcode}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Ocp-Apim-Subscription-Key': apiKey,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Tesco API returned ${response.status}: ${response.statusText}`);
    }

    const data = await response.json() as TescoApiResponse;
    
    if (!data.products || data.products.length === 0) {
      return {
        product: null,
        success: false,
        error: 'Product not found in Tesco database',
        source: BarcodeDatabase.TESCO
      };
    }

    const product = data.products[0];
    
    // Extract nutrition data if available
    const nutrition = product.calcNutrition || {};
    
    // Map to our FoodItem interface
    const foodItem: FoodItem = {
      id: `tesco-${barcode}`,
      name: product.description || 'Unknown Product',
      calories: nutrition.calories || 0,
      protein: nutrition.protein || 0,
      carbs: nutrition.carbohydrate || 0,
      fat: nutrition.fat || 0,
      barcode: barcode,
      brand: product.brand || '',
      category: product.superDepartment || '',
      imageUrl: product.image || '',
      portions: [
        {
          name: 'serving',
          grams: 100,
          quantity: 1
        }
      ]
    };

    return {
      product: foodItem,
      success: true,
      source: BarcodeDatabase.TESCO
    };
  } catch (error) {
    console.error('Error querying Tesco API:', error);
    return {
      product: null,
      success: false,
      error: error instanceof Error ? error.message : 'Error querying Tesco API',
      source: BarcodeDatabase.TESCO
    };
  }
}

/**
 * Query Amazon database (placeholder implementation)
 */
async function queryAmazon(barcode: string): Promise<BarcodeRecognitionResult> {
  try {
    const apiKey = currentConfig.apiKeys[BarcodeDatabase.AMAZON];
    if (!apiKey) {
      throw new Error('Amazon API key not configured');
    }
    
    // Amazon Product API - Using the Product Advertising API or similar
    const url = `https://webservices.amazon.com/paapi5/searchitems`;
    
    // Build the request payload
    const payload = {
      'Keywords': barcode,
      'Resources': [
        'ItemInfo.Title',
        'ItemInfo.ByLineInfo',
        'ItemInfo.ProductInfo',
        'Images.Primary.Medium'
      ],
      'PartnerTag': process.env.EXPO_PUBLIC_AMAZON_ASSOCIATE_TAG || '',
      'PartnerType': 'Associates',
      'Marketplace': 'www.amazon.com'
    };
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'X-Amz-Target': 'com.amazon.paapi5.v1.ProductAdvertisingAPIv1.SearchItems',
        'Content-Type': 'application/json',
        'X-Amz-Date': new Date().toISOString().split('T')[0].replace(/-/g, ''),
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      throw new Error(`Amazon API returned ${response.status}: ${response.statusText}`);
    }

    const data = await response.json() as AmazonApiResponse;
    
    if (!data.SearchResult || !data.SearchResult.Items || data.SearchResult.Items.length === 0) {
      return {
        product: null,
        success: false,
        error: 'Product not found in Amazon database',
        source: BarcodeDatabase.AMAZON
      };
    }

    const item = data.SearchResult.Items[0];
    
    // Map to our FoodItem interface - note that Amazon doesn't typically provide nutrition info
    // so we'll only use it as a fallback for product identification
    const foodItem: FoodItem = {
      id: `amazon-${barcode}`,
      name: item.ItemInfo.Title.DisplayValue || 'Unknown Product',
      calories: 0, // Nutrition info typically not available from Amazon
      protein: 0,
      carbs: 0,
      fat: 0,
      barcode: barcode,
      brand: item.ItemInfo.ByLineInfo?.Brand?.DisplayValue || '',
      category: item.ItemInfo.Classifications?.ProductGroup?.DisplayValue || '',
      imageUrl: item.Images?.Primary?.Medium?.URL || '',
      portions: [
        {
          name: 'serving',
          grams: 100,
          quantity: 1
        }
      ]
    };

    // Since Amazon doesn't provide nutrition data, we'll mark this product as needing
    // nutrition data enhancement from another source
    return {
      product: foodItem,
      success: true,
      source: BarcodeDatabase.AMAZON,
      error: 'Nutrition data not available from Amazon'
    };
  } catch (error) {
    console.error('Error querying Amazon API:', error);
    return {
      product: null,
      success: false,
      error: error instanceof Error ? error.message : 'Error querying Amazon API',
      source: BarcodeDatabase.AMAZON
    };
  }
}

/**
 * Cache a product locally
 */
async function cacheProduct(barcode: string, product: FoodItem): Promise<void> {
  try {
    const cacheKey = `${BARCODE_CACHE_KEY}${barcode}`;
    const cacheData = {
      product,
      timestamp: Date.now(),
      expiry: Date.now() + currentConfig.cacheExpiration
    };
    
    await AsyncStorage.setItem(cacheKey, JSON.stringify(cacheData));

    // Also cache in Firebase if available
    try {
      const auth = getAuth();
      const userId = auth.currentUser?.uid;
      
      // Create a document reference for the barcode product
      const productRef = doc(db, 'barcode_products', barcode);
      
      await setDoc(productRef, {
        barcode,
        name: product.name,
        calories: product.calories,
        protein: product.protein,
        carbs: product.carbs,
        fat: product.fat,
        portions: product.portions,
        brand: product.brand,
        category: product.category,
        image_url: product.imageUrl,
        updated_at: serverTimestamp(),
        created_by: userId || 'anonymous'
      }, { merge: true });

      console.log('Successfully cached barcode in Firebase');
    } catch (firebaseError) {
      console.error('Error with Firebase barcode caching:', firebaseError);
    }
  } catch (error) {
    console.error('Error caching product:', error);
  }
}

/**
 * Get a cached product
 */
async function getCachedProduct(barcode: string): Promise<FoodItem | null> {
  try {
    const cacheKey = `${BARCODE_CACHE_KEY}${barcode}`;
    const cachedData = await AsyncStorage.getItem(cacheKey);
    
    if (!cachedData) {
      return null;
    }
    
    const { product, expiry } = JSON.parse(cachedData);
    
    // Check if cache has expired
    if (Date.now() > expiry && !currentConfig.offlineMode) {
      // Cache expired, but preserve it if in offline mode
      if (!currentConfig.offlineMode) {
        await AsyncStorage.removeItem(cacheKey);
        return null;
      }
    }
    
    return product;
  } catch (error) {
    console.error('Error getting cached product:', error);
    return null;
  }
}

/**
 * Clear the barcode cache
 */
export async function clearBarcodeCache(): Promise<void> {
  try {
    // Get all keys from AsyncStorage
    const keys = await AsyncStorage.getAllKeys();
    
    // Filter keys that start with the barcode cache prefix
    const cacheKeys = keys.filter(key => key.startsWith(BARCODE_CACHE_KEY));
    
    // Remove all cache keys
    if (cacheKeys.length > 0) {
      await AsyncStorage.multiRemove(cacheKeys);
    }
    
    console.log(`Cleared ${cacheKeys.length} barcode cache entries`);
  } catch (error) {
    console.error('Error clearing barcode cache:', error);
  }
}

/**
 * Get barcode cache statistics
 */
export async function getBarcodeStatistics(): Promise<{
  cachedBarcodes: number;
  databaseCounts: Record<BarcodeDatabase, number>;
  totalLookups: number;
  successRate: number;
}> {
  try {
    // Get all keys from AsyncStorage
    const keys = await AsyncStorage.getAllKeys();
    const cacheKeys = keys.filter(key => key.startsWith(BARCODE_CACHE_KEY));
    
    // Initialize database counts
    const databaseCounts: Record<BarcodeDatabase, number> = {
      [BarcodeDatabase.OPEN_FOOD_FACTS]: 0,
      [BarcodeDatabase.USDA]: 0,
      [BarcodeDatabase.TESCO]: 0,
      [BarcodeDatabase.AMAZON]: 0,
      [BarcodeDatabase.INTERNAL]: 0
    };

    // Count products by database source
    for (const key of cacheKeys) {
      const cachedData = await AsyncStorage.getItem(key);
      if (cachedData) {
        const { product } = JSON.parse(cachedData);
        const source = product.id.split('-')[0];
        
        if (source === 'off') {
          databaseCounts[BarcodeDatabase.OPEN_FOOD_FACTS]++;
        } else if (source === 'usda') {
          databaseCounts[BarcodeDatabase.USDA]++;
        } else if (source === 'tesco') {
          databaseCounts[BarcodeDatabase.TESCO]++;
        } else if (source === 'amazon') {
          databaseCounts[BarcodeDatabase.AMAZON]++;
        } else {
          databaseCounts[BarcodeDatabase.INTERNAL]++;
        }
      }
    }

    // Get statistics from Firebase if available
    let totalLookups = 0;
    let successfulLookups = 0;

    try {
      // Query Firebase for barcode lookup statistics
      const { getDocs, collection: firestoreCollection, query, limit } = await import('firebase/firestore');
      
      const lookupsQuery = query(
        firestoreCollection(db, 'barcode_lookups'),
        limit(1000)
      );
      
      const querySnapshot = await getDocs(lookupsQuery);
      
      if (!querySnapshot.empty) {
        totalLookups = querySnapshot.size;
        successfulLookups = 0;
        
        querySnapshot.forEach((doc) => {
          const data = doc.data();
          if (data.product_found) {
            successfulLookups++;
          }
        });
      }
    } catch (firebaseError) {
      console.error('Error getting barcode statistics from Firebase:', firebaseError);
    }

    return {
      cachedBarcodes: cacheKeys.length,
      databaseCounts,
      totalLookups,
      successRate: totalLookups > 0 ? (successfulLookups / totalLookups) * 100 : 0
    };
  } catch (error) {
    console.error('Error getting barcode statistics:', error);
    return {
      cachedBarcodes: 0,
      databaseCounts: {
        [BarcodeDatabase.OPEN_FOOD_FACTS]: 0,
        [BarcodeDatabase.USDA]: 0,
        [BarcodeDatabase.TESCO]: 0,
        [BarcodeDatabase.AMAZON]: 0,
        [BarcodeDatabase.INTERNAL]: 0
      },
      totalLookups: 0,
      successRate: 0
    };
  }
}

/**
 * Get supported regions with descriptions
 */
export function getSupportedRegions(): {
  id: ProductRegion;
  name: string;
  description: string;
  countries: string[];
}[] {
  return [
    {
      id: ProductRegion.NORTH_AMERICA,
      name: 'North America',
      description: 'United States, Canada, and Mexico',
      countries: ['United States', 'Canada', 'Mexico']
    },
    {
      id: ProductRegion.EUROPE,
      name: 'Europe',
      description: 'European Union countries and United Kingdom',
      countries: ['United Kingdom', 'France', 'Germany', 'Spain', 'Italy', 'and more']
    },
    {
      id: ProductRegion.ASIA,
      name: 'Asia',
      description: 'Asian countries including Japan, China, and Korea',
      countries: ['Japan', 'China', 'South Korea', 'India', 'Thailand', 'and more']
    },
    {
      id: ProductRegion.AUSTRALIA,
      name: 'Australia & Oceania',
      description: 'Australia and New Zealand',
      countries: ['Australia', 'New Zealand']
    },
    {
      id: ProductRegion.GLOBAL,
      name: 'Global',
      description: 'Worldwide database with no regional filter',
      countries: ['All available countries']
    }
  ];
} 