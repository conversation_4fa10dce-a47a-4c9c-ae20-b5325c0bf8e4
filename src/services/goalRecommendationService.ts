import { 
  getUserProfile, 
  NutritionGoalType, 
  ActivityLevel, 
  DietaryPreference, 
  UserProfile,
  getAdjustedNutritionGoals
} from './nutritionGoalService';
import { NutritionInfo } from '@/types/api-responses';
// import { generateMealPlan } from './mealPlannerService';
import { Recipe, getRecommendedRecipes } from './recipeRecommendationService';
import { format, subDays } from 'date-fns';
import { getMealsForDate } from './dietitianService';
import { generateNutritionRecommendations } from './openai/secureApiClient';

interface GoalRecommendation {
  title: string;
  description: string;
  actionItems: string[];
  recipes?: Recipe[];
  mealPlan?: any;
}

interface GoalProgress {
  period: 'day' | 'week' | 'month';
  metrics: {
    calories: { actual: number; target: number; percentage: number };
    protein: { actual: number; target: number; percentage: number };
    carbs: { actual: number; target: number; percentage: number };
    fat: { actual: number; target: number; percentage: number };
  };
  trends: {
    caloriesTrend: 'increasing' | 'decreasing' | 'stable';
    proteinTrend: 'increasing' | 'decreasing' | 'stable';
    adherenceRate: number; // 0-100%
  };
}

interface DynamicTargets {
  dailyCalorieTarget: number;
  dynamicProteinTarget: number;
  dynamicCarbsTarget: number;
  dynamicFatTarget: number;
  adjustmentReason?: string;
}

/**
 * Generate personalized goal-based recommendations
 */
export async function getGoalBasedRecommendations(): Promise<GoalRecommendation | null> {
  try {
    // Get user profile
    const userProfile = await getUserProfile();
    if (!userProfile) {
      console.error('No user profile found');
      return null;
    }

    // Get goal progress data
    const goalProgress = await calculateGoalProgress(userProfile);
    
    // Get dynamic targets
    const dynamicTargets = await calculateDynamicTargets(userProfile, goalProgress);
    
    // Generate GPT-based recommendations
    const recommendations = await generateGoalSpecificRecommendations(
      userProfile,
      goalProgress,
      dynamicTargets
    );
    
    // Get recommended recipes based on goals
    let recommendedRecipes: Recipe[] = [];
    try {
      const currentTime = new Date().getHours();
      let mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
      
      if (currentTime >= 5 && currentTime < 11) {
        mealType = 'breakfast';
      } else if (currentTime >= 11 && currentTime < 16) {
        mealType = 'lunch';
      } else if (currentTime >= 16 && currentTime < 22) {
        mealType = 'dinner';
      } else {
        mealType = 'snack';
      }
      
      const response = await getRecommendedRecipes(mealType, 3);
      recommendedRecipes = response.recipes || [];
    } catch (error) {
      console.error('Error fetching recommended recipes:', error);
    }
    
    // Create cohesive recommendations
    return {
      title: generateRecommendationTitle(userProfile.goalType),
      description: recommendations.description,
      actionItems: recommendations.actionItems,
      recipes: recommendedRecipes
    };
  } catch (error) {
    console.error('Error generating goal-based recommendations:', error);
    return null;
  }
}

/**
 * Calculate user's progress toward their goals
 */
async function calculateGoalProgress(userProfile: UserProfile): Promise<GoalProgress> {
  // Get today's date
  const today = format(new Date(), 'yyyy-MM-dd');
  const yesterday = format(subDays(new Date(), 1), 'yyyy-MM-dd');
  const twoDaysAgo = format(subDays(new Date(), 2), 'yyyy-MM-dd');
  
  // Get meals for the last 3 days
  const todayMeals = await getMealsForDate(today);
  const yesterdayMeals = await getMealsForDate(yesterday);
  const twoDaysAgoMeals = await getMealsForDate(twoDaysAgo);
  
  // Calculate daily nutrition totals
  const todayTotals = calculateTotalNutrition(todayMeals);
  const yesterdayTotals = calculateTotalNutrition(yesterdayMeals);
  const twoDaysAgoTotals = calculateTotalNutrition(twoDaysAgoMeals);
  
  // Calculate adherence rate (percentage of days where user was within 20% of calorie target)
  let daysWithinTarget = 0;
  if (Math.abs((todayTotals.calories / userProfile.calorieGoal) - 1) <= 0.2) daysWithinTarget++;
  if (Math.abs((yesterdayTotals.calories / userProfile.calorieGoal) - 1) <= 0.2) daysWithinTarget++;
  if (Math.abs((twoDaysAgoTotals.calories / userProfile.calorieGoal) - 1) <= 0.2) daysWithinTarget++;
  
  const adherenceRate = (daysWithinTarget / 3) * 100;
  
  // Determine calorie trend
  let caloriesTrend: 'increasing' | 'decreasing' | 'stable';
  if (todayTotals.calories > yesterdayTotals.calories && yesterdayTotals.calories > twoDaysAgoTotals.calories) {
    caloriesTrend = 'increasing';
  } else if (todayTotals.calories < yesterdayTotals.calories && yesterdayTotals.calories < twoDaysAgoTotals.calories) {
    caloriesTrend = 'decreasing';
  } else {
    caloriesTrend = 'stable';
  }
  
  // Determine protein trend
  let proteinTrend: 'increasing' | 'decreasing' | 'stable';
  if (todayTotals.protein > yesterdayTotals.protein && yesterdayTotals.protein > twoDaysAgoTotals.protein) {
    proteinTrend = 'increasing';
  } else if (todayTotals.protein < yesterdayTotals.protein && yesterdayTotals.protein < twoDaysAgoTotals.protein) {
    proteinTrend = 'decreasing';
  } else {
    proteinTrend = 'stable';
  }
  
  return {
    period: 'day',
    metrics: {
      calories: {
        actual: todayTotals.calories,
        target: userProfile.calorieGoal,
        percentage: (todayTotals.calories / userProfile.calorieGoal) * 100
      },
      protein: {
        actual: todayTotals.protein,
        target: userProfile.proteinGoal,
        percentage: (todayTotals.protein / userProfile.proteinGoal) * 100
      },
      carbs: {
        actual: todayTotals.carbs,
        target: userProfile.carbsGoal,
        percentage: (todayTotals.carbs / userProfile.carbsGoal) * 100
      },
      fat: {
        actual: todayTotals.fat,
        target: userProfile.fatGoal,
        percentage: (todayTotals.fat / userProfile.fatGoal) * 100
      }
    },
    trends: {
      caloriesTrend,
      proteinTrend,
      adherenceRate
    }
  };
}

/**
 * Calculate total nutrition from meals
 */
function calculateTotalNutrition(meals: any[]): NutritionInfo {
  return meals.reduce(
    (total, meal) => ({
      calories: total.calories + (meal.nutrition?.calories || 0),
      protein: total.protein + (meal.nutrition?.protein || 0),
      carbs: total.carbs + (meal.nutrition?.carbs || 0),
      fat: total.fat + (meal.nutrition?.fat || 0),
      fiber: (total.fiber || 0) + (meal.nutrition?.fiber || 0),
      sugar: (total.sugar || 0) + (meal.nutrition?.sugar || 0),
      sodium: (total.sodium || 0) + (meal.nutrition?.sodium || 0)
    }),
    { calories: 0, protein: 0, carbs: 0, fat: 0, fiber: 0, sugar: 0, sodium: 0 }
  );
}

/**
 * Calculate dynamic daily targets based on user activity and progress
 */
async function calculateDynamicTargets(
  userProfile: UserProfile,
  goalProgress: GoalProgress
): Promise<DynamicTargets> {
  // Get adjusted nutrition goals based on activity
  const adjustedGoals = await getAdjustedNutritionGoals();
  
  if (adjustedGoals) {
    return {
      dailyCalorieTarget: adjustedGoals.calorieGoal,
      dynamicProteinTarget: adjustedGoals.proteinGoal,
      dynamicCarbsTarget: adjustedGoals.carbsGoal,
      dynamicFatTarget: adjustedGoals.fatGoal,
      adjustmentReason: adjustedGoals.adjustmentReason
    };
  }
  
  // If no adjusted goals available, apply goal-specific logic
  
  // Base targets from user profile
  let dailyCalorieTarget = userProfile.calorieGoal;
  let dynamicProteinTarget = userProfile.proteinGoal;
  let dynamicCarbsTarget = userProfile.carbsGoal;
  let dynamicFatTarget = userProfile.fatGoal;
  let adjustmentReason: string | undefined;
  
  // Apply goal-specific adjustments
  switch (userProfile.goalType) {
    case NutritionGoalType.WEIGHT_LOSS:
      // For weight loss, if adherence is low, slightly increase calories to increase adherence
      if (goalProgress.trends.adherenceRate < 60) {
        dailyCalorieTarget = Math.round(userProfile.calorieGoal * 1.05);
        adjustmentReason = "Slightly increased calorie target to improve dietary adherence";
      }
      break;
      
    case NutritionGoalType.MUSCLE_GAIN:
      // For muscle gain, if protein intake trend is decreasing, increase protein target
      if (goalProgress.trends.proteinTrend === 'decreasing') {
        dynamicProteinTarget = Math.round(userProfile.proteinGoal * 1.1);
        adjustmentReason = "Increased protein target to support muscle building";
      }
      break;
      
    case NutritionGoalType.ATHLETIC_PERFORMANCE:
      // For athletic performance, adjust carbs based on activity patterns
      // This is a simplified example - in a real app, would use activity data
      const today = new Date();
      const dayOfWeek = today.getDay();
      
      // Assume weekends are higher activity days
      if (dayOfWeek === 0 || dayOfWeek === 6) {
        dynamicCarbsTarget = Math.round(userProfile.carbsGoal * 1.15);
        adjustmentReason = "Increased carb target for weekend activity";
      }
      break;
  }
  
  return {
    dailyCalorieTarget,
    dynamicProteinTarget,
    dynamicCarbsTarget,
    dynamicFatTarget,
    adjustmentReason
  };
}

/**
 * Generate personalized recommendations based on goal type
 */
async function generateGoalSpecificRecommendations(
  userProfile: UserProfile,
  goalProgress: GoalProgress,
  dynamicTargets: DynamicTargets
): Promise<{ description: string; actionItems: string[] }> {
  // Try to generate AI-powered recommendations using Firebase Functions
  try {
    const aiRecommendations = await generateAIRecommendations(userProfile, goalProgress, dynamicTargets);
    if (aiRecommendations) {
      return aiRecommendations;
    }
  } catch (error) {
    console.error('Error generating AI recommendations:', error);
    // Fall back to rule-based recommendations if AI fails
  }
  
  // Rule-based recommendations as fallback
  return generateRuleBasedRecommendations(userProfile, goalProgress, dynamicTargets);
}

/**
 * Generate recommendations using AI
 */
async function generateAIRecommendations(
  userProfile: UserProfile,
  goalProgress: GoalProgress,
  dynamicTargets: DynamicTargets
): Promise<{ description: string; actionItems: string[] } | null> {
  try {
    // Use Firebase Functions for AI recommendations
    console.log('Using Firebase Functions for AI recommendations');
    
    // Generate goal-specific system prompt
    const systemPrompt = getGoalSpecificSystemPrompt(userProfile.goalType);
    
    // Generate user prompt with profile and progress data
    const userPrompt = `Generate personalized nutrition and fitness recommendations based on the following data:

User Profile:
- Goal Type: ${userProfile.goalType.replace('_', ' ')}
- Activity Level: ${userProfile.activityLevel.replace('_', ' ')}
- Dietary Preference: ${userProfile.dietaryPreference.replace('_', ' ')}
${userProfile.age ? `- Age: ${userProfile.age}` : ''}
${userProfile.gender ? `- Gender: ${userProfile.gender}` : ''}
${userProfile.weight ? `- Weight: ${userProfile.weight} kg` : ''}
${userProfile.height ? `- Height: ${userProfile.height} cm` : ''}

Current Progress:
- Calories: ${goalProgress.metrics.calories.actual}/${goalProgress.metrics.calories.target} (${Math.round(goalProgress.metrics.calories.percentage)}%)
- Protein: ${goalProgress.metrics.protein.actual}g/${goalProgress.metrics.protein.target}g (${Math.round(goalProgress.metrics.protein.percentage)}%)
- Carbs: ${goalProgress.metrics.carbs.actual}g/${goalProgress.metrics.carbs.target}g (${Math.round(goalProgress.metrics.carbs.percentage)}%)
- Fat: ${goalProgress.metrics.fat.actual}g/${goalProgress.metrics.fat.target}g (${Math.round(goalProgress.metrics.fat.percentage)}%)

Trends:
- Calorie intake trend: ${goalProgress.trends.caloriesTrend}
- Protein intake trend: ${goalProgress.trends.proteinTrend}
- Adherence rate: ${Math.round(goalProgress.trends.adherenceRate)}%

Recommended Targets for Today:
- Calories: ${dynamicTargets.dailyCalorieTarget}
- Protein: ${dynamicTargets.dynamicProteinTarget}g
- Carbs: ${dynamicTargets.dynamicCarbsTarget}g
- Fat: ${dynamicTargets.dynamicFatTarget}g
${dynamicTargets.adjustmentReason ? `- Adjustment reason: ${dynamicTargets.adjustmentReason}` : ''}

Format your response as a JSON object with:
1. A "description" field with a personalized recommendation message
2. An "actionItems" array with 3-5 specific, actionable recommendations

Example format:
{
  "description": "Your personalized recommendation message here",
  "actionItems": [
    "First specific actionable recommendation",
    "Second specific actionable recommendation",
    "Third specific actionable recommendation"
  ]
}
`;

    // Combined prompt for Firebase function
    const combinedPrompt = `${systemPrompt}

${userPrompt}`;

    // Call Firebase Function
    const response = await generateNutritionRecommendations(
      {
        calories: goalProgress.metrics.calories.actual,
        protein: goalProgress.metrics.protein.actual,
        carbs: goalProgress.metrics.carbs.actual,
        fat: goalProgress.metrics.fat.actual
      },
      dynamicTargets.dailyCalorieTarget,
      dynamicTargets.dynamicProteinTarget,
      dynamicTargets.dynamicCarbsTarget,
      dynamicTargets.dynamicFatTarget,
      userProfile.goalType
    );
    
    if (!response.success) {
      console.error('Firebase Functions recommendation generation failed');
      return null;
    }
    
    if (response.recommendations) {
      try {
        // The recommendations might be a string that needs parsing
        let recommendations;
        if (typeof response.recommendations === 'string') {
          recommendations = JSON.parse(response.recommendations);
        } else {
          recommendations = response.recommendations;
        }
        
        return {
          description: recommendations.description || '',
          actionItems: Array.isArray(recommendations.actionItems) ? recommendations.actionItems : []
        };
      } catch (error) {
        console.error('Error parsing Firebase Functions response:', error);
      }
    }
    
    return null;
  } catch (error) {
    console.error('Error generating AI recommendations:', error);
    return null;
  }
}

/**
 * Get system prompt customized for specific goal types
 */
function getGoalSpecificSystemPrompt(goalType: NutritionGoalType): string {
  const basePrompt = `You are a professional nutritionist and fitness expert providing personalized recommendations based on user goals, nutrition data, and progress trends. Your recommendations should be:
- Supportive, encouraging, and non-judgmental
- Evidence-based and aligned with nutritional science
- Specific, concrete, and actionable
- Focused on sustainable habits rather than quick fixes
- Tailored to the specific goal type and dietary preferences`;
  
  // Add goal-specific guidance
  switch (goalType) {
    case NutritionGoalType.WEIGHT_LOSS:
      return `${basePrompt}

For weight loss goals:
- Focus on sustainable caloric deficit while maintaining sufficient protein intake
- Emphasize nutrient-dense, satiating foods that help manage hunger
- Recommend strategies for managing cravings and emotional eating
- Suggest meal timing approaches that support adherence
- Include recommendations for both nutrition and physical activity`;
    
    case NutritionGoalType.MUSCLE_GAIN:
      return `${basePrompt}

For muscle gain goals:
- Emphasize sufficient protein intake and timing (especially around workouts)
- Focus on caloric surplus while maintaining good nutritional quality
- Recommend nutrient timing strategies (pre/post-workout nutrition)
- Suggest food choices that support recovery and muscle protein synthesis
- Include both nutrition and resistance training recommendations`;
    
    case NutritionGoalType.ATHLETIC_PERFORMANCE:
      return `${basePrompt}

For athletic performance goals:
- Focus on fueling strategies for training and competition
- Emphasize carbohydrate timing and amounts based on training intensity
- Recommend hydration and electrolyte strategies
- Suggest recovery nutrition approaches
- Include periodized nutrition approaches that match training cycles`;
    
    default:
      return basePrompt;
  }
}

/**
 * Generate rule-based recommendations as fallback
 */
function generateRuleBasedRecommendations(
  userProfile: UserProfile,
  goalProgress: GoalProgress,
  dynamicTargets: DynamicTargets
): { description: string; actionItems: string[] } {
  // Base description for each goal type
  let description = '';
  const actionItems: string[] = [];
  
  // Generate goal-specific description
  switch (userProfile.goalType) {
    case NutritionGoalType.WEIGHT_LOSS:
      description = "Your personalized weight loss plan focuses on creating a sustainable calorie deficit while maintaining adequate nutrition.";
      break;
    case NutritionGoalType.WEIGHT_GAIN:
      description = "Your weight gain plan focuses on a calorie surplus with quality nutrition to support healthy weight gain.";
      break;
    case NutritionGoalType.MUSCLE_GAIN:
      description = "Your muscle building plan emphasizes protein intake and resistance training to maximize muscle growth.";
      break;
    case NutritionGoalType.ATHLETIC_PERFORMANCE:
      description = "Your athletic performance plan focuses on properly fueling your workouts and supporting recovery.";
      break;
    case NutritionGoalType.MAINTENANCE:
      description = "Your maintenance plan helps you sustain your current weight while optimizing nutrition.";
      break;
    default:
      description = "Your personalized nutrition plan is designed to help you achieve your health goals.";
  }
  
  // Add recommendations based on goal progress
  // Calorie-related recommendations
  if (goalProgress.metrics.calories.percentage < 70) {
    actionItems.push(`Try to increase your calorie intake - you're currently at ${Math.round(goalProgress.metrics.calories.percentage)}% of your daily target.`);
  } else if (goalProgress.metrics.calories.percentage > 120 && userProfile.goalType === NutritionGoalType.WEIGHT_LOSS) {
    actionItems.push(`Consider reducing your portion sizes slightly - you've exceeded your calorie target by ${Math.round(goalProgress.metrics.calories.percentage - 100)}%.`);
  }
  
  // Protein-related recommendations
  if (goalProgress.metrics.protein.percentage < 80) {
    actionItems.push(`Increase your protein intake to reach your daily target of ${userProfile.proteinGoal}g. Consider adding protein-rich foods like eggs, yogurt, or lean meats to your meals.`);
  } else if (goalProgress.metrics.protein.percentage > 90) {
    actionItems.push("Great job meeting your protein goals! Keep up the good work.");
  }
  
  // Adherence-related recommendations
  if (goalProgress.trends.adherenceRate < 70) {
    actionItems.push("Try meal planning and preparation to help stay on track with your nutrition goals.");
  }
  
  // Dietary preference recommendations
  switch (userProfile.dietaryPreference) {
    case DietaryPreference.VEGETARIAN:
      actionItems.push("Include a variety of plant proteins like beans, lentils, tofu, and tempeh to ensure you get all essential amino acids.");
      break;
    case DietaryPreference.VEGAN:
      actionItems.push("Consider tracking your vitamin B12 and iron intake, and include fortified foods in your diet.");
      break;
    case DietaryPreference.KETO:
      actionItems.push("Stay hydrated and ensure you're getting enough electrolytes like sodium, potassium, and magnesium.");
      break;
  }
  
  // Add dynamic target information
  if (dynamicTargets.adjustmentReason) {
    actionItems.push(`Today's nutrition targets have been adjusted: ${dynamicTargets.adjustmentReason}.`);
  }
  
  // Make sure we have at least 3 recommendations
  if (actionItems.length < 3) {
    actionItems.push("Stay hydrated by drinking water throughout the day.");
    actionItems.push("Try to include a variety of colorful fruits and vegetables in your diet for essential micronutrients.");
  }
  
  return { description, actionItems };
}

/**
 * Generate recommendation title based on goal type
 */
function generateRecommendationTitle(goalType: NutritionGoalType): string {
  switch (goalType) {
    case NutritionGoalType.WEIGHT_LOSS:
      return "Your Weight Loss Journey";
    case NutritionGoalType.WEIGHT_GAIN:
      return "Your Healthy Weight Gain Plan";
    case NutritionGoalType.MUSCLE_GAIN:
      return "Building Strength & Muscle";
    case NutritionGoalType.ATHLETIC_PERFORMANCE:
      return "Optimizing Athletic Performance";
    case NutritionGoalType.MAINTENANCE:
      return "Maintaining Your Progress";
    default:
      return "Your Personalized Nutrition Plan";
  }
}

/**
 * Get dynamic daily targets based on goals and activity
 */
export async function getDynamicDailyTargets(): Promise<DynamicTargets | null> {
  try {
    const userProfile = await getUserProfile();
    if (!userProfile) return null;
    
    const goalProgress = await calculateGoalProgress(userProfile);
    return calculateDynamicTargets(userProfile, goalProgress);
  } catch (error) {
    console.error('Error getting dynamic daily targets:', error);
    return null;
  }
} 