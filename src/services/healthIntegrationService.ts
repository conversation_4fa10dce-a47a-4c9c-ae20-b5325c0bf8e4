import { Platform } from 'react-native';
import Constants from 'expo-constants';
import { z } from 'zod';
import { getAuth } from 'firebase/auth';
import { getFirestore, collection, addDoc, writeBatch, doc } from 'firebase/firestore';


// Firebase instances
const auth = getAuth();
const firestore = getFirestore();

// Type definitions 
interface HealthValue {
  value: number;
  startDate: string;
  sourceType: string;
  sourceName: string;
}

interface HealthKitPermissions {
  permissions: {
    read: string[];
    write: string[];
  };
}

interface HealthInputOptions {
  value: number;
  unit: string;
  date: string;
}

// Add this interface near the top with other interfaces
interface GoogleFitStepData {
  source: string;
  steps: {
    date: string | number;
    value: number;
  }[];
}

// Mock implementations for when libraries aren't available
const MockAppleHealthKit = {
  Constants: {
    Permissions: {
      StepCount: 'StepCount',
      Weight: 'Weight',
      Height: 'Height',
      HeartRate: 'HeartRate',
      ActiveEnergyBurned: 'ActiveEnergyBurned',
      SleepAnalysis: 'SleepAnalysis',
      WaterIntake: 'WaterIntake',
      BloodPressureDiastolic: 'BloodPressureDiastolic',
      BloodPressureSystolic: 'BloodPressureSystolic',
      BloodGlucose: 'BloodGlucose',
    },
  },
  initHealthKit: (_: any, callback: (error: string) => void) => {
    callback('HealthKit not available');
  },
  getStepCount: (_: any, callback: (error: string, results: any) => void) => {
    callback('HealthKit not available', { value: 0 });
  },
  getWeightSamples: (_: any, callback: (error: string, results: any) => void) => {
    callback('HealthKit not available', []);
  },
  saveWeight: (_: any, callback: (error: string) => void) => {
    callback('HealthKit not available');
  },
  saveWater: (_: any, callback: (error: string) => void) => {
    callback('HealthKit not available');
  },
  setObserver: (options?: any) => {},
  observerQuery: (_: any, __: any) => {},
  stopObserverQuery: (options?: any) => {},
};

const MockGoogleFit = {
  Scopes: {
    FITNESS_ACTIVITY_READ: 'FITNESS_ACTIVITY_READ',
    FITNESS_BODY_READ: 'FITNESS_BODY_READ',
    FITNESS_BLOOD_PRESSURE_READ: 'FITNESS_BLOOD_PRESSURE_READ',
    FITNESS_BLOOD_GLUCOSE_READ: 'FITNESS_BLOOD_GLUCOSE_READ',
    FITNESS_HEART_RATE_READ: 'FITNESS_HEART_RATE_READ',
    FITNESS_SLEEP_READ: 'FITNESS_SLEEP_READ',
    FITNESS_NUTRITION_READ: 'FITNESS_NUTRITION_READ',
    FITNESS_BODY_TEMPERATURE_READ: 'FITNESS_BODY_TEMPERATURE_READ',
  },
  authorize: async (options?: any) => false,
  isAuthorized: async () => false,
  getDailyStepCountSamples: async (options?: any) => [],
  getWeightSamples: async (options?: any) => [],
  saveWeight: async (options?: any) => {},
};

// Try to import real implementations, fall back to mocks
let AppleHealthKit = MockAppleHealthKit;
let GoogleFit = MockGoogleFit;

// Use try-catch to handle import failures
try {
  const healthKit = require('react-native-health');
  if (healthKit) {
    AppleHealthKit = healthKit.default || healthKit;
  }
} catch (error) {
  console.warn('react-native-health not available, using mock implementation');
}

try {
  const fitness = require('react-native-fitness');
  if (fitness && fitness.GoogleFit) {
    GoogleFit = fitness.GoogleFit;
  }
} catch (error) {
  console.warn('react-native-fitness not available, using mock implementation');
}

// Define schema for health metric validation
const HealthMetricSchema = z.object({
  date: z.string(),
  value: z.number(),
  sourceType: z.string().optional(),
  sourceName: z.string().optional(),
  unit: z.string().optional(),
});

type HealthMetric = z.infer<typeof HealthMetricSchema>;

// Define available health data types
export enum HealthDataType {
  STEPS = 'steps',
  WEIGHT = 'weight',
  HEIGHT = 'height',
  HEART_RATE = 'heartRate',
  ACTIVE_ENERGY = 'activeEnergy',
  SLEEP = 'sleep',
  WATER = 'water',
  BLOOD_PRESSURE = 'bloodPressure',
  BLOOD_GLUCOSE = 'bloodGlucose',
}

// Define wearable device capability check
export const isWearableSupported = (): boolean => {
  return !!(Constants.manifest as any)?.extra?.supportsWearable;
};

// Initialize health integration based on platform
export const initHealthIntegration = async (): Promise<boolean> => {
  try {
    if (Platform.OS === 'ios') {
      const permissions = {
        permissions: {
          read: [
            AppleHealthKit.Constants.Permissions.StepCount,
            AppleHealthKit.Constants.Permissions.Weight,
            AppleHealthKit.Constants.Permissions.Height,
            AppleHealthKit.Constants.Permissions.HeartRate,
            AppleHealthKit.Constants.Permissions.ActiveEnergyBurned,
            AppleHealthKit.Constants.Permissions.SleepAnalysis,
            AppleHealthKit.Constants.Permissions.WaterIntake,
            AppleHealthKit.Constants.Permissions.BloodPressureDiastolic,
            AppleHealthKit.Constants.Permissions.BloodPressureSystolic,
            AppleHealthKit.Constants.Permissions.BloodGlucose,
          ],
          write: [
            AppleHealthKit.Constants.Permissions.Weight,
            AppleHealthKit.Constants.Permissions.Height,
            AppleHealthKit.Constants.Permissions.WaterIntake,
          ],
        },
      } as HealthKitPermissions;

      return new Promise((resolve) => {
        AppleHealthKit.initHealthKit(permissions, (error: string) => {
          if (error) {
            console.error('Error initializing Apple HealthKit:', error);
            resolve(false);
          } else {
            console.log('Apple HealthKit initialized successfully');
            resolve(true);
          }
        });
      });
    } else if (Platform.OS === 'android') {
      const options = {
        scopes: [
          GoogleFit.Scopes.FITNESS_ACTIVITY_READ,
          GoogleFit.Scopes.FITNESS_BODY_READ,
          GoogleFit.Scopes.FITNESS_BLOOD_PRESSURE_READ,
          GoogleFit.Scopes.FITNESS_BLOOD_GLUCOSE_READ,
          GoogleFit.Scopes.FITNESS_HEART_RATE_READ,
          GoogleFit.Scopes.FITNESS_SLEEP_READ,
          GoogleFit.Scopes.FITNESS_NUTRITION_READ,
          GoogleFit.Scopes.FITNESS_BODY_TEMPERATURE_READ,
        ],
      };

      try {
        await GoogleFit.authorize(options);
        const authStatus = await GoogleFit.isAuthorized();
        console.log('Google Fit authorization status:', authStatus);
        return authStatus;
      } catch (error) {
        console.error('Error initializing Google Fit:', error);
        return false;
      }
    }
    return false;
  } catch (error) {
    console.error('Error in health integration initialization:', error);
    return false;
  }
};

// Get health metrics based on data type and date range
export const getHealthData = async (
  dataType: HealthDataType,
  startDate: string,
  endDate: string
): Promise<{ success: boolean; data?: HealthMetric[]; error?: string }> => {
  try {
    if (!isWearableSupported()) {
      return { success: false, error: 'Wearable integration not supported on this device' };
    }

    if (Platform.OS === 'ios') {
      return await getAppleHealthData(dataType, startDate, endDate);
    } else if (Platform.OS === 'android') {
      return await getGoogleFitData(dataType, startDate, endDate);
    }

    return { success: false, error: 'Platform not supported' };
  } catch (error) {
    console.error(`Error getting ${dataType} data:`, error);
    return { success: false, error: `Failed to get ${dataType} data: ${error}` };
  }
};

// Write health data to the wearable platform
export const writeHealthData = async (
  dataType: HealthDataType,
  value: number,
  date: string,
  unit?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    if (!isWearableSupported()) {
      return { success: false, error: 'Wearable integration not supported on this device' };
    }

    if (Platform.OS === 'ios') {
      return await writeAppleHealthData(dataType, value, date, unit);
    } else if (Platform.OS === 'android') {
      return await writeGoogleFitData(dataType, value, date, unit);
    }

    return { success: false, error: 'Platform not supported' };
  } catch (error) {
    console.error(`Error writing ${dataType} data:`, error);
    return { success: false, error: `Failed to write ${dataType} data: ${error}` };
  }
};

// Sync health data with the server
export const syncHealthData = async (
  dataType: HealthDataType,
  startDate: string,
  endDate: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const result = await getHealthData(dataType, startDate, endDate);
    
    if (!result.success || !result.data) {
      return { success: false, error: result.error || 'No data available' };
    }

    // Get current user from Firebase Auth
    const currentUser = auth.currentUser;
    if (!currentUser) {
      return { success: false, error: 'User not authenticated' };
    }

    // Upload health data to Firestore
    const batch = writeBatch(firestore);
    
    result.data.forEach(item => {
      const metricDoc = doc(collection(firestore, 'health_metrics'));
      batch.set(metricDoc, {
        user_id: currentUser.uid,
        metric: dataType,
        date: item.date,
        value: item.value,
        source_type: item.sourceType || 'wearable',
        source_name: item.sourceName || Platform.OS,
        unit: item.unit || '',
        created_at: new Date().toISOString()
      });
    });
    
    await batch.commit();

    return { success: true };
  } catch (error) {
    console.error('Error in health data sync:', error);
    return { success: false, error: `Failed to sync health data: ${error}` };
  }
};

// Subscribe to realtime health updates from wearable
export const subscribeToHealthUpdates = (
  dataType: HealthDataType,
  callback: (data: HealthMetric) => void
): { unsubscribe: () => void } => {
  if (Platform.OS === 'ios') {
    // Set up Apple HealthKit observers
    // Implementation varies based on data type
    let unsubscribe = () => {};
    
    switch (dataType) {
      case HealthDataType.HEART_RATE:
        AppleHealthKit.setObserver({ type: 'HeartRate' });
        AppleHealthKit.observerQuery(
          { type: 'HeartRate' },
          (error: any, results: any) => {
            if (error) {
              console.error('HealthKit observer error:', error);
              return;
            }
            const data = {
              date: new Date().toISOString(),
              value: results.value,
              sourceType: 'watch',
              sourceName: 'Apple Watch',
              unit: 'bpm',
            };
            callback(data);
          }
        );
        unsubscribe = () => AppleHealthKit.stopObserverQuery({ type: 'HeartRate' });
        break;
      // Add other health data types as needed
    }
    
    return { unsubscribe };
  } else if (Platform.OS === 'android') {
    // Google Fit doesn't have direct observer pattern
    // We can use polling or other mechanisms
    const interval = setInterval(async () => {
      const now = new Date();
      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60000);
      
      const result = await getHealthData(
        dataType,
        fiveMinutesAgo.toISOString(),
        now.toISOString()
      );
      
      if (result.success && result.data && result.data.length > 0) {
        callback(result.data[result.data.length - 1]);
      }
    }, 300000); // Poll every 5 minutes
    
    return {
      unsubscribe: () => clearInterval(interval),
    };
  }
  
  return { unsubscribe: () => {} };
};

// ---- Private helper functions ----

// Get data from Apple HealthKit
const getAppleHealthData = async (
  dataType: HealthDataType,
  startDate: string,
  endDate: string
): Promise<{ success: boolean; data?: HealthMetric[]; error?: string }> => {
  const options = {
    startDate,
    endDate,
  };

  return new Promise((resolve) => {
    switch (dataType) {
      case HealthDataType.STEPS:
        AppleHealthKit.getStepCount(
          options,
          (error: string, results: HealthValue) => {
            if (error) {
              resolve({ success: false, error });
              return;
            }
            resolve({
              success: true,
              data: [
                {
                  date: new Date().toISOString(),
                  value: results.value,
                  unit: 'count',
                },
              ],
            });
          }
        );
        break;
      
      case HealthDataType.WEIGHT:
        AppleHealthKit.getWeightSamples(
          options,
          (error: string, results: HealthValue[]) => {
            if (error) {
              resolve({ success: false, error });
              return;
            }
            resolve({
              success: true,
              data: results.map(item => ({
                date: item.startDate,
                value: item.value,
                sourceType: item.sourceType,
                sourceName: item.sourceName,
                unit: 'kg',
              })),
            });
          }
        );
        break;
      
      // Add other health data types
      default:
        resolve({ success: false, error: 'Health data type not supported' });
    }
  });
};

// Get data from Google Fit
const getGoogleFitData = async (
  dataType: HealthDataType,
  startDate: string,
  endDate: string
): Promise<{ success: boolean; data?: HealthMetric[]; error?: string }> => {
  try {
    const options = {
      startDate: new Date(startDate).toISOString(),
      endDate: new Date(endDate).toISOString(),
    };

    switch (dataType) {
      case HealthDataType.STEPS:
        const stepsResult = await GoogleFit.getDailyStepCountSamples(options);
        if (stepsResult.length > 0) {
          // Extract data from the results
          const stepData = stepsResult.find((data: any) => data.source === 'com.google.android.gms:estimated_steps') as GoogleFitStepData | undefined;
          if (stepData && stepData.steps && stepData.steps.length > 0) {
            return {
              success: true,
              data: stepData.steps.map((item: any) => ({
                date: new Date(item.date).toISOString(),
                value: item.value,
                sourceType: 'GoogleFit',
                sourceName: stepData.source,
                unit: 'count',
              })),
            };
          }
        }
        return { success: false, error: 'No step data available' };
      
      case HealthDataType.WEIGHT:
        const weightResult = await GoogleFit.getWeightSamples(options);
        if (weightResult.length > 0) {
          return {
            success: true,
            data: weightResult.map((item: any) => ({
              date: new Date(item.startDate).toISOString(),
              value: item.value,
              sourceType: 'GoogleFit',
              sourceName: item.sourceName || 'Unknown',
              unit: 'kg',
            })),
          };
        }
        return { success: false, error: 'No weight data available' };
      
      // Add other health data types
      default:
        return { success: false, error: 'Health data type not supported' };
    }
  } catch (error) {
    return { success: false, error: `Failed to get Google Fit data: ${error}` };
  }
};

// Write data to Apple HealthKit
const writeAppleHealthData = async (
  dataType: HealthDataType,
  value: number,
  date: string,
  unit?: string
): Promise<{ success: boolean; error?: string }> => {
  return new Promise((resolve) => {
    switch (dataType) {
      case HealthDataType.WEIGHT:
        const weightOptions: HealthInputOptions = {
          value,
          unit: unit || 'kg',
          date: date,
        };
        
        AppleHealthKit.saveWeight(
          weightOptions,
          (error: string) => {
            if (error) {
              resolve({ success: false, error });
              return;
            }
            resolve({ success: true });
          }
        );
        break;
      
      case HealthDataType.WATER:
        const waterOptions: HealthInputOptions = {
          value,
          unit: unit || 'ml',
          date: date,
        };
        
        AppleHealthKit.saveWater(
          waterOptions,
          (error: string) => {
            if (error) {
              resolve({ success: false, error });
              return;
            }
            resolve({ success: true });
          }
        );
        break;
      
      // Add other health data types
      default:
        resolve({ success: false, error: 'Health data type not supported for writing' });
    }
  });
};

// Write data to Google Fit
const writeGoogleFitData = async (
  dataType: HealthDataType,
  value: number,
  date: string,
  unit?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const options = {
      value,
      date: new Date(date).getTime(),
      unit: unit,
    };

    switch (dataType) {
      case HealthDataType.WEIGHT:
        await GoogleFit.saveWeight(options);
        return { success: true };
      
      // Add other health data types
      default:
        return { success: false, error: 'Health data type not supported for writing' };
    }
  } catch (error) {
    return { success: false, error: `Failed to write Google Fit data: ${error}` };
  }
}; 