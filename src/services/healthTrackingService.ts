import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as secureStorage from './secureStorage';
import { healthDataConnector } from '../utils/healthDataConnector';
// Use relative path instead of alias to avoid TypeScript path resolution issues
import { firebaseService } from './firebaseService';
import { doc, getDoc, setDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';

// Real database service for water intake
const databaseService = {
  async addWaterIntake(entry: WaterIntakeEntry): Promise<void> {
    const user = firebaseService.getCurrentUser();
    if (!user) return;
    
    try {
      await firebaseService.addDocument('water_intake', {
        user_id: user.uid,
        amount_ml: entry.amount,
        date: entry.date
      });
    } catch (error) {
      console.error('Error saving water intake to database:', error);
    }
  },
  
  async getWaterIntake(startDate: string, endDate: string): Promise<WaterIntakeEntry[]> {
    const user = firebaseService.getCurrentUser();
    if (!user) return [];
    
    try {
      const data = await firebaseService.queryDocuments('water_intake', [
        { field: 'user_id', operator: '==', value: user.uid },
        { field: 'date', operator: '>=', value: startDate },
        { field: 'date', operator: '<=', value: endDate }
      ]);
      
      return data.map(item => ({
        amount: item.amount_ml ?? 0,
        unit: 'ml',
        date: item.date ?? ''
      }));
    } catch (error) {
      console.error('Error getting water intake from database:', error);
      return [];
    }
  }
};

// Interface for water intake
interface WaterIntakeEntry {
  amount: number;
  unit: string;
  date: string;
}

// Only import these modules on native platforms
let AppleHealthKit: any = null;
let GoogleFit: any = null;
let HealthConnect: any = null;

// Dynamically import platform-specific health modules
if (Platform.OS === 'ios') {
  try {
    AppleHealthKit = require('react-native-health').default;
  } catch (error) {
    // Silently continue if module is not available
  }
} else if (Platform.OS === 'android') {
  try {
    GoogleFit = require('react-native-google-fit').default;
    HealthConnect = require('react-native-health-connect');
  } catch (error) {
    // Silently continue if modules are not available
  }
}

// Types for health tracking
export interface HealthData {
  steps: number;
  activeCalories: number;
  restingCalories: number;
  totalCalories: number;
  distance: number;
  sleepHours: number;
  heartRate: number;
  bloodPressure?: {
    systolic: number;
    diastolic: number;
  };
  weight?: number;
  height?: number;
  bodyFat?: number;
  waterIntake: number;
  date: string;
}

export interface HealthGoals {
  dailySteps: number;
  dailyActiveCalories: number;
  dailyWaterIntake: number;
  weeklyActiveMinutes: number;
  sleepHoursPerNight: number;
}

export interface HealthPermissions {
  steps: boolean;
  distance: boolean;
  calories: boolean;
  heartRate: boolean;
  sleep: boolean;
  weight: boolean;
  bloodPressure: boolean;
  water: boolean;
}

export interface WearableDevice {
  id: string;
  name: string;
  type: string;
  connected: boolean;
  lastSync?: string;
}

export interface RecipeInstruction {
  id: string;
  recipe_id: string;
  step_number: number;
  instruction: string;
}

// Health tracking service
class HealthTrackingService {
  private isInitialized = false;
  private permissionsGranted: HealthPermissions = {
    steps: false,
    distance: false,
    calories: false,
    heartRate: false,
    sleep: false,
    weight: false,
    bloodPressure: false,
    water: false,
  };

  private connectedDevices: WearableDevice[] = [];
  
  // Initialize health services based on platform
  async initialize(): Promise<boolean> {
    try {
      if (this.isInitialized) {
        return true;
      }
      
      // Use our healthDataConnector for initializing and requesting permissions
      const hasPermission = await healthDataConnector.requestPermissions();
      this.isInitialized = hasPermission;
      
      if (hasPermission) {
        await this.updatePermissionsStatus();
      }
      
      return hasPermission;
    } catch (error) {
      console.error('Error initializing health tracking:', error);
      return false;
    }
  }
  
  // Update permissions status
  private async updatePermissionsStatus(): Promise<void> {
    const hasPermission = await healthDataConnector.checkPermissions();
    
    if (hasPermission) {
      // If permissions are granted, we'll consider all permissions enabled
      // for simplicity. In a real app, you might want to check specific permissions.
      this.permissionsGranted = {
        steps: true,
        distance: true,
        calories: true,
        heartRate: true,
        sleep: true,
        weight: true,
        bloodPressure: true,
        water: true,
      };
    }
  }
  
  // Get daily step count
  async getStepCount(date: Date): Promise<number> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    try {
      // Get health data from the connector
      const healthData = await healthDataConnector.fetchHealthData();
      
      // If the date is today, return the steps from the health data
      const today = new Date().toISOString().split('T')[0];
      const requestDate = date.toISOString().split('T')[0];
      
      if (requestDate === today) {
        return healthData.steps;
      }
      
      // For past dates, check Firebase
      const user = firebaseService.getCurrentUser();
      if (user) {
        const data = await firebaseService.queryDocuments('health_daily_summary', [
          { field: 'user_id', operator: '==', value: user.uid },
          { field: 'date', operator: '==', value: requestDate }
        ], undefined, undefined, 1);
        
        if (data && data.length > 0) {
          return data[0].steps || 0;
        }
      }
      
      // If no data found for past dates
      return 0;
    } catch (error) {
      console.error('Error getting step count:', error);
      return 0;
    }
  }
  
  // Get calories burned
  async getCaloriesBurned(date: Date): Promise<{ active: number; resting: number; total: number }> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    try {
      // Get health data from the connector
      const healthData = await healthDataConnector.fetchHealthData();
      
      // If the date is today, return the calories from the health data
      const today = new Date().toISOString().split('T')[0];
      const requestDate = date.toISOString().split('T')[0];
      
      if (requestDate === today) {
        // Estimate resting calories (basal metabolic rate) as 70% of total
        const active = healthData.caloriesBurned;
        const resting = Math.round(active * 0.7 / 0.3); // If active is 30%, then resting is 70%
        
        return {
          active,
          resting,
          total: active + resting
        };
      }
      
      // For past dates, check Firebase
      const user = firebaseService.getCurrentUser();
      if (user) {
        const data = await firebaseService.queryDocuments('health_daily_summary', [
          { field: 'user_id', operator: '==', value: user.uid },
          { field: 'date', operator: '==', value: requestDate }
        ], undefined, undefined, 1);
        
        if (data && data.length > 0) {
          const active = data[0].calories_burned || 0;
          const resting = data[0].resting_calories || Math.round(active * 0.7 / 0.3);
          
          return {
            active,
            resting,
            total: active + resting
          };
        }
      }
      
      // Default response if no data found
      return {
        active: 0,
        resting: 0,
        total: 0
      };
    } catch (error) {
      console.error('Error getting calories burned:', error);
      return {
        active: 0,
        resting: 0,
        total: 0
      };
    }
  }
  
  // Get sleep data
  async getSleepData(date: Date): Promise<number> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    try {
      // Get health data from the connector
      const healthData = await healthDataConnector.fetchHealthData();
      
      // If the date is today or yesterday (since sleep data is typically from the night before)
      const today = new Date().toISOString().split('T')[0];
      const yesterday = new Date(Date.now() - 86400000).toISOString().split('T')[0];
      const requestDate = date.toISOString().split('T')[0];
      
      if (requestDate === today || requestDate === yesterday) {
        return healthData.sleep.hours;
      }
      
      // For past dates, check Firebase
      const user = firebaseService.getCurrentUser();
      if (user) {
        const data = await firebaseService.queryDocuments('health_daily_summary', [
          { field: 'user_id', operator: '==', value: user.uid },
          { field: 'date', operator: '==', value: requestDate }
        ], undefined, undefined, 1);
        
        if (data && data.length > 0) {
          return data[0].sleep_hours || 0;
        }
      }
      
      return 0;
    } catch (error) {
      console.error('Error getting sleep data:', error);
      return 0;
    }
  }
  
  // Get heart rate data
  async getHeartRate(date: Date): Promise<number> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    try {
      // Get health data from the connector
      const healthData = await healthDataConnector.fetchHealthData();
      
      // If the date is today
      const today = new Date().toISOString().split('T')[0];
      const requestDate = date.toISOString().split('T')[0];
      
      if (requestDate === today) {
        return healthData.heartRate;
      }
      
      // For past dates, check Firebase
      const user = firebaseService.getCurrentUser();
      if (user) {
        const data = await firebaseService.queryDocuments('health_daily_summary', [
          { field: 'user_id', operator: '==', value: user.uid },
          { field: 'date', operator: '==', value: requestDate }
        ], undefined, undefined, 1);
        
        if (data && data.length > 0) {
          return data[0].heart_rate || 0;
        }
      }
      
      return 0;
    } catch (error) {
      console.error('Error getting heart rate data:', error);
      return 0;
    }
  }
  
  // Get weight data
  async getWeight(): Promise<number> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    try {
      // Check Firebase for the most recent weight entry
      const user = firebaseService.getCurrentUser();
      if (user) {
        const data = await firebaseService.queryDocuments('user_measurements', [
          { field: 'user_id', operator: '==', value: user.uid }
        ], 'measured_at', 'desc', 1);
        
        if (data && data.length > 0 && data[0].weight) {
          return data[0].weight;
        }
      }
      
      // If no weight data is found, return a default value or 0
      return 0;
    } catch (error) {
      console.error('Error getting weight data:', error);
      return 0;
    }
  }
  
  // Get all health data for a day
  async getDailyHealthData(date: Date): Promise<HealthData> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    try {
      const dateStr = date.toISOString().split('T')[0];
      const today = new Date().toISOString().split('T')[0];
      
      // Check for existing data in Firebase first
      const user = firebaseService.getCurrentUser();
      
      if (user) {
        try {
          // Get Firestore instance from firebaseService to ensure it's properly initialized
          const firestore = firebaseService.getFirestore();
          
          // First check if user profile exists, if not create it
          const profileRef = doc(db, 'profiles', user.uid);
          const profileSnap = await getDoc(profileRef);
          
          if (!profileSnap.exists()) {
            console.log('Creating profile for user:', user.uid);
            // Create a basic profile
            await setDoc(profileRef, {
              id: user.uid,
              email: user.email || '',
              full_name: user.displayName || '',
              created_at: Timestamp.now(),
              updated_at: Timestamp.now(),
              daily_calorie_goal: 2000,
            });
          }
          
          // Then get health data
          const data = await firebaseService.queryDocuments('health_daily_summary', [
            { field: 'user_id', operator: '==', value: user.uid },
            { field: 'date', operator: '==', value: dateStr }
          ], undefined, undefined, 1);
          
          if (data && data.length > 0) {
            const item = data[0];
            return {
              steps: item.steps || 0,
              activeCalories: item.active_calories || 0,
              restingCalories: item.resting_calories || 0,
              totalCalories: (item.active_calories || 0) + (item.resting_calories || 0),
              distance: item.distance || 0,
              sleepHours: item.sleep_hours || 0,
              heartRate: item.heart_rate || 0,
              weight: item.weight || 0,
              waterIntake: item.water_ml || 0,
              date: item.date || new Date().toISOString().split('T')[0],
            };
          }
        } catch (err) {
          console.error('Error fetching user profile or health data:', err);
          // Continue execution to use health data connector as fallback
        }
      }
      
      // Always get fresh data from health sources regardless of date
      // Use our health data connector to get real-time data from health platforms
      const healthData = await healthDataConnector.fetchHealthData();
      
      // Get water intake from our app's database
      const waterIntake = await this.getWaterIntake(date);
      
      // Get weight data
      const weight = await this.getWeight();
      
      // Create the health data object
      const activeCalories = healthData.caloriesBurned;
      const restingCalories = Math.round(activeCalories * 0.7 / 0.3); // Estimate resting calories if not directly available
      
      const healthDataResult = {
        steps: healthData.steps,
        activeCalories,
        restingCalories,
        totalCalories: activeCalories + restingCalories,
        distance: healthData.distance,
        sleepHours: healthData.sleep.hours,
        heartRate: healthData.heartRate,
        weight,
        waterIntake,
        date: dateStr,
      };
      
      // Save to Firebase if user is logged in
      if (user) {
        try {
          await firebaseService.addDocument('health_daily_summary', {
            user_id: user.uid,
            date: dateStr,
            steps: healthDataResult.steps,
            active_calories: healthDataResult.activeCalories,
            resting_calories: healthDataResult.restingCalories,
            total_calories: healthDataResult.totalCalories,
            distance: healthDataResult.distance,
            sleep_hours: healthDataResult.sleepHours,
            heart_rate: healthDataResult.heartRate,
            weight: healthDataResult.weight,
            water_ml: healthDataResult.waterIntake,
            updated_at: new Date().toISOString()
          });
        } catch (err) {
          console.error('Error saving health data to Firebase:', err);
        }
      }
      
      return healthDataResult;
    } catch (err) {
      console.error('Error getting daily health data:', err);
      
      // If there's an error, still try to return real data from connector
      try {
        const healthData = await healthDataConnector.fetchHealthData();
        const activeCalories = healthData.caloriesBurned;
        const restingCalories = Math.round(activeCalories * 0.7 / 0.3);
        
        return {
          steps: healthData.steps,
          activeCalories,
          restingCalories,
          totalCalories: activeCalories + restingCalories,
          distance: healthData.distance,
          sleepHours: healthData.sleep.hours,
          heartRate: healthData.heartRate,
          weight: 0, 
          waterIntake: 0,
          date: date.toISOString().split('T')[0],
        };
      } catch (innerErr) {
        console.error('Error fetching from health connector:', innerErr);
        // As a last resort, return empty data
        return {
          steps: 0,
          activeCalories: 0,
          restingCalories: 0,
          totalCalories: 0,
          distance: 0,
          sleepHours: 0,
          heartRate: 0,
          weight: 0,
          waterIntake: 0,
          date: date.toISOString().split('T')[0],
        };
      }
    }
  }
  
  // Record water intake
  async recordWaterIntake(amount: number, date: Date): Promise<boolean> {
    try {
      // Use healthDataConnector to log water intake
      await healthDataConnector.logWaterIntake(amount / 250); // Convert ml to glasses
      
      const user = firebaseService.getCurrentUser();
      
      if (!user) {
        console.error('No user authenticated');
        return false;
      }
      
      const dateStr = date.toISOString().split('T')[0];
      
      // Save to water intake collection in Firebase
      await firebaseService.addDocument('water_intake', {
        user_id: user.uid,
        date: dateStr,
        amount_ml: amount
      });
      
      // Also update the daily summary
      // First check if there's an existing record for this day
      const existingData = await firebaseService.queryDocuments('health_daily_summary', [
        { field: 'user_id', operator: '==', value: user.uid },
        { field: 'date', operator: '==', value: dateStr }
      ], undefined, undefined, 1);
      
      if (existingData && existingData.length > 0) {
        // Update existing document
        const currentWaterML = existingData[0].water_ml || 0;
        await firebaseService.updateDocument('health_daily_summary', existingData[0].id, {
          water_ml: currentWaterML + amount
        });
      } else {
        // Create new document
        await firebaseService.addDocument('health_daily_summary', {
          user_id: user.uid,
          date: dateStr,
          water_ml: amount
        });
      }
      
      return true;
    } catch (error) {
      console.error('Error recording water intake:', error);
      return false;
    }
  }
  
  // Get water intake
  async getWaterIntake(date: Date): Promise<number> {
    try {
      const user = firebaseService.getCurrentUser();
      
      if (!user) {
        console.error('No user authenticated');
        return 0;
      }
      
      const dateStr = date.toISOString().split('T')[0];
      
      // Get total water intake for the day from Firebase
      const data = await firebaseService.queryDocuments('water_intake', [
        { field: 'user_id', operator: '==', value: user.uid },
        { field: 'date', operator: '==', value: dateStr }
      ]);
      
      if (!data || data.length === 0) {
        return 0;
      }
      
      // Sum up all water intake entries for the day
      return data.reduce((total, item) => total + (item.amount_ml || 0), 0);
    } catch (error) {
      console.error('Error getting water intake:', error);
      return 0;
    }
  }
  
  // Set or update health goals
  async setHealthGoals(goals: HealthGoals): Promise<boolean> {
    try {
      // Store goals in secure storage
      await secureStorage.saveSecurely('healthGoals', JSON.stringify(goals));
      
      // Also save to Firebase if user is logged in
      const user = firebaseService.getCurrentUser();
      if (user) {
        // Check if there's an existing record
        const existingData = await firebaseService.queryDocuments('user_health_goals', [
          { field: 'user_id', operator: '==', value: user.uid }
        ], undefined, undefined, 1);
        
        if (existingData && existingData.length > 0) {
          // Update existing document
          await firebaseService.updateDocument('user_health_goals', existingData[0].id, {
            steps_goal: goals.dailySteps,
            active_calories_goal: goals.dailyActiveCalories,
            water_goal: goals.dailyWaterIntake,
            active_minutes_goal: goals.weeklyActiveMinutes / 7, // Convert weekly to daily
            sleep_goal: goals.sleepHoursPerNight
          });
        } else {
          // Create new document
          await firebaseService.addDocument('user_health_goals', {
            user_id: user.uid,
            steps_goal: goals.dailySteps,
            active_calories_goal: goals.dailyActiveCalories,
            water_goal: goals.dailyWaterIntake,
            active_minutes_goal: goals.weeklyActiveMinutes / 7, // Convert weekly to daily
            sleep_goal: goals.sleepHoursPerNight
          });
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error setting health goals:', error);
      return false;
    }
  }
  
  // Get health goals
  async getHealthGoals(): Promise<HealthGoals> {
    try {
      // Try to get from Firebase first
      const user = firebaseService.getCurrentUser();
      if (user) {
        const data = await firebaseService.queryDocuments('user_health_goals', [
          { field: 'user_id', operator: '==', value: user.uid }
        ], undefined, undefined, 1);
        
        if (data && data.length > 0) {
          const item = data[0];
          return {
            dailySteps: item.steps_goal || 10000,
            dailyActiveCalories: item.active_calories_goal || 500,
            dailyWaterIntake: item.water_goal || 2000,
            weeklyActiveMinutes: (item.active_minutes_goal || 30) * 7, // Convert daily to weekly
            sleepHoursPerNight: item.sleep_goal || 8,
          };
        }
      }
      
      // Fall back to secure storage
      const goalsJson = await secureStorage.getSecurely('healthGoals');
      
      if (goalsJson) {
        return JSON.parse(goalsJson);
      }
      
      // Return default goals
      return {
        dailySteps: 10000,
        dailyActiveCalories: 500,
        dailyWaterIntake: 2000,
        weeklyActiveMinutes: 150,
        sleepHoursPerNight: 8,
      };
    } catch (error) {
      console.error('Error getting health goals:', error);
      
      // Return default goals
      return {
        dailySteps: 10000,
        dailyActiveCalories: 500,
        dailyWaterIntake: 2000,
        weeklyActiveMinutes: 150,
        sleepHoursPerNight: 8,
      };
    }
  }
  
  // Connect to wearable device
  async connectWearableDevice(deviceId: string, deviceType: string): Promise<boolean> {
    try {
      // Get device info from the API
      const deviceInfo = await firebaseService.queryDocuments('wearable_devices', [
        { field: 'device_id', operator: '==', value: deviceId }
      ], undefined, undefined, 1);
      
      // This is a real implementation that would connect to a device API
      // For now we'll create a device record with the connection status
      const device: WearableDevice = {
        id: deviceId,
        name: deviceInfo && deviceInfo.length > 0 ? deviceInfo[0].device_name : `Device ${deviceId}`,
        type: deviceType,
        connected: true,
        lastSync: new Date().toISOString(),
      };
      
      // Add to connected devices
      this.connectedDevices = this.connectedDevices.filter(d => d.id !== deviceId);
      this.connectedDevices.push(device);
      
      // Save connected devices to secure storage
      await secureStorage.saveSecurely('connectedDevices', JSON.stringify(this.connectedDevices));
      
      // Also save to Firebase if logged in
      const user = firebaseService.getCurrentUser();
      if (user) {
        // Check if there's an existing record
        const existingData = await firebaseService.queryDocuments('user_connected_devices', [
          { field: 'user_id', operator: '==', value: user.uid },
          { field: 'device_id', operator: '==', value: deviceId }
        ], undefined, undefined, 1);
        
        if (existingData && existingData.length > 0) {
          // Update existing document
          await firebaseService.updateDocument('user_connected_devices', existingData[0].id, {
            device_name: device.name,
            device_type: deviceType,
            connected: true,
            last_sync: device.lastSync
          });
        } else {
          // Create new document
          await firebaseService.addDocument('user_connected_devices', {
            user_id: user.uid,
            device_id: deviceId,
            device_name: device.name,
            device_type: deviceType,
            connected: true,
            last_sync: device.lastSync
          });
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error connecting wearable device:', error);
      return false;
    }
  }
  
  // Get connected wearable devices
  async getConnectedDevices(): Promise<WearableDevice[]> {
    try {
      // Try to get from Firebase first
      const user = firebaseService.getCurrentUser();
      if (user) {
        const data = await firebaseService.queryDocuments('user_connected_devices', [
          { field: 'user_id', operator: '==', value: user.uid },
          { field: 'connected', operator: '==', value: true }
        ]);
        
        if (data && data.length > 0) {
          this.connectedDevices = data.map(item => ({
            id: item.device_id,
            name: item.device_name,
            type: item.device_type,
            connected: item.connected,
            lastSync: item.last_sync,
          }));
          
          return this.connectedDevices;
        }
      }
      
      // Fall back to secure storage
      const devicesJson = await secureStorage.getSecurely('connectedDevices');
      
      if (devicesJson) {
        this.connectedDevices = JSON.parse(devicesJson);
      }
      
      return this.connectedDevices;
    } catch (error) {
      console.error('Error getting connected devices:', error);
      return [];
    }
  }
  
  // Get health insights
  async getHealthInsights(startDate: Date, endDate: Date): Promise<string[]> {
    try {
      // Get health data for the date range from Firebase
      const user = firebaseService.getCurrentUser();
      
      if (!user) {
        return ['Sign in to see your personalized health insights.'];
      }
      
      const startDateStr = startDate.toISOString().split('T')[0];
      const endDateStr = endDate.toISOString().split('T')[0];
      
      const data = await firebaseService.queryDocuments('health_daily_summary', [
        { field: 'user_id', operator: '==', value: user.uid },
        { field: 'date', operator: '>=', value: startDateStr },
        { field: 'date', operator: '<=', value: endDateStr }
      ], 'date', 'asc');
      
      if (!data || data.length === 0) {
        return ['No health data available. Start tracking to see insights.'];
      }
      
      // Calculate averages
      const avgSteps = data.reduce((sum, item) => sum + (item.steps || 0), 0) / data.length;
      const avgActiveCalories = data.reduce((sum, item) => sum + (item.active_calories || 0), 0) / data.length;
      const avgSleepHours = data.reduce((sum, item) => sum + (item.sleep_hours || 0), 0) / data.length;
      const avgWaterIntake = data.reduce((sum, item) => sum + (item.water_ml || 0), 0) / data.length;
      
      // Get goals
      const goals = await this.getHealthGoals();
      
      // Generate insights
      const insights: string[] = [];
      
      if (avgSteps < goals.dailySteps * 0.7) {
        insights.push('Your step count is below your goal. Try to incorporate more walking into your daily routine.');
      } else if (avgSteps >= goals.dailySteps * 1.2) {
        insights.push('Great job exceeding your step goal! Consider increasing your target for a new challenge.');
      }
      
      if (avgActiveCalories < goals.dailyActiveCalories * 0.7) {
        insights.push('You\'re not burning as many active calories as your goal. Consider adding more intensity to your workouts.');
      }
      
      if (avgSleepHours < goals.sleepHoursPerNight - 1) {
        insights.push('You\'re not getting enough sleep. Aim for at least 7-8 hours per night for optimal health.');
      }
      
      if (avgWaterIntake < goals.dailyWaterIntake * 0.7) {
        insights.push('You\'re not drinking enough water. Try to increase your daily water intake.');
      }
      
      // Trend analysis
      if (data.length > 3) {
        // Check for step trends
        const firstHalfSteps = data.slice(0, Math.floor(data.length / 2))
          .reduce((sum, item) => sum + (item.steps || 0), 0) / Math.floor(data.length / 2);
        const secondHalfSteps = data.slice(Math.floor(data.length / 2))
          .reduce((sum, item) => sum + (item.steps || 0), 0) / (data.length - Math.floor(data.length / 2));
        
        if (secondHalfSteps > firstHalfSteps * 1.2) {
          insights.push('Your step count is trending upward! Keep up the good work!');
        } else if (secondHalfSteps < firstHalfSteps * 0.8) {
          insights.push('Your activity level has been decreasing. Try to stay consistent with your daily movement.');
        }
      }
      
      // Add factual insights based on real data
      if (insights.length === 0) {
        insights.push('Connect your wearable or start tracking to see more personalized insights.');
      }
      
      return insights;
    } catch (error) {
      console.error('Error generating health insights:', error);
      return ['Connect your wearable or start tracking to see health insights.'];
    }
  }
}

export const healthTrackingService = new HealthTrackingService(); 