import { z } from 'zod';

/**
 * Database of healthy ingredient substitutions based on USDA guidelines
 * and nutritional research
 */

// Schema for nutritional facts
export const NutritionFactsSchema = z.object({
  calories: z.number(),
  protein: z.number().optional(),
  carbs: z.number().optional(),
  fat: z.number().optional(),
  saturatedFat: z.number().optional(),
  transFat: z.number().optional(),
  cholesterol: z.number().optional(),
  sodium: z.number().optional(),
  fiber: z.number().optional(),
  sugar: z.number().optional(),
  calcium: z.number().optional(),
  iron: z.number().optional(),
  potassium: z.number().optional(),
  vitaminA: z.number().optional(),
  vitaminC: z.number().optional(),
  vitaminD: z.number().optional(),
  vitaminE: z.number().optional(),
});

export type NutritionFacts = z.infer<typeof NutritionFactsSchema>;

// Schema for a food item
export const FoodItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  category: z.string(),
  nutrition: NutritionFactsSchema,
  glycemicIndex: z.number().optional(),
  processing: z.enum(['whole', 'minimal', 'processed', 'ultraProcessed']).optional(),
  commonAllergens: z.array(z.string()).optional(),
  dietaryCategories: z.array(z.string()).optional(),
});

export type FoodItem = z.infer<typeof FoodItemSchema>;

// Schema for a substitution
export const SubstitutionSchema = z.object({
  originalId: z.string(),
  substituteId: z.string(),
  healthBenefits: z.array(z.string()),
  bestFor: z.array(z.string()).optional(), // dietary preferences this substitution works best for
  swapRatio: z.string().optional(), // e.g., "1:1" or "2 tbsp:1 tbsp"
  culinaryNotes: z.string().optional(), // cooking adjustments needed
  tasteImpact: z.enum(['none', 'mild', 'moderate', 'significant']).optional(),
  textureImpact: z.enum(['none', 'mild', 'moderate', 'significant']).optional(),
});

export type Substitution = z.infer<typeof SubstitutionSchema>;

// Define common categories for dietary preferences
export const DIETARY_CATEGORIES = [
  'vegan',
  'vegetarian',
  'pescatarian',
  'dairy-free',
  'gluten-free',
  'nut-free',
  'keto',
  'paleo',
  'low-carb',
  'low-fat',
  'low-sodium',
  'high-protein',
  'mediterranean',
  'diabetic-friendly',
  'heart-healthy',
];

// Define health focus categories
export const HEALTH_FOCUSES = [
  'weight-loss',
  'heart-health',
  'blood-sugar-control',
  'muscle-building',
  'digestive-health',
  'anti-inflammatory',
  'bone-health',
  'brain-health',
  'immune-support',
  'energy-boost',
];

// Food items database
export const foodItems: FoodItem[] = [
  {
    id: 'butter',
    name: 'Butter',
    category: 'dairy',
    nutrition: {
      calories: 717,
      fat: 81,
      saturatedFat: 51,
      cholesterol: 215,
      sodium: 11,
      carbs: 0.1,
      protein: 0.9,
    },
    glycemicIndex: 0,
    processing: 'minimal',
    commonAllergens: ['dairy'],
    dietaryCategories: ['low-carb', 'keto'],
  },
  {
    id: 'olive-oil',
    name: 'Olive Oil',
    category: 'oils',
    nutrition: {
      calories: 884,
      fat: 100,
      saturatedFat: 14,
      cholesterol: 0,
      sodium: 2,
      carbs: 0,
      protein: 0,
    },
    glycemicIndex: 0,
    processing: 'minimal',
    dietaryCategories: ['vegan', 'vegetarian', 'dairy-free', 'gluten-free', 'mediterranean', 'heart-healthy'],
  },
  {
    id: 'avocado-oil',
    name: 'Avocado Oil',
    category: 'oils',
    nutrition: {
      calories: 884,
      fat: 100,
      saturatedFat: 12,
      cholesterol: 0,
      sodium: 0,
      carbs: 0,
      protein: 0,
    },
    glycemicIndex: 0,
    processing: 'minimal',
    dietaryCategories: ['vegan', 'vegetarian', 'dairy-free', 'gluten-free', 'keto', 'paleo'],
  },
  {
    id: 'white-sugar',
    name: 'White Sugar',
    category: 'sweeteners',
    nutrition: {
      calories: 387,
      carbs: 100,
      sugar: 100,
      protein: 0,
      fat: 0,
    },
    glycemicIndex: 65,
    processing: 'processed',
    dietaryCategories: ['vegan', 'vegetarian', 'dairy-free', 'nut-free'],
  },
  {
    id: 'honey',
    name: 'Honey',
    category: 'sweeteners',
    nutrition: {
      calories: 304,
      carbs: 82,
      sugar: 82,
      protein: 0.3,
      fat: 0,
    },
    glycemicIndex: 55,
    processing: 'whole',
    dietaryCategories: ['vegetarian', 'dairy-free', 'gluten-free', 'nut-free'],
  },
  {
    id: 'maple-syrup',
    name: 'Maple Syrup',
    category: 'sweeteners',
    nutrition: {
      calories: 260,
      carbs: 67,
      sugar: 60,
      protein: 0,
      fat: 0.1,
    },
    glycemicIndex: 54,
    processing: 'minimal',
    dietaryCategories: ['vegan', 'vegetarian', 'dairy-free', 'gluten-free', 'nut-free'],
  },
  {
    id: 'white-flour',
    name: 'White All-Purpose Flour',
    category: 'grains',
    nutrition: {
      calories: 364,
      carbs: 76,
      fiber: 2.7,
      protein: 10,
      fat: 1,
    },
    glycemicIndex: 85,
    processing: 'processed',
    commonAllergens: ['gluten', 'wheat'],
    dietaryCategories: ['vegan', 'vegetarian', 'dairy-free', 'nut-free'],
  },
  {
    id: 'almond-flour',
    name: 'Almond Flour',
    category: 'flours',
    nutrition: {
      calories: 590,
      carbs: 22,
      fiber: 12,
      protein: 21,
      fat: 52,
    },
    glycemicIndex: 0,
    processing: 'minimal',
    commonAllergens: ['nuts'],
    dietaryCategories: ['gluten-free', 'grain-free', 'low-carb', 'keto', 'paleo'],
  },
  {
    id: 'oat-flour',
    name: 'Oat Flour',
    category: 'flours',
    nutrition: {
      calories: 404,
      carbs: 69,
      fiber: 7,
      protein: 14.7,
      fat: 9,
    },
    glycemicIndex: 40,
    processing: 'minimal',
    dietaryCategories: ['vegetarian', 'vegan', 'dairy-free', 'nut-free'],
  },
  {
    id: 'ground-beef-80-20',
    name: 'Ground Beef (80% lean)',
    category: 'meats',
    nutrition: {
      calories: 254,
      protein: 17.3,
      fat: 20,
      saturatedFat: 7.8,
      cholesterol: 75,
      sodium: 75,
      carbs: 0,
    },
    processing: 'minimal',
    dietaryCategories: ['low-carb', 'keto', 'paleo'],
  },
  {
    id: 'ground-turkey',
    name: 'Ground Turkey (93% lean)',
    category: 'meats',
    nutrition: {
      calories: 164,
      protein: 22,
      fat: 8,
      saturatedFat: 2.3,
      cholesterol: 94,
      sodium: 86,
      carbs: 0,
    },
    processing: 'minimal',
    dietaryCategories: ['low-carb', 'keto', 'paleo'],
  },
  {
    id: 'lentils',
    name: 'Lentils (cooked)',
    category: 'legumes',
    nutrition: {
      calories: 116,
      protein: 9,
      fat: 0.4,
      carbs: 20,
      fiber: 7.9,
      sodium: 2,
      iron: 3.3,
    },
    glycemicIndex: 35,
    processing: 'whole',
    dietaryCategories: ['vegan', 'vegetarian', 'dairy-free', 'gluten-free', 'nut-free', 'heart-healthy'],
  },
  {
    id: 'cream',
    name: 'Heavy Cream',
    category: 'dairy',
    nutrition: {
      calories: 340,
      fat: 36,
      saturatedFat: 23,
      cholesterol: 130,
      sodium: 35,
      carbs: 2.8,
      protein: 2.1,
    },
    processing: 'minimal',
    commonAllergens: ['dairy'],
    dietaryCategories: ['low-carb', 'keto'],
  },
  {
    id: 'coconut-cream',
    name: 'Coconut Cream',
    category: 'dairy-alternatives',
    nutrition: {
      calories: 298,
      fat: 29.7,
      saturatedFat: 26.4,
      sodium: 41,
      carbs: 6.7,
      fiber: 2.2,
      protein: 3,
    },
    processing: 'minimal',
    dietaryCategories: ['vegan', 'vegetarian', 'dairy-free', 'gluten-free', 'paleo'],
  },
  {
    id: 'white-rice',
    name: 'White Rice (cooked)',
    category: 'grains',
    nutrition: {
      calories: 130,
      carbs: 28.2,
      protein: 2.7,
      fat: 0.3,
      fiber: 0.4,
    },
    glycemicIndex: 73,
    processing: 'processed',
    dietaryCategories: ['vegan', 'vegetarian', 'dairy-free', 'gluten-free', 'nut-free'],
  },
  {
    id: 'brown-rice',
    name: 'Brown Rice (cooked)',
    category: 'grains',
    nutrition: {
      calories: 123,
      carbs: 25.8,
      fiber: 1.8,
      protein: 2.7,
      fat: 0.9,
    },
    glycemicIndex: 50,
    processing: 'whole',
    dietaryCategories: ['vegan', 'vegetarian', 'dairy-free', 'gluten-free', 'nut-free', 'heart-healthy'],
  },
  {
    id: 'cauliflower-rice',
    name: 'Cauliflower Rice',
    category: 'vegetables',
    nutrition: {
      calories: 25,
      carbs: 5,
      fiber: 2.5,
      protein: 2,
      fat: 0.3,
      vitaminC: 46,
    },
    glycemicIndex: 15,
    processing: 'minimal',
    dietaryCategories: ['vegan', 'vegetarian', 'dairy-free', 'gluten-free', 'grain-free', 'low-carb', 'keto', 'paleo'],
  },
  // Add more food items as needed
];

// Substitutions database
export const substitutions: Substitution[] = [
  {
    originalId: 'butter',
    substituteId: 'olive-oil',
    healthBenefits: [
      'Replaces saturated fats with heart-healthy monounsaturated fats',
      'Reduces cholesterol intake',
      'Contains antioxidants and anti-inflammatory compounds',
    ],
    bestFor: ['heart-health', 'weight-loss', 'mediterranean'],
    swapRatio: '1:3/4 (3/4 tbsp olive oil for 1 tbsp butter)',
    culinaryNotes: 'Best for sautéing and general cooking, not ideal for baking',
    tasteImpact: 'moderate',
    textureImpact: 'mild',
  },
  {
    originalId: 'butter',
    substituteId: 'avocado-oil',
    healthBenefits: [
      'Contains heart-healthy monounsaturated fats',
      'Has a higher smoke point for high-heat cooking',
      'Neutral flavor profile compared to olive oil',
    ],
    bestFor: ['heart-health', 'paleo', 'keto'],
    swapRatio: '1:3/4 (3/4 tbsp avocado oil for 1 tbsp butter)',
    culinaryNotes: 'Excellent for high-heat cooking and has a neutral flavor',
    tasteImpact: 'mild',
    textureImpact: 'mild',
  },
  {
    originalId: 'white-sugar',
    substituteId: 'honey',
    healthBenefits: [
      'Contains antioxidants and trace nutrients',
      'Lower glycemic index than white sugar',
      'Has antimicrobial properties',
    ],
    bestFor: ['anti-inflammatory', 'immune-support'],
    swapRatio: '1:3/4 (3/4 cup honey for 1 cup sugar + reduce liquids by 1/4 cup)',
    culinaryNotes: 'Adds moisture to baked goods; reduce liquid in recipe and bake at 25°F lower temperature',
    tasteImpact: 'moderate',
    textureImpact: 'moderate',
  },
  {
    originalId: 'white-sugar',
    substituteId: 'maple-syrup',
    healthBenefits: [
      'Contains minerals like manganese and zinc',
      'Lower glycemic index than white sugar',
      'Contains antioxidants',
    ],
    bestFor: ['vegan', 'anti-inflammatory'],
    swapRatio: '1:3/4 (3/4 cup maple syrup for 1 cup sugar + reduce liquids by 3 tbsp)',
    culinaryNotes: 'Adds moisture and a distinct flavor; reduce liquid in recipe slightly',
    tasteImpact: 'significant',
    textureImpact: 'moderate',
  },
  {
    originalId: 'white-flour',
    substituteId: 'almond-flour',
    healthBenefits: [
      'Higher in protein and healthy fats',
      'Lower in carbohydrates',
      'Contains vitamin E and magnesium',
      'High in fiber',
    ],
    bestFor: ['gluten-free', 'low-carb', 'keto', 'diabetic-friendly'],
    swapRatio: '1:1 (may need to adjust liquids)',
    culinaryNotes: 'Creates denser baked goods; often needs more binding agent like eggs',
    tasteImpact: 'moderate',
    textureImpact: 'significant',
  },
  {
    originalId: 'white-flour',
    substituteId: 'oat-flour',
    healthBenefits: [
      'Higher in fiber and protein',
      'Contains beta-glucans that lower cholesterol',
      'Provides more nutrients like iron and B vitamins',
    ],
    bestFor: ['heart-health', 'digestive-health'],
    swapRatio: '1:1 for up to 20% of flour in recipe',
    culinaryNotes: 'Best when combined with other flours; adds a slightly nutty flavor',
    tasteImpact: 'mild',
    textureImpact: 'moderate',
  },
  {
    originalId: 'ground-beef-80-20',
    substituteId: 'ground-turkey',
    healthBenefits: [
      'Lower in total fat and saturated fat',
      'Fewer calories',
      'Still high in protein',
    ],
    bestFor: ['weight-loss', 'heart-health'],
    swapRatio: '1:1',
    culinaryNotes: 'May need added moisture or fat (like olive oil) for juiciness',
    tasteImpact: 'moderate',
    textureImpact: 'mild',
  },
  {
    originalId: 'ground-beef-80-20',
    substituteId: 'lentils',
    healthBenefits: [
      'Plant-based protein source',
      'High in fiber',
      'Low in fat and contains no cholesterol',
      'Rich in iron and folate',
    ],
    bestFor: ['vegan', 'vegetarian', 'heart-health', 'digestive-health'],
    swapRatio: '1 cup cooked lentils for 1 pound ground beef',
    culinaryNotes: 'Works best in dishes with strong flavors like chili, tacos, and sloppy joes',
    tasteImpact: 'significant',
    textureImpact: 'significant',
  },
  {
    originalId: 'cream',
    substituteId: 'coconut-cream',
    healthBenefits: [
      'Dairy-free alternative',
      'Contains medium-chain triglycerides (MCTs)',
      'No cholesterol',
    ],
    bestFor: ['vegan', 'dairy-free', 'paleo'],
    swapRatio: '1:1',
    culinaryNotes: 'Adds coconut flavor which works well in many desserts and curries',
    tasteImpact: 'significant',
    textureImpact: 'mild',
  },
  {
    originalId: 'white-rice',
    substituteId: 'brown-rice',
    healthBenefits: [
      'Higher in fiber',
      'Contains more vitamins and minerals',
      'Lower glycemic index',
      'Supports digestive health',
    ],
    bestFor: ['heart-health', 'digestive-health', 'blood-sugar-control'],
    swapRatio: '1:1 (may need longer cooking time)',
    culinaryNotes: 'Has a nuttier flavor and chewier texture; requires longer cooking time',
    tasteImpact: 'mild',
    textureImpact: 'moderate',
  },
  {
    originalId: 'white-rice',
    substituteId: 'cauliflower-rice',
    healthBenefits: [
      'Significantly lower in calories and carbs',
      'Higher in fiber and nutrients',
      'Contains cancer-fighting compounds',
      'Adds vegetable serving to meal',
    ],
    bestFor: ['weight-loss', 'low-carb', 'keto', 'diabetic-friendly'],
    swapRatio: '1:1',
    culinaryNotes: 'Best when lightly cooked to maintain texture; works well with flavorful dishes',
    tasteImpact: 'moderate',
    textureImpact: 'significant',
  },
  // Add more substitutions as needed
];

/**
 * Find a food item by ID
 */
export function findFoodById(id: string): FoodItem | undefined {
  return foodItems.find(item => item.id === id);
}

/**
 * Find all possible substitutions for a given food item
 */
export function findSubstitutionsFor(foodId: string): Substitution[] {
  return substitutions.filter(sub => sub.originalId === foodId);
}

/**
 * Find the best substitution based on dietary preferences and health focus
 */
export function findBestSubstitution(
  foodId: string,
  dietaryPreferences: string[] = [],
  healthFocus: string[] = []
): Substitution | undefined {
  const options = findSubstitutionsFor(foodId);
  if (options.length === 0) return undefined;
  
  // Score each substitution based on matching dietary preferences and health focus
  const scoredOptions = options.map(option => {
    let score = 0;
    
    // Check dietary preferences
    if (option.bestFor) {
      dietaryPreferences.forEach(pref => {
        if (option.bestFor?.includes(pref)) score += 2;
      });
    }
    
    // Check health focus
    if (option.bestFor) {
      healthFocus.forEach(focus => {
        if (option.bestFor?.includes(focus)) score += 3;
      });
    }
    
    return { option, score };
  });
  
  // Sort by score (highest first)
  scoredOptions.sort((a, b) => b.score - a.score);
  
  // Return the best match (or first option if no preferences matched)
  return scoredOptions[0].option;
}

/**
 * Calculate nutrition improvement percentages
 */
export function calculateNutritionImprovement(
  originalId: string,
  substituteId: string
): Record<string, number> {
  const original = findFoodById(originalId);
  const substitute = findFoodById(substituteId);
  
  if (!original || !substitute) return {};
  
  const improvements: Record<string, number> = {};
  
  // Calculate percentage improvements for all available nutrition facts
  Object.keys(original.nutrition).forEach(key => {
    const origValue = original.nutrition[key as keyof NutritionFacts];
    const subValue = substitute.nutrition[key as keyof NutritionFacts];
    
    if (origValue !== undefined && subValue !== undefined) {
      // For positive nutrients (fiber, protein, vitamins), higher is better
      const positiveNutrients = ['fiber', 'protein', 'calcium', 'iron', 'potassium', 'vitaminA', 'vitaminC', 'vitaminD', 'vitaminE'];
      
      if (positiveNutrients.includes(key)) {
        // Calculate percentage increase (positive is good)
        if (origValue > 0) {
          improvements[key] = ((subValue - origValue) / origValue) * 100;
        } else if (subValue > 0) {
          improvements[key] = 100; // If original is 0, and substitute has some, it's a 100% improvement
        }
      } else {
        // For negative nutrients (calories, fat, sugar, sodium), lower is better
        // Calculate percentage reduction (negative is good)
        if (origValue > 0) {
          improvements[key] = ((origValue - subValue) / origValue) * 100;
        }
      }
    }
  });
  
  return improvements;
}

/**
 * Get long-term health impact description based on a substitution
 */
export function getLongTermHealthImpact(
  substitution: Substitution
): string {
  const original = findFoodById(substitution.originalId);
  const substitute = findFoodById(substitution.substituteId);
  
  if (!original || !substitute) {
    return "Unable to determine long-term health impact.";
  }
  
  // Calculate calorie difference per year if used weekly
  const caloriesPerServing = original.nutrition.calories - substitute.nutrition.calories;
  const caloriesPerYear = caloriesPerServing * 52; // assuming once per week for a year
  const weightImpactLbs = (caloriesPerYear / 3500).toFixed(1); // 3500 calories = approx. 1 lb
  
  let impact = '';
  
  // Add calorie and weight impact if significant
  if (caloriesPerServing > 50) {
    impact += `Making this swap once a week for a year could save you about ${caloriesPerYear.toFixed(0)} calories, potentially resulting in ${weightImpactLbs} pounds of weight difference.\n\n`;
  }
  
  // Add specific health impacts based on nutritional differences
  const nutritionImpacts: string[] = [];
  
  // Saturated fat impact
  if (original.nutrition.saturatedFat && substitute.nutrition.saturatedFat) {
    const satFatDiff = original.nutrition.saturatedFat - substitute.nutrition.saturatedFat;
    if (satFatDiff > 2) {
      nutritionImpacts.push("Reduced saturated fat intake helps lower LDL cholesterol levels, reducing risk of heart disease and stroke.");
    }
  }
  
  // Fiber impact
  if (substitute.nutrition.fiber && original.nutrition.fiber) {
    const fiberDiff = substitute.nutrition.fiber - original.nutrition.fiber;
    if (fiberDiff > 2) {
      nutritionImpacts.push("Increased fiber intake supports digestive health, helps manage blood sugar, and promotes feelings of fullness that may help with weight management.");
    }
  }
  
  // Sugar impact
  if (original.nutrition.sugar && substitute.nutrition.sugar) {
    const sugarDiff = original.nutrition.sugar - substitute.nutrition.sugar;
    if (sugarDiff > 5) {
      nutritionImpacts.push("Reduced sugar intake helps regulate blood glucose levels, reduces inflammation, and lowers risk of conditions like metabolic syndrome and type 2 diabetes.");
    }
  }
  
  // Add glycemic index impact
  if (original.glycemicIndex && substitute.glycemicIndex) {
    const giDiff = original.glycemicIndex - substitute.glycemicIndex;
    if (giDiff > 10) {
      nutritionImpacts.push("Lower glycemic index foods help maintain more stable blood sugar levels, reducing insulin spikes and potentially lowering risk of insulin resistance and type 2 diabetes.");
    }
  }
  
  // Add processing level impact
  if (original.processing && substitute.processing) {
    const processLevels = {
      'whole': 0,
      'minimal': 1,
      'processed': 2,
      'ultraProcessed': 3
    };
    
    const originalLevel = processLevels[original.processing];
    const substituteLevel = processLevels[substitute.processing];
    
    if (originalLevel > substituteLevel) {
      nutritionImpacts.push("Choosing less processed foods generally means more nutrients are preserved and fewer additives are consumed, supporting overall health and reducing exposure to potentially harmful compounds.");
    }
  }
  
  // Combine all impacts
  if (nutritionImpacts.length > 0) {
    impact += "Health impacts include:\n- " + nutritionImpacts.join("\n- ");
  }
  
  return impact || "This substitution may provide subtle health benefits over time.";
} 