import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
// Firebase imports
import { getFirestore, doc, updateDoc } from 'firebase/firestore';
import { app } from '../lib/firebase';
import Constants from 'expo-constants';
// API keys are now handled server-side via Firebase Functions
// import { OPENAI_API_KEY } from '@/utils/config';

// Initialize Firestore
const db = getFirestore(app);

// Cache for image generation results to avoid repeated API calls
const IMAGE_CACHE_KEY = 'recipe_image_cache';

// Cache timeout in milliseconds (12 hours)
const CACHE_TIMEOUT = 12 * 60 * 60 * 1000;

// Type definitions
interface ImageGenerationStatusUpdate {
  status: 'pending' | 'processing' | 'completed' | 'failed';
  image_url?: string;
  error?: string;
}

interface ImageGenerationResult {
  success: boolean;
  imageUrl: string | null;
  error?: string;
}

interface StabilityAIResponse {
  artifacts: {
    base64: string;
    seed: number;
    finishReason: string;
  }[];
  error?: {
    message: string;
  };
}

// Define interface for OpenAI Image Generation response
interface OpenAIImageResponse {
  created: number;
  data: {
    url: string;
    revised_prompt?: string;
  }[];
}

/**
 * Generate an image using OpenAI's DALL-E 3 model
 * @param prompt The text prompt to generate an image from
 * @param size The size of the image to generate (1024x1024, 1792x1024, or 1024x1792)
 * @returns URL of the generated image
 */
export async function generateImageWithDalle(
  prompt: string,
  size: '1024x1024' | '1792x1024' | '1024x1792' = '1024x1024'
): Promise<string | null> {
  try {
    // Use Firebase Functions for image generation
    const { getFunctions, httpsCallable } = await import('firebase/functions');
    const functions = getFunctions();

    // Enhance the prompt with details for food images
    const enhancedPrompt = enhanceFoodPrompt(prompt);
    
    // Call Firebase Function for image generation
    const generateImageFunction = httpsCallable<
      { prompt: string; size: string },
      { success: boolean; imageUrl: string }
    >(functions, 'generateRecipeImage');

    const result = await generateImageFunction({
      prompt: enhancedPrompt,
      size
    });

    if (!result.data.success) {
      throw new Error('Image generation failed');
    }

    return result.data.imageUrl;
  } catch (error) {
    console.error('Error generating image with DALL-E:', error);
    return null;
  }
}

/**
 * Generate a recipe image based on its name and ingredients
 * @param recipeName The name of the recipe
 * @param ingredients List of main ingredients
 * @returns URL of the generated recipe image
 */
export async function generateRecipeImage(
  recipeName: string,
  ingredients: string[] = []
): Promise<string | null> {
  try {
    // Create a prompt for a food photo
    const ingredientsText = ingredients.length > 0 
      ? `with ${ingredients.slice(0, 5).join(', ')}` 
      : '';
    
    const prompt = `Professional food photography of ${recipeName} ${ingredientsText}. High resolution, appetizing presentation, studio lighting, on a beautiful plate, restaurant quality, gourmet styling, overhead view.`;
    
    return await generateImageWithDalle(prompt);
  } catch (error) {
    console.error('Error generating recipe image:', error);
    return null;
  }
}

/**
 * Enhance a food-related prompt with details that work well with DALL-E 3
 * @param basePrompt The basic food prompt
 * @returns Enhanced prompt
 */
function enhanceFoodPrompt(basePrompt: string): string {
  // Check if it's already a detailed prompt
  if (basePrompt.length > 100) {
    return basePrompt;
  }
  
  // Add photography and styling details to make food look appealing
  return `${basePrompt}. Professional food photography with natural lighting, shallow depth of field, on a beautiful plate or dish. Vibrant colors, appetizing presentation, styled like a high-end restaurant or cookbook photo. 4K, highly detailed.`;
}

/**
 * Generate an image of a daily meal plan visualization
 * @param mealPlanName Name of the meal plan (e.g., "Keto Day 1")
 * @param meals Array of meal names
 * @returns URL of the generated meal plan visualization
 */
export async function generateMealPlanVisualization(
  mealPlanName: string,
  meals: string[]
): Promise<string | null> {
  try {
    const mealsText = meals.join(', ');
    const prompt = `A beautiful visual presentation of a meal plan called "${mealPlanName}" showing all meals for the day: ${mealsText}. Clean, modern design with separate sections for each meal, high-quality food photography style, appealing colors and presentation.`;
    
    return await generateImageWithDalle(prompt, '1792x1024');
  } catch (error) {
    console.error('Error generating meal plan visualization:', error);
    return null;
  }
}

/**
 * Update the image generation status for a recipe
 * @param recipeId Recipe ID
 * @param status New status value ('pending', 'processing', 'completed', 'failed')
 * @param imageUrl Optional image URL to set
 * @param error Optional error message
 */
export async function updateImageGenerationStatus(
  recipeId: string,
  status: 'pending' | 'processing' | 'completed' | 'failed',
  imageUrl?: string,
  error?: string
): Promise<void> {
  try {
    // Create an update data object with camelCase property names for Firestore
    const updateData: Record<string, any> = { status };
    
    if (imageUrl) {
      updateData.imageUrl = imageUrl; // Use camelCase for Firestore
    }
    
    if (error) {
      updateData.error = error;
    }
    
    try {
      const recipeRef = doc(db, 'recipes', recipeId);
      await updateDoc(recipeRef, updateData);
    } catch (updateError) {
      console.error('Error updating image generation status:', updateError);
    }
  } catch (error) {
    console.error('Error updating image generation status:', error);
  }
}

/**
 * Save image URL to cache for a recipe
 * @param recipeId Recipe ID
 * @param imageUrl Image URL to cache
 */
export async function saveImageToCache(recipeId: string, imageUrl: string): Promise<void> {
  try {
    if (Platform.OS === 'web') {
      // Web doesn't support FileSystem caching
      return;
    }
    
    const cacheDir = FileSystem.cacheDirectory;
    if (!cacheDir) return;
    
    const filename = `${cacheDir}recipe_image_${recipeId}.png`;
    
    await FileSystem.downloadAsync(imageUrl, filename);
    console.log(`Image saved to cache: ${filename}`);
  } catch (error) {
    console.error('Error saving image to cache:', error);
  }
}

/**
 * Check if image generation is allowed
 * @returns Boolean indicating if generation is allowed
 */
export function canGenerateImage(): boolean {
  const apiKey = process.env.STABILITY_API_KEY || Constants.expoConfig?.extra?.stabilityApiKey;
  return !!apiKey;
}

/**
 * Helper function to decode base64
 * @param base64 Base64 string to decode
 * @returns Decoded Uint8Array
 */
function decode(base64: string): Uint8Array {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes;
}

/**
 * Get cached image URL for a recipe if available
 * @param recipeId Recipe ID
 * @returns Cached image URL or null
 */
export async function getCachedImage(recipeId: string): Promise<string | null> {
  try {
    if (Platform.OS === 'web') {
      return null;
    }
    
    const cacheDir = FileSystem.cacheDirectory;
    if (!cacheDir) return null;
    
    const filename = `${cacheDir}recipe_image_${recipeId}.png`;
    const fileInfo = await FileSystem.getInfoAsync(filename);
    
    if (fileInfo.exists) {
      return fileInfo.uri;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting cached image:', error);
    return null;
  }
}

/**
 * Get a placeholder image for a recipe while the real one is being generated
 * @param keyword A keyword to help select an appropriate food image
 * @returns URL to a placeholder image
 */
export function getPlaceholderFoodImage(keyword: string = ''): string {
  // Map of keywords to image URLs
  const foodImages: Record<string, string> = {
    'breakfast': 'https://images.pexels.com/photos/376464/pexels-photo-376464.jpeg?auto=compress&cs=tinysrgb&w=800',
    'lunch': 'https://images.pexels.com/photos/1640772/pexels-photo-1640772.jpeg?auto=compress&cs=tinysrgb&w=800',
    'dinner': 'https://images.pexels.com/photos/1279330/pexels-photo-1279330.jpeg?auto=compress&cs=tinysrgb&w=800',
    'salad': 'https://images.pexels.com/photos/257816/pexels-photo-257816.jpeg?auto=compress&cs=tinysrgb&w=800',
    'soup': 'https://images.pexels.com/photos/539451/pexels-photo-539451.jpeg?auto=compress&cs=tinysrgb&w=800',
    'sandwich': 'https://images.pexels.com/photos/1647163/pexels-photo-1647163.jpeg?auto=compress&cs=tinysrgb&w=800',
    'pasta': 'https://images.pexels.com/photos/1279330/pexels-photo-1279330.jpeg?auto=compress&cs=tinysrgb&w=800',
    'smoothie': 'https://images.pexels.com/photos/1092730/pexels-photo-1092730.jpeg?auto=compress&cs=tinysrgb&w=800',
    'bowl': 'https://images.pexels.com/photos/1640770/pexels-photo-1640770.jpeg?auto=compress&cs=tinysrgb&w=800',
    'healthy': 'https://images.pexels.com/photos/1640774/pexels-photo-1640774.jpeg?auto=compress&cs=tinysrgb&w=800',
    'protein': 'https://images.pexels.com/photos/3997309/pexels-photo-3997309.jpeg?auto=compress&cs=tinysrgb&w=800',
    'vegetarian': 'https://images.pexels.com/photos/1059905/pexels-photo-1059905.jpeg?auto=compress&cs=tinysrgb&w=800',
    'vegan': 'https://images.pexels.com/photos/1128678/pexels-photo-1128678.jpeg?auto=compress&cs=tinysrgb&w=800',
    'dessert': 'https://images.pexels.com/photos/2144112/pexels-photo-2144112.jpeg?auto=compress&cs=tinysrgb&w=800',
    'baked': 'https://images.pexels.com/photos/1070850/pexels-photo-1070850.jpeg?auto=compress&cs=tinysrgb&w=800',
    'grilled': 'https://images.pexels.com/photos/236887/pexels-photo-236887.jpeg?auto=compress&cs=tinysrgb&w=800',
    'roasted': 'https://images.pexels.com/photos/361184/asparagus-steak-veal-steak-veal-361184.jpeg?auto=compress&cs=tinysrgb&w=800',
    'chicken': 'https://images.pexels.com/photos/616354/pexels-photo-616354.jpeg?auto=compress&cs=tinysrgb&w=800',
    'fish': 'https://images.pexels.com/photos/262959/pexels-photo-262959.jpeg?auto=compress&cs=tinysrgb&w=800',
    'rice': 'https://images.pexels.com/photos/723198/pexels-photo-723198.jpeg?auto=compress&cs=tinysrgb&w=800',
    'quinoa': 'https://images.pexels.com/photos/5749107/pexels-photo-5749107.jpeg?auto=compress&cs=tinysrgb&w=800',
    'toast': 'https://images.pexels.com/photos/704569/pexels-photo-704569.jpeg?auto=compress&cs=tinysrgb&w=800',
  };

  // Look for matching keywords
  const lowerKeyword = keyword.toLowerCase();
  for (const [key, url] of Object.entries(foodImages)) {
    if (lowerKeyword.includes(key)) {
      return url;
    }
  }

  // Default images if no matching keyword
  const defaultImages = [
    'https://images.pexels.com/photos/1640774/pexels-photo-1640774.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/1640770/pexels-photo-1640770.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/1279330/pexels-photo-1279330.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/1640772/pexels-photo-1640772.jpeg?auto=compress&cs=tinysrgb&w=800',
  ];
  
  const randomIndex = Math.floor(Math.random() * defaultImages.length);
  return defaultImages[randomIndex];
}