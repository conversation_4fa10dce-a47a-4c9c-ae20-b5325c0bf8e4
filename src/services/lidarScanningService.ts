/**
 * LiDAR Scanning Service
 * Connects LiDAR scanning functionality to other app services (Vision, Nutrition, Database)
 */
import { Platform, Image } from 'react-native';
import { v4 as uuidv4 } from 'uuid';
import * as FileSystem from 'expo-file-system';
import { db } from '@/lib/firebase';
import { doc, setDoc, getDoc, addDoc, collection, query, where, getDocs, serverTimestamp } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';

// Import existing services
import { classifyFoodImage } from './classificationService';
import { analyzeNutrition } from './openaiNutritionService';
import { detectContainers, DeviceCapability } from '@/utils/volumeEstimator';
import { 
  calculateFoodWeight, 
  getFoodDensity, 
  enhancedFoodWeightCalculation,
  WeightCalibrationData 
} from '@/utils/foodDensityMap';
import * as MealHistory from '@/utils/mealHistoryManager';
import { ScanResult } from '@/utils/LiDAR/types';

interface FoodScanItem {
  id: string;
  name: string;
  volumeCm3: number;
  weightGrams: number;
  confidenceScore: number;
  boundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  imageBasedWeightEstimate?: number;
}

interface FoodScanRequest {
  imageUri: string;
  volumeData?: any;
  deviceHasLidar: boolean;
  calibrationFactor?: number;
  userId?: string;
  weightCalibrationData?: WeightCalibrationData;
}

interface NutritionRequest {
  foodItems: FoodScanItem[];
  mealType?: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  userId?: string;
}

/**
 * Process a food scan using LiDAR data or 2D estimation
 */
export async function processFoodScan(request: FoodScanRequest): Promise<ScanResult> {
  try {
    // Step 1: Classify food with Vision API
    const foodClassificationResults = await classifyFoodImage(request.imageUri);
    
    if (!foodClassificationResults || foodClassificationResults.length === 0) {
      throw new Error("Food classification failed");
    }
    
    // Step 2: Detect containers to exclude from volume estimates
    const containerResults = await detectContainers(request.imageUri);
    
    // Step 3: Get food item information (filtering out containers)
    const foodItems = foodClassificationResults
      .filter(result => {
        // Filter out items classified as containers
        const isContainer = containerResults.some(container => 
          container.isContainer && container.confidenceScore > 0.7
        );
        return !isContainer;
      })
      .map((result, index) => ({
        id: `food-${index}`,
        name: result.label,
        confidence: result.confidence,
        boundingBox: result.boundingBox
      }));
    
    // Step 4: Get image dimensions for 2D-based estimation
    const imageInfo = await new Promise<{width: number, height: number}>((resolve, reject) => {
      Image.getSize(
        request.imageUri, 
        (width, height) => resolve({width, height}),
        (error) => reject(error)
      );
    });
    
    // Step 5: Calculate volumes and weights
    const foodScanItems: FoodScanItem[] = [];
    let totalVolumeCm3 = 0;
    let totalWeightGrams = 0;
    
    // Get image-based weight estimates for all food items
    const imageBasedWeightEstimates = await getImageBasedWeightEstimation(
      request.imageUri, 
      foodItems.map(item => item.name)
    );
    
    // Process each food item
    for (const item of foodItems) {
      // Calculate volume using native module result or 2D estimation
      let volumeCm3 = 0;
      
      if (request.deviceHasLidar && request.volumeData) {
        // Apply calibration factor if available
        const calibrationFactor = request.calibrationFactor || 1.0;
        volumeCm3 = (request.volumeData.volumeCm3 / foodItems.length) * calibrationFactor;
      } else {
        // Fallback to 2D-based estimation
        const estimatedVolume = estimateVolumeFrom2D(
          item.name,
          item.boundingBox,
          imageInfo.width,
          imageInfo.height
        );
        volumeCm3 = estimatedVolume;
      }
      
      // Get image-based weight estimate for this food item
      const imageBasedWeight = imageBasedWeightEstimates[item.name];
      
      // Calculate weight using enhanced weight calculation
      const weightGrams = enhancedFoodWeightCalculation(
        item.name, 
        volumeCm3, 
        imageBasedWeight, 
        request.weightCalibrationData
      );
      
      // Add to totals
      totalVolumeCm3 += volumeCm3;
      totalWeightGrams += weightGrams;
      
      // Add to item list
      foodScanItems.push({
        id: item.id,
        name: item.name,
        volumeCm3,
        weightGrams,
        confidenceScore: item.confidence,
        boundingBox: item.boundingBox,
        imageBasedWeightEstimate: imageBasedWeight
      });
    }
    
    // Step 6: Get nutrition analysis with weight data
    const nutritionData = await getNutritionAnalysis({
      foodItems: foodScanItems,
      mealType: guessCurrentMealType()
    });
    
    // Step 7: Save scan to history
    const scanId = await saveScanToHistory(request.imageUri, foodScanItems, nutritionData, request.userId);
    
    // Step 8: Format and return result
    return {
      foods: foodScanItems.map(item => ({
        name: item.name,
        volumeCm3: item.volumeCm3,
        weightGrams: item.weightGrams
      })),
      totalVolumeCm3,
      totalWeightGrams,
      nutritionAnalysis: nutritionData,
      image: request.imageUri
    };
    
  } catch (error: any) {
    console.error('Food scan processing error:', error);
    throw new Error(`Failed to process food scan: ${error.message}`);
  }
}

/**
 * Estimate volume using 2D bounding box and food type
 */
function estimateVolumeFrom2D(
  foodName: string, 
  boundingBox?: {x: number, y: number, width: number, height: number},
  imageWidth?: number,
  imageHeight?: number
): number {
  if (!boundingBox || !imageWidth || !imageHeight) {
    // If we don't have bounding box data, return an approximation based on food type
    return estimateStandardPortion(foodName);
  }
  
  // Calculate the area of the bounding box in pixels
  const boxAreaPx = boundingBox.width * boundingBox.height;
  
  // Calculate the area of the entire image in pixels
  const imageAreaPx = imageWidth * imageHeight;
  
  // Calculate box area as percentage of total image
  const boxPercent = boxAreaPx / imageAreaPx;
  
  // Estimate volume based on the percentage of the image the food occupies
  // Base volume in cm³ - adjustable based on testing
  const BASE_VOLUME = 500;
  
  // Calculate base volume, applying a non-linear scale (sqrt) for better proportionality
  let estimatedVolume = BASE_VOLUME * Math.sqrt(boxPercent);
  
  // Apply food-specific adjustments based on shape
  const foodShape = determineFoodShape(foodName);
  
  // Shape-specific volume adjustment factors
  const shapeFactors = {
    'flat': 0.3,         // Flat foods have much less volume than area suggests
    'regular': 1.0,      // Standard shape with regular proportions
    'tall': 1.8,         // Tall foods have more volume than area suggests
    'irregularPile': 0.7, // Irregular piles have air gaps
    'spherical': 0.9,    // Spherical foods with predictable dimensions
    'cylindrical': 1.2,  // Cylindrical foods with predictable cross-section
    'liquid': 1.1        // Liquids conform to their container
  };
  
  // Apply shape-specific volume adjustment
  estimatedVolume *= shapeFactors[foodShape];
  
  // Apply additional adjustments for specific food types
  const lowerFoodName = foodName.toLowerCase();
  
  // Adjust for foods with specific density characteristics
  if (lowerFoodName.includes('foam') || lowerFoodName.includes('mousse')) {
    // Very light, airy foods
    estimatedVolume *= 0.6;
  } else if (lowerFoodName.includes('dense') || lowerFoodName.includes('compressed')) {
    // Dense, compressed foods
    estimatedVolume *= 1.3;
  }
  
  // Additional adjustments based on specific food preparation methods
  if (lowerFoodName.includes('fried') || lowerFoodName.includes('puffed')) {
    // Fried or puffed foods tend to have more volume but less density
    estimatedVolume *= 1.2;
  } else if (lowerFoodName.includes('mashed') || lowerFoodName.includes('pureed')) {
    // Mashed or pureed foods have fewer air pockets
    estimatedVolume *= 0.9;
  }
  
  return Math.round(estimatedVolume);
}

/**
 * Determine the shape category of a food item for better volume estimation
 */
function determineFoodShape(foodName: string): 'flat' | 'regular' | 'tall' | 'irregularPile' | 'spherical' | 'cylindrical' | 'liquid' {
  const normalizedName = foodName.toLowerCase();
  
  // Flat foods (height much less than width)
  if (
    normalizedName.includes('pizza') ||
    normalizedName.includes('pancake') ||
    normalizedName.includes('flatbread') ||
    normalizedName.includes('tortilla') ||
    normalizedName.includes('crepe') ||
    normalizedName.includes('toast') ||
    normalizedName.includes('slice') && (
      normalizedName.includes('bread') || 
      normalizedName.includes('cheese')
    ) ||
    normalizedName.includes('naan') ||
    normalizedName.includes('pita') ||
    normalizedName.includes('waffle') ||
    normalizedName.includes('omelette') ||
    normalizedName.includes('quesadilla')
  ) {
    return 'flat';
  }
  
  // Tall foods (height greater than width)
  if (
    normalizedName.includes('burger') ||
    normalizedName.includes('sandwich') ||
    normalizedName.includes('layered') ||
    normalizedName.includes('stacked') ||
    normalizedName.includes('cake') ||
    normalizedName.includes('lasagna') ||
    normalizedName.includes('casserole') ||
    normalizedName.includes('glass') ||
    normalizedName.includes('mug') ||
    normalizedName.includes('cup') ||
    normalizedName.includes('bottle') ||
    normalizedName.includes('burrito')
  ) {
    return 'tall';
  }
  
  // Spherical foods (roughly equal in all dimensions, round shape)
  if (
    normalizedName.includes('apple') ||
    normalizedName.includes('orange') ||
    normalizedName.includes('grapefruit') ||
    normalizedName.includes('kiwi') ||
    normalizedName.includes('plum') ||
    normalizedName.includes('peach') ||
    normalizedName.includes('tomato') ||
    normalizedName.includes('onion') ||
    normalizedName.includes('meatball') ||
    normalizedName.includes('egg') && !normalizedName.includes('white') ||
    normalizedName.includes('avocado') ||
    normalizedName.includes('potato') && !normalizedName.includes('fries') ||
    normalizedName.includes('ball')
  ) {
    return 'spherical';
  }
  
  // Cylindrical foods (roughly circular cross-section, longer in one dimension)
  if (
    normalizedName.includes('banana') ||
    normalizedName.includes('cucumber') ||
    normalizedName.includes('carrot') ||
    normalizedName.includes('corn') ||
    normalizedName.includes('sausage') ||
    normalizedName.includes('hot dog') ||
    normalizedName.includes('zucchini') ||
    normalizedName.includes('celery') ||
    normalizedName.includes('asparagus') ||
    normalizedName.includes('roll')
  ) {
    return 'cylindrical';
  }
  
  // Irregular pile foods (foods with irregular shape, often in a pile)
  if (
    normalizedName.includes('salad') ||
    normalizedName.includes('french fries') || 
    normalizedName.includes('fries') ||
    normalizedName.includes('chips') ||
    normalizedName.includes('popcorn') ||
    normalizedName.includes('rice') ||
    normalizedName.includes('cereal') ||
    normalizedName.includes('granola') ||
    normalizedName.includes('nuts') ||
    normalizedName.includes('beans') ||
    normalizedName.includes('pasta') && !normalizedName.includes('lasagna') ||
    normalizedName.includes('vegetables') ||
    normalizedName.includes('mixed')
  ) {
    return 'irregularPile';
  }
  
  // Liquid foods
  if (
    normalizedName.includes('soup') ||
    normalizedName.includes('stew') ||
    normalizedName.includes('sauce') ||
    normalizedName.includes('drink') ||
    normalizedName.includes('beverage') ||
    normalizedName.includes('milk') ||
    normalizedName.includes('juice') ||
    normalizedName.includes('water') ||
    normalizedName.includes('coffee') ||
    normalizedName.includes('tea') ||
    normalizedName.includes('yogurt') ||
    normalizedName.includes('smoothie') ||
    normalizedName.includes('shake') ||
    normalizedName.includes('broth')
  ) {
    return 'liquid';
  }
  
  // Default: regular shape (default case when no specific shape is identified)
  return 'regular';
}

/**
 * Estimate standard portion size for a food by name
 */
function estimateStandardPortion(foodName: string): number {
  // Simplified serving sizes in cm³
  const commonServingSizes: Record<string, number> = {
    'apple': 200,
    'banana': 150,
    'orange': 170,
    'chicken breast': 200,
    'chicken thigh': 120,
    'steak': 250,
    'burger': 130,
    'pizza slice': 120,
    'pasta': 300,
    'rice': 200,
    'bread slice': 50,
    'bagel': 150,
    'cereal': 40,
    'milk': 250,
    'yogurt': 170,
    'cheese': 30,
    'egg': 60,
    'potato': 200,
    'broccoli': 100,
    'carrot': 70,
    'avocado': 130,
    'salmon': 190,
    'ice cream': 100,
    'cake slice': 100,
    'cookie': 30,
    'chips': 40,
    'nuts': 30,
    'soup': 250,
    'salad': 150
  };
  
  // Find the best match
  const normalizedName = foodName.toLowerCase();
  for (const [key, volume] of Object.entries(commonServingSizes)) {
    if (normalizedName.includes(key) || key.includes(normalizedName)) {
      return volume;
    }
  }
  
  // Default volume if no match (150 cm³ is a medium-sized portion)
  return 150;
}

/**
 * Get nutrition analysis using OpenAI
 */
async function getNutritionAnalysis(request: NutritionRequest): Promise<any> {
  try {
    // Format food items with weights for the nutrition prompt
    const foodItemsDescription = request.foodItems
      .map(item => `${Math.round(item.weightGrams)} grams of ${item.name}`)
      .join(", ");
    
    // Create enhanced prompt with meal type if available
    const mealContext = request.mealType 
      ? `for ${request.mealType}` 
      : '';
    
    const enhancedPrompt = `A user ate a meal containing ${foodItemsDescription} ${mealContext}. 
    Provide detailed nutrition information including total calories, macronutrients (protein, fat, carbs), 
    and a brief health assessment. Also include individual calorie counts for each food item.`;
    
    // Call nutrition analysis service
    return await analyzeNutrition(enhancedPrompt);
  } catch (error: any) {
    console.error('Nutrition analysis error:', error);
    return {
      analysis: "Could not analyze nutrition information due to an error.",
      error: error.message
    };
  }
}

/**
 * Save scan to history and/or cloud storage
 */
async function saveScanToHistory(
  imageUri: string, 
  foodItems: FoodScanItem[], 
  nutritionData: any,
  userId?: string
): Promise<string> {
  // Generate unique ID for the scan
  const scanId = uuidv4();
  
  // Calculate totals
  const totalWeight = foodItems.reduce((sum, item) => sum + item.weightGrams, 0);
  
  // Extract calories if available
  const calorieMatch = nutritionData.analysis?.match(/(\d+)\s*calories/i);
  const totalCalories = calorieMatch && calorieMatch[1] ? parseInt(calorieMatch[1], 10) : undefined;
  
  // Create tags from food names
  const tags = Array.from(new Set(
    foodItems.flatMap(item => {
      const words = item.name.toLowerCase().split(/\s+/);
      return words.filter(word => word.length > 2); // Only include words longer than 2 chars
    })
  ));
  
  // Create meal record without ID
  const mealRecord = {
    timestamp: new Date().toISOString(),
    image: imageUri,
    foodItems: foodItems.map(item => ({
      foodName: item.name,
      volumeCm3: item.volumeCm3,
      weightGrams: item.weightGrams,
      measurementMethod: 'lidar',
      confidence: item.confidenceScore
    })),
    totalCalories,
    totalWeight,
    nutritionSummary: nutritionData.analysis?.substring(0, 200) + '...',
    userAdjusted: false,
    tags,
    mealType: guessCurrentMealType(),
    favorite: false
  };
  
  // Add the ID separately and save to history
  await MealHistory.saveMealScan({
    ...mealRecord,
    id: scanId
  } as MealHistory.MealScanRecord);
  
  
  if (userId) {
    try {
      // Upload image to storage if it's a local URI
      let cloudImageUrl = imageUri;
      if (imageUri.startsWith('file://')) {
        cloudImageUrl = await uploadImageToCloud(imageUri, userId, scanId);
      }
      
      // Save record to Firebase
      try {
        const scanDocRef = doc(db, 'food_scans', scanId);
        await setDoc(scanDocRef, {
          id: scanId,
          user_id: userId,
          timestamp: serverTimestamp(),
          image_url: cloudImageUrl,
          food_items: foodItems,
          total_calories: totalCalories,
          total_weight: totalWeight,
          nutrition_summary: nutritionData.analysis?.substring(0, 200),
          meal_type: guessCurrentMealType(),
          tags,
          created_at: new Date().toISOString()
        });
        
        console.log('Successfully saved scan to Firebase');
      } catch (saveError) {
        console.error('Error saving scan to Firebase:', saveError);
      }
    } catch (error: any) {
      console.error('Error in cloud sync:', error);
      // We still return the scan ID since it was saved locally
    }
  }
  
  return scanId;
}

/**
 * Upload image to cloud storage
 */
async function uploadImageToCloud(localUri: string, userId: string, scanId: string): Promise<string> {
  try {
    // Create path for image storage
    const path = `${userId}/food-scans/${scanId}.jpg`;
    
    // Get the storage reference
    const storage = getStorage();
    const storageRef = ref(storage, path);
    
    // Read the file as a blob
    const response = await fetch(localUri);
    const blob = await response.blob();
    
    // Upload file to Firebase Storage
    const snapshot = await uploadBytes(storageRef, blob, {
      contentType: 'image/jpeg',
      cacheControl: 'max-age=3600'
    });
    
    // Get the download URL
    const downloadUrl = await getDownloadURL(snapshot.ref);
    
    return downloadUrl;
  } catch (error: any) {
    console.error('Image upload error:', error);
    // Return the local URI if upload fails
    return localUri;
  }
}

/**
 * Guess meal type based on current time
 */
function guessCurrentMealType(): 'breakfast' | 'lunch' | 'dinner' | 'snack' {
  const hour = new Date().getHours();
  
  if (hour >= 5 && hour < 10) {
    return 'breakfast';
  } else if (hour >= 10 && hour < 15) {
    return 'lunch';
  } else if (hour >= 17 && hour < 22) {
    return 'dinner';
  } else {
    return 'snack';
  }
}

/**
 * Check if device can use LiDAR for food scanning
 */
export async function checkLiDARAvailability(): Promise<{
  available: boolean;
  reason?: string;
  fallbackAvailable: boolean;
}> {
  // Only iOS devices can have LiDAR
  if (Platform.OS !== 'ios') {
    return {
      available: false,
      reason: 'LiDAR scanning is only available on iOS devices',
      fallbackAvailable: true
    };
  }
  
  try {
    // Import ARKit native module dynamically
    const { ARKitVolumeScanner } = require('react-native').NativeModules;
    
    // Check if device supports LiDAR scanning
    const isLiDARSupported = await ARKitVolumeScanner.isLiDARSupported();
    
    if (!isLiDARSupported) {
      return {
        available: false,
        reason: 'Your device does not have a LiDAR sensor',
        fallbackAvailable: true
      };
    }
    
    return {
      available: true,
      fallbackAvailable: true
    };
  } catch (error: any) {
    console.error('Error checking LiDAR availability:', error);
    return {
      available: false,
      reason: 'Error detecting LiDAR capabilities',
      fallbackAvailable: true
    };
  }
}

/**
 * Get calibration data from cloud storage for a user
 */
export async function syncCalibrationData(userId: string): Promise<Record<string, number> | null> {
  if (!userId) return null;
  
  try {
    // Get calibration document from Firebase
    const calibrationDocRef = doc(db, 'lidar_calibrations', userId);
    const calibrationDoc = await getDoc(calibrationDocRef);
    
    if (!calibrationDoc.exists()) {
      return null;
    }
    
    const data = calibrationDoc.data();
    
    // Ensure we return a valid Record<string, number> or null
    if (data?.calibration_data && typeof data.calibration_data === 'object') {
      return data.calibration_data as Record<string, number>;
    }
    
    return null;
  } catch (error: any) {
    console.error('Error syncing calibration data:', error);
    return null;
  }
}

/**
 * Save calibration data to cloud storage
 */
export async function saveCalibrationToCloud(
  userId: string,
  calibrationData: Record<string, number>
): Promise<boolean> {
  if (!userId) return false;
  
  try {
    // Get calibration document reference
    const calibrationDocRef = doc(db, 'lidar_calibrations', userId);
    
    // Check if document exists
    const existingDoc = await getDoc(calibrationDocRef);
    
    if (existingDoc.exists()) {
      // Update existing record
      await setDoc(calibrationDocRef, {
        calibration_data: calibrationData,
        updated_at: serverTimestamp()
      }, { merge: true });
    } else {
      // Create new record
      await setDoc(calibrationDocRef, {
        user_id: userId,
        calibration_data: calibrationData,
        created_at: serverTimestamp(),
        updated_at: serverTimestamp()
      });
    }
    
    return true;
  } catch (error: any) {
    console.error('Error saving calibration to cloud:', error);
    return false;
  }
}

export async function getImageBasedWeightEstimation(
  imageUri: string,
  foodNames: string[]
): Promise<Record<string, number>> {
  // Placeholder implementation
  return foodNames.reduce((acc, name) => {
    acc[name] = 100; // Default 100g estimate
    return acc;
  }, {} as Record<string, number>);
} 