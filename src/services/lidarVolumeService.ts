

import { useDatabaseType } from '@/contexts/DatabaseContext';
import { httpsCallable, getFunctions } from 'firebase/functions';

/**
 * Interface for a food segment in an image
 */
export interface FoodSegment {
  id: string;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  mask?: number[][]; // Binary mask for pixel-level segmentation
  confidence: number;
  estimatedVolumeCm3?: number;
}

/**
 * Interface for volume distribution request
 */
export interface DistributeVolumeRequest {
  totalVolumeCm3: number;
  segments: FoodSegment[];
  userId?: string;
}

/**
 * Interface for volume distribution response
 */
export interface DistributeVolumeResponse {
  segments: FoodSegment[];
}

/**
 * Service for LiDAR volume distribution
 */
export function useLidarVolumeService() {
  const { useFirebase } = useDatabaseType();

  /**
   * Distribute total volume across food segments
   */
  const distributeVolume = async (
    request: DistributeVolumeRequest
  ): Promise<DistributeVolumeResponse> => {
    try {
      // Always use Firebase Cloud Function regardless of database type
      const functions = getFunctions();
      const distributeLidarVolumeFn = httpsCallable<
        DistributeVolumeRequest,
        DistributeVolumeResponse
      >(functions, 'distributeLidarVolume');

      const result = await distributeLidarVolumeFn(request);
      return result.data;
    } catch (error) {
      console.error('Error in lidarVolumeService:', error);
      throw error;
    }
  };

  /**
   * Calculate volume estimate for a single food item
   * This is a utility function that handles the case of a single segment
   */
  const calculateSingleItemVolume = async (
    segment: FoodSegment,
    totalVolumeCm3: number,
    userId?: string
  ): Promise<FoodSegment> => {
    // If it's just one segment, we can directly set the volume without calling the function
    if (!segment.estimatedVolumeCm3) {
      // But if there's a userId, we still want to apply user preferences, so use the function
      if (userId) {
        const result = await distributeVolume({
          totalVolumeCm3,
          segments: [segment],
          userId
        });
        return result.segments[0];
      } else {
        // Simple case: just assign the total volume
        return {
          ...segment,
          estimatedVolumeCm3: totalVolumeCm3
        };
      }
    }
    return segment;
  };

  return {
    distributeVolume,
    calculateSingleItemVolume
  };
} 