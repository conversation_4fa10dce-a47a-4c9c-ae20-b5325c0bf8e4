import { getUserProfile } from './nutritionGoalService';
import { FoodItem, Meal } from '@/types/food';
import { getCachedMealScore, cacheMealScore } from './cachingService';
import { analyzeFood } from './openai/secureApiClient';

interface MealHealthScoreResult {
  score: number; // 0-100 score
  explanation: string; // Detailed explanation of the score
  macroAnalysis: {
    protein: string; // e.g. "high", "moderate", "low"
    carbs: string;
    fat: string;
    fiber: string;
  };
  strengths: string[]; // What's good about this meal
  improvements: string[]; // What could be improved
  goalAlignment: string; // How well it aligns with user goals
}

/**
 * Generate a health score for a meal based on its nutritional content and user's goals
 */
export async function generateMealHealthScore(
  meal: Meal | { 
    id: string; 
    name: string; 
    items: FoodItem[]; 
    totalCalories: number;
    totalProtein: number;
    totalCarbs: number;
    totalFat: number;
  }
): Promise<MealHealthScoreResult> {
  // Get user profile for personalized scoring outside try/catch scope
  let userProfile;
  try {
    // Check cache first
    const cachedScore = await getCachedMealScore(meal.id);
    if (cachedScore) {
      console.log('Using cached meal score');
      return cachedScore;
    }
    
    // Get user profile for personalized scoring
    userProfile = await getUserProfile();
    
    // Use OpenAI via Firebase Functions
    console.log('Using OpenAI via Firebase Functions for meal scoring');
    
    // Prepare macronutrient ratios (percentage of total calories)
    const totalCals = meal.totalCalories;
    const proteinCals = meal.totalProtein * 4;
    const carbsCals = meal.totalCarbs * 4;
    const fatCals = meal.totalFat * 9;
    
    const proteinPercent = Math.round((proteinCals / totalCals) * 100);
    const carbsPercent = Math.round((carbsCals / totalCals) * 100);
    const fatPercent = Math.round((fatCals / totalCals) * 100);
    
    // Create system prompt
    const systemPrompt = `You are a nutrition expert that analyzes meals and provides health scores on a scale of 0-100, where:
- 0-20: Very unhealthy, significant nutritional concerns
- 21-40: Poor nutritional value with few redeeming qualities
- 41-60: Average nutritional value, some positives and negatives
- 61-80: Good nutritional value with some room for improvement
- 81-100: Excellent nutritional value, balanced and aligned with health goals

Analyze the meal's macronutrient distribution, ingredients, and alignment with the user's health goals.
Your response must be in JSON format with the following structure:
{
  "score": number, // 0-100 score
  "explanation": string, // Brief explanation of the score in 1-2 short sentences
  "macroAnalysis": {
    "protein": string, // "high", "moderate", or "low"
    "carbs": string, // "high", "moderate", or "low"
    "fat": string, // "high", "moderate", or "low"
    "fiber": string // "high", "moderate", or "low", based on estimated fiber content
  },
  "strengths": string[], // 2-3 bullet points of positives about the meal
  "improvements": string[], // 2-3 bullet points of suggested improvements
  "goalAlignment": string // Brief statement on how well this aligns with the user's stated goals
}`;

    // Create user prompt with meal info and user goals
    const userPrompt = `Please analyze this meal and provide a health score (0-100):

Meal name: ${meal.name}
Total calories: ${meal.totalCalories} kcal
Protein: ${meal.totalProtein}g (${proteinPercent}% of calories)
Carbs: ${meal.totalCarbs}g (${carbsPercent}% of calories)
Fat: ${meal.totalFat}g (${fatPercent}% of calories)

Ingredients: ${meal.items.map(item => item.name).join(', ')}

${userProfile ? `User's health goals: ${userProfile.goalType}
User's dietary preferences: ${userProfile.dietaryPreference || 'None specified'}
User's daily calorie target: ${userProfile.calorieGoal} kcal
User's daily protein target: ${userProfile.proteinGoal}g` : 'No user profile available, provide a general analysis.'}`;

    // Create a dummy image for text-only analysis
    const dummyImageUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    
    // Combine system and user prompts for the Firebase function
    const combinedPrompt = `${systemPrompt}

${userPrompt}

IMPORTANT: Return your response as a valid JSON object with this exact structure:
{
  "score": <number 0-100>,
  "explanation": "<string>",
  "macroAnalysis": {
    "protein": "<string>",
    "carbs": "<string>",
    "fat": "<string>",
    "fiber": "<string>"
  },
  "strengths": ["<string>", ...],
  "improvements": ["<string>", ...],
  "goalAlignment": "<string>"
}`;

    try {
      const response = await analyzeFood(dummyImageUrl, combinedPrompt);
      
      if (!response.success) {
        throw new Error('OpenAI analysis via Firebase Functions failed');
      }
      
      // Extract meal score from the response
      let mealHealthInfo = null;
      if (response.healthHighlights) {
        // Try to extract from healthHighlights first
        mealHealthInfo = response.healthHighlights;
      } else if (response.description) {
        // Try to parse from description if it contains JSON
        try {
          mealHealthInfo = JSON.parse(response.description);
        } catch (e) {
          // If not parseable, create a basic structure
          mealHealthInfo = {
            score: 75,
            explanation: response.description,
            macroAnalysis: {
              protein: "moderate",
              carbs: "moderate",
              fat: "moderate",
              fiber: "unknown"
            },
            strengths: ["Analyzed meal"],
            improvements: ["Consider portion sizes"],
            goalAlignment: "General assessment"
          };
        }
      }
      
      const scoreResult: MealHealthScoreResult = mealHealthInfo as MealHealthScoreResult;
      
      // Cache the result
      cacheMealScore(meal.id, scoreResult);
      
      return scoreResult;
    } catch (error) {
      console.error('Error making OpenAI API request via Firebase Functions:', error);
      return generateFallbackScore(meal, userProfile);
    }
  } catch (error) {
    console.error('Error generating meal health score:', error);
    return generateFallbackScore(meal, userProfile);
  }
}

/**
 * Generate a fallback score when the API call fails
 */
function generateFallbackScore(
  meal: Meal | { 
    id: string; 
    name: string; 
    items: FoodItem[]; 
    totalCalories: number;
    totalProtein: number;
    totalCarbs: number;
    totalFat: number;
  },
  userProfile?: any
): MealHealthScoreResult {
  // Calculate base score using simple heuristics
  let baseScore = 50; // Start with average score
  
  // Calculate macronutrient percentages
  const totalCals = meal.totalCalories;
  const proteinCals = meal.totalProtein * 4;
  const carbsCals = meal.totalCarbs * 4;
  const fatCals = meal.totalFat * 9;
  
  const proteinPercent = (proteinCals / totalCals) * 100;
  const carbsPercent = (carbsCals / totalCals) * 100;
  const fatPercent = (fatCals / totalCals) * 100;
  
  // Analyze protein content
  let proteinRating = 'moderate';
  if (proteinPercent >= 30) {
    proteinRating = 'high';
    baseScore += 10;
  } else if (proteinPercent <= 15) {
    proteinRating = 'low';
    baseScore -= 5;
  }
  
  // Analyze carbs content
  let carbsRating = 'moderate';
  if (carbsPercent >= 60) {
    carbsRating = 'high';
    baseScore -= 5;
  } else if (carbsPercent <= 30) {
    carbsRating = 'low';
    baseScore += 5; // Lower carbs often aligns with modern nutrition guidance
  }
  
  // Analyze fat content
  let fatRating = 'moderate';
  if (fatPercent >= 40) {
    fatRating = 'high';
    baseScore -= 5;
  } else if (fatPercent <= 20) {
    fatRating = 'low';
    baseScore += 5;
  }
  
  // Estimate fiber content based on ingredients
  const hasFiber = meal.items.some(item => 
    /vegetable|fruit|grain|bean|legume|nut|seed|fiber|broccoli|spinach|kale|avocado|berry/i.test(item.name)
  );
  
  let fiberRating = 'low';
  if (hasFiber) {
    const fiberRichCount = meal.items.filter(item => 
      /vegetable|fruit|grain|bean|legume|nut|seed|fiber|broccoli|spinach|kale|avocado|berry/i.test(item.name)
    ).length;
    
    if (fiberRichCount >= 3) {
      fiberRating = 'high';
      baseScore += 10;
    } else {
      fiberRating = 'moderate';
      baseScore += 5;
    }
  } else {
    baseScore -= 10;
  }
  
  // Calculate strengths and improvements
  const strengths: string[] = [];
  const improvements: string[] = [];
  
  if (proteinRating === 'high') {
    strengths.push('High in protein, which supports muscle maintenance and satiety');
  } else if (proteinRating === 'low') {
    improvements.push('Could benefit from more protein sources');
  }
  
  if (fiberRating === 'high') {
    strengths.push('Good fiber content from plant-based ingredients');
  } else if (fiberRating === 'low') {
    improvements.push('Adding more vegetables or whole grains would increase fiber content');
  }
  
  if (fatRating === 'low' && meal.items.some(item => /olive oil|avocado|nut|seed|fish/i.test(item.name))) {
    strengths.push('Contains healthy fats from quality sources');
  } else if (fatRating === 'high') {
    improvements.push('Consider reducing overall fat content or focusing on healthier fat sources');
  }
  
  // If we don't have enough strengths, add a generic one
  if (strengths.length < 2) {
    strengths.push('Provides a mix of macronutrients for energy');
  }
  
  // If we don't have enough improvements, add a generic one
  if (improvements.length < 2) {
    improvements.push('Consider adding more variety of whole foods for a broader nutrient profile');
  }
  
  // Ensure the score stays within 0-100 range
  baseScore = Math.max(0, Math.min(100, baseScore));
  
  // Goal alignment statement
  let goalAlignment = 'This meal provides a moderate nutritional profile suitable for general health maintenance.';
  
  if (userProfile) {
    const goalType = userProfile.goalType?.toLowerCase() || '';
    
    if (goalType.includes('weight loss') && meal.totalCalories < (userProfile.calorieGoal / 3)) {
      goalAlignment = 'This meal supports your weight loss goals by providing nutrition with controlled calories.';
    } else if (goalType.includes('muscle') && proteinRating === 'high') {
      goalAlignment = 'This meal supports your muscle building goals with its high protein content.';
    } else if (goalType.includes('maintenance') && baseScore > 60) {
      goalAlignment = 'This meal aligns well with your goal of maintaining a balanced and healthy diet.';
    }
  }
  
  return {
    score: Math.round(baseScore),
    explanation: `This meal scores ${Math.round(baseScore)}/100 with ${proteinRating} protein, ${carbsRating} carbs, and ${fiberRating} estimated fiber content.`,
    macroAnalysis: {
      protein: proteinRating,
      carbs: carbsRating,
      fat: fatRating,
      fiber: fiberRating
    },
    strengths,
    improvements,
    goalAlignment
  };
} 