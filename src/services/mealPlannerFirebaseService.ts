
export async function generateMealPlan(
  date: string, 
  nutritionGoal: NutritionGoal,
  userId: string
): Promise<MealPlan> {
  // Check if we already have a plan for this date
  const existingPlan = await getMealPlan(date, userId);
  if (existingPlan) {
    return existingPlan;
  }
  
  // Check cache
  try {
    const cacheKey = `${CACHE_KEY_PREFIX}${date}_${nutritionGoal.calories}_${nutritionGoal.protein}_${nutritionGoal.carbs}_${nutritionGoal.fat}`;
    const cachedPlan = localStorage.getItem(cacheKey);
    if (cachedPlan) {
      const parsedPlan = JSON.parse(cachedPlan);
      const cacheTimestamp = parsedPlan._timestamp || 0;
      const now = Date.now();
      // Cache is valid for CACHE_EXPIRY_DAYS
      if (now - cacheTimestamp < CACHE_EXPIRY_DAYS * 24 * 60 * 60 * 1000) {
        console.log('Using cached meal plan for', date);
        delete parsedPlan._timestamp; // Remove timestamp before returning
        return parsedPlan;
      }
      // Cache expired, generate new meal plan
    }
  } catch (cacheError) {
    console.error('Error checking meal plan cache:', cacheError);
    // Continue with generation if cache check fails
  }

  // Get user preferences
  const userPreferences = await getUserPreferences(userId);

  // Get user's meal history to avoid repetition
  const last7Days = new Date();
  last7Days.setDate(last7Days.getDate() - 7);
  const today = new Date();
  const lastWeek = formatDate(last7Days);
  const todayFormatted = formatDate(today);
  
  const recentPlans = await getMealPlansForDateRange(lastWeek, todayFormatted, userId);
  
  // Extract meal names from recent plans to avoid repetition
  let recentMeals: string[] = [];
  recentPlans.forEach(plan => {
    recentMeals.push(plan.meals.breakfast.name);
    recentMeals.push(plan.meals.lunch.name);
    recentMeals.push(plan.meals.dinner.name);
    plan.meals.snacks.forEach(snack => recentMeals.push(snack.name));
  });
  
  // Get API key
  const apiKey = OPENAI_API_KEY?.replace('_openai_api_key_here', '');
  
  if (!apiKey) {
    return generateFallbackMealPlan(date, nutritionGoal, userPreferences, recentMeals);
  }

  // System prompt for OpenAI
  const systemPrompt = `You are a professional nutritionist and chef who creates personalized meal plans.
Your task is to create a full day's meal plan that meets the user's nutritional goals and preferences.

Follow these requirements:
1. Create a breakfast, lunch, dinner, and 1-2 snacks that meet the nutritional targets
2. Meals should be varied, balanced, and practical to prepare
3. Consider dietary preferences, allergies, and recently eaten meals to avoid repetition
4. Provide accurate nutritional information for each meal and the complete day
5. Include a brief description for each meal and a list of required ingredients
6. Sum up the total calories, protein, carbs, and fat for the day

Format your response as a JSON object exactly matching this structure:
{
  "date": "YYYY-MM-DD",
  "meals": {
    "breakfast": {
      "name": "Meal name",
      "description": "Brief description",
      "ingredients": ["ingredient 1", "ingredient 2", "..."],
      "nutrition": {
        "calories": 0,
        "protein": 0,
        "carbs": 0,
        "fat": 0
      },
      "preparation": "Brief preparation steps",
      "mealType": "breakfast"
    },
    "lunch": { /* same structure as breakfast */ },
    "dinner": { /* same structure as breakfast */ },
    "snacks": [
      { /* same structure as breakfast, with mealType: "snack" */ }
    ]
  },
  "nutritionSummary": {
    "calories": 0,
    "protein": 0,
    "carbs": 0,
    "fat": 0
  }
}`;

  // User prompt with specific details
  const userPrompt = `Please create a meal plan for ${date} with these nutritional targets:
- Calories: ${nutritionGoal.calories} kcal
- Protein: ${nutritionGoal.protein}g
- Carbs: ${nutritionGoal.carbs}g
- Fat: ${nutritionGoal.fat}g
${nutritionGoal.dietary && nutritionGoal.dietary.length > 0 ? `- Dietary preferences: ${nutritionGoal.dietary.join(', ')}` : ''}

User preferences:
- Allergies: ${userPreferences.allergies.length > 0 ? userPreferences.allergies.join(', ') : 'None'}
- Disliked foods: ${userPreferences.dislikedFoods.length > 0 ? userPreferences.dislikedFoods.join(', ') : 'None'}
- Favorite ingredients: ${userPreferences.favoriteIngredients.length > 0 ? userPreferences.favoriteIngredients.join(', ') : 'Not specified'}
- Cuisine preferences: ${userPreferences.cuisinePreferences.length > 0 ? userPreferences.cuisinePreferences.join(', ') : 'Not specified'}
- Preferred meal complexity: ${userPreferences.mealComplexity}

Recently eaten meals (please avoid repetition):
${recentMeals.length > 0 ? recentMeals.slice(0, 10).join(', ') : 'No recent meal history available'}`;

  try {
    // Call OpenAI API
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: OPENAI_MODEL || 'gpt-4o',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: 2000,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json() as {
      choices?: {
        message?: {
          content?: string;
        };
      }[];
    };
    
    if (!result.choices || !result.choices[0] || !result.choices[0].message || !result.choices[0].message.content) {
      throw new Error('Invalid response format from OpenAI API');
    }

    // Parse the response
    const mealPlanData = JSON.parse(result.choices[0].message.content) as MealPlan;
    
    // Validate meal plan data
    validateMealPlan(mealPlanData);
    
    // Save the meal plan to Firebase
    const savedPlan = await saveMealPlan(mealPlanData, userId);
    
    // Cache the result
    try {
      const cacheKey = `${CACHE_KEY_PREFIX}${date}_${nutritionGoal.calories}_${nutritionGoal.protein}_${nutritionGoal.carbs}_${nutritionGoal.fat}`;
      // Add timestamp to track cache freshness
      const cacheData = { ...mealPlanData, _timestamp: Date.now() };
      localStorage.setItem(cacheKey, JSON.stringify(cacheData));
    } catch (cacheError) {
      console.error('Error caching meal plan:', cacheError);
    }
    
    return savedPlan;
  } catch (error) {
    console.error('Error generating meal plan with OpenAI:', error);
    // Fallback to locally generated meal plan
    const fallbackPlan = generateFallbackMealPlan(date, nutritionGoal, userPreferences, recentMeals);
    
    // Save the fallback plan to Firebase
    try {
      await saveMealPlan(fallbackPlan, userId);
    } catch (saveError) {
      console.error('Error saving fallback meal plan:', saveError);
    }
    
    return fallbackPlan;
  }
}

/**
 * Validate meal plan data structure
 */
function validateMealPlan(mealPlanData: any): void {
  // Validate basic structure
  if (!mealPlanData.date || !mealPlanData.meals || !mealPlanData.nutritionSummary) {
    throw new Error('Meal plan data is missing required fields');
  }
  
  // Validate meals
  if (!mealPlanData.meals.breakfast || !mealPlanData.meals.lunch || !mealPlanData.meals.dinner) {
    throw new Error('Meal plan is missing one or more required meals');
  }
  
  // Ensure snacks array exists
  if (!mealPlanData.meals.snacks) {
    mealPlanData.meals.snacks = [];
  }
  
  // Validate nutrition summary
  if (!mealPlanData.nutritionSummary.calories || 
      !mealPlanData.nutritionSummary.protein || 
      !mealPlanData.nutritionSummary.carbs || 
      !mealPlanData.nutritionSummary.fat) {
    throw new Error('Nutrition summary is missing required fields');
  }
  
  // Validate each meal
  const validateMeal = (meal: any, mealType: string) => {
    if (!meal.name || !meal.ingredients || !meal.nutrition) {
      throw new Error(`${mealType} is missing required fields`);
    }
    
    if (!meal.nutrition.calories || !meal.nutrition.protein || 
        !meal.nutrition.carbs || !meal.nutrition.fat) {
      throw new Error(`${mealType} nutrition data is incomplete`);
    }
    
    // Ensure ingredients is an array
    if (!Array.isArray(meal.ingredients)) {
      meal.ingredients = [meal.ingredients];
    }
    
    // Set meal type if not present
    if (!meal.mealType) {
      meal.mealType = mealType === 'snacks' ? 'snack' : mealType;
    }
  };
  
  validateMeal(mealPlanData.meals.breakfast, 'breakfast');
  validateMeal(mealPlanData.meals.lunch, 'lunch');
  validateMeal(mealPlanData.meals.dinner, 'dinner');
  
  mealPlanData.meals.snacks.forEach((snack: any, index: number) => {
    validateMeal(snack, `snack ${index + 1}`);
  });
}

/**
 * Generate a fallback meal plan when OpenAI API is unavailable
 */
function generateFallbackMealPlan(
  date: string, 
  nutritionGoal: NutritionGoal,
  userPreferences: UserPreferences, 
  recentMeals: string[]
): MealPlan {
  // Set of predefined meals for fallback
  const breakfastOptions = [
    {
      name: "Greek Yogurt Parfait",
      description: "Creamy Greek yogurt layered with fresh berries and honey",
      ingredients: ["Greek yogurt", "mixed berries", "honey", "granola", "chia seeds"],
      nutrition: { calories: 350, protein: 20, carbs: 45, fat: 10 },
      preparation: "Layer yogurt with berries, sprinkle with granola and seeds, drizzle with honey",
      mealType: "breakfast" as const
    },
    {
      name: "Avocado Toast with Egg",
      description: "Whole grain toast with smashed avocado and poached egg",
      ingredients: ["whole grain bread", "avocado", "eggs", "cherry tomatoes", "salt", "pepper", "red pepper flakes"],
      nutrition: { calories: 380, protein: 15, carbs: 30, fat: 22 },
      preparation: "Toast bread, spread with mashed avocado, top with poached egg and seasonings",
      mealType: "breakfast" as const
    },
    {
      name: "Protein Smoothie Bowl",
      description: "Protein-packed smoothie topped with fruits and nuts",
      ingredients: ["protein powder", "banana", "almond milk", "spinach", "berries", "sliced almonds", "chia seeds"],
      nutrition: { calories: 320, protein: 25, carbs: 40, fat: 8 },
      preparation: "Blend protein powder with banana, milk, and spinach. Pour into bowl and top with remaining ingredients",
      mealType: "breakfast" as const
    }
  ];
  
  const lunchOptions = [
    {
      name: "Mediterranean Chickpea Salad",
      description: "Fresh salad with chickpeas, vegetables, and feta cheese",
      ingredients: ["chickpeas", "cucumber", "cherry tomatoes", "red onion", "bell pepper", "feta cheese", "olive oil", "lemon juice", "herbs"],
      nutrition: { calories: 420, protein: 18, carbs: 50, fat: 15 },
      preparation: "Combine all ingredients, toss with olive oil and lemon juice, season to taste",
      mealType: "lunch" as const
    },
    {
      name: "Grilled Chicken Wrap",
      description: "Grilled chicken with vegetables wrapped in a whole wheat tortilla",
      ingredients: ["chicken breast", "whole wheat tortilla", "lettuce", "tomato", "avocado", "Greek yogurt", "mustard"],
      nutrition: { calories: 450, protein: 35, carbs: 40, fat: 18 },
      preparation: "Grill chicken, spread tortilla with yogurt and mustard, add chicken and vegetables, wrap",
      mealType: "lunch" as const
    },
    {
      name: "Quinoa Buddha Bowl",
      description: "Nutrient-rich bowl with quinoa, roasted vegetables, and tahini dressing",
      ingredients: ["quinoa", "sweet potato", "broccoli", "chickpeas", "avocado", "tahini", "lemon juice", "garlic"],
      nutrition: { calories: 480, protein: 20, carbs: 60, fat: 20 },
      preparation: "Roast vegetables, cook quinoa, arrange in bowl with chickpeas and sliced avocado, drizzle with tahini sauce",
      mealType: "lunch" as const
    }
  ];
  
  const dinnerOptions = [
    {
      name: "Baked Salmon with Roasted Vegetables",
      description: "Herb-crusted salmon fillet with colorful roasted vegetables",
      ingredients: ["salmon fillet", "asparagus", "cherry tomatoes", "zucchini", "olive oil", "lemon", "herbs", "garlic"],
      nutrition: { calories: 420, protein: 35, carbs: 20, fat: 22 },
      preparation: "Season salmon, arrange with vegetables on baking sheet, bake until salmon is flaky",
      mealType: "dinner" as const
    },
    {
      name: "Vegetable and Bean Chili",
      description: "Hearty plant-based chili with beans and vegetables",
      ingredients: ["kidney beans", "black beans", "onion", "bell pepper", "tomatoes", "corn", "chili powder", "cumin", "brown rice"],
      nutrition: { calories: 380, protein: 18, carbs: 65, fat: 6 },
      preparation: "Sauté vegetables, add beans and seasonings, simmer until flavors meld, serve over rice",
      mealType: "dinner" as const
    },
    {
      name: "Turkey Meatballs with Zucchini Noodles",
      description: "Lean turkey meatballs with garlic-infused zucchini noodles",
      ingredients: ["ground turkey", "egg", "almond flour", "Italian seasoning", "zucchini", "marinara sauce", "garlic", "olive oil"],
      nutrition: { calories: 450, protein: 40, carbs: 15, fat: 25 },
      preparation: "Form and bake meatballs, spiralize zucchini, sauté with garlic, top with meatballs and sauce",
      mealType: "dinner" as const
    }
  ];
  
  const snackOptions = [
    {
      name: "Apple with Almond Butter",
      description: "Crisp apple slices with creamy almond butter",
      ingredients: ["apple", "almond butter"],
      nutrition: { calories: 180, protein: 5, carbs: 20, fat: 10 },
      preparation: "Slice apple and serve with a side of almond butter for dipping",
      mealType: "snack" as const
    },
    {
      name: "Greek Yogurt with Berries",
      description: "Protein-rich Greek yogurt topped with fresh berries",
      ingredients: ["Greek yogurt", "mixed berries", "honey"],
      nutrition: { calories: 150, protein: 15, carbs: 15, fat: 3 },
      preparation: "Top yogurt with berries and a drizzle of honey",
      mealType: "snack" as const
    },
    {
      name: "Hummus with Vegetable Sticks",
      description: "Creamy hummus with fresh vegetable sticks for dipping",
      ingredients: ["hummus", "carrot sticks", "cucumber sticks", "bell pepper sticks"],
      nutrition: { calories: 160, protein: 6, carbs: 15, fat: 8 },
      preparation: "Serve hummus with prepared vegetable sticks",
      mealType: "snack" as const
    }
  ];
  
  // Pseudo-random selection based on date string to ensure consistency for the same date
  const dateNum = parseInt(date.replace(/-/g, ''));
  const getOption = (options: any[], offset: number = 0) => {
    return options[(dateNum + offset) % options.length];
  };
  
  // Build the meal plan
  const breakfast = getOption(breakfastOptions);
  const lunch = getOption(lunchOptions, 1);
  const dinner = getOption(dinnerOptions, 2);
  const snack1 = getOption(snackOptions, 3);
  const snack2 = getOption(snackOptions, 4);
  
  // Calculate total nutrition
  const totalNutrition = {
    calories: breakfast.nutrition.calories + lunch.nutrition.calories + dinner.nutrition.calories + snack1.nutrition.calories + snack2.nutrition.calories,
    protein: breakfast.nutrition.protein + lunch.nutrition.protein + dinner.nutrition.protein + snack1.nutrition.protein + snack2.nutrition.protein,
    carbs: breakfast.nutrition.carbs + lunch.nutrition.carbs + dinner.nutrition.carbs + snack1.nutrition.carbs + snack2.nutrition.carbs,
    fat: breakfast.nutrition.fat + lunch.nutrition.fat + dinner.nutrition.fat + snack1.nutrition.fat + snack2.nutrition.fat
  };
  
  // Ensure total is close to user's goal by scaling
  const calorieRatio = nutritionGoal.calories / totalNutrition.calories;
  const snacks = [snack1];
  
  // Only add second snack if needed to reach calorie goal
  if (calorieRatio > 1.2) {
    snacks.push(snack2);
  }
  
  // Adjust portion sizes to better match nutrition goals
  const adjustedBreakfast = scaleNutrition(breakfast, calorieRatio);
  const adjustedLunch = scaleNutrition(lunch, calorieRatio);
  const adjustedDinner = scaleNutrition(dinner, calorieRatio);
  const adjustedSnacks = snacks.map(snack => scaleNutrition(snack, calorieRatio));
  
  // Recalculate total nutrition with adjusted portions
  const adjustedTotalNutrition = {
    calories: adjustedBreakfast.nutrition.calories + adjustedLunch.nutrition.calories + adjustedDinner.nutrition.calories + 
              adjustedSnacks.reduce((sum, snack) => sum + snack.nutrition.calories, 0),
    protein: adjustedBreakfast.nutrition.protein + adjustedLunch.nutrition.protein + adjustedDinner.nutrition.protein + 
             adjustedSnacks.reduce((sum, snack) => sum + snack.nutrition.protein, 0),
    carbs: adjustedBreakfast.nutrition.carbs + adjustedLunch.nutrition.carbs + adjustedDinner.nutrition.carbs + 
           adjustedSnacks.reduce((sum, snack) => sum + snack.nutrition.carbs, 0),
    fat: adjustedBreakfast.nutrition.fat + adjustedLunch.nutrition.fat + adjustedDinner.nutrition.fat + 
         adjustedSnacks.reduce((sum, snack) => sum + snack.nutrition.fat, 0)
  };
  
  return {
    date,
    meals: {
      breakfast: adjustedBreakfast,
      lunch: adjustedLunch,
      dinner: adjustedDinner,
      snacks: adjustedSnacks
    },
    nutritionSummary: adjustedTotalNutrition
  };
}

/**
 * Scale nutrition values by a factor to adjust portion sizes
 */
function scaleNutrition(meal: Meal, factor: number): Meal {
  return {
    ...meal,
    nutrition: {
      calories: Math.round(meal.nutrition.calories * factor),
      protein: Math.round(meal.nutrition.protein * factor),
      carbs: Math.round(meal.nutrition.carbs * factor),
      fat: Math.round(meal.nutrition.fat * factor)
    }
  };
}

/**
 * Format a Date object to YYYY-MM-DD string
 */
function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
} 