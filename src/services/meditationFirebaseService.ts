/**
 * Meditation Firebase Service
 * 
 * Implementation of mindfulness tracking functionality using Firebase.
 * Handles meditation sessions, goals, and mood tracking.
 */

import { 
  collection, doc, getDoc, getDocs, query, where, orderBy, 
  setDoc, addDoc, updateDoc, Timestamp, serverTimestamp
} from 'firebase/firestore';
import { firestore } from '@/lib/firebase';
import { format } from 'date-fns';

// Types
export enum MeditationType {
  FOCUSED = 'focused',
  BREATH = 'breath',
  BODY_SCAN = 'body_scan',
  LOVING_KINDNESS = 'loving_kindness',
  OPEN_MONITORING = 'open_monitoring',
  GUIDED = 'guided',
  CUSTOM = 'custom'
}

export interface MeditationSession {
  id: string;
  userId: string;
  date: string; // YYYY-MM-DD format
  startTime: string; // ISO datetime string
  durationMinutes: number;
  meditationType: MeditationType;
  guidedSessionId?: string;
  moodBefore?: number; // 1-10 scale
  moodAfter?: number; // 1-10 scale
  notes?: string;
  completed: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface MeditationGoals {
  targetMinutesPerDay: number;
  targetDaysPerWeek: number;
  remindersEnabled: boolean;
  reminderTime?: string; // HH:MM format
}

export interface MoodEntry {
  id: string;
  userId: string;
  timestamp: string; // ISO datetime string
  moodScore: number; // 1-10 scale
  moodNotes?: string;
  meditationSessionId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface MeditationStatistics {
  totalSessions: number;
  totalMinutes: number;
  averageDuration: number;
  streakDays: number;
  completedDaysThisWeek: number;
  completedDaysLastWeek: number;
  moodImprovement: number;
  consistencyScore: number;
  lastSessionDate?: string;
}

// Default values
const DEFAULT_MINUTES_PER_DAY = 10;
const DEFAULT_DAYS_PER_WEEK = 5;

/**
 * Add a new meditation session
 */
export async function addMeditationSession(
  userId: string,
  startTime: Date,
  durationMinutes: number,
  meditationType: MeditationType,
  moodBefore?: number,
  moodAfter?: number,
  notes?: string,
  guidedSessionId?: string
): Promise<{
  success: boolean;
  data?: MeditationSession;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    const date = format(startTime, 'yyyy-MM-dd');
    const sessionId = crypto.randomUUID();
    
    // Create the new meditation session
    const newSession: MeditationSession = {
      id: sessionId,
      userId,
      date,
      startTime: startTime.toISOString(),
      durationMinutes,
      meditationType,
      guidedSessionId,
      moodBefore,
      moodAfter,
      notes,
      completed: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const sessionRef = doc(firestore, 'meditation_sessions', sessionId);
    
    await setDoc(sessionRef, {
      userId,
      date,
      startTime: startTime.toISOString(),
      durationMinutes,
      meditationType,
      guidedSessionId,
      moodBefore,
      moodAfter,
      notes,
      completed: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // Save mood entries if provided
    if (moodBefore !== undefined) {
      await addMoodEntry(userId, moodBefore, 'Before meditation', startTime, newSession.id);
    }
    
    if (moodAfter !== undefined) {
      const endTime = new Date(startTime.getTime() + durationMinutes * 60000);
      await addMoodEntry(userId, moodAfter, 'After meditation', endTime, newSession.id);
    }

    return {
      success: true,
      data: newSession
    };
  } catch (error) {
    console.error('Error adding meditation session:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Get meditation sessions for a date range
 */
export async function getMeditationSessionsForDateRange(
  userId: string,
  startDate: string,
  endDate: string
): Promise<{
  success: boolean;
  data?: MeditationSession[];
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    const sessionsRef = collection(firestore, 'meditation_sessions');
    const sessionsQuery = query(
      sessionsRef,
      where('userId', '==', userId),
      where('date', '>=', startDate),
      where('date', '<=', endDate),
      orderBy('date', 'desc')
    );

    const querySnapshot = await getDocs(sessionsQuery);
    
    const sessions: MeditationSession[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      sessions.push({
        id: doc.id,
        userId: data.userId,
        date: data.date,
        startTime: data.startTime,
        durationMinutes: data.durationMinutes,
        meditationType: data.meditationType,
        guidedSessionId: data.guidedSessionId,
        moodBefore: data.moodBefore,
        moodAfter: data.moodAfter,
        notes: data.notes,
        completed: data.completed,
        createdAt: data.createdAt instanceof Timestamp ? 
          data.createdAt.toDate().toISOString() : data.createdAt,
        updatedAt: data.updatedAt instanceof Timestamp ? 
          data.updatedAt.toDate().toISOString() : data.updatedAt
      });
    });

    return {
      success: true,
      data: sessions
    };
  } catch (error) {
    console.error('Error getting meditation sessions:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Get meditation goals for a user
 */
export async function getMeditationGoals(
  userId: string
): Promise<{
  success: boolean;
  data?: MeditationGoals;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    const goalsRef = doc(firestore, 'meditation_goals', userId);
    const goalsSnap = await getDoc(goalsRef);

    if (goalsSnap.exists()) {
      const data = goalsSnap.data();
      const goals: MeditationGoals = {
        targetMinutesPerDay: data.targetMinutesPerDay,
        targetDaysPerWeek: data.targetDaysPerWeek,
        remindersEnabled: data.remindersEnabled,
        reminderTime: data.reminderTime
      };

      return {
        success: true,
        data: goals
      };
    }

    // No goals found, create default goals
    const defaultGoals: MeditationGoals = {
      targetMinutesPerDay: DEFAULT_MINUTES_PER_DAY,
      targetDaysPerWeek: DEFAULT_DAYS_PER_WEEK,
      remindersEnabled: false
    };

    // Save default goals to Firestore
    await setDoc(goalsRef, {
      targetMinutesPerDay: defaultGoals.targetMinutesPerDay,
      targetDaysPerWeek: defaultGoals.targetDaysPerWeek,
      remindersEnabled: defaultGoals.remindersEnabled,
      reminderTime: defaultGoals.reminderTime,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      data: defaultGoals
    };
  } catch (error) {
    console.error('Error getting meditation goals:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Set meditation goals for a user
 */
export async function setMeditationGoals(
  userId: string,
  goals: MeditationGoals
): Promise<{
  success: boolean;
  data?: MeditationGoals;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    const goalsRef = doc(firestore, 'meditation_goals', userId);
    
    await setDoc(goalsRef, {
      targetMinutesPerDay: goals.targetMinutesPerDay,
      targetDaysPerWeek: goals.targetDaysPerWeek,
      remindersEnabled: goals.remindersEnabled,
      reminderTime: goals.reminderTime,
      updatedAt: serverTimestamp()
    }, { merge: true });

    return {
      success: true,
      data: goals
    };
  } catch (error) {
    console.error('Error setting meditation goals:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Add a mood entry
 */
export async function addMoodEntry(
  userId: string,
  moodScore: number,
  moodNotes?: string,
  timestamp: Date = new Date(),
  meditationSessionId?: string
): Promise<{
  success: boolean;
  data?: MoodEntry;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    const entryId = crypto.randomUUID();
    
    // Create the new mood entry
    const newEntry: MoodEntry = {
      id: entryId,
      userId,
      timestamp: timestamp.toISOString(),
      moodScore,
      moodNotes,
      meditationSessionId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const entryRef = doc(firestore, 'mood_entries', entryId);
    
    await setDoc(entryRef, {
      userId,
      timestamp: timestamp.toISOString(),
      moodScore,
      moodNotes,
      meditationSessionId,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      data: newEntry
    };
  } catch (error) {
    console.error('Error adding mood entry:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Get mood entries for a user in a date range
 */
export async function getMoodEntriesForDateRange(
  userId: string,
  startDate: string,
  endDate: string
): Promise<{
  success: boolean;
  data?: MoodEntry[];
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    const startTimestamp = new Date(startDate + 'T00:00:00.000Z').toISOString();
    const endTimestamp = new Date(endDate + 'T23:59:59.999Z').toISOString();

    const entriesRef = collection(firestore, 'mood_entries');
    const entriesQuery = query(
      entriesRef,
      where('userId', '==', userId),
      where('timestamp', '>=', startTimestamp),
      where('timestamp', '<=', endTimestamp),
      orderBy('timestamp', 'desc')
    );

    const querySnapshot = await getDocs(entriesQuery);
    
    const entries: MoodEntry[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      entries.push({
        id: doc.id,
        userId: data.userId,
        timestamp: data.timestamp,
        moodScore: data.moodScore,
        moodNotes: data.moodNotes,
        meditationSessionId: data.meditationSessionId,
        createdAt: data.createdAt instanceof Timestamp ? 
          data.createdAt.toDate().toISOString() : data.createdAt,
        updatedAt: data.updatedAt instanceof Timestamp ? 
          data.updatedAt.toDate().toISOString() : data.updatedAt
      });
    });

    return {
      success: true,
      data: entries
    };
  } catch (error) {
    console.error('Error getting mood entries:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Get meditation statistics for a date range
 */
export async function getMeditationStatistics(
  userId: string,
  startDate: string,
  endDate: string
): Promise<{
  success: boolean;
  data?: MeditationStatistics;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    // Get meditation sessions for the date range
    const sessionsResult = await getMeditationSessionsForDateRange(
      userId, 
      startDate, 
      endDate
    );
    
    if (!sessionsResult.success || !sessionsResult.data) {
      return {
        success: false,
        error: sessionsResult.error || 'Failed to fetch meditation sessions'
      };
    }
    
    const sessions = sessionsResult.data;
    
    if (sessions.length === 0) {
      return {
        success: true,
        data: {
          totalSessions: 0,
          totalMinutes: 0,
          averageDuration: 0,
          streakDays: 0,
          completedDaysThisWeek: 0,
          completedDaysLastWeek: 0,
          moodImprovement: 0,
          consistencyScore: 0
        }
      };
    }
    
    // Calculate statistics
    const totalSessions = sessions.length;
    const totalMinutes = sessions.reduce((sum, session) => sum + session.durationMinutes, 0);
    const averageDuration = Math.round(totalMinutes / totalSessions);
    
    // Calculate streak days (consecutive days with at least one session)
    let streakDays = 0;
    
    // Calculate mood improvement (average difference between before and after mood)
    let moodImprovementSum = 0;
    let moodImprovementCount = 0;
    
    for (const session of sessions) {
      if (session.moodBefore !== undefined && session.moodAfter !== undefined) {
        moodImprovementSum += session.moodAfter - session.moodBefore;
        moodImprovementCount++;
      }
    }
    
    const moodImprovement = moodImprovementCount > 0 ? 
      Math.round((moodImprovementSum / moodImprovementCount) * 10) / 10 : 0;
    
    // Simple consistency score calculation
    // Based on percentage of days meditated out of total days in range
    const uniqueDays = new Set(sessions.map(session => session.date)).size;
    const totalDaysInRange = Math.max(1, 
      (new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24) + 1
    );
    const consistencyScore = Math.round((uniqueDays / totalDaysInRange) * 100);
    
    // Find last session date
    const lastSessionDate = sessions.reduce((latest, session) => {
      return new Date(session.date) > new Date(latest) ? session.date : latest;
    }, startDate);
    
    // Calculate completed days in current and last week
    // (This would require actual calendar logic in a real implementation)
    const completedDaysThisWeek = Math.min(7, uniqueDays);
    const completedDaysLastWeek = 0; // Placeholder, would require historical data
    
    return {
      success: true,
      data: {
        totalSessions,
        totalMinutes,
        averageDuration,
        streakDays,
        completedDaysThisWeek,
        completedDaysLastWeek,
        moodImprovement,
        consistencyScore,
        lastSessionDate
      }
    };
  } catch (error) {
    console.error('Error calculating meditation statistics:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
} 