/**
 * Mindfulness Service
 * 
 * This service provides functionality for mindfulness tracking using Firebase.
 */


import * as FirebaseMeditationService from './meditationFirebaseService';

// Re-export types
export type { 
  MeditationType,
  MeditationSession,
  MeditationGoals,
  MoodEntry,
  MeditationStatistics 
} from './meditationFirebaseService';

/**
 * Hook for mindfulness tracking service
 * 
 * @returns Functions for managing meditation sessions, goals, and mood tracking
 */
export function useMindfulnessService() {
  const { db  } = useDatabaseType();
  
  /**
   * Add a new meditation session
   */
  const addMeditationSession = async (
    startTime: Date,
    durationMinutes: number,
    meditationType: FirebaseMeditationService.MeditationType,
    moodBefore?: number,
    moodAfter?: number,
    notes?: string,
    guidedSessionId?: string
  ) => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      return await FirebaseMeditationService.addMeditationSession(
        user.id,
        startTime,
        durationMinutes,
        meditationType,
        moodBefore,
        moodAfter,
        notes,
        guidedSessionId
      );
    } catch (error) {
      console.error('Error adding meditation session:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Get meditation sessions for a date range
   */
  const getMeditationSessionsForDateRange = async (
    startDate: string,
    endDate: string
  ) => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      return await FirebaseMeditationService.getMeditationSessionsForDateRange(
        user.id,
        startDate,
        endDate
      );
    } catch (error) {
      console.error('Error getting meditation sessions:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Get meditation goals
   */
  const getMeditationGoals = async () => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      return await FirebaseMeditationService.getMeditationGoals(user.id);
    } catch (error) {
      console.error('Error getting meditation goals:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Set meditation goals
   */
  const setMeditationGoals = async (
    goals: FirebaseMeditationService.MeditationGoals
  ) => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      return await FirebaseMeditationService.setMeditationGoals(user.id, goals);
    } catch (error) {
      console.error('Error setting meditation goals:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Add a mood entry
   */
  const addMoodEntry = async (
    moodScore: number,
    moodNotes?: string,
    timestamp: Date = new Date(),
    meditationSessionId?: string
  ) => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      return await FirebaseMeditationService.addMoodEntry(
        user.id,
        moodScore,
        moodNotes,
        timestamp,
        meditationSessionId
      );
    } catch (error) {
      console.error('Error adding mood entry:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Get mood entries for a date range
   */
  const getMoodEntriesForDateRange = async (
    startDate: string,
    endDate: string
  ) => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      return await FirebaseMeditationService.getMoodEntriesForDateRange(
        user.id,
        startDate,
        endDate
      );
    } catch (error) {
      console.error('Error getting mood entries:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Get meditation statistics for a date range
   */
  const getMeditationStatistics = async (
    startDate: string,
    endDate: string
  ) => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      return await FirebaseMeditationService.getMeditationStatistics(
        user.id,
        startDate,
        endDate
      );
    } catch (error) {
      console.error('Error getting meditation statistics:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  return {
    addMeditationSession,
    getMeditationSessionsForDateRange,
    getMeditationGoals,
    setMeditationGoals,
    addMoodEntry,
    getMoodEntriesForDateRange,
    getMeditationStatistics
  };
} 