/**
 * DEPRECATED - THIS FILE HAS BEEN REPLACED
 * 
 * This mock implementation has been replaced with a real Google Vision API implementation.
 * See services/vision/visionApi.ts for the actual implementation used in the app.
 * 
 * This file is kept for reference only and is no longer used in the application.
 */

// Original mock implementation below, not used in the actual app
import Constants from 'expo-constants';
import * as FileSystem from 'expo-file-system';

interface ClassificationResult {
  label: string;
  confidence: number;
}

interface GoogleVisionAnnotation {
  description: string;
  score: number;
}

interface GoogleVisionResponse {
  responses: {
    labelAnnotations?: GoogleVisionAnnotation[];
  }[];
  error?: {
    message: string;
  };
}

// This function is no longer used - see real implementation in services/vision/visionApi.ts
export async function classifyFoodImage(imageUri: string): Promise<ClassificationResult> {
  // DEPRECATED - MOCK IMPLEMENTATION
  console.warn('Using deprecated mock classificationService. This should not be called in production.');
  
  try {
    const apiKey = process.env.GOOGLE_VISION_API_KEY || Constants.expoConfig?.extra?.googleVisionApiKey;
    
    if (!apiKey) {
      throw new Error('Google Vision API key is not configured');
    }

    // Convert image to base64
    const base64Data = await FileSystem.readAsStringAsync(imageUri, {
      encoding: FileSystem.EncodingType.Base64,
    });

    const endpoint = `https://vision.googleapis.com/v1/images:annotate?key=${apiKey}`;
    
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        requests: [
          {
            image: {
              content: base64Data,
            },
            features: [
              {
                type: 'LABEL_DETECTION',
                maxResults: 10,
              },
            ],
          },
        ],
      }),
    });

    const data = await response.json() as GoogleVisionResponse;
    
    if (!response.ok) {
      throw new Error(`Google Vision API error: ${data.error?.message || response.statusText}`);
    }

    // Find the most likely food-related label
    const foodLabels = data.responses[0]?.labelAnnotations?.filter(
      (annotation) => isFoodCategory(annotation.description)
    ) || [];

    if (foodLabels.length > 0) {
      return {
        label: foodLabels[0].description.toLowerCase(),
        confidence: foodLabels[0].score,
      };
    }

    // Fallback if no food labels are found
    const firstLabel = data.responses[0]?.labelAnnotations?.[0];
    if (firstLabel) {
      return {
        label: firstLabel.description.toLowerCase(),
        confidence: firstLabel.score,
      };
    }

    throw new Error('No labels detected in the image');
  } catch (error) {
    console.error('Error classifying food image:', error);
    throw error;
  }
}

// Helper function to determine if a label is food-related
function isFoodCategory(label: string): boolean {
  const foodCategories = [
    'food', 'fruit', 'vegetable', 'dish', 'meal', 'cuisine', 'breakfast',
    'lunch', 'dinner', 'dessert', 'snack', 'meat', 'fish', 'bread',
    'dairy', 'beverage', 'drink'
  ];
  
  return foodCategories.some(category => 
    label.toLowerCase().includes(category)
  );
} 