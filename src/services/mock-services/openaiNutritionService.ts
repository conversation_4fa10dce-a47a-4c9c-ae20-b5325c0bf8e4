/**
 * DEPRECATED - THIS FILE HAS BEEN REPLACED
 * 
 * This mock implementation has been replaced with a real OpenAI API implementation.
 * See services/nutritionService.ts for the actual implementation used in the app.
 * 
 * This file is kept for reference only and is no longer used in the application.
 */

// Original mock implementation below, not used in the actual app
import Constants from 'expo-constants';

interface NutritionAnalysisResponse {
  analysis: string;
}

interface OpenAIResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
  error?: {
    message: string;
  };
}

// This function is no longer used - see real implementation in services/nutritionService.ts
export async function analyzeNutrition(prompt: string): Promise<NutritionAnalysisResponse> {
  // DEPRECATED - MOCK IMPLEMENTATION
  console.warn('Using deprecated mock openaiNutritionService. This should not be called in production.');
  
  try {
    const apiKey = process.env.OPENAI_API_KEY || Constants.expoConfig?.extra?.openaiApiKey;
    
    if (!apiKey) {
      throw new Error('OpenAI API key is not configured');
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are a nutrition expert. Provide detailed nutritional information for the food described, including calories, macronutrients, and beneficial vitamins/minerals. Format the response in a concise, easy-to-read format.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 300
      })
    });

    const data = await response.json() as OpenAIResponse;
    
    if (!response.ok) {
      throw new Error(`OpenAI API error: ${data.error?.message || response.statusText}`);
    }

    return {
      analysis: data.choices[0]?.message?.content || 'No analysis available'
    };
  } catch (error) {
    console.error('Error analyzing nutrition:', error);
    throw error;
  }
} 