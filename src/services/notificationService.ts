
import { Platform } from 'react-native';
import { getAuth } from 'firebase/auth';
import { getFirestore, collection, query, where, getDocs, updateDoc, doc, setDoc, deleteDoc, addDoc, orderBy, limit, writeBatch } from 'firebase/firestore';
import { useNotifications } from '@/contexts/NotificationContext';

// Get Firebase instances
const auth = getAuth();
const db = getFirestore();

// Try to import the real notifications, but provide fallbacks
let Notifications: any = {
  getPermissionsAsync: async () => ({ status: 'denied', granted: false }),
  requestPermissionsAsync: async () => ({ status: 'denied', granted: false }),
  scheduleNotificationAsync: async () => 'mock-notification-id',
  cancelScheduledNotificationAsync: async () => {},
  cancelAllScheduledNotificationsAsync: async () => {},
  getAllScheduledNotificationsAsync: async () => [],
  setBadgeCountAsync: async () => {},
  getBadgeCountAsync: async () => 0,
  setNotificationHandler: () => {},
  addNotificationReceivedListener: () => ({ remove: () => {} }),
  addNotificationResponseReceivedListener: () => ({ remove: () => {} }),
};

// Try to import the real module
try {
  const notificationModule = require('expo-notifications');
  if (notificationModule) {
    Notifications = notificationModule;
    console.log('Using real expo-notifications');
  }
} catch (error) {
  console.warn('expo-notifications not available, using mock implementation');
}

// Notification types
export type NotificationType = 'system' | 'activity' | 'goal' | 'reminder' | 'achievement' | 'social';

// App notification interface
export interface AppNotification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  is_read: boolean;
  created_at: string; // ISO date string
  image_url?: string;
  action_url?: string;
  category?: string;
}

export interface NotificationPreferences {
  enablePush: boolean;
  enableEmail: boolean;
  types: {
    [key in NotificationType]: boolean;
  };
  quietHoursStart?: string;
  quietHoursEnd?: string;
}

const DEFAULT_PREFERENCES: NotificationPreferences = {
  enablePush: true,
  enableEmail: false,
  types: {
    system: true,
    activity: true,
    goal: true,
    reminder: true,
    achievement: true,
    social: true,
  },
};

export async function getUserNotificationPreferences(): Promise<NotificationPreferences> {
  try {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Query Firebase for user preferences
    const preferencesQuery = query(
      collection(db, 'user_notification_preferences'),
      where('user_id', '==', user.uid),
      limit(1)
    );
    
    const querySnapshot = await getDocs(preferencesQuery);
    
    if (querySnapshot.empty) {
      // If no preferences found, create defaults
      return DEFAULT_PREFERENCES;
    }
    
    const data = querySnapshot.docs[0].data();

    return {
      enablePush: data.enabled,
      enableEmail: data.enable_email || false,
      types: {
        system: true,
        activity: data.progress_updates,
        goal: data.progress_updates,
        reminder: data.meal_reminders || data.water_reminders,
        achievement: data.progress_updates,
        social: data.weekly_reports,
      },
      quietHoursStart: data.quiet_hours_start,
      quietHoursEnd: data.quiet_hours_end,
    };
  } catch (error) {
    console.error('Error fetching notification preferences:', error);
    return DEFAULT_PREFERENCES;
  }
}

export async function updateUserNotificationPreferences(
  preferences: NotificationPreferences
): Promise<boolean> {
  try {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Check if document exists
    const preferencesQuery = query(
      collection(db, 'user_notification_preferences'),
      where('user_id', '==', user.uid)
    );
    
    const querySnapshot = await getDocs(preferencesQuery);
    const preferencesData = {
      user_id: user.uid,
      enabled: preferences.enablePush,
      meal_reminders: preferences.types.reminder,
      water_reminders: preferences.types.reminder,
      progress_updates: preferences.types.activity || preferences.types.goal || preferences.types.achievement,
      weekly_reports: preferences.types.social,
      updated_at: new Date().toISOString(),
    };
    
    if (querySnapshot.empty) {
      // Create new document
      await addDoc(collection(db, 'user_notification_preferences'), preferencesData);
    } else {
      // Update existing document
      await updateDoc(doc(db, 'user_notification_preferences', querySnapshot.docs[0].id), preferencesData);
    }

    return true;
  } catch (error) {
    console.error('Error updating notification preferences:', error);
    return false;
  }
}

// Initialize notifications
export async function initializeNotifications() {
  try {
    // Set notification handler
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      }),
    });

    // Request permissions
    const { status } = await Notifications.requestPermissionsAsync();
    return status === 'granted';
  } catch (error) {
    console.error('Error initializing notifications:', error);
    return false;
  }
}

// Schedule a notification
export async function scheduleNotification(
  title: string,
  body: string,
  data: any = {},
  trigger: any = null
) {
  try {
    const id = await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
      },
      trigger,
    });
    return id;
  } catch (error) {
    console.error('Error scheduling notification:', error);
    return null;
  }
}

// Cancel a notification
export async function cancelNotification(id: string) {
  try {
    await Notifications.cancelScheduledNotificationAsync(id);
    return true;
  } catch (error) {
    console.error('Error canceling notification:', error);
    return false;
  }
}

// Get all scheduled notifications
export async function getAllNotifications() {
  try {
    return await Notifications.getAllScheduledNotificationsAsync();
  } catch (error) {
    console.error('Error getting notifications:', error);
    return [];
  }
}

// Helper to schedule a reminder
export async function scheduleReminder(
  title: string,
  body: string,
  date: Date,
  repeats: boolean = false
) {
  const trigger = Platform.OS === 'ios'
    ? {
        date,
        repeats,
      }
    : {
        seconds: Math.floor((date.getTime() - Date.now()) / 1000),
        repeats,
      };

  return scheduleNotification(title, body, { type: 'reminder' }, trigger);
}

// Add notification listeners
export function addNotificationListeners(
  onReceive: (notification: any) => void,
  onResponse: (response: any) => void
) {
  const receiveListener = Notifications.addNotificationReceivedListener(onReceive);
  const responseListener = Notifications.addNotificationResponseReceivedListener(onResponse);

  return {
    remove: () => {
      receiveListener.remove();
      responseListener.remove();
    },
  };
}

// Set badge count
export async function setBadgeCount(count: number) {
  try {
    await Notifications.setBadgeCountAsync(count);
    return true;
  } catch (error) {
    console.error('Error setting badge count:', error);
    return false;
  }
}

/**
 * Fetch notifications from the backend
 * @returns Promise resolving to array of notifications
 */
export async function fetchNotifications(): Promise<AppNotification[]> {
  // Now we use Firebase to fetch notifications directly
  try {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    const notificationsRef = collection(db, 'notifications');
    const userNotificationsQuery = query(
      notificationsRef,
      where('userId', '==', user.uid),
      orderBy('created_at', 'desc'),
      limit(50)
    );
    
    const snapshot = await getDocs(userNotificationsQuery);
    const notifications: AppNotification[] = [];
    
    snapshot.forEach(doc => {
      const data = doc.data();
      notifications.push({
        id: doc.id,
        title: data.title,
        message: data.message,
        type: data.type as NotificationType,
        is_read: data.is_read || false,
        created_at: data.created_at || new Date().toISOString(),
        image_url: data.image_url,
        action_url: data.action_url,
        category: data.category,
      });
    });
    
    return notifications;
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return [];
  }
}

/**
 * Mark notifications as read
 * @param ids Array of notification IDs to mark as read
 * @returns Promise resolving to true if successful
 */
export async function markNotificationsAsRead(ids: string[]): Promise<boolean> {
  try {
    const batch = writeBatch(db);
    
    for (const id of ids) {
      const notificationRef = doc(db, 'notifications', id);
      batch.update(notificationRef, { is_read: true });
    }
    
    await batch.commit();
    return true;
  } catch (error) {
    console.error('Error marking notifications as read:', error);
    return false;
  }
}

/**
 * Mark all notifications as read
 * @returns Promise resolving to true if successful
 */
export async function markAllNotificationsAsRead(): Promise<boolean> {
  try {
    const user = auth.currentUser;
    if (!user) return false;
    
    // Get all unread notifications
    const unreadQuery = query(
      collection(db, 'notifications'),
      where('userId', '==', user.uid),
      where('is_read', '==', false)
    );
    
    const snapshot = await getDocs(unreadQuery);
    
    // Update each one with a batch write
    const batch = writeBatch(db);
    
    snapshot.forEach(doc => {
      batch.update(doc.ref, { is_read: true });
    });
    
    await batch.commit();
    return true;
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    return false;
  }
}

/**
 * Hook to add real notifications to the app
 */
export function useAddSampleNotifications() {
  const { addNotification } = useNotifications();
  
  // Add real notifications
  const addSampleNotifications = async () => {
    try {
      // Food entry notification
      await addNotification({
        title: 'Food entry logged',
        message: 'Your meal has been successfully saved to your food history.',
        type: 'success'
      });
      
      // Nutrition summary notification (after a short delay)
      setTimeout(async () => {
        await addNotification({
          title: 'Daily Nutrition Summary',
          message: 'You\'ve consumed 1,800 calories today, 70% of your daily goal.',
          type: 'info'
        });
      }, 3000);
      
      // Water intake reminder (after a longer delay)
      setTimeout(async () => {
        await addNotification({
          title: 'Water Intake Reminder',
          message: 'You\'ve only had 2 glasses of water today. Stay hydrated!',
          type: 'warning'
        });
      }, 6000);
    } catch (error) {
      console.error('Error adding notifications:', error);
    }
  };
  
  return { addSampleNotifications };
}

/**
 * Updates notification settings for the user
 */
export async function updateNotificationSettings(settings: {
  enablePush?: boolean;
  enableEmail?: boolean;
  types?: Record<NotificationType, boolean>;
}) {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Authentication error: Not authenticated' };
    }

    // Check if settings document exists
    const settingsQuery = query(
      collection(db, 'notification_settings'),
      where('user_id', '==', user.uid)
    );
    
    const querySnapshot = await getDocs(settingsQuery);
    
    const settingsData = {
      user_id: user.uid,
      push_enabled: settings.enablePush,
      email_enabled: settings.enableEmail,
      type_preferences: settings.types,
      updated_at: new Date().toISOString()
    };
    
    if (querySnapshot.empty) {
      // Create new settings
      await addDoc(collection(db, 'notification_settings'), settingsData);
    } else {
      // Update existing settings
      await updateDoc(doc(db, 'notification_settings', querySnapshot.docs[0].id), settingsData);
    }

    return { success: true };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in updateNotificationSettings:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Get notification settings for the current user
 */
export async function getNotificationSettings() {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      console.error('Error getting user: No user found');
      return { success: false, error: 'Authentication error' };
    }

    // Query for notification settings
    const settingsQuery = query(
      collection(db, 'notification_settings'),
      where('user_id', '==', user.uid),
      limit(1)
    );
    
    const querySnapshot = await getDocs(settingsQuery);
    
    if (querySnapshot.empty) {
      return { 
        success: true, 
        data: {
          push_enabled: true,
          email_enabled: true,
          type_preferences: {
            system: true,
            activity: true,
            goal: true,
            reminder: true,
            achievement: true,
            social: true
          }
        }
      };
    }
    
    const data = querySnapshot.docs[0].data();

    return { 
      success: true, 
      data: data || {
        push_enabled: true,
        email_enabled: true,
        type_preferences: {
          system: true,
          activity: true,
          goal: true,
          reminder: true,
          achievement: true,
          social: true
        }
      }
    };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in getNotificationSettings:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Deletes notifications
 */
export async function deleteNotifications(notificationIds: string[]) {
  try {
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Authentication error: Not authenticated' };
    }

    // Delete each notification document
    const batch = writeBatch(db);
    
    for (const notificationId of notificationIds) {
      const notificationRef = doc(db, 'notifications', notificationId);
      batch.delete(notificationRef);
    }
    
    await batch.commit();

    return { success: true };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in deleteNotifications:', errorMessage);
    return { success: false, error: errorMessage };
  }
} 