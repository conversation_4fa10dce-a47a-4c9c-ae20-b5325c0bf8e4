import AsyncStorage from '@react-native-async-storage/async-storage';
import { getHealthMetrics, HealthMetricType } from './wearableService';
import { db } from '@/lib/firebase';
import { getDoc, doc, setDoc, collection, query, where, getDocs, addDoc, updateDoc } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

// Nutrition goal types
export enum NutritionGoalType {
  WEIGHT_LOSS = 'weight_loss',
  WEIGHT_GAIN = 'weight_gain',
  MAINTENANCE = 'maintenance',
  MUSCLE_GAIN = 'muscle_gain',
  ATHLETIC_PERFORMANCE = 'athletic_performance',
  GENERAL_HEALTH = 'general_health',
}

// Activity levels
export enum ActivityLevel {
  SEDENTARY = 'sedentary',
  LIGHTLY_ACTIVE = 'lightly_active',
  MODERATELY_ACTIVE = 'moderately_active',
  VERY_ACTIVE = 'very_active',
  EXTREMELY_ACTIVE = 'extremely_active',
}

// Dietary preference
export enum DietaryPreference {
  STANDARD = 'standard',
  VEGETARIAN = 'vegetarian',
  VEGAN = 'vegan',
  PESCATARIAN = 'pescatarian',
  KETO = 'keto',
  PALEO = 'paleo',
  MEDITERRANEAN = 'mediterranean',
  LOW_CARB = 'low_carb',
  LOW_FAT = 'low_fat',
  GLUTEN_FREE = 'gluten_free',
  DAIRY_FREE = 'dairy_free',
}

// User profile interface
export interface UserProfile {
  id: string;
  age?: number;
  gender?: 'male' | 'female' | 'other';
  height?: number; // in cm
  weight?: number; // in kg
  goalType: NutritionGoalType;
  activityLevel: ActivityLevel;
  dietaryPreference: DietaryPreference;
  calorieGoal: number;
  proteinGoal: number; // in grams
  carbsGoal: number; // in grams
  fatGoal: number; // in grams
  waterGoal: number; // in ml
  targetWeight?: number; // in kg
  excludedIngredients?: string[];
  healthConditions?: string[];
  weeklyWeightChangeTarget?: number; // in kg/week, negative for loss, positive for gain
  customMacroRatio?: {
    protein: number; // percentage (0-100)
    carbs: number; // percentage (0-100)
    fat: number; // percentage (0-100)
  };
}

// Goal calculation constants
const BMR_MULTIPLIERS = {
  [ActivityLevel.SEDENTARY]: 1.2,
  [ActivityLevel.LIGHTLY_ACTIVE]: 1.375,
  [ActivityLevel.MODERATELY_ACTIVE]: 1.55,
  [ActivityLevel.VERY_ACTIVE]: 1.725,
  [ActivityLevel.EXTREMELY_ACTIVE]: 1.9,
};

const MACRO_RATIOS = {
  [NutritionGoalType.WEIGHT_LOSS]: { protein: 40, carbs: 30, fat: 30 },
  [NutritionGoalType.WEIGHT_GAIN]: { protein: 30, carbs: 50, fat: 20 },
  [NutritionGoalType.MAINTENANCE]: { protein: 30, carbs: 40, fat: 30 },
  [NutritionGoalType.MUSCLE_GAIN]: { protein: 40, carbs: 40, fat: 20 },
  [NutritionGoalType.ATHLETIC_PERFORMANCE]: { protein: 30, carbs: 50, fat: 20 },
  [NutritionGoalType.GENERAL_HEALTH]: { protein: 25, carbs: 45, fat: 30 },
};

const DIETARY_ADJUSTMENTS = {
  [DietaryPreference.STANDARD]: { protein: 1, carbs: 1, fat: 1 },
  [DietaryPreference.VEGETARIAN]: { protein: 0.9, carbs: 1.1, fat: 1 },
  [DietaryPreference.VEGAN]: { protein: 0.9, carbs: 1.2, fat: 0.9 },
  [DietaryPreference.PESCATARIAN]: { protein: 1, carbs: 1, fat: 1 },
  [DietaryPreference.KETO]: { protein: 1.2, carbs: 0.1, fat: 2.2 },
  [DietaryPreference.PALEO]: { protein: 1.3, carbs: 0.7, fat: 1.3 },
  [DietaryPreference.MEDITERRANEAN]: { protein: 1, carbs: 1, fat: 1.1 },
  [DietaryPreference.LOW_CARB]: { protein: 1.2, carbs: 0.5, fat: 1.5 },
  [DietaryPreference.LOW_FAT]: { protein: 1.1, carbs: 1.3, fat: 0.6 },
  [DietaryPreference.GLUTEN_FREE]: { protein: 1, carbs: 0.9, fat: 1.1 },
  [DietaryPreference.DAIRY_FREE]: { protein: 1, carbs: 1.1, fat: 0.9 },
};

// Default profile values
const DEFAULT_PROFILE: Partial<UserProfile> = {
  goalType: NutritionGoalType.MAINTENANCE,
  activityLevel: ActivityLevel.MODERATELY_ACTIVE,
  dietaryPreference: DietaryPreference.STANDARD,
  calorieGoal: 1700,
  proteinGoal: 112,
  carbsGoal: 144,
  fatGoal: 80,
  waterGoal: 2500,
};

// Storage keys
const USER_PROFILE_KEY = 'user_nutrition_profile';

/**
 * Helper function to get the current user ID
 */
async function getCurrentUserId(): Promise<string> {
  const auth = getAuth();
  const user = auth.currentUser;
  
  if (!user) {
    throw new Error('No authenticated user found');
  }
  
  return user.uid;
}

/**
 * Get the user's nutrition profile
 */
export async function getUserProfile(): Promise<UserProfile | null> {
  try {
    // Try to load from local storage first
    const profileStr = await AsyncStorage.getItem(USER_PROFILE_KEY);
    
    if (profileStr) {
      return JSON.parse(profileStr);
    }
    
    // If not in cache, try to fetch from Firebase
    try {
      const userId = await getCurrentUserId();
      
      const profileDoc = await getDoc(doc(db, 'profiles', userId));
      
      if (profileDoc.exists()) {
        const data = profileDoc.data();
        
        // Map database fields to UserProfile
        const profile: UserProfile = {
          id: profileDoc.id,
          age: data.age,
          gender: data.gender,
          height: data.height,
          weight: data.weight,
          goalType: data.goal_type || DEFAULT_PROFILE.goalType as NutritionGoalType,
          activityLevel: data.activity_level || DEFAULT_PROFILE.activityLevel as ActivityLevel,
          dietaryPreference: data.dietary_preference || DEFAULT_PROFILE.dietaryPreference as DietaryPreference,
          calorieGoal: data.daily_calorie_goal || DEFAULT_PROFILE.calorieGoal as number,
          proteinGoal: data.daily_protein_goal || DEFAULT_PROFILE.proteinGoal as number,
          carbsGoal: data.daily_carbs_goal || DEFAULT_PROFILE.carbsGoal as number,
          fatGoal: data.daily_fat_goal || DEFAULT_PROFILE.fatGoal as number,
          waterGoal: data.daily_water_goal || DEFAULT_PROFILE.waterGoal as number,
          targetWeight: data.target_weight,
          excludedIngredients: data.excluded_ingredients,
          healthConditions: data.health_conditions,
          weeklyWeightChangeTarget: data.weekly_weight_change,
          customMacroRatio: data.custom_macro_ratio,
        };
        
        // Cache the profile
        await AsyncStorage.setItem(USER_PROFILE_KEY, JSON.stringify(profile));
        
        return profile;
      }
    } catch (error) {
      console.error('Error fetching user profile from Firebase:', error);
    }
    
    return null;
  } catch (error) {
    console.error('Error getting user profile:', error);
    return null;
  }
}

/**
 * Update user profile
 */
export async function updateUserProfile(profile: Partial<UserProfile>): Promise<UserProfile | null> {
  try {
    // Get current profile
    const currentProfile = await getUserProfile();
    
    if (!currentProfile) {
      console.error('Cannot update profile: no existing profile found');
      return null;
    }
    
    // Merge with new data
    const updatedProfile = {
      ...currentProfile,
      ...profile,
    };
    
    // Update in local storage
    await AsyncStorage.setItem(USER_PROFILE_KEY, JSON.stringify(updatedProfile));
    
    // Update in Firebase
    try {
      const userId = await getCurrentUserId();
      
      await updateDoc(doc(db, 'profiles', userId), {
        age: updatedProfile.age,
        gender: updatedProfile.gender,
        height: updatedProfile.height,
        weight: updatedProfile.weight,
        goal_type: updatedProfile.goalType,
        activity_level: updatedProfile.activityLevel,
        dietary_preference: updatedProfile.dietaryPreference,
        daily_calorie_goal: updatedProfile.calorieGoal,
        daily_protein_goal: updatedProfile.proteinGoal,
        daily_carbs_goal: updatedProfile.carbsGoal,
        daily_fat_goal: updatedProfile.fatGoal,
        daily_water_goal: updatedProfile.waterGoal,
        target_weight: updatedProfile.targetWeight,
        excluded_ingredients: updatedProfile.excludedIngredients,
        health_conditions: updatedProfile.healthConditions,
        weekly_weight_change: updatedProfile.weeklyWeightChangeTarget,
        custom_macro_ratio: updatedProfile.customMacroRatio,
        updated_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error updating profile in Firebase:', error);
    }
    
    return updatedProfile;
  } catch (error) {
    console.error('Error updating user profile:', error);
    return null;
  }
}

/**
 * Create a new user profile
 */
export async function createUserProfile(profile: Partial<UserProfile>): Promise<UserProfile | null> {
  try {
    const userId = await getCurrentUserId();
    
    // Merge with defaults
    const newProfile: UserProfile = {
      ...DEFAULT_PROFILE,
      ...profile,
      id: userId,
    } as UserProfile;
    
    // If we don't have enough information, calculate goals
    if (!profile.calorieGoal && profile.age && profile.gender && profile.height && profile.weight) {
      const calculatedGoals = calculateNutritionGoals(
        profile.age,
        profile.gender,
        profile.height,
        profile.weight,
        (profile.activityLevel || DEFAULT_PROFILE.activityLevel) as ActivityLevel,
        (profile.goalType || DEFAULT_PROFILE.goalType) as NutritionGoalType,
        (profile.dietaryPreference || DEFAULT_PROFILE.dietaryPreference) as DietaryPreference,
        profile.weeklyWeightChangeTarget,
        profile.customMacroRatio
      );
      
      Object.assign(newProfile, calculatedGoals);
    }
    
    // Save to local storage
    await AsyncStorage.setItem(USER_PROFILE_KEY, JSON.stringify(newProfile));
    
    // Save to Firebase
    try {
      await setDoc(doc(db, 'profiles', userId), {
        id: userId,
        age: newProfile.age,
        gender: newProfile.gender,
        height: newProfile.height,
        weight: newProfile.weight,
        goal_type: newProfile.goalType,
        activity_level: newProfile.activityLevel,
        dietary_preference: newProfile.dietaryPreference,
        daily_calorie_goal: newProfile.calorieGoal,
        daily_protein_goal: newProfile.proteinGoal,
        daily_carbs_goal: newProfile.carbsGoal,
        daily_fat_goal: newProfile.fatGoal,
        daily_water_goal: newProfile.waterGoal,
        target_weight: newProfile.targetWeight,
        excluded_ingredients: newProfile.excludedIngredients,
        health_conditions: newProfile.healthConditions,
        weekly_weight_change: newProfile.weeklyWeightChangeTarget,
        custom_macro_ratio: newProfile.customMacroRatio,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error creating profile in Firebase:', error);
    }
    
    return newProfile;
  } catch (error) {
    console.error('Error creating user profile:', error);
    return null;
  }
}

/**
 * Calculate nutrition goals based on user data
 */
export function calculateNutritionGoals(
  age: number,
  gender: 'male' | 'female' | 'other',
  height: number, // in cm
  weight: number, // in kg
  activityLevel: ActivityLevel,
  goalType: NutritionGoalType,
  dietaryPreference: DietaryPreference = DietaryPreference.STANDARD,
  weeklyWeightChangeTarget?: number,
  customMacroRatio?: { protein: number; carbs: number; fat: number }
): {
  calorieGoal: number;
  proteinGoal: number;
  carbsGoal: number;
  fatGoal: number;
  waterGoal: number;
} {
  // Calculate Basal Metabolic Rate (BMR) using Mifflin-St Jeor Equation
  let bmr: number;
  if (gender === 'male') {
    bmr = 10 * weight + 6.25 * height - 5 * age + 5;
  } else {
    bmr = 10 * weight + 6.25 * height - 5 * age - 161;
  }
  
  // Apply activity level multiplier
  const tdee = bmr * BMR_MULTIPLIERS[activityLevel];
  
  // Calculate calorie goal based on goal type and weekly weight change target
  let calorieGoal = tdee;
  
  if (weeklyWeightChangeTarget) {
    // 1 kg of fat is approximately 7700 calories
    const dailyCalorieAdjustment = (weeklyWeightChangeTarget * 7700) / 7;
    calorieGoal += dailyCalorieAdjustment;
  } else {
    // Use default calorie adjustments based on goal type
    switch (goalType) {
      case NutritionGoalType.WEIGHT_LOSS:
        calorieGoal *= 0.8; // 20% deficit
        break;
      case NutritionGoalType.WEIGHT_GAIN:
        calorieGoal *= 1.15; // 15% surplus
        break;
      case NutritionGoalType.MUSCLE_GAIN:
        calorieGoal *= 1.1; // 10% surplus
        break;
      case NutritionGoalType.ATHLETIC_PERFORMANCE:
        calorieGoal *= 1.05; // 5% surplus
        break;
      case NutritionGoalType.MAINTENANCE:
      case NutritionGoalType.GENERAL_HEALTH:
      default:
        // No adjustment for maintenance
        break;
    }
  }
  
  // Round calorie goal to nearest 50
  calorieGoal = Math.round(calorieGoal / 50) * 50;
  
  // Get macro ratio based on goal type or use custom ratio
  const macroRatio = customMacroRatio || MACRO_RATIOS[goalType];
  
  // Calculate macronutrient goals (in grams)
  let proteinGoal = (calorieGoal * (macroRatio.protein / 100)) / 4; // 4 calories per gram of protein
  let carbsGoal = (calorieGoal * (macroRatio.carbs / 100)) / 4; // 4 calories per gram of carbs
  let fatGoal = (calorieGoal * (macroRatio.fat / 100)) / 9; // 9 calories per gram of fat
  
  // Apply dietary preference adjustments
  const dietaryAdjustment = DIETARY_ADJUSTMENTS[dietaryPreference];
  proteinGoal *= dietaryAdjustment.protein;
  carbsGoal *= dietaryAdjustment.carbs;
  fatGoal *= dietaryAdjustment.fat;
  
  // Round macronutrient goals
  proteinGoal = Math.round(proteinGoal);
  carbsGoal = Math.round(carbsGoal);
  fatGoal = Math.round(fatGoal);
  
  // Calculate water goal (ml) - standard formula is 30-35ml per kg of body weight
  const waterGoal = Math.round(weight * 35);
  
  return {
    calorieGoal,
    proteinGoal,
    carbsGoal,
    fatGoal,
    waterGoal,
  };
}

/**
 * Calculate adjusted nutrition goals based on wearable activity data
 */
export async function getAdjustedNutritionGoals(): Promise<{
  calorieGoal: number;
  proteinGoal: number;
  carbsGoal: number;
  fatGoal: number;
  waterGoal: number;
  adjustmentReason?: string;
} | null> {
  console.debug('nutritionGoalService: Starting to calculate adjusted goals');
  try {
    // Get user profile
    const profile = await getUserProfile();
    
    console.debug('nutritionGoalService: User profile obtained:', !!profile);
    
    if (!profile) {
      console.debug('nutritionGoalService: No profile found, returning null');
      // Return default values instead of null to prevent loading indefinitely
      return {
        calorieGoal: 2000,
        proteinGoal: 150,
        carbsGoal: 200,
        fatGoal: 70,
        waterGoal: 2500,
      };
    }
    
    // Get today's date
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    console.debug('nutritionGoalService: Fetching health metrics');
    
    try {
      // Get calories burned from wearable today
      const calorieData = await getHealthMetrics(
        HealthMetricType.CALORIES_BURNED, 
        today.toISOString()
      );
      
      // Get steps from wearable today
      const stepsData = await getHealthMetrics(
        HealthMetricType.STEPS, 
        today.toISOString()
      );
      
      // Get active minutes from wearable today
      const activeMinutesData = await getHealthMetrics(
        HealthMetricType.ACTIVE_MINUTES, 
        today.toISOString()
      );
      
      console.debug('nutritionGoalService: Health metrics fetched:', {
        calorieDataCount: calorieData.length,
        stepsDataCount: stepsData.length,
        activeMinutesDataCount: activeMinutesData.length
      });
      
      // Calculate adjustments
      let calorieAdjustment = 0;
      let adjustmentReason = '';
      
      // If we have calorie data, use it directly
      if (calorieData.length > 0) {
        // Get total calories burned for the day
        const caloriesBurned = calorieData.reduce((sum, metric) => sum + metric.value, 0);
        
        // Adjust calorie goal based on activity
        // Use 30% of extra calories burned above BMR
        const estimatedBMR = profile.calorieGoal * 0.7; // rough estimate
        const extraCaloriesBurned = Math.max(0, caloriesBurned - estimatedBMR);
        calorieAdjustment = Math.round((extraCaloriesBurned * 0.3) / 50) * 50; // Round to nearest 50
        
        if (calorieAdjustment > 0) {
          adjustmentReason = `Added ${calorieAdjustment} calories based on activity tracking data.`;
        }
      } 
      // If we have steps data, use it to estimate
      else if (stepsData.length > 0) {
        const totalSteps = stepsData.reduce((sum, metric) => sum + metric.value, 0);
        
        // Rough estimate: 1 step = 0.04 calories
        const estimatedCalories = totalSteps * 0.04;
        
        // Only adjust if steps are above 5000 (base activity level)
        if (totalSteps > 5000) {
          const extraSteps = totalSteps - 5000;
          calorieAdjustment = Math.round((extraSteps * 0.04 * 0.3) / 50) * 50; // 30% adjustment, rounded to nearest 50
          
          if (calorieAdjustment > 0) {
            adjustmentReason = `Added ${calorieAdjustment} calories based on ${totalSteps} steps.`;
          }
        }
      }
      // If we have active minutes, use it as a fallback
      else if (activeMinutesData.length > 0) {
        const activeMinutes = activeMinutesData.reduce((sum, metric) => sum + metric.value, 0);
        
        // Rough estimate: 1 active minute = 5-10 calories
        const estimatedCalories = activeMinutes * 7.5;
        
        // Only adjust if active minutes are above 20 minutes
        if (activeMinutes > 20) {
          calorieAdjustment = Math.round((estimatedCalories * 0.3) / 50) * 50; // 30% adjustment, rounded to nearest 50
          
          if (calorieAdjustment > 0) {
            adjustmentReason = `Added ${calorieAdjustment} calories based on ${activeMinutes} active minutes.`;
          }
        }
      }
      
      console.debug('nutritionGoalService: Calorie adjustment calculated:', calorieAdjustment);
      
      // Apply adjustments
      const adjustedCalorieGoal = profile.calorieGoal + calorieAdjustment;
      
      // Get macro ratio based on goal type or use custom ratio
      const macroRatio = profile.customMacroRatio || MACRO_RATIOS[profile.goalType];
      
      // Calculate adjusted macronutrient goals (in grams)
      let adjustedProteinGoal = (adjustedCalorieGoal * (macroRatio.protein / 100)) / 4;
      let adjustedCarbsGoal = (adjustedCalorieGoal * (macroRatio.carbs / 100)) / 4;
      let adjustedFatGoal = (adjustedCalorieGoal * (macroRatio.fat / 100)) / 9;
      
      // Round macronutrient goals
      adjustedProteinGoal = Math.round(adjustedProteinGoal);
      adjustedCarbsGoal = Math.round(adjustedCarbsGoal);
      adjustedFatGoal = Math.round(adjustedFatGoal);
      
      // Calculate adjusted water goal - increase with activity
      const waterAdjustment = calorieAdjustment > 0 ? 500 : 0; // Add 500ml for active days
      const adjustedWaterGoal = profile.waterGoal + waterAdjustment;
      
      console.debug('nutritionGoalService: Returning adjusted goals');
      
      return {
        calorieGoal: adjustedCalorieGoal,
        proteinGoal: adjustedProteinGoal,
        carbsGoal: adjustedCarbsGoal,
        fatGoal: adjustedFatGoal,
        waterGoal: adjustedWaterGoal,
        adjustmentReason: calorieAdjustment > 0 ? adjustmentReason : undefined,
      };
    } catch (metricError) {
      console.error('Error fetching health metrics:', metricError);
      console.debug('nutritionGoalService: Using base profile goals due to metric error');
      
      // Return base profile goals if there's an error getting metrics
      return {
        calorieGoal: profile.calorieGoal,
        proteinGoal: profile.proteinGoal,
        carbsGoal: profile.carbsGoal,
        fatGoal: profile.fatGoal,
        waterGoal: profile.waterGoal,
      };
    }
  } catch (error) {
    console.error('Error getting adjusted nutrition goals:', error);
    console.debug('nutritionGoalService: Returning default goals due to error');
    
    // Return default goals instead of null to prevent UI from getting stuck
    return {
      calorieGoal: 2000,
      proteinGoal: 150,
      carbsGoal: 200,
      fatGoal: 70,
      waterGoal: 2500,
    };
  }
} 