// API keys are now handled server-side via Firebase Functions
// import { USDA_API_KEY, NUTRITIONIX_APP_ID, NUTRITIONIX_API_KEY } from '@/utils/config';

import { NutritionData } from '@/types/food';
import { useStorageService } from './storageService';
import { v4 as uuidv4 } from 'uuid';
import { collection, query, where, getDocs, addDoc, updateDoc, deleteDoc, doc, getDoc, setDoc } from 'firebase/firestore';
import { firestore } from '@/lib/firebase';
import { getAuth } from 'firebase/auth';

interface FoodNutrient {
  nutrientId: number;
  nutrientName: string;
  nutrientNumber: string;
  unitName: string;
  value: number;
}

interface SearchResult {
  fdcId: number;
  description: string;
  dataType: string;
  foodNutrients: FoodNutrient[];
}

// Nutritionix API interfaces
export interface NutritionixNutrient {
  attr_id: number;
  value: number;
  name: string;
  unit: string;
}

export interface NutritionixFood {
  food_name: string;
  brand_name?: string;
  serving_qty: number;
  serving_unit: string;
  serving_weight_grams: number;
  nf_calories: number;
  nf_total_fat: number;
  nf_saturated_fat: number;
  nf_cholesterol: number;
  nf_sodium: number;
  nf_total_carbohydrate: number;
  nf_dietary_fiber: number;
  nf_sugars: number;
  nf_protein: number;
  nf_potassium: number;
  full_nutrients: NutritionixNutrient[];
  photo?: {
    thumb: string;
    highres: string;
  };
}

export interface NutritionixNaturalResponse {
  foods: NutritionixFood[];
}

export interface NutritionixExercise {
  tag_id: number;
  user_input: string;
  duration_min: number;
  met: number;
  nf_calories: number;
  photo: {
    highres: string;
    thumb: string;
  };
  compendium_code: number;
  name: string;
}

export interface NutritionixExerciseResponse {
  exercises: NutritionixExercise[];
}

// Add common recipes with pre-defined nutrition data for faster lookup
const COMMON_RECIPES: Record<string, {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  ingredients: string[];
}> = {
  // American dishes
  "goulash": {
    name: "Goulash",
    calories: 367,
    protein: 22,
    carbs: 37,
    fat: 14,
    ingredients: [
      "Ground Beef", "Onion", "Garlic", "Paprika", 
      "Tomato Sauce", "Diced Tomatoes", "Elbow Macaroni", 
      "Cheddar Cheese", "Olive Oil", "Worcestershire Sauce"
    ]
  },
  "hungarian goulash": {
    name: "Hungarian Goulash",
    calories: 382,
    protein: 26,
    carbs: 25,
    fat: 19,
    ingredients: [
      "Beef Chuck", "Onion", "Garlic", "Paprika", 
      "Tomato Paste", "Beef Broth", "Carrots", 
      "Potatoes", "Bell Peppers", "Sour Cream"
    ]
  },
  "american goulash": {
    name: "American Goulash",
    calories: 367,
    protein: 22,
    carbs: 37,
    fat: 14,
    ingredients: [
      "Ground Beef", "Onion", "Garlic", "Paprika", 
      "Tomato Sauce", "Diced Tomatoes", "Elbow Macaroni", 
      "Cheddar Cheese", "Olive Oil", "Worcestershire Sauce"
    ]
  },
  "green bean casserole": {
    name: "Green Bean Casserole",
    calories: 191,
    protein: 4,
    carbs: 22,
    fat: 10,
    ingredients: [
      "Green Beans", "Cream of Mushroom Soup", "Fried Onions", 
      "Butter", "Milk", "Black Pepper", "Salt"
    ]
  },
  "mac and cheese": {
    name: "Mac and Cheese",
    calories: 350,
    protein: 13,
    carbs: 43,
    fat: 15,
    ingredients: [
      "Elbow Macaroni", "Cheddar Cheese", "Milk", "Butter", 
      "Flour", "Salt", "Black Pepper", "Breadcrumbs"
    ]
  },
  "meatloaf": {
    name: "Meatloaf",
    calories: 305,
    protein: 28,
    carbs: 12,
    fat: 17,
    ingredients: [
      "Ground Beef", "Onion", "Breadcrumbs", "Egg", "Ketchup", 
      "Worcestershire Sauce", "Garlic", "Salt", "Black Pepper"
    ]
  },
  "chicken pot pie": {
    name: "Chicken Pot Pie",
    calories: 367,
    protein: 19,
    carbs: 30,
    fat: 19,
    ingredients: [
      "Chicken", "Pie Crust", "Carrots", "Peas", "Celery", 
      "Onion", "Flour", "Chicken Broth", "Milk", "Butter"
    ]
  },
  "cheeseburger": {
    name: "Cheeseburger",
    calories: 520,
    protein: 31,
    carbs: 40,
    fat: 26,
    ingredients: [
      "Ground Beef", "Hamburger Bun", "American Cheese", "Lettuce", 
      "Tomato", "Onion", "Pickles", "Ketchup", "Mustard", "Mayonnaise"
    ]
  },
  "buffalo wings": {
    name: "Buffalo Wings",
    calories: 440,
    protein: 39,
    carbs: 3,
    fat: 31,
    ingredients: [
      "Chicken Wings", "Hot Sauce", "Butter", "Garlic Powder", 
      "Salt", "Black Pepper", "Vegetable Oil"
    ]
  },
  
  // Filipino dishes
  "filipino breakfast": {
    name: "Filipino Breakfast (Silog)",
    calories: 650,
    protein: 28,
    carbs: 85,
    fat: 24,
    ingredients: [
      "Garlic Rice (Sinangag)", "Fried Egg (Sunny Side Up)", 
      "Meat (Tocino/Longganisa/Tapa)", "Tomatoes", "Vinegar Dip"
    ]
  },
  "adobo": {
    name: "Adobo",
    calories: 460,
    protein: 35,
    carbs: 12,
    fat: 30,
    ingredients: [
      "Chicken or Pork", "Soy Sauce", "Vinegar", "Garlic", 
      "Bay Leaves", "Black Peppercorns", "Sugar", "Cooking Oil"
    ]
  },
  "chicken adobo": {
    name: "Chicken Adobo",
    calories: 440,
    protein: 38,
    carbs: 10,
    fat: 27,
    ingredients: [
      "Chicken", "Soy Sauce", "Vinegar", "Garlic", 
      "Bay Leaves", "Black Peppercorns", "Sugar", "Cooking Oil"
    ]
  },
  "pork adobo": {
    name: "Pork Adobo",
    calories: 480,
    protein: 32,
    carbs: 10,
    fat: 34,
    ingredients: [
      "Pork", "Soy Sauce", "Vinegar", "Garlic", 
      "Bay Leaves", "Black Peppercorns", "Sugar", "Cooking Oil"
    ]
  },
  "sinigang": {
    name: "Sinigang",
    calories: 320,
    protein: 25,
    carbs: 18,
    fat: 16,
    ingredients: [
      "Pork/Shrimp/Fish", "Tamarind", "Tomatoes", "Onion", 
      "String Beans", "Radish", "Eggplant", "Taro", "Fish Sauce"
    ]
  },
  "pork sinigang": {
    name: "Pork Sinigang",
    calories: 340,
    protein: 28,
    carbs: 18,
    fat: 18,
    ingredients: [
      "Pork", "Tamarind", "Tomatoes", "Onion", "String Beans", 
      "Radish", "Eggplant", "Taro", "Fish Sauce"
    ]
  },
  "kare kare": {
    name: "Kare Kare",
    calories: 520,
    protein: 30,
    carbs: 33,
    fat: 30,
    ingredients: [
      "Oxtail/Beef/Tripe", "Peanut Butter", "Annatto Seeds", "Eggplant", 
      "String Beans", "Banana Blossom", "Shrimp Paste", "Rice"
    ]
  },
  "lechon": {
    name: "Lechon",
    calories: 650,
    protein: 45,
    carbs: 2,
    fat: 52,
    ingredients: [
      "Whole Pig", "Lemongrass", "Garlic", "Salt", 
      "Black Pepper", "Star Anise", "Bay Leaves"
    ]
  },
  "pancit": {
    name: "Pancit",
    calories: 380,
    protein: 17,
    carbs: 52,
    fat: 12,
    ingredients: [
      "Rice Noodles/Egg Noodles", "Chicken", "Shrimp", "Cabbage", 
      "Carrots", "Snow Peas", "Soy Sauce", "Calamansi/Lemon"
    ]
  },
  "pancit canton": {
    name: "Pancit Canton",
    calories: 400,
    protein: 18,
    carbs: 55,
    fat: 13,
    ingredients: [
      "Egg Noodles", "Chicken", "Shrimp", "Cabbage", "Carrots", 
      "Snow Peas", "Soy Sauce", "Calamansi/Lemon"
    ]
  },
  "pancit bihon": {
    name: "Pancit Bihon",
    calories: 360,
    protein: 16,
    carbs: 50,
    fat: 11,
    ingredients: [
      "Rice Noodles", "Chicken", "Shrimp", "Cabbage", "Carrots", 
      "Snow Peas", "Soy Sauce", "Calamansi/Lemon"
    ]
  },
  "sisig": {
    name: "Sisig",
    calories: 580,
    protein: 38,
    carbs: 8,
    fat: 45,
    ingredients: [
      "Pork Face/Belly", "Chicken Liver", "Onion", "Chili", 
      "Calamansi", "Mayonnaise", "Egg"
    ]
  },
  "halo halo": {
    name: "Halo-Halo",
    calories: 400,
    protein: 5,
    carbs: 75,
    fat: 10,
    ingredients: [
      "Shaved Ice", "Evaporated Milk", "Sweet Beans", "Nata de Coco", 
      "Kaong", "Ube Halaya", "Leche Flan", "Jackfruit", "Ice Cream"
    ]
  },
  "lumpia": {
    name: "Lumpia",
    calories: 210,
    protein: 10,
    carbs: 20,
    fat: 11,
    ingredients: [
      "Ground Pork", "Carrots", "Onions", "Garlic", 
      "Lumpia Wrapper", "Cooking Oil", "Sweet Chili Sauce"
    ]
  },
  "tocino": {
    name: "Tocino",
    calories: 320,
    protein: 24,
    carbs: 12,
    fat: 20,
    ingredients: [
      "Pork Belly/Shoulder", "Sugar", "Salt", "Anisette Wine", 
      "Annatto Powder", "Ketchup", "Black Pepper"
    ]
  },
  "longganisa": {
    name: "Longganisa",
    calories: 300,
    protein: 17,
    carbs: 8,
    fat: 23,
    ingredients: [
      "Ground Pork", "Garlic", "Sugar", "Vinegar", 
      "Soy Sauce", "Black Pepper", "Salt", "Annatto"
    ]
  },
  "tapsilog": {
    name: "Tapsilog",
    calories: 670,
    protein: 32,
    carbs: 80,
    fat: 25,
    ingredients: [
      "Beef Tapa", "Garlic Rice", "Fried Egg", "Tomatoes", "Vinegar Dip"
    ]
  },
  "tosilog": {
    name: "Tosilog",
    calories: 650,
    protein: 25,
    carbs: 85,
    fat: 24,
    ingredients: [
      "Tocino", "Garlic Rice", "Fried Egg", "Tomatoes", "Vinegar Dip"
    ]
  },
  "longsilog": {
    name: "Longsilog",
    calories: 640,
    protein: 24,
    carbs: 82,
    fat: 26,
    ingredients: [
      "Longganisa", "Garlic Rice", "Fried Egg", "Tomatoes", "Vinegar Dip"
    ]
  },
  "dinuguan": {
    name: "Dinuguan",
    calories: 460,
    protein: 35,
    carbs: 10,
    fat: 32,
    ingredients: [
      "Pork", "Pork Blood", "Vinegar", "Garlic", "Onion", 
      "Chili Pepper", "Cooking Oil", "Salt", "Black Pepper"
    ]
  },
  "sinigang na baboy": {
    name: "Sinigang na Baboy",
    calories: 340,
    protein: 28,
    carbs: 18,
    fat: 18,
    ingredients: [
      "Pork", "Tamarind", "Tomatoes", "Onion", "String Beans", 
      "Radish", "Eggplant", "Taro", "Fish Sauce"
    ]
  },
  
  // Mexican dishes
  "tacos": {
    name: "Tacos",
    calories: 210,
    protein: 12,
    carbs: 20,
    fat: 9,
    ingredients: [
      "Corn Tortillas", "Ground Beef", "Lettuce", "Tomato", 
      "Onion", "Cheese", "Salsa", "Cilantro", "Lime"
    ]
  },
  "quesadilla": {
    name: "Quesadilla",
    calories: 350,
    protein: 15,
    carbs: 27,
    fat: 20,
    ingredients: [
      "Flour Tortilla", "Cheese", "Chicken/Beef", "Bell Peppers", 
      "Onion", "Sour Cream", "Guacamole", "Salsa"
    ]
  },
  "burrito": {
    name: "Burrito",
    calories: 510,
    protein: 23,
    carbs: 55,
    fat: 22,
    ingredients: [
      "Flour Tortilla", "Rice", "Beans", "Beef/Chicken", "Cheese", 
      "Lettuce", "Tomato", "Sour Cream", "Guacamole", "Salsa"
    ]
  },
  "enchiladas": {
    name: "Enchiladas",
    calories: 390,
    protein: 18,
    carbs: 40,
    fat: 17,
    ingredients: [
      "Corn Tortillas", "Chicken", "Enchilada Sauce", "Cheese", 
      "Onion", "Sour Cream", "Cilantro"
    ]
  },
  
  // Italian dishes
  "pizza": {
    name: "Pizza",
    calories: 285,
    protein: 12,
    carbs: 36,
    fat: 10,
    ingredients: [
      "Pizza Dough", "Tomato Sauce", "Mozzarella Cheese", "Pepperoni/Vegetables", 
      "Olive Oil", "Oregano", "Basil"
    ]
  },
  "lasagna": {
    name: "Lasagna",
    calories: 430,
    protein: 25,
    carbs: 35,
    fat: 22,
    ingredients: [
      "Lasagna Noodles", "Ground Beef", "Tomato Sauce", "Ricotta Cheese", 
      "Mozzarella Cheese", "Parmesan Cheese", "Onion", "Garlic", "Italian Herbs"
    ]
  },
  "spaghetti bolognese": {
    name: "Spaghetti Bolognese",
    calories: 380,
    protein: 22,
    carbs: 45,
    fat: 12,
    ingredients: [
      "Spaghetti", "Ground Beef", "Tomato Sauce", "Onion", 
      "Garlic", "Carrot", "Celery", "Red Wine", "Olive Oil", "Parmesan Cheese"
    ]
  },
  "risotto": {
    name: "Risotto",
    calories: 410,
    protein: 10,
    carbs: 67,
    fat: 12,
    ingredients: [
      "Arborio Rice", "Onion", "Garlic", "White Wine", "Chicken Broth", 
      "Parmesan Cheese", "Butter", "Olive Oil", "Parsley"
    ]
  },
  
  // Chinese dishes
  "fried rice": {
    name: "Fried Rice",
    calories: 350,
    protein: 14,
    carbs: 55,
    fat: 10,
    ingredients: [
      "Rice", "Egg", "Carrots", "Peas", "Onion", 
      "Soy Sauce", "Sesame Oil", "Green Onions"
    ]
  },
  "kung pao chicken": {
    name: "Kung Pao Chicken",
    calories: 410,
    protein: 38,
    carbs: 18,
    fat: 22,
    ingredients: [
      "Chicken", "Peanuts", "Bell Peppers", "Dried Chili Peppers", 
      "Soy Sauce", "Vinegar", "Sugar", "Cornstarch", "Cooking Oil"
    ]
  },
  "sweet and sour pork": {
    name: "Sweet and Sour Pork",
    calories: 435,
    protein: 25,
    carbs: 35,
    fat: 23,
    ingredients: [
      "Pork", "Bell Peppers", "Pineapple", "Onion", "Vinegar", 
      "Ketchup", "Sugar", "Soy Sauce", "Cornstarch", "Cooking Oil"
    ]
  },
  "chow mein": {
    name: "Chow Mein",
    calories: 380,
    protein: 15,
    carbs: 54,
    fat: 14,
    ingredients: [
      "Egg Noodles", "Chicken/Beef", "Cabbage", "Carrots", 
      "Bean Sprouts", "Onion", "Soy Sauce", "Oyster Sauce", "Sesame Oil"
    ]
  },
  
  // Japanese dishes
  "sushi": {
    name: "Sushi",
    calories: 280,
    protein: 10,
    carbs: 45,
    fat: 5,
    ingredients: [
      "Sushi Rice", "Nori", "Fish (Salmon/Tuna)", "Avocado", 
      "Cucumber", "Soy Sauce", "Wasabi", "Pickled Ginger"
    ]
  },
  "ramen": {
    name: "Ramen",
    calories: 450,
    protein: 20,
    carbs: 60,
    fat: 15,
    ingredients: [
      "Ramen Noodles", "Pork/Chicken", "Soft-boiled Egg", "Green Onions", 
      "Nori", "Bean Sprouts", "Corn", "Miso/Soy Broth"
    ]
  },
  "tempura": {
    name: "Tempura",
    calories: 370,
    protein: 12,
    carbs: 28,
    fat: 24,
    ingredients: [
      "Shrimp/Vegetables", "Tempura Batter", "Cooking Oil", 
      "Dipping Sauce", "Grated Daikon"
    ]
  },
  "teriyaki chicken": {
    name: "Teriyaki Chicken",
    calories: 375,
    protein: 35,
    carbs: 25,
    fat: 14,
    ingredients: [
      "Chicken", "Soy Sauce", "Mirin", "Sake", "Sugar", 
      "Ginger", "Garlic", "Green Onions", "Sesame Seeds"
    ]
  },
  
  // Indian dishes
  "chicken tikka masala": {
    name: "Chicken Tikka Masala",
    calories: 410,
    protein: 32,
    carbs: 15,
    fat: 25,
    ingredients: [
      "Chicken", "Yogurt", "Tomato Sauce", "Cream", "Onion", 
      "Garlic", "Ginger", "Garam Masala", "Turmeric", "Cumin"
    ]
  },
  "butter chicken": {
    name: "Butter Chicken",
    calories: 440,
    protein: 30,
    carbs: 12,
    fat: 30,
    ingredients: [
      "Chicken", "Butter", "Cream", "Tomato Puree", "Onion", 
      "Garlic", "Ginger", "Garam Masala", "Fenugreek", "Cardamom"
    ]
  },
  "biryani": {
    name: "Biryani",
    calories: 470,
    protein: 28,
    carbs: 60,
    fat: 15,
    ingredients: [
      "Basmati Rice", "Chicken/Lamb", "Yogurt", "Onion", "Tomatoes", 
      "Garlic", "Ginger", "Biryani Masala", "Saffron", "Mint", "Cilantro"
    ]
  },
  "samosa": {
    name: "Samosa",
    calories: 260,
    protein: 5,
    carbs: 28,
    fat: 14,
    ingredients: [
      "Pastry Dough", "Potatoes", "Peas", "Onion", "Cumin", 
      "Coriander", "Garam Masala", "Cooking Oil"
    ]
  },
  
  // Thai dishes
  "pad thai": {
    name: "Pad Thai",
    calories: 410,
    protein: 17,
    carbs: 56,
    fat: 14,
    ingredients: [
      "Rice Noodles", "Chicken/Shrimp/Tofu", "Eggs", "Bean Sprouts", 
      "Peanuts", "Green Onions", "Tamarind Paste", "Fish Sauce", "Lime"
    ]
  },
  "green curry": {
    name: "Green Curry",
    calories: 385,
    protein: 25,
    carbs: 16,
    fat: 26,
    ingredients: [
      "Chicken/Beef", "Coconut Milk", "Green Curry Paste", "Eggplant", 
      "Bamboo Shoots", "Basil", "Fish Sauce", "Sugar", "Kaffir Lime Leaves"
    ]
  },
  "tom yum soup": {
    name: "Tom Yum Soup",
    calories: 240,
    protein: 18,
    carbs: 12,
    fat: 12,
    ingredients: [
      "Shrimp", "Lemongrass", "Galangal", "Kaffir Lime Leaves", 
      "Mushrooms", "Tomatoes", "Lime Juice", "Fish Sauce", "Chili"
    ]
  },
  "mango sticky rice": {
    name: "Mango Sticky Rice",
    calories: 350,
    protein: 5,
    carbs: 68,
    fat: 7,
    ingredients: [
      "Sticky Rice", "Coconut Milk", "Mango", "Sugar", "Salt"
    ]
  },
  
  // Middle Eastern dishes
  "hummus": {
    name: "Hummus",
    calories: 210,
    protein: 6,
    carbs: 25,
    fat: 10,
    ingredients: [
      "Chickpeas", "Tahini", "Olive Oil", "Lemon Juice", 
      "Garlic", "Cumin", "Salt", "Paprika"
    ]
  },
  "falafel": {
    name: "Falafel",
    calories: 330,
    protein: 12,
    carbs: 32,
    fat: 17,
    ingredients: [
      "Chickpeas", "Onion", "Garlic", "Parsley", "Cilantro", 
      "Cumin", "Coriander", "Flour", "Cooking Oil"
    ]
  },
  "shawarma": {
    name: "Shawarma",
    calories: 380,
    protein: 28,
    carbs: 35,
    fat: 15,
    ingredients: [
      "Chicken/Beef", "Pita Bread", "Tahini Sauce", "Garlic Sauce", 
      "Lettuce", "Tomato", "Onion", "Pickles", "Sumac", "Cumin"
    ]
  },
  "tabbouleh": {
    name: "Tabbouleh",
    calories: 180,
    protein: 4,
    carbs: 28,
    fat: 7,
    ingredients: [
      "Bulgur Wheat", "Parsley", "Mint", "Tomatoes", 
      "Onion", "Lemon Juice", "Olive Oil", "Salt"
    ]
  },
  
  // Greek dishes
  "greek salad": {
    name: "Greek Salad",
    calories: 230,
    protein: 7,
    carbs: 12,
    fat: 17,
    ingredients: [
      "Cucumber", "Tomatoes", "Red Onion", "Feta Cheese", 
      "Kalamata Olives", "Olive Oil", "Lemon Juice", "Oregano"
    ]
  },
  "moussaka": {
    name: "Moussaka",
    calories: 410,
    protein: 20,
    carbs: 22,
    fat: 28,
    ingredients: [
      "Eggplant", "Ground Lamb/Beef", "Potatoes", "Tomatoes", 
      "Onion", "Bechamel Sauce", "Cinnamon", "Nutmeg", "Olive Oil"
    ]
  },
  "gyro": {
    name: "Gyro",
    calories: 365,
    protein: 25,
    carbs: 33,
    fat: 16,
    ingredients: [
      "Lamb/Beef/Chicken", "Pita Bread", "Tzatziki Sauce", 
      "Lettuce", "Tomato", "Onion", "Feta Cheese"
    ]
  },
  "spanakopita": {
    name: "Spanakopita",
    calories: 320,
    protein: 8,
    carbs: 32,
    fat: 18,
    ingredients: [
      "Phyllo Dough", "Spinach", "Feta Cheese", "Onion", 
      "Dill", "Parsley", "Eggs", "Olive Oil"
    ]
  },
  
  // French dishes
  "coq au vin": {
    name: "Coq au Vin",
    calories: 390,
    protein: 34,
    carbs: 6,
    fat: 24,
    ingredients: [
      "Chicken", "Red Wine", "Bacon", "Mushrooms", "Pearl Onions", 
      "Garlic", "Thyme", "Bay Leaf", "Butter", "Flour"
    ]
  },
  "ratatouille": {
    name: "Ratatouille",
    calories: 190,
    protein: 4,
    carbs: 22,
    fat: 11,
    ingredients: [
      "Eggplant", "Zucchini", "Bell Peppers", "Tomatoes", 
      "Onion", "Garlic", "Olive Oil", "Herbs de Provence", "Basil"
    ]
  },
  "beef bourguignon": {
    name: "Beef Bourguignon",
    calories: 480,
    protein: 42,
    carbs: 16,
    fat: 26,
    ingredients: [
      "Beef Chuck", "Red Wine", "Bacon", "Mushrooms", "Pearl Onions", 
      "Carrots", "Garlic", "Thyme", "Bay Leaf", "Butter", "Flour"
    ]
  },
  "croissant": {
    name: "Croissant",
    calories: 235,
    protein: 5,
    carbs: 26,
    fat: 12,
    ingredients: [
      "Flour", "Butter", "Sugar", "Yeast", "Milk", "Salt", "Egg Wash"
    ]
  },
  
  // Korean dishes
  "bibimbap": {
    name: "Bibimbap",
    calories: 425,
    protein: 20,
    carbs: 58,
    fat: 12,
    ingredients: [
      "Rice", "Beef", "Egg", "Spinach", "Bean Sprouts", "Carrots", 
      "Zucchini", "Mushrooms", "Gochujang", "Sesame Oil", "Soy Sauce"
    ]
  },
  "bulgogi": {
    name: "Bulgogi",
    calories: 370,
    protein: 32,
    carbs: 14,
    fat: 19,
    ingredients: [
      "Beef", "Soy Sauce", "Sugar", "Sesame Oil", "Garlic", 
      "Ginger", "Onion", "Sesame Seeds", "Green Onions"
    ]
  },
  "kimchi": {
    name: "Kimchi",
    calories: 30,
    protein: 2,
    carbs: 5,
    fat: 0,
    ingredients: [
      "Napa Cabbage", "Korean Radish", "Green Onions", "Garlic", 
      "Ginger", "Red Pepper Flakes", "Fish Sauce", "Salt"
    ]
  },
  "korean fried chicken": {
    name: "Korean Fried Chicken",
    calories: 460,
    protein: 34,
    carbs: 26,
    fat: 26,
    ingredients: [
      "Chicken", "Flour", "Cornstarch", "Gochujang", "Soy Sauce", 
      "Honey", "Garlic", "Ginger", "Cooking Oil", "Sesame Seeds"
    ]
  },
  
  // Vietnamese dishes
  "pho": {
    name: "Pho",
    calories: 370,
    protein: 28,
    carbs: 48,
    fat: 7,
    ingredients: [
      "Rice Noodles", "Beef/Chicken", "Bean Sprouts", "Thai Basil", 
      "Lime", "Onion", "Ginger", "Star Anise", "Cinnamon", "Fish Sauce"
    ]
  },
  "banh mi": {
    name: "Banh Mi",
    calories: 390,
    protein: 20,
    carbs: 42,
    fat: 16,
    ingredients: [
      "French Baguette", "Pork/Chicken", "Cucumber", "Carrots", 
      "Daikon", "Cilantro", "Mayonnaise", "Pate", "Soy Sauce"
    ]
  },
  "spring rolls": {
    name: "Spring Rolls",
    calories: 160,
    protein: 4,
    carbs: 26,
    fat: 5,
    ingredients: [
      "Rice Paper", "Shrimp/Pork", "Lettuce", "Mint", "Rice Vermicelli", 
      "Bean Sprouts", "Cilantro", "Peanut Sauce"
    ]
  },
  
  // Healthy options
  "açaí bowl": {
    name: "Açaí Bowl",
    calories: 320,
    protein: 6,
    carbs: 65,
    fat: 6,
    ingredients: [
      "Açaí Puree", "Banana", "Strawberries", "Blueberries", 
      "Granola", "Honey", "Coconut Flakes", "Chia Seeds"
    ]
  },
  "avocado toast": {
    name: "Avocado Toast",
    calories: 280,
    protein: 8,
    carbs: 30,
    fat: 14,
    ingredients: [
      "Whole Grain Bread", "Avocado", "Lemon Juice", "Red Pepper Flakes", 
      "Salt", "Black Pepper", "Olive Oil", "Eggs (optional)"
    ]
  },
  "quinoa bowl": {
    name: "Quinoa Bowl",
    calories: 410,
    protein: 16,
    carbs: 55,
    fat: 14,
    ingredients: [
      "Quinoa", "Chickpeas", "Roasted Vegetables", "Avocado", 
      "Kale", "Tahini Dressing", "Lemon Juice", "Olive Oil"
    ]
  },
  
  // Breakfast dishes
  "omelette": {
    name: "Omelette",
    calories: 310,
    protein: 21,
    carbs: 3,
    fat: 24,
    ingredients: [
      "Eggs", "Cheese", "Bell Peppers", "Onion", "Mushrooms", 
      "Spinach", "Butter", "Salt", "Black Pepper"
    ]
  },
  "pancakes": {
    name: "Pancakes",
    calories: 350,
    protein: 8,
    carbs: 60,
    fat: 9,
    ingredients: [
      "Flour", "Milk", "Eggs", "Sugar", "Baking Powder", 
      "Butter", "Maple Syrup", "Berries"
    ]
  },
  "french toast": {
    name: "French Toast",
    calories: 340,
    protein: 10,
    carbs: 45,
    fat: 13,
    ingredients: [
      "Bread", "Eggs", "Milk", "Vanilla Extract", "Cinnamon", 
      "Butter", "Maple Syrup", "Powdered Sugar", "Berries"
    ]
  },
  "breakfast burrito": {
    name: "Breakfast Burrito",
    calories: 430,
    protein: 23,
    carbs: 40,
    fat: 20,
    ingredients: [
      "Flour Tortilla", "Eggs", "Bacon/Sausage", "Potatoes", 
      "Cheese", "Salsa", "Avocado", "Bell Peppers", "Onion"
    ]
  },
  
  // Regional American Cuisine
  "jambalaya": {
    name: "Jambalaya",
    calories: 430,
    protein: 28,
    carbs: 48,
    fat: 15,
    ingredients: [
      "Rice", "Chicken", "Andouille Sausage", "Shrimp", "Bell Peppers", 
      "Celery", "Onion", "Garlic", "Cajun Seasoning", "Tomatoes", "Bay Leaves"
    ]
  },
  "gumbo": {
    name: "Gumbo",
    calories: 410,
    protein: 25,
    carbs: 36,
    fat: 19,
    ingredients: [
      "Chicken", "Andouille Sausage", "Shrimp", "Okra", "Bell Peppers", 
      "Celery", "Onion", "Garlic", "Roux", "Filé Powder", "Rice", "Cajun Seasoning"
    ]
  },
  "clam chowder": {
    name: "New England Clam Chowder",
    calories: 350,
    protein: 18,
    carbs: 30,
    fat: 17,
    ingredients: [
      "Clams", "Potatoes", "Bacon", "Onion", "Celery", 
      "Flour", "Clam Juice", "Heavy Cream", "Thyme", "Bay Leaves"
    ]
  },
  "cornbread": {
    name: "Cornbread",
    calories: 210,
    protein: 5,
    carbs: 30,
    fat: 9,
    ingredients: [
      "Cornmeal", "Flour", "Butter", "Eggs", "Milk", 
      "Sugar", "Baking Powder", "Salt"
    ]
  },
  "biscuits and gravy": {
    name: "Biscuits and Gravy",
    calories: 520,
    protein: 15,
    carbs: 42,
    fat: 32,
    ingredients: [
      "Flour", "Baking Powder", "Butter", "Milk", "Salt", 
      "Sausage", "Black Pepper", "Heavy Cream"
    ]
  },
  "pulled pork": {
    name: "Pulled Pork",
    calories: 390,
    protein: 35,
    carbs: 12,
    fat: 22,
    ingredients: [
      "Pork Shoulder", "BBQ Sauce", "Brown Sugar", "Paprika", 
      "Garlic Powder", "Onion Powder", "Mustard Powder", "Cayenne Pepper"
    ]
  },
  "chicken and dumplings": {
    name: "Chicken and Dumplings",
    calories: 380,
    protein: 24,
    carbs: 32,
    fat: 16,
    ingredients: [
      "Chicken", "Carrots", "Celery", "Onion", "Flour", 
      "Chicken Broth", "Baking Powder", "Milk", "Butter", "Thyme"
    ]
  },
  "shrimp and grits": {
    name: "Shrimp and Grits",
    calories: 410,
    protein: 26,
    carbs: 35,
    fat: 19,
    ingredients: [
      "Shrimp", "Grits", "Bacon", "Bell Peppers", "Onion", 
      "Garlic", "Butter", "Cheese", "Lemon Juice", "Cajun Seasoning"
    ]
  },
  
  // African Dishes
  "jollof rice": {
    name: "Jollof Rice",
    calories: 380,
    protein: 8,
    carbs: 65,
    fat: 10,
    ingredients: [
      "Rice", "Tomatoes", "Onion", "Bell Peppers", "Scotch Bonnet Pepper", 
      "Vegetable Oil", "Chicken Broth", "Thyme", "Curry Powder", "Bay Leaves"
    ]
  },
  "egusi soup": {
    name: "Egusi Soup",
    calories: 420,
    protein: 28,
    carbs: 14,
    fat: 30,
    ingredients: [
      "Melon Seeds", "Spinach/Bitter Leaf", "Beef/Chicken", "Palm Oil", 
      "Stockfish", "Crayfish", "Onion", "Chili Peppers", "Salt", "Bouillon Cube"
    ]
  },
  "doro wat": {
    name: "Doro Wat",
    calories: 390,
    protein: 32,
    carbs: 15,
    fat: 22,
    ingredients: [
      "Chicken", "Onion", "Berbere Spice", "Niter Kibbeh", "Garlic", 
      "Ginger", "Hard-boiled Eggs", "Lemon Juice", "Cardamom"
    ]
  },
  "injera": {
    name: "Injera",
    calories: 180,
    protein: 5,
    carbs: 35,
    fat: 2,
    ingredients: [
      "Teff Flour", "Water", "Salt"
    ]
  },
  "tagine": {
    name: "Moroccan Tagine",
    calories: 410,
    protein: 26,
    carbs: 30,
    fat: 22,
    ingredients: [
      "Lamb/Chicken", "Onion", "Carrots", "Potatoes", "Chickpeas", 
      "Apricots", "Almonds", "Cumin", "Cinnamon", "Turmeric", "Harissa", "Olive Oil"
    ]
  },
  
  // Caribbean Dishes
  "jerk chicken": {
    name: "Jerk Chicken",
    calories: 380,
    protein: 38,
    carbs: 6,
    fat: 22,
    ingredients: [
      "Chicken", "Scotch Bonnet Pepper", "Allspice", "Thyme", "Garlic", 
      "Ginger", "Brown Sugar", "Soy Sauce", "Lime Juice", "Cinnamon"
    ]
  },
  "rice and peas": {
    name: "Rice and Peas",
    calories: 320,
    protein: 8,
    carbs: 60,
    fat: 5,
    ingredients: [
      "Rice", "Kidney Beans", "Coconut Milk", "Thyme", "Garlic", 
      "Scallions", "Scotch Bonnet Pepper", "Allspice"
    ]
  },
  "callaloo": {
    name: "Callaloo",
    calories: 160,
    protein: 10,
    carbs: 18,
    fat: 6,
    ingredients: [
      "Callaloo Leaves/Spinach", "Okra", "Onion", "Garlic", 
      "Coconut Milk", "Scotch Bonnet Pepper", "Thyme"
    ]
  },
  "ropa vieja": {
    name: "Ropa Vieja",
    calories: 350,
    protein: 40,
    carbs: 12,
    fat: 14,
    ingredients: [
      "Flank Steak", "Bell Peppers", "Onion", "Garlic", "Tomatoes", 
      "Olives", "Capers", "Cumin", "Bay Leaves", "Olive Oil"
    ]
  },
  "mofongo": {
    name: "Mofongo",
    calories: 390,
    protein: 8,
    carbs: 48,
    fat: 20,
    ingredients: [
      "Green Plantains", "Garlic", "Pork Cracklings", "Olive Oil", "Chicken Broth"
    ]
  },
  
  // Mediterranean Dishes
  "paella": {
    name: "Paella",
    calories: 450,
    protein: 30,
    carbs: 55,
    fat: 14,
    ingredients: [
      "Rice", "Chicken", "Chorizo", "Shrimp", "Mussels", "Bell Peppers", 
      "Peas", "Saffron", "Paprika", "Tomatoes", "Olive Oil"
    ]
  },
  "gazpacho": {
    name: "Gazpacho",
    calories: 120,
    protein: 3,
    carbs: 18,
    fat: 5,
    ingredients: [
      "Tomatoes", "Cucumber", "Bell Peppers", "Onion", "Garlic", 
      "Olive Oil", "Vinegar", "Bread", "Salt", "Pepper"
    ]
  },
  "caponata": {
    name: "Caponata",
    calories: 180,
    protein: 3,
    carbs: 22,
    fat: 9,
    ingredients: [
      "Eggplant", "Celery", "Onion", "Tomatoes", "Olives", 
      "Capers", "Vinegar", "Sugar", "Pine Nuts", "Olive Oil"
    ]
  },
  
  // Desserts
  "tiramisu": {
    name: "Tiramisu",
    calories: 380,
    protein: 7,
    carbs: 40,
    fat: 20,
    ingredients: [
      "Ladyfingers", "Mascarpone", "Eggs", "Sugar", "Coffee", 
      "Cocoa Powder", "Coffee Liqueur"
    ]
  },
  "apple pie": {
    name: "Apple Pie",
    calories: 350,
    protein: 3,
    carbs: 55,
    fat: 14,
    ingredients: [
      "Pie Crust", "Apples", "Sugar", "Cinnamon", "Nutmeg", 
      "Butter", "Lemon Juice"
    ]
  },
  "cheesecake": {
    name: "Cheesecake",
    calories: 420,
    protein: 8,
    carbs: 36,
    fat: 28,
    ingredients: [
      "Cream Cheese", "Sugar", "Eggs", "Sour Cream", "Vanilla Extract", 
      "Graham Cracker Crust", "Butter"
    ]
  },
  "crème brûlée": {
    name: "Crème Brûlée",
    calories: 310,
    protein: 5,
    carbs: 28,
    fat: 22,
    ingredients: [
      "Heavy Cream", "Egg Yolks", "Sugar", "Vanilla Bean", "Salt"
    ]
  },
  "churros": {
    name: "Churros",
    calories: 240,
    protein: 4,
    carbs: 35,
    fat: 11,
    ingredients: [
      "Flour", "Water", "Butter", "Salt", "Sugar", "Cinnamon", "Vegetable Oil"
    ]
  },
  
  // Special Diets
  "vegan buddha bowl": {
    name: "Vegan Buddha Bowl",
    calories: 380,
    protein: 12,
    carbs: 52,
    fat: 16,
    ingredients: [
      "Quinoa", "Sweet Potatoes", "Chickpeas", "Avocado", "Kale", 
      "Broccoli", "Tahini", "Lemon Juice", "Olive Oil", "Hemp Seeds"
    ]
  },
  "keto salmon": {
    name: "Keto Salmon with Avocado",
    calories: 450,
    protein: 35,
    carbs: 5,
    fat: 32,
    ingredients: [
      "Salmon", "Avocado", "Asparagus", "Butter", "Garlic", 
      "Lemon", "Dill", "Olive Oil", "Salt", "Pepper"
    ]
  },
  "gluten free pizza": {
    name: "Gluten-Free Pizza",
    calories: 320,
    protein: 14,
    carbs: 40,
    fat: 12,
    ingredients: [
      "Cauliflower Crust/Gluten-Free Flour", "Tomato Sauce", "Mozzarella Cheese", 
      "Basil", "Olive Oil", "Toppings of Choice"
    ]
  },
  "paleo steak": {
    name: "Paleo Steak and Vegetables",
    calories: 410,
    protein: 36,
    carbs: 12,
    fat: 24,
    ingredients: [
      "Grass-fed Steak", "Broccoli", "Carrots", "Mushrooms", "Olive Oil", 
      "Garlic", "Rosemary", "Thyme", "Salt", "Pepper"
    ]
  }
};


export function useNutritionService() {
  const { db, useFirebase  } = useDatabaseType();
  const storageService = useStorageService();

  /**
   * Add a food log entry
   */
  const addFoodLog = async (foodLog: Omit<FoodLog, 'id' | 'createdAt'>): Promise<string> => {
    try {
      // Add timestamp
      const now = new Date().toISOString();
      const logData = {
        ...foodLog,
        createdAt: now
      };

      // Add to database
      const collection = 'food_logs';
      const id = await db.createDocument(collection, logData);
      
      return id;
    } catch (error) {
      console.error('Error adding food log:', error);
      throw error;
    }
  };

  /**
   * Get food logs for a specific date
   */
  const getFoodLogsForDate = async (userId: string, date: string): Promise<FoodLog[]> => {
    try {
      const collection = 'food_logs';
      const filters = [
        { field: 'userId', operator: '==', value: userId },
        { field: 'date', operator: '==', value: date }
      ];
      
      const logs = await db.queryDocuments(collection, filters);
      return logs as FoodLog[];
    } catch (error) {
      console.error(`Error getting food logs for date ${date}:`, error);
      throw error;
    }
  };

  /**
   * Get food logs for a date range
   */
  const getFoodLogsForDateRange = async (userId: string, startDate: string, endDate: string): Promise<FoodLog[]> => {
    try {
      const collection = 'food_logs';
      const filters = [
        { field: 'userId', operator: '==', value: userId },
        { field: 'date', operator: '>=', value: startDate },
        { field: 'date', operator: '<=', value: endDate }
      ];
      
      const logs = await db.queryDocuments(collection, filters);
      return logs as FoodLog[];
    } catch (error) {
      console.error(`Error getting food logs for date range ${startDate} to ${endDate}:`, error);
      throw error;
    }
  };

  /**
   * Upload food image and add to log
   */
  const uploadFoodImageAndAddLog = async (
    userId: string, 
    foodData: Omit<FoodLog, 'id' | 'imageUrl' | 'createdAt'>,
    imageUri: string
  ): Promise<string> => {
    try {
      // Upload image first
      const imageUrl = await storageService.uploadFoodImage(userId, imageUri);
      
      // Then add food log with image URL
      const logData = {
        ...foodData,
        userId,
        imageUrl
      };
      
      return await addFoodLog(logData);
    } catch (error) {
      console.error('Error uploading food image and adding log:', error);
      throw error;
    }
  };

  /**
   * Update a food log entry
   */
  const updateFoodLog = async (id: string, updates: Partial<FoodLog>): Promise<void> => {
    try {
      const collection = 'food_logs';
      await db.updateDocument(collection, id, {
        ...updates,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error(`Error updating food log ${id}:`, error);
      throw error;
    }
  };

  /**
   * Delete a food log entry
   */
  const deleteFoodLog = async (id: string, imageUrl?: string): Promise<void> => {
    try {
      // Delete the image if it exists
      if (imageUrl) {
        try {
          // Extract the path from the URL
          // Firebase URL pattern: .com/o/[PATH]?alt=...
          const path = imageUrl.includes('firebase')
            ? imageUrl.split('/o/')[1]?.split('?')[0]
            : `food-images/${imageUrl.split('food-images/')[1]}`;
          
          if (path) {
            await storageService.deleteFile(decodeURIComponent(path));
          }
        } catch (storageError) {
          console.warn('Error deleting food image:', storageError);
          // Continue with deleting the log even if image deletion fails
        }
      }
      
      // Delete the log
      const collection = 'food_logs';
      await db.deleteDocument(collection, id);
    } catch (error) {
      console.error(`Error deleting food log ${id}:`, error);
      throw error;
    }
  };

  /**
   * Get nutrition goal for user
   */
  const getNutritionGoal = async (userId: string): Promise<NutritionGoal | null> => {
    try {
      const collection = 'nutrition_goals';
      
      // First try to get by userId
      const filters = [
        { field: 'userId', operator: '==', value: userId }
      ];
      
      const goals = await db.queryDocuments(collection, filters);
      
      if (goals && goals.length > 0) {
        return goals[0] as NutritionGoal;
      }
      
      // If no goal found, try to get directly by id (assuming userId is the document id)
      try {
        const goal = await db.getDocument(collection, userId);
        if (goal) {
          return goal as NutritionGoal;
        }
      } catch (docError) {
        console.log('No direct nutrition goal found with userId as id');
      }
      
      return null;
    } catch (error) {
      console.error(`Error getting nutrition goal for user ${userId}:`, error);
      throw error;
    }
  };

  /**
   * Set nutrition goal for user
   */
  const setNutritionGoal = async (goal: NutritionGoal): Promise<void> => {
    try {
      const collection = 'nutrition_goals';
      const now = new Date().toISOString();
      
      // Check if goal already exists
      const existingGoal = await getNutritionGoal(goal.userId);
      
      if (existingGoal) {
        // Update existing goal
        await db.updateDocument(collection, existingGoal.id!, {
          ...goal,
          updatedAt: now
        });
      } else {
        // Create new goal
        await db.createDocument(collection, {
          ...goal,
          createdAt: now,
          updatedAt: now
        });
      }
    } catch (error) {
      console.error('Error setting nutrition goal:', error);
      throw error;
    }
  };

  return {
    addFoodLog,
    getFoodLogsForDate,
    getFoodLogsForDateRange,
    uploadFoodImageAndAddLog,
    updateFoodLog,
    deleteFoodLog,
    getNutritionGoal,
    setNutritionGoal
  };
}