import { GenerateAlternativeRecipeRequest, Recipe } from '@/services/openai/recipeTypes';
import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getCachedRecipes } from '@/services/recipeGenerationCacheService';

// Constants
const OFFLINE_RECIPE_TEMPLATES_KEY = 'offline_recipe_templates';
const DEFAULT_RECIPE_TEMPLATE_COUNT = 20;

// Recipe template types
interface RecipeTemplate {
  id: string;
  name: string;
  focus: string;
  description: string;
  ingredients: {
    name: string;
    amount: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  }[];
  instructions: string[];
  preparationTime: string;
  cookingTime: string;
  healthBenefits: string[];
}

/**
 * Check if the device is currently offline
 * @returns Promise resolving to a boolean indicating if device is offline
 */
export async function isOffline(): Promise<boolean> {
  const netInfoState = await NetInfo.fetch();
  return !netInfoState.isConnected || !netInfoState.isInternetReachable;
}

/**
 * Generate a recipe for offline use
 * This uses templates and adjusts them based on the original food's nutritional profile
 * @param request The recipe generation request
 * @returns A recipe generation response with adjusted templates
 */
export async function generateOfflineRecipe(
  request: GenerateAlternativeRecipeRequest
): Promise<{
  success: boolean;
  recipes?: Recipe[];
  error?: string;
}> {
  try {
    // First check cache - fast path
    const cachedRecipes = await getCachedRecipes(
      request.originalFoodName,
      request.nutritionalInfo,
      request.dietaryPreferences,
      request.allergies
    );
      
    if (cachedRecipes && cachedRecipes.length > 0) {
      console.log('Using cached recipe for faster offline response');
      return {
        success: true,
        recipes: cachedRecipes
      };
    }
    
    // Get recipe templates - limit to one for speed
    const templates = await getRecipeTemplates();
    
    if (!templates || templates.length === 0) {
      return {
        success: false,
        error: 'No offline recipe templates available'
      };
    }
    
    // Select and adapt just one template for speed
    const adaptedRecipes = adaptRecipeTemplates(templates.slice(0, 1), request);
    
    return {
      success: true,
      recipes: adaptedRecipes
    };
  } catch (error) {
    console.error('Error generating offline recipe:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error generating offline recipe'
    };
  }
}

/**
 * Load recipe templates from storage
 * @returns Array of recipe templates
 */
async function getRecipeTemplates(): Promise<RecipeTemplate[]> {
  try {
    const templatesJson = await AsyncStorage.getItem(OFFLINE_RECIPE_TEMPLATES_KEY);
    
    if (templatesJson) {
      return JSON.parse(templatesJson);
    }
    
    // If no templates exist, create and store default templates
    const defaultTemplates = createDefaultTemplates();
    await AsyncStorage.setItem(OFFLINE_RECIPE_TEMPLATES_KEY, JSON.stringify(defaultTemplates));
    
    return defaultTemplates;
  } catch (error) {
    console.error('Error loading recipe templates:', error);
    return createDefaultTemplates();
  }
}

/**
 * Create default recipe templates for various food types
 * @returns Array of default recipe templates
 */
function createDefaultTemplates(): RecipeTemplate[] {
  // High-protein meal template
  const highProteinTemplate: RecipeTemplate = {
    id: 'high-protein-1',
    name: 'High-Protein {FOOD_NAME}',
    focus: 'High Protein',
    description: 'A protein-packed version of {FOOD_NAME} that maintains the delicious flavors while boosting the protein content. Perfect for muscle recovery and satiety.',
    ingredients: [
      {
        name: 'Lean Protein (Chicken/Tofu/Fish)',
        amount: '6 oz',
        calories: 180,
        protein: 35,
        carbs: 0,
        fat: 3
      },
      {
        name: 'Mixed Vegetables',
        amount: '1 cup',
        calories: 50,
        protein: 2,
        carbs: 10,
        fat: 0
      },
      {
        name: 'Complex Carbs',
        amount: '1/2 cup',
        calories: 100,
        protein: 3,
        carbs: 20,
        fat: 1
      },
      {
        name: 'Olive Oil',
        amount: '1 tbsp',
        calories: 120,
        protein: 0,
        carbs: 0,
        fat: 14
      },
      {
        name: 'Fresh Herbs',
        amount: '2 tbsp',
        calories: 5,
        protein: 0,
        carbs: 1,
        fat: 0
      }
    ],
    instructions: [
      'Prepare protein by grilling or baking with minimal oil. Preheat the oven to 375°F (190°C) or heat a grill pan over medium-high heat. Season the protein with salt, pepper, and your choice of herbs. Cook chicken or fish for 15-20 minutes in the oven until internal temperature reaches 165°F (74°C) for chicken or 145°F (63°C) for fish. For tofu, press first to remove excess water, then bake for 25-30 minutes until golden and crisp around the edges.',
      'Steam or roast vegetables until tender-crisp. For steaming, place vegetables in a steamer basket over boiling water for 5-7 minutes until bright in color but still slightly firm. For roasting, toss with 1 teaspoon of olive oil, spread on a baking sheet, and roast at 400°F (200°C) for 15-20 minutes until edges begin to caramelize.',
      'Combine with complex carbs like quinoa or brown rice. Cook according to package instructions - generally 1 part grain to 2 parts water, simmered covered for 15-20 minutes until water is absorbed and grains are tender. Fluff with a fork before serving.',
      'Drizzle with olive oil and season with fresh herbs. Use no more than 1 tablespoon of high-quality extra virgin olive oil. Chop herbs finely and sprinkle generously over the dish, gently tossing all components together to distribute flavors evenly.',
      'Serve immediately for optimal flavor and nutrition on a warmed plate. Arrange protein as the centerpiece with vegetables and grains surrounding it for an appealing presentation.'
    ],
    preparationTime: '10 minutes',
    cookingTime: '20 minutes',
    healthBenefits: [
      'High in protein for muscle repair and growth',
      'Contains essential amino acids',
      'Good source of various vitamins and minerals'
    ]
  };

  // Low-carb meal template
  const lowCarbTemplate: RecipeTemplate = {
    id: 'low-carb-1',
    name: 'Low-Carb {FOOD_NAME}',
    focus: 'Low Carb',
    description: 'A low-carbohydrate twist on traditional {FOOD_NAME} that substitutes carb-heavy ingredients with lighter alternatives.',
    ingredients: [
      {
        name: 'Protein Source',
        amount: '4 oz',
        calories: 120,
        protein: 25,
        carbs: 0,
        fat: 2
      },
      {
        name: 'Cauliflower Rice/Zucchini Noodles',
        amount: '1 cup',
        calories: 40,
        protein: 2,
        carbs: 8,
        fat: 0
      },
      {
        name: 'Low-Carb Vegetables',
        amount: '1.5 cups',
        calories: 75,
        protein: 3,
        carbs: 15,
        fat: 0
      },
      {
        name: 'Healthy Fats',
        amount: '2 tbsp',
        calories: 180,
        protein: 0,
        carbs: 0,
        fat: 20
      },
      {
        name: 'Low-Carb Seasoning Blend',
        amount: '1 tbsp',
        calories: 10,
        protein: 0,
        carbs: 2,
        fat: 0
      }
    ],
    instructions: [
      'Prepare your low-carb base (cauliflower rice or veggie noodles). For cauliflower rice, pulse florets in a food processor until rice-sized, then microwave for 3-4 minutes or sauté in a non-stick pan for 5-6 minutes until tender but not mushy. For zucchini noodles, use a spiralizer or julienne peeler to create strands, then sauté for just 2-3 minutes until al dente - avoid overcooking to prevent sogginess.',
      'Cook protein using preferred method until done. For fish, cook in a hot pan for 3-4 minutes per side until it flakes easily with a fork. For beef or chicken, cook to an internal temperature of 145°F (63°C) or 165°F (74°C) respectively. Season with salt, pepper, and herbs during cooking to build flavor layers.',
      'Sauté low-carb vegetables until tender. Heat a large skillet over medium-high heat with 1-2 teaspoons of olive oil. Add firmer vegetables first (bell peppers, broccoli), cooking for 3-4 minutes, then add more delicate vegetables (greens, tomatoes) in the final 1-2 minutes. Season with garlic, herbs, and a pinch of salt.',
      'Combine all ingredients with healthy fats and seasonings. Gently fold together the base, protein, and vegetables in a large bowl. Add a drizzle of olive oil, avocado slices, nuts, or seeds. Incorporate fresh herbs like basil, cilantro, or parsley just before serving to preserve their bright flavors.',
      'Adjust seasoning to taste and serve immediately on a warmed plate. Finish with a squeeze of fresh lemon or lime juice to brighten flavors, and a small sprinkle of flaky sea salt if needed. Garnish with additional fresh herbs for visual appeal.'
    ],
    preparationTime: '15 minutes',
    cookingTime: '15 minutes',
    healthBenefits: [
      'Helps regulate blood sugar levels',
      'Supports ketogenic and low-carb dietary needs',
      'Rich in fiber despite lower carb content'
    ]
  };

  // Quick meal template
  const quickTemplate: RecipeTemplate = {
    id: 'quick-1',
    name: 'Quick {FOOD_NAME}',
    focus: 'Quick',
    description: 'A simplified version of {FOOD_NAME} that can be prepared in under 30 minutes without sacrificing nutrition or flavor.',
    ingredients: [
      {
        name: 'Pre-cooked Protein',
        amount: '4 oz',
        calories: 150,
        protein: 28,
        carbs: 0,
        fat: 4
      },
      {
        name: 'Pre-cut Vegetables',
        amount: '2 cups',
        calories: 80,
        protein: 4,
        carbs: 16,
        fat: 0
      },
      {
        name: 'Instant Whole Grains',
        amount: '1/2 cup',
        calories: 120,
        protein: 5,
        carbs: 25,
        fat: 1
      },
      {
        name: 'Premade Healthy Sauce',
        amount: '2 tbsp',
        calories: 60,
        protein: 1,
        carbs: 8,
        fat: 3
      },
      {
        name: 'Herbs & Spices',
        amount: 'to taste',
        calories: 5,
        protein: 0,
        carbs: 1,
        fat: 0
      }
    ],
    instructions: [
      'Heat pre-cooked protein in microwave or skillet. For microwave, place protein on a microwave-safe plate, cover loosely with a damp paper towel, and heat on high for 1-2 minutes until internal temperature reaches 165°F (74°C). For skillet method, heat 1 teaspoon oil over medium heat, add protein, and warm for 2-3 minutes until heated through, stirring occasionally to prevent sticking or burning.',
      'Steam pre-cut vegetables in microwave for perfect texture. Place vegetables in a microwave-safe bowl with 1-2 tablespoons of water. Cover with a microwave-safe lid or plate (leaving slight vent) and microwave on high for 3-4 minutes. Check for doneness - vegetables should be vibrant in color and tender-crisp. Drain any excess water immediately to prevent overcooking.',
      'Prepare instant whole grains according to package with this enhancement. Most quick-cooking grains need boiling water and 5-10 minutes of standing time. For better flavor, substitute half the water with low-sodium broth, and add a bay leaf or a few whole spices (cumin seeds, cardamom pods) to the cooking liquid. Remove spices before serving.',
      'Combine all ingredients and add sauce in a large bowl. Start with grains as the base, then add vegetables and protein. Drizzle with sauce, starting with half the recommended amount and adding more to taste. Gently fold ingredients together using a large spoon or spatula to maintain texture integrity and ensure even distribution.',
      'Top with herbs and serve immediately for maximum flavor and visual appeal. Finely chop fresh herbs like parsley, cilantro, or chives and sprinkle generously over the dish just before serving. Consider adding a small sprinkle of toasted nuts or seeds for added texture and nutrition. Serve on warmed plates to maintain the dish\'s temperature.'
    ],
    preparationTime: '5 minutes',
    cookingTime: '10 minutes',
    healthBenefits: [
      'Balanced nutrition in minimal time',
      'Perfect for busy weeknights',
      'Contains all essential macronutrients'
    ]
  };

  // Plant-based meal template
  const plantBasedTemplate: RecipeTemplate = {
    id: 'plant-based-1',
    name: 'Plant-Based {FOOD_NAME}',
    focus: 'Plant-Based',
    description: 'A completely plant-based version of {FOOD_NAME} that\'s rich in nutrients, fiber, and plant protein while maintaining a satisfying taste and texture.',
    ingredients: [
      {
        name: 'Plant Protein (Tofu/Tempeh/Legumes)',
        amount: '6 oz',
        calories: 160,
        protein: 18,
        carbs: 8,
        fat: 9
      },
      {
        name: 'Colorful Vegetables',
        amount: '2 cups',
        calories: 90,
        protein: 5,
        carbs: 18,
        fat: 0
      },
      {
        name: 'Whole Grains',
        amount: '3/4 cup',
        calories: 150,
        protein: 6,
        carbs: 30,
        fat: 2
      },
      {
        name: 'Healthy Plant Fats',
        amount: '1 tbsp',
        calories: 120,
        protein: 0,
        carbs: 0,
        fat: 14
      },
      {
        name: 'Nutritional Yeast',
        amount: '1 tbsp',
        calories: 20,
        protein: 3,
        carbs: 2,
        fat: 0
      },
      {
        name: 'Fresh Herbs',
        amount: '1/4 cup',
        calories: 10,
        protein: 1,
        carbs: 2,
        fat: 0
      }
    ],
    instructions: [
      'Prepare plant protein according to type for optimal texture and flavor. For tofu: press between paper towels for 30 minutes, then cut into 1-inch cubes and toss with 1 tablespoon cornstarch before baking at 400°F (200°C) for 25 minutes, turning halfway. For tempeh: steam for 10 minutes to remove bitterness, then marinate in soy sauce and rice vinegar for 15 minutes before cooking. For legumes: rinse thoroughly and soak dried beans overnight, or use canned varieties rinsed under cold water.',
      'Sauté or roast vegetables with minimal oil for maximum nutritional value. For roasting: toss vegetables with 1-2 teaspoons olive oil, spread in a single layer on a baking sheet, and roast at 425°F (220°C) for 20-25 minutes until edges are browned and centers are tender. For sautéing: heat a non-stick pan with 1 teaspoon oil, add vegetables in batches according to cooking time (harder vegetables first), and cook over medium-high heat, stirring frequently, until tender but still vibrant in color.',
      'Cook whole grains until tender but not mushy for the ideal texture. Rinse grains thoroughly before cooking to remove excess starch. Use the ratio of 1 part grain to 2 parts liquid for most varieties. Bring to a boil, then reduce heat to low, cover, and simmer undisturbed (quinoa: 15 minutes, brown rice: 40 minutes, farro: 30 minutes). Let stand covered for 5 minutes after cooking, then fluff with a fork.',
      'Combine all ingredients in a bowl, creating a balanced meal composition. Start with a base of cooked grains (about 1 cup), add 1-1.5 cups of vegetables arranged by color, and top with 3/4 cup of prepared plant protein. Create a visually appealing arrangement by grouping similar colors together rather than mixing everything immediately.',
      'Add plant fats, nutritional yeast, and herbs for depth of flavor and nutrition. Drizzle 1-2 teaspoons of high-quality olive oil, flaxseed oil, or tahini over the bowl. Sprinkle 1-2 tablespoons of nutritional yeast for a cheesy flavor and B-vitamin boost. Add 2-3 tablespoons of freshly chopped herbs like cilantro, basil, or parsley, and finish with a squeeze of fresh lemon or lime juice to brighten flavors.',
      'Toss well and serve warm, ensuring all components are evenly distributed. Gently fold all ingredients together just before serving to maintain the integrity of more delicate components. For optimal digestion and nutrient absorption, serve immediately while still warm. Garnish with additional herbs, microgreens, or a light sprinkle of hemp seeds for extra protein and visual appeal.'
    ],
    preparationTime: '15 minutes',
    cookingTime: '25 minutes',
    healthBenefits: [
      'High in dietary fiber and plant nutrients',
      'Cholesterol-free and heart-healthy',
      'Rich in antioxidants and phytonutrients',
      'Environmentally sustainable option'
    ]
  };

  // Return array with all templates
  return [
    highProteinTemplate,
    lowCarbTemplate,
    quickTemplate,
    plantBasedTemplate
  ];
}

/**
 * Adapt recipe templates based on the original food's nutritional profile
 * Optimized for speed over variation
 * @param templates Array of recipe templates
 * @param request Recipe generation request
 * @returns Array of adapted recipes
 */
function adaptRecipeTemplates(
  templates: RecipeTemplate[],
  request: GenerateAlternativeRecipeRequest
): Recipe[] {
  const { originalFoodName, nutritionalInfo, originalIngredients } = request;
  
  // Use just the first template for speed
  const template = templates[0];
  
  // Replace placeholders with actual food name
  const replacedName = template.name.replace('{FOOD_NAME}', originalFoodName);
  const replacedDescription = template.description.replace('{FOOD_NAME}', originalFoodName);
  
  // Simple calculation for nutritional info - no fancy math
  const calcNutrition = {
    calories: Math.round(nutritionalInfo.calories * 0.8),
    protein: Math.round(nutritionalInfo.protein), 
    carbs: Math.round(nutritionalInfo.carbs * 0.7),
    fat: Math.round(nutritionalInfo.fat * 0.7),
    fiber: 8,
    sugar: 4
  };
  
  // PRIORITY 1: Use the original scanned ingredients when available
  let recipeIngredients: {name: string, amount: string, calories: number, protein: number, carbs: number, fat: number}[] = [];
  
  if (originalIngredients && originalIngredients.length > 0) {
    console.log('Using original scanned ingredients for recipe generation');
    // Convert the detected ingredients to recipe ingredients
    recipeIngredients = originalIngredients.map(ingredient => {
      return {
        name: ingredient.name,
        amount: ingredient.estimatedAmount || '1 serving',
        calories: ingredient.calories || 0,
        protein: ingredient.protein || 0,
        carbs: ingredient.carbs || 0,
        fat: ingredient.fat || 0
      };
    });
  } else {
    // PRIORITY 2: Check if we should use specific ingredients based on food type
    let specificIngredients: {name: string, amount: string, calories: number, protein: number, carbs: number, fat: number}[] = [];
    const specificTemplates = generateSpecificIngredients(originalFoodName);
    
    if (specificTemplates.length > 0) {
      // Use a specific template that matches our food type
      // Pick one based on the template focus (e.g. high protein, low carb, etc)
      let templateIndex = 0;
      if (template.focus === "High Protein") {
        templateIndex = 0; // High protein template
      } else if (template.focus === "Low Carb") {
        templateIndex = 1; // Low carb template
      } else if (template.focus === "Quick") {
        templateIndex = 2; // Quick template
      } else if (template.focus === "Plant-Based") {
        templateIndex = 3; // Plant-based template (if available)
      }
      
      // Make sure we don't exceed the array bounds
      templateIndex = Math.min(templateIndex, specificTemplates.length - 1);
      specificIngredients = specificTemplates[templateIndex];
      recipeIngredients = specificIngredients;
    } else {
      // PRIORITY 3: Fall back to generic template ingredients
      recipeIngredients = template.ingredients;
    }
  }
  
  // Create the adapted recipe
  const adaptedRecipe: Recipe = {
    name: replacedName,
    focus: template.focus,
    description: replacedDescription,
    // Use the ingredients determined by our priority system
    ingredients: recipeIngredients,
    instructions: template.instructions,
    nutritionalInfo: calcNutrition,
    preparationTime: template.preparationTime,
    cookingTime: template.cookingTime,
    healthBenefits: template.healthBenefits,
    imagePrompt: `A healthy version of ${originalFoodName} with a ${template.focus.toLowerCase()} focus.`
  };
  
  return [adaptedRecipe];
}

/**
 * Generate more specific ingredients based on the original food name
 * This creates food-specific templates rather than generic ones
 */
function generateSpecificIngredients(foodName: string): {name: string, amount: string, calories: number, protein: number, carbs: number, fat: number}[][] {
  const lowercaseFood = foodName.toLowerCase();
  
  // Template ingredients for different food types
  if (lowercaseFood.includes('burger') || lowercaseFood.includes('sandwich')) {
    return [
      // High protein burger/sandwich
      [
        { name: 'Lean Ground Turkey or Chicken Breast', amount: '6 oz', calories: 180, protein: 36, carbs: 0, fat: 4 },
        { name: 'Whole Grain Bun', amount: '1', calories: 120, protein: 5, carbs: 22, fat: 2 },
        { name: 'Lettuce, Tomato & Onion', amount: '1/2 cup', calories: 20, protein: 1, carbs: 4, fat: 0 },
        { name: 'Avocado', amount: '1/4', calories: 80, protein: 1, carbs: 4, fat: 7 },
        { name: 'Greek Yogurt Sauce', amount: '2 tbsp', calories: 30, protein: 5, carbs: 2, fat: 0 }
      ],
      // Low carb burger/sandwich
      [
        { name: 'Grass-fed Beef Patty', amount: '5 oz', calories: 250, protein: 25, carbs: 0, fat: 18 },
        { name: 'Lettuce Wrap or Portobello Bun', amount: '2 leaves/1 cap', calories: 15, protein: 1, carbs: 3, fat: 0 },
        { name: 'Sliced Cheese', amount: '1 slice', calories: 70, protein: 5, carbs: 0, fat: 6 },
        { name: 'Tomato & Pickles', amount: '2 slices each', calories: 10, protein: 0, carbs: 2, fat: 0 },
        { name: 'Mustard & Herbs', amount: 'to taste', calories: 5, protein: 0, carbs: 1, fat: 0 }
      ],
      // Quick burger/sandwich
      [
        { name: 'Pre-cooked Chicken or Turkey Slices', amount: '4 oz', calories: 120, protein: 24, carbs: 2, fat: 3 },
        { name: 'Sprouted Grain Bread', amount: '2 slices', calories: 160, protein: 8, carbs: 30, fat: 2 },
        { name: 'Pre-sliced Vegetables', amount: '1/2 cup', calories: 25, protein: 1, carbs: 5, fat: 0 },
        { name: 'Hummus Spread', amount: '2 tbsp', calories: 70, protein: 2, carbs: 6, fat: 5 },
        { name: 'Pre-mixed Salad Greens', amount: '1 cup', calories: 10, protein: 1, carbs: 2, fat: 0 }
      ],
      // Plant-based burger/sandwich
      [
        { name: 'Lentil & Mushroom Patty', amount: '1 patty (5 oz)', calories: 150, protein: 12, carbs: 18, fat: 5 },
        { name: 'Sprouted Whole Grain Bun', amount: '1', calories: 150, protein: 6, carbs: 28, fat: 2 },
        { name: 'Sliced Avocado', amount: '1/4 avocado', calories: 80, protein: 1, carbs: 4, fat: 7 },
        { name: 'Sprouts & Roasted Red Pepper', amount: '1/4 cup & 2 slices', calories: 25, protein: 2, carbs: 5, fat: 0 },
        { name: 'Cashew Cheese Spread', amount: '1 tbsp', calories: 50, protein: 2, carbs: 2, fat: 4 }
      ]
    ];
  }
  
  if (lowercaseFood.includes('pasta') || lowercaseFood.includes('noodle')) {
    return [
      // High protein pasta/noodles
      [
        { name: 'Protein-enriched Pasta or Legume Pasta', amount: '1.5 cups cooked', calories: 200, protein: 25, carbs: 40, fat: 2 },
        { name: 'Lean Ground Turkey', amount: '4 oz', calories: 120, protein: 24, carbs: 0, fat: 3 },
        { name: 'Marinara Sauce (No Added Sugar)', amount: '1/2 cup', calories: 60, protein: 2, carbs: 12, fat: 2 },
        { name: 'Sautéed Bell Peppers & Spinach', amount: '1 cup', calories: 40, protein: 2, carbs: 8, fat: 0 },
        { name: 'Parmesan Cheese', amount: '1 tbsp', calories: 20, protein: 2, carbs: 0, fat: 1 }
      ],
      // Low carb pasta/noodles
      [
        { name: 'Zucchini or Spaghetti Squash Noodles', amount: '2 cups', calories: 70, protein: 2, carbs: 14, fat: 1 },
        { name: 'Italian Chicken Sausage', amount: '3 oz', calories: 140, protein: 14, carbs: 2, fat: 10 },
        { name: 'Low-sugar Tomato Sauce', amount: '1/2 cup', calories: 50, protein: 2, carbs: 10, fat: 1 },
        { name: 'Mushrooms & Kale', amount: '1 cup', calories: 50, protein: 3, carbs: 10, fat: 0 },
        { name: 'Olive Oil & Italian Herbs', amount: '1 tsp oil', calories: 40, protein: 0, carbs: 0, fat: 4.5 }
      ],
      // Quick pasta/noodles
      [
        { name: 'Precooked Whole Grain Pasta', amount: '1.5 cups', calories: 180, protein: 8, carbs: 36, fat: 2 },
        { name: 'Pre-cooked Shrimp or Rotisserie Chicken', amount: '4 oz', calories: 120, protein: 24, carbs: 0, fat: 2 },
        { name: 'Jarred Pesto (Lower Fat Version)', amount: '2 tbsp', calories: 100, protein: 2, carbs: 2, fat: 10 },
        { name: 'Cherry Tomatoes & Arugula', amount: '1 cup', calories: 30, protein: 1, carbs: 6, fat: 0 },
        { name: 'Pre-grated Parmesan', amount: '1 tbsp', calories: 20, protein: 2, carbs: 0, fat: 1 }
      ],
      // Plant-based pasta/noodles
      [
        { name: 'Red Lentil or Chickpea Pasta', amount: '1.5 cups cooked', calories: 240, protein: 18, carbs: 40, fat: 2 },
        { name: 'Tempeh or Tofu Crumbles', amount: '3 oz', calories: 160, protein: 15, carbs: 8, fat: 9 },
        { name: 'Roasted Vegetables (Zucchini, Peppers, Tomatoes)', amount: '1.5 cups', calories: 75, protein: 3, carbs: 15, fat: 1 },
        { name: 'Dairy-free Cashew Cream Sauce', amount: '1/4 cup', calories: 120, protein: 3, carbs: 6, fat: 10 },
        { name: 'Nutritional Yeast', amount: '1 tbsp', calories: 20, protein: 3, carbs: 2, fat: 0 }
      ]
    ];
  }
  
  // Return empty array if no specific templates found - will use generic templates
  return [];
}

/**
 * Store additional recipe templates for offline use
 * @param templates Array of recipe templates to store
 * @returns Promise resolving to success status
 */
export async function storeRecipeTemplates(templates: RecipeTemplate[]): Promise<boolean> {
  try {
    // Get existing templates
    const existingTemplates = await getRecipeTemplates();
    
    // Combine existing templates with new ones, avoiding duplicates
    const combinedTemplates = [...existingTemplates];
    
    for (const template of templates) {
      if (!combinedTemplates.some(t => t.id === template.id)) {
        combinedTemplates.push(template);
      }
    }
    
    // Store combined templates
    await AsyncStorage.setItem(OFFLINE_RECIPE_TEMPLATES_KEY, JSON.stringify(combinedTemplates));
    
    return true;
  } catch (error) {
    console.error('Error storing recipe templates:', error);
    return false;
  }
}

/**
 * Clear all stored recipe templates
 * @returns Promise resolving to success status
 */
export async function clearRecipeTemplates(): Promise<boolean> {
  try {
    await AsyncStorage.removeItem(OFFLINE_RECIPE_TEMPLATES_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing recipe templates:', error);
    return false;
  }
}

/**
 * Reset to default recipe templates
 * @returns Promise resolving to success status
 */
export async function resetToDefaultTemplates(): Promise<boolean> {
  try {
    const defaultTemplates = createDefaultTemplates();
    await AsyncStorage.setItem(OFFLINE_RECIPE_TEMPLATES_KEY, JSON.stringify(defaultTemplates));
    return true;
  } catch (error) {
    console.error('Error resetting to default recipe templates:', error);
    return false;
  }
} 