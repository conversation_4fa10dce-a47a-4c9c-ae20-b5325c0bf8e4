import { ImageAnalysisOptions, UnifiedFoodAnalysisResult } from '@/services/vision/types';
import { AnalysisCacheService } from '@/services/vision/cacheService';
import { uriToBase64 } from '@/services/vision/visionApi';
import { getOpenAIApiKey, OPENAI_API_URL, OpenAIChatCompletionResponse } from './openaiConfig';
import { OPENAI_MODEL } from '@/utils/config';
import { DetectedReferenceObject } from '@/utils/referenceObjectDetection';
import { DetectedTableware, TablewareType } from '@/utils/plateDetection';
import { FoodShape } from '@/utils/foodShapeRecognition';
import { analyzeFood as analyzeFoodSecure } from './secureApiClient';

/**
 * Use OpenAI to identify food from ingredients and visual elements with improved confidence scoring
 * @param detectedItems Array of detected items from the image
 * @param imageBase64 Base64 encoded image data
 * @returns Identified dish name, confidence level, and detailed nutritional breakdown
 */
export async function identifyFoodFromDetectedItems(
  detectedItems: {name: string, score?: number}[],
  imageBase64: string
): Promise<{ 
  success: boolean, 
  dishName?: string, 
  confidence?: number, 
  description?: string,
  servingInfo?: {
    servingSize: string;
    totalServings: number;
    caloriesPerServing: number;
    proteinPerServing: number;
    carbsPerServing: number;
    fatPerServing: number;
    fiberPerServing?: number;
    sugarPerServing?: number;
    sodiumPerServing?: number;
  },
  healthHighlights?: {
    positives: string[];
    considerations: string[];
  },
  ingredients?: {
    name: string;
    estimatedAmount: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  }[],
  preparationMethod?: string;
  cuisineType?: string;
  mealType?: string;
  error?: string 
}> {
  try {
    // No longer need to check for API key since we're using secure endpoint
    // const apiKey = getOpenAIApiKey();
    // if (!apiKey) {
    //   return { success: false, error: 'OpenAI API key not configured' };
    // }
    
    // Debug log - using secure endpoint now
    console.log('Using secure Firebase Cloud Function for OpenAI analysis');
    
    // For confidence weighting - visual evidence is important
    const highConfidenceVisualItems = detectedItems
      .filter(item => (item.score || 0) >= 0.7)
      .map(item => item.name);
    
    const itemNames = detectedItems.map(item => item.name);
    
    // Enhanced system prompt with better instructions for confidence scoring
    const systemPrompt = `
Role: You are a professional executive chef and certified clinical nutritionist with expertise in food image analysis and dietary evaluation.
Goal: Given a high-quality food image and a supporting list of visually detected ingredients (which may contain errors), perform a comprehensive food and nutrition analysis using your expert judgment. Prioritize image-based evidence over the detected list.

Output Format: Respond strictly as a valid, parseable JSON object with the following structure:
{
  "dishName": "string (Concise, general dish name. Prefer simplicity unless specific cuisine is obvious)",
  "description": "string (A rich, human-like description of the dish's appearance, ingredients, and appeal)",
  "confidence": "number (0.0–1.0 indicating confidence in identification. BE CONSERVATIVE - only use values above 0.8 when extremely confident)",
  "servingInfo": {
    "servingSize": "string (e.g., '1 full plate', '100g')",
    "totalServings": "number (e.g., 1 or more if dish is shareable)",
    "caloriesPerServing": "number",
    "proteinPerServing": "number",
    "carbsPerServing": "number",
    "fatPerServing": "number",
    "fiberPerServing": "number (optional)",
    "sugarPerServing": "number (optional)",
    "sodiumPerServing": "number (optional)"
  },
  "healthHighlights": {
    "positives": "array of strings (e.g., 'High in protein', 'Good source of fiber')",
    "considerations": "array of strings (e.g., 'High in sodium', 'Processed meats')"
  },
  "ingredients": [
    {
      "name": "string (e.g., 'fried egg', 'steamed white rice')",
      "estimatedAmount": "string (e.g., '2 pieces', '1 cup')",
      "calories": "number",
      "protein": "number",
      "carbs": "number",
      "fat": "number"
    }
  ],
  "preparationMethod": "string (e.g., 'fried', 'boiled', 'grilled', 'raw')",
  "cuisineType": "string (e.g., 'Filipino', 'American', 'Generic')",
  "mealType": "string (e.g., 'Breakfast', 'Lunch', 'Dinner', 'Snack')"
}

Confidence Scoring Guidelines:
- 0.95-1.0: Perfect recognition with clear visibility of all ingredients and preparation methods
- 0.85-0.94: Strong recognition with minor uncertainties
- 0.75-0.84: Good recognition with some uncertainty about specific ingredients or preparation
- 0.65-0.74: Moderate recognition with significant uncertainty
- 0.55-0.64: Basic recognition with major uncertainties
- <0.55: Poor recognition, very uncertain

Analysis Guidelines:
- Base your assessment primarily on visual inspection of the image. Use the detected list only as context — never as truth.
- Favor generic or simple names unless strong visual cues clearly indicate a cultural or regional dish.
- Identify all visible food items individually (e.g., 2 eggs, 3 hotdogs, 1 cup rice). Avoid guessing ingredients that are not visually present.
- Use realistic nutrition estimates per item. Reference known USDA or Open Food Facts values.
- Estimate quantity and serving size visually based on standard portion sizes (plates, cups, pieces).
- Ensure total calorie/macronutrient estimates match the listed ingredients.
- Highlight both health benefits (e.g., lean protein, fiber) and dietary concerns (e.g., saturated fat, high sodium).
- Return valid JSON only — do not include explanations or surrounding text.
`;

    const userPrompt = `Analyze the provided food image carefully. The following items were also detected (may be inaccurate): ${itemNames.join(', ')}. 
Please return a JSON object that includes:
1. A likely dish name and description
2. Full nutritional breakdown per serving
3. A detailed breakdown of all visible ingredients and their nutritional content
4. Health-related highlights and concerns
5. The most likely cuisine, preparation style, and meal type
6. A confidence score in your analysis

Highly confident visually detected items: ${highConfidenceVisualItems.join(', ')}
Prioritize these visually detected items in your analysis.`;
    
    try {
      // Create a combined prompt that includes both system and user messages
      const combinedPrompt = `${systemPrompt}\n\n${userPrompt}`;
      
      // Convert base64 to a data URL for the secure endpoint
      const imageUrl = `data:image/jpeg;base64,${imageBase64}`;
      
      // Use secure Firebase Cloud Function
      const response = await analyzeFoodSecure(imageUrl, combinedPrompt);
      
      if (!response.success || !response.analysis) {
        throw new Error('Failed to get analysis from secure endpoint');
      }
      
      // *** LOG RAW RESPONSE CONTENT ***
      console.log("--- Secure Endpoint Response ---");
      console.log(response.analysis);
      console.log("---------------------------------");
      // *** END LOG ***
      
      try {
        const analysisJson = JSON.parse(response.analysis);
        
        // *** LOG PARSED JSON ***
        console.log("--- Secure API Parsed JSON ---");
        console.log(JSON.stringify(analysisJson, null, 2)); // Pretty print
        console.log("--------------------------");
        // *** END LOG ***

        if (!analysisJson.dishName) {
          throw new Error('Missing dishName in response');
        }
        
        // Validate nutritional data for consistency
        validateNutritionalData(analysisJson);
        
        console.log(`AI identified dish: ${analysisJson.dishName} (confidence: ${analysisJson.confidence})`);
        
        // Process ingredients to add ingredients array to each item
        let processedIngredients = analysisJson.ingredients;
        if (processedIngredients) {
          processedIngredients = processedIngredients.map((ingredient: any) => ({
            ...ingredient,
            ingredients: [ingredient.name] // Add ingredients array with the item's name
          }));
        }
        
        return {
          success: true,
          dishName: analysisJson.dishName,
          confidence: analysisJson.confidence || 0.5,
          description: analysisJson.description,
          servingInfo: analysisJson.servingInfo,
          healthHighlights: analysisJson.healthHighlights,
          ingredients: processedIngredients,
          preparationMethod: analysisJson.preparationMethod,
          cuisineType: analysisJson.cuisineType,
          mealType: analysisJson.mealType
        };
      } catch (parseError) {
        console.error('Error parsing secure API response:', parseError, response.analysis);
        throw new Error('Failed to parse food identification data from secure API response');
      }
    } catch (error) {
      console.error('Error making secure API request:', error);
      return { success: false, error: 'Error calling secure food analysis API' };
    }
  } catch (error) {
    console.error('Error identifying food with OpenAI:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Validate nutritional data for consistency
 */
function validateNutritionalData(analysisJson: any): void {
  // Check if we have serving info with calories
  if (analysisJson.servingInfo && typeof analysisJson.servingInfo.caloriesPerServing === 'number') {
    // Calculate expected calories from macros
    const protein = analysisJson.servingInfo.proteinPerServing || 0;
    const carbs = analysisJson.servingInfo.carbsPerServing || 0;
    const fat = analysisJson.servingInfo.fatPerServing || 0;
    
    const expectedCalories = (protein * 4) + (carbs * 4) + (fat * 9);
    const actualCalories = analysisJson.servingInfo.caloriesPerServing;
    
    // If there's a significant discrepancy (more than 20%)
    const discrepancy = Math.abs(expectedCalories - actualCalories);
    
    if (discrepancy > actualCalories * 0.2) {
      console.log(`Nutrition inconsistency detected: Expected ${expectedCalories} calories from macros, but got ${actualCalories}`);
      
      // Option 1: Adjust calories to match macros
      // analysisJson.servingInfo.caloriesPerServing = Math.round(expectedCalories);
      
      // Option 2: Adjust macros to match calories (distribute proportionally)
      const currentMacroCalories = expectedCalories;
      if (currentMacroCalories > 0) {
        const ratio = actualCalories / currentMacroCalories;
        
        analysisJson.servingInfo.proteinPerServing = Math.round(protein * ratio * 10) / 10;
        analysisJson.servingInfo.carbsPerServing = Math.round(carbs * ratio * 10) / 10;
        analysisJson.servingInfo.fatPerServing = Math.round(fat * ratio * 10) / 10;
        
        console.log('Adjusted macronutrients to match calorie count');
      }
    }
  }
  
  // Check ingredients for consistency if present
  if (analysisJson.ingredients && Array.isArray(analysisJson.ingredients)) {
    let totalIngredientsCalories = 0;
    
    for (const ingredient of analysisJson.ingredients) {
      // Calculate expected calories from macros for each ingredient
      if (typeof ingredient.protein === 'number' && 
          typeof ingredient.carbs === 'number' && 
          typeof ingredient.fat === 'number') {
        
        const expectedIngredientCalories = (ingredient.protein * 4) + (ingredient.carbs * 4) + (ingredient.fat * 9);
        const actualIngredientCalories = ingredient.calories || 0;
        
        // If significant discrepancy
        if (Math.abs(expectedIngredientCalories - actualIngredientCalories) > actualIngredientCalories * 0.2) {
          // Adjust ingredient calories to match its macros
          ingredient.calories = Math.round(expectedIngredientCalories);
        }
      }
      
      totalIngredientsCalories += ingredient.calories || 0;
    }
    
    // Check if total ingredients calories match serving info calories
    if (analysisJson.servingInfo && typeof analysisJson.servingInfo.caloriesPerServing === 'number') {
      const servingCalories = analysisJson.servingInfo.caloriesPerServing * (analysisJson.servingInfo.totalServings || 1);
      
      // If more than 20% difference between total ingredients calories and serving calories
      if (Math.abs(totalIngredientsCalories - servingCalories) > servingCalories * 0.2) {
        // Adjust serving info to match ingredients total
        analysisJson.servingInfo.caloriesPerServing = Math.round(totalIngredientsCalories / (analysisJson.servingInfo.totalServings || 1));
        console.log(`Adjusted serving calories to match ingredients total: ${analysisJson.servingInfo.caloriesPerServing} cal/serving`);
      }
    }
  }
}

/**
 * Generates a detailed food description based on detected ingredients
 * @param items Array of detected food items
 * @returns A detailed description of the food with all ingredients and potential preparation methods
 */
export async function generateFoodDescriptionFromItems(
  items: {name: string, score?: number}[]
): Promise<{ success: boolean, description?: string, error?: string }> {
  try {
    const apiKey = getOpenAIApiKey();
    if (!apiKey) {
      console.warn('OpenAI API key not configured, using simulated description');
      return generateSimulatedDescription(items);
    }
    
    const itemNames = items.map(item => item.name);
    
    const systemPrompt = `You are a friendly nutritionist and chef who provides engaging, informative food descriptions in a conversational tone.
When describing food, follow these guidelines:
1. Start with an appealing overview of what the dish appears to be
2. Mention key ingredients and their potential health benefits
3. Briefly describe likely preparation methods
4. Include nutritional highlights (protein, fiber, vitamins, etc.)
5. Add a personal touch that sounds like a friendly expert talking to the user
6. Keep your tone warm, approachable, and conversational - like you're speaking directly to a friend`;

    const userPrompt = `Detected food items in this image: ${itemNames.join(', ')}
    
Provide a friendly, conversational analysis of what this dish likely is. Include what's visible in the food, how it might have been prepared, and its nutritional qualities. Write as if you're a knowledgeable friend explaining the dish - sound natural and engaging!`;
    
    try {
      const response = await fetch(OPENAI_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: "gpt-4o", // Updated to use the current vision-capable model
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          temperature: 0.7,
          max_tokens: 350
        })
      });
      
      if (!response.ok) {
        const errorData = await response.text();
        console.error('OpenAI API error response:', errorData);
        throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
      }
      
      const result = await response.json() as OpenAIChatCompletionResponse;
      
      // Validate the structure more safely
      const description = result?.choices?.[0]?.message?.content?.trim();
      
      if (!description) { 
        throw new Error('Invalid or empty response format from OpenAI API');
      }
      
      return {
        success: true,
        description
      };
    } catch (error) {
      console.error('Error making OpenAI API request:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to generate description via OpenAI API'
      };
    }
  } catch (error) {
    console.error('Error generating food description with OpenAI:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'An unexpected error occurred during description generation'
    };
  }
}

/**
 * Analyze food image in a unified way using OpenAI
 */
export async function analyzeWithOpenAI(
  imageUri: string,
  options: Partial<ImageAnalysisOptions> = {}
): Promise<UnifiedFoodAnalysisResult> {
  try {
    // Convert image to base64
    const imageBase64 = await uriToBase64(imageUri);
    
    // Get cached result if available
    const optionsString = JSON.stringify(options);
    if (!options.useProgressiveLoading) {
      const cacheService = AnalysisCacheService.getInstance();
      const cachedResult = await cacheService.getCachedAnalysisResult(imageBase64, optionsString);
      
      if (cachedResult) {
        return cachedResult.result;
      }
    }
    
    const startTime = Date.now();
    
    // Identify food with OpenAI
    const aiResult = await identifyFoodWithAI(imageBase64);
    
    // Process the result into our unified format
    const result: UnifiedFoodAnalysisResult = {
      foodItems: [{
        name: aiResult.name,
        confidence: aiResult.confidence
      }],
      imageProperties: {
        dominantColors: ['#CCCCCC', '#999999', '#666666'], // Placeholder colors
        brightness: 0.7,
        contrast: 0.7,
        sharpness: 0.7,
        hasPlate: true
      },
      detailedFoodInfo: [{
        mainCategory: getCategoryFromFood(aiResult.name),
        specificDish: aiResult.name,
        ingredients: aiResult.ingredients,
        cookingMethod: aiResult.preparationMethod || guessCookingMethod(aiResult.name),
        cuisineOrigin: aiResult.cuisineType || guessCuisine(aiResult.name),
        isFreshOrProcessed: guessFreshnessType(aiResult.name),
        nutrition: aiResult.nutritionalEstimate,
        confidenceScore: aiResult.confidence
      }],
      volumeInfo: [{
        volumeMl: estimateVolume(aiResult.name),
        confidenceScore: 0.6,
        estimationMethod: 'ai_estimation',
        portionSize: 'medium'
      }],
      nutritionalSummary: {
        totalCalories: aiResult.nutritionalEstimate.calories,
        macronutrients: {
          protein: aiResult.nutritionalEstimate.protein,
          carbs: aiResult.nutritionalEstimate.carbs,
          fat: aiResult.nutritionalEstimate.fat,
          fiber: aiResult.nutritionalEstimate.fiber
        },
        mealType: guessMealType(aiResult.name),
        isBalancedMeal: isBalanced(aiResult.nutritionalEstimate),
        nutritionalQualityScore: calculateQualityScore(aiResult.nutritionalEstimate)
      },
      textualAnalysis: {
        description: aiResult.description,
        preparationMethod: aiResult.preparationMethod || '',
        dietaryCategories: aiResult.dietaryCategories || [],
        cuisineType: aiResult.cuisineType || guessCuisine(aiResult.name),
        healthInsights: generateHealthInsights(aiResult.nutritionalEstimate, aiResult.ingredients),
        allergens: aiResult.allergens || []
      },
      meta: {
        analysisTimestamp: Date.now(),
        modelVersion: 'openai-vision-1.0',
        confidenceScore: aiResult.confidence,
        processingTimeMs: Date.now() - startTime,
        apiCalls: {
          llm: true
        }
      }
    };
    
    // Cache the result for future use
    const cacheService = AnalysisCacheService.getInstance();
    await cacheService.cacheAnalysisResult(imageBase64, result, optionsString);
    
    return result;
  } catch (error) {
    console.error('Error in OpenAI analysis:', error);
    return createEmptyResult(error instanceof Error ? error.message : 'Unknown error');
  }
}

/**
 * Generates a simulated food description when the API call fails
 */
function generateSimulatedDescription(items: {name: string, score?: number}[]): { success: boolean, description: string } {
  const itemNames = items.map(item => item.name);
  
  // Helper function to randomly select from array
  const randomChoice = <T>(arr: T[]): T => arr[Math.floor(Math.random() * arr.length)];
  
  // Define key food categories to check against
  const hasProtein = itemNames.some(item => 
    /chicken|beef|fish|pork|tofu|lamb|shrimp|salmon|tuna|turkey|egg/i.test(item)
  );
  
  const hasGrain = itemNames.some(item => 
    /rice|pasta|noodle|grain|bread|toast|sandwich|wrap|tortilla|quinoa|cereal|oat/i.test(item)
  );
  
  const hasVegetables = itemNames.some(item => 
    /veggie|vegetable|salad|green|lettuce|spinach|kale|broccoli|carrot|tomato|pepper|onion|cucumber/i.test(item)
  );
  
  // Create introduction based on combination of items
  let intro = "";
  
  if (hasProtein && hasVegetables && hasGrain) {
    intro = randomChoice([
      "Looks like you have a balanced meal here! ",
      "This appears to be a well-rounded dish with protein, veggies, and grains. ",
      "You've got a complete meal with all the essentials here. "
    ]);
  } else if (hasProtein && hasVegetables) {
    intro = randomChoice([
      "I can see a protein-packed dish with some veggies! ",
      "This looks like a nutritious combination of protein and vegetables. ",
      "You've got a healthy protein and veggie combo here. "
    ]);
  } else {
    intro = randomChoice([
      "I can see a tasty dish featuring ",
      "This appears to be a meal containing ",
      "Your plate has what looks like "
    ]) + itemNames.slice(0, 3).join(", ") + (itemNames.length > 3 ? ", and more" : "") + ". ";
  }
  
  // Nutritional benefits
  let nutrition = "";
  
  if (hasProtein && hasVegetables) {
    nutrition = randomChoice([
      "This meal offers a good balance of protein for muscle support and vegetables for vitamins and fiber. ",
      "You're getting quality protein along with the antioxidants and fiber from those vegetables. ",
      "This combination provides sustained energy from protein while the vegetables deliver essential nutrients. "
    ]);
  } else if (hasProtein) {
    nutrition = "The protein in this dish will help keep you feeling full and support muscle maintenance. ";
  } else if (hasVegetables) {
    nutrition = "These vegetables provide a range of vitamins, minerals, and fiber to support your overall health. ";
  } else {
    nutrition = "This offers a mix of nutrients that contribute to your daily intake. ";
  }
  
  // Personal touch/closing
  const closing = randomChoice([
    "Enjoy your meal!",
    "Hope it tastes as good as it looks!",
    "This looks delicious!",
    "Bon appétit!"
  ]);
  
  return {
    success: true,
    description: `${intro}${nutrition}${closing}`
  };
}

/**
 * Create an empty result with error message
 */
function createEmptyResult(errorMessage: string): UnifiedFoodAnalysisResult {
  return {
    foodItems: [],
    imageProperties: {
      dominantColors: ['#CCCCCC', '#999999', '#666666'],
      brightness: 0.5,
      contrast: 0.5,
      sharpness: 0.5,
      hasPlate: false
    },
    detailedFoodInfo: [],
    volumeInfo: [],
    nutritionalSummary: {
      totalCalories: 0,
      macronutrients: {
        protein: 0,
        carbs: 0,
        fat: 0,
        fiber: 0
      },
      mealType: 'unknown',
      isBalancedMeal: false,
      nutritionalQualityScore: 0
    },
    textualAnalysis: {
      description: errorMessage || 'Unable to analyze image',
      preparationMethod: '',
      dietaryCategories: [],
      cuisineType: '',
      healthInsights: []
    },
    meta: {
      analysisTimestamp: Date.now(),
      modelVersion: 'openai-vision-1.0',
      confidenceScore: 0,
      processingTimeMs: 0,
      error: errorMessage
    }
  };
}

/**
 * Identify food using OpenAI Vision API
 */
async function identifyFoodWithAI(imageBase64: string): Promise<{
  name: string;
  confidence: number;
  description: string;
  ingredients: string[];
  nutritionalEstimate: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  };
  cuisineType?: string;
  preparationMethod?: string;
  dietaryCategories?: string[];
  allergens?: string[];
}> {
  // Create a simulated result for testing
  return simulateFoodIdentification();
}

/**
 * Fallback function to simulate food identification when API is unavailable
 */
function simulateFoodIdentification() {
  const randomFoods = [
    {
      name: 'Chicken Salad',
      confidence: 0.82,
      description: 'Fresh salad with grilled chicken, mixed greens, and vegetables',
      ingredients: [
        'Grilled chicken breast', 
        'Romaine lettuce', 
        'Cherry tomatoes', 
        'English cucumber'
      ],
      nutritionalEstimate: { calories: 320, protein: 25, carbs: 10, fat: 18, fiber: 3 },
      cuisineType: 'Mediterranean',
      preparationMethod: 'Mixed',
      dietaryCategories: ['High protein', 'Low carb'],
      allergens: []
    },
    {
      name: 'Spaghetti Bolognese',
      confidence: 0.85,
      description: 'Italian pasta with rich meat sauce',
      ingredients: [
        'Durum wheat spaghetti pasta', 
        'Grass-fed ground beef', 
        'San Marzano tomatoes'
      ],
      nutritionalEstimate: { calories: 450, protein: 22, carbs: 65, fat: 12, fiber: 5 },
      cuisineType: 'Italian',
      preparationMethod: 'Boiled',
      dietaryCategories: [],
      allergens: ['Gluten', 'Dairy']
    }
  ];
  
  return randomFoods[Math.floor(Math.random() * randomFoods.length)];
}

// Various helper functions for food analysis
function getCategoryFromFood(food: string): string {
  const foodLower = food.toLowerCase();
  
  if (foodLower.includes('salad')) return 'Salad';
  if (foodLower.includes('soup')) return 'Soup';
  if (foodLower.includes('sandwich')) return 'Sandwich';
  if (foodLower.includes('burger')) return 'Burger';
  
  return 'Mixed Dish';
}

function guessCookingMethod(food: string): string {
  const foodLower = food.toLowerCase();
  
  if (foodLower.includes('salad')) return 'raw';
  if (foodLower.includes('soup')) return 'boiled';
  if (foodLower.includes('sandwich')) return 'assembled';
  
  return 'mixed';
}

function guessCuisine(food: string): string {
  const foodLower = food.toLowerCase();
  
  if (foodLower.includes('pasta') || foodLower.includes('pizza')) return 'Italian';
  if (foodLower.includes('sushi') || foodLower.includes('ramen')) return 'Japanese';
  if (foodLower.includes('taco') || foodLower.includes('burrito')) return 'Mexican';
  
  return 'International';
}

function guessFreshnessType(food: string): 'fresh' | 'processed' | 'mixed' | 'unknown' {
  return 'mixed';
}

function guessMealType(food: string): 'breakfast' | 'lunch' | 'dinner' | 'snack' | 'unknown' {
  return 'lunch';
}

function isBalanced(nutrition: { protein: number; carbs: number; fat: number; fiber: number }): boolean {
  return true;
}

function calculateQualityScore(nutrition: { protein: number; carbs: number; fat: number; fiber: number }): number {
  return 7;
}

function estimateVolume(food: string): number {
  return 300;
}

function generateHealthInsights(
  nutrition: { calories: number; protein: number; carbs: number; fat: number; fiber: number },
  ingredients: string[]
): string[] {
  return [
    "Good source of protein",
    "Contains essential nutrients",
    "Moderate calorie content"
  ];
}

/**
 * Weight estimation result from GPT-4V
 */
export interface GPT4VWeightEstimation {
  foodName: string;
  weightGrams: number;
  confidence: number;
  error?: string;
  estimationDetails?: {
    shape?: FoodShape;
    volume?: number; // in cm³
    density?: number; // in g/cm³
    referenceObjectsUsed?: string[];
    reasoningProcess?: string;
  };
}

/**
 * Parameters for GPT-4V weight estimation
 */
export interface GPT4VWeightEstimationParams {
  imageBase64: string;
  foodName: string;
  referenceObjects?: DetectedReferenceObject[];
  tableware?: DetectedTableware[];
  pixelDimensions?: {
    width: number;
    height: number;
    area: number;
  };
}

/**
 * Estimate food weight using GPT-4V with precise prompt engineering
 * focused on accurate weight estimation
 * 
 * @param params Weight estimation parameters
 * @returns Weight estimation result
 */
export async function estimateFoodWeightWithGPT4V(
  params: GPT4VWeightEstimationParams
): Promise<GPT4VWeightEstimation> {
  try {
    const apiKey = getOpenAIApiKey();
    if (!apiKey) {
      return { 
        foodName: params.foodName,
        weightGrams: 0,
        confidence: 0,
        error: 'OpenAI API key not configured' 
      };
    }
    
    // Simplified implementation - returns a fallback weight
    console.log(`Weight estimation requested for ${params.foodName}, but using fallback`);
    
    // Helper function for reference objects formatting
    function formatReferenceObjects(objects?: any[], tableware?: any[]): string {
      if (!objects || objects.length === 0) return '';
      
      const refObjInfo = objects.map(obj => 
        `- ${obj.type || 'Object'}: width ${obj.width}mm x height ${obj.height}mm`
      ).join('\n');
      
      const tablewareInfo = tableware && tableware.length > 0 
        ? '\nTableware:\n' + tableware.map(item => 
            `- ${item.type || 'Item'}: ${item.diameter ? `diameter ${item.diameter}mm` : 
              item.width ? `width ${item.width}mm x height ${item.height}mm` : 'dimensions unknown'}`
          ).join('\n')
        : '';
      
      return refObjInfo + tablewareInfo;
    }
    
    // Use the helper function directly here
    const referenceObjectsInfo = formatReferenceObjects(
      params.referenceObjects,
      params.tableware
    );
    
    // Use dimensions info if available
    const dimensionsInfo = params.pixelDimensions 
      ? `Image dimensions: ${params.pixelDimensions.width}px × ${params.pixelDimensions.height}px, area: ${params.pixelDimensions.area}px²`
      : 'No pixel dimensions available';
    
    return {
      foodName: params.foodName,
      weightGrams: estimateFallbackWeight(params.foodName),
      confidence: 0.5,
      error: 'Using simplified fallback estimation',
      estimationDetails: {
        shape: 'regular' as FoodShape, // Cast to the imported FoodShape type
        reasoningProcess: `Estimated based on food type "${params.foodName}" using fallback system. ${dimensionsInfo}. ${referenceObjectsInfo ? `Reference objects were available but not used in this fallback implementation.` : ''}`
      }
    };
  } catch (error) {
    console.error('Error estimating weight with GPT-4V:', error);
    return { 
      foodName: params.foodName,
      weightGrams: 0,
      confidence: 0,
      error: error instanceof Error ? error.message : 'Unknown error in weight estimation' 
    };
  }
}

/**
 * Estimate a fallback weight when AI estimation fails
 */
function estimateFallbackWeight(foodName: string): number {
  const lowerFoodName = foodName.toLowerCase();
  
  // Default fallbacks based on food categories
  if (/fruit|apple|orange|banana|berry/i.test(lowerFoodName)) {
    return 150; // Average fruit
  } else if (/vegetable|carrot|broccoli|salad/i.test(lowerFoodName)) {
    return 120; // Average vegetable
  } else if (/meat|beef|chicken|pork|steak|fish/i.test(lowerFoodName)) {
    return 200; // Average meat portion
  } else if (/pasta|rice|grain|cereal/i.test(lowerFoodName)) {
    return 250; // Average grain
  } else if (/drink|beverage|juice|water|coffee/i.test(lowerFoodName)) {
    return 300; // Average beverage
  } else if (/dessert|cake|cookie|ice cream/i.test(lowerFoodName)) {
    return 120; // Average dessert
  }
  
  // Generic fallback
  return 150;
}
