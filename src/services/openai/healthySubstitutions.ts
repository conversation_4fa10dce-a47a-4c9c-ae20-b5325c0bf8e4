/**
 * Healthy Ingredient Substitution Database
 * 
 * This module provides a database of healthy ingredient substitutions based on USDA guidelines
 * and nutrition research. It enables the app to suggest healthier alternatives for ingredients
 * in recipes while maintaining flavor profiles and culinary functionality.
 */

import { z } from 'zod';

/**
 * Types for ingredient substitutions
 */
export const SubstitutionReason = {
  LOWER_CALORIES: 'lowerCalories',
  LOWER_FAT: 'lowerFat',
  LOWER_SATURATED_FAT: 'lowerSaturatedFat',
  LOWER_SODIUM: 'lowerSodium',
  LOWER_SUGAR: 'lowerSugar',
  HIGHER_FIBER: 'higherFiber',
  HIGHER_PROTEIN: 'higherProtein',
  LESS_PROCESSED: 'lessProcessed',
  HEART_HEALTHY: 'heartHealthy',
  DIABETIC_FRIENDLY: 'diabeticFriendly',
  GLUTEN_FREE: 'glutenFree',
  DAIRY_FREE: 'dairyFree',
} as const;

export type SubstitutionReasonType = typeof SubstitutionReason[keyof typeof SubstitutionReason];

// Schema for nutritional content
export const NutritionSchema = z.object({
  calories: z.number().optional(),
  protein: z.number().optional(),
  carbs: z.number().optional(),
  fat: z.number().optional(),
  saturatedFat: z.number().optional(),
  sugar: z.number().optional(),
  fiber: z.number().optional(),
  sodium: z.number().optional(),
});

export type Nutrition = z.infer<typeof NutritionSchema>;

// Schema for ingredient substitution
export const IngredientSubstitutionSchema = z.object({
  originalIngredient: z.string(),
  substitution: z.string(),
  ratio: z.string(), // e.g. "1:1", "1:0.75", etc.
  reasons: z.array(z.enum([
    SubstitutionReason.LOWER_CALORIES,
    SubstitutionReason.LOWER_FAT,
    SubstitutionReason.LOWER_SATURATED_FAT,
    SubstitutionReason.LOWER_SODIUM,
    SubstitutionReason.LOWER_SUGAR,
    SubstitutionReason.HIGHER_FIBER,
    SubstitutionReason.HIGHER_PROTEIN,
    SubstitutionReason.LESS_PROCESSED,
    SubstitutionReason.HEART_HEALTHY,
    SubstitutionReason.DIABETIC_FRIENDLY,
    SubstitutionReason.GLUTEN_FREE,
    SubstitutionReason.DAIRY_FREE,
  ])),
  benefits: z.string(), // Human-readable explanation of benefits
  culinaryNotes: z.string().optional(), // Notes about flavor impact, texture, etc.
  originalNutrition: NutritionSchema.optional(),
  substitutionNutrition: NutritionSchema.optional(),
  tags: z.array(z.string()).optional(),
});

export type IngredientSubstitution = z.infer<typeof IngredientSubstitutionSchema>;

/**
 * Database of ingredient substitutions
 * Based on USDA dietary guidelines and nutrition research
 */
export const HEALTHY_SUBSTITUTIONS: IngredientSubstitution[] = [
  // Dairy substitutions
  {
    originalIngredient: "whole milk",
    substitution: "skim milk",
    ratio: "1:1",
    reasons: [SubstitutionReason.LOWER_CALORIES, SubstitutionReason.LOWER_FAT, SubstitutionReason.LOWER_SATURATED_FAT, SubstitutionReason.HEART_HEALTHY],
    benefits: "Reduces calories by about 60 per cup and removes almost all saturated fat while maintaining calcium content",
    culinaryNotes: "Works well in most recipes, though may change texture slightly in creamy dishes",
    originalNutrition: { calories: 150, fat: 8, saturatedFat: 5, protein: 8, carbs: 12 },
    substitutionNutrition: { calories: 90, fat: 0.5, saturatedFat: 0.3, protein: 8, carbs: 12 },
    tags: ["dairy", "beverage", "baking"]
  },
  {
    originalIngredient: "heavy cream",
    substitution: "evaporated skim milk",
    ratio: "1:1",
    reasons: [SubstitutionReason.LOWER_CALORIES, SubstitutionReason.LOWER_FAT, SubstitutionReason.LOWER_SATURATED_FAT, SubstitutionReason.HEART_HEALTHY],
    benefits: "Reduces calories by about 60% and fat by over 80% while maintaining creaminess",
    culinaryNotes: "Works best in cooking rather than whipping; add 1 tbsp cornstarch per cup to thicken sauces if needed",
    originalNutrition: { calories: 414, fat: 44, saturatedFat: 28, protein: 2.5, carbs: 3.3 },
    substitutionNutrition: { calories: 200, fat: 5, saturatedFat: 3, protein: 19, carbs: 29 },
    tags: ["dairy", "cooking", "sauce"]
  },
  {
    originalIngredient: "sour cream",
    substitution: "plain non-fat Greek yogurt",
    ratio: "1:1",
    reasons: [SubstitutionReason.LOWER_CALORIES, SubstitutionReason.LOWER_FAT, SubstitutionReason.HIGHER_PROTEIN, SubstitutionReason.HEART_HEALTHY],
    benefits: "Reduces calories by about 70% and fat by over 90% while adding additional protein",
    culinaryNotes: "Slightly tangier than sour cream; for hot dishes, add at end of cooking or may separate",
    originalNutrition: { calories: 198, fat: 19, protein: 3, carbs: 5 },
    substitutionNutrition: { calories: 59, fat: 0, protein: 10, carbs: 3.5 },
    tags: ["dairy", "topping", "dips"]
  },
  
  // Fat substitutions
  {
    originalIngredient: "butter",
    substitution: "applesauce",
    ratio: "1:0.75",
    reasons: [SubstitutionReason.LOWER_CALORIES, SubstitutionReason.LOWER_FAT, SubstitutionReason.LOWER_SATURATED_FAT, SubstitutionReason.HEART_HEALTHY],
    benefits: "Dramatically reduces calories and fat while adding moisture to baked goods",
    culinaryNotes: "Best in sweet baked goods like muffins, quick breads, and cookies; may alter texture slightly",
    originalNutrition: { calories: 102, fat: 12, saturatedFat: 7 },
    substitutionNutrition: { calories: 19, fat: 0, saturatedFat: 0, sugar: 3.5, fiber: 0.5 },
    tags: ["fat", "baking", "sweets"]
  },
  {
    originalIngredient: "vegetable oil",
    substitution: "olive oil",
    ratio: "1:0.75",
    reasons: [SubstitutionReason.HEART_HEALTHY, SubstitutionReason.LESS_PROCESSED],
    benefits: "Replaces refined oils with heart-healthy monounsaturated fats and antioxidants",
    culinaryNotes: "Works well in savory dishes; may impart flavor in baked goods",
    originalNutrition: { calories: 124, fat: 14 },
    substitutionNutrition: { calories: 124, fat: 14 },
    tags: ["fat", "cooking", "mediterranean"]
  },
  
  // Meat substitutions
  {
    originalIngredient: "ground beef (80% lean)",
    substitution: "ground turkey breast",
    ratio: "1:1",
    reasons: [SubstitutionReason.LOWER_CALORIES, SubstitutionReason.LOWER_FAT, SubstitutionReason.LOWER_SATURATED_FAT, SubstitutionReason.HEART_HEALTHY],
    benefits: "Reduces calories by about 60% and fat by over 75% while maintaining high protein content",
    culinaryNotes: "May be drier; add extra moisture when cooking. Add mushrooms for umami flavor",
    originalNutrition: { calories: 290, fat: 22, saturatedFat: 8, protein: 20, carbs: 0 },
    substitutionNutrition: { calories: 120, fat: 2, saturatedFat: 0.5, protein: 26, carbs: 0 },
    tags: ["meat", "protein", "main dish"]
  },
  
  // Grain substitutions
  {
    originalIngredient: "white rice",
    substitution: "brown rice",
    ratio: "1:1",
    reasons: [SubstitutionReason.HIGHER_FIBER, SubstitutionReason.LESS_PROCESSED, SubstitutionReason.DIABETIC_FRIENDLY],
    benefits: "Triples the fiber content and adds more vitamins, minerals, and phytonutrients",
    culinaryNotes: "Takes longer to cook; has nuttier flavor and chewier texture",
    originalNutrition: { calories: 205, carbs: 45, fiber: 0.6, protein: 4 },
    substitutionNutrition: { calories: 218, carbs: 46, fiber: 3.5, protein: 5 },
    tags: ["grain", "side dish", "whole grain"]
  },
  {
    originalIngredient: "white flour",
    substitution: "whole wheat flour",
    ratio: "1:0.75",
    reasons: [SubstitutionReason.HIGHER_FIBER, SubstitutionReason.LESS_PROCESSED, SubstitutionReason.DIABETIC_FRIENDLY],
    benefits: "Adds fiber, protein, and micronutrients while reducing refined carbohydrates",
    culinaryNotes: "Makes denser products; best to substitute half the flour in recipes, add 1 tbsp liquid per cup",
    originalNutrition: { calories: 455, carbs: 95, fiber: 3, protein: 13 },
    substitutionNutrition: { calories: 407, carbs: 87, fiber: 15, protein: 16 },
    tags: ["baking", "flour", "whole grain"]
  },
  
  // Sugar substitutions
  {
    originalIngredient: "sugar",
    substitution: "applesauce",
    ratio: "1:1",
    reasons: [SubstitutionReason.LOWER_CALORIES, SubstitutionReason.LOWER_SUGAR, SubstitutionReason.DIABETIC_FRIENDLY],
    benefits: "Reduces added sugar and calories while adding moisture and natural sweetness",
    culinaryNotes: "Works best in moist baked goods; may need to reduce liquid elsewhere in recipe",
    originalNutrition: { calories: 774, carbs: 200, sugar: 200 },
    substitutionNutrition: { calories: 167, carbs: 42, sugar: 32, fiber: 3 },
    tags: ["sweetener", "baking", "natural"]
  },
  {
    originalIngredient: "breadcrumbs",
    substitution: "rolled oats",
    ratio: "1:1",
    reasons: [SubstitutionReason.HIGHER_FIBER, SubstitutionReason.LESS_PROCESSED, SubstitutionReason.HEART_HEALTHY],
    benefits: "Adds fiber and nutrients while reducing processed ingredients",
    culinaryNotes: "Works well in meatloaf, meatballs, and as toppings; can pulse in food processor for finer texture",
    originalNutrition: { calories: 107, carbs: 20, fiber: 0.8, protein: 3.5 },
    substitutionNutrition: { calories: 153, carbs: 27, fiber: 4, protein: 5.3 },
    tags: ["baking", "binding", "whole grain"]
  }
];

/**
 * Find healthy substitutions for a given ingredient
 * 
 * @param ingredient The original ingredient to find substitutions for
 * @param dietaryPreferences Optional array of dietary preferences to filter by
 * @returns Array of possible substitutions
 */
export function findSubstitutions(
  ingredient: string, 
  dietaryPreferences?: string[]
): IngredientSubstitution[] {
  // Normalize the ingredient name for better matching
  const normalizedIngredient = ingredient.toLowerCase().trim();
  
  // Find exact or partial matches in the substitution database
  let matches = HEALTHY_SUBSTITUTIONS.filter(sub => {
    const original = sub.originalIngredient.toLowerCase();
    return original === normalizedIngredient || 
           original.includes(normalizedIngredient) || 
           normalizedIngredient.includes(original);
  });
  
  // If dietary preferences are specified, filter matches accordingly
  if (dietaryPreferences && dietaryPreferences.length > 0) {
    matches = matches.filter(match => {
      // Check if the substitution meets dietary preferences
      if (dietaryPreferences.includes('gluten-free') && 
          !match.reasons.includes(SubstitutionReason.GLUTEN_FREE)) {
        return false;
      }
      
      if (dietaryPreferences.includes('dairy-free') && 
          !match.reasons.includes(SubstitutionReason.DAIRY_FREE)) {
        return false;
      }
      
      if (dietaryPreferences.includes('heart-healthy') && 
          !match.reasons.includes(SubstitutionReason.HEART_HEALTHY)) {
        return false;
      }
      
      if (dietaryPreferences.includes('diabetic-friendly') && 
          !match.reasons.includes(SubstitutionReason.DIABETIC_FRIENDLY)) {
        return false;
      }
      
      return true;
    });
  }
  
  return matches;
}

/**
 * Get the explanation of health benefits for a substitution
 * 
 * @param originalIngredient The original ingredient
 * @param substitution The healthier substitution
 * @returns Human-readable explanation of health benefits or undefined if not found
 */
export function getSubstitutionBenefits(
  originalIngredient: string,
  substitution: string
): string | undefined {
  const match = HEALTHY_SUBSTITUTIONS.find(
    sub => sub.originalIngredient.toLowerCase() === originalIngredient.toLowerCase() &&
           sub.substitution.toLowerCase() === substitution.toLowerCase()
  );
  
  return match?.benefits;
}

/**
 * Calculate the nutritional improvement from a substitution
 * 
 * @param originalIngredient The original ingredient
 * @param substitution The healthier substitution
 * @returns Object with percentage changes in nutritional values
 */
export function calculateNutritionalImprovements(
  originalIngredient: string,
  substitution: string
): Record<string, number> | undefined {
  const match = HEALTHY_SUBSTITUTIONS.find(
    sub => sub.originalIngredient.toLowerCase() === originalIngredient.toLowerCase() &&
           sub.substitution.toLowerCase() === substitution.toLowerCase()
  );
  
  if (!match || !match.originalNutrition || !match.substitutionNutrition) {
    return undefined;
  }
  
  const improvements: Record<string, number> = {};
  
  // Calculate percentage changes for each nutritional element
  Object.keys(match.originalNutrition).forEach(key => {
    const originalValue = match.originalNutrition?.[key as keyof Nutrition];
    const substitutionValue = match.substitutionNutrition?.[key as keyof Nutrition];
    
    if (originalValue !== undefined && substitutionValue !== undefined && originalValue !== 0) {
      const percentChange = ((substitutionValue - originalValue) / originalValue) * 100;
      improvements[key] = percentChange;
    }
  });
  
  return improvements;
}

/**
 * Generate a personalized substitution suggestion based on user health goals
 * 
 * @param ingredient The original ingredient
 * @param healthGoals Array of user health goals
 * @returns The best substitution based on user goals
 */
export function getPersonalizedSubstitution(
  ingredient: string,
  healthGoals: string[]
): IngredientSubstitution | undefined {
  const substitutions = findSubstitutions(ingredient);
  
  if (substitutions.length === 0) {
    return undefined;
  }
  
  // Map health goals to substitution reasons
  const goalToReasonMap: Record<string, SubstitutionReasonType> = {
    'weight-loss': SubstitutionReason.LOWER_CALORIES,
    'heart-health': SubstitutionReason.HEART_HEALTHY,
    'diabetes-management': SubstitutionReason.DIABETIC_FRIENDLY,
    'reduce-fat': SubstitutionReason.LOWER_FAT,
    'increase-protein': SubstitutionReason.HIGHER_PROTEIN,
    'increase-fiber': SubstitutionReason.HIGHER_FIBER,
    'reduce-sugar': SubstitutionReason.LOWER_SUGAR,
    'reduce-sodium': SubstitutionReason.LOWER_SODIUM,
    'clean-eating': SubstitutionReason.LESS_PROCESSED,
  };
  
  // Convert user health goals to substitution reasons
  const targetReasons = healthGoals
    .map(goal => goalToReasonMap[goal])
    .filter(reason => reason !== undefined) as SubstitutionReasonType[];
  
  if (targetReasons.length === 0) {
    // If no specific reasons mapped, return the first substitution
    return substitutions[0];
  }
  
  // Score each substitution based on how well it matches the user's health goals
  const scoredSubstitutions = substitutions.map(sub => {
    const matchingReasons = sub.reasons.filter(reason => 
      targetReasons.includes(reason)
    );
    
    return {
      substitution: sub,
      score: matchingReasons.length
    };
  });
  
  // Sort by score (descending) and return the best match
  scoredSubstitutions.sort((a, b) => b.score - a.score);
  return scoredSubstitutions[0].substitution;
} 