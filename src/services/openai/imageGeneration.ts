import { generateRecipeImage } from './secureApiClient';

/**
 * Generates a DALLE image for recipe from the image prompt
 * @param imagePrompt A detailed prompt for image generation
 * @returns Image URL or fallback if generation fails
 */
export async function generateDalleImage(imagePrompt: string): Promise<string> {
  try {
    // Enhanced prompt for better food photography results
    const enhancedPrompt = `${imagePrompt}. Professional food photography, natural lighting, shallow depth of field, high-end restaurant presentation.`;
    
    // Use secure Firebase function for image generation
    const response = await generateRecipeImage("Recipe Image", enhancedPrompt, "professional");
    
    if (response.success && response.imageUrl) {
      return response.imageUrl;
    }
    
    console.warn('Failed to generate image via Firebase function, using fallback');
    return getFallbackFoodImageUrl(imagePrompt);
  } catch (error) {
    console.error('Error generating DALLE image:', error);
    return getFallbackFoodImageUrl(imagePrompt);
  }
}

/**
 * Get a fallback food image URL based on the prompt keywords
 * @param prompt The image prompt
 * @returns A URL to a relevant food image
 */
function getFallbackFoodImageUrl(prompt: string): string {
  // Map of keywords to image URLs (similar to the placeholders in imageGenerationService)
  const foodImages: Record<string, string> = {
    'breakfast': 'https://images.pexels.com/photos/376464/pexels-photo-376464.jpeg?auto=compress&cs=tinysrgb&w=800',
    'lunch': 'https://images.pexels.com/photos/1640772/pexels-photo-1640772.jpeg?auto=compress&cs=tinysrgb&w=800',
    'dinner': 'https://images.pexels.com/photos/1279330/pexels-photo-1279330.jpeg?auto=compress&cs=tinysrgb&w=800',
    'salad': 'https://images.pexels.com/photos/257816/pexels-photo-257816.jpeg?auto=compress&cs=tinysrgb&w=800',
    'soup': 'https://images.pexels.com/photos/539451/pexels-photo-539451.jpeg?auto=compress&cs=tinysrgb&w=800',
    'sandwich': 'https://images.pexels.com/photos/1647163/pexels-photo-1647163.jpeg?auto=compress&cs=tinysrgb&w=800',
    'pasta': 'https://images.pexels.com/photos/1279330/pexels-photo-1279330.jpeg?auto=compress&cs=tinysrgb&w=800',
    'bowl': 'https://images.pexels.com/photos/1640770/pexels-photo-1640770.jpeg?auto=compress&cs=tinysrgb&w=800',
    'healthy': 'https://images.pexels.com/photos/1640774/pexels-photo-1640774.jpeg?auto=compress&cs=tinysrgb&w=800',
    'chicken': 'https://images.pexels.com/photos/616354/pexels-photo-616354.jpeg?auto=compress&cs=tinysrgb&w=800',
    'rice': 'https://images.pexels.com/photos/723198/pexels-photo-723198.jpeg?auto=compress&cs=tinysrgb&w=800',
  };

  // Look for matching keywords
  const lowerPrompt = prompt.toLowerCase();
  for (const [key, url] of Object.entries(foodImages)) {
    if (lowerPrompt.includes(key)) {
      return url;
    }
  }

  // Default images if no matching keyword
  const defaultImages = [
    'https://images.pexels.com/photos/1640774/pexels-photo-1640774.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/1640770/pexels-photo-1640770.jpeg?auto=compress&cs=tinysrgb&w=800',
    'https://images.pexels.com/photos/1279330/pexels-photo-1279330.jpeg?auto=compress&cs=tinysrgb&w=800',
  ];
  
  const randomIndex = Math.floor(Math.random() * defaultImages.length);
  return defaultImages[randomIndex];
} 