// OpenAI Service Module - Main export file
// Re-exports all functionality from sub-modules

// Configuration exports
export { 
  setSpecialUserStatus,
  getOpenAIApiKey,
  isSpecialDemoUser,
  OPENAI_API_URL,
  DEMO_API_KEY
} from './openaiConfig';

// Recipe generation exports
export {
  generateAlternativeRecipe,
  generateEnhancedDemoRecipe,
  generateFallbackRecipe,
  determineFoodType
} from './recipeGeneration';

export {
  RECIPE_DATABASE
} from './recipeTypes';

export type {
  Recipe,
  GenerateAlternativeRecipeRequest,
  GenerateAlternativeRecipeResponse
} from './recipeTypes';

// Food analysis exports
export {
  identifyFoodFromDetectedItems,
  generateFoodDescriptionFromItems,
  analyzeWithOpenAI
} from './foodAnalysis';

// Nutritional explanation exports
export {
  explainNutritionalImpact
} from './nutritionalExplanation';

// Image generation exports
export {
  generateDalleImage
} from './imageGeneration'; 