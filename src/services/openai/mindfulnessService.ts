
import AsyncStorage from '@react-native-async-storage/async-storage';
import { safeRequest } from '@/utils/requestUtils';
import { getAuth } from 'firebase/auth';
import { getDatabase, ref, get } from 'firebase/database';
import { analyzeFood } from './secureApiClient';

// Simple wrapper for Firebase Functions chat completion
const simpleChatCompletion = async (
  messages: { role: string; content: string }[],
  options: { temperature?: number; model?: string; timeout?: number } = {}
): Promise<string | null> => {
  try {
    // Create a combined prompt from messages
    const systemPrompt = messages.find(m => m.role === 'system')?.content || '';
    const userPrompt = messages.find(m => m.role === 'user')?.content || '';
    const combinedPrompt = `${systemPrompt}\n\n${userPrompt}`;
    
    // Use dummy image URL since we're not analyzing an actual image
    const dummyImageUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    
    // Call Firebase Function with timeout handling
    const { data: result, error } = await safeRequest(
      () => analyzeFood(dummyImageUrl, combinedPrompt),
      {
        timeout: options.timeout || 8000,
        errorMessage: 'Firebase Functions completion failed',
      }
    );
    
    if (result && result.success && result.analysis) {
      return result.analysis;
    }
    
    if (error) {
      console.error('Error in Firebase Functions completion:', error);
      if (error.message.includes('timeout')) {
        throw new Error('Firebase Functions request timed out');
      }
    }
    
    return null;
  } catch (error) {
    console.error('Error in Firebase Functions chat completion:', error);
    if (error instanceof Error && error.message.includes('timeout')) {
      throw error; // Rethrow timeout errors for proper handling upstream
    }
    return null;
  }
};

// Interface for mindfulness recommendation
export interface MindfulnessRecommendation {
  title: string;
  message: string;
  category: string;
  icon: string;
  gradient_start: string;
  gradient_end: string;
  image_url?: string;
}

// Interface for user context
interface UserContext {
  recentActivities?: string[];
  preferredCategories?: string[];
  lastSession?: string;
  stressLevel?: number;
  timeOfDay?: string;
  goalArea?: string;
}

// Interface for mindfulness session from database
interface MindfulnessSession {
  tip_id: string;
  session_date: string;
}

// Interface for user preferences from database
interface UserPreferences {
  preferred_categories: string[];
}

// Fallback recommendations when OpenAI is not available
const FALLBACK_RECOMMENDATIONS: MindfulnessRecommendation[] = [
  {
    title: "Morning Breath Awareness",
    message: "Take 5 deep breaths, focusing on the sensation of air flowing in and out. Notice how your body feels more energized with each breath.",
    category: "stress-relief",
    icon: "breathing",
    gradient_start: "#9CECFB",
    gradient_end: "#65C7F7",
    image_url: "https://images.pexels.com/photos/775417/pexels-photo-775417.jpeg?auto=compress&cs=tinysrgb&w=600"
  },
  {
    title: "Mindful Hydration",
    message: "Take a moment to slowly drink a glass of water. Feel the sensation as it travels through your body, bringing hydration and renewal.",
    category: "nutrition",
    icon: "hydration",
    gradient_start: "#43CEA2",
    gradient_end: "#185A9D",
    image_url: "https://images.pexels.com/photos/327090/pexels-photo-327090.jpeg?auto=compress&cs=tinysrgb&w=600"
  },
  {
    title: "Gratitude Reflection",
    message: "Pause and name three things you're grateful for today. Focusing on gratitude can shift your perspective and enhance wellbeing.",
    category: "mental-health",
    icon: "gratitude",
    gradient_start: "#B06AB3",
    gradient_end: "#4568DC",
    image_url: "https://images.pexels.com/photos/3560044/pexels-photo-3560044.jpeg?auto=compress&cs=tinysrgb&w=600"
  },
  {
    title: "Mindful Movement Break",
    message: "Stand up and stretch your arms overhead. Gently twist your torso from side to side, noticing the sensations in your muscles and joints.",
    category: "movement",
    icon: "stretch",
    gradient_start: "#FF9966",
    gradient_end: "#FF5E62",
    image_url: "https://images.pexels.com/photos/4056723/pexels-photo-4056723.jpeg?auto=compress&cs=tinysrgb&w=600"
  }
];

// Generate personalized mindfulness recommendation
export async function generateMindfulnessRecommendation(
  userContext: UserContext = {}
): Promise<MindfulnessRecommendation | null> {
  try {
    // Check for cached recommendation in AsyncStorage
    const cachedRecommendation = await AsyncStorage.getItem('cachedMindfulnessRecommendation');
    const cacheTimestamp = await AsyncStorage.getItem('mindfulnessRecommendationTimestamp');
    
    // Use cache if it's less than 1 hour old
    if (cachedRecommendation && cacheTimestamp) {
      const timestamp = parseInt(cacheTimestamp);
      if (Date.now() - timestamp < 60 * 60 * 1000) {
        return JSON.parse(cachedRecommendation);
      }
    }
    
    // Set default context values if not provided
    const timeOfDay = userContext.timeOfDay || getTimeOfDay();
    const context = {
      ...userContext,
      timeOfDay
    };
    
    
    let userId: string | undefined = undefined;
    try {
      const auth = getAuth();
      userId = auth.currentUser?.uid;
    } catch (err) {
      console.error('Error getting user:', err);
    }
    
    // If we have a user ID, get additional context
    if (userId) {
      try {
        // Get data from Firebase Realtime Database
        const db = getDatabase();
        
        // Get recent sessions from Firebase
        const sessionsRef = ref(db, `users/${userId}/mindfulness_sessions`);
        const sessionsSnapshot = await get(sessionsRef);
        
        if (sessionsSnapshot.exists()) {
          const sessions = sessionsSnapshot.val();
          // Convert to array and sort by date
          const sessionsArray = Object.values(sessions) as {
            tip_id: string;
            session_date: string;
            created_at: string;
          }[];
          
          const sortedSessions = sessionsArray.sort((a, b) => 
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          ).slice(0, 5);
          
          if (sortedSessions.length > 0) {
            context.recentActivities = sortedSessions.map(s => s.tip_id);
            context.lastSession = sortedSessions[0].session_date;
          }
        }
        
        // Get user preferences from Firebase
        const preferencesRef = ref(db, `users/${userId}/mindfulness_preferences`);
        const preferencesSnapshot = await get(preferencesRef);
        
        if (preferencesSnapshot.exists()) {
          const preferences = preferencesSnapshot.val();
          if (preferences.preferred_categories) {
            context.preferredCategories = preferences.preferred_categories;
          }
        }
      } catch (err) {
        console.error('Error fetching user context:', err);
      }
    }
    
    // Generate recommendation using our simple OpenAI client with improved prompt
    const prompt = `You are a mindfulness and wellness expert. Generate a personalized mindfulness recommendation based on the following context: 
      - Time of day: ${context.timeOfDay} 
      ${context.stressLevel ? `- Current stress level: ${context.stressLevel}/10` : ''}
      ${context.goalArea ? `- User goal area: ${context.goalArea}` : ''}
      ${context.preferredCategories?.length ? `- Preferred categories: ${context.preferredCategories.join(', ')}` : ''}
      
      Guidelines for a great mindfulness tip:
      1. Be specific and actionable - guide the user through the practice step by step
      2. Keep it brief - the activity should take 1-5 minutes to complete
      3. Make it contextually relevant to the time of day and user's current state
      4. Focus on the present moment and sensory awareness
      5. Avoid generic advice; provide a novel and engaging practice
      
      The response should include:
      1. A title (short, engaging)
      2. A message (2-3 sentences with step-by-step actionable instructions)
      3. A category (one of: nutrition, stress-relief, mental-health, movement, wellness)
      4. An icon suggestion (one of: eating, breathing, hydration, gratitude, stretch, nature, posture, digital, walking, emotion, meditation)
      5. Gradient colors (start and end hex colors that match the mood of the recommendation)
      
      Format your response as JSON only, with no other text.`;

    // Call our simple OpenAI client with proper timeout handling
    const { data: openAIResult, error: openAIError } = await safeRequest(
      () => simpleChatCompletion([
        { role: 'system', content: prompt }
      ], { 
        temperature: 0.7,
        timeout: 7000 
      }),
      {
        timeout: 8000,
        errorMessage: 'OpenAI completion failed',
      }
    );
    
    // Handle OpenAI result
    if (openAIResult) {
      try {
        const recommendation = JSON.parse(openAIResult) as MindfulnessRecommendation;
        
        // Cache the recommendation
        await AsyncStorage.setItem('cachedMindfulnessRecommendation', JSON.stringify(recommendation));
        await AsyncStorage.setItem('mindfulnessRecommendationTimestamp', Date.now().toString());
        
        return recommendation;
      } catch (parseError) {
        console.error('Error parsing OpenAI response:', parseError);
        throw new Error('Invalid response format from OpenAI');
      }
    }
    
    throw new Error(openAIError?.message || 'Failed to get recommendation from OpenAI');
  } catch (error) {
    console.error('Error generating mindfulness recommendation:', error);
    
    // Return a fallback recommendation on error
    const randomIndex = Math.floor(Math.random() * FALLBACK_RECOMMENDATIONS.length);
    return FALLBACK_RECOMMENDATIONS[randomIndex];
  }
}

// Helper function to determine time of day
function getTimeOfDay(): string {
  const hour = new Date().getHours();
  if (hour >= 5 && hour < 12) return 'morning';
  if (hour >= 12 && hour < 17) return 'afternoon';
  if (hour >= 17 && hour < 21) return 'evening';
  return 'night';
}

// Generate mindfulness tip based on user activity data
export async function generatePersonalizedTip(
  activityData?: { 
    sleepHours?: number; 
    steps?: number; 
    lastMeal?: string;
    waterIntake?: number;
  }
): Promise<MindfulnessRecommendation | null> {
  try {
    // Default values if not provided
    const data = {
      sleepHours: activityData?.sleepHours || 7,
      steps: activityData?.steps || 3000,
      lastMeal: activityData?.lastMeal || 'unknown',
      waterIntake: activityData?.waterIntake || 1000, // ml
      timeOfDay: getTimeOfDay()
    };
    
    // Generate recommendation based on activity data with improved prompt
    const prompt = `You are a mindfulness and wellness expert. Generate a personalized mindfulness tip based on the following health data:
      - Sleep hours: ${data.sleepHours}
      - Steps today: ${data.steps}
      - Last meal: ${data.lastMeal}
      - Water intake today: ${data.waterIntake}ml
      - Current time of day: ${data.timeOfDay}
      
      Guidelines for a great mindfulness tip:
      1. Be specific and actionable - guide the user through the practice step by step
      2. Keep it brief - the activity should take 1-5 minutes to complete
      3. Make it contextually relevant to the time of day and user's current state
      4. Focus on the present moment and sensory awareness
      5. Relate the advice to the health data provided (e.g., if low water intake, consider a mindful hydration exercise)
      
      Based on this data, suggest a helpful and specific mindfulness activity that would benefit the user.
      
      Format your response as a JSON object with the following properties:
      - title: A short, engaging title
      - message: 2-3 sentences with step-by-step actionable instructions
      - category: One of: nutrition, stress-relief, mental-health, movement, wellness
      - icon: One of: eating, breathing, hydration, gratitude, stretch, nature, posture, digital, walking, emotion, meditation
      - gradient_start: A hex color code (e.g., "#43CEA2")
      - gradient_end: A hex color code (e.g., "#185A9D")`;
    
    // Call our simple OpenAI client with proper timeout handling
    const { data: openAIResult, error: openAIError } = await safeRequest(
      () => simpleChatCompletion([
        { role: 'system', content: prompt }
      ], { 
        temperature: 0.7,
        timeout: 7000 
      }),
      {
        timeout: 8000,
        errorMessage: 'OpenAI completion failed',
      }
    );
    
    if (openAIResult) {
      try {
        const recommendation = JSON.parse(openAIResult) as MindfulnessRecommendation;
        
        // Set an appropriate image based on the category
        if (!recommendation.image_url) {
          recommendation.image_url = getCategoryImage(recommendation.category);
        }
        
        return recommendation;
      } catch (parseError) {
        console.error('Error parsing OpenAI response:', parseError);
        throw new Error('Invalid response format from OpenAI');
      }
    }
    
    throw new Error(openAIError?.message || 'Failed to get personalized tip from OpenAI');
  } catch (error) {
    console.error('Error generating personalized tip:', error);
    
    // Return a fallback recommendation on error
    const randomIndex = Math.floor(Math.random() * FALLBACK_RECOMMENDATIONS.length);
    return FALLBACK_RECOMMENDATIONS[randomIndex];
  }
}

// Helper function to get an appropriate image URL for a category
function getCategoryImage(category: string): string {
  switch (category) {
    case 'nutrition':
      return 'https://images.pexels.com/photos/1640773/pexels-photo-1640773.jpeg?auto=compress&cs=tinysrgb&w=600';
    case 'stress-relief':
      return 'https://images.pexels.com/photos/775417/pexels-photo-775417.jpeg?auto=compress&cs=tinysrgb&w=600';
    case 'mental-health':
      return 'https://images.pexels.com/photos/3560044/pexels-photo-3560044.jpeg?auto=compress&cs=tinysrgb&w=600';
    case 'movement':
      return 'https://images.pexels.com/photos/4056723/pexels-photo-4056723.jpeg?auto=compress&cs=tinysrgb&w=600';
    case 'wellness':
      return 'https://images.pexels.com/photos/3560168/pexels-photo-3560168.jpeg?auto=compress&cs=tinysrgb&w=600';
    default:
      return 'https://images.pexels.com/photos/3560168/pexels-photo-3560168.jpeg?auto=compress&cs=tinysrgb&w=600';
  }
} 