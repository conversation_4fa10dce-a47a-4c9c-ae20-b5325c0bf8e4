import { OPENAI_MODEL } from '@/utils/config';
import { getCachedNutritionalExplanation, cacheNutritionalExplanation } from '@/services/cachingService';
import { analyzeFood } from './secureApiClient';

/**
 * Explains the nutritional impact of a meal using OpenAI
 * @param mealData Food analysis data to explain
 * @returns Nutritional explanation with health benefits and considerations
 */
export async function explainNutritionalImpact(
  mealData: {
    name: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber?: number;
    items?: {name: string}[];
  }
): Promise<{ 
  success: boolean; 
  explanation?: {
    summary: string;
    healthBenefits: string[];
    considerations: string[];
    dietaryImplications: string[];
    balanceAssessment: string;
  };
  error?: string;
  fromCache?: boolean;
}> {
  try {
    // First, try to get the explanation from cache
    const cachedResult = await getCachedNutritionalExplanation(mealData);
    if (cachedResult.success && cachedResult.explanation) {
      console.log('Using cached nutritional explanation');
      return {
        success: true,
        explanation: cachedResult.explanation,
        fromCache: true
      };
    }
    
    // If not in cache, continue with Firebase Functions API

    const systemPrompt = `You are a qualified nutritionist who provides balanced, informative explanations about the health impact of meals.
Focus on scientific facts without being judgmental or using extreme language.
Provide a fair assessment that acknowledges both positive aspects and areas for improvement.
Tailor your response to be helpful for someone trying to make informed dietary choices.`;

    const userPrompt = `Analyze this meal and explain its nutritional impact:

Meal: ${mealData.name}
Calories: ${mealData.calories} kcal
Protein: ${mealData.protein}g
Carbs: ${mealData.carbs}g
Fat: ${mealData.fat}g
${mealData.fiber !== undefined ? `Fiber: ${mealData.fiber}g` : ''}
${mealData.items && mealData.items.length > 0 ? `Ingredients: ${mealData.items.map(item => item.name).join(', ')}` : ''}

Please format your response as a JSON object with the following structure:
{
  "summary": "A concise 2-3 sentence overview of the meal's nutritional profile",
  "healthBenefits": ["Benefit 1", "Benefit 2", "Benefit 3"],
  "considerations": ["Consideration 1", "Consideration 2"],
  "dietaryImplications": ["Implication for weight management", "Implication for energy levels", "Implication for overall health"],
  "balanceAssessment": "An assessment of the meal's macronutrient balance and how it fits into a balanced diet"
}`;

    // Make API call via Firebase Functions
    try {
      // Create combined prompt with JSON formatting specifically instructed
      const combinedPrompt = `${systemPrompt}\n\n${userPrompt}\n\nImportant: Return ONLY a valid JSON object, with no additional text or explanation.`;
      
      // Since we don't have an image for this call, we'll pass a dummy placeholder
      const dummyImageUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
      
      const response = await analyzeFood(dummyImageUrl, combinedPrompt);
      
      if (!response.success || !response.analysis) {
        throw new Error('Failed to get nutritional explanation from secure endpoint');
      }
      
      try {
        const parsedJson = JSON.parse(response.analysis);
        
        // Validate required fields
        if (!parsedJson.summary || !parsedJson.healthBenefits || !parsedJson.considerations) {
          throw new Error('Response is missing required fields');
        }

        const explanation = {
          summary: parsedJson.summary,
          healthBenefits: Array.isArray(parsedJson.healthBenefits) ? parsedJson.healthBenefits : [parsedJson.healthBenefits],
          considerations: Array.isArray(parsedJson.considerations) ? parsedJson.considerations : [parsedJson.considerations],
          dietaryImplications: Array.isArray(parsedJson.dietaryImplications) ? parsedJson.dietaryImplications : [parsedJson.dietaryImplications],
          balanceAssessment: parsedJson.balanceAssessment || 'No balance assessment provided'
        };
        
        // Cache the explanation
        await cacheNutritionalExplanation(mealData, explanation);

        return {
          success: true,
          explanation,
          fromCache: false
        };
      } catch (parseError) {
        console.error('Error parsing response:', parseError, response.analysis);
        throw new Error('Failed to parse nutritional explanation data from response');
      }
    } catch (error) {
      console.error('Error making secure API request:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error generating nutritional explanation:', error);
    
    // Fall back to local generation if API fails
    console.log('Falling back to simulated nutritional explanation');
    const result = generateSimulatedNutritionalExplanation(mealData);
    // Cache the simulated explanation too
    if (result.success && result.explanation) {
      await cacheNutritionalExplanation(mealData, result.explanation);
    }
    return result;
  }
}

/**
 * Generates a simulated nutritional explanation when OpenAI is unavailable
 */
function generateSimulatedNutritionalExplanation(mealData: {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber?: number;
  items?: {name: string}[];
}): { 
  success: boolean; 
  explanation: {
    summary: string;
    healthBenefits: string[];
    considerations: string[];
    dietaryImplications: string[];
    balanceAssessment: string;
  }
} {
  // Calculate macronutrient percentages
  const totalCalories = mealData.protein * 4 + mealData.carbs * 4 + mealData.fat * 9;
  const proteinPercentage = Math.round((mealData.protein * 4 / totalCalories) * 100) || 0;
  const carbsPercentage = Math.round((mealData.carbs * 4 / totalCalories) * 100) || 0;
  const fatPercentage = Math.round((mealData.fat * 9 / totalCalories) * 100) || 0;
  
  // Determine if macros are balanced
  const isBalanced = proteinPercentage >= 15 && proteinPercentage <= 35 &&
                     carbsPercentage >= 40 && carbsPercentage <= 65 &&
                     fatPercentage >= 20 && fatPercentage <= 35;
                     
  // Generate benefits based on protein content
  let benefits: string[] = [];
  if (mealData.protein >= 20) {
    benefits.push("Good source of protein to support muscle maintenance and growth");
  }
  if (mealData.protein >= 25) {
    benefits.push("Excellent protein content that helps with satiety and metabolic health");
  }
  if (mealData.fiber && mealData.fiber >= 5) {
    benefits.push("Contains fiber which supports digestive health and sustained energy");
  }
  
  // If benefits are empty, add a generic one
  if (benefits.length === 0) {
    benefits.push("Provides nutrition to fuel your day");
    benefits.push("Contains a mix of macronutrients for energy");
  }
  
  // Generate considerations
  let considerations: string[] = [];
  if (mealData.calories > 800) {
    considerations.push("Higher calorie content - consider portion control if watching weight");
  }
  if (fatPercentage > 40) {
    considerations.push("Contains a higher proportion of fat than recommended");
  }
  if (carbsPercentage > 65) {
    considerations.push("High in carbohydrates which may affect blood sugar levels");
  }
  if (mealData.fiber !== undefined && mealData.fiber < 3) {
    considerations.push("Low in fiber - consider adding vegetables or whole grains");
  }
  
  // If considerations are empty, add a generic one
  if (considerations.length === 0) {
    considerations.push("No major nutritional concerns identified");
  }
  
  // Generate dietary implications
  const dietaryImplications = [
    mealData.calories < 500 
      ? "Lower calorie option suitable for weight management goals" 
      : "Provides substantial energy - ideal for active individuals",
    mealData.protein >= 20 
      ? "Supports muscle recovery and maintenance" 
      : "May need complementary protein sources throughout the day",
    isBalanced 
      ? "Well-balanced macronutrient profile supports overall health" 
      : "Consider balancing with other meals to achieve nutritional targets"
  ];
  
  // Generate balance assessment
  let balanceAssessment = "";
  if (isBalanced) {
    balanceAssessment = `This meal has a well-balanced macronutrient profile with approximately ${proteinPercentage}% protein, ${carbsPercentage}% carbohydrates, and ${fatPercentage}% fat, which aligns with general nutritional guidelines.`;
  } else {
    balanceAssessment = `This meal contains approximately ${proteinPercentage}% protein, ${carbsPercentage}% carbohydrates, and ${fatPercentage}% fat. For optimal nutrition, aim for meals with 15-35% protein, 40-65% carbs, and 20-35% fat.`;
  }
  
  // Generate summary
  const summary = `${mealData.name} provides ${mealData.calories} calories with ${mealData.protein}g protein, ${mealData.carbs}g carbohydrates, and ${mealData.fat}g fat. ${isBalanced ? 'It has a balanced macronutrient profile.' : 'It may benefit from macronutrient adjustments for better balance.'}`;
  
  return {
    success: true,
    explanation: {
      summary,
      healthBenefits: benefits,
      considerations,
      dietaryImplications,
      balanceAssessment
    }
  };
} 