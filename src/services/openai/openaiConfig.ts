import Constants from 'expo-constants';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

// Export constants for OpenAI API
export const OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';
export const OPENAI_MODEL = process.env.EXPO_PUBLIC_OPENAI_MODEL || 'gpt-4-1106-preview';
// API key is now handled by Firebase Functions - no longer exposed
// export const OPENAI_API_KEY = process.env.EXPO_PUBLIC_OPENAI_API_KEY;

// Use tiered models based on complexity and cost
export const OPENAI_MODELS = {
  HIGH_QUALITY: 'gpt-4-1106-preview',   // Most capable, most expensive
  STANDARD: 'gpt-4o-mini',                   // Good balance of capability and cost
  EFFICIENT: 'gpt-3.5-turbo',           // Most cost-effective
  VISION: 'gpt-4o'                      // For image analysis (using gpt-4o which now has vision capabilities)
};

// Track approximate costs per 1000 tokens (subject to change)
export const COST_PER_1K_TOKENS = {
  'gpt-4o': { input: 0.005, output: 0.015 },
  'gpt-4o-mini': { input: 0.0015, output: 0.0060 },
  'gpt-3.5-turbo': { input: 0.001, output: 0.002 },
  'gpt-4-vision-preview': { input: 0.01, output: 0.03 }
};

// Rate limits for API calls (prevent runaway costs)
export const RATE_LIMITS = {
  MAX_REQUESTS_PER_MINUTE: 20,
  MAX_TOKENS_PER_HOUR: 100000,
  MAX_DAILY_COST_USD: 5.00
};

// Set timeout values
export const API_TIMEOUTS = {
  STANDARD_REQUEST: 15000,  // 15 seconds
  COMPLEX_REQUEST: 45000,   // 45 seconds (increased from 25 seconds)
  VISION_REQUEST: 30000     // 30 seconds for image analysis
};

// Interface for OpenAI response
export interface OpenAIChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  choices: {
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
    index: number;
  }[];
}

// Interface for DALL-E Image Generation response
export interface DalleImageGenerationResponse {
  created: number;
  data: { url: string }[];
}

// Track usage stats
interface UsageStats {
  totalApiCalls: number;
  totalTokensUsed: number;
  estimatedCost: number;
  lastReset: string; // ISO date string
}

const API_USAGE_KEY = 'openai_api_usage_stats';
const API_KEY_STORAGE_KEY = 'openai_api_key';

// Add a hardcoded demo API key for development/demo purposes
// This is only for the specific user and will be used when the real API key is not available
export const DEMO_API_KEY = "sk-1234567890abcdefghijklmnopqrstuvwxyz1234567890";
let isSpecialUser = false;

// Function to check if the current user is the special user
export function setSpecialUserStatus(email: string | null) {
  isSpecialUser = email === '<EMAIL>';
  console.log(`Special user status: ${isSpecialUser ? 'Enabled' : 'Disabled'}`);
}

/**
 * Check if the user is a special demo user (for enhanced demo features)
 * @returns True if the user is a special demo user
 */
export function isSpecialDemoUser(): boolean {
  // Always return false to force using the real OpenAI API
  return false;
}

/**
 * Get the OpenAI API key with various fallback mechanisms
 * @returns API key string or undefined if not available
 * @deprecated API keys are now handled server-side via Firebase Functions
 */
export async function getOpenAIApiKeyAsync(): Promise<string | undefined> {
  console.warn('getOpenAIApiKeyAsync is deprecated. Use Firebase Functions for API calls.');
  return undefined;
}

/**
 * Get the OpenAI API key (synchronous version)
 * @returns OpenAI API key string
 * @deprecated API keys are now handled server-side via Firebase Functions
 */
export function getOpenAIApiKey(): string {
  console.warn('getOpenAIApiKey is deprecated. Use Firebase Functions for API calls.');
  return '';
}

/**
 * Store the OpenAI API key in secure storage
 * @param apiKey The API key to store
 * @returns Promise that resolves to true if successful
 */
export async function storeOpenAIApiKey(apiKey: string): Promise<boolean> {
  try {
    // Skip storage on web platform
    if (Platform.OS === 'web') {
      console.log('API key storage not supported on web platform');
      return true;
    }
    
    await SecureStore.setItemAsync(API_KEY_STORAGE_KEY, apiKey);
    return true;
  } catch (error) {
    console.error('Error storing OpenAI API key:', error);
    return false;
  }
}

/**
 * Clear the stored OpenAI API key
 * @returns Promise that resolves to true if successful
 */
export async function clearOpenAIApiKey(): Promise<boolean> {
  try {
    // Skip operations on web platform
    if (Platform.OS === 'web') {
      console.log('API key clearing not supported on web platform');
      return true;
    }
    
    await SecureStore.deleteItemAsync(API_KEY_STORAGE_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing OpenAI API key:', error);
    return false;
  }
}

/**
 * Estimate token count for a string
 * This is a rough approximation; each model tokenizes a bit differently
 * @param text The text to estimate tokens for
 * @returns Estimated token count
 */
export function estimateTokenCount(text: string): number {
  // GPT tokenization is roughly 4 characters per token on average
  // This is a simplified estimate and may vary based on content
  if (!text) return 0;
  
  // If text contains mostly non-Latin characters, adjust the calculation
  const nonLatinCharRatio = Array.from(text).filter(char => char.charCodeAt(0) > 127).length / text.length;
  
  // Use different character-per-token ratios based on content
  const charsPerToken = nonLatinCharRatio > 0.5 ? 2 : 4;
  
  return Math.ceil(text.length / charsPerToken);
}

/**
 * Estimate token count and cost for a full request
 * @param model The model to use
 * @param messages The messages array
 * @returns Estimated tokens and cost
 */
export function estimateRequestImpact(
  model: string, 
  messages: {role: string, content: string}[]
): { 
  inputTokens: number, 
  estimatedOutputTokens: number, 
  estimatedCost: number 
} {
  // Get the model's cost rates, defaulting to gpt-4o if not found
  const costRates = COST_PER_1K_TOKENS[model as keyof typeof COST_PER_1K_TOKENS] || 
                    COST_PER_1K_TOKENS['gpt-4o'];
  
  // Calculate total input tokens
  const inputTokens = messages.reduce((total, msg) => {
    return total + estimateTokenCount(msg.content) + 4; // +4 for role formatting overhead
  }, 0);
  
  // Estimate output tokens (typically 1/3 to 1/2 of input for completion tasks)
  const estimatedOutputTokens = Math.ceil(inputTokens * 0.5);
  
  // Calculate estimated cost
  const inputCost = (inputTokens / 1000) * costRates.input;
  const outputCost = (estimatedOutputTokens / 1000) * costRates.output;
  const estimatedCost = inputCost + outputCost;
  
  return {
    inputTokens,
    estimatedOutputTokens,
    estimatedCost
  };
}

/**
 * Select the most appropriate model based on complexity and cost considerations
 * @param complexity Complexity level of the task (0-1)
 * @param costSensitivity Cost sensitivity level (0-1)
 * @param requiresVision Whether the task requires vision capabilities
 * @returns The most appropriate model name
 */
export function selectAppropriateModel(
  complexity: number = 0.5, 
  costSensitivity: number = 0.5,
  requiresVision: boolean = false
): string {
  // If vision is required, return the vision model regardless
  if (requiresVision) {
    return OPENAI_MODELS.VISION;
  }
  
  // Calculate a score based on complexity and cost sensitivity
  // Higher complexity pushes toward better models
  // Higher cost sensitivity pushes toward cheaper models
  const score = complexity * (1 - costSensitivity);
  
  if (score > 0.7) {
    return OPENAI_MODELS.HIGH_QUALITY;
  } else if (score > 0.3) {
    return OPENAI_MODELS.STANDARD;
  } else {
    return OPENAI_MODELS.EFFICIENT;
  }
}

/**
 * Track API usage
 * @param model The model used
 * @param promptTokens Number of prompt tokens
 * @param completionTokens Number of completion tokens
 */
export async function trackApiUsage(
  model: string,
  promptTokens: number,
  completionTokens: number
): Promise<void> {
  try {
    // Get current stats
    const currentStats = await getApiUsageStats();
    
    // Get cost rates for the model
    const costRates = COST_PER_1K_TOKENS[model as keyof typeof COST_PER_1K_TOKENS] || 
                      COST_PER_1K_TOKENS['gpt-4o'];
    
    // Calculate cost
    const promptCost = (promptTokens / 1000) * costRates.input;
    const completionCost = (completionTokens / 1000) * costRates.output;
    const totalCost = promptCost + completionCost;
    
    // Update stats
    const updatedStats: UsageStats = {
      totalApiCalls: currentStats.totalApiCalls + 1,
      totalTokensUsed: currentStats.totalTokensUsed + promptTokens + completionTokens,
      estimatedCost: currentStats.estimatedCost + totalCost,
      lastReset: currentStats.lastReset
    };
    
    // Store updated stats
    await storeApiUsageStats(updatedStats);
    
    // Log for debugging
    console.log(`API usage tracked: ${promptTokens} prompt + ${completionTokens} completion tokens, $${totalCost.toFixed(4)}`);
  } catch (error) {
    console.error('Error tracking API usage:', error);
  }
}

/**
 * Get current API usage statistics
 * @returns Current usage statistics
 */
export async function getApiUsageStats(): Promise<UsageStats> {
  try {
    if (Platform.OS === 'web') {
      // For web, use a default empty stats object
      return {
        totalApiCalls: 0,
        totalTokensUsed: 0,
        estimatedCost: 0,
        lastReset: new Date().toISOString()
      };
    }
    
    const statsJson = await SecureStore.getItemAsync(API_USAGE_KEY);
    
    if (statsJson) {
      return JSON.parse(statsJson);
    }
    
    // Initialize with empty stats
    const initialStats: UsageStats = {
      totalApiCalls: 0,
      totalTokensUsed: 0,
      estimatedCost: 0,
      lastReset: new Date().toISOString()
    };
    
    await storeApiUsageStats(initialStats);
    return initialStats;
  } catch (error) {
    console.error('Error getting API usage stats:', error);
    
    // Return empty stats as fallback
    return {
      totalApiCalls: 0,
      totalTokensUsed: 0,
      estimatedCost: 0,
      lastReset: new Date().toISOString()
    };
  }
}

/**
 * Store API usage statistics
 * @param stats The stats to store
 */
async function storeApiUsageStats(stats: UsageStats): Promise<void> {
  try {
    if (Platform.OS === 'web') {
      // Skip for web platform
      return;
    }
    
    await SecureStore.setItemAsync(API_USAGE_KEY, JSON.stringify(stats));
  } catch (error) {
    console.error('Error storing API usage stats:', error);
  }
}

/**
 * Reset API usage statistics
 */
export async function resetApiUsageStats(): Promise<void> {
  try {
    if (Platform.OS === 'web') {
      // Skip for web platform
      return;
    }
    
    const resetStats: UsageStats = {
      totalApiCalls: 0,
      totalTokensUsed: 0,
      estimatedCost: 0,
      lastReset: new Date().toISOString()
    };
    
    await storeApiUsageStats(resetStats);
  } catch (error) {
    console.error('Error resetting API usage stats:', error);
  }
}

/**
 * Check if we've exceeded any rate limits
 * @returns True if rate limits have been exceeded
 */
export async function hasExceededRateLimits(): Promise<boolean> {
  try {
    const stats = await getApiUsageStats();
    
    // Check if stats were reset more than 24 hours ago
    const lastReset = new Date(stats.lastReset);
    const now = new Date();
    const hoursSinceReset = (now.getTime() - lastReset.getTime()) / (1000 * 60 * 60);
    
    // If more than 24 hours, reset the stats
    if (hoursSinceReset > 24) {
      await resetApiUsageStats();
      return false;
    }
    
    // Check if we've exceeded the daily cost limit
    if (stats.estimatedCost > RATE_LIMITS.MAX_DAILY_COST_USD) {
      console.warn(`Rate limit exceeded: Daily cost limit of $${RATE_LIMITS.MAX_DAILY_COST_USD} exceeded`);
      return true;
    }
    
    // Check if we've exceeded the hourly token limit (pro-rated based on hours since reset)
    const hourlyTokenAllowance = RATE_LIMITS.MAX_TOKENS_PER_HOUR * hoursSinceReset;
    if (stats.totalTokensUsed > hourlyTokenAllowance) {
      console.warn(`Rate limit exceeded: Hourly token limit exceeded`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Error checking rate limits:', error);
    return false; // Default to not exceeding limits on error
  }
} 