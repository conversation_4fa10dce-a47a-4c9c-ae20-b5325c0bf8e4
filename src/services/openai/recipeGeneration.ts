import { getNutritionForDish } from '@/services/nutritionService';
import { OPENAI_MODEL } from '@/utils/config';
import { isSpecialDemoUser, trackApiUsage, hasExceededRateLimits, API_TIMEOUTS } from './openaiConfig';
import { GenerateAlternativeRecipeRequest, GenerateAlternativeRecipeResponse, Recipe, RECIPE_DATABASE } from './recipeTypes';
import { 
  getCachedRecipes, 
  cacheRecipes, 
  cleanupExpiredRecipeCache 
} from '@/services/recipeGenerationCacheService';
import { isOffline, generateOfflineRecipe, storeRecipeTemplates } from '@/services/offline/offlineRecipeGenerator';
import {
  OperationType,
  ErrorType,
  startOperation,
  completeOperation,
  failOperation,
  determineErrorType,
  getRecoverableRecipe,
  markRecipeRetryAttempt,
  updateRecipePartialResults
} from '@/services/errorRecoveryService';
import { generateRecipe as generateRecipeSecure, generateHealthyAlternative as generateHealthyAlternativeSecure } from './secureApiClient';

/**
 * Generates an alternative recipe using OpenAI based on original food analysis
 * @param request Original food details and user preferences
 * @returns Alternative recipe with nutritional information
 */
export async function generateAlternativeRecipe(
  request: GenerateAlternativeRecipeRequest
): Promise<GenerateAlternativeRecipeResponse> {
  // Emergency fallback recipe in case all other methods fail
  const emergencyFallbackRecipe: Recipe = {
    name: `Simple Healthy ${request.originalFoodName}`,
    focus: "Essential Nutrition",
    description: `A basic healthier version of ${request.originalFoodName}.`,
    ingredients: [
      { name: "Main Ingredient (healthier version)", amount: "1 serving", calories: 100, protein: 10, carbs: 15, fat: 3 },
      { name: "Vegetables", amount: "1 cup", calories: 50, protein: 2, carbs: 10, fat: 0 },
      { name: "Healthy Fat", amount: "1 tbsp", calories: 120, protein: 0, carbs: 0, fat: 14 }
    ],
    instructions: [
      "Combine all ingredients",
      "Cook to your preference",
      "Serve and enjoy!"
    ],
    nutritionalInfo: {
      calories: 270,
      protein: 12,
      carbs: 25,
      fat: 17,
      fiber: 5,
      sugar: 3
    },
    preparationTime: "10 minutes",
    cookingTime: "15 minutes",
    healthBenefits: [
      "Lower in calories than original",
      "More balanced macronutrients",
      "Higher in essential nutrients"
    ],
    imagePrompt: `A simple, healthy version of ${request.originalFoodName} on a plate.`
  };
  
  try {
    // Add debug logging
    console.log('generateAlternativeRecipe called with:', JSON.stringify(request));
    
    try {
      // Start tracking this operation
      await startOperation(OperationType.RECIPE_GENERATION);
      
      // Check for any recoverable recipe generation operations first
      const recoverableRecipe = await getRecoverableRecipe();
      if (recoverableRecipe && 
          recoverableRecipe.originalFoodName === request.originalFoodName && 
          recoverableRecipe.partialResults && 
          recoverableRecipe.partialResults.length > 0) {
        console.log('Recovering previous recipe generation attempt');
        
        // Mark that we're retrying
        await markRecipeRetryAttempt();
        
        // Return the partial results
        return {
          success: true,
          recipes: recoverableRecipe.partialResults,
          recovered: true
        };
      }
      
      // Clean up expired cache entries in the background
      cleanupExpiredRecipeCache().catch(err => console.error('Error cleaning up cache:', err));
      
      // Check cache first to avoid unnecessary API calls
      const cachedRecipes = await getCachedRecipes(
        request.originalFoodName,
        request.nutritionalInfo,
        request.dietaryPreferences,
        request.allergies
      );
      
      if (cachedRecipes) {
        console.log(`Using cached recipes for ${request.originalFoodName}`);
        
        // Mark operation as completed
        await completeOperation(OperationType.RECIPE_GENERATION);
        
        return {
          success: true,
          recipes: cachedRecipes,
          fromCache: true
        };
      }
      
      // Check if device is offline
      const offline = await isOffline();
      if (offline) {
        console.log('Device is offline, using offline recipe generator');
        const offlineResult = await generateOfflineRecipe(request);
        
        // Cache offline recipes for future use
        if (offlineResult.success && offlineResult.recipes && offlineResult.recipes.length > 0) {
          cacheRecipes(
            request.originalFoodName,
            request.nutritionalInfo,
            offlineResult.recipes,
            request.dietaryPreferences,
            request.allergies
          ).catch(err => console.error('Error caching offline recipes:', err));
          
          // Mark operation as completed (even though it's offline)
          await completeOperation(OperationType.RECIPE_GENERATION);
        } else {
          // Mark operation as failed
          await failOperation(
            OperationType.RECIPE_GENERATION,
            ErrorType.NETWORK,
            {
              timestamp: Date.now(),
              originalFoodName: request.originalFoodName,
              nutritionalInfo: request.nutritionalInfo,
              dietaryPreferences: request.dietaryPreferences,
              allergies: request.allergies,
              retryCount: 0
            }
          );
        }
        
        return offlineResult;
      }
      
      // Check if we've exceeded rate limits
      const rateLimitExceeded = await hasExceededRateLimits();
      if (rateLimitExceeded) {
        console.log('Rate limits exceeded, using offline recipe generator');
        
        // Mark as API error
        await failOperation(
          OperationType.RECIPE_GENERATION,
          ErrorType.API_ERROR,
          {
            timestamp: Date.now(),
            originalFoodName: request.originalFoodName,
            nutritionalInfo: request.nutritionalInfo,
            dietaryPreferences: request.dietaryPreferences,
            allergies: request.allergies,
            retryCount: 0
          }
        );
        
        return generateOfflineRecipe(request);
      }
      
      // Continue with API call if online and not in cache
      console.log('Using secure Firebase Functions for recipe generation');

      try {
        // Use the secure Firebase Function instead of direct OpenAI API call
        const response = await generateHealthyAlternativeSecure(
          // Convert request data to match the Firebase function format
          request.originalFoodName,
          `
Original food: ${request.originalFoodName}
Nutritional information: Calories: ${request.nutritionalInfo.calories}kcal, Protein: ${request.nutritionalInfo.protein}g, Carbs: ${request.nutritionalInfo.carbs}g, Fat: ${request.nutritionalInfo.fat}g
${request.dietaryPreferences?.length ? `Dietary preferences: ${request.dietaryPreferences.join(', ')}` : ''}
${request.allergies?.length ? `Allergies to avoid: ${request.allergies.join(', ')}` : ''}

Create ONE healthier alternative recipe with similar flavor profile but improved nutrition.`
        );
        
        if (!response.success || !response.recipe) {
          throw new Error('Failed to generate recipe via Firebase Function');
        }
        
        // Track API usage for cost and rate limiting
        if (response.usage) {
          await trackApiUsage(
            'gpt-4o',
            response.usage.prompt_tokens,
            response.usage.completion_tokens
          );
        }
        
        try {
          // Parse the response from Firebase Function
          const responseContent = response.recipe;
          
          // Strip markdown code block formatting if present
          let cleanedContent = responseContent;
          if (responseContent.includes('```')) {
            // Remove markdown code blocks (```json and ```)
            cleanedContent = responseContent
              .replace(/```json\n/g, '')
              .replace(/```\n/g, '')
              .replace(/```/g, '');
          }
          
          const parsedJson = JSON.parse(cleanedContent);
          
          if (!parsedJson.alternatives || !Array.isArray(parsedJson.alternatives)) {
             throw new Error('Response does not contain an "alternatives" array.');
          }

          const recipes: Recipe[] = [];
          for (const recipeJson of parsedJson.alternatives) {
            // Validate required fields for each recipe
            if (!recipeJson.name || !recipeJson.ingredients || !recipeJson.instructions || !recipeJson.nutritionalInfo) {
              console.warn('Skipping recipe due to missing required fields:', recipeJson.name || 'Unknown');
              continue;
            }

            // Process and validate the recipe
            const validatedRecipe = validateAndFormatRecipe(recipeJson, request.originalFoodName);
            recipes.push(validatedRecipe);
            
            // Update recovery data with partial results as they're validated
            if (recipes.length > 0) {
              await updateRecipePartialResults(recipes);
            }
          }

          if (recipes.length === 0) {
            throw new Error('No valid recipes found in the response.');
          }
          
          // Cache the generated recipes for future use
          cacheRecipes(
            request.originalFoodName,
            request.nutritionalInfo,
            recipes,
            request.dietaryPreferences,
            request.allergies
          ).catch(err => console.error('Error caching recipes:', err));
          
          // Mark operation as completed
          await completeOperation(OperationType.RECIPE_GENERATION);
          
          return {
            success: true,
            recipes: recipes
          };
        } catch (parseError) {
          console.error('Error parsing response:', parseError);
          throw new Error('Failed to parse recipe data from response');
        }
      } catch (fetchError) {
        console.error('Error calling Firebase Function:', fetchError);
        
        // Determine error type for recovery
        const errorType = determineErrorType(fetchError);
        
        // Save recovery data
        await failOperation(
          OperationType.RECIPE_GENERATION,
          errorType,
          {
            timestamp: Date.now(),
            originalFoodName: request.originalFoodName,
            nutritionalInfo: request.nutritionalInfo,
            dietaryPreferences: request.dietaryPreferences,
            allergies: request.allergies,
            retryCount: 0
          }
        );
        
        // Enhanced error handling for network-related issues
        if (fetchError instanceof Error && fetchError.message.includes('timed out')) {
          console.log('API request timed out, falling back to offline generation');
          console.log('Failed recipe_generation operation with error type: timeout');
        } else {
          console.log('API request failed due to connectivity issues, falling back to offline generation');
          
          // Log more specific error information for debugging
          if (fetchError instanceof Error) {
            console.log('Failed recipe_generation operation with error type:', 
              fetchError.message.includes('timed out') ? 'timeout' : 
              fetchError.message.includes('network') ? 'network' : 
              'api_error'
            );
          }
        }
        
        // Try to get data from the cache first
        try {
          const cachedRecipes = await getCachedRecipes(
            request.originalFoodName,
            request.nutritionalInfo,
            request.dietaryPreferences,
            request.allergies
          );
          
          if (cachedRecipes && cachedRecipes.length > 0) {
            console.log('Found cached recipes to use as fallback');
            return {
              success: true,
              recipes: cachedRecipes,
              fromCache: true
            };
          }
        } catch (cacheError) {
          console.warn('Error checking recipe cache:', cacheError);
        }
        
        // If no cache, try offline generation
        try {
          console.log('Attempting offline recipe generation as fallback');
          return await generateOfflineRecipe(request);
        } catch (offlineError) {
          console.error('Offline generation failed, using emergency fallback recipe:', offlineError);
          return {
            success: true,
            recipes: [emergencyFallbackRecipe],
            isEmergencyFallback: true
          };
        }
      }
    } catch (error) {
      console.error('Error generating recipe with OpenAI:', error);
      
      // Determine error type for recovery
      const errorType = determineErrorType(error);
      
      // Save recovery data
      await failOperation(
        OperationType.RECIPE_GENERATION,
        errorType,
        {
          timestamp: Date.now(),
          originalFoodName: request.originalFoodName,
          nutritionalInfo: request.nutritionalInfo,
          dietaryPreferences: request.dietaryPreferences,
          allergies: request.allergies,
          retryCount: 0
        }
      );
      
      // Handle various error scenarios with appropriate fallbacks
      if (error instanceof Error) {
        // API key issues, connection problems, or parsing errors - use offline generation
        if (error.message.includes('API key') || 
            error.message.includes('OpenAI API error') ||
            error.message.includes('No valid recipes') ||
            error.message.includes('Failed to parse recipe data') ||
            error.message.includes('network') ||
            error.message.includes('connection') ||
            error.message.includes('timed out')) {
          
          console.log('Falling back to offline recipe generation due to error:', error.message);
          try {
            return await generateOfflineRecipe(request);
          } catch (offlineError) {
            console.error('Offline generator failed, using emergency fallback:', offlineError);
            return {
              success: true,
              recipes: [emergencyFallbackRecipe],
              isEmergencyFallback: true
            };
          }
        }
      }
      
      // For other errors, try using demo generator if special user, otherwise use offline generator
      console.log('Falling back to local recipe generation.');
      try {
        // Check if we're a special demo user
        const isSpecial = await isSpecialDemoUser();
        if (isSpecial) {
          console.log('Special user detected, using enhanced demo recipe generator');
          const result = generateEnhancedDemoRecipe(request);
          
          // Cache the demo recipes
          if (result.success && result.recipes && result.recipes.length > 0) {
            cacheRecipes(
              request.originalFoodName,
              request.nutritionalInfo,
              result.recipes,
              request.dietaryPreferences,
              request.allergies
            ).catch(err => console.error('Error caching demo recipes:', err));
          }
          
          return result;
        } else {
          // Standard user, fall back to offline generation
          const result = await generateOfflineRecipe(request);
          
          // Cache the fallback recipes if successful
          if (result.success && result.recipes && result.recipes.length > 0) {
            cacheRecipes(
              request.originalFoodName,
              request.nutritionalInfo,
              result.recipes,
              request.dietaryPreferences,
              request.allergies
            ).catch(err => console.error('Error caching fallback recipes:', err));
          }
          
          return result;
        }
      } catch (fallbackError) {
        console.error('All fallbacks failed, using emergency fallback recipe:', fallbackError);
        return {
          success: true,
          recipes: [emergencyFallbackRecipe],
          isEmergencyFallback: true
        };
      }
    }
  } catch (error) {
    console.error('Error generating recipe with OpenAI:', error);
    
    // Record failure in operation tracking
    const finalError = error as Error;
    await failOperation(OperationType.RECIPE_GENERATION, ErrorType.UNKNOWN, {
      timestamp: Date.now(),
      originalFoodName: request.originalFoodName,
      nutritionalInfo: request.nutritionalInfo,
      retryCount: 0
    });
    
    // Handle various error scenarios with appropriate fallbacks
    if (finalError instanceof Error) {
      // API key issues, connection problems, or parsing errors - use offline generation
      if (finalError.message.includes('API key') || 
          finalError.message.includes('OpenAI API error') ||
          finalError.message.includes('No valid recipes') ||
          finalError.message.includes('Failed to parse recipe data') ||
          finalError.message.includes('network') ||
          finalError.message.includes('connection') ||
          finalError.message.includes('timed out')) {
        
        console.log('Falling back to offline recipe generation due to error:', finalError.message);
        try {
          return await generateOfflineRecipe(request);
        } catch (offlineError) {
          console.error('Offline generator failed, using emergency fallback:', offlineError);
          return {
            success: true,
            recipes: [emergencyFallbackRecipe],
            isEmergencyFallback: true
          };
        }
      }
    }
    
    // For other errors, try using demo generator if special user, otherwise use offline generator
    console.log('Falling back to local recipe generation.');
    try {
      // Check if we're a special demo user
      const isSpecial = await isSpecialDemoUser();
      if (isSpecial) {
        console.log('Special user detected, using enhanced demo recipe generator');
        const result = generateEnhancedDemoRecipe(request);
        
        // Cache the demo recipes
        if (result.success && result.recipes && result.recipes.length > 0) {
          cacheRecipes(
            request.originalFoodName,
            request.nutritionalInfo,
            result.recipes,
            request.dietaryPreferences,
            request.allergies
          ).catch(err => console.error('Error caching demo recipes:', err));
        }
        
        return result;
      } else {
        // Standard user, fall back to offline generation
        const result = await generateOfflineRecipe(request);
        
        // Cache the fallback recipes if successful
        if (result.success && result.recipes && result.recipes.length > 0) {
          cacheRecipes(
            request.originalFoodName,
            request.nutritionalInfo,
            result.recipes,
            request.dietaryPreferences,
            request.allergies
          ).catch(err => console.error('Error caching fallback recipes:', err));
        }
        
        return result;
      }
    } catch (fallbackError) {
      console.error('All fallbacks failed, using emergency fallback recipe:', fallbackError);
      return {
        success: true,
        recipes: [emergencyFallbackRecipe],
        isEmergencyFallback: true
      };
    }
  }
}

/**
 * Helper function to validate and format a recipe from the API response
 */
function validateAndFormatRecipe(recipeJson: any, originalFoodName: string): Recipe {
  // Ensure healthBenefits is an array
  if (recipeJson.healthBenefits && !Array.isArray(recipeJson.healthBenefits)) {
    recipeJson.healthBenefits = [recipeJson.healthBenefits];
  } else if (!recipeJson.healthBenefits) {
    recipeJson.healthBenefits = ['More nutritious than the original recipe'];
  }

  // Make sure instructions is an array
  if (recipeJson.instructions && !Array.isArray(recipeJson.instructions)) {
    recipeJson.instructions = [recipeJson.instructions];
  } else if (!recipeJson.instructions) {
    recipeJson.instructions = ['Combine all ingredients and mix well.'];
  }
  
  // Add sensible defaults for missing fields
  if (!recipeJson.description) {
    recipeJson.description = `A healthier version of ${originalFoodName} with improved nutritional profile.`;
  }
  if (!recipeJson.preparationTime) {
    recipeJson.preparationTime = '15 minutes';
  }
  if (!recipeJson.cookingTime) {
    recipeJson.cookingTime = '20 minutes';
  }
  if (!recipeJson.imagePrompt) {
    recipeJson.imagePrompt = `A healthy, appetizing version of ${recipeJson.name || originalFoodName} on a white plate with garnish.`;
  }
  if (!recipeJson.focus) {
    recipeJson.focus = 'Balanced';
  }
  
  return recipeJson as Recipe;
}

/**
 * Generates an enhanced demo recipe for the special user
 * This provides higher quality recipes for demo purposes without using the actual API
 */
export function generateEnhancedDemoRecipe(request: GenerateAlternativeRecipeRequest): GenerateAlternativeRecipeResponse {
  const originalName = request.originalFoodName;
  
  // Check if this is a dessert by looking for keywords in the name
  const isDessert = /cake|pie|cookie|dessert|sweet|cheesecake|chocolate|ice cream|pastry|brownie|pudding/i.test(originalName);
  
  // If this is a dessert, use dessert-specific templates
  if (isDessert) {
    return generateDessertRecipes(request);
  }
  
  // Generate high-quality demo recipes based on food type
  const foodCategory = determineFoodType(originalName).toLowerCase();
  
  // Create 4 distinct recipe variations for demo purposes
  const demoRecipes: Recipe[] = [];
  
  // First recipe - Protein focused
  const proteinRecipe: Recipe = {
    name: `Protein-Rich ${originalName}`,
    focus: "High Protein",
    description: `A high-protein version of ${originalName} that maintains the delicious flavors while boosting the protein content. Perfect for muscle recovery and satiety.`,
    ingredients: [
      { name: "Lean Protein (Chicken/Tofu/Fish)", amount: "6 oz", calories: 180, protein: 35, carbs: 0, fat: 3 },
      { name: "Mixed Vegetables", amount: "1 cup", calories: 50, protein: 2, carbs: 10, fat: 0 },
      { name: "Complex Carbs", amount: "1/2 cup", calories: 100, protein: 3, carbs: 20, fat: 1 },
      { name: "Olive Oil", amount: "1 tbsp", calories: 120, protein: 0, carbs: 0, fat: 14 },
      { name: "Fresh Herbs", amount: "2 tbsp", calories: 5, protein: 0, carbs: 1, fat: 0 }
    ],
    instructions: [
      "Prepare protein by grilling or baking with minimal oil",
      "Steam or roast vegetables until tender-crisp",
      "Combine with complex carbs like quinoa or brown rice",
      "Drizzle with olive oil and season with fresh herbs",
      "Serve immediately for optimal flavor and nutrition"
    ],
    nutritionalInfo: {
      calories: 455,
      protein: 40,
      carbs: 31,
      fat: 18,
      fiber: 5,
      sugar: 3
    },
    preparationTime: "10 minutes",
    cookingTime: "20 minutes",
    healthBenefits: [
      "High in protein for muscle repair and growth",
      "Contains essential amino acids",
      "Good source of various vitamins and minerals"
    ],
    imagePrompt: `A beautifully plated high-protein version of ${originalName} with visible lean protein, colorful vegetables, and garnish on a white plate with natural lighting.`
  };
  demoRecipes.push(proteinRecipe);
  
  // Second recipe - Low carb
  const lowCarbRecipe: Recipe = {
    name: `Low-Carb ${originalName}`,
    focus: "Low Carb",
    description: `A low-carbohydrate twist on traditional ${originalName} that substitutes carb-heavy ingredients with lighter alternatives.`,
    ingredients: [
      { name: "Protein Source", amount: "4 oz", calories: 120, protein: 25, carbs: 0, fat: 2 },
      { name: "Cauliflower Rice/Zucchini Noodles", amount: "1 cup", calories: 40, protein: 2, carbs: 8, fat: 0 },
      { name: "Low-Carb Vegetables", amount: "1.5 cups", calories: 75, protein: 3, carbs: 15, fat: 0 },
      { name: "Healthy Fats", amount: "2 tbsp", calories: 180, protein: 0, carbs: 0, fat: 20 },
      { name: "Low-Carb Seasoning Blend", amount: "1 tbsp", calories: 10, protein: 0, carbs: 2, fat: 0 }
    ],
    instructions: [
      "Prepare your low-carb base (cauliflower rice or veggie noodles)",
      "Cook protein using preferred method until done",
      "Sauté low-carb vegetables until tender",
      "Combine all ingredients with healthy fats and seasonings",
      "Adjust seasoning to taste and serve"
    ],
    nutritionalInfo: {
      calories: 425,
      protein: 30,
      carbs: 25,
      fat: 22,
      fiber: 8,
      sugar: 4
    },
    preparationTime: "15 minutes",
    cookingTime: "15 minutes",
    healthBenefits: [
      "Helps regulate blood sugar levels",
      "Supports ketogenic and low-carb dietary needs",
      "Rich in fiber despite lower carb content"
    ],
    imagePrompt: `A delicious low-carb version of ${originalName} featuring vegetable substitutes for high-carb ingredients.`
  };
  demoRecipes.push(lowCarbRecipe);
  
  // Third recipe - Quick & Easy
  const quickRecipe: Recipe = {
    name: `Quick ${originalName}`,
    focus: "Quick",
    description: `A simplified version of ${originalName} that can be prepared in under 30 minutes without sacrificing nutrition or flavor.`,
    ingredients: [
      { name: "Pre-cooked Protein", amount: "4 oz", calories: 150, protein: 28, carbs: 0, fat: 4 },
      { name: "Pre-cut Vegetables", amount: "2 cups", calories: 80, protein: 4, carbs: 16, fat: 0 },
      { name: "Instant Whole Grains", amount: "1/2 cup", calories: 120, protein: 5, carbs: 25, fat: 1 },
      { name: "Premade Healthy Sauce", amount: "2 tbsp", calories: 60, protein: 1, carbs: 8, fat: 3 },
      { name: "Herbs & Spices", amount: "to taste", calories: 5, protein: 0, carbs: 1, fat: 0 }
    ],
    instructions: [
      "Heat pre-cooked protein in microwave or skillet",
      "Steam pre-cut vegetables in microwave (3-4 minutes)",
      "Prepare instant whole grains according to package",
      "Combine all ingredients and add sauce",
      "Top with herbs and serve immediately"
    ],
    nutritionalInfo: {
      calories: 415,
      protein: 38,
      carbs: 50,
      fat: 8,
      fiber: 7,
      sugar: 6
    },
    preparationTime: "5 minutes",
    cookingTime: "10 minutes",
    healthBenefits: [
      "Balanced nutrition in minimal time",
      "Perfect for busy weeknights",
      "Contains all essential macronutrients"
    ],
    imagePrompt: `A quick and healthy version of ${originalName} arranged neatly on a plate, showing how fast meal prep can still look appetizing.`
  };
  demoRecipes.push(quickRecipe);
  
  // Fourth recipe - Plant-based
  const plantBasedRecipe: Recipe = {
    name: `Plant-Based ${originalName}`,
    focus: "Plant-Based",
    description: `A completely plant-based version of ${originalName} that's rich in nutrients, fiber, and plant protein while maintaining a satisfying taste and texture.`,
    ingredients: [
      { name: "Plant Protein (Tofu/Tempeh/Legumes)", amount: "6 oz", calories: 160, protein: 18, carbs: 8, fat: 9 },
      { name: "Colorful Vegetables", amount: "2 cups", calories: 90, protein: 5, carbs: 18, fat: 0 },
      { name: "Whole Grains", amount: "3/4 cup", calories: 150, protein: 6, carbs: 30, fat: 2 },
      { name: "Healthy Plant Fats", amount: "1 tbsp", calories: 120, protein: 0, carbs: 0, fat: 14 },
      { name: "Nutritional Yeast", amount: "1 tbsp", calories: 20, protein: 3, carbs: 2, fat: 0 },
      { name: "Fresh Herbs", amount: "1/4 cup", calories: 10, protein: 1, carbs: 2, fat: 0 }
    ],
    instructions: [
      "Prepare plant protein according to type (e.g., press and cube tofu, rinse legumes)",
      "Sauté or roast vegetables with minimal oil",
      "Cook whole grains until tender",
      "Combine all ingredients in a bowl",
      "Add plant fats, nutritional yeast, and herbs for flavor",
      "Toss well and serve warm"
    ],
    nutritionalInfo: {
      calories: 550,
      protein: 33,
      carbs: 60,
      fat: 25,
      fiber: 16,
      sugar: 8
    },
    preparationTime: "15 minutes",
    cookingTime: "25 minutes",
    healthBenefits: [
      "High in dietary fiber and plant nutrients",
      "Cholesterol-free and heart-healthy",
      "Rich in antioxidants and phytonutrients",
      "Environmentally sustainable option"
    ],
    imagePrompt: `A vibrant plant-based version of ${originalName} showcasing colorful vegetables, whole grains, and plant proteins arranged beautifully on a rustic plate.`
  };
  demoRecipes.push(plantBasedRecipe);
  
  // Return all 4 recipes
  return {
    success: true,
    recipes: demoRecipes
  };
}

/**
 * Generates dessert-specific recipes for detected dessert items
 */
function generateDessertRecipes(request: GenerateAlternativeRecipeRequest): GenerateAlternativeRecipeResponse {
  const originalName = request.originalFoodName;
  const dessertRecipes: Recipe[] = [];
  
  // 1. Healthier version with reduced sugar and fat
  const healthierDessertRecipe: Recipe = {
    name: `Healthier ${originalName}`,
    focus: "Lower Calorie",
    description: `A lighter version of traditional ${originalName} with reduced sugar and fat content, but still maintaining the delicious taste and texture you love.`,
    ingredients: [
      { name: "Greek Yogurt", amount: "1 cup", calories: 130, protein: 22, carbs: 8, fat: 0 },
      { name: "Low-fat Cream Cheese", amount: "8 oz", calories: 200, protein: 14, carbs: 8, fat: 14 },
      { name: "Monk Fruit Sweetener", amount: "1/3 cup", calories: 0, protein: 0, carbs: 0, fat: 0 },
      { name: "Almond Flour", amount: "1 cup", calories: 160, protein: 6, carbs: 6, fat: 14 },
      { name: "Dark Chocolate (70%)", amount: "2 oz", calories: 180, protein: 2, carbs: 14, fat: 12 },
      { name: "Fresh Berries", amount: "1 cup", calories: 85, protein: 1, carbs: 20, fat: 0 }
    ],
    instructions: [
      "Preheat oven to 325°F and prepare a springform pan",
      "Blend almond flour with a touch of coconut oil for the crust",
      "Press mixture into pan and bake for 10 minutes",
      "Mix cream cheese, Greek yogurt, and monk fruit sweetener until smooth",
      "Pour filling over crust and bake for 30 minutes",
      "Let cool and top with melted dark chocolate and fresh berries"
    ],
    nutritionalInfo: {
      calories: 320,
      protein: 12,
      carbs: 24,
      fat: 20,
      fiber: 6,
      sugar: 12
    },
    preparationTime: "15 minutes",
    cookingTime: "40 minutes",
    healthBenefits: [
      "40% fewer calories than traditional recipe",
      "Reduced sugar content with natural sweeteners",
      "Higher protein content for better satiety",
      "Added fiber for digestive health"
    ],
    imagePrompt: `A beautifully plated slice of healthier ${originalName} garnished with fresh berries and mint leaves, showing a creamy texture and rich appearance despite being a lighter version.`
  };
  dessertRecipes.push(healthierDessertRecipe);
  
  // 2. Gluten-free version
  const glutenFreeDessertRecipe: Recipe = {
    name: `Gluten-Free ${originalName}`,
    focus: "Gluten-Free",
    description: `A completely gluten-free version of ${originalName} that's perfect for those with celiac disease or gluten sensitivity, without compromising on taste.`,
    ingredients: [
      { name: "Gluten-free Graham Crackers", amount: "1.5 cups", calories: 180, protein: 2, carbs: 30, fat: 6 },
      { name: "Cream Cheese", amount: "16 oz", calories: 400, protein: 16, carbs: 16, fat: 32 },
      { name: "Pure Maple Syrup", amount: "1/4 cup", calories: 200, protein: 0, carbs: 53, fat: 0 },
      { name: "Eggs", amount: "2 large", calories: 140, protein: 12, carbs: 0, fat: 10 },
      { name: "Vanilla Extract", amount: "1 tsp", calories: 12, protein: 0, carbs: 1, fat: 0 },
      { name: "Dark Chocolate Chips (GF)", amount: "1/2 cup", calories: 280, protein: 3, carbs: 30, fat: 17 }
    ],
    instructions: [
      "Crush gluten-free graham crackers and mix with melted butter",
      "Press into springform pan and chill",
      "Beat cream cheese until fluffy, then add maple syrup and vanilla",
      "Mix in eggs one at a time until smooth",
      "Pour over crust and bake at 325°F for 45-50 minutes",
      "Melt chocolate chips and drizzle over cooled cake"
    ],
    nutritionalInfo: {
      calories: 380,
      protein: 8,
      carbs: 32,
      fat: 24,
      fiber: 2,
      sugar: 18
    },
    preparationTime: "20 minutes",
    cookingTime: "50 minutes",
    healthBenefits: [
      "Safe for those with celiac disease or gluten sensitivity",
      "Uses natural sweeteners where possible",
      "Rich in protein from cream cheese and eggs"
    ],
    imagePrompt: `A delicious gluten-free ${originalName} on a decorative plate with a slice removed to show the creamy texture, topped with dark chocolate drizzle and fresh mint.`
  };
  dessertRecipes.push(glutenFreeDessertRecipe);
  
  // 3. Vegan version
  const veganDessertRecipe: Recipe = {
    name: `Vegan ${originalName}`,
    focus: "Plant-Based",
    description: `A completely plant-based version of ${originalName} that uses no animal products but still delivers a rich, creamy texture and decadent flavor.`,
    ingredients: [
      { name: "Cashews (soaked)", amount: "2 cups", calories: 450, protein: 12, carbs: 24, fat: 36 },
      { name: "Coconut Cream", amount: "1 cup", calories: 320, protein: 3, carbs: 8, fat: 32 },
      { name: "Agave Nectar", amount: "1/3 cup", calories: 240, protein: 0, carbs: 60, fat: 0 },
      { name: "Coconut Oil", amount: "1/4 cup", calories: 480, protein: 0, carbs: 0, fat: 56 },
      { name: "Lemon Juice", amount: "2 tbsp", calories: 10, protein: 0, carbs: 3, fat: 0 },
      { name: "Vegan Chocolate Chips", amount: "1/2 cup", calories: 280, protein: 2, carbs: 32, fat: 16 },
      { name: "Almond Flour", amount: "1.5 cups", calories: 240, protein: 9, carbs: 9, fat: 21 }
    ],
    instructions: [
      "Process almond flour and coconut oil for crust and press into pan",
      "Blend soaked cashews, coconut cream, agave, and lemon juice until smooth",
      "Pour filling over crust and freeze for 4 hours",
      "Melt vegan chocolate chips with a little coconut oil",
      "Top frozen cake with chocolate sauce and return to freezer briefly",
      "Thaw for 20 minutes before serving"
    ],
    nutritionalInfo: {
      calories: 350,
      protein: 5,
      carbs: 28,
      fat: 26,
      fiber: 3,
      sugar: 20
    },
    preparationTime: "25 minutes",
    cookingTime: "0 minutes (+ 4 hours freezing)",
    healthBenefits: [
      "Completely plant-based and dairy-free",
      "Contains healthy fats from nuts and coconut",
      "Free from cholesterol and animal products",
      "Rich in minerals from nuts and plant ingredients"
    ],
    imagePrompt: `A slice of vegan ${originalName} with a beautiful creamy texture, topped with chocolate drizzle and berries, shown on a dark plate against a light background.`
  };
  dessertRecipes.push(veganDessertRecipe);
  
  // 4. Protein-boosted version
  const proteinDessertRecipe: Recipe = {
    name: `Protein-Boosted ${originalName}`,
    focus: "High Protein",
    description: `A fitness-friendly version of ${originalName} that's higher in protein and lower in sugar, perfect for satisfying your sweet tooth while supporting your fitness goals.`,
    ingredients: [
      { name: "Low-fat Cream Cheese", amount: "8 oz", calories: 200, protein: 14, carbs: 8, fat: 14 },
      { name: "Greek Yogurt", amount: "1 cup", calories: 130, protein: 22, carbs: 8, fat: 0 },
      { name: "Vanilla Protein Powder", amount: "2 scoops", calories: 240, protein: 48, carbs: 6, fat: 2 },
      { name: "Egg Whites", amount: "4", calories: 70, protein: 14, carbs: 0, fat: 0 },
      { name: "Stevia", amount: "2 tbsp", calories: 0, protein: 0, carbs: 0, fat: 0 },
      { name: "Oat Fiber", amount: "1/2 cup", calories: 40, protein: 1, carbs: 18, fat: 0 },
      { name: "Dark Chocolate Protein Topping", amount: "1/4 cup", calories: 120, protein: 10, carbs: 8, fat: 6 }
    ],
    instructions: [
      "Mix oat fiber with a little coconut oil for the crust",
      "Press into springform pan and bake for 10 minutes at 325°F",
      "Beat cream cheese, Greek yogurt, protein powder, and stevia until smooth",
      "Fold in whipped egg whites gently",
      "Pour over crust and bake for 25-30 minutes",
      "Cool and top with protein chocolate mixture"
    ],
    nutritionalInfo: {
      calories: 290,
      protein: 28,
      carbs: 16,
      fat: 14,
      fiber: 4,
      sugar: 6
    },
    preparationTime: "15 minutes",
    cookingTime: "40 minutes",
    healthBenefits: [
      "High protein content supports muscle recovery",
      "Lower in carbs and sugar than traditional recipe",
      "Contains slow-digesting proteins for satiety",
      "Can fit into a balanced fitness nutrition plan"
    ],
    imagePrompt: `A protein-rich slice of ${originalName} on a white plate with a fork taking a bite, showing its dense, creamy texture with a chocolate protein topping.`
  };
  dessertRecipes.push(proteinDessertRecipe);
  
  return {
    success: true,
    recipes: dessertRecipes
  };
}

/**
 * Generates a recipe based on the actual food detected in the image
 * @param request Original food details and user preferences
 * @returns Recipe with the original food's ingredients
 */
export async function generateRecipeFromFood(
  request: GenerateAlternativeRecipeRequest
): Promise<GenerateAlternativeRecipeResponse> {
  // Emergency fallback recipe in case all other methods fail
  const emergencyFallbackRecipe: Recipe = {
    name: `Recipe with ${request.originalFoodName}`,
    focus: "Original Dish",
    description: `A delicious recipe featuring ${request.originalFoodName}.`,
    ingredients: [
      { name: "Main Ingredient", amount: "1 serving", calories: 100, protein: 10, carbs: 15, fat: 3 },
      { name: "Vegetables", amount: "1 cup", calories: 50, protein: 2, carbs: 10, fat: 0 },
      { name: "Seasonings", amount: "to taste", calories: 0, protein: 0, carbs: 0, fat: 0 }
    ],
    instructions: [
      "Prepare all ingredients",
      "Combine ingredients according to your preference",
      "Serve and enjoy"
    ],
    nutritionalInfo: {
      calories: request.nutritionalInfo.calories,
      protein: request.nutritionalInfo.protein,
      carbs: request.nutritionalInfo.carbs,
      fat: request.nutritionalInfo.fat,
      fiber: 3,
      sugar: 2
    },
    preparationTime: "10 minutes",
    cookingTime: "15 minutes",
    healthBenefits: [
      "Enjoy the original flavors",
      "Simple to prepare"
    ],
    imagePrompt: `A delicious serving of ${request.originalFoodName}.`
  };

  // Operation tracking
  await startOperation(OperationType.RECIPE_GENERATION);

  try {
    // Check if this is a pasta dish - these often timeout with the API
    // Generate immediately with the offline generator
    if (
      request.originalFoodName.toLowerCase().includes('pasta') || 
      request.originalFoodName.toLowerCase().includes('noodle') ||
      request.originalFoodName.toLowerCase().includes('casserole') ||
      request.originalFoodName.toLowerCase().includes('macaroni') ||
      request.originalFoodName.toLowerCase().includes('lasagna')
    ) {
      console.log('Pasta dish detected, using offline generator for faster response');
      
      // Pass the scanned ingredients to the offline generator if available
      // We can access these from request.nutritionalInfo if provided
      const pastalResult = await generateOfflineRecipe(request);
      
      return pastalResult;
    }

    // Check if we're offline
    if (await isOffline()) {
      console.log('Device is offline, using offline recipe generator');
      const offlineRecipe = await generateOfflineRecipe(request);
      return offlineRecipe;
    }

    // Try to get recipes from cache first
    const cachedRecipes = await getCachedRecipes(
      request.originalFoodName,
      request.nutritionalInfo,
      request.dietaryPreferences,
      request.allergies
    );

    if (cachedRecipes) {
      console.log(`Found ${cachedRecipes.length} cached recipes for ${request.originalFoodName}`);
      
      // Return cached recipes
      await completeOperation(OperationType.RECIPE_GENERATION);
      return {
        success: true,
        recipes: cachedRecipes
      };
    }

    // If we're a special demo user, generate enhanced recipes
    if (await isSpecialDemoUser()) {
      console.log('Special user detected, but using real OpenAI API instead of demo generator');
      // Continue with the real OpenAI API flow instead of using the demo generator
    }

    // Check if we have exceeded rate limits
    if (await hasExceededRateLimits()) {
      console.log('Rate limits exceeded, using fallback recipe generator');
      return {
        success: true,
        recipes: [emergencyFallbackRecipe]
      };
    }

    // Check for recoverable recipe attempt
    const recoverableRecipes = await getRecoverableRecipe();
    if (recoverableRecipes && recoverableRecipes.partialResults && recoverableRecipes.partialResults.length > 0) {
      console.log('Using recoverable recipe from previous attempt');
      
      // Mark this as a retry attempt
      await markRecipeRetryAttempt();
      
      return {
        success: true,
        recipes: recoverableRecipes.partialResults
      };
    }

    // Prepare a simplified prompt for the Firebase function
    const recipePrompt = `Original food: ${request.originalFoodName}
Nutritional information:
- Calories: ${request.nutritionalInfo.calories} kcal
- Protein: ${request.nutritionalInfo.protein}g
- Carbs: ${request.nutritionalInfo.carbs}g
- Fat: ${request.nutritionalInfo.fat}g

${request.dietaryPreferences?.length ? `Dietary preferences: ${request.dietaryPreferences.join(', ')}` : ''}
${request.allergies?.length ? `Allergies to avoid: ${request.allergies.join(', ')}` : ''}

Create a recipe using the ingredients in ${request.originalFoodName}.`;

    try {
      // Use the Firebase function instead of direct OpenAI call
      const response = await generateRecipeSecure(
        [request.originalFoodName], // ingredients array
        recipePrompt,
        'Main Dish', // meal type
        undefined, // cuisine type
        'Medium' // difficulty
      );
      
      if (!response.success || !response.recipe) {
        throw new Error('Failed to generate recipe via Firebase Function');
      }
      
      // Track API usage for cost and rate limiting
      if (response.usage) {
        await trackApiUsage(
          'gpt-4o',
          response.usage.prompt_tokens,
          response.usage.completion_tokens
        );
      }
      
      // Parse the response from Firebase Function
      try {
        const responseContent = response.recipe;
        
        // Strip markdown code block formatting if present
        let cleanedContent = responseContent;
        if (responseContent.includes('```')) {
          // Remove markdown code blocks (```json and ```)
          cleanedContent = responseContent
            .replace(/```json\n/g, '')
            .replace(/```\n/g, '')
            .replace(/```/g, '');
        }
        
        const parsedJson = JSON.parse(cleanedContent);
        
        if (!parsedJson.alternatives || !Array.isArray(parsedJson.alternatives)) {
           throw new Error('Response does not contain an "alternatives" array.');
        }

        const recipes: Recipe[] = [];
        for (const recipeJson of parsedJson.alternatives) {
          // Validate required fields for each recipe
          if (!recipeJson.name || !recipeJson.ingredients || !recipeJson.instructions || !recipeJson.nutritionalInfo) {
            console.warn('Skipping recipe due to missing required fields:', recipeJson.name || 'Unknown');
            continue;
          }

          // Process and validate the recipe
          const validatedRecipe = validateAndFormatRecipe(recipeJson, request.originalFoodName);
          recipes.push(validatedRecipe);
          
          // Update recovery data with partial results as they're validated
          if (recipes.length > 0) {
            await updateRecipePartialResults(recipes);
          }
        }

        if (recipes.length === 0) {
          throw new Error('No valid recipes found in the response.');
        }
        
        // Cache the generated recipes for future use
        cacheRecipes(
          request.originalFoodName,
          request.nutritionalInfo,
          recipes,
          request.dietaryPreferences,
          request.allergies
        ).catch(err => console.error('Error caching recipes:', err));
        
        // Mark operation as completed
        await completeOperation(OperationType.RECIPE_GENERATION);
        
        return {
          success: true,
          recipes: recipes
        };
      } catch (parseError) {
        console.error('Error parsing response:', parseError);
        console.log('Response content:', response.recipe);
        
        // Record failure in operation tracking
        await failOperation(
          OperationType.RECIPE_GENERATION,
          ErrorType.API_ERROR,
          {
            timestamp: Date.now(),
            originalFoodName: request.originalFoodName,
            nutritionalInfo: request.nutritionalInfo,
            retryCount: 0
          }
        );
        
        // Return fallback recipe
        return {
          success: true,
          recipes: [emergencyFallbackRecipe]
        };
      }
    } catch (error) {
      // Check if this was a timeout error
      const error_ = error as Error;
      if (error_.message.includes('timed out')) {
        console.error('Request timed out');
        
        // Record failure in operation tracking
        await failOperation(
          OperationType.RECIPE_GENERATION,
          ErrorType.TIMEOUT,
          {
            timestamp: Date.now(),
            originalFoodName: request.originalFoodName,
            nutritionalInfo: request.nutritionalInfo,
            retryCount: 0
          }
        );
      } else {
        console.error('Error calling Firebase Function:', error_);
        
        // Record failure in operation tracking
        await failOperation(
          OperationType.RECIPE_GENERATION,
          ErrorType.NETWORK,
          {
            timestamp: Date.now(),
            originalFoodName: request.originalFoodName,
            nutritionalInfo: request.nutritionalInfo,
            retryCount: 0
          }
        );
      }
      
      // Return fallback recipe
      return {
        success: true,
        recipes: [emergencyFallbackRecipe]
      };
    }
  } catch (error) {
    console.error('Error generating recipe:', error);
    
    // Record failure in operation tracking
    const finalError = error as Error;
    await failOperation(OperationType.RECIPE_GENERATION, ErrorType.UNKNOWN, {
      timestamp: Date.now(),
      originalFoodName: request.originalFoodName,
      nutritionalInfo: request.nutritionalInfo,
      retryCount: 0
    });
    
    // Generate fallback recipe
    return generateFallbackRecipe(request);
  }
}

/**
 * Generates a fallback recipe when the API call fails
 * @param request Original food details
 * @returns An array of fallback recipes
 */
export function generateFallbackRecipe(request: GenerateAlternativeRecipeRequest): GenerateAlternativeRecipeResponse {
  const originalName = request.originalFoodName;
  
  // Create a fallback recipe
  const recipe: Recipe = {
    name: `Healthier ${originalName}`,
    focus: "Balanced Nutrition",
    description: `A lighter version of ${originalName} with improved nutritional profile, focusing on balanced macronutrients and wholesome ingredients.`,
    ingredients: [
      { name: "Main Ingredient", amount: "1 cup", calories: 150, protein: 15, carbs: 10, fat: 5 },
      { name: "Secondary Ingredient", amount: "1/2 cup", calories: 100, protein: 5, carbs: 15, fat: 2 },
      { name: "Vegetables", amount: "1 cup", calories: 50, protein: 2, carbs: 10, fat: 0 },
      { name: "Healthy Fat", amount: "1 tbsp", calories: 120, protein: 0, carbs: 0, fat: 14 },
      { name: "Herbs and Spices", amount: "to taste", calories: 5, protein: 0, carbs: 1, fat: 0 }
    ],
    instructions: [
      "Prepare all ingredients as directed",
      "Combine main and secondary ingredients",
      "Add vegetables and mix well",
      "Season with herbs and spices",
      "Finish with healthy fat and serve"
    ],
    nutritionalInfo: {
      calories: Math.max(200, request.nutritionalInfo.calories * 0.8),
      protein: Math.max(15, request.nutritionalInfo.protein * 1.2),
      carbs: Math.max(20, request.nutritionalInfo.carbs * 0.7),
      fat: Math.max(7, request.nutritionalInfo.fat * 0.7),
      fiber: 5,
      sugar: 3
    },
    preparationTime: "15 minutes",
    cookingTime: "20 minutes",
    healthBenefits: [
      "Lower in calories than the original",
      "Higher protein content for satiety",
      "Added fiber for digestive health",
      "Balanced macronutrients for sustained energy"
    ],
    imagePrompt: `A healthy version of ${originalName} plated beautifully with garnish.`
  };
  
  return {
    success: true,
    recipes: [recipe]
  };
}

/**
 * Determines the food type based on the original name
 * @param originalName The name of the original food
 * @returns The type of food
 */
export function determineFoodType(originalName: string): string {
  const nameLower = originalName.toLowerCase();
  
  if (nameLower.includes('bowl')) return 'Power Bowl';
  if (nameLower.includes('salad')) return 'Super Salad';
  if (nameLower.includes('sandwich') || nameLower.includes('burger') || nameLower.includes('wrap')) return 'Sandwich';
  if (nameLower.includes('pasta') || nameLower.includes('noodle')) return 'Pasta';
  if (nameLower.includes('soup') || nameLower.includes('stew')) return 'Soup';
  if (nameLower.includes('breakfast') || nameLower.includes('oatmeal') || nameLower.includes('cereal')) return 'Breakfast';
  if (nameLower.includes('smoothie') || nameLower.includes('shake')) return 'Smoothie';
  if (nameLower.includes('pizza')) return 'Flatbread';
  
  // Default type is "Meal"
  return 'Meal';
} 