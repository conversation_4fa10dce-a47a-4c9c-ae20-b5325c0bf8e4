// Recipe-related types and interfaces

export interface GenerateAlternativeRecipeRequest {
  originalFoodName: string;
  nutritionalInfo: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    [key: string]: any;
  };
  dietaryPreferences?: string[];
  allergies?: string[];
  // Add support for original ingredients detected from the scan
  originalIngredients?: {
    name: string;
    estimatedAmount?: string;
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
    ingredients?: string[];
  }[];
}

// Define a type for a single recipe structure
export interface Recipe {
  name: string;
  description: string;
  ingredients: {
    name: string;
    amount: string;
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
  }[];
  instructions: string[];
  nutritionalInfo: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    [key: string]: any; // Allow for fiber, sugar etc.
  };
  preparationTime: string;
  cookingTime: string;
  healthBenefits: string[];
  imagePrompt?: string;
  focus?: string; // Added field for recipe focus/theme
}

// Update the response interface to contain an array of recipes
export interface GenerateAlternativeRecipeResponse {
  success: boolean;
  recipes?: Recipe[]; // Changed from 'recipe' to 'recipes' and made it an array
  error?: string;
  recovered?: boolean; // Whether this response was recovered from a previous failed attempt
  fromCache?: boolean; // Whether this response was served from the cache
  isEmergencyFallback?: boolean;
}

/**
 * Recipe database for common dishes to improve recognition accuracy
 */
export const RECIPE_DATABASE: {
  [key: string]: {
    name: string;
    ingredients: string[];
    description: string;
  }
} = {
  // Add known recipes with their ingredients and descriptions
  "goulash": {
    name: "Goulash",
    ingredients: [
      "olive oil", "yellow onion", "garlic", "ground beef", "salt", "pepper", 
      "paprika", "Italian seasoning", "tomato paste", "diced tomatoes", 
      "tomato sauce", "Worcestershire sauce", "beef broth", "pasta", 
      "cheddar cheese", "parsley"
    ],
    description: "Goulash is a hearty dish featuring ground beef, pasta, and a rich tomato sauce seasoned with paprika. This comfort food combines meat, vegetables, and pasta in a savory one-pot meal that's perfect for family dinners."
  },
  // Add variants of goulash that might be detected differently
  "american goulash": {
    name: "American Goulash",
    ingredients: [
      "olive oil", "onion", "garlic", "ground beef", "salt", "pepper", 
      "paprika", "Italian seasoning", "tomato paste", "diced tomatoes", 
      "tomato sauce", "Worcestershire sauce", "beef broth", "elbow macaroni", 
      "cheddar cheese", "parsley"
    ],
    description: "American Goulash is a comforting one-pot meal consisting of ground beef, macaroni pasta, and a rich tomato sauce seasoned with paprika and Italian herbs. This hearty dish is topped with melted cheddar cheese and fresh parsley."
  },
  "beef pasta": {
    name: "Beef and Pasta Goulash",
    ingredients: [
      "ground beef", "pasta", "tomato sauce", "diced tomatoes", "onion", 
      "garlic", "paprika", "Italian seasoning", "beef broth", "cheddar cheese"
    ],
    description: "A hearty one-pot meal featuring seasoned ground beef, pasta, and a rich tomato sauce infused with paprika and herbs. This classic comfort food is perfect for family dinners."
  },
  "beef stroganoff": {
    name: "Beef Stroganoff",
    ingredients: [
      "beef strips", "mushrooms", "onion", "garlic", "butter", "flour", 
      "beef broth", "sour cream", "Worcestershire sauce", "dijon mustard", 
      "egg noodles", "parsley"
    ],
    description: "Beef Stroganoff is a classic dish with tender beef strips and mushrooms in a creamy sour cream sauce, typically served over egg noodles."
  },
  "chicken_rice": {
    name: "Chicken and Rice",
    ingredients: [
      "chicken", "rice", "vegetables", "onion", "garlic", 
      "salt", "pepper", "olive oil", "herbs"
    ],
    description: "A simple and nutritious dish featuring tender chicken pieces with fluffy rice and mixed vegetables. This versatile and comforting meal is popular across many cultures for its balanced flavors and satisfying combination of protein and carbohydrates."
  }
}; 