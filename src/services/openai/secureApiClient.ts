/**
 * Secure API Client for making calls to Firebase Cloud Functions
 * Replaces direct API calls with secure proxy calls
 */

import { getFunctions, httpsCallable, HttpsCallableResult } from 'firebase/functions';
import { functions } from '@/lib/firebase';

// Initialize functions (use the import from your existing firebase setup)
const firebaseFunctions = getFunctions();

// Type definitions for function responses
interface AnalyzeFoodResponse {
  success: boolean;
  analysis: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface DetectLabelsResponse {
  success: boolean;
  labels: { description: string; score: number }[];
  text: { description: string }[];
}

interface NutritionDataResponse {
  success: boolean;
  foods: {
    food_name: string;
    nf_calories: number;
    nf_protein: number;
    nf_total_carbohydrate: number;
    nf_total_fat: number;
  }[];
}

// Cloud function wrappers
export async function analyzeFood(imageUrl: string, prompt: string): Promise<AnalyzeFoodResponse> {
  try {
    const analyzeFoodFunc = httpsCallable<{ imageUrl: string; prompt: string }, AnalyzeFoodResponse>(
      firebaseFunctions, 
      'analyzeFood'
    );
    
    const result = await analyzeFoodFunc({ imageUrl, prompt });
    return result.data;
  } catch (error) {
    console.error('Error calling analyzeFood:', error);
    throw new Error('Failed to analyze food image');
  }
}

export async function detectLabels(imageUrl: string): Promise<DetectLabelsResponse> {
  try {
    const detectLabelsFunc = httpsCallable<{ imageUrl: string }, DetectLabelsResponse>(
      firebaseFunctions, 
      'detectLabels'
    );
    
    const result = await detectLabelsFunc({ imageUrl });
    return result.data;
  } catch (error) {
    console.error('Error calling detectLabels:', error);
    throw new Error('Failed to detect image labels');
  }
}

export async function getNutritionData(query: string): Promise<NutritionDataResponse> {
  try {
    const getNutritionDataFunc = httpsCallable<{ query: string }, NutritionDataResponse>(
      firebaseFunctions, 
      'getNutritionData'
    );
    
    const result = await getNutritionDataFunc({ query });
    return result.data;
  } catch (error) {
    console.error('Error calling getNutritionData:', error);
    throw new Error('Failed to get nutrition data');
  }
}

// Error handling utility
export function isFirebaseFunctionError(error: any): boolean {
  return error.code && error.code.startsWith('functions/');
}

export function getErrorMessage(error: any): string {
  if (isFirebaseFunctionError(error)) {
    switch (error.code) {
      case 'functions/unauthenticated':
        return 'Please sign in to use this feature';
      case 'functions/permission-denied':
        return 'You do not have permission to perform this action';
      case 'functions/resource-exhausted':
        return 'Too many requests. Please try again later.';
      case 'functions/invalid-argument':
        return 'Invalid request. Please check your input.';
      default:
        return error.message || 'An error occurred. Please try again.';
    }
  }
  return error.message || 'An unexpected error occurred';
}

// Recipe generation functions
interface GenerateRecipeResponse {
  success: boolean;
  recipe: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export async function generateRecipe(
  ingredients: string[],
  preferences: string,
  mealType?: string,
  cuisineType?: string,
  difficulty?: string
): Promise<GenerateRecipeResponse> {
  try {
    const generateRecipeFunc = httpsCallable<any, GenerateRecipeResponse>(
      firebaseFunctions,
      'generateRecipe'
    );
    
    const result = await generateRecipeFunc({
      ingredients,
      preferences,
      mealType,
      cuisineType,
      difficulty
    });
    return result.data;
  } catch (error) {
    console.error('Error calling generateRecipe:', error);
    throw new Error('Failed to generate recipe');
  }
}

export async function generateHealthyAlternative(
  recipe: string,
  healthGoals: string
): Promise<GenerateRecipeResponse> {
  try {
    const generateHealthyAlternativeFunc = httpsCallable<any, GenerateRecipeResponse>(
      firebaseFunctions,
      'generateHealthyAlternative'
    );
    
    const result = await generateHealthyAlternativeFunc({
      recipe,
      healthGoals
    });
    return result.data;
  } catch (error) {
    console.error('Error calling generateHealthyAlternative:', error);
    throw new Error('Failed to generate healthy alternative');
  }
}

// Image generation functions
interface GenerateImageResponse {
  success: boolean;
  imageUrl: string;
  revised_prompt?: string;
}

export async function generateRecipeImage(
  recipeName: string,
  description: string,
  style?: string
): Promise<GenerateImageResponse> {
  try {
    const generateRecipeImageFunc = httpsCallable<any, GenerateImageResponse>(
      firebaseFunctions,
      'generateRecipeImage'
    );
    
    const result = await generateRecipeImageFunc({
      recipeName,
      description,
      style
    });
    return result.data;
  } catch (error) {
    console.error('Error calling generateRecipeImage:', error);
    throw new Error('Failed to generate recipe image');
  }
}

interface SegmentVisualizationResponse {
  success: boolean;
  visualizationUrl: string;
  segments: {
    name: string;
    percentage: number;
    color: string;
  }[];
}

export async function generateSegmentVisualization(
  segments: { name: string; percentage: number }[],
  baseImageUrl: string
): Promise<SegmentVisualizationResponse> {
  try {
    const generateSegmentVisualizationFunc = httpsCallable<any, SegmentVisualizationResponse>(
      firebaseFunctions,
      'generateSegmentVisualization'  
    );
    
    const result = await generateSegmentVisualizationFunc({
      segments,
      baseImageUrl
    });
    return result.data;
  } catch (error) {
    console.error('Error calling generateSegmentVisualization:', error);
    throw new Error('Failed to generate segment visualization');
  }
}

// Dietitian service functions
interface NutritionRecommendationsResponse {
  success: boolean;
  recommendations: string[];
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export async function generateNutritionRecommendations(
  nutrition: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber?: number;
    sugar?: number;
    sodium?: number;
  },
  calorieGoal: number,
  proteinGoal: number,
  carbsGoal: number,
  fatGoal: number,
  goalType: string
): Promise<NutritionRecommendationsResponse> {
  try {
    const func = httpsCallable<any, NutritionRecommendationsResponse>(
      firebaseFunctions,
      'generateNutritionRecommendations'
    );
    
    const result = await func({
      nutrition,
      calorieGoal,
      proteinGoal,
      carbsGoal,
      fatGoal,
      goalType
    });
    return result.data;
  } catch (error) {
    console.error('Error calling generateNutritionRecommendations:', error);
    throw new Error('Failed to generate nutrition recommendations');
  }
}

interface MealPlanResponse {
  success: boolean;
  mealPlan: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export async function generateMealPlan(
  preferences: string,
  nutritionGoals: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  },
  mealTypes: string[],
  days: number
): Promise<MealPlanResponse> {
  try {
    const func = httpsCallable<any, MealPlanResponse>(
      firebaseFunctions,
      'generateMealPlan'
    );
    
    const result = await func({
      preferences,
      nutritionGoals,
      mealTypes,
      days
    });
    return result.data;
  } catch (error) {
    console.error('Error calling generateMealPlan:', error);
    throw new Error('Failed to generate meal plan');
  }
}

interface HealthInsightsResponse {
  success: boolean;
  insights: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export async function generateHealthInsights(
  weeklyNutrition: {
    avgCalories: number;
    avgProtein: number;
    avgCarbs: number;
    avgFat: number;
  },
  userProfile: {
    age: number;
    gender: string;
    goals: string;
  },
  activityLevel: string
): Promise<HealthInsightsResponse> {
  try {
    const func = httpsCallable<any, HealthInsightsResponse>(
      firebaseFunctions,
      'generateHealthInsights'
    );
    
    const result = await func({
      weeklyNutrition,
      userProfile,
      activityLevel
    });
    return result.data;
  } catch (error) {
    console.error('Error calling generateHealthInsights:', error);
    throw new Error('Failed to generate health insights');
  }
}