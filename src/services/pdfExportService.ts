import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { Meal } from '@/types/food';
import { Platform } from 'react-native';
import { getUserProfile } from './nutritionGoalService';
import * as Print from 'expo-print';
import { manipulateAsync } from 'expo-image-manipulator';
import { getAuth } from 'firebase/auth';
import { collection, addDoc, getFirestore } from 'firebase/firestore';
import { app } from '@/lib/firebase';


// Initialize Firebase services
const db = getFirestore(app);
const auth = getAuth(app);

// Define the interface for the export options
interface NutritionExportOptions {
  startDate: Date;
  endDate: Date;
  includeRecipes?: boolean;
  includeMealBreakdown?: boolean;
  includeGoalComparison?: boolean;
  title?: string;
}

/**
 * Generate and export a nutrition summary PDF
 * @param meals The meals to include in the summary
 * @param options Export options
 * @returns The file URI of the generated PDF
 */
export async function exportNutritionSummary(
  meals: Meal[],
  options: NutritionExportOptions
): Promise<string | null> {
  try {
    // Filter meals based on date range
    const filteredMeals = filterMealsByDateRange(meals, options.startDate, options.endDate);
    
    if (filteredMeals.length === 0) {
      throw new Error('No meals found in the specified date range');
    }
    
    // Generate HTML content for the PDF
    const htmlContent = await generateNutritionSummaryHtml(filteredMeals, options);
    
    // Generate the PDF file using expo-print
    const { uri: pdfUri } = await Print.printToFileAsync({
      html: htmlContent,
      width: 612, // Letter size width in points (8.5 inches)
      height: 792, // Letter size height in points (11 inches)
      base64: false
    });
    
    // Generate the PDF file path with formatted date
    const formattedStartDate = formatDateForFilename(options.startDate);
    const formattedEndDate = formatDateForFilename(options.endDate);
    const pdfFilename = `NutritionSummary_${formattedStartDate}_${formattedEndDate}.pdf`;
    
    // Copy the generated PDF to a permanent location
    const permanentPath = `${FileSystem.documentDirectory}${pdfFilename}`;
    await FileSystem.copyAsync({
      from: pdfUri,
      to: permanentPath
    });
    
    // Clean up the temporary file
    await FileSystem.deleteAsync(pdfUri, { idempotent: true });
    
    // Log the export to analytics (optional)
    try {
      const user = auth.currentUser;
      if (user) {
        await addDoc(collection(db, 'export_logs'), {
          user_id: user.uid,
          export_type: 'nutrition_summary',
          date_range_start: options.startDate.toISOString(),
          date_range_end: options.endDate.toISOString(),
          options: {
            includeRecipes: options.includeRecipes,
            includeMealBreakdown: options.includeMealBreakdown,
            includeGoalComparison: options.includeGoalComparison
          },
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      console.warn('Failed to log export analytics:', error);
      // Non-critical error, continue
    }
    
    return permanentPath;
  } catch (error) {
    console.error('Error exporting nutrition summary:', error);
    return null;
  }
}

/**
 * Share the generated PDF file
 * @param filePath The path to the PDF file
 */
export async function shareNutritionSummary(filePath: string): Promise<void> {
  try {
    if (!(await Sharing.isAvailableAsync())) {
      throw new Error('Sharing is not available on this device');
    }
    
    await Sharing.shareAsync(filePath, {
      mimeType: 'application/pdf',
      dialogTitle: 'Share Nutrition Summary',
      UTI: 'com.adobe.pdf' // For iOS
    });
  } catch (error) {
    console.error('Error sharing nutrition summary:', error);
    throw error;
  }
}

/**
 * Filter meals by date range
 * @param meals The meals to filter
 * @param startDate The start date
 * @param endDate The end date
 * @returns The filtered meals
 */
function filterMealsByDateRange(meals: Meal[], startDate: Date, endDate: Date): Meal[] {
  return meals.filter(meal => {
    const mealDate = new Date(meal.date);
    return mealDate >= startDate && mealDate <= endDate;
  });
}

/**
 * Format a date for use in a filename
 * @param date The date to format
 * @returns The formatted date string
 */
function formatDateForFilename(date: Date): string {
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
}

/**
 * Format a date for display
 * @param date The date to format
 * @returns The formatted date string
 */
function formatDateForDisplay(date: Date): string {
  return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
}

/**
 * Format a date string for display
 * @param dateStr The date string to format
 * @returns The formatted date string
 */
function formatDateStringForDisplay(dateStr: string): string {
  const date = new Date(dateStr);
  return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
}

/**
 * Generate a chart for the PDF
 * @param caloriesByDate Array of date/calorie pairs
 * @param startDate Start date of the report
 * @param endDate End date of the report
 * @returns Base64 encoded image of the chart
 */
async function generateCalorieChart(
  caloriesByDate: {date: string, calories: number}[], 
  startDate: Date, 
  endDate: Date
): Promise<string> {
  // In a real implementation, this would use a charting library or API
  // For this example we're returning a placeholder, but in production
  // you would use Chart.js, D3.js, or a chart API service
  
  // Create a simple HTML5 canvas chart
  const chartHtml = `
    <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
      </head>
      <body style="margin: 0; padding: 0;">
        <canvas id="calorieChart" width="600" height="300"></canvas>
        <script>
          const ctx = document.getElementById('calorieChart').getContext('2d');
          new Chart(ctx, {
            type: 'line',
            data: {
              labels: ${JSON.stringify(caloriesByDate.map(d => d.date))},
              datasets: [{
                label: 'Calories',
                data: ${JSON.stringify(caloriesByDate.map(d => d.calories))},
                borderColor: '#4a90e2',
                backgroundColor: 'rgba(74, 144, 226, 0.1)',
                borderWidth: 2,
                tension: 0.1,
                fill: true
              }]
            },
            options: {
              responsive: true,
              plugins: {
                title: {
                  display: true,
                  text: 'Daily Calorie Intake'
                },
                legend: {
                  display: false
                }
              },
              scales: {
                y: {
                  min: 0,
                  title: {
                    display: true,
                    text: 'Calories'
                  }
                },
                x: {
                  title: {
                    display: true,
                    text: 'Date'
                  }
                }
              }
            }
          });
        </script>
      </body>
    </html>
  `;
  
  try {
    // In a web environment, we could render this directly
    if (Platform.OS === 'web') {
      return chartHtml;
    }
    
    // For mobile platforms, we need to render the chart to an image
    // This would be implemented using a webview or chart library
    // For this example, we're returning a base64 stub
    // In production, you would use react-native-webview to render HTML to an image
    
    // Simulate generating a chart image
    return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAFeAJ5jHz5jwAAAABJRU5ErkJggg==';
  } catch (error) {
    console.error('Error generating chart:', error);
    return '';
  }
}

/**
 * Generate HTML content for the nutrition summary
 * @param meals The meals to include in the summary
 * @param options Export options
 * @returns The HTML content
 */
async function generateNutritionSummaryHtml(
  meals: Meal[],
  options: NutritionExportOptions
): Promise<string> {
  // Get user profile for goal comparison if needed
  const userProfile = options.includeGoalComparison ? await getUserProfile() : null;
  
  // Calculate daily averages
  const totalDays = Math.ceil((options.endDate.getTime() - options.startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
  
  const totalCalories = meals.reduce((sum, meal) => sum + meal.totalCalories, 0);
  const totalProtein = meals.reduce((sum, meal) => sum + meal.totalProtein, 0);
  const totalCarbs = meals.reduce((sum, meal) => sum + meal.totalCarbs, 0);
  const totalFat = meals.reduce((sum, meal) => sum + meal.totalFat, 0);
  
  const avgCalories = Math.round(totalCalories / totalDays);
  const avgProtein = Math.round(totalProtein / totalDays);
  const avgCarbs = Math.round(totalCarbs / totalDays);
  const avgFat = Math.round(totalFat / totalDays);
  
  // Group meals by date for daily breakdown
  const mealsByDate: { [date: string]: Meal[] } = {};
  
  meals.forEach(meal => {
    const dateStr = meal.date;
    if (!mealsByDate[dateStr]) {
      mealsByDate[dateStr] = [];
    }
    mealsByDate[dateStr].push(meal);
  });
  
  // Create date entries for the entire range
  const allDates: string[] = [];
  const currentDate = new Date(options.startDate);
  while (currentDate <= options.endDate) {
    const dateStr = formatDateForFilename(currentDate);
    allDates.push(dateStr);
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  // Calculate calorie trend data for the chart
  const caloriesByDate = allDates.map(date => {
    const mealsForDate = mealsByDate[date] || [];
    return {
      date,
      calories: mealsForDate.reduce((sum, meal) => sum + meal.totalCalories, 0)
    };
  });
  
  // Generate chart for the PDF
  const chartBase64 = await generateCalorieChart(caloriesByDate, options.startDate, options.endDate);
  const chartHtml = chartBase64 ? 
    `<div class="chart-container">
      <img src="${chartBase64}" style="width: 100%; max-width: 600px;" />
    </div>` :
    '';
  
  // Build the HTML content
  const title = options.title || 'Nutrition Summary';
  const formattedStartDate = formatDateForDisplay(options.startDate);
  const formattedEndDate = formatDateForDisplay(options.endDate);
  
  let html = `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        <style>
          body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
          }
          h1 {
            color: #2c3e50;
            margin-bottom: 10px;
          }
          .date-range {
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 20px;
          }
          .summary-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .summary-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
          }
          .nutrient-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 20px;
          }
          .nutrient-box {
            width: 22%;
            background-color: white;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            text-align: center;
          }
          .nutrient-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #3498db;
          }
          .nutrient-label {
            font-size: 14px;
            color: #7f8c8d;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          th, td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
          }
          th {
            background-color: #f2f2f2;
            font-weight: bold;
          }
          tr:nth-child(even) {
            background-color: #f9f9f9;
          }
          .goal-comparison {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
          }
          .goal-item {
            width: 48%;
            margin-bottom: 15px;
            padding: 10px;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          }
          .goal-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 5px;
          }
          .goal-values {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
          }
          .actual-value {
            font-size: 18px;
            font-weight: bold;
            color: #3498db;
          }
          .target-value {
            font-size: 14px;
            color: #7f8c8d;
          }
          .progress-container {
            height: 8px;
            background-color: #ecf0f1;
            border-radius: 4px;
            margin-top: 5px;
            overflow: hidden;
          }
          .progress-bar {
            height: 100%;
            background-color: #2ecc71;
          }
          .chart-container {
            margin-top: 20px;
            height: 200px;
            text-align: center;
          }
          .meal-list {
            margin-top: 10px;
          }
          .meal-item {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
          }
          .meal-name {
            font-weight: bold;
          }
          .meal-time {
            color: #7f8c8d;
            font-size: 14px;
          }
          .meal-nutrients {
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 5px;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #7f8c8d;
          }
          @media print {
            body {
              padding: 0;
              margin: 0;
            }
            .summary-card {
              page-break-inside: avoid;
            }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${title}</h1>
          <div class="date-range">${formattedStartDate} - ${formattedEndDate}</div>
        </div>
        
        <div class="summary-card">
          <div class="summary-title">Daily Averages</div>
          <div class="nutrient-grid">
            <div class="nutrient-box">
              <div class="nutrient-value">${avgCalories}</div>
              <div class="nutrient-label">Calories</div>
            </div>
            <div class="nutrient-box">
              <div class="nutrient-value">${avgProtein}g</div>
              <div class="nutrient-label">Protein</div>
            </div>
            <div class="nutrient-box">
              <div class="nutrient-value">${avgCarbs}g</div>
              <div class="nutrient-label">Carbs</div>
            </div>
            <div class="nutrient-box">
              <div class="nutrient-value">${avgFat}g</div>
              <div class="nutrient-label">Fat</div>
            </div>
          </div>
          
          <div class="summary-title">Macronutrient Ratio</div>
          <div style="display: flex; height: 30px; margin-bottom: 15px;">
            <div style="background-color: #3498db; width: ${Math.round(avgProtein * 4 / avgCalories * 100)}%; height: 100%;"></div>
            <div style="background-color: #2ecc71; width: ${Math.round(avgCarbs * 4 / avgCalories * 100)}%; height: 100%;"></div>
            <div style="background-color: #f1c40f; width: ${Math.round(avgFat * 9 / avgCalories * 100)}%; height: 100%;"></div>
          </div>
          <div style="display: flex; justify-content: space-around; font-size: 14px; color: #7f8c8d;">
            <div>${Math.round(avgProtein * 4 / avgCalories * 100)}% Protein</div>
            <div>${Math.round(avgCarbs * 4 / avgCalories * 100)}% Carbs</div>
            <div>${Math.round(avgFat * 9 / avgCalories * 100)}% Fat</div>
          </div>
          
          ${chartHtml}
        </div>`;
  
  // Add goal comparison if enabled
  if (options.includeGoalComparison && userProfile) {
    const caloriePercentage = Math.min(Math.round((avgCalories / userProfile.calorieGoal) * 100), 100);
    const proteinPercentage = Math.min(Math.round((avgProtein / userProfile.proteinGoal) * 100), 100);
    const carbsPercentage = userProfile.carbsGoal ? Math.min(Math.round((avgCarbs / userProfile.carbsGoal) * 100), 100) : 0;
    const fatPercentage = userProfile.fatGoal ? Math.min(Math.round((avgFat / userProfile.fatGoal) * 100), 100) : 0;
    
    html += `
      <div class="summary-card">
        <div class="summary-title">Goal Progress</div>
        <div class="goal-comparison">
          <div class="goal-item">
            <div class="goal-label">Daily Calories</div>
            <div class="goal-values">
              <div class="actual-value">${avgCalories}</div>
              <div class="target-value">Target: ${userProfile.calorieGoal}</div>
            </div>
            <div class="progress-container">
              <div class="progress-bar" style="width: ${caloriePercentage}%;"></div>
            </div>
          </div>
          
          <div class="goal-item">
            <div class="goal-label">Daily Protein</div>
            <div class="goal-values">
              <div class="actual-value">${avgProtein}g</div>
              <div class="target-value">Target: ${userProfile.proteinGoal}g</div>
            </div>
            <div class="progress-container">
              <div class="progress-bar" style="width: ${proteinPercentage}%;"></div>
            </div>
          </div>
          
          ${userProfile.carbsGoal ? `
          <div class="goal-item">
            <div class="goal-label">Daily Carbs</div>
            <div class="goal-values">
              <div class="actual-value">${avgCarbs}g</div>
              <div class="target-value">Target: ${userProfile.carbsGoal}g</div>
            </div>
            <div class="progress-container">
              <div class="progress-bar" style="width: ${carbsPercentage}%;"></div>
            </div>
          </div>
          ` : ''}
          
          ${userProfile.fatGoal ? `
          <div class="goal-item">
            <div class="goal-label">Daily Fat</div>
            <div class="goal-values">
              <div class="actual-value">${avgFat}g</div>
              <div class="target-value">Target: ${userProfile.fatGoal}g</div>
            </div>
            <div class="progress-container">
              <div class="progress-bar" style="width: ${fatPercentage}%;"></div>
            </div>
          </div>
          ` : ''}
        </div>
      </div>`;
  }
  
  // Add daily breakdown if enabled
  if (options.includeMealBreakdown) {
    html += `
      <div class="summary-card">
        <div class="summary-title">Daily Breakdown</div>
        <table>
          <thead>
            <tr>
              <th>Date</th>
              <th>Calories</th>
              <th>Protein (g)</th>
              <th>Carbs (g)</th>
              <th>Fat (g)</th>
              <th>Meals</th>
            </tr>
          </thead>
          <tbody>`;
    
    // Add a row for each date in the range
    for (const dateStr of allDates) {
      const mealsForDate = mealsByDate[dateStr] || [];
      const dayCalories = mealsForDate.reduce((sum, meal) => sum + meal.totalCalories, 0);
      const dayProtein = mealsForDate.reduce((sum, meal) => sum + meal.totalProtein, 0);
      const dayCarbs = mealsForDate.reduce((sum, meal) => sum + meal.totalCarbs, 0);
      const dayFat = mealsForDate.reduce((sum, meal) => sum + meal.totalFat, 0);
      
      // Format date for display
      const displayDate = formatDateStringForDisplay(dateStr);
      
      html += `
        <tr>
          <td>${displayDate}</td>
          <td>${dayCalories}</td>
          <td>${dayProtein}</td>
          <td>${dayCarbs}</td>
          <td>${dayFat}</td>
          <td>
            <div class="meal-list">`;
      
      // Add a list of meals for the day
      if (mealsForDate.length > 0) {
        mealsForDate.forEach(meal => {
          html += `
            <div class="meal-item">
              <div class="meal-name">${meal.name}</div>
              <div class="meal-time">${meal.time}</div>
              <div class="meal-nutrients">
                Calories: ${meal.totalCalories} | Protein: ${meal.totalProtein}g | Carbs: ${meal.totalCarbs}g | Fat: ${meal.totalFat}g
              </div>
            </div>`;
        });
      } else {
        html += `<div style="color: #7f8c8d;">No meals recorded</div>`;
      }
      
      html += `
            </div>
          </td>
        </tr>`;
    }
    
    html += `
          </tbody>
        </table>
      </div>`;
  }
  
  // Add recipe details if enabled
  if (options.includeRecipes) {
    const recipeMeals = meals.filter(meal => meal.items.length > 1); // Assuming meals with multiple items are recipes
    
    if (recipeMeals.length > 0) {
      html += `
        <div class="summary-card">
          <div class="summary-title">Recipe Details</div>`;
      
      recipeMeals.forEach(meal => {
        html += `
          <div style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee;">
            <h3 style="margin-bottom: 5px;">${meal.name}</h3>
            <div style="color: #7f8c8d; margin-bottom: 10px;">
              ${formatDateStringForDisplay(meal.date)} | ${meal.time}
            </div>
            <div style="font-weight: bold; margin-bottom: 8px;">Ingredients:</div>
            <ul>`;
        
        meal.items.forEach(item => {
          html += `<li>${item.name} (${item.calories} cal)</li>`;
        });
        
        html += `
            </ul>
            <div style="margin-top: 10px;">
              <div style="font-weight: bold; margin-bottom: 5px;">Nutrition:</div>
              <div>Calories: ${meal.totalCalories} | Protein: ${meal.totalProtein}g | Carbs: ${meal.totalCarbs}g | Fat: ${meal.totalFat}g</div>
            </div>
          </div>`;
      });
      
      html += `</div>`;
    }
  }
  
  // Add footer
  html += `
        <div class="footer">
          <p>Generated on ${new Date().toLocaleDateString()} | Health App Nutrition Report</p>
        </div>
      </body>
    </html>`;
  
  return html;
} 