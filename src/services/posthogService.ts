import AsyncStorage from '@react-native-async-storage/async-storage';
import PostHog from 'posthog-react-native';
import { Platform } from 'react-native';
import Constants from 'expo-constants';

// PostHog configuration
const POSTHOG_API_KEY = process.env.EXPO_PUBLIC_POSTHOG_API_KEY || '';
const POSTHOG_HOST = process.env.EXPO_PUBLIC_POSTHOG_HOST || 'https://us.i.posthog.com';

class PostHogService {
  private static instance: PostHogService;
  private posthog: PostHog | null = null;
  private initialized = false;
  
  private constructor() {}
  
  static getInstance(): PostHogService {
    if (!PostHogService.instance) {
      PostHogService.instance = new PostHogService();
    }
    return PostHogService.instance;
  }
  
  async initialize(): Promise<void> {
    if (this.initialized || !POSTHOG_API_KEY) {
      return;
    }
    
    try {
      this.posthog = new PostHog(POSTHOG_API_KEY, {
        host: POSTHOG_HOST,
        enable: true,
        persistence: 'async_storage',
        androidAsyncStorage: AsyncStorage,
        iosAsyncStorage: AsyncStorage,
        // Capture device info
        captureApplicationLifecycleEvents: true,
        // Privacy: Don't capture location data
        captureDeviceId: true,
      });
      
      // Wait for initialization
      await this.posthog.setupAsync();
      
      // Set super properties that will be sent with every event
      this.posthog.register({
        platform: Platform.OS,
        version: Constants.expoVersion || 'unknown',
        environment: __DEV__ ? 'development' : 'production',
      });
      
      this.initialized = true;
      console.log('PostHog initialized successfully');
    } catch (error) {
      console.error('Failed to initialize PostHog:', error);
    }
  }
  
  // Feature flags
  async getFeatureFlag(key: string): Promise<boolean> {
    if (!this.posthog || !this.initialized) {
      return false;
    }
    
    try {
      const enabled = await this.posthog.isFeatureEnabled(key);
      return enabled || false;
    } catch (error) {
      console.error('Error getting feature flag:', error);
      return false;
    }
  }
  
  async getFeatureFlagPayload(key: string): Promise<any> {
    if (!this.posthog || !this.initialized) {
      return null;
    }
    
    try {
      return await this.posthog.getFeatureFlagPayload(key);
    } catch (error) {
      console.error('Error getting feature flag payload:', error);
      return null;
    }
  }
  
  // Analytics events
  trackEvent(event: string, properties?: Record<string, any>): void {
    if (!this.posthog || !this.initialized) {
      return;
    }
    
    try {
      this.posthog.capture(event, properties);
    } catch (error) {
      console.error('Error tracking event:', error);
    }
  }
  
  // Screen tracking
  trackScreen(screenName: string, properties?: Record<string, any>): void {
    if (!this.posthog || !this.initialized) {
      return;
    }
    
    try {
      this.posthog.screen(screenName, properties);
    } catch (error) {
      console.error('Error tracking screen:', error);
    }
  }
  
  // User identification
  identify(userId: string, properties?: Record<string, any>): void {
    if (!this.posthog || !this.initialized) {
      return;
    }
    
    try {
      this.posthog.identify(userId, properties);
    } catch (error) {
      console.error('Error identifying user:', error);
    }
  }
  
  // Clear user identity (on logout)
  reset(): void {
    if (!this.posthog || !this.initialized) {
      return;
    }
    
    try {
      this.posthog.reset();
    } catch (error) {
      console.error('Error resetting PostHog:', error);
    }
  }
  
  // Track errors
  trackError(error: Error, context?: Record<string, any>): void {
    if (!this.posthog || !this.initialized) {
      return;
    }
    
    try {
      this.posthog.capture('app_error', {
        error_message: error.message,
        error_stack: error.stack,
        error_name: error.name,
        ...context,
      });
    } catch (trackingError) {
      console.error('Error tracking error:', trackingError);
    }
  }
  
  // Session recording control
  pauseSessionRecording(): void {
    if (!this.posthog || !this.initialized) {
      return;
    }
    
    try {
      this.posthog.pauseSessionRecording();
    } catch (error) {
      console.error('Error pausing session recording:', error);
    }
  }
  
  resumeSessionRecording(): void {
    if (!this.posthog || !this.initialized) {
      return;
    }
    
    try {
      this.posthog.resumeSessionRecording();
    } catch (error) {
      console.error('Error resuming session recording:', error);
    }
  }
  
  // Group tracking (for organizations/teams)
  group(groupType: string, groupKey: string, properties?: Record<string, any>): void {
    if (!this.posthog || !this.initialized) {
      return;
    }
    
    try {
      this.posthog.group(groupType, groupKey, properties);
    } catch (error) {
      console.error('Error tracking group:', error);
    }
  }
  
  // Opt user out of tracking
  optOut(): void {
    if (!this.posthog || !this.initialized) {
      return;
    }
    
    try {
      this.posthog.optOut();
    } catch (error) {
      console.error('Error opting out:', error);
    }
  }
  
  // Opt user back in to tracking
  optIn(): void {
    if (!this.posthog || !this.initialized) {
      return;
    }
    
    try {
      this.posthog.optIn();
    } catch (error) {
      console.error('Error opting in:', error);
    }
  }
}

export const posthog = PostHogService.getInstance();

// Export convenience functions for direct use
export const getFeatureFlag = (key: string) => posthog.getFeatureFlag(key);
export const trackEvent = (event: string, properties?: Record<string, any>) => posthog.trackEvent(event, properties);
export const trackScreen = (screenName: string, properties?: Record<string, any>) => posthog.trackScreen(screenName, properties);
export const identify = (userId: string, properties?: Record<string, any>) => posthog.identify(userId, properties);
export const trackError = (error: Error, context?: Record<string, any>) => posthog.trackError(error, context);

export default posthog;