import { doc, getDoc, updateDoc, setDoc, getFirestore } from 'firebase/firestore';
import { httpsCallable, getFunctions } from 'firebase/functions';
import { app } from '@/lib/firebase';

const db = getFirestore(app);
const functions = getFunctions(app);

export interface PrivacySettings {
  shareActivityData: boolean;
  shareHealthData: boolean;
  shareMealData: boolean;
  publicProfile: boolean;
  allowFriendRequests: boolean;
  dataCollection: boolean;
  biometricAuth: boolean;
  appLock: boolean;
  appLockTimeout: number; // minutes
  emergencyContacts: string[];
  user_id: string;
  updated_at: string;
}

export interface LoadPrivacySettingsResult {
  success: boolean;
  settings?: PrivacySettings;
  error?: Error;
}

export interface UpdatePrivacySettingResult {
  success: boolean;
  error?: Error;
}

export interface RequestDataExportResult {
  success: boolean;
  error?: Error;
}

export interface DeleteAllDataResult {
  success: boolean;
  error?: Error;
}

export class PrivacySettingsService {
  private getDefaultSettings(userId: string): PrivacySettings {
    return {
      shareActivityData: false,
      shareHealthData: false,
      shareMealData: false,
      publicProfile: false,
      allowFriendRequests: true,
      dataCollection: true,
      biometricAuth: false,
      appLock: false,
      appLockTimeout: 5, // 5 minutes
      emergencyContacts: [],
      user_id: userId,
      updated_at: new Date().toISOString()
    };
  }

  async loadSettings(userId: string): Promise<LoadPrivacySettingsResult> {
    try {
      const privacySettingsRef = doc(db, 'privacy_settings', userId);
      const docSnap = await getDoc(privacySettingsRef);
      
      if (docSnap.exists()) {
        return {
          success: true,
          settings: docSnap.data() as PrivacySettings
        };
      } else {
        // Create default settings
        const defaultSettings = this.getDefaultSettings(userId);
        
        // Add to Firebase
        await setDoc(privacySettingsRef, defaultSettings);
        
        return {
          success: true,
          settings: defaultSettings
        };
      }
    } catch (error) {
      console.error('Error loading privacy settings:', error);
      return {
        success: false,
        error: error instanceof Error ? error : new Error('Unknown error loading privacy settings')
      };
    }
  }

  async updateSetting(userId: string, key: keyof PrivacySettings, value: any): Promise<UpdatePrivacySettingResult> {
    try {
      const privacySettingsRef = doc(db, 'privacy_settings', userId);
      await updateDoc(privacySettingsRef, { 
        [key]: value,
        updated_at: new Date().toISOString()
      });
      
      return { success: true };
    } catch (error) {
      console.error('Error updating privacy setting:', error);
      return {
        success: false,
        error: error instanceof Error ? error : new Error('Unknown error updating privacy setting')
      };
    }
  }

  async updateAppLockTimeout(userId: string, timeout: number): Promise<UpdatePrivacySettingResult> {
    try {
      const privacySettingsRef = doc(db, 'privacy_settings', userId);
      await updateDoc(privacySettingsRef, { 
        appLockTimeout: timeout,
        updated_at: new Date().toISOString()
      });
      
      return { success: true };
    } catch (error) {
      console.error('Error updating app lock timeout:', error);
      return {
        success: false,
        error: error instanceof Error ? error : new Error('Unknown error updating app lock timeout')
      };
    }
  }

  async resetAllSettings(userId: string): Promise<UpdatePrivacySettingResult> {
    try {
      const defaultSettings = this.getDefaultSettings(userId);
      const privacySettingsRef = doc(db, 'privacy_settings', userId);
      await setDoc(privacySettingsRef, {
        ...defaultSettings,
        updated_at: new Date().toISOString()
      });
      
      return { success: true };
    } catch (error) {
      console.error('Error resetting privacy settings:', error);
      return {
        success: false,
        error: error instanceof Error ? error : new Error('Unknown error resetting privacy settings')
      };
    }
  }

  async requestDataExport(userId: string): Promise<RequestDataExportResult> {
    try {
      const exportData = httpsCallable(functions, 'exportUserData');
      await exportData({ userId });
      
      return { success: true };
    } catch (error) {
      console.error('Error requesting data export:', error);
      return {
        success: false,
        error: error instanceof Error ? error : new Error('Unknown error requesting data export')
      };
    }
  }

  async deleteAllData(userId: string): Promise<DeleteAllDataResult> {
    try {
      const deleteUserData = httpsCallable(functions, 'deleteAllUserData');
      await deleteUserData({ userId });
      
      return { success: true };
    } catch (error) {
      console.error('Error deleting all data:', error);
      return {
        success: false,
        error: error instanceof Error ? error : new Error('Unknown error deleting all data')
      };
    }
  }
}