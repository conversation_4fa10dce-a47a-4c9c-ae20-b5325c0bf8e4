import { getFirestore, doc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { getAuth, updateProfile as updateAuthProfile } from 'firebase/auth';
import { app } from '@/lib/firebase';

const db = getFirestore(app);
const auth = getAuth(app);

export interface ProfileData {
  full_name?: string;
  username?: string;
  bio?: string;
  location?: string;
  birthday?: string;
  preferences?: {
    notifications?: boolean;
    darkMode?: boolean;
    language?: string;
  };
  stats?: {
    height?: number;
    weight?: number;
    activityLevel?: string;
  };
  health?: {
    dietaryRestrictions?: string[];
    allergies?: string[];
    conditions?: string[];
  };
  social?: {
    instagram?: string;
    twitter?: string;
    website?: string;
  };
  avatarUrl?: string;
  updatedAt?: any;
}

export interface UpdateResult {
  success: boolean;
  error?: string;
}

class ProfileEditService {
  /**
   * Get user profile data
   */
  async getProfile(userId: string): Promise<ProfileData | null> {
    try {
      const profileRef = doc(db, 'profiles', userId);
      const profileSnap = await getDoc(profileRef);
      
      if (profileSnap.exists()) {
        return profileSnap.data() as ProfileData;
      }
      
      return null;
    } catch (error: any) {
      console.error('Error fetching profile:', error);
      throw new Error(error.message || 'Failed to fetch profile');
    }
  }

  /**
   * Update user profile data
   */
  async updateProfile(userId: string, data: Partial<ProfileData>): Promise<UpdateResult> {
    try {
      const profileRef = doc(db, 'profiles', userId);
      
      // Add timestamp
      const updateData = {
        ...data,
        updatedAt: serverTimestamp()
      };
      
      await updateDoc(profileRef, updateData);
      
      // Update auth profile if name changed
      if (data.full_name && auth.currentUser) {
        await updateAuthProfile(auth.currentUser, {
          displayName: data.full_name
        });
      }
      
      return { success: true };
    } catch (error: any) {
      console.error('Error updating profile:', error);
      return { 
        success: false, 
        error: error.message || 'Failed to update profile' 
      };
    }
  }

  /**
   * Update specific profile sections
   */
  async updateProfileSection(
    userId: string, 
    section: 'preferences' | 'stats' | 'health' | 'social', 
    data: any
  ): Promise<UpdateResult> {
    try {
      const profileRef = doc(db, 'profiles', userId);
      
      const updateData = {
        [section]: data,
        updatedAt: serverTimestamp()
      };
      
      await updateDoc(profileRef, updateData);
      
      return { success: true };
    } catch (error: any) {
      console.error(`Error updating ${section}:`, error);
      return { 
        success: false, 
        error: error.message || `Failed to update ${section}` 
      };
    }
  }

  /**
   * Validate username availability
   */
  async checkUsernameAvailable(username: string, currentUserId: string): Promise<boolean> {
    try {
      // This would need a username index in Firestore
      // For now, return true (available)
      // TODO: Implement proper username uniqueness check
      return true;
    } catch (error) {
      console.error('Error checking username:', error);
      return false;
    }
  }
}

export const profileEditService = new ProfileEditService();