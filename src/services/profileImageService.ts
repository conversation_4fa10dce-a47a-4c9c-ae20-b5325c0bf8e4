import { getStorage, ref, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import { getFirestore, doc, updateDoc, serverTimestamp, getDoc } from 'firebase/firestore';
import { getAuth, updateProfile } from 'firebase/auth';
import { app } from '@/lib/firebase';

// Initialize Firebase services once
const storage = getStorage(app);
const db = getFirestore(app);
const auth = getAuth(app);

export interface UploadProgress {
  bytesTransferred: number;
  totalBytes: number;
  percentage: number;
}

export interface UploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

class ProfileImageService {
  /**
   * Upload a profile image for the current user
   */
  async uploadProfileImage(
    uri: string,
    userId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      // Create a blob from the URI
      const response = await fetch(uri);
      const blob = await response.blob();
      
      // Create unique filename
      const timestamp = Date.now();
      const filename = `profile_${timestamp}.jpg`;
      const filePath = `avatars/${userId}/${filename}`;
      
      // Create storage reference
      const storageRef = ref(storage, filePath);
      
      // Start upload
      const uploadTask = uploadBytesResumable(storageRef, blob);
      
      // Return a promise that resolves when upload completes
      return new Promise((resolve, reject) => {
        uploadTask.on(
          'state_changed',
          (snapshot) => {
            const progress = {
              bytesTransferred: snapshot.bytesTransferred,
              totalBytes: snapshot.totalBytes,
              percentage: (snapshot.bytesTransferred / snapshot.totalBytes) * 100
            };
            onProgress?.(progress);
          },
          (error) => {
            console.error('Upload error:', error);
            resolve({
              success: false,
              error: error.message || 'Failed to upload image'
            });
          },
          async () => {
            try {
              // Get download URL
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              
              // Update profile in Firestore
              await this.updateProfileImage(userId, downloadURL);
              
              // Update auth profile
              await this.updateAuthProfile(downloadURL);
              
              resolve({
                success: true,
                url: downloadURL
              });
            } catch (error: any) {
              console.error('Post-upload error:', error);
              resolve({
                success: false,
                error: error.message || 'Failed to update profile'
              });
            }
          }
        );
      });
    } catch (error: any) {
      console.error('Upload preparation error:', error);
      return {
        success: false,
        error: error.message || 'Failed to prepare image for upload'
      };
    }
  }
  
  /**
   * Update user's profile image URL in Firestore
   */
  private async updateProfileImage(userId: string, imageUrl: string): Promise<void> {
    const profileRef = doc(db, 'profiles', userId);
    await updateDoc(profileRef, {
      avatarUrl: imageUrl,
      updatedAt: serverTimestamp()
    });
  }
  
  /**
   * Update auth user's photo URL
   */
  private async updateAuthProfile(photoURL: string): Promise<void> {
    const currentUser = auth.currentUser;
    if (currentUser) {
      await updateProfile(currentUser, { photoURL });
    }
  }
  
  /**
   * Get the current user's profile image URL
   */
  async getCurrentProfileImage(userId: string): Promise<string | null> {
    try {
      const profileRef = doc(db, 'profiles', userId);
      const snapshot = await getDoc(profileRef);
      
      if (snapshot.exists()) {
        const data = snapshot.data();
        return data.avatarUrl || null;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching profile image:', error);
      return null;
    }
  }
}

// Export singleton instance
export const profileImageService = new ProfileImageService();