import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import { Platform } from 'react-native';
import { decode } from 'base64-arraybuffer';
import { Profile } from './databaseService';
import { useDatabaseType } from '@/contexts/DatabaseContext';

import { getAuth } from 'firebase/auth';
import { getFirestore, doc, getDoc, updateDoc, setDoc, Timestamp, serverTimestamp, collection, initializeFirestore } from 'firebase/firestore';
import { getApp, getApps, initializeApp } from 'firebase/app';
import firebaseConfig from '@/firebase.config';

// Ensure Firebase app is initialized
const ensureFirebaseApp = () => {
  if (!getApps().length) {
    initializeApp(firebaseConfig);
  }
  return getApp();
};

// Ensure Firestore is initialized and returned
const getFirestoreInstance = () => {
  ensureFirebaseApp();
  return getFirestore();
};

/**
 * Create a user profile if it doesn't exist
 */
export async function createProfile() {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }
    
    const db = getFirestoreInstance();
    const userRef = doc(db, 'users', user.uid);
    const userDoc = await getDoc(userRef);
    
    if (!userDoc.exists()) {
      // Create a default profile for the user
      await setDoc(userRef, {
        id: user.uid,
        email: user.email || '',
        full_name: user.displayName || '',
        weight: 0,
        height: 0,
        dailyCalorieGoal: 2000,
        dailyWaterGoal: 2000,
        activity_level: 'moderate',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
      
      console.log('Profile created successfully');
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error creating profile:', error);
    return { success: false, error: 'Failed to create profile' };
  }
}

/**
 * Fetch a user's health statistics
 */
export async function fetchHealthStats() {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }
    
    const db = getFirestoreInstance();
    const userDoc = await getDoc(doc(db, 'users', user.uid));
    
    if (!userDoc.exists()) {
      // Create profile if it doesn't exist
      const profileResult = await createProfile();
      if (!profileResult.success) {
        return { success: false, error: 'User profile not found and could not be created' };
      }
      
      // Get the newly created profile
      const newUserDoc = await getDoc(doc(db, 'users', user.uid));
      if (!newUserDoc.exists()) {
        return { success: false, error: 'Failed to retrieve newly created profile' };
      }
      
      const userData = newUserDoc.data();
      return { 
        success: true, 
        data: {
          weight: userData.weight || 0,
          height: userData.height || 0,
          calorieGoal: userData.dailyCalorieGoal || 2000,
          waterGoal: userData.dailyWaterGoal || 2000,
          consumedCalories: 0,
          consumedWater: 0,
          consumedProtein: 0,
          consumedCarbs: 0,
          consumedFat: 0
        } 
      };
    }
    
    const userData = userDoc.data();
    
    // Get today's nutrition data if available
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayStr = today.toISOString().split('T')[0];
    
    let nutritionData = { consumedCalories: 0, consumedWater: 0, consumedProtein: 0, consumedCarbs: 0, consumedFat: 0 };
    
    try {
      const nutritionDoc = await getDoc(doc(db, 'nutrition_logs', `${user.uid}_${todayStr}`));
      if (nutritionDoc.exists()) {
        const data = nutritionDoc.data();
        nutritionData = {
          consumedCalories: data.totalCalories || 0,
          consumedWater: data.waterIntake || 0,
          consumedProtein: data.totalProtein || 0,
          consumedCarbs: data.totalCarbs || 0,
          consumedFat: data.totalFat || 0,
        };
      }
    } catch (error) {
      console.error('Error fetching nutrition data:', error);
    }
    
    return { 
      success: true, 
      data: {
        weight: userData.weight || 0,
        height: userData.height || 0,
        calorieGoal: userData.dailyCalorieGoal || 2000,
        waterGoal: userData.dailyWaterGoal || 2000,
        ...nutritionData
      } 
    };
  } catch (error) {
    console.error('Error fetching health stats:', error);
    return { success: false, error: 'Failed to fetch health data' };
  }
}

/**
 * Update a user's health metrics
 */
export async function updateHealthMetrics(metrics: {
  weight?: number;
  height?: number;
  calorieGoal?: number;
  waterGoal?: number;
}) {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }
    
    const db = getFirestoreInstance();
    const userRef = doc(db, 'users', user.uid);
    
    // Prepare data for user profile update
    const updateData: any = {};
    
    if (metrics.weight !== undefined) {
      updateData.weight = metrics.weight;
      
      // Also record in health_metrics collection for historical tracking
      await setDoc(doc(db, 'health_metrics', `weight_${user.uid}_${Date.now()}`), {
        user_id: user.uid,
        type: 'weight',
        value: metrics.weight,
        timestamp: Timestamp.now()
      });
    }
    
    if (metrics.height !== undefined) {
      updateData.height = metrics.height;
      
      // Also record in health_metrics collection
      await setDoc(doc(db, 'health_metrics', `height_${user.uid}_${Date.now()}`), {
        user_id: user.uid,
        type: 'height',
        value: metrics.height,
        timestamp: Timestamp.now()
      });
    }
    
    if (metrics.calorieGoal !== undefined) {
      updateData.dailyCalorieGoal = metrics.calorieGoal;
    }
    
    if (metrics.waterGoal !== undefined) {
      updateData.dailyWaterGoal = metrics.waterGoal;
    }
    
    // Update user profile
    await updateDoc(userRef, updateData);
    
    return { success: true };
  } catch (error) {
    console.error('Error updating health metrics:', error);
    return { success: false, error: 'Failed to update health metrics' };
  }
}

/**
 * Hook to access profile functionality with Firebase
 */
export function useProfileService() {
  const { db } = useDatabaseType();
  
  /**
   * Get a user's profile
   */
  const getProfile = async (userId: string) => {
    try {
      const collection = 'users';
      const profile = await db.getDocument(collection, userId);
      return profile;
    } catch (error) {
      console.error('Error getting profile:', error);
      return null;
    }
  };
  
  /**
   * Update a user's profile
   */
  const updateProfile = async (userId: string, data: any) => {
    try {
      const collection = 'users';
      await db.updateDocument(collection, userId, data);
      return { success: true };
    } catch (error) {
      console.error('Error updating profile:', error);
      return { success: false, error };
    }
  };
  
  /**
   * Upload a profile avatar
   */
  const uploadProfileAvatar = async (userId: string, file: any) => {
    try {
      // First, resize and compress the image
      const processedImage = await ImageManipulator.manipulateAsync(
        file,
        [{ resize: { width: 300, height: 300 } }],
        { compress: 0.7, format: ImageManipulator.SaveFormat.JPEG }
      );
      
      // Get base64 data - handle web platform appropriately
      let base64Data;
      
      if (Platform.OS === 'web') {
        // For web platform, use fetch API
        const response = await fetch(processedImage.uri);
        const blob = await response.blob();
        base64Data = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            const result = reader.result as string;
            resolve(result);
          };
          reader.onerror = reject;
          reader.readAsDataURL(blob);
        });
      } else {
        // For native platforms
        const base64 = await FileSystem.readAsStringAsync(processedImage.uri, {
          encoding: FileSystem.EncodingType.Base64,
        });
        base64Data = `data:image/jpeg;base64,${base64}`;
      }
      
      // Upload file using abstraction layer
      const path = `user-content/avatars/${userId}/profile-${Date.now()}.jpg`;
      const url = await db.uploadFile(path, base64Data, { contentType: 'image/jpeg' });
      
      // Update profile with new avatar URL
      await updateProfile(userId, { avatar_url: url });
      
      return { success: true, url };
    } catch (error) {
      console.error('Error uploading profile avatar:', error);
      return { success: false, error };
    }
  };
  
  /**
   * Get user's health metrics
   */
  const getHealthMetrics = async (userId: string) => {
    try {
      const profile = await getProfile(userId);
      
      if (!profile) {
        return null;
      }
      
      return {
        weight: profile.weight,
        height: profile.height,
        dailyCalorieGoal: profile.dailyCalorieGoal,
        dailyWaterGoal: profile.dailyWaterGoal,
      };
    } catch (error) {
      console.error('Error getting health metrics:', error);
      return null;
    }
  };
  
  /**
   * Update user's health metrics
   */
  const updateHealthMetrics = async (userId: string, metrics: {
    weight?: number;
    height?: number;
    dailyCalorieGoal?: number;
    dailyWaterGoal?: number;
  }) => {
    try {
      const data = {
        weight: metrics.weight,
        height: metrics.height,
        dailyCalorieGoal: metrics.dailyCalorieGoal,
        dailyWaterGoal: metrics.dailyWaterGoal,
      };
      
      return await updateProfile(userId, data);
    } catch (error) {
      console.error('Error updating health metrics:', error);
      return { success: false, error };
    }
  };
  
  return {
    getProfile,
    updateProfile,
    uploadProfileAvatar,
    getHealthMetrics,
    updateHealthMetrics,
  };
} 