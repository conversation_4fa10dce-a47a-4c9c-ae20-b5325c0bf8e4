import { getAuth } from 'firebase/auth';
import { getFirestore, doc, getDoc } from 'firebase/firestore';

export interface ProfileStats {
  calories?: number;
  water?: number;
  steps?: number;
  workouts?: number;
}

export interface Achievement {
  title: string;
  description: string;
  date: string;
  color?: string;
}

export interface ProfileData {
  id: string;
  name?: string;
  full_name?: string;
  username?: string;
  email?: string;
  photoURL?: string;
  avatar_url?: string;
  height?: string | number;
  weight?: number;
  daily_calorie_goal?: number;
  daily_water_goal?: number;
  body_fat?: number;
  created_at?: string;
  updated_at?: string;
  age?: number;
  location?: string;
  gender?: string;
  blood_pressure?: string;
  resting_heart_rate?: number;
  sleep_hours?: number;
  activity_level?: string;
  fitness_goal?: string;
  dietary_preferences?: string;
  is_fallback?: boolean;
  stats?: ProfileStats;
  achievements?: Achievement[];
  [key: string]: any;
}

export interface LoadProfileResult {
  success: boolean;
  profile?: ProfileData;
  error?: Error;
}

export class ProfileViewService {
  private db = getFirestore();
  private auth = getAuth();

  async loadProfile(): Promise<LoadProfileResult> {
    try {
      const user = this.auth.currentUser;
      
      if (!user) {
        console.log('No user logged in');
        return { success: false, error: new Error('No user logged in') };
      }
      
      console.log('Loading profile data for user:', user.uid);
      const profileRef = doc(this.db, 'profiles', user.uid);
      const profileDoc = await getDoc(profileRef);
      
      if (profileDoc.exists()) {
        // Ensure stats and achievements exist even if not in the database
        const profileData = profileDoc.data() as ProfileData;
        
        // Handle multiple possible field names for the profile image
        if (!profileData.photoURL && profileData.avatar_url) {
          profileData.photoURL = profileData.avatar_url;
        }
        
        if (profileData.photoURL) {
          console.log('Profile image URL found:', profileData.photoURL);
        } else {
          console.log('No profile image URL found in profile data');
          // Try to use the auth user's photoURL as fallback
          if (user.photoURL) {
            profileData.photoURL = user.photoURL;
            console.log('Using auth user photoURL as fallback:', user.photoURL);
          }
        }
        
        profileData.stats = profileData.stats || {};
        profileData.achievements = profileData.achievements || [];
        
        return { 
          success: true, 
          profile: {
            ...profileData,
            id: user.uid
          }
        };
      } else {
        console.log('No profile exists for user, creating default');
        // If no profile exists, create a default one
        const defaultProfile: ProfileData = {
          id: user.uid,
          name: user.displayName || 'User',
          email: user.email || '',
          photoURL: user.photoURL || '',
          stats: {},
          achievements: []
        };
        
        return {
          success: true,
          profile: defaultProfile
        };
      }
    } catch (error) {
      console.error('Error loading profile:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error : new Error('Unknown error loading profile') 
      };
    }
  }

  calculateProfileCompletion(profileData: ProfileData): number {
    const requiredFields = [
      'name', 'email', 'photoURL', 'height', 'weight', 
      'daily_calorie_goal', 'daily_water_goal', 'body_fat'
    ];
    
    const completedFields = requiredFields.filter(field => {
      return profileData[field] !== undefined && 
             profileData[field] !== null && 
             profileData[field] !== '';
    });
    
    return Math.floor((completedFields.length / requiredFields.length) * 100);
  }

  getDaysSinceCreation(profile: ProfileData): number | null {
    if (!profile.created_at) return null;
    
    const creationDate = new Date(profile.created_at);
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - creationDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  }
}