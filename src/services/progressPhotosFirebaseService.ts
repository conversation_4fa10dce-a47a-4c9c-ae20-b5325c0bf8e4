/**
 * Progress Photos Firebase Service
 * 
 * Implementation of progress photo storage functionality using Firebase.
 * Handles secure storage, retrieval, and management of user progress photos.
 */

import { ref, uploadBytes, getDownloadURL, deleteObject, listAll } from 'firebase/storage';
import { collection, addDoc, getDoc, getDocs, query, where, doc, updateDoc, deleteDoc, orderBy, serverTimestamp, Timestamp, limit } from 'firebase/firestore';
import { storage, firestore } from '@/lib/firebase';
import * as ImageManipulator from 'expo-image-manipulator';
import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';

// Types
export interface ProgressPhoto {
  id: string;
  userId: string;
  date: string; // ISO date string (YYYY-MM-DD)
  storagePath: string;
  thumbnailPath?: string;
  imageUrl?: string;
  thumbnailUrl?: string;
  metrics?: {
    weight?: number;
    bodyFat?: number;
    musclePercentage?: number;
    note?: string;
    [key: string]: any;
  };
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Upload a progress photo to Firebase Storage
 * 
 * @param userId User ID who uploaded the photo
 * @param imageUri URI of the image to upload
 * @param date Date the photo was taken (YYYY-MM-DD)
 * @param metrics Optional metrics to associate with the photo
 * @param tags Optional tags for the photo
 * @returns Promise with success status and progress photo data or error
 */
export async function uploadProgressPhoto(
  userId: string,
  imageUri: string,
  date: string,
  metrics?: ProgressPhoto['metrics'],
  tags?: string[]
): Promise<{
  success: boolean;
  data?: ProgressPhoto;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    // Validate date format
    if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      return {
        success: false,
        error: 'Date must be in YYYY-MM-DD format'
      };
    }

    // Create unique file paths
    const timestamp = new Date().getTime();
    const fileName = `${date}_${timestamp}.jpg`;
    const thumbnailName = `${date}_${timestamp}_thumb.jpg`;
    const storagePath = `${userId}/progress_photos/${fileName}`;
    const thumbnailPath = `${userId}/progress_photos/thumbnails/${thumbnailName}`;

    // Process image - resize the original to a reasonable size
    const processedImage = await ImageManipulator.manipulateAsync(
      imageUri,
      [{ resize: { width: 1200 } }],
      { format: ImageManipulator.SaveFormat.JPEG, compress: 0.8 }
    );

    // Create thumbnail
    const thumbnail = await ImageManipulator.manipulateAsync(
      imageUri,
      [{ resize: { width: 300 } }],
      { format: ImageManipulator.SaveFormat.JPEG, compress: 0.7 }
    );

    // Convert image to blob
    const imageBlob = await uriToBlob(processedImage.uri);
    const thumbnailBlob = await uriToBlob(thumbnail.uri);

    // Upload to Firebase Storage
    const storageRef = ref(storage, storagePath);
    const thumbnailRef = ref(storage, thumbnailPath);

    await uploadBytes(storageRef, imageBlob, {
      contentType: 'image/jpeg',
      customMetadata: {
        userId,
        date,
        uploadedAt: timestamp.toString()
      }
    });

    await uploadBytes(thumbnailRef, thumbnailBlob, {
      contentType: 'image/jpeg',
      customMetadata: {
        userId,
        date,
        uploadedAt: timestamp.toString(),
        isThumbnail: 'true'
      }
    });

    // Get download URLs
    const imageUrl = await getDownloadURL(storageRef);
    const thumbnailUrl = await getDownloadURL(thumbnailRef);

    // Add record to Firestore
    const photoData = {
      userId,
      date,
      storagePath,
      thumbnailPath,
      imageUrl,
      thumbnailUrl,
      metrics: metrics || {},
      tags: tags || [],
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    const photoRef = await addDoc(collection(firestore, 'progress_photos'), photoData);

    // Return the progress photo record
    const progressPhoto: ProgressPhoto = {
      id: photoRef.id,
      userId,
      date,
      storagePath,
      thumbnailPath,
      imageUrl,
      thumbnailUrl,
      metrics: metrics || {},
      tags: tags || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return {
      success: true,
      data: progressPhoto
    };
  } catch (error) {
    console.error('Error uploading progress photo:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get progress photos for a user by date range
 * 
 * @param userId User ID to get photos for
 * @param startDate Start date (inclusive) in YYYY-MM-DD format
 * @param endDate End date (inclusive) in YYYY-MM-DD format
 * @returns Promise with success status and array of progress photos or error
 */
export async function getProgressPhotosByDateRange(
  userId: string,
  startDate: string,
  endDate: string
): Promise<{
  success: boolean;
  data?: ProgressPhoto[];
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    // Query Firestore
    const photosQuery = query(
      collection(firestore, 'progress_photos'),
      where('userId', '==', userId),
      where('date', '>=', startDate),
      where('date', '<=', endDate),
      orderBy('date', 'desc')
    );

    const querySnapshot = await getDocs(photosQuery);

    const photos: ProgressPhoto[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      photos.push({
        id: doc.id,
        userId: data.userId,
        date: data.date,
        storagePath: data.storagePath,
        thumbnailPath: data.thumbnailPath,
        imageUrl: data.imageUrl,
        thumbnailUrl: data.thumbnailUrl,
        metrics: data.metrics || {},
        tags: data.tags || [],
        createdAt: data.createdAt instanceof Timestamp ? 
          data.createdAt.toDate().toISOString() : data.createdAt,
        updatedAt: data.updatedAt instanceof Timestamp ? 
          data.updatedAt.toDate().toISOString() : data.updatedAt
      });
    });

    return {
      success: true,
      data: photos
    };
  } catch (error) {
    console.error('Error getting progress photos:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get the latest progress photo for a user
 * 
 * @param userId User ID to get photo for
 * @returns Promise with success status and latest progress photo or error
 */
export async function getLatestProgressPhoto(
  userId: string
): Promise<{
  success: boolean;
  data?: ProgressPhoto;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    // Query Firestore
    const photosQuery = query(
      collection(firestore, 'progress_photos'),
      where('userId', '==', userId),
      orderBy('date', 'desc'),
      orderBy('createdAt', 'desc'),
      limit(1)
    );

    const querySnapshot = await getDocs(photosQuery);

    if (querySnapshot.empty) {
      return {
        success: true,
        data: undefined
      };
    }

    const docSnapshot = querySnapshot.docs[0];
    const data = docSnapshot.data();

    if (!data) {
      return {
        success: false,
        error: 'No data found for document'
      };
    }

    const photo: ProgressPhoto = {
      id: docSnapshot.id,
      userId: data.userId,
      date: data.date,
      storagePath: data.storagePath,
      thumbnailPath: data.thumbnailPath,
      imageUrl: data.imageUrl,
      thumbnailUrl: data.thumbnailUrl,
      metrics: data.metrics || {},
      tags: data.tags || [],
      createdAt: data.createdAt instanceof Timestamp ? 
        data.createdAt.toDate().toISOString() : data.createdAt,
      updatedAt: data.updatedAt instanceof Timestamp ? 
        data.updatedAt.toDate().toISOString() : data.updatedAt
    };

    return {
      success: true,
      data: photo
    };
  } catch (error) {
    console.error('Error getting latest progress photo:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get a single progress photo by ID
 * 
 * @param photoId Progress photo ID to get
 * @returns Promise with success status and progress photo or error
 */
export async function getProgressPhoto(
  photoId: string
): Promise<{
  success: boolean;
  data?: ProgressPhoto;
  error?: string;
}> {
  try {
    const photoRef = doc(firestore, 'progress_photos', photoId);
    const photoSnap = await getDoc(photoRef);

    if (!photoSnap.exists()) {
      return {
        success: false,
        error: 'Progress photo not found'
      };
    }

    const data = photoSnap.data();

    const photo: ProgressPhoto = {
      id: photoSnap.id,
      userId: data.userId,
      date: data.date,
      storagePath: data.storagePath,
      thumbnailPath: data.thumbnailPath,
      imageUrl: data.imageUrl,
      thumbnailUrl: data.thumbnailUrl,
      metrics: data.metrics || {},
      tags: data.tags || [],
      createdAt: data.createdAt instanceof Timestamp ? 
        data.createdAt.toDate().toISOString() : data.createdAt,
      updatedAt: data.updatedAt instanceof Timestamp ? 
        data.updatedAt.toDate().toISOString() : data.updatedAt
    };

    return {
      success: true,
      data: photo
    };
  } catch (error) {
    console.error('Error getting progress photo:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Update a progress photo's metadata
 * 
 * @param photoId Progress photo ID to update
 * @param updates Updates to apply (metrics, tags)
 * @returns Promise with success status and updated progress photo or error
 */
export async function updateProgressPhotoMetadata(
  photoId: string,
  updates: {
    metrics?: ProgressPhoto['metrics'];
    tags?: string[];
  }
): Promise<{
  success: boolean;
  data?: ProgressPhoto;
  error?: string;
}> {
  try {
    const photoRef = doc(firestore, 'progress_photos', photoId);
    const photoSnap = await getDoc(photoRef);

    if (!photoSnap.exists()) {
      return {
        success: false,
        error: 'Progress photo not found'
      };
    }

    // Build the update object
    const updateData: any = {
      updatedAt: serverTimestamp()
    };

    if (updates.metrics) {
      updateData.metrics = {
        ...photoSnap.data().metrics,
        ...updates.metrics
      };
    }

    if (updates.tags) {
      updateData.tags = updates.tags;
    }

    // Update the document
    await updateDoc(photoRef, updateData);

    // Get the updated document
    const updatedPhotoSnap = await getDoc(photoRef);
    const data = updatedPhotoSnap.data();
    
    if (!data) {
      return {
        success: false,
        error: 'Failed to retrieve updated photo data'
      };
    }

    const updatedPhoto: ProgressPhoto = {
      id: updatedPhotoSnap.id,
      userId: data.userId,
      date: data.date,
      storagePath: data.storagePath,
      thumbnailPath: data.thumbnailPath,
      imageUrl: data.imageUrl,
      thumbnailUrl: data.thumbnailUrl,
      metrics: data.metrics || {},
      tags: data.tags || [],
      createdAt: data.createdAt instanceof Timestamp ? 
        data.createdAt.toDate().toISOString() : data.createdAt,
      updatedAt: data.updatedAt instanceof Timestamp ? 
        data.updatedAt.toDate().toISOString() : data.updatedAt
    };

    return {
      success: true,
      data: updatedPhoto
    };
  } catch (error) {
    console.error('Error updating progress photo metadata:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Delete a progress photo
 * 
 * @param photoId Progress photo ID to delete
 * @returns Promise with success status or error
 */
export async function deleteProgressPhoto(
  photoId: string
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Get the photo first to get the storage paths
    const photoRef = doc(firestore, 'progress_photos', photoId);
    const photoSnap = await getDoc(photoRef);

    if (!photoSnap.exists()) {
      return {
        success: false,
        error: 'Progress photo not found'
      };
    }

    const data = photoSnap.data();
    const storagePath = data.storagePath;
    const thumbnailPath = data.thumbnailPath;

    // Delete the files from storage
    const storageRef = ref(storage, storagePath);
    const thumbnailRef = ref(storage, thumbnailPath);

    try {
      await deleteObject(storageRef);
    } catch (storageError) {
      console.warn('Error deleting main image, continuing:', storageError);
    }

    try {
      await deleteObject(thumbnailRef);
    } catch (thumbnailError) {
      console.warn('Error deleting thumbnail, continuing:', thumbnailError);
    }

    // Delete the document record
    await deleteDoc(photoRef);

    return {
      success: true
    };
  } catch (error) {
    console.error('Error deleting progress photo:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Delete all progress photos for a user
 * 
 * @param userId User ID to delete photos for
 * @returns Promise with success status and count of deleted photos or error
 */
export async function deleteAllProgressPhotos(
  userId: string
): Promise<{
  success: boolean;
  deletedCount?: number;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    // Query all photos for this user
    const photosQuery = query(
      collection(firestore, 'progress_photos'),
      where('userId', '==', userId)
    );

    const querySnapshot = await getDocs(photosQuery);

    if (querySnapshot.empty) {
      return {
        success: true,
        deletedCount: 0
      };
    }

    // Delete each photo
    let deletedCount = 0;
    for (const photoDoc of querySnapshot.docs) {
      const data = photoDoc.data();
      const storagePath = data.storagePath;
      const thumbnailPath = data.thumbnailPath;

      // Delete the files from storage
      const storageRef = ref(storage, storagePath);
      const thumbnailRef = ref(storage, thumbnailPath);

      try {
        await deleteObject(storageRef);
      } catch (storageError) {
        console.warn('Error deleting main image, continuing:', storageError);
      }

      try {
        await deleteObject(thumbnailRef);
      } catch (thumbnailError) {
        console.warn('Error deleting thumbnail, continuing:', thumbnailError);
      }

      // Delete the document record
      await deleteDoc(photoDoc.ref);
      deletedCount++;
    }

    return {
      success: true,
      deletedCount
    };
  } catch (error) {
    console.error('Error deleting all progress photos:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get comparison photos for a specific date range
 * 
 * @param userId User ID to get photos for
 * @param firstDate First date to compare (YYYY-MM-DD)
 * @param secondDate Second date to compare (YYYY-MM-DD)
 * @returns Promise with success status and comparison photos or error
 */
export async function getComparisonPhotos(
  userId: string,
  firstDate: string,
  secondDate: string
): Promise<{
  success: boolean;
  data?: {
    firstPhoto?: ProgressPhoto;
    secondPhoto?: ProgressPhoto;
  };
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    // Get photos closest to the specified dates
    const firstDateObj = new Date(firstDate);
    const secondDateObj = new Date(secondDate);

    // Query all photos
    const photosQuery = query(
      collection(firestore, 'progress_photos'),
      where('userId', '==', userId),
      orderBy('date', 'asc')
    );

    const querySnapshot = await getDocs(photosQuery);
    
    if (querySnapshot.empty) {
      return {
        success: true,
        data: {}
      };
    }

    const photos: ProgressPhoto[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      photos.push({
        id: doc.id,
        userId: data.userId,
        date: data.date,
        storagePath: data.storagePath,
        thumbnailPath: data.thumbnailPath,
        imageUrl: data.imageUrl,
        thumbnailUrl: data.thumbnailUrl,
        metrics: data.metrics || {},
        tags: data.tags || [],
        createdAt: data.createdAt instanceof Timestamp ? 
          data.createdAt.toDate().toISOString() : data.createdAt,
        updatedAt: data.updatedAt instanceof Timestamp ? 
          data.updatedAt.toDate().toISOString() : data.updatedAt
      });
    });

    // Find closest photos to requested dates
    let firstPhoto: ProgressPhoto | undefined;
    let secondPhoto: ProgressPhoto | undefined;

    let minFirstDiff = Infinity;
    let minSecondDiff = Infinity;

    for (const photo of photos) {
      const photoDate = new Date(photo.date);
      
      // Calculate difference in days
      const firstDiff = Math.abs(photoDate.getTime() - firstDateObj.getTime()) / (1000 * 60 * 60 * 24);
      const secondDiff = Math.abs(photoDate.getTime() - secondDateObj.getTime()) / (1000 * 60 * 60 * 24);

      // Update if this is the closest photo to first date
      if (firstDiff < minFirstDiff) {
        minFirstDiff = firstDiff;
        firstPhoto = photo;
      }

      // Update if this is the closest photo to second date
      if (secondDiff < minSecondDiff) {
        minSecondDiff = secondDiff;
        secondPhoto = photo;
      }
    }

    // Make sure we didn't pick the same photo for both dates
    if (firstPhoto && secondPhoto && firstPhoto.id === secondPhoto.id) {
      // If dates are closer to each other than available photos, we may need to not show comparison
      if (photos.length < 2) {
        // Not enough photos for a proper comparison
        secondPhoto = undefined;
      } else {
        // Try to find next best match for second date
        let nextBestSecondPhoto: ProgressPhoto | undefined;
        let nextBestDiff = Infinity;

        for (const photo of photos) {
          if (photo.id === firstPhoto.id) continue; // Skip the first photo

          const photoDate = new Date(photo.date);
          const diff = Math.abs(photoDate.getTime() - secondDateObj.getTime()) / (1000 * 60 * 60 * 24);

          if (diff < nextBestDiff) {
            nextBestDiff = diff;
            nextBestSecondPhoto = photo;
          }
        }

        secondPhoto = nextBestSecondPhoto;
      }
    }

    return {
      success: true,
      data: {
        firstPhoto,
        secondPhoto
      }
    };
  } catch (error) {
    console.error('Error getting comparison photos:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Helper function to convert a URI to a Blob
 */
async function uriToBlob(uri: string): Promise<Blob> {
  if (Platform.OS === 'web') {
    const response = await fetch(uri);
    const blob = await response.blob();
    return blob;
  } else {
    const fileString = await FileSystem.readAsStringAsync(uri, {
      encoding: FileSystem.EncodingType.Base64,
    });
    const base64 = `data:image/jpeg;base64,${fileString}`;
    const response = await fetch(base64);
    const blob = await response.blob();
    return blob;
  }
}

// limitToFirst is no longer needed as we're using limit directly 