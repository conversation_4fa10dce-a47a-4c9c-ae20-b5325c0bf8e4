import * as FirebaseProgressPhotos from './progressPhotosFirebaseService';
export type { ProgressPhoto } from './progressPhotosFirebaseService';


  const getProgressPhotosByDateRange = async (
    startDate: string,
    endDate: string
  ) => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      if (useFirebase) {
        // Use Firebase implementation
        return await FirebaseProgressPhotos.getProgressPhotosByDateRange(
          user.id,
          startDate,
          endDate
        );
      } else {
        
        // For now, return an empty array
        return {
          success: true,
          data: []
        };
      }
    } catch (error) {
      console.error('Error getting progress photos:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  
  const updateProgressPhotoMetadata = async (
    photoId: string,
    updates: {
      metrics?: FirebaseProgressPhotos.ProgressPhoto['metrics'];
      tags?: string[];
    }
  ) => {
    try {
      if (useFirebase) {
        // Use Firebase implementation
        return await FirebaseProgressPhotos.updateProgressPhotoMetadata(photoId, updates);
      } else {
        
        // For now, return an error
        return {
          success: false,
          error: 'Progress photo update not implemented for firebase'
        };
      }
    } catch (error) {
      console.error('Error updating progress photo metadata:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  
  const deleteAllProgressPhotos = async () => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      if (useFirebase) {
        // Use Firebase implementation
        return await FirebaseProgressPhotos.deleteAllProgressPhotos(user.id);
      } else {
        
        // For now, return an error
        return {
          success: false,
          error: 'Progress photos deletion not implemented for firebase'
        };
      }
    } catch (error) {
      console.error('Error deleting all progress photos:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Get comparison photos for before/after comparisons
   */
  const getComparisonPhotos = async (
    firstDate: string,
    secondDate: string
  ) => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      if (useFirebase) {
        // Use Firebase implementation
        return await FirebaseProgressPhotos.getComparisonPhotos(
          user.id,
          firstDate,
          secondDate
        );
      } else {
        
        // For now, return an empty object
        return {
          success: true,
          data: {}
        };
      }
    } catch (error) {
      console.error('Error getting comparison photos:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  return {
    uploadProgressPhoto,
    getProgressPhotosByDateRange,
    getLatestProgressPhoto,
    getProgressPhoto,
    updateProgressPhotoMetadata,
    deleteProgressPhoto,
    deleteAllProgressPhotos,
    getComparisonPhotos
  };
}
