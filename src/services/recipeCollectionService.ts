import AsyncStorage from '@react-native-async-storage/async-storage';

import { getAuth } from 'firebase/auth';
import { getFirestore, collection, doc, query, where, getDocs, addDoc, setDoc, deleteDoc, updateDoc, increment, arrayUnion, arrayRemove } from 'firebase/firestore';
import { z } from 'zod';

// Get Firebase instances
const auth = getAuth();
const db = getFirestore();

// Define storage keys for local data
const STORAGE_KEYS = {
  SAVED_RECIPES: 'user_saved_recipes',
  RECIPE_RATINGS: 'user_recipe_ratings',
  RECIPE_COLLECTIONS: 'user_recipe_collections',
};

// Schema for a saved recipe
export const SavedRecipeSchema = z.object({
  id: z.string(),
  name: z.string(),
  imageUrl: z.string().optional(),
  description: z.string(),
  nutritionalInfo: z.object({
    calories: z.number(),
    protein: z.number(),
    carbs: z.number(),
    fat: z.number(),
    fiber: z.number().optional(),
    sugar: z.number().optional(),
  }),
  originalFoodName: z.string(),
  focus: z.string().optional(),
  dateAdded: z.string(),
  rating: z.number().optional(),
  collectionIds: z.array(z.string()).optional(),
});

export type SavedRecipe = z.infer<typeof SavedRecipeSchema>;

// Schema for a recipe collection
export const RecipeCollectionSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  dateCreated: z.string(),
  coverImageUrl: z.string().optional(),
  recipeCount: z.number().default(0),
});

export type RecipeCollection = z.infer<typeof RecipeCollectionSchema>;

/**
 * Get all saved recipes from local storage or database
 */
export async function getSavedRecipes(): Promise<SavedRecipe[]> {
  try {
    // Try to get user from Firebase
    const user = auth.currentUser;
    
    if (user) {
      // If user is logged in, get from Firestore
      const recipesQuery = query(
        collection(db, 'saved_recipes'),
        where('user_id', '==', user.uid)
      );
      
      const querySnapshot = await getDocs(recipesQuery);
      
      if (querySnapshot.empty) {
        // No recipes found, fall back to local storage
        return getLocalSavedRecipes();
      }
      
      const parsedRecipes: SavedRecipe[] = querySnapshot.docs.map(doc => {
        const item = doc.data();
        return {
          id: item.recipe_id,
          name: item.recipe_name,
          imageUrl: item.image_url,
          description: item.description,
          nutritionalInfo: typeof item.nutritional_info === 'string' 
            ? JSON.parse(item.nutritional_info) 
            : item.nutritional_info,
          originalFoodName: item.original_food_name,
          focus: item.focus,
          dateAdded: item.date_added,
          rating: item.rating,
          collectionIds: item.collection_ids,
        };
      });
      
      return parsedRecipes;
    }
    
    // Fall back to local storage if no user or no data
    return getLocalSavedRecipes();
  } catch (error) {
    console.error('Error in getSavedRecipes:', error);
    return getLocalSavedRecipes();
  }
}

/**
 * Save a recipe to the user's collection
 */
export async function saveRecipe(recipe: Omit<SavedRecipe, 'dateAdded'>): Promise<boolean> {
  try {
    const newRecipe: SavedRecipe = {
      ...recipe,
      dateAdded: new Date().toISOString(),
    };
    
    // Try to get user from Firebase
    const user = auth.currentUser;
    
    if (user) {
      // If user is logged in, save to Firestore
      try {
        await addDoc(collection(db, 'saved_recipes'), {
          user_id: user.uid,
          recipe_id: newRecipe.id,
          recipe_name: newRecipe.name,
          image_url: newRecipe.imageUrl,
          description: newRecipe.description,
          nutritional_info: JSON.stringify(newRecipe.nutritionalInfo),
          original_food_name: newRecipe.originalFoodName,
          focus: newRecipe.focus,
          date_added: newRecipe.dateAdded,
          rating: newRecipe.rating,
          collection_ids: newRecipe.collectionIds,
        });
        
        return true;
      } catch (error) {
        console.error('Error saving recipe to Firestore:', error);
        // Fall back to local storage
        return saveRecipeLocally(newRecipe);
      }
    }
    
    // Fall back to local storage if no user
    return saveRecipeLocally(newRecipe);
  } catch (error) {
    console.error('Error in saveRecipe:', error);
    return false;
  }
}

/**
 * Delete a saved recipe
 */
export async function deleteSavedRecipe(recipeId: string): Promise<boolean> {
  try {
    // Try to get user from Firebase
    const user = auth.currentUser;
    
    if (user) {
      // If user is logged in, delete from Firestore
      try {
        // Query for the document first
        const recipesQuery = query(
          collection(db, 'saved_recipes'),
          where('user_id', '==', user.uid),
          where('recipe_id', '==', recipeId)
        );
        
        const querySnapshot = await getDocs(recipesQuery);
        
        if (querySnapshot.empty) {
          console.error('Recipe not found in Firestore');
          return deleteRecipeLocally(recipeId);
        }
        
        // Delete the document
        await deleteDoc(doc(db, 'saved_recipes', querySnapshot.docs[0].id));
        return true;
      } catch (error) {
        console.error('Error deleting recipe from Firestore:', error);
        // Fall back to local storage
        return deleteRecipeLocally(recipeId);
      }
    }
    
    // Fall back to local storage if no user
    return deleteRecipeLocally(recipeId);
  } catch (error) {
    console.error('Error in deleteSavedRecipe:', error);
    return false;
  }
}

/**
 * Rate a saved recipe
 */
export async function rateRecipe(recipeId: string, rating: number): Promise<boolean> {
  try {
    // Ensure rating is between 1 and 5
    const validRating = Math.min(Math.max(rating, 1), 5);
    
    // Try to get user from Firebase
    const user = auth.currentUser;
    
    if (user) {
      // If user is logged in, update in Firestore
      try {
        // Query for the document first
        const recipesQuery = query(
          collection(db, 'saved_recipes'),
          where('user_id', '==', user.uid),
          where('recipe_id', '==', recipeId)
        );
        
        const querySnapshot = await getDocs(recipesQuery);
        
        if (querySnapshot.empty) {
          console.error('Recipe not found in Firestore');
          return rateRecipeLocally(recipeId, validRating);
        }
        
        // Update the document
        await updateDoc(doc(db, 'saved_recipes', querySnapshot.docs[0].id), {
          rating: validRating
        });
        
        return true;
      } catch (error) {
        console.error('Error rating recipe in Firestore:', error);
        // Fall back to local storage
        return rateRecipeLocally(recipeId, validRating);
      }
    }
    
    // Fall back to local storage if no user
    return rateRecipeLocally(recipeId, validRating);
  } catch (error) {
    console.error('Error in rateRecipe:', error);
    return false;
  }
}

/**
 * Get recipe collections
 */
export async function getRecipeCollections(): Promise<RecipeCollection[]> {
  try {
    // Try to get user from Firebase
    const user = auth.currentUser;
    
    if (user) {
      // If user is logged in, get from Firestore
      try {
        const collectionsQuery = query(
          collection(db, 'recipe_collections'),
          where('user_id', '==', user.uid)
        );
        
        const querySnapshot = await getDocs(collectionsQuery);
        
        if (querySnapshot.empty) {
          // No collections found, fall back to local storage
          return getLocalRecipeCollections();
        }
        
        const parsedCollections: RecipeCollection[] = querySnapshot.docs.map(doc => {
          const item = doc.data();
          return {
            id: item.id,
            name: item.name,
            description: item.description,
            dateCreated: item.date_created,
            coverImageUrl: item.cover_image_url,
            recipeCount: item.recipe_count || 0,
          };
        });
        
        return parsedCollections;
      } catch (error) {
        console.error('Error fetching collections from Firestore:', error);
        // Fall back to local storage
        return getLocalRecipeCollections();
      }
    }
    
    // Fall back to local storage if no user or no data
    return getLocalRecipeCollections();
  } catch (error) {
    console.error('Error in getRecipeCollections:', error);
    return getLocalRecipeCollections();
  }
}

/**
 * Create a new recipe collection
 */
export async function createRecipeCollection(
  name: string, 
  description?: string
): Promise<RecipeCollection | null> {
  try {
    const newCollection: RecipeCollection = {
      id: generateUUID(),
      name,
      description,
      dateCreated: new Date().toISOString(),
      recipeCount: 0,
    };
    
    // Try to get user from Firebase
    const user = auth.currentUser;
    
    if (user) {
      // If user is logged in, save to Firestore
      try {
        await addDoc(collection(db, 'recipe_collections'), {
          user_id: user.uid,
          id: newCollection.id,
          name: newCollection.name,
          description: newCollection.description,
          date_created: newCollection.dateCreated,
          recipe_count: 0
        });
        
        return newCollection;
      } catch (error) {
        console.error('Error creating collection in Firestore:', error);
        // Fall back to local storage
        return createCollectionLocally(newCollection);
      }
    }
    
    // Fall back to local storage if no user
    return createCollectionLocally(newCollection);
  } catch (error) {
    console.error('Error in createRecipeCollection:', error);
    return null;
  }
}

/**
 * Add a recipe to a collection
 */
export async function addRecipeToCollection(
  recipeId: string, 
  collectionId: string
): Promise<boolean> {
  try {
    // Try to get user from Firebase
    const user = auth.currentUser;
    
    if (user) {
      // If user is logged in, update in Firestore
      try {
        // First, get the recipe document to update its collections
        const recipeQuery = query(
          collection(db, 'saved_recipes'),
          where('user_id', '==', user.uid),
          where('recipe_id', '==', recipeId)
        );
        
        const recipeSnapshot = await getDocs(recipeQuery);
        
        if (recipeSnapshot.empty) {
          console.error('Recipe not found in Firestore');
          return addRecipeToCollectionLocally(recipeId, collectionId);
        }
        
        const recipeDoc = recipeSnapshot.docs[0];
        const recipeData = recipeDoc.data();
        
        // Get current collection IDs or initialize empty array
        const collectionIds = recipeData.collection_ids || [];
        
        // Add collection ID if not already present
        if (!collectionIds.includes(collectionId)) {
          // Update recipe with new collection ID
          await updateDoc(doc(db, 'saved_recipes', recipeDoc.id), {
            collection_ids: arrayUnion(collectionId)
          });
          
          // Now update the collection's recipe count
          const collectionQuery = query(
            collection(db, 'recipe_collections'),
            where('user_id', '==', user.uid),
            where('id', '==', collectionId)
          );
          
          const collectionSnapshot = await getDocs(collectionQuery);
          
          if (!collectionSnapshot.empty) {
            await updateDoc(doc(db, 'recipe_collections', collectionSnapshot.docs[0].id), {
              recipe_count: increment(1)
            });
          }
        }
        
        return true;
      } catch (error) {
        console.error('Error adding recipe to collection in Firestore:', error);
        return addRecipeToCollectionLocally(recipeId, collectionId);
      }
    }
    
    // Fall back to local storage if no user
    return addRecipeToCollectionLocally(recipeId, collectionId);
  } catch (error) {
    console.error('Error in addRecipeToCollection:', error);
    return false;
  }
}

/**
 * Remove a recipe from a collection
 */
export async function removeRecipeFromCollection(
  recipeId: string, 
  collectionId: string
): Promise<boolean> {
  try {
    // Try to get user from Firebase
    const user = auth.currentUser;
    
    if (user) {
      // If user is logged in, update in Firestore
      try {
        // First, get the recipe document to update its collections
        const recipeQuery = query(
          collection(db, 'saved_recipes'),
          where('user_id', '==', user.uid),
          where('recipe_id', '==', recipeId)
        );
        
        const recipeSnapshot = await getDocs(recipeQuery);
        
        if (recipeSnapshot.empty) {
          console.error('Recipe not found in Firestore');
          return removeRecipeFromCollectionLocally(recipeId, collectionId);
        }
        
        const recipeDoc = recipeSnapshot.docs[0];
        
        // Update recipe by removing collection ID
        await updateDoc(doc(db, 'saved_recipes', recipeDoc.id), {
          collection_ids: arrayRemove(collectionId)
        });
        
        // Now update the collection's recipe count
        const collectionQuery = query(
          collection(db, 'recipe_collections'),
          where('user_id', '==', user.uid),
          where('id', '==', collectionId)
        );
        
        const collectionSnapshot = await getDocs(collectionQuery);
        
        if (!collectionSnapshot.empty) {
          await updateDoc(doc(db, 'recipe_collections', collectionSnapshot.docs[0].id), {
            recipe_count: increment(-1)
          });
        }
        
        return true;
      } catch (error) {
        console.error('Error removing recipe from collection in Firestore:', error);
        return removeRecipeFromCollectionLocally(recipeId, collectionId);
      }
    }
    
    // Fall back to local storage if no user
    return removeRecipeFromCollectionLocally(recipeId, collectionId);
  } catch (error) {
    console.error('Error in removeRecipeFromCollection:', error);
    return false;
  }
}

/**
 * Get recipes in a specific collection
 */
export async function getRecipesInCollection(collectionId: string): Promise<SavedRecipe[]> {
  try {
    // Get all saved recipes
    const allRecipes = await getSavedRecipes();
    
    // Filter recipes that are in the specified collection
    return allRecipes.filter(recipe => 
      recipe.collectionIds && recipe.collectionIds.includes(collectionId)
    );
  } catch (error) {
    console.error('Error in getRecipesInCollection:', error);
    return [];
  }
}

// Helper functions for local storage operations

/**
 * Get saved recipes from local storage
 */
async function getLocalSavedRecipes(): Promise<SavedRecipe[]> {
  try {
    const data = await AsyncStorage.getItem(STORAGE_KEYS.SAVED_RECIPES);
    if (data) {
      const recipes = JSON.parse(data);
      return recipes;
    }
    return [];
  } catch (error) {
    console.error('Error getting local saved recipes:', error);
    return [];
  }
}

/**
 * Save a recipe to local storage
 */
async function saveRecipeLocally(recipe: SavedRecipe): Promise<boolean> {
  try {
    // Get current recipes
    const recipes = await getLocalSavedRecipes();
    
    // Add new recipe
    recipes.push(recipe);
    
    // Save back to storage
    await AsyncStorage.setItem(STORAGE_KEYS.SAVED_RECIPES, JSON.stringify(recipes));
    
    return true;
  } catch (error) {
    console.error('Error saving recipe locally:', error);
    return false;
  }
}

/**
 * Delete a recipe from local storage
 */
async function deleteRecipeLocally(recipeId: string): Promise<boolean> {
  try {
    // Get current recipes
    const recipes = await getLocalSavedRecipes();
    
    // Filter out the recipe to delete
    const updatedRecipes = recipes.filter(recipe => recipe.id !== recipeId);
    
    // Save back to storage
    await AsyncStorage.setItem(STORAGE_KEYS.SAVED_RECIPES, JSON.stringify(updatedRecipes));
    
    return true;
  } catch (error) {
    console.error('Error deleting recipe locally:', error);
    return false;
  }
}

/**
 * Rate a recipe in local storage
 */
async function rateRecipeLocally(recipeId: string, rating: number): Promise<boolean> {
  try {
    // Get current recipes
    const recipes = await getLocalSavedRecipes();
    
    // Find and update the recipe rating
    const updatedRecipes = recipes.map(recipe => {
      if (recipe.id === recipeId) {
        return { ...recipe, rating };
      }
      return recipe;
    });
    
    // Save back to storage
    await AsyncStorage.setItem(STORAGE_KEYS.SAVED_RECIPES, JSON.stringify(updatedRecipes));
    
    return true;
  } catch (error) {
    console.error('Error rating recipe locally:', error);
    return false;
  }
}

/**
 * Get recipe collections from local storage
 */
async function getLocalRecipeCollections(): Promise<RecipeCollection[]> {
  try {
    const data = await AsyncStorage.getItem(STORAGE_KEYS.RECIPE_COLLECTIONS);
    if (data) {
      const collections = JSON.parse(data);
      return collections;
    }
    return [];
  } catch (error) {
    console.error('Error getting local recipe collections:', error);
    return [];
  }
}

/**
 * Create a collection in local storage
 */
async function createCollectionLocally(collection: RecipeCollection): Promise<RecipeCollection | null> {
  try {
    // Get current collections
    const collections = await getLocalRecipeCollections();
    
    // Add new collection
    collections.push(collection);
    
    // Save back to storage
    await AsyncStorage.setItem(STORAGE_KEYS.RECIPE_COLLECTIONS, JSON.stringify(collections));
    
    return collection;
  } catch (error) {
    console.error('Error creating collection locally:', error);
    return null;
  }
}

/**
 * Add a recipe to a collection in local storage
 */
async function addRecipeToCollectionLocally(
  recipeId: string, 
  collectionId: string
): Promise<boolean> {
  try {
    // Get current recipes
    const recipes = await getLocalSavedRecipes();
    
    // Find and update the recipe's collections
    const updatedRecipes = recipes.map(recipe => {
      if (recipe.id === recipeId) {
        const collectionIds = recipe.collectionIds || [];
        if (!collectionIds.includes(collectionId)) {
          return { 
            ...recipe, 
            collectionIds: [...collectionIds, collectionId] 
          };
        }
      }
      return recipe;
    });
    
    // Save back to storage
    await AsyncStorage.setItem(STORAGE_KEYS.SAVED_RECIPES, JSON.stringify(updatedRecipes));
    
    // Update collection recipe count
    const collections = await getLocalRecipeCollections();
    const updatedCollections = collections.map(collection => {
      if (collection.id === collectionId) {
        return {
          ...collection,
          recipeCount: (collection.recipeCount || 0) + 1
        };
      }
      return collection;
    });
    
    // Save updated collections
    await AsyncStorage.setItem(STORAGE_KEYS.RECIPE_COLLECTIONS, JSON.stringify(updatedCollections));
    
    return true;
  } catch (error) {
    console.error('Error adding recipe to collection locally:', error);
    return false;
  }
}

/**
 * Remove a recipe from a collection in local storage
 */
async function removeRecipeFromCollectionLocally(
  recipeId: string, 
  collectionId: string
): Promise<boolean> {
  try {
    // Get current recipes
    const recipes = await getLocalSavedRecipes();
    
    // Find and update the recipe's collections
    const updatedRecipes = recipes.map(recipe => {
      if (recipe.id === recipeId) {
        const collectionIds = recipe.collectionIds || [];
        return { 
          ...recipe, 
          collectionIds: collectionIds.filter(id => id !== collectionId)
        };
      }
      return recipe;
    });
    
    // Save back to storage
    await AsyncStorage.setItem(STORAGE_KEYS.SAVED_RECIPES, JSON.stringify(updatedRecipes));
    
    // Update collection recipe count
    const collections = await getLocalRecipeCollections();
    const updatedCollections = collections.map(collection => {
      if (collection.id === collectionId) {
        return {
          ...collection,
          recipeCount: Math.max((collection.recipeCount || 0) - 1, 0)
        };
      }
      return collection;
    });
    
    // Save updated collections
    await AsyncStorage.setItem(STORAGE_KEYS.RECIPE_COLLECTIONS, JSON.stringify(updatedCollections));
    
    return true;
  } catch (error) {
    console.error('Error removing recipe from collection locally:', error);
    return false;
  }
}

/**
 * Generate a UUID for local IDs
 */
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
} 