import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Crypto from 'expo-crypto';
import { Recipe } from '@/services/openai/recipeTypes';

// Constants
const RECIPE_CACHE_KEY_PREFIX = 'recipe_cache_';
const RECIPE_CACHE_INDEX_KEY = 'recipe_cache_index';
const RECIPE_CACHE_EXPIRATION = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds
const MAX_CACHE_ENTRIES = 100; // Maximum number of cached recipes to store

// Cache entry interface
interface RecipeCacheEntry {
  id: string;
  originalFood: {
    name: string;
    nutritionalInfo: {
      calories: number;
      protein: number;
      carbs: number;
      fat: number;
    };
    dietaryPreferences?: string[];
    allergies?: string[];
  };
  recipes: Recipe[];
  timestamp: number;
  expiresAt: number;
}

// Cache index to keep track of all cached items
interface RecipeCacheIndex {
  entries: {
    id: string;
    originalFoodName: string;
    timestamp: number;
    expiresAt: number;
  }[];
}

/**
 * Generate a unique cache ID for a food item
 * @param foodName Original food name
 * @param nutritionalInfo Nutritional information of the original food
 * @returns Promise resolving to a unique cache ID
 */
async function generateCacheId(
  foodName: string,
  nutritionalInfo: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  },
  dietaryPreferences?: string[],
  allergies?: string[]
): Promise<string> {
  // Create a normalized string representation of the food data
  const foodDataString = JSON.stringify({
    name: foodName.toLowerCase().trim(),
    // Round nutritional values to avoid minor differences causing cache misses
    calories: Math.round(nutritionalInfo.calories),
    protein: Math.round(nutritionalInfo.protein),
    carbs: Math.round(nutritionalInfo.carbs),
    fat: Math.round(nutritionalInfo.fat),
    // Sort arrays to ensure consistent ordering
    dietaryPreferences: dietaryPreferences ? [...dietaryPreferences].sort() : undefined,
    allergies: allergies ? [...allergies].sort() : undefined
  });
  
  // Create a hash from the string using SHA-256
  const hash = await Crypto.digestStringAsync(
    Crypto.CryptoDigestAlgorithm.SHA256,
    foodDataString
  );
  
  return hash;
}

/**
 * Load the recipe cache index
 * @returns Promise resolving to the cache index
 */
async function loadCacheIndex(): Promise<RecipeCacheIndex> {
  try {
    const indexJson = await AsyncStorage.getItem(RECIPE_CACHE_INDEX_KEY);
    return indexJson ? JSON.parse(indexJson) : { entries: [] };
  } catch (error) {
    console.error('Error loading recipe cache index:', error);
    return { entries: [] };
  }
}

/**
 * Save the recipe cache index
 * @param index The cache index to save
 */
async function saveCacheIndex(index: RecipeCacheIndex): Promise<void> {
  try {
    await AsyncStorage.setItem(RECIPE_CACHE_INDEX_KEY, JSON.stringify(index));
  } catch (error) {
    console.error('Error saving recipe cache index:', error);
  }
}

/**
 * Add an entry to the cache index
 * @param id Cache entry ID
 * @param originalFoodName Original food name
 * @param timestamp Cache creation timestamp
 * @param expiresAt Cache expiration timestamp
 */
async function addToCacheIndex(
  id: string,
  originalFoodName: string,
  timestamp: number,
  expiresAt: number
): Promise<void> {
  try {
    const index = await loadCacheIndex();
    
    // Remove entry if it already exists
    const existingIndex = index.entries.findIndex(entry => entry.id === id);
    if (existingIndex >= 0) {
      index.entries.splice(existingIndex, 1);
    }
    
    // Add new entry at the beginning (most recent first)
    index.entries.unshift({
      id,
      originalFoodName,
      timestamp,
      expiresAt
    });
    
    // Trim the cache if it exceeds the maximum size
    if (index.entries.length > MAX_CACHE_ENTRIES) {
      // Get the IDs of entries to remove
      const entriesToRemove = index.entries.slice(MAX_CACHE_ENTRIES);
      
      // Remove the oldest entries from AsyncStorage
      await Promise.all(
        entriesToRemove.map(entry => 
          AsyncStorage.removeItem(`${RECIPE_CACHE_KEY_PREFIX}${entry.id}`)
        )
      );
      
      // Trim the index
      index.entries = index.entries.slice(0, MAX_CACHE_ENTRIES);
    }
    
    await saveCacheIndex(index);
  } catch (error) {
    console.error('Error adding to recipe cache index:', error);
  }
}

/**
 * Remove an entry from the cache index
 * @param id Cache entry ID to remove
 */
async function removeFromCacheIndex(id: string): Promise<void> {
  try {
    const index = await loadCacheIndex();
    
    // Remove the entry
    index.entries = index.entries.filter(entry => entry.id !== id);
    
    await saveCacheIndex(index);
  } catch (error) {
    console.error('Error removing from recipe cache index:', error);
  }
}

/**
 * Cache generated recipes for a food item
 * @param originalFoodName Name of the original food
 * @param nutritionalInfo Nutritional information of the original food
 * @param recipes Array of alternative recipes
 * @param dietaryPreferences Optional dietary preferences
 * @param allergies Optional allergies
 * @returns Promise resolving to success status
 */
export async function cacheRecipes(
  originalFoodName: string,
  nutritionalInfo: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  },
  recipes: Recipe[],
  dietaryPreferences?: string[],
  allergies?: string[]
): Promise<boolean> {
  try {
    // Generate cache ID
    const cacheId = await generateCacheId(
      originalFoodName,
      nutritionalInfo,
      dietaryPreferences,
      allergies
    );
    
    const now = Date.now();
    const expiresAt = now + RECIPE_CACHE_EXPIRATION;
    
    // Create cache entry
    const cacheEntry: RecipeCacheEntry = {
      id: cacheId,
      originalFood: {
        name: originalFoodName,
        nutritionalInfo,
        dietaryPreferences,
        allergies
      },
      recipes,
      timestamp: now,
      expiresAt
    };
    
    // Store cache entry
    await AsyncStorage.setItem(
      `${RECIPE_CACHE_KEY_PREFIX}${cacheId}`,
      JSON.stringify(cacheEntry)
    );
    
    // Update cache index
    await addToCacheIndex(cacheId, originalFoodName, now, expiresAt);
    
    console.log(`Cached ${recipes.length} alternative recipes for ${originalFoodName}`);
    return true;
  } catch (error) {
    console.error('Error caching recipes:', error);
    return false;
  }
}

/**
 * Get cached recipes for a food item
 * @param originalFoodName Name of the original food
 * @param nutritionalInfo Nutritional information of the original food
 * @param dietaryPreferences Optional dietary preferences
 * @param allergies Optional allergies
 * @returns Promise resolving to cached recipes or null if not found
 */
export async function getCachedRecipes(
  originalFoodName: string,
  nutritionalInfo: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  },
  dietaryPreferences?: string[],
  allergies?: string[]
): Promise<Recipe[] | null> {
  try {
    // Generate cache ID
    const cacheId = await generateCacheId(
      originalFoodName,
      nutritionalInfo,
      dietaryPreferences,
      allergies
    );
    
    // Retrieve from cache
    const cacheJson = await AsyncStorage.getItem(`${RECIPE_CACHE_KEY_PREFIX}${cacheId}`);
    
    if (!cacheJson) {
      return null;
    }
    
    const cacheEntry: RecipeCacheEntry = JSON.parse(cacheJson);
    
    // Check if cache has expired
    if (Date.now() > cacheEntry.expiresAt) {
      console.log(`Cache expired for ${originalFoodName}, removing`);
      
      // Remove expired entry
      await AsyncStorage.removeItem(`${RECIPE_CACHE_KEY_PREFIX}${cacheId}`);
      await removeFromCacheIndex(cacheId);
      
      return null;
    }
    
    console.log(`Found ${cacheEntry.recipes.length} cached recipes for ${originalFoodName}`);
    return cacheEntry.recipes;
  } catch (error) {
    console.error('Error retrieving cached recipes:', error);
    return null;
  }
}

/**
 * Clear all cached recipes
 * @returns Promise resolving to success status
 */
export async function clearRecipeCache(): Promise<boolean> {
  try {
    // Get all cache entries
    const index = await loadCacheIndex();
    
    // Remove all cache entries
    await Promise.all(
      index.entries.map(entry => 
        AsyncStorage.removeItem(`${RECIPE_CACHE_KEY_PREFIX}${entry.id}`)
      )
    );
    
    // Clear the index
    await AsyncStorage.removeItem(RECIPE_CACHE_INDEX_KEY);
    
    console.log('Recipe cache cleared');
    return true;
  } catch (error) {
    console.error('Error clearing recipe cache:', error);
    return false;
  }
}

/**
 * Clean up expired cache entries
 * @returns Promise resolving to the number of removed entries
 */
export async function cleanupExpiredRecipeCache(): Promise<number> {
  try {
    const index = await loadCacheIndex();
    const now = Date.now();
    
    // Find expired entries
    const expiredEntries = index.entries.filter(entry => entry.expiresAt < now);
    
    // Remove expired entries from storage
    await Promise.all(
      expiredEntries.map(entry => 
        AsyncStorage.removeItem(`${RECIPE_CACHE_KEY_PREFIX}${entry.id}`)
      )
    );
    
    // Update the index
    index.entries = index.entries.filter(entry => entry.expiresAt >= now);
    await saveCacheIndex(index);
    
    console.log(`Removed ${expiredEntries.length} expired recipe cache entries`);
    return expiredEntries.length;
  } catch (error) {
    console.error('Error cleaning up expired recipe cache:', error);
    return 0;
  }
}

/**
 * Get cache statistics
 * @returns Promise resolving to cache statistics
 */
export async function getRecipeCacheStats(): Promise<{
  totalEntries: number;
  oldestEntry: Date | null;
  newestEntry: Date | null;
  totalSizeEstimate: number;
}> {
  try {
    const index = await loadCacheIndex();
    
    // Sort entries by timestamp
    const sortedEntries = [...index.entries].sort((a, b) => a.timestamp - b.timestamp);
    
    return {
      totalEntries: index.entries.length,
      oldestEntry: sortedEntries.length > 0 ? new Date(sortedEntries[0].timestamp) : null,
      newestEntry: sortedEntries.length > 0 ? new Date(sortedEntries[sortedEntries.length - 1].timestamp) : null,
      // Rough estimate of cache size (avg 10KB per recipe × 4 recipes per entry)
      totalSizeEstimate: index.entries.length * 40 * 1024
    };
  } catch (error) {
    console.error('Error getting recipe cache stats:', error);
    return {
      totalEntries: 0,
      oldestEntry: null,
      newestEntry: null,
      totalSizeEstimate: 0
    };
  }
} 