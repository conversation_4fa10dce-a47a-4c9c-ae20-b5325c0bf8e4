/**
 * Recipe Image Cache Firebase Service
 * 
 * Implementation of recipe image caching functionality using Firebase Storage.
 * Handles caching of recipe images for offline use and performance optimization.
 */

import { ref, uploadBytes, getDownloadURL, deleteObject, listAll } from 'firebase/storage';
import { doc, getDoc, setDoc, getDocs, query, collection, where, orderBy, limit, Timestamp, serverTimestamp, deleteDoc } from 'firebase/firestore';
import { storage, firestore } from '@/lib/firebase';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';

// Types
export interface CachedImage {
  id: string;
  userId: string;
  recipeId: string;
  imageUrl: string;
  cachedUrl: string;
  storagePath: string;
  size: number;
  lastAccessed: string;
  expiresAt: string;
  createdAt: string;
  updatedAt: string;
}

// Constants
const CACHE_EXPIRY_DAYS = 30; // Number of days before cache expiry
const MAX_CACHE_SIZE_MB = 200; // Maximum cache size in MB

/**
 * Cache a recipe image for offline use
 * 
 * @param userId User ID
 * @param recipeId Recipe ID
 * @param imageUrl Remote URL of the image to cache
 * @returns Promise with success status and cached image data or error
 */
export async function cacheRecipeImage(
  userId: string,
  recipeId: string,
  imageUrl: string
): Promise<{
  success: boolean;
  data?: CachedImage;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    if (!recipeId) {
      return {
        success: false,
        error: 'Recipe ID is required'
      };
    }

    if (!imageUrl) {
      return {
        success: false,
        error: 'Image URL is required'
      };
    }

    // Generate a unique ID for the cached image
    const cacheId = `${recipeId}_${Date.now()}`;
    const storagePath = `${userId}/recipe_images/${cacheId}.jpg`;

    // Download the image from the URL
    const { uri, size } = await downloadImage(imageUrl);

    // Convert the image to Blob
    const imageBlob = await uriToBlob(uri);

    // Upload to Firebase Storage
    const storageRef = ref(storage, storagePath);
    await uploadBytes(storageRef, imageBlob, {
      contentType: 'image/jpeg',
      customMetadata: {
        userId,
        recipeId,
        originalUrl: imageUrl,
        cachedAt: new Date().toISOString()
      }
    });

    // Get the download URL
    const cachedUrl = await getDownloadURL(storageRef);

    // Calculate expiry date
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + CACHE_EXPIRY_DAYS);

    // Store reference in Firestore
    const cachedImage: CachedImage = {
      id: cacheId,
      userId,
      recipeId,
      imageUrl,
      cachedUrl,
      storagePath,
      size,
      lastAccessed: new Date().toISOString(),
      expiresAt: expiresAt.toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    await setDoc(doc(firestore, 'cached_images', cacheId), {
      userId,
      recipeId,
      imageUrl,
      cachedUrl,
      storagePath,
      size,
      lastAccessed: serverTimestamp(),
      expiresAt: Timestamp.fromDate(expiresAt),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // Clean up cache if needed
    checkCacheSize(userId);

    return {
      success: true,
      data: cachedImage
    };
  } catch (error) {
    console.error('Error caching recipe image:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get a cached image by recipe ID
 * 
 * @param userId User ID
 * @param recipeId Recipe ID
 * @returns Promise with success status and cached image data or error
 */
export async function getCachedRecipeImage(
  userId: string,
  recipeId: string
): Promise<{
  success: boolean;
  data?: CachedImage;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    if (!recipeId) {
      return {
        success: false,
        error: 'Recipe ID is required'
      };
    }

    // Query Firestore
    const imagesQuery = query(
      collection(firestore, 'cached_images'),
      where('userId', '==', userId),
      where('recipeId', '==', recipeId),
      orderBy('createdAt', 'desc'),
      limit(1)
    );

    const querySnapshot = await getDocs(imagesQuery);

    if (querySnapshot.empty) {
      return {
        success: true,
        data: undefined
      };
    }

    const docSnapshot = querySnapshot.docs[0];
    const data = docSnapshot.data();

    // Update last accessed time
    await setDoc(docSnapshot.ref, {
      lastAccessed: serverTimestamp(),
      updatedAt: serverTimestamp()
    }, { merge: true });

    const cachedImage: CachedImage = {
      id: docSnapshot.id,
      userId: data.userId,
      recipeId: data.recipeId,
      imageUrl: data.imageUrl,
      cachedUrl: data.cachedUrl,
      storagePath: data.storagePath,
      size: data.size,
      lastAccessed: new Date().toISOString(),
      expiresAt: data.expiresAt instanceof Timestamp ? 
        data.expiresAt.toDate().toISOString() : data.expiresAt,
      createdAt: data.createdAt instanceof Timestamp ? 
        data.createdAt.toDate().toISOString() : data.createdAt,
      updatedAt: data.updatedAt instanceof Timestamp ? 
        data.updatedAt.toDate().toISOString() : data.updatedAt
    };

    return {
      success: true,
      data: cachedImage
    };
  } catch (error) {
    console.error('Error getting cached recipe image:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Delete a cached image
 * 
 * @param cacheId Cache ID to delete
 * @returns Promise with success status or error
 */
export async function deleteCachedImage(
  cacheId: string
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Get the cached image first
    const cacheRef = doc(firestore, 'cached_images', cacheId);
    const cacheSnap = await getDoc(cacheRef);

    if (!cacheSnap.exists()) {
      return {
        success: false,
        error: 'Cached image not found'
      };
    }

    const data = cacheSnap.data();
    const storagePath = data.storagePath;

    // Delete the image from storage
    const storageRef = ref(storage, storagePath);
    
    try {
      await deleteObject(storageRef);
    } catch (storageError) {
      console.warn('Error deleting cached image from storage, continuing:', storageError);
    }

    // Delete the document record
    await deleteDoc(cacheRef);

    return {
      success: true
    };
  } catch (error) {
    console.error('Error deleting cached image:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Purge expired cached images for a user
 * 
 * @param userId User ID
 * @returns Promise with success status and count of purged images or error
 */
export async function purgeExpiredCache(
  userId: string
): Promise<{
  success: boolean;
  purgedCount?: number;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    const now = new Date();
    
    // Query for expired images
    const expiredQuery = query(
      collection(firestore, 'cached_images'),
      where('userId', '==', userId),
      where('expiresAt', '<', Timestamp.fromDate(now))
    );

    const querySnapshot = await getDocs(expiredQuery);

    if (querySnapshot.empty) {
      return {
        success: true,
        purgedCount: 0
      };
    }

    // Delete each expired image
    let purgedCount = 0;
    for (const docSnapshot of querySnapshot.docs) {
      const data = docSnapshot.data();
      const storagePath = data.storagePath;

      // Delete from storage
      const storageRef = ref(storage, storagePath);
      
      try {
        await deleteObject(storageRef);
      } catch (storageError) {
        console.warn('Error deleting expired image from storage:', storageError);
      }

      // Delete the document
      await deleteDoc(docSnapshot.ref);
      purgedCount++;
    }

    return {
      success: true,
      purgedCount
    };
  } catch (error) {
    console.error('Error purging expired cache:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Clear all cached images for a user
 * 
 * @param userId User ID
 * @returns Promise with success status and count of cleared images or error
 */
export async function clearRecipeImageCache(
  userId: string
): Promise<{
  success: boolean;
  clearedCount?: number;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    // Query all cached images for this user
    const cacheQuery = query(
      collection(firestore, 'cached_images'),
      where('userId', '==', userId)
    );

    const querySnapshot = await getDocs(cacheQuery);

    if (querySnapshot.empty) {
      return {
        success: true,
        clearedCount: 0
      };
    }

    // Delete each cached image
    let clearedCount = 0;
    for (const docSnapshot of querySnapshot.docs) {
      const data = docSnapshot.data();
      const storagePath = data.storagePath;

      // Delete from storage
      const storageRef = ref(storage, storagePath);
      
      try {
        await deleteObject(storageRef);
      } catch (storageError) {
        console.warn('Error deleting image from storage:', storageError);
      }

      // Delete the document
      await deleteDoc(docSnapshot.ref);
      clearedCount++;
    }

    return {
      success: true,
      clearedCount
    };
  } catch (error) {
    console.error('Error clearing recipe image cache:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get the current cache size for a user
 * 
 * @param userId User ID
 * @returns Promise with success status and cache size in bytes or error
 */
export async function getCacheSize(
  userId: string
): Promise<{
  success: boolean;
  size?: number;
  itemCount?: number;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    // Query all cached images for this user
    const cacheQuery = query(
      collection(firestore, 'cached_images'),
      where('userId', '==', userId)
    );

    const querySnapshot = await getDocs(cacheQuery);

    if (querySnapshot.empty) {
      return {
        success: true,
        size: 0,
        itemCount: 0
      };
    }

    // Calculate total size
    let totalSize = 0;
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      totalSize += data.size || 0;
    });

    return {
      success: true,
      size: totalSize,
      itemCount: querySnapshot.size
    };
  } catch (error) {
    console.error('Error getting cache size:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Helper function to download an image and get its size
 */
async function downloadImage(url: string): Promise<{ uri: string; size: number }> {
  // Create a temporary file path
  const fileUri = FileSystem.cacheDirectory + 'temp_' + Date.now() + '.jpg';
  
  // Download the file
  const downloadResult = await FileSystem.downloadAsync(url, fileUri);
  
  return {
    uri: downloadResult.uri,
    size: downloadResult.headers['content-length'] ? 
      parseInt(downloadResult.headers['content-length']) : 
      await FileSystem.getInfoAsync(fileUri).then(info => (info as any).size || 0)
  };
}

/**
 * Helper function to convert a URI to a Blob
 */
async function uriToBlob(uri: string): Promise<Blob> {
  if (Platform.OS === 'web') {
    const response = await fetch(uri);
    const blob = await response.blob();
    return blob;
  } else {
    const fileString = await FileSystem.readAsStringAsync(uri, {
      encoding: FileSystem.EncodingType.Base64,
    });
    const base64 = `data:image/jpeg;base64,${fileString}`;
    const response = await fetch(base64);
    const blob = await response.blob();
    return blob;
  }
}

/**
 * Check if cache size exceeds limit and clean if necessary
 * Uses a Least Recently Used (LRU) strategy for cache eviction
 */
async function checkCacheSize(userId: string): Promise<void> {
  try {
    // Get current cache size
    const cacheResult = await getCacheSize(userId);
    
    if (!cacheResult.success || !cacheResult.size) {
      return;
    }
    
    const cacheSizeMB = cacheResult.size / (1024 * 1024);
    
    // If cache size is below limit, we're done
    if (cacheSizeMB < MAX_CACHE_SIZE_MB) {
      return;
    }
    
    // Otherwise, fetch all cached images sorted by last accessed time
    const cacheQuery = query(
      collection(firestore, 'cached_images'),
      where('userId', '==', userId),
      orderBy('lastAccessed', 'asc')
    );
    
    const querySnapshot = await getDocs(cacheQuery);
    
    // Calculate how much we need to delete
    const excessMB = cacheSizeMB - (MAX_CACHE_SIZE_MB * 0.8); // Remove enough to get to 80% of max
    const excessBytes = excessMB * 1024 * 1024;
    
    let bytesRemoved = 0;
    
    // Delete oldest accessed images until we've freed up enough space
    for (const docSnapshot of querySnapshot.docs) {
      if (bytesRemoved >= excessBytes) {
        break;
      }
      
      const data = docSnapshot.data();
      
      // Delete from storage
      try {
        const storageRef = ref(storage, data.storagePath);
        await deleteObject(storageRef);
      } catch (storageError) {
        console.warn('Error deleting image during cache cleanup:', storageError);
      }
      
      // Delete the document
      await deleteDoc(docSnapshot.ref);
      
      bytesRemoved += data.size || 0;
    }
    
    console.log(`Cache cleaned: removed ${bytesRemoved / (1024 * 1024)}MB`);
  } catch (error) {
    console.error('Error checking cache size:', error);
  }
} 