import * as FirebaseCacheService from './recipeImageCacheFirebaseService';
export type { CachedImage } from './recipeImageCacheFirebaseService';


  const getCachedRecipeImage = async (recipeId: string) => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      if (useFirebase) {
        // Use Firebase implementation
        return await FirebaseCacheService.getCachedRecipeImage(user.id, recipeId);
      } else {
        
        // For now, return undefined indicating no cache
        return {
          success: true,
          data: undefined
        };
      }
    } catch (error) {
      console.error('Error getting cached recipe image:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Delete a cached image
   */
  const deleteCachedImage = async (cacheId: string) => {
    try {
      if (useFirebase) {
        // Use Firebase implementation
        return await FirebaseCacheService.deleteCachedImage(cacheId);
      } else {
        
        // For now, just return success
        return {
          success: true
        };
      }
    } catch (error) {
      console.error('Error deleting cached image:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Purge expired cache items
   */
  const purgeExpiredCache = async () => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      if (useFirebase) {
        // Use Firebase implementation
        return await FirebaseCacheService.purgeExpiredCache(user.id);
      } else {
        
        // For now, just return success with 0 purged
        return {
          success: true,
          purgedCount: 0
        };
      }
    } catch (error) {
      console.error('Error purging expired cache:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Clear all cached images
   */
  const clearRecipeImageCache = async () => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      if (useFirebase) {
        // Use Firebase implementation
        return await FirebaseCacheService.clearRecipeImageCache(user.id);
      } else {
        
        // For now, just return success with 0 cleared
        return {
          success: true,
          clearedCount: 0
        };
      }
    } catch (error) {
      console.error('Error clearing recipe image cache:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Get current cache size
   */
  const getCacheSize = async () => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      if (useFirebase) {
        // Use Firebase implementation
        return await FirebaseCacheService.getCacheSize(user.id);
      } else {
        
        // For now, just return 0 size
        return {
          success: true,
          size: 0,
          itemCount: 0
        };
      }
    } catch (error) {
      console.error('Error getting cache size:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Check if an image URL is already cached
   */
  const isImageCached = async (recipeId: string) => {
    try {
      const cachedImage = await getCachedRecipeImage(recipeId);
      return {
        success: true,
        isCached: Boolean(cachedImage.data),
        data: cachedImage.data
      };
    } catch (error) {
      console.error('Error checking if image is cached:', error);
      return {
        success: false,
        isCached: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  return {
    cacheRecipeImage,
    getCachedRecipeImage,
    deleteCachedImage,
    purgeExpiredCache,
    clearRecipeImageCache,
    getCacheSize,
    isImageCached
  };
}
