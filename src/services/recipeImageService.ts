import { useDatabaseType } from '@/contexts/DatabaseContext';
import { httpsCallable, getFunctions } from 'firebase/functions';


// Types
export interface RecipeImageRequest {
  recipeId?: string;
  recipeName: string;
  ingredients?: string[];
  cuisine?: string;
  mealType?: string;
  style?: 'realistic' | 'illustration' | 'overhead' | 'closeup';
  customPrompt?: string;
  saveResult?: boolean;
}

export interface RecipeImageResult {
  id: string;
  recipeId?: string;
  imageUrl: string;
  thumbnailUrl?: string;
  prompt: string;
  style: string;
  userId: string;
  createdAt: string;
}

/**
 * Service for recipe image generation using Firebase
 */
export function useRecipeImageService() {
  const { db  } = useDatabaseType();

  /**
   * Generate an image for a recipe
   */
  const generateRecipeImage = async (
    request: RecipeImageRequest
  ): Promise<RecipeImageResult> => {
    try {
      // Use Firebase Cloud Function
      const functions = getFunctions();
      const generateImageFn = httpsCallable<
        RecipeImageRequest,
        RecipeImageResult
      >(functions, 'generateRecipeImage');

      const result = await generateImageFn(request);
      return result.data;
    } catch (error) {
      console.error('Error in recipeImageService:', error);
      throw error;
    }
  };

  /**
   * Get recipe images generated by a user
   */
  const getUserRecipeImages = async (userId: string, limit = 10): Promise<RecipeImageResult[]> => {
    try {
      const collection = 'recipe_images';
      const filters = [
        { field: 'userId', operator: '==', value: userId }
      ];
      
      const results = await db.queryDocuments(collection, filters);
      
      // Apply the limit after fetching
      return (results as RecipeImageResult[]).slice(0, limit);
    } catch (error) {
      console.error('Error getting recipe images:', error);
      throw error;
    }
  };

  /**
   * Get recipe images for a specific recipe
   */
  const getRecipeImages = async (recipeId: string): Promise<RecipeImageResult[]> => {
    try {
      const collection = 'recipe_images';
      const filters = [
        { field: 'recipeId', operator: '==', value: recipeId }
      ];
      
      const results = await db.queryDocuments(collection, filters);
      return results as RecipeImageResult[];
    } catch (error) {
      console.error(`Error getting images for recipe ${recipeId}:`, error);
      throw error;
    }
  };

  /**
   * Delete a recipe image
   */
  const deleteRecipeImage = async (imageId: string): Promise<void> => {
    try {
      const collection = 'recipe_images';
      await db.deleteDocument(collection, imageId);
    } catch (error) {
      console.error(`Error deleting recipe image ${imageId}:`, error);
      throw error;
    }
  };

  return {
    generateRecipeImage,
    getUserRecipeImages,
    getRecipeImages,
    deleteRecipeImage
  };
} 