import { Recipe } from '@/types/food';
import { getNutritionForDish } from './nutritionService';
import { analyzeFood } from './openai/secureApiClient';

interface ImportedRecipe {
  name: string;
  description?: string;
  ingredients: {
    name: string;
    amount: string;
    unit: string;
  }[];
  instructions: string[];
  servings: number;
  prepTime?: number;
  cookTime?: number;
  totalTime?: number;
  nutritionFacts?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber?: number;
    sugar?: number;
    sodium?: number;
  };
  sourceUrl: string;
  imageUrl?: string;
  tags?: string[];
}

/**
 * Imports a recipe from a URL
 * @param url The URL of the recipe to import
 * @returns The imported recipe data
 */
export async function importRecipeFromUrl(url: string): Promise<ImportedRecipe | null> {
  try {
    // First attempt to fetch the recipe using standard fetch
    const htmlContent = await fetchRecipeHtml(url);
    
    if (!htmlContent) {
      console.log('Failed to fetch HTML content from URL');
      return null;
    }
    
    // Use Firebase Functions to parse the recipe data from the HTML content
    console.log('Using Firebase Functions for recipe parsing');
    return await parseRecipeWithOpenAI(htmlContent, url);
    
    // Fallback to simple HTML parsing if OpenAI is not available
    return await parseRecipeFromHtml(htmlContent, url);
  } catch (error) {
    console.error('Error importing recipe:', error);
    return null;
  }
}

/**
 * Fetches the HTML content from a URL
 * @param url The URL to fetch
 * @returns The HTML content as a string
 */
async function fetchRecipeHtml(url: string): Promise<string | null> {
  try {
    // Check if the URL is valid
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }
    
    // Try to fetch the URL directly
    try {
      const response = await fetch(url);
      if (response.ok) {
        return await response.text();
      }
    } catch (directFetchError) {
      console.log('Direct fetch failed, trying proxy:', directFetchError);
    }
    
    // If direct fetch fails, try using a CORS proxy
    const corsProxyUrl = `https://corsproxy.io/?${encodeURIComponent(url)}`;
    const proxyResponse = await fetch(corsProxyUrl);
    
    if (!proxyResponse.ok) {
      throw new Error(`Failed to fetch URL content: ${proxyResponse.status} ${proxyResponse.statusText}`);
    }
    
    return await proxyResponse.text();
  } catch (error) {
    console.error('Error fetching recipe HTML:', error);
    return null;
  }
}

/**
 * Parses a recipe from HTML content using OpenAI
 * @param htmlContent The HTML content to parse
 * @param sourceUrl The source URL of the recipe
 * @returns The parsed recipe data
 */
async function parseRecipeWithOpenAI(htmlContent: string, sourceUrl: string): Promise<ImportedRecipe | null> {
  try {
    // We need to limit the HTML content to avoid token limits
    const truncatedHtml = htmlContent.substring(0, 100000);
    
    // Use a dummy image for text-only analysis
    const dummyImageUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    
    const systemPrompt = `You are a helpful assistant that extracts structured recipe data from HTML content. 
Given the HTML of a recipe page, extract the following information in a structured JSON format:
1. Recipe name
2. Description (brief summary of the recipe)
3. Ingredients list with amounts and units separated
4. Step-by-step instructions
5. Number of servings
6. Preparation time in minutes
7. Cooking time in minutes
8. Nutrition facts if available
9. Image URL if found

The output should strictly follow this JSON format:
{
  "name": "Recipe Name",
  "description": "Brief description of the recipe",
  "ingredients": [
    {"name": "Ingredient 1", "amount": "1", "unit": "cup"},
    {"name": "Ingredient 2", "amount": "2", "unit": "tablespoons"}
  ],
  "instructions": [
    "Step 1 instruction",
    "Step 2 instruction"
  ],
  "servings": 4,
  "prepTime": 15,
  "cookTime": 30,
  "nutritionFacts": {
    "calories": 350,
    "protein": 20,
    "carbs": 30,
    "fat": 15,
    "fiber": 5,
    "sugar": 10,
    "sodium": 500
  },
  "imageUrl": "https://example.com/image.jpg"
}

If any information is not found, set the value to null or omit the field. Do your best to intelligently parse the HTML even if it's not well structured.`;

    const userPrompt = `Extract the recipe data from the following HTML content from URL: ${sourceUrl}\n\n${truncatedHtml}`;
    
    // Combine system and user prompts for the Firebase function
    const combinedPrompt = `${systemPrompt}

${userPrompt}

IMPORTANT: Return your response as a valid JSON object matching the format described above.`;

    const response = await analyzeFood(dummyImageUrl, combinedPrompt);
    
    if (!response.success) {
      throw new Error('Firebase Functions recipe analysis failed');
    }
    
    // Parse the recipe data from the response
    let recipeData;
    if (response.description) {
      try {
        // Try to parse from description if it contains JSON
        recipeData = JSON.parse(response.description);
      } catch (e) {
        console.error('Failed to parse JSON from response description');
        throw new Error('Invalid JSON in response');
      }
    } else {
      throw new Error('No recipe data in response');
    }
    
    // If nutrition facts are not available, calculate them from ingredients
    if (!recipeData.nutritionFacts || !recipeData.nutritionFacts.calories) {
      console.log('Nutrition facts not found, calculating from ingredients');
      const ingredientNames = recipeData.ingredients.map((ing: any) => ing.name);
      const nutritionData = await getNutritionForDish(recipeData.name, ingredientNames);
      
      recipeData.nutritionFacts = {
        calories: nutritionData.calories,
        protein: nutritionData.protein,
        carbs: nutritionData.carbs,
        fat: nutritionData.fat
      };
    }
    
    // Add the source URL to the recipe data
    recipeData.sourceUrl = sourceUrl;
    
    return recipeData as ImportedRecipe;
  } catch (error) {
    console.error('Error parsing recipe via Firebase Functions:', error);
    return null;
  }
}

/**
 * Parses a recipe from HTML content using simple HTML parsing
 * @param htmlContent The HTML content to parse
 * @param sourceUrl The source URL of the recipe
 * @returns The parsed recipe data
 */
async function parseRecipeFromHtml(htmlContent: string, sourceUrl: string): Promise<ImportedRecipe | null> {
  try {
    // This is a very simplified parser as a fallback
    // In a real application, you would use a more sophisticated HTML parser
    
    // Extract title (likely to be in a h1 tag)
    const titleMatch = htmlContent.match(/<h1[^>]*>(.*?)<\/h1>/i);
    const name = titleMatch ? cleanHtmlText(titleMatch[1]) : 'Imported Recipe';
    
    // Try to find ingredients (look for common recipe patterns)
    let ingredients: {name: string, amount: string, unit: string}[] = [];
    
    // Look for ingredient lists in li elements within common ingredient list containers
    const ingredientListsRegex = /<ul[^>]*class="[^"]*ingredients[^"]*"[^>]*>(.*?)<\/ul>|<div[^>]*class="[^"]*ingredients[^"]*"[^>]*>(.*?)<\/div>/igs;
    let ingredientListMatch;
    
    while ((ingredientListMatch = ingredientListsRegex.exec(htmlContent)) !== null) {
      const ingredientListHtml = ingredientListMatch[1] || ingredientListMatch[2];
      const ingredientItemsRegex = /<li[^>]*>(.*?)<\/li>/ig;
      let ingredientItemMatch;
      
      while ((ingredientItemMatch = ingredientItemsRegex.exec(ingredientListHtml)) !== null) {
        const ingredientText = cleanHtmlText(ingredientItemMatch[1]);
        
        // Try to separate amount, unit, and name
        // This is a simplified approach - in real implementation use more sophisticated parsing
        const parts = ingredientText.trim().split(' ');
        let amount = '';
        let unit = '';
        let name = '';
        
        if (parts.length > 0) {
          // First part is usually the amount
          if (/^\d+(\.\d+)?(\s*-\s*\d+(\.\d+)?)?$/.test(parts[0]) || /^\d+\/\d+$/.test(parts[0])) {
            amount = parts[0];
            parts.shift();
          }
          
          // Second part might be the unit
          if (parts.length > 0) {
            const commonUnits = ['cup', 'cups', 'tablespoon', 'tablespoons', 'tbsp', 'teaspoon', 'teaspoons', 'tsp', 'ounce', 'ounces', 'oz', 'pound', 'pounds', 'lb', 'gram', 'grams', 'g', 'kilogram', 'kilograms', 'kg', 'ml', 'milliliter', 'milliliters', 'l', 'liter', 'liters', 'pinch', 'dash'];
            if (commonUnits.includes(parts[0].toLowerCase())) {
              unit = parts[0];
              parts.shift();
            }
          }
          
          // The rest is the ingredient name
          name = parts.join(' ');
        } else {
          name = ingredientText;
        }
        
        ingredients.push({ name, amount, unit });
      }
    }
    
    // If we couldn't find ingredients in a list, try a more generic approach
    if (ingredients.length === 0) {
      // Look for text that might be ingredients
      const potentialIngredients = htmlContent.match(/\d+\s+(cup|tablespoon|teaspoon|ounce|pound|gram)[s]?\s+[a-zA-Z\s,]+/g);
      if (potentialIngredients) {
        ingredients = potentialIngredients.map(ing => {
          const parts = ing.trim().split(/\s+/);
          let amount = '';
          let unit = '';
          
          if (/^\d+(\.\d+)?$/.test(parts[0]) || /^\d+\/\d+$/.test(parts[0])) {
            amount = parts[0];
            parts.shift();
          }
          
          if (parts.length > 0) {
            const commonUnits = ['cup', 'cups', 'tablespoon', 'tablespoons', 'tbsp', 'teaspoon', 'teaspoons', 'tsp', 'ounce', 'ounces', 'oz', 'pound', 'pounds', 'lb', 'gram', 'grams', 'g', 'kilogram', 'kilograms', 'kg', 'ml', 'milliliter', 'milliliters', 'l', 'liter', 'liters', 'pinch', 'dash'];
            if (commonUnits.includes(parts[0].toLowerCase())) {
              unit = parts[0];
              parts.shift();
            }
          }
          
          const name = parts.join(' ');
          
          return { name, amount, unit };
        });
      }
    }
    
    // Try to find instructions
    let instructions: string[] = [];
    
    // Look for ordered lists that might contain instructions
    const instructionsListRegex = /<ol[^>]*>(.*?)<\/ol>/igs;
    let instructionsListMatch;
    
    while ((instructionsListMatch = instructionsListRegex.exec(htmlContent)) !== null) {
      const instructionsHtml = instructionsListMatch[1];
      const instructionItemsRegex = /<li[^>]*>(.*?)<\/li>/ig;
      let instructionItemMatch;
      
      while ((instructionItemMatch = instructionItemsRegex.exec(instructionsHtml)) !== null) {
        instructions.push(cleanHtmlText(instructionItemMatch[1]));
      }
    }
    
    // If we couldn't find instructions in an ordered list, try a more generic approach
    if (instructions.length === 0) {
      // Look for paragraphs that might be instructions
      const instructionParagraphsRegex = /<p[^>]*>(.*?)<\/p>/igs;
      let instructionParagraphMatch;
      
      while ((instructionParagraphMatch = instructionParagraphsRegex.exec(htmlContent)) !== null) {
        const paragraph = cleanHtmlText(instructionParagraphMatch[1]);
        if (paragraph.length > 20 && paragraph.match(/^\d+\.\s+|^Step\s+\d+:\s+/i)) {
          instructions.push(paragraph);
        }
      }
    }
    
    // Try to find servings
    let servings = 4; // Default to 4 servings
    
    const servingsRegex = /serves\s+(\d+)|servings:\s*(\d+)|yield:\s*(\d+)/i;
    const servingsMatch = htmlContent.match(servingsRegex);
    if (servingsMatch) {
      servings = parseInt(servingsMatch[1] || servingsMatch[2] || servingsMatch[3], 10);
    }
    
    // Try to find prep and cook times
    let prepTime: number | undefined;
    let cookTime: number | undefined;
    
    const prepTimeRegex = /prep\s+time:?\s*(\d+)\s*(min|minute|minutes)/i;
    const prepTimeMatch = htmlContent.match(prepTimeRegex);
    if (prepTimeMatch) {
      prepTime = parseInt(prepTimeMatch[1], 10);
    }
    
    const cookTimeRegex = /cook\s+time:?\s*(\d+)\s*(min|minute|minutes)/i;
    const cookTimeMatch = htmlContent.match(cookTimeRegex);
    if (cookTimeMatch) {
      cookTime = parseInt(cookTimeMatch[1], 10);
    }
    
    // Try to find an image
    let imageUrl: string | undefined;
    
    const imageRegex = /<img[^>]*src="([^"]*)"[^>]*>/i;
    const imageMatch = htmlContent.match(imageRegex);
    if (imageMatch) {
      imageUrl = imageMatch[1];
      
      // Make sure the URL is absolute
      if (imageUrl && !imageUrl.startsWith('http://') && !imageUrl.startsWith('https://')) {
        const urlParts = new URL(sourceUrl);
        imageUrl = `${urlParts.protocol}//${urlParts.host}${imageUrl.startsWith('/') ? '' : '/'}${imageUrl}`;
      }
    }
    
    // Calculate nutrition using our service based on ingredients
    const ingredientNames = ingredients.map(ing => ing.name);
    const nutritionData = await getNutritionForDish(name, ingredientNames);
    
    // Create and return the recipe
    const recipe: ImportedRecipe = {
      name,
      ingredients,
      instructions,
      servings,
      prepTime,
      cookTime,
      sourceUrl,
      imageUrl,
      nutritionFacts: {
        calories: nutritionData.calories,
        protein: nutritionData.protein,
        carbs: nutritionData.carbs,
        fat: nutritionData.fat
      }
    };
    
    return recipe;
  } catch (error) {
    console.error('Error parsing recipe from HTML:', error);
    return null;
  }
}

/**
 * Cleans HTML text by removing tags and entities
 * @param html The HTML text to clean
 * @returns The cleaned text
 */
function cleanHtmlText(html: string): string {
  // Remove HTML tags
  const withoutTags = html.replace(/<[^>]*>/g, '');
  
  // Replace HTML entities
  return withoutTags
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&nbsp;/g, ' ')
    .trim();
}

/**
 * Saves an imported recipe to the user's saved recipes
 * @param recipe The recipe to save
 * @returns The saved recipe with an ID
 */
export async function saveImportedRecipe(recipe: ImportedRecipe): Promise<Recipe> {
  // In a real app, this would save to a database
  // For now, we'll just return a mock recipe with an ID
  
  // Generate a random ID
  const id = Math.random().toString(36).substring(2, 15);
  
  const savedRecipe: Recipe = {
    id,
    name: recipe.name,
    description: recipe.description || `Imported from ${recipe.sourceUrl}`,
    imageUrl: recipe.imageUrl || 'https://via.placeholder.com/300x200?text=Recipe',
    prepTime: recipe.prepTime || 0,
    cookTime: recipe.cookTime || 0,
    servings: recipe.servings,
    ingredients: recipe.ingredients.map(ingredient => ({
      id: Math.random().toString(36).substring(2, 15),
      name: ingredient.name,
      calories: 0,
      protein: 0,
      carbs: 0,
      fat: 0
    })),
    instructions: recipe.instructions,
    totalCalories: recipe.nutritionFacts?.calories || 0,
    totalProtein: recipe.nutritionFacts?.protein || 0,
    totalCarbs: recipe.nutritionFacts?.carbs || 0,
    totalFat: recipe.nutritionFacts?.fat || 0,
    tags: recipe.tags || ['imported']
  };
  
  console.log('Saved imported recipe:', savedRecipe);
  
  return savedRecipe;
} 