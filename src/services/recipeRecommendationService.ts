import { getUserProfile, DietaryPreference, NutritionGoalType } from './nutritionGoalService';
import Constants from 'expo-constants';
import { SPOONACULAR_API_KEY } from '@/utils/config';

export interface Recipe {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  prepTime: number; // in minutes
  cookTime: number; // in minutes
  servings: number;
  difficulty: 'easy' | 'medium' | 'hard';
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  tags: string[];
  ingredients: {
    name: string;
    amount: string;
    unit: string;
  }[];
  instructions: string[];
  nutritionFacts: {
    calories: number;
    protein: number; // in grams
    carbs: number; // in grams
    fat: number; // in grams
    fiber: number; // in grams
    sugar: number; // in grams
    sodium: number; // in mg
  };
  sourceUrl?: string;
  isFavorite?: boolean;
}

export interface RecipeSearchFilters {
  query?: string;
  mealType?: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  maxPrepTime?: number;
  dietaryPreference?: DietaryPreference;
  includeIngredients?: string[];
  excludeIngredients?: string[];
  calorieRange?: { min: number; max: number };
  proteinRange?: { min: number; max: number };
  carbsRange?: { min: number; max: number };
  fatRange?: { min: number; max: number };
  tags?: string[];
}

interface SpoonacularRecipe {
  id: number;
  title: string;
  image: string;
  imageType: string;
  summary: string;
  readyInMinutes: number;
  servings: number;
  analyzedInstructions: {
    steps: {
      number: number;
      step: string;
    }[];
  }[];
  extendedIngredients: {
    id: number;
    aisle: string;
    name: string;
    original: string;
    amount: number;
    unit: string;
  }[];
  dishTypes: string[];
  diets: string[];
  nutrition?: {
    nutrients: {
      name: string;
      amount: number;
      unit: string;
    }[];
  };
  sourceUrl?: string;
}

/**
 * Map meal type to Spoonacular meal type
 */
function mapMealType(mealType?: 'breakfast' | 'lunch' | 'dinner' | 'snack'): string {
  switch (mealType) {
    case 'breakfast': return 'breakfast';
    case 'lunch': return 'lunch';
    case 'dinner': return 'main course';
    case 'snack': return 'snack';
    default: return '';
  }
}

/**
 * Map dietary preference to Spoonacular diet
 */
function mapDietaryPreference(dietaryPreference?: DietaryPreference): string {
  switch (dietaryPreference) {
    case DietaryPreference.VEGETARIAN: return 'vegetarian';
    case DietaryPreference.VEGAN: return 'vegan';
    case DietaryPreference.PESCATARIAN: return 'pescetarian';
    case DietaryPreference.KETO: return 'ketogenic';
    case DietaryPreference.PALEO: return 'paleo';
    case DietaryPreference.GLUTEN_FREE: return 'gluten free';
    case DietaryPreference.DAIRY_FREE: return 'dairy free';
    default: return '';
  }
}

/**
 * Estimate recipe difficulty based on readyInMinutes and ingredients count
 */
function estimateDifficulty(recipe: SpoonacularRecipe): 'easy' | 'medium' | 'hard' {
  const totalTime = recipe.readyInMinutes;
  const ingredientCount = recipe.extendedIngredients.length;
  
  if (totalTime <= 20 && ingredientCount <= 5) return 'easy';
  if (totalTime >= 45 || ingredientCount >= 12) return 'hard';
  return 'medium';
}

/**
 * Extract tags from Spoonacular recipe
 */
function extractTags(recipe: SpoonacularRecipe): string[] {
  const tags: string[] = [];
  
  // Add diets as tags
  if (recipe.diets && recipe.diets.length > 0) {
    tags.push(...recipe.diets.map(diet => diet.replace(' ', '-')));
  }
  
  // Add dish types as tags
  if (recipe.dishTypes && recipe.dishTypes.length > 0) {
    tags.push(...recipe.dishTypes.map(type => type.replace(' ', '-')));
  }
  
  // Add nutrition-based tags
  if (recipe.nutrition && recipe.nutrition.nutrients) {
    const protein = recipe.nutrition.nutrients.find(n => n.name === 'Protein');
    const carbs = recipe.nutrition.nutrients.find(n => n.name === 'Carbohydrates');
    const fat = recipe.nutrition.nutrients.find(n => n.name === 'Fat');
    
    if (protein && protein.amount >= 20) tags.push('high-protein');
    if (carbs && carbs.amount <= 20) tags.push('low-carb');
    if (fat && fat.amount <= 10) tags.push('low-fat');
  }
  
  return Array.from(new Set(tags)); // Remove duplicates
}

/**
 * Extract nutrition facts from Spoonacular recipe
 */
function extractNutritionFacts(recipe: SpoonacularRecipe): Recipe['nutritionFacts'] {
  const defaultNutrition = {
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    fiber: 0,
    sugar: 0,
    sodium: 0,
  };
  
  if (!recipe.nutrition || !recipe.nutrition.nutrients) {
    return defaultNutrition;
  }
  
  const nutrients = recipe.nutrition.nutrients;
  
  return {
    calories: nutrients.find(n => n.name === 'Calories')?.amount || 0,
    protein: nutrients.find(n => n.name === 'Protein')?.amount || 0,
    carbs: nutrients.find(n => n.name === 'Carbohydrates')?.amount || 0,
    fat: nutrients.find(n => n.name === 'Fat')?.amount || 0,
    fiber: nutrients.find(n => n.name === 'Fiber')?.amount || 0,
    sugar: nutrients.find(n => n.name === 'Sugar')?.amount || 0,
    sodium: nutrients.find(n => n.name === 'Sodium')?.amount || 0,
  };
}

/**
 * Convert Spoonacular recipe to our Recipe format
 */
function convertSpoonacularRecipe(recipe: any, mealType?: 'breakfast' | 'lunch' | 'dinner' | 'snack'): Recipe {
  // Determine prepTime and cookTime (estimated as 1/3 and 2/3 of total time)
  const totalTime = recipe.readyInMinutes || 30;
  const prepTime = Math.round(totalTime / 3);
  const cookTime = totalTime - prepTime;
  
  // Determine difficulty
  let difficulty: 'easy' | 'medium' | 'hard' = 'medium';
  if (totalTime <= 20 && (recipe.extendedIngredients?.length || 0) <= 5) difficulty = 'easy';
  if (totalTime >= 45 || (recipe.extendedIngredients?.length || 0) >= 12) difficulty = 'hard';
  
  // Determine meal type if not specified
  let detectedMealType: 'breakfast' | 'lunch' | 'dinner' | 'snack' = mealType || 'dinner';
  if (!mealType && recipe.dishTypes) {
    if (recipe.dishTypes.includes('breakfast')) detectedMealType = 'breakfast';
    else if (recipe.dishTypes.includes('lunch')) detectedMealType = 'lunch';
    else if (recipe.dishTypes.includes('main course') || recipe.dishTypes.includes('dinner')) detectedMealType = 'dinner';
    else if (recipe.dishTypes.includes('snack')) detectedMealType = 'snack';
  }
  
  // Extract tags
  const tags: string[] = [];
  if (recipe.diets) tags.push(...recipe.diets.map((d: string) => d.replace(' ', '-')));
  if (recipe.dishTypes) tags.push(...recipe.dishTypes.map((d: string) => d.replace(' ', '-')));
  
  // Extract nutritional info
  const nutritionFacts = {
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    fiber: 0,
    sugar: 0,
    sodium: 0,
  };
  
  if (recipe.nutrition && recipe.nutrition.nutrients) {
    for (const nutrient of recipe.nutrition.nutrients) {
      if (nutrient.name === 'Calories') nutritionFacts.calories = nutrient.amount;
      else if (nutrient.name === 'Protein') nutritionFacts.protein = nutrient.amount;
      else if (nutrient.name === 'Carbohydrates') nutritionFacts.carbs = nutrient.amount;
      else if (nutrient.name === 'Fat') nutritionFacts.fat = nutrient.amount;
      else if (nutrient.name === 'Fiber') nutritionFacts.fiber = nutrient.amount;
      else if (nutrient.name === 'Sugar') nutritionFacts.sugar = nutrient.amount;
      else if (nutrient.name === 'Sodium') nutritionFacts.sodium = nutrient.amount;
    }
    
    // Add nutrition-based tags
    if (nutritionFacts.protein >= 20) tags.push('high-protein');
    if (nutritionFacts.carbs <= 20) tags.push('low-carb');
    if (nutritionFacts.fat <= 10) tags.push('low-fat');
  }
  
  // Extract ingredients
  const ingredients = recipe.extendedIngredients?.map((i: any) => ({
    name: i.name || i.originalName || '',
    amount: i.amount?.toString() || '',
    unit: i.unit || '',
  })) || [];
  
  // Extract instructions
  let instructions: string[] = [];
  if (recipe.analyzedInstructions && recipe.analyzedInstructions.length > 0) {
    instructions = recipe.analyzedInstructions[0].steps.map((s: any) => s.step);
  } else if (recipe.instructions) {
    // Split by periods or numbers if no structured instructions
    instructions = recipe.instructions
      .replace(/<[^>]*>/g, '')
      .split(/\.\s+|\d+\.\s+/)
      .filter((s: string) => s.trim().length > 0);
  }
  
  return {
    id: recipe.id.toString(),
    name: recipe.title,
    description: (recipe.summary || '').replace(/<[^>]*>/g, '').substring(0, 200) + '...',
    imageUrl: recipe.image || '',
    prepTime,
    cookTime,
    servings: recipe.servings || 4,
    difficulty,
    mealType: detectedMealType,
    tags: Array.from(new Set(tags)), // Remove duplicates
    ingredients,
    instructions,
    nutritionFacts,
    sourceUrl: recipe.sourceUrl,
  };
}

/**
 * Get API key for Spoonacular
 */
function getApiKey(): string {
  return SPOONACULAR_API_KEY || process.env.EXPO_PUBLIC_SPOONACULAR_API_KEY || Constants.expoConfig?.extra?.spoonacularApiKey || '';
}

/**
 * Logs API key issues and shows helpful instructions for the 402 payment required error
 */
function logApiQuotaExceededHelp(): void {
  console.warn(`
============================================================
SPOONACULAR API QUOTA EXCEEDED (402 Error)
============================================================
Your API key's quota limit has been reached. To fix this:

1. Get a new API key from https://spoonacular.com/food-api
2. Add the key to your .env file as EXPO_PUBLIC_SPOONACULAR_API_KEY
3. Restart your application

Note: The free tier has limited daily requests.
Consider upgrading your plan if you need more requests.
============================================================`);
}

/**
 * Get recommended recipes based on user profile and nutritional goals
 */
export async function getRecommendedRecipes(mealType?: 'breakfast' | 'lunch' | 'dinner' | 'snack', limit: number = 5): Promise<{ recipes: Recipe[], error?: string }> {
  try {
    // Get user profile to determine nutritional goals and preferences
    const userProfile = await getUserProfile();
    
    const apiKey = getApiKey();
    if (!apiKey) {
      throw new Error('Spoonacular API key is not configured');
    }
    
    // Build request parameters
    const params = new URLSearchParams();
    params.append('apiKey', apiKey);
    params.append('number', limit.toString());
    params.append('addRecipeNutrition', 'true');
    
    // Add meal type if provided
    if (mealType) {
      // Use the mapping function to get the correct Spoonacular type
      const spoonacularType = mapMealType(mealType);
      params.append('type', spoonacularType);
    }
    
    // Add user preferences if available
    if (userProfile) {
      // Add dietary preference
      if (userProfile.dietaryPreference) {
        let diet = '';
        switch (userProfile.dietaryPreference) {
          case DietaryPreference.VEGETARIAN: diet = 'vegetarian'; break;
          case DietaryPreference.VEGAN: diet = 'vegan'; break;
          case DietaryPreference.PESCATARIAN: diet = 'pescetarian'; break;
          case DietaryPreference.KETO: diet = 'ketogenic'; break;
          case DietaryPreference.PALEO: diet = 'paleo'; break;
          case DietaryPreference.GLUTEN_FREE: diet = 'gluten free'; break;
        }
        if (diet) params.append('diet', diet);
      }
      
      // Add excluded ingredients
      if (userProfile.excludedIngredients && userProfile.excludedIngredients.length > 0) {
        params.append('excludeIngredients', userProfile.excludedIngredients.join(','));
      }
      
      // Add nutritional requirements based on goals
      if (userProfile.goalType === NutritionGoalType.MUSCLE_GAIN || 
          userProfile.goalType === NutritionGoalType.ATHLETIC_PERFORMANCE) {
        params.append('minProtein', '25');
      }
      
      if (userProfile.goalType === NutritionGoalType.WEIGHT_LOSS) {
        params.append('maxCalories', '500');
      }
    }
    
    try {
      // Make API call
      const response = await fetch(`https://api.spoonacular.com/recipes/complexSearch?${params.toString()}`);
      
      // Handle 402 Payment Required error (quota exceeded)
      if (response.status === 402) {
        console.log('Spoonacular API quota exceeded (402 error). Using fallback recipe data.');
        logApiQuotaExceededHelp();
        return { recipes: getQuotaExceededFallbackRecipes(mealType, limit) };
      }
      
      if (!response.ok) {
        throw new Error(`Spoonacular API error: ${response.status}`);
      }
      
      // Type the response data
      interface SpoonacularSearchResponse {
        results: {
          id: number;
          title: string;
          image: string;
          imageType: string;
        }[];
        offset: number;
        number: number;
        totalResults: number;
      }
      
      const data = await response.json() as SpoonacularSearchResponse;
      
      if (!data.results || !Array.isArray(data.results)) {
        return { recipes: [], error: 'No recipes found' };
      }
      
      // Get full recipe details for each result
      const recipePromises = data.results.map(async (result) => {
        const recipeResponse = await fetch(`https://api.spoonacular.com/recipes/${result.id}/information?apiKey=${apiKey}&includeNutrition=true`);
        if (!recipeResponse.ok) return null;
        const recipeData = await recipeResponse.json();
        return convertSpoonacularRecipe(recipeData, mealType);
      });
      
      const recipes = await Promise.all(recipePromises);
      return { recipes: recipes.filter((r): r is Recipe => r !== null) };
    } catch (error) {
      console.warn('Error with Spoonacular API, using fallback recipes:', error);
      return { recipes: getFallbackRecipes(mealType, limit) };
    }
  } catch (error) {
    console.error('Error getting recommended recipes:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch recipes';
    
    // If any error occurs, return fallback data
    return { 
      recipes: getFallbackRecipes(mealType, limit), 
      error: errorMessage 
    };
  }
}

/**
 * Provides fallback recipes when the API is unavailable
 */
function getFallbackRecipes(mealType?: 'breakfast' | 'lunch' | 'dinner' | 'snack', limit: number = 5): Recipe[] {
  const breakfastRecipes: Recipe[] = [
    {
      id: "1001",
      name: "Greek Yogurt with Honey and Berries",
      description: "A nutritious breakfast bowl with Greek yogurt, fresh berries, honey, and granola for a perfect start to your day.",
      imageUrl: "https://spoonacular.com/recipeImages/1001-556x370.jpg",
      prepTime: 5,
      cookTime: 0,
      servings: 1,
      difficulty: "easy",
      mealType: "breakfast",
      tags: ["healthy", "quick", "high-protein", "low-fat"],
      ingredients: [
        { name: "Greek yogurt", amount: "1", unit: "cup" },
        { name: "Mixed berries", amount: "1/2", unit: "cup" },
        { name: "Honey", amount: "1", unit: "tablespoon" },
        { name: "Granola", amount: "2", unit: "tablespoons" }
      ],
      instructions: [
        "Add Greek yogurt to a bowl.",
        "Top with fresh berries, granola, and a drizzle of honey.",
        "Serve immediately and enjoy!"
      ],
      nutritionFacts: {
        calories: 250,
        protein: 18,
        carbs: 32,
        fat: 6,
        fiber: 4,
        sugar: 18,
        sodium: 70
      }
    },
    {
      id: "1002",
      name: "Avocado Toast with Poached Egg",
      description: "Creamy avocado on whole grain toast topped with a perfectly poached egg and a sprinkle of red pepper flakes.",
      imageUrl: "https://spoonacular.com/recipeImages/1002-556x370.jpg",
      prepTime: 5,
      cookTime: 5,
      servings: 1,
      difficulty: "medium",
      mealType: "breakfast",
      tags: ["healthy", "high-protein"],
      ingredients: [
        { name: "Whole grain bread", amount: "1", unit: "slice" },
        { name: "Avocado", amount: "1/2", unit: "" },
        { name: "Egg", amount: "1", unit: "" },
        { name: "Red pepper flakes", amount: "1/4", unit: "teaspoon" },
        { name: "Salt", amount: "1/4", unit: "teaspoon" }
      ],
      instructions: [
        "Toast the bread until golden brown.",
        "Mash avocado and spread on toast. Season with salt.",
        "Poach the egg for about 3 minutes.",
        "Place egg on top of avocado toast and sprinkle with red pepper flakes."
      ],
      nutritionFacts: {
        calories: 280,
        protein: 12,
        carbs: 22,
        fat: 18,
        fiber: 7,
        sugar: 2,
        sodium: 380
      }
    },
    {
      id: "1003",
      name: "Blueberry Banana Smoothie Bowl",
      description: "A thick, creamy smoothie bowl packed with blueberries and banana, topped with granola, seeds, and fresh fruit.",
      imageUrl: "https://spoonacular.com/recipeImages/1003-556x370.jpg",
      prepTime: 10,
      cookTime: 0,
      servings: 1,
      difficulty: "easy",
      mealType: "breakfast",
      tags: ["vegetarian", "healthy", "quick"],
      ingredients: [
        { name: "Frozen banana", amount: "1", unit: "" },
        { name: "Frozen blueberries", amount: "1", unit: "cup" },
        { name: "Greek yogurt", amount: "1/2", unit: "cup" },
        { name: "Almond milk", amount: "1/4", unit: "cup" },
        { name: "Honey", amount: "1", unit: "teaspoon" },
        { name: "Granola", amount: "2", unit: "tablespoons" },
        { name: "Chia seeds", amount: "1", unit: "teaspoon" }
      ],
      instructions: [
        "Blend banana, blueberries, yogurt, milk, and honey until smooth and thick.",
        "Pour into a bowl and top with granola, chia seeds, and fresh fruit.",
        "Serve immediately."
      ],
      nutritionFacts: {
        calories: 320,
        protein: 14,
        carbs: 60,
        fat: 5,
        fiber: 10,
        sugar: 30,
        sodium: 65
      }
    }
  ];

  const lunchRecipes: Recipe[] = [
    {
      id: "2001",
      name: "Grilled Chicken Salad with Avocado",
      description: "A protein-packed salad with grilled chicken, fresh vegetables, avocado, and a light vinaigrette dressing.",
      imageUrl: "https://spoonacular.com/recipeImages/2001-556x370.jpg",
      prepTime: 15,
      cookTime: 15,
      servings: 2,
      difficulty: "medium",
      mealType: "lunch",
      tags: ["high-protein", "low-carb", "healthy"],
      ingredients: [
        { name: "Chicken breast", amount: "8", unit: "oz" },
        { name: "Mixed greens", amount: "4", unit: "cups" },
        { name: "Avocado", amount: "1", unit: "" },
        { name: "Cherry tomatoes", amount: "1", unit: "cup" },
        { name: "Cucumber", amount: "1/2", unit: "" },
        { name: "Olive oil", amount: "2", unit: "tablespoons" },
        { name: "Lemon juice", amount: "1", unit: "tablespoon" },
        { name: "Salt and pepper", amount: "", unit: "to taste" }
      ],
      instructions: [
        "Season chicken with salt and pepper and grill until cooked through, about 6-7 minutes per side.",
        "Let chicken rest for 5 minutes, then slice.",
        "Combine mixed greens, sliced avocado, halved cherry tomatoes, and sliced cucumber in a large bowl.",
        "Whisk together olive oil, lemon juice, salt, and pepper for the dressing.",
        "Add sliced chicken to the salad, drizzle with dressing, and toss gently to combine."
      ],
      nutritionFacts: {
        calories: 420,
        protein: 35,
        carbs: 12,
        fat: 28,
        fiber: 8,
        sugar: 3,
        sodium: 320
      }
    },
    {
      id: "2002",
      name: "Mediterranean Quinoa Bowl",
      description: "A nourishing quinoa bowl with roasted vegetables, chickpeas, feta cheese, and a tangy tahini dressing.",
      imageUrl: "https://spoonacular.com/recipeImages/2002-556x370.jpg",
      prepTime: 15,
      cookTime: 25,
      servings: 2,
      difficulty: "medium",
      mealType: "lunch",
      tags: ["vegetarian", "Mediterranean", "healthy"],
      ingredients: [
        { name: "Quinoa", amount: "1", unit: "cup" },
        { name: "Chickpeas", amount: "1", unit: "can" },
        { name: "Bell pepper", amount: "1", unit: "" },
        { name: "Zucchini", amount: "1", unit: "" },
        { name: "Red onion", amount: "1/2", unit: "" },
        { name: "Feta cheese", amount: "1/4", unit: "cup" },
        { name: "Olive oil", amount: "2", unit: "tablespoons" },
        { name: "Tahini", amount: "2", unit: "tablespoons" },
        { name: "Lemon juice", amount: "1", unit: "tablespoon" },
        { name: "Garlic", amount: "1", unit: "clove" }
      ],
      instructions: [
        "Cook quinoa according to package instructions.",
        "Preheat oven to 425°F. Chop vegetables, toss with olive oil, salt, and pepper.",
        "Roast vegetables for 20-25 minutes until tender.",
        "Mix tahini, lemon juice, minced garlic, and 2 tbsp water for the dressing.",
        "Assemble bowls with quinoa, roasted vegetables, chickpeas, and feta. Drizzle with tahini dressing."
      ],
      nutritionFacts: {
        calories: 480,
        protein: 18,
        carbs: 65,
        fat: 20,
        fiber: 12,
        sugar: 8,
        sodium: 580
      }
    }
  ];

  const dinnerRecipes: Recipe[] = [
    {
      id: "3001",
      name: "Baked Salmon with Roasted Vegetables",
      description: "Perfectly baked salmon fillets with a lemon herb crust, served with colorful roasted vegetables.",
      imageUrl: "https://spoonacular.com/recipeImages/3001-556x370.jpg",
      prepTime: 15,
      cookTime: 25,
      servings: 2,
      difficulty: "medium",
      mealType: "dinner",
      tags: ["high-protein", "low-carb", "pescatarian", "healthy"],
      ingredients: [
        { name: "Salmon fillets", amount: "2", unit: "6 oz fillets" },
        { name: "Broccoli", amount: "2", unit: "cups" },
        { name: "Bell peppers", amount: "2", unit: "" },
        { name: "Cherry tomatoes", amount: "1", unit: "cup" },
        { name: "Olive oil", amount: "2", unit: "tablespoons" },
        { name: "Lemon", amount: "1", unit: "" },
        { name: "Garlic", amount: "2", unit: "cloves" },
        { name: "Fresh dill", amount: "2", unit: "tablespoons" },
        { name: "Salt and pepper", amount: "", unit: "to taste" }
      ],
      instructions: [
        "Preheat oven to 400°F.",
        "Toss vegetables with 1 tbsp olive oil, salt, and pepper. Spread on a baking sheet.",
        "Place salmon on another baking sheet. Mix remaining olive oil with minced garlic, lemon zest, chopped dill, salt, and pepper.",
        "Spread herb mixture over salmon fillets.",
        "Roast vegetables for 25 minutes and salmon for 12-15 minutes or until cooked through.",
        "Serve salmon with roasted vegetables and lemon wedges."
      ],
      nutritionFacts: {
        calories: 420,
        protein: 38,
        carbs: 15,
        fat: 24,
        fiber: 6,
        sugar: 8,
        sodium: 350
      }
    },
    {
      id: "3002",
      name: "Turkey Meatballs with Zucchini Noodles",
      description: "Lean turkey meatballs simmered in marinara sauce, served over zucchini noodles for a low-carb, protein-rich dinner.",
      imageUrl: "https://spoonacular.com/recipeImages/3002-556x370.jpg",
      prepTime: 20,
      cookTime: 25,
      servings: 4,
      difficulty: "medium",
      mealType: "dinner",
      tags: ["high-protein", "low-carb", "healthy"],
      ingredients: [
        { name: "Ground turkey", amount: "1", unit: "lb" },
        { name: "Bread crumbs", amount: "1/4", unit: "cup" },
        { name: "Egg", amount: "1", unit: "" },
        { name: "Parmesan cheese", amount: "1/4", unit: "cup" },
        { name: "Garlic", amount: "2", unit: "cloves" },
        { name: "Zucchini", amount: "4", unit: "medium" },
        { name: "Marinara sauce", amount: "2", unit: "cups" },
        { name: "Italian seasoning", amount: "1", unit: "tablespoon" },
        { name: "Salt and pepper", amount: "", unit: "to taste" }
      ],
      instructions: [
        "In a large bowl, combine ground turkey, bread crumbs, egg, grated Parmesan, minced garlic, Italian seasoning, salt, and pepper.",
        "Form mixture into 16 meatballs.",
        "Heat oil in a large skillet over medium heat. Brown meatballs on all sides, about 5-6 minutes.",
        "Add marinara sauce, reduce heat to low, cover and simmer for 15-20 minutes until meatballs are cooked through.",
        "While meatballs are cooking, use a spiralizer to create zucchini noodles.",
        "Sauté zucchini noodles in a separate pan for 2-3 minutes until just tender.",
        "Serve meatballs and sauce over zucchini noodles."
      ],
      nutritionFacts: {
        calories: 360,
        protein: 32,
        carbs: 18,
        fat: 20,
        fiber: 5,
        sugar: 10,
        sodium: 680
      }
    }
  ];

  const snackRecipes: Recipe[] = [
    {
      id: "4001",
      name: "Apple Slices with Almond Butter",
      description: "A simple, nutritious snack combining crisp apple slices with creamy almond butter for the perfect balance of carbs and protein.",
      imageUrl: "https://spoonacular.com/recipeImages/4001-556x370.jpg",
      prepTime: 5,
      cookTime: 0,
      servings: 1,
      difficulty: "easy",
      mealType: "snack",
      tags: ["vegetarian", "vegan", "quick", "healthy"],
      ingredients: [
        { name: "Apple", amount: "1", unit: "medium" },
        { name: "Almond butter", amount: "2", unit: "tablespoons" },
        { name: "Cinnamon", amount: "1/4", unit: "teaspoon" },
        { name: "Honey", amount: "1", unit: "teaspoon" }
      ],
      instructions: [
        "Wash and slice the apple into wedges.",
        "Arrange apple slices on a plate.",
        "Drizzle with almond butter and optional honey.",
        "Sprinkle with cinnamon and serve immediately."
      ],
      nutritionFacts: {
        calories: 200,
        protein: 5,
        carbs: 22,
        fat: 12,
        fiber: 5,
        sugar: 16,
        sodium: 2
      }
    },
    {
      id: "4002",
      name: "Greek Yogurt Parfait",
      description: "Layers of Greek yogurt, fresh berries, and crunchy granola for a satisfying protein-rich snack.",
      imageUrl: "https://spoonacular.com/recipeImages/4002-556x370.jpg",
      prepTime: 5,
      cookTime: 0,
      servings: 1,
      difficulty: "easy",
      mealType: "snack",
      tags: ["vegetarian", "quick", "high-protein"],
      ingredients: [
        { name: "Greek yogurt", amount: "3/4", unit: "cup" },
        { name: "Mixed berries", amount: "1/2", unit: "cup" },
        { name: "Granola", amount: "2", unit: "tablespoons" },
        { name: "Honey", amount: "1", unit: "teaspoon" }
      ],
      instructions: [
        "In a glass or bowl, add half of the Greek yogurt.",
        "Top with half of the mixed berries and 1 tablespoon of granola.",
        "Repeat with remaining yogurt, berries, and granola.",
        "Drizzle with honey and serve immediately."
      ],
      nutritionFacts: {
        calories: 220,
        protein: 15,
        carbs: 30,
        fat: 4,
        fiber: 3,
        sugar: 20,
        sodium: 60
      }
    }
  ];

  // Select recipes based on meal type
  let availableRecipes: Recipe[] = [];
  
  switch (mealType) {
    case 'breakfast':
      availableRecipes = breakfastRecipes;
      break;
    case 'lunch':
      availableRecipes = lunchRecipes;
      break;
    case 'dinner':
      availableRecipes = dinnerRecipes;
      break;
    case 'snack':
      availableRecipes = snackRecipes;
      break;
    default:
      // If no meal type specified, combine all recipes
      availableRecipes = [
        ...breakfastRecipes,
        ...lunchRecipes,
        ...dinnerRecipes,
        ...snackRecipes
      ];
  }
  
  // Return random selection based on limit
  if (availableRecipes.length <= limit) {
    return availableRecipes;
  }
  
  // Shuffle array and return limited number of recipes
  return availableRecipes
    .sort(() => 0.5 - Math.random())
    .slice(0, limit);
}

/**
 * Provides fallback recipes when the API quota is exceeded
 */
function getQuotaExceededFallbackRecipes(mealType?: 'breakfast' | 'lunch' | 'dinner' | 'snack', limit: number = 5): Recipe[] {
  // Create a few basic recipes as fallbacks when API is unavailable
  const fallbackRecipes: Recipe[] = [
    {
      id: "fallback-1",
      name: "Avocado Toast with Poached Egg",
      description: "Creamy avocado on whole grain toast topped with a perfectly poached egg and seasonings.",
      imageUrl: "https://images.unsplash.com/photo-1525351484163-7529414344d8?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80",
      prepTime: 5,
      cookTime: 5,
      servings: 1,
      difficulty: "easy",
      mealType: "breakfast",
      tags: ["vegetarian", "healthy", "high-protein"],
      ingredients: [
        { name: "Whole grain bread", amount: "1", unit: "slice" },
        { name: "Avocado", amount: "1/2", unit: "" },
        { name: "Egg", amount: "1", unit: "" },
        { name: "Salt", amount: "1/4", unit: "teaspoon" },
        { name: "Pepper", amount: "1/4", unit: "teaspoon" }
      ],
      instructions: [
        "Toast the bread until golden brown.",
        "Mash avocado and spread on toast. Season with salt and pepper.",
        "Poach the egg for about 3 minutes.",
        "Place egg on top of avocado toast and serve immediately."
      ],
      nutritionFacts: {
        calories: 280,
        protein: 12,
        carbs: 22,
        fat: 18,
        fiber: 7,
        sugar: 2,
        sodium: 380
      }
    },
    {
      id: "fallback-2",
      name: "Grilled Chicken Salad",
      description: "A protein-packed salad with grilled chicken, fresh vegetables, and a light vinaigrette.",
      imageUrl: "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80",
      prepTime: 15,
      cookTime: 15,
      servings: 2,
      difficulty: "medium",
      mealType: "lunch",
      tags: ["high-protein", "low-carb", "healthy"],
      ingredients: [
        { name: "Chicken breast", amount: "8", unit: "oz" },
        { name: "Mixed greens", amount: "4", unit: "cups" },
        { name: "Cherry tomatoes", amount: "1", unit: "cup" },
        { name: "Cucumber", amount: "1/2", unit: "" },
        { name: "Olive oil", amount: "2", unit: "tablespoons" },
        { name: "Lemon juice", amount: "1", unit: "tablespoon" }
      ],
      instructions: [
        "Season chicken with salt and pepper and grill until cooked through.",
        "Let chicken rest for 5 minutes, then slice.",
        "Combine mixed greens, halved cherry tomatoes, and sliced cucumber in a bowl.",
        "Make dressing with olive oil, lemon juice, salt, and pepper.",
        "Add sliced chicken to the salad and drizzle with dressing."
      ],
      nutritionFacts: {
        calories: 350,
        protein: 35,
        carbs: 10,
        fat: 20,
        fiber: 4,
        sugar: 3,
        sodium: 300
      }
    },
    {
      id: "fallback-3",
      name: "Baked Salmon with Roasted Vegetables",
      description: "Healthy baked salmon with seasoned roasted vegetables.",
      imageUrl: "https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80",
      prepTime: 15,
      cookTime: 25,
      servings: 2,
      difficulty: "medium",
      mealType: "dinner",
      tags: ["high-protein", "low-carb", "pescatarian"],
      ingredients: [
        { name: "Salmon fillets", amount: "2", unit: "6 oz fillets" },
        { name: "Broccoli", amount: "2", unit: "cups" },
        { name: "Bell peppers", amount: "2", unit: "" },
        { name: "Olive oil", amount: "2", unit: "tablespoons" },
        { name: "Lemon", amount: "1", unit: "" },
        { name: "Salt and pepper", amount: "", unit: "to taste" }
      ],
      instructions: [
        "Preheat oven to 400°F.",
        "Toss vegetables with olive oil, salt, and pepper. Spread on a baking sheet.",
        "Place salmon on another baking sheet, season with salt, pepper, and lemon juice.",
        "Roast vegetables for 25 minutes and salmon for 12-15 minutes.",
        "Serve salmon with roasted vegetables and lemon wedges."
      ],
      nutritionFacts: {
        calories: 420,
        protein: 38,
        carbs: 15,
        fat: 24,
        fiber: 6,
        sugar: 8,
        sodium: 350
      }
    }
  ];

  // Filter recipes by meal type if specified
  let filteredRecipes = fallbackRecipes;
  if (mealType) {
    filteredRecipes = fallbackRecipes.filter(recipe => recipe.mealType === mealType);
    // If no recipes match the meal type, return any available recipes
    if (filteredRecipes.length === 0) {
      filteredRecipes = fallbackRecipes;
    }
  }
  
  // Return up to the limit number of recipes
  return filteredRecipes.slice(0, limit);
}

/**
 * Search recipes based on filters
 */
export async function searchRecipes(filters: RecipeSearchFilters): Promise<Recipe[]> {
  try {
    const apiKey = getApiKey();
    if (!apiKey) {
      throw new Error('Spoonacular API key is not configured');
    }
    
    // Build request parameters
    const params = new URLSearchParams();
    params.append('apiKey', apiKey);
    params.append('number', '20');
    params.append('addRecipeNutrition', 'true');
    
    // Add query if provided
    if (filters.query) {
      params.append('query', filters.query);
    }
    
    // Add meal type if provided
    if (filters.mealType) {
      // Use the mapping function to get the correct Spoonacular type
      const spoonacularType = mapMealType(filters.mealType);
      params.append('type', spoonacularType);
    }
    
    // Add max prep time if provided
    if (filters.maxPrepTime !== undefined) {
      params.append('maxReadyTime', filters.maxPrepTime.toString());
    }
    
    // Add dietary preference if provided
    if (filters.dietaryPreference) {
      let diet = '';
      switch (filters.dietaryPreference) {
        case DietaryPreference.VEGETARIAN: diet = 'vegetarian'; break;
        case DietaryPreference.VEGAN: diet = 'vegan'; break;
        case DietaryPreference.PESCATARIAN: diet = 'pescetarian'; break;
        case DietaryPreference.KETO: diet = 'ketogenic'; break;
        case DietaryPreference.PALEO: diet = 'paleo'; break;
        case DietaryPreference.GLUTEN_FREE: diet = 'gluten free'; break;
      }
      if (diet) params.append('diet', diet);
    }
    
    // Add included ingredients if provided
    if (filters.includeIngredients && filters.includeIngredients.length > 0) {
      params.append('includeIngredients', filters.includeIngredients.join(','));
    }
    
    // Add excluded ingredients if provided
    if (filters.excludeIngredients && filters.excludeIngredients.length > 0) {
      params.append('excludeIngredients', filters.excludeIngredients.join(','));
    }
    
    // Add calorie range if provided
    if (filters.calorieRange) {
      params.append('minCalories', filters.calorieRange.min.toString());
      params.append('maxCalories', filters.calorieRange.max.toString());
    }
    
    // Add other nutrient ranges if provided
    if (filters.proteinRange) {
      params.append('minProtein', filters.proteinRange.min.toString());
      params.append('maxProtein', filters.proteinRange.max.toString());
    }
    
    if (filters.carbsRange) {
      params.append('minCarbs', filters.carbsRange.min.toString());
      params.append('maxCarbs', filters.carbsRange.max.toString());
    }
    
    if (filters.fatRange) {
      params.append('minFat', filters.fatRange.min.toString());
      params.append('maxFat', filters.fatRange.max.toString());
    }
    
    // Add tags if provided
    if (filters.tags && filters.tags.length > 0) {
      // Handle special tags
      if (filters.tags.includes('high-protein')) {
        params.append('minProtein', '25');
      }
      if (filters.tags.includes('low-carb')) {
        params.append('maxCarbs', '20');
      }
      if (filters.tags.includes('low-fat')) {
        params.append('maxFat', '10');
      }
      if (filters.tags.includes('quick')) {
        params.append('maxReadyTime', '30');
      }
    }
    
    // Make API call
    const response = await fetch(`https://api.spoonacular.com/recipes/complexSearch?${params.toString()}`);
    
    // Handle 402 Payment Required error (quota exceeded)
    if (response.status === 402) {
      console.log('Spoonacular API quota exceeded (402 error). Using fallback recipe data.');
      logApiQuotaExceededHelp();
      return getQuotaExceededFallbackRecipes(filters.mealType, 5);
    }
    
    if (!response.ok) {
      throw new Error(`Spoonacular API error: ${response.status}`);
    }
    
    // Type the response data
    interface SpoonacularSearchResponse {
      results: {
        id: number;
        title: string;
        image: string;
        imageType: string;
      }[];
      offset: number;
      number: number;
      totalResults: number;
    }
    
    const data = await response.json() as SpoonacularSearchResponse;
    
    if (!data.results || !Array.isArray(data.results)) {
      return [];
    }
    
    // Get full recipe details for each result
    const recipePromises = data.results.map(async (result) => {
      const recipeResponse = await fetch(`https://api.spoonacular.com/recipes/${result.id}/information?apiKey=${apiKey}&includeNutrition=true`);
      if (!recipeResponse.ok) return null;
      const recipeData = await recipeResponse.json();
      return convertSpoonacularRecipe(recipeData, filters.mealType);
    });
    
    const recipes = await Promise.all(recipePromises);
    return recipes.filter((r): r is Recipe => r !== null);
  } catch (error) {
    console.error('Error searching recipes:', error);
    // Return fallback recipes for any error
    return getQuotaExceededFallbackRecipes(filters.mealType, 5);
  }
}

/**
 * Get recipe details by ID
 */
export async function getRecipeById(id: string): Promise<Recipe | null> {
  try {
    const apiKey = getApiKey();
    if (!apiKey) {
      throw new Error('Spoonacular API key is not configured');
    }
    
    // Make API call
    const response = await fetch(`https://api.spoonacular.com/recipes/${id}/information?apiKey=${apiKey}&includeNutrition=true`);
    
    // Handle 402 Payment Required error (quota exceeded)
    if (response.status === 402) {
      console.log('Spoonacular API quota exceeded (402 error). Returning fallback recipe.');
      logApiQuotaExceededHelp();
      // Try to find a fallback recipe by ID or return the first fallback
      const fallbacks = getQuotaExceededFallbackRecipes();
      const matchingRecipe = fallbacks.find(recipe => recipe.id === id) || fallbacks[0];
      return matchingRecipe;
    }
    
    if (!response.ok) {
      throw new Error(`Spoonacular API error: ${response.status}`);
    }
    
    const data = await response.json();
    return convertSpoonacularRecipe(data);
  } catch (error) {
    console.error('Error getting recipe details:', error);
    // Return a fallback recipe for any error
    const fallbacks = getQuotaExceededFallbackRecipes();
    return fallbacks[0];
  }
}

/**
 * Get similar recipes
 */
export async function getSimilarRecipes(recipeId: string, limit: number = 3): Promise<Recipe[]> {
  try {
    const apiKey = getApiKey();
    if (!apiKey) {
      throw new Error('Spoonacular API key is not configured');
    }
    
    // Make API call to get similar recipes
    const response = await fetch(`https://api.spoonacular.com/recipes/${recipeId}/similar?apiKey=${apiKey}&number=${limit}`);
    
    // Handle 402 Payment Required error (quota exceeded)
    if (response.status === 402) {
      console.log('Spoonacular API quota exceeded (402 error). Returning fallback similar recipes.');
      logApiQuotaExceededHelp();
      // Return different fallback recipes than the one with the given ID
      const fallbacks = getQuotaExceededFallbackRecipes();
      return fallbacks.filter(recipe => recipe.id !== recipeId).slice(0, limit);
    }
    
    if (!response.ok) {
      throw new Error(`Spoonacular API error: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (!Array.isArray(data)) {
      return [];
    }
    
    // Get full recipe details for each similar recipe
    const recipePromises = data.map(async (result: any) => {
      const recipeResponse = await fetch(`https://api.spoonacular.com/recipes/${result.id}/information?apiKey=${apiKey}&includeNutrition=true`);
      if (!recipeResponse.ok) return null;
      const recipeData = await recipeResponse.json();
      return convertSpoonacularRecipe(recipeData);
    });
    
    const recipes = await Promise.all(recipePromises);
    return recipes.filter((r): r is Recipe => r !== null);
  } catch (error) {
    console.error('Error getting similar recipes:', error);
    // Return fallback recipes for any error
    const fallbacks = getQuotaExceededFallbackRecipes();
    return fallbacks.slice(0, limit);
  }
} 