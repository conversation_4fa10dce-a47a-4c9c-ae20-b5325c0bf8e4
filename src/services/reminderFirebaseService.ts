/**
 * Reminder Firebase Service
 * 
 * Implementation of reminder functionality using Firebase.
 * Handles scheduling and managing notifications, including Firebase Cloud Messaging (FCM).
 */

import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { doc, getDoc, setDoc, collection, query, where, getDocs, Timestamp, serverTimestamp } from 'firebase/firestore';
import { firestore } from '@/lib/firebase';

// Types
export interface Reminder {
  id: string;
  userId: string;
  title: string;
  body: string;
  scheduledTime: string; // ISO datetime string
  type: 'mindfulness' | 'water' | 'meal' | 'sleep' | 'medication' | 'custom';
  repeat: 'none' | 'daily' | 'weekly' | 'monthly';
  active: boolean;
  lastTriggered?: string; // ISO datetime string
  createdAt: string;
  updatedAt: string;
}

export interface ReminderPreferences {
  mindfulnessEnabled: boolean;
  mindfulnessTime?: string; // HH:MM format
  mindfulnessFrequency: 'daily' | 'weekly' | 'none';
  sleepEnabled: boolean;
  sleepTime?: string; // HH:MM format
  waterEnabled: boolean;
  waterIntervalHours: number;
  mealEnabled: boolean;
  mealTimes: string[]; // Array of HH:MM formats
  soundEnabled: boolean;
  vibrationEnabled: boolean;
}

const DEFAULT_PREFERENCES: ReminderPreferences = {
  mindfulnessEnabled: true,
  mindfulnessFrequency: 'daily',
  mindfulnessTime: '08:00',
  sleepEnabled: true,
  sleepTime: '22:00',
  waterEnabled: true,
  waterIntervalHours: 2,
  mealEnabled: true,
  mealTimes: ['08:00', '13:00', '19:00'],
  soundEnabled: true,
  vibrationEnabled: true
};

/**
 * Register for push notifications
 */
export async function registerForPushNotifications() {
  try {
    // Request permission to show notifications
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    
    if (finalStatus !== 'granted') {
      return { success: false, error: 'Permission not granted for notifications' };
    }
    
    // Configure notification handler
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: false,
      }),
    });
    
    return { success: true };
  } catch (error) {
    console.error('Error registering for push notifications:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Get reminder preferences for a user
 */
export async function getReminderPreferences(
  userId: string
): Promise<{
  success: boolean;
  data?: ReminderPreferences;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    const preferencesRef = doc(firestore, 'reminder_preferences', userId);
    const preferencesSnap = await getDoc(preferencesRef);

    if (preferencesSnap.exists()) {
      const data = preferencesSnap.data();
      const preferences: ReminderPreferences = {
        mindfulnessEnabled: data.mindfulnessEnabled,
        mindfulnessTime: data.mindfulnessTime,
        mindfulnessFrequency: data.mindfulnessFrequency,
        sleepEnabled: data.sleepEnabled,
        sleepTime: data.sleepTime,
        waterEnabled: data.waterEnabled,
        waterIntervalHours: data.waterIntervalHours,
        mealEnabled: data.mealEnabled,
        mealTimes: data.mealTimes,
        soundEnabled: data.soundEnabled,
        vibrationEnabled: data.vibrationEnabled
      };

      return {
        success: true,
        data: preferences
      };
    }

    // No preferences found, create default preferences
    await setDoc(preferencesRef, {
      ...DEFAULT_PREFERENCES,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      data: DEFAULT_PREFERENCES
    };
  } catch (error) {
    console.error('Error getting reminder preferences:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Set reminder preferences for a user
 */
export async function setReminderPreferences(
  userId: string,
  preferences: ReminderPreferences
): Promise<{
  success: boolean;
  data?: ReminderPreferences;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    const preferencesRef = doc(firestore, 'reminder_preferences', userId);
    
    await setDoc(preferencesRef, {
      ...preferences,
      updatedAt: serverTimestamp()
    }, { merge: true });

    return {
      success: true,
      data: preferences
    };
  } catch (error) {
    console.error('Error setting reminder preferences:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Create a new reminder
 */
export async function createReminder(
  userId: string,
  title: string,
  body: string,
  scheduledTime: Date,
  type: Reminder['type'] = 'custom',
  repeat: Reminder['repeat'] = 'none'
): Promise<{
  success: boolean;
  data?: Reminder;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    const reminderId = crypto.randomUUID();
    const now = new Date();
    
    // Create reminder object
    const newReminder: Reminder = {
      id: reminderId,
      userId,
      title,
      body,
      scheduledTime: scheduledTime.toISOString(),
      type,
      repeat,
      active: true,
      createdAt: now.toISOString(),
      updatedAt: now.toISOString()
    };
    
    const reminderRef = doc(firestore, 'reminders', reminderId);
    
    await setDoc(reminderRef, {
      userId,
      title,
      body,
      scheduledTime: scheduledTime.toISOString(),
      type,
      repeat,
      active: true,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    
    // Schedule the notification
    let identifier;
    
    try {
      // Calculate trigger seconds
      const triggerSeconds = Math.max(1, Math.floor(
        (scheduledTime.getTime() - new Date().getTime()) / 1000
      ));
      
      identifier = await Notifications.scheduleNotificationAsync({
        identifier: reminderId,
        content: {
          title,
          body,
          data: {
            type,
            reminderId,
            timestamp: now.toISOString(),
          },
        },
        trigger: {
          seconds: triggerSeconds,
          repeats: false,
        },
      });
      
      // If this is a repeating reminder, handle repeat scheduling
      if (repeat !== 'none') {
        await scheduleRepeatingReminder(newReminder);
      }
    } catch (notificationError) {
      console.error('Error scheduling notification:', notificationError);
      // Continue even if scheduling fails - we'll store the reminder anyway
    }
    
    return {
      success: true,
      data: newReminder
    };
  } catch (error) {
    console.error('Error creating reminder:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get all reminders for a user
 */
export async function getUserReminders(
  userId: string
): Promise<{
  success: boolean;
  data?: Reminder[];
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    const remindersRef = collection(firestore, 'reminders');
    const remindersQuery = query(
      remindersRef,
      where('userId', '==', userId),
      where('active', '==', true)
    );

    const querySnapshot = await getDocs(remindersQuery);
    
    const reminders: Reminder[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      reminders.push({
        id: doc.id,
        userId: data.userId,
        title: data.title,
        body: data.body,
        scheduledTime: data.scheduledTime,
        type: data.type,
        repeat: data.repeat,
        active: data.active,
        lastTriggered: data.lastTriggered,
        createdAt: data.createdAt instanceof Timestamp ? 
          data.createdAt.toDate().toISOString() : data.createdAt,
        updatedAt: data.updatedAt instanceof Timestamp ? 
          data.updatedAt.toDate().toISOString() : data.updatedAt
      });
    });

    return {
      success: true,
      data: reminders.sort((a, b) => 
        new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime()
      )
    };
  } catch (error) {
    console.error('Error getting user reminders:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Delete a reminder
 */
export async function deleteReminder(
  reminderId: string
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const reminderRef = doc(firestore, 'reminders', reminderId);
    
    // Set active to false (soft delete)
    await setDoc(reminderRef, {
      active: false,
      updatedAt: serverTimestamp()
    }, { merge: true });
    
    // Cancel any scheduled notifications for this reminder
    const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();
    
    for (const notification of scheduledNotifications) {
      const data = notification.request?.content?.data as any;
      if (data?.reminderId === reminderId) {
        await Notifications.cancelScheduledNotificationAsync(notification.request?.identifier || '');
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error deleting reminder:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Schedule a mindfulness reminder
 */
export async function scheduleMindfulnessReminder(
  userId: string,
  title: string = 'Mindfulness Reminder',
  body: string = 'Take a moment to practice mindfulness.',
  time?: Date
): Promise<{
  success: boolean;
  identifier?: string;
  error?: string;
}> {
  try {
    // Get user preferences
    const preferencesResult = await getReminderPreferences(userId);
    
    if (!preferencesResult.success || !preferencesResult.data) {
      return { success: false, error: 'Failed to get user preferences' };
    }
    
    const preferences = preferencesResult.data;
    
    if (!preferences.mindfulnessEnabled) {
      return { success: false, error: 'Mindfulness notifications disabled in user preferences' };
    }
    
    // Use the provided time or get from preferences
    let reminderTime = time;
    if (!reminderTime && preferences.mindfulnessTime) {
      const [hours, minutes] = preferences.mindfulnessTime.split(':').map(Number);
      reminderTime = new Date();
      reminderTime.setHours(hours, minutes, 0, 0);
      
      // If the time is already past for today, schedule for tomorrow
      if (reminderTime < new Date()) {
        reminderTime.setDate(reminderTime.getDate() + 1);
      }
    }
    
    // If still no time, default to tomorrow at 8 AM
    if (!reminderTime) {
      reminderTime = new Date();
      reminderTime.setDate(reminderTime.getDate() + 1);
      reminderTime.setHours(8, 0, 0, 0);
    }
    
    // Create the reminder
    const reminderResult = await createReminder(
      userId,
      title,
      body,
      reminderTime,
      'mindfulness',
      preferences.mindfulnessFrequency
    );
    
    if (!reminderResult.success) {
      return { success: false, error: reminderResult.error };
    }
    
    return { 
      success: true, 
      identifier: reminderResult.data?.id 
    };
  } catch (error) {
    console.error('Error scheduling mindfulness reminder:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Helper function to schedule repeating reminders
 */
async function scheduleRepeatingReminder(reminder: Reminder): Promise<void> {
  try {
    const baseTime = new Date(reminder.scheduledTime);
    const futureTimes: Date[] = [];
    
    // Create next occurrences based on repeat pattern
    if (reminder.repeat === 'daily') {
      // Schedule for the next 7 days
      for (let i = 1; i <= 7; i++) {
        const nextDay = new Date(baseTime);
        nextDay.setDate(nextDay.getDate() + i);
        futureTimes.push(nextDay);
      }
    } else if (reminder.repeat === 'weekly') {
      // Schedule for next 4 weeks
      for (let i = 1; i <= 4; i++) {
        const nextWeek = new Date(baseTime);
        nextWeek.setDate(nextWeek.getDate() + (i * 7));
        futureTimes.push(nextWeek);
      }
    } else if (reminder.repeat === 'monthly') {
      // Schedule for next 3 months
      for (let i = 1; i <= 3; i++) {
        const nextMonth = new Date(baseTime);
        nextMonth.setMonth(nextMonth.getMonth() + i);
        futureTimes.push(nextMonth);
      }
    }
    
    // Schedule notifications for each future time
    for (const futureTime of futureTimes) {
      await Notifications.scheduleNotificationAsync({
        identifier: `${reminder.id}-${futureTime.getTime()}`,
        content: {
          title: reminder.title,
          body: reminder.body,
          data: {
            type: reminder.type,
            reminderId: reminder.id,
            timestamp: new Date().toISOString(),
            isRepeating: true
          },
        },
        trigger: {
          seconds: Math.max(1, Math.floor((futureTime.getTime() - new Date().getTime()) / 1000)),
          repeats: false,
        },
      });
    }
  } catch (error) {
    console.error('Error scheduling repeating reminders:', error);
    // Continue even if some fail
  }
} 