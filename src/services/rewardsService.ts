import AsyncStorage from '@react-native-async-storage/async-storage';

import { format, addDays, subDays, isToday, differenceInDays } from 'date-fns';
import { Challenge, ChallengeStatus, updateChallengeProgress, ChallengeDifficulty } from './challengeService';
import { getUserProfile } from './nutritionGoalService';
import { getFirestore, collection, doc, getDoc, getDocs, setDoc, addDoc, updateDoc, query, where, orderBy, limit as firestoreLimit, Timestamp, serverTimestamp } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

// Firebase instances
const firestore = getFirestore();
const auth = getAuth();

// Storage keys
const REWARDS_KEY = 'user_rewards';
const BADGES_KEY = 'user_badges';
const STREAK_KEY = 'user_streaks';
const ACHIEVEMENT_KEY = 'user_achievements';

// Reward types
export enum RewardType {
  CHALLENGE_COMPLETION = 'challenge_completion',
  DAILY_STREAK = 'daily_streak',
  MEAL_LOGGING = 'meal_logging',
  GOAL_ACHIEVEMENT = 'goal_achievement',
  PROFILE_COMPLETION = 'profile_completion',
  SPECIAL_EVENT = 'special_event',
}

// Badge categories
export enum BadgeCategory {
  NUTRITION = 'nutrition',
  FITNESS = 'fitness',
  CONSISTENCY = 'consistency',
  SPECIAL = 'special',
}

// Badge levels
export enum BadgeLevel {
  BRONZE = 'bronze',
  SILVER = 'silver',
  GOLD = 'gold',
  PLATINUM = 'platinum',
}

// Streak types
export enum StreakType {
  DAILY_LOGIN = 'daily_login',
  MEAL_LOGGING = 'meal_logging',
  WATER_TRACKING = 'water_tracking',
  CALORIE_GOAL = 'calorie_goal',
  PROTEIN_GOAL = 'protein_goal',
}

// Interfaces
export interface Reward {
  id: string;
  userId: string;
  type: RewardType;
  points: number;
  description: string;
  dateEarned: string;
  relatedId?: string; // ID of related item (challenge, achievement, etc.)
}

export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: BadgeCategory;
  level: BadgeLevel;
  pointsValue: number;
  dateEarned?: string;
  unlockedAt?: number; // Requirement to unlock (e.g., streak days, meals logged)
  progress?: number; // Current progress toward unlocking
}

export interface Streak {
  type: StreakType;
  currentStreak: number;
  longestStreak: number;
  lastUpdateDate: string;
  streakHistory: { date: string; completed: boolean }[];
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  isSecret: boolean;
  isCompleted: boolean;
  progress: number;
  target: number;
  pointsReward: number;
  badgeReward?: string; // ID of badge earned
  dateCompleted?: string;
}

interface UserRewards {
  totalPoints: number;
  level: number;
  badgeCount: number;
  achievements: number;
  streaks: Record<StreakType, Streak>;
}

// Predefined badges
const predefinedBadges: Badge[] = [
  // Meal logging badges
  {
    id: 'meal_logger_bronze',
    name: 'Meal Logger',
    description: 'Log 10 meals',
    icon: 'silverware-fork-knife',
    category: BadgeCategory.NUTRITION,
    level: BadgeLevel.BRONZE,
    pointsValue: 50,
    unlockedAt: 10,
  },
  {
    id: 'meal_logger_silver',
    name: 'Meal Master',
    description: 'Log 50 meals',
    icon: 'silverware-fork-knife',
    category: BadgeCategory.NUTRITION,
    level: BadgeLevel.SILVER,
    pointsValue: 150,
    unlockedAt: 50,
  },
  {
    id: 'meal_logger_gold',
    name: 'Nutrition Tracker',
    description: 'Log 200 meals',
    icon: 'silverware-fork-knife',
    category: BadgeCategory.NUTRITION,
    level: BadgeLevel.GOLD,
    pointsValue: 300,
    unlockedAt: 200,
  },
  
  // Streak badges
  {
    id: 'consistency_bronze',
    name: 'Consistency Starter',
    description: 'Maintain a 7-day streak',
    icon: 'calendar-check',
    category: BadgeCategory.CONSISTENCY,
    level: BadgeLevel.BRONZE,
    pointsValue: 70,
    unlockedAt: 7,
  },
  {
    id: 'consistency_silver',
    name: 'Consistency Builder',
    description: 'Maintain a 30-day streak',
    icon: 'calendar-check',
    category: BadgeCategory.CONSISTENCY,
    level: BadgeLevel.SILVER,
    pointsValue: 300,
    unlockedAt: 30,
  },
  {
    id: 'consistency_gold',
    name: 'Consistency Master',
    description: 'Maintain a 100-day streak',
    icon: 'calendar-check',
    category: BadgeCategory.CONSISTENCY,
    level: BadgeLevel.GOLD,
    pointsValue: 1000,
    unlockedAt: 100,
  },
  
  // Water tracking badges
  {
    id: 'hydration_bronze',
    name: 'Hydration Beginner',
    description: 'Track water intake for 7 days',
    icon: 'cup-water',
    category: BadgeCategory.NUTRITION,
    level: BadgeLevel.BRONZE,
    pointsValue: 70,
    unlockedAt: 7,
  },
  {
    id: 'hydration_silver',
    name: 'Hydration Enthusiast',
    description: 'Track water intake for 30 days',
    icon: 'cup-water',
    category: BadgeCategory.NUTRITION,
    level: BadgeLevel.SILVER,
    pointsValue: 300,
    unlockedAt: 30,
  },
  {
    id: 'hydration_gold',
    name: 'Hydration Master',
    description: 'Meet your water goal for 30 days',
    icon: 'cup-water',
    category: BadgeCategory.NUTRITION,
    level: BadgeLevel.GOLD,
    pointsValue: 400,
    unlockedAt: 30,
  },
  
  // Goal completion badges
  {
    id: 'goal_achiever_bronze',
    name: 'Goal Setter',
    description: 'Complete 5 challenges',
    icon: 'trophy',
    category: BadgeCategory.SPECIAL,
    level: BadgeLevel.BRONZE,
    pointsValue: 100,
    unlockedAt: 5,
  },
  {
    id: 'goal_achiever_silver',
    name: 'Goal Crusher',
    description: 'Complete 15 challenges',
    icon: 'trophy',
    category: BadgeCategory.SPECIAL,
    level: BadgeLevel.SILVER,
    pointsValue: 300,
    unlockedAt: 15,
  },
  {
    id: 'goal_achiever_gold',
    name: 'Achievement Hunter',
    description: 'Complete 30 challenges',
    icon: 'trophy',
    category: BadgeCategory.SPECIAL,
    level: BadgeLevel.GOLD,
    pointsValue: 600,
    unlockedAt: 30,
  },
];

// Predefined achievements
const predefinedAchievements: Achievement[] = [
  {
    id: 'first_meal_log',
    name: 'First Steps',
    description: 'Log your first meal',
    icon: 'food-apple',
    isSecret: false,
    isCompleted: false,
    progress: 0,
    target: 1,
    pointsReward: 10,
  },
  {
    id: 'complete_profile',
    name: 'Identity Established',
    description: 'Complete your profile with all details',
    icon: 'account',
    isSecret: false,
    isCompleted: false,
    progress: 0,
    target: 1,
    pointsReward: 20,
  },
  {
    id: 'first_week_streak',
    name: 'Consistency Week',
    description: 'Maintain a 7-day app usage streak',
    icon: 'calendar-week',
    isSecret: false,
    isCompleted: false,
    progress: 0,
    target: 7,
    pointsReward: 50,
    badgeReward: 'consistency_bronze',
  },
  {
    id: 'nutrition_goals_week',
    name: 'Nutrition Tracker',
    description: 'Meet your nutrition goals for 7 days',
    icon: 'nutrition',
    isSecret: false,
    isCompleted: false,
    progress: 0,
    target: 7,
    pointsReward: 70,
  },
  {
    id: 'challenge_master',
    name: 'Challenge Accepted',
    description: 'Complete 5 different challenges',
    icon: 'trophy',
    isSecret: false,
    isCompleted: false,
    progress: 0,
    target: 5,
    pointsReward: 100,
    badgeReward: 'goal_achiever_bronze',
  },
  {
    id: 'meal_variety',
    name: 'Variety is the Spice of Life',
    description: 'Log 20 different food items',
    icon: 'food-variant',
    isSecret: false,
    isCompleted: false,
    progress: 0,
    target: 20,
    pointsReward: 60,
  },
  {
    id: 'water_master',
    name: 'Hydration Master',
    description: 'Meet your daily water goal for 10 days',
    icon: 'cup-water',
    isSecret: false,
    isCompleted: false,
    progress: 0,
    target: 10,
    pointsReward: 80,
    badgeReward: 'hydration_bronze',
  },
  {
    id: 'perfect_week',
    name: 'Perfect Week',
    description: 'Log all meals and meet all nutrition goals for 7 consecutive days',
    icon: 'star',
    isSecret: false,
    isCompleted: false,
    progress: 0,
    target: 7,
    pointsReward: 150,
  },
  {
    id: 'early_bird',
    name: 'Early Bird',
    description: 'Log breakfast before 8 AM for 5 days',
    icon: 'weather-sunset-up',
    isSecret: true,
    isCompleted: false,
    progress: 0,
    target: 5,
    pointsReward: 50,
  },
  {
    id: 'night_owl',
    name: 'Night Owl',
    description: 'Log dinner after 8 PM for 5 days',
    icon: 'weather-night',
    isSecret: true,
    isCompleted: false,
    progress: 0,
    target: 5,
    pointsReward: 50,
  },
];

/**
 * Initialize user rewards data
 */
export async function initializeUserRewards(): Promise<UserRewards> {
  try {
    const userId = await getUserId();
    
    // Check if rewards data already exists
    const existingRewards = await AsyncStorage.getItem(`${REWARDS_KEY}_${userId}`);
    if (existingRewards) {
      return JSON.parse(existingRewards);
    }
    
    // Initialize default rewards
    const defaultRewards: UserRewards = {
      totalPoints: 0,
      level: 1,
      badgeCount: 0,
      achievements: 0,
      streaks: {
        [StreakType.DAILY_LOGIN]: createDefaultStreak(StreakType.DAILY_LOGIN),
        [StreakType.MEAL_LOGGING]: createDefaultStreak(StreakType.MEAL_LOGGING),
        [StreakType.WATER_TRACKING]: createDefaultStreak(StreakType.WATER_TRACKING),
        [StreakType.CALORIE_GOAL]: createDefaultStreak(StreakType.CALORIE_GOAL),
        [StreakType.PROTEIN_GOAL]: createDefaultStreak(StreakType.PROTEIN_GOAL),
      },
    };
    
    // Save to local storage
    await AsyncStorage.setItem(`${REWARDS_KEY}_${userId}`, JSON.stringify(defaultRewards));
    
    // Initialize badges and achievements
    await initializeBadges();
    await initializeAchievements();
    
    return defaultRewards;
  } catch (error) {
    console.error('Error initializing user rewards:', error);
    return {
      totalPoints: 0,
      level: 1,
      badgeCount: 0,
      achievements: 0,
      streaks: {
        [StreakType.DAILY_LOGIN]: createDefaultStreak(StreakType.DAILY_LOGIN),
        [StreakType.MEAL_LOGGING]: createDefaultStreak(StreakType.MEAL_LOGGING),
        [StreakType.WATER_TRACKING]: createDefaultStreak(StreakType.WATER_TRACKING),
        [StreakType.CALORIE_GOAL]: createDefaultStreak(StreakType.CALORIE_GOAL),
        [StreakType.PROTEIN_GOAL]: createDefaultStreak(StreakType.PROTEIN_GOAL),
      },
    };
  }
}

/**
 * Create default streak object
 */
function createDefaultStreak(type: StreakType): Streak {
  return {
    type,
    currentStreak: 0,
    longestStreak: 0,
    lastUpdateDate: '',
    streakHistory: [],
  };
}

/**
 * Initialize user badges
 */
async function initializeBadges(): Promise<void> {
  try {
    const userId = await getUserId();
    
    // Check if badges already exist
    const existingBadges = await AsyncStorage.getItem(`${BADGES_KEY}_${userId}`);
    if (existingBadges) {
      return;
    }
    
    // Initialize with predefined badges (all locked)
    await AsyncStorage.setItem(`${BADGES_KEY}_${userId}`, JSON.stringify(predefinedBadges));
    
    // Check if badges exist in Firestore
    const badgesQuery = query(
      collection(firestore, 'user_badges'),
      where('user_id', '==', userId)
    );
    
    const badgesSnapshot = await getDocs(badgesQuery);
    
    if (badgesSnapshot.empty) {
      // Insert predefined badges for user
      const badgeRecords = predefinedBadges.map(badge => ({
        user_id: userId,
        badge_id: badge.id,
        progress: 0,
        is_unlocked: false,
      }));
      
      // Add each badge to Firestore
      for (const badgeRecord of badgeRecords) {
        await addDoc(collection(firestore, 'user_badges'), {
          ...badgeRecord,
          created_at: serverTimestamp()
        });
      }
    }
  } catch (error) {
    console.error('Error initializing badges:', error);
  }
}

/**
 * Initialize user achievements
 */
async function initializeAchievements(): Promise<void> {
  try {
    const userId = await getUserId();
    
    // Check if achievements already exist
    const existingAchievements = await AsyncStorage.getItem(`${ACHIEVEMENT_KEY}_${userId}`);
    if (existingAchievements) {
      return;
    }
    
    // Initialize with predefined achievements
    await AsyncStorage.setItem(`${ACHIEVEMENT_KEY}_${userId}`, JSON.stringify(predefinedAchievements));
    
    // Check if achievements exist in Firestore
    const achievementsQuery = query(
      collection(firestore, 'user_achievements'),
      where('user_id', '==', userId)
    );
    
    const achievementsSnapshot = await getDocs(achievementsQuery);
    
    if (achievementsSnapshot.empty) {
      // Insert predefined achievements for user
      const achievementRecords = predefinedAchievements.map(achievement => ({
        user_id: userId,
        achievement_id: achievement.id,
        progress: 0,
        is_completed: false,
      }));
      
      // Add each achievement to Firestore
      for (const achievement of achievementRecords) {
        await addDoc(collection(firestore, 'user_achievements'), {
          ...achievement,
          created_at: serverTimestamp()
        });
      }
    }
  } catch (error) {
    console.error('Error initializing achievements:', error);
  }
}

/**
 * Get user rewards
 */
export async function getUserRewards(): Promise<UserRewards> {
  try {
    // Ensure rewards are initialized
    return await initializeUserRewards();
  } catch (error) {
    console.error('Error getting user rewards:', error);
    throw error;
  }
}

/**
 * Get user badges
 */
export async function getUserBadges(): Promise<Badge[]> {
  try {
    const userId = await getUserId();
    
    // Check local storage first
    const cachedBadges = await AsyncStorage.getItem(`${BADGES_KEY}_${userId}`);
    if (cachedBadges) {
      return JSON.parse(cachedBadges);
    }
    
    // Initialize badges if not found
    await initializeBadges();
    const initBadges = await AsyncStorage.getItem(`${BADGES_KEY}_${userId}`);
    return JSON.parse(initBadges || '[]');
  } catch (error) {
    console.error('Error getting user badges:', error);
    return [];
  }
}

/**
 * Get user achievements
 */
export async function getUserAchievements(): Promise<Achievement[]> {
  try {
    const userId = await getUserId();
    
    // Check local storage first
    const cachedAchievements = await AsyncStorage.getItem(`${ACHIEVEMENT_KEY}_${userId}`);
    if (cachedAchievements) {
      return JSON.parse(cachedAchievements);
    }
    
    // Initialize achievements if not found
    await initializeAchievements();
    const initAchievements = await AsyncStorage.getItem(`${ACHIEVEMENT_KEY}_${userId}`);
    return JSON.parse(initAchievements || '[]');
  } catch (error) {
    console.error('Error getting user achievements:', error);
    return [];
  }
}

/**
 * Add reward points
 */
export async function addRewardPoints(
  points: number, 
  type: RewardType, 
  description: string, 
  relatedId?: string
): Promise<{ success: boolean; newTotal: number; newReward?: Reward }> {
  try {
    const userId = await getUserId();
    
    // Get current rewards
    const rewards = await getUserRewards();
    
    // Create new reward
    const newReward: Reward = {
      id: generateId(),
      userId,
      type,
      points,
      description,
      dateEarned: new Date().toISOString(),
      relatedId,
    };
    
    // Update total points
    rewards.totalPoints += points;
    
    // Calculate new level (1 level per 500 points)
    const newLevel = Math.floor(rewards.totalPoints / 500) + 1;
    const leveledUp = newLevel > rewards.level;
    rewards.level = newLevel;
    
    // Save updated rewards
    await AsyncStorage.setItem(`${REWARDS_KEY}_${userId}`, JSON.stringify(rewards));
    
    // Save reward to history in Firestore
    await addDoc(collection(firestore, 'user_rewards'), {
      user_id: userId,
      type: type,
      points: points,
      description: description,
      date_earned: newReward.dateEarned,
      related_id: relatedId,
      created_at: serverTimestamp()
    });
    
    // Check for level up achievements
    if (leveledUp) {
      await checkAndUpdateAchievement('level_up', 1);
    }
    
    return {
      success: true,
      newTotal: rewards.totalPoints,
      newReward,
    };
  } catch (error) {
    console.error('Error adding reward points:', error);
    return { success: false, newTotal: 0 };
  }
}

/**
 * Update streak
 */
export async function updateStreak(type: StreakType, completed: boolean): Promise<Streak> {
  try {
    const userId = await getUserId();
    const rewards = await getUserRewards();
    const streak = rewards.streaks[type];
    const today = new Date().toISOString().split('T')[0];
    
    // If this is the first update or it's a new day
    if (!streak.lastUpdateDate || streak.lastUpdateDate !== today) {
      // If completed, increment streak
      if (completed) {
        // Check if the last update was yesterday to continue the streak
        const lastDate = streak.lastUpdateDate ? new Date(streak.lastUpdateDate) : null;
        const isYesterday = lastDate ? differenceInDays(new Date(), lastDate) === 1 : false;
        
        if (isYesterday || !streak.lastUpdateDate) {
          // Continue streak
          streak.currentStreak += 1;
        } else {
          // Streak broken, start new streak
          streak.currentStreak = 1;
        }
        
        // Update longest streak if needed
        if (streak.currentStreak > streak.longestStreak) {
          streak.longestStreak = streak.currentStreak;
        }
      } else {
        // Reset streak if not completed
        streak.currentStreak = 0;
      }
      
      // Update history
      streak.streakHistory.push({ date: today, completed });
      // Keep only the last 100 entries to prevent storage growth
      if (streak.streakHistory.length > 100) {
        streak.streakHistory = streak.streakHistory.slice(-100);
      }
      
      // Update lastUpdateDate
      streak.lastUpdateDate = today;
      
      // Save updated streaks
      rewards.streaks[type] = streak;
      await AsyncStorage.setItem(`${REWARDS_KEY}_${userId}`, JSON.stringify(rewards));
      
      // Check for streak-related achievements and badges
      if (completed) {
        await checkStreakAchievements(type, streak.currentStreak);
      }
      
      // Update streak in Firestore
      const streakQuery = query(
        collection(firestore, 'user_streaks'),
        where('user_id', '==', userId),
        where('streak_type', '==', type)
      );
      
      const streakSnapshot = await getDocs(streakQuery);
      
      if (streakSnapshot.empty) {
        // Create new streak document
        await addDoc(collection(firestore, 'user_streaks'), {
          user_id: userId,
          streak_type: type,
          current_streak: streak.currentStreak,
          longest_streak: streak.longestStreak,
          last_update_date: today,
          created_at: serverTimestamp(),
          updated_at: serverTimestamp()
        });
      } else {
        // Update existing streak document
        await updateDoc(streakSnapshot.docs[0].ref, {
          current_streak: streak.currentStreak,
          longest_streak: streak.longestStreak,
          last_update_date: today,
          updated_at: serverTimestamp()
        });
      }
    }
    
    return streak;
  } catch (error) {
    console.error('Error updating streak:', error);
    return createDefaultStreak(type);
  }
}

/**
 * Check and update achievements related to streaks
 */
async function checkStreakAchievements(type: StreakType, currentStreak: number): Promise<void> {
  try {
    // Update streak achievements
    if (type === StreakType.DAILY_LOGIN) {
      // Check for streak-related achievements
      if (currentStreak >= 7) {
        await checkAndUpdateAchievement('first_week_streak', 7);
      }
      
      // Check for streak badges
      if (currentStreak >= 7) {
        await unlockBadge('consistency_bronze');
      }
      if (currentStreak >= 30) {
        await unlockBadge('consistency_silver');
      }
      if (currentStreak >= 100) {
        await unlockBadge('consistency_gold');
      }
    }
    
    // Check for perfect week
    if (currentStreak >= 7 && 
        (type === StreakType.MEAL_LOGGING || 
         type === StreakType.CALORIE_GOAL || 
         type === StreakType.PROTEIN_GOAL)) {
      await checkAndUpdateAchievement('perfect_week', 1);
    }
    
    // Water tracking achievements
    if (type === StreakType.WATER_TRACKING) {
      if (currentStreak >= 7) {
        await unlockBadge('hydration_bronze');
      }
      if (currentStreak >= 30) {
        await unlockBadge('hydration_silver');
      }
    }
  } catch (error) {
    console.error('Error checking streak achievements:', error);
  }
}

/**
 * Check and update achievement progress
 */
export async function checkAndUpdateAchievement(achievementId: string, progressIncrement: number = 1): Promise<{ isCompleted: boolean; isNewlyCompleted: boolean }> {
  try {
    const userId = await getUserId();
    
    // Get achievements
    const achievements = await getUserAchievements();
    const achievement = achievements.find(a => a.id === achievementId);
    
    if (!achievement) {
      return { isCompleted: false, isNewlyCompleted: false };
    }
    
    // Skip if already completed
    if (achievement.isCompleted) {
      return { isCompleted: true, isNewlyCompleted: false };
    }
    
    // Update progress
    achievement.progress += progressIncrement;
    
    // Check if completed
    let isNewlyCompleted = false;
    if (achievement.progress >= achievement.target && !achievement.isCompleted) {
      achievement.isCompleted = true;
      achievement.dateCompleted = new Date().toISOString();
      isNewlyCompleted = true;
      
      // Award points
      await addRewardPoints(
        achievement.pointsReward,
        RewardType.GOAL_ACHIEVEMENT,
        `Completed achievement: ${achievement.name}`,
        achievement.id
      );
      
      // Unlock badge if one is associated
      if (achievement.badgeReward) {
        await unlockBadge(achievement.badgeReward);
      }
      
      // Update user rewards
      const rewards = await getUserRewards();
      rewards.achievements += 1;
      await AsyncStorage.setItem(`${REWARDS_KEY}_${userId}`, JSON.stringify(rewards));
    }
    
    // Save updated achievements
    await AsyncStorage.setItem(`${ACHIEVEMENT_KEY}_${userId}`, JSON.stringify(achievements));
    
    // Query for existing achievement document
    const achievementQuery = query(
      collection(firestore, 'user_achievements'),
      where('user_id', '==', userId),
      where('achievement_id', '==', achievement.id)
    );
    
    const achievementSnapshot = await getDocs(achievementQuery);
    
    if (achievementSnapshot.empty) {
      // Create new achievement document
      await addDoc(collection(firestore, 'user_achievements'), {
        user_id: userId,
        achievement_id: achievement.id,
        progress: achievement.progress,
        is_completed: achievement.isCompleted,
        date_completed: achievement.dateCompleted,
        created_at: serverTimestamp(),
        updated_at: serverTimestamp()
      });
    } else {
      // Update existing achievement document
      await updateDoc(achievementSnapshot.docs[0].ref, {
        progress: achievement.progress,
        is_completed: achievement.isCompleted,
        date_completed: achievement.dateCompleted,
        updated_at: serverTimestamp()
      });
    }
    
    return { 
      isCompleted: achievement.isCompleted, 
      isNewlyCompleted 
    };
  } catch (error) {
    console.error('Error checking achievement:', error);
    return { isCompleted: false, isNewlyCompleted: false };
  }
}

/**
 * Unlock a badge
 */
export async function unlockBadge(badgeId: string): Promise<{ success: boolean; badge?: Badge }> {
  try {
    const userId = await getUserId();
    
    // Get badges
    const badges = await getUserBadges();
    const badge = badges.find(b => b.id === badgeId);
    
    if (!badge) {
      return { success: false };
    }
    
    // Skip if already earned
    if (badge.dateEarned) {
      return { success: true, badge };
    }
    
    // Unlock badge
    badge.dateEarned = new Date().toISOString();
    
    // Add points for earning badge
    await addRewardPoints(
      badge.pointsValue,
      RewardType.GOAL_ACHIEVEMENT,
      `Earned badge: ${badge.name}`,
      badge.id
    );
    
    // Update badges
    await AsyncStorage.setItem(`${BADGES_KEY}_${userId}`, JSON.stringify(badges));
    
    // Update user rewards
    const rewards = await getUserRewards();
    rewards.badgeCount += 1;
    await AsyncStorage.setItem(`${REWARDS_KEY}_${userId}`, JSON.stringify(rewards));
    
    // Query for existing badge document
    const badgeQuery = query(
      collection(firestore, 'user_badges'),
      where('user_id', '==', userId),
      where('badge_id', '==', badge.id)
    );
    
    const badgeSnapshot = await getDocs(badgeQuery);
    
    if (badgeSnapshot.empty) {
      // Create new badge document
      await addDoc(collection(firestore, 'user_badges'), {
        user_id: userId,
        badge_id: badge.id,
        is_unlocked: true,
        date_unlocked: badge.dateEarned,
        created_at: serverTimestamp(),
        updated_at: serverTimestamp()
      });
    } else {
      // Update existing badge document
      await updateDoc(badgeSnapshot.docs[0].ref, {
        is_unlocked: true,
        date_unlocked: badge.dateEarned,
        updated_at: serverTimestamp()
      });
    }
    
    return { success: true, badge };
  } catch (error) {
    console.error('Error unlocking badge:', error);
    return { success: false };
  }
}

/**
 * Update badge progress
 */
export async function updateBadgeProgress(badgeId: string, increment: number = 1): Promise<{ success: boolean; isUnlocked: boolean; badge?: Badge }> {
  try {
    const userId = await getUserId();
    
    // Get badges
    const badges = await getUserBadges();
    const badge = badges.find(b => b.id === badgeId);
    
    if (!badge || badge.dateEarned) {
      return { success: false, isUnlocked: !!badge?.dateEarned };
    }
    
    // Update progress
    badge.progress = (badge.progress || 0) + increment;
    
    // Check if badge should be unlocked
    let isUnlocked = false;
    if (badge.unlockedAt && badge.progress >= badge.unlockedAt) {
      badge.dateEarned = new Date().toISOString();
      isUnlocked = true;
      
      // Add points for earning badge
      await addRewardPoints(
        badge.pointsValue,
        RewardType.GOAL_ACHIEVEMENT,
        `Earned badge: ${badge.name}`,
        badge.id
      );
      
      // Update user rewards
      const rewards = await getUserRewards();
      rewards.badgeCount += 1;
      await AsyncStorage.setItem(`${REWARDS_KEY}_${userId}`, JSON.stringify(rewards));
    }
    
    // Save updated badges
    await AsyncStorage.setItem(`${BADGES_KEY}_${userId}`, JSON.stringify(badges));
    
    // Query for existing badge document
    const badgeQuery = query(
      collection(firestore, 'user_badges'),
      where('user_id', '==', userId),
      where('badge_id', '==', badge.id)
    );
    
    const badgeSnapshot = await getDocs(badgeQuery);
    
    if (badgeSnapshot.empty) {
      // Create new badge document
      await addDoc(collection(firestore, 'user_badges'), {
        user_id: userId,
        badge_id: badge.id,
        progress: badge.progress,
        is_unlocked: isUnlocked,
        date_unlocked: badge.dateEarned,
        created_at: serverTimestamp(),
        updated_at: serverTimestamp()
      });
    } else {
      // Update existing badge document
      await updateDoc(badgeSnapshot.docs[0].ref, {
        progress: badge.progress,
        is_unlocked: isUnlocked,
        date_unlocked: badge.dateEarned,
        updated_at: serverTimestamp()
      });
    }
    
    return { success: true, isUnlocked, badge };
  } catch (error) {
    console.error('Error updating badge progress:', error);
    return { success: false, isUnlocked: false };
  }
}

/**
 * Track meal logging for rewards
 */
export async function trackMealLogging(): Promise<void> {
  try {
    // Update meal logging streak
    await updateStreak(StreakType.MEAL_LOGGING, true);
    
    // Update achievements related to meal logging
    await checkAndUpdateAchievement('first_meal_log', 1);
    
    // Update badges related to meal logging
    await updateBadgeProgress('meal_logger_bronze', 1);
    await updateBadgeProgress('meal_logger_silver', 1);
    await updateBadgeProgress('meal_logger_gold', 1);
  } catch (error) {
    console.error('Error tracking meal logging:', error);
  }
}

/**
 * Track water intake for rewards
 */
export async function trackWaterIntake(goalMet: boolean): Promise<void> {
  try {
    // Update water tracking streak if goal met
    await updateStreak(StreakType.WATER_TRACKING, goalMet);
    
    // Update water-related achievements
    if (goalMet) {
      await checkAndUpdateAchievement('water_master', 1);
    }
  } catch (error) {
    console.error('Error tracking water intake:', error);
  }
}

/**
 * Track daily nutrition goal achievement
 */
export async function trackNutritionGoals(calorieGoalMet: boolean, proteinGoalMet: boolean): Promise<void> {
  try {
    // Update calorie goal streak
    if (calorieGoalMet) {
      await updateStreak(StreakType.CALORIE_GOAL, true);
    }
    
    // Update protein goal streak
    if (proteinGoalMet) {
      await updateStreak(StreakType.PROTEIN_GOAL, true);
    }
    
    // If both met, update nutrition goals achievement
    if (calorieGoalMet && proteinGoalMet) {
      await checkAndUpdateAchievement('nutrition_goals_week', 1);
    }
  } catch (error) {
    console.error('Error tracking nutrition goals:', error);
  }
}

/**
 * Track daily login for streak
 */
export async function trackDailyLogin(): Promise<Streak> {
  try {
    return await updateStreak(StreakType.DAILY_LOGIN, true);
  } catch (error) {
    console.error('Error tracking daily login:', error);
    return createDefaultStreak(StreakType.DAILY_LOGIN);
  }
}

/**
 * Track challenge completion for rewards
 */
export async function trackChallengeCompletion(challenge: Challenge): Promise<void> {
  try {
    // Add points based on challenge difficulty and duration
    let pointsEarned = challenge.rewards?.points || calculateChallengePoints(challenge);
    
    // Add reward points
    await addRewardPoints(
      pointsEarned,
      RewardType.CHALLENGE_COMPLETION,
      `Completed challenge: ${challenge.title}`,
      challenge.id
    );
    
    // Update challenge achievements
    await checkAndUpdateAchievement('challenge_master', 1);
    
    // Update badge progress for goal achiever badges
    await updateBadgeProgress('goal_achiever_bronze', 1);
    await updateBadgeProgress('goal_achiever_silver', 1);
    await updateBadgeProgress('goal_achiever_gold', 1);
    
    // If challenge has specific badge, unlock it
    if (challenge.rewards?.badge) {
      await unlockBadge(challenge.rewards.badge);
    }
  } catch (error) {
    console.error('Error tracking challenge completion:', error);
  }
}

/**
 * Calculate points for a challenge based on difficulty and duration
 */
function calculateChallengePoints(challenge: Challenge): number {
  const difficultyMultiplier = {
    [ChallengeDifficulty.EASY]: 1,
    [ChallengeDifficulty.MEDIUM]: 1.5,
    [ChallengeDifficulty.HARD]: 2,
  };
  
  const basePoints = 10;
  const durationFactor = Math.min(1 + (challenge.duration / 30), 3); // Cap at 3x for very long challenges
  
  return Math.round(basePoints * difficultyMultiplier[challenge.difficulty] * durationFactor);
}

/**
 * Check progress on all achievements
 */
export async function checkAllAchievements(): Promise<number> {
  try {
    const userId = await getUserId();
    const profile = await getUserProfile();
    
    // Check profile-related achievements
    if (profile && 
        profile.age && 
        profile.gender && 
        profile.height && 
        profile.weight && 
        profile.targetWeight) {
      await checkAndUpdateAchievement('complete_profile', 1);
    }
    
    // Get user streaks to check streak-related achievements
    const rewards = await getUserRewards();
    
    // Get login streak
    const loginStreak = rewards.streaks[StreakType.DAILY_LOGIN];
    if (loginStreak.currentStreak >= 7) {
      await checkAndUpdateAchievement('first_week_streak', 7);
    }
    
    // Count completed achievements
    const achievements = await getUserAchievements();
    const completedCount = achievements.filter(a => a.isCompleted).length;
    
    return completedCount;
  } catch (error) {
    console.error('Error checking all achievements:', error);
    return 0;
  }
}

/**
 * Get recent rewards history
 */
export async function getRewardsHistory(limit: number = 10): Promise<Reward[]> {
  try {
    const userId = await getUserId();
    
    // Get from Firestore
    const rewardsQuery = query(
      collection(firestore, 'user_rewards'),
      where('user_id', '==', userId),
      orderBy('date_earned', 'desc'),
      firestoreLimit(limit)
    );
    
    const rewardsSnapshot = await getDocs(rewardsQuery);
    
    if (rewardsSnapshot.empty) {
      return [];
    }
    
    return rewardsSnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        userId: data.user_id,
        type: data.type as RewardType,
        points: data.points,
        description: data.description,
        dateEarned: data.date_earned,
        relatedId: data.related_id,
      };
    });
  } catch (error) {
    console.error('Error getting rewards history:', error);
    return [];
  }
}

/**
 * Get next badges to unlock
 */
export async function getNextBadgesToUnlock(limit: number = 3): Promise<Badge[]> {
  try {
    const badges = await getUserBadges();
    return badges
      .filter(badge => !badge.dateEarned)
      .sort((a, b) => {
        // If both have progress, compare progress percentage
        if (a.progress && b.progress && a.unlockedAt && b.unlockedAt) {
          const aPercentage = a.progress / a.unlockedAt;
          const bPercentage = b.progress / b.unlockedAt;
          return bPercentage - aPercentage; // Higher percentage first
        }
        // If only one has progress, prioritize it
        if (a.progress && a.unlockedAt) return -1;
        if (b.progress && b.unlockedAt) return 1;
        // Otherwise sort by points value (smaller first as they're likely easier)
        return a.pointsValue - b.pointsValue;
      })
      .slice(0, limit);
  } catch (error) {
    console.error('Error getting next badges to unlock:', error);
    return [];
  }
}

/**
 * Get level info for current points
 */
export function getLevelInfo(points: number): { level: number; currentPoints: number; nextLevelPoints: number; progress: number } {
  const pointsPerLevel = 500;
  const level = Math.floor(points / pointsPerLevel) + 1;
  const currentLevelMinPoints = (level - 1) * pointsPerLevel;
  const nextLevelPoints = level * pointsPerLevel;
  const currentPoints = points - currentLevelMinPoints;
  const progress = (currentPoints / pointsPerLevel) * 100;
  
  return {
    level,
    currentPoints,
    nextLevelPoints,
    progress,
  };
}

/**
 * Helper function to get user ID
 */
async function getUserId(): Promise<string> {
  const currentUser = auth.currentUser;
  return currentUser?.uid || '';
}

/**
 * Helper function to generate ID
 */
function generateId(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
} 