/**
 * Scan Results Storage Firebase Service
 * 
 * Implementation of scan results storage using Firebase Storage and Firestore.
 * Handles storing and retrieving food scan results, including LiDAR volume data.
 */

import { ref, uploadBytes, getDownloadURL, deleteObject, listAll } from 'firebase/storage';
import { 
  collection, doc, getDoc, getDocs, query, where, orderBy, limit,
  setDoc, addDoc, updateDoc, deleteDoc, Timestamp, serverTimestamp 
} from 'firebase/firestore';
import { storage, firestore } from '@/lib/firebase';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';

// Types
export interface ScanResult {
  id: string;
  userId: string;
  date: string; // ISO date string (YYYY-MM-DD)
  timestamp: string; // ISO datetime string
  foodItems: FoodItem[];
  scanType: 'lidar' | 'photo' | 'barcode' | 'text';
  sourceImage?: string; // URL to the scan source image
  volumeData?: VolumeData;
  segmentationData?: SegmentationData;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface FoodItem {
  id: string;
  name: string;
  category?: string;
  amount?: number;
  unit?: string;
  calories?: number;
  nutrients?: Record<string, number>;
  confidence?: number;
  boundingBox?: BoundingBox;
  volumeEstimate?: number; // In cubic centimeters (cm³)
}

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface VolumeData {
  points?: ArrayBuffer; // Point cloud data
  mesh?: ArrayBuffer; // 3D mesh data
  totalVolume?: number; // Total volume in cubic centimeters (cm³)
  surfaceArea?: number; // Surface area in square centimeters (cm²)
  dimensions?: { // Maximum dimensions in centimeters
    width: number;
    height: number;
    depth: number;
  };
  metadata?: Record<string, any>;
}

export interface SegmentationData {
  maskUrl?: string; // URL to segmentation mask
  masks?: Record<string, string>; // URLs to individual masks by food ID
  confidence?: number;
  metadata?: Record<string, any>;
}

/**
 * Save a scan result to Firebase
 * 
 * @param userId User ID
 * @param scanResult Scan result data
 * @param sourceImageUri Optional URI of the source image to upload
 * @param volumeDataBuffer Optional volume data to upload
 * @returns Promise with success status and saved scan result data or error
 */
export async function saveScanResult(
  userId: string,
  scanResult: Omit<ScanResult, 'id' | 'userId' | 'createdAt' | 'updatedAt'>,
  sourceImageUri?: string,
  volumeDataBuffer?: ArrayBuffer
): Promise<{
  success: boolean;
  data?: ScanResult;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    const scanId = crypto.randomUUID();
    const now = new Date();

    // Create storage paths
    const sourceImagePath = sourceImageUri ? 
      `${userId}/scan_results/${scanId}/source.jpg` : undefined;
    const volumeDataPath = volumeDataBuffer ? 
      `${userId}/scan_results/${scanId}/volume.bin` : undefined;

    // If source image provided, upload it
    let sourceImageUrl: string | undefined;
    if (sourceImageUri) {
      try {
        const imageBlob = await uriToBlob(sourceImageUri);
        const imageRef = ref(storage, sourceImagePath);
        
        await uploadBytes(imageRef, imageBlob, {
          contentType: 'image/jpeg',
          customMetadata: {
            userId,
            scanId,
            uploadedAt: now.toISOString()
          }
        });
        
        sourceImageUrl = await getDownloadURL(imageRef);
      } catch (imageError) {
        console.error('Error uploading source image:', imageError);
        // Continue without the image
      }
    }

    // If volume data provided, upload it
    let volumeDataUrl: string | undefined;
    if (volumeDataBuffer && volumeDataPath) {
      try {
        const volumeRef = ref(storage, volumeDataPath);
        
        await uploadBytes(volumeRef, volumeDataBuffer, {
          contentType: 'application/octet-stream',
          customMetadata: {
            userId,
            scanId,
            uploadedAt: now.toISOString()
          }
        });
        
        volumeDataUrl = await getDownloadURL(volumeRef);
      } catch (volumeError) {
        console.error('Error uploading volume data:', volumeError);
        // Continue without the volume data
      }
    }

    // Prepare scan result with updated URLs
    const updatedScanResult: ScanResult = {
      id: scanId,
      userId,
      date: scanResult.date || now.toISOString().split('T')[0],
      timestamp: scanResult.timestamp || now.toISOString(),
      foodItems: scanResult.foodItems || [],
      scanType: scanResult.scanType,
      sourceImage: sourceImageUrl || scanResult.sourceImage,
      volumeData: {
        ...(scanResult.volumeData || {}),
        metadata: {
          ...(scanResult.volumeData?.metadata || {}),
          storagePath: volumeDataPath,
          downloadUrl: volumeDataUrl
        }
      },
      segmentationData: scanResult.segmentationData,
      metadata: {
        ...(scanResult.metadata || {}),
        sourceImagePath,
        volumeDataPath
      },
      createdAt: now.toISOString(),
      updatedAt: now.toISOString()
    };

    // Save to Firestore
    await setDoc(doc(firestore, 'scan_results', scanId), {
      userId,
      date: updatedScanResult.date,
      timestamp: updatedScanResult.timestamp,
      foodItems: updatedScanResult.foodItems,
      scanType: updatedScanResult.scanType,
      sourceImage: updatedScanResult.sourceImage,
      volumeData: updatedScanResult.volumeData,
      segmentationData: updatedScanResult.segmentationData,
      metadata: updatedScanResult.metadata,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return {
      success: true,
      data: updatedScanResult
    };
  } catch (error) {
    console.error('Error saving scan result:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get a scan result by ID
 * 
 * @param scanId Scan result ID
 * @returns Promise with success status and scan result data or error
 */
export async function getScanResult(
  scanId: string
): Promise<{
  success: boolean;
  data?: ScanResult;
  error?: string;
}> {
  try {
    const scanRef = doc(firestore, 'scan_results', scanId);
    const scanSnap = await getDoc(scanRef);

    if (!scanSnap.exists()) {
      return {
        success: false,
        error: 'Scan result not found'
      };
    }

    const data = scanSnap.data();

    const scanResult: ScanResult = {
      id: scanSnap.id,
      userId: data.userId,
      date: data.date,
      timestamp: data.timestamp,
      foodItems: data.foodItems || [],
      scanType: data.scanType,
      sourceImage: data.sourceImage,
      volumeData: data.volumeData,
      segmentationData: data.segmentationData,
      metadata: data.metadata || {},
      createdAt: data.createdAt instanceof Timestamp ? 
        data.createdAt.toDate().toISOString() : data.createdAt,
      updatedAt: data.updatedAt instanceof Timestamp ? 
        data.updatedAt.toDate().toISOString() : data.updatedAt
    };

    return {
      success: true,
      data: scanResult
    };
  } catch (error) {
    console.error('Error getting scan result:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get scan results for a user by date range
 * 
 * @param userId User ID
 * @param startDate Start date (inclusive) in YYYY-MM-DD format
 * @param endDate End date (inclusive) in YYYY-MM-DD format
 * @param limit Optional limit on number of results to return
 * @returns Promise with success status and array of scan results or error
 */
export async function getScanResultsByDateRange(
  userId: string,
  startDate: string,
  endDate: string,
  resultsLimit: number = 50
): Promise<{
  success: boolean;
  data?: ScanResult[];
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    // Query Firestore
    const scanQuery = query(
      collection(firestore, 'scan_results'),
      where('userId', '==', userId),
      where('date', '>=', startDate),
      where('date', '<=', endDate),
      orderBy('date', 'desc'),
      orderBy('timestamp', 'desc'),
      limit(resultsLimit)
    );

    const querySnapshot = await getDocs(scanQuery);

    const scanResults: ScanResult[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      scanResults.push({
        id: doc.id,
        userId: data.userId,
        date: data.date,
        timestamp: data.timestamp,
        foodItems: data.foodItems || [],
        scanType: data.scanType,
        sourceImage: data.sourceImage,
        volumeData: data.volumeData,
        segmentationData: data.segmentationData,
        metadata: data.metadata || {},
        createdAt: data.createdAt instanceof Timestamp ? 
          data.createdAt.toDate().toISOString() : data.createdAt,
        updatedAt: data.updatedAt instanceof Timestamp ? 
          data.updatedAt.toDate().toISOString() : data.updatedAt
      });
    });

    return {
      success: true,
      data: scanResults
    };
  } catch (error) {
    console.error('Error getting scan results by date range:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get the latest scan result for a user
 * 
 * @param userId User ID
 * @returns Promise with success status and latest scan result or error
 */
export async function getLatestScanResult(
  userId: string
): Promise<{
  success: boolean;
  data?: ScanResult;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    // Query Firestore
    const scanQuery = query(
      collection(firestore, 'scan_results'),
      where('userId', '==', userId),
      orderBy('timestamp', 'desc'),
      limit(1)
    );

    const querySnapshot = await getDocs(scanQuery);

    if (querySnapshot.empty) {
      return {
        success: true,
        data: undefined
      };
    }

    const doc = querySnapshot.docs[0];
    const data = doc.data();

    if (!data) {
      return {
        success: false,
        error: 'No data found for document'
      };
    }

    const scanResult: ScanResult = {
      id: doc.id,
      userId: data.userId,
      date: data.date,
      timestamp: data.timestamp,
      foodItems: data.foodItems || [],
      scanType: data.scanType,
      sourceImage: data.sourceImage,
      volumeData: data.volumeData,
      segmentationData: data.segmentationData,
      metadata: data.metadata || {},
      createdAt: data.createdAt instanceof Timestamp ? 
        data.createdAt.toDate().toISOString() : data.createdAt,
      updatedAt: data.updatedAt instanceof Timestamp ? 
        data.updatedAt.toDate().toISOString() : data.updatedAt
    };

    return {
      success: true,
      data: scanResult
    };
  } catch (error) {
    console.error('Error getting latest scan result:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Update a scan result
 * 
 * @param scanId Scan result ID
 * @param updates Updates to apply to the scan result
 * @returns Promise with success status and updated scan result or error
 */
export async function updateScanResult(
  scanId: string,
  updates: Partial<Omit<ScanResult, 'id' | 'userId' | 'createdAt' | 'updatedAt'>>
): Promise<{
  success: boolean;
  data?: ScanResult;
  error?: string;
}> {
  try {
    const scanRef = doc(firestore, 'scan_results', scanId);
    const scanSnap = await getDoc(scanRef);

    if (!scanSnap.exists()) {
      return {
        success: false,
        error: 'Scan result not found'
      };
    }

    // Update the document
    const updateData: any = {
      updatedAt: serverTimestamp()
    };

    if (updates.date !== undefined) updateData.date = updates.date;
    if (updates.timestamp !== undefined) updateData.timestamp = updates.timestamp;
    if (updates.foodItems !== undefined) updateData.foodItems = updates.foodItems;
    if (updates.scanType !== undefined) updateData.scanType = updates.scanType;
    if (updates.sourceImage !== undefined) updateData.sourceImage = updates.sourceImage;
    if (updates.volumeData !== undefined) updateData.volumeData = updates.volumeData;
    if (updates.segmentationData !== undefined) updateData.segmentationData = updates.segmentationData;
    
    if (updates.metadata) {
      updateData.metadata = {
        ...(scanSnap.data().metadata || {}),
        ...updates.metadata
      };
    }

    await updateDoc(scanRef, updateData);

    // Get the updated document
    const updatedScanSnap = await getDoc(scanRef);
    const data = updatedScanSnap.data();
    
    if (!data) {
      return {
        success: false,
        error: 'Failed to retrieve updated scan data'
      };
    }

    const scanResult: ScanResult = {
      id: updatedScanSnap.id,
      userId: data.userId,
      date: data.date,
      timestamp: data.timestamp,
      foodItems: data.foodItems || [],
      scanType: data.scanType,
      sourceImage: data.sourceImage,
      volumeData: data.volumeData,
      segmentationData: data.segmentationData,
      metadata: data.metadata || {},
      createdAt: data.createdAt instanceof Timestamp ? 
        data.createdAt.toDate().toISOString() : data.createdAt,
      updatedAt: data.updatedAt instanceof Timestamp ? 
        data.updatedAt.toDate().toISOString() : data.updatedAt
    };

    return {
      success: true,
      data: scanResult
    };
  } catch (error) {
    console.error('Error updating scan result:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Delete a scan result
 * 
 * @param scanId Scan result ID
 * @returns Promise with success status or error
 */
export async function deleteScanResult(
  scanId: string
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Get the scan result first
    const scanRef = doc(firestore, 'scan_results', scanId);
    const scanSnap = await getDoc(scanRef);

    if (!scanSnap.exists()) {
      return {
        success: false,
        error: 'Scan result not found'
      };
    }

    const data = scanSnap.data();
    const metadata = data.metadata || {};

    // Delete files from storage if they exist
    if (metadata.sourceImagePath) {
      try {
        const imageRef = ref(storage, metadata.sourceImagePath);
        await deleteObject(imageRef);
      } catch (imageError) {
        console.warn('Error deleting source image:', imageError);
      }
    }

    if (metadata.volumeDataPath) {
      try {
        const volumeRef = ref(storage, metadata.volumeDataPath);
        await deleteObject(volumeRef);
      } catch (volumeError) {
        console.warn('Error deleting volume data:', volumeError);
      }
    }

    // Delete the document
    await deleteDoc(scanRef);

    return {
      success: true
    };
  } catch (error) {
    console.error('Error deleting scan result:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Delete all scan results for a user
 * 
 * @param userId User ID
 * @returns Promise with success status and count of deleted results or error
 */
export async function deleteAllScanResults(
  userId: string
): Promise<{
  success: boolean;
  deletedCount?: number;
  error?: string;
}> {
  try {
    if (!userId) {
      return {
        success: false,
        error: 'User ID is required'
      };
    }

    // Query all scan results for this user
    const scanQuery = query(
      collection(firestore, 'scan_results'),
      where('userId', '==', userId)
    );

    const querySnapshot = await getDocs(scanQuery);

    if (querySnapshot.empty) {
      return {
        success: true,
        deletedCount: 0
      };
    }

    // Delete storage folder first
    try {
      const storageRef = ref(storage, `${userId}/scan_results`);
      const listResult = await listAll(storageRef);
      
      // Delete all items in the folder
      await Promise.all([
        ...listResult.items.map(itemRef => deleteObject(itemRef)),
        ...listResult.prefixes.map(async (folderRef) => {
          const folderItems = await listAll(folderRef);
          return Promise.all(folderItems.items.map(itemRef => deleteObject(itemRef)));
        })
      ]);
    } catch (storageError) {
      console.warn('Error deleting scan results storage folder:', storageError);
    }

    // Delete each document from Firestore
    let deletedCount = 0;
    for (const docSnapshot of querySnapshot.docs) {
      await deleteDoc(docSnapshot.ref);
      deletedCount++;
    }

    return {
      success: true,
      deletedCount
    };
  } catch (error) {
    console.error('Error deleting all scan results:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Download volume data for a scan result
 * 
 * @param scanId Scan result ID
 * @returns Promise with success status and volume data or error
 */
export async function downloadVolumeData(
  scanId: string
): Promise<{
  success: boolean;
  data?: ArrayBuffer;
  error?: string;
}> {
  try {
    // Get the scan result first
    const result = await getScanResult(scanId);
    
    if (!result.success || !result.data) {
      return {
        success: false,
        error: result.error || 'Scan result not found'
      };
    }
    
    const scanResult = result.data;
    const metadata = scanResult.metadata || {};
    const volumeDataPath = metadata.volumeDataPath;
    
    if (!volumeDataPath) {
      return {
        success: false,
        error: 'No volume data available for this scan'
      };
    }
    
    // Get download URL
    const volumeRef = ref(storage, volumeDataPath);
    const downloadUrl = await getDownloadURL(volumeRef);
    
    // Download the file
    const response = await fetch(downloadUrl);
    const data = await response.arrayBuffer();
    
    return {
      success: true,
      data
    };
  } catch (error) {
    console.error('Error downloading volume data:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Helper function to convert a URI to a Blob
 */
async function uriToBlob(uri: string): Promise<Blob> {
  if (Platform.OS === 'web') {
    const response = await fetch(uri);
    const blob = await response.blob();
    return blob;
  } else {
    const fileString = await FileSystem.readAsStringAsync(uri, {
      encoding: FileSystem.EncodingType.Base64,
    });
    const base64 = `data:image/jpeg;base64,${fileString}`;
    const response = await fetch(base64);
    const blob = await response.blob();
    return blob;
  }
} 