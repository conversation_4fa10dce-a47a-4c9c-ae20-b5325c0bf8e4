import * as FirebaseScanStorage from './scanResultsStorageFirebaseService';
export type { 
  ScanResult, 
  FoodItem, 
  BoundingBox, 
  VolumeData, 
  SegmentationData 
} from './scanResultsStorageFirebaseService';


  const getScanResult = async (scanId: string) => {
    try {
      if (useFirebase) {
        // Use Firebase implementation
        return await FirebaseScanStorage.getScanResult(scanId);
      } else {
        
        // For now, return an error
        return {
          success: false,
          error: 'Scan results retrieval not implemented for firebase'
        };
      }
    } catch (error) {
      console.error('Error getting scan result:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  
  const deleteScanResult = async (scanId: string) => {
    try {
      if (useFirebase) {
        // Use Firebase implementation
        return await FirebaseScanStorage.deleteScanResult(scanId);
      } else {
        
        // For now, return an error
        return {
          success: false,
          error: 'Scan results deletion not implemented for firebase'
        };
      }
    } catch (error) {
      console.error('Error deleting scan result:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Delete all scan results
   */
  const deleteAllScanResults = async () => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      if (useFirebase) {
        // Use Firebase implementation
        return await FirebaseScanStorage.deleteAllScanResults(user.id);
      } else {
        
        // For now, return success with 0 deleted
        return {
          success: true,
          deletedCount: 0
        };
      }
    } catch (error) {
      console.error('Error deleting all scan results:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Download volume data for a scan result
   */
  const downloadVolumeData = async (scanId: string) => {
    try {
      if (useFirebase) {
        // Use Firebase implementation
        return await FirebaseScanStorage.downloadVolumeData(scanId);
      } else {
        
        // For now, return an error
        return {
          success: false,
          error: 'Volume data download not implemented for firebase'
        };
      }
    } catch (error) {
      console.error('Error downloading volume data:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  return {
    saveScanResult,
    getScanResult,
    getScanResultsByDateRange,
    getLatestScanResult,
    updateScanResult,
    deleteScanResult,
    deleteAllScanResults,
    downloadVolumeData
  };
}
