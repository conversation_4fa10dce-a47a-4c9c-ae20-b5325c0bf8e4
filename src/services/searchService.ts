/**
 * Search service
 * Provides search functionality across different data sources in the app
 */

import { getFoodHistory } from './foodHistoryService';
import { SearchResult } from '@/components/GlobalSearch';

/**
 * Search across all data sources in the app
 * @param query Search query
 * @returns Array of search results
 */
export async function searchAllSources(query: string): Promise<SearchResult[]> {
  if (!query || query.trim() === '') {
    return [];
  }

  try {
    // Normalize query
    const normalizedQuery = query.toLowerCase().trim();
    const results: SearchResult[] = [];

    // Search food history
    const foodResults = await searchFoodHistory(normalizedQuery);
    results.push(...foodResults);

    // Add other search sources here as needed:
    // - Recipes
    // - Workouts 
    // - Articles
    // - Settings pages

    return results;
  } catch (error) {
    console.error('Error searching all sources:', error);
    return [];
  }
}

/**
 * Search food history
 * @param query Search query
 * @returns Array of search results for food items
 */
async function searchFoodHistory(query: string): Promise<SearchResult[]> {
  try {
    // Get all food history
    const foodHistory = await getFoodHistory({
      searchTerm: query,
      limit: 20
    });

    // Map to search results
    return foodHistory.map(food => ({
      id: food.id,
      title: food.name,
      description: `${Math.round(food.calories)} cal • ${Math.round(food.protein)}g protein • ${Math.round(food.carbs)}g carbs • ${Math.round(food.fat)}g fat`,
      type: 'food',
      route: `/food-detail/${food.id}`,
      date: food.date,
      imageUrl: food.imageUri,
      tags: food.mealType ? [food.mealType] : [],
      meta: {
        calories: food.calories,
        protein: food.protein,
        carbs: food.carbs,
        fat: food.fat
      }
    }));
  } catch (error) {
    console.error('Error searching food history:', error);
    return [];
  }
}

/**
 * Add a search entry to recent searches
 * @param query Search query to save
 */
export async function addToRecentSearches(query: string): Promise<void> {
  // This functionality is handled in the GlobalSearch component
}

/**
 * Hook to use search functionality
 * @returns Search functions and state
 */
export function useSearch() {
  return {
    searchAllSources
  };
} 