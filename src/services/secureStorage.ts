/**
 * Secure Storage Service
 * Provides encrypted storage for sensitive data like API keys
 */

import * as SecureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import { z } from 'zod';

// Add window declaration for web environment
declare global {
  interface Window {
    crypto: any; // Using any to avoid lib reference complexities
  }
}

// Define keys for stored values
export const SECURE_STORAGE_KEYS = {
  // Authentication
  ACCESS_TOKEN: 'auth_access_token',
  REFRESH_TOKEN: 'auth_refresh_token',
  // API Keys
  OPENAI_API_KEY: 'api_key_openai',
  NUTRITIONIX_API_KEY: 'api_key_nutritionix',
  NUTRITIONIX_APP_ID: 'api_id_nutritionix',
  // User data
  USER_PROFILE: 'user_profile',
  HEALTH_DATA: 'health_data',
  // Settings
  APP_SETTINGS: 'app_settings',
};

// Type definition for secure storage options
interface SecureStorageOptions {
  requireAuthentication?: boolean;
  accessGroup?: string;
}

// Schema for validating API key format
const ApiKeySchema = z.string().min(10).max(100);

// Schema for user profile
const UserProfileSchema = z.object({
  id: z.string().optional(),
  email: z.string().email().optional(),
  name: z.string().optional(),
  preferences: z.object({
    useBiometrics: z.boolean().optional(),
    offlineMode: z.boolean().optional(),
    theme: z.enum(['light', 'dark', 'system']).optional(),
    measurementUnit: z.enum(['metric', 'imperial']).optional(),
  }).optional(),
});

/**
 * Check if secure storage is available on the current platform
 */
export const isSecureStorageAvailable = (): boolean => {
  // On native platforms, secure storage is always available
  if (Platform.OS !== 'web') return true;
  
  // On web, check if crypto is available
  if (typeof globalThis !== 'undefined' && 'window' in globalThis) {
    const win = globalThis.window as Window;
    return 'crypto' in win;
  }
  
  return false;
};

/**
 * Save value to secure storage with encryption
 * Falls back to AsyncStorage on web with a warning
 */
export async function saveSecurely(
  key: string,
  value: string,
  options?: SecureStorageOptions
): Promise<boolean> {
  try {
    // Validate sensitive data when saving
    if (key === SECURE_STORAGE_KEYS.OPENAI_API_KEY || 
        key === SECURE_STORAGE_KEYS.NUTRITIONIX_API_KEY) {
      try {
        ApiKeySchema.parse(value);
      } catch (validationError) {
        console.error(`Invalid API key format for ${key}`);
        return false;
      }
    }

    if (Platform.OS !== 'web') {
      // Use SecureStore on native platforms
      await SecureStore.setItemAsync(key, value, options);
    } else {
      // On web, we encrypt using a simple approach
      // In a production app, you would use a proper encryption library
      console.warn('Using less secure storage on web platform');
      
      // For web, we'll add a prefix to indicate it's sensitive data
      const webStorageKey = `secure_${key}`;
      
      // Store with a timestamp for expiration
      const secureItem = {
        value,
        timestamp: Date.now(),
        // 30-day expiration for sensitive data
        expiry: Date.now() + (30 * 24 * 60 * 60 * 1000),
      };
      
      await AsyncStorage.setItem(webStorageKey, JSON.stringify(secureItem));
    }
    return true;
  } catch (error) {
    console.error('Error saving to secure storage:', error);
    return false;
  }
}

/**
 * Retrieve value from secure storage
 */
export async function getSecurely(
  key: string,
  options?: SecureStorageOptions
): Promise<string | null> {
  try {
    if (Platform.OS !== 'web') {
      // Use SecureStore on native platforms
      return await SecureStore.getItemAsync(key, options);
    } else {
      // On web, retrieve from AsyncStorage with our prefix
      const webStorageKey = `secure_${key}`;
      const storedItem = await AsyncStorage.getItem(webStorageKey);
      
      if (!storedItem) return null;
      
      try {
        const { value, expiry } = JSON.parse(storedItem);
        
        // Check if item has expired
        if (expiry && Date.now() > expiry) {
          await AsyncStorage.removeItem(webStorageKey);
          return null;
        }
        
        return value;
      } catch {
        return null;
      }
    }
  } catch (error) {
    console.error('Error retrieving from secure storage:', error);
    return null;
  }
}

/**
 * Delete value from secure storage
 */
export async function removeSecurely(key: string): Promise<boolean> {
  try {
    if (Platform.OS !== 'web') {
      await SecureStore.deleteItemAsync(key);
    } else {
      const webStorageKey = `secure_${key}`;
      await AsyncStorage.removeItem(webStorageKey);
    }
    return true;
  } catch (error) {
    console.error('Error removing from secure storage:', error);
    return false;
  }
}

/**
 * Save user profile securely
 */
export async function saveUserProfile(profile: z.infer<typeof UserProfileSchema>): Promise<boolean> {
  try {
    // Validate profile data
    UserProfileSchema.parse(profile);
    
    // Store user profile
    return await saveSecurely(
      SECURE_STORAGE_KEYS.USER_PROFILE, 
      JSON.stringify(profile)
    );
  } catch (error) {
    console.error('Error saving user profile:', error);
    return false;
  }
}

/**
 * Get user profile from secure storage
 */
export async function getUserProfile(): Promise<z.infer<typeof UserProfileSchema> | null> {
  try {
    const profileData = await getSecurely(SECURE_STORAGE_KEYS.USER_PROFILE);
    
    if (!profileData) return null;
    
    // Parse and validate
    return UserProfileSchema.parse(JSON.parse(profileData));
  } catch (error) {
    console.error('Error retrieving user profile:', error);
    return null;
  }
}

/**
 * Save API keys securely
 */
export async function saveApiKey(service: 'openai' | 'nutritionix', key: string): Promise<boolean> {
  const storageKey = 
    service === 'openai' 
      ? SECURE_STORAGE_KEYS.OPENAI_API_KEY 
      : SECURE_STORAGE_KEYS.NUTRITIONIX_API_KEY;
  
  return saveSecurely(storageKey, key, { requireAuthentication: true });
}

/**
 * Get API key from secure storage
 */
export async function getApiKey(service: 'openai' | 'nutritionix'): Promise<string | null> {
  const storageKey = 
    service === 'openai' 
      ? SECURE_STORAGE_KEYS.OPENAI_API_KEY 
      : SECURE_STORAGE_KEYS.NUTRITIONIX_API_KEY;
  
  return getSecurely(storageKey);
}

/**
 * Clear all secure storage (e.g., for logout)
 */
export async function clearAllSecureData(): Promise<boolean> {
  try {
    if (Platform.OS !== 'web') {
      // On native, we need to manually delete each key
      const keysToRemove = Object.values(SECURE_STORAGE_KEYS);
      await Promise.all(keysToRemove.map(key => SecureStore.deleteItemAsync(key)));
    } else {
      // On web, we need to clear all items with our prefix
      const allKeys = await AsyncStorage.getAllKeys();
      const secureKeys = allKeys.filter(key => key.startsWith('secure_'));
      await AsyncStorage.multiRemove(secureKeys);
    }
    return true;
  } catch (error) {
    console.error('Error clearing secure storage:', error);
    return false;
  }
} 