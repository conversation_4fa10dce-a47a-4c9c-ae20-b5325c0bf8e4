

import { useDatabaseType } from '@/contexts/DatabaseContext';
import { httpsCallable, getFunctions } from 'firebase/functions';

/**
 * Interface for a food segment in an image
 */
export interface FoodSegment {
  id: string;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  mask?: number[][]; // Binary mask for pixel-level segmentation
  confidence: number;
  estimatedVolumeCm3?: number;
}

/**
 * Interface for segment visualization request
 */
export interface SegmentVisualizationRequest {
  imageUrl: string;
  segments: FoodSegment[];
  highlightMode?: 'overlay' | 'outline' | 'mask';
}

/**
 * Interface for segment visualization response
 */
export interface SegmentVisualizationResponse {
  visualizedImageUrl: string;
  visualizationId: string;
  segmentsVisualized: number;
}

/**
 * Service for food segment visualization
 */
export function useSegmentVisualizationService() {
  const { useFirebase, db } = useDatabaseType();

  /**
   * Generate a visualization of food segments in an image
   */
  const generateVisualization = async (
    request: SegmentVisualizationRequest
  ): Promise<SegmentVisualizationResponse> => {
    try {
      // Always use Firebase Cloud Function regardless of database type
      const functions = getFunctions();
      const generateSegmentVisualizationFn = httpsCallable<
        SegmentVisualizationRequest,
        SegmentVisualizationResponse
      >(functions, 'generateSegmentVisualization');

      const result = await generateSegmentVisualizationFn(request);
      return result.data;
    } catch (error) {
      console.error('Error in segmentVisualizationService:', error);
      throw error;
    }
  };

  /**
   * Get visualizations for a specific user
   */
  const getUserVisualizations = async (limit = 10): Promise<any[]> => {
    try {
      const collection = 'visualizations';
      // We'll assume that the 'created_by' field contains the user ID
      const filters = [
        // User auth is handled within the DatabaseProvider implementation
      ];
      
      const results = await db.queryDocuments(collection, filters);
      
      // Apply the limit after fetching
      return results.slice(0, limit);
    } catch (error) {
      console.error('Error getting user visualizations:', error);
      throw error;
    }
  };

  return {
    generateVisualization,
    getUserVisualizations
  };
} 