import { Share, Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { captureRef } from 'react-native-view-shot';

/**
 * Food analysis data interface
 */
export interface FoodAnalysisData {
  name: string;
  dishName?: string;
  date?: string | Date;
  mealType?: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  items?: {name: string; estimatedAmount?: string}[];
  healthHighlights?: {
    positives?: string[];
    considerations?: string[];
  };
}

/**
 * Privacy options for food sharing
 */
export interface FoodSharingPrivacyOptions {
  includeImage: boolean;
  includeNutritionalInfo: boolean;
  includeMealType: boolean;
  includeIngredients: boolean;
  includeDateTime: boolean;
  includeHealthHighlights: boolean;
}

/**
 * Default privacy options
 */
export const defaultPrivacyOptions: FoodSharingPrivacyOptions = {
  includeImage: true,
  includeNutritionalInfo: true,
  includeMealType: true,
  includeIngredients: true,
  includeDateTime: true,
  includeHealthHighlights: true
};

/**
 * Share food analysis with privacy options
 * @param foodData The food analysis data to share
 * @param imageUri URI of the food image
 * @param viewRef React ref to capture if sharing as image
 * @param privacyOptions Privacy options to customize what to share
 * @param shareAsImage Whether to share as an image (screenshot) instead of text
 */
export async function shareFoodAnalysis(
  foodData: FoodAnalysisData, 
  imageUri: string,
  viewRef: React.RefObject<any> | null = null,
  privacyOptions: FoodSharingPrivacyOptions = defaultPrivacyOptions,
  shareAsImage: boolean = false
): Promise<void> {
  try {
    if (shareAsImage && viewRef && viewRef.current) {
      await shareAsScreenshot(viewRef);
    } else {
      await shareAsText(foodData, imageUri, privacyOptions);
    }
  } catch (error) {
    console.error('Error sharing food analysis:', error);
    throw error;
  }
}

/**
 * Generate text message for sharing food analysis
 */
function generateShareText(
  foodData: FoodAnalysisData,
  privacyOptions: FoodSharingPrivacyOptions
): string {
  let shareText = `🍽️ Check out what I ate!\n\n`;
  
  // Include food name
  shareText += `${foodData.dishName || foodData.name}\n\n`;
  
  // Include date/time if enabled
  if (privacyOptions.includeDateTime && foodData.date) {
    const date = new Date(foodData.date);
    shareText += `📅 ${date.toLocaleDateString()} ${date.toLocaleTimeString()}\n\n`;
  }
  
  // Include meal type if enabled
  if (privacyOptions.includeMealType && foodData.mealType) {
    shareText += `🕒 ${foodData.mealType.charAt(0).toUpperCase() + foodData.mealType.slice(1)}\n\n`;
  }
  
  // Include nutritional info if enabled
  if (privacyOptions.includeNutritionalInfo) {
    shareText += `📊 Nutritional Info:\n`;
    shareText += `• Calories: ${foodData.calories} kcal\n`;
    shareText += `• Protein: ${foodData.protein}g\n`;
    shareText += `• Carbs: ${foodData.carbs}g\n`;
    shareText += `• Fat: ${foodData.fat}g\n\n`;
  }
  
  // Include ingredients if enabled
  if (privacyOptions.includeIngredients && foodData.items && foodData.items.length > 0) {
    shareText += `🥗 Ingredients:\n`;
    foodData.items.forEach(item => {
      shareText += `• ${item.name} (${item.estimatedAmount || 'Unknown amount'})\n`;
    });
    shareText += '\n';
  }
  
  // Include health highlights if enabled
  if (privacyOptions.includeHealthHighlights && foodData.healthHighlights) {
    const { positives, considerations } = foodData.healthHighlights;
    
    if (positives && positives.length > 0) {
      shareText += `✅ Health Positives:\n`;
      positives.forEach(item => {
        shareText += `• ${item}\n`;
      });
      shareText += '\n';
    }
    
    if (considerations && considerations.length > 0) {
      shareText += `⚠️ Health Considerations:\n`;
      considerations.forEach(item => {
        shareText += `• ${item}\n`;
      });
      shareText += '\n';
    }
  }
  
  shareText += `Shared via Health App`;
  
  return shareText;
}

/**
 * Share food analysis as text with optional image
 */
async function shareAsText(
  foodData: FoodAnalysisData,
  imageUri: string,
  privacyOptions: FoodSharingPrivacyOptions
): Promise<void> {
  const shareText = generateShareText(foodData, privacyOptions);
  
  // Check if we should include the image
  if (privacyOptions.includeImage && imageUri) {
    // On Android, we need to copy the image to a shareable location
    if (Platform.OS === 'android') {
      const fileUri = `${FileSystem.cacheDirectory}shared_food_image.jpg`;
      
      try {
        await FileSystem.copyAsync({
          from: imageUri,
          to: fileUri
        });
        
        // Now share with the copied file
        await Sharing.shareAsync(fileUri, {
          mimeType: 'image/jpeg',
          dialogTitle: 'Share Food Analysis',
          UTI: 'public.jpeg'
        });
      } catch (error) {
        console.error('Error sharing with image:', error);
        // Fallback to sharing just text
        await Share.share({ message: shareText });
      }
    } else {
      // On iOS, we can share the image directly
      await Sharing.shareAsync(imageUri, {
        mimeType: 'image/jpeg',
        dialogTitle: 'Share Food Analysis',
        UTI: 'public.jpeg'
      });
    }
  } else {
    // Share as plain text
    await Share.share({
      message: shareText
    });
  }
}

/**
 * Share a screenshot of the view
 */
async function shareAsScreenshot(viewRef: React.RefObject<any>): Promise<void> {
  try {
    // Capture the view as an image
    const uri = await captureRef(viewRef, {
      format: 'jpg',
      quality: 0.8
    });
    
    // Share the captured image
    await Sharing.shareAsync(uri, {
      mimeType: 'image/jpeg',
      dialogTitle: 'Share Food Analysis',
      UTI: 'public.jpeg'
    });
  } catch (error) {
    console.error('Error sharing screenshot:', error);
    throw error;
  }
} 