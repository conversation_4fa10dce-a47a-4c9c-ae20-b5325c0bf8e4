import { Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { format, parse, addMinutes, subMinutes, isAfter } from 'date-fns';
import { SleepGoal } from './sleepTrackingService';
import { getAuth } from 'firebase/auth';
import { getFirestore, doc, getDoc, query, collection, where, getDocs } from 'firebase/firestore';

// Types
export interface SleepReminder {
  bedtimeNotificationId?: string;
  wakeupNotificationId?: string;
  enabled: boolean;
}

// Interface that matches the Firestore document structure
interface SleepGoalDocument {
  userId: string;
  targetBedtime?: string;
  targetWakeTime?: string;
  remindersEnabled?: boolean;
  bedTimeReminderMinutes?: number;
}

/**
 * Wrapper function to get sleep goals for the current user
 */
export async function getSleepGoals(): Promise<{
  success: boolean;
  data?: {
    bedTimeTarget: string;
    wakeTimeTarget: string;
    remindersEnabled: boolean;
    bedTimeReminderMinutes?: number;
  };
  error?: string;
}> {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return {
        success: false,
        error: 'User not authenticated'
      };
    }
    
    // Access Firestore directly instead of using the hook
    const db = getFirestore();
    
    // First try to query by userId
    const sleepGoalsQuery = query(
      collection(db, 'sleep_goals'),
      where('userId', '==', user.uid)
    );
    
    const querySnapshot = await getDocs(sleepGoalsQuery);
    let sleepGoalDoc: SleepGoalDocument | null = null;
    
    if (!querySnapshot.empty) {
      sleepGoalDoc = querySnapshot.docs[0].data() as SleepGoalDocument;
    } else {
      // If not found by query, try direct document lookup
      const docRef = doc(db, 'sleep_goals', user.uid);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        sleepGoalDoc = docSnap.data() as SleepGoalDocument;
      }
    }
    
    if (!sleepGoalDoc) {
      return {
        success: false,
        error: 'No sleep goal found'
      };
    }
    
    return {
      success: true,
      data: {
        bedTimeTarget: sleepGoalDoc.targetBedtime || '22:00',
        wakeTimeTarget: sleepGoalDoc.targetWakeTime || '07:00',
        remindersEnabled: sleepGoalDoc.remindersEnabled || false,
        bedTimeReminderMinutes: sleepGoalDoc.bedTimeReminderMinutes || 30
      }
    };
  } catch (error) {
    console.error('Error getting sleep goals:', error);
    return {
      success: false,
      error: 'Failed to get sleep goals'
    };
  }
}

/**
 * Request permissions for notifications
 */
export async function requestNotificationPermissions(): Promise<boolean> {
  if (!Device.isDevice) {
    console.warn('Notifications are not supported in the simulator');
    return false;
  }

  const { status: existingStatus } = await Notifications.getPermissionsAsync();
  let finalStatus = existingStatus;

  if (existingStatus !== 'granted') {
    const { status } = await Notifications.requestPermissionsAsync();
    finalStatus = status;
  }

  if (finalStatus !== 'granted') {
    console.warn('Failed to get notification permissions');
    return false;
  }

  return true;
}

/**
 * Configure notification handler
 */
export function configureSleepNotifications() {
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: false,
    }),
  });
}

/**
 * Schedule bedtime reminder notification
 */
export async function scheduleBedtimeReminder(): Promise<string | null> {
  try {
    const hasPermission = await requestNotificationPermissions();
    if (!hasPermission) {
      return null;
    }

    const sleepGoalsResult = await getSleepGoals();
    if (!sleepGoalsResult.success || !sleepGoalsResult.data) {
      return null;
    }
    
    const { bedTimeTarget, remindersEnabled, bedTimeReminderMinutes = 30 } = sleepGoalsResult.data;
    
    // If reminders are disabled, don't schedule
    if (!remindersEnabled) {
      return null;
    }
    
    // Parse the bedtime from HH:MM format
    const today = new Date();
    const bedTimeString = `${format(today, 'yyyy-MM-dd')} ${bedTimeTarget}`;
    const bedTime = parse(bedTimeString, 'yyyy-MM-dd HH:mm', new Date());
    
    // Calculate reminder time (defaulting to 30 minutes before bedtime)
    const reminderTime = subMinutes(bedTime, bedTimeReminderMinutes);
    
    // Only schedule if the reminder time is in the future
    if (!isAfter(reminderTime, new Date())) {
      console.log('Bedtime reminder time has already passed for today');
      return null;
    }
    
    // Cancel any existing reminder
    await cancelBedtimeReminder();
    
    // Schedule the new reminder
    const notificationId = await Notifications.scheduleNotificationAsync({
      identifier: 'bedtime-reminder',
      content: {
        title: 'Bedtime Reminder',
        body: `Time to start winding down. Your target bedtime is ${format(bedTime, 'h:mm a')}.`,
        sound: true,
      },
      trigger: {
        seconds: Math.floor((reminderTime.getTime() - Date.now()) / 1000),
        channelId: 'sleep-reminders',
      },
    });
    
    return notificationId;
  } catch (error) {
    console.error('Error scheduling bedtime reminder:', error);
    return null;
  }
}

/**
 * Schedule wake-up reminder notification
 */
export async function scheduleWakeupReminder(): Promise<string | null> {
  try {
    const hasPermission = await requestNotificationPermissions();
    if (!hasPermission) {
      return null;
    }

    const sleepGoalsResult = await getSleepGoals();
    if (!sleepGoalsResult.success || !sleepGoalsResult.data) {
      return null;
    }
    
    const { wakeTimeTarget, remindersEnabled } = sleepGoalsResult.data;
    
    // If reminders are disabled, don't schedule
    if (!remindersEnabled) {
      return null;
    }
    
    // Parse the wake time from HH:MM format
    const today = new Date();
    const wakeTimeString = `${format(today, 'yyyy-MM-dd')} ${wakeTimeTarget}`;
    const wakeTime = parse(wakeTimeString, 'yyyy-MM-dd HH:mm', new Date());
    
    // If wake time is before the current time (or very close), schedule for tomorrow
    if (!isAfter(wakeTime, addMinutes(new Date(), 5))) {
      wakeTime.setDate(wakeTime.getDate() + 1);
    }
    
    // Cancel any existing reminder
    await cancelWakeupReminder();
    
    // Schedule the new reminder
    const notificationId = await Notifications.scheduleNotificationAsync({
      identifier: 'wakeup-reminder',
      content: {
        title: 'Good Morning!',
        body: `It's ${format(wakeTime, 'h:mm a')}, your target wake-up time. Start your day refreshed!`,
        sound: true,
      },
      trigger: {
        seconds: Math.floor((wakeTime.getTime() - Date.now()) / 1000),
        channelId: 'sleep-reminders',
      },
    });
    
    return notificationId;
  } catch (error) {
    console.error('Error scheduling wake-up reminder:', error);
    return null;
  }
}

/**
 * Cancel bedtime reminder notification
 */
export async function cancelBedtimeReminder(): Promise<boolean> {
  try {
    const notifications = await Notifications.getAllScheduledNotificationsAsync();
    const bedtimeNotifications = notifications.filter(notification => 
      notification.request.content.title === 'Bedtime Reminder'
    );
    
    for (const notification of bedtimeNotifications) {
      await Notifications.cancelScheduledNotificationAsync(notification.request.identifier);
    }
    
    return true;
  } catch (error) {
    console.error('Error canceling bedtime reminder:', error);
    return false;
  }
}

/**
 * Cancel wake-up reminder notification
 */
export async function cancelWakeupReminder(): Promise<boolean> {
  try {
    const notifications = await Notifications.getAllScheduledNotificationsAsync();
    const wakeupNotifications = notifications.filter(notification => 
      notification.request.content.title === 'Good Morning!'
    );
    
    for (const notification of wakeupNotifications) {
      await Notifications.cancelScheduledNotificationAsync(notification.request.identifier);
    }
    
    return true;
  } catch (error) {
    console.error('Error canceling wake-up reminder:', error);
    return false;
  }
}

/**
 * Update sleep reminders based on sleep goals
 */
export async function updateSleepReminders(): Promise<SleepReminder> {
  try {
    const sleepGoalsResult = await getSleepGoals();
    
    // If unable to get sleep goals, disable reminders
    if (!sleepGoalsResult.success || !sleepGoalsResult.data) {
      return { enabled: false };
    }
    
    const { remindersEnabled } = sleepGoalsResult.data;
    
    // If reminders are disabled, cancel any existing ones
    if (!remindersEnabled) {
      await cancelBedtimeReminder();
      await cancelWakeupReminder();
      return { enabled: false };
    }
    
    // Schedule both types of reminders
    const bedtimeNotificationId = await scheduleBedtimeReminder();
    const wakeupNotificationId = await scheduleWakeupReminder();
    
    return {
      bedtimeNotificationId: bedtimeNotificationId || undefined,
      wakeupNotificationId: wakeupNotificationId || undefined,
      enabled: true
    };
  } catch (error) {
    console.error('Error updating sleep reminders:', error);
    return { enabled: false };
  }
} 