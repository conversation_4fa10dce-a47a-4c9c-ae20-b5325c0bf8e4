import AsyncStorage from '@react-native-async-storage/async-storage';
import { format, parseISO, addDays, differenceInMinutes, isWithinInterval } from 'date-fns';
import { getUserProfile } from './nutritionGoalService';
// import previously removed: useDatabaseType from '@/contexts/DatabaseContext'
import { getAuth } from 'firebase/auth';
import { collection, getDocs, query, where, addDoc, doc, getDoc, updateDoc, deleteDoc, Timestamp } from 'firebase/firestore';
import { firestore } from '@/lib/firebase';

// Storage keys
const SLEEP_RECORDS_KEY = 'sleep_records_';
const SLEEP_GOALS_KEY = 'sleep_goals_';

// Default values
const DEFAULT_SLEEP_GOAL_HOURS = 8;

// Firebase database interfaces
interface SleepRecordDB {
  id: string;
  user_id: string;
  date: string;
  bed_time: string;
  wake_time: string;
  duration: number;
  quality: string;
  interruptions: number;
  sleep_notes?: string;
  tags?: string[];
  sleep_score?: number;
  created_at: string;
  updated_at: string;
}

interface SleepGoalsDB {
  user_id: string;
  target_hours: number;
  bed_time_target: string;
  wake_time_target: string;
  weekday_bed_time_target?: string;
  weekend_bed_time_target?: string;
  reminders_enabled: boolean;
  bed_time_reminder_minutes?: number;
  created_at?: string;
  updated_at?: string;
}

export enum SleepQuality {
  POOR = 'poor',
  FAIR = 'fair',
  GOOD = 'good',
  EXCELLENT = 'excellent'
}

export interface SleepRecord {
  id?: string;
  userId: string;
  date: string;
  startTime: string;
  endTime: string;
  durationMinutes: number;
  quality: number | SleepQuality; // Allow both number and SleepQuality enum
  interruptions?: number; // Add interruptions property
  sleepStages?: {
    deep?: number; // minutes
    light?: number; // minutes
    rem?: number; // minutes
    awake?: number; // minutes
  };
  notes?: string;
  tags?: string[]; // Add tags property
  sleepScore?: number; // Add sleepScore property
  source?: 'manual' | 'google_fit' | 'apple_health' | 'garmin' | 'fitbit' | 'other';
  createdAt?: string;
  updatedAt?: string;
}

export interface SleepGoal {
  id?: string;
  userId: string;
  dailyTargetHours: number;
  targetBedtime?: string; // 24hr format HH:MM
  targetWakeTime?: string; // 24hr format HH:MM
  remindersEnabled?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface SleepStage {
  stage: 'awake' | 'light' | 'deep' | 'rem';
  duration: number; // in minutes
  startTime: string; // ISO datetime
}

export interface SleepStatistics {
  averageDuration: number; // in minutes
  averageQuality: SleepQuality;
  averageSleepScore: number;
  averageBedTime: string; // HH:MM format
  averageWakeTime: string; // HH:MM format
  sleepDebt: number; // in minutes, negative means sleep surplus
  consistencyScore: number; // 0-100 scale
  daysTracked: number;
  sleepEfficiency: number; // percentage
  totalSleepTime: number; // in minutes
  interruptions: number; // total count
}

/**
 * Hook to provide sleep tracking functionality
 */
export function useSleepTrackingService() {
  // const { db } was previously removed - using Firebase directly now

  /**
   * Add a sleep record
   */
  const addSleepRecord = async (sleepRecord: Omit<SleepRecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
    try {
      // Add timestamps
      const now = new Date().toISOString();
      const recordData = {
        ...sleepRecord,
        createdAt: now,
        updatedAt: now
      };

      // Add to Firebase database
      const auth = getAuth();
      const user = auth.currentUser;
      if (!user) throw new Error('User not authenticated');
      
      const sleepRef = collection(firestore, 'sleep_records');
      const docRef = await addDoc(sleepRef, {
        ...recordData,
        userId: user.uid
      });
      const id = docRef.id;
      
      return id;
    } catch (error) {
      console.error('Error adding sleep record:', error);
      throw error;
    }
  };

  /**
   * Get sleep records for a specific date
   * Note: Date refers to the "sleep date" (when the sleep started)
   */
  const getSleepRecordsForDate = async (userId: string, date: string): Promise<SleepRecord[]> => {
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      if (!user) throw new Error('User not authenticated');
      
      const sleepRef = collection(firestore, 'sleep_records');
      const q = query(sleepRef, where('userId', '==', user.uid), where('date', '==', date));
      const snapshot = await getDocs(q);
      const records = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as SleepRecord[];
      return records;
    } catch (error) {
      console.error(`Error getting sleep records for date ${date}:`, error);
      throw error;
    }
  };

  /**
   * Get sleep records for a date range
   */
  const getSleepRecordsForDateRange = async (userId: string, startDate: string, endDate: string): Promise<SleepRecord[]> => {
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      if (!user) throw new Error('User not authenticated');
      
      const sleepRef = collection(firestore, 'sleep_records');
      const q = query(
        sleepRef,
        where('userId', '==', user.uid),
        where('date', '>=', startDate),
        where('date', '<=', endDate)
      );
      const snapshot = await getDocs(q);
      const records = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as SleepRecord[];
      return records;
    } catch (error) {
      console.error(`Error getting sleep records for date range ${startDate} to ${endDate}:`, error);
      throw error;
    }
  };

  /**
   * Update a sleep record
   */
  const updateSleepRecord = async (id: string, updates: Partial<SleepRecord>): Promise<void> => {
    try {
      const now = new Date().toISOString();
      
      const sleepRecordRef = doc(firestore, 'sleep_records', id);
      await updateDoc(sleepRecordRef, {
        ...updates,
        updatedAt: now
      });
    } catch (error) {
      console.error(`Error updating sleep record ${id}:`, error);
      throw error;
    }
  };

  /**
   * Delete a sleep record
   */
  const deleteSleepRecord = async (id: string): Promise<void> => {
    try {
      const sleepRecordRef = doc(firestore, 'sleep_records', id);
      await deleteDoc(sleepRecordRef);
    } catch (error) {
      console.error(`Error deleting sleep record ${id}:`, error);
      throw error;
    }
  };

  /**
   * Get sleep summary for a date range
   */
  const getSleepSummary = async (
    userId: string, 
    startDate: string, 
    endDate: string
  ): Promise<{
    averageDuration: number;
    averageQuality: number;
    averageBedtime: string;
    averageWakeTime: string;
    totalSleepDays: number;
    totalSleepMinutes: number;
    averageSleepStages?: {
      deep: number;
      light: number;
      rem: number;
      awake: number;
    };
  }> => {
    try {
      // Get all sleep records for the date range
      const records = await getSleepRecordsForDateRange(userId, startDate, endDate);
      
      if (records.length === 0) {
        return {
          averageDuration: 0,
          averageQuality: 0,
          averageBedtime: '00:00',
          averageWakeTime: '00:00',
          totalSleepDays: 0,
          totalSleepMinutes: 0
        };
      }
      
      // Calculate summary values
      let totalDuration = 0;
      let totalQuality = 0;
      let qualityCount = 0;
      
      // For calculating average bedtime and wake time
      let totalBedtimeMinutes = 0;
      let totalWakeTimeMinutes = 0;
      
      // For sleep stages
      let totalDeepMinutes = 0;
      let totalLightMinutes = 0;
      let totalRemMinutes = 0;
      let totalAwakeMinutes = 0;
      let sleepStagesCount = 0;
      
      for (const record of records) {
        totalDuration += record.durationMinutes;
        
        if (record.quality != null) {
          totalQuality += typeof record.quality === 'number' ? record.quality : 0;
          qualityCount++;
        }
        
        // Calculate bedtime minutes (from midnight)
        const bedtimeParts = record.startTime.split(':');
        const bedtimeHour = parseInt(bedtimeParts[0]);
        const bedtimeMinute = parseInt(bedtimeParts[1]);
        const bedtimeMinutes = bedtimeHour * 60 + bedtimeMinute;
        totalBedtimeMinutes += bedtimeMinutes;
        
        // Calculate wake time minutes (from midnight)
        const wakeTimeParts = record.endTime.split(':');
        const wakeTimeHour = parseInt(wakeTimeParts[0]);
        const wakeTimeMinute = parseInt(wakeTimeParts[1]);
        const wakeTimeMinutes = wakeTimeHour * 60 + wakeTimeMinute;
        totalWakeTimeMinutes += wakeTimeMinutes;
        
        // Calculate sleep stages if available
        if (record.sleepStages) {
          totalDeepMinutes += record.sleepStages.deep || 0;
          totalLightMinutes += record.sleepStages.light || 0;
          totalRemMinutes += record.sleepStages.rem || 0;
          totalAwakeMinutes += record.sleepStages.awake || 0;
          sleepStagesCount++;
        }
      }
      
      // Calculate averages
      const averageDuration = Math.round(totalDuration / records.length);
      const averageQuality = qualityCount > 0 ? Math.round((totalQuality / qualityCount) * 10) / 10 : 0;
      
      // Calculate average bedtime
      const avgBedtimeMinutes = Math.round(totalBedtimeMinutes / records.length);
      const avgBedtimeHour = Math.floor(avgBedtimeMinutes / 60) % 24;
      const avgBedtimeMinute = avgBedtimeMinutes % 60;
      const averageBedtime = `${avgBedtimeHour.toString().padStart(2, '0')}:${avgBedtimeMinute.toString().padStart(2, '0')}`;
      
      // Calculate average wake time
      const avgWakeTimeMinutes = Math.round(totalWakeTimeMinutes / records.length);
      const avgWakeTimeHour = Math.floor(avgWakeTimeMinutes / 60) % 24;
      const avgWakeTimeMinute = avgWakeTimeMinutes % 60;
      const averageWakeTime = `${avgWakeTimeHour.toString().padStart(2, '0')}:${avgWakeTimeMinute.toString().padStart(2, '0')}`;
      
      // Prepare result
      const result: any = {
        averageDuration,
        averageQuality,
        averageBedtime,
        averageWakeTime,
        totalSleepDays: records.length,
        totalSleepMinutes: totalDuration
      };
      
      // Add sleep stages if available
      if (sleepStagesCount > 0) {
        result.averageSleepStages = {
          deep: Math.round(totalDeepMinutes / sleepStagesCount),
          light: Math.round(totalLightMinutes / sleepStagesCount),
          rem: Math.round(totalRemMinutes / sleepStagesCount),
          awake: Math.round(totalAwakeMinutes / sleepStagesCount)
        };
      }
      
      return result;
    } catch (error) {
      console.error(`Error getting sleep summary for ${startDate} to ${endDate}:`, error);
      throw error;
    }
  };

  /**
   * Get sleep goal for user
   */
  const getSleepGoal = async (userId: string): Promise<SleepGoal | null> => {
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      if (!user) throw new Error('User not authenticated');
      
      // First try to get by userId
      const goalsRef = collection(firestore, 'sleep_goals');
      const q = query(goalsRef, where('userId', '==', user.uid));
      const snapshot = await getDocs(q);
      
      if (!snapshot.empty) {
        const goalDoc = snapshot.docs[0];
        return { id: goalDoc.id, ...goalDoc.data() } as SleepGoal;
      }
      
      // If no goal found, try to get directly by id (assuming userId is the document id)
      try {
        const goalRef = doc(firestore, 'sleep_goals', user.uid);
        const goalDoc = await getDoc(goalRef);
        if (goalDoc.exists()) {
          return { id: goalDoc.id, ...goalDoc.data() } as SleepGoal;
        }
      } catch (docError) {
        console.log('No direct sleep goal found with userId as id');
      }
      
      return null;
    } catch (error) {
      console.error(`Error getting sleep goal for user ${userId}:`, error);
      throw error;
    }
  };

  /**
   * Set sleep goal for user
   */
  const setSleepGoal = async (goal: SleepGoal): Promise<void> => {
    try {
      const now = new Date().toISOString();
      
      // Check if goal already exists
      const existingGoal = await getSleepGoal(goal.userId);
      
      if (existingGoal && existingGoal.id) {
        // Update existing goal
        const goalRef = doc(firestore, 'sleep_goals', existingGoal.id);
        await updateDoc(goalRef, {
          ...goal,
          updatedAt: now
        });
      } else {
        // Create new goal
        const goalsRef = collection(firestore, 'sleep_goals');
        await addDoc(goalsRef, {
          ...goal,
          createdAt: now,
          updatedAt: now
        });
      }
    } catch (error) {
      console.error('Error setting sleep goal:', error);
      throw error;
    }
  };

  /**
   * Check if a user has met their sleep goal for a given date
   */
  const checkSleepGoalForDate = async (
    userId: string, 
    date: string
  ): Promise<{
    hasGoal: boolean;
    goalHours: number;
    actualHours: number;
    goalMet: boolean;
    progress: number;
    deficit: number;
  }> => {
    try {
      // Get the user's sleep goal
      const goal = await getSleepGoal(userId);
      const hasGoal = goal != null && goal.dailyTargetHours > 0;
      const goalHours = goal?.dailyTargetHours || 8; // Default to 8 hours if no goal set
      
      // Get sleep records for the date
      const records = await getSleepRecordsForDate(userId, date);
      
      // Calculate total sleep duration
      let totalMinutes = 0;
      for (const record of records) {
        totalMinutes += record.durationMinutes;
      }
      
      const actualHours = totalMinutes / 60;
      const goalMet = actualHours >= goalHours;
      const progress = Math.min(100, Math.round((actualHours / goalHours) * 100));
      const deficit = Math.max(0, goalHours - actualHours);
      
      return {
        hasGoal,
        goalHours,
        actualHours,
        goalMet,
        progress,
        deficit
      };
    } catch (error) {
      console.error(`Error checking sleep goal for ${date}:`, error);
      throw error;
    }
  };

  /**
   * Get sleep quality over time
   */
  const getSleepQualityTrend = async (
    userId: string, 
    startDate: string, 
    endDate: string
  ): Promise<{
    date: string;
    quality: number | null;
    duration: number;
  }[]> => {
    try {
      // Get all sleep records for the date range
      const records = await getSleepRecordsForDateRange(userId, startDate, endDate);
      
      // Group records by date
      const recordsByDate: Record<string, SleepRecord[]> = {};
      for (const record of records) {
        if (!recordsByDate[record.date]) {
          recordsByDate[record.date] = [];
        }
        recordsByDate[record.date].push(record);
      }
      
      // Create a set of all dates in the range
      const allDates: string[] = [];
      const start = new Date(startDate);
      const end = new Date(endDate);
      const current = new Date(start);
      
      while (current <= end) {
        allDates.push(current.toISOString().split('T')[0]);
        current.setDate(current.getDate() + 1);
      }
      
      // Calculate quality and duration for each date
      return allDates.map(date => {
        const dateRecords = recordsByDate[date] || [];
        
        if (dateRecords.length === 0) {
          return {
            date,
            quality: null,
            duration: 0
          };
        }
        
        // Calculate average quality and total duration
        let totalQuality = 0;
        let qualityCount = 0;
        let totalDuration = 0;
        
        for (const record of dateRecords) {
          if (record.quality != null) {
            totalQuality += typeof record.quality === 'number' ? record.quality : 0;
            qualityCount++;
          }
          totalDuration += record.durationMinutes;
        }
        
        const avgQuality = qualityCount > 0 ? Math.round((totalQuality / qualityCount) * 10) / 10 : null;
        
        return {
          date,
          quality: avgQuality,
          duration: totalDuration
        };
      });
    } catch (error) {
      console.error(`Error getting sleep quality trend for ${startDate} to ${endDate}:`, error);
      throw error;
    }
  };

  return {
    addSleepRecord,
    getSleepRecordsForDate,
    getSleepRecordsForDateRange,
    updateSleepRecord,
    deleteSleepRecord,
    getSleepSummary,
    getSleepGoal,
    setSleepGoal,
    checkSleepGoalForDate,
    getSleepQualityTrend
  };
}

/**
 * Add a sleep record
 */
export async function addSleepRecord(
  bedTime: Date,
  wakeTime: Date,
  quality: SleepQuality,
  interruptions: number = 0,
  notes?: string,
  tags?: string[]
): Promise<{
  success: boolean;
  data?: SleepRecord;
  error?: string;
}> {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return {
        success: false,
        error: 'User not authenticated'
      };
    }
    
    const userId = user.uid;
    
    // Calculate sleep data
    const bedTimeString = format(bedTime, 'HH:mm');
    const wakeTimeString = format(wakeTime, 'HH:mm');
    
    // Use the date of bedtime for date tracking
    const sleepDate = format(bedTime, 'yyyy-MM-dd');
    
    // Calculate duration in minutes
    const durationMinutes = differenceInMinutes(wakeTime, bedTime);
    
    if (durationMinutes <= 0) {
      return {
        success: false,
        error: 'Wake time must be after bed time'
      };
    }
    
    // Calculate sleep score
    const sleepScore = calculateSleepScore(
      durationMinutes,
      quality,
      interruptions
    );
    
    // Create the record object
    const sleepRecord: SleepRecord = {
      userId,
      date: sleepDate,
      startTime: bedTimeString,
      endTime: wakeTimeString,
      durationMinutes,
      quality,
      interruptions,
      notes,
      tags,
      sleepScore,
      source: 'manual',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Save to Firestore
    const recordRef = await addDoc(collection(firestore, 'sleep_records'), {
      ...sleepRecord,
      created_at: Timestamp.now(),
      updated_at: Timestamp.now()
    });
    
    // Successfully saved to Firestore
    sleepRecord.id = recordRef.id;
    
    // Save to local storage as backup
    const localRecords = await getLocalSleepRecords(userId, sleepDate);
    localRecords.push(sleepRecord);
    await AsyncStorage.setItem(
      `${SLEEP_RECORDS_KEY}${userId}_${sleepDate}`,
      JSON.stringify(localRecords)
    );
    
    return {
      success: true,
      data: sleepRecord
    };
  } catch (dbError) {
    console.error('Error saving sleep record:', dbError);
    
    // Save to local storage even if Firestore failed
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      
      if (user) {
        const userId = user.uid;
        const sleepDate = format(bedTime, 'yyyy-MM-dd');
        const bedTimeString = format(bedTime, 'HH:mm');
        const wakeTimeString = format(wakeTime, 'HH:mm');
        const durationMinutes = differenceInMinutes(wakeTime, bedTime);
        
        const sleepRecord: SleepRecord = {
          userId,
          date: sleepDate,
          startTime: bedTimeString,
          endTime: wakeTimeString,
          durationMinutes,
          quality,
          interruptions,
          notes,
          tags,
          sleepScore: calculateSleepScore(durationMinutes, quality, interruptions),
          source: 'manual',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        const localRecords = await getLocalSleepRecords(userId, sleepDate);
        localRecords.push(sleepRecord);
        await AsyncStorage.setItem(
          `${SLEEP_RECORDS_KEY}${userId}_${sleepDate}`,
          JSON.stringify(localRecords)
        );
        
        return {
          success: true,
          data: sleepRecord,
          error: 'Saved locally only due to database error'
        };
      }
    } catch (localError) {
      console.error('Error saving to local storage:', localError);
    }
    
    return {
      success: false,
      error: `Failed to save sleep record: ${dbError}`
    };
  }
}

/**
 * Helper function to get local sleep records
 */
async function getLocalSleepRecords(userId: string, date: string): Promise<SleepRecord[]> {
  try {
    const records = await AsyncStorage.getItem(`${SLEEP_RECORDS_KEY}${userId}_${date}`);
    return records ? JSON.parse(records) : [];
  } catch (error) {
    console.error('Error getting local sleep records:', error);
    return [];
  }
}

/**
 * Get a sleep record for a specific date
 */
export async function getSleepRecordByDate(date: string): Promise<{
  success: boolean;
  data?: SleepRecord;
  error?: string;
}> {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return {
        success: false,
        error: 'User not authenticated'
      };
    }
    
    const userId = user.uid;
    
    // Try to get from Firestore first
    const q = query(
      collection(firestore, 'sleep_records'),
      where('userId', '==', userId),
      where('date', '==', date)
    );
    
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      const data = doc.data() as SleepRecord;
      data.id = doc.id;
      
      return {
        success: true,
        data
      };
    }
    
    // If Firestore failed or no records, try local storage
    try {
      const localRecords = await getLocalSleepRecords(userId, date);
      
      if (localRecords.length > 0) {
        // Sort by created date and take the latest
        localRecords.sort((a, b) => {
          return new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime();
        });
        
        return {
          success: true,
          data: localRecords[0]
        };
      }
    } catch (localError) {
      console.error('Error getting local sleep records:', localError);
    }
    
    // No records found
    return {
      success: true,
      data: undefined
    };
  } catch (dbError) {
    console.error('Error fetching sleep record from Firestore:', dbError);
    
    // If Firestore failed, try local storage
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      
      if (user) {
        const userId = user.uid;
        const localRecords = await getLocalSleepRecords(userId, date);
        
        if (localRecords.length > 0) {
          // Sort by created date and take the latest
          localRecords.sort((a, b) => {
            return new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime();
          });
          
          return {
            success: true,
            data: localRecords[0]
          };
        }
      }
    } catch (localError) {
      console.error('Error getting local sleep records:', localError);
    }
    
    return {
      success: false,
      error: `Failed to fetch sleep record: ${dbError}`
    };
  }
}

/**
 * Calculate sleep score based on duration, quality, and interruptions
 * Returns a score from 0-100
 */
function calculateSleepScore(
  durationMinutes: number,
  quality: number | SleepQuality,
  interruptions: number | undefined
): number {
  // Ensure interruptions is a number
  const interruptionsCount = interruptions || 0;
  
  // Base score from duration (optimal is 7-9 hours)
  let durationScore = 0;
  const durationHours = durationMinutes / 60;
  
  if (durationHours >= 7 && durationHours <= 9) {
    // Optimal range
    durationScore = 50;
  } else if (durationHours >= 6 && durationHours < 7) {
    // Slightly too short
    durationScore = 40;
  } else if (durationHours > 9 && durationHours <= 10) {
    // Slightly too long
    durationScore = 40;
  } else if (durationHours >= 5 && durationHours < 6) {
    // Too short
    durationScore = 30;
  } else if (durationHours > 10 && durationHours <= 11) {
    // Too long
    durationScore = 30;
  } else if (durationHours >= 4 && durationHours < 5) {
    // Very short
    durationScore = 20;
  } else if (durationHours > 11) {
    // Very long
    durationScore = 20;
  } else {
    // Extremely short
    durationScore = 10;
  }
  
  // Quality score
  let qualityScore = 0;
  if (typeof quality === 'number') {
    qualityScore = Math.min(40, quality * 4); // Scale up to 40 max
  } else {
    // Quality score from enum
    switch (quality) {
      case SleepQuality.EXCELLENT:
        qualityScore = 40;
        break;
      case SleepQuality.GOOD:
        qualityScore = 30;
        break;
      case SleepQuality.FAIR:
        qualityScore = 20;
        break;
      case SleepQuality.POOR:
        qualityScore = 10;
        break;
    }
  }
  
  // Interruptions penalty (each interruption costs 5 points, max 20)
  const interruptionPenalty = Math.min(20, interruptionsCount * 5);
  
  // Calculate final score
  const totalScore = durationScore + qualityScore - interruptionPenalty;
  
  // Ensure score is between 0 and 100
  return Math.max(0, Math.min(100, totalScore));
}

/**
 * Generate simulated sleep stages for visualization
 * Note: This is for UI demonstration only. Real sleep stages would come from wearable devices.
 */
export function generateSleepStages(duration: number): SleepStage[] {
  const stages: SleepStage[] = [];
  const startTime = new Date();
  startTime.setHours(23, 0, 0, 0); // Assume 11:00 PM start
  
  let remainingTime = duration;
  let currentTime = new Date(startTime.getTime());
  
  // A typical sleep cycle is 90-110 minutes
  while (remainingTime > 0) {
    // Light sleep (beginning of cycle)
    if (remainingTime > 0) {
      const lightDuration = Math.min(30, remainingTime);
      stages.push({
        stage: 'light',
        duration: lightDuration,
        startTime: currentTime.toISOString()
      });
      
      remainingTime -= lightDuration;
      currentTime = new Date(currentTime.getTime() + lightDuration * 60000);
    }
    
    // Deep sleep (middle of cycle)
    if (remainingTime > 0) {
      const deepDuration = Math.min(30, remainingTime);
      stages.push({
        stage: 'deep',
        duration: deepDuration,
        startTime: currentTime.toISOString()
      });
      
      remainingTime -= deepDuration;
      currentTime = new Date(currentTime.getTime() + deepDuration * 60000);
    }
    
    // REM sleep (end of cycle)
    if (remainingTime > 0) {
      const remDuration = Math.min(20, remainingTime);
      stages.push({
        stage: 'rem',
        duration: remDuration,
        startTime: currentTime.toISOString()
      });
      
      remainingTime -= remDuration;
      currentTime = new Date(currentTime.getTime() + remDuration * 60000);
    }
    
    // Brief awakening (more common at the end of cycles)
    if (remainingTime > 15 && Math.random() > 0.7) {
      const awakeDuration = Math.min(5, remainingTime);
      stages.push({
        stage: 'awake',
        duration: awakeDuration,
        startTime: currentTime.toISOString()
      });
      
      remainingTime -= awakeDuration;
      currentTime = new Date(currentTime.getTime() + awakeDuration * 60000);
    }
  }
  
  return stages;
}

/**
 * Provide sleep insights based on statistics
 */
export function generateSleepInsights(stats: SleepStatistics): string[] {
  const insights: string[] = [];
  
  // Duration-based insights
  const avgHours = Math.round(stats.averageDuration / 6) / 10;
  
  if (avgHours < 6) {
    insights.push(`You're averaging ${avgHours} hours of sleep per night, which is less than the recommended 7-9 hours for optimal health.`);
    insights.push('Chronic sleep deprivation can affect mood, memory, and immune function.');
  } else if (avgHours > 9) {
    insights.push(`You're averaging ${avgHours} hours of sleep per night, which is more than the typical recommendation. While extra sleep is sometimes needed, consistently oversleeping can be associated with health issues.`);
  } else {
    insights.push(`Your average sleep duration of ${avgHours} hours falls within the recommended range of 7-9 hours.`);
  }
  
  // Sleep debt insights
  if (stats.sleepDebt > 60) {
    const debtHours = Math.round(stats.sleepDebt / 60);
    insights.push(`You have a sleep debt of approximately ${debtHours} hours over the tracked period. Consider gradually extending your sleep time to repay this debt.`);
  } else if (stats.sleepDebt < -60) {
    const surplusHours = Math.round(-stats.sleepDebt / 60);
    insights.push(`You have a sleep surplus of approximately ${surplusHours} hours over the tracked period. You're getting more sleep than your target.`);
  }
  
  // Consistency insights
  if (stats.consistencyScore < 50) {
    insights.push("Your sleep schedule consistency could use improvement. Going to bed and waking up at the same time each day, even on weekends, helps regulate your body's internal clock.");
  } else if (stats.consistencyScore >= 80) {
    insights.push("You maintain a consistent sleep schedule, which is excellent for your body's internal clock and overall sleep quality.");
  }
  
  // Sleep quality insights
  if (stats.averageQuality === SleepQuality.POOR || stats.averageQuality === SleepQuality.FAIR) {
    insights.push('Your reported sleep quality is lower than ideal. Consider addressing factors like your sleep environment, evening routine, or stress levels.');
  } else {
    insights.push('You report good sleep quality, which suggests your sleep is restorative.');
  }
  
  // Sleep efficiency insights
  if (stats.sleepEfficiency < 70) {
    insights.push('Your sleep efficiency is low, meaning you spend a lot of time in bed not sleeping. This might indicate sleep maintenance issues.');
  } else if (stats.sleepEfficiency >= 85) {
    insights.push('Your sleep efficiency is excellent, suggesting you fall asleep quickly and stay asleep through the night.');
  }
  
  // Interruptions insights
  if (stats.interruptions > 3 && stats.daysTracked > 0) {
    const avgInterruptions = Math.round((stats.interruptions / stats.daysTracked) * 10) / 10;
    insights.push(`You experience an average of ${avgInterruptions} interruptions per night, which may be affecting your sleep quality. Consider addressing potential causes like noise, temperature, or sleep apnea.`);
  }
  
  // Return unique insights
  return [...new Set(insights)];
} 