import { Platform, Share } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as ImageManipulator from 'expo-image-manipulator';
import { captureRef } from 'react-native-view-shot';
import * as secureStorage from './secureStorage';

// Types for social sharing
export interface SocialShareOptions {
  title?: string;
  message: string;
  url?: string;
  imageUrl?: string;
  hashtags?: string[];
  platform?: SocialPlatform;
}

export enum SocialPlatform {
  FACEBOOK = 'facebook',
  INSTAGRAM = 'instagram',
  TWITTER = 'twitter',
  GENERIC = 'generic',
}

export interface MealShareData {
  mealId: string;
  mealName: string;
  imageUri?: string;
  nutritionData: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  date: string;
  description?: string;
}

export interface ProgressShareData {
  achievementType: string;
  title: string;
  description: string;
  date: string;
  imageUri?: string;
  stats?: Record<string, any>;
}

// Social sharing service
class SocialSharingService {
  // Share content to the device's native share dialog
  async shareGeneric(options: SocialShareOptions): Promise<boolean> {
    try {
      const shareOptions = {
        title: options.title,
        message: options.message,
        url: options.url,
      };
      
      const result = await Share.share(shareOptions);
      return result.action !== Share.dismissedAction;
    } catch (error) {
      console.error('Error sharing content:', error);
      return false;
    }
  }

  // Share to specific platform
  async shareToPlatform(options: SocialShareOptions): Promise<boolean> {
    try {
      if (!options.platform) {
        return this.shareGeneric(options);
      }

      // Use the native share dialog instead of platform-specific APIs
      // since we don't have the expo-social-media package
      return await this.shareGeneric(options);

      // The following code would be used if we had platform-specific APIs
      /*
      switch (options.platform) {
        case SocialPlatform.FACEBOOK:
          return await SocialMedia.shareToFacebook({
            message: options.message,
            url: options.url,
            imageUrl: options.imageUrl,
          });
        
        case SocialPlatform.INSTAGRAM:
          // Instagram requires image sharing
          if (!options.imageUrl) {
            throw new Error('An image is required for Instagram sharing');
          }
          return await SocialMedia.shareToInstagram({
            imageUrl: options.imageUrl,
            caption: options.message,
          });
        
        case SocialPlatform.TWITTER:
          return await SocialMedia.shareToTwitter({
            message: options.message,
            url: options.url,
            hashtags: options.hashtags || [],
          });
          
        default:
          return this.shareGeneric(options);
      }
      */
    } catch (error) {
      console.error(`Error sharing to ${options.platform}:`, error);
      return false;
    }
  }

  // Share meal data with image
  async shareMeal(mealData: MealShareData): Promise<boolean> {
    try {
      const message = `I just had ${mealData.mealName} (${mealData.nutritionData.calories} cal)! #HealthApp #HealthyEating`;
      
      const options: SocialShareOptions = {
        title: 'Share Your Meal',
        message,
        imageUrl: mealData.imageUri,
        hashtags: ['HealthApp', 'HealthyEating', 'Nutrition'],
      };
      
      return await this.shareGeneric(options);
    } catch (error) {
      console.error('Error sharing meal:', error);
      return false;
    }
  }

  // Share progress/achievements
  async shareProgress(progressData: ProgressShareData): Promise<boolean> {
    try {
      const message = `I just ${progressData.title}: ${progressData.description} #HealthApp #FitnessGoals`;
      
      const options: SocialShareOptions = {
        title: 'Share Your Achievement',
        message,
        imageUrl: progressData.imageUri,
        hashtags: ['HealthApp', 'FitnessGoals', 'HealthyLiving'],
      };
      
      return await this.shareGeneric(options);
    } catch (error) {
      console.error('Error sharing progress:', error);
      return false;
    }
  }

  // Export meal plan as PDF
  async exportMealPlan(weekStartDate: Date, weekEndDate: Date): Promise<string | null> {
    try {
      // Implementation would connect to a PDF generation service
      // For now, we'll simulate creating a shareable file
      const fileName = `meal-plan-${weekStartDate.toISOString().split('T')[0]}.pdf`;
      const filePath = `${FileSystem.documentDirectory}${fileName}`;
      
      // Here would be the logic to create the PDF
      
      // Check if sharing is available
      if (!(await Sharing.isAvailableAsync())) {
        return null;
      }
      
      // Share the file
      await Sharing.shareAsync(filePath);
      return filePath;
    } catch (error) {
      console.error('Error exporting meal plan:', error);
      return null;
    }
  }

  // Export shopping list
  async exportShoppingList(listItems: string[]): Promise<string | null> {
    try {
      // Create a text file with shopping list items
      const content = listItems.join('\n');
      const fileName = `shopping-list-${new Date().toISOString().split('T')[0]}.txt`;
      const filePath = `${FileSystem.documentDirectory}${fileName}`;
      
      await FileSystem.writeAsStringAsync(filePath, content);
      
      // Check if sharing is available
      if (!(await Sharing.isAvailableAsync())) {
        return null;
      }
      
      // Share the file
      await Sharing.shareAsync(filePath);
      return filePath;
    } catch (error) {
      console.error('Error exporting shopping list:', error);
      return null;
    }
  }

  // Capture and share component screenshot
  async captureAndShareComponent(componentRef: React.RefObject<any>, caption: string): Promise<boolean> {
    try {
      if (!componentRef.current) {
        throw new Error('Component reference is not available');
      }
      
      // Capture component as image
      const uri = await captureRef(componentRef, {
        format: 'png',
        quality: 0.8,
      });
      
      // Compress image for sharing
      const manipResult = await ImageManipulator.manipulateAsync(
        uri,
        [{ resize: { width: 1200 } }],
        { compress: 0.8, format: ImageManipulator.SaveFormat.PNG }
      );
      
      // Share the image
      const options: SocialShareOptions = {
        message: caption,
        imageUrl: manipResult.uri,
      };
      
      return await this.shareGeneric(options);
    } catch (error) {
      console.error('Error capturing and sharing component:', error);
      return false;
    }
  }
}

export const socialSharingService = new SocialSharingService(); 