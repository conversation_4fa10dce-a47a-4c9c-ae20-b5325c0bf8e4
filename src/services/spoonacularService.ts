import { SPOONACULAR_API_KEY } from '@/utils/config';

const BASE_URL = 'https://api.spoonacular.com';

// Define interfaces for API responses
interface SearchResult {
  results: {
    id: number;
    title: string;
    image: string;
  }[];
  totalResults: number;
}

interface IngredientSearchResult {
  results: {
    id: number;
    name: string;
    image: string;
  }[];
}

/**
 * Search for recipes
 */
export async function searchRecipes(query: string, number: number = 10) {
  try {
    const url = `${BASE_URL}/recipes/complexSearch?apiKey=${SPOONACULAR_API_KEY}&query=${encodeURIComponent(query)}&number=${number}`;
    const response = await fetch(url);
    return await response.json() as SearchResult;
  } catch (error) {
    console.error('Error searching recipes:', error);
    throw error;
  }
}

/**
 * Get recipe information
 */
export async function getRecipeInformation(id: number) {
  try {
    const url = `${BASE_URL}/recipes/${id}/information?apiKey=${SPOONACULAR_API_KEY}&includeNutrition=true`;
    const response = await fetch(url);
    return await response.json();
  } catch (error) {
    console.error('Error getting recipe information:', error);
    throw error;
  }
}

/**
 * Get nutrition information for a food item
 */
export async function getFoodInformation(query: string) {
  try {
    const url = `${BASE_URL}/food/ingredients/search?apiKey=${SPOONACULAR_API_KEY}&query=${encodeURIComponent(query)}&number=1`;
    const response = await fetch(url);
    const data = await response.json() as IngredientSearchResult;
    
    if (data.results && data.results.length > 0) {
      const id = data.results[0].id;
      const detailUrl = `${BASE_URL}/food/ingredients/${id}/information?apiKey=${SPOONACULAR_API_KEY}&amount=1`;
      const detailResponse = await fetch(detailUrl);
      return await detailResponse.json();
    }
    return null;
  } catch (error) {
    console.error('Error getting food information:', error);
    throw error;
  }
}

/**
 * Get nutrition information by natural language query
 */
export async function getNutritionByQuery(query: string) {
  try {
    const url = `${BASE_URL}/recipes/parseIngredients?apiKey=${SPOONACULAR_API_KEY}`;
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `ingredientList=${encodeURIComponent(query)}&servings=1`
    });
    return await response.json();
  } catch (error) {
    console.error('Error parsing ingredient:', error);
    throw error;
  }
}

/**
 * Generate meal plan
 */
export async function generateMealPlan(
  timeFrame: 'day' | 'week', 
  targetCalories?: number, 
  diet?: string,
  exclude?: string
) {
  try {
    let url = `${BASE_URL}/mealplanner/generate?apiKey=${SPOONACULAR_API_KEY}&timeFrame=${timeFrame}`;
    
    if (targetCalories) url += `&targetCalories=${targetCalories}`;
    if (diet) url += `&diet=${diet}`;
    if (exclude) url += `&exclude=${exclude}`;
    
    const response = await fetch(url);
    return await response.json();
  } catch (error) {
    console.error('Error generating meal plan:', error);
    throw error;
  }
} 