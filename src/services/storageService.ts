import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import { Platform } from 'react-native';
// import { useDatabaseType } from '@/contexts/DatabaseContext';
import { decode } from 'base64-arraybuffer';


export function useStorageService() {
  const { db, useFirebase  } = useDatabaseType();

  /**
   * Upload profile avatar
   */
  const uploadProfileAvatar = async (userId: string, uri: string): Promise<string> => {
    try {
      // Resize and compress image
      const manipResult = await ImageManipulator.manipulateAsync(
        uri,
        [{ resize: { width: 300, height: 300 } }],
        { compress: 0.7, format: ImageManipulator.SaveFormat.JPEG }
      );

      // For mobile platforms, we need the file URI
      if (Platform.OS !== 'web') {
        // Convert to base64 string to handle both providers uniformly
        const base64 = await FileSystem.readAsStringAsync(manipResult.uri, {
          encoding: FileSystem.EncodingType.Base64,
        });
        
        // Create the full data URL
        const dataUrl = `data:image/jpeg;base64,${base64}`;
        
        // Use the appropriate path based on provider
        const path = useFirebase 
          ? `avatars/${userId}/profile.jpg` 
          : `avatars/${userId}/profile`;

        return await db.uploadFile(path, dataUrl, {
          contentType: 'image/jpeg'
        });
      } 
      
      // For web, we can use the result URI directly
      const blob = await fetch(manipResult.uri).then(r => r.blob());
      const path = useFirebase 
        ? `avatars/${userId}/profile.jpg` 
        : `avatars/${userId}/profile`;
        
      return await db.uploadFile(path, blob, {
        contentType: 'image/jpeg'
      });
    } catch (error) {
      console.error('Error uploading profile avatar:', error);
      throw error;
    }
  };

  /**
   * Get profile avatar URL
   */
  const getProfileAvatarUrl = async (userId: string): Promise<string> => {
    try {
      const path = useFirebase 
        ? `avatars/${userId}/profile.jpg` 
        : `avatars/${userId}/profile`;
        
      return await db.getFileUrl(path);
    } catch (error) {
      console.error('Error getting profile avatar URL:', error);
      // Return a default avatar URL if not found
      return 'https://via.placeholder.com/150';
    }
  };

  /**
   * Upload food image
   */
  const uploadFoodImage = async (userId: string, uri: string): Promise<string> => {
    try {
      // Manipulate image if needed
      const manipResult = await ImageManipulator.manipulateAsync(
        uri,
        [{ resize: { width: 800, height: 800 } }],
        { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
      );

      // Generate a unique filename
      const timestamp = new Date().getTime();
      const filename = `food-${timestamp}.jpg`;
      
      // For mobile platforms
      if (Platform.OS !== 'web') {
        const base64 = await FileSystem.readAsStringAsync(manipResult.uri, {
          encoding: FileSystem.EncodingType.Base64,
        });
        
        const dataUrl = `data:image/jpeg;base64,${base64}`;
        const path = useFirebase 
          ? `food-images/${userId}/${filename}` 
          : `food-images/${userId}/${filename}`;
          
        return await db.uploadFile(path, dataUrl, {
          contentType: 'image/jpeg'
        });
      } 
      
      // For web
      const blob = await fetch(manipResult.uri).then(r => r.blob());
      const path = useFirebase 
        ? `food-images/${userId}/${filename}` 
        : `food-images/${userId}/${filename}`;
        
      return await db.uploadFile(path, blob, {
        contentType: 'image/jpeg'
      });
    } catch (error) {
      console.error('Error uploading food image:', error);
      throw error;
    }
  };

  /**
   * Delete file
   */
  const deleteFile = async (path: string): Promise<void> => {
    try {
      await db.deleteFile(path);
    } catch (error) {
      console.error(`Error deleting file at ${path}:`, error);
      throw error;
    }
  };

  return {
    uploadProfileAvatar,
    getProfileAvatarUrl,
    uploadFoodImage,
    deleteFile
  };
} 