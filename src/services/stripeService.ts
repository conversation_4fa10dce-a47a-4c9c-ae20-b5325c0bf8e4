
import { useDatabaseType } from '@/contexts/DatabaseContext';
import { httpsCallable, getFunctions } from 'firebase/functions';
import { getAuth } from 'firebase/auth';
import { addDoc, collection, query, where, getFirestore, orderBy, getDocs } from 'firebase/firestore';

interface CreateCheckoutSessionParams {
  priceId: string;
  mode: 'payment' | 'subscription';
  successUrl: string;
  cancelUrl: string;
}

interface SubscriptionResponse {
  subscription: any;
  error: string | null;
}

interface CheckoutResponse {
  url?: string;
  sessionId?: string;
  error?: string;
}

/**
 * Interface for Stripe checkout request
 */
export interface StripeCheckoutRequest {
  price_id: string;
  success_url: string;
  cancel_url: string;
  mode: 'payment' | 'subscription';
}

/**
 * Interface for Stripe checkout response
 */
export interface StripeCheckoutResponse {
  sessionId: string;
  url: string;
}

/**
 * Interface for a Stripe subscription
 */
export interface StripeSubscription {
  id?: string;
  customer_id: string;
  subscription_id?: string;
  price_id?: string;
  status: string;
  current_period_start?: Date;
  current_period_end?: Date;
  cancel_at_period_end?: boolean;
  payment_method_brand?: string;
  payment_method_last4?: string;
  created_at?: Date;
  updated_at?: Date;
}

/**
 * Interface for a Stripe order
 */
export interface StripeOrder {
  id?: string;
  checkout_session_id: string;
  payment_intent_id?: string;
  customer_id: string;
  amount_subtotal?: number;
  amount_total: number;
  currency: string;
  payment_status: string;
  status: string;
  created_at?: Date;
  updated_at?: Date;
}

/**
 * Creates a Stripe checkout session for a product or subscription
 */
export async function createCheckoutSession(params: CreateCheckoutSessionParams): Promise<CheckoutResponse> {
  try {
    // Log analytics event
    await logBillingEvent('checkout_initiated', { priceId: params.priceId, mode: params.mode });
    
    // Convert camelCase to snake_case for the Cloud Function
    const functionParams = {
      price_id: params.priceId,
      mode: params.mode,
      success_url: params.successUrl,
      cancel_url: params.cancelUrl
    };

    // Use Firebase Cloud Function
    const functions = getFunctions();
    const stripeCheckoutFn = httpsCallable<
      StripeCheckoutRequest,
      StripeCheckoutResponse
    >(functions, 'stripeCheckout');

    const result = await stripeCheckoutFn(functionParams);
    const data = result.data;

    await logBillingEvent('checkout_success', { sessionId: data.sessionId });
    return data;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error creating checkout session:', errorMessage);
    await logBillingEvent('checkout_error', { error: errorMessage });
    return { error: 'Failed to create checkout session' };
  }
}

/**
 * Get the current user's subscription status
 */
export async function checkSubscription(): Promise<SubscriptionResponse> {
  try {
    // First, get the current user's email to check if they're an admin
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return { error: 'User not authenticated', subscription: null };
    }
    
    if (isAdminUser(user.email)) {
      console.log('Admin user detected, returning admin subscription');
      // Return a mock subscription for admin users
      return { 
        subscription: {
          id: 'admin-subscription',
          user_id: user.uid,
          subscription_status: 'active',
          current_period_end: Math.floor(new Date().setFullYear(new Date().getFullYear() + 10) / 1000), // 10 years from now
          plan_name: 'Admin Plan',
          product_id: 'admin-unlimited',
          is_admin: true,
        }, 
        error: null 
      };
    }
    
    // Use Firestore query to get subscription
    const firestore = getFirestore();
    const subscriptionsRef = collection(firestore, 'stripe_user_subscriptions');
    const q = query(subscriptionsRef, where('user_id', '==', user.uid));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return { subscription: null, error: null };
    }
    
    const data = querySnapshot.docs[0].data();
    return { subscription: data, error: null };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error checking subscription:', errorMessage);
    return { error: 'Failed to check subscription status', subscription: null };
  }
}

/**
 * Create a billing portal session for managing subscriptions
 */
export async function createPortalSession(returnUrl: string): Promise<CheckoutResponse> {
  try {
    // Use Firebase Cloud Function
    const functions = getFunctions();
    const createPortalLinkFn = httpsCallable(functions, 'createPortalLink');
    
    const result = await createPortalLinkFn({ returnUrl });
    const data = result.data as { url: string };
    
    return { url: data.url };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error creating portal session:', errorMessage);
    return { error: 'Failed to create portal session' };
  }
}

/**
 * Check if a feature is available based on subscription tier
 */
export async function canAccessFeature(featureKey: string): Promise<boolean> {
  try {
    // First, get the current user's email to check if they're an admin
    const auth = getAuth();
    const user = auth.currentUser;
    
    // Admin users have access to all features
    if (user && isAdminUser(user.email)) {
      console.log(`Admin access granted for feature: ${featureKey}`);
      return true;
    }
    
    const { subscription, error } = await checkSubscription();
    
    if (error || !subscription) {
      return false;
    }
    
    // If subscription has is_admin flag, grant access to all features
    if (subscription.is_admin) {
      return true;
    }
    
    // Basic features available to all subscribers
    const basicFeatures = ['nutrition_tracking', 'meal_logging', 'basic_analytics'];
    
    // Premium features
    const premiumFeatures = [
      'ai_recommendations', 
      'advanced_analytics', 
      'unlimited_meals'
    ];
    
    // Family plan features
    const familyFeatures = [
      'family_accounts',
      'priority_support',
      'meal_sharing'
    ];
    
    // If user has an active subscription
    if (subscription.subscription_status === 'active') {
      // Basic tier has access to basic features only
      if (subscription.plan_name?.toLowerCase().includes('basic')) {
        return basicFeatures.includes(featureKey);
      }
      
      // Premium tier has access to basic and premium features
      if (subscription.plan_name?.toLowerCase().includes('premium')) {
        return [...basicFeatures, ...premiumFeatures].includes(featureKey);
      }
      
      // Family tier has access to all features
      if (subscription.plan_name?.toLowerCase().includes('family')) {
        return [...basicFeatures, ...premiumFeatures, ...familyFeatures].includes(featureKey);
      }
    }
    
    return false;
  } catch (error) {
    console.error('Error checking feature access:', error);
    return false;
  }
}

/**
 * Log billing-related events for analytics
 */
async function logBillingEvent(eventName: string, properties: Record<string, any> = {}): Promise<void> {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    if (!user) return;
    
    const firestore = getFirestore();
    await addDoc(collection(firestore, 'user_analytics_events'), {
      user_id: user.uid,
      event_name: `billing_${eventName}`,
      properties,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    // Don't let analytics errors affect the main flow
    console.error('Error logging billing event:', error);
  }
}

// Add a function to check if a user is an admin
export function isAdminUser(email: string | undefined | null): boolean {
  // Admin email addresses
  const adminEmails = ['<EMAIL>'];
  return adminEmails.includes(email || '');
}

/**
 * Service for Stripe functionality
 */
export function useStripeService() {
  const { db, useFirebase } = useDatabaseType();

  /**
   * Create a Stripe checkout session
   */
  const createCheckoutSession = async (
    request: StripeCheckoutRequest
  ): Promise<StripeCheckoutResponse> => {
    try {
      if (useFirebase) {
        // Use Firebase Cloud Function
        const functions = getFunctions();
        const stripeCheckoutFn = httpsCallable<
          StripeCheckoutRequest,
          StripeCheckoutResponse
        >(functions, 'stripeCheckout');

        const result = await stripeCheckoutFn(request);
        return result.data;
      } else {
        // Direct API call since DatabaseProvider doesn't support function calls
        const functions = getFunctions();
        const stripeCheckoutFn = httpsCallable<
          StripeCheckoutRequest,
          StripeCheckoutResponse
        >(functions, 'stripeCheckout');
        
        const result = await stripeCheckoutFn(request);
        return result.data;
      }
    } catch (error) {
      console.error('Error in stripeService:', error);
      throw error;
    }
  };

  /**
   * Get current user's subscription
   */
  const getUserSubscription = async (): Promise<StripeSubscription | null> => {
    try {
      const user = await db.getCurrentUser();
      const userId = user?.id;
      
      if (!userId) {
        throw new Error('User not authenticated');
      }
      
      // Get customer mapping
      const customerFilters = [
        { field: 'user_id', operator: '==', value: userId },
        { field: 'deleted_at', operator: '==', value: null }
      ];
      
      const customerResults = await db.queryDocuments('stripe_customers', customerFilters);
      
      if (!customerResults.length) {
        return null; // No subscription found
      }
      
      const customerId = customerResults[0].customer_id;
      
      // Get subscription for this customer
      const subscriptionFilters = [
        { field: 'customer_id', operator: '==', value: customerId }
      ];
      
      const subscriptionResults = await db.queryDocuments('stripe_subscriptions', subscriptionFilters);
      
      if (!subscriptionResults.length) {
        return null; // No subscription found
      }
      
      return subscriptionResults[0] as StripeSubscription;
    } catch (error) {
      console.error('Error getting user subscription:', error);
      throw error;
    }
  };

  /**
   * Get user's order history
   */
  const getUserOrders = async (limit = 10): Promise<StripeOrder[]> => {
    try {
      const user = await db.getCurrentUser();
      const userId = user?.id;
      
      if (!userId) {
        throw new Error('User not authenticated');
      }
      
      // Get customer mapping
      const customerFilters = [
        { field: 'user_id', operator: '==', value: userId },
        { field: 'deleted_at', operator: '==', value: null }
      ];
      
      const customerResults = await db.queryDocuments('stripe_customers', customerFilters);
      
      if (!customerResults.length) {
        return []; // No customer, so no orders
      }
      
      const customerId = customerResults[0].customer_id;
      
      // Get orders for this customer
      const orderFilters = [
        { field: 'customer_id', operator: '==', value: customerId }
      ];
      
      const orderResults = await db.queryDocuments('stripe_orders', orderFilters);
      
      // Apply the limit after fetching
      return (orderResults as StripeOrder[]).slice(0, limit);
    } catch (error) {
      console.error('Error getting user orders:', error);
      throw error;
    }
  };

  return {
    createCheckoutSession,
    getUserSubscription,
    getUserOrders
  };
}