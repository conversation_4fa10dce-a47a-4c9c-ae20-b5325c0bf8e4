/**
 * Reminder Service
 * 
 * This service provides functionality for reminders and notifications using Firebase.
 */

import { useDatabaseType } from '@/contexts/DatabaseContext';
import * as FirebaseReminderService from './reminderFirebaseService';
export type { ReminderPreferences, Reminder } from './reminderFirebaseService';

/**
 * Hook for reminder and notification service
 * 
 * @returns Functions for managing reminders and notifications
 */
export function useReminderService() {
  const { db  } = useDatabaseType();
  
  /**
   * Register for push notifications
   */
  const registerForPushNotifications = async () => {
    try {
      return await FirebaseReminderService.registerForPushNotifications();
    } catch (error) {
      console.error('Error registering for push notifications:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Schedule a mindfulness reminder
   */
  const scheduleMindfulnessReminder = async (
    title: string = 'Mindfulness Reminder',
    body: string = 'Take a moment to practice mindfulness.',
    time?: Date
  ) => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      return await FirebaseReminderService.scheduleMindfulnessReminder(
        user.id,
        title,
        body,
        time
      );
    } catch (error) {
      console.error('Error scheduling mindfulness reminder:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Cancel all existing mindfulness reminders
   */
  const cancelMindfulnessReminders = async () => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      const remindersResult = await FirebaseReminderService.getUserReminders(user.id);
      
      if (!remindersResult.success || !remindersResult.data) {
        return {
          success: false,
          error: remindersResult.error || 'Failed to get user reminders'
        };
      }
      
      const mindfulnessReminders = remindersResult.data.filter(
        reminder => reminder.type === 'mindfulness'
      );
      
      let successCount = 0;
      for (const reminder of mindfulnessReminders) {
        const deleteResult = await FirebaseReminderService.deleteReminder(reminder.id);
        if (deleteResult.success) {
          successCount++;
        }
      }
      
      return {
        success: true,
        canceledCount: successCount
      };
    } catch (error) {
      console.error('Error canceling mindfulness reminders:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Setup default mindfulness reminders for a user
   */
  const setupDefaultMindfulnessReminders = async () => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      // Get preferences first
      const prefsResult = await FirebaseReminderService.getReminderPreferences(user.id);
      
      if (!prefsResult.success || !prefsResult.data) {
        return {
          success: false,
          error: prefsResult.error || 'Failed to get user preferences'
        };
      }
      
      // Schedule morning reminder
      const morning = new Date();
      morning.setHours(8, 0, 0, 0);
      
      if (morning < new Date()) {
        morning.setDate(morning.getDate() + 1);
      }
      
      await FirebaseReminderService.createReminder(
        user.id,
        'Morning Mindfulness',
        'Start your day with a moment of mindfulness. Take a deep breath and set your intentions.',
        morning,
        'mindfulness',
        'daily'
      );
      
      // Schedule evening reminder
      const evening = new Date();
      evening.setHours(18, 0, 0, 0);
      
      if (evening < new Date()) {
        evening.setDate(evening.getDate() + 1);
      }
      
      await FirebaseReminderService.createReminder(
        user.id,
        'Evening Reflection',
        'Take a moment to reflect on your day. What are you grateful for today?',
        evening,
        'mindfulness',
        'daily'
      );
      
      return { success: true, message: 'Default reminders set up' };
    } catch (error) {
      console.error('Error setting up default mindfulness reminders:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Get reminder preferences for a user
   */
  const getReminderPreferences = async () => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      return await FirebaseReminderService.getReminderPreferences(user.id);
    } catch (error) {
      console.error('Error getting reminder preferences:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Set reminder preferences for a user
   */
  const setReminderPreferences = async (preferences: FirebaseReminderService.ReminderPreferences) => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      return await FirebaseReminderService.setReminderPreferences(user.id, preferences);
    } catch (error) {
      console.error('Error setting reminder preferences:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Create a new reminder
   */
  const createReminder = async (
    title: string,
    body: string,
    scheduledTime: Date,
    type: FirebaseReminderService.Reminder['type'] = 'custom',
    repeat: FirebaseReminderService.Reminder['repeat'] = 'none'
  ) => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      return await FirebaseReminderService.createReminder(
        user.id,
        title,
        body,
        scheduledTime,
        type,
        repeat
      );
    } catch (error) {
      console.error('Error creating reminder:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Get all reminders for the current user
   */
  const getUserReminders = async () => {
    try {
      const user = await db.getCurrentUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      return await FirebaseReminderService.getUserReminders(user.id);
    } catch (error) {
      console.error('Error getting user reminders:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  /**
   * Delete a reminder
   */
  const deleteReminder = async (reminderId: string) => {
    try {
      return await FirebaseReminderService.deleteReminder(reminderId);
    } catch (error) {
      console.error('Error deleting reminder:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  };
  
  return {
    registerForPushNotifications,
    scheduleMindfulnessReminder,
    cancelMindfulnessReminders,
    setupDefaultMindfulnessReminders,
    getReminderPreferences,
    setReminderPreferences,
    createReminder,
    getUserReminders,
    deleteReminder
  };
} 