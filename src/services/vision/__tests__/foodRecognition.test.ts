/// <reference types="jest" />

// @ts-nocheck
import { analyzeFoodImage, getFoodNutritionData } from '../foodRecognition';
import { apiClient } from '../../apiClient';
import * as ImageProcessing from '@/utils/imageProcessing';

// Mock the API client and image processing utilities
jest.mock('../../apiClient', () => ({
  apiClient: {
    post: jest.fn(),
    get: jest.fn(),
  },
  visionApiClient: {
    post: jest.fn(),
  },
}));

jest.mock('../../../utils/imageProcessing', () => ({
  resizeImage: jest.fn(),
  optimizeImageForUpload: jest.fn(),
  getBase64FromUri: jest.fn(),
}));

describe('Food Recognition Service', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    
    // Setup default mock implementations
    (ImageProcessing.optimizeImageForUpload as jest.Mock).mockResolvedValue('optimized-image-uri');
    (ImageProcessing.getBase64FromUri as jest.Mock).mockResolvedValue('base64-image-data');
  });

  describe('analyzeFoodImage', () => {
    it('should properly optimize and process the image before API call', async () => {
      // Mock successful API response
      (apiClient.post as jest.Mock).mockResolvedValue({
        success: true,
        data: {
          name: 'Test Food',
          calories: 250,
          protein: 10,
          carbs: 30,
          fat: 8,
          items: [],
        },
      });

      // Call the function
      await analyzeFoodImage('test-image-uri', { includeDetailsLevel: 'standard' });

      // Verify image was optimized
      expect(ImageProcessing.optimizeImageForUpload).toHaveBeenCalledWith(
        'test-image-uri',
        expect.any(Number)
      );

      // Verify base64 conversion
      expect(ImageProcessing.getBase64FromUri).toHaveBeenCalledWith('optimized-image-uri');

      // Verify API call with correct parameters
      expect(apiClient.post).toHaveBeenCalledWith(
        '/analyze-food',
        expect.objectContaining({
          image: 'base64-image-data',
          options: expect.objectContaining({
            includeDetailsLevel: 'standard',
          }),
        }),
        expect.any(Object)
      );
    });

    it('should handle API errors properly', async () => {
      // Mock API error
      (apiClient.post as jest.Mock).mockRejectedValue({
        success: false,
        error: 'API error',
      });

      // Call the function and expect it to handle the error
      const result = await analyzeFoodImage('test-image-uri');

      // Verify error handling
      expect(result).toEqual(
        expect.objectContaining({
          success: false,
          error: expect.any(String),
        })
      );
    });

    it('should include additional options in the API request', async () => {
      // Mock successful API response
      (apiClient.post as jest.Mock).mockResolvedValue({
        success: true,
        data: {
          name: 'Test Food',
          calories: 250,
          protein: 10,
          carbs: 30,
          fat: 8,
          items: [],
        },
      });

      // Call with additional options
      await analyzeFoodImage('test-image-uri', {
        includeDetailsLevel: 'comprehensive',
        includeFoodQualityAssessment: true,
        includeIngredientDetection: true,
      });

      // Verify API call included all options
      expect(apiClient.post).toHaveBeenCalledWith(
        '/analyze-food',
        expect.objectContaining({
          options: expect.objectContaining({
            includeDetailsLevel: 'comprehensive',
            includeFoodQualityAssessment: true,
            includeIngredientDetection: true,
          }),
        }),
        expect.any(Object)
      );
    });
  });

  describe('getFoodNutritionData', () => {
    it('should fetch nutrition data for a food item', async () => {
      // Mock successful API response
      (apiClient.get as jest.Mock).mockResolvedValue({
        success: true,
        data: {
          name: 'Banana',
          calories: 105,
          protein: 1.3,
          carbs: 27,
          fat: 0.4,
          servingSize: '118g (1 medium)',
        },
      });

      // Call the function
      const result = await getFoodNutritionData('Banana');

      // Verify API call
      expect(apiClient.get).toHaveBeenCalledWith(
        '/food-nutrition',
        expect.objectContaining({
          name: 'Banana',
        }),
        expect.any(Object)
      );

      // Verify result
      expect(result).toEqual(
        expect.objectContaining({
          success: true,
          data: expect.objectContaining({
            name: 'Banana',
            calories: 105,
          }),
        })
      );
    });

    it('should handle API errors when fetching nutrition data', async () => {
      // Mock API error
      (apiClient.get as jest.Mock).mockRejectedValue({
        success: false,
        error: 'Food not found',
      });

      // Call the function
      const result = await getFoodNutritionData('UnknownFood');

      // Verify error handling
      expect(result).toEqual(
        expect.objectContaining({
          success: false,
          error: expect.any(String),
        })
      );
    });

    it('should include portion information when provided', async () => {
      // Mock successful API response
      (apiClient.get as jest.Mock).mockResolvedValue({
        success: true,
        data: {
          name: 'Rice',
          calories: 200,
          protein: 4.5,
          carbs: 45,
          fat: 0.6,
          servingSize: '158g (1 cup cooked)',
        },
      });

      // Call with portion info
      await getFoodNutritionData('Rice', { portionSize: 'cup', quantity: 0.5 });

      // Verify API call included portion info
      expect(apiClient.get).toHaveBeenCalledWith(
        '/food-nutrition',
        expect.objectContaining({
          name: 'Rice',
          portionSize: 'cup',
          quantity: 0.5,
        }),
        expect.any(Object)
      );
    });
  });
}); 