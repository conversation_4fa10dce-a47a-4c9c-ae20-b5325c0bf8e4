import { Camera } from 'expo-camera';
import { UnifiedFoodAnalysisResult } from './types';
import { nutritionApiClient } from '@/services/apiClient';
import { FoodItem } from '@/types/food';
import { useState, useEffect } from 'react';
import { Text } from 'react-native';
import * as firebaseServices from '@/lib/firebase';
import { collection, query, where, getDocs, addDoc, setDoc, doc, Firestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';

// Type the Firebase database
const db = firebaseServices.db as Firestore;

// Define product info interface
interface ProductInfo {
  name: string;
  brand: string;
  nutritionalInfo: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    sodium: number;
    sugar: number;
  };
  ingredients: string[];
  allergens: string[];
}

const BARCODE_API_KEY = process.env.EXPO_PUBLIC_BARCODE_API_KEY;
const BARCODE_API_URL = process.env.EXPO_PUBLIC_BARCODE_API_URL || 'https://api.calorielens.com/barcode';
const OPEN_FOOD_FACTS_API_URL = 'https://world.openfoodfacts.org/api/v0/product';

interface BarcodeRecognitionResult {
  product: FoodItem | null;
  success: boolean;
  error?: string;
}

// Add interfaces for API responses
interface BarcodeApiResponse {
  barcodes?: any[];
}

interface OpenFoodFactsResponse {
  status?: number;
  product?: {
    product_name?: string;
    brands?: string;
    nutriments?: any;
    ingredients_text?: string;
    allergens_tags?: string[];
    image_url?: string;
  };
}

interface ProductApiResponse {
  product?: {
    name?: string;
    brand?: string;
    nutrients?: {
      calories?: number;
      protein?: number;
      carbs?: number;
      fat?: number;
      fiber?: number;
      sodium?: number;
      sugar?: number;
    };
    ingredients?: string[];
    allergens?: string[];
    serving_size?: number;
  };
}

/**
 * Detects barcodes in an image
 */
export async function detectBarcodes(imageData: string): Promise<{
  value: string;
  format: string;
  bounds: {
    topLeft: [number, number];
    topRight: [number, number];
    bottomLeft: [number, number];
    bottomRight: [number, number];
  }
}[]> {
  try {
    // Request permission for camera (needed on some platforms)
    const { status } = await Camera.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      console.warn('Camera permission not granted');
      return [];
    }

    // Use a third-party service or library to process the image
    const apiUrl = process.env.EXPO_PUBLIC_BARCODE_DETECTION_API || 'https://api.calorielens.com/detect-barcode';
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(BARCODE_API_KEY ? { 'Authorization': `Bearer ${BARCODE_API_KEY}` } : {})
      },
      body: JSON.stringify({
        image: imageData.startsWith('data:') ? imageData : `data:image/jpeg;base64,${imageData}`
      })
    });

    if (!response.ok) {
      throw new Error(`Barcode detection API failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json() as BarcodeApiResponse;
    
    if (!result.barcodes || !Array.isArray(result.barcodes) || result.barcodes.length === 0) {
      console.log('No barcodes detected in the image');
      return [];
    }

    return result.barcodes.map((barcode: any) => ({
      value: barcode.value,
      format: barcode.format || 'unknown',
      bounds: barcode.bounds || {
        topLeft: [0, 0],
        topRight: [1, 0],
        bottomLeft: [0, 1],
        bottomRight: [1, 1]
      }
    }));
  } catch (error) {
    console.error('Error detecting barcodes:', error);
    return [];
  }
}

/**
 * Get product information from a barcode using Open Food Facts API
 */
export async function getProductFromBarcode(barcode: string): Promise<ProductInfo | null> {
  try {
    // First check if we have this barcode in our Firebase database
    const barcodeQuery = query(
      collection(db, 'barcode_products'),
      where('barcode', '==', barcode)
    );
    
    const querySnapshot = await getDocs(barcodeQuery);
    let cachedProduct = null;
    
    if (!querySnapshot.empty) {
      cachedProduct = querySnapshot.docs[0].data();
    }

    // If found in cache, format and return it
    if (cachedProduct) {
      return {
        name: cachedProduct.name as string,
        brand: cachedProduct.brand as string || 'Unknown Brand',
        nutritionalInfo: {
          calories: Number(cachedProduct.calories) || 0,
          protein: Number(cachedProduct.protein) || 0,
          carbs: Number(cachedProduct.carbs) || 0,
          fat: Number(cachedProduct.fat) || 0,
          fiber: Number(cachedProduct.fiber) || 0,
          sodium: Number(cachedProduct.sodium) || 0,
          sugar: Number(cachedProduct.sugar) || 0,
        },
        ingredients: (cachedProduct.ingredients as string[]) || [],
        allergens: (cachedProduct.allergens as string[]) || []
      };
    }

    // Try Open Food Facts API
    try {
      const response = await fetch(`${OPEN_FOOD_FACTS_API_URL}/${barcode}.json`);
      
      if (!response.ok) {
        throw new Error(`Open Food Facts API returned ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json() as OpenFoodFactsResponse;
      
      if (!data || data.status !== 1 || !data.product) {
        throw new Error('Product not found in Open Food Facts');
      }
      
      // Extract nutritional data and ingredients
      const nutriments = data.product.nutriments || {};
      const ingredients = data.product.ingredients_text ? 
        data.product.ingredients_text.split(',').map((i: string) => i.trim()) : 
        [];
      
      // Extract allergens
      const allergens = data.product.allergens_tags ? 
        data.product.allergens_tags.map((a: string) => a.replace('en:', '')) : 
        [];
      
      const product: ProductInfo = {
        name: data.product.product_name || 'Unknown Product',
        brand: data.product.brands || 'Unknown Brand',
        nutritionalInfo: {
          calories: nutriments.energy_kcal_100g || nutriments.energy_kcal || 0,
          protein: nutriments.proteins_100g || 0,
          carbs: nutriments.carbohydrates_100g || 0,
          fat: nutriments.fat_100g || 0,
          fiber: nutriments.fiber_100g || 0,
          sodium: nutriments.sodium_100g || 0,
          sugar: nutriments.sugars_100g || 0
        },
        ingredients,
        allergens
      };
      
      // Cache the product in our database for future lookups
      try {
        const barcodeProductData = {
          barcode,
          name: product.name,
          brand: product.brand,
          calories: product.nutritionalInfo.calories,
          protein: product.nutritionalInfo.protein,
          carbs: product.nutritionalInfo.carbs,
          fat: product.nutritionalInfo.fat,
          fiber: product.nutritionalInfo.fiber,
          sodium: product.nutritionalInfo.sodium,
          sugar: product.nutritionalInfo.sugar,
          ingredients: product.ingredients,
          allergens: product.allergens,
          updated_at: new Date().toISOString()
        };
        
        await addDoc(collection(db, 'barcode_products'), barcodeProductData);
      } catch (insertError) {
        console.error('Error caching barcode product:', insertError);
      }
      
      return product;
    } catch (openFoodError) {
      console.error('Open Food Facts API error:', openFoodError);
      
      // If Open Food Facts fails, try our backup barcode API
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      if (BARCODE_API_KEY) {
        headers['Authorization'] = `Bearer ${BARCODE_API_KEY}`;
      }

      const response = await fetch(`${BARCODE_API_URL}/${barcode}`, {
        method: 'GET',
        headers
      });

      if (!response.ok) {
        throw new Error(`Barcode API returned ${response.status}: ${response.statusText}`);
      }

      const data = await response.json() as ProductApiResponse;

      if (!data || !data.product) {
        console.log(`Product not found for barcode: ${barcode}`);
        return null;
      }

      // Format the API response
      const product = {
        name: data.product.name || 'Unknown Product',
        brand: data.product.brand || 'Unknown Brand',
        nutritionalInfo: {
          calories: Number(data.product.nutrients?.calories) || 0,
          protein: Number(data.product.nutrients?.protein) || 0,
          carbs: Number(data.product.nutrients?.carbs) || 0,
          fat: Number(data.product.nutrients?.fat) || 0,
          fiber: Number(data.product.nutrients?.fiber) || 0,
          sodium: Number(data.product.nutrients?.sodium) || 0,
          sugar: Number(data.product.nutrients?.sugar) || 0,
        },
        ingredients: Array.isArray(data.product.ingredients) ? data.product.ingredients : [],
        allergens: Array.isArray(data.product.allergens) ? data.product.allergens : []
      };

      // Cache the product in our database for future lookups
      try {
        const barcodeProductData = {
          barcode,
          name: product.name,
          brand: product.brand,
          calories: product.nutritionalInfo.calories,
          protein: product.nutritionalInfo.protein,
          carbs: product.nutritionalInfo.carbs,
          fat: product.nutritionalInfo.fat,
          fiber: product.nutritionalInfo.fiber,
          sodium: product.nutritionalInfo.sodium,
          sugar: product.nutritionalInfo.sugar,
          ingredients: product.ingredients,
          allergens: product.allergens,
          updated_at: new Date().toISOString()
        };
        
        await addDoc(collection(db, 'barcode_products'), barcodeProductData);
      } catch (insertError) {
        console.error('Error caching barcode product:', insertError);
      }

      return product;
    }
  } catch (error) {
    console.error('Error getting product from barcode:', error);
    return null;
  }
}

/**
 * Recognizes a barcode and retrieves product information
 * @param barcode The barcode string to look up
 * @returns Promise resolving to barcode recognition result
 */
export async function recognizeBarcode(barcode: string): Promise<BarcodeRecognitionResult> {
  try {
    if (!barcode) {
      throw new Error('Barcode is empty');
    }

    // First check if we have this barcode in our Firebase database
    const barcodeQuery = query(
      collection(db, 'barcode_products'),
      where('barcode', '==', barcode)
    );
    
    const querySnapshot = await getDocs(barcodeQuery);
    let cachedProduct = null;
    
    if (!querySnapshot.empty) {
      cachedProduct = querySnapshot.docs[0].data();
      const docId = querySnapshot.docs[0].id;
      
      return {
        product: {
          id: docId,
          name: cachedProduct.name,
          calories: Number(cachedProduct.calories),
          protein: Number(cachedProduct.protein),
          carbs: Number(cachedProduct.carbs),
          fat: Number(cachedProduct.fat),
          portions: cachedProduct.portions || [{
            name: 'serving',
            grams: 100,
            quantity: 1
          }]
        },
        success: true
      };
    }

    // Try Open Food Facts API first
    try {
      const response = await fetch(`${OPEN_FOOD_FACTS_API_URL}/${barcode}.json`);
      
      if (!response.ok) {
        throw new Error(`Open Food Facts API returned ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json() as OpenFoodFactsResponse;
      
      if (!data || data.status !== 1 || !data.product) {
        throw new Error('Product not found in Open Food Facts');
      }
      
      // Extract nutritional data
      const nutriments = data.product.nutriments || {};
      
      // Create a food item from the API response
      const product: FoodItem = {
        id: `off-${barcode}`,
        name: data.product.product_name || 'Unknown Product',
        calories: nutriments.energy_kcal_100g || nutriments.energy_kcal || 0,
        protein: nutriments.proteins_100g || 0,
        carbs: nutriments.carbohydrates_100g || 0,
        fat: nutriments.fat_100g || 0,
        portions: [{
          name: 'serving',
          grams: 100,
          quantity: 1
        }],
        barcode,
        brand: data.product.brands || '',
        imageUrl: data.product.image_url || ''
      };
      
      // Cache the product in our database for future lookups
      try {
        const barcodeProductData = {
          barcode,
          name: product.name,
          calories: product.calories,
          protein: product.protein,
          carbs: product.carbs,
          fat: product.fat,
          portions: product.portions,
          brand: product.brand,
          image_url: product.imageUrl,
          updated_at: new Date().toISOString()
        };
        
        await addDoc(collection(db, 'barcode_products'), barcodeProductData);
        
        // Log this barcode lookup
        const auth = getAuth();
        const user = auth.currentUser;
        
        if (user) {
          await addDoc(collection(db, 'barcode_lookups'), {
            user_id: user.uid,
            barcode,
            product_found: true,
            timestamp: new Date().toISOString()
          });
        }
      } catch (insertError) {
        console.error('Error caching barcode product:', insertError);
      }

      return {
        product,
        success: true
      };
    } catch (openFoodError) {
      console.error('Open Food Facts API error:', openFoodError);
      
      // If Open Food Facts fails, try our backup barcode API
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      if (BARCODE_API_KEY) {
        headers['Authorization'] = `Bearer ${BARCODE_API_KEY}`;
      }

      const response = await fetch(`${BARCODE_API_URL}/${barcode}`, {
        method: 'GET',
        headers
      });

      if (!response.ok) {
        throw new Error(`Barcode API returned ${response.status}: ${response.statusText}`);
      }

      const data = await response.json() as ProductApiResponse;

      if (!data || !data.product) {
        return {
          product: null,
          success: false,
          error: 'Product not found'
        };
      }

      // Create a food item from the API response
      const product: FoodItem = {
        id: `barcode-${barcode}`,
        name: data.product.name || 'Unknown Product',
        calories: data.product.nutrients?.calories || 0,
        protein: data.product.nutrients?.protein || 0,
        carbs: data.product.nutrients?.carbs || 0,
        fat: data.product.nutrients?.fat || 0,
        portions: [{
          name: 'serving',
          grams: data.product.serving_size || 100,
          quantity: 1
        }]
      };

      // Cache the product in our database for future lookups
      try {
        const barcodeProductData = {
          barcode,
          name: product.name,
          calories: product.calories,
          protein: product.protein,
          carbs: product.carbs,
          fat: product.fat,
          portions: product.portions,
          updated_at: new Date().toISOString()
        };
        
        await addDoc(collection(db, 'barcode_products'), barcodeProductData);
        
        // Log this barcode lookup
        const auth = getAuth();
        const user = auth.currentUser;
        
        if (user) {
          await addDoc(collection(db, 'barcode_lookups'), {
            user_id: user.uid,
            barcode,
            product_found: true,
            timestamp: new Date().toISOString()
          });
        }
      } catch (insertError) {
        console.error('Error caching barcode product:', insertError);
      }

      return {
        product,
        success: true
      };
    }
  } catch (error) {
    console.error('Barcode recognition error:', error);
    
    // Log failed lookup
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      
      if (user) {
        await addDoc(collection(db, 'barcode_lookups'), {
          user_id: user.uid,
          barcode,
          product_found: false,
          timestamp: new Date().toISOString()
        });
      }
    } catch (logError) {
      console.error('Error logging barcode lookup:', logError);
    }
    
    return {
      product: null,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error during barcode recognition'
    };
  }
}