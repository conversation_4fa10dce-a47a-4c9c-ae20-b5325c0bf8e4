import AsyncStorage from '@react-native-async-storage/async-storage';
import { UnifiedFoodAnalysisResult } from './types';
import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as Crypto from 'expo-crypto';

// Cache TTL (time to live) in milliseconds
const CACHE_TTL = {
  RESULTS: 7 * 24 * 60 * 60 * 1000, // 7 days for analysis results
  MODELS: 30 * 24 * 60 * 60 * 1000, // 30 days for models
};

// Max cache size 
const MAX_CACHE_SIZE = 100 * 1024 * 1024; // 100MB

/**
 * Interface for cache entry metadata
 */
interface CacheEntry {
  key: string;
  metaKey: string;
  timestamp: number;
}

/**
 * Service for caching image analysis results and models
 */
export class AnalysisCacheService {
  private static instance: AnalysisCacheService;
  private modelCacheDir: string;
  private initialized: boolean = false;
  private cacheSize: number = 0;

  private constructor() {
    // Set up cache directory for models
    this.modelCacheDir = `${FileSystem.cacheDirectory}vision-models/`;
    this.init();
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): AnalysisCacheService {
    if (!AnalysisCacheService.instance) {
      AnalysisCacheService.instance = new AnalysisCacheService();
    }
    return AnalysisCacheService.instance;
  }

  /**
   * Initialize the cache service
   */
  private async init() {
    if (this.initialized) return;

    try {
      // Create model cache directory if it doesn't exist
      const dirInfo = await FileSystem.getInfoAsync(this.modelCacheDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.modelCacheDir, { intermediates: true });
      }

      // Calculate current cache size
      this.cacheSize = await this.calculateCacheSize();
      
      // Clean up old cache entries
      await this.cleanupExpiredCaches();
      
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize cache service:', error);
    }
  }

  /**
   * Calculate total cache size
   */
  private async calculateCacheSize(): Promise<number> {
    try {
      // Get size of local storage cache
      const keys = await AsyncStorage.getAllKeys();
      const analysisCacheKeys = keys.filter(key => key.startsWith('food_analysis_'));
      
      let storageSize = 0;
      for (const key of analysisCacheKeys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          storageSize += value.length * 2; // Rough estimate: 2 bytes per character
        }
      }
      
      // Get size of model cache
      let modelSize = 0;
      if (Platform.OS !== 'web') {
        const dirContents = await FileSystem.readDirectoryAsync(this.modelCacheDir);
        for (const file of dirContents) {
          const fileInfo = await FileSystem.getInfoAsync(`${this.modelCacheDir}${file}`);
          if (fileInfo.exists && !fileInfo.isDirectory) {
            modelSize += fileInfo.size;
          }
        }
      }
      
      return storageSize + modelSize;
    } catch (error) {
      console.error('Error calculating cache size:', error);
      return 0;
    }
  }

  /**
   * Clean up expired cache entries
   */
  private async cleanupExpiredCaches() {
    try {
      const now = Date.now();
      
      // Clean up results cache
      const keys = await AsyncStorage.getAllKeys();
      const analysisCacheKeys = keys.filter(key => key.startsWith('food_analysis_'));
      
      for (const key of analysisCacheKeys) {
        const metaKey = `${key}_meta`;
        const metaValue = await AsyncStorage.getItem(metaKey);
        
        if (metaValue) {
          const meta = JSON.parse(metaValue);
          if (now - meta.timestamp > CACHE_TTL.RESULTS) {
            await AsyncStorage.removeItem(key);
            await AsyncStorage.removeItem(metaKey);
          }
        }
      }
      
      // Clean up model cache
      if (Platform.OS !== 'web') {
        const dirContents = await FileSystem.readDirectoryAsync(this.modelCacheDir);
        for (const file of dirContents) {
          const filePath = `${this.modelCacheDir}${file}`;
          const fileInfo = await FileSystem.getInfoAsync(filePath);
          const metaFilePath = `${filePath}.meta`;
          
          try {
            const metaContent = await FileSystem.readAsStringAsync(metaFilePath);
            const meta = JSON.parse(metaContent);
            
            if (now - meta.timestamp > CACHE_TTL.MODELS) {
              await FileSystem.deleteAsync(filePath, { idempotent: true });
              await FileSystem.deleteAsync(metaFilePath, { idempotent: true });
            }
          } catch (e) {
            // If meta file doesn't exist or is corrupted, delete the model file
            await FileSystem.deleteAsync(filePath, { idempotent: true });
          }
        }
      }
      
      // Recalculate cache size
      this.cacheSize = await this.calculateCacheSize();
      
      // If cache is still too large, remove oldest entries
      if (this.cacheSize > MAX_CACHE_SIZE) {
        await this.pruneCache();
      }
    } catch (error) {
      console.error('Error cleaning up caches:', error);
    }
  }

  /**
   * Prune cache to stay under size limit by removing oldest entries
   */
  private async pruneCache() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const analysisCacheKeys = keys.filter(key => key.startsWith('food_analysis_'));
      
      // Get timestamps for all cache entries
      const cacheEntries: CacheEntry[] = [];
      for (const key of analysisCacheKeys) {
        const metaKey = `${key}_meta`;
        const metaValue = await AsyncStorage.getItem(metaKey);
        
        if (metaValue) {
          const meta = JSON.parse(metaValue);
          cacheEntries.push({
            key,
            metaKey,
            timestamp: meta.timestamp
          });
        }
      }
      
      // Sort by timestamp (oldest first)
      cacheEntries.sort((a, b) => a.timestamp - b.timestamp);
      
      // Remove oldest entries until under size limit
      let currentSize = this.cacheSize;
      for (const entry of cacheEntries) {
        if (currentSize <= MAX_CACHE_SIZE * 0.8) { // Target 80% of max to avoid frequent pruning
          break;
        }
        
        const value = await AsyncStorage.getItem(entry.key);
        if (value) {
          const entrySize = value.length * 2; // Rough estimate: 2 bytes per character
          await AsyncStorage.removeItem(entry.key);
          await AsyncStorage.removeItem(entry.metaKey);
          
          currentSize -= entrySize;
        }
      }
      
      // Update cache size
      this.cacheSize = await this.calculateCacheSize();
    } catch (error) {
      console.error('Error pruning cache:', error);
    }
  }

  /**
   * Generate a cache key for an image
   */
  private async generateCacheKey(imageData: string, options?: string): Promise<string> {
    try {
      // Use only first 1000 chars of image data for hashing to improve performance
      const dataToHash = imageData.substring(0, 1000) + (options || '');
      const hash = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        dataToHash
      );
      return `food_analysis_${hash}`;
    } catch (error) {
      // Fallback to simpler key if cryptography not available
      console.warn('Error generating crypto hash, using fallback:', error);
      let simpleHash = 0;
      for (let i = 0; i < 100 && i < imageData.length; i++) {
        simpleHash = ((simpleHash << 5) - simpleHash) + imageData.charCodeAt(i);
        simpleHash = simpleHash & simpleHash; // Convert to 32bit integer
      }
      return `food_analysis_${simpleHash}_${Date.now()}`;
    }
  }

  /**
   * Cache analysis results
   */
  public async cacheAnalysisResult(
    imageData: string, 
    result: UnifiedFoodAnalysisResult,
    optionsString?: string
  ): Promise<void> {
    try {
      await this.init();
      const cacheKey = await this.generateCacheKey(imageData, optionsString);
      const metaKey = `${cacheKey}_meta`;
      
      await AsyncStorage.setItem(cacheKey, JSON.stringify(result));
      await AsyncStorage.setItem(metaKey, JSON.stringify({
        timestamp: Date.now(),
        optionsString
      }));
      
      // Update cache size
      this.cacheSize = await this.calculateCacheSize();
    } catch (error) {
      console.error('Error caching analysis result:', error);
    }
  }

  /**
   * Get cached analysis results
   */
  public async getCachedAnalysisResult(
    imageData: string,
    optionsString?: string
  ): Promise<{ result: UnifiedFoodAnalysisResult; timestamp: number } | null> {
    try {
      await this.init();
      const cacheKey = await this.generateCacheKey(imageData, optionsString);
      const metaKey = `${cacheKey}_meta`;
      
      const cachedResult = await AsyncStorage.getItem(cacheKey);
      const cachedMeta = await AsyncStorage.getItem(metaKey);
      
      if (cachedResult && cachedMeta) {
        const result = JSON.parse(cachedResult);
        const meta = JSON.parse(cachedMeta);
        
        // Validate that options match, if provided
        if (optionsString && meta.optionsString !== optionsString) {
          return null;
        }
        
        // Check if result is still valid
        if (Date.now() - meta.timestamp <= CACHE_TTL.RESULTS) {
          return {
            result,
            timestamp: meta.timestamp
          };
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error retrieving cached analysis result:', error);
      return null;
    }
  }

  /**
   * Download and cache a model
   */
  public async cacheModel(modelUrl: string, modelName: string): Promise<string | null> {
    try {
      if (Platform.OS === 'web') {
        console.warn('Model caching not supported on web platform');
        return null;
      }
      
      await this.init();
      const modelPath = `${this.modelCacheDir}${modelName}`;
      const metaPath = `${modelPath}.meta`;
      
      // Check if model already exists
      const modelInfo = await FileSystem.getInfoAsync(modelPath);
      if (modelInfo.exists) {
        try {
          const metaContent = await FileSystem.readAsStringAsync(metaPath);
          const meta = JSON.parse(metaContent);
          
          // If model is still valid, return its path
          if (Date.now() - meta.timestamp <= CACHE_TTL.MODELS) {
            return modelPath;
          }
        } catch (e) {
          // Meta file doesn't exist or is corrupted, continue with download
        }
      }
      
      // Download the model
      const downloadResult = await FileSystem.downloadAsync(modelUrl, modelPath);
      
      if (downloadResult.status === 200) {
        // Save meta information
        await FileSystem.writeAsStringAsync(metaPath, JSON.stringify({
          timestamp: Date.now(),
          url: modelUrl,
          name: modelName
        }));
        
        // Update cache size
        this.cacheSize = await this.calculateCacheSize();
        
        return modelPath;
      }
      
      return null;
    } catch (error) {
      console.error('Error caching model:', error);
      return null;
    }
  }

  /**
   * Get cached model path
   */
  public async getCachedModelPath(modelName: string): Promise<string | null> {
    try {
      if (Platform.OS === 'web') {
        return null;
      }
      
      await this.init();
      const modelPath = `${this.modelCacheDir}${modelName}`;
      const metaPath = `${modelPath}.meta`;
      
      // Check if model exists
      const modelInfo = await FileSystem.getInfoAsync(modelPath);
      if (modelInfo.exists) {
        try {
          const metaContent = await FileSystem.readAsStringAsync(metaPath);
          const meta = JSON.parse(metaContent);
          
          // Check if model is still valid
          if (Date.now() - meta.timestamp <= CACHE_TTL.MODELS) {
            return modelPath;
          } else {
            // Model expired, delete it
            await FileSystem.deleteAsync(modelPath, { idempotent: true });
            await FileSystem.deleteAsync(metaPath, { idempotent: true });
          }
        } catch (e) {
          // Meta file doesn't exist or is corrupted, delete the model
          await FileSystem.deleteAsync(modelPath, { idempotent: true });
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error getting cached model path:', error);
      return null;
    }
  }

  /**
   * Clear all caches
   */
  public async clearAllCaches(): Promise<void> {
    try {
      await this.init();
      
      // Clear analysis results cache
      const keys = await AsyncStorage.getAllKeys();
      const analysisCacheKeys = keys.filter(key => key.startsWith('food_analysis_'));
      
      if (analysisCacheKeys.length > 0) {
        await AsyncStorage.multiRemove(analysisCacheKeys);
        
        // Also remove meta keys
        const metaKeys = analysisCacheKeys.map(key => `${key}_meta`);
        await AsyncStorage.multiRemove(metaKeys);
      }
      
      // Clear model cache
      if (Platform.OS !== 'web') {
        await FileSystem.deleteAsync(this.modelCacheDir, { idempotent: true });
        await FileSystem.makeDirectoryAsync(this.modelCacheDir, { intermediates: true });
      }
      
      // Reset cache size
      this.cacheSize = 0;
    } catch (error) {
      console.error('Error clearing caches:', error);
    }
  }

  /**
   * Clear the analysis cache for a specific image or all food analysis caches
   * @param imageData Optional base64 image data to clear specific cache entry
   * @returns Promise that resolves when the cache has been cleared
   */
  async clearAnalysisCache(imageData?: string): Promise<void> {
    try {
      if (!AsyncStorage) {
        console.warn('AsyncStorage not available for cache clearing');
        return;
      }

      if (imageData) {
        // Clear specific image cache
        const imageHash = await this.generateCacheKey(imageData);
        const keys = await AsyncStorage.getAllKeys();
        const cacheKeys = keys.filter(key => key.startsWith(`food_analysis_${imageHash}`));
        
        if (cacheKeys.length > 0) {
          await AsyncStorage.multiRemove(cacheKeys);
          console.log(`Cleared ${cacheKeys.length} cache entries for specific image`);
        } else {
          console.log('No cache entries found for the specified image');
        }
      } else {
        // Clear all food analysis caches
        const keys = await AsyncStorage.getAllKeys();
        const cacheKeys = keys.filter(key => key.startsWith('food_analysis_'));
        
        if (cacheKeys.length > 0) {
          await AsyncStorage.multiRemove(cacheKeys);
          console.log(`Cleared all ${cacheKeys.length} food analysis cache entries`);
        } else {
          console.log('No food analysis cache entries found');
        }
      }
    } catch (error) {
      console.error('Error clearing analysis cache:', error);
    }
  }
} 