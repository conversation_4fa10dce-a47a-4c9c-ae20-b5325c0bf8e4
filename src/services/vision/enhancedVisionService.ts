// import { analyzeWithOpenAI } from '@/services/openaiService';
import { AnalyzeImageResponse, UnifiedFoodAnalysisResult } from './types';
import { detectObjectsWithGoogleVision } from './googleVisionApi';
// Removing unused imports but keeping as comments for future reference
// import { advancedFoodDetection as advancedHuggingFaceDetection, HuggingFaceFoodItem } from './huggingfaceVisionApi';
import { segmentFoodItems, getSimplifiedFoodSegments, FoodSegment } from './segmentationService';
// Import config using relative path
// API keys no longer needed - using Firebase Functions
import { detectLabelsFromImage, uriToBase64 } from './visionApi';
import { isFoodCategory, detectFilipinoDishComponents } from './utils';
// import { enhancedFoodAnalysis as typedEnhancedFoodAnalysis } from './typedUtils';
import { detectGlobalFoodItems, generateDishName, calculateNutrition, generateHealthInsights, DetectedFoodItem, CuisineDetectionResult } from './globalFoodDetection';
import { detectReferenceObjects, estimateFoodSize, DetectedReferenceObject } from './referenceObjectDetection';
import { Image, Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
// import { AnalysisCacheService } from './cacheService';
// Import identifyFoodFromDetectedItems directly
import { identifyFoodFromDetectedItems } from '@/services/openai/foodAnalysis';

// Confidence threshold for vision APIs
const CONFIDENCE_THRESHOLD = 0.7;

// Threshold for considering multiple items
const MULTI_ITEM_THRESHOLD = 2; // If more than this number of items detected, use segmentation

interface GoogleVisionFoodItem {
  name: string;
  score: number;
  boundingBox?: {
    topLeft: { x: number, y: number },
    bottomRight: { x: number, y: number }
  };
}

// Generic food item interface to handle both Google Vision and Clarifai results
interface FoodItem {
  name: string;
  score: number;
  boundingBox?: any;
}

// Define the structure of the estimatedSize return type
interface EstimatedSizeType {
  width: number;
  height: number;
  depth: number;
  volume: number;
  referenceObject: {
    name: string;
    width: number;
    height: number;
  };
}

// Standardized bounding box interface
interface BoundingBox {
  topLeft: { x: number, y: number };
  bottomRight: { x: number, y: number };
}

async function generateFoodDescription(primaryFood: string, foodLabels: string[]) {
  console.log(`Generating mock food description for ${primaryFood}`);
  return {
    description: `This appears to be ${primaryFood}. It looks fresh and well-prepared.`,
    cuisineType: 'Mixed',
    preparationMethod: 'Unknown',
    healthInsights: ['Good source of nutrients', 'Consider portion control'],
  };
}

/**
 * Enhanced food analysis with multiple vision services and portion size estimation
 * @param imageUri URI of the image to analyze
 * @param bypassCache Optional flag to bypass cache and force fresh analysis
 * @returns Enhanced analysis results
 */
export async function enhancedFoodAnalysis(imageUri: string, bypassCache: boolean = false): Promise<AnalyzeImageResponse> {
  try {
    console.log(`Starting enhanced food analysis for image: ${imageUri}, bypassCache: ${bypassCache}`);
    
    // Variable to store OpenAI result for later use
    let openAIResult: any = null;
    
    // Variables to track what backend provided our results
    let visionSource = '';
    
    // Variables to store our detected items
    let foodLabels: string[] = [];
    let isMultiItem = false;
    let nutritionalData: any = null;
    let cuisineType = '';
    let preparationMethod = '';
    let foodDescription = '';
    let healthInsights: string[] = [];
    let items: any[] = [];
    let segments: FoodSegment[] = [];
    let referenceObjects: DetectedReferenceObject[] = [];
    let imageSize: { width: number; height: number } | null = null;
    
    // Variables to keep track of results and sources
    let topFoodItem: GoogleVisionFoodItem | null = null;
    let allFoodItems: FoodItem[] = [];
    let primaryVisionResult: any = null;
    let highConfidence = false;
    let globalDetectionResult: any = null;
    
    // Get image dimensions for size calculations
    imageSize = await getImageSize(imageUri);
    
    // Try to detect reference objects for portion size estimation
    try {
      const referenceResult = await detectReferenceObjects(imageUri);
      if (referenceResult.success && referenceResult.referenceObjects && referenceResult.referenceObjects.length > 0) {
        referenceObjects = referenceResult.referenceObjects;
        console.log(`Detected ${referenceObjects.length} reference objects for portion estimation`);
      } else {
        console.log('No reference objects detected, skipping portion size estimation');
      }
    } catch (error) {
      console.warn('Error detecting reference objects:', error);
      // Provide empty reference objects instead of failing
      referenceObjects = [];
    }
    
    // Use Google Vision API via Firebase Functions
    try {
      console.log('Using Google Vision API via Firebase Functions for object detection');
      const googleVisionResults = await detectObjectsWithGoogleVision(imageUri, bypassCache);
      
      // Save food labels for global detection
      foodLabels = googleVisionResults.labels && googleVisionResults.labels.length > 0 
        ? googleVisionResults.labels.map((label: { description: string }) => label.description)
        : [];
        
      // Filter for food-related labels
      foodLabels = foodLabels.filter(label => isFoodCategory(label));
      
      // Check if we have food items in the results
      if (googleVisionResults.foodItems && googleVisionResults.foodItems.length > 0) {
        console.log('Google Vision detected food items:', googleVisionResults.foodItems);
        
        // Add food item names to labels if not already included
        googleVisionResults.foodItems.forEach((item: GoogleVisionFoodItem) => {
          if (!foodLabels.includes(item.name)) {
            foodLabels.push(item.name);
          }
        });
        
        // Get the highest confidence food item
        topFoodItem = googleVisionResults.foodItems[0] as GoogleVisionFoodItem;
        allFoodItems = googleVisionResults.foodItems as FoodItem[];
        visionSource = 'Google Vision API via Firebase Functions';
        
        // Check if the confidence is high enough
        highConfidence = topFoodItem.score >= CONFIDENCE_THRESHOLD;
        
        primaryVisionResult = googleVisionResults;
        
        // Check if we have multiple food items
        isMultiItem = googleVisionResults.foodItems.length > MULTI_ITEM_THRESHOLD;
      } else {
        console.log('No food items detected by Google Vision API');
      }
    } catch (error) {
      console.error('Error using Google Vision API via Firebase Functions:', error);
    }
    
    // Remove Clarifai fallback and replace with global detection
    if (!primaryVisionResult && foodLabels.length === 0) {
      // If Google Vision didn't detect anything, use a simple fallback
      foodLabels = ['Food']; // Default label
      console.log('Using fallback detection with basic food label');
    }
    
    // Try with global food detection if we have labels
    if (foodLabels.length > 0) {
      try {
        console.log('Performing global food detection with labels:', foodLabels);
        globalDetectionResult = await detectGlobalFoodItems(foodLabels);
      } catch (error) {
        console.error('Error in global food detection:', error);
      }
    }
    
    // Segment the image if we identified multiple food items
    if (isMultiItem && highConfidence) {
      try {
        console.log('Attempting food segmentation for multiple items');
        const segmentationResult = await segmentFoodItems(imageUri);
        if (segmentationResult.success && segmentationResult.segments) {
          segments = segmentationResult.segments as FoodSegment[];
          console.log(`Found ${segments.length} food segments`);
        }
      } catch (error) {
        console.error('Error segmenting food items:', error);
      }
    }
    
    // Get nutritional information from the food database
    try {
      const primaryFoodName = topFoodItem ? topFoodItem.name : foodLabels.length > 0 ? foodLabels[0] : 'food';
      const secondaryFoods = foodLabels.filter(label => label !== primaryFoodName).slice(0, 3);
      
      console.log(`Getting nutrition for: ${primaryFoodName} with secondary items:`, secondaryFoods);
      
      // Check if OpenAI API key is available for food analysis
      // Always use Firebase Functions for OpenAI - no API key needed
      if (true) {
        console.log('Using OpenAI via Firebase Functions');
        
        try {
          // Use the directly imported function instead of dynamic import
          const imageBase64 = await uriToBase64(imageUri);
          
          // Create detected items list from food labels
          const detectedItems = foodLabels.map(label => ({ name: label }));
          
          // Call OpenAI via Firebase Functions
          console.log("Calling OpenAI via Firebase Functions for food analysis...");
          openAIResult = await identifyFoodFromDetectedItems(detectedItems, imageBase64);
          
          if (openAIResult.success) {
            console.log("✅ OpenAI food analysis via Firebase Functions successful!");
            nutritionalData = openAIResult.servingInfo || {};
            
            // Use AI-identified items if available
            if (openAIResult.ingredients && openAIResult.ingredients.length > 0) {
              items = openAIResult.ingredients.map((ingredient: { 
                name: string; 
                calories: number; 
                protein: number; 
                carbs: number; 
                fat: number; 
              }) => ({
                name: ingredient.name,
                calories: ingredient.calories || 0,
                protein: ingredient.protein || 0,
                carbs: ingredient.carbs || 0,
                fat: ingredient.fat || 0,
                confidence: 0.8
              }));
            }
            
            // Update top food item if we got a better name
            if (openAIResult.dishName && (openAIResult.confidence || 0) > 0.7) {
              topFoodItem = {
                name: openAIResult.dishName,
                score: openAIResult.confidence || 0.8,
                boundingBox: topFoodItem?.boundingBox
              };
            }
            
            // Update food description and metadata
            foodDescription = openAIResult.description || '';
            cuisineType = openAIResult.cuisineType || '';
            preparationMethod = openAIResult.preparationMethod || '';
            
            // Update health insights
            if (openAIResult.healthHighlights) {
              healthInsights = [...(openAIResult.healthHighlights.positives || [])];
              
              if (openAIResult.healthHighlights.considerations) {
                healthInsights = [...healthInsights, ...openAIResult.healthHighlights.considerations];
              }
            }
          } else {
            console.log("❌ OpenAI food analysis via Firebase Functions failed:", openAIResult.error);
            throw new Error(`OpenAI analysis via Firebase Functions failed: ${openAIResult.error}`);
          }
        } catch (openaiError: any) {
          console.error("Error calling OpenAI via Firebase Functions:", openaiError);
          throw new Error(`Food analysis via Firebase Functions error: ${openaiError.message || 'Unknown error'}`);
        }
      } // No else needed - always using Firebase Functions
    } catch (error) {
      console.error('Error getting nutritional data:', error);
      // Create a basic nutritional data object if we fail
      nutritionalData = {
        calories: 0,
        protein: 0,
        fat: 0,
        carbs: 0,
        fiber: 0,
        sugar: 0,
        servingSize: 'unknown',
        servingUnit: 'g'
      };
    }
    
    // Determine which food items to use based on available data
    let finalFoodItems = (globalDetectionResult && globalDetectionResult.items.length > 0) 
      ? globalDetectionResult.items
      : allFoodItems.map((item: FoodItem) => ({
          name: item.name,
          confidence: item.score,
          calories: 0,
          protein: 0,
          carbs: 0,
          fat: 0
        }));
    
    if (segments.length > 0) {
      // Use segmentation data to enrich the items
      items = segments.map((segment, index) => {
        // Try to match segment with a detected food item
        const matchingItem = finalFoodItems.find((item: FoodItem) => 
          segment.labeledAs === item.name || segment.labeledAs?.toLowerCase() === item.name.toLowerCase()
        );
        
        const defaultItem = index < finalFoodItems.length ? finalFoodItems[index] : null;
        const item = matchingItem || defaultItem;
        
        // If we have reference objects, estimate portion size
        let estimatedSize: EstimatedSizeType | null = null;
        if (referenceObjects.length > 0 && segment.boundingBox && imageSize) {
          try {
            estimatedSize = estimateFoodSize(
              convertBoundingBox(segment.boundingBox),
              referenceObjects,
              imageSize.width,
              imageSize.height
            );
          } catch (sizeError) {
            console.log('Error estimating food size:', sizeError);
            estimatedSize = null;
          }
        }
        
        return {
          name: segment.labeledAs || (item ? item.name : `Food item ${index + 1}`),
          calories: item ? item.calories : 0,
          protein: item ? item.protein : 0,
          carbs: item ? item.carbs : 0,
          fat: item ? item.fat : 0,
          fiber: item && 'fiber' in item ? Number(item.fiber) || undefined : undefined,
          boundingBox: segment.boundingBox,
          area: segment.area,
          maskUri: segment.maskUri,
          estimatedSize
        };
      });
    } else if (finalFoodItems.length > 0) {
      // If no segmentation, just use the items we have
      items = finalFoodItems.map((item: FoodItem, index: number) => {
        // If we have a bounding box from Vision API and reference objects, estimate portion size
        let estimatedSize: EstimatedSizeType | null = null;
        if (referenceObjects.length > 0 && item.boundingBox && imageSize) {
          try {
            estimatedSize = estimateFoodSize(
              convertBoundingBox(item.boundingBox),
              referenceObjects,
              imageSize.width,
              imageSize.height
            );
          } catch (sizeError) {
            console.log('Error estimating food size:', sizeError);
            estimatedSize = null;
          }
        }
        
        return {
          ...item,
          estimatedSize
        };
      });
    }
    
    // If still no items, create a placeholder
    if (items.length === 0) {
      const foodName = foodLabels.length > 0 ? foodLabels[0] : 'Unknown food';
      items = [{ name: foodName, calories: 0, protein: 0, carbs: 0, fat: 0 }];
    }
    
    // Get a detailed food description using a language model
    try {
      // Always use Firebase Functions
      if (true) {
        // Only run this if we didn't already get a description from OpenAI earlier
        if (!foodDescription) {
          console.log('Getting detailed food description');
          const detailedDescription = await generateFoodDescription(
            topFoodItem ? topFoodItem.name : (foodLabels.length > 0 ? foodLabels.join(', ') : 'food'),
            foodLabels
          );
          
          foodDescription = detailedDescription.description || '';
          cuisineType = detailedDescription.cuisineType || '';
          preparationMethod = detailedDescription.preparationMethod || '';
          healthInsights = detailedDescription.healthInsights || [];
        }
      }
    } catch (error) {
      console.error('Error generating food description:', error);
      foodDescription = `This appears to be ${topFoodItem ? topFoodItem.name : 'food'}.`;
    }
    
    // Calculate total nutritional values from available data
    const totalCalories = items.reduce((sum, item) => sum + (item.calories || 0), 0);
    const totalProtein = items.reduce((sum, item) => sum + (item.protein || 0), 0);
    const totalCarbs = items.reduce((sum, item) => sum + (item.carbs || 0), 0);
    const totalFat = items.reduce((sum, item) => sum + (item.fat || 0), 0);
    
    // Use global values as a fallback if item-specific values seem wrong or missing
    const useGlobalNutrition = totalCalories === 0 || isNaN(totalCalories);
    
    // Construct the full response
    const result = {
      success: true,
      textualAnalysis: {
        description: foodDescription,
        cuisineType,
        preparationMethod,
        healthInsights,
        confidence: topFoodItem ? topFoodItem.score : 0.5
      },
      nutritionalSummary: {
        totalCalories: useGlobalNutrition ? (nutritionalData as any).calories || 0 : totalCalories,
        macronutrients: {
          protein: useGlobalNutrition ? (nutritionalData as any).protein || 0 : totalProtein,
          carbs: useGlobalNutrition ? (nutritionalData as any).carbs || 0 : totalCarbs,
          fat: useGlobalNutrition ? (nutritionalData as any).fat || 0 : totalFat,
          fiber: useGlobalNutrition ? (nutritionalData as any).fiber || 0 : 0
        },
        mealType: determineMealType(foodLabels)
      },
      detailedFoodInfo: items.map(item => ({
        specificDish: item.name,
        confidenceScore: item.confidence || 0.7,
        nutrition: {
          calories: item.calories || 0,
          protein: item.protein || 0,
          carbs: item.carbs || 0,
          fat: item.fat || 0
        },
        cookingMethod: detectCookingMethod(item.name, foodLabels),
        isFreshOrProcessed: determineIfFreshOrProcessed(item.name),
        ingredients: detectIngredients(item.name, foodLabels),
        boundingBox: item.boundingBox,
        estimatedSize: item.estimatedSize
      })),
      imageSource: imageUri,
      imageProperties: {
        hasMultipleItems: isMultiItem,
        segmentCount: segments.length,
        width: imageSize?.width,
        height: imageSize?.height
      },
      ingredients: openAIResult?.ingredients || [],
      data: {
        // Add this explicitly to store the dish name from OpenAI
        name: topFoodItem ? topFoodItem.name : foodLabels.length > 0 ? foodLabels[0] : 'Unknown food',
        calories: useGlobalNutrition ? (nutritionalData as any).calories || 0 : totalCalories,
        protein: useGlobalNutrition ? (nutritionalData as any).protein || 0 : totalProtein,
        carbs: useGlobalNutrition ? (nutritionalData as any).carbs || 0 : totalCarbs, 
        fat: useGlobalNutrition ? (nutritionalData as any).fat || 0 : totalFat,
        confidence: topFoodItem ? topFoodItem.score : 0.5,
        description: foodDescription,
        cuisineType,
        preparationMethod,
        items: items,
        healthHighlights: {
          positives: healthInsights.filter(insight => !insight.includes('concern') && !insight.includes('high in') && !insight.includes('limit')),
          considerations: healthInsights.filter(insight => insight.includes('concern') || insight.includes('high in') || insight.includes('limit'))
        },
        servingInfo: nutritionalData
      },
      segmentation: segments.length > 0 ? { segments } : undefined,
      referenceObjects: referenceObjects.length > 0 ? referenceObjects : undefined,
      metaData: {
        visionSource,
        analysisTimestamp: new Date().toISOString(),
        confidence: topFoodItem ? topFoodItem.score : 0.5,
        rawLabels: foodLabels
      }
    } as unknown as AnalyzeImageResponse;
    
    console.log('Enhanced food analysis complete:', 
      (result as any).detailedFoodInfo?.length > 0 ? 
      `${(result as any).detailedFoodInfo.length} items detected` : 
      'No food items detected');
    
    return result;
  } catch (error) {
    console.error('Error in enhanced food analysis:', error);
    // Return a basic result with error information
    return {
      success: false,
      textualAnalysis: {
        description: 'An error occurred while analyzing this image.',
        cuisineType: '',
        preparationMethod: '',
        healthInsights: [],
        confidence: 0
      },
      nutritionalSummary: {
        totalCalories: 0,
        macronutrients: {
          protein: 0,
          carbs: 0,
          fat: 0,
          fiber: 0
        },
        mealType: 'Unknown'
      },
      detailedFoodInfo: [],
      imageSource: imageUri,
      imageProperties: {
        hasMultipleItems: false,
        segmentCount: 0
      },
      metaData: {
        visionSource: 'Error',
        analysisTimestamp: new Date().toISOString(),
        confidence: 0,
        rawLabels: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    } as unknown as AnalyzeImageResponse;
  }
}

/**
 * Get bounding boxes for food items in an image
 * @param imageUri URI of the image to analyze
 * @returns Array of food items with bounding boxes
 */
export async function getFoodBoundingBoxes(imageUri: string): Promise<{
  success: boolean;
  foodItems?: GoogleVisionFoodItem[];
  segments?: FoodSegment[];
  error?: string;
}> {
  try {
    let foodItems: GoogleVisionFoodItem[] = [];
    let segments: FoodSegment[] = [];
    
    // Use Google Vision via Firebase Functions
    if (true) {
      try {
        const googleVisionResults = await detectObjectsWithGoogleVision(imageUri);
        
        // Get food items with bounding boxes
        foodItems = googleVisionResults.foodItems
          .filter((item: GoogleVisionFoodItem) => item.boundingBox != null) as GoogleVisionFoodItem[];
      } catch (error) {
        console.warn('Google Vision bounding box detection failed:', error);
      }
    }
    
    // Try segmentation to get more accurate boundaries
    try {
      const segmentationResult = await segmentFoodItems(imageUri);
      
      if (segmentationResult.success && segmentationResult.segments) {
        segments = segmentationResult.segments as unknown as FoodSegment[];
      }
    } catch (error) {
      console.warn('Segmentation failed:', error);
    }
    
    return {
      success: true,
      foodItems,
      segments
    };
  } catch (error) {
    console.error('Error getting food bounding boxes:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error getting bounding boxes'
    };
  }
}

/**
 * Creates an empty analysis result
 */
function createEmptyResult(message: string): UnifiedFoodAnalysisResult {
  return {
    foodItems: [],
    imageProperties: {
      dominantColors: ['#CCCCCC'],
      brightness: 0.5,
      contrast: 0.5,
      sharpness: 0.5,
      hasPlate: false
    },
    detailedFoodInfo: [],
    volumeInfo: [],
    nutritionalSummary: {
      totalCalories: 0,
      macronutrients: {
        protein: 0,
        carbs: 0,
        fat: 0,
        fiber: 0
      },
      mealType: 'unknown',
      isBalancedMeal: false,
      nutritionalQualityScore: 0
    },
    textualAnalysis: {
      description: message,
      preparationMethod: '',
      dietaryCategories: [],
      cuisineType: '',
      healthInsights: []
    },
    meta: {
      analysisTimestamp: Date.now(),
      modelVersion: 'enhanced-food-analysis-1.0',
      confidenceScore: 0,
      processingTimeMs: 0
    }
  };
}

// Add these utility functions 
function detectCookingMethod(foodName: string, labels: string[]): string {
  const cookingMethods = ['fried', 'baked', 'grilled', 'roasted', 'steamed', 'boiled', 'raw', 'stir-fried'];
  
  // Check if any cooking method is in the food name
  for (const method of cookingMethods) {
    if (foodName.toLowerCase().includes(method)) {
      return method;
    }
  }
  
  // Check if any cooking method is in the labels
  for (const method of cookingMethods) {
    if (labels.some(label => label.toLowerCase().includes(method))) {
      return method;
    }
  }
  
  return 'unknown';
}

function determineMealType(labels: string[]): string {
  const mealTypes = {
    breakfast: ['breakfast', 'eggs', 'cereal', 'oatmeal', 'pancake', 'waffle', 'bacon'],
    lunch: ['sandwich', 'salad', 'soup', 'burger', 'wrap'],
    dinner: ['steak', 'dinner', 'pasta', 'curry', 'roast', 'casserole'],
    snack: ['snack', 'chips', 'nuts', 'fruit', 'cookie', 'chocolate']
  };
  
  for (const [type, keywords] of Object.entries(mealTypes)) {
    if (labels.some(label => keywords.some(keyword => label.toLowerCase().includes(keyword)))) {
      return type;
    }
  }
  
  return 'unknown';
}

function determineIfFreshOrProcessed(foodName: string): string {
  const processedKeywords = ['canned', 'frozen', 'packaged', 'processed', 'instant', 'preserved'];
  
  for (const keyword of processedKeywords) {
    if (foodName.toLowerCase().includes(keyword)) {
      return 'processed';
    }
  }
  
  return 'fresh';
}

function detectIngredients(foodName: string, labels: string[]): string[] {
  // Simple implementation - return labels filtered for likely ingredients
  const nonIngredientWords = ['food', 'meal', 'dish', 'plate', 'cuisine', 'breakfast', 'lunch', 'dinner'];
  
  return labels
    .filter(label => !nonIngredientWords.some(word => label.toLowerCase() === word))
    .slice(0, 5); // Return up to 5 potential ingredients
}

/**
 * Get the dimensions of an image from its URI
 * @param uri Image URI to get dimensions for
 * @returns Object containing width and height, or null if failed
 */
async function getImageSize(uri: string): Promise<{ width: number; height: number } | null> {
  return new Promise((resolve) => {
    Image.getSize(
      uri,
      (width, height) => {
        resolve({ width, height });
      },
      () => {
        resolve(null);
      }
    );
  });
}

/**
 * Convert various bounding box formats to a standardized format
 * @param box The input bounding box in any format
 * @returns Standardized bounding box
 */
function convertBoundingBox(box: any): BoundingBox {
  if (!box) {
    // Return default values if no box is provided
    return {
      topLeft: { x: 0, y: 0 },
      bottomRight: { x: 1, y: 1 }
    };
  }
  
  try {
    // Check if the box already has our expected format
    if (box.topLeft && box.bottomRight) {
      return {
        topLeft: {
          x: typeof box.topLeft.x === 'number' ? box.topLeft.x : 0,
          y: typeof box.topLeft.y === 'number' ? box.topLeft.y : 0
        },
        bottomRight: {
          x: typeof box.bottomRight.x === 'number' ? box.bottomRight.x : 1,
          y: typeof box.bottomRight.y === 'number' ? box.bottomRight.y : 1
        }
      };
    }
    
    // Handle other common formats
    // Format with x, y, width, height
    if ('x' in box && 'y' in box && 'width' in box && 'height' in box) {
      return {
        topLeft: { x: box.x, y: box.y },
        bottomRight: { x: box.x + box.width, y: box.y + box.height }
      };
    }
    
    // Format with left, top, right, bottom
    if ('left' in box && 'top' in box && 'right' in box && 'bottom' in box) {
      return {
        topLeft: { x: box.left, y: box.top },
        bottomRight: { x: box.right, y: box.bottom }
      };
    }
    
    // Format with normalized vertices (Google Vision format)
    if (box.normalizedVertices && Array.isArray(box.normalizedVertices) && box.normalizedVertices.length >= 4) {
      return {
        topLeft: { 
          x: box.normalizedVertices[0].x || 0, 
          y: box.normalizedVertices[0].y || 0 
        },
        bottomRight: { 
          x: box.normalizedVertices[2].x || 1, 
          y: box.normalizedVertices[2].y || 1 
        }
      };
    }
    
    // Last resort, try to extract values from the object if possible
    if (typeof box === 'object') {
      const topLeftX = box.topLeftX || box.top_left_x || box.x1 || box.x || 0;
      const topLeftY = box.topLeftY || box.top_left_y || box.y1 || box.y || 0;
      const bottomRightX = box.bottomRightX || box.bottom_right_x || box.x2 || box.x + (box.width || 1) || 1;
      const bottomRightY = box.bottomRightY || box.bottom_right_y || box.y2 || box.y + (box.height || 1) || 1;
      
      return {
        topLeft: { x: topLeftX, y: topLeftY },
        bottomRight: { x: bottomRightX, y: bottomRightY }
      };
    }
  } catch (error) {
    console.error('Error converting bounding box format:', error);
  }
  
  // Default return if all else fails
  return {
    topLeft: { x: 0, y: 0 },
    bottomRight: { x: 1, y: 1 }
  };
}

// All mock functions removed - using imported functions from segmentationService.ts 