import { AnalyzeImageResponse } from './types';
// API keys are now handled server-side via Firebase Functions
// import { GOOGLE_VISION_API_KEY, OPENAI_API_KEY, HUGGINGFACE_API_KEY } from '@/utils/config';
import { detectLabelsFromImage, uriToBase64, detectObjectsWithGoogleVision } from './visionApi';
import { isFoodRelated, formatFoodItemName } from './utils';
import { getNutritionForDish , getNutritionData } from '@/services/nutritionService';
import { generateFoodDescriptionFromItems } from '@/services/openaiService';
import { apiClient } from '@/services/apiClient';
import { FoodItem, NutritionData } from '@/types/food';
import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import { webUriToBase64 } from '@/utils/imageProcessing';
import { identifyFoodFromDetectedItems } from '@/services/openai/foodAnalysis';
import { collection, addDoc, query, where, getDocs, Firestore } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, FirebaseStorage } from 'firebase/storage';
import { getAuth } from 'firebase/auth';
// Import Firebase services from lib
import * as firebaseServices from '@/lib/firebase';

// Type the Firebase services explicitly
const typedDb = firebaseServices.db as Firestore;
const typedStorage = firebaseServices.storage as FirebaseStorage;

// API configuration is now handled server-side via Firebase Functions
// const API_KEY = process.env.EXPO_PUBLIC_VISION_API_KEY;
// const API_ENDPOINT = process.env.EXPO_PUBLIC_VISION_API_ENDPOINT || 'https://api.foodrecognition.com/v1/recognize';

interface FoodRecognitionResult {
  foods: FoodItem[];
  success: boolean;
  error?: string;
}

// Define interface for the food recognition API response
interface FoodRecognitionApiResponse {
  foods: {
    name: string;
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
    confidence?: number;
  }[];
}

// Update the interface to include confidence and source properties
interface AnalyzeImageResponseData {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  description?: string;
  items: {
    name: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    ingredients?: string[];
    portion?: string;
    imageUrl?: string;
    maskUri?: string;
  }[];
  imageUri?: string;
  segmentation?: {
    maskUri: string;
    foodMasks: {
      name: string;
      maskUri: string;
      color: string;
    }[];
  };
  confidence?: number;
  source?: string;
}

/**
 * Recognizes food in an image and returns nutrition data
 * @param imageUri URI of the image to analyze
 * @returns Promise resolving to food recognition result
 */
export async function recognizeFood(imageUri: string): Promise<FoodRecognitionResult> {
  try {
    // Use Firebase Functions for food recognition
    const { getFunctions, httpsCallable } = await import('firebase/functions');
    const functions = getFunctions();
    
    // Upload image to Firebase storage for processing
    const fileName = `food_recognition_${Date.now()}.jpg`;
    
    // Fetch image data from URI
    const imageResponse = await fetch(imageUri);
    const blob = await imageResponse.blob();
    
    // Upload to Firebase Storage
    const storageRef = ref(typedStorage, `food-images/${fileName}`);
    await uploadBytes(storageRef, blob);
    
    // Get download URL
    const publicUrl = await getDownloadURL(storageRef);

    // Call Firebase Function for food recognition
    const recognizeFoodFunction = httpsCallable<{ imageUrl: string }, FoodRecognitionApiResponse>(
      functions,
      'recognizeFood'
    );

    const result = await recognizeFoodFunction({ imageUrl: publicUrl });
    const data = result.data;

    // Process the results
    const recognizedFoods: FoodItem[] = [];

    // Map API response to our food item format
    for (const item of data.foods) {
      const nutritionData = await getNutritionData(item.name);
      
      recognizedFoods.push({
        id: `food-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        name: item.name,
        calories: nutritionData?.calories || item.calories || 0,
        protein: nutritionData?.protein || item.protein || 0,
        carbs: nutritionData?.carbs || item.carbs || 0,
        fat: nutritionData?.fat || item.fat || 0,
        confidence: item.confidence || 0.9,
        portions: nutritionData?.portions || [{
          name: 'serving',
          grams: 100,
          quantity: 1
        }]
      });
    }

    // Save the recognition results to Firebase
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      
      if (user) {
        await addDoc(collection(typedDb, 'food_recognition_logs'), {
          user_id: user.uid,
          image_url: publicUrl,
          recognized_foods: recognizedFoods,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Error logging food recognition:', error);
    }

    return {
      foods: recognizedFoods,
      success: true
    };
  } catch (error) {
    console.error('Food recognition error:', error);
    return {
      foods: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error during food recognition'
    };
  }
}