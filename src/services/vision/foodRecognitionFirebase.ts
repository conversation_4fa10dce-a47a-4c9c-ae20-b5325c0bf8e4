// This file was cleaned up to remove unused imports
import { FoodItem } from '@/types/food';
import { getNutritionData } from '../nutritionService';
import { db, storage } from '@/lib/firebase';
import { collection, addDoc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { getAuth } from 'firebase/auth';

const API_KEY = process.env.EXPO_PUBLIC_VISION_API_KEY;
const API_ENDPOINT = process.env.EXPO_PUBLIC_VISION_API_ENDPOINT || 'https://api.foodrecognition.com/v1/recognize';

interface FoodRecognitionResult {
  foods: FoodItem[];
  success: boolean;
  error?: string;
}

// Define interface for the food recognition API response
interface FoodRecognitionApiResponse {
  foods: {
    name: string;
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
    confidence?: number;
  }[];
}

// Update the interface to include confidence and source properties
interface AnalyzeImageResponseData {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  description?: string;
  items: {
    name: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    ingredients?: string[];
    portion?: string;
    imageUrl?: string;
    maskUri?: string;
  }[];
  imageUri?: string;
  segmentation?: {
    maskUri: string;
    foodMasks: {
      name: string;
      maskUri: string;
      color: string;
    }[];
  };
  confidence?: number;
  source?: string;
}

/**
 * Recognizes food in an image and returns nutrition data
 * @param imageUri URI of the image to analyze
 * @returns Promise resolving to food recognition result
 */
export async function recognizeFood(imageUri: string): Promise<FoodRecognitionResult> {
  try {
    if (!API_KEY || !API_ENDPOINT) {
      throw new Error('Food recognition API credentials not configured');
    }

    // Upload image to Firebase storage for processing
    const fileName = `food_recognition_${Date.now()}.jpg`;
    
    // Fetch image data from URI
    const imageResponse = await fetch(imageUri);
    const blob = await imageResponse.blob();
    
    // Upload to Firebase Storage
    const storageRef = ref(storage, `food-images/${fileName}`);
    await uploadBytes(storageRef, blob);
    
    // Get download URL
    const publicUrl = await getDownloadURL(storageRef);

    // Call the food recognition API with the image URL
    const response = await fetch(API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        image_url: publicUrl
      })
    });

    if (!response.ok) {
      throw new Error(`Food recognition API returned ${response.status}: ${response.statusText}`);
    }

    const data = await response.json() as FoodRecognitionApiResponse;

    // Process the results
    const recognizedFoods: FoodItem[] = [];

    // Map API response to our food item format
    for (const item of data.foods) {
      const nutritionData = await getNutritionData(item.name);
      
      recognizedFoods.push({
        id: `food-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        name: item.name,
        calories: nutritionData?.calories || item.calories || 0,
        protein: nutritionData?.protein || item.protein || 0,
        carbs: nutritionData?.carbs || item.carbs || 0,
        fat: nutritionData?.fat || item.fat || 0,
        confidence: item.confidence || 0.9,
        portions: nutritionData?.portions || [{
          name: 'serving',
          grams: 100,
          quantity: 1
        }]
      });
    }

    // Save the recognition results to Firebase
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      
      if (user) {
        await addDoc(collection(db, 'food_recognition_logs'), {
          user_id: user.uid,
          image_url: publicUrl,
          recognized_foods: recognizedFoods,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Error logging food recognition:', error);
    }

    return {
      foods: recognizedFoods,
      success: true
    };
  } catch (error) {
    console.error('Food recognition error:', error);
    return {
      foods: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error during food recognition'
    };
  }
}