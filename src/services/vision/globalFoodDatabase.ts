/**
 * Global Food Database with nutritional information for foods across multiple cuisines
 */

export interface FoodItem {
  name: string;
  alternateNames?: string[];
  cuisine: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber?: number;
  sugar?: number;
  sodium?: number;
  keywords: string[];
  ingredients?: string[];
  cookingMethod?: string;
  isFreshOrProcessed?: 'fresh' | 'processed' | 'mixed' | 'unknown';
  category: 'protein' | 'carb' | 'vegetable' | 'fruit' | 'dairy' | 'fat' | 'dessert' | 'beverage' | 'mixed';
  mealType?: 'breakfast' | 'lunch' | 'dinner' | 'snack' | 'dessert' | 'any';
  isVegetarian?: boolean;
  isVegan?: boolean;
  isGlutenFree?: boolean;
}

// Filipino cuisine
export const filipinoFoods: FoodItem[] = [
  { 
    name: 'Sunny Side Up Egg',
    cuisine: 'Filipino',
    calories: 90,
    protein: 6,
    carbs: 0.5,
    fat: 7,
    keywords: ['egg', 'fried egg', 'sunny side up', 'itlog'],
    ingredients: ['Egg', 'Cooking Oil', 'Salt'],
    cookingMethod: 'fried',
    isFreshOrProcessed: 'fresh',
    category: 'protein',
    mealType: 'breakfast',
    isVegetarian: true,
    isGlutenFree: true
  },
  { 
    name: 'Garlic Fried Rice (Sinangag)',
    cuisine: 'Filipino',
    calories: 200,
    protein: 4,
    carbs: 45,
    fat: 4,
    keywords: ['rice', 'garlic rice', 'sinangag', 'fried rice'],
    ingredients: ['Rice', 'Garlic', 'Cooking Oil', 'Salt'],
    cookingMethod: 'fried',
    isFreshOrProcessed: 'processed',
    category: 'carb',
    mealType: 'breakfast',
    isVegetarian: true,
    isVegan: true,
    isGlutenFree: true
  },
  { 
    name: 'Fried Spam',
    cuisine: 'Filipino',
    calories: 180,
    protein: 7,
    carbs: 1,
    fat: 16,
    keywords: ['spam', 'fried spam', 'luncheon meat'],
    ingredients: ['SPAM Luncheon Meat', 'Cooking Oil'],
    cookingMethod: 'fried',
    isFreshOrProcessed: 'processed',
    category: 'protein',
    mealType: 'breakfast',
    isGlutenFree: true
  },
  { 
    name: 'Longganisa (Filipino Sweet Sausage)',
    cuisine: 'Filipino',
    calories: 100,
    protein: 5,
    carbs: 3,
    fat: 7,
    keywords: ['sausage', 'longganisa', 'longanisa', 'filipino sausage'],
    ingredients: ['Ground Pork', 'Garlic', 'Sugar', 'Vinegar', 'Soy Sauce', 'Black Pepper'],
    cookingMethod: 'fried',
    isFreshOrProcessed: 'processed',
    category: 'protein',
    mealType: 'breakfast',
    isGlutenFree: true
  },
  { 
    name: 'Filipino Red Hotdog',
    cuisine: 'Filipino',
    calories: 50,
    protein: 2,
    carbs: 2,
    fat: 4,
    keywords: ['hotdog', 'red hotdog', 'filipino hotdog'],
    ingredients: ['Red Hotdog', 'Cooking Oil'],
    cookingMethod: 'fried',
    isFreshOrProcessed: 'processed',
    category: 'protein',
    mealType: 'breakfast'
  },
  { 
    name: 'Corned Beef with Onions',
    cuisine: 'Filipino',
    calories: 100,
    protein: 7,
    carbs: 1,
    fat: 7,
    keywords: ['beef', 'corned beef', 'canned beef'],
    ingredients: ['Canned Corned Beef', 'Onions', 'Garlic', 'Cooking Oil'],
    cookingMethod: 'sautéed',
    isFreshOrProcessed: 'processed',
    category: 'protein',
    mealType: 'breakfast',
    isGlutenFree: true
  },
  { 
    name: 'Tocino (Sweet Cured Pork)',
    cuisine: 'Filipino',
    calories: 150,
    protein: 10,
    carbs: 5,
    fat: 10,
    keywords: ['tocino', 'sweet pork', 'cured pork'],
    ingredients: ['Pork', 'Sugar', 'Salt', 'Anisette Wine', 'Annatto', 'Garlic'],
    cookingMethod: 'fried',
    isFreshOrProcessed: 'processed',
    category: 'protein',
    mealType: 'breakfast',
    isGlutenFree: true
  },
  { 
    name: 'Beef Tapa (Cured Beef)',
    cuisine: 'Filipino',
    calories: 150,
    protein: 15,
    carbs: 2,
    fat: 9,
    keywords: ['tapa', 'beef tapa', 'dried beef'],
    ingredients: ['Beef', 'Soy Sauce', 'Garlic', 'Black Pepper', 'Sugar'],
    cookingMethod: 'fried',
    isFreshOrProcessed: 'processed',
    category: 'protein',
    mealType: 'breakfast',
    isGlutenFree: true
  },
  { 
    name: 'Tomato Slices',
    cuisine: 'Universal',
    calories: 5,
    protein: 0.2,
    carbs: 1,
    fat: 0,
    fiber: 0.5,
    keywords: ['tomato', 'tomatoes', 'sliced tomato'],
    ingredients: ['Tomato'],
    cookingMethod: 'raw',
    isFreshOrProcessed: 'fresh',
    category: 'vegetable',
    mealType: 'any',
    isVegetarian: true,
    isVegan: true,
    isGlutenFree: true
  },
  { 
    name: 'Pancit (Filipino Noodles)',
    cuisine: 'Filipino',
    calories: 250,
    protein: 8,
    carbs: 40,
    fat: 6,
    keywords: ['pancit', 'noodles', 'filipino noodles', 'pancit bihon', 'pancit canton'],
    ingredients: ['Rice Noodles', 'Vegetables', 'Meat', 'Soy Sauce', 'Garlic', 'Onion'],
    cookingMethod: 'stir-fried',
    isFreshOrProcessed: 'mixed',
    category: 'mixed',
    isGlutenFree: true
  },
  { 
    name: 'Adobo (Filipino Stew)',
    cuisine: 'Filipino',
    calories: 320,
    protein: 20,
    carbs: 5,
    fat: 25,
    keywords: ['adobo', 'filipino stew', 'chicken adobo', 'pork adobo'],
    ingredients: ['Chicken/Pork', 'Soy Sauce', 'Vinegar', 'Garlic', 'Bay Leaves', 'Black Pepper'],
    cookingMethod: 'stewed',
    isFreshOrProcessed: 'mixed',
    category: 'protein',
    isGlutenFree: true
  }
];

// Mexican cuisine
export const mexicanFoods: FoodItem[] = [
  {
    name: 'Taco (Beef)',
    cuisine: 'Mexican',
    calories: 170,
    protein: 10,
    carbs: 12,
    fat: 9,
    fiber: 1.5,
    keywords: ['taco', 'beef taco', 'mexican taco', 'soft taco'],
    ingredients: ['Corn Tortilla', 'Ground Beef', 'Lettuce', 'Tomato', 'Cheese', 'Salsa'],
    cookingMethod: 'fried',
    isFreshOrProcessed: 'mixed',
    category: 'mixed',
    isGlutenFree: true
  },
  {
    name: 'Guacamole',
    cuisine: 'Mexican',
    calories: 120,
    protein: 1.5,
    carbs: 6,
    fat: 10,
    fiber: 5,
    keywords: ['guacamole', 'avocado dip', 'avocado', 'guac'],
    ingredients: ['Avocado', 'Tomato', 'Onion', 'Lime Juice', 'Cilantro', 'Salt'],
    cookingMethod: 'fresh',
    isFreshOrProcessed: 'fresh',
    category: 'vegetable',
    mealType: 'any',
    isVegetarian: true,
    isVegan: true,
    isGlutenFree: true
  },
  {
    name: 'Burrito',
    cuisine: 'Mexican',
    calories: 380,
    protein: 15,
    carbs: 50,
    fat: 12,
    fiber: 6,
    keywords: ['burrito', 'mexican burrito', 'wrapped burrito'],
    ingredients: ['Flour Tortilla', 'Beans', 'Rice', 'Meat', 'Cheese', 'Vegetables'],
    cookingMethod: 'mixed',
    isFreshOrProcessed: 'mixed',
    category: 'mixed'
  },
  {
    name: 'Enchiladas',
    cuisine: 'Mexican',
    calories: 320,
    protein: 14,
    carbs: 36,
    fat: 14,
    fiber: 4,
    keywords: ['enchilada', 'mexican enchilada', 'cheese enchilada', 'beef enchilada'],
    ingredients: ['Corn Tortilla', 'Meat/Cheese', 'Enchilada Sauce', 'Onion', 'Cheese'],
    cookingMethod: 'baked',
    isFreshOrProcessed: 'mixed',
    category: 'mixed',
    isGlutenFree: true
  },
  {
    name: 'Quesadilla',
    cuisine: 'Mexican',
    calories: 410,
    protein: 15,
    carbs: 30,
    fat: 26,
    keywords: ['quesadilla', 'cheese quesadilla', 'mexican quesadilla'],
    ingredients: ['Flour Tortilla', 'Cheese', 'Optional Meat', 'Vegetables'],
    cookingMethod: 'grilled',
    isFreshOrProcessed: 'mixed',
    category: 'mixed',
    isVegetarian: true
  }
];

// Italian cuisine
export const italianFoods: FoodItem[] = [
  {
    name: 'Pizza (Cheese)',
    cuisine: 'Italian',
    calories: 285, // Per slice
    protein: 12,
    carbs: 36,
    fat: 10,
    keywords: ['pizza', 'cheese pizza', 'pizza slice', 'margherita'],
    ingredients: ['Pizza Dough', 'Tomato Sauce', 'Mozzarella Cheese', 'Basil', 'Olive Oil'],
    cookingMethod: 'baked',
    isFreshOrProcessed: 'mixed',
    category: 'mixed',
    isVegetarian: true
  },
  {
    name: 'Pasta Carbonara',
    cuisine: 'Italian',
    calories: 440,
    protein: 16,
    carbs: 54,
    fat: 18,
    keywords: ['pasta', 'carbonara', 'spaghetti carbonara', 'bacon pasta'],
    ingredients: ['Pasta', 'Egg', 'Pecorino Cheese', 'Pancetta/Bacon', 'Black Pepper'],
    cookingMethod: 'boiled',
    isFreshOrProcessed: 'mixed',
    category: 'carb'
  },
  {
    name: 'Lasagna',
    cuisine: 'Italian',
    calories: 380,
    protein: 21,
    carbs: 40,
    fat: 15,
    keywords: ['lasagna', 'italian lasagna', 'beef lasagna', 'pasta layers'],
    ingredients: ['Pasta Sheets', 'Ground Beef', 'Tomato Sauce', 'Bechamel Sauce', 'Cheese'],
    cookingMethod: 'baked',
    isFreshOrProcessed: 'mixed',
    category: 'mixed'
  },
  {
    name: 'Risotto',
    cuisine: 'Italian',
    calories: 350,
    protein: 8,
    carbs: 45,
    fat: 14,
    keywords: ['risotto', 'italian rice', 'creamy rice', 'mushroom risotto'],
    ingredients: ['Arborio Rice', 'Broth', 'Butter', 'Parmesan Cheese', 'Wine', 'Vegetables/Meat'],
    cookingMethod: 'simmered',
    isFreshOrProcessed: 'mixed',
    category: 'carb',
    isGlutenFree: true
  },
  {
    name: 'Tiramisu',
    cuisine: 'Italian',
    calories: 320,
    protein: 5,
    carbs: 30,
    fat: 20,
    keywords: ['tiramisu', 'italian dessert', 'coffee dessert', 'mascarpone dessert'],
    ingredients: ['Ladyfingers', 'Mascarpone Cheese', 'Coffee', 'Eggs', 'Sugar', 'Cocoa Powder'],
    cookingMethod: 'chilled',
    isFreshOrProcessed: 'mixed',
    category: 'dessert',
    isVegetarian: true
  }
];

// Japanese cuisine
export const japaneseFoods: FoodItem[] = [
  {
    name: 'Sushi (Salmon)',
    cuisine: 'Japanese',
    calories: 35, // Per piece
    protein: 3,
    carbs: 7,
    fat: 0.1,
    keywords: ['sushi', 'salmon sushi', 'nigiri', 'japanese sushi'],
    ingredients: ['Rice', 'Salmon', 'Nori', 'Wasabi', 'Soy Sauce'],
    cookingMethod: 'raw',
    isFreshOrProcessed: 'mixed',
    category: 'mixed',
    isGlutenFree: true
  },
  {
    name: 'Ramen',
    cuisine: 'Japanese',
    calories: 450,
    protein: 14,
    carbs: 60,
    fat: 15,
    keywords: ['ramen', 'japanese noodles', 'noodle soup', 'pork ramen'],
    ingredients: ['Wheat Noodles', 'Broth', 'Meat', 'Vegetables', 'Egg', 'Seaweed'],
    cookingMethod: 'boiled',
    isFreshOrProcessed: 'mixed',
    category: 'mixed'
  },
  {
    name: 'Tempura',
    cuisine: 'Japanese',
    calories: 250,
    protein: 10,
    carbs: 15,
    fat: 16,
    keywords: ['tempura', 'fried tempura', 'shrimp tempura', 'vegetable tempura'],
    ingredients: ['Seafood/Vegetables', 'Tempura Batter', 'Oil'],
    cookingMethod: 'deep-fried',
    isFreshOrProcessed: 'mixed',
    category: 'mixed'
  },
  {
    name: 'Miso Soup',
    cuisine: 'Japanese',
    calories: 40,
    protein: 3,
    carbs: 4,
    fat: 1,
    keywords: ['miso', 'miso soup', 'japanese soup', 'dashi soup'],
    ingredients: ['Dashi', 'Miso Paste', 'Tofu', 'Seaweed', 'Green Onion'],
    cookingMethod: 'simmered',
    isFreshOrProcessed: 'mixed',
    category: 'mixed',
    isVegetarian: true,
    isGlutenFree: true
  },
  {
    name: 'Gyoza (Japanese Dumplings)',
    cuisine: 'Japanese',
    calories: 260,
    protein: 8,
    carbs: 28,
    fat: 14,
    keywords: ['gyoza', 'japanese dumplings', 'fried dumplings', 'potstickers'],
    ingredients: ['Dumpling Wrappers', 'Ground Meat', 'Cabbage', 'Garlic', 'Ginger'],
    cookingMethod: 'pan-fried',
    isFreshOrProcessed: 'mixed',
    category: 'mixed'
  }
];

// Indian cuisine
export const indianFoods: FoodItem[] = [
  {
    name: 'Butter Chicken',
    cuisine: 'Indian',
    calories: 490,
    protein: 32,
    carbs: 9,
    fat: 36,
    keywords: ['butter chicken', 'murgh makhani', 'indian chicken', 'creamy chicken curry'],
    ingredients: ['Chicken', 'Tomato', 'Butter', 'Cream', 'Spices', 'Yogurt'],
    cookingMethod: 'simmered',
    isFreshOrProcessed: 'mixed',
    category: 'protein',
    isGlutenFree: true
  },
  {
    name: 'Naan Bread',
    cuisine: 'Indian',
    calories: 260,
    protein: 9,
    carbs: 48,
    fat: 3.3,
    keywords: ['naan', 'indian bread', 'flatbread', 'garlic naan'],
    ingredients: ['Flour', 'Yogurt', 'Butter', 'Yeast', 'Garlic (optional)'],
    cookingMethod: 'baked',
    isFreshOrProcessed: 'processed',
    category: 'carb',
    isVegetarian: true
  },
  {
    name: 'Samosa',
    cuisine: 'Indian',
    calories: 250,
    protein: 5,
    carbs: 28,
    fat: 14,
    keywords: ['samosa', 'indian pastry', 'potato samosa', 'fried pastry'],
    ingredients: ['Pastry Dough', 'Potatoes', 'Peas', 'Spices', 'Oil'],
    cookingMethod: 'deep-fried',
    isFreshOrProcessed: 'processed',
    category: 'carb',
    isVegetarian: true,
    isVegan: true
  },
  {
    name: 'Chicken Tikka Masala',
    cuisine: 'Indian',
    calories: 430,
    protein: 28,
    carbs: 17,
    fat: 28,
    keywords: ['tikka masala', 'chicken tikka', 'masala chicken', 'indian curry'],
    ingredients: ['Chicken', 'Tomato Sauce', 'Cream', 'Yogurt', 'Spices'],
    cookingMethod: 'simmered',
    isFreshOrProcessed: 'mixed',
    category: 'protein',
    isGlutenFree: true
  },
  {
    name: 'Palak Paneer',
    cuisine: 'Indian',
    calories: 280,
    protein: 15,
    carbs: 10,
    fat: 20,
    keywords: ['palak paneer', 'spinach paneer', 'indian cheese', 'spinach curry'],
    ingredients: ['Paneer Cheese', 'Spinach', 'Spices', 'Cream', 'Tomato'],
    cookingMethod: 'simmered',
    isFreshOrProcessed: 'mixed',
    category: 'mixed',
    isVegetarian: true,
    isGlutenFree: true
  }
];

// Chinese cuisine
export const chineseFoods: FoodItem[] = [
  {
    name: 'Kung Pao Chicken',
    cuisine: 'Chinese',
    calories: 390,
    protein: 26,
    carbs: 15,
    fat: 25,
    keywords: ['kung pao', 'kung pao chicken', 'spicy chicken', 'chinese chicken'],
    ingredients: ['Chicken', 'Peanuts', 'Vegetables', 'Chili Peppers', 'Soy Sauce'],
    cookingMethod: 'stir-fried',
    isFreshOrProcessed: 'mixed',
    category: 'protein',
    isGlutenFree: true
  },
  {
    name: 'Fried Rice',
    cuisine: 'Chinese',
    calories: 330,
    protein: 10,
    carbs: 55,
    fat: 7,
    keywords: ['fried rice', 'chinese rice', 'egg fried rice', 'rice dish'],
    ingredients: ['Rice', 'Egg', 'Vegetables', 'Meat (optional)', 'Soy Sauce'],
    cookingMethod: 'stir-fried',
    isFreshOrProcessed: 'mixed',
    category: 'carb',
    isGlutenFree: true
  },
  {
    name: 'Dim Sum (Dumplings)',
    cuisine: 'Chinese',
    calories: 40, // Per piece
    protein: 3,
    carbs: 5,
    fat: 1,
    keywords: ['dim sum', 'dumpling', 'chinese dumpling', 'steamed dumpling'],
    ingredients: ['Dumpling Wrapper', 'Meat/Vegetables', 'Soy Sauce'],
    cookingMethod: 'steamed',
    isFreshOrProcessed: 'mixed',
    category: 'mixed'
  },
  {
    name: 'Mapo Tofu',
    cuisine: 'Chinese',
    calories: 290,
    protein: 17,
    carbs: 10,
    fat: 21,
    keywords: ['mapo tofu', 'spicy tofu', 'szechuan tofu', 'tofu dish'],
    ingredients: ['Tofu', 'Ground Pork', 'Chili Bean Paste', 'Sichuan Peppercorns', 'Garlic'],
    cookingMethod: 'simmered',
    isFreshOrProcessed: 'mixed',
    category: 'protein',
    isGlutenFree: true
  }
];

// American cuisine
export const americanFoods: FoodItem[] = [
  {
    name: 'Hamburger',
    cuisine: 'American',
    calories: 540,
    protein: 25,
    carbs: 38,
    fat: 32,
    keywords: ['hamburger', 'burger', 'beef burger', 'cheeseburger'],
    ingredients: ['Beef Patty', 'Bun', 'Lettuce', 'Tomato', 'Onion', 'Cheese', 'Condiments'],
    cookingMethod: 'grilled',
    isFreshOrProcessed: 'mixed',
    category: 'mixed'
  },
  {
    name: 'Mac and Cheese',
    cuisine: 'American',
    calories: 350,
    protein: 12,
    carbs: 43,
    fat: 15,
    keywords: ['mac and cheese', 'macaroni cheese', 'pasta cheese', 'cheesy pasta'],
    ingredients: ['Pasta', 'Cheese', 'Milk', 'Butter', 'Flour'],
    cookingMethod: 'baked',
    isFreshOrProcessed: 'processed',
    category: 'carb',
    isVegetarian: true
  },
  {
    name: 'Fried Chicken',
    cuisine: 'American',
    calories: 320, // Per piece
    protein: 20,
    carbs: 11,
    fat: 21,
    keywords: ['fried chicken', 'crispy chicken', 'southern fried chicken', 'chicken piece'],
    ingredients: ['Chicken', 'Flour', 'Spices', 'Buttermilk', 'Oil'],
    cookingMethod: 'deep-fried',
    isFreshOrProcessed: 'processed',
    category: 'protein'
  },
  {
    name: 'Hot Dog',
    cuisine: 'American',
    calories: 290,
    protein: 10,
    carbs: 21,
    fat: 18,
    keywords: ['hot dog', 'frankfurter', 'sausage sandwich', 'american hot dog'],
    ingredients: ['Sausage', 'Bun', 'Mustard', 'Ketchup', 'Onion', 'Relish'],
    cookingMethod: 'grilled',
    isFreshOrProcessed: 'processed',
    category: 'mixed'
  },
  {
    name: 'Apple Pie',
    cuisine: 'American',
    calories: 350,
    protein: 3,
    carbs: 40,
    fat: 19,
    keywords: ['apple pie', 'american pie', 'pie slice', 'dessert pie'],
    ingredients: ['Pastry', 'Apples', 'Sugar', 'Cinnamon', 'Butter'],
    cookingMethod: 'baked',
    isFreshOrProcessed: 'processed',
    category: 'dessert',
    isVegetarian: true
  }
];

// Combine all food items
export const globalFoodDatabase: FoodItem[] = [
  ...filipinoFoods,
  ...mexicanFoods,
  ...italianFoods,
  ...japaneseFoods,
  ...indianFoods,
  ...chineseFoods,
  ...americanFoods,
  // Universal food items
  {
    name: 'Salad (Mixed Greens)',
    cuisine: 'Universal',
    calories: 100,
    protein: 2,
    carbs: 10,
    fat: 6,
    fiber: 3,
    keywords: ['salad', 'green salad', 'mixed greens', 'garden salad'],
    ingredients: ['Lettuce', 'Vegetables', 'Dressing'],
    cookingMethod: 'fresh',
    isFreshOrProcessed: 'fresh',
    category: 'vegetable',
    mealType: 'any',
    isVegetarian: true,
    isVegan: true,
    isGlutenFree: true
  },
  {
    name: 'Grilled Salmon',
    cuisine: 'Universal',
    calories: 350,
    protein: 34,
    carbs: 0,
    fat: 21,
    keywords: ['salmon', 'grilled salmon', 'fish filet', 'cooked salmon'],
    ingredients: ['Salmon', 'Oil', 'Lemon', 'Herbs', 'Salt', 'Pepper'],
    cookingMethod: 'grilled',
    isFreshOrProcessed: 'fresh',
    category: 'protein',
    mealType: 'any',
    isGlutenFree: true
  },
  {
    name: 'Steamed Rice',
    cuisine: 'Universal',
    calories: 150,
    protein: 3,
    carbs: 33,
    fat: 0.3,
    fiber: 0.6,
    keywords: ['rice', 'steamed rice', 'white rice', 'plain rice'],
    ingredients: ['Rice', 'Water', 'Salt (optional)'],
    cookingMethod: 'steamed',
    isFreshOrProcessed: 'processed',
    category: 'carb',
    mealType: 'any',
    isVegetarian: true,
    isVegan: true,
    isGlutenFree: true
  },
  {
    name: 'Banana',
    cuisine: 'Universal',
    calories: 105,
    protein: 1.3,
    carbs: 27,
    fat: 0.4,
    fiber: 3.1,
    sugar: 14,
    keywords: ['banana', 'fruit', 'yellow fruit', 'fresh banana'],
    ingredients: ['Banana'],
    cookingMethod: 'raw',
    isFreshOrProcessed: 'fresh',
    category: 'fruit',
    mealType: 'any',
    isVegetarian: true,
    isVegan: true,
    isGlutenFree: true
  }
];

/**
 * Search for food items by name or keywords
 * @param query Search term
 * @returns Matching food items
 */
export function searchFoodDatabase(query: string): FoodItem[] {
  const searchTerms = query.toLowerCase().split(/\s+/);
  
  return globalFoodDatabase.filter(food => {
    // Check food name
    if (food.name.toLowerCase().includes(query.toLowerCase())) {
      return true;
    }
    
    // Check alternate names
    if (food.alternateNames && food.alternateNames.some(name => 
      name.toLowerCase().includes(query.toLowerCase())
    )) {
      return true;
    }
    
    // Check keywords - require at least one keyword to match a search term
    if (food.keywords.some(keyword => 
      searchTerms.some(term => keyword.toLowerCase().includes(term))
    )) {
      return true;
    }
    
    // Check ingredients for multi-word matches
    if (food.ingredients && food.ingredients.some(ingredient => 
      ingredient.toLowerCase().includes(query.toLowerCase())
    )) {
      return true;
    }
    
    return false;
  });
}

/**
 * Get food items by cuisine
 * @param cuisine Cuisine type
 * @returns Food items for that cuisine
 */
export function getFoodsByCuisine(cuisine: string): FoodItem[] {
  return globalFoodDatabase.filter(food => 
    food.cuisine.toLowerCase() === cuisine.toLowerCase()
  );
}

/**
 * Find a specific food item by exact name match
 * @param name Food name to find
 * @returns Food item or undefined if not found
 */
export function getFoodByName(name: string): FoodItem | undefined {
  return globalFoodDatabase.find(food => 
    food.name.toLowerCase() === name.toLowerCase()
  );
}

/**
 * Get similar food items to a given food
 * @param foodName Name of the food to find similar items for
 * @param limit Maximum number of results to return
 * @returns Array of similar food items
 */
export function getSimilarFoods(foodName: string, limit: number = 5): FoodItem[] {
  const targetFood = getFoodByName(foodName);
  
  if (!targetFood) {
    return [];
  }
  
  // Get foods from the same cuisine first
  const sameCuisineFoods = globalFoodDatabase.filter(food => 
    food.name !== targetFood.name && 
    food.cuisine === targetFood.cuisine
  );
  
  // Find foods with similar categories
  const similarCategoryFoods = globalFoodDatabase.filter(food => 
    food.name !== targetFood.name && 
    food.cuisine !== targetFood.cuisine &&
    food.category === targetFood.category
  );
  
  // Combine and limit results
  return [...sameCuisineFoods, ...similarCategoryFoods].slice(0, limit);
} 