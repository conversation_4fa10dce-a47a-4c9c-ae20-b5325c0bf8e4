import { FoodItem, globalFoodDatabase } from './globalFoodDatabase';
import { calculateStringSimilarity } from './utils';

/**
 * Interface to represent detected food items with confidence scores and nutritional data
 */
export interface DetectedFoodItem {
  name: string;
  confidence: number;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber?: number;
  cuisine: string;
  category: string;
  cookingMethod?: string;
  isVegetarian?: boolean;
  isVegan?: boolean;
  isGlutenFree?: boolean;
}

/**
 * Interface for cuisine detection results
 */
export interface CuisineDetectionResult {
  items: DetectedFoodItem[];
  cuisineType: string;
  confidence: number;
  isSpecificCuisine: boolean;
}

/**
 * Detect specific food items from a global cuisine database
 * @param labels Food labels detected by general image recognition
 * @returns Detailed breakdown of detected food items and cuisine type
 */
export function detectGlobalFoodItems(labels: string[]): CuisineDetectionResult {
  // Track cuisines and confidence scores
  const cuisineConfidence: Record<string, number> = {
    'Filipino': 0,
    'Mexican': 0,
    'Italian': 0,
    'Japanese': 0,
    'Indian': 0,
    'Chinese': 0,
    'American': 0,
    'Universal': 0
  };
  
  // Generic dish indicators for each cuisine
  const cuisineIndicators: Record<string, string[]> = {
    'Filipino': ['filipino breakfast', 'silog', 'filipino cuisine', 'pinoy food'],
    'Mexican': ['mexican food', 'taco', 'burrito', 'quesadilla', 'enchilada', 'nachos'],
    'Italian': ['italian food', 'pasta', 'pizza', 'risotto', 'lasagna'],
    'Japanese': ['japanese food', 'sushi', 'ramen', 'tempura', 'miso', 'bento'],
    'Indian': ['indian food', 'curry', 'naan', 'masala', 'tandoori', 'biryani'],
    'Chinese': ['chinese food', 'stir fry', 'fried rice', 'dim sum', 'kung pao', 'dumplings'],
    'American': ['american food', 'burger', 'hot dog', 'french fries', 'mac and cheese']
  };
  
  // Check for cuisine indicators in labels
  Object.entries(cuisineIndicators).forEach(([cuisine, indicators]) => {
    for (const indicator of indicators) {
      if (labels.some(label => label.toLowerCase().includes(indicator))) {
        cuisineConfidence[cuisine] += 0.4;
      }
    }
  });

  // Detect food items by comparing labels to database
  const detectedItems: DetectedFoodItem[] = [];
  
  for (const foodItem of globalFoodDatabase) {
    // For each food item in database, check all labels
    for (const label of labels) {
      const labelLower = label.toLowerCase();
      
      // First check exact matches in name or alternate names
      if (labelLower.includes(foodItem.name.toLowerCase())) {
        addDetectedItem(detectedItems, foodItem, 0.9);
        cuisineConfidence[foodItem.cuisine] += 0.3;
        break;
      }
      
      // Check alternate names if available
      if (foodItem.alternateNames && 
          foodItem.alternateNames.some(name => labelLower.includes(name.toLowerCase()))) {
        addDetectedItem(detectedItems, foodItem, 0.85);
        cuisineConfidence[foodItem.cuisine] += 0.3;
        break;
      }
      
      // Check keywords for partial matches
      const keywordMatches = foodItem.keywords.filter(keyword => 
        labelLower.includes(keyword.toLowerCase())
      );
      
      if (keywordMatches.length > 0) {
        // Calculate confidence based on number of matching keywords
        const matchConfidence = 0.7 + (Math.min(keywordMatches.length / foodItem.keywords.length, 1) * 0.2);
        addDetectedItem(detectedItems, foodItem, matchConfidence);
        cuisineConfidence[foodItem.cuisine] += 0.2;
        break;
      }
      
      // As a last resort, check for string similarity
      const similarity = calculateStringSimilarity(labelLower, foodItem.name.toLowerCase());
      if (similarity > 0.7) {
        addDetectedItem(detectedItems, foodItem, similarity);
        cuisineConfidence[foodItem.cuisine] += 0.1;
        break;
      }
    }
  }
  
  // Find the most likely cuisine
  let dominantCuisine = 'Universal';
  let highestConfidence = 0;
  
  Object.entries(cuisineConfidence).forEach(([cuisine, confidence]) => {
    if (confidence > highestConfidence) {
      highestConfidence = confidence;
      dominantCuisine = cuisine;
    }
  });
  
  // Boost confidence if multiple items from same cuisine
  const cuisineCounts: Record<string, number> = {};
  detectedItems.forEach(item => {
    cuisineCounts[item.cuisine] = (cuisineCounts[item.cuisine] || 0) + 1;
  });
  
  Object.entries(cuisineCounts).forEach(([cuisine, count]) => {
    if (count >= 2) cuisineConfidence[cuisine] += 0.2;
    if (count >= 3) cuisineConfidence[cuisine] += 0.3;
  });
  
  // Re-evaluate dominant cuisine after count boosts
  Object.entries(cuisineConfidence).forEach(([cuisine, confidence]) => {
    if (confidence > highestConfidence) {
      highestConfidence = confidence;
      dominantCuisine = cuisine;
    }
  });
  
  return {
    items: detectedItems,
    cuisineType: dominantCuisine,
    confidence: highestConfidence,
    isSpecificCuisine: highestConfidence > 0.6 && dominantCuisine !== 'Universal'
  };
}

/**
 * Helper function to add a food item to detected items list if not already there
 */
function addDetectedItem(detectedItems: DetectedFoodItem[], foodItem: FoodItem, confidence: number): void {
  // Check if item already exists
  const existingIndex = detectedItems.findIndex(item => item.name === foodItem.name);
  
  if (existingIndex >= 0) {
    // If exists with lower confidence, update confidence
    if (detectedItems[existingIndex].confidence < confidence) {
      detectedItems[existingIndex].confidence = confidence;
    }
    return;
  }
  
  // Add as new item
  detectedItems.push({
    name: foodItem.name,
    confidence: confidence,
    calories: foodItem.calories,
    protein: foodItem.protein,
    carbs: foodItem.carbs,
    fat: foodItem.fat,
    fiber: foodItem.fiber,
    cuisine: foodItem.cuisine,
    category: foodItem.category,
    cookingMethod: foodItem.cookingMethod,
    isVegetarian: foodItem.isVegetarian,
    isVegan: foodItem.isVegan,
    isGlutenFree: foodItem.isGlutenFree
  });
}

/**
 * Generate a descriptive name for a food dish based on detected items from a specific cuisine
 */
export function generateDishName(items: DetectedFoodItem[], cuisineType: string): string {
  if (items.length === 0) return "Unknown Dish";
  
  // Check for special dish patterns based on cuisine
  switch (cuisineType) {
    case 'Filipino':
      return generateFilipinoMealName(items);
    case 'Mexican':
      return generateMexicanMealName(items);
    case 'Italian':
      return generateItalianMealName(items);
    case 'Japanese':
      return generateJapaneseMealName(items);
    case 'Indian':
      return generateIndianMealName(items);
    case 'Chinese':
      return generateChineseMealName(items);
    case 'American':
      return generateAmericanMealName(items);
    default:
      // For generic/universal dishes
      return generateGenericMealName(items);
  }
}

/**
 * Generate a descriptive name for Filipino meal based on detected items
 */
function generateFilipinoMealName(items: DetectedFoodItem[]): string {
  // Check for common silog variants
  const hasEgg = items.some(item => item.name.includes('Egg'));
  const hasRice = items.some(item => item.name.includes('Rice'));
  
  if (hasEgg && hasRice) {
    const hasSpam = items.some(item => item.name.includes('Spam'));
    const hasLongganisa = items.some(item => item.name.includes('Longganisa'));
    const hasTapa = items.some(item => item.name.includes('Tapa'));
    const hasHotdog = items.some(item => item.name.includes('Hotdog'));
    const hasCorned = items.some(item => item.name.includes('Corned Beef'));
    const hasTocino = items.some(item => item.name.includes('Tocino'));
    
    if (hasSpam) return "Spamsilog (Spam, Sinangag, Itlog)";
    if (hasLongganisa) return "Longsilog (Longganisa, Sinangag, Itlog)";
    if (hasTapa) return "Tapsilog (Tapa, Sinangag, Itlog)";
    if (hasHotdog) return "Hotsilog (Hotdog, Sinangag, Itlog)";
    if (hasCorned) return "Cornsilog (Corned Beef, Sinangag, Itlog)";
    if (hasTocino) return "Tosilog (Tocino, Sinangag, Itlog)";
    
    return "Filipino Breakfast Silog";
  }
  
  // Check for other common Filipino dishes
  const hasAdobo = items.some(item => item.name.includes('Adobo'));
  const hasPancit = items.some(item => item.name.includes('Pancit'));
  
  if (hasAdobo) return "Filipino Adobo Dish";
  if (hasPancit) return "Filipino Pancit Noodles";
  
  return "Filipino Cuisine Plate";
}

/**
 * Generate a descriptive name for Mexican meal based on detected items
 */
function generateMexicanMealName(items: DetectedFoodItem[]): string {
  const hasTaco = items.some(item => item.name.includes('Taco'));
  const hasBurrito = items.some(item => item.name.includes('Burrito'));
  const hasEnchilada = items.some(item => item.name.includes('Enchilada'));
  const hasQuesadilla = items.some(item => item.name.includes('Quesadilla'));
  const hasGuacamole = items.some(item => item.name.includes('Guacamole'));
  
  if (hasTaco) return "Mexican Taco Plate";
  if (hasBurrito) return "Mexican Burrito Plate";
  if (hasEnchilada) return "Mexican Enchilada Plate";
  if (hasQuesadilla) return "Mexican Quesadilla Plate";
  
  // If guacamole and one of the main dishes
  if (hasGuacamole && (hasTaco || hasBurrito || hasEnchilada || hasQuesadilla)) {
    return "Mexican Combo Plate with Guacamole";
  }
  
  return "Mexican Cuisine Plate";
}

/**
 * Generate a descriptive name for Italian meal based on detected items
 */
function generateItalianMealName(items: DetectedFoodItem[]): string {
  const hasPasta = items.some(item => item.name.includes('Pasta'));
  const hasPizza = items.some(item => item.name.includes('Pizza'));
  const hasLasagna = items.some(item => item.name.includes('Lasagna'));
  const hasRisotto = items.some(item => item.name.includes('Risotto'));
  const hasTiramisu = items.some(item => item.name.includes('Tiramisu'));
  
  if (hasPasta) return "Italian Pasta Dish";
  if (hasPizza) return "Italian Pizza";
  if (hasLasagna) return "Italian Lasagna";
  if (hasRisotto) return "Italian Risotto";
  
  // If dessert with main dish
  if (hasTiramisu && (hasPasta || hasPizza || hasLasagna || hasRisotto)) {
    return "Italian Meal with Tiramisu";
  }
  
  return "Italian Cuisine Plate";
}

/**
 * Generate a descriptive name for Japanese meal based on detected items
 */
function generateJapaneseMealName(items: DetectedFoodItem[]): string {
  const hasSushi = items.some(item => item.name.includes('Sushi'));
  const hasRamen = items.some(item => item.name.includes('Ramen'));
  const hasTempura = items.some(item => item.name.includes('Tempura'));
  const hasMiso = items.some(item => item.name.includes('Miso'));
  const hasGyoza = items.some(item => item.name.includes('Gyoza'));
  
  if (hasSushi) return "Japanese Sushi Plate";
  if (hasRamen) return "Japanese Ramen Bowl";
  if (hasTempura) return "Japanese Tempura Plate";
  
  // Combinations
  if (hasRamen && hasMiso) return "Japanese Ramen with Miso Soup";
  if (hasSushi && hasGyoza) return "Japanese Sushi and Gyoza Combo";
  
  return "Japanese Cuisine Plate";
}

/**
 * Generate a descriptive name for Indian meal based on detected items
 */
function generateIndianMealName(items: DetectedFoodItem[]): string {
  const hasButterChicken = items.some(item => item.name.includes('Butter Chicken'));
  const hasNaan = items.some(item => item.name.includes('Naan'));
  const hasSamosa = items.some(item => item.name.includes('Samosa'));
  const hasTikkaMasala = items.some(item => item.name.includes('Tikka Masala'));
  const hasPalakPaneer = items.some(item => item.name.includes('Palak Paneer'));
  
  if (hasButterChicken && hasNaan) return "Indian Butter Chicken with Naan";
  if (hasTikkaMasala && hasNaan) return "Indian Tikka Masala with Naan";
  
  if (hasButterChicken) return "Indian Butter Chicken Curry";
  if (hasTikkaMasala) return "Indian Tikka Masala Curry";
  if (hasPalakPaneer) return "Indian Palak Paneer";
  
  // Appetizer detection
  if (hasSamosa) return "Indian Samosa Plate";
  
  return "Indian Cuisine Plate";
}

/**
 * Generate a descriptive name for Chinese meal based on detected items
 */
function generateChineseMealName(items: DetectedFoodItem[]): string {
  const hasKungPao = items.some(item => item.name.includes('Kung Pao'));
  const hasFriedRice = items.some(item => item.name.includes('Fried Rice'));
  const hasDimSum = items.some(item => item.name.includes('Dim Sum'));
  const hasMapoTofu = items.some(item => item.name.includes('Mapo Tofu'));
  
  if (hasKungPao && hasFriedRice) return "Chinese Kung Pao with Fried Rice";
  if (hasKungPao) return "Chinese Kung Pao Chicken";
  if (hasMapoTofu) return "Chinese Mapo Tofu";
  if (hasFriedRice) return "Chinese Fried Rice";
  if (hasDimSum) return "Chinese Dim Sum Selection";
  
  // If no specific dishes were detected, get the most confident item
  if (items.length > 0) {
    const mainItem = items.reduce((prev, current) => 
      (prev.confidence > current.confidence) ? prev : current
    );
    return `Chinese ${mainItem.name} Dish`;
  }
  
  return "Chinese Cuisine Plate";
}

/**
 * Generate a descriptive name for American meal based on detected items
 */
function generateAmericanMealName(items: DetectedFoodItem[]): string {
  const hasHamburger = items.some(item => item.name.includes('Hamburger'));
  const hasMacAndCheese = items.some(item => item.name.includes('Mac and Cheese'));
  const hasFriedChicken = items.some(item => item.name.includes('Fried Chicken'));
  const hasHotDog = items.some(item => item.name.includes('Hot Dog'));
  const hasApplePie = items.some(item => item.name.includes('Apple Pie'));
  
  if (hasHamburger) return "American Hamburger";
  if (hasFriedChicken) return "American Fried Chicken";
  if (hasMacAndCheese) return "American Mac and Cheese";
  if (hasHotDog) return "American Hot Dog";
  
  // Dessert with main course
  if (hasApplePie && (hasHamburger || hasFriedChicken || hasMacAndCheese || hasHotDog)) {
    return "American Meal with Apple Pie";
  }
  
  return "American Cuisine Plate";
}

/**
 * Generate a generic meal name based on detected items
 */
function generateGenericMealName(items: DetectedFoodItem[]): string {
  // Group items by category
  const hasProtein = items.some(item => item.category === 'protein');
  const hasCarb = items.some(item => item.category === 'carb');
  const hasVegetable = items.some(item => item.category === 'vegetable');
  const hasFruit = items.some(item => item.category === 'fruit');
  const hasDessert = items.some(item => item.category === 'dessert');
  
  // Generate description based on meal composition
  if (hasProtein && hasCarb && hasVegetable) {
    return "Balanced Meal with Protein, Carbs and Vegetables";
  }
  
  if (hasProtein && hasCarb) {
    return "Protein and Carb Meal";
  }
  
  if (hasProtein && hasVegetable) {
    return "Protein and Vegetable Meal (Low Carb)";
  }
  
  if (hasCarb && hasVegetable) {
    return "Vegetarian Carb and Vegetable Meal";
  }
  
  if (hasDessert) {
    return "Dessert Plate";
  }
  
  if (hasFruit) {
    return "Fruit Plate";
  }
  
  // Get the most confident item as the main dish
  if (items.length > 0) {
    const mainItem = items.reduce((prev, current) => 
      (prev.confidence > current.confidence) ? prev : current
    );
    return `${mainItem.name} Dish`;
  }
  
  return "Mixed Food Plate";
}

/**
 * Calculate nutritional data for a set of food items
 */
export function calculateNutrition(items: DetectedFoodItem[]) {
  // Calculate totals
  const totalCalories = items.reduce((sum, item) => sum + item.calories, 0);
  const totalProtein = items.reduce((sum, item) => sum + item.protein, 0);
  const totalCarbs = items.reduce((sum, item) => sum + item.carbs, 0);
  const totalFat = items.reduce((sum, item) => sum + item.fat, 0);
  const totalFiber = items.reduce((sum, item) => sum + (item.fiber || 0), 0);
  
  // Determine meal type based on composition
  const mealType = determineMealType(items, totalCalories);
  
  // Check if nutritionally balanced
  const isBalanced = isBalancedMeal(totalProtein, totalCarbs, totalFat);
  
  // Calculate quality score
  const qualityScore = calculateNutritionalQualityScore(
    items, totalCalories, totalProtein, totalCarbs, totalFat, totalFiber
  );
  
  return {
    totalCalories,
    macronutrients: {
      protein: totalProtein,
      carbs: totalCarbs,
      fat: totalFat,
      fiber: totalFiber
    },
    mealType,
    isBalancedMeal: isBalanced,
    nutritionalQualityScore: qualityScore
  };
}

/**
 * Determine meal type based on detected items and calories
 */
function determineMealType(items: DetectedFoodItem[], calories: number): 'breakfast' | 'lunch' | 'dinner' | 'snack' | 'unknown' {
  // Check if items have defined meal types
  const breakfastItems = items.filter(item => item.name.includes('Breakfast') || 
                                             (item.cuisine === 'Filipino' && item.name.includes('Silog')));
  
  if (breakfastItems.length > 0) return 'breakfast';
  
  // Check based on calories
  if (calories < 400) return 'snack';
  if (calories >= 400 && calories < 650) return 'lunch';
  if (calories >= 650) return 'dinner';
  
  // Default fallback
  return 'unknown';
}

/**
 * Determine if a meal is nutritionally balanced
 */
function isBalancedMeal(protein: number, carbs: number, fat: number): boolean {
  // Calculate calories from each macronutrient
  const proteinCalories = protein * 4;
  const carbCalories = carbs * 4;
  const fatCalories = fat * 9;
  const totalCalories = proteinCalories + carbCalories + fatCalories;
  
  if (totalCalories === 0) return false;
  
  // Calculate percentages
  const proteinPercentage = proteinCalories / totalCalories;
  const carbPercentage = carbCalories / totalCalories;
  const fatPercentage = fatCalories / totalCalories;
  
  // Check if within recommended ranges
  const isProteinBalanced = proteinPercentage >= 0.15 && proteinPercentage <= 0.35;
  const isCarbsBalanced = carbPercentage >= 0.40 && carbPercentage <= 0.65;
  const isFatBalanced = fatPercentage >= 0.20 && fatPercentage <= 0.35;
  
  return isProteinBalanced && isCarbsBalanced && isFatBalanced;
}

/**
 * Calculate a nutritional quality score (0-10)
 */
function calculateNutritionalQualityScore(
  items: DetectedFoodItem[],
  calories: number,
  protein: number,
  carbs: number,
  fat: number,
  fiber: number
): number {
  // Start with a baseline score
  let score = 5;
  
  // Adjust based on macronutrient balance
  if (isBalancedMeal(protein, carbs, fat)) score += 2;
  
  // Adjust based on fiber content (important for digestive health)
  if (fiber >= 5) score += 1;
  
  // Check for presence of vegetables or fruits
  const hasFruitOrVeg = items.some(item => 
    item.category === 'vegetable' || item.category === 'fruit'
  );
  if (hasFruitOrVeg) score += 1;
  
  // Check for processed vs. fresh foods
  const processedItems = items.filter(item => 
    item.cuisine === 'Universal' && 
    (item.name.includes('processed') || item.name.includes('fried'))
  );
  
  if (processedItems.length === 0) score += 1;
  if (processedItems.length >= 2) score -= 1;
  
  // Ensure score is within 0-10 range
  return Math.max(0, Math.min(10, score));
}

/**
 * Generate health insights based on detected food items
 */
export function generateHealthInsights(
  items: DetectedFoodItem[],
  totalCalories: number,
  protein: number,
  carbs: number,
  fat: number,
  fiber: number
): string[] {
  const insights: string[] = [];
  
  // Calorie insights
  if (totalCalories > 800) {
    insights.push("This is a high-calorie meal. Consider smaller portions if monitoring calorie intake.");
  } else if (totalCalories < 300) {
    insights.push("This is a relatively low-calorie meal, suitable as a light meal or snack.");
  }
  
  // Protein insights
  if (protein > 25) {
    insights.push("This meal is rich in protein, which supports muscle maintenance and repair.");
  } else if (protein < 10) {
    insights.push("This meal is relatively low in protein. Consider adding protein sources for a more balanced meal.");
  }
  
  // Carb insights
  if (carbs > 60) {
    insights.push("This meal is high in carbohydrates, providing energy but may cause blood sugar spikes.");
  } else if (carbs < 20) {
    insights.push("This meal is low in carbohydrates, making it suitable for low-carb dietary preferences.");
  }
  
  // Fat insights
  if (fat > 30) {
    insights.push("This meal contains significant fat content. Focus on portion control if monitoring fat intake.");
  }
  
  // Fiber insights
  if (fiber > 8) {
    insights.push("This meal has excellent fiber content, supporting digestive health and satiety.");
  } else if (fiber < 3) {
    insights.push("This meal is low in fiber. Consider adding fruits, vegetables, or whole grains for more fiber.");
  }
  
  // Vegetarian/Vegan insights
  const isVegetarian = items.every(item => item.isVegetarian === true);
  const isVegan = items.every(item => item.isVegan === true);
  
  if (isVegan) {
    insights.push("This is a plant-based vegan meal with no animal products.");
  } else if (isVegetarian) {
    insights.push("This is a vegetarian meal with no meat products.");
  }
  
  // Gluten-free insights
  const isGlutenFree = items.every(item => item.isGlutenFree === true);
  
  if (isGlutenFree) {
    insights.push("This meal appears to be gluten-free, suitable for those with gluten sensitivities.");
  }
  
  // Balance insights
  if (isBalancedMeal(protein, carbs, fat)) {
    insights.push("This meal has a balanced macronutrient profile with appropriate proportions of protein, carbs, and fat.");
  } else {
    // Identify the specific imbalance
    const proteinPct = (protein * 4) / ((protein * 4) + (carbs * 4) + (fat * 9));
    const carbsPct = (carbs * 4) / ((protein * 4) + (carbs * 4) + (fat * 9));
    const fatPct = (fat * 9) / ((protein * 4) + (carbs * 4) + (fat * 9));
    
    if (proteinPct < 0.15) {
      insights.push("This meal could benefit from more protein sources for better macronutrient balance.");
    }
    if (carbsPct > 0.65) {
      insights.push("This meal is very high in carbohydrates relative to other macronutrients.");
    }
    if (fatPct > 0.35) {
      insights.push("This meal has a relatively high fat content compared to recommended dietary guidelines.");
    }
  }
  
  // If no insights were generated, add a generic one
  if (insights.length === 0) {
    insights.push("This meal provides a mix of nutrients. Aim for variety in your diet for optimal nutrition.");
  }
  
  return insights;
} 