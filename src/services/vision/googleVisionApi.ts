import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { detectLabels } from '../openai/secureApiClient';

// No longer using direct API endpoint - using Firebase Functions instead

/**
 * Convert an image URI to base64 format
 * @param uri Image URI to convert
 * @returns Base64 encoded image string
 */
export async function uriToBase64(uri: string): Promise<string> {
  try {
    // Handle web platform differently
    if (Platform.OS === 'web') {
      const response = await fetch(uri);
      const blob = await response.blob();
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          const base64 = result.split(',')[1];
          resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    }

    // Handle native platforms
    // First check if the uri is a remote URL or a local file
    if (uri.startsWith('http')) {
      // For remote URLs, we need to download the file first
      const fileInfo = await FileSystem.getInfoAsync(FileSystem.cacheDirectory + 'temp_image.jpg');
      
      if (!fileInfo.exists) {
        await FileSystem.downloadAsync(
          uri,
          FileSystem.cacheDirectory + 'temp_image.jpg'
        );
      }
      
      uri = FileSystem.cacheDirectory + 'temp_image.jpg';
    }
    
    // Read the file as base64
    const base64 = await FileSystem.readAsStringAsync(uri, {
      encoding: FileSystem.EncodingType.Base64
    });
    
    return base64;
  } catch (error) {
    console.error('Error converting image to base64:', error);
    throw error;
  }
}

interface GoogleVisionResponse {
  responses: {
    localizedObjectAnnotations?: {
      name: string;
      score: number;
      boundingPoly: {
        normalizedVertices: {
          x?: number;
          y?: number;
        }[];
      };
    }[];
    labelAnnotations?: {
      description: string;
      score: number;
    }[];
  }[];
}

interface BoundingBox {
  topLeft: { x: number; y: number };
  bottomRight: { x: number; y: number };
}

interface FoodItem {
  name: string;
  score: number;
  boundingBox: BoundingBox | null;
}

/**
 * Detect objects in an image using Google Cloud Vision API via Firebase Functions
 * @param imageUri URI of the image to analyze
 * @param bypassCache Optional flag to bypass cache and force fresh analysis
 * @returns Array of detected objects with bounding boxes and scores
 */
export async function detectObjectsWithGoogleVision(imageUri: string, bypassCache: boolean = false) {
  try {
    console.log(`Detecting objects in image: ${imageUri}, bypassCache: ${bypassCache}`);
    
    // Only check cache if we're not bypassing it
    if (!bypassCache) {
      try {
        // Generate a more specific cache key that just uses the base URI without query params
        const baseUri = imageUri.split('?')[0];
        const cacheKey = `google_vision_${baseUri}`;
        console.log(`Checking cache with key: ${cacheKey}`);
        
        const cachedData = await AsyncStorage.getItem(cacheKey);
        
        if (cachedData) {
          console.log('Using cached Google Vision result');
          return JSON.parse(cachedData);
        } else {
          console.log('No cached result found, proceeding with API call');
        }
      } catch (cacheError) {
        console.log('Cache check failed, proceeding with API call', cacheError);
      }
    } else {
      console.log('Cache bypassed, performing fresh analysis');
    }
    
    // Convert image to base64
    const base64Image = await uriToBase64(imageUri);
    console.log(`Successfully converted image to base64, length: ${base64Image.length}`);
    
    // Create data URL from base64
    const dataUrl = `data:image/jpeg;base64,${base64Image}`;
    
    // Call Firebase Function
    console.log('Calling Google Vision via Firebase Functions');
    const response = await detectLabels(dataUrl);
    
    if (!response.success || !response.labels) {
      throw new Error('Google Vision API error via Firebase Functions');
    }
    
    // Transform response to match the expected format
    const data: GoogleVisionResponse = {
      responses: [{
        labelAnnotations: response.labels,
        localizedObjectAnnotations: [] // Firebase function doesn't return objects yet
      }]
    };
    
    // Process results
    const objects = data.responses?.[0]?.localizedObjectAnnotations || [];
    const labels = data.responses?.[0]?.labelAnnotations || [];
    
    console.log(`Google Vision API returned: ${objects.length} objects, ${labels.length} labels`);
    
    // Create normalized result
    const result = {
      objects: objects.map((obj: any) => ({
        name: obj.name,
        score: obj.score,
        boundingBox: {
          topLeft: {
            x: obj.boundingPoly.normalizedVertices[0].x || 0,
            y: obj.boundingPoly.normalizedVertices[0].y || 0
          },
          bottomRight: {
            x: obj.boundingPoly.normalizedVertices[2].x || 1,
            y: obj.boundingPoly.normalizedVertices[2].y || 1
          }
        }
      })),
      labels: labels.map((label: any) => ({
        description: label.description,
        score: label.score
      })),
      foodItems: objects
        .filter((obj: any) => isFoodObject(obj.name))
        .map((obj: any) => ({
          name: obj.name,
          score: obj.score,
          boundingBox: {
            topLeft: {
              x: obj.boundingPoly.normalizedVertices[0].x || 0,
              y: obj.boundingPoly.normalizedVertices[0].y || 0
            },
            bottomRight: {
              x: obj.boundingPoly.normalizedVertices[2].x || 1,
              y: obj.boundingPoly.normalizedVertices[2].y || 1
            }
          }
        })) as FoodItem[]
    };
    
    // Filter food related labels
    const foodLabels = labels
      .filter((label: any) => isFoodLabel(label.description))
      .map((label: any) => ({
        name: label.description,
        score: label.score,
        boundingBox: null
      } as FoodItem));
    
    // Combine food objects and labels
    result.foodItems = [...result.foodItems, ...foodLabels];
    
    // Remove duplicates and sort by score
    result.foodItems = removeDuplicateFoodItems(result.foodItems);
    
    // Cache the result only if we're not bypassing the cache
    if (!bypassCache) {
      try {
        // Generate a more specific cache key that just uses the base URI without query params
        const baseUri = imageUri.split('?')[0];
        const cacheKey = `google_vision_${baseUri}`;
        await AsyncStorage.setItem(cacheKey, JSON.stringify(result));
        console.log(`Cached result with key: ${cacheKey}`);
      } catch (cacheError) {
        console.log('Failed to cache result', cacheError);
      }
    } else {
      console.log('Skipping cache storage as bypassCache is enabled');
    }
    
    return result;
  } catch (error) {
    console.error('Error detecting objects with Google Vision:', error);
    throw error;
  }
}

/**
 * Check if an object name is food-related
 * @param name Object name to check
 * @returns Boolean indicating if the object is food
 */
function isFoodObject(name: string): boolean {
  const foodCategories = [
    'Food', 'Fruit', 'Vegetable', 'Dessert', 'Baked goods', 'Dish', 'Cuisine',
    'Bread', 'Meat', 'Fast food', 'Produce', 'Ingredient', 'Seafood',
    'Snack', 'Drink', 'Beverage', 'Salad', 'Sandwich', 'Breakfast', 'Lunch',
    'Dinner', 'Cake', 'Pasta', 'Rice', 'Soup', 'Pizza', 'Burger', 'Taco',
    'Cheese', 'Egg', 'Fish', 'Chicken', 'Beef', 'Pork', 'Cookie', 'Ice cream'
  ];
  
  return foodCategories.some(category => 
    name.toLowerCase().includes(category.toLowerCase()) || 
    category.toLowerCase().includes(name.toLowerCase())
  );
}

/**
 * Check if a label is food-related
 * @param description Label description
 * @returns Boolean indicating if the label is food
 */
function isFoodLabel(description: string): boolean {
  const foodKeywords = [
    'food', 'dish', 'meal', 'cuisine', 'dinner', 'lunch', 'breakfast',
    'snack', 'dessert', 'baked', 'fried', 'grilled', 'roasted', 'steamed',
    'recipe', 'ingredient', 'cooked', 'eating', 'dietary', 'nutrition',
    'tasty', 'delicious', 'savory', 'sweet', 'menu', 'restaurant', 'cafe',
    'homemade', 'organic', 'vegetarian', 'vegan', 'pescatarian', 'carnivore'
  ];
  
  return foodKeywords.some(keyword => 
    description.toLowerCase().includes(keyword)
  );
}

/**
 * Remove duplicate food items by name and prefer items with bounding boxes
 * @param items Array of detected food items
 * @returns Deduplicated array of food items
 */
function removeDuplicateFoodItems(items: any[]) {
  const uniqueItems = new Map();
  
  // Process items with bounding boxes first, then those without
  const sortedItems = [...items].sort((a, b) => {
    // Prefer items with bounding boxes
    if (a.boundingBox && !b.boundingBox) return -1;
    if (!a.boundingBox && b.boundingBox) return 1;
    
    // If both have or don't have bounding boxes, prefer higher score
    return b.score - a.score;
  });
  
  // Keep only the highest scoring item for each name
  for (const item of sortedItems) {
    const normalizedName = item.name.toLowerCase();
    if (!uniqueItems.has(normalizedName)) {
      uniqueItems.set(normalizedName, item);
    }
  }
  
  return Array.from(uniqueItems.values());
}

/**
 * Crop an image to a bounding box using Expo FileSystem
 * @param imageUri URI of the image to crop
 * @param boundingBox Bounding box coordinates (normalized 0-1)
 * @returns URI of the cropped image
 */
export async function cropImageToBoundingBox(imageUri: string, boundingBox: {
  topLeft: { x: number, y: number },
  bottomRight: { x: number, y: number }
}) {
  try {
    // This function is a placeholder as Expo FileSystem doesn't natively support
    // image manipulation. In a real application, you would use:
    // 1. expo-image-manipulator
    // 2. Send the cropping to a server
    // 3. Use a native module
    
    console.log('Image cropping requested:', boundingBox);
    
    // For now, we'll just return the original image
    return imageUri;
  } catch (error) {
    console.error('Error cropping image:', error);
    return imageUri;
  }
} 