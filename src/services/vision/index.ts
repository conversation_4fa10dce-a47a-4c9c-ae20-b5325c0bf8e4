// Re-export all public API functions
import { AnalysisCacheService } from './cacheService';
import { ImageAnalysisOptions, UnifiedFoodAnalysisResult } from './types';
// import { enhanceWithBarcodeInfo } from './barcodeRecognition';
import { Platform } from 'react-native';

export * from './types';
export * from './visionApi';
export * from './foodRecognition';
export * from './nutritionAnalysis';
export * from './recipeDatabase';
export * from './advancedVision';
export * from './utils';
export * from './barcodeRecognition';

// Main functions
// export { analyzeFoodImage } from './foodRecognition';
export { unifiedAnalyzeFoodImage } from './advancedVision';
// import { webUriToBase64 } from '@/utils/imageProcessing';

// At the top of the file, add a more robust approach
type ExtendedAnalysisResult = UnifiedFoodAnalysisResult & {
  textualAnalysis: any;
  detailedFoodInfo: any[];
  foodItems: any[];
  offlineProcessingInfo?: any;
};

/**
 * Enhanced image analyzer with progressive loading, caching, and multi-view support
 */
export async function analyzeImageWithDetails(
  imageUri: string, 
  options?: Partial<ImageAnalysisOptions>,
  secondaryImages?: string[]
): Promise<{
  success: boolean;
  data?: any;
  meta?: any;
  error?: string;
  progressiveUpdates?: (updatedData: any) => void;
}> {
  try {
    // Create a copy of the options with defaults
    const analysisOptions: any = {
      includeDetailsLevel: 'standard',
      includeImageQuality: true,
      includeIngredientDetection: true,
      includeFoodQualityAssessment: true,
      includeMultiView: false,
      includeBarcode: false,
      preferOfflineMode: false,
      useProgressiveLoading: false,
      bypassCache: false,
      ...options
    };
    
    // Processing options for caching
    const optionsString = JSON.stringify(analysisOptions);
    
    // Get image data - use appropriate method for web platform
    let imageData: string;
    if (Platform.OS === 'web') {
      // For web platforms, we'll need to implement or import a suitable function
      // const { webUriToBase64 } = await import('./utils');
      // imageData = await webUriToBase64(imageUri);
      imageData = imageUri; // Simplified for now
    } else {
      const { uriToBase64 } = await import('./visionApi');
      imageData = await uriToBase64(imageUri);
    }
    
    // Check cache first if we're not using progressive loading and not bypassing cache
    if (!analysisOptions.useProgressiveLoading && !analysisOptions.bypassCache) {
      const cachedResult = await AnalysisCacheService.getInstance().getCachedAnalysisResult(imageData, optionsString);
      
      if (cachedResult) {
        console.log('Using cached result from', new Date(cachedResult.timestamp).toISOString());
        // Format the results based on detail level
        return {
          success: cachedResult.result.foodItems.length > 0,
          data: formatAnalysisResult(cachedResult.result, analysisOptions),
          meta: {
            ...cachedResult.result.meta,
            cacheHit: true
          }
        };
      }
    } else if (analysisOptions.bypassCache) {
      console.log('Cache bypassed, performing fresh analysis');
    }
    
    // Map detail level to advanced options
    const advancedOptions = {
      includeVolumeEstimation: analysisOptions.includeDetailsLevel !== 'basic',
      includeFineGrainedDetails: analysisOptions.includeDetailsLevel !== 'basic',
      includeNutritionalAnalysis: true,
      includeTextualDescription: true,
      additionalImages: secondaryImages || [],
      bypassCache: analysisOptions.bypassCache,
      modelQuality: (analysisOptions.includeDetailsLevel === 'comprehensive' ? 'accurate' : 
                 (analysisOptions.includeDetailsLevel === 'standard' ? 'balanced' : 'fast')) as 'fast' | 'balanced' | 'accurate'
    };
    
    // For progressive loading, set up an intermediate result handler
    let progressCallback: ((data: any) => void) | undefined;
    
    if (analysisOptions.useProgressiveLoading) {
      const { createProgressiveAnalyzer } = await import('./progressiveAnalysis');
      const { analyzeProgressively, registerCallback } = createProgressiveAnalyzer();
      
      // Register the callback for progressive updates
      const callbackFunction = (progressiveResult: UnifiedFoodAnalysisResult) => {
        return {
          success: progressiveResult.foodItems.length > 0,
          data: formatAnalysisResult(progressiveResult, {
            ...analysisOptions,
            // Limit the data returned for initial updates
            includeDetailsLevel: 'basic'
          }),
          meta: {
            ...progressiveResult.meta,
            progressive: true,
            progressPercentage: progressiveResult.progressiveResults?.progressPercentage || 0
          }
        };
      };
      
      progressCallback = callbackFunction;
      const registeredCallback = registerCallback(callbackFunction);
      
      // Start progressive analysis
      const finalResult = await analyzeProgressively(imageData, advancedOptions, registeredCallback);
      
      // Cache the final result if we're not bypassing the cache
      if (!analysisOptions.bypassCache) {
        await AnalysisCacheService.getInstance().cacheAnalysisResult(imageData, finalResult, optionsString);
      }
      
      // Return the final formatted result
      return {
        success: finalResult.foodItems.length > 0,
        data: formatAnalysisResult(finalResult, analysisOptions),
        meta: finalResult.meta,
        progressiveUpdates: progressCallback
      };
    }
    
    // Standard (non-progressive) analysis path
    // Get full analysis using the unified analyzer
    const { unifiedAnalyzeFoodImage } = await import('./advancedVision');
    let result = await unifiedAnalyzeFoodImage(imageData, advancedOptions);
    
    // Enhance with barcode information if enabled
    if (analysisOptions.includeBarcode) {
      console.log('Barcode enhancement is currently disabled');
    }
    
    // Add nutritional goal comparison if provided
    if (analysisOptions.nutritionalGoals) {
      // Use type assertion to allow adding properties
      (result.nutritionalSummary as any).nutritionalGoalComparison = calculateNutritionalGoalComparison(
        result.nutritionalSummary,
        analysisOptions.nutritionalGoals
      );
    }
    
    // If offline mode is preferred, add offline processing info
    if (analysisOptions.preferOfflineMode) {
      // Use type assertion to allow adding properties
      (result as any).offlineProcessingInfo = {
        wasProcessedOffline: true,
        confidenceReduction: 0.1, // Slight confidence reduction for offline processing
        limitedFeaturesUsed: ['basic recognition', 'cached models']
      };
    }
    
    // Cache the result for future use if we're not bypassing the cache
    if (!analysisOptions.bypassCache) {
      await AnalysisCacheService.getInstance().cacheAnalysisResult(imageData, result, optionsString);
    }
    
    // Then cast once
    const typedResult = result as unknown as ExtendedAnalysisResult;
    
    // Format the results based on detail level
    return {
      success: typedResult.foodItems.length > 0,
      data: formatAnalysisResult(typedResult, analysisOptions),
      meta: typedResult.meta
    };
  } catch (error) {
    console.error('Error in advanced image analysis:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error analyzing image with details'
    };
  }
}

/**
 * Compare nutritional values with user goals
 */
function calculateNutritionalGoalComparison(
  nutritionalSummary: UnifiedFoodAnalysisResult['nutritionalSummary'],
  goals?: {
    targetCalories?: number;
    targetProtein?: number;
    targetCarbs?: number;
    targetFat?: number;
  }
): {
  caloriesDifference: number;
  proteinDifference: number;
  carbsDifference: number;
  fatDifference: number;
  isWithinTarget: boolean;
} {
  if (!goals) {
    return {
      caloriesDifference: 0,
      proteinDifference: 0,
      carbsDifference: 0,
      fatDifference: 0,
      isWithinTarget: true
    };
  }
  
  const caloriesDifference = goals.targetCalories ? 
    nutritionalSummary.totalCalories - goals.targetCalories : 0;
  
  const proteinDifference = goals.targetProtein ? 
    nutritionalSummary.macronutrients.protein - goals.targetProtein : 0;
  
  const carbsDifference = goals.targetCarbs ? 
    nutritionalSummary.macronutrients.carbs - goals.targetCarbs : 0;
  
  const fatDifference = goals.targetFat ? 
    nutritionalSummary.macronutrients.fat - goals.targetFat : 0;
  
  // Check if values are within acceptable range (±10% of target)
  const isWithinTarget = 
    (!goals.targetCalories || Math.abs(caloriesDifference) <= goals.targetCalories * 0.1) &&
    (!goals.targetProtein || Math.abs(proteinDifference) <= goals.targetProtein * 0.1) &&
    (!goals.targetCarbs || Math.abs(carbsDifference) <= goals.targetCarbs * 0.1) &&
    (!goals.targetFat || Math.abs(fatDifference) <= goals.targetFat * 0.1);
  
  return {
    caloriesDifference,
    proteinDifference,
    carbsDifference,
    fatDifference,
    isWithinTarget
  };
}

/**
 * Analyze multiple views of the same food item for improved accuracy
 */
export async function analyzeMultiViewImages(
  imageUris: string[],
  options?: Partial<ImageAnalysisOptions>
): Promise<{
  success: boolean;
  data?: any;
  meta?: any;
  error?: string;
}> {
  if (!imageUris || imageUris.length === 0) {
    return {
      success: false,
      error: 'No images provided for multi-view analysis'
    };
  }
  
  try {
    // Use the primary image for main analysis
    const primaryImageUri = imageUris[0];
    const secondaryImages = imageUris.slice(1);
    
    // Convert secondary images to base64
    const secondaryImagesData = await Promise.all(
      secondaryImages.map(uri => import('./visionApi').then(api => api.uriToBase64(uri)))
    );
    
    // Perform analysis with multi-view option enabled
    return analyzeImageWithDetails(
      primaryImageUri,
      {
        ...options,
        includeMultiView: true
      },
      secondaryImagesData
    );
  } catch (error) {
    console.error('Error in multi-view analysis:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error during multi-view analysis'
    };
  }
}

/**
 * Realtime food analysis optimized for performance
 */
export async function analyzeImageRealtime(
  imageUri: string
): Promise<{
  success: boolean;
  data?: any;
  meta?: any;
  error?: string;
}> {
  try {
    // For realtime analysis, use basic detail level and prefer cached results
    return analyzeImageWithDetails(imageUri, {
      includeDetailsLevel: 'basic',
      includeImageQuality: false,
      includeIngredientDetection: false,
      includeFoodQualityAssessment: false,
      preferOfflineMode: true,
      useProgressiveLoading: true
    });
  } catch (error) {
    console.error('Error in realtime analysis:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error during realtime analysis'
    };
  }
}

/**
 * Formats the unified analysis result based on user options
 */
function formatAnalysisResult(
  result: UnifiedFoodAnalysisResult, 
  options: ImageAnalysisOptions
) {
  // Cast once at the beginning
  const typedResult = result as unknown as ExtendedAnalysisResult;
  
  // Basic info is always included
  const formattedResult = {
    name: result.foodItems.length > 0 ? result.foodItems[0].name : 'Unknown food',
    confidence: result.foodItems.length > 0 ? result.foodItems[0].confidence : 0,
    description: result.textualAnalysis.description,
    calories: result.nutritionalSummary.totalCalories,
    protein: result.nutritionalSummary.macronutrients.protein,
    carbs: result.nutritionalSummary.macronutrients.carbs,
    fat: result.nutritionalSummary.macronutrients.fat,
    fiber: result.nutritionalSummary.macronutrients.fiber,
    items: result.foodItems.map(item => ({
      name: item.name,
      confidence: item.confidence,
      boundingBox: item.boundingBox
    }))
  };
  
  // Add image quality if requested
  if (options.includeImageQuality) {
    Object.assign(formattedResult, {
      imageQuality: {
        brightness: result.imageProperties.brightness,
        contrast: result.imageProperties.contrast,
        sharpness: result.imageProperties.sharpness,
        hasPlate: result.imageProperties.hasPlate,
        dominantColors: result.imageProperties.dominantColors
        // Additional properties commented out due to type issues
        // additionalProperties: result.imageProperties.additionalProperties,
        // improvementSuggestions: result.imageProperties.improvementSuggestions
      }
    });
  }
  
  // Add ingredients if requested
  if (options.includeIngredientDetection) {
    const allIngredients = result.detailedFoodInfo.flatMap(item => item.ingredients);
    const uniqueIngredients = [...new Set(allIngredients)];
    
    Object.assign(formattedResult, {
      ingredients: uniqueIngredients,
      allergens: result.textualAnalysis.allergens || []
    });
  }
  
  // Add food quality assessment if requested
  if (options.includeFoodQualityAssessment) {
    // Add a type assertion to tell TypeScript these properties might exist
    type DetailedFoodInfoExtended = any; // Using any for brevity, ideally define proper interface

    // Then modify the filtering code with type assertions
    const qualityAssessments = result.detailedFoodInfo
      .filter(item => (item as DetailedFoodInfoExtended).qualityAssessment)
      .map(item => (item as DetailedFoodInfoExtended).qualityAssessment!);
    
    if (qualityAssessments.length > 0) {
      const avgPresentationScore = qualityAssessments.reduce(
        (sum, qa) => sum + qa.presentationScore, 0
      ) / qualityAssessments.length;
      
      const avgFreshness = qualityAssessments.reduce(
        (sum, qa) => sum + qa.freshness, 0
      ) / qualityAssessments.length;
      
      Object.assign(formattedResult, {
        qualityAssessment: {
          presentationScore: avgPresentationScore,
          freshness: avgFreshness,
          nutritionalQualityScore: result.nutritionalSummary.nutritionalQualityScore,
          isBalancedMeal: result.nutritionalSummary.isBalancedMeal,
          ...(((result.textualAnalysis as any).qualitativeAssessment) && {
            qualitativeRating: (result.textualAnalysis as any).qualitativeAssessment
          })
        }
      });
    }
  }
  
  // Add barcode information if available
  if ((result as any).barcodeInfo) {
    Object.assign(formattedResult, {
      barcode: {
        value: (result as any).barcodeInfo.value,
        format: (result as any).barcodeInfo.format,
        product: (result as any).barcodeInfo.productName ? {
          name: (result as any).barcodeInfo.productName,
          brand: (result as any).barcodeInfo.brand,
          nutritionalInfo: (result as any).barcodeInfo.nutritionalInfo
        } : undefined
      }
    });
  }
  
  // Add comparable foods if available
  if (typedResult.detailedFoodInfo.some(info => (info as any).comparableFoods?.length)) {
    const comparableFoods = typedResult.detailedFoodInfo
      .filter(info => (info as any).comparableFoods?.length)
      .flatMap(info => (info as any).comparableFoods || []);
    
    if (comparableFoods.length > 0) {
      Object.assign(formattedResult, {
        comparableFoods: comparableFoods.map(food => ({
          name: food.name,
          caloriesDifference: food.caloriesDifference,
          proteinDifference: food.proteinDifference,
          carbsDifference: food.carbsDifference,
          fatDifference: food.fatDifference
        }))
      });
    }
  }
  
  // Add recipe match if available
  if (typedResult.detailedFoodInfo.some(info => (info as any).recipeMatch)) {
    const recipeMatches = typedResult.detailedFoodInfo
      .filter(info => (info as any).recipeMatch)
      .map(info => (info as any).recipeMatch!);
    
    if (recipeMatches.length > 0) {
      // Use the recipe match with the highest confidence
      const bestMatch = recipeMatches.reduce(
        (best, current) => current.matchConfidence > best.matchConfidence ? current : best, 
        recipeMatches[0]
      );
      
      Object.assign(formattedResult, {
        recipeMatch: {
          name: bestMatch.recipeName,
          confidence: bestMatch.matchConfidence,
          url: bestMatch.recipeUrl,
          cookingTime: bestMatch.cookingTime,
          difficulty: bestMatch.difficulty
        }
      });
    }
  }
  
  // Add nutritional goal comparison if available
  if ((result.nutritionalSummary as any).nutritionalGoalComparison) {
    Object.assign(formattedResult, {
      nutritionalGoalComparison: (result.nutritionalSummary as any).nutritionalGoalComparison
    });
  }
  
  // Add comprehensive details if requested
  if (options.includeDetailsLevel === 'comprehensive') {
    Object.assign(formattedResult, {
      cuisineAnalysis: {
        cuisineType: typedResult.textualAnalysis.cuisineType,
        preparationMethod: typedResult.textualAnalysis.preparationMethod,
        mealType: typedResult.nutritionalSummary.mealType,
        mealOccasionSuggestion: typedResult.textualAnalysis.mealOccasionSuggestion || 'general meal',
        authenticity: typedResult.textualAnalysis.qualitativeAssessment?.authenticity
      },
      nutritionalAnalysis: {
        macronutrients: typedResult.nutritionalSummary.macronutrients,
        isBalancedMeal: typedResult.nutritionalSummary.isBalancedMeal,
        nutritionalQualityScore: typedResult.nutritionalSummary.nutritionalQualityScore,
        healthInsights: typedResult.textualAnalysis.healthInsights,
        dietaryCategories: typedResult.textualAnalysis.dietaryCategories,
        mealCompositionSuggestions: (typedResult.nutritionalSummary as any).mealCompositionSuggestions
      },
      volumeInfo: typedResult.volumeInfo.length > 0 ? {
        estimatedVolumeMl: typedResult.volumeInfo[0].volumeMl,
        portionSize: typedResult.volumeInfo[0].portionSize,
        confidenceScore: typedResult.volumeInfo[0].confidenceScore,
        userAdjusted: (typedResult.volumeInfo[0] as any).userAdjusted
      } : undefined,
      alternativeOptions: typedResult.textualAnalysis.alternativeOptions
    });
  }
  
  // Include AR markers if present
  if (typedResult.foodItems.some(item => (item as any).arMarker)) {
    Object.assign(formattedResult, {
      arMarkers: typedResult.foodItems
        .filter(item => (item as any).arMarker)
        .map(item => ({
          itemName: item.name,
          markerId: (item as any).arMarker!.id,
          cornerPoints: (item as any).arMarker!.cornerPoints
        }))
    });
  }
  
  // Include offline processing info if present
  if (typedResult.offlineProcessingInfo) {
    Object.assign(formattedResult, {
      offlineProcessing: {
        wasProcessedOffline: typedResult.offlineProcessingInfo.wasProcessedOffline,
        confidenceReduction: typedResult.offlineProcessingInfo.confidenceReduction,
        limitedFeaturesUsed: typedResult.offlineProcessingInfo.limitedFeaturesUsed
      }
    });
  }
  
  // Include progressive results info if present
  if (result.progressiveResults) {
    Object.assign(formattedResult, {
      progressive: {
        initialResultsTime: result.progressiveResults.initialResultsTime,
        fullResultsTime: result.progressiveResults.fullResultsTime,
        progressPercentage: result.progressiveResults.progressPercentage
      }
    });
  }
  
  return formattedResult;
} 