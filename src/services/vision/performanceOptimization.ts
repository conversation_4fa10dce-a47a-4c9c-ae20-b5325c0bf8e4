/**
 * Performance Optimization Service
 * 
 * Provides utilities for optimizing CPU and battery usage in vision analysis tasks.
 * Implements optimizations like lazy loading, throttling, memoization, and adaptive
 * quality settings based on device capabilities and battery level.
 */

import { Platform, NativeModules, AppState } from 'react-native';
import * as Battery from 'expo-battery';
import * as Device from 'expo-device';
import { BoundingBox } from '@/types/scanning';

interface DeviceCapabilities {
  cpuCores: number;
  totalMemory: number;
  isLowPowerDevice: boolean;
  batteryLevel: number;
  isLowBatteryMode: boolean;
  performanceLevel: 'low' | 'medium' | 'high';
}

let deviceCapabilities: DeviceCapabilities | null = null;
let lastCapabilitiesCheck = 0;
const CAPABILITIES_CHECK_INTERVAL = 60000; // 1 minute

/**
 * Get current device capabilities and performance profile
 * This is cached to avoid frequent expensive checks
 */
export async function getDeviceCapabilities(): Promise<DeviceCapabilities> {
  const now = Date.now();
  
  // Use cached values if recent enough
  if (deviceCapabilities && (now - lastCapabilitiesCheck < CAPABILITIES_CHECK_INTERVAL)) {
    return deviceCapabilities;
  }
  
  // Get CPU info
  let cpuCores = 2; // Default fallback
  let totalMemory = 2048; // Default fallback in MB
  let isLowPowerDevice = false;
  
  try {
    // Try to get device CPU/memory info
    if (Platform.OS === 'ios') {
      if (NativeModules.DeviceInfoModule) {
        const deviceInfo = await NativeModules.DeviceInfoModule.getDeviceInfo();
        cpuCores = deviceInfo.cpuCores || cpuCores;
        totalMemory = deviceInfo.totalMemory || totalMemory;
      }
    } else {
      // For Android we could use a different approach
      // This is a simplified example
      const deviceInfo = await Device.getDeviceTypeAsync();
      isLowPowerDevice = deviceInfo !== Device.DeviceType.PHONE;
    }
    
    // Detect low power devices based on cores and memory
    if (!isLowPowerDevice) {
      isLowPowerDevice = (cpuCores <= 2 || totalMemory < 2048);
    }
  } catch (error) {
    console.warn('Failed to get device CPU info:', error);
  }
  
  // Get battery info
  let batteryLevel = 1.0; // Default to full if can't detect
  let isLowBatteryMode = false;
  
  try {
    batteryLevel = await Battery.getBatteryLevelAsync();
    
    if (Platform.OS === 'ios') {
      // iOS has a low power mode we can detect
      isLowBatteryMode = await Battery.isLowPowerModeEnabledAsync();
    } else {
      // For Android we can use the battery level as a proxy
      isLowBatteryMode = batteryLevel < 0.2; // Less than 20%
    }
  } catch (error) {
    console.warn('Failed to get battery info:', error);
  }
  
  // Determine overall performance level
  let performanceLevel: 'low' | 'medium' | 'high' = 'medium';
  
  if (isLowPowerDevice || isLowBatteryMode || batteryLevel < 0.15) {
    performanceLevel = 'low';
  } else if (cpuCores >= 4 && totalMemory >= 4096 && batteryLevel > 0.5) {
    performanceLevel = 'high';
  }
  
  // Cache the results
  deviceCapabilities = {
    cpuCores,
    totalMemory,
    isLowPowerDevice,
    batteryLevel,
    isLowBatteryMode,
    performanceLevel
  };
  
  lastCapabilitiesCheck = now;
  return deviceCapabilities;
}

/**
 * Get the optimal quality level for image processing based on device capabilities
 */
export async function getOptimalQualityLevel(): Promise<{
  resolution: 'low' | 'medium' | 'high';
  processingQuality: 'low' | 'medium' | 'high';
  useBackgroundProcessing: boolean;
  batchSize: number;
  performanceLevel: 'low' | 'medium' | 'high';
}> {
  const capabilities = await getDeviceCapabilities();
  
  switch (capabilities.performanceLevel) {
    case 'low':
      return {
        resolution: 'low', // e.g., 480p or lower
        processingQuality: 'low',
        useBackgroundProcessing: true,
        batchSize: 1,
        performanceLevel: 'low'
      };
      
    case 'medium':
      return {
        resolution: 'medium', // e.g., 720p
        processingQuality: 'medium',
        useBackgroundProcessing: true,
        batchSize: 2,
        performanceLevel: 'medium'
      };
      
    case 'high':
      return {
        resolution: 'high', // e.g., 1080p
        processingQuality: 'high',
        useBackgroundProcessing: false, // Process in main thread for speed
        batchSize: 4,
        performanceLevel: 'high'
      };
  }
}

/**
 * Create throttled version of a function that limits call frequency
 * @param func Function to throttle
 * @param limit Time in ms to wait between function calls
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T, 
  limit: number
): (...args: Parameters<T>) => ReturnType<T> | undefined {
  let lastCall = 0;
  let lastResult: ReturnType<T>;
  
  return function(...args: Parameters<T>): ReturnType<T> | undefined {
    const now = Date.now();
    if (now - lastCall >= limit) {
      lastCall = now;
      lastResult = func(...args);
    }
    return lastResult;
  };
}

/**
 * Memoize a function to cache results based on input arguments
 * @param func Function to memoize
 */
export function memoize<T extends (...args: any[]) => any>(
  func: T
): T {
  const cache = new Map();
  
  return function(...args: Parameters<T>): ReturnType<T> {
    const key = JSON.stringify(args);
    if (cache.has(key)) {
      return cache.get(key);
    }
    
    const result = func(...args);
    cache.set(key, result);
    return result;
  } as T;
}

/**
 * Optimize image processing by downsizing based on device capabilities
 * @param imageData Original image data
 * @param processingFn Function to process the image
 */
export async function optimizedImageProcessing<T>(
  imageData: string | ArrayBuffer,
  processingFn: (optimizedImage: string | ArrayBuffer) => Promise<T>
): Promise<T> {
  const quality = await getOptimalQualityLevel();
  
  // In production, this would resize the image based on quality level
  // For now, we'll just pass it through
  let optimizedImage = imageData;
  
  // Process the image with the provided function
  return processingFn(optimizedImage);
}

/**
 * Adaptive region of interest (ROI) processing
 * Only processes the most important regions of an image to save CPU
 * @param imageData Image data
 * @param regions Regions of interest to process
 * @param processingFn Processing function for each region
 */
export async function processRegionsOfInterest<T>(
  imageData: string | ArrayBuffer,
  regions: BoundingBox[],
  processingFn: (regionData: { image: string | ArrayBuffer; region: BoundingBox }) => Promise<T>
): Promise<T[]> {
  // Get optimal settings based on device capabilities
  const quality = await getOptimalQualityLevel();
  
  // Calculate how many regions to process in parallel
  const batchSize = quality.batchSize;
  
  // Process regions in batches to avoid overloading the CPU
  const results: T[] = [];
  
  for (let i = 0; i < regions.length; i += batchSize) {
    const batch = regions.slice(i, i + batchSize);
    
    // Process this batch in parallel
    const batchResults = await Promise.all(
      batch.map(region => {
        // In a real implementation, we would extract just this region
        // For now, we'll pass the whole image with region info
        return processingFn({ image: imageData, region });
      })
    );
    
    results.push(...batchResults);
    
    // Add a small delay between batches on low-power devices
    if (quality.processingQuality === 'low' && i + batchSize < regions.length) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }
  
  return results;
}

/**
 * Register performance monitoring for a component or process
 * @param processName Name of the process to monitor
 */
export function monitorPerformance(processName: string) {
  let startTime = Date.now();
  let cpuUsage = 0;
  
  // In a real implementation, this would use the Performance API
  // or a native module to track more detailed metrics
  
  return {
    // Mark the start of a task
    start: () => {
      startTime = Date.now();
    },
    
    // Mark the end of a task and log performance
    end: () => {
      const duration = Date.now() - startTime;
      console.log(`Performance: ${processName} took ${duration}ms`);
      
      // Report long-running operations 
      if (duration > 500) {
        console.warn(`Long-running operation: ${processName} (${duration}ms)`);
      }
    }
  };
}

/**
 * Determines if heavy operations should be deferred based on app state
 */
export function shouldDeferHeavyOperation(): boolean {
  const currentAppState = AppState.currentState;
  
  // Defer if app is in background
  if (currentAppState !== 'active') {
    return true;
  }
  
  // Check battery level
  const lowBattery = deviceCapabilities?.isLowBatteryMode || 
                    (deviceCapabilities?.batteryLevel || 1) < 0.15;
  
  return lowBattery;
}

/**
 * Adaptive scanning configuration based on device capabilities
 */
export async function getOptimalScanningConfig(): Promise<{
  highResolutionScanning: boolean;
  adaptiveQualityMode: boolean;
  enableSceneReconstruction: boolean;
  environmentTexturing: boolean;
  useLowPowerMode: boolean;
  scanningFrequency: number;
  meshQuality: 'low' | 'medium' | 'high';
  objectTrackingQuality: 'low' | 'medium' | 'high';
}> {
  const capabilities = await getDeviceCapabilities();
  
  switch (capabilities.performanceLevel) {
    case 'low':
      return {
        highResolutionScanning: false,
        adaptiveQualityMode: true,
        enableSceneReconstruction: false,
        environmentTexturing: false,
        useLowPowerMode: true,
        scanningFrequency: 10, // Hz
        meshQuality: 'low',
        objectTrackingQuality: 'low'
      };
      
    case 'medium':
      return {
        highResolutionScanning: false,
        adaptiveQualityMode: true,
        enableSceneReconstruction: true,
        environmentTexturing: false,
        useLowPowerMode: false,
        scanningFrequency: 20, // Hz
        meshQuality: 'medium',
        objectTrackingQuality: 'medium'
      };
      
    case 'high':
      return {
        highResolutionScanning: true,
        adaptiveQualityMode: true,
        enableSceneReconstruction: true,
        environmentTexturing: true,
        useLowPowerMode: false,
        scanningFrequency: 30, // Hz
        meshQuality: 'high',
        objectTrackingQuality: 'high'
      };
  }
} 