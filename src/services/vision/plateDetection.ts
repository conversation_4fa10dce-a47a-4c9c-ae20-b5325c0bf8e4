import { BoundingBox } from './scanning';
import { analyzeImageEdges } from './edgeDetection';
import { calculateLightingConditions } from './imageAnalysis';

/**
 * Define valid tableware types
 */
export type TablewareType = 'dinner_plate' | 'salad_plate' | 'dessert_plate' | 
  'soup_bowl' | 'cereal_bowl' | 'small_bowl' | 'mug' | 'water_glass' | 'wine_glass';

/**
 * Standard dimensions for common tableware in centimeters
 */
export const STANDARD_TABLEWARE: Record<TablewareType, {
  diameter: number;
  height: number;
  volumeCm3: number;
}> = {
  // Plates
  dinner_plate: {
    diameter: 25.0, // cm
    height: 2.0,    // cm
    volumeCm3: 0    // Not used for volume displacement (negligible)
  },
  salad_plate: {
    diameter: 20.0, // cm
    height: 2.0,    // cm
    volumeCm3: 0    // Not used for volume displacement (negligible)
  },
  dessert_plate: {
    diameter: 18.0, // cm
    height: 1.5,    // cm
    volumeCm3: 0    // Not used for volume displacement (negligible)
  },
  
  // Bowls
  soup_bowl: {
    diameter: 16.0, // cm
    height: 4.0,    // cm
    volumeCm3: 500  // Approximate volume a soup bowl displaces
  },
  cereal_bowl: {
    diameter: 15.0, // cm
    height: 6.0,    // cm
    volumeCm3: 600  // Approximate volume a cereal bowl displaces
  },
  small_bowl: {
    diameter: 12.0, // cm
    height: 4.0,    // cm
    volumeCm3: 300  // Approximate volume a small bowl displaces
  },
  
  // Cups
  mug: {
    diameter: 8.0,  // cm
    height: 10.0,   // cm
    volumeCm3: 300  // Approximate volume
  },
  water_glass: {
    diameter: 7.0,  // cm
    height: 12.0,   // cm
    volumeCm3: 350  // Approximate volume
  },
  wine_glass: {
    diameter: 8.0,  // cm
    height: 15.0,   // cm
    volumeCm3: 250  // Approximate volume
  }
};

export interface PlateDetectionResult {
  type: string;
  confidence: number;
  boundingBox?: BoundingBox;
  diameter: number;  // in cm
  referenceObject: boolean;
  isPartiallyVisible?: boolean;
  edgeVisibility?: number; // 0-1 scale of how visible the plate edges are
  adjustedDiameter?: number; // Diameter after applying corrections
}

/**
 * Automatically detect plates and bowls in an image using computer vision
 * This can serve as a reference object for size calibration
 * 
 * @param imageData Image data from the scan
 * @param detectedObjects List of objects already detected in the image
 * @returns Information about detected plates/bowls
 */
export async function detectPlatesAndBowls(
  imageData: string | ArrayBuffer,
  detectedObjects: { className: string; boundingBox: BoundingBox; confidence: number }[]
): Promise<PlateDetectionResult[]> {
  let results: PlateDetectionResult[] = [];
  
  // Get lighting information for confidence adjustments
  const lightingConditions = await calculateLightingConditions(imageData);
  
  // Look for plates and bowls in already detected objects
  for (const object of detectedObjects) {
    const className = object.className.toLowerCase();
    
    if (
      className.includes('plate') || 
      className.includes('bowl') || 
      className.includes('dish') ||
      className.includes('saucer')
    ) {
      // Determine what type of tableware it is
      let type = 'dinner_plate'; // Default
      let confidence = object.confidence;
      
      if (className.includes('soup') || className.includes('deep')) {
        type = 'soup_bowl';
      } else if (className.includes('cereal')) {
        type = 'cereal_bowl';
      } else if (className.includes('small') && className.includes('bowl')) {
        type = 'small_bowl';
      } else if (className.includes('salad')) {
        type = 'salad_plate';
      } else if (className.includes('dessert')) {
        type = 'dessert_plate';
      }
      
      // Check edge visibility - reduces confidence if edges are obscured
      const edgeVisibility = await calculatePlateEdgeVisibility(imageData, object.boundingBox);
      
      // Check if plate is partially out of frame
      const isPartiallyVisible = isObjectPartiallyVisible(object.boundingBox);
      
      // Adjust confidence based on conditions
      confidence = adjustConfidenceForConditions(
        confidence,
        lightingConditions,
        edgeVisibility,
        isPartiallyVisible
      );
      
      // Calculate adjusted diameter if plate is partially visible
      let adjustedDiameter = STANDARD_TABLEWARE[type as TablewareType].diameter;
      if (isPartiallyVisible) {
        adjustedDiameter = estimateFullDiameterFromPartial(
          object.boundingBox,
          STANDARD_TABLEWARE[type as TablewareType].diameter
        );
      }
      
      // Add to results
      results.push({
        type,
        confidence,
        boundingBox: object.boundingBox,
        diameter: STANDARD_TABLEWARE[type as TablewareType].diameter,
        referenceObject: true,
        isPartiallyVisible,
        edgeVisibility,
        adjustedDiameter
      });
    }
  }
  
  // If no plates or bowls were explicitly detected, try to infer from the image
  if (results.length === 0) {
    // Look for circular objects that might be plates
    const circularObjects = await detectCircularObjects(imageData);
    
    for (const circular of circularObjects) {
      // Estimate what type of tableware it might be based on size relative to image
      const estimatedType = estimateTablewareType(circular.radius, circular.proportion);
      
      if (estimatedType) {
        // Check if plate is partially out of frame
        const isPartiallyVisible = isObjectPartiallyVisible(circular.boundingBox);
        
        // Check edge visibility
        const edgeVisibility = circular.edgeClarity;
        
        // Adjust confidence based on conditions
        const adjustedConfidence = adjustConfidenceForConditions(
          circular.confidence,
          lightingConditions,
          edgeVisibility,
          isPartiallyVisible
        );
        
        // Calculate adjusted diameter if plate is partially visible
        let adjustedDiameter = STANDARD_TABLEWARE[estimatedType as TablewareType].diameter;
        if (isPartiallyVisible) {
          adjustedDiameter = estimateFullDiameterFromPartial(
            circular.boundingBox,
            STANDARD_TABLEWARE[estimatedType as TablewareType].diameter
          );
        }
        
        results.push({
          type: estimatedType,
          confidence: adjustedConfidence,
          boundingBox: circular.boundingBox,
          diameter: STANDARD_TABLEWARE[estimatedType as TablewareType].diameter,
          referenceObject: true,
          isPartiallyVisible,
          edgeVisibility,
          adjustedDiameter
        });
      }
    }
  }
  
  // Filter out low confidence results or merge overlapping detections
  results = filterAndMergePlateDetections(results);
  
  return results;
}

/**
 * Detect circular objects that could be plates or bowls using enhanced methods
 */
async function detectCircularObjects(
  imageData: string | ArrayBuffer
): Promise<{
  radius: number;
  proportion: number;
  confidence: number;
  edgeClarity: number;
  boundingBox: BoundingBox;
}[]> {
  try {
    // Analyze image edges to detect potential circles
    const edgeAnalysis = await analyzeImageEdges(imageData);
    
    // Extract circular shapes from the edge analysis
    // (This would be implemented in the edge detection module)
    const detectedCircles = edgeAnalysis.circles || [];
    
    // Process each detected circle
    return detectedCircles.map(circle => {
      // Calculate proportion relative to image
      const imgWidth = edgeAnalysis.imageWidth || 1000; // fallback width
      const imgHeight = edgeAnalysis.imageHeight || 1000; // fallback height
      const proportion = Math.PI * circle.radius * circle.radius / (imgWidth * imgHeight);
      
      // Map circle to expected format
      return {
        radius: circle.radius,
        proportion,
        confidence: circle.confidence || 0.65,
        edgeClarity: circle.edgeClarity || 0.7,
        boundingBox: {
          x: (circle.centerX - circle.radius) / imgWidth,
          y: (circle.centerY - circle.radius) / imgHeight,
          width: (2 * circle.radius) / imgWidth,
          height: (2 * circle.radius) / imgHeight
        }
      };
    });
  } catch (error) {
    console.error('Error in circular object detection:', error);
    return [];
  }
}

/**
 * Estimate the type of tableware based on size metrics
 * 
 * @param radius Detected radius in pixels
 * @param proportion Proportion of image the object takes up
 * @returns Type of tableware or undefined if not recognized
 */
function estimateTablewareType(
  radius: number,
  proportion: number
): string | undefined {
  // Improved heuristic with more detailed size ranges
  
  // Very large circular objects are likely dinner plates
  if (proportion > 0.3) {
    return 'dinner_plate';
  }
  
  // Medium-large circular objects could be salad plates
  if (proportion > 0.2) {
    return 'salad_plate'; 
  }
  
  // Medium circular objects could be soup bowls
  if (proportion > 0.15) {
    return 'soup_bowl';
  }
  
  // Smaller circular objects could be cereal bowls
  if (proportion > 0.12) {
    return 'cereal_bowl';
  }
  
  // Small circular objects could be small bowls
  if (proportion > 0.08) {
    return 'small_bowl';
  }
  
  // Very small circular objects could be mugs
  if (proportion > 0.05) {
    return 'mug';
  }
  
  // Too small to reliably identify
  return undefined;
}

/**
 * Use plate detection to calibrate the size of food items with improved accuracy
 * 
 * @param foodBoundingBox Bounding box of the food item
 * @param plateDetection Detected plate information
 * @param imageWidth Image width in pixels
 * @param imageHeight Image height in pixels
 * @returns Estimated dimensions of the food in cm
 */
export function calibrateSizeWithPlate(
  foodBoundingBox: BoundingBox,
  plateDetection: PlateDetectionResult,
  imageWidth: number,
  imageHeight: number
): { widthCm: number; heightCm: number; areaCm2: number } {
  // Calculate the diameter of the plate in pixels
  const plateBB = plateDetection.boundingBox;
  if (!plateBB) {
    throw new Error('Plate bounding box is required for calibration');
  }
  
  // Use the adjusted diameter if plate is partially visible
  const plateDiameterCm = plateDetection.adjustedDiameter || plateDetection.diameter;
  
  // Calculate plate diameter in pixels (average of width and height)
  const plateDiameterPx = (plateBB.width * imageWidth + plateBB.height * imageHeight) / 2;
  
  // Calculate the scale factor (cm/px)
  const scaleFactor = plateDiameterCm / plateDiameterPx;
  
  // Convert food bounding box to cm
  const widthCm = foodBoundingBox.width * imageWidth * scaleFactor;
  const heightCm = foodBoundingBox.height * imageHeight * scaleFactor;
  
  return {
    widthCm,
    heightCm,
    areaCm2: widthCm * heightCm
  };
}

/**
 * Calculate the visibility of plate edges based on image analysis
 * 
 * @param imageData Image data to analyze
 * @param boundingBox Bounding box of the detected plate
 * @returns A score (0-1) indicating how visible the plate edges are
 */
async function calculatePlateEdgeVisibility(
  imageData: string | ArrayBuffer,
  boundingBox: BoundingBox
): Promise<number> {
  try {
    // This would call into a separate module that does edge detection
    // For now, we'll simulate a result
    const edgeAnalysis = await analyzeImageEdges(imageData, boundingBox);
    return edgeAnalysis.edgeVisibility || 0.8;
  } catch (error) {
    console.error('Error calculating plate edge visibility:', error);
    return 0.7; // Default reasonable value
  }
}

/**
 * Check if an object is partially out of the frame
 * 
 * @param boundingBox Bounding box of the object
 * @returns True if object appears to be partially out of frame
 */
function isObjectPartiallyVisible(boundingBox: BoundingBox): boolean {
  // Objects that touch the edge of the frame are likely partially visible
  const margin = 0.02; // 2% margin
  
  return (
    boundingBox.x < margin ||
    boundingBox.y < margin ||
    boundingBox.x + boundingBox.width > 1 - margin ||
    boundingBox.y + boundingBox.height > 1 - margin
  );
}

/**
 * Adjust confidence based on lighting and other conditions
 * 
 * @param baseConfidence Initial confidence score
 * @param lightingConditions Lighting analysis results
 * @param edgeVisibility Edge visibility score
 * @param isPartiallyVisible Whether object is partially out of frame
 * @returns Adjusted confidence score
 */
function adjustConfidenceForConditions(
  baseConfidence: number,
  lightingConditions: { quality: string; evenness: number },
  edgeVisibility: number,
  isPartiallyVisible: boolean
): number {
  let adjustedConfidence = baseConfidence;
  
  // Reduce confidence for poor lighting
  if (lightingConditions.quality === 'poor') {
    adjustedConfidence *= 0.8;
  } else if (lightingConditions.quality === 'uneven' && lightingConditions.evenness < 0.7) {
    adjustedConfidence *= 0.9;
  }
  
  // Reduce confidence for low edge visibility
  adjustedConfidence *= Math.max(0.5, edgeVisibility);
  
  // Reduce confidence for partially visible objects
  if (isPartiallyVisible) {
    adjustedConfidence *= 0.8;
  }
  
  return Math.min(1, Math.max(0.1, adjustedConfidence));
}

/**
 * Estimate the full diameter of a partially visible circular object
 * 
 * @param boundingBox Detected bounding box of the partially visible object
 * @param standardDiameter Standard diameter for this type of object
 * @returns Estimated full diameter
 */
function estimateFullDiameterFromPartial(
  boundingBox: BoundingBox,
  standardDiameter: number
): number {
  // Check which edge the circle is touching
  const touchingLeft = boundingBox.x < 0.05;
  const touchingRight = boundingBox.x + boundingBox.width > 0.95;
  const touchingTop = boundingBox.y < 0.05;
  const touchingBottom = boundingBox.y + boundingBox.height > 0.95;
  
  // If touching multiple edges, use a more aggressive correction
  const touchCount = 
    (touchingLeft ? 1 : 0) + 
    (touchingRight ? 1 : 0) + 
    (touchingTop ? 1 : 0) + 
    (touchingBottom ? 1 : 0);
  
  if (touchCount === 0) {
    return standardDiameter; // Not touching any edge
  }
  
  // Calculate the aspect ratio to determine how much is cut off
  const aspectRatio = boundingBox.width / boundingBox.height;
  
  // For a circular object, aspect ratio should be close to 1
  // Deviation from 1 indicates how much is cut off
  const aspectDeviation = Math.abs(1 - aspectRatio);
  
  // Calculate corrected diameter with a scaling formula based on deviation
  // More deviation = more aggressive correction
  const correctionFactor = 1 + (aspectDeviation * touchCount * 0.5);
  
  return standardDiameter * correctionFactor;
}

/**
 * Filter low confidence detections and merge overlapping plate detections
 * 
 * @param detections Array of plate detection results
 * @returns Filtered and merged plate detections
 */
function filterAndMergePlateDetections(
  detections: PlateDetectionResult[]
): PlateDetectionResult[] {
  // Filter out very low confidence results
  const filtered = detections.filter(d => d.confidence > 0.3);
  
  // Sort by confidence
  filtered.sort((a, b) => b.confidence - a.confidence);
  
  // Initialize result array
  const merged: PlateDetectionResult[] = [];
  const used = new Set<number>();
  
  // Merge overlapping detections
  for (let i = 0; i < filtered.length; i++) {
    if (used.has(i)) continue;
    
    const current = filtered[i];
    let overlapCount = 0;
    let totalConfidence = current.confidence;
    let bestBoundingBox = current.boundingBox;
    
    // Find all overlapping detections
    for (let j = i + 1; j < filtered.length; j++) {
      if (used.has(j)) continue;
      
      const other = filtered[j];
      
      // Skip if types don't match or no bounding boxes
      if (current.type !== other.type || !current.boundingBox || !other.boundingBox) continue;
      
      // Check for significant overlap
      if (calculateBoxOverlap(current.boundingBox, other.boundingBox) > 0.5) {
        overlapCount++;
        totalConfidence += other.confidence;
        
        // Keep the bounding box with higher confidence
        if (other.confidence > current.confidence && !other.isPartiallyVisible) {
          bestBoundingBox = other.boundingBox;
        }
        
        used.add(j);
      }
    }
    
    // Create merged detection
    merged.push({
      ...current,
      boundingBox: bestBoundingBox,
      confidence: Math.min(1.0, totalConfidence / (overlapCount + 1) * (1 + overlapCount * 0.1)),
    });
    
    used.add(i);
  }
  
  return merged;
}

/**
 * Calculate overlap between two bounding boxes
 * 
 * @param box1 First bounding box
 * @param box2 Second bounding box
 * @returns Intersection over Union (IoU) score
 */
function calculateBoxOverlap(box1: BoundingBox, box2: BoundingBox): number {
  // Calculate coordinates of intersection
  const xLeft = Math.max(box1.x, box2.x);
  const yTop = Math.max(box1.y, box2.y);
  const xRight = Math.min(box1.x + box1.width, box2.x + box2.width);
  const yBottom = Math.min(box1.y + box1.height, box2.y + box2.height);
  
  // Check if there is an intersection
  if (xRight < xLeft || yBottom < yTop) {
    return 0;
  }
  
  // Calculate intersection area
  const intersectionArea = (xRight - xLeft) * (yBottom - yTop);
  
  // Calculate union area
  const box1Area = box1.width * box1.height;
  const box2Area = box2.width * box2.height;
  const unionArea = box1Area + box2Area - intersectionArea;
  
  // Return IoU
  return intersectionArea / unionArea;
} 