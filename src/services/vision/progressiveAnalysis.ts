import { UnifiedFoodAnalysisResult } from './types';
import { AnalysisCacheService } from './cacheService';

// Configurable timeout constants
const EARLY_TIMEOUT = 5000;    // Increased from 4000ms to 5000ms
const INITIAL_TIMEOUT = 6000;  // Increased from 4000ms to 6000ms
const DETAILED_TIMEOUT = 8000; // Increased from 4000ms to 8000ms

/**
 * Creates a progressive analyzer that can provide incremental analysis updates
 */
export function createProgressiveAnalyzer() {
  // Array of callbacks to trigger for progressive updates
  const callbacks: ((result: UnifiedFoodAnalysisResult) => void)[] = [];
  
  /**
   * Register a callback to receive progressive analysis updates
   */
  function registerCallback(callback: (result: UnifiedFoodAnalysisResult) => void) {
    callbacks.push(callback);
    return callback;
  }
  
  /**
   * Process an image with progressive updates
   */
  async function analyzeProgressively(
    imageData: string,
    options: any,
    callback?: (result: UnifiedFoodAnalysisResult) => void
  ): Promise<UnifiedFoodAnalysisResult> {
    // Start timer to track processing time
    const startTime = Date.now();
    
    // Storage for intermediate results
    let lastResult: UnifiedFoodAnalysisResult | null = null;
    
    // Check cache first
    try {
      const cacheService = AnalysisCacheService.getInstance();
      const cachedResult = await cacheService.getCachedAnalysisResult(imageData, JSON.stringify(options));
      
      if (cachedResult) {
        console.log('Found cached analysis result, serving immediately');
        // Serve the cached result immediately but still try to do a fresh analysis
        if (callback) callback(cachedResult.result);
        
        // If options specifically request cache only, return the cached result
        if (options?.preferCacheOnly) {
          return cachedResult.result;
        }
      }
    } catch (cacheError) {
      console.warn('Error checking analysis cache:', cacheError);
    }
    
    // Helper function to send progressive updates to all callbacks
    function sendProgressiveUpdate(result: UnifiedFoodAnalysisResult) {
      // Store the last result for return value
      lastResult = result;
      
      // Mark result as partial and update timestamp
      result.meta.processingTimeMs = Date.now() - startTime;
      result.meta.isPartialResult = true;
      
      // Cache the intermediate result if it has some valuable data
      if (result.foodItems && result.foodItems.length > 0) {
        try {
          const cacheService = AnalysisCacheService.getInstance();
          const cacheKey = `intermediate-${Date.now()}`;
          cacheService.cacheAnalysisResult(imageData, result, cacheKey);
        } catch (cacheError) {
          console.warn('Failed to cache intermediate result:', cacheError);
        }
      }
      
      // Send to all registered callbacks
      callbacks.forEach(cb => cb(result));
      
      // If a specific callback was provided to this call, use it too
      if (callback) callback(result);
    }
    
    // Define early timeout ID
    let earlyTimeoutId: NodeJS.Timeout | null = null;
    
    try {
      // Step 1: Create timeout with early basic result
      const earlyTimeoutPromise = new Promise<UnifiedFoodAnalysisResult>((resolve) => {
        earlyTimeoutId = setTimeout(() => {
          console.warn('Early analysis timeout reached - sending basic results');
          resolve(getEmptyProgressiveResult('Basic analysis timeout reached'));
        }, EARLY_TIMEOUT); // Increased timeout for very early basic results
      });
      
      // Step 2: Fast initial pass - basic food recognition (30% progress)
      // Use a simplified version of the analyzer for quick results
      const initialResultPromise = performInitialAnalysis(imageData);
      
      // Create a timeout promise for the initial analysis
      const initialTimeout = new Promise<UnifiedFoodAnalysisResult>((resolve) => {
        setTimeout(() => {
          console.warn('Initial analysis timeout - using empty result');
          resolve(getEmptyProgressiveResult('Initial analysis timeout'));
        }, INITIAL_TIMEOUT); // Increased timeout for initial analysis
      });
      
      // Use race to get either the real result or timeout result
      const initialResult = await Promise.race([initialResultPromise, initialTimeout]);
      initialResult.progressiveResults = {
        stage: 'initial',
        initialResultsTime: Date.now() - startTime,
        fullResultsTime: 0,
        progressPercentage: 30
      };
      
      // Save reference to the result for the timeout handler
      lastResult = initialResult;
      
      // Send update to all registered callbacks
      sendProgressiveUpdate(initialResult);
      
      // Step 3: Start detailed analysis in parallel
      const { unifiedAnalyzeFoodImage } = await import('./advancedVision');
      
      // Create a timeout promise for the detailed analysis
      const detailedTimeout = new Promise<UnifiedFoodAnalysisResult>((resolve) => {
        setTimeout(() => {
          console.warn('Detailed analysis timeout - using intermediate result');
          // Use the best result we have so far and mark it as a timeout
          const result = lastResult || getEmptyProgressiveResult('Detailed analysis timeout');
          result.meta.timeoutOccurred = true;
          result.meta.processingTimeMs = Date.now() - startTime;
          // Add a user-friendly message to the result
          if (!result.textualAnalysis.description.includes('timeout')) {
            result.textualAnalysis.description = 
              `${result.textualAnalysis.description} (Analysis timeout occurred - showing best available results)`;
          }
          resolve(result);
        }, DETAILED_TIMEOUT); // Increased timeout for detailed analysis 
      });
      
      // Start the detailed analysis in parallel with a failsafe timeout
      const detailedResultPromise = Promise.race([
        unifiedAnalyzeFoodImage(imageData, options),
        // Add an absolute maximum timeout that will never be hit in normal operation
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Maximum analysis time exceeded')), 30000)
        )
      ]);
      
      // Step 4: Intermediate pass with more details (70% progress)
      // Wrap this in a try/catch so a failure here doesn't stop the whole process
      let intermediateResult = initialResult;
      try {
        intermediateResult = await enhanceWithIntermediateDetails(initialResult, imageData);
        intermediateResult.progressiveResults = {
          stage: 'basic',
          initialResultsTime: initialResult.progressiveResults?.initialResultsTime,
          fullResultsTime: 0,
          progressPercentage: 70
        };
        
        // Update our last result reference
        lastResult = intermediateResult;
        
        // Send update with intermediate results
        sendProgressiveUpdate(intermediateResult);
      } catch (intermediateError) {
        console.warn('Error enhancing intermediate results, continuing with initial results:', intermediateError);
        // Keep using initialResult as our best result
      }
      
      // Step 5: Wait for full detailed analysis to complete or timeout
      let fullResult: UnifiedFoodAnalysisResult;
      try {
        fullResult = await Promise.race([detailedResultPromise, detailedTimeout]);
      } catch (detailedError) {
        console.warn('Error in detailed analysis, using best available results:', detailedError);
        // Use the best result we have so far
        fullResult = lastResult || getEmptyProgressiveResult('Detailed analysis failed');
        fullResult.meta.error = detailedError instanceof Error ? detailedError.message : 'Unknown error in detailed analysis';
        fullResult.meta.timeoutOccurred = true;
      }
      
      // Make sure we have at least some content in the result
      if (!fullResult.foodItems || fullResult.foodItems.length === 0) {
        console.warn('Full analysis returned no food items, using intermediate results');
        // If no results in the full analysis, use the intermediate ones
        if (intermediateResult.foodItems && intermediateResult.foodItems.length > 0) {
          intermediateResult.meta.timeoutOccurred = true;
          intermediateResult.meta.isPartialResult = true;
          return intermediateResult;
        }
      }
      
      // Clear the timeout since we got a result (either real or timeout-induced)
      if (earlyTimeoutId) {
        clearTimeout(earlyTimeoutId);
      }
      
      // Preserve progressive info in full result
      fullResult.progressiveResults = {
        stage: 'complete',
        initialResultsTime: initialResult.progressiveResults?.initialResultsTime,
        fullResultsTime: Date.now() - startTime,
        progressPercentage: fullResult.meta.timeoutOccurred ? 70 : 100
      };
      
      // Cache the final result for future use
      try {
        const cacheService = AnalysisCacheService.getInstance();
        await cacheService.cacheAnalysisResult(imageData, fullResult, JSON.stringify(options));
        console.log('Cached final analysis result for future use');
      } catch (cacheError) {
        console.warn('Failed to cache final result:', cacheError);
      }
      
      // Send final update
      sendProgressiveUpdate(fullResult);
      
      return fullResult;
    } catch (error) {
      console.error('Error in progressive analysis:', error);
      
      // Clear the timeout if it exists
      if (earlyTimeoutId) {
        clearTimeout(earlyTimeoutId);
      }
      
      // Return the last result we had if available, otherwise create a new error result
      if (lastResult) {
        lastResult.meta.error = error instanceof Error ? error.message : 'Unknown error in progressive analysis';
        lastResult.meta.processingTimeMs = Date.now() - startTime;
        
        // Add user-friendly message
        if (!lastResult.textualAnalysis.description.includes('error')) {
          lastResult.textualAnalysis.description += ' (Error occurred during analysis - showing partial results)';
        }
        
        return lastResult;
      }
      
      // Create a basic error result if we don't have any partial results
      const errorResult = getEmptyProgressiveResult();
      errorResult.meta = {
        analysisTimestamp: Date.now(),
        modelVersion: 'progressive-2025.03.1',
        confidenceScore: 0,
        processingTimeMs: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error in progressive analysis'
      };
      
      sendProgressiveUpdate(errorResult);
      return errorResult;
    }
  }
  
  /**
   * Trigger all registered callbacks with updated result
   */
  function triggerCallbacks(result: UnifiedFoodAnalysisResult) {
    for (const callback of callbacks) {
      try {
        callback({...result}); // Send copy to avoid mutation issues
      } catch (error) {
        console.error('Error in progressive analysis callback:', error);
      }
    }
  }
  
  return {
    analyzeProgressively,
    registerCallback
  };
}

/**
 * Perform quick initial analysis for early results
 */
async function performInitialAnalysis(imageData: string): Promise<UnifiedFoodAnalysisResult> {
  try {
    // Import only what's needed for basic detection
    const { detectLabelsFromImage } = await import('./visionApi');
    const { isFoodCategory } = await import('./utils');
    
    // Get basic labels from the fast API
    const labels = await detectLabelsFromImage(imageData);
    
    // Filter for food items only
    const foodLabels = labels.filter(label => 
      isFoodCategory(label.description) && label.score > 0.4
    );
    
    if (foodLabels.length === 0) {
      return getEmptyProgressiveResult('No food detected');
    }
    
    // Create a basic result with minimal information
    const result: UnifiedFoodAnalysisResult = {
      foodItems: foodLabels.map(label => ({
        name: label.description,
        confidence: label.score
      })),
      imageProperties: {
        dominantColors: ['#CCCCCC', '#999999', '#666666'],
        brightness: 0.5,
        contrast: 0.5,
        sharpness: 0.5,
        hasPlate: true
      },
      detailedFoodInfo: [],
      volumeInfo: [],
      nutritionalSummary: {
        totalCalories: estimateCalories(foodLabels[0]?.description || 'food'),
        macronutrients: {
          protein: 0,
          carbs: 0,
          fat: 0,
          fiber: 0
        },
        mealType: 'unknown',
        isBalancedMeal: false,
        nutritionalQualityScore: 5
      },
      textualAnalysis: {
        description: foodLabels.slice(0, 3).map(l => l.description).join(', '),
        preparationMethod: '',
        dietaryCategories: [],
        cuisineType: '',
        healthInsights: []
      },
      meta: {
        analysisTimestamp: Date.now(),
        modelVersion: 'progressive-initial-2025.03.1',
        confidenceScore: foodLabels.length > 0 ? foodLabels[0].score : 0,
        processingTimeMs: 0
      }
    };
    
    return result;
  } catch (error) {
    console.error('Error in initial analysis:', error);
    return getEmptyProgressiveResult('Error in initial analysis');
  }
}

/**
 * Enhance initial result with some intermediate details
 * This provides more information before the full analysis completes
 */
async function enhanceWithIntermediateDetails(
  initialResult: UnifiedFoodAnalysisResult,
  imageData: string
): Promise<UnifiedFoodAnalysisResult> {
  try {
    // Clone the initial result to avoid mutation
    const enhancedResult = JSON.parse(JSON.stringify(initialResult)) as UnifiedFoodAnalysisResult;
    
    // Get the main food item from initial detection
    const mainFoodItem = enhancedResult.foodItems[0]?.name || 'Unknown food';
    
    // Add a placeholder nutrition estimate based on the food type
    if (enhancedResult.foodItems.length > 0) {
      const estimatedNutrition = estimateNutrition(mainFoodItem);
      
      enhancedResult.nutritionalSummary = {
        ...enhancedResult.nutritionalSummary,
        totalCalories: estimatedNutrition.calories,
        macronutrients: {
          protein: estimatedNutrition.protein,
          carbs: estimatedNutrition.carbs,
          fat: estimatedNutrition.fat,
          fiber: estimatedNutrition.fiber
        }
      };
      
      // Add basic food details
      enhancedResult.detailedFoodInfo = [{
        mainCategory: getCategoryFromFood(mainFoodItem),
        specificDish: mainFoodItem,
        ingredients: getCommonIngredients(mainFoodItem),
        cookingMethod: guessCookingMethod(mainFoodItem),
        cuisineOrigin: guessCuisine(mainFoodItem),
        isFreshOrProcessed: guessFreshnessType(mainFoodItem),
        nutrition: {
          calories: estimatedNutrition.calories,
          protein: estimatedNutrition.protein,
          carbs: estimatedNutrition.carbs,
          fat: estimatedNutrition.fat,
          fiber: estimatedNutrition.fiber
        },
        confidenceScore: enhancedResult.foodItems[0].confidence
      }];
      
      // Add better description
      enhancedResult.textualAnalysis.description = 
        `${mainFoodItem}, which typically contains ${getCommonIngredients(mainFoodItem).slice(0, 3).join(', ')}.`;
    }
    
    // Extract some basic image properties
    try {
      // In real implementation, we'd do a quick analysis of the image here
      // For this demo, just use simulated values
      enhancedResult.imageProperties = {
        dominantColors: [
          '#' + Math.floor(Math.random()*16777215).toString(16),
          '#' + Math.floor(Math.random()*16777215).toString(16),
          '#' + Math.floor(Math.random()*16777215).toString(16)
        ],
        brightness: 0.3 + Math.random() * 0.7,
        contrast: 0.4 + Math.random() * 0.6,
        sharpness: 0.5 + Math.random() * 0.5,
        hasPlate: Math.random() > 0.3
      };
    } catch (e) {
      // Just keep the original image properties
    }
    
    // Add a volume estimate
    enhancedResult.volumeInfo = [{
      volumeMl: 200 + Math.floor(Math.random() * 300),
      confidenceScore: 0.6,
      estimationMethod: 'quick_estimate',
      portionSize: 'medium'
    }];
    
    return enhancedResult;
  } catch (error) {
    console.error('Error enhancing with intermediate details:', error);
    return initialResult; // Return the original result if enhancement fails
  }
}

/**
 * Get an empty result for progressive analysis
 */
function getEmptyProgressiveResult(errorMessage: string = ''): UnifiedFoodAnalysisResult {
  return {
    foodItems: [],
    imageProperties: {
      dominantColors: ['#CCCCCC', '#999999', '#666666'],
      brightness: 0.5,
      contrast: 0.5,
      sharpness: 0.5,
      hasPlate: false
    },
    detailedFoodInfo: [],
    volumeInfo: [],
    nutritionalSummary: {
      totalCalories: 0,
      macronutrients: {
        protein: 0,
        carbs: 0,
        fat: 0,
        fiber: 0
      },
      mealType: 'unknown',
      isBalancedMeal: false,
      nutritionalQualityScore: 0
    },
    textualAnalysis: {
      description: errorMessage || 'No food detected in image',
      preparationMethod: '',
      dietaryCategories: [],
      cuisineType: '',
      healthInsights: []
    },
    meta: {
      analysisTimestamp: Date.now(),
      modelVersion: 'progressive-2025.03.1',
      confidenceScore: 0,
      processingTimeMs: 0
    },
    progressiveResults: {
      stage: 'initial',
      initialResultsTime: 0,
      fullResultsTime: 0,
      progressPercentage: 0
    }
  };
}

// Helper functions for quick estimations

function estimateCalories(food: string): number {
  // Very rough estimates for demo purposes
  const foodLower = food.toLowerCase();
  
  // Main meals and combo dishes
  if (foodLower.includes('burger')) return 650;
  if (foodLower.includes('pizza')) {
    if (foodLower.includes('slice')) return 300;
    return 800; // Assuming multiple slices or a whole pizza
  }
  if (foodLower.includes('sandwich')) return 450;
  if (foodLower.includes('wrap')) return 400;
  if (foodLower.includes('burrito')) return 700;
  if (foodLower.includes('taco')) return 250;
  if (foodLower.includes('pasta')) return 500;
  if (foodLower.includes('noodle')) return 450;
  if (foodLower.includes('curry')) return 600;
  if (foodLower.includes('stir fry')) return 450;
  if (foodLower.includes('fried rice')) return 550;
  
  // Protein mains
  if (foodLower.includes('steak')) return 500;
  if (foodLower.includes('beef')) return 350;
  if (foodLower.includes('chicken breast')) return 200;
  if (foodLower.includes('chicken')) return 300;
  if (foodLower.includes('pork')) return 350;
  if (foodLower.includes('fish')) return 250;
  if (foodLower.includes('salmon')) return 300;
  if (foodLower.includes('tuna')) return 180;
  
  // Sides and carbs
  if (foodLower.includes('fries')) return 400;
  if (foodLower.includes('potato')) return 200;
  if (foodLower.includes('rice')) return 200;
  if (foodLower.includes('bread')) return 100;
  if (foodLower.includes('toast')) return 80;
  
  // Vegetables and salads
  if (foodLower.includes('salad')) {
    if (foodLower.includes('caesar') || foodLower.includes('chicken')) return 350;
    return 150; // Basic salad
  }
  if (foodLower.includes('vegetable')) return 80;
  if (foodLower.includes('broccoli')) return 50;
  if (foodLower.includes('carrot')) return 50;
  if (foodLower.includes('spinach')) return 40;
  
  // Breakfast items
  if (foodLower.includes('pancake')) return 350;
  if (foodLower.includes('waffle')) return 400;
  if (foodLower.includes('egg')) return 80;
  if (foodLower.includes('omelette')) return 300;
  if (foodLower.includes('cereal')) return 250;
  if (foodLower.includes('oatmeal')) return 300;
  if (foodLower.includes('yogurt')) return 150;
  
  // Soups
  if (foodLower.includes('soup')) {
    if (foodLower.includes('cream')) return 300;
    return 200; // Clear soups
  }
  
  // Desserts and sweets
  if (foodLower.includes('cake')) return 400;
  if (foodLower.includes('cookie')) return 200;
  if (foodLower.includes('ice cream')) return 300;
  if (foodLower.includes('dessert')) return 350;
  if (foodLower.includes('chocolate')) return 250;
  
  // Fruits
  if (foodLower.includes('fruit')) return 100;
  if (foodLower.includes('apple')) return 80;
  if (foodLower.includes('banana')) return 110;
  if (foodLower.includes('orange')) return 70;
  if (foodLower.includes('berry') || foodLower.includes('berries')) return 70;
  
  // Baked goods
  if (foodLower.includes('muffin')) return 350;
  if (foodLower.includes('pastry')) return 400;
  if (foodLower.includes('croissant')) return 350;
  if (foodLower.includes('donut')) return 300;
  
  // Snacks
  if (foodLower.includes('chips')) return 300;
  if (foodLower.includes('nuts')) return 200;
  if (foodLower.includes('popcorn')) return 150;
  
  // Beverages
  if (foodLower.includes('soda') || foodLower.includes('coke')) return 150;
  if (foodLower.includes('smoothie')) return 250;
  if (foodLower.includes('milkshake')) return 500;
  if (foodLower.includes('juice')) return 120;
  if (foodLower.includes('coffee')) {
    if (foodLower.includes('latte') || foodLower.includes('cappuccino')) return 150;
    return 5; // Black coffee
  }
  
  // Fast food
  if (foodLower.includes('mcdonalds') || foodLower.includes('mcdonald')) return 700;
  if (foodLower.includes('kfc')) return 800;
  if (foodLower.includes('subway')) return 500;
  
  // Meal types by size
  if (foodLower.includes('breakfast')) return 500;
  if (foodLower.includes('lunch')) return 700;
  if (foodLower.includes('dinner')) return 800;
  if (foodLower.includes('snack')) return 200;
  
  // Cuisine types (if only cuisine is mentioned)
  if (foodLower.includes('mexican')) return 700;
  if (foodLower.includes('italian')) return 800;
  if (foodLower.includes('chinese')) return 700;
  if (foodLower.includes('japanese')) return 600;
  if (foodLower.includes('indian')) return 700;
  if (foodLower.includes('thai')) return 650;
  
  // Generic meal terms
  if (foodLower.includes('meal')) return 700;
  if (foodLower.includes('dish')) return 600;
  if (foodLower.includes('plate')) return 650;
  if (foodLower.includes('bowl')) return 550;
  
  // Default value for unknown foods
  return 300;
}

function estimateNutrition(food: string): {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber: number;
} {
  const foodLower = food.toLowerCase();
  const calories = estimateCalories(food);
  
  // Define default macronutrient ratios (percentage of total calories)
  let proteinPct = 0.2;  // 20% from protein
  let carbsPct = 0.5;    // 50% from carbs  
  let fatPct = 0.3;      // 30% from fat
  let fiberGrams = 3;    // Default fiber amount
  
  // Adjust macronutrient ratios based on food type
  
  // High protein foods
  if (foodLower.includes('steak') || foodLower.includes('beef') || 
      foodLower.includes('chicken') || foodLower.includes('fish') || 
      foodLower.includes('pork') || foodLower.includes('egg') ||
      foodLower.includes('protein')) {
    proteinPct = 0.45;
    carbsPct = 0.1;
    fatPct = 0.45;
    fiberGrams = 0;
  } 
  // High carb foods
  else if (foodLower.includes('pasta') || foodLower.includes('rice') || 
          foodLower.includes('bread') || foodLower.includes('cereal') || 
          foodLower.includes('potato') || foodLower.includes('fries') ||
          foodLower.includes('noodle')) {
    proteinPct = 0.12;
    carbsPct = 0.7;
    fatPct = 0.18;
    fiberGrams = 3;
  }
  // High fat foods
  else if (foodLower.includes('cheese') || foodLower.includes('bacon') || 
          foodLower.includes('nuts') || foodLower.includes('oil') ||
          foodLower.includes('butter') || foodLower.includes('cream')) {
    proteinPct = 0.15;
    carbsPct = 0.15;
    fatPct = 0.7;
    fiberGrams = 1;
  }
  // Fruits
  else if (foodLower.includes('fruit') || foodLower.includes('apple') || 
          foodLower.includes('banana') || foodLower.includes('berry') ||
          foodLower.includes('orange')) {
    proteinPct = 0.05;
    carbsPct = 0.9;
    fatPct = 0.05;
    fiberGrams = 4;
  }
  // Vegetables
  else if (foodLower.includes('vegetable') || foodLower.includes('broccoli') || 
          foodLower.includes('carrot') || foodLower.includes('spinach') ||
          foodLower.includes('salad')) {
    proteinPct = 0.15;
    carbsPct = 0.7;
    fatPct = 0.15;
    fiberGrams = 5;
  }
  // Fast food
  else if (foodLower.includes('burger') || foodLower.includes('pizza') || 
          foodLower.includes('fries') || foodLower.includes('mcdonalds') ||
          foodLower.includes('kfc') || foodLower.includes('fast food')) {
    proteinPct = 0.18;
    carbsPct = 0.42;
    fatPct = 0.4;
    fiberGrams = 2;
  }
  // Desserts
  else if (foodLower.includes('cake') || foodLower.includes('cookie') || 
          foodLower.includes('ice cream') || foodLower.includes('dessert') ||
          foodLower.includes('chocolate') || foodLower.includes('sweet')) {
    proteinPct = 0.08;
    carbsPct = 0.65;
    fatPct = 0.27;
    fiberGrams = 1;
  }
  // Mexican food
  else if (foodLower.includes('mexican') || foodLower.includes('taco') || 
          foodLower.includes('burrito') || foodLower.includes('quesadilla')) {
    proteinPct = 0.2;
    carbsPct = 0.5;
    fatPct = 0.3;
    fiberGrams = 6;
  }
  // Asian food
  else if (foodLower.includes('chinese') || foodLower.includes('japanese') || 
          foodLower.includes('thai') || foodLower.includes('asian') ||
          foodLower.includes('stir fry') || foodLower.includes('fried rice')) {
    proteinPct = 0.25;
    carbsPct = 0.55;
    fatPct = 0.2;
    fiberGrams = 3;
  }
  
  // Calculate macros based on percentages and calories
  // Protein and carbs = 4 calories per gram, fat = 9 calories per gram
  const protein = Math.round((calories * proteinPct) / 4);
  const carbs = Math.round((calories * carbsPct) / 4);
  const fat = Math.round((calories * fatPct) / 9);
  
  return {
    calories,
    protein,
    carbs,
    fat,
    fiber: fiberGrams
  };
}

function getCategoryFromFood(food: string): string {
  const foodLower = food.toLowerCase();
  
  if (foodLower.includes('salad')) return 'Salad';
  if (foodLower.includes('soup')) return 'Soup';
  if (foodLower.includes('sandwich')) return 'Sandwich';
  if (foodLower.includes('burger')) return 'Burger';
  if (foodLower.includes('pizza')) return 'Pizza';
  if (foodLower.includes('pasta')) return 'Pasta';
  if (foodLower.includes('rice')) return 'Rice Dish';
  if (foodLower.includes('fish')) return 'Seafood';
  if (foodLower.includes('chicken')) return 'Poultry';
  if (foodLower.includes('beef') || foodLower.includes('steak')) return 'Beef';
  if (foodLower.includes('dessert') || foodLower.includes('cake') || foodLower.includes('ice cream')) return 'Dessert';
  
  return 'Mixed Dish';
}

function getCommonIngredients(food: string): string[] {
  const foodLower = food.toLowerCase();
  
  if (foodLower.includes('salad')) {
    return ['lettuce', 'tomato', 'cucumber', 'dressing'];
  }
  
  if (foodLower.includes('soup')) {
    return ['broth', 'vegetables', 'salt', 'herbs'];
  }
  
  if (foodLower.includes('sandwich')) {
    return ['bread', 'meat', 'lettuce', 'tomato', 'cheese'];
  }
  
  if (foodLower.includes('burger')) {
    return ['beef patty', 'bun', 'lettuce', 'tomato', 'cheese'];
  }
  
  if (foodLower.includes('pizza')) {
    return ['dough', 'tomato sauce', 'cheese', 'toppings'];
  }
  
  if (foodLower.includes('pasta')) {
    return ['pasta', 'sauce', 'herbs', 'cheese'];
  }
  
  if (foodLower.includes('rice')) {
    return ['rice', 'vegetables', 'spices'];
  }
  
  if (foodLower.includes('fish')) {
    return ['fish', 'seasoning', 'lemon', 'herbs'];
  }
  
  if (foodLower.includes('chicken')) {
    return ['chicken', 'spices', 'herbs', 'salt'];
  }
  
  if (foodLower.includes('beef') || foodLower.includes('steak')) {
    return ['beef', 'salt', 'pepper', 'herbs'];
  }
  
  if (foodLower.includes('dessert') || foodLower.includes('cake') || foodLower.includes('ice cream')) {
    return ['sugar', 'flour', 'milk', 'cream', 'eggs'];
  }
  
  return ['mixed ingredients'];
}

function guessCookingMethod(food: string): string {
  const foodLower = food.toLowerCase();
  
  if (foodLower.includes('salad')) return 'raw';
  if (foodLower.includes('soup')) return 'boiled';
  if (foodLower.includes('sandwich')) return 'assembled';
  if (foodLower.includes('burger')) return 'grilled';
  if (foodLower.includes('pizza')) return 'baked';
  if (foodLower.includes('pasta')) return 'boiled';
  if (foodLower.includes('rice')) return 'boiled';
  if (foodLower.includes('fish')) return 'grilled';
  if (foodLower.includes('chicken')) return 'roasted';
  if (foodLower.includes('beef') || foodLower.includes('steak')) return 'grilled';
  if (foodLower.includes('dessert') || foodLower.includes('cake')) return 'baked';
  if (foodLower.includes('ice cream')) return 'frozen';
  
  return 'mixed';
}

function guessCuisine(food: string): string {
  const foodLower = food.toLowerCase();
  
  if (foodLower.includes('pasta') || foodLower.includes('pizza')) return 'Italian';
  if (foodLower.includes('sushi') || foodLower.includes('ramen')) return 'Japanese';
  if (foodLower.includes('taco') || foodLower.includes('burrito')) return 'Mexican';
  if (foodLower.includes('curry')) return 'Indian';
  if (foodLower.includes('stir fry') || foodLower.includes('fried rice')) return 'Chinese';
  if (foodLower.includes('burger') || foodLower.includes('sandwich')) return 'American';
  if (foodLower.includes('gyro') || foodLower.includes('falafel')) return 'Mediterranean';
  if (foodLower.includes('kimchi') || foodLower.includes('bibimbap')) return 'Korean';
  if (foodLower.includes('pad thai') || foodLower.includes('tom yum')) return 'Thai';
  
  return 'International';
}

function guessFreshnessType(food: string): 'fresh' | 'processed' | 'mixed' | 'unknown' {
  const foodLower = food.toLowerCase();
  
  if (foodLower.includes('salad') || foodLower.includes('fruit')) return 'fresh';
  if (foodLower.includes('chips') || foodLower.includes('candy') || foodLower.includes('snack')) return 'processed';
  
  // Most prepared foods are mixed
  return 'mixed';
} 