import { detectObjectsWithGoogleVision } from './googleVisionApi';
// import { detectFoodWithClarifai } from './clarifaiVisionApi';
import { GOOGLE_VISION_API_KEY } from '@/utils/config';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';

// Interface for detection result
export interface RealTimeFoodDetectionResult {
  foodItems: {
    name: string;
    score: number;
    boundingBox?: {
      topLeft: { x: number, y: number },
      bottomRight: { x: number, y: number }
    };
  }[];
  success: boolean;
  error?: string;
}

// Counter to throttle API calls
let processCounter = 0;
// Default processing interval (process every N frames)
const PROCESS_INTERVAL = 30; // Approximately 1 second at 30fps
// Minimum confidence threshold
const CONFIDENCE_THRESHOLD = 0.6;
// Cache the last result to avoid flickering
let lastResult: RealTimeFoodDetectionResult | null = null;
// Timestamp for rate limiting
let lastProcessTimestamp = 0;
// Rate limit in milliseconds
const RATE_LIMIT_MS = 2000; // Process at most once every 2 seconds

/**
 * Process a frame for real-time food detection
 * Uses a throttled approach to avoid excessive API calls
 */
export async function processFrameForFoodDetection(
  frame: string, 
  forceProcess = false
): Promise<RealTimeFoodDetectionResult> {
  // Rate limiting check
  const now = Date.now();
  if (!forceProcess && now - lastProcessTimestamp < RATE_LIMIT_MS) {
    // Return last result if within rate limit window
    return lastResult || { foodItems: [], success: false, error: 'Rate limited' };
  }

  // Process only every N frames unless forced
  if (!forceProcess && processCounter++ % PROCESS_INTERVAL !== 0) {
    return lastResult || { foodItems: [], success: false };
  }
  
  // Update timestamp
  lastProcessTimestamp = now;

  try {
    // Resize image for faster processing and lower bandwidth
    const resizedImage = await manipulateAsync(
      frame,
      [{ resize: { width: 400 } }], // Lower resolution for real-time processing
      { format: SaveFormat.JPEG, compress: 0.7 }
    );

    // Use Google Vision if available (preferred for bounding boxes)
    if (GOOGLE_VISION_API_KEY) {
      try {
        const googleVisionResults = await detectObjectsWithGoogleVision(resizedImage.uri);
        
        // Filter for food items with sufficient confidence
        const foodItems = googleVisionResults.foodItems
          .filter((item: { name: string; score: number; boundingBox?: any }) => item.score >= CONFIDENCE_THRESHOLD);
        
        if (foodItems.length > 0) {
          lastResult = {
            foodItems,
            success: true
          };
          return lastResult;
        }
      } catch (error) {
        console.warn('Real-time Google Vision API error:', error);
      }
    }

    // Fallback to Clarifai if available
    // if (CLARIFAI_API_KEY) {
    //   try {
    //     const clarifaiResults = await detectFoodWithClarifai(resizedImage.uri);
    //     
    //     // Filter for food items with sufficient confidence
    //     const foodItems = clarifaiResults.foodItems
    //       .filter((item: { name: string; score: number }) => item.score >= CONFIDENCE_THRESHOLD)
    //       .map((item: { name: string; score: number }) => ({
    //         name: item.name,
    //         score: item.score,
    //         // Clarifai usually doesn't provide bounding boxes, so create a centered one
    //         boundingBox: {
    //           topLeft: { x: 0.3, y: 0.3 },
    //           bottomRight: { x: 0.7, y: 0.7 }
    //         }
    //       }));
    //     
    //     if (foodItems.length > 0) {
    //       lastResult = {
    //         foodItems,
    //         success: true
    //       };
    //       return lastResult;
    //     }
    //   } catch (error) {
    //     console.warn('Real-time Clarifai API error:', error);
    //   }
    // }

    // If both APIs failed or returned no results
    lastResult = {
      foodItems: [],
      success: false,
      error: 'No food detected'
    };
    return lastResult;
  } catch (error) {
    console.error('Error in real-time food detection:', error);
    lastResult = {
      foodItems: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    return lastResult;
  }
}

/**
 * Reset the frame processor state
 * Call this when the camera is closed or when switching modes
 */
export function resetFrameProcessor(): void {
  processCounter = 0;
  lastResult = null;
  lastProcessTimestamp = 0;
} 