/**
 * Recipe database for food recognition and enhanced descriptions
 */

// Recipe database for common dishes to improve recognition accuracy
export const RECIPE_DATABASE: {
  [key: string]: {
    name: string;
    ingredients: string[];
    description: string;
  }
} = {
  // Add known recipes with their ingredients and descriptions
  "goulash": {
    name: "Goulash",
    ingredients: [
      "olive oil", "yellow onion", "garlic", "ground beef", "salt", "pepper", 
      "paprika", "Italian seasoning", "tomato paste", "diced tomatoes", 
      "tomato sauce", "Worcestershire sauce", "beef broth", "pasta", 
      "cheddar cheese", "parsley"
    ],
    description: "Goulash is a hearty dish featuring ground beef, pasta, and a rich tomato sauce seasoned with paprika. This comfort food combines meat, vegetables, and pasta in a savory one-pot meal that's perfect for family dinners."
  },
  "american goulash": {
    name: "American Goulash",
    ingredients: [
      "olive oil", "onion", "garlic", "ground beef", "salt", "pepper", 
      "paprika", "Italian seasoning", "tomato paste", "diced tomatoes", 
      "tomato sauce", "Worcestershire sauce", "beef broth", "elbow macaroni", 
      "cheddar cheese", "parsley"
    ],
    description: "American Goulash is a comforting one-pot meal consisting of ground beef, macaroni pasta, and a rich tomato sauce seasoned with paprika and Italian herbs. This hearty dish is topped with melted cheddar cheese and fresh parsley."
  },
  "beef pasta": {
    name: "Beef and Pasta Goulash",
    ingredients: [
      "ground beef", "pasta", "tomato sauce", "diced tomatoes", "onion", 
      "garlic", "paprika", "Italian seasoning", "beef broth", "cheddar cheese"
    ],
    description: "A hearty one-pot meal featuring seasoned ground beef, pasta, and a rich tomato sauce infused with paprika and herbs. This classic comfort food is perfect for family dinners."
  },
  "beef stroganoff": {
    name: "Beef Stroganoff",
    ingredients: [
      "beef strips", "mushrooms", "onion", "garlic", "butter", "flour", 
      "beef broth", "sour cream", "Worcestershire sauce", "dijon mustard", 
      "egg noodles", "parsley"
    ],
    description: "Beef Stroganoff is a classic dish with tender beef strips and mushrooms in a creamy sour cream sauce, typically served over egg noodles."
  },
  "sausage and sun dried tomato pasta": {
    name: "Sausage and Sun-Dried Tomato Pasta",
    ingredients: [
      "pasta", "sausage", "sun-dried tomatoes", "garlic", "olive oil", 
      "parmesan cheese", "red pepper flakes", "parsley"
    ],
    description: "A savory pasta dish featuring Italian sausage and rich sun-dried tomatoes tossed with pasta in a light olive oil sauce with garlic and herbs."
  },
  "tuna casserole": {
    name: "Tuna Casserole",
    ingredients: [
      "tuna", "pasta", "cream of mushroom soup", "peas", "onion", 
      "breadcrumbs", "cheese", "milk"
    ],
    description: "A creamy baked dish combining canned tuna, pasta, and vegetables in a savory sauce, typically topped with breadcrumbs and cheese."
  },
  "lasagna": {
    name: "Lasagna",
    ingredients: [
      "pasta sheets", "ground beef", "tomato sauce", "ricotta cheese", "mozzarella cheese",
      "parmesan cheese", "garlic", "onion", "italian herbs"
    ],
    description: "A classic Italian dish made with layers of flat pasta sheets, ground beef in tomato sauce, and multiple cheeses, baked until bubbly and golden on top."
  },
  "chicken parmesan": {
    name: "Chicken Parmesan",
    ingredients: [
      "chicken breast", "breadcrumbs", "parmesan cheese", "mozzarella cheese",
      "tomato sauce", "pasta", "olive oil", "herbs"
    ],
    description: "A popular Italian-American dish featuring breaded chicken breasts topped with tomato sauce and melted cheese, typically served with pasta."
  },
  "stir fry": {
    name: "Vegetable Stir Fry",
    ingredients: [
      "mixed vegetables", "soy sauce", "garlic", "ginger", "oil", 
      "rice", "tofu", "sesame oil", "green onion"
    ],
    description: "A quick-cooked Asian dish with crisp vegetables in a savory sauce, often served with rice or noodles."
  },
  "burrito bowl": {
    name: "Burrito Bowl",
    ingredients: [
      "rice", "beans", "corn", "tomato", "avocado", "cheese", 
      "lettuce", "sour cream", "salsa", "cilantro", "lime"
    ],
    description: "A deconstructed burrito served in a bowl with layers of rice, beans, vegetables, and toppings without the tortilla wrap."
  },
  "buddha bowl": {
    name: "Buddha Bowl",
    ingredients: [
      "quinoa", "roasted vegetables", "avocado", "chickpeas", "tofu", 
      "kale", "hummus", "seeds", "tahini dressing"
    ],
    description: "A nourishing plant-based meal consisting of small portions of various foods arranged in a bowl, typically featuring grains, vegetables, proteins, and dressing."
  },
  "ramen": {
    name: "Ramen",
    ingredients: [
      "noodles", "broth", "egg", "green onion", "pork", "nori", 
      "bamboo shoots", "bean sprouts", "sesame"
    ],
    description: "A Japanese noodle soup dish with wheat noodles served in a meat or fish-based broth, often flavored with soy sauce or miso, and topped with ingredients such as sliced pork, dried seaweed, and green onions."
  },
  "bibimbap": {
    name: "Bibimbap",
    ingredients: [
      "rice", "vegetables", "beef", "egg", "gochujang", "sesame oil", 
      "sprouts", "spinach", "carrots", "mushrooms"
    ],
    description: "A Korean rice dish with an array of sautéed and seasoned vegetables, meat, an egg, and gochujang (chili pepper paste) arranged attractively over rice."
  }
};

/**
 * Finds a matching recipe for a set of detected items
 */
export function findMatchingRecipe(items: {name: string, score?: number}[]): any {
  // Extract item names
  const detectedItems = items.map(item => item.name.toLowerCase());
  
  // Check for exact keywords that indicate specific dishes
  // This is useful for very specific dishes like goulash that might be misidentified
  const keywordsToRecipes: {[key: string]: string} = {
    "goulash": "goulash",
    "pasta with meat": "beef pasta",
    "meat sauce": "beef pasta",
    "beef pasta": "beef pasta",
    "pasta beef": "beef pasta",
    "american goulash": "american goulash",
    "stroganoff": "beef stroganoff",
    "beef with noodles": "beef stroganoff",
    "chicken curry": "chicken_curry",
    "curry chicken": "chicken_curry",
    "chicken with curry": "chicken_curry",
    "chicken and rice": "chicken_rice",
    "chicken rice": "chicken_rice",
    "rice chicken": "chicken_rice"
  };
  
  // First check the original dish name
  for (const key of Object.keys(keywordsToRecipes)) {
    if (detectedItems.some(item => item.includes(key) || key.includes(item))) {
      const recipeKey = keywordsToRecipes[key];
      console.log(`Exact keyword match: ${key} → ${recipeKey}`);
      
      // Special case for chicken curry
      if (recipeKey === "chicken_curry") {
        const hasRice = detectedItems.some(item => item.includes('rice'));
        if (hasRice) {
          return {
            name: "Chicken Curry with Rice",
            ingredients: [
              "chicken", "rice", "curry powder", "coconut milk", "onion", 
              "garlic", "ginger", "tomato", "bell pepper", "vegetable oil", 
              "cilantro", "spices"
            ],
            description: "A flavorful dish combining tender chicken pieces cooked in a rich curry sauce made with coconut milk, aromatic spices, onions, and tomatoes, served over a bed of fluffy basmati rice."
          };
        } else {
          return {
            name: "Chicken Curry",
            ingredients: [
              "chicken", "curry powder", "coconut milk", "onion", 
              "garlic", "ginger", "tomato", "bell pepper", "vegetable oil", 
              "cilantro", "spices"
            ],
            description: "A rich and aromatic dish with tender chicken pieces simmered in a flavorful curry sauce made with coconut milk, spices, onions, and tomatoes."
          };
        }
      }
      
      // Special case for chicken and rice
      if (recipeKey === "chicken_rice") {
        return {
          name: "Chicken and Rice",
          ingredients: [
            "chicken", "rice", "vegetables", "onion", "garlic", 
            "salt", "pepper", "olive oil", "herbs"
          ],
          description: "A simple and nutritious dish featuring tender chicken pieces with fluffy rice and mixed vegetables. This versatile and comforting meal is popular across many cultures for its balanced flavors and satisfying combination of protein and carbohydrates."
        };
      }
      
      return RECIPE_DATABASE[recipeKey];
    }
  }
  
  // Add a special check for chicken and rice combinations
  if (detectedItems.includes("chicken") && detectedItems.includes("rice")) {
    console.log("Detected chicken and rice combination");
    return {
      name: "Chicken and Rice",
      ingredients: [
        "chicken", "rice", "vegetables", "onion", "garlic", 
        "salt", "pepper", "olive oil", "herbs"
      ],
      description: "A simple and nutritious dish featuring tender chicken pieces with fluffy rice and mixed vegetables. This versatile and comforting meal is popular across many cultures for its balanced flavors and satisfying combination of protein and carbohydrates."
    };
  }
  
  // Return null if no match found
  return null;
}

/**
 * Overrides basic food data with recipe information if a match is found
 */
export async function overrideWithRecipeInfo(foodData: any): Promise<any> {
  // If we have a description and name already, no need to override
  if (foodData.description && foodData.name !== "Food" && foodData.name !== "Meal") {
    return foodData;
  }
  
  // Try to find a matching recipe based on the detected items
  const recipeMatch = findMatchingRecipe(foodData.items);
  
  // If no match found, return the original data
  if (!recipeMatch) {
    return foodData;
  }
  
  // Override the food data with the recipe information
  return {
    ...foodData,
    name: recipeMatch.name,
    description: recipeMatch.description
  };
} 