import { detectObjectsWithGoogleVision } from './googleVisionApi';

export interface DetectedReferenceObject {
  name: string;
  width: number;  // mm
  height: number; // mm
  depth?: number; // mm (optional)
  boundingBox: {
    topLeft: { x: number, y: number },
    bottomRight: { x: number, y: number }
  };
  confidence: number;
}

// Common reference objects with standardized measurements in mm
const REFERENCE_OBJECTS = {
  // Cards and currency
  'credit card': { width: 85.6, height: 53.98, depth: 0.76 },
  'card': { width: 85.6, height: 53.98, depth: 0.76 },
  'dollar bill': { width: 155.956, height: 66.294, depth: 0.11 },
  'coin': { width: 19.05, height: 19.05, depth: 1.55 }, // US quarter as default
  
  // Tableware
  'plate': { width: 255, height: 255, depth: 15 }, // Standard dinner plate
  'fork': { width: 20, height: 180, depth: 5 },
  'spoon': { width: 40, height: 170, depth: 5 },
  'knife': { width: 18, height: 220, depth: 2 },
  'cup': { width: 80, height: 95, depth: 80 },
  'bottle': { width: 70, height: 230, depth: 70 },
  
  // Fruits
  'banana': { width: 180, height: 35, depth: 35 },
  'apple': { width: 75, height: 75, depth: 75 },
  'orange': { width: 70, height: 70, depth: 70 },
  
  // Electronics
  'smartphone': { width: 70, height: 150, depth: 8 },
  'phone': { width: 70, height: 150, depth: 8 },
  'battery': { width: 14.5, height: 50.5, depth: 14.5 }, // AA battery
  
  // Others
  'pen': { width: 8, height: 145, depth: 8 },
  'eyeglasses': { width: 140, height: 40, depth: 10 },
  'glasses': { width: 140, height: 40, depth: 10 },
  'hand': { width: 85, height: 190, depth: 25 }, // Average adult hand
};

// Required confidence to accept an object as a reference
const CONFIDENCE_THRESHOLD = 0.7;

/**
 * Find suitable reference objects in an image to help with portion size estimation
 * @param imageUri URI of the image to analyze
 * @returns Array of detected reference objects with their real-world dimensions
 */
export async function detectReferenceObjects(imageUri: string): Promise<{
  success: boolean;
  referenceObjects?: DetectedReferenceObject[];
  error?: string;
}> {
  try {
    // 1. Use Google Vision API to detect objects in the image
    let googleVisionResult;
    
    try {
      googleVisionResult = await detectObjectsWithGoogleVision(imageUri);
    } catch (apiError) {
      console.log('Error using Google Vision API, returning empty result', apiError);
      return {
        success: true,
        referenceObjects: []
      };
    }
    
    if (!googleVisionResult.success) {
      console.log('Google Vision returned unsuccessful result, using empty reference objects');
      return {
        success: true,
        referenceObjects: []
      };
    }
    
    // 2. Filter detected objects to find reference objects
    const detectedObjects = googleVisionResult.labels || [];
    const objectsWithLocations = googleVisionResult.localizedObjects || [];
    
    // If we have no localized objects, return early with an empty result
    if (!objectsWithLocations || objectsWithLocations.length === 0) {
      return {
        success: true,
        referenceObjects: []
      };
    }
    
    const referenceObjects: DetectedReferenceObject[] = [];
    
    // Process objects that have bounding boxes
    objectsWithLocations.forEach((obj: {
      name: string;
      score: number;
      boundingBox?: {
        topLeft: { x: number, y: number };
        bottomRight: { x: number, y: number };
      };
    }) => {
      // Skip objects with undefined/null properties
      if (!obj || !obj.name || !obj.score || !obj.boundingBox) return;
      
      // Skip low-confidence detections
      if (obj.score < CONFIDENCE_THRESHOLD) return;
      
      // Convert object name to lowercase for matching
      const objName = obj.name.toLowerCase();
      
      // Find if this is one of our reference objects
      const matchedRefObj = Object.entries(REFERENCE_OBJECTS).find(([key]) => {
        return objName.includes(key) || key.includes(objName);
      });
      
      if (matchedRefObj && obj.boundingBox) {
        const [name, dimensions] = matchedRefObj;
        const boundingBox = obj.boundingBox;
        
        // Ensure boundingBox properties exist
        if (!boundingBox.topLeft || !boundingBox.bottomRight) return;
        
        referenceObjects.push({
          name: obj.name,
          width: dimensions.width,
          height: dimensions.height,
          depth: dimensions.depth,
          boundingBox: {
            topLeft: boundingBox.topLeft,
            bottomRight: boundingBox.bottomRight,
          },
          confidence: obj.score
        });
      }
    });
    
    return {
      success: true,
      referenceObjects
    };
  } catch (error) {
    console.error('Error detecting reference objects:', error);
    // Instead of failing with an error, provide an empty result
    return {
      success: true,
      referenceObjects: [],
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Estimate the size of a food item relative to detected reference objects
 * @param foodBoundingBox Bounding box of the food item
 * @param referenceObjects Array of detected reference objects
 * @param imageWidth Width of the image in pixels
 * @param imageHeight Height of the image in pixels
 * @returns Estimated dimensions in mm and volume in cm³
 */
export function estimateFoodSize(
  foodBoundingBox: {
    topLeft: { x: number, y: number },
    bottomRight: { x: number, y: number }
  },
  referenceObjects: DetectedReferenceObject[],
  imageWidth: number,
  imageHeight: number
) {
  // If no reference objects found, return null
  if (referenceObjects.length === 0) {
    return null;
  }
  
  // Get the most confident reference object
  const refObject = [...referenceObjects].sort((a, b) => b.confidence - a.confidence)[0];
  
  // Calculate pixel dimensions
  const refPixelWidth = (refObject.boundingBox.bottomRight.x - refObject.boundingBox.topLeft.x) * imageWidth;
  const refPixelHeight = (refObject.boundingBox.bottomRight.y - refObject.boundingBox.topLeft.y) * imageHeight;
  
  const foodPixelWidth = (foodBoundingBox.bottomRight.x - foodBoundingBox.topLeft.x) * imageWidth;
  const foodPixelHeight = (foodBoundingBox.bottomRight.y - foodBoundingBox.topLeft.y) * imageHeight;
  
  // Calculate mm per pixel ratios
  const pixelToMmRatioWidth = refObject.width / refPixelWidth;
  const pixelToMmRatioHeight = refObject.height / refPixelHeight;
  
  // Use the average ratio for more accurate estimation
  const avgPixelToMmRatio = (pixelToMmRatioWidth + pixelToMmRatioHeight) / 2;
  
  // Estimate food dimensions in mm
  const estimatedWidth = foodPixelWidth * avgPixelToMmRatio;
  const estimatedHeight = foodPixelHeight * avgPixelToMmRatio;
  
  // Estimate depth (assuming circular/spherical food items)
  // For simplicity, using average of width/height as depth for irregular objects
  const estimatedDepth = (estimatedWidth + estimatedHeight) / 2;
  
  // Calculate estimated volume (assuming ellipsoid/cylindrical shape)
  // Volume = 4/3 * π * (width/2) * (height/2) * (depth/2) ≈ π/6 * width * height * depth
  const estimatedVolumeMm3 = (Math.PI / 6) * estimatedWidth * estimatedHeight * estimatedDepth;
  const estimatedVolumeCm3 = estimatedVolumeMm3 / 1000; // Convert to cm³
  
  return {
    width: estimatedWidth,
    height: estimatedHeight,
    depth: estimatedDepth,
    volume: estimatedVolumeCm3,
    referenceObject: {
      name: refObject.name,
      width: refObject.width,
      height: refObject.height
    }
  };
} 