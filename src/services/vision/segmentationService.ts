import * as FileSystem from 'expo-file-system';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { uriToBase64 } from './googleVisionApi';
import { HUGGINGFACE_API_KEY } from '@/utils/config';
import { enhancedFoodAnalysis } from './typedUtils';

// Use Hugging Face API for lightweight segmentation model
const HUGGINGFACE_API_URL = 'https://api-inference.huggingface.co/models';

// Use MobileSAM as a lightweight alternative to SAM
// Mobile SAM is optimized for mobile devices and is much smaller than the full SAM model
const MOBILE_SAM_MODEL_ID = 'ybelkada/segment-anything-mobilesam';

// For more advanced segmentation (if mobile resources permit)
const DEEPLAB_MODEL_ID = 'tensorflow/deeplabv3';

// Interface for segmentation results
export interface SegmentationResult {
  masks: {
    mask: number[][]; // Binary mask (0 or 1)
    score: number;
    bbox: [number, number, number, number]; // [x1, y1, x2, y2] normalized coordinates
  }[];
  processingTimeMs: number;
}

export interface FoodSegment {
  id: string;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  maskUri?: string; // URI to stored mask image
  score?: number;
  area: number; // Pixel area of the segment
  labeledAs?: string; // Optional food name from vision API
  confidence?: number; // Confidence score for the food item
  nutrition?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  enhancedNutrition?: {
    name: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    ingredients?: string[];
    cookingMethod?: string;
    isFreshOrProcessed?: 'fresh' | 'processed' | 'mixed' | 'unknown';
  };
}

/**
 * Segment an image using MobileSAM to find distinct objects/foods
 * 
 * @param imageUri URI of the image to segment
 * @returns Segmentation results with masks and bounding boxes
 */
export async function segmentImageWithMobileSAM(imageUri: string): Promise<{
  success: boolean;
  segments?: FoodSegment[];
  error?: string;
  processingTimeMs?: number;
}> {
  try {
    // Check cache first
    try {
      const cacheKey = `segmentation_${imageUri}`;
      const cachedData = await AsyncStorage.getItem(cacheKey);
      
      if (cachedData) {
        console.log('Using cached segmentation result');
        return JSON.parse(cachedData);
      }
    } catch (cacheError) {
      console.log('Cache check failed, proceeding with API call', cacheError);
    }
    
    if (!HUGGINGFACE_API_KEY) {
      console.log('Hugging Face API key is not configured, using fallback approach');
      // Return empty segments with a notice that full segmentation is unavailable
      return {
        success: true,
        segments: [],
        error: 'Hugging Face API key not configured, no segmentation available'
      };
    }
    
    // Convert image to base64
    const base64Image = await uriToBase64(imageUri);
    
    const startTime = Date.now();
    
    // Call Hugging Face API with MobileSAM model
    const response = await fetch(`${HUGGINGFACE_API_URL}/${MOBILE_SAM_MODEL_ID}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${HUGGINGFACE_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        inputs: base64Image,
        parameters: {
          // Optional parameters for MobileSAM
          points_per_side: 8, // Lower for faster but less precise results
          pred_iou_thresh: 0.8, // Higher means more confident masks
          stability_score_thresh: 0.8, // Higher means more stable masks
          crop_n_layers: 1, // Lower for faster results
          crop_n_points_downscale_factor: 2 // Lower for faster results
        }
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Segmentation API error:', errorText);
      throw new Error(`Segmentation API error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    const processingTimeMs = Date.now() - startTime;
    
    // Process segmentation results
    // Convert API response to our FoodSegment format
    let segments: FoodSegment[] = [];
    
    if (data && typeof data === 'object' && 'masks' in data) {
      // Process masks from API response
      segments = await processMaskResults(data.masks as any[], imageUri);
    } else if (data && Array.isArray(data)) {
      // Alternative API response format
      segments = await processMaskResults(data, imageUri);
    }
    
    // Filter segments to likely food items (remove tiny segments and background)
    segments = segments
      .filter(segment => segment.area > 1000) // Filter out tiny segments
      .filter(segment => (segment.score ?? 0) > 0.5) // Only keep high confidence segments
      .sort((a, b) => b.area - a.area); // Sort by size (largest first)
    
    const result = {
      success: true,
      segments,
      processingTimeMs
    };
    
    // Cache the result
    try {
      const cacheKey = `segmentation_${imageUri}`;
      await AsyncStorage.setItem(cacheKey, JSON.stringify(result));
    } catch (cacheError) {
      console.log('Failed to cache segmentation result', cacheError);
    }
    
    return result;
  } catch (error) {
    console.error('Error in image segmentation:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error during segmentation'
    };
  }
}

/**
 * Process masks from segmentation model into our format
 * Also saves mask images to file system for later display
 */
async function processMaskResults(masks: any[], imageUri: string): Promise<FoodSegment[]> {
  const segments: FoodSegment[] = [];
  
  for (let i = 0; i < masks.length; i++) {
    const mask = masks[i];
    
    // Handle different API response formats
    let maskData: number[][] = [];
    let bbox: [number, number, number, number] = [0, 0, 1, 1];
    let score = 0.9;
    
    if (mask.segmentation) {
      // Some APIs return segmentation data directly
      maskData = mask.segmentation;
      bbox = mask.bbox || [0, 0, 1, 1];
      score = mask.score || 0.9;
    } else if (mask.mask) {
      // MobileSAM format
      maskData = mask.mask;
      bbox = mask.bbox || [0, 0, 1, 1];
      score = mask.score || 0.9;
    } else if (Array.isArray(mask)) {
      // Raw mask format
      maskData = mask;
      // Estimate bounding box from mask
      bbox = estimateBoundingBoxFromMask(maskData);
    }
    
    // Normalize bbox if needed and convert to our format
    const boundingBox = {
      x: bbox[0],
      y: bbox[1],
      width: bbox[2] - bbox[0],
      height: bbox[3] - bbox[1]
    };
    
    // Calculate area of the segment
    const area = calculateMaskArea(maskData);
    
    // Generate an ID for the segment
    const id = `segment-${i}-${Date.now().toString(36)}`;
    
    // Save mask to file for visualization if needed
    let maskUri;
    try {
      maskUri = await saveMaskToFile(maskData, id);
    } catch (err) {
      console.warn('Could not save mask to file:', err);
    }
    
    segments.push({
      id,
      boundingBox,
      maskUri,
      score,
      area
    });
  }
  
  return segments;
}

/**
 * Estimate a bounding box from a binary mask
 */
function estimateBoundingBoxFromMask(mask: number[][]): [number, number, number, number] {
  let minX = mask[0].length;
  let minY = mask.length;
  let maxX = 0;
  let maxY = 0;
  
  // Find the bounds of non-zero pixels
  for (let y = 0; y < mask.length; y++) {
    for (let x = 0; x < mask[y].length; x++) {
      if (mask[y][x] > 0) {
        minX = Math.min(minX, x);
        minY = Math.min(minY, y);
        maxX = Math.max(maxX, x);
        maxY = Math.max(maxY, y);
      }
    }
  }
  
  // Normalize to 0-1 range
  const width = mask[0].length;
  const height = mask.length;
  
  return [
    minX / width,
    minY / height,
    maxX / width,
    maxY / height
  ];
}

/**
 * Calculate the area of a binary mask (number of non-zero pixels)
 */
function calculateMaskArea(mask: number[][]): number {
  let area = 0;
  
  for (let y = 0; y < mask.length; y++) {
    for (let x = 0; x < mask[y].length; x++) {
      if (mask[y][x] > 0) {
        area++;
      }
    }
  }
  
  return area;
}

/**
 * Save a mask to a temporary file for visualization
 */
async function saveMaskToFile(mask: number[][], id: string): Promise<string | undefined> {
  try {
    // Convert mask to a base64 PNG
    // For simplicity, we'll create a very simple black and white PNG
    // In a production app, you'd use a proper image library
    
    // Create a temporary canvas to draw the mask (This would actually be done natively)
    // This is a mock implementation - in a real app, we'd use a native module
    const tempFileName = `${FileSystem.cacheDirectory}/mask-${id}.png`;
    
    // For now, just return the filename that would be created
    // In a real implementation, we'd generate and save the actual PNG file
    return tempFileName;
  } catch (error) {
    console.error('Error saving mask to file:', error);
    return undefined;
  }
}

/**
 * Segment food items in an image and return portions with metadata
 * This is a higher-level function that combines segmentation with classification
 * 
 * @param imageUri URI of the image to analyze
 * @returns Segmented food items with portion estimation
 */
export async function segmentFoodItems(imageUri: string): Promise<{
  success: boolean;
  segments?: FoodSegment[];
  error?: string;
}> {
  try {
    // First segment the image
    const segmentationResult = await segmentImageWithMobileSAM(imageUri);
    
    if (!segmentationResult.success || !segmentationResult.segments) {
      return {
        success: false,
        error: segmentationResult.error || 'Segmentation failed'
      };
    }
    
    // Filter segments to focus on likely food items
    let foodSegments = segmentationResult.segments
      .filter(segment => segment.area > 1000) // Remove tiny segments
      .filter(segment => (segment.score ?? 0) > 0.6) // Only high confidence segments
      .slice(0, 5); // Limit to top 5 segments to avoid overwhelming
    
    return {
      success: true,
      segments: foodSegments
    };
  } catch (error) {
    console.error('Error segmenting food items:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error during food segmentation'
    };
  }
}

/**
 * Converts segments to a simplified list of food items
 */
export function getSimplifiedFoodSegments(segments: FoodSegment[]): {
  name: string;
  confidence: number;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  ingredients?: string[];
  cookingMethod?: string;
  isFreshOrProcessed?: 'fresh' | 'processed' | 'mixed' | 'unknown';
}[] {
  return segments.map(segment => {
    // If we have enhanced nutrition data with specific food type detection, use it
    if (segment.enhancedNutrition) {
      return {
        name: segment.enhancedNutrition.name || segment.labeledAs || 'Unknown food',
        confidence: segment.confidence || 0.7,
        calories: segment.enhancedNutrition.calories || 0,
        protein: segment.enhancedNutrition.protein || 0,
        carbs: segment.enhancedNutrition.carbs || 0,
        fat: segment.enhancedNutrition.fat || 0,
        ingredients: segment.enhancedNutrition.ingredients,
        cookingMethod: segment.enhancedNutrition.cookingMethod,
        isFreshOrProcessed: segment.enhancedNutrition.isFreshOrProcessed
      };
    }
    
    // Otherwise use basic nutrition data
    return {
      name: segment.labeledAs || 'Unknown food',
      confidence: segment.confidence || 0.7,
      calories: segment.nutrition?.calories || 0,
      protein: segment.nutrition?.protein || 0,
      carbs: segment.nutrition?.carbs || 0,
      fat: segment.nutrition?.fat || 0
    };
  });
}

/**
 * Apply enhanced detection to segments
 */
export async function enhanceFoodSegments(
  segments: FoodSegment[], 
  originalImageBase64: string
): Promise<FoodSegment[]> {
  try {
    // First try to do a full image analysis using enhanced detection
    const enhancedResult = await enhancedFoodAnalysis(originalImageBase64);
    
    // If we have detailed results, map them to the segments
    if (enhancedResult.detailedFoodInfo.length > 0) {
      // Map enhanced items to segments based on name similarity
      const enhancedSegments = segments.map(segment => {
        // Find the closest match in enhanced results
        const matchingItem = enhancedResult.detailedFoodInfo.find(item => {
          // Simple case-insensitive matching
          const segmentName = segment.labeledAs?.toLowerCase() || '';
          const itemName = item.specificDish?.toLowerCase() || '';
          
          return segmentName.includes(itemName) || itemName.includes(segmentName);
        });
        
        if (matchingItem) {
          // Enhance the segment with detailed information
          return {
            ...segment,
            enhancedNutrition: {
              name: matchingItem.specificDish,
              calories: matchingItem.nutrition.calories,
              protein: matchingItem.nutrition.protein,
              carbs: matchingItem.nutrition.carbs,
              fat: matchingItem.nutrition.fat,
              ingredients: matchingItem.ingredients,
              cookingMethod: matchingItem.cookingMethod,
              isFreshOrProcessed: matchingItem.isFreshOrProcessed
            }
          };
        }
        
        return segment;
      });
      
      return enhancedSegments;
    }
    
    // If no specific matches found, return original segments
    return segments;
  } catch (error) {
    console.error('Error in enhancing food segments:', error);
    return segments;
  }
}

/**
 * For devices/environments that don't support the full segmentation,
 * this function provides a simplified approach using bounding boxes only
 */
export async function createFoodSegmentsFromBoundingBoxes(
  boundingBoxes: {
    name: string;
    score: number;
    boundingBox: {
      topLeft: { x: number, y: number },
      bottomRight: { x: number, y: number }
    };
  }[]
): Promise<FoodSegment[]> {
  // Convert bounding boxes to segments format
  return boundingBoxes.map((item, index) => {
    const bb = item.boundingBox;
    const width = bb.bottomRight.x - bb.topLeft.x;
    const height = bb.bottomRight.y - bb.topLeft.y;
    
    return {
      id: `bbox-${index}-${Date.now().toString(36)}`,
      boundingBox: {
        x: bb.topLeft.x,
        y: bb.topLeft.y,
        width,
        height
      },
      score: item.score,
      area: width * height * 1000000, // Rough approximation
      labeledAs: item.name
    };
  });
} 