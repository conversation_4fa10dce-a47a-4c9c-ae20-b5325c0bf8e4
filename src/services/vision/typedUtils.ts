import { UnifiedFoodAnalysisResult, FineGrainedFoodDetails, VolumeEstimate } from './types';
import { detectGlobalFoodItems, generateDishName, calculateNutrition, generateHealthInsights, DetectedFoodItem } from './globalFoodDetection';

/**
 * Enhanced food analysis with specific food type detection
 * @param imageData Base64 encoded image data
 * @param bypassCache Optional flag to bypass cache and force fresh analysis
 * @returns Enhanced analysis with detected food items
 */
export async function enhancedFoodAnalysis(imageData: string, bypassCache: boolean = false): Promise<UnifiedFoodAnalysisResult> {
  try {
    // Import dependencies dynamically to avoid circular references
    const { detectLabelsFromImage } = await import('./visionApi');
    const { detectObjectsWithGoogleVision } = await import('./googleVisionApi');
    const { isFoodCategory } = await import('./utils');
    
    console.log(`Enhanced food analysis starting with bypassCache: ${bypassCache}`);
    
    // Create a timeout promise that resolves with a basic result after 8 seconds
    const timeoutPromise = new Promise<UnifiedFoodAnalysisResult>((resolve) => {
      setTimeout(() => {
        console.log('Enhanced food analysis timed out - returning basic results');
        // Create a basic result with minimal information
        resolve(createEmptyResult('Analysis completed with basic results'));
      }, 8000); // 8 second timeout for faster results
    });
    
    // Create the main analysis promise
    const analysisPromise = (async () => {
      // Step 1: Get basic labels from vision API
      const labels = await detectLabelsFromImage(imageData);
      
      // Store the labels for use in case of timeout
      const foodLabels = labels
        .filter(label => isFoodCategory(label.description))
        .map(label => label.description);
      
      // Also try to get objects with Google Vision - pass through the bypassCache flag
      try {
        await detectObjectsWithGoogleVision(imageData, bypassCache);
      } catch (error) {
        console.log('Google Vision detection failed, continuing with labels only:', error);
      }
      
      if (foodLabels.length === 0) {
        return createEmptyResult('No food detected in image');
      }
      
      // Step 2: Detect specific cuisines and detailed food items using global food database
      const globalAnalysis = detectGlobalFoodItems(foodLabels);
      
      // If specific cuisine and items detected, use the detailed analysis
      if (globalAnalysis.isSpecificCuisine && globalAnalysis.items.length > 0) {
        console.log(`${globalAnalysis.cuisineType} cuisine detected with specific items:`, 
          globalAnalysis.items.map(item => item.name));
        
        // Calculate nutrition using the utility function
        const nutritionData = calculateNutrition(globalAnalysis.items);
        
        // Generate descriptive name for the dish
        const dishName = generateDishName(globalAnalysis.items, globalAnalysis.cuisineType);
        
        // Generate health insights
        const healthInsights = generateHealthInsights(
          globalAnalysis.items,
          nutritionData.totalCalories,
          nutritionData.macronutrients.protein,
          nutritionData.macronutrients.carbs,
          nutritionData.macronutrients.fat,
          nutritionData.macronutrients.fiber
        );
        
        return {
          foodItems: globalAnalysis.items.map(item => ({
            name: item.name,
            confidence: item.confidence
          })),
          imageProperties: {
            dominantColors: generateDominantColors(globalAnalysis.cuisineType),
            brightness: 0.7,
            contrast: 0.6,
            sharpness: 0.8,
            hasPlate: true
          },
          detailedFoodInfo: globalAnalysis.items.map(item => ({
            mainCategory: `${item.cuisine} Cuisine`,
            specificDish: item.name,
            ingredients: getIngredientsForItem(item.name, item.cuisine),
            cookingMethod: item.cookingMethod || 'unknown',
            cuisineOrigin: item.cuisine,
            isFreshOrProcessed: getProcessingTypeForItem(item),
            nutrition: {
              calories: item.calories,
              protein: item.protein,
              carbs: item.carbs,
              fat: item.fat,
              fiber: item.fiber || 0
            },
            confidenceScore: item.confidence
          })) as FineGrainedFoodDetails[],
          volumeInfo: generateVolumeEstimates(globalAnalysis.items),
          nutritionalSummary: {
            totalCalories: nutritionData.totalCalories,
            macronutrients: nutritionData.macronutrients,
            mealType: nutritionData.mealType,
            isBalancedMeal: nutritionData.isBalancedMeal,
            nutritionalQualityScore: nutritionData.nutritionalQualityScore
          },
          textualAnalysis: {
            description: generateMealDescription(dishName, globalAnalysis),
            preparationMethod: generatePreparationMethod(globalAnalysis.items),
            dietaryCategories: generateDietaryCategories(globalAnalysis.items),
            cuisineType: globalAnalysis.cuisineType,
            healthInsights: healthInsights
          },
          meta: {
            analysisTimestamp: Date.now(),
            modelVersion: 'enhanced-global-cuisine-detector-1.0',
            confidenceScore: globalAnalysis.confidence,
            processingTimeMs: 0
          }
        };
      }
      
      // If no specific cuisine detected, fall back to general analysis
      return createEmptyResult('Food detected but no specific cuisine pattern recognized');
    })();
    
    // Use Promise.race to return either the full analysis or a timeout result
    return Promise.race([analysisPromise, timeoutPromise]);
    
  } catch (error) {
    console.error('Error in enhanced food analysis:', error);
    return createEmptyResult('Error analyzing image');
  }
}

/**
 * Generate a cuisine-specific set of dominant colors
 */
function generateDominantColors(cuisineType: string): string[] {
  switch (cuisineType) {
    case 'Filipino':
      return ['#F5F5DC', '#A52A2A', '#FFFF00']; // Beige, brown, yellow
    case 'Mexican':
      return ['#FF4500', '#008000', '#FFFFFF']; // Red, green, white (Mexican flag colors)
    case 'Italian':
      return ['#FF0000', '#FFFFFF', '#006400']; // Red, white, green (Italian flag colors)
    case 'Japanese':
      return ['#FFFFFF', '#FF0000', '#000000']; // White, red, black
    case 'Indian':
      return ['#FF9933', '#FFFFFF', '#138808']; // Saffron, white, green (Indian flag colors)
    case 'Chinese':
      return ['#FF0000', '#FFFF00', '#8B4513']; // Red, yellow, brown
    case 'American':
      return ['#FF0000', '#0000FF', '#FFFFFF']; // Red, blue, white (American flag colors)
    default:
      return ['#CCCCCC', '#EEEEEE', '#AAAAAA']; // Generic grays
  }
}

/**
 * Generate volume estimates for food items
 */
function generateVolumeEstimates(items: DetectedFoodItem[]): VolumeEstimate[] {
  return items.map(item => ({
    volumeMl: calculateEstimatedVolume(item),
    confidenceScore: 0.7,
    estimationMethod: 'quick_estimate' as 'ai_estimation' | 'reference_object' | 'depth_sensing' | 'quick_estimate',
    portionSize: determinePortion(item.calories)
  })) as VolumeEstimate[];
}

/**
 * Estimate volume based on food item
 */
function calculateEstimatedVolume(item: DetectedFoodItem): number {
  // Estimate volume based on food category
  switch(item.category) {
    case 'protein':
      return 150 + Math.floor(Math.random() * 100); // 150-250ml
    case 'carb':
      return 200 + Math.floor(Math.random() * 150); // 200-350ml
    case 'vegetable':
      return 100 + Math.floor(Math.random() * 100); // 100-200ml
    case 'fruit':
      return 120 + Math.floor(Math.random() * 80); // 120-200ml
    case 'dessert':
      return 100 + Math.floor(Math.random() * 50); // 100-150ml
    case 'beverage':
      return 250 + Math.floor(Math.random() * 100); // 250-350ml
    case 'mixed':
      return 250 + Math.floor(Math.random() * 200); // 250-450ml
    default:
      return 200; // Default volume
  }
}

/**
 * Determine portion size based on calories
 */
function determinePortion(calories: number): 'small' | 'medium' | 'large' {
  if (calories < 100) return 'small';
  if (calories >= 100 && calories < 300) return 'medium';
  return 'large';
}

/**
 * Generate a meal description based on dish name and analysis
 */
function generateMealDescription(dishName: string, analysis: { items: { name: string }[], cuisineType: string }): string {
  const foodItemsText = analysis.items
    .map(item => item.name)
    .join(', ');
  
  return `This is ${dishName}, a ${analysis.cuisineType} dish. It includes ${foodItemsText}.`;
}

/**
 * Generate overall preparation method description based on items
 */
function generatePreparationMethod(items: any[]): string {
  const cookingMethods = new Set(items
    .map(item => item.cookingMethod)
    .filter(method => method && method !== 'unknown'));
  
  if (cookingMethods.size === 0) {
    return 'Mixed preparation methods';
  }
  
  return Array.from(cookingMethods).join(', ');
}

/**
 * Generate dietary categories based on the detected items
 */
function generateDietaryCategories(items: any[]): string[] {
  const categories: string[] = [];
  
  // Check total protein amount
  const totalProtein = items.reduce((sum, item) => sum + item.protein, 0);
  if (totalProtein > 20) categories.push('High Protein');
  
  // Check if low carb
  const totalCarbs = items.reduce((sum, item) => sum + item.carbs, 0);
  if (totalCarbs < 20) categories.push('Low Carb');
  else if (totalCarbs > 60) categories.push('High Carb');
  
  // Check if vegetarian or vegan
  const isVegetarian = items.every(item => item.isVegetarian === true);
  const isVegan = items.every(item => item.isVegan === true);
  
  if (isVegan) categories.push('Vegan');
  else if (isVegetarian) categories.push('Vegetarian');
  
  // Check if gluten-free
  const isGlutenFree = items.every(item => item.isGlutenFree === true);
  if (isGlutenFree) categories.push('Gluten-Free');
  
  // Add generic category if none found
  if (categories.length === 0) {
    categories.push('Standard Diet');
  }
  
  return categories;
}

/**
 * Get typical ingredients for a food item based on name and cuisine
 */
function getIngredientsForItem(itemName: string, cuisine: string): string[] {
  // Import the global food database function to get the food item
  const { getFoodByName } = require('./globalFoodDatabase');
  const foodItem = getFoodByName(itemName);
  
  if (foodItem && foodItem.ingredients && foodItem.ingredients.length > 0) {
    return foodItem.ingredients;
  }
  
  // Fallback to empty array if no ingredients found
  return ['Various Ingredients'];
}

/**
 * Determine if a food item is fresh or processed
 */
function getProcessingTypeForItem(item: any): 'fresh' | 'processed' | 'mixed' | 'unknown' {
  if (item.isFreshOrProcessed) {
    return item.isFreshOrProcessed;
  }
  
  // If not explicitly defined, make educated guess
  if (item.category === 'fruit' || item.category === 'vegetable') {
    return 'fresh';
  } else if (item.category === 'dessert' || item.cookingMethod === 'fried') {
    return 'processed';
  }
  
  return 'mixed'; // Default assumption for most prepared dishes
}

/**
 * Create an empty analysis result
 */
function createEmptyResult(message: string): UnifiedFoodAnalysisResult {
  return {
    foodItems: [],
    imageProperties: {
      dominantColors: ['#CCCCCC'],
      brightness: 0.5,
      contrast: 0.5,
      sharpness: 0.5,
      hasPlate: false
    },
    detailedFoodInfo: [],
    volumeInfo: [],
    nutritionalSummary: {
      totalCalories: 0,
      macronutrients: {
        protein: 0,
        carbs: 0,
        fat: 0,
        fiber: 0
      },
      mealType: 'unknown',
      isBalancedMeal: false,
      nutritionalQualityScore: 0
    },
    textualAnalysis: {
      description: message,
      preparationMethod: '',
      dietaryCategories: [],
      cuisineType: '',
      healthInsights: []
    },
    meta: {
      analysisTimestamp: Date.now(),
      modelVersion: 'enhanced-food-analysis-1.0',
      confidenceScore: 0,
      processingTimeMs: 0
    }
  };
}

function determineMealType(items: DetectedFoodItem[], calories: number): 'breakfast' | 'lunch' | 'dinner' | 'snack' | 'unknown' {
  // Check if items have defined meal types
  const breakfastItems = items.filter(item => item.name.includes('Breakfast') || 
                                             (item.cuisine === 'Filipino' && item.name.includes('Silog')));
  
  if (breakfastItems.length > 0) return 'breakfast';
  
  // Check based on calories
  if (calories < 400) return 'snack';
  if (calories >= 400 && calories < 650) return 'lunch';
  if (calories >= 650) return 'dinner';
  
  // Default fallback
  return 'unknown';
} 