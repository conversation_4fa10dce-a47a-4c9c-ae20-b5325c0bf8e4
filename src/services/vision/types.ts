// Common types used across vision services

export interface AnalyzeImageResponse {
  success: boolean;
  data?: {
    name: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    description?: string;
    items: {
      name: string;
      calories: number;
      protein: number;
      carbs: number;
      fat: number;
      confidence?: number;
      servingSize?: string;
      cookingMethod?: string;
      isFreshOrProcessed?: 'fresh' | 'processed' | 'mixed' | 'unknown';
      ingredients?: string[];
      estimatedAmount?: string;
    }[];
    imageUri?: string;
    segmentation?: {
      maskUri: string;
      foodMasks: {
        name: string;
        maskUri: string;
        color: string;
      }[];
    };
    confidence?: number;
    source?: string;
    cuisineType?: string;
    preparationMethod?: string;
    healthHighlights?: {
      positives: string[];
      considerations: string[];
    };
    servingInfo?: {
      servingSize: string;
      totalServings: number;
      caloriesPerServing: number;
      proteinPerServing: number;
      carbsPerServing: number;
      fatPerServing: number;
      fiberPerServing?: number;
      sugarPerServing?: number;
      sodiumPerServing?: number;
    };
    mealType?: string;
    allergens?: string[];
    isPartial?: boolean;
    partialAnalysisMessage?: string;
  };
  error?: string;
  processingTime?: number;
  cacheHit?: boolean;
  alternativeSources?: {
    source: string;
    data: any;
  }[];
}

export interface VolumeEstimate {
  volumeMl: number;
  confidenceScore: number;
  estimationMethod: 'ai_estimation' | 'reference_object' | 'depth_sensing' | 'quick_estimate';
  portionSize: 'small' | 'medium' | 'large';
  userAdjusted?: boolean;
  originalEstimate?: number;
}

export interface FoodDimensions {
  width: number;
  height: number;
  depth: number;
}

export interface FineGrainedFoodDetails {
  mainCategory: string;
  specificDish: string;
  ingredients: string[];
  cookingMethod: string;
  cuisineOrigin: string;
  isFreshOrProcessed: 'fresh' | 'processed' | 'mixed' | 'unknown';
  nutrition: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  };
  confidenceScore: number;
  qualityAssessment?: {
    presentationScore: number; // 0-1
    freshness: number; // 0-1
    colorVibrancy: number; // 0-1
    textureAppearance: string;
    authenticity?: number; // 0-1 (how authentic the dish appears)
  };
  recommendations?: string[];
  comparableFoods?: {
    name: string;
    caloriesDifference: number; // positive means more calories than current
    proteinDifference: number;
    carbsDifference: number;
    fatDifference: number;
  }[];
  recipeMatch?: {
    recipeName: string;
    matchConfidence: number;
    recipeUrl?: string;
    cookingTime?: string;
    difficulty?: 'easy' | 'medium' | 'hard';
  };
}

export interface ImageAnalysisOptions {
  useProgressiveLoading: boolean;
  bypassCache: boolean;
  includeVolumeEstimation: boolean;
  includeFineGrainedDetails: boolean;
  includeNutritionalAnalysis: boolean;
  includeTextualDescription: boolean;
  modelQuality: 'fast' | 'balanced' | 'accurate';
  includeDetailsLevel: 'basic' | 'standard' | 'comprehensive';
  includeImageQuality: boolean;
  includeIngredientDetection: boolean;
  includeFoodQualityAssessment: boolean;
  includeMultiView: boolean;
  includeBarcode: boolean;
  preferOfflineMode: boolean;
  nutritionalGoals?: {
    targetCalories: number;
    targetProtein: number;
    targetCarbs: number;
    targetFat: number;
  };
}

export interface UnifiedFoodAnalysisResult {
  foodItems: {
    name: string;
    confidence: number;
    boundingBox?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  }[];
  imageProperties: {
    dominantColors: string[];
    brightness: number; // 0-1 scale
    contrast: number; // 0-1 scale
    sharpness: number; // 0-1 scale
    hasPlate: boolean;
  };
  detailedFoodInfo: {
    mainCategory: string;
    specificDish: string;
    ingredients: string[];
    cookingMethod?: string;
    cuisineOrigin?: string;
    isFreshOrProcessed: 'fresh' | 'processed' | 'mixed' | 'unknown';
    nutrition: {
      calories: number;
      protein: number;
      carbs: number;
      fat: number;
      fiber?: number;
      sugar?: number;
      sodium?: number;
    };
    confidenceScore: number;
  }[];
  volumeInfo: {
    volumeMl: number;
    confidenceScore: number;
    estimationMethod: 'ai_estimation' | 'reference_object' | 'depth_sensing' | 'quick_estimate';
    portionSize: 'small' | 'medium' | 'large';
  }[];
  nutritionalSummary: {
    totalCalories: number;
    macronutrients: {
      protein: number;
      carbs: number;
      fat: number;
      fiber: number;
    };
    mealType: string;
    isBalancedMeal: boolean;
    nutritionalQualityScore: number; // 1-10 scale
  };
  textualAnalysis: {
    description: string;
    preparationMethod: string;
    dietaryCategories: string[];
    cuisineType: string;
    healthInsights: string[];
    allergens?: string[];
  };
  meta: {
    analysisTimestamp: number;
    modelVersion: string;
    confidenceScore: number;
    processingTimeMs: number;
    error?: string;
    isPartialResult?: boolean;
    timeoutOccurred?: boolean;
    apiCalls?: {
      vision?: boolean;
      llm?: boolean;
      database?: boolean;
    };
  };
  progressiveResults?: {
    stage: 'initial' | 'basic' | 'complete';
    progressPercentage: number;
    initialResultsTime?: number;
    fullResultsTime?: number;
  };
}

export interface MultiViewAnalysisInput {
  primaryImageData: string;
  secondaryImages: string[];
  options?: Partial<ImageAnalysisOptions>;
}

export interface ImageImprovementSuggestion {
  issue: 'lighting' | 'blur' | 'angle' | 'distance' | 'framing';
  description: string;
  severityScore: number; // 0-1
  improvementTip: string;
}

export interface FoodSegment {
  id: string;
  // Support both mask-based and bounding box approaches
  mask?: number[][];
  maskUri?: string;
  boundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  // Properties for confidence/score and appearance
  confidence?: number;
  score?: number;
  color?: string;
  area: number;
  // Food identification and nutrition data
  labeledAs?: string;
  nutrition?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  enhancedNutrition?: {
    name: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    ingredients?: string[];
    cookingMethod?: string;
    isFreshOrProcessed?: 'fresh' | 'processed' | 'mixed' | 'unknown';
  };
} 