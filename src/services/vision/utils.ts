/**
 * Helper functions used across vision services
 */

/**
 * Formats a food item name to be more readable
 */
export function formatFoodItemName(name: string): string {
  // Capitalize first letter of each word
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

/**
 * Capitalizes the first letter of a string
 */
export function capitalizeFirstLetter(string: string): string {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

/**
 * Checks if a category is food-related
 */
export function isFoodCategory(category: string): boolean {
  const foodKeywords = [
    'food', 'meal', 'dish', 'cuisine', 'breakfast', 'lunch', 'dinner',
    'snack', 'appetizer', 'dessert', 'fruit', 'vegetable', 'meat',
    'chicken', 'beef', 'pork', 'fish', 'seafood', 'pasta', 'rice',
    'pizza', 'sandwich', 'burger', 'salad', 'soup', 'stew', 'bread',
    'cake', 'cookie', 'pie', 'ice cream', 'chocolate', 'cheese',
    'dairy', 'egg', 'milk', 'yogurt', 'cereal', 'nuts', 'bean'
  ];
  
  const lowerCategory = category.toLowerCase();
  return foodKeywords.some(keyword => lowerCategory.includes(keyword));
}

/**
 * Checks if a label or object name is related to food
 */
export function isFoodRelated(item: string): boolean {
  if (!item) return false;
  
  const itemLower = item.toLowerCase();
  
  // List of non-food items to explicitly exclude
  const nonFoodItems = [
    // Cookware and utensils
    'plate', 'bowl', 'cup', 'glass', 'mug', 'fork', 'knife', 'spoon', 'chopstick', 'utensil',
    'container', 'jar', 'bottle', 'pan', 'pot', 'skillet', 'wok', 'dish', 'platter', 'tray',
    'spatula', 'ladle', 'whisk', 'colander', 'strainer',
    
    // Dining/kitchen furniture
    'table', 'chair', 'dining', 'kitchen', 'counter', 'countertop', 'tablecloth', 'napkin',
    
    // Materials
    'ceramic', 'porcelain', 'plastic', 'metal', 'paper', 'cardboard', 'wooden', 'wood',
    
    // Generic objects
    'object', 'item', 'material', 'product', 'tableware',
    
    // Technology/devices
    'device', 'phone', 'camera', 'computer', 'tablet', 'screen'
  ];
  
  // Check if the item is in our explicit non-food list
  // If it's only a non-food item (like just "plate" or "bowl") exclude it
  if (nonFoodItems.some(nonFood => nonFood === itemLower)) {
    return false;
  }
  
  // Extended list of food categories for better food detection
  const foodCategories = [
    // Basic food categories
    'food', 'cuisine', 'meal', 'breakfast', 'lunch', 'dinner',
    'fruit', 'vegetable', 'meat', 'dessert', 'snack', 'beverage', 'drink',
    
    // Common dishes and meal types
    'salad', 'soup', 'sandwich', 'pizza', 'pasta', 'rice', 'bread', 'cake',
    'stew', 'casserole', 'roast', 'grill', 'bake', 'boil', 'fry', 'stir-fry',
    'steak', 'burger', 'taco', 'burrito', 'wrap', 'roll', 'sushi', 'curry',
    'toast', 'pancake', 'waffle', 'cereal', 'oatmeal', 'granola',
    
    // Specific food ingredients
    'yogurt', 'egg', 'cheese', 'milk', 'cream', 'butter', 'oil',
    'chicken', 'beef', 'pork', 'lamb', 'fish', 'seafood', 'shrimp', 'salmon',
    'apple', 'banana', 'orange', 'berry', 'avocado', 'tomato', 'potato',
    'carrot', 'onion', 'garlic', 'herb', 'spice', 'pepper', 'salt',
    
    // Beverages
    'juice', 'coffee', 'tea', 'water', 'soda', 'beer', 'wine', 'cocktail',
    'smoothie', 'shake', 'latte', 'espresso', 'cappuccino'
  ];
  
  // More aggressive filtering for mixed items (like "plate of food" or "bowl of rice")
  if (itemLower.includes('plate') || itemLower.includes('bowl') || itemLower.includes('dish')) {
    // Must have an actual food item mentioned, not just the container
    return foodCategories.some(category => 
      itemLower.includes(category) && category !== 'food' && category !== 'dish' && category !== 'meal'
    );
  }
  
  // Check if the item contains any food category
  return foodCategories.some(category => 
    itemLower.includes(category.toLowerCase())
  );
}

/**
 * Calculates string similarity for fuzzy matching
 */
export function calculateStringSimilarity(str1: string, str2: string): number {
  // Simple implementation of Levenshtein distance-based similarity
  const maxLength = Math.max(str1.length, str2.length);
  if (maxLength === 0) return 1.0;
  
  // Check if one string contains the other
  if (str1.includes(str2) || str2.includes(str1)) {
    return 0.9;
  }
  
  // Check for partial matches
  const words1 = str1.split(' ');
  const words2 = str2.split(' ');
  let matchedWords = 0;
  
  for (const word1 of words1) {
    if (word1.length < 3) continue; // Skip very short words
    for (const word2 of words2) {
      if (word2.length < 3) continue;
      if (word1.includes(word2) || word2.includes(word1)) {
        matchedWords++;
        break;
      }
    }
  }
  
  const wordMatchRatio = matchedWords / Math.max(words1.length, words2.length);
  if (wordMatchRatio > 0) {
    return 0.5 + (wordMatchRatio * 0.4); // Scale between 0.5 and 0.9
  }
  
  // Basic character-level similarity as fallback
  let matches = 0;
  for (let i = 0; i < str1.length; i++) {
    if (str2.includes(str1[i])) {
      matches++;
    }
  }
  
  return matches / maxLength;
}

/**
 * Detects specific Filipino food items in an image
 * @param labels Food labels detected by general image recognition
 * @returns Detailed breakdown of Filipino food items
 */
export function detectFilipinoDishComponents(labels: string[]): {
  items: {
    name: string;
    confidence: number;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  }[];
  isFilipinoCuisine: boolean;
} {
  // Track whether this is likely Filipino cuisine
  let filipinoConfidence = 0;
  
  // Common Filipino breakfast items with nutrition data
  const filipinoFoodItems = [
    { 
      keywords: ['egg', 'fried egg', 'sunny side up'], 
      name: 'Sunny Side Up Egg',
      calories: 90,
      protein: 6,
      carbs: 0.5,
      fat: 7
    },
    { 
      keywords: ['rice', 'garlic rice', 'sinangag'], 
      name: 'Garlic Fried Rice (Sinangag)',
      calories: 200,
      protein: 4,
      carbs: 45,
      fat: 4
    },
    { 
      keywords: ['spam', 'fried spam', 'luncheon meat'], 
      name: 'Fried Spam',
      calories: 180,
      protein: 7,
      carbs: 1,
      fat: 16
    },
    { 
      keywords: ['sausage', 'longganisa', 'longanisa', 'filipino sausage'], 
      name: 'Longganisa (Filipino Sweet Sausage)',
      calories: 100,
      protein: 5,
      carbs: 3,
      fat: 7
    },
    { 
      keywords: ['hotdog', 'red hotdog', 'filipino hotdog'], 
      name: 'Filipino Red Hotdog',
      calories: 50,
      protein: 2,
      carbs: 2,
      fat: 4
    },
    { 
      keywords: ['beef', 'corned beef', 'canned beef'], 
      name: 'Corned Beef with Onions',
      calories: 100,
      protein: 7,
      carbs: 1,
      fat: 7
    },
    { 
      keywords: ['tocino', 'sweet pork', 'cured pork'], 
      name: 'Tocino (Sweet Cured Pork)',
      calories: 150,
      protein: 10,
      carbs: 5,
      fat: 10
    },
    { 
      keywords: ['tapa', 'beef tapa', 'dried beef'], 
      name: 'Beef Tapa (Cured Beef)',
      calories: 150,
      protein: 15,
      carbs: 2,
      fat: 9
    },
    { 
      keywords: ['tomato', 'tomatoes', 'sliced tomato'], 
      name: 'Tomato Slices',
      calories: 5,
      protein: 0.2,
      carbs: 1,
      fat: 0
    },
    { 
      keywords: ['eggplant', 'fried eggplant', 'aubergine'], 
      name: 'Fried Eggplant Slices',
      calories: 40,
      protein: 1,
      carbs: 5,
      fat: 2
    },
    { 
      keywords: ['banana', 'fried banana', 'saba', 'plantain'], 
      name: 'Fried Saba Banana',
      calories: 120,
      protein: 1,
      carbs: 28,
      fat: 3
    }
  ];

  // Filipino dish pattern indicators
  const filipinoDishIndicators = [
    'filipino breakfast', 'silog', 'tapsilog', 'longsilog', 'tosilog', 
    'spamsilog', 'cornsilog', 'hotsilog', 'bangsilog'
  ];
  
  // Check for Filipino dish indicators
  for (const indicator of filipinoDishIndicators) {
    if (labels.some(label => label.toLowerCase().includes(indicator))) {
      filipinoConfidence += 0.4;
    }
  }
  
  // If multiple Filipino food items are detected, it strengthens the confidence
  let detectedItems: {
    name: string;
    confidence: number;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  }[] = [];
  let itemsFound = 0;
  
  // Check for specific Filipino food items
  for (const filipinoItem of filipinoFoodItems) {
    for (const label of labels) {
      const labelLower = label.toLowerCase();
      
      // Check if any keywords match
      if (filipinoItem.keywords.some(keyword => labelLower.includes(keyword))) {
        // If match found, add to detected items
        detectedItems.push({
          name: filipinoItem.name,
          confidence: 0.8, // Using a fixed confidence for now; could be more dynamic
          calories: filipinoItem.calories,
          protein: filipinoItem.protein,
          carbs: filipinoItem.carbs,
          fat: filipinoItem.fat
        });
        
        itemsFound++;
        break; // Move to next item once we find a match
      }
    }
  }

  // If multiple items detected, increase Filipino cuisine confidence
  if (itemsFound >= 2) {
    filipinoConfidence += 0.2;
  }
  if (itemsFound >= 3) {
    filipinoConfidence += 0.3;
  }
  
  // Check for "silog" pattern - rice (sinangag) + egg (itlog) + protein
  const hasRice = detectedItems.some(item => item.name.includes('Rice'));
  const hasEgg = detectedItems.some(item => item.name.includes('Egg'));
  const hasProtein = detectedItems.some(item => 
    item.name.includes('Spam') || 
    item.name.includes('Longganisa') || 
    item.name.includes('Hotdog') ||
    item.name.includes('Beef') ||
    item.name.includes('Tocino') ||
    item.name.includes('Tapa')
  );
  
  if (hasRice && hasEgg && hasProtein) {
    filipinoConfidence += 0.5;
  }

  return {
    items: detectedItems,
    isFilipinoCuisine: filipinoConfidence > 0.6
  };
} 