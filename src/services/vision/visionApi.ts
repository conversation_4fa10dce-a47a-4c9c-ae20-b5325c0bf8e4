import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';
import { AnalysisCacheService } from './cacheService';
import { detectLabels as detectLabelsSecure } from '../openai/secureApiClient';

// Google Vision API endpoint
const GOOGLE_VISION_API_URL = 'https://vision.googleapis.com/v1/images:annotate';

/**
 * Detects labels in an image using Google Vision API (via secure Firebase function)
 * @param imageData Base64 encoded image data
 * @param bypassCache Optional flag to bypass cache and force fresh analysis
 * @returns Array of detected labels with scores
 */
export async function detectLabelsFromImage(imageData: string, bypassCache: boolean = false): Promise<any[]> {
  console.log('Using secure Firebase Cloud Function for Google Vision API');
  console.log('- Bypass cache:', bypassCache);
  
  // Check for cached results if not bypassing
  if (!bypassCache) {
    try {
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      // Generate cache key from first 1000 chars of image data
      const sampleData = imageData.substring(0, 1000);
      const cacheKey = `labels_${sampleData.length}_${sampleData.substring(0, 50).replace(/[^a-zA-Z0-9]/g, '')}`;
      
      const cachedLabels = await AsyncStorage.getItem(cacheKey);
      if (cachedLabels) {
        console.log('Using cached labels');
        return JSON.parse(cachedLabels);
      }
    } catch (cacheError) {
      console.warn('Cache check failed:', cacheError);
    }
  } else {
    console.log('Bypassing labels cache, performing fresh analysis');
  }
  
  try {
    console.log('Calling secure detectLabels function');
    
    // Convert base64 to data URL for the secure function
    const imageUrl = `data:image/jpeg;base64,${imageData}`;
    
    // Use secure Firebase Cloud Function
    const result = await detectLabelsSecure(imageUrl);
    
    // Extract labels from the secure response
    const labels = result.labels || [];
    
    // Cache the results if not bypassing cache
    if (!bypassCache) {
      try {
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;
        const sampleData = imageData.substring(0, 1000);
        const cacheKey = `labels_${sampleData.length}_${sampleData.substring(0, 50).replace(/[^a-zA-Z0-9]/g, '')}`;
        
        await AsyncStorage.setItem(cacheKey, JSON.stringify(labels));
        console.log('Cached label results');
      } catch (cacheError) {
        console.warn('Failed to cache labels:', cacheError);
      }
    }
    
    return labels;
  } catch (error) {
    console.error('Error detecting labels from image:', error);
    return [];
  }
}

/**
 * Convert an image URI to base64 format
 * @param uri Image URI to convert
 * @returns Base64 encoded image string
 */
export async function uriToBase64(uri: string): Promise<string> {
  try {
    console.log(`Converting image to base64. URI type: ${typeof uri}, Platform: ${Platform.OS}`);
    
    // Validate URI
    if (!uri || typeof uri !== 'string') {
      console.error('Invalid URI provided:', uri);
      throw new Error('Invalid image URI');
    }

    // Handle web platform differently
    if (Platform.OS === 'web') {
      console.log('Using web conversion path');
      try {
        const response = await fetch(uri);
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
        }
        const blob = await response.blob();
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            const result = reader.result as string;
            const base64 = result.split(',')[1];
            console.log(`Web base64 conversion successful. Result length: ${base64.length}`);
            resolve(base64);
          };
          reader.onerror = (e) => {
            console.error('FileReader error:', e);
            reject(new Error('Failed to read image file'));
          };
          reader.readAsDataURL(blob);
        });
      } catch (webError) {
        console.error('Web image conversion error:', webError);
        throw webError;
      }
    }

    // Handle native platforms
    console.log('Using native conversion path');
    // First check if the uri is a remote URL or a local file
    if (uri.startsWith('http')) {
      console.log('Remote URL detected, downloading first');
      try {
        // For remote URLs, we need to download the file first
        const tempPath = FileSystem.cacheDirectory + 'temp_image.jpg';
        const fileInfo = await FileSystem.getInfoAsync(tempPath);
        
        if (!fileInfo.exists) {
          console.log(`Downloading from ${uri} to ${tempPath}`);
          const downloadResult = await FileSystem.downloadAsync(uri, tempPath);
          console.log('Download result:', downloadResult);
        } else {
          console.log('Using existing cached download');
        }
        
        uri = tempPath;
      } catch (downloadError) {
        console.error('Error downloading remote image:', downloadError);
        throw new Error('Failed to download image from URL');
      }
    }
    
    try {
      // Verify file exists before trying to read it
      const fileInfo = await FileSystem.getInfoAsync(uri);
      if (!fileInfo.exists) {
        console.error('File does not exist:', uri);
        throw new Error('Image file does not exist');
      }
      
      console.log(`Reading file as base64: ${uri}`);
      // Read the file as base64
      const base64 = await FileSystem.readAsStringAsync(uri, {
        encoding: FileSystem.EncodingType.Base64
      });
      
      console.log(`Native base64 conversion successful. Result length: ${base64.length}`);
      return base64;
    } catch (readError) {
      console.error('Error reading file as base64:', readError);
      throw new Error('Failed to read image file as base64');
    }
  } catch (error) {
    console.error('Error converting image to base64:', error);
    throw error;
  }
}

// Vision model configuration
export const VISION_LANGUAGE_MODEL = {
  enabled: true,
  modelType: 'hybrid', // 'clip', 'llama', 'hybrid'
  confidenceThreshold: 0.75,
  
  // Modern multimodal CLIP-based embedding model for food recognition
  clipModel: {
    name: 'nutrition-clip-vit-large-2025',
    imageEncoder: 'ViT-L/14',
    textEncoder: 'roberta-large-v2',
    embeddingDimension: 768,
    contextLength: 128,
    maxBatchSize: 16
  },
  
  // Large language model for enhanced food descriptions and reasoning
  llmModel: {
    name: 'llama-3-8b-food-nutrition-2025',
    maxTokens: 1024,
    temperature: 0.4,
    topP: 0.95,
    presencePenalty: 0.0,
    frequencyPenalty: 0.0
  }
};

/**
 * Detect objects in an image using Google Cloud Vision API
 * @param imageUri URI of the image to analyze
 * @param bypassCache Optional flag to bypass cache and force fresh analysis
 * @returns Array of detected objects with bounding boxes and scores
 */
export async function detectObjectsWithGoogleVision(imageUri: string, bypassCache: boolean = false) {
  try {
    console.log(`Detecting objects in image: ${imageUri}, bypassCache: ${bypassCache}`);
    
    // Only check cache if we're not bypassing it
    if (!bypassCache) {
      try {
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;
        // Generate a more specific cache key that just uses the base URI without query params
        const baseUri = imageUri.split('?')[0];
        const cacheKey = `google_vision_${baseUri}`;
        console.log(`Checking cache with key: ${cacheKey}`);
        
        const cachedData = await AsyncStorage.getItem(cacheKey);
        
        if (cachedData) {
          console.log('Using cached Google Vision result');
          return JSON.parse(cachedData);
        } else {
          console.log('No cached result found, proceeding with API call');
        }
      } catch (cacheError) {
        console.log('Cache check failed, proceeding with API call', cacheError);
      }
    } else {
      console.log('Cache bypassed, performing fresh analysis');
    }
    
    if (!GOOGLE_VISION_API_KEY) {
      throw new Error('Google Vision API key is not configured');
    }
    
    // Convert image to base64
    const base64Image = await uriToBase64(imageUri);
    console.log(`Successfully converted image to base64, length: ${base64Image.length}`);
    
    // Prepare request for Google Vision API
    const request = {
      requests: [
        {
          image: {
            content: base64Image
          },
          features: [
            {
              type: 'OBJECT_LOCALIZATION',
              maxResults: 10
            },
            {
              type: 'LABEL_DETECTION',
              maxResults: 10
            }
          ]
        }
      ]
    };
    
    // Call Google Vision API
    console.log('Calling Google Vision API');
    const response = await fetch(`${GOOGLE_VISION_API_URL}?key=${GOOGLE_VISION_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(request)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Google Vision API error:', errorText);
      throw new Error(`Google Vision API error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json() as {
      responses?: {
        localizedObjectAnnotations?: any[];
        labelAnnotations?: any[];
      }[];
    };
    
    // Process results
    const objects = data.responses?.[0]?.localizedObjectAnnotations || [];
    const labels = data.responses?.[0]?.labelAnnotations || [];
    
    console.log(`Google Vision API returned: ${objects.length} objects, ${labels.length} labels`);
    
    // Create normalized result
    const result = {
      objects: objects.map((obj: any) => ({
        name: obj.name,
        score: obj.score,
        boundingBox: {
          topLeft: {
            x: obj.boundingPoly.normalizedVertices[0].x || 0,
            y: obj.boundingPoly.normalizedVertices[0].y || 0
          },
          bottomRight: {
            x: obj.boundingPoly.normalizedVertices[2].x || 1,
            y: obj.boundingPoly.normalizedVertices[2].y || 1
          }
        }
      })),
      labels: labels.map((label: any) => ({
        description: label.description,
        score: label.score
      })),
      foodItems: objects
        .filter((obj: any) => isFoodObject(obj.name))
        .map((obj: any) => ({
          name: obj.name,
          score: obj.score,
          boundingBox: {
            topLeft: {
              x: obj.boundingPoly.normalizedVertices[0].x || 0,
              y: obj.boundingPoly.normalizedVertices[0].y || 0
            },
            bottomRight: {
              x: obj.boundingPoly.normalizedVertices[2].x || 1,
              y: obj.boundingPoly.normalizedVertices[2].y || 1
            }
          }
        }))
    };
    
    // Filter food related labels
    const foodLabels = labels
      .filter((label: any) => isFoodLabel(label.description))
      .map((label: any) => ({
        name: label.description,
        score: label.score
      }));
    
    // Combine food objects and labels
    result.foodItems = [...result.foodItems, ...foodLabels.map((label: any) => ({
      name: label.name,
      score: label.score,
      boundingBox: null // Labels don't have bounding boxes
    }))] as any;
    
    // Remove duplicates and sort by score
    result.foodItems = removeDuplicateFoodItems(result.foodItems);
    
    // Cache the result only if we're not bypassing the cache
    if (!bypassCache) {
      try {
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;
        const baseUri = imageUri.split('?')[0];
        const cacheKey = `google_vision_${baseUri}`;
        await AsyncStorage.setItem(cacheKey, JSON.stringify(result));
        console.log(`Cached result with key: ${cacheKey}`);
      } catch (cacheError) {
        console.log('Failed to cache result', cacheError);
      }
    } else {
      console.log('Skipping cache storage as bypassCache is enabled');
    }
    
    return result;
  } catch (error) {
    console.error('Error detecting objects with Google Vision:', error);
    throw error;
  }
}

/**
 * Check if an object name is food-related
 * @param name Object name to check
 * @returns Boolean indicating if the object is food
 */
function isFoodObject(name: string): boolean {
  const foodCategories = [
    'Food', 'Fruit', 'Vegetable', 'Dessert', 'Baked goods', 'Dish', 'Cuisine',
    'Bread', 'Meat', 'Fast food', 'Produce', 'Ingredient', 'Seafood',
    'Snack', 'Drink', 'Beverage', 'Salad', 'Sandwich', 'Breakfast', 'Lunch',
    'Dinner', 'Cake', 'Pasta', 'Rice', 'Soup', 'Pizza', 'Burger', 'Taco',
    'Cheese', 'Egg', 'Fish', 'Chicken', 'Beef', 'Pork', 'Cookie', 'Ice cream'
  ];
  
  return foodCategories.some(category => 
    name.toLowerCase().includes(category.toLowerCase()) || 
    category.toLowerCase().includes(name.toLowerCase())
  );
}

/**
 * Check if a label is food-related
 * @param description Label description
 * @returns Boolean indicating if the label is food
 */
function isFoodLabel(description: string): boolean {
  const foodKeywords = [
    'food', 'dish', 'meal', 'cuisine', 'dinner', 'lunch', 'breakfast',
    'snack', 'dessert', 'baked', 'fried', 'grilled', 'roasted', 'steamed',
    'recipe', 'ingredient', 'cooked', 'eating', 'dietary', 'nutrition',
    'tasty', 'delicious', 'savory', 'sweet', 'menu', 'restaurant', 'cafe',
    'homemade', 'organic', 'vegetarian', 'vegan', 'pescatarian', 'carnivore'
  ];
  
  return foodKeywords.some(keyword => 
    description.toLowerCase().includes(keyword)
  );
}

/**
 * Remove duplicate food items by name and prefer items with bounding boxes
 * @param items Array of detected food items
 * @returns Deduplicated array of food items
 */
function removeDuplicateFoodItems(items: any[]) {
  const uniqueItems = new Map();
  
  // Process items with bounding boxes first, then those without
  const sortedItems = [...items].sort((a, b) => {
    // Prefer items with bounding boxes
    if (a.boundingBox && !b.boundingBox) return -1;
    if (!a.boundingBox && b.boundingBox) return 1;
    
    // If both have or don't have bounding boxes, prefer higher score
    return b.score - a.score;
  });
  
  // Keep only the highest scoring item for each name
  for (const item of sortedItems) {
    const normalizedName = item.name.toLowerCase();
    if (!uniqueItems.has(normalizedName)) {
      uniqueItems.set(normalizedName, item);
    }
  }
  
  return Array.from(uniqueItems.values());
}

/**
 * Crop an image to a bounding box using Expo FileSystem
 * @param imageUri URI of the image to crop
 * @param boundingBox Bounding box coordinates (normalized 0-1)
 * @returns URI of the cropped image
 */
export async function cropImageToBoundingBox(imageUri: string, boundingBox: {
  topLeft: { x: number, y: number },
  bottomRight: { x: number, y: number }
}) {
  try {
    // This function is a placeholder as Expo FileSystem doesn't natively support
    // image manipulation. In a real application, you would use:
    // 1. expo-image-manipulator
    // 2. Send the cropping to a server
    // 3. Use a native module
    
    console.log('Image cropping requested:', boundingBox);
    
    // For now, we'll just return the original image
    return imageUri;
  } catch (error) {
    console.error('Error cropping image:', error);
    return imageUri;
  }
} 