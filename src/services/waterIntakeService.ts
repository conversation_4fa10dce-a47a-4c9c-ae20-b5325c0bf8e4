import AsyncStorage from '@react-native-async-storage/async-storage';

import { getUserProfile } from './nutritionGoalService';
import { format } from 'date-fns';
import { db } from '@/lib/firebase';
import { useDatabaseType } from '@/contexts/DatabaseContext';
import { getDoc, doc, collection, query, where, getDocs, addDoc, updateDoc, deleteDoc, orderBy, setDoc } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { Platform } from 'react-native';
import * as offlineDataFallback from '@/utils/offlineDataFallback';
import { trackFeatureError, trackApiError } from './errorTrackingService';
import { trackEvent } from './posthogService';

// Storage keys
const WATER_INTAKE_KEY = 'water_intake_';
const WATER_GOALS_KEY = 'water_goals';

// Default values
const DEFAULT_DAILY_GOAL = 2000; // 2000ml (2 liters) as a default goal
const STANDARD_GLASS_SIZE = 250; // ml
const STANDARD_BOTTLE_SIZE = 500; // ml

// Intake record type
export interface WaterIntakeRecord {
  id: string;
  date: string; // ISO format date (YYYY-MM-DD)
  time: string; // 24h format (HH:MM)
  amount: number; // in milliliters
  source?: 'glass' | 'bottle' | 'other'; // tracking source
  notes?: string;
}

// Goals and settings
export interface WaterIntakeGoals {
  dailyGoal: number; // in milliliters
  standardGlassSize: number; // in milliliters
  standardBottleSize: number; // in milliliters
  remindersEnabled: boolean;
  reminderInterval?: number; // in minutes
  wakeTime?: string; // HH:MM format
  sleepTime?: string; // HH:MM format
}

// Add timeout to prevent operations from hanging
const withTimeout = (promise, timeoutMs) => {
  const timeout = new Promise((_, reject) => {
    const id = setTimeout(() => {
      clearTimeout(id);
      reject(new Error(`Operation timed out after ${timeoutMs}ms`));
    }, timeoutMs);
  });
  return Promise.race([promise, timeout]);
};

/**
 * Hook to access water intake functionality that works with Firebase
 */
export function useWaterIntakeService() {
  const { db, useFirebase } = useDatabaseType();
  
  /**
   * Get the user's water intake goal
   */
  const getWaterIntakeGoal = async (userId: string) => {
    try {
      // Check if we should use offline fallbacks
      if (Platform.OS === 'ios' || await offlineDataFallback.shouldUseOfflineFallbacks()) {
        const config = await offlineDataFallback.getAppConfig();
        return {
          user_id: userId,
          daily_target_ml: config?.water_tracking?.default_goal || 2000,
          unit_preference: 'ml',
        };
      }
      
      const collection = 'water_intake_goals';
      const goal = await db.getDocument(collection, userId);
      return goal;
    } catch (error) {
      console.error('Error getting water intake goal:', error);
      // Return a default goal if none is found
      return {
        user_id: userId,
        daily_target_ml: 2000, // Default 2L
        unit_preference: 'ml',
      };
    }
  };
  
  /**
   * Set the user's water intake goal
   */
  const setWaterIntakeGoal = async (userId: string, dailyTargetMl: number, unitPreference: string = 'ml') => {
    try {
      const data = {
        user_id: userId,
        daily_target_ml: dailyTargetMl,
        unit_preference: unitPreference,
        updated_at: new Date().toISOString(),
      };
      
      // For iOS, prioritize local storage
      if (Platform.OS === 'ios' || await offlineDataFallback.shouldUseOfflineFallbacks()) {
        // Get existing app config
        const config = await offlineDataFallback.getAppConfig();
        
        // Update water tracking config
        const updatedConfig = {
          ...config,
          water_tracking: {
            ...config.water_tracking,
            default_goal: dailyTargetMl
          },
          updated_at: new Date().toISOString()
        };
        
        // Save to local storage
        await AsyncStorage.setItem('essential_app_config', JSON.stringify(updatedConfig));
        
        // Record as pending operation
        await offlineDataFallback.recordPendingOperation('water_intake_goals', 'update', data);
        
        return { success: true };
      }
      
      // Try Firebase if not using fallbacks
      const collection = 'water_intake_goals';
      
      // Check if goal already exists
      try {
        const existing = await db.getDocument(collection, userId);
        if (existing) {
          await db.updateDocument(collection, userId, data);
        } else {
          await db.createDocument(collection, { id: userId, ...data });
        }
      } catch (error) {
        // If getting the document failed, just create a new one
        await db.createDocument(collection, { id: userId, ...data });
      }
      
      return { success: true };
    } catch (error) {
      console.error('Error setting water intake goal:', error);
      return { success: false, error };
    }
  };
  
  /**
   * Get the user's water intake records for a specific date
   */
  const getWaterIntakeForDate = async (userId: string, date: Date) => {
    try {
      const dateStr = format(date, 'yyyy-MM-dd');
      
      // For iOS, prioritize local storage
      if (Platform.OS === 'ios' || await offlineDataFallback.shouldUseOfflineFallbacks()) {
        // Get from local storage
        const key = `${WATER_INTAKE_KEY}${dateStr}`;
        const recordsJson = await AsyncStorage.getItem(key);
        
        if (recordsJson) {
          return JSON.parse(recordsJson);
        }
        
        return [];
      }
      
      // Try Firebase if not using fallbacks
      const collection = 'water_intake_records';
      
      // Create a filter for the date
      const filters = [
        { field: 'user_id', operator: '==', value: userId },
        { field: 'date', operator: '==', value: dateStr },
      ];
      
      const records = await db.queryDocuments(collection, filters);
      
      // Cache results in local storage 
      await AsyncStorage.setItem(`${WATER_INTAKE_KEY}${dateStr}`, JSON.stringify(records));
      
      return records;
    } catch (error) {
      console.error('Error getting water intake records:', error);
      return [];
    }
  };
  
  /**
   * Add a water intake record
   */
  const addWaterIntake = async (userId: string, amountMl: number, date: Date = new Date()) => {
    try {
      const dateStr = format(date, 'yyyy-MM-dd');
      const timeStr = format(date, 'HH:mm:ss');
      
      const data = {
        user_id: userId,
        amount_ml: amountMl,
        date: dateStr,
        time: timeStr,
        created_at: new Date().toISOString(),
      };
      
      // For iOS, prioritize local storage
      if (Platform.OS === 'ios' || await offlineDataFallback.shouldUseOfflineFallbacks()) {
        // Generate a temporary ID
        const tempId = `local_${Date.now()}`;
        
        // Get existing records
        const recordsJson = await AsyncStorage.getItem(`${WATER_INTAKE_KEY}${dateStr}`);
        const existingRecords = recordsJson ? JSON.parse(recordsJson) : [];
        
        // Add new record
        const newRecord = { id: tempId, ...data };
        const updatedRecords = [...existingRecords, newRecord];
        
        // Save to local storage
        await AsyncStorage.setItem(`${WATER_INTAKE_KEY}${dateStr}`, JSON.stringify(updatedRecords));
        
        // Record as pending operation
        await offlineDataFallback.recordPendingOperation('water_intake_records', 'add', data);
        
        return { success: true, id: tempId };
      }
      
      // Try Firebase if not using fallbacks
      const collection = 'water_intake_records';
      
      // Add record to database
      const recordId = await db.createDocument(collection, data);
      
      // Update local cache
      const recordsJson = await AsyncStorage.getItem(`${WATER_INTAKE_KEY}${dateStr}`);
      const existingRecords = recordsJson ? JSON.parse(recordsJson) : [];
      const updatedRecords = [...existingRecords, { id: recordId, ...data }];
      await AsyncStorage.setItem(`${WATER_INTAKE_KEY}${dateStr}`, JSON.stringify(updatedRecords));
      
      return { success: true, id: recordId };
    } catch (error) {
      console.error('Error adding water intake:', error);
      return { success: false, error };
    }
  };
  
  /**
   * Delete a water intake record
   */
  const deleteWaterIntake = async (recordId: string, date: string) => {
    try {
      // For iOS, prioritize local storage
      if (Platform.OS === 'ios' || await offlineDataFallback.shouldUseOfflineFallbacks()) {
        // Get existing records
        const recordsJson = await AsyncStorage.getItem(`${WATER_INTAKE_KEY}${date}`);
        const existingRecords = recordsJson ? JSON.parse(recordsJson) : [];
        
        // Filter out the record to delete
        const updatedRecords = existingRecords.filter(record => record.id !== recordId);
        
        // Save to local storage
        await AsyncStorage.setItem(`${WATER_INTAKE_KEY}${date}`, JSON.stringify(updatedRecords));
        
        // Record as pending operation if it's not a local ID (starts with "local_")
        if (!recordId.startsWith('local_')) {
          await offlineDataFallback.recordPendingOperation('water_intake_records', 'delete', { id: recordId });
        }
        
        return { success: true };
      }
      
      // Try Firebase if not using fallbacks
      const collection = 'water_intake_records';
      
      // Delete from database
      await db.deleteDocument(collection, recordId);
      
      // Update local cache
      const recordsJson = await AsyncStorage.getItem(`${WATER_INTAKE_KEY}${date}`);
      const existingRecords = recordsJson ? JSON.parse(recordsJson) : [];
      const updatedRecords = existingRecords.filter(record => record.id !== recordId);
      await AsyncStorage.setItem(`${WATER_INTAKE_KEY}${date}`, JSON.stringify(updatedRecords));
      
      return { success: true };
    } catch (error) {
      console.error('Error deleting water intake:', error);
      return { success: false, error };
    }
  };
  
  /**
   * Get the user's water intake summary for a specific date
   */
  const getWaterIntakeSummary = async (userId: string, date: Date = new Date()) => {
    try {
      const records = await getWaterIntakeForDate(userId, date);
      const goal = await getWaterIntakeGoal(userId);
      
      // Calculate total intake
      const totalIntake = records.reduce((sum, record) => sum + (record.amount_ml || 0), 0);
      
      // Calculate completion percentage
      const percentage = Math.min(
        Math.round((totalIntake / goal.daily_target_ml) * 100), 
        100
      );
      
      return {
        date: format(date, 'yyyy-MM-dd'),
        records,
        totalIntake,
        goal: goal.daily_target_ml,
        unitPreference: goal.unit_preference,
        percentage,
      };
    } catch (error) {
      console.error('Error getting water intake summary:', error);
      return {
        date: format(date, 'yyyy-MM-dd'),
        records: [],
        totalIntake: 0,
        goal: 2000,
        unitPreference: 'ml',
        percentage: 0,
      };
    }
  };
  
  return {
    getWaterIntakeGoal,
    setWaterIntakeGoal,
    getWaterIntakeForDate,
    addWaterIntake,
    deleteWaterIntake,
    getWaterIntakeSummary,
  };
}

// Helper function to get current user ID
async function getCurrentUserId(): Promise<string> {
  const auth = getAuth();
  const user = auth.currentUser;
  
  // For iOS or offline mode, try to get from local storage
  if (!user) {
    if (Platform.OS === 'ios' || await offlineDataFallback.isInOfflineMode()) {
      const userData = await offlineDataFallback.getLocalUserData();
      if (userData && userData.uid) {
        return userData.uid;
      } else {
        // Generate a temporary local user ID
        const tempId = `local_${Date.now()}`;
        await offlineDataFallback.storeLocalUserData({ uid: tempId });
        return tempId;
      }
    }
    throw new Error('User not authenticated');
  }
  
  return user.uid;
}

/**
 * Add a water intake record
 */
export async function addWaterIntake(
  amount: number,
  source: 'glass' | 'bottle' | 'other' = 'glass',
  notes?: string
): Promise<WaterIntakeRecord> {
  try {
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
    const timeStr = now.toLocaleTimeString('en-GB', {
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false
    });
    
    let record: WaterIntakeRecord = {
      id: Date.now().toString(), // Temporary ID, will be replaced by Firebase ID
      date: dateStr,
      time: timeStr,
      amount,
      source,
      notes
    };
    
    // Check if we need to use offline fallback (especially for iOS)
    try {
      // Import the offline fallback utilities
      const { isIOSEnvironment, storeLocalWaterIntake, shouldUseOfflineFallbacks } = await import('../utils/offlineDataFallback');
      
      // For iOS, prioritize local storage to avoid Firebase connection issues
      if (await shouldUseOfflineFallbacks() || isIOSEnvironment()) {
        console.log('Using offline fallback for adding water intake');
        
        // Load existing records from local storage
        const storageKey = `${WATER_INTAKE_KEY}${dateStr}`;
        const existingRecordsJson = await AsyncStorage.getItem(storageKey);
        const existingRecords = existingRecordsJson ? JSON.parse(existingRecordsJson) : [];
        
        // Add new record to existing records
        const updatedRecords = [...existingRecords, record];
        
        // Save to local storage
        await AsyncStorage.setItem(storageKey, JSON.stringify(updatedRecords));
        
        // Also update daily total in the dedicated offline storage
        await storeLocalWaterIntake(dateStr, amount);
        
        console.log('Successfully saved water intake to local storage with ID:', record.id);
        return record;
      }
    } catch (fallbackError) {
      console.warn('Error using offline fallbacks:', fallbackError);
      // Continue with regular flow if offline fallback fails
    }
    
    // Try to save to Firebase
    try {
      const userId = await getCurrentUserId();
      
      const waterIntakeData = {
        user_id: userId,
        date: dateStr,
        time: timeStr,
        amount,
        source,
        notes,
        created_at: new Date().toISOString()
      };
      
      const docRef = await addDoc(collection(db, 'water_intake'), waterIntakeData);
      record.id = docRef.id; // Update the ID with Firebase ID
      console.log('Successfully saved water intake to Firebase with ID:', record.id);
      
      // Get existing records for today and update local storage
      // Load existing records directly from Firebase to ensure consistency
      const updatedRecords = await getWaterIntakeForDate(dateStr);
      
      // Save to local storage - don't need to add record again since getWaterIntakeForDate 
      // will already include it from Firebase
      await AsyncStorage.setItem(
        `${WATER_INTAKE_KEY}${dateStr}`,
        JSON.stringify(updatedRecords)
      );
      
    } catch (dbError) {
      console.error('Firebase error in addWaterIntake:', dbError);
      // If Firebase fails, save to local storage with the temporary ID
      const todayRecords = await getWaterIntakeForDate(dateStr);
      const updatedRecords = [...todayRecords, record];
      
      await AsyncStorage.setItem(
        `${WATER_INTAKE_KEY}${dateStr}`,
        JSON.stringify(updatedRecords)
      );
    }
    
    return record;
  } catch (error) {
    console.error('Error adding water intake:', error);
    throw new Error('Failed to add water intake record');
  }
}

/**
 * Remove a water intake record
 */
export async function removeWaterIntake(recordId: string, date: string): Promise<boolean> {
  try {
    // Try to remove from Firebase
    try {
      await deleteDoc(doc(db, 'water_intake', recordId));
      console.log('Successfully removed water intake from Firebase with ID:', recordId);
    } catch (dbError) {
      console.error('Firebase error in removeWaterIntake:', dbError);
    }
    
    // Get existing records for date
    const records = await getWaterIntakeForDate(date);
    
    // Filter out the record to remove
    const updatedRecords = records.filter(record => record.id !== recordId);
    
    // Save updated records to local storage
    await AsyncStorage.setItem(
      `${WATER_INTAKE_KEY}${date}`,
      JSON.stringify(updatedRecords)
    );
    
    return true;
  } catch (error) {
    console.error('Error removing water intake:', error);
    return false;
  }
}

/**
 * Get water intake records for a date range
 */
export async function getWaterIntakeForDateRange(
  startDate: string,
  endDate: string
): Promise<Record<string, WaterIntakeRecord[]>> {
  try {
    const result: Record<string, WaterIntakeRecord[]> = {};
    
    // Generate list of dates in range
    const dates = getDatesInRange(startDate, endDate);
    
    // Get records for each date
    for (const date of dates) {
      const records = await getWaterIntakeForDate(date);
      result[date] = records;
    }
    
    return result;
  } catch (error) {
    console.error('Error getting water intake for date range:', error);
    return {};
  }
}

/**
 * Get total water intake for a specific date
 */
export async function getTotalWaterIntakeForDate(date: string): Promise<number> {
  try {
    const records = await getWaterIntakeForDate(date);
    
    return records.reduce((total, record) => total + record.amount, 0);
  } catch (error) {
    console.error('Error getting total water intake for date:', error);
    return 0;
  }
}

/**
 * Get total water intake for a date range
 */
export async function getTotalWaterIntakeForDateRange(
  startDate: string,
  endDate: string
): Promise<Record<string, number>> {
  try {
    const intakeByDate: Record<string, number> = {};
    
    // Get records for date range
    const recordsByDate = await getWaterIntakeForDateRange(startDate, endDate);
    
    // Calculate total for each date
    for (const [date, records] of Object.entries(recordsByDate)) {
      intakeByDate[date] = records.reduce((total, record) => total + record.amount, 0);
    }
    
    return intakeByDate;
  } catch (error) {
    console.error('Error getting total water intake for date range:', error);
    return {};
  }
}

/**
 * Get water intake goals
 */
export async function getWaterIntakeGoals(): Promise<WaterIntakeGoals> {
  try {
    // Try to get goals from local storage
    const goalsJson = await AsyncStorage.getItem(WATER_GOALS_KEY);
    
    if (goalsJson) {
      return JSON.parse(goalsJson);
    }
    
    // Try to get from Firebase if available
    try {
      const userId = await getCurrentUserId();
      
      // Try to get the user's goals
      const goalsDoc = await getDoc(doc(db, 'water_intake_goals', userId));
      
      if (goalsDoc.exists()) {
        const data = goalsDoc.data();
        const goals: WaterIntakeGoals = {
          dailyGoal: (data.daily_goal as number) || DEFAULT_DAILY_GOAL,
          standardGlassSize: (data.standard_glass_size as number) || STANDARD_GLASS_SIZE,
          standardBottleSize: (data.standard_bottle_size as number) || STANDARD_BOTTLE_SIZE,
          remindersEnabled: (data.reminders_enabled as boolean) || false,
          reminderInterval: data.reminder_interval as number | undefined,
          wakeTime: data.wake_time as string | undefined,
          sleepTime: data.sleep_time as string | undefined
        };
        
        // Cache in local storage
        await AsyncStorage.setItem(WATER_GOALS_KEY, JSON.stringify(goals));
        
        return goals;
      } else {
        // No goals found for this user, create default ones
        return await createDefaultWaterIntakeGoals(userId);
      }
    } catch (dbError) {
      console.error('Firebase error:', dbError);
    }
    
    // Create and return default goals if we can't retrieve them from Firebase
    return await createDefaultGoals();
  } catch (error) {
    console.error('Error getting water intake goals:', error);
    
    // Return default goals if there's an error
    return {
      dailyGoal: DEFAULT_DAILY_GOAL,
      standardGlassSize: STANDARD_GLASS_SIZE,
      standardBottleSize: STANDARD_BOTTLE_SIZE,
      remindersEnabled: false
    };
  }
}

/**
 * Create default water intake goals for a user in Firebase
 */
async function createDefaultWaterIntakeGoals(userId: string): Promise<WaterIntakeGoals> {
  try {
    // Calculate default daily goal based on user profile if available
    let dailyGoal = DEFAULT_DAILY_GOAL;
    
    try {
      const userProfile = await getUserProfile();
      if (userProfile && userProfile.weight) {
        // Calculate recommended water intake based on weight and activity level
        // 30ml per kg of body weight + activity level adjustment
        dailyGoal = Math.round(userProfile.weight * 30);
        
        // Adjust for activity level
        if (userProfile.activityLevel && userProfile.activityLevel.toLowerCase() === 'high') {
          dailyGoal = Math.round(dailyGoal * 1.2); // 20% more for high activity
        } else if (userProfile.activityLevel && userProfile.activityLevel.toLowerCase() === 'low') {
          dailyGoal = Math.round(dailyGoal * 0.9); // 10% less for low activity
        }
      }
    } catch (profileError) {
      console.error('Error getting user profile for water goal calculation:', profileError);
    }
    
    // Create default goals
    const defaultGoals: WaterIntakeGoals = {
      dailyGoal,
      standardGlassSize: STANDARD_GLASS_SIZE,
      standardBottleSize: STANDARD_BOTTLE_SIZE,
      remindersEnabled: false
    };
    
    // Save to Firebase if possible
    try {
      await setDoc(doc(db, 'water_intake_goals', userId), {
        user_id: userId,
        daily_goal: defaultGoals.dailyGoal,
        standard_glass_size: defaultGoals.standardGlassSize,
        standard_bottle_size: defaultGoals.standardBottleSize,
        reminders_enabled: defaultGoals.remindersEnabled,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    } catch (err) {
      console.error('Failed to save default water intake goals to Firebase:', err);
    }
    
    // Save to AsyncStorage
    await AsyncStorage.setItem(WATER_GOALS_KEY, JSON.stringify(defaultGoals));
    
    return defaultGoals;
  } catch (error) {
    console.error('Error creating default water intake goals:', error);
    const basicDefaults = {
      dailyGoal: DEFAULT_DAILY_GOAL,
      standardGlassSize: STANDARD_GLASS_SIZE,
      standardBottleSize: STANDARD_BOTTLE_SIZE,
      remindersEnabled: false
    };
    
    await AsyncStorage.setItem(WATER_GOALS_KEY, JSON.stringify(basicDefaults));
    
    return basicDefaults;
  }
}

/**
 * Create default water intake goals
 */
async function createDefaultGoals(): Promise<WaterIntakeGoals> {
  const defaultGoals: WaterIntakeGoals = {
    dailyGoal: DEFAULT_DAILY_GOAL,
    standardGlassSize: STANDARD_GLASS_SIZE,
    standardBottleSize: STANDARD_BOTTLE_SIZE,
    remindersEnabled: false
  };
  
  await AsyncStorage.setItem(WATER_GOALS_KEY, JSON.stringify(defaultGoals));
  
  return defaultGoals;
}

/**
 * Set water intake goals
 */
export async function setWaterIntakeGoals(goals: WaterIntakeGoals): Promise<boolean> {
  try {
    // Save to AsyncStorage
    await AsyncStorage.setItem(WATER_GOALS_KEY, JSON.stringify(goals));
    
    // Save to Firebase if possible
    try {
      const userId = await getCurrentUserId();
      
      await setDoc(doc(db, 'water_intake_goals', userId), {
        user_id: userId,
        daily_goal: goals.dailyGoal,
        standard_glass_size: goals.standardGlassSize,
        standard_bottle_size: goals.standardBottleSize,
        reminders_enabled: goals.remindersEnabled,
        reminder_interval: goals.reminderInterval,
        wake_time: goals.wakeTime,
        sleep_time: goals.sleepTime,
        updated_at: new Date().toISOString()
      });
    } catch (dbError) {
      console.error('Firebase error in setWaterIntakeGoals:', dbError);
    }
    
    return true;
  } catch (error) {
    console.error('Error setting water intake goals:', error);
    return false;
  }
}

/**
 * Get water intake progress for a date
 */
export async function getWaterIntakeProgress(date: string): Promise<{
  total: number;
  goal: number;
  percentage: number;
}> {
  try {
    // Get goals
    const { dailyGoal } = await getWaterIntakeGoals();
    
    // Get total intake for the date
    const totalIntake = await getTotalWaterIntakeForDate(date);
    
    // Calculate percentage
    const percentage = Math.min(Math.round((totalIntake / dailyGoal) * 100), 100);
    
    return {
      total: totalIntake,
      goal: dailyGoal,
      percentage
    };
  } catch (error) {
    console.error('Error getting water intake progress:', error);
    return {
      total: 0,
      goal: DEFAULT_DAILY_GOAL,
      percentage: 0
    };
  }
}

/**
 * Get water intake statistics for a date range
 */
export async function getWaterIntakeStatistics(
  startDate: string,
  endDate: string
): Promise<{
  averageIntake: number;
  goalMeetDays: number;
  totalDays: number;
  goalMeetPercentage: number;
  totalIntake: number;
  dailyIntake: Record<string, number>;
}> {
  try {
    // Get goals
    const { dailyGoal } = await getWaterIntakeGoals();
    
    // Get total intake for each date in range
    const dailyIntake = await getTotalWaterIntakeForDateRange(startDate, endDate);
    
    // Calculate statistics
    const dateEntries = Object.entries(dailyIntake);
    const totalDays = dateEntries.length;
    
    if (totalDays === 0) {
      return {
        averageIntake: 0,
        goalMeetDays: 0,
        totalDays: 0,
        goalMeetPercentage: 0,
        totalIntake: 0,
        dailyIntake: {}
      };
    }
    
    const totalIntake = dateEntries.reduce((sum, [_, intake]) => sum + intake, 0);
    const averageIntake = Math.round(totalIntake / totalDays);
    const goalMeetDays = dateEntries.filter(([_, intake]) => intake >= dailyGoal).length;
    const goalMeetPercentage = Math.round((goalMeetDays / totalDays) * 100);
    
    return {
      averageIntake,
      goalMeetDays,
      totalDays,
      goalMeetPercentage,
      totalIntake,
      dailyIntake
    };
  } catch (error) {
    console.error('Error getting water intake statistics:', error);
    return {
      averageIntake: 0,
      goalMeetDays: 0,
      totalDays: 0,
      goalMeetPercentage: 0,
      totalIntake: 0,
      dailyIntake: {}
    };
  }
}

/**
 * Add a quick water intake record (glass or bottle)
 */
export async function addQuickWaterIntake(type: 'glass' | 'bottle'): Promise<WaterIntakeRecord> {
  const { standardGlassSize, standardBottleSize } = await getWaterIntakeGoals();
  const amount = type === 'glass' ? standardGlassSize : standardBottleSize;
  
  return await addWaterIntake(amount, type);
}

/**
 * Helper function to get dates in a range
 */
function getDatesInRange(startDate: string, endDate: string): string[] {
  const dates: string[] = [];
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  // Adjust end date to include the end date itself
  end.setDate(end.getDate() + 1);
  
  const current = new Date(start);
  while (current < end) {
    dates.push(current.toISOString().split('T')[0]);
    current.setDate(current.getDate() + 1);
  }
  
  return dates;
}

/**
 * Sync local water intake records with Firebase
 */
export async function syncWaterIntakeWithFirebase(date: string): Promise<boolean> {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return false;
    }
    
    // Get local records
    const recordsJson = await AsyncStorage.getItem(`${WATER_INTAKE_KEY}${date}`);
    if (!recordsJson) {
      return true; // Nothing to sync
    }
    
    const localRecords: WaterIntakeRecord[] = JSON.parse(recordsJson);
    
    // Get existing Firebase records
    const q = query(
      collection(db, 'water_intake'),
      where('user_id', '==', user.uid),
      where('date', '==', date)
    );
    
    const querySnapshot = await getDocs(q);
    const existingRecords: Record<string, {id: string, data: any}> = {};
    
    // Store Firebase records by ID and also by time+amount for duplicate detection
    querySnapshot.forEach(doc => {
      const data = doc.data();
      existingRecords[doc.id] = {id: doc.id, data};
    });
    
    // Create a map of local record IDs that have already been synced to Firebase
    const updatedLocalRecords: WaterIntakeRecord[] = [];
    const recordsToSync: WaterIntakeRecord[] = [];
    
    // First pass: Identify which records need to be synced and which are already in Firebase
    for (const record of localRecords) {
      // If the record ID is already a Firebase ID and exists in Firebase, just keep it
      if (existingRecords[record.id]) {
        updatedLocalRecords.push(record);
      } else {
        // Record might be a duplicate with different ID, or needs to be synced
        // Check for similar records (same time and amount) to avoid duplication
        let foundMatchingRecord = false;
        
        for (const [existingId, {data}] of Object.entries(existingRecords)) {
          if (data.time === record.time && data.amount === record.amount) {
            // This is likely the same record, just use the Firebase ID
            updatedLocalRecords.push({
              ...record,
              id: existingId
            });
            foundMatchingRecord = true;
            break;
          }
        }
        
        if (!foundMatchingRecord) {
          // No matching record found, needs to be synced
          recordsToSync.push(record);
        }
      }
    }
    
    // Second pass: Sync records that need to be synced to Firebase
    for (const record of recordsToSync) {
      const waterIntakeData = {
        user_id: user.uid,
        date: record.date,
        time: record.time,
        amount: record.amount,
        source: record.source,
        notes: record.notes,
        created_at: new Date().toISOString()
      };
      
      try {
        const docRef = await addDoc(collection(db, 'water_intake'), waterIntakeData);
        // Add to updated records with the new Firebase ID
        updatedLocalRecords.push({
          ...record,
          id: docRef.id
        });
        console.log('Successfully synced water intake record to Firebase with ID:', docRef.id);
      } catch (error) {
        console.error('Error syncing record to Firebase:', error);
        // Keep the original record in case of error
        updatedLocalRecords.push(record);
      }
    }
    
    // Save updated local records with Firebase IDs
    await AsyncStorage.setItem(
      `${WATER_INTAKE_KEY}${date}`,
      JSON.stringify(updatedLocalRecords)
    );
    
    console.log(`Sync completed: ${recordsToSync.length} records synced, ${updatedLocalRecords.length} total records`);
    return true;
  } catch (error) {
    console.error('Error syncing water intake with Firebase:', error);
    return false;
  }
}

/**
 * Get water intake records for a date
 */
export async function getWaterIntakeForDate(dateStr: string): Promise<WaterIntakeRecord[]> {
  try {
    // Check if we need to use offline fallback (especially for iOS)
    try {
      // Import the offline fallback utilities
      const { isIOSEnvironment, getLocalWaterIntake, shouldUseOfflineFallbacks } = await import('../utils/offlineDataFallback');
      
      // Use local storage directly if we're on iOS 
      if (await shouldUseOfflineFallbacks() || isIOSEnvironment()) {
        console.log('Using offline fallback for water intake data');
        // For iOS, prioritize local storage to avoid Firebase connection issues
        const storageKey = `${WATER_INTAKE_KEY}${dateStr}`;
        const recordsJson = await AsyncStorage.getItem(storageKey);
        
        if (recordsJson) {
          return JSON.parse(recordsJson);
        }
        
        // If no local storage records, create an empty set
        return [];
      }
    } catch (fallbackError) {
      console.warn('Error using offline fallbacks:', fallbackError);
      // Continue with regular flow if offline fallback fails
    }
    
    // Try to get from Firebase first
    try {
      const userId = await getCurrentUserId();
      
      const q = query(
        collection(db, 'water_intake'),
        where('user_id', '==', userId),
        where('date', '==', dateStr),
        orderBy('time', 'asc')
      );
      
      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        const records: WaterIntakeRecord[] = [];
        
        querySnapshot.forEach(doc => {
          const data = doc.data();
          records.push({
            id: doc.id,
            date: data.date,
            time: data.time,
            amount: data.amount,
            source: data.source,
            notes: data.notes,
          });
        });
        
        // Cache to local storage
        await AsyncStorage.setItem(
          `${WATER_INTAKE_KEY}${dateStr}`,
          JSON.stringify(records)
        );
        
        return records;
      }
    } catch (dbError) {
      console.error('Firebase error in getWaterIntakeForDate:', dbError);
      // Fall back to local storage
    }
    
    // Fall back to local storage
    const storageKey = `${WATER_INTAKE_KEY}${dateStr}`;
    const recordsJson = await AsyncStorage.getItem(storageKey);
    
    if (recordsJson) {
      return JSON.parse(recordsJson);
    }
    
    return []; // No records found
  } catch (error) {
    console.error('Error getting water intake for date:', error);
    return []; // Return empty array on error
  }
} 