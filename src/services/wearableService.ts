import AsyncStorage from '@react-native-async-storage/async-storage';

import { Platform , NativeModules } from 'react-native';
import * as SecureStore from 'expo-secure-store';
import * as WebBrowser from 'expo-web-browser';
import * as Linking from 'expo-linking';
import { db , functions } from '../lib/firebase';
import { getDoc, doc, collection, query, where, getDocs, addDoc, updateDoc, deleteDoc, orderBy, limit as firestoreLimit, setDoc } from 'firebase/firestore';
import { getFunctions, httpsCallable } from 'firebase/functions';
import { getAuth } from 'firebase/auth';
// Use require for modules that don't have type declarations
const AppleHealthKit = Platform.OS === 'ios' ? require('react-native-health') : null;
const GoogleFit = Platform.OS === 'android' ? require('react-native-google-fit') : null;
const Scopes = Platform.OS === 'android' && GoogleFit ? GoogleFit.Scopes : {};

// Type declarations for health tracking libraries moved to separate files in types directory

// Supported wearable platforms
export enum WearablePlatform {
  FITBIT = 'fitbit',
  GARMIN = 'garmin',
  APPLE_HEALTH = 'apple_health',
  GOOGLE_FIT = 'google_fit',
  SAMSUNG_HEALTH = 'samsung_health',
}

// Connection status
export enum ConnectionStatus {
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  ERROR = 'error',
}

// Health metric types
export enum HealthMetricType {
  STEPS = 'steps',
  HEART_RATE = 'heart_rate',
  SLEEP = 'sleep',
  CALORIES_BURNED = 'calories_burned',
  ACTIVE_MINUTES = 'active_minutes',
  DISTANCE = 'distance',
  WEIGHT = 'weight',
}

// Connection info interface
export interface WearableConnection {
  id: string;
  platform: WearablePlatform;
  status: ConnectionStatus;
  lastSynced?: string;
  authData?: any;
  deviceInfo?: {
    name?: string;
    model?: string;
    batteryLevel?: number;
  };
}

// Health data interface
export interface HealthMetric {
  id: string;
  userId: string;
  type: HealthMetricType;
  value: number;
  unit: string;
  timestamp: string;
  source: WearablePlatform;
  sourceId?: string;
}

// Storage keys
const WEARABLE_CONNECTION_KEY = 'wearable_connection';
const HEALTH_METRICS_KEY = 'health_metrics';

/**
 * Get all connected wearable devices for the current user
 */
export async function getConnectedWearables(): Promise<WearableConnection[]> {
  try {
    // Try to load from local storage first
    const connectionsStr = await AsyncStorage.getItem(WEARABLE_CONNECTION_KEY);
    let connections: WearableConnection[] = connectionsStr ? JSON.parse(connectionsStr) : [];
    
    // If no local data, try to fetch from Firebase
    if (connections.length === 0) {
      try {
        const userId = await getUserId();
        const wearablesRef = collection(db, 'wearable_connections');
        const q = query(
          wearablesRef,
          where('user_id', '==', userId)
        );
        
        const querySnapshot = await getDocs(q);
        
        if (!querySnapshot.empty) {
          connections = querySnapshot.docs.map(doc => 
            mapDbWearableToConnection({ id: doc.id, ...doc.data() })
          );
          
          // Cache the connections
          await AsyncStorage.setItem(WEARABLE_CONNECTION_KEY, JSON.stringify(connections));
        }
      } catch (error) {
        console.error('Error fetching wearable connections:', error);
      }
    }
    
    return connections;
  } catch (error) {
    console.error('Error getting connected wearables:', error);
    return [];
  }
}

/**
 * Connect to a wearable device platform
 */
export async function connectWearable(platform: WearablePlatform): Promise<WearableConnection | null> {
  try {
    // Check if platform is already connected
    const connections = await getConnectedWearables();
    const existingConnection = connections.find(conn => conn.platform === platform);
    
    if (existingConnection && existingConnection.status === ConnectionStatus.CONNECTED) {
      return existingConnection;
    }
    
    // Set initial connection status
    const connection: WearableConnection = {
      id: existingConnection?.id || generateId(),
      platform,
      status: ConnectionStatus.CONNECTING,
    };
    
    // Update connections in storage
    await updateConnectionInStorage(connection);
    
    // Attempt to connect based on platform
    let authData;
    switch (platform) {
      case WearablePlatform.FITBIT:
        authData = await connectToFitbit();
        break;
      case WearablePlatform.GARMIN:
        authData = await connectToGarmin();
        break;
      case WearablePlatform.APPLE_HEALTH:
        if (Platform.OS === 'ios') {
          authData = await connectToAppleHealth();
        } else {
          throw new Error('Apple Health is only available on iOS');
        }
        break;
      case WearablePlatform.GOOGLE_FIT:
        if (Platform.OS === 'android') {
          authData = await connectToGoogleFit();
        } else {
          throw new Error('Google Fit is only available on Android');
        }
        break;
      case WearablePlatform.SAMSUNG_HEALTH:
        if (Platform.OS === 'android') {
          authData = await connectToSamsungHealth();
        } else {
          throw new Error('Samsung Health is only available on Android');
        }
        break;
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
    
    // If successfully connected, update connection
    if (authData) {
      const updatedConnection: WearableConnection = {
        ...connection,
        status: ConnectionStatus.CONNECTED,
        lastSynced: new Date().toISOString(),
        authData,
      };
      
      // Update the connection in storage
      await updateConnectionInStorage(updatedConnection);
      
      // Also save to Firebase
      await saveConnectionToFirebase(updatedConnection);
      
      return updatedConnection;
    } else {
      // Connection failed
      const errorConnection: WearableConnection = {
        ...connection,
        status: ConnectionStatus.ERROR,
      };
      
      await updateConnectionInStorage(errorConnection);
      
      return null;
    }
  } catch (error) {
    console.error(`Error connecting to ${platform}:`, error);
    return null;
  }
}

/**
 * Disconnect from a wearable platform
 */
export async function disconnectWearable(platform: WearablePlatform): Promise<boolean> {
  try {
    // Get current connections
    const connections = await getConnectedWearables();
    const connection = connections.find(conn => conn.platform === platform);
    
    if (!connection) {
      return true; // Already disconnected
    }
    
    // Disconnect based on platform
    switch (platform) {
      case WearablePlatform.FITBIT:
        await disconnectFromFitbit(connection.authData);
        break;
      case WearablePlatform.GARMIN:
        await disconnectFromGarmin(connection.authData);
        break;
      case WearablePlatform.APPLE_HEALTH:
        if (Platform.OS === 'ios') {
          await disconnectFromAppleHealth();
        }
        break;
      case WearablePlatform.GOOGLE_FIT:
        if (Platform.OS === 'android') {
          await disconnectFromGoogleFit();
        }
        break;
      case WearablePlatform.SAMSUNG_HEALTH:
        if (Platform.OS === 'android') {
          await disconnectFromSamsungHealth();
        }
        break;
    }
    
    // Update connection status
    const updatedConnection: WearableConnection = {
      ...connection,
      status: ConnectionStatus.DISCONNECTED,
      authData: null,
    };
    
    // Update in storage
    await updateConnectionInStorage(updatedConnection);
    
    // Update in Firebase
    await updateDoc(doc(db, 'wearable_connections', connection.id), {
      status: ConnectionStatus.DISCONNECTED,
      auth_data: null,
      updated_at: new Date().toISOString()
    });
    
    return true;
  } catch (error) {
    console.error(`Error disconnecting from ${platform}:`, error);
    return false;
  }
}

/**
 * Sync health data from connected wearables
 */
export async function syncHealthData(
  platform: WearablePlatform,
  metrics: HealthMetricType[] = Object.values(HealthMetricType),
  startDate?: string,
  endDate?: string
): Promise<HealthMetric[]> {
  try {
    // Check if platform is connected
    const connections = await getConnectedWearables();
    const connection = connections.find(conn => 
      conn.platform === platform && conn.status === ConnectionStatus.CONNECTED
    );
    
    if (!connection) {
      throw new Error(`Platform ${platform} is not connected`);
    }
    
    // Default date range to last 7 days if not specified
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(end);
    if (!startDate) {
      start.setDate(start.getDate() - 7);
    }
    
    // Sync data based on platform
    let healthData: HealthMetric[] = [];
    
    switch (platform) {
      case WearablePlatform.FITBIT:
        healthData = await syncFitbitData(connection, metrics, start, end);
        break;
      case WearablePlatform.GARMIN:
        healthData = await syncGarminData(connection, metrics, start, end);
        break;
      case WearablePlatform.APPLE_HEALTH:
        if (Platform.OS === 'ios') {
          healthData = await syncAppleHealthData(metrics, start, end);
        }
        break;
      case WearablePlatform.GOOGLE_FIT:
        if (Platform.OS === 'android') {
          healthData = await syncGoogleFitData(connection, metrics, start, end);
        }
        break;
      case WearablePlatform.SAMSUNG_HEALTH:
        if (Platform.OS === 'android') {
          healthData = await syncSamsungHealthData(connection, metrics, start, end);
        }
        break;
    }
    
    // Save synced data
    if (healthData.length > 0) {
      // Update last synced timestamp
      connection.lastSynced = new Date().toISOString();
      await updateConnectionInStorage(connection);
      
      // Save health data to storage and Firebase
      await saveHealthMetrics(healthData);
    }
    
    return healthData;
  } catch (error) {
    console.error(`Error syncing data from ${platform}:`, error);
    return [];
  }
}

/**
 * Get health metrics from storage
 */
export async function getHealthMetrics(
  type?: HealthMetricType,
  startDate?: string,
  endDate?: string,
  limit: number = 100
): Promise<HealthMetric[]> {
  try {
    // Try to load from local storage first
    const metricsStr = await AsyncStorage.getItem(HEALTH_METRICS_KEY);
    let metrics: HealthMetric[] = metricsStr ? JSON.parse(metricsStr) : [];
    
    // If no local data, try to fetch from Firebase
    if (metrics.length === 0) {
      try {
        const userId = await getUserId();
        const metricsRef = collection(db, 'health_metrics');
        
        // Build the query constraints
        const constraints: any[] = [
          where('user_id', '==', userId),
          orderBy('timestamp', 'desc'),
          firestoreLimit(limit)
        ];
        
        if (type) {
          constraints.push(where('type', '==', type));
        }
        
        if (startDate) {
          constraints.push(where('timestamp', '>=', startDate));
        }
        
        if (endDate) {
          constraints.push(where('timestamp', '<=', endDate));
        }
        
        const q = query(metricsRef, ...constraints);
        const querySnapshot = await getDocs(q);
        
        if (!querySnapshot.empty) {
          metrics = querySnapshot.docs.map(doc => 
            mapDbMetricToHealthMetric({ id: doc.id, ...doc.data() })
          );
          
          // Cache the metrics
          await AsyncStorage.setItem(HEALTH_METRICS_KEY, JSON.stringify(metrics));
        }
      } catch (error) {
        console.error('Error fetching health metrics:', error);
      }
    } else {
      // Filter local data based on parameters
      if (type) {
        metrics = metrics.filter(m => m.type === type);
      }
      
      if (startDate) {
        metrics = metrics.filter(m => new Date(m.timestamp) >= new Date(startDate));
      }
      
      if (endDate) {
        metrics = metrics.filter(m => new Date(m.timestamp) <= new Date(endDate));
      }
      
      // Sort by timestamp descending
      metrics.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      
      // Apply limit
      metrics = metrics.slice(0, limit);
    }
    
    return metrics;
  } catch (error) {
    console.error('Error getting health metrics:', error);
    return [];
  }
}

/**
 * Get device information for a connected wearable
 */
export async function getDeviceInfo(platform: WearablePlatform): Promise<any | null> {
  try {
    const connections = await getConnectedWearables();
    const connection = connections.find(conn => 
      conn.platform === platform && conn.status === ConnectionStatus.CONNECTED
    );
    
    if (!connection) {
      return null;
    }
    
    // If we already have device info, return it
    if (connection.deviceInfo) {
      return connection.deviceInfo;
    }
    
    // Otherwise fetch it based on platform
    let deviceInfo;
    
    switch (platform) {
      case WearablePlatform.FITBIT:
        deviceInfo = await getFitbitDeviceInfo(connection);
        break;
      case WearablePlatform.GARMIN:
        deviceInfo = await getGarminDeviceInfo(connection);
        break;
      case WearablePlatform.APPLE_HEALTH:
        if (Platform.OS === 'ios') {
          deviceInfo = { name: 'Apple Health', model: 'iOS' };
        }
        break;
      case WearablePlatform.GOOGLE_FIT:
        if (Platform.OS === 'android') {
          deviceInfo = { name: 'Google Fit', model: 'Android' };
        }
        break;
      case WearablePlatform.SAMSUNG_HEALTH:
        if (Platform.OS === 'android') {
          deviceInfo = await getSamsungHealthDeviceInfo(connection);
        }
        break;
    }
    
    if (deviceInfo) {
      // Update connection with device info
      connection.deviceInfo = deviceInfo;
      await updateConnectionInStorage(connection);
    }
    
    return deviceInfo;
  } catch (error) {
    console.error(`Error getting device info for ${platform}:`, error);
    return null;
  }
}

/**
 * Update a connection in storage
 */
async function updateConnectionInStorage(connection: WearableConnection): Promise<void> {
  try {
    const connectionsStr = await AsyncStorage.getItem(WEARABLE_CONNECTION_KEY);
    let connections: WearableConnection[] = connectionsStr ? JSON.parse(connectionsStr) : [];
    
    // Update or add the connection
    const index = connections.findIndex(conn => conn.id === connection.id);
    if (index >= 0) {
      connections[index] = connection;
    } else {
      connections.push(connection);
    }
    
    await AsyncStorage.setItem(WEARABLE_CONNECTION_KEY, JSON.stringify(connections));
  } catch (error) {
    console.error('Error updating connection in storage:', error);
  }
}

/**
 * Save connection to Firebase
 */
async function saveConnectionToFirebase(connection: WearableConnection): Promise<void> {
  try {
    const userId = await getUserId();
    
    const connectionData = {
      id: connection.id,
      user_id: userId,
      platform: connection.platform,
      status: connection.status,
      last_synced: connection.lastSynced,
      auth_data: connection.authData,
      device_info: connection.deviceInfo,
      updated_at: new Date().toISOString()
    };
    
    await setDoc(doc(db, 'wearable_connections', connection.id), connectionData, { merge: true });
  } catch (error) {
    console.error('Error saving connection to Firebase:', error);
  }
}

/**
 * Save health metrics to storage and Firebase
 */
async function saveHealthMetrics(metrics: HealthMetric[]): Promise<void> {
  try {
    // First save to local storage for quick access
    const metricsStr = await AsyncStorage.getItem(HEALTH_METRICS_KEY);
    let existingMetrics: HealthMetric[] = metricsStr ? JSON.parse(metricsStr) : [];
    
    // Add new metrics and deduplicate by ID
    const allMetrics = [...existingMetrics];
    for (const metric of metrics) {
      const index = allMetrics.findIndex(m => m.id === metric.id);
      if (index >= 0) {
        allMetrics[index] = metric;
      } else {
        allMetrics.push(metric);
      }
    }
    
    // Save to local storage
    await AsyncStorage.setItem(HEALTH_METRICS_KEY, JSON.stringify(allMetrics));
    
    // Save to Firebase
    const userId = await getUserId();
    
    for (const metric of metrics) {
      const metricData = {
        id: metric.id,
        user_id: userId,
        type: metric.type,
        value: metric.value,
        unit: metric.unit,
        timestamp: metric.timestamp,
        source: metric.source,
        source_id: metric.sourceId,
      };
      
      // Use setDoc with merge to update existing metrics or create new ones
      await setDoc(
        doc(db, 'health_metrics', metric.id),
        metricData,
        { merge: true }
      );
    }
  } catch (error) {
    console.error('Error saving health metrics:', error);
  }
}

// Helper functions for connecting to specific platforms

/**
 * Connect to Fitbit using OAuth
 */
async function connectToFitbit(): Promise<any> {
  try {
    // Create a URL for handling the redirect back to the app
    const redirectUri = Linking.createURL('wearable-auth/fitbit');
    
    // Create a random state value for security
    const state = Math.random().toString(36).substring(2, 15);
    await SecureStore.setItemAsync('fitbit_auth_state', state);
    
    // Call Firebase cloud function to get authorization URL
    const fitbitTokenExchange = httpsCallable(functions, 'fitbitTokenExchange');
    const result = await fitbitTokenExchange({ 
      action: 'get_auth_url',
      redirectUri,
      state 
    });
    
    const data = result.data as any;
    
    if (!data || !data.authUrl) {
      throw new Error('Failed to get Fitbit authorization URL');
    }
    
    // Open browser for OAuth flow
    const authResponse = await WebBrowser.openAuthSessionAsync(
      data.authUrl,
      redirectUri
    );
    
    if (authResponse.type === 'success' && authResponse.url) {
      // Extract the authorization code from the redirect URL
      const url = new URL(authResponse.url);
      const code = url.searchParams.get('code');
      const returnedState = url.searchParams.get('state');
      
      // Verify state to prevent CSRF attacks
      const savedState = await SecureStore.getItemAsync('fitbit_auth_state');
      if (returnedState !== savedState) {
        throw new Error('OAuth state mismatch');
      }
      
      if (!code) {
        throw new Error('No authorization code received');
      }
      
      // Exchange code for access token
      const tokenResult = await fitbitTokenExchange({
        action: 'exchange_token',
        code,
        redirectUri
      });
      
      const tokenData = tokenResult.data as any;
      
      if (!tokenData || !tokenData.access_token) {
        throw new Error('Failed to exchange authorization code for token');
      }
      
      // Return the auth data
      return {
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token,
        expires_in: tokenData.expires_in,
        user_id: tokenData.user_id,
        scope: tokenData.scope,
      };
    } else {
      throw new Error('Authorization canceled or failed');
    }
  } catch (error) {
    console.error('Error connecting to Fitbit:', error);
    throw error;
  }
}

/**
 * Connect to Garmin
 */
async function connectToGarmin(): Promise<any> {
  try {
    // Create a URL for handling the redirect back to the app
    const redirectUri = Linking.createURL('wearable-auth/garmin');
    
    // Call Firebase cloud function to handle Garmin authentication
    const garminAuth = httpsCallable(functions, 'garminAuth');
    const result = await garminAuth({ 
      action: 'get_auth_url',
      redirectUri 
    });
    
    const data = result.data as any;
    
    if (!data || !data.authUrl) {
      throw new Error('Failed to get Garmin authorization URL');
    }
    
    // Open browser for authentication
    const authResponse = await WebBrowser.openAuthSessionAsync(
      data.authUrl,
      redirectUri
    );
    
    if (authResponse.type === 'success' && authResponse.url) {
      // Extract token information from redirect URL
      const url = new URL(authResponse.url);
      const token = url.searchParams.get('oauth_token');
      const verifier = url.searchParams.get('oauth_verifier');
      
      if (!token || !verifier) {
        throw new Error('Missing OAuth parameters in redirect');
      }
      
      // Exchange for access token
      const tokenResult = await garminAuth({
        action: 'complete_auth',
        token,
        verifier
      });
      
      const tokenData = tokenResult.data as any;
      
      if (!tokenData || !tokenData.accessToken) {
        throw new Error('Failed to get Garmin access token');
      }
      
      return {
        accessToken: tokenData.accessToken,
        accessTokenSecret: tokenData.accessTokenSecret,
        userId: tokenData.userId
      };
    } else {
      throw new Error('Garmin authorization canceled or failed');
    }
  } catch (error) {
    console.error('Error connecting to Garmin:', error);
    throw error;
  }
}

async function connectToAppleHealth(): Promise<any> {
  // Request permissions for Apple Health
  if (Platform.OS !== 'ios') {
    throw new Error('Apple Health is only available on iOS');
  }
  
  try {
    const permissions = {
      permissions: {
        read: [
          AppleHealthKit.Constants.Permissions.Steps,
          AppleHealthKit.Constants.Permissions.DistanceWalkingRunning,
          AppleHealthKit.Constants.Permissions.ActiveEnergyBurned,
          AppleHealthKit.Constants.Permissions.HeartRate,
          AppleHealthKit.Constants.Permissions.SleepAnalysis,
          AppleHealthKit.Constants.Permissions.Weight,
          AppleHealthKit.Constants.Permissions.Height,
        ],
        write: []
      }
    };
    
    return new Promise((resolve, reject) => {
      AppleHealthKit.initHealthKit(permissions, (error: any) => {
        if (error) {
          reject(error);
          return;
        }
        
        AppleHealthKit.getAuthStatus(permissions, (err: any, result: any) => {
          if (err) {
            reject(err);
            return;
          }
          
          resolve({
            authorized: true,
            permissions: result
          });
        });
      });
    });
  } catch (error) {
    console.error('Apple Health init error:', error);
    throw error;
  }
}

async function connectToGoogleFit(): Promise<any> {
  // Set up Google Fit configuration
  if (Platform.OS !== 'android') {
    throw new Error('Google Fit is only available on Android');
  }
  
  try {
    const options = {
      scopes: [
        Scopes.FITNESS_ACTIVITY_READ,
        Scopes.FITNESS_BODY_READ,
        Scopes.FITNESS_HEART_RATE_READ,
        Scopes.FITNESS_SLEEP_READ,
      ]
    };
    
    const authResult = await GoogleFit.authorize(options);
    
    if (!authResult.success) {
      throw new Error('Failed to authorize Google Fit');
    }
    
    return {
      accessToken: 'google-fit-authorized',
      scopes: options.scopes,
    };
  } catch (error) {
    console.error('Google Fit auth error:', error);
    throw error;
  }
}

async function connectToSamsungHealth(): Promise<any> {
  // Samsung Health connection
  if (Platform.OS !== 'android') {
    throw new Error('Samsung Health is only available on Android');
  }
  
  try {
    // Call platform-specific module to handle Samsung Health connection
    const SamsungHealth = NativeModules.SamsungHealthModule;
    
    if (!SamsungHealth) {
      throw new Error('Samsung Health module not available');
    }
    
    const permissions = [
      'weight',
      'sleep',
      'step_count',
      'heart_rate',
      'exercise',
    ];
    
    const result = await SamsungHealth.connect(permissions);
    
    return {
      connected: result.connected,
      permissions: result.permissions,
    };
  } catch (error) {
    console.error('Samsung Health connection error:', error);
    throw error;
  }
}

// Disconnection methods

async function disconnectFromFitbit(authData: any): Promise<void> {
  try {
    // Revoke Fitbit token
    if (!authData || !authData.access_token) {
      return;
    }
    
    // Call Firebase Cloud Function to revoke token
    const fitbitRevokeToken = httpsCallable(functions, 'fitbitRevokeToken');
    await fitbitRevokeToken({ accessToken: authData.access_token });
  } catch (error) {
    console.error('Error disconnecting from Fitbit:', error);
    throw error;
  }
}

async function disconnectFromGarmin(authData: any): Promise<void> {
  // Logout from Garmin
  try {
    if (!authData || !authData.accessToken) {
      return;
    }
    
    // Call Firebase Cloud Function to logout
    const garminLogout = httpsCallable(functions, 'garminLogout');
    await garminLogout({ accessToken: authData.accessToken });
  } catch (error) {
    console.error('Error disconnecting from Garmin:', error);
    throw error;
  }
}

async function disconnectFromAppleHealth(): Promise<void> {
  // There's no real "disconnect" for Apple Health, just stop reading data
  console.log('Disconnected from Apple Health - permissions must be revoked in Health app');
}

async function disconnectFromGoogleFit(): Promise<void> {
  try {
    if (Platform.OS !== 'android') {
      return;
    }
    
    // Disconnect from Google Fit
    await GoogleFit.disconnect();
  } catch (error) {
    console.error('Error disconnecting from Google Fit:', error);
    throw error;
  }
}

async function disconnectFromSamsungHealth(): Promise<void> {
  try {
    if (Platform.OS !== 'android') {
      return;
    }
    
    // Disconnect from Samsung Health
    const SamsungHealth = NativeModules.SamsungHealthModule;
    
    if (!SamsungHealth) {
      throw new Error('Samsung Health module not available');
    }
    
    await SamsungHealth.disconnect();
  } catch (error) {
    console.error('Error disconnecting from Samsung Health:', error);
    throw error;
  }
}

// Data synchronization methods

async function syncFitbitData(
  connection: WearableConnection,
  metrics: HealthMetricType[],
  startDate: Date,
  endDate: Date
): Promise<HealthMetric[]> {
  if (!connection.authData || !connection.authData.access_token) {
    throw new Error('Fitbit authentication data missing');
  }
  
  const userId = await getUserId();
  const healthData: HealthMetric[] = [];
  
  // Format dates for Fitbit API
  const startDateStr = startDate.toISOString().split('T')[0];
  const endDateStr = endDate.toISOString().split('T')[0];
  
  try {
    // Call Fitbit API through Firebase Cloud Function
    const fitbitSyncData = httpsCallable(functions, 'fitbitSyncData');
    const result = await fitbitSyncData({
      accessToken: connection.authData.access_token,
      refreshToken: connection.authData.refresh_token,
      metrics: metrics,
      startDate: startDateStr,
      endDate: endDateStr
    });
    
    const data = result.data as any;
    if (!data) {
      throw new Error('Failed to sync Fitbit data');
    }
    
    // Process data returned from the serverless function
    if (data.steps && metrics.includes(HealthMetricType.STEPS)) {
      healthData.push({
        id: generateId(),
        userId,
        type: HealthMetricType.STEPS,
        value: data.steps.value,
        unit: 'steps',
        timestamp: new Date().toISOString(),
        source: WearablePlatform.FITBIT,
        sourceId: 'fitbit-steps',
      });
    }
    
    if (data.heartRate && metrics.includes(HealthMetricType.HEART_RATE)) {
      healthData.push({
        id: generateId(),
        userId,
        type: HealthMetricType.HEART_RATE,
        value: data.heartRate.value,
        unit: 'bpm',
        timestamp: new Date().toISOString(),
        source: WearablePlatform.FITBIT,
        sourceId: 'fitbit-hr',
      });
    }
    
    if (data.calories && metrics.includes(HealthMetricType.CALORIES_BURNED)) {
      healthData.push({
        id: generateId(),
        userId,
        type: HealthMetricType.CALORIES_BURNED,
        value: data.calories.value,
        unit: 'kcal',
        timestamp: new Date().toISOString(),
        source: WearablePlatform.FITBIT,
        sourceId: 'fitbit-cal',
      });
    }
    
    // Update refresh token if provided
    if (data.refreshedToken) {
      connection.authData.access_token = data.refreshedToken.accessToken;
      connection.authData.refresh_token = data.refreshedToken.refreshToken;
      await updateConnectionInStorage(connection);
      await saveConnectionToFirebase(connection);
    }
    
    return healthData;
  } catch (error) {
    console.error('Error syncing Fitbit data:', error);
    throw error;
  }
}

async function syncGarminData(
  connection: WearableConnection,
  metrics: HealthMetricType[],
  startDate: Date,
  endDate: Date
): Promise<HealthMetric[]> {
  if (!connection.authData || !connection.authData.accessToken) {
    throw new Error('Garmin authentication data missing');
  }
  
  const userId = await getUserId();
  const healthData: HealthMetric[] = [];
  
  // Format dates for Garmin API
  const startDateStr = startDate.toISOString().split('T')[0];
  const endDateStr = endDate.toISOString().split('T')[0];
  
  try {
    // Call Garmin API through Firebase Function
    const garminSyncData = httpsCallable(functions, 'garminSyncData');
    const result = await garminSyncData({
      accessToken: connection.authData.accessToken,
      accessTokenSecret: connection.authData.accessTokenSecret,
      metrics: metrics,
      startDate: startDateStr,
      endDate: endDateStr
    });
    
    const data = result.data as any;
    
    if (!data) {
      throw new Error('Failed to sync Garmin data');
    }
    
    // Process steps data
    if (data.steps && metrics.includes(HealthMetricType.STEPS)) {
      healthData.push({
        id: generateId(),
        userId,
        type: HealthMetricType.STEPS,
        value: data.steps.value,
        unit: 'steps',
        timestamp: new Date().toISOString(),
        source: WearablePlatform.GARMIN,
        sourceId: 'garmin-steps',
      });
    }
    
    // Process distance data
    if (data.distance && metrics.includes(HealthMetricType.DISTANCE)) {
      healthData.push({
        id: generateId(),
        userId,
        type: HealthMetricType.DISTANCE,
        value: data.distance.value,
        unit: 'km',
        timestamp: new Date().toISOString(),
        source: WearablePlatform.GARMIN,
        sourceId: 'garmin-distance',
      });
    }
    
    return healthData;
  } catch (error) {
    console.error('Error syncing Garmin data:', error);
    throw error;
  }
}

async function syncAppleHealthData(
  metrics: HealthMetricType[],
  startDate: Date,
  endDate: Date
): Promise<HealthMetric[]> {
  if (Platform.OS !== 'ios') {
    throw new Error('Apple Health is only available on iOS');
  }
  
  const userId = await getUserId();
  const healthData: HealthMetric[] = [];
  
  try {
    // Get steps data
    if (metrics.includes(HealthMetricType.STEPS)) {
      const options = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      };
      
      const stepsData = await new Promise<number>((resolve, reject) => {
        AppleHealthKit.getStepCount(options, (err, results) => {
          if (err) {
            reject(err);
            return;
          }
          resolve(results.value);
        });
      });
      
      healthData.push({
        id: generateId(),
        userId,
        type: HealthMetricType.STEPS,
        value: stepsData,
        unit: 'steps',
        timestamp: new Date().toISOString(),
        source: WearablePlatform.APPLE_HEALTH,
      });
    }
    
    // Get active minutes data
    if (metrics.includes(HealthMetricType.ACTIVE_MINUTES)) {
      const options = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      };
      
      const activeMinutesData = await new Promise<number>((resolve, reject) => {
        AppleHealthKit.getActiveEnergyBurned(options, (err, results) => {
          if (err) {
            reject(err);
            return;
          }
          
          // Estimate active minutes based on active calories (rough approximation)
          // Typically, a person burns ~10 calories per minute of moderate activity
          const activeMinutes = Math.round(results.reduce((sum, current) => sum + current.value, 0) / 10);
          resolve(activeMinutes);
        });
      });
      
      healthData.push({
        id: generateId(),
        userId,
        type: HealthMetricType.ACTIVE_MINUTES,
        value: activeMinutesData,
        unit: 'min',
        timestamp: new Date().toISOString(),
        source: WearablePlatform.APPLE_HEALTH,
      });
    }
    
    return healthData;
  } catch (error) {
    console.error('Error syncing Apple Health data:', error);
    throw error;
  }
}

async function syncGoogleFitData(
  connection: WearableConnection,
  metrics: HealthMetricType[],
  startDate: Date,
  endDate: Date
): Promise<HealthMetric[]> {
  if (Platform.OS !== 'android') {
    throw new Error('Google Fit is only available on Android');
  }
  
  const userId = await getUserId();
  const healthData: HealthMetric[] = [];
  
  try {
    // Get steps data
    if (metrics.includes(HealthMetricType.STEPS)) {
      const options = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      };
      
      const stepsData = await GoogleFit.getDailyStepCountSamples(options);
      
      // Sum steps from all sources
      let totalSteps = 0;
      for (const source of stepsData) {
        for (const step of source.steps) {
          totalSteps += step.value;
        }
      }
      
      healthData.push({
        id: generateId(),
        userId,
        type: HealthMetricType.STEPS,
        value: totalSteps,
        unit: 'steps',
        timestamp: new Date().toISOString(),
        source: WearablePlatform.GOOGLE_FIT,
      });
    }
    
    // Get weight data
    if (metrics.includes(HealthMetricType.WEIGHT)) {
      const options = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      };
      
      const weightData = await GoogleFit.getWeightSamples(options);
      
      if (weightData && weightData.length > 0) {
        // Get the most recent weight reading
        const latestWeight = weightData.reduce((latest, current) => {
          return current.endDate > latest.endDate ? current : latest;
        });
        
        healthData.push({
          id: generateId(),
          userId,
          type: HealthMetricType.WEIGHT,
          value: latestWeight.value,
          unit: 'kg',
          timestamp: new Date(latestWeight.endDate).toISOString(),
          source: WearablePlatform.GOOGLE_FIT,
        });
      }
    }
    
    return healthData;
  } catch (error) {
    console.error('Error syncing Google Fit data:', error);
    throw error;
  }
}

async function syncSamsungHealthData(
  connection: WearableConnection,
  metrics: HealthMetricType[],
  startDate: Date,
  endDate: Date
): Promise<HealthMetric[]> {
  if (Platform.OS !== 'android') {
    throw new Error('Samsung Health is only available on Android');
  }
  
  const userId = await getUserId();
  const healthData: HealthMetric[] = [];
  
  try {
    const SamsungHealth = NativeModules.SamsungHealthModule;
    
    if (!SamsungHealth) {
      throw new Error('Samsung Health module not available');
    }
    
    // Format dates for Samsung Health
    const startTimestamp = startDate.getTime();
    const endTimestamp = endDate.getTime();
    
    // Get steps data
    if (metrics.includes(HealthMetricType.STEPS)) {
      const stepsData = await SamsungHealth.getStepCount(startTimestamp, endTimestamp);
      
      if (stepsData) {
        healthData.push({
          id: generateId(),
          userId,
          type: HealthMetricType.STEPS,
          value: stepsData.count,
          unit: 'steps',
          timestamp: new Date().toISOString(),
          source: WearablePlatform.SAMSUNG_HEALTH,
        });
      }
    }
    
    // Get sleep data
    if (metrics.includes(HealthMetricType.SLEEP)) {
      const sleepData = await SamsungHealth.getSleepData(startTimestamp, endTimestamp);
      
      if (sleepData) {
        healthData.push({
          id: generateId(),
          userId,
          type: HealthMetricType.SLEEP,
          value: sleepData.lengthInHours,
          unit: 'hours',
          timestamp: new Date().toISOString(),
          source: WearablePlatform.SAMSUNG_HEALTH,
        });
      }
    }
    
    return healthData;
  } catch (error) {
    console.error('Error syncing Samsung Health data:', error);
    throw error;
  }
}

// Device info methods

async function getFitbitDeviceInfo(connection: WearableConnection): Promise<any> {
  if (!connection.authData || !connection.authData.access_token) {
    throw new Error('Fitbit authentication data missing');
  }
  
  try {
    // Get device info from Fitbit API through Firebase function
    const fitbitDeviceInfo = httpsCallable(functions, 'fitbitDeviceInfo');
    const result = await fitbitDeviceInfo({
      accessToken: connection.authData.access_token
    });
    
    const data = result.data as any;
    
    if (!data) {
      throw new Error('Failed to get Fitbit device info');
    }
    
    return {
      name: data.deviceName || 'Fitbit Device',
      model: data.deviceVersion || 'Unknown',
      batteryLevel: data.batteryLevel || 0,
    };
  } catch (error) {
    console.error('Error getting Fitbit device info:', error);
    throw error;
  }
}

async function getGarminDeviceInfo(connection: WearableConnection): Promise<any> {
  if (!connection.authData || !connection.authData.accessToken) {
    throw new Error('Garmin authentication data missing');
  }
  
  try {
    // Get device info from Garmin API through Firebase function
    const garminDeviceInfo = httpsCallable(functions, 'garminDeviceInfo');
    const result = await garminDeviceInfo({
      accessToken: connection.authData.accessToken,
      accessTokenSecret: connection.authData.accessTokenSecret
    });
    
    const data = result.data as any;
    
    if (!data) {
      throw new Error('Failed to get Garmin device info');
    }
    
    return {
      name: data.deviceName || 'Garmin Device',
      model: data.deviceModel || 'Unknown',
      batteryLevel: data.batteryLevel || 0,
    };
  } catch (error) {
    console.error('Error getting Garmin device info:', error);
    throw error;
  }
}

async function getSamsungHealthDeviceInfo(connection: WearableConnection): Promise<any> {
  if (Platform.OS !== 'android') {
    throw new Error('Samsung Health is only available on Android');
  }
  
  try {
    const SamsungHealth = NativeModules.SamsungHealthModule;
    
    if (!SamsungHealth) {
      throw new Error('Samsung Health module not available');
    }
    
    const deviceInfo = await SamsungHealth.getDeviceInfo();
    
    return {
      name: deviceInfo.deviceName || 'Samsung Device',
      model: deviceInfo.deviceModel || 'Galaxy Watch',
      batteryLevel: deviceInfo.batteryLevel || 0,
    };
  } catch (error) {
    console.error('Error getting Samsung Health device info:', error);
    throw error;
  }
}

// Helper functions for mapping database objects to our interfaces

function mapDbWearableToConnection(dbWearable: any): WearableConnection {
  return {
    id: dbWearable.id,
    platform: dbWearable.platform,
    status: dbWearable.status,
    lastSynced: dbWearable.last_synced,
    authData: dbWearable.auth_data,
    deviceInfo: dbWearable.device_info,
  };
}

function mapDbMetricToHealthMetric(dbMetric: any): HealthMetric {
  return {
    id: dbMetric.id,
    userId: dbMetric.user_id,
    type: dbMetric.type,
    value: dbMetric.value,
    unit: dbMetric.unit,
    timestamp: dbMetric.timestamp,
    source: dbMetric.source,
    sourceId: dbMetric.source_id,
  };
}

/**
 * Get current user ID
 */
async function getUserId(): Promise<string> {
  const auth = getAuth();
  const user = auth.currentUser;
  
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  return user.uid;
}

/**
 * Generate a unique ID
 */
function generateId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

 