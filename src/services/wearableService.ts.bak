import AsyncStorage from '@react-native-async-storage/async-storage';

import { Platform } from 'react-native';
import * as SecureStore from 'expo-secure-store';
// Use require for modules that don't have type declarations
const AppleHealthKit = Platform.OS === 'ios' ? require('react-native-health') : null;
const GoogleFit = Platform.OS === 'android' ? require('react-native-google-fit') : null;
const Scopes = Platform.OS === 'android' && GoogleFit ? GoogleFit.Scopes : {};
import { NativeModules } from 'react-native';
import * as WebBrowser from 'expo-web-browser';
import * as Linking from 'expo-linking';
import { firestore } from '@/lib/firebase';
import { getDoc, doc, collection, query, where, getDocs, addDoc, updateDoc, deleteDoc, orderBy, limit as firestoreLimit, setDoc } from 'firebase/firestore';
import { getFunctions, httpsCallable } from 'firebase/functions';
import { getAuth } from 'firebase/auth';
import { functions } from '@/lib/firebase';

// Type declarations for health tracking libraries moved to separate files in types directory

// Supported wearable platforms
export enum WearablePlatform {
  FITBIT = 'fitbit',
  GARMIN = 'garmin',
  APPLE_HEALTH = 'apple_health',
  GOOGLE_FIT = 'google_fit',
  SAMSUNG_HEALTH = 'samsung_health',
}

// Connection status
export enum ConnectionStatus {
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  ERROR = 'error',
}

// Health metric types
export enum HealthMetricType {
  STEPS = 'steps',
  HEART_RATE = 'heart_rate',
  SLEEP = 'sleep',
  CALORIES_BURNED = 'calories_burned',
  ACTIVE_MINUTES = 'active_minutes',
  DISTANCE = 'distance',
  WEIGHT = 'weight',
}

// Connection info interface
export interface WearableConnection {
  id: string;
  platform: WearablePlatform;
  status: ConnectionStatus;
  lastSynced?: string;
  authData?: any;
  deviceInfo?: {
    name?: string;
    model?: string;
    batteryLevel?: number;
  };
}

// Health data interface
export interface HealthMetric {
  id: string;
  userId: string;
  type: HealthMetricType;
  value: number;
  unit: string;
  timestamp: string;
  source: WearablePlatform;
  sourceId?: string;
}

// Storage keys
const WEARABLE_CONNECTION_KEY = 'wearable_connection';
const HEALTH_METRICS_KEY = 'health_metrics';


async function saveConnectionToFirebase(connection: WearableConnection): Promise<void> {
  try {
    const userId = await getUserId();
    
    const connectionData = {
      id: connection.id,
      user_id: userId,
      platform: connection.platform,
      status: connection.status,
      last_synced: connection.lastSynced,
      auth_data: connection.authData,
      device_info: connection.deviceInfo,
      updated_at: new Date().toISOString()
    };
    
    await setDoc(doc(firestore, 'wearable_connections', connection.id), connectionData, { merge: true });
  } catch (error) {
    console.error('Error saving connection to Firebase:', error);
  }
}


async function getUserId(): Promise<string> {
  const auth = getAuth();
  const user = auth.currentUser;
  
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  return user.uid;
}

/**
 * Generate a unique ID
 */
function generateId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

/**
 * Save connection to Firebase
 */
async function saveConnectionToFirebase(connection: WearableConnection): Promise<void> {
  try {
    const userId = await getUserId();
    
    const connectionData = {
      id: connection.id,
      user_id: userId,
      platform: connection.platform,
      status: connection.status,
      last_synced: connection.lastSynced,
      auth_data: connection.authData,
      device_info: connection.deviceInfo,
      updated_at: new Date().toISOString()
    };
    
    await setDoc(doc(firestore, 'wearable_connections', connection.id), connectionData, { merge: true });
  } catch (error) {
    console.error('Error saving connection to Firebase:', error);
  }
} 