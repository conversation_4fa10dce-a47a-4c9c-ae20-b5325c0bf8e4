/**
 * Weight Analysis Service
 * 
 * Provides utilities for analyzing and estimating weight of food items from images
 * Uses AI models to estimate weight based on visual cues and reference objects
 */


import { Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { httpsCallable } from 'firebase/functions';
import { functions } from '@/lib/firebase';

/**
 * Interface for weight estimation results
 */
interface WeightEstimationResult {
  [foodName: string]: number; // Maps food name to estimated weight in grams
}

/**
 * Get image-based weight estimation for food items
 * 
 * @param imageUri URI of the food image
 * @param foodItems Array of food item names to estimate weights for
 * @returns Object mapping food names to estimated weights in grams
 */
export async function getImageBasedWeightEstimation(
  imageUri: string,
  foodItems: string[]
): Promise<WeightEstimationResult> {
  try {
    // Check if we should use the AI model or mock data
    const useAiModel = await shouldUseAiModel();
    
    if (useAiModel) {
      return await estimateWeightWithAi(imageUri, foodItems);
    } else {
      return mockWeightEstimation(foodItems);
    }
  } catch (error) {
    console.error('Error in weight estimation:', error);
    // Return empty result in case of error
    return foodItems.reduce((result, foodName) => {
      result[foodName] = 0;
      return result;
    }, {} as WeightEstimationResult);
  }
}

/**
 * Check if the AI model should be used or if mock data is sufficient
 */
async function shouldUseAiModel(): Promise<boolean> {
  try {
    // Get setting from AsyncStorage
    const useMock = await AsyncStorage.getItem('useMockWeightEstimation');
    
    // If explicitly set to use mock data, return false
    if (useMock === 'true') {
      return false;
    }
    
    // If explicitly set to use AI model, return true
    if (useMock === 'false') {
      return true;
    }
    
    // If nothing is set, check if we're in a dev environment
    if (__DEV__) {
      // In development, default to mock data to save API costs
      return false;
    }
    
    // In production, use the AI model
    return true;
  } catch (error) {
    console.error('Error checking AI model usage:', error);
    // Default to mock data in case of error
    return false;
  }
}

/**
 * Estimate food weights using AI model
 * 
 * @param imageUri URI of the food image
 * @param foodItems Array of food item names
 * @returns Object mapping food names to estimated weights
 */
async function estimateWeightWithAi(
  imageUri: string,
  foodItems: string[]
): Promise<WeightEstimationResult> {
  try {
    // Convert image to base64 if it's a local file
    let imageBase64: string | null = null;
    
    if (imageUri.startsWith('file://')) {
      imageBase64 = await FileSystem.readAsStringAsync(imageUri, {
        encoding: FileSystem.EncodingType.Base64,
      });
    }
    
    // Prepare request data
    const requestData = {
      image: imageBase64 || imageUri,
      foods: foodItems,
      options: {
        // Additional options for the AI model
        includeReferences: true,
        highPrecision: true,
      }
    };
    
    // Call Firebase Function for weight estimation
    const foodWeightEstimation = httpsCallable(functions, 'foodWeightEstimation');
    const result = await foodWeightEstimation(requestData);
    
    const data = result.data as any;
    
    // Format the result
    const estimationResult: WeightEstimationResult = {};
    
    if (data && data.estimates) {
      for (const foodName of foodItems) {
        // Find the matching estimate
        const estimate = data.estimates.find((est: any) => 
          est.food.toLowerCase() === foodName.toLowerCase()
        );
        
        if (estimate) {
          estimationResult[foodName] = estimate.weightGrams;
        } else {
          // If no estimate found for this food, set to 0
          estimationResult[foodName] = 0;
        }
      }
    }
    
    return estimationResult;
  } catch (error) {
    console.error('Error in AI weight estimation:', error);
    // Return mock data as fallback
    return mockWeightEstimation(foodItems);
  }
}

/**
 * Provide mock weight estimations for testing
 * 
 * @param foodItems Array of food item names
 * @returns Object mapping food names to estimated weights
 */
function mockWeightEstimation(foodItems: string[]): WeightEstimationResult {
  const result: WeightEstimationResult = {};
  
  // Common food weights in grams
  const mockWeights: Record<string, number> = {
    // Fruits
    'apple': 180,
    'banana': 120,
    'orange': 130,
    'grapes': 150,
    'strawberry': 12,
    'blueberry': 2,
    
    // Vegetables
    'broccoli': 90,
    'carrot': 60,
    'tomato': 125,
    'lettuce': 40,
    'cucumber': 200,
    'potato': 170,
    
    // Proteins
    'chicken breast': 170,
    'steak': 220,
    'salmon': 175,
    'tofu': 100,
    'egg': 50,
    
    // Grains
    'rice': 150,
    'pasta': 140,
    'bread': 40,
    'oatmeal': 230,
    
    // Dairy
    'cheese': 30,
    'yogurt': 220,
    'milk': 240,
    
    // Prepared foods
    'pizza slice': 110,
    'hamburger': 150,
    'sandwich': 230,
    'salad': 140,
    'soup': 250,
    
    // Snacks
    'cookies': 15,
    'chips': 28,
    'nuts': 30,
    'chocolate': 40,
    
    // Default for unknown items
    'default': 100
  };
  
  // Apply some randomness to the mock data to simulate AI variability
  for (const foodName of foodItems) {
    const normalizedName = foodName.toLowerCase();
    
    // Find the best match in our mock data
    let bestMatch = 'default';
    let bestMatchScore = 0;
    
    for (const knownFood of Object.keys(mockWeights)) {
      // Simple matching score based on substring presence
      if (normalizedName.includes(knownFood) || knownFood.includes(normalizedName)) {
        const score = knownFood.length; // Prefer longer matches
        if (score > bestMatchScore) {
          bestMatch = knownFood;
          bestMatchScore = score;
        }
      }
    }
    
    // Get the base weight
    const baseWeight = mockWeights[bestMatch];
    
    // Add random variation (±15%)
    const variation = (Math.random() * 0.3) - 0.15;
    result[foodName] = Math.round(baseWeight * (1 + variation));
  }
  
  return result;
}

/**
 * Get reference object data to help with weight calibration
 * 
 * @returns Array of common reference objects with their dimensions
 */
export function getReferenceObjects(): {name: string, weightGrams: number, description: string}[] {
  return [
    { name: 'US Quarter', weightGrams: 5.67, description: 'US Quarter coin' },
    { name: 'Credit Card', weightGrams: 5, description: 'Standard plastic credit card' },
    { name: 'AAA Battery', weightGrams: 11.5, description: 'Standard AAA battery' },
    { name: 'AA Battery', weightGrams: 23, description: 'Standard AA battery' },
    { name: 'iPhone 12', weightGrams: 164, description: 'iPhone 12 smartphone' },
    { name: 'iPhone 13', weightGrams: 174, description: 'iPhone 13 smartphone' },
    { name: 'AirPods Case', weightGrams: 46, description: 'Apple AirPods charging case' },
    { name: 'Golf Ball', weightGrams: 46, description: 'Standard golf ball' },
    { name: 'Tennis Ball', weightGrams: 58, description: 'Standard tennis ball' },
    { name: 'Baseball', weightGrams: 145, description: 'Standard baseball' },
    { name: 'Soda Can', weightGrams: 390, description: '12oz aluminum soda can (full)' },
    { name: 'Water Bottle', weightGrams: 500, description: '16.9oz plastic water bottle (full)' }
  ];
} 