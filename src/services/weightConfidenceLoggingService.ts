/**
 * Weight Confidence Logging Service
 * 
 * Logs and analyzes confidence metrics for food weight estimations
 * to track performance over time and identify areas for improvement.
 */


import AsyncStorage from '@react-native-async-storage/async-storage';
import { EnhancedWeightEstimate, WeightEstimationSource } from '../utils/enhancedWeightEstimation';
import { FoodShape } from '../utils/foodShapeRecognition';
import { firestore } from '@/lib/firebase';
import { getDoc, doc, collection, query, where, getDocs, addDoc, updateDoc, deleteDoc, orderBy, limit } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

// Type definitions
export interface ConfidenceLogEntry {
  id?: string;
  userId?: string;
  foodName: string;
  weightGrams: number;
  confidence: number;
  source: WeightEstimationSource;
  shape?: FoodShape;
  density?: number;
  hasReferenceObject: boolean;
  hasFeedback: boolean;
  userCorrectedWeight?: number;
  accuracyPercentage?: number;
  timestamp: number;
}

export interface AggregatedMetrics {
  overallConfidence: number;
  sourceBreakdown: Record<WeightEstimationSource, {
    count: number;
    avgConfidence: number;
    avgAccuracy?: number;
  }>;
  foodTypeBreakdown: Record<string, {
    count: number;
    avgConfidence: number;
    avgAccuracy?: number;
  }>;
  shapeBreakdown: Record<string, {
    count: number;
    avgConfidence: number;
    avgAccuracy?: number;
  }>;
}

// Constants
const CONFIDENCE_LOGS_KEY = 'weight_confidence_logs';
const MAX_LOCAL_LOGS = 1000;

/**
 * Weight Confidence Logging Service for tracking estimation accuracy
 */
export class WeightConfidenceLoggingService {
  private static instance: WeightConfidenceLoggingService;
  private localLogs: ConfidenceLogEntry[] = [];
  private isInitialized = false;

  private constructor() {}

  /**
   * Get service instance (singleton pattern)
   */
  public static getInstance(): WeightConfidenceLoggingService {
    if (!WeightConfidenceLoggingService.instance) {
      WeightConfidenceLoggingService.instance = new WeightConfidenceLoggingService();
    }
    return WeightConfidenceLoggingService.instance;
  }

  /**
   * Initialize the service by loading cached logs
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    try {
      // Load locally stored logs
      const storedLogs = await AsyncStorage.getItem(CONFIDENCE_LOGS_KEY);
      if (storedLogs) {
        this.localLogs = JSON.parse(storedLogs);
      }
      
      // Sync with remote if possible
      await this.syncLogsWithRemote();
      
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize weight confidence logging service:', error);
    }
  }

  /**
   * Log a weight estimation event with confidence metrics
   * 
   * @param weightEstimate The weight estimation result
   * @param hasReferenceObject Whether reference objects were used
   */
  public async logEstimation(
    weightEstimate: EnhancedWeightEstimate,
    hasReferenceObject: boolean
  ): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    try {
      // Create log entry
      const logEntry: ConfidenceLogEntry = {
        foodName: weightEstimate.foodName,
        weightGrams: weightEstimate.weightGrams,
        confidence: weightEstimate.confidence,
        source: weightEstimate.source,
        shape: weightEstimate.details?.shape,
        density: weightEstimate.details?.density,
        hasReferenceObject,
        hasFeedback: false,
        timestamp: Date.now()
      };
      
      // Add to local logs
      this.localLogs.push(logEntry);
      
      // Trim local logs if needed
      if (this.localLogs.length > MAX_LOCAL_LOGS) {
        this.localLogs = this.localLogs.slice(-MAX_LOCAL_LOGS);
      }
      
      // Save to local storage
      await AsyncStorage.setItem(CONFIDENCE_LOGS_KEY, JSON.stringify(this.localLogs));
      
      // Try to send to remote
      await this.saveLogToRemote(logEntry);
    } catch (error) {
      console.error('Failed to log weight estimation:', error);
    }
  }

  /**
   * Update a log entry with user feedback
   * 
   * @param estimationId ID or timestamp of the original estimation
   * @param userCorrectedWeight The user-provided correct weight
   */
  public async updateWithFeedback(
    estimationId: string | number,
    userCorrectedWeight: number
  ): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    try {
      // Find the log entry (either by ID or timestamp)
      const isTimestamp = typeof estimationId === 'number';
      const logIndex = this.localLogs.findIndex(log => 
        isTimestamp ? log.timestamp === estimationId : log.id === estimationId
      );
      
      if (logIndex >= 0) {
        const logEntry = this.localLogs[logIndex];
        
        // Calculate accuracy percentage
        const originalWeight = logEntry.weightGrams;
        const accuracyPercentage = 
          100 - Math.min(100, Math.abs((originalWeight - userCorrectedWeight) / userCorrectedWeight * 100));
        
        // Update the log entry
        this.localLogs[logIndex] = {
          ...logEntry,
          hasFeedback: true,
          userCorrectedWeight,
          accuracyPercentage
        };
        
        // Save to local storage
        await AsyncStorage.setItem(CONFIDENCE_LOGS_KEY, JSON.stringify(this.localLogs));
        
        // Try to update in remote database
        if (logEntry.id) {
          await this.updateLogInRemote(logEntry.id, {
            hasFeedback: true,
            userCorrectedWeight,
            accuracyPercentage
          });
        }
      }
    } catch (error) {
      console.error('Failed to update log with feedback:', error);
    }
  }

  /**
   * Get aggregated confidence metrics
   * 
   * @param days Number of days to include in analysis (0 for all)
   * @param userId Optional user ID to filter by
   */
  public async getAggregatedMetrics(
    days: number = 30,
    userId?: string
  ): Promise<AggregatedMetrics> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    // Filter logs by time period and user if specified
    const cutoffTime = days > 0 ? Date.now() - (days * 24 * 60 * 60 * 1000) : 0;
    const filteredLogs = this.localLogs.filter(log => 
      log.timestamp >= cutoffTime && (!userId || log.userId === userId)
    );
    
    // Initialize results
    const metrics: AggregatedMetrics = {
      overallConfidence: 0,
      sourceBreakdown: {} as Record<WeightEstimationSource, any>,
      foodTypeBreakdown: {},
      shapeBreakdown: {}
    };
    
    // Initialize source breakdown
    Object.values(WeightEstimationSource).forEach(source => {
      metrics.sourceBreakdown[source] = {
        count: 0,
        avgConfidence: 0,
        avgAccuracy: undefined
      };
    });
    
    // Return empty results if no logs
    if (filteredLogs.length === 0) {
      return metrics;
    }
    
    // Calculate overall confidence
    const totalConfidence = filteredLogs.reduce((sum, log) => sum + log.confidence, 0);
    metrics.overallConfidence = totalConfidence / filteredLogs.length;
    
    // Process each log for breakdowns
    filteredLogs.forEach(log => {
      // Source breakdown
      const sourceStats = metrics.sourceBreakdown[log.source];
      sourceStats.count++;
      sourceStats.avgConfidence = 
        (sourceStats.avgConfidence * (sourceStats.count - 1) + log.confidence) / sourceStats.count;
      
      if (log.hasFeedback && log.accuracyPercentage !== undefined) {
        if (sourceStats.avgAccuracy === undefined) {
          sourceStats.avgAccuracy = log.accuracyPercentage;
        } else {
          sourceStats.avgAccuracy = 
            (sourceStats.avgAccuracy * (sourceStats.count - 1) + log.accuracyPercentage) / sourceStats.count;
        }
      }
      
      // Food type breakdown
      // Simplify food name to category (e.g., "grilled chicken breast" -> "chicken")
      const foodCategory = this.simplifyFoodName(log.foodName);
      if (!metrics.foodTypeBreakdown[foodCategory]) {
        metrics.foodTypeBreakdown[foodCategory] = {
          count: 0,
          avgConfidence: 0,
          avgAccuracy: undefined
        };
      }
      
      const foodStats = metrics.foodTypeBreakdown[foodCategory];
      foodStats.count++;
      foodStats.avgConfidence = 
        (foodStats.avgConfidence * (foodStats.count - 1) + log.confidence) / foodStats.count;
      
      if (log.hasFeedback && log.accuracyPercentage !== undefined) {
        if (foodStats.avgAccuracy === undefined) {
          foodStats.avgAccuracy = log.accuracyPercentage;
        } else {
          foodStats.avgAccuracy = 
            (foodStats.avgAccuracy * (foodStats.count - 1) + log.accuracyPercentage) / foodStats.count;
        }
      }
      
      // Shape breakdown
      if (log.shape) {
        if (!metrics.shapeBreakdown[log.shape]) {
          metrics.shapeBreakdown[log.shape] = {
            count: 0,
            avgConfidence: 0,
            avgAccuracy: undefined
          };
        }
        
        const shapeStats = metrics.shapeBreakdown[log.shape];
        shapeStats.count++;
        shapeStats.avgConfidence = 
          (shapeStats.avgConfidence * (shapeStats.count - 1) + log.confidence) / shapeStats.count;
        
        if (log.hasFeedback && log.accuracyPercentage !== undefined) {
          if (shapeStats.avgAccuracy === undefined) {
            shapeStats.avgAccuracy = log.accuracyPercentage;
          } else {
            shapeStats.avgAccuracy = 
              (shapeStats.avgAccuracy * (shapeStats.count - 1) + log.accuracyPercentage) / shapeStats.count;
          }
        }
      }
    });
    
    return metrics;
  }

  /**
   * Get raw log entries for detailed analysis
   * 
   * @param limit Maximum number of logs to return
   * @param offset Starting position
   * @param userId Optional user ID to filter by
   */
  public async getLogEntries(
    limit: number = 100,
    offset: number = 0,
    userId?: string
  ): Promise<ConfidenceLogEntry[]> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    // Filter by user if specified
    const filteredLogs = userId 
      ? this.localLogs.filter(log => log.userId === userId)
      : [...this.localLogs];
    
    // Sort by timestamp descending (newest first)
    filteredLogs.sort((a, b) => b.timestamp - a.timestamp);
    
    // Apply limit and offset
    return filteredLogs.slice(offset, offset + limit);
  }

  /**
   * Sync locally stored logs with remote database
   */
  private async syncLogsWithRemote(): Promise<void> {
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      
      if (!user) {
        return; // Not logged in, can't sync
      }
      
      // Get logs from remote that aren't in local cache
      const logsRef = collection(firestore, 'weight_confidence_logs');
      const q = query(
        logsRef,
        where('userId', '==', user.uid),
        orderBy('timestamp', 'desc'),
        limit(MAX_LOCAL_LOGS)
      );
      
      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        // Merge remote logs with local logs
        const localTimeMap = new Map(this.localLogs.map(log => [log.timestamp, log]));
        
        querySnapshot.forEach(doc => {
          const remoteLog = { id: doc.id, ...doc.data() } as ConfidenceLogEntry;
          if (!localTimeMap.has(remoteLog.timestamp)) {
            this.localLogs.push(remoteLog);
          }
        });
        
        // Sort by timestamp and limit size
        this.localLogs.sort((a, b) => b.timestamp - a.timestamp);
        this.localLogs = this.localLogs.slice(0, MAX_LOCAL_LOGS);
        
        // Save updated logs to local storage
        await AsyncStorage.setItem(CONFIDENCE_LOGS_KEY, JSON.stringify(this.localLogs));
      }
      
      // Find local logs that need to be sent to remote
      const unsyncedLogs = this.localLogs.filter(log => !log.id);
      
      // Send unsynced logs to remote
      for (const log of unsyncedLogs) {
        await this.saveLogToRemote(log);
      }
    } catch (error) {
      console.error('Failed to sync weight confidence logs with remote:', error);
    }
  }

  /**
   * Save a log entry to remote database
   */
  private async saveLogToRemote(logEntry: ConfidenceLogEntry): Promise<void> {
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      
      if (!user) {
        return; // Not logged in, can't save to remote
      }
      
      // Add user ID to log entry
      const logWithUser = {
        ...logEntry,
        userId: user.uid
      };
      
      // Save to Firebase
      const docRef = await addDoc(collection(firestore, 'weight_confidence_logs'), logWithUser);
      
      if (docRef.id) {
        // Update local copy with ID from remote
        const index = this.localLogs.findIndex(log => log.timestamp === logEntry.timestamp);
        if (index >= 0) {
          this.localLogs[index] = { ...logWithUser, id: docRef.id };
          await AsyncStorage.setItem(CONFIDENCE_LOGS_KEY, JSON.stringify(this.localLogs));
        }
      }
    } catch (error) {
      console.error('Failed to save weight confidence log to remote:', error);
    }
  }

  /**
   * Update a log entry in remote database
   */
  private async updateLogInRemote(
    id: string,
    updates: Partial<ConfidenceLogEntry>
  ): Promise<void> {
    try {
      const docRef = doc(firestore, 'weight_confidence_logs', id);
      await updateDoc(docRef, updates);
    } catch (error) {
      console.error('Failed to update weight confidence log in remote:', error);
    }
  }

  /**
   * Simplify food name to category for better grouping
   */
  private simplifyFoodName(foodName: string): string {
    // Convert to lowercase
    const lowerName = foodName.toLowerCase();
    
    // List of common food categories
    const categories = [
      'chicken', 'beef', 'pork', 'fish', 'seafood', 'vegetable', 'fruit',
      'salad', 'pasta', 'rice', 'bread', 'sandwich', 'soup', 'dessert',
      'cake', 'cookie', 'ice cream', 'pizza', 'burger', 'egg', 'cheese',
      'yogurt', 'cereal', 'nuts', 'beans', 'tofu'
    ];
    
    // Try to find a matching category
    for (const category of categories) {
      if (lowerName.includes(category)) {
        return category;
      }
    }
    
    // If no category matches, return first two words
    const words = lowerName.split(' ');
    return words.length > 1 ? `${words[0]} ${words[1]}` : words[0];
  }
}

/**
 * Log a weight estimation event
 * 
 * @param weightEstimate The weight estimation result
 * @param hasReferenceObject Whether reference objects were used
 */
export async function logWeightEstimation(
  weightEstimate: EnhancedWeightEstimate,
  hasReferenceObject: boolean
): Promise<void> {
  const loggingService = WeightConfidenceLoggingService.getInstance();
  await loggingService.logEstimation(weightEstimate, hasReferenceObject);
}

/**
 * Get aggregated confidence metrics
 * 
 * @param days Number of days to include in analysis (0 for all)
 * @param userId Optional user ID to filter by
 */
export async function getConfidenceMetrics(
  days: number = 30,
  userId?: string
): Promise<AggregatedMetrics> {
  const loggingService = WeightConfidenceLoggingService.getInstance();
  return await loggingService.getAggregatedMetrics(days, userId);
}

/**
 * Update a log with user feedback
 * 
 * @param estimationId ID or timestamp of the original estimation
 * @param userCorrectedWeight The user-provided correct weight
 */
export async function updateLogWithFeedback(
  estimationId: string | number,
  userCorrectedWeight: number
): Promise<void> {
  const loggingService = WeightConfidenceLoggingService.getInstance();
  await loggingService.updateWithFeedback(estimationId, userCorrectedWeight);
} 