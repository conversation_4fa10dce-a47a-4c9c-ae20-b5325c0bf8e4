
import AsyncStorage from '@react-native-async-storage/async-storage';
import { firestore } from '@/lib/firebase';
import { getDoc, doc, collection, query, where, getDocs, addDoc, updateDoc, deleteDoc, orderBy, setDoc } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { format } from 'date-fns';
import { Platform } from 'react-native';
import { WeightCalibrationData, DEFAULT_WEIGHT_CALIBRATION } from '../utils/foodDensityMap';

// Storage keys
const WEIGHT_FEEDBACK_KEY = 'weight_feedback';
const CALIBRATION_KEY_PREFIX = 'weight_calibration_';

/**
 * Types of weight estimation feedback
 */
export type WeightFeedbackType = 'accurate' | 'too-high' | 'too-low' | 'way-off';

/**
 * Interface for weight feedback data
 */
export interface WeightFeedbackData {
  id?: string;
  userId?: string;
  foodName: string;
  estimatedWeight: number;
  actualWeight?: number;
  feedbackType: WeightFeedbackType;
  timestamp: string;
  deviceInfo?: string;
  scanId?: string;
}

/**
 * Interface for meal weight feedback
 */
export interface WeightFeedback {
  id: string;
  userId: string;
  date: string;
  weight: number;
  mealId?: string;
  confidence: number;
  adjustments?: {
    type: 'portion' | 'ingredient';
    name: string;
    original: number;
    adjusted: number;
    unit: string;
  }[];
  notes?: string;
  createdAt: string;
}

/**
 * Save weight feedback to local storage and optionally to the cloud
 */
export async function saveWeightFeedbackData(
  feedbackData: Omit<WeightFeedbackData, 'id' | 'timestamp'>,
  userId?: string
): Promise<{success: boolean, message?: string}> {
  try {
    // Create complete feedback record
    const fullFeedback: WeightFeedbackData = {
      ...feedbackData,
      userId,
      timestamp: new Date().toISOString(),
      deviceInfo: `${Platform.OS} ${Platform.Version}`
    };
    
    // Save to local storage first
    await saveLocalFeedback(fullFeedback);
    
    // If user is logged in, save to Firebase
    if (userId) {
      await saveCloudFeedback(fullFeedback);
    }
    
    // Update calibration data based on feedback
    await updateCalibrationFromFeedback(fullFeedback, userId);
    
    return { success: true };
  } catch (error: any) {
    console.error('Error saving weight feedback:', error);
    return {
      success: false,
      message: error.message
    };
  }
}

/**
 * Save feedback to local AsyncStorage
 */
async function saveLocalFeedback(feedback: WeightFeedbackData): Promise<void> {
  try {
    // Get existing feedback array
    const existingData = await AsyncStorage.getItem('weight_feedback_data');
    const feedbackArray: WeightFeedbackData[] = existingData 
      ? JSON.parse(existingData) 
      : [];
    
    // Add new feedback
    feedbackArray.push(feedback);
    
    // Save back to AsyncStorage
    await AsyncStorage.setItem('weight_feedback_data', JSON.stringify(feedbackArray));
  } catch (error) {
    console.error('Error saving local feedback:', error);
    throw error;
  }
}

/**
 * Save feedback to Firebase cloud database
 */
async function saveCloudFeedback(feedback: WeightFeedbackData): Promise<void> {
  try {
    const feedbackData = {
      user_id: feedback.userId,
      food_name: feedback.foodName,
      estimated_weight: feedback.estimatedWeight,
      actual_weight: feedback.actualWeight,
      feedback_type: feedback.feedbackType,
      timestamp: feedback.timestamp,
      device_info: feedback.deviceInfo,
      scan_id: feedback.scanId,
      created_at: new Date().toISOString()
    };
    
    await addDoc(collection(firestore, 'weight_feedback_data'), feedbackData);
  } catch (error) {
    console.error('Error saving cloud feedback:', error);
    // Don't throw - we'll still have local data
  }
}

/**
 * Update calibration data based on user feedback
 */
async function updateCalibrationFromFeedback(
  feedback: WeightFeedbackData,
  userId?: string
): Promise<void> {
  try {
    // Get current calibration data
    const calibrationData = await getCalibrationData(userId);
    
    // Calculate adjustment factor based on feedback type
    let adjustmentFactor = 1.0;
    
    if (feedback.actualWeight) {
      // If we have the actual weight, calculate precise adjustment
      adjustmentFactor = feedback.actualWeight / feedback.estimatedWeight;
    } else {
      // Otherwise, use approximation based on feedback type
      switch (feedback.feedbackType) {
        case 'accurate':
          adjustmentFactor = 1.0;
          break;
        case 'too-high':
          adjustmentFactor = 0.85; // Reduce by ~15%
          break;
        case 'too-low':
          adjustmentFactor = 1.15; // Increase by ~15%
          break;
        case 'way-off':
          // Don't adjust calibration for "way-off" feedback
          // without actual weight - too unpredictable
          return;
      }
    }
    
    // Add item-specific calibration factor
    calibrationData.itemSpecificFactors[feedback.foodName.toLowerCase()] = adjustmentFactor;
    
    // Store updated calibration data
    await saveCalibrationData(calibrationData, userId);
  } catch (error) {
    console.error('Error updating calibration data:', error);
  }
}

/**
 * Get current weight calibration data
 */
async function getCalibrationData(userId?: string): Promise<WeightCalibrationData> {
  try {
    // Try to get from local storage first
    const storageKey = userId 
      ? `${CALIBRATION_KEY_PREFIX}${userId}` 
      : `${CALIBRATION_KEY_PREFIX}anonymous`;
      
    const storedData = await AsyncStorage.getItem(storageKey);
    
    if (storedData) {
      return JSON.parse(storedData);
    }
    
    // If logged in, try to get from Firebase
    if (userId) {
      const calibrationDoc = await getDoc(doc(firestore, 'weight_calibrations', userId));
      
      if (calibrationDoc.exists()) {
        return calibrationDoc.data().calibration_data as WeightCalibrationData;
      }
    }
    
    // If no data found, return default
    return {
      ...DEFAULT_WEIGHT_CALIBRATION,
      userId: userId || ''
    };
  } catch (error) {
    console.error('Error getting calibration data:', error);
    return {
      ...DEFAULT_WEIGHT_CALIBRATION,
      userId: userId || ''
    };
  }
}

/**
 * Save weight calibration data
 */
async function saveCalibrationData(
  calibrationData: WeightCalibrationData,
  userId?: string
): Promise<void> {
  try {
    // Update timestamp
    calibrationData.lastUpdated = Date.now();
    
    // Save to local storage
    const storageKey = userId 
      ? `${CALIBRATION_KEY_PREFIX}${userId}` 
      : `${CALIBRATION_KEY_PREFIX}anonymous`;
      
    await AsyncStorage.setItem(storageKey, JSON.stringify(calibrationData));
    
    // If logged in, save to Firebase
    if (userId) {
      await setDoc(doc(firestore, 'weight_calibrations', userId), {
        user_id: userId,
        calibration_data: calibrationData,
        updated_at: new Date().toISOString()
      }, { merge: true });
    }
  } catch (error) {
    console.error('Error saving calibration data:', error);
  }
}

/**
 * Get weight feedback statistics
 */
export async function getFeedbackStats(userId?: string): Promise<{
  totalFeedback: number;
  accuratePercentage: number;
  mostCorrectedFoods: {foodName: string, count: number}[];
}> {
  try {
    let feedbackArray: WeightFeedbackData[] = [];
    
    // Get local feedback
    const localData = await AsyncStorage.getItem('weight_feedback_data');
    if (localData) {
      const parsedData = JSON.parse(localData) as WeightFeedbackData[];
      feedbackArray = userId
        ? parsedData.filter(item => item.userId === userId)
        : parsedData;
    }
    
    // Calculate statistics
    const totalFeedback = feedbackArray.length;
    const accurateFeedback = feedbackArray.filter(
      item => item.feedbackType === 'accurate'
    ).length;
    
    // Count corrections by food
    const foodCounts = new Map<string, number>();
    feedbackArray
      .filter(item => item.feedbackType !== 'accurate')
      .forEach(item => {
        const currentCount = foodCounts.get(item.foodName) || 0;
        foodCounts.set(item.foodName, currentCount + 1);
      });
    
    // Convert to array and sort by count
    const mostCorrectedFoods = Array.from(foodCounts.entries())
      .map(([foodName, count]) => ({ foodName, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5); // Top 5 most corrected foods
    
    return {
      totalFeedback,
      accuratePercentage: totalFeedback > 0 
        ? (accurateFeedback / totalFeedback) * 100
        : 0,
      mostCorrectedFoods
    };
  } catch (error) {
    console.error('Error getting feedback stats:', error);
    return {
      totalFeedback: 0,
      accuratePercentage: 0,
      mostCorrectedFoods: []
    };
  }
}

/**
 * Save weight feedback for a meal or ingredient
 */
export async function saveWeightFeedback(
  weight: number,
  confidence: number,
  adjustments?: WeightFeedback['adjustments'],
  mealId?: string,
  notes?: string
): Promise<WeightFeedback> {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    const now = new Date();
    const dateStr = format(now, 'yyyy-MM-dd');
    
    const feedback: WeightFeedback = {
      id: Date.now().toString(), // Temporary ID
      userId: user?.uid || 'anonymous',
      date: dateStr,
      weight,
      confidence,
      mealId,
      adjustments,
      notes,
      createdAt: now.toISOString()
    };
    
    // If user is logged in, save to Firebase
    if (user) {
      try {
        const feedbackData = {
          user_id: user.uid,
          date: dateStr,
          weight,
          confidence,
          meal_id: mealId,
          adjustments,
          notes,
          created_at: feedback.createdAt
        };
        
        const docRef = await addDoc(collection(firestore, 'weight_feedback'), feedbackData);
        feedback.id = docRef.id;
        console.log('Saved weight feedback to Firebase with ID:', feedback.id);
      } catch (error) {
        console.error('Error saving feedback to Firebase:', error);
      }
    }
    
    // Also save locally
    const existingJson = await AsyncStorage.getItem(WEIGHT_FEEDBACK_KEY);
    let existingFeedback: WeightFeedback[] = [];
    
    if (existingJson) {
      existingFeedback = JSON.parse(existingJson);
    }
    
    existingFeedback.push(feedback);
    
    await AsyncStorage.setItem(WEIGHT_FEEDBACK_KEY, JSON.stringify(existingFeedback));
    
    return feedback;
  } catch (error) {
    console.error('Error saving weight feedback:', error);
    throw error;
  }
}

/**
 * Save feedback to Firebase cloud database
 */
export async function syncWeightFeedbackToCloud(): Promise<boolean> {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      console.log('User not logged in, skipping sync');
      return false;
    }
    
    const existingJson = await AsyncStorage.getItem(WEIGHT_FEEDBACK_KEY);
    if (!existingJson) {
      return true; // Nothing to sync
    }
    
    const existingFeedback: WeightFeedback[] = JSON.parse(existingJson);
    const localOnlyFeedback = existingFeedback.filter(
      f => f.userId === 'anonymous' || !f.id.includes('-')
    );
    
    if (localOnlyFeedback.length === 0) {
      return true; // Nothing new to sync
    }
    
    // Sync each local item to Firebase
    for (const feedback of localOnlyFeedback) {
      try {
        const feedbackData = {
          user_id: user.uid,
          date: feedback.date,
          weight: feedback.weight,
          confidence: feedback.confidence,
          meal_id: feedback.mealId,
          adjustments: feedback.adjustments,
          notes: feedback.notes,
          created_at: feedback.createdAt
        };
        
        const docRef = await addDoc(collection(firestore, 'weight_feedback'), feedbackData);
        
        // Update the local record with the Firebase ID
        feedback.id = docRef.id;
        feedback.userId = user.uid;
      } catch (error) {
        console.error('Error syncing feedback to Firebase:', error);
      }
    }
    
    // Save updated records with Firebase IDs
    await AsyncStorage.setItem(WEIGHT_FEEDBACK_KEY, JSON.stringify(existingFeedback));
    
    return true;
  } catch (error) {
    console.error('Error syncing weight feedback:', error);
    return false;
  }
}

/**
 * Get all weight feedback for the current user
 */
export async function getWeightFeedback(): Promise<WeightFeedback[]> {
  try {
    // Start with local feedback
    const existingJson = await AsyncStorage.getItem(WEIGHT_FEEDBACK_KEY);
    let feedback: WeightFeedback[] = [];
    
    if (existingJson) {
      feedback = JSON.parse(existingJson);
    }
    
    const auth = getAuth();
    const user = auth.currentUser;
    
    // If logged in, try to get from Firebase
    if (user) {
      try {
        const q = query(
          collection(firestore, 'weight_feedback'),
          where('user_id', '==', user.uid),
          orderBy('created_at', 'desc')
        );
        
        const querySnapshot = await getDocs(q);
        
        if (!querySnapshot.empty) {
          const remoteFeedback: WeightFeedback[] = querySnapshot.docs.map(doc => {
            const data = doc.data();
            return {
              id: doc.id,
              userId: data.user_id,
              date: data.date,
              weight: data.weight,
              confidence: data.confidence,
              mealId: data.meal_id,
              adjustments: data.adjustments,
              notes: data.notes,
              createdAt: data.created_at
            };
          });
          
          // Merge remote and local, removing duplicates
          const mergedFeedback = [...remoteFeedback];
          
          // Add local items that don't exist in remote (by ID)
          const remoteIds = new Set(remoteFeedback.map(f => f.id));
          const localOnlyFeedback = feedback.filter(f => !remoteIds.has(f.id));
          
          mergedFeedback.push(...localOnlyFeedback);
          
          // Sort by creation date (newest first)
          mergedFeedback.sort((a, b) => 
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
          
          // Update local cache
          await AsyncStorage.setItem(WEIGHT_FEEDBACK_KEY, JSON.stringify(mergedFeedback));
          
          return mergedFeedback;
        }
      } catch (error) {
        console.error('Error retrieving feedback from Firebase:', error);
        // Continue with local data only
      }
    }
    
    // Sort by creation date (newest first)
    feedback.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
    
    return feedback;
  } catch (error) {
    console.error('Error getting weight feedback:', error);
    return [];
  }
}

/**
 * Update existing weight feedback
 */
export async function updateWeightFeedback(
  id: string,
  updates: Partial<Omit<WeightFeedback, 'id' | 'userId' | 'createdAt'>>
): Promise<boolean> {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    // If logged in, save to Firebase
    if (user) {
      try {
        // Create Firebase-friendly update object
        const updateData: Record<string, any> = {};
        
        if (updates.weight !== undefined) updateData.weight = updates.weight;
        if (updates.confidence !== undefined) updateData.confidence = updates.confidence;
        if (updates.mealId !== undefined) updateData.meal_id = updates.mealId;
        if (updates.adjustments !== undefined) updateData.adjustments = updates.adjustments;
        if (updates.notes !== undefined) updateData.notes = updates.notes;
        if (updates.date !== undefined) updateData.date = updates.date;
        
        updateData.updated_at = new Date().toISOString();
        
        await updateDoc(doc(firestore, 'weight_feedback', id), updateData);
        console.log('Updated weight feedback in Firebase with ID:', id);
      } catch (error) {
        console.error('Error saving to Firebase:', error);
      }
    }
    
    // Update locally too
    const existingJson = await AsyncStorage.getItem(WEIGHT_FEEDBACK_KEY);
    if (!existingJson) {
      return false;
    }
    
    let existingFeedback: WeightFeedback[] = JSON.parse(existingJson);
    
    // Find and update the feedback item
    existingFeedback = existingFeedback.map(item => {
      if (item.id === id) {
        return { ...item, ...updates };
      }
      return item;
    });
    
    await AsyncStorage.setItem(WEIGHT_FEEDBACK_KEY, JSON.stringify(existingFeedback));
    
    return true;
  } catch (error) {
    console.error('Error updating weight feedback:', error);
    return false;
  }
} 