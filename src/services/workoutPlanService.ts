
import { ActivityType } from './activityService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getUserProfile } from './nutritionGoalService';

import { firestore } from '@/lib/firebase';
import { getDoc, doc, collection, query, where, getDocs, addDoc, updateDoc, deleteDoc, orderBy } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

// Storage keys
const WORKOUT_PLANS_KEY = 'workout_plans_';
const WORKOUT_HISTORY_KEY = 'workout_history_';

export enum WorkoutDifficulty {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced'
}

export enum WorkoutFocus {
  STRENGTH = 'strength',
  CARDIO = 'cardio',
  FLEXIBILITY = 'flexibility',
  BALANCE = 'balance',
  WEIGHT_LOSS = 'weight_loss',
  MUSCLE_GAIN = 'muscle_gain',
  ENDURANCE = 'endurance',
  GENERAL_FITNESS = 'general_fitness'
}

export enum BodyArea {
  FULL_BODY = 'full_body',
  UPPER_BODY = 'upper_body',
  LOWER_BODY = 'lower_body',
  CORE = 'core',
  ARMS = 'arms',
  LEGS = 'legs',
  BACK = 'back',
  CHEST = 'chest',
  SHOULDERS = 'shoulders'
}

export interface Exercise {
  id: string;
  name: string;
  description: string;
  videoUrl?: string;
  imageUrl?: string;
  bodyArea: BodyArea;
  equipment?: string[];
  tips?: string[];
  difficulty: WorkoutDifficulty;
  primaryMuscles: string[];
  secondaryMuscles: string[];
  tags?: string[];
}

export interface WorkoutExercise {
  exercise: Exercise;
  sets?: number;
  reps?: number;
  duration?: number; // in seconds
  distance?: number; // in meters
  restBetweenSets?: number; // in seconds
  order: number;
  notes?: string;
}

export interface WorkoutSession {
  id: string;
  name: string;
  description: string;
  difficulty: WorkoutDifficulty;
  duration: number; // in minutes
  focus: WorkoutFocus[];
  bodyAreas: BodyArea[];
  exercises: WorkoutExercise[];
  warmup?: WorkoutExercise[];
  cooldown?: WorkoutExercise[];
  tags?: string[];
  imageUrl?: string;
}

export interface WorkoutPlan {
  id: string;
  userId: string;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  difficulty: WorkoutDifficulty;
  focus: WorkoutFocus[];
  sessions: WorkoutSession[];
  daysPerWeek: number;
  restDays: number[];
  tags?: string[];
  imageUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface WorkoutCompletion {
  id: string;
  userId: string;
  workoutSessionId: string;
  workoutPlanId?: string;
  completedExercises: {
    exerciseId: string;
    sets?: number;
    reps?: number;
    duration?: number;
    distance?: number;
    weight?: number;
    completed: boolean;
    notes?: string;
  }[];
  startTime: string;
  endTime: string;
  duration: number; // in minutes
  caloriesBurned?: number;
  rating?: number; // 1-5
  notes?: string;
  createdAt: string;
}

/**
 * Fetch available workout plans for the current user
 */
export async function fetchWorkoutPlans(): Promise<{
  success: boolean;
  data?: WorkoutPlan[];
  error?: string;
}> {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      console.error('Error getting user: No user found');
      return { success: false, error: 'Authentication error' };
    }

    // Try to get plans from Firebase
    try {
      const plansRef = collection(firestore, 'workout_plans');
      const q = query(
        plansRef, 
        where('user_id', '==', user.uid),
        orderBy('created_at', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        const plans: WorkoutPlan[] = querySnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            userId: data.user_id,
            name: data.name,
            description: data.description,
            startDate: data.start_date,
            endDate: data.end_date,
            difficulty: data.difficulty as WorkoutDifficulty,
            focus: data.focus as WorkoutFocus[],
            sessions: data.sessions as WorkoutSession[],
            daysPerWeek: data.days_per_week,
            restDays: data.rest_days,
            tags: data.tags,
            imageUrl: data.image_url,
            createdAt: data.created_at,
            updatedAt: data.updated_at
          };
        });

        return { success: true, data: plans };
      }
    } catch (dbError) {
      console.error('Error fetching from database:', dbError);
      // Continue to try local storage
    }

    // If database fetch failed or returned no results, try local storage
    const plansJson = await AsyncStorage.getItem(`${WORKOUT_PLANS_KEY}${user.uid}`);
    if (plansJson) {
      const plans = JSON.parse(plansJson) as WorkoutPlan[];
      return { success: true, data: plans };
    }

    // If no plans found, return empty array
    return { success: true, data: [] };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in fetchWorkoutPlans:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Fetch a specific workout plan by ID
 */
export async function fetchWorkoutPlan(planId: string): Promise<{
  success: boolean;
  data?: WorkoutPlan;
  error?: string;
}> {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      console.error('Error getting user: No user found');
      return { success: false, error: 'Authentication error' };
    }

    // Try to get plan from Firebase
    try {
      const planDoc = await getDoc(doc(firestore, 'workout_plans', planId));
      
      if (planDoc.exists()) {
        const data = planDoc.data();
        // Verify this plan belongs to the current user
        if (data.user_id === user.uid) {
          const plan: WorkoutPlan = {
            id: planDoc.id,
            userId: data.user_id,
            name: data.name,
            description: data.description,
            startDate: data.start_date,
            endDate: data.end_date,
            difficulty: data.difficulty as WorkoutDifficulty,
            focus: data.focus as WorkoutFocus[],
            sessions: data.sessions as WorkoutSession[],
            daysPerWeek: data.days_per_week,
            restDays: data.rest_days,
            tags: data.tags,
            imageUrl: data.image_url,
            createdAt: data.created_at,
            updatedAt: data.updated_at
          };

          return { success: true, data: plan };
        }
      }
    } catch (dbError) {
      console.error('Error fetching from database:', dbError);
      // Continue to try local storage
    }

    // If database fetch failed, try local storage
    const plansJson = await AsyncStorage.getItem(`${WORKOUT_PLANS_KEY}${user.uid}`);
    if (plansJson) {
      const plans = JSON.parse(plansJson) as WorkoutPlan[];
      const plan = plans.find(p => p.id === planId);
      if (plan) {
        return { success: true, data: plan };
      }
    }

    return { success: false, error: 'Workout plan not found' };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in fetchWorkoutPlan:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Creates a new workout plan
 */
export async function createWorkoutPlan(planData: Omit<WorkoutPlan, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<{
  success: boolean;
  data?: WorkoutPlan;
  error?: string;
}> {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Authentication error: Not authenticated' };
    }

    const newPlan: WorkoutPlan = {
      ...planData,
      id: Date.now().toString(),
      userId: user.uid,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Try to store in Firebase
    try {
      const planData = {
        user_id: newPlan.userId,
        name: newPlan.name,
        description: newPlan.description,
        start_date: newPlan.startDate,
        end_date: newPlan.endDate,
        difficulty: newPlan.difficulty,
        focus: newPlan.focus,
        sessions: newPlan.sessions,
        days_per_week: newPlan.daysPerWeek,
        rest_days: newPlan.restDays,
        tags: newPlan.tags,
        image_url: newPlan.imageUrl,
        created_at: newPlan.createdAt,
        updated_at: newPlan.updatedAt
      };
      
      const docRef = await addDoc(collection(firestore, 'workout_plans'), planData);
      newPlan.id = docRef.id;
      
      return { success: true, data: newPlan };
    } catch (dbError) {
      console.error('Error storing in database:', dbError);
      // Continue to store in local storage
    }

    // If database storage failed, store in local storage
    const plansJson = await AsyncStorage.getItem(`${WORKOUT_PLANS_KEY}${user.uid}`);
    let plans: WorkoutPlan[] = [];
    
    if (plansJson) {
      plans = JSON.parse(plansJson);
    }
    
    plans.push(newPlan);
    await AsyncStorage.setItem(`${WORKOUT_PLANS_KEY}${user.uid}`, JSON.stringify(plans));

    return { success: true, data: newPlan };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in createWorkoutPlan:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Updates an existing workout plan
 */
export async function updateWorkoutPlan(planId: string, updates: Partial<Omit<WorkoutPlan, 'id' | 'userId' | 'createdAt' | 'updatedAt'>>): Promise<{
  success: boolean;
  data?: WorkoutPlan;
  error?: string;
}> {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Authentication error: Not authenticated' };
    }

    // First try to get the plan to update
    const { success, data: existingPlan, error: fetchError } = await fetchWorkoutPlan(planId);
    if (!success || !existingPlan) {
      return { success: false, error: fetchError || 'Plan not found' };
    }

    const updatedPlan: WorkoutPlan = {
      ...existingPlan,
      ...updates,
      updatedAt: new Date().toISOString()
    };

    // Try to update in Firebase
    try {
      const planRef = doc(firestore, 'workout_plans', planId);
      
      await updateDoc(planRef, {
        name: updatedPlan.name,
        description: updatedPlan.description,
        start_date: updatedPlan.startDate,
        end_date: updatedPlan.endDate,
        difficulty: updatedPlan.difficulty,
        focus: updatedPlan.focus,
        sessions: updatedPlan.sessions,
        days_per_week: updatedPlan.daysPerWeek,
        rest_days: updatedPlan.restDays,
        tags: updatedPlan.tags,
        image_url: updatedPlan.imageUrl,
        updated_at: updatedPlan.updatedAt
      });
      
      return { success: true, data: updatedPlan };
    } catch (dbError) {
      console.error('Error updating in database:', dbError);
      // Continue to update in local storage
    }

    // If database update failed, update in local storage
    const plansJson = await AsyncStorage.getItem(`${WORKOUT_PLANS_KEY}${user.uid}`);
    if (plansJson) {
      let plans = JSON.parse(plansJson) as WorkoutPlan[];
      plans = plans.map(p => p.id === planId ? updatedPlan : p);
      await AsyncStorage.setItem(`${WORKOUT_PLANS_KEY}${user.uid}`, JSON.stringify(plans));
      return { success: true, data: updatedPlan };
    }

    return { success: false, error: 'Failed to update workout plan' };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in updateWorkoutPlan:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Deletes a workout plan
 */
export async function deleteWorkoutPlan(planId: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Authentication error: Not authenticated' };
    }

    // Try to delete from Firebase
    try {
      const planRef = doc(firestore, 'workout_plans', planId);
      
      // Verify ownership before deleting
      const planDoc = await getDoc(planRef);
      if (planDoc.exists() && planDoc.data().user_id === user.uid) {
        await deleteDoc(planRef);
        return { success: true };
      }
    } catch (dbError) {
      console.error('Error deleting from database:', dbError);
      // Continue to delete from local storage
    }

    // If database deletion failed, delete from local storage
    const plansJson = await AsyncStorage.getItem(`${WORKOUT_PLANS_KEY}${user.uid}`);
    if (plansJson) {
      let plans = JSON.parse(plansJson) as WorkoutPlan[];
      plans = plans.filter(p => p.id !== planId);
      await AsyncStorage.setItem(`${WORKOUT_PLANS_KEY}${user.uid}`, JSON.stringify(plans));
      return { success: true };
    }

    return { success: false, error: 'Failed to delete workout plan' };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in deleteWorkoutPlan:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Record a completed workout session
 */
export async function recordWorkoutCompletion(completion: Omit<WorkoutCompletion, 'id' | 'userId' | 'createdAt'>): Promise<{
  success: boolean;
  data?: WorkoutCompletion;
  error?: string;
}> {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Authentication error: Not authenticated' };
    }

    const newCompletion: WorkoutCompletion = {
      ...completion,
      id: Date.now().toString(),
      userId: user.uid,
      createdAt: new Date().toISOString()
    };

    // Try to store in Firebase
    try {
      const completionData = {
        user_id: newCompletion.userId,
        workout_session_id: newCompletion.workoutSessionId,
        workout_plan_id: newCompletion.workoutPlanId,
        completed_exercises: newCompletion.completedExercises,
        start_time: newCompletion.startTime,
        end_time: newCompletion.endTime,
        duration: newCompletion.duration,
        calories_burned: newCompletion.caloriesBurned,
        rating: newCompletion.rating,
        notes: newCompletion.notes,
        created_at: newCompletion.createdAt
      };
      
      const docRef = await addDoc(collection(firestore, 'workout_completions'), completionData);
      newCompletion.id = docRef.id;
      
      return { success: true, data: newCompletion };
    } catch (dbError) {
      console.error('Error storing in database:', dbError);
      // Continue to store in local storage
    }

    // If database storage failed, store in local storage
    const completionsJson = await AsyncStorage.getItem(`${WORKOUT_HISTORY_KEY}${user.uid}`);
    let completions: WorkoutCompletion[] = [];
    
    if (completionsJson) {
      completions = JSON.parse(completionsJson);
    }
    
    completions.push(newCompletion);
    await AsyncStorage.setItem(`${WORKOUT_HISTORY_KEY}${user.uid}`, JSON.stringify(completions));

    return { success: true, data: newCompletion };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in recordWorkoutCompletion:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Fetch workout completion history
 */
export async function fetchWorkoutHistory(options?: {
  startDate?: string;
  endDate?: string;
  planId?: string;
  limit?: number;
}): Promise<{
  success: boolean;
  data?: WorkoutCompletion[];
  error?: string;
}> {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      console.error('Error getting user: No user found');
      return { success: false, error: 'Authentication error', data: [] };
    }

    // Try Firebase first
    try {
      const completionsRef = collection(firestore, 'workout_completions');
      
      // Build the query
      let constraints = [
        where('user_id', '==', user.uid),
        orderBy('start_time', 'desc')
      ];
      
      if (options?.startDate) {
        constraints.push(where('start_time', '>=', options.startDate));
      }
      
      if (options?.endDate) {
        constraints.push(where('start_time', '<=', options.endDate));
      }
      
      if (options?.planId) {
        constraints.push(where('workout_plan_id', '==', options.planId));
      }
      
      const q = query(completionsRef, ...constraints);
      
      const querySnapshot = await getDocs(q);
      
      if (!querySnapshot.empty) {
        let completions = querySnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            id: doc.id,
            userId: data.user_id,
            workoutSessionId: data.workout_session_id,
            workoutPlanId: data.workout_plan_id,
            completedExercises: data.completed_exercises,
            startTime: data.start_time,
            endTime: data.end_time,
            duration: data.duration,
            caloriesBurned: data.calories_burned,
            rating: data.rating,
            notes: data.notes,
            createdAt: data.created_at
          } as WorkoutCompletion;
        });
        
        // Apply limit after query since Firebase may not handle multiple range/inequality filters
        if (options?.limit) {
          completions = completions.slice(0, options.limit);
        }
        
        return { success: true, data: completions };
      }
    } catch (dbError) {
      console.error('Error fetching from database:', dbError);
      // Continue to try local storage
    }

    // Try local storage
    const completionsJson = await AsyncStorage.getItem(`${WORKOUT_HISTORY_KEY}${user.uid}`);
    if (completionsJson) {
      let completions = JSON.parse(completionsJson) as WorkoutCompletion[];
      
      // Apply filters
      if (options?.startDate) {
        completions = completions.filter(c => c.startTime >= options.startDate!);
      }
      
      if (options?.endDate) {
        completions = completions.filter(c => c.startTime <= options.endDate!);
      }
      
      if (options?.planId) {
        completions = completions.filter(c => c.workoutPlanId === options.planId);
      }
      
      // Sort by start time (descending)
      completions.sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime());
      
      if (options?.limit) {
        completions = completions.slice(0, options.limit);
      }
      
      return { success: true, data: completions };
    }

    return { success: true, data: [] };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in fetchWorkoutHistory:', errorMessage);
    return { success: false, error: errorMessage, data: [] };
  }
}

/**
 * Generate a personalized workout plan using user profile and AI
 */
export async function generateWorkoutPlan(options: {
  difficulty: WorkoutDifficulty;
  focus: WorkoutFocus[];
  daysPerWeek: number;
  preferredDuration: number;
  equipment?: string[];
  bodyAreas?: BodyArea[];
  healthConditions?: string[];
  planDurationWeeks?: number;
}): Promise<{
  success: boolean;
  data?: WorkoutPlan;
  error?: string;
}> {
  try {
    const auth = getAuth();
    const user = auth.currentUser;
    
    if (!user) {
      return { success: false, error: 'Authentication error: Not authenticated' };
    }
    
    // Get user profile to personalize the plan
    const userProfile = await getUserProfile();
    if (!userProfile) {
      return { success: false, error: 'User profile not found' };
    }
    
    const planDurationWeeks = options.planDurationWeeks || 4; // Default to 4 weeks
    
    // Calculate start and end dates
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + (planDurationWeeks * 7));
    
    // Calculate rest days (e.g., if 5 days per week, rest days might be 0 and 6, which are Sunday and Saturday)
    const restDays: number[] = [];
    if (options.daysPerWeek < 7) {
      // Prioritize weekend days for rest
      const numRestDays = 7 - options.daysPerWeek;
      const possibleRestDays = [0, 6, 1, 5, 2, 4, 3]; // Sunday, Saturday, Monday, Friday, etc.
      for (let i = 0; i < numRestDays; i++) {
        restDays.push(possibleRestDays[i]);
      }
    }
    
    // Use OpenAI to generate workout sessions if available
    try {
      const generationPrompt = `
        Generate a ${planDurationWeeks}-week workout plan for a ${userProfile.age || 30}-year-old ${userProfile.gender || 'person'} 
        with ${options.difficulty} fitness level, 
        focusing on ${options.focus.join(', ')}.
        The plan should have ${options.daysPerWeek} workouts per week, 
        each session lasting about ${options.preferredDuration} minutes.
        ${options.bodyAreas ? `Targeting body areas: ${options.bodyAreas.join(', ')}.` : ''}
        ${options.equipment ? `Available equipment: ${options.equipment.join(', ')}.` : 'No specialized equipment available.'}
        ${options.healthConditions ? `Health considerations: ${options.healthConditions.join(', ')}.` : 'No specific health conditions to consider.'}
        ${userProfile.weight ? `Current weight: ${userProfile.weight} kg.` : ''}
        ${userProfile.height ? `Height: ${userProfile.height} cm.` : ''}
        
        Format the response as a structured workout plan with unique exercises for each day, 
        including sets, reps, rest periods, and any notes or form tips.
        Make the plan progressive, gradually increasing intensity over the weeks.
        Include warmup and cooldown suggestions for each session.
      `;

      // Instead of calling OpenAI API directly, just log the prompt and use a mock response
      console.log('Would generate workout plan with prompt:', generationPrompt);
      
      // Mock a successful response
      const planResponse = {
        success: true,
        data: "This would be the AI-generated workout plan text. Since we're not actually calling the API, we'll use our fallback plan generation."
      };
      
      // TODO: Parse AI response into structured workout plan
      // For now, create a simple plan as fallback
      console.log('Mock AI Generated Plan:', planResponse.data);
    } catch (aiError) {
      console.error('Error with workout plan generation:', aiError);
      // Continue with fallback plan creation
    }
    
    // Create a sample plan as fallback
    const workoutSessions = generateSampleWorkoutSessions(options);
    
    const newPlan: WorkoutPlan = {
      id: Date.now().toString(),
      userId: user.uid,
      name: `${options.focus[0].charAt(0).toUpperCase() + options.focus[0].slice(1)} ${options.difficulty.charAt(0).toUpperCase() + options.difficulty.slice(1)} Plan`,
      description: `A ${options.daysPerWeek}-day workout plan focused on ${options.focus.join(', ')} training.`,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      difficulty: options.difficulty,
      focus: options.focus,
      sessions: workoutSessions,
      daysPerWeek: options.daysPerWeek,
      restDays,
      tags: options.focus,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Save the plan
    const saveResult = await createWorkoutPlan(newPlan);
    
    return saveResult;
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in generateWorkoutPlan:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Get workout statistics for a user
 */
export async function getWorkoutStatistics(timeframe: 'week' | 'month' | 'year' = 'week'): Promise<{
  success: boolean;
  data?: {
    totalWorkouts: number;
    totalDuration: number;
    totalCaloriesBurned: number;
    sessionsPerFocus: Record<WorkoutFocus, number>;
    completionRate: number;
    averageRating: number;
    mostCommonBodyArea: BodyArea;
    comparisonToPreviousPeriod: {
      workouts: number;
      duration: number;
      caloriesBurned: number;
    };
  },
  error?: string;
}> {
  try {
    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    if (timeframe === 'week') {
      startDate.setDate(endDate.getDate() - 7);
    } else if (timeframe === 'month') {
      startDate.setMonth(endDate.getMonth() - 1);
    } else if (timeframe === 'year') {
      startDate.setFullYear(endDate.getFullYear() - 1);
    }
    
    // Get workout history for the period
    const currentPeriodResult = await fetchWorkoutHistory({
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString()
    });
    
    if (!currentPeriodResult.success || !currentPeriodResult.data) {
      return { success: false, error: currentPeriodResult.error || 'Failed to fetch workout history' };
    }
    
    const currentPeriodWorkouts = currentPeriodResult.data;
    
    // Get workout history for the previous period
    const previousPeriodStart = new Date(startDate);
    const previousPeriodEnd = new Date(startDate);
    
    if (timeframe === 'week') {
      previousPeriodStart.setDate(previousPeriodStart.getDate() - 7);
    } else if (timeframe === 'month') {
      previousPeriodStart.setMonth(previousPeriodStart.getMonth() - 1);
    } else if (timeframe === 'year') {
      previousPeriodStart.setFullYear(previousPeriodStart.getFullYear() - 1);
    }
    
    const previousPeriodResult = await fetchWorkoutHistory({
      startDate: previousPeriodStart.toISOString(),
      endDate: previousPeriodEnd.toISOString()
    });
    
    const previousPeriodWorkouts = previousPeriodResult.success ? previousPeriodResult.data || [] : [];
    
    // Calculate statistics
    const totalWorkouts = currentPeriodWorkouts.length;
    const totalDuration = currentPeriodWorkouts.reduce((sum, workout) => sum + workout.duration, 0);
    const totalCaloriesBurned = currentPeriodWorkouts.reduce((sum, workout) => sum + (workout.caloriesBurned || 0), 0);
    
    // Calculate completion rate (completed exercises / total exercises)
    let totalExercises = 0;
    let completedExercises = 0;
    
    currentPeriodWorkouts.forEach(workout => {
      workout.completedExercises.forEach(exercise => {
        totalExercises++;
        if (exercise.completed) {
          completedExercises++;
        }
      });
    });
    
    const completionRate = totalExercises > 0 ? (completedExercises / totalExercises) * 100 : 0;
    
    // Calculate average rating
    const workoutsWithRatings = currentPeriodWorkouts.filter(workout => workout.rating !== undefined);
    const averageRating = workoutsWithRatings.length > 0 
      ? workoutsWithRatings.reduce((sum, workout) => sum + (workout.rating || 0), 0) / workoutsWithRatings.length 
      : 0;
    
    // Get statistics for previous period
    const previousTotalWorkouts = previousPeriodWorkouts.length;
    const previousTotalDuration = previousPeriodWorkouts.reduce((sum, workout) => sum + workout.duration, 0);
    const previousTotalCaloriesBurned = previousPeriodWorkouts.reduce((sum, workout) => sum + (workout.caloriesBurned || 0), 0);
    
    // Calculate comparison to previous period
    const comparisonToPreviousPeriod = {
      workouts: previousTotalWorkouts > 0 ? ((totalWorkouts - previousTotalWorkouts) / previousTotalWorkouts) * 100 : 0,
      duration: previousTotalDuration > 0 ? ((totalDuration - previousTotalDuration) / previousTotalDuration) * 100 : 0,
      caloriesBurned: previousTotalCaloriesBurned > 0 ? ((totalCaloriesBurned - previousTotalCaloriesBurned) / previousTotalCaloriesBurned) * 100 : 0
    };
    
    // Fetch workout plans to get sessions by focus
    const plansResult = await fetchWorkoutPlans();
    const plans = plansResult.success ? plansResult.data || [] : [];
    
    // Initialize counters for sessions per focus and body area
    const sessionsPerFocus: Record<WorkoutFocus, number> = {
      [WorkoutFocus.STRENGTH]: 0,
      [WorkoutFocus.CARDIO]: 0,
      [WorkoutFocus.FLEXIBILITY]: 0,
      [WorkoutFocus.BALANCE]: 0,
      [WorkoutFocus.WEIGHT_LOSS]: 0,
      [WorkoutFocus.MUSCLE_GAIN]: 0,
      [WorkoutFocus.ENDURANCE]: 0,
      [WorkoutFocus.GENERAL_FITNESS]: 0
    };
    
    const bodyAreaCount: Record<string, number> = {};
    
    // Count completed sessions by type and body area
    for (const workout of currentPeriodWorkouts) {
      const plan = plans.find(p => p.id === workout.workoutPlanId);
      if (plan) {
        const session = plan.sessions.find(s => s.id === workout.workoutSessionId);
        if (session) {
          // Count focus areas
          session.focus.forEach(focus => {
            sessionsPerFocus[focus] = (sessionsPerFocus[focus] || 0) + 1;
          });
          
          // Count body areas
          session.bodyAreas.forEach(area => {
            bodyAreaCount[area] = (bodyAreaCount[area] || 0) + 1;
          });
        }
      }
    }
    
    // Find most common body area
    let mostCommonBodyArea = BodyArea.FULL_BODY;
    let highestCount = 0;
    
    for (const [area, count] of Object.entries(bodyAreaCount)) {
      if (count > highestCount) {
        highestCount = count;
        mostCommonBodyArea = area as BodyArea;
      }
    }
    
    return {
      success: true,
      data: {
        totalWorkouts,
        totalDuration,
        totalCaloriesBurned,
        sessionsPerFocus,
        completionRate,
        averageRating,
        mostCommonBodyArea,
        comparisonToPreviousPeriod
      }
    };
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error('Unexpected error in getWorkoutStatistics:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Helper function to generate sample workout sessions
 * Used as a fallback when AI generation fails
 */
function generateSampleWorkoutSessions(options: {
  difficulty: WorkoutDifficulty;
  focus: WorkoutFocus[];
  daysPerWeek: number;
  preferredDuration: number;
  bodyAreas?: BodyArea[];
}): WorkoutSession[] {
  const sessions: WorkoutSession[] = [];
  const { difficulty, focus, daysPerWeek, preferredDuration, bodyAreas = [BodyArea.FULL_BODY] } = options;
  
  // Basic templates for different focus areas
  const templates: Partial<Record<WorkoutFocus, Exercise[]>> = {
    [WorkoutFocus.STRENGTH]: [
      {
        id: 'squat',
        name: 'Barbell Squat',
        description: 'A compound lower body exercise that targets the quadriceps, hamstrings, and glutes.',
        bodyArea: BodyArea.LOWER_BODY,
        difficulty: WorkoutDifficulty.INTERMEDIATE,
        primaryMuscles: ['quadriceps', 'glutes'],
        secondaryMuscles: ['hamstrings', 'calves', 'lower back']
      },
      {
        id: 'bench-press',
        name: 'Bench Press',
        description: 'A compound upper body exercise that targets the chest, shoulders, and triceps.',
        bodyArea: BodyArea.CHEST,
        difficulty: WorkoutDifficulty.INTERMEDIATE,
        primaryMuscles: ['chest', 'triceps'],
        secondaryMuscles: ['shoulders', 'biceps']
      },
      {
        id: 'deadlift',
        name: 'Deadlift',
        description: 'A compound full body exercise that targets the lower back, hamstrings, and glutes.',
        bodyArea: BodyArea.BACK,
        difficulty: WorkoutDifficulty.ADVANCED,
        primaryMuscles: ['lower back', 'hamstrings', 'glutes'],
        secondaryMuscles: ['forearms', 'upper back', 'trapezius']
      }
    ],
    [WorkoutFocus.CARDIO]: [
      {
        id: 'running',
        name: 'Running',
        description: 'A cardiovascular exercise that targets the legs and core.',
        bodyArea: BodyArea.FULL_BODY,
        difficulty: WorkoutDifficulty.INTERMEDIATE,
        primaryMuscles: ['quadriceps', 'hamstrings', 'calves'],
        secondaryMuscles: ['glutes', 'core']
      },
      {
        id: 'cycling',
        name: 'Cycling',
        description: 'A low-impact cardiovascular exercise that targets the legs.',
        bodyArea: BodyArea.LOWER_BODY,
        difficulty: WorkoutDifficulty.BEGINNER,
        primaryMuscles: ['quadriceps', 'hamstrings', 'calves'],
        secondaryMuscles: ['glutes', 'core']
      },
      {
        id: 'jump-rope',
        name: 'Jump Rope',
        description: 'A high-intensity cardiovascular exercise that targets the legs, shoulders, and core.',
        bodyArea: BodyArea.FULL_BODY,
        difficulty: WorkoutDifficulty.INTERMEDIATE,
        primaryMuscles: ['calves', 'shoulders'],
        secondaryMuscles: ['forearms', 'core', 'quadriceps']
      }
    ],
    [WorkoutFocus.FLEXIBILITY]: [
      {
        id: 'hamstring-stretch',
        name: 'Hamstring Stretch',
        description: 'A stretching exercise that targets the hamstrings.',
        bodyArea: BodyArea.LOWER_BODY,
        difficulty: WorkoutDifficulty.BEGINNER,
        primaryMuscles: ['hamstrings'],
        secondaryMuscles: ['lower back']
      },
      {
        id: 'hip-flexor-stretch',
        name: 'Hip Flexor Stretch',
        description: 'A stretching exercise that targets the hip flexors.',
        bodyArea: BodyArea.LOWER_BODY,
        difficulty: WorkoutDifficulty.BEGINNER,
        primaryMuscles: ['hip flexors'],
        secondaryMuscles: ['quadriceps']
      },
      {
        id: 'shoulder-stretch',
        name: 'Shoulder Stretch',
        description: 'A stretching exercise that targets the shoulders.',
        bodyArea: BodyArea.UPPER_BODY,
        difficulty: WorkoutDifficulty.BEGINNER,
        primaryMuscles: ['shoulders'],
        secondaryMuscles: ['chest', 'upper back']
      }
    ]
  };
  
  // Generate sessions based on focus and days per week
  for (let day = 0; day < daysPerWeek; day++) {
    // Pick a focus for this day - rotate through available focus options
    const dayFocus = focus[day % focus.length];
    
    // Pick a body area for this day - rotate through available body areas
    const dayBodyArea = bodyAreas[day % bodyAreas.length];
    
    // Get exercises for this focus
    const focusExercises = templates[dayFocus] || templates[WorkoutFocus.GENERAL_FITNESS] || [];
    
    // Filter exercises by body area if needed
    const bodyAreaExercises = dayBodyArea !== BodyArea.FULL_BODY
      ? focusExercises.filter(e => e.bodyArea === dayBodyArea || e.bodyArea === BodyArea.FULL_BODY)
      : focusExercises;
    
    // Use all exercises if none match the body area
    const exercises = bodyAreaExercises.length > 0 ? bodyAreaExercises : focusExercises;
    
    // Create workout exercises with appropriate sets/reps/duration based on focus and difficulty
    const workoutExercises: WorkoutExercise[] = exercises.map((exercise, index) => {
      const baseWorkoutExercise: WorkoutExercise = {
        exercise,
        order: index + 1,
      };
      
      // Configure sets/reps/duration based on focus
      if (dayFocus === WorkoutFocus.STRENGTH || dayFocus === WorkoutFocus.MUSCLE_GAIN) {
        return {
          ...baseWorkoutExercise,
          sets: difficulty === WorkoutDifficulty.BEGINNER ? 3 : (difficulty === WorkoutDifficulty.INTERMEDIATE ? 4 : 5),
          reps: difficulty === WorkoutDifficulty.BEGINNER ? 10 : (difficulty === WorkoutDifficulty.INTERMEDIATE ? 8 : 6),
          restBetweenSets: difficulty === WorkoutDifficulty.BEGINNER ? 60 : (difficulty === WorkoutDifficulty.INTERMEDIATE ? 90 : 120),
        };
      } else if (dayFocus === WorkoutFocus.CARDIO || dayFocus === WorkoutFocus.ENDURANCE) {
        return {
          ...baseWorkoutExercise,
          duration: difficulty === WorkoutDifficulty.BEGINNER ? 300 : (difficulty === WorkoutDifficulty.INTERMEDIATE ? 600 : 900),
        };
      } else if (dayFocus === WorkoutFocus.FLEXIBILITY) {
        return {
          ...baseWorkoutExercise,
          duration: 45,
          sets: 3,
        };
      } else {
        // General fitness or other focus
        return {
          ...baseWorkoutExercise,
          sets: 3,
          reps: 12,
          restBetweenSets: 60,
        };
      }
    });
    
    // Create a session
    const session: WorkoutSession = {
      id: `session-${day+1}`,
      name: `Day ${day+1}: ${dayFocus.charAt(0).toUpperCase() + dayFocus.slice(1)} - ${dayBodyArea.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}`,
      description: `A ${difficulty} ${dayFocus} workout focusing on the ${dayBodyArea.replace('_', ' ')} area.`,
      difficulty,
      duration: preferredDuration,
      focus: [dayFocus],
      bodyAreas: [dayBodyArea],
      exercises: workoutExercises,
      warmup: [
        {
          exercise: {
            id: 'warmup-1',
            name: 'Dynamic Stretching',
            description: 'Perform dynamic stretches to prepare your body for the workout.',
            bodyArea: BodyArea.FULL_BODY,
            difficulty: WorkoutDifficulty.BEGINNER,
            primaryMuscles: ['full body'],
            secondaryMuscles: []
          },
          duration: 300,
          order: 1
        }
      ],
      cooldown: [
        {
          exercise: {
            id: 'cooldown-1',
            name: 'Static Stretching',
            description: 'Perform static stretches to cool down and improve flexibility.',
            bodyArea: BodyArea.FULL_BODY,
            difficulty: WorkoutDifficulty.BEGINNER,
            primaryMuscles: ['full body'],
            secondaryMuscles: []
          },
          duration: 300,
          order: 1
        }
      ]
    };
    
    sessions.push(session);
  }
  
  return sessions;
} 