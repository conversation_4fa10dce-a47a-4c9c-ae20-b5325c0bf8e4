/**
 * Centralized type definitions for API responses
 * This file contains standardized interfaces for all API responses used in the app
 */

/**
 * Base API response interface that all responses should extend
 */
export interface BaseApiResponse {
  success: boolean;
  error?: string;
  timestamp?: number;
}

/**
 * Standard error response object
 */
export interface ErrorResponse extends BaseApiResponse {
  success: false;
  error: string;
  code?: string;
  details?: Record<string, unknown>;
}

/**
 * Base nutrition information interface used across the app
 */
export interface NutritionInfo {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber?: number;
  sugar?: number;
  sodium?: number;
}

/**
 * Food item interface for consistent typing across components
 */
export interface FoodItem {
  id?: string;
  name: string;
  calories: number;
  nutrition: NutritionInfo;
  estimatedAmount?: string;
  allergens?: string[];
}

/**
 * Serving information interface for meal/food items
 */
export interface ServingInfo {
  servingSize: string;
  totalServings: number;
  caloriesPerServing: number;
  proteinPerServing: number;
  carbsPerServing: number;
  fatPerServing: number;
  fiberPerServing?: number;
  sugarPerServing?: number;
  sodiumPerServing?: number;
}

/**
 * Barcode information interface for scanning results
 */
export interface BarcodeInfo {
  value: string;
  format: string;
  productName?: string;
  brand?: string;
  nutritionalInfo?: NutritionInfo;
  ingredients?: string[];
  allergens?: string[];
}

/**
 * Vision API response standardized interface
 */
export interface VisionApiResponse extends BaseApiResponse {
  success: true;
  data: {
    foodItems: FoodItem[];
    nutritionalSummary: {
      totalCalories: number;
      macronutrients: NutritionInfo;
      mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack' | 'unknown';
      isBalancedMeal: boolean;
    };
    barcodeInfo?: BarcodeInfo;
  };
  processingTime?: number;
  cacheHit?: boolean;
}

/**
 * OpenAI API response for food analysis
 */
export interface OpenAIFoodAnalysisResponse extends BaseApiResponse {
  success: true;
  data: {
    description: string;
    foodItems: FoodItem[];
    nutritionalSummary: {
      totalCalories: number;
      protein: number;
      carbs: number;
      fat: number;
      fiber?: number;
    };
    healthHighlights?: {
      positives: string[];
      considerations: string[];
    };
    cuisineType?: string;
    preparationMethod?: string;
  };
  processingTime?: number;
}

/**
 * Standardized response for nutrition database queries
 */
export interface NutritionDatabaseResponse extends BaseApiResponse {
  success: true;
  data: {
    items: FoodItem[];
    pagination?: {
      page: number;
      perPage: number;
      totalItems: number;
      totalPages: number;
    };
  };
} 