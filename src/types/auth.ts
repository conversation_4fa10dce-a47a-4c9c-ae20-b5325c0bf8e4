/**
 * Authentication Type Definitions
 * Comprehensive TypeScript types for authentication system
 */

// Core User interface
export interface User {
  id: string;
  email: string;
  name?: string;
  photoURL?: string;
  provider: AuthProvider;
  verified: boolean;
  createdAt?: Date;
  lastLoginAt?: Date;
}

// Authentication providers
export type AuthProvider = 'email' | 'google.com' | 'apple.com';

// Comprehensive error handling
export interface AuthError {
  code: AuthErrorCode;
  message: string;
  details?: any;
  timestamp: Date;
  context?: AuthErrorContext;
}

export type AuthErrorCode = 
  // Network errors
  | 'network/offline'
  | 'network/timeout'
  | 'network/unavailable'
  
  // Firebase Auth errors
  | 'auth/user-not-found'
  | 'auth/wrong-password'
  | 'auth/email-already-in-use'
  | 'auth/weak-password'
  | 'auth/invalid-email'
  | 'auth/user-disabled'
  | 'auth/too-many-requests'
  | 'auth/popup-closed-by-user'
  | 'auth/popup-blocked'
  | 'auth/cancelled-popup-request'
  
  // OAuth specific errors
  | 'oauth/google-cancelled'
  | 'oauth/apple-cancelled'
  | 'oauth/configuration-error'
  | 'oauth/token-exchange-failed'
  
  // Platform errors
  | 'platform/not-supported'
  | 'platform/permission-denied'
  | 'platform/service-unavailable'
  
  // Application errors
  | 'app/initialization-failed'
  | 'app/profile-creation-failed'
  | 'app/session-expired'
  | 'app/unknown-error';

export interface AuthErrorContext {
  operation: AuthOperation;
  platform: 'web' | 'ios' | 'android';
  provider?: AuthProvider;
  userId?: string;
  timestamp: Date;
}

export type AuthOperation = 
  | 'sign-in'
  | 'sign-up'
  | 'sign-out'
  | 'refresh-session'
  | 'google-oauth'
  | 'apple-oauth'
  | 'profile-creation'
  | 'profile-update';

// Enhanced auth result with better error information
export interface AuthResult {
  success: boolean;
  user?: User | null;
  error?: AuthError | null;
  metadata?: AuthResultMetadata;
}

export interface AuthResultMetadata {
  duration: number; // milliseconds
  retryCount?: number;
  fallbackUsed?: boolean;
  provider: AuthProvider;
  operation: AuthOperation;
}

// Auth state interface
export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: AuthError | null;
  lastOperation: AuthOperation | null;
  sessionInfo: SessionInfo | null;
}

export interface SessionInfo {
  sessionId: string;
  expiresAt: Date;
  refreshedAt: Date;
  platform: 'web' | 'ios' | 'android';
  provider: AuthProvider;
}

// OAuth configuration interfaces
export interface GoogleOAuthConfig {
  webClientId: string;
  iosClientId?: string;
  androidClientId?: string;
  scopes: string[];
}

export interface AppleOAuthConfig {
  clientId: string;
  redirectUri: string;
  scopes: AppleAuthenticationScope[];
}

export type AppleAuthenticationScope = 'email' | 'name';

// Test account configuration
export interface TestAccountConfig {
  email: string;
  password: string;
  displayName: string;
  photoURL: string;
  provider: AuthProvider;
}

// Platform-specific auth capabilities
export interface PlatformCapabilities {
  supportsGoogleOAuth: boolean;
  supportsAppleOAuth: boolean;
  supportsWebOAuth: boolean;
  supportsBiometric: boolean;
  supportsKeychain: boolean;
}

// Auth service interfaces
export interface AuthServiceInterface {
  signInWithEmail(email: string, password: string): Promise<AuthResult>;
  signUp(email: string, password: string, name: string): Promise<AuthResult>;
  signInWithGoogle(): Promise<AuthResult>;
  signInWithApple(): Promise<AuthResult>;
  signOut(): Promise<AuthResult>;
  refreshSession(): Promise<AuthResult>;
  getCurrentUser(): User | null;
  getCapabilities(): PlatformCapabilities;
}

export interface AuthStateManagerInterface {
  getState(): AuthState;
  setState(state: Partial<AuthState>): void;
  subscribe(listener: (state: AuthState) => void): () => void;
  clearState(): void;
}

export interface PlatformAuthHandlerInterface {
  signInWithGoogle(): Promise<AuthResult>;
  signInWithApple(): Promise<AuthResult>;
  getCapabilities(): PlatformCapabilities;
  cleanup(): Promise<void>;
}

// Firebase user profile interface
export interface UserProfile {
  id: string;
  email: string;
  full_name: string;
  created_at: Date;
  updated_at: Date;
  last_login?: Date;
  auth_provider: AuthProvider;
  daily_calorie_goal?: number;
  daily_water_goal?: number;
  daily_protein_goal?: number;
  daily_carbs_goal?: number;
  daily_fat_goal?: number;
  height?: string;
  weight?: number;
  activity_level?: 'sedentary' | 'light' | 'moderate' | 'active' | 'very_active';
  profile_image_url?: string;
}

// Auth context interface
export interface AuthContextValue {
  // State
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: AuthError | null;
  
  // Core auth methods
  signIn: (email: string, password: string) => Promise<AuthResult>;
  signUp: (email: string, password: string, name: string) => Promise<AuthResult>;
  signInWithGoogle: () => Promise<AuthResult>;
  signInWithApple: () => Promise<AuthResult>;
  signOut: () => Promise<AuthResult>;
  
  // Utility methods
  refreshSession: () => Promise<void>;
  getCurrentUser: () => User | null;
  clearError: () => void;
  
  // Platform info
  capabilities: PlatformCapabilities;
}

// Event interfaces for auth state changes
export interface AuthStateChangeEvent {
  type: 'user-signed-in' | 'user-signed-out' | 'session-refreshed' | 'error-occurred';
  user?: User | null;
  error?: AuthError | null;
  timestamp: Date;
  operation?: AuthOperation;
}

// Utility type for creating auth errors
export type CreateAuthErrorOptions = {
  code: AuthErrorCode;
  message: string;
  details?: any;
  context?: Partial<AuthErrorContext>;
  originalError?: Error;
};

// Helper function interfaces
export interface AuthErrorHelper {
  createError(options: CreateAuthErrorOptions): AuthError;
  isNetworkError(error: AuthError): boolean;
  isUserError(error: AuthError): boolean;
  isRecoverableError(error: AuthError): boolean;
  formatErrorMessage(error: AuthError): string;
}

export interface AuthValidationHelper {
  validateEmail(email: string): { valid: boolean; error?: string };
  validatePassword(password: string): { valid: boolean; error?: string };
  validateName(name: string): { valid: boolean; error?: string };
}

// Configuration validation
export interface AuthConfigValidation {
  valid: boolean;
  errors: string[];
  warnings: string[];
  missingEnvVars: string[];
}