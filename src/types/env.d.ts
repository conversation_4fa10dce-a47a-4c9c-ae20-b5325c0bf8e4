declare global {
  namespace NodeJS {
    interface ProcessEnv {
      // Firebase Configuration
      EXPO_PUBLIC_FIREBASE_API_KEY: string;
      EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN: string;
      EXPO_PUBLIC_FIREBASE_PROJECT_ID: string;
      EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET: string;
      EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: string;
      EXPO_PUBLIC_FIREBASE_APP_ID: string;
      EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID: string;
      
      // OpenAI API Configuration
      EXPO_PUBLIC_OPENAI_API_KEY: string;
      EXPO_PUBLIC_OPENAI_MODEL: string;
      
      // Google Vision API Configuration
      EXPO_PUBLIC_GOOGLE_VISION_API_KEY: string;
      
      // App Configuration
      EXPO_PUBLIC_API_BASE_URL: string;
    }
  }
}

declare module '@env' {
  // Firebase Configuration
  string;
  string;
  string;
  string;
  string;
  string;
  string;

  // OpenAI Configuration
  string;
  string;

  // Google Vision API Configuration
  string;

  // Nutritionix API Configuration
  string;
  string;

  // USDA API Configuration
  string;
}

// Ensure this file is treated as a module
export {};