declare module 'expo-background-fetch' {
  export enum BackgroundFetchStatus {
    Restricted = 0,
    Denied = 1,
    Available = 2,
  }

  export enum BackgroundFetchResult {
    NoData = 0,
    NewData = 1,
    Failed = 2,
  }

  export function getStatusAsync(): Promise<BackgroundFetchStatus>;
  export function registerTaskAsync(taskName: string, options?: {
    minimumInterval?: number;
    stopOnTerminate?: boolean;
    startOnBoot?: boolean;
  }): Promise<void>;
  export function unregisterTaskAsync(taskName: string): Promise<void>;
  export function setMinimumIntervalAsync(minimumInterval: number): Promise<void>;
} 