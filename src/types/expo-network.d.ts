declare module 'expo-network' {
  export enum NetworkStateType {
    UNKNOWN = 0,
    NONE = 1,
    CELLULAR = 2,
    WIFI = 3,
    BLUETOOTH = 4,
    ETHERNET = 5,
    WIMAX = 6,
    VPN = 7,
    OTHER = 8,
  }
  
  export interface NetworkState {
    type: NetworkStateType;
    isConnected: boolean;
    isInternetReachable: boolean;
  }
  
  export function getNetworkStateAsync(): Promise<NetworkState>;
  export function getIpAddressAsync(): Promise<string>;
} 