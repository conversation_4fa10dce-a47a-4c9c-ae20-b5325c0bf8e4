declare module 'expo-notifications' {
  export interface NotificationRequest {
    identifier: string;
    content: {
      title?: string;
      body?: string;
      data?: Record<string, any>;
      badge?: number;
      sound?: boolean | string;
    };
    trigger: {
      seconds?: number;
      repeats?: boolean;
      dateComponents?: any;
      channelId?: string;
    } | null;
  }

  export interface Notification {
    date: number;
    request: NotificationRequest;
  }

  export type NotificationBehavior = {
    shouldShowAlert: boolean;
    shouldPlaySound: boolean;
    shouldSetBadge: boolean;
    priority?: string;
  };

  export type NotificationPermissionsStatus = {
    status: 'granted' | 'undetermined' | 'denied';
    granted: boolean;
    ios?: {
      status: 'authorized' | 'denied' | 'ephemeral' | 'notDetermined' | 'provisional';
    };
    android?: {
      status: 'granted' | 'denied';
    };
  };

  export async function getPermissionsAsync(): Promise<NotificationPermissionsStatus>;
  export async function requestPermissionsAsync(): Promise<NotificationPermissionsStatus>;
  export async function scheduleNotificationAsync(request: NotificationRequest): Promise<string>;
  export async function cancelScheduledNotificationAsync(identifier: string): Promise<void>;
  export async function cancelAllScheduledNotificationsAsync(): Promise<void>;
  export async function getAllScheduledNotificationsAsync(): Promise<Notification[]>;
  export async function setBadgeCountAsync(count: number): Promise<void>;
  export async function getBadgeCountAsync(): Promise<number>;

  export function setNotificationHandler(handler: {
    handleNotification: (notification: Notification) => Promise<NotificationBehavior>;
  }): void;

  export function addNotificationReceivedListener(
    listener: (notification: Notification) => void
  ): { remove: () => void };

  export function addNotificationResponseReceivedListener(
    listener: (response: { notification: Notification; actionIdentifier: string }) => void
  ): { remove: () => void };

  export default {
    getPermissionsAsync,
    requestPermissionsAsync,
    scheduleNotificationAsync,
    cancelScheduledNotificationAsync,
    cancelAllScheduledNotificationsAsync,
    getAllScheduledNotificationsAsync,
    setBadgeCountAsync,
    getBadgeCountAsync,
    setNotificationHandler,
    addNotificationReceivedListener,
    addNotificationResponseReceivedListener
  };
} 