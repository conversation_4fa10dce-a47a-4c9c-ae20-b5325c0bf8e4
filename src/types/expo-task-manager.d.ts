declare module 'expo-task-manager' {
  export interface TaskManagerTask {
    taskName: string;
    taskType: 'background' | 'location';
    options: Record<string, any>;
  }

  export function defineTask(
    taskName: string,
    taskExecutor: (data: { data: Record<string, any>, error: Error | null }) => any
  ): void;

  export function isTaskRegisteredAsync(taskName: string): Promise<boolean>;
  export function getRegisteredTasksAsync(): Promise<TaskManagerTask[]>;
  export function unregisterTaskAsync(taskName: string): Promise<void>;
  export function unregisterAllTasksAsync(): Promise<void>;
} 