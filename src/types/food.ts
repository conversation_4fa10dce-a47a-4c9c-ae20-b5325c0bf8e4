/**
 * Food portion definition
 */
export interface FoodPortion {
  name: string;
  grams: number;
  quantity: number;
}

/**
 * Food item definition
 */
export interface FoodItem {
  id: string;
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  confidence?: number;
  portions?: {
    name: string;
    grams: number;
    quantity: number;
  }[];
  imageUrl?: string;
  source?: string;
  barcode?: string;
  brand?: string;
  category?: string;
  tags?: string[];
}

/**
 * Nutritional information
 */
export interface NutritionData {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber?: number;
  sugar?: number;
  sodium?: number;
  portions: FoodPortion[];
}

/**
 * Meal definition
 */
export interface Meal {
  id: string;
  name: string;
  date: string;
  time: string;
  items: FoodItem[];
  totalCalories: number;
  totalProtein: number;
  totalCarbs: number;
  totalFat: number;
  imageUrl?: string;
}

/**
 * Recipe definition
 */
export interface Recipe {
  id: string;
  name: string;
  description?: string;
  ingredients: FoodItem[];
  instructions: string[];
  prepTime?: number;
  cookTime?: number;
  servings: number;
  totalCalories: number;
  totalProtein: number;
  totalCarbs: number;
  totalFat: number;
  imageUrl?: string;
  tags?: string[];
} 