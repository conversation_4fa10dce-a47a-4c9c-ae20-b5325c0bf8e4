// Global TypeScript declarations for various environments

// Declare window for web environment
interface Window {
  open: (url?: string, target?: string, features?: string) => Window | null;
}

declare global {
  interface Window {
    open: (url?: string, target?: string, features?: string) => Window | null;
  }
}

// For react-native-qrcode-svg in non-web environments
declare module 'react-native-qrcode-svg' {
  import { Component } from 'react';
  
  interface QRCodeProps {
    value: string;
    size?: number;
    color?: string;
    backgroundColor?: string;
    logo?: any;
    logoSize?: number;
    logoBackgroundColor?: string;
    logoMargin?: number;
    logoBorderRadius?: number;
    quietZone?: number;
    enableLinearGradient?: boolean;
    gradientDirection?: string;
    linearGradient?: string[];
    ecl?: 'L' | 'M' | 'Q' | 'H';
    getRef?: (c: any) => void;
    onError?: (error: any) => void;
  }
  
  export default class QRCode extends Component<QRCodeProps> {}
}

// For expo-print in Expo environments
declare module 'expo-print' {
  export interface PrintOptions {
    html: string;
    width?: number;
    height?: number;
    base64?: boolean;
  }
  
  export function printToFileAsync(options: PrintOptions): Promise<{ uri: string }>;
}

export {}; 