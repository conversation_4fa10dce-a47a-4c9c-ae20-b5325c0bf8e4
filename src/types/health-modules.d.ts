declare module 'react-native-health' {
  export const Constants: {
    Permissions: {
      Steps: string;
      DistanceWalkingRunning: string;
      ActiveEnergyBurned: string;
      HeartRate: string;
      SleepAnalysis: string;
      Weight: string;
      Height: string;
      BloodPressureDiastolic: string;
    };
  };
  
  export function initHealthKit(permissions: any, callback: (error: Error | null) => void): void;
  export function getAuthStatus(permissions: any, callback: (error: Error | null, results: any) => void): void;
  export function getStepCount(options: any, callback: (error: Error | null, results: any) => void): void;
  export function getActiveEnergyBurned(options: any, callback: (error: Error | null, results: any[]) => void): void;
  export function getSleepSamples(options: any, callback: (error: Error | null, results: any[]) => void): void;
  export function getHeartRateSamples(options: any, callback: (error: Error | null, results: any[]) => void): void;
}

declare module 'react-native-google-fit' {
  export const Scopes: Record<string, string>;
  export function authorize(options: any): Promise<any>;
  export function disconnect(): Promise<void>;
  export function getDailyStepCountSamples(options: any): Promise<any[]>;
  export function getDailyCalorieSamples(options: any): Promise<any[]>;
  export function getHeartRateSamples(options: any): Promise<any[]>;
  export function getWeightSamples(options: any): Promise<any[]>;
  export function getSleepSamples(options: any): Promise<any[]>;
} 