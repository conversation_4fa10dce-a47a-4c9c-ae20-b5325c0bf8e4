// Custom Jest type declarations
declare global {
  // Add Jest to the global scope
  const jest: {
    mock: (moduleName: string, factory?: () => any) => any;
    fn: <T extends (...args: any[]) => any>(implementation?: T) => jest.Mock<ReturnType<T>, Parameters<T>>;
    clearAllMocks: () => void;
  };

  // Jest mock type
  namespace jest {
    interface Mock<T = any, Y extends any[] = any[]> {
      (...args: Y): T;
      mockImplementation: (fn: (...args: Y) => T) => Mock<T, Y>;
      mockReturnValue: (value: T) => Mock<T, Y>;
      mockResolvedValue: (value: T) => Mock<T, Y>;
      mockRejectedValue: (error: any) => Mock<T, Y>;
    }
  }

  // Test functions
  function describe(name: string, fn: () => void): void;
  function beforeEach(fn: () => void): void;
  function it(name: string, fn: (...args: any[]) => any): void;
  function expect(value: any): {
    toEqual: (expected: any) => void;
    toHaveBeenCalledWith: (...args: any[]) => void;
    objectContaining: (obj: object) => any;
    any: (type: any) => any;
  };
} 