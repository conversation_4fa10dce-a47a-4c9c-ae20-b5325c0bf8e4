declare module 'react-i18next' {
  // Basic i18n functionality
  export function useTranslation(namespace?: string | string[]): {
    t: (key: string, options?: any) => string;
    i18n: any;
    ready: boolean;
  };

  export function withTranslation(namespace?: string | string[]): any;
  
  export function Trans(props: {
    i18nKey?: string;
    t?: (key: string, options?: any) => string;
    [key: string]: any;
  }): JSX.Element;

  export interface UseTranslationOptions {
    i18n?: any;
    useSuspense?: boolean;
  }

  export interface TranslationProps {
    i18n?: any;
    t?: (key: string, options?: any) => string;
    [key: string]: any;
  }
} 