declare module '@ovalmoney/react-native-fitness' {
  export const Scopes: {
    FITNESS_ACTIVITY_READ: string;
    FITNESS_ACTIVITY_WRITE: string;
    FITNESS_BODY_READ: string;
    FITNESS_BODY_WRITE: string;
    FITNESS_BLOOD_PRESSURE_READ: string;
    FITNESS_BLOOD_PRESSURE_WRITE: string;
    FITNESS_BLOOD_GLUCOSE_READ: string;
    FITNESS_BLOOD_GLUCOSE_WRITE: string;
    FITNESS_NUTRITION_WRITE: string;
    FITNESS_SLEEP_READ: string;
  };

  export const DataType: {
    CALORIES: string;
  };

  export function authorize(options: { scopes: string[] }): Promise<{ success: boolean; message?: string }>;
  export function checkIsAuthorized(): Promise<{ isAuthorized: boolean }>;
  export function getDailySteps(options: { startDate: string; endDate: string }): Promise<{ steps: number }[]>;
  export function getDailyCalorieSamples(options: { startDate: string; endDate: string; dataType: string }): Promise<{ calorie: number }[]>;
  export function getDailyDistanceSamples(options: { startDate: string; endDate: string }): Promise<{ distance: number }[]>;
  export function getDailyActivitySamples(options: { startDate: string; endDate: string; basedOnActivity: boolean }): Promise<{ intensity: number; duration: number }[]>;
  export function getHeartRateSamples(options: { startDate: string; endDate: string }): Promise<{ value: number; startDate: string }[]>;
  export function getSleepSamples(options: { startDate: string; endDate: string }): Promise<{ sleepStage: number; startDate: string; endDate: string }[]>;

  export default {
    Scopes,
    DataType,
    authorize,
    checkIsAuthorized,
    getDailySteps,
    getDailyCalorieSamples,
    getDailyDistanceSamples,
    getDailyActivitySamples,
    getHeartRateSamples,
    getSleepSamples
  };
} 