declare module 'react-native-health' {
  export interface HealthValue {
    value: number;
    startDate: string;
    endDate?: string;
    sourceType: string;
    sourceName: string;
  }

  export interface HealthKitPermissions {
    permissions: {
      read: string[];
      write: string[];
    };
  }

  export interface HealthInputOptions {
    value: number;
    unit: string;
    date: string;
  }

  export const Constants: {
    Permissions: {
      Steps: string;
      DistanceWalkingRunning: string;
      ActiveEnergyBurned: string;
      HeartRate: string;
      SleepAnalysis: string;
      Weight: string;
      Height: string;
      BloodPressureDiastolic: string;
    };
  };

  export function initHealthKit(permissions: any, callback: (error: Error | null) => void): void;
  export function getAuthStatus(permissions: any, callback: (error: Error | null, results: any) => void): void;
  export function getStepCount(options: any, callback: (error: Error | null, results: any) => void): void;
  export function getActiveEnergyBurned(options: any, callback: (error: Error | null, results: any[]) => void): void;

  export function getWeightSamples(
    options: { startDate: string; endDate: string },
    callback: (error: string, results: HealthValue[]) => void
  ): void;

  export function saveWeight(
    options: HealthInputOptions,
    callback: (error: string) => void
  ): void;

  export function saveWater(
    options: HealthInputOptions,
    callback: (error: string) => void
  ): void;

  export function setObserver(options: { type: string }): void;

  export function observerQuery(
    options: { type: string },
    callback: (error: any, results: any) => void
  ): void;

  export function stopObserverQuery(options: { type: string }): void;
  
  export default {
    Constants,
    initHealthKit,
    getStepCount,
    getWeightSamples,
    saveWeight,
    saveWater,
    setObserver,
    observerQuery,
    stopObserverQuery
  };
} 