/**
 * Scan-related type definitions
 */

// Type for camera direction
export type CameraDirection = 'front' | 'back';

// Type for scan mode
export type ScanMode = 'food' | 'barcode';

// Interface for recent scan item
export interface RecentScan {
  id: string;
  imageUri: string;
  foodName: string;
  timestamp: number;
  calories: number;
}

// Interface for barcode scan result
export interface BarcodeScanResult {
  type: string;
  data: string;
}

// Interface for detected food item
export interface DetectedFoodItem {
  name: string;
  confidence: number;
}

// Interface for product info from barcode scan
export interface ProductInfo {
  product_name: string;
  image_url: string;
  nutriments: {
    energy_value?: number;
    proteins?: number;
    carbohydrates?: number;
    fat?: number;
    [key: string]: any;
  };
  [key: string]: any;
}

// Interface for generated recipe
export interface GeneratedRecipe {
  name: string;
  focus: string;
  description: string;
  ingredients: {
    name: string;
    amount: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  }[];
  instructions: string[];
  nutritionalInfo: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  preparationTime: string;
  cookingTime: string;
  healthBenefits: string[];
  imagePrompt?: string;
}

// Interface for food analysis result data
export interface FoodAnalysisData {
  dishName: string;
  description?: string;
  confidence?: number;
  servingInfo?: {
    servingSize?: string;
    totalServings?: number;
    caloriesPerServing?: number;
    proteinPerServing?: number;
    carbsPerServing?: number;
    fatPerServing?: number;
    fiberPerServing?: number;
    sugarPerServing?: number;
    sodiumPerServing?: number;
  };
  healthHighlights?: {
    positives?: string[];
    considerations?: string[];
  };
  ingredients?: {
    name: string;
    estimatedAmount?: string;
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
  }[];
  preparationMethod?: string;
  cuisineType?: string;
  mealType?: string;
  isPartial?: boolean;
  partialAnalysisMessage?: string;
  isMultiItem?: boolean; // Indicates if this is a multi-item food analysis
}

// Interface for food analysis response
export interface FoodAnalysisResponse {
  success: boolean;
  data?: FoodAnalysisData;
  error?: string;
}

// Interface for multi-item analysis result
export interface MultiItemAnalysisResult {
  items: DetectedFoodItem[];
  selectedItems: string[];
}

// Interface for nutritional info
export interface NutritionalInfo {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
}

// Interface for bounding box in normalized coordinates (0-1)
export interface BoundingBox {
  x: number;      // left coordinate (0-1)
  y: number;      // top coordinate (0-1)
  width: number;  // width (0-1)
  height: number; // height (0-1)
} 