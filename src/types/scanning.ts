/**
 * Type definitions for food scanning and computer vision functionality
 */

/**
 * Normalized bounding box coordinates (0-1 range)
 * These are used throughout the app for object detection results
 */
export interface BoundingBox {
  x: number;      // Left edge (0-1)
  y: number;      // Top edge (0-1)
  width: number;  // Width (0-1)
  height: number; // Height (0-1)
  confidence?: number; // Optional confidence score (0-1)
}

/**
 * Standard confidence levels used throughout the app
 */
export type ConfidenceLevel = 'high' | 'medium' | 'low' | 'unknown';

/**
 * Methods used for volume estimation
 */
export type VolumeEstimationMethod = 'lidar' | '2d' | 'ai' | 'user' | 'reference';

/**
 * Processing stages for food scanning
 */
export type ProcessingStage = 
  | 'idle'
  | 'scanning'
  | 'analyzing'
  | 'classifying'
  | 'estimating'
  | 'calibrating'
  | 'complete';

/**
 * Quality levels for scan data
 */
export type ScanQualityLevel = 'high' | 'medium' | 'low';

/**
 * Stability levels for device during scanning
 */
export type StabilityLevel = 'stable' | 'moderate' | 'unstable';

/**
 * Reference object types for size calibration
 */
export enum ReferenceObjectType {
  CREDIT_CARD = 'creditCard',
  COIN = 'coin',
  PLATE = 'plate',
  BOWL = 'bowl',
  BOTTLE_CAP = 'bottleCap',
  STANDARD_PAPER = 'standardPaper',
  BUSINESS_CARD = 'businessCard',
  SMARTPHONE = 'smartphone',
  UNKNOWN = 'unknown'
}

/**
 * Detected reference object information
 */
export interface DetectedReferenceObject {
  type: ReferenceObjectType;
  subtype?: string;
  boundingBox: BoundingBox;
  pixelsPerMm: number;
  confidence: number;
}

/**
 * Food detection result
 */
export interface FoodDetectionResult {
  name: string;
  confidence: number;
  boundingBox: BoundingBox;
  segmentationMask?: Uint8Array;
  volumeCm3?: number;
  weightGrams?: number;
}

/**
 * Complete scan result
 */
export interface ScanResult {
  imageUri: string;
  foods: FoodDetectionResult[];
  referenceObjects?: DetectedReferenceObject[];
  totalVolumeCm3?: number;
  nutritionAnalysis?: {
    calories: number;
    analysis: string;
    macronutrients?: {
      protein: number;
      carbs: number;
      fat: number;
    };
  };
  scanQuality: ScanQualityLevel;
  scanMethod: 'lidar' | '2d' | 'hybrid';
  timestamp: number;
}

/**
 * Object detection result from image analysis
 */
export interface DetectionResult {
  boundingBox: BoundingBox;
  label: string;
  confidence: number;
} 