import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { getAuth, onAuthStateChanged } from 'firebase/auth';
import { app, auth, db, storage, functions, isInitialized } from '@/firebase.config';

export function FirebaseTest() {
  const [status, setStatus] = useState({
    app: !!app,
    auth: !!auth,
    db: !!db,
    storage: !!storage,
    functions: !!functions,
    authListener: false,
    authError: null as Error | null
  });

  useEffect(() => {
    // Test auth state listener
    try {
      const unsubscribe = onAuthStateChanged(
        auth!,
        (user) => {
          console.log('Auth state changed:', user ? `User: ${user.uid}` : 'No user');
          setStatus(prev => ({ ...prev, authListener: true }));
        },
        (error) => {
          console.error('Auth state listener error:', error);
          setStatus(prev => ({ ...prev, authError: error }));
        }
      );
      
      return () => unsubscribe();
    } catch (error) {
      console.error('Error setting up auth listener:', error);
      setStatus(prev => ({ ...prev, authError: error as Error }));
    }
  }, []);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Firebase Status</Text>
      
      <View style={styles.statusItem}>
        <Text>App: </Text>
        <Text style={status.app ? styles.success : styles.error}>
          {status.app ? 'Initialized ✅' : 'Failed ❌'}
        </Text>
      </View>
      
      <View style={styles.statusItem}>
        <Text>Auth: </Text>
        <Text style={status.auth ? styles.success : styles.error}>
          {status.auth ? 'Initialized ✅' : 'Failed ❌'}
        </Text>
      </View>
      
      <View style={styles.statusItem}>
        <Text>Auth Listener: </Text>
        <Text style={status.authListener ? styles.success : styles.error}>
          {status.authListener ? 'Working ✅' : 'Not Working ❌'}
        </Text>
      </View>
      
      <View style={styles.statusItem}>
        <Text>Firestore: </Text>
        <Text style={status.db ? styles.success : styles.error}>
          {status.db ? 'Initialized ✅' : 'Failed ❌'}
        </Text>
      </View>
      
      <View style={styles.statusItem}>
        <Text>Storage: </Text>
        <Text style={status.storage ? styles.success : styles.error}>
          {status.storage ? 'Initialized ✅' : 'Failed ❌'}
        </Text>
      </View>
      
      <View style={styles.statusItem}>
        <Text>Functions: </Text>
        <Text style={status.functions ? styles.success : styles.error}>
          {status.functions ? 'Initialized ✅' : 'Failed ❌'}
        </Text>
      </View>
      
      {status.authError && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Auth Error:</Text>
          <Text style={styles.errorText}>{status.authError.message}</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f9f9f9',
    borderRadius: 10,
    margin: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  statusItem: {
    flexDirection: 'row',
    marginVertical: 5,
  },
  success: {
    color: 'green',
    fontWeight: 'bold',
  },
  error: {
    color: 'red',
    fontWeight: 'bold',
  },
  errorContainer: {
    marginTop: 10,
    padding: 10,
    backgroundColor: '#ffebee',
    borderRadius: 5,
  },
  errorTitle: {
    color: 'red',
    fontWeight: 'bold',
  },
  errorText: {
    color: 'red',
  },
}); 