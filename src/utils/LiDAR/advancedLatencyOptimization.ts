/**
 * Advanced Latency Optimization Utilities
 * 
 * Extends latencyOptimization.ts with more sophisticated techniques
 * for reducing AR/LiDAR feedback loop latency and improving
 * real-time performance.
 */

import { Platform, InteractionManager } from 'react-native';
import { getDeviceCapabilities } from '@/services/vision/performanceOptimization';
import {
  getOptimizedFeedbackSettings,
  predictMotion,
  reuseCalculation,
  shouldSkipFrame,
  FeedbackLoopSettings
} from './latencyOptimization';

/**
 * Enhanced feedback loop settings with additional optimizations
 */
export interface EnhancedFeedbackLoopSettings extends FeedbackLoopSettings {
  // Use double buffering for frame processing
  useDoubleBuffering: boolean;
  
  // Pre-warm shader cache for AR rendering
  preWarmShaders: boolean;
  
  // Process frames in background using Web Workers when available
  useWebWorkers: boolean;
  
  // Use temporal smoothing to reduce jitter
  useTemporalSmoothing: boolean;
  
  // Reduced resolution factor during rapid motion (0.0-1.0)
  motionScalingFactor: number;
  
  // Frame budget target time in ms (16ms = 60fps)
  frameBudgetMs: number;
}

/**
 * Get enhanced feedback loop settings with more aggressive
 * optimizations for low-latency AR experiences
 */
export async function getEnhancedFeedbackSettings(): Promise<EnhancedFeedbackLoopSettings> {
  const capabilities = await getDeviceCapabilities();
  const baseSettings = await getOptimizedFeedbackSettings();
  
  // Start with base settings from standard optimization
  const enhancedSettings: EnhancedFeedbackLoopSettings = {
    ...baseSettings,
    useDoubleBuffering: true,
    preWarmShaders: true,
    useWebWorkers: Platform.OS === 'web',
    useTemporalSmoothing: true,
    motionScalingFactor: 0.5,
    frameBudgetMs: 16 // Target 60fps
  };
  
  // Further customize based on device performance level
  switch (capabilities.performanceLevel) {
    case 'high':
      enhancedSettings.frameBudgetMs = 16; // 60fps
      enhancedSettings.motionScalingFactor = 0.7; // Less aggressive downscaling
      enhancedSettings.useTemporalSmoothing = true;
      break;
      
    case 'medium':
      enhancedSettings.frameBudgetMs = 20; // 50fps
      enhancedSettings.motionScalingFactor = 0.5;
      enhancedSettings.useTemporalSmoothing = true;
      break;
      
    case 'low':
      enhancedSettings.frameBudgetMs = 33; // 30fps
      enhancedSettings.motionScalingFactor = 0.3; // More aggressive downscaling
      enhancedSettings.useTemporalSmoothing = false; // Smoothing can be costly
      break;
  }
  
  return enhancedSettings;
}

/**
 * Implement rate-limited frame processing to ensure
 * CPU time is fairly divided between AR processing and UI
 * 
 * @param processingFn Function that processes a frame
 * @param frameBudgetMs Maximum time in ms to spend processing
 * @returns Result from the processing function
 */
export async function processingWithTimeBudget<T>(
  processingFn: () => Promise<T>,
  frameBudgetMs: number = 16
): Promise<T> {
  const startTime = performance.now();
  
  // Run the processing function
  const result = await processingFn();
  
  const endTime = performance.now();
  const processingTime = endTime - startTime;
  
  // If we've used more than our budget, yield to UI thread
  if (processingTime > frameBudgetMs) {
    // Use InteractionManager to schedule after any pending interactions/animations
    await new Promise<void>(resolve => {
      InteractionManager.runAfterInteractions(() => {
        resolve();
      });
    });
  }
  
  return result;
}

// State for motion-based optimizations
let lastMotionTimestamp = 0;
let currentMotionLevel: 'none' | 'low' | 'medium' | 'high' = 'none';
let motionHistory: number[] = [];
const MOTION_HISTORY_SIZE = 10;

/**
 * Track device motion level to dynamically adjust processing quality
 * 
 * @param movementAmount Amount of device movement detected (0-1)
 * @returns Current motion level classification
 */
export function trackDeviceMotion(movementAmount: number): 'none' | 'low' | 'medium' | 'high' {
  const now = Date.now();
  
  // Add to motion history
  motionHistory.push(movementAmount);
  if (motionHistory.length > MOTION_HISTORY_SIZE) {
    motionHistory.shift();
  }
  
  // Calculate average motion
  const avgMotion = motionHistory.reduce((sum, val) => sum + val, 0) / motionHistory.length;
  
  // Classify motion level
  if (avgMotion < 0.01) {
    currentMotionLevel = 'none';
  } else if (avgMotion < 0.05) {
    currentMotionLevel = 'low';
  } else if (avgMotion < 0.2) {
    currentMotionLevel = 'medium';
  } else {
    currentMotionLevel = 'high';
  }
  
  lastMotionTimestamp = now;
  return currentMotionLevel;
}

/**
 * Get dynamic quality factor based on current device motion
 * Reduces quality during rapid movement to maintain responsiveness
 * 
 * @param motionScalingFactor Base scaling factor from settings (0-1)
 * @returns Quality factor to apply to processing (0-1)
 */
export function getMotionAdaptiveQuality(motionScalingFactor: number = 0.5): number {
  switch (currentMotionLevel) {
    case 'none':
      return 1.0; // Full quality when device is still
    case 'low':
      return 0.9; // Slight reduction for minor movement
    case 'medium':
      return 0.7 * motionScalingFactor; // Moderate reduction
    case 'high':
      return 0.4 * motionScalingFactor; // Major reduction during rapid movement
    default:
      return 1.0;
  }
}

// Temporal smoothing for position data to reduce jitter
const positionHistory: Map<string, [number, number, number][]> = new Map();

/**
 * Apply temporal smoothing to position data to reduce jitter
 * but preserve responsiveness with motion prediction
 * 
 * @param key Identifier for this position data stream
 * @param position Current raw position data
 * @param velocity Current velocity if available
 * @param predictionMs Prediction time in ms
 * @param smoothingFactor Amount of smoothing (0-1, higher = more smoothing)
 * @returns Smoothed and predicted position
 */
export function getSmoothedPosition(
  key: string,
  position: [number, number, number],
  velocity?: [number, number, number],
  predictionMs: number = 20,
  smoothingFactor: number = 0.5
): [number, number, number] {
  // Get or create history for this position stream
  if (!positionHistory.has(key)) {
    positionHistory.set(key, []);
  }
  
  const history = positionHistory.get(key)!;
  
  // Add current position to history
  history.push(position);
  
  // Keep history at reasonable size
  if (history.length > 10) {
    history.shift();
  }
  
  // If we don't have enough history, or we're in high motion mode
  // use prediction without smoothing for responsiveness
  if (history.length < 3 || currentMotionLevel === 'high') {
    if (velocity) {
      return predictMotion(position, velocity, predictionMs);
    }
    return position;
  }
  
  // Calculate smoothed position
  const smoothed: [number, number, number] = [0, 0, 0];
  
  // Weight more recent positions more heavily
  let totalWeight = 0;
  for (let i = 0; i < history.length; i++) {
    const weight = i / (history.length - 1); // 0 to 1
    totalWeight += weight;
    
    for (let j = 0; j < 3; j++) {
      smoothed[j] += history[i][j] * weight;
    }
  }
  
  // Normalize by total weight
  for (let j = 0; j < 3; j++) {
    smoothed[j] /= totalWeight;
  }
  
  // Blend between raw and smoothed based on smoothing factor
  const result: [number, number, number] = [0, 0, 0];
  for (let j = 0; j < 3; j++) {
    result[j] = position[j] * (1 - smoothingFactor) + smoothed[j] * smoothingFactor;
  }
  
  // Apply prediction if velocity is available
  if (velocity) {
    return predictMotion(result, velocity, predictionMs);
  }
  
  return result;
}

/**
 * Async batching for multiple operations to improve throughput
 * 
 * @param operations Array of operations to run
 * @param batchSize Number of operations to run concurrently
 * @param yieldInterval Time in ms to yield to UI thread between batches
 * @returns Array of operation results
 */
export async function processBatch<T>(
  operations: (() => Promise<T>)[],
  batchSize: number = 4,
  yieldInterval: number = 5
): Promise<T[]> {
  const results: T[] = [];
  
  for (let i = 0; i < operations.length; i += batchSize) {
    const batch = operations.slice(i, i + batchSize);
    
    // Process this batch in parallel
    const batchResults = await Promise.all(batch.map(op => op()));
    results.push(...batchResults);
    
    // After each batch, yield to UI thread briefly to keep responsiveness
    if (i + batchSize < operations.length) {
      await new Promise(resolve => setTimeout(resolve, yieldInterval));
    }
  }
  
  return results;
}

/**
 * Prefetch and cache mesh data to reduce latency spikes
 */
export function prewarmMeshProcessing(): void {
  // This would initialize mesh processing operations
  // and prepare any acceleration structures needed
  // For now, this is a placeholder for the actual implementation
}

/**
 * Get acceleration structure configuration for faster LiDAR mesh processing
 */
export function getAccelerationStructureConfig(): {
  useBVH: boolean;
  useOctree: boolean;
  maxDepth: number;
  maxLeafSize: number;
} {
  // Bounding Volume Hierarchy (BVH) and Octree settings
  // for optimizing spatial queries in point cloud data
  return {
    useBVH: true,
    useOctree: true,
    maxDepth: 8,
    maxLeafSize: 32
  };
}

// Smart frame dropping with dynamic adaptation
let consecutiveDroppedFrames = 0;
const MAX_CONSECUTIVE_DROPPED = 3;

/**
 * Smart frame dropping that adapts to maintain minimum frame rate
 * 
 * @param currentLatencyMs Current processing latency 
 * @param targetFrameTimeMs Target frame time (e.g., 16ms for 60fps)
 * @returns Whether to skip this frame
 */
export async function shouldUseAdaptiveSkipFrame(
  currentLatencyMs: number, 
  targetFrameTimeMs: number = 16
): Promise<boolean> {
  // First check with standard skip logic
  const shouldSkip = await shouldSkipFrame(currentLatencyMs);
  
  // If not scheduled to skip, reset counter and return
  if (!shouldSkip) {
    consecutiveDroppedFrames = 0;
    return false;
  }
  
  // Prevent too many consecutive dropped frames
  if (consecutiveDroppedFrames >= MAX_CONSECUTIVE_DROPPED) {
    consecutiveDroppedFrames = 0;
    return false; // Force processing this frame
  }
  
  // Otherwise, drop the frame and increment counter
  consecutiveDroppedFrames++;
  return true;
}

/**
 * Automatically select optimal WebGL/Metal settings for AR rendering
 */
export function getOptimalRenderingSettings(): {
  msaaSamples: number;
  textureQuality: 'low' | 'medium' | 'high';
  shaderComplexity: 'low' | 'medium' | 'high';
  useMipMapping: boolean;
  useInstancing: boolean;
} {
  // Default for unknown devices
  let settings = {
    msaaSamples: 4,
    textureQuality: 'medium' as ('low' | 'medium' | 'high'),
    shaderComplexity: 'medium' as ('low' | 'medium' | 'high'),
    useMipMapping: true,
    useInstancing: true
  };
  
  // For iOS, get device-specific settings
  if (Platform.OS === 'ios') {
    const deviceYear = getDeviceYear();
    
    if (deviceYear >= 2022) {
      // Latest devices can handle full quality
      settings = {
        msaaSamples: 4,
        textureQuality: 'high' as ('low' | 'medium' | 'high'),
        shaderComplexity: 'high' as ('low' | 'medium' | 'high'),
        useMipMapping: true,
        useInstancing: true
      };
    } else if (deviceYear >= 2020) {
      // Slightly older devices get medium-high settings
      settings = {
        msaaSamples: 2,
        textureQuality: 'medium' as ('low' | 'medium' | 'high'),
        shaderComplexity: 'medium' as ('low' | 'medium' | 'high'),
        useMipMapping: true,
        useInstancing: true
      };
    } else {
      // Older devices get low-medium settings
      settings = {
        msaaSamples: 0,
        textureQuality: 'low' as ('low' | 'medium' | 'high'),
        shaderComplexity: 'low' as ('low' | 'medium' | 'high'),
        useMipMapping: true,
        useInstancing: false
      };
    }
  }
  
  return settings;
}

/**
 * Helper function to estimate device year based on model
 * This is a simplified version that would be more extensive in production
 */
function getDeviceYear(): number {
  // This would use device detection to determine the model year
  // For simplicity, return a recent year as default
  return 2022;
} 