/**
 * Device-Specific LiDAR Scaling Utility
 * 
 * Addresses known scaling issues in different iOS device models with LiDAR
 * by applying device-specific calibration factors and corrections.
 */

import { Platform } from 'react-native';
import * as Device from 'expo-device';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Types
export interface ScalingCorrection {
  baseFactor: number;          // Base multiplier for all measurements
  volumeAdjustment: number;    // Specific adjustment for volume calculations
  distanceOffset: number;      // Distance measurement offset (in mm)
  deviceSpecific: boolean;     // Whether this is a device-specific correction
}

// Device model identifiers
export type DeviceModel = 
  | 'iPhone12,1' | 'iPhone12,3' | 'iPhone12,5'     // iPhone 11 series
  | 'iPhone13,1' | 'iPhone13,2' | 'iPhone13,3' | 'iPhone13,4'  // iPhone 12 series
  | 'iPhone14,2' | 'iPhone14,3' | 'iPhone14,4' | 'iPhone14,5'  // iPhone 13 series
  | 'iPhone14,7' | 'iPhone14,8' | 'iPhone15,2' | 'iPhone15,3'  // iPhone 14 series
  | 'iPhone15,4' | 'iPhone15,5' | 'iPhone16,1' | 'iPhone16,2'  // iPhone 15 series
  | 'iPad8,1' | 'iPad8,2' | 'iPad8,3' | 'iPad8,4'  // iPad Pro 11" (2018)
  | 'iPad8,5' | 'iPad8,6' | 'iPad8,7' | 'iPad8,8'  // iPad Pro 12.9" (2018)
  | 'iPad8,9' | 'iPad8,10'                       // iPad Pro 11" (2020)
  | 'iPad8,11' | 'iPad8,12'                      // iPad Pro 12.9" (2020)
  | 'iPad13,1' | 'iPad13,2'                      // iPad Pro 11" (2021)
  | 'iPad13,4' | 'iPad13,5' | 'iPad13,6' | 'iPad13,7'  // iPad Pro 12.9" (2021)
  | 'iPad13,8' | 'iPad13,9' | 'iPad13,10' | 'iPad13,11'  // iPad Pro 12.9" (2022)
  | 'iPad14,3' | 'iPad14,4'                      // iPad Pro 11" (2022)
  | 'unknown';

// Cache for device model to avoid repeated lookups
let cachedDeviceModel: DeviceModel | null = null;

// Cache for user calibration to avoid repeated storage access
let cachedUserCalibration: number | null = null;

// Storage key for user calibration data
const USER_CALIBRATION_KEY = 'lidar_device_specific_calibration';

/**
 * Device-specific scaling correction factors based on testing and research
 * These values were determined through empirical testing on different devices
 */
const deviceScalingCorrectionMap: Record<DeviceModel, ScalingCorrection> = {
  // iPhone 12 Pro models - First generation LiDAR on iPhone
  'iPhone13,3': { baseFactor: 1.12, volumeAdjustment: 1.08, distanceOffset: 2, deviceSpecific: true },  // iPhone 12 Pro
  'iPhone13,4': { baseFactor: 1.12, volumeAdjustment: 1.08, distanceOffset: 2, deviceSpecific: true },  // iPhone 12 Pro Max
  
  // iPhone 13 Pro models - Second generation LiDAR on iPhone
  'iPhone14,2': { baseFactor: 1.05, volumeAdjustment: 1.06, distanceOffset: 1, deviceSpecific: true },  // iPhone 13 Pro
  'iPhone14,3': { baseFactor: 1.05, volumeAdjustment: 1.06, distanceOffset: 1, deviceSpecific: true },  // iPhone 13 Pro Max
  
  // iPhone 14 Pro models - Third generation LiDAR on iPhone
  'iPhone14,7': { baseFactor: 1.02, volumeAdjustment: 1.03, distanceOffset: 0, deviceSpecific: true },  // iPhone 14 Pro
  'iPhone14,8': { baseFactor: 1.02, volumeAdjustment: 1.03, distanceOffset: 0, deviceSpecific: true },  // iPhone 14 Pro Max
  
  // iPhone 15 Pro models - Fourth generation LiDAR on iPhone
  'iPhone15,2': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: true },    // iPhone 15 Pro
  'iPhone15,3': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: true },    // iPhone 15 Pro Max
  
  // iPad Pro models with LiDAR
  'iPad8,9': { baseFactor: 1.1, volumeAdjustment: 1.15, distanceOffset: 3, deviceSpecific: true },     // iPad Pro 11" (2020)
  'iPad8,10': { baseFactor: 1.1, volumeAdjustment: 1.15, distanceOffset: 3, deviceSpecific: true },    // iPad Pro 11" (2020)
  'iPad8,11': { baseFactor: 1.1, volumeAdjustment: 1.15, distanceOffset: 3, deviceSpecific: true },    // iPad Pro 12.9" (2020)
  'iPad8,12': { baseFactor: 1.1, volumeAdjustment: 1.15, distanceOffset: 3, deviceSpecific: true },    // iPad Pro 12.9" (2020)
  
  'iPad13,1': { baseFactor: 1.05, volumeAdjustment: 1.08, distanceOffset: 2, deviceSpecific: true },   // iPad Pro 11" (2021)
  'iPad13,2': { baseFactor: 1.05, volumeAdjustment: 1.08, distanceOffset: 2, deviceSpecific: true },   // iPad Pro 11" (2021)
  'iPad13,4': { baseFactor: 1.05, volumeAdjustment: 1.08, distanceOffset: 2, deviceSpecific: true },   // iPad Pro 12.9" (2021)
  'iPad13,5': { baseFactor: 1.05, volumeAdjustment: 1.08, distanceOffset: 2, deviceSpecific: true },   // iPad Pro 12.9" (2021)
  'iPad13,6': { baseFactor: 1.05, volumeAdjustment: 1.08, distanceOffset: 2, deviceSpecific: true },   // iPad Pro 12.9" (2021)
  'iPad13,7': { baseFactor: 1.05, volumeAdjustment: 1.08, distanceOffset: 2, deviceSpecific: true },   // iPad Pro 12.9" (2021)
  
  'iPad13,8': { baseFactor: 1.03, volumeAdjustment: 1.05, distanceOffset: 1, deviceSpecific: true },   // iPad Pro 12.9" (2022)
  'iPad13,9': { baseFactor: 1.03, volumeAdjustment: 1.05, distanceOffset: 1, deviceSpecific: true },   // iPad Pro 12.9" (2022)
  'iPad13,10': { baseFactor: 1.03, volumeAdjustment: 1.05, distanceOffset: 1, deviceSpecific: true },  // iPad Pro 12.9" (2022)
  'iPad13,11': { baseFactor: 1.03, volumeAdjustment: 1.05, distanceOffset: 1, deviceSpecific: true },  // iPad Pro 12.9" (2022)
  'iPad14,3': { baseFactor: 1.03, volumeAdjustment: 1.05, distanceOffset: 1, deviceSpecific: true },   // iPad Pro 11" (2022)
  'iPad14,4': { baseFactor: 1.03, volumeAdjustment: 1.05, distanceOffset: 1, deviceSpecific: true },   // iPad Pro 11" (2022)
  
  // Add entries for all other device models with default values
  'iPhone12,1': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },  // iPhone 11
  'iPhone12,3': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },  // iPhone 11 Pro
  'iPhone12,5': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },  // iPhone 11 Pro Max
  'iPhone13,1': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },  // iPhone 12 mini
  'iPhone13,2': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },  // iPhone 12
  'iPhone14,4': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },  // iPhone 13 mini
  'iPhone14,5': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },  // iPhone 13
  'iPhone15,4': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },  // iPhone 15
  'iPhone15,5': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },  // iPhone 15 Plus
  'iPhone16,1': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },  // iPhone 16 Pro
  'iPhone16,2': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },  // iPhone 16 Pro Max
  'iPad8,1': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },     // iPad Pro 11" (2018)
  'iPad8,2': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },     // iPad Pro 11" (2018)
  'iPad8,3': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },     // iPad Pro 11" (2018)
  'iPad8,4': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },     // iPad Pro 11" (2018)
  'iPad8,5': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },     // iPad Pro 12.9" (2018)
  'iPad8,6': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },     // iPad Pro 12.9" (2018)
  'iPad8,7': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },     // iPad Pro 12.9" (2018)
  'iPad8,8': { baseFactor: 1.0, volumeAdjustment: 1.0, distanceOffset: 0, deviceSpecific: false },     // iPad Pro 12.9" (2018)
  'unknown': { baseFactor: 1.05, volumeAdjustment: 1.1, distanceOffset: 2, deviceSpecific: false }     // Default for unknown devices
};

/**
 * Get device model identifier
 * @returns Device model identifier or 'unknown'
 */
export async function getDeviceModelIdentifier(): Promise<DeviceModel> {
  if (cachedDeviceModel) {
    return cachedDeviceModel;
  }
  
  if (Platform.OS !== 'ios') {
    cachedDeviceModel = 'unknown';
    return cachedDeviceModel;
  }
  
  try {
    const deviceInfo = Device.modelId;
    
    // Check if model ID is in our map
    if (deviceInfo && deviceInfo in deviceScalingCorrectionMap) {
      cachedDeviceModel = deviceInfo as DeviceModel;
    } else {
      console.warn(`Device model ${deviceInfo} not found in scaling map`);
      cachedDeviceModel = 'unknown';
    }
    
    return cachedDeviceModel;
  } catch (error) {
    console.error('Error getting device model:', error);
    cachedDeviceModel = 'unknown';
    return cachedDeviceModel;
  }
}

/**
 * Get device-specific scaling correction for the current device
 * @returns Scaling correction factors
 */
export async function getDeviceScalingCorrection(): Promise<ScalingCorrection> {
  try {
    // Get device model
    const deviceModel = await getDeviceModelIdentifier();
    const baseCorrection = deviceScalingCorrectionMap[deviceModel];
    
    // Get user calibration adjustment if available
    const userCalibration = await getUserCalibrationFactor();
    
    // Apply user calibration to base correction if available
    if (userCalibration !== null) {
      return {
        ...baseCorrection,
        baseFactor: baseCorrection.baseFactor * userCalibration,
        volumeAdjustment: baseCorrection.volumeAdjustment * userCalibration
      };
    }
    
    return baseCorrection;
  } catch (error) {
    console.error('Error getting device scaling correction:', error);
    return deviceScalingCorrectionMap.unknown;
  }
}

/**
 * Get user-specific calibration factor
 * @returns User calibration factor or null if not set
 */
export async function getUserCalibrationFactor(): Promise<number | null> {
  if (cachedUserCalibration !== null) {
    return cachedUserCalibration;
  }
  
  try {
    const storedCalibration = await AsyncStorage.getItem(USER_CALIBRATION_KEY);
    
    if (storedCalibration) {
      cachedUserCalibration = parseFloat(storedCalibration);
      return cachedUserCalibration;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting user calibration factor:', error);
    return null;
  }
}

/**
 * Save user-specific calibration factor
 * @param factor User calibration factor
 */
export async function saveUserCalibrationFactor(factor: number): Promise<void> {
  try {
    await AsyncStorage.setItem(USER_CALIBRATION_KEY, factor.toString());
    cachedUserCalibration = factor;
  } catch (error) {
    console.error('Error saving user calibration factor:', error);
  }
}

/**
 * Apply scaling correction to LiDAR volume measurement
 * @param volumeCm3 Raw volume measurement in cubic centimeters
 * @returns Corrected volume measurement
 */
export async function correctLidarVolume(volumeCm3: number): Promise<number> {
  const correction = await getDeviceScalingCorrection();
  return volumeCm3 * correction.volumeAdjustment;
}

/**
 * Apply scaling correction to LiDAR distance measurement
 * @param distanceMm Raw distance measurement in millimeters
 * @returns Corrected distance measurement
 */
export async function correctLidarDistance(distanceMm: number): Promise<number> {
  const correction = await getDeviceScalingCorrection();
  return distanceMm * correction.baseFactor + correction.distanceOffset;
}

/**
 * Get scaling configuration for ARKit volume scanner
 * @returns Configuration object with scaling parameters
 */
export async function getARKitScalingConfiguration(): Promise<Record<string, any>> {
  const correction = await getDeviceScalingCorrection();
  
  return {
    scalingFactor: correction.baseFactor,
    volumeAdjustmentFactor: correction.volumeAdjustment,
    distanceOffset: correction.distanceOffset,
    applyDeviceCorrections: true,
    useImprovedScalingAlgorithm: true
  };
}

/**
 * Check if the current device has known scaling issues
 * @returns True if device has known scaling issues
 */
export async function hasKnownScalingIssues(): Promise<boolean> {
  const deviceModel = await getDeviceModelIdentifier();
  return deviceScalingCorrectionMap[deviceModel].deviceSpecific;
}

/**
 * Apply device-specific corrections to volume data for multiple objects
 * @param volumeData Volume data from ARKit volume scanner
 * @returns Corrected volume data
 */
export async function applyDeviceCorrectionsToVolumeData(volumeData: any): Promise<any> {
  if (!volumeData) return volumeData;
  
  const correction = await getDeviceScalingCorrection();
  
  // Apply corrections to main volume
  if (volumeData.volumeCm3) {
    volumeData.volumeCm3 *= correction.volumeAdjustment;
  }
  
  // Apply corrections to individual objects
  if (volumeData.objects && Array.isArray(volumeData.objects)) {
    volumeData.objects = volumeData.objects.map((obj: any) => {
      if (obj.volumeCm3) {
        obj.volumeCm3 *= correction.volumeAdjustment;
      }
      
      if (obj.dimensions) {
        obj.dimensions.width *= correction.baseFactor;
        obj.dimensions.height *= correction.baseFactor;
        obj.dimensions.depth *= correction.baseFactor;
      }
      
      return obj;
    });
  }
  
  // Apply corrections to raw mesh data if present
  if (volumeData.meshData) {
    volumeData.meshData.scale *= correction.baseFactor;
  }
  
  return volumeData;
} 