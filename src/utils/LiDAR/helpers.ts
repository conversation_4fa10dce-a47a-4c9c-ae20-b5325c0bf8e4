/**
 * Helper functions for LiDAR scanning functionality
 */
import { ScanQualityLevel, StabilityLevel } from './types';

/**
 * Get appropriate color for scan quality level
 */
export function getScanQualityColor(quality: ScanQualityLevel): string {
  switch (quality) {
    case 'low': return '#f44336'; // Red
    case 'medium': return '#ffeb3b'; // Yellow
    case 'high': return '#4caf50'; // Green
    default: return '#2196F3'; // Default blue
  }
}

/**
 * Get appropriate color for confidence level
 */
export function getConfidenceColor(confidence: number): string {
  if (confidence >= 0.8) return '#4caf50'; // Green
  if (confidence >= 0.6) return '#ffeb3b'; // Yellow
  return '#f44336'; // Red
}

/**
 * Extract calories from nutrition analysis text
 */
export function extractCalories(nutritionData: any): number | undefined {
  if (!nutritionData || !nutritionData.analysis) return undefined;
  
  const calorieMatch = nutritionData.analysis.match(/(\d+)\s*calories/i);
  return calorieMatch && calorieMatch[1] ? parseInt(calorieMatch[1], 10) : undefined;
}

/**
 * Generate tags from food items
 */
export function generateTagsFromFoodItems(foodItems: any[]): string[] {
  return Array.from(new Set(
    foodItems.flatMap(item => {
      const words = item.foodName.toLowerCase().split(/\s+/);
      return words.filter(word => word.length > 2); // Only include words longer than 2 chars
    })
  ));
}

/**
 * Check if two bounding boxes overlap
 */
export function areOverlappingBoxes(
  box1: { x: number, y: number, width: number, height: number }, 
  box2: { x: number, y: number, width: number, height: number }
): boolean {
  return !(
    box1.x + box1.width < box2.x ||  // box1 is completely to the left of box2
    box1.x > box2.x + box2.width ||  // box1 is completely to the right of box2
    box1.y + box1.height < box2.y || // box1 is completely above box2
    box1.y > box2.y + box2.height    // box1 is completely below box2
  );
}

/**
 * Guess meal type based on time of day
 */
export function guessMealTypeFromTime(date: Date): 'breakfast' | 'lunch' | 'dinner' | 'snack' {
  const hour = date.getHours();
  
  if (hour >= 5 && hour < 10) {
    return 'breakfast';
  } else if (hour >= 10 && hour < 15) {
    return 'lunch';
  } else if (hour >= 17 && hour < 22) {
    return 'dinner';
  } else {
    return 'snack';
  }
}

/**
 * Get food color based on food name for visualization
 */
export function getFoodColor(name: string): string {
  const lowerName = name.toLowerCase();
  
  if (lowerName.includes('apple') || lowerName.includes('strawberry') || 
      lowerName.includes('cherry') || lowerName.includes('tomato')) {
    return '#e74c3c'; // Red
  } else if (lowerName.includes('banana') || lowerName.includes('lemon') ||
            lowerName.includes('corn') || lowerName.includes('pineapple')) {
    return '#f1c40f'; // Yellow
  } else if (lowerName.includes('broccoli') || lowerName.includes('spinach') ||
            lowerName.includes('lettuce') || lowerName.includes('kale')) {
    return '#2ecc71'; // Green
  } else if (lowerName.includes('blueberry') || lowerName.includes('grape')) {
    return '#9b59b6'; // Purple
  } else if (lowerName.includes('chicken') || lowerName.includes('beef') ||
            lowerName.includes('steak') || lowerName.includes('meat')) {
    return '#e67e22'; // Brown
  } else if (lowerName.includes('yogurt') || lowerName.includes('milk') ||
            lowerName.includes('cream') || lowerName.includes('cheese')) {
    return '#ecf0f1'; // White
  }
  
  // Default color
  return '#3498db'; // Blue
}

/**
 * Get shape for food visualization based on food name
 */
export function getShapeForFood(name: string): 'circle' | 'rectangle' {
  const lowerName = name.toLowerCase();
  
  // Fruits that are typically round
  if (lowerName.includes('apple') || lowerName.includes('orange') || 
      lowerName.includes('peach') || lowerName.includes('ball')) {
    return 'circle';
  }
  
  // Default to rectangle for other foods
  return 'rectangle';
}

/**
 * Get standard serving size for a food
 */
export function getStandardServing(name: string): string {
  const lowerName = name.toLowerCase();
  
  if (lowerName.includes('apple'))
    return '1 medium apple (~182g)';
  else if (lowerName.includes('banana'))
    return '1 medium banana (~118g)';
  else if (lowerName.includes('broccoli'))
    return '1 cup chopped (~91g)';
  else if (lowerName.includes('rice'))
    return '1 cup cooked (~195g)';
  else if (lowerName.includes('chicken'))
    return '3 oz portion (~85g)';
  else if (lowerName.includes('potato'))
    return '1 medium potato (~173g)';
  else
    return 'Standard serving';
}

/**
 * Calculate the percentage of a standard portion
 */
export function getPortionPercentage(foodName: string, volume: number): number | null {
  // This is a simplified example - would need a proper database of standard portions
  const standardPortions: {[key: string]: number} = {
    'apple': 182,
    'banana': 118,
    'broccoli': 91,
    'rice': 195,
    'chicken': 85,
    'potato': 173
  };
  
  // Try to find a matching food
  const match = Object.entries(standardPortions).find(([key]) => 
    foodName.toLowerCase().includes(key)
  );
  
  if (match) {
    const [foodType, standardWeight] = match;
    // Calculate current weight based on density
    const density = calculateFoodDensity(foodName);
    const currentWeight = volume * density;
    return Math.round((currentWeight / standardWeight) * 100);
  }
  
  return null;
}

/**
 * Calculate simple food density (for visualization only)
 */
export function calculateFoodDensity(name: string): number {
  // Simplified density calculator - in a real app this would use the foodDensityMap
  const lowerName = name.toLowerCase();
  
  if (lowerName.includes('apple') || lowerName.includes('fruit'))
    return 0.8; // g/cm³
  else if (lowerName.includes('vegetable'))
    return 0.6;
  else if (lowerName.includes('meat'))
    return 1.0;
  else if (lowerName.includes('bread') || lowerName.includes('grain'))
    return 0.3;
  else
    return 0.7; // Default density
} 