/**
 * Latency Optimization Utilities
 * 
 * Provides functions to reduce latency in AR/LiDAR feedback loops,
 * making the scanning process more responsive and real-time.
 */

import { Platform } from 'react-native';
import { getDeviceCapabilities } from '@/services/vision/performanceOptimization';

/**
 * Scanning feedback loop settings
 */
export interface FeedbackLoopSettings {
  // How often to update UI elements (in ms)
  uiUpdateInterval: number;
  
  // How often to request new frames from camera/LiDAR (in ms)
  frameCaptureInterval: number;
  
  // How often to process depth/mesh data (in ms)
  meshProcessingInterval: number;
  
  // Whether to run complex calculations on a separate thread
  useBackgroundProcessing: boolean;
  
  // Skip some processing steps to prioritize responsiveness
  prioritizeResponseOverAccuracy: boolean;
  
  // Control how much of the frame gets processed (0-1)
  processingCoverage: number;
  
  // Control depth resolution for LiDAR (lower = faster)
  depthResolution: 'low' | 'medium' | 'high';
}

/**
 * Get optimized feedback loop settings based on device capabilities
 * to minimize latency while maintaining necessary accuracy
 */
export async function getOptimizedFeedbackSettings(): Promise<FeedbackLoopSettings> {
  const capabilities = await getDeviceCapabilities();
  
  // Base settings on device performance level
  switch (capabilities.performanceLevel) {
    case 'high':
      return {
        uiUpdateInterval: 16, // ~60fps
        frameCaptureInterval: 33, // ~30fps
        meshProcessingInterval: 100, // 10 times per second
        useBackgroundProcessing: false, // Fast enough to run on main thread
        prioritizeResponseOverAccuracy: false,
        processingCoverage: 1.0, // Process full frame
        depthResolution: 'high'
      };
    
    case 'medium':
      return {
        uiUpdateInterval: 16, // ~60fps
        frameCaptureInterval: 66, // ~15fps
        meshProcessingInterval: 200, // 5 times per second
        useBackgroundProcessing: true,
        prioritizeResponseOverAccuracy: false,
        processingCoverage: 0.8, // Process 80% of frame
        depthResolution: 'medium'
      };
    
    case 'low':
      return {
        uiUpdateInterval: 33, // ~30fps
        frameCaptureInterval: 100, // ~10fps
        meshProcessingInterval: 500, // 2 times per second
        useBackgroundProcessing: true,
        prioritizeResponseOverAccuracy: true,
        processingCoverage: 0.5, // Process only half of frame
        depthResolution: 'low'
      };
  }
}

// Cache for reusing calculations
const calculationCache = new Map<string, { result: any; timestamp: number }>();
const CACHE_TTL = 1000; // 1 second cache lifetime

/**
 * Reuse calculations from recent frames when possible to avoid redundant processing
 * 
 * @param cacheKey Unique key for this calculation
 * @param calculateFn Function to perform the calculation if cache miss
 * @param forceRefresh Force recalculation even if cached value exists
 * @returns Calculation result
 */
export async function reuseCalculation<T>(
  cacheKey: string,
  calculateFn: () => Promise<T>,
  forceRefresh: boolean = false
): Promise<T> {
  const now = Date.now();
  const cached = calculationCache.get(cacheKey);
  
  // Use cached value if it's recent enough and we're not forcing a refresh
  if (!forceRefresh && cached && (now - cached.timestamp < CACHE_TTL)) {
    return cached.result;
  }
  
  // Otherwise perform the calculation
  const result = await calculateFn();
  
  // Cache the new result
  calculationCache.set(cacheKey, { result, timestamp: now });
  
  // Clean up old cache entries
  cleanCache();
  
  return result;
}

/**
 * Clean up old cache entries to prevent memory leaks
 */
function cleanCache() {
  const now = Date.now();
  for (const [key, { timestamp }] of calculationCache.entries()) {
    if (now - timestamp > CACHE_TTL * 2) {
      calculationCache.delete(key);
    }
  }
}

/**
 * Preload common AR assets to avoid loading delays during scanning
 */
export async function preloadARAssets(): Promise<void> {
  // In a real implementation, this would preload textures, models, and shaders
  // For now, just return immediately
  return Promise.resolve();
}

// Track when frame processing starts to measure latency
const frameStartTimes = new Map<string, number>();

/**
 * Mark the start of frame processing to measure latency
 * 
 * @param frameId Unique identifier for this frame
 */
export function startLatencyMeasurement(frameId: string): void {
  frameStartTimes.set(frameId, performance.now());
}

/**
 * End latency measurement and get processing time
 * 
 * @param frameId Unique identifier for this frame
 * @returns Processing time in ms
 */
export function endLatencyMeasurement(frameId: string): number {
  const startTime = frameStartTimes.get(frameId);
  if (!startTime) return 0;
  
  const endTime = performance.now();
  const latency = endTime - startTime;
  
  // Clean up
  frameStartTimes.delete(frameId);
  
  return latency;
}

/**
 * Get AR rendering configuration to minimize latency
 */
export function getARRenderingConfig(): {
  useHardwareAcceleration: boolean;
  preferredFrameRate: number;
  useLowResolutionOnMotion: boolean;
  reduceRenderingEffects: boolean;
} {
  // iOS-specific optimizations
  if (Platform.OS === 'ios') {
    return {
      useHardwareAcceleration: true,
      preferredFrameRate: 30,
      useLowResolutionOnMotion: true,
      reduceRenderingEffects: true
    };
  }
  
  // Default for other platforms
  return {
    useHardwareAcceleration: true,
    preferredFrameRate: 30,
    useLowResolutionOnMotion: true,
    reduceRenderingEffects: true
  };
}

/**
 * Apply frame prediction to compensate for rendering latency
 * This helps AR elements appear more stable even with processing delays
 * 
 * @param position Current position
 * @param velocity Current velocity
 * @param predictionTimeMs How far ahead to predict (ms)
 * @returns Predicted position
 */
export function predictMotion(
  position: [number, number, number],
  velocity: [number, number, number],
  predictionTimeMs: number
): [number, number, number] {
  // Simple linear prediction
  const predictionFactor = predictionTimeMs / 1000; // Convert to seconds
  
  return [
    position[0] + velocity[0] * predictionFactor,
    position[1] + velocity[1] * predictionFactor,
    position[2] + velocity[2] * predictionFactor
  ];
}

/**
 * Determine if a frame should be skipped based on device performance 
 * and current processing latency
 * 
 * @param currentLatencyMs Current processing latency in ms
 * @returns True if frame should be skipped
 */
export async function shouldSkipFrame(currentLatencyMs: number): Promise<boolean> {
  const capabilities = await getDeviceCapabilities();
  
  // Skip frames if latency gets too high
  if (capabilities.performanceLevel === 'low' && currentLatencyMs > 200) {
    return true;
  }
  
  if (capabilities.performanceLevel === 'medium' && currentLatencyMs > 300) {
    return true;
  }
  
  if (capabilities.performanceLevel === 'high' && currentLatencyMs > 500) {
    return true;
  }
  
  return false;
} 