/**
 * Types for LiDAR scanning functionality
 * Shared across LiDAR components and utilities
 */

// Device LiDAR capability status
export enum DeviceCapability {
  UNKNOWN = 'unknown',
  LIDAR_SUPPORTED = 'lidar_supported',
  LIDAR_NOT_SUPPORTED = 'lidar_not_supported'
}

// ScannerWithLiDAR props
export interface ScannerWithLiDARProps {
  onScanComplete?: (result: ScanResult) => void;
  onClose?: () => void;
  referenceObjects?: ReferenceObject[];
}

// Scan result object
export interface ScanResult {
  foods: {
    name: string;
    volumeCm3: number;
    weightGrams: number;
  }[];
  totalVolumeCm3: number;
  totalWeightGrams: number;
  nutritionAnalysis: any;
  image: string;
}

// Scan processing stages
export type ProcessingStage = 
  'idle' | 
  'scanning' | 
  'analyzing' | 
  'classifying' | 
  'estimating' | 
  'complete' | 
  'calibrating';

// Scan quality levels  
export type ScanQualityLevel = 'low' | 'medium' | 'high';

// Scan stability levels
export type StabilityLevel = 'unstable' | 'moderate' | 'stable';

// Volume visualizer props
export interface VolumeVisualizerProps {
  volume: number;
  maxVolume?: number;
  foodName: string;
  style?: any;
}

// Enhanced calibration modal props
export interface EnhancedCalibrationModalProps {
  visible: boolean;
  onClose: () => void;
  onStartCalibration: (objectName: string, volumeStr: string) => void;
  theme: { 
    colors: any; 
    isDark: boolean;
  };
  referenceObjects?: ReferenceObject[];
}

// Reference object for calibration
export interface ReferenceObject {
  id: string;
  name: string;
  volume: number;
  unit: string;
  image?: any;
  iconName?: string; // Optional Ionicons name to use instead of image
} 