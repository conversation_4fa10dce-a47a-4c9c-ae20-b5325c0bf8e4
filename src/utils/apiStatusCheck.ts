import { NUTRITIONIX_APP_ID, NUTRITIONIX_API_KEY } from './config';

// Define interface for Nutritionix API response
interface NutritionixResponse {
  foods?: {
    food_name: string;
    nf_calories: number;
    // other properties as needed
  }[];
}

/**
 * Check if Nutritionix API credentials are valid and API is accessible
 * @returns Object with status info
 */
export async function checkNutritionixStatus(): Promise<{
  isConfigured: boolean;
  isAccessible: boolean;
  error?: string;
  details?: string;
}> {
  try {
    // First check if credentials are available
    if (!NUTRITIONIX_APP_ID || !NUTRITIONIX_API_KEY) {
      return {
        isConfigured: false,
        isAccessible: false,
        error: 'Missing API credentials',
        details: 'Nutritionix API credentials not available in environment variables'
      };
    }

    // Try to make a simple request to check API accessibility
    // Using a very basic food item for testing
    const url = 'https://trackapi.nutritionix.com/v2/natural/nutrients';
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'x-app-id': NUTRITIONIX_APP_ID,
        'x-app-key': NUTRITIONIX_API_KEY,
        'x-remote-user-id': '0', // Required header for Nutritionix API
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query: 'apple'
      })
    });
    
    // Check if the API request was successful
    if (response.ok) {
      // Try to parse the response to verify it's valid
      const data = await response.json() as NutritionixResponse;
      
      if (data && data.foods && Array.isArray(data.foods)) {
        return {
          isConfigured: true,
          isAccessible: true,
          details: `API working. Found ${data.foods.length} foods in response.`
        };
      } else {
        return {
          isConfigured: true,
          isAccessible: false,
          error: 'API returned unexpected response format',
          details: JSON.stringify(data).substring(0, 150) + '...'
        };
      }
    } else {
      // Handle common error status codes
      let errorDetail = '';
      
      switch (response.status) {
        case 401:
          errorDetail = 'Invalid API credentials (unauthorized)';
          break;
        case 403:
          errorDetail = 'API access forbidden - may be rate limited or invalid credentials';
          break;
        case 404:
          errorDetail = 'API endpoint not found - the endpoint URL may have changed';
          break;
        case 429:
          errorDetail = 'Too many requests - API rate limit exceeded';
          break;
        case 500:
        case 502:
        case 503:
        case 504:
          errorDetail = `Server error (${response.status}) - the Nutritionix service may be down`;
          break;
        default:
          errorDetail = `API error code: ${response.status}`;
      }
      
      return {
        isConfigured: true,
        isAccessible: false,
        error: `API request failed with status ${response.status}`,
        details: errorDetail
      };
    }
  } catch (error) {
    // Network errors or other exceptions
    return {
      isConfigured: NUTRITIONIX_APP_ID && NUTRITIONIX_API_KEY ? true : false,
      isAccessible: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: 'Exception occurred when attempting to contact the API'
    };
  }
}

/**
 * Log detailed information about API status
 */
export async function logApiStatus(): Promise<void> {
  try {
    console.log('Checking Nutritionix API status...');
    const status = await checkNutritionixStatus();
    
    console.log('=== Nutritionix API Status ===');
    console.log(`Credentials configured: ${status.isConfigured ? 'Yes' : 'No'}`);
    console.log(`API accessible: ${status.isAccessible ? 'Yes' : 'No'}`);
    
    if (status.error) {
      console.log(`Error: ${status.error}`);
    }
    
    if (status.details) {
      console.log(`Details: ${status.details}`);
    }
    
    // Partially mask API credentials for logging
    if (NUTRITIONIX_APP_ID) {
      const maskedAppId = NUTRITIONIX_APP_ID.substring(0, 4) + '****';
      console.log(`App ID: ${maskedAppId}`);
    }
    
    if (NUTRITIONIX_API_KEY) {
      const maskedApiKey = NUTRITIONIX_API_KEY.substring(0, 4) + '****' + 
                           NUTRITIONIX_API_KEY.substring(NUTRITIONIX_API_KEY.length - 4);
      console.log(`API Key: ${maskedApiKey}`);
    }
    
    console.log('============================');
  } catch (error) {
    console.error('Error checking API status:', error);
  }
} 