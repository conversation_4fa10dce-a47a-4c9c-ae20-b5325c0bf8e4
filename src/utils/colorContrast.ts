/**
 * Utilities for calculating and ensuring proper color contrast for accessibility.
 * Following WCAG 2.1 guidelines requiring a minimum contrast ratio of 4.5:1 for normal text
 * and 3:1 for large text.
 */

/**
 * Convert a hex color to RGB components
 */
export function hexToRgb(hex: string): { r: number; g: number; b: number } {
  // Remove # if present
  const cleanHex = hex.startsWith('#') ? hex.slice(1) : hex;
  
  // Parse the hex values
  const r = parseInt(cleanHex.substring(0, 2), 16);
  const g = parseInt(cleanHex.substring(2, 4), 16);
  const b = parseInt(cleanHex.substring(4, 6), 16);
  
  return { r, g, b };
}

/**
 * Convert RGB to luminance value using WCAG formula
 * Formula: L = 0.2126 * R + 0.7152 * G + 0.0722 * B
 * where R, G, and B are normalized to [0, 1]
 */
export function calculateLuminance(rgb: { r: number; g: number; b: number }): number {
  // Normalize RGB values to [0, 1]
  const r = rgb.r / 255;
  const g = rgb.g / 255;
  const b = rgb.b / 255;
  
  // Apply gamma correction
  const R = r <= 0.03928 ? r / 12.92 : Math.pow((r + 0.055) / 1.055, 2.4);
  const G = g <= 0.03928 ? g / 12.92 : Math.pow((g + 0.055) / 1.055, 2.4);
  const B = b <= 0.03928 ? b / 12.92 : Math.pow((b + 0.055) / 1.055, 2.4);
  
  // Calculate luminance
  return 0.2126 * R + 0.7152 * G + 0.0722 * B;
}

/**
 * Calculate contrast ratio between two colors
 * Formula: (L1 + 0.05) / (L2 + 0.05) where L1 is the lighter color and L2 is the darker
 */
export function calculateContrastRatio(color1: string, color2: string): number {
  const lum1 = calculateLuminance(hexToRgb(color1));
  const lum2 = calculateLuminance(hexToRgb(color2));
  
  // Find the lighter and darker colors
  const lighter = Math.max(lum1, lum2);
  const darker = Math.min(lum1, lum2);
  
  // Calculate contrast ratio
  return (lighter + 0.05) / (darker + 0.05);
}

/**
 * Check if a color pair meets WCAG contrast requirements
 * @param foreground Foreground color (hex)
 * @param background Background color (hex)
 * @param isLargeText Whether this is for large text (>=18pt or >=14pt bold)
 * @returns Whether the contrast ratio meets WCAG requirements
 */
export function hasAdequateContrast(
  foreground: string,
  background: string,
  isLargeText: boolean = false
): boolean {
  const ratio = calculateContrastRatio(foreground, background);
  
  // WCAG AA requires 4.5:1 for normal text, 3:1 for large text
  // WCAG AAA requires 7:1 for normal text, 4.5:1 for large text
  const requiredRatio = isLargeText ? 3 : 4.5;
  
  return ratio >= requiredRatio;
}

/**
 * Adjust a color to ensure it has sufficient contrast with a background color
 * @param color The color to adjust (hex)
 * @param backgroundColor The background color (hex)
 * @param isLargeText Whether this is for large text
 * @returns A color with sufficient contrast
 */
export function ensureContrast(
  color: string,
  backgroundColor: string,
  isLargeText: boolean = false
): string {
  if (hasAdequateContrast(color, backgroundColor, isLargeText)) {
    return color;
  }
  
  // Convert colors to RGB
  const rgb = hexToRgb(color);
  const bgLuminance = calculateLuminance(hexToRgb(backgroundColor));
  
  // Determine whether to lighten or darken based on background luminance
  let shouldLighten = bgLuminance < 0.5;
  
  // Adjust color until we get adequate contrast
  let adjustedRgb = { ...rgb };
  let step = shouldLighten ? 10 : -10;
  
  while (!hasAdequateContrast(rgbToHex(adjustedRgb), backgroundColor, isLargeText)) {
    // Lighten or darken the color
    adjustedRgb = {
      r: Math.max(0, Math.min(255, adjustedRgb.r + step)),
      g: Math.max(0, Math.min(255, adjustedRgb.g + step)),
      b: Math.max(0, Math.min(255, adjustedRgb.b + step))
    };
    
    // If we've reached the limit and still don't have enough contrast, reverse direction
    if ((shouldLighten && (adjustedRgb.r === 255 || adjustedRgb.g === 255 || adjustedRgb.b === 255)) ||
        (!shouldLighten && (adjustedRgb.r === 0 || adjustedRgb.g === 0 || adjustedRgb.b === 0))) {
      step = -step; // Reverse direction
      shouldLighten = !shouldLighten;
    }
  }
  
  return rgbToHex(adjustedRgb);
}

/**
 * Convert RGB components to hex color string
 */
function rgbToHex(rgb: { r: number; g: number; b: number }): string {
  const toHex = (value: number) => {
    const hex = Math.max(0, Math.min(255, Math.round(value))).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };
  
  return `#${toHex(rgb.r)}${toHex(rgb.g)}${toHex(rgb.b)}`;
}

/**
 * Generate a color palette with accessible variations from a base color
 * @param baseColor The primary color to generate variations from
 * @param backgroundColor The background color to check contrast against
 * @returns An object containing light, medium, and dark variations of the base color
 */
export function generateAccessibleColorPalette(
  baseColor: string,
  backgroundColor: string
): { light: string; medium: string; dark: string } {
  const rgb = hexToRgb(baseColor);
  
  // Generate lighter and darker variations
  const light = {
    r: Math.min(255, rgb.r + 40),
    g: Math.min(255, rgb.g + 40),
    b: Math.min(255, rgb.b + 40)
  };
  
  const dark = {
    r: Math.max(0, rgb.r - 40),
    g: Math.max(0, rgb.g - 40),
    b: Math.max(0, rgb.b - 40)
  };
  
  // Ensure all variations have adequate contrast with the background
  const lightHex = ensureContrast(rgbToHex(light), backgroundColor);
  const mediumHex = ensureContrast(baseColor, backgroundColor);
  const darkHex = ensureContrast(rgbToHex(dark), backgroundColor);
  
  return {
    light: lightHex,
    medium: mediumHex,
    dark: darkHex
  };
} 