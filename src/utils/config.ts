// Configuration utility for accessing environment variables

import Constants from 'expo-constants';
import { Platform } from 'react-native';

// Get environment variables
const envData = Constants.expoConfig?.extra || {};

// Firebase Configuration
export const FIREBASE_API_KEY = process.env.EXPO_PUBLIC_FIREBASE_API_KEY || envData.firebaseApiKey || '';
export const FIREBASE_AUTH_DOMAIN = process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN || envData.firebaseAuthDomain || '';
export const FIREBASE_PROJECT_ID = process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID || envData.firebaseProjectId || '';
export const FIREBASE_STORAGE_BUCKET = process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET || envData.firebaseStorageBucket || '';
export const FIREBASE_MESSAGING_SENDER_ID = process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || envData.firebaseMessagingSenderId || '';
export const FIREBASE_APP_ID = process.env.EXPO_PUBLIC_FIREBASE_APP_ID || envData.firebaseAppId || '';
export const FIREBASE_MEASUREMENT_ID = process.env.EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID || envData.firebaseMeasurementId || '';





// OpenAI API Configuration
// Ensure we have a valid API key for food analysis
export const OPENAI_API_KEY = process.env.EXPO_PUBLIC_OPENAI_API_KEY || envData.openaiApiKey || '';
export const OPENAI_MODEL = process.env.EXPO_PUBLIC_OPENAI_MODEL || 'gpt-4-1106-preview';

// Google Vision API Configuration
export const GOOGLE_VISION_API_KEY = process.env.EXPO_PUBLIC_GOOGLE_VISION_API_KEY as string;

// Clarifai API Configuration
export const CLARIFAI_API_KEY = process.env.EXPO_PUBLIC_CLARIFAI_API_KEY as string;

// Hugging Face API Configuration
export const HUGGINGFACE_API_KEY = process.env.EXPO_PUBLIC_HUGGINGFACE_API_KEY as string;

// Add the USDA FoodData Central API key
export const USDA_API_KEY = 'Af38vZC48sSZCnVRNWCnGp5wL7CVdDt1foGkD9qi';

// Nutritionix API Configuration
export const NUTRITIONIX_APP_ID = process.env.EXPO_PUBLIC_NUTRITIONIX_APP_ID || '';
export const NUTRITIONIX_API_KEY = process.env.EXPO_PUBLIC_NUTRITIONIX_API_KEY || '';

// Add the Spoonacular API key to your config exports
export const SPOONACULAR_API_KEY = process.env.EXPO_PUBLIC_SPOONACULAR_API_KEY || '';

/**
 * Validates that all required environment variables are set
 * @returns Array of missing environment variables
 */
export function validateEnv(): string[] {
  const requiredVars = [
    { key: 'FIREBASE_API_KEY', value: FIREBASE_API_KEY },
    { key: 'FIREBASE_PROJECT_ID', value: FIREBASE_PROJECT_ID },
    { key: 'OPENAI_API_KEY', value: OPENAI_API_KEY },
    { key: 'GOOGLE_VISION_API_KEY', value: GOOGLE_VISION_API_KEY },
  ];

  // Debug logging for API keys
  console.log('DEBUG - ENVIRONMENT VARIABLES:');
  console.log('GOOGLE_VISION_API_KEY:', GOOGLE_VISION_API_KEY ? `Present (${GOOGLE_VISION_API_KEY.length} chars)` : 'Missing');
  console.log('OPENAI_API_KEY:', OPENAI_API_KEY ? `Present (${OPENAI_API_KEY.length} chars)` : 'Missing');
  console.log('CLARIFAI_API_KEY:', CLARIFAI_API_KEY ? `Present (${CLARIFAI_API_KEY.length} chars)` : 'Missing');
  console.log('HUGGINGFACE_API_KEY:', HUGGINGFACE_API_KEY ? `Present (${HUGGINGFACE_API_KEY.length} chars)` : 'Missing');
  
  return requiredVars
    .filter(v => !v.value)
    .map(v => v.key);
}

/**
 * Checks if all environment variables are configured
 * @returns boolean indicating if environment is properly configured
 */
export function isEnvConfigured(): boolean {
  return validateEnv().length === 0;
}

/**
 * Logs configuration issues for debugging
 */
export function logConfigStatus() {
  // Only log in development to avoid leaking keys in production logs
  if (__DEV__) {
    console.log('=== Configuration Status ===');
    console.log('Platform:', Platform.OS);
    console.log('Firebase API Key configured:', !!FIREBASE_API_KEY);
    console.log('Firebase Project ID configured:', !!FIREBASE_PROJECT_ID);
    console.log('OpenAI API Key configured:', !!OPENAI_API_KEY);
    console.log('===========================');
  }
}

// Ensure you check for the presence of the API key in your services
export function isSpoonacularConfigured(): boolean {
  return SPOONACULAR_API_KEY !== '';
}

// Service URLs
export const SERVICE_URLS = {
  FOOD_SEGMENTATION: '/functions/v1/food-segmentation',
  FOOD_WEIGHT_ESTIMATION: '/functions/v1/food-weight-estimation',
  GENERATE_RECIPE_IMAGE: '/functions/v1/generate-recipe-image',
  GENERATE_SEGMENT_VISUALIZATION: '/functions/v1/generate-segment-visualization',
  LIDAR_VOLUME_DISTRIBUTION: '/functions/v1/distribute-lidar-volume'
};