/**
 * Contextual Navigation
 * 
 * This utility tracks app usage patterns and provides contextual navigation 
 * suggestions based on user behavior.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage keys
const SCREEN_VISITS_KEY = 'screen_visits_history';
const TIME_PATTERNS_KEY = 'time_patterns_history';
const SHORTCUTS_KEY = 'contextual_shortcuts';

// Max days to keep in history for pattern recognition
const MAX_HISTORY_DAYS = 14; 

// Interface for screen visit data
interface ScreenVisit {
  screen: string;            // Screen path
  timestamp: number;         // Visit timestamp
  duration?: number;         // Time spent on screen (if tracked)
  dayOfWeek: number;         // 0-6 (Sunday-Saturday)
  timeOfDay: number;         // Hour of day (0-23)
}

// Interface for time pattern data
interface TimePattern {
  screen: string;            // Screen path
  dayOfWeek: number;         // 0-6 (Sunday-Saturday)
  timeOfDay: number;         // Hour of day (0-23)
  count: number;             // Number of visits at this time
}

// Interface for a shortcut suggestion
export interface ShortcutSuggestion {
  screen: string;            // Screen to navigate to
  title: string;             // Display name
  confidence: number;        // 0-1 confidence score
  icon: string;              // Icon name
}

class ContextualNavigation {
  private screenVisits: ScreenVisit[] = [];
  private timePatterns: TimePattern[] = [];
  private currentScreen: string | null = null;
  private screenEntryTime: number = 0;
  
  constructor() {
    this.loadData();
  }
  
  /**
   * Load saved data from storage
   */
  private async loadData() {
    try {
      // Load screen visits
      const visitsJson = await AsyncStorage.getItem(SCREEN_VISITS_KEY);
      if (visitsJson) {
        this.screenVisits = JSON.parse(visitsJson);
      }
      
      // Load time patterns
      const patternsJson = await AsyncStorage.getItem(TIME_PATTERNS_KEY);
      if (patternsJson) {
        this.timePatterns = JSON.parse(patternsJson);
      }
      
      // Prune old data
      this.pruneOldData();
    } catch (error) {
      console.error('Failed to load navigation history:', error);
    }
  }
  
  /**
   * Save data to storage
   */
  private async saveData() {
    try {
      await AsyncStorage.setItem(SCREEN_VISITS_KEY, JSON.stringify(this.screenVisits));
      await AsyncStorage.setItem(TIME_PATTERNS_KEY, JSON.stringify(this.timePatterns));
    } catch (error) {
      console.error('Failed to save navigation history:', error);
    }
  }
  
  /**
   * Remove data older than MAX_HISTORY_DAYS
   */
  private pruneOldData() {
    const now = Date.now();
    const maxAge = MAX_HISTORY_DAYS * 24 * 60 * 60 * 1000;
    
    this.screenVisits = this.screenVisits.filter(visit => now - visit.timestamp < maxAge);
    
    // Save pruned data
    this.saveData();
  }
  
  /**
   * Track when a user enters a screen
   */
  public trackScreenEntry(screen: string) {
    this.currentScreen = screen;
    this.screenEntryTime = Date.now();
  }
  
  /**
   * Track when a user leaves a screen
   */
  public trackScreenExit() {
    if (!this.currentScreen) return;
    
    const now = Date.now();
    const duration = now - this.screenEntryTime;
    const date = new Date();
    
    // Create screen visit record
    const visit: ScreenVisit = {
      screen: this.currentScreen,
      timestamp: now,
      duration: duration,
      dayOfWeek: date.getDay(),
      timeOfDay: date.getHours(),
    };
    
    // Add to history
    this.screenVisits.push(visit);
    
    // Update time patterns
    this.updateTimePattern(visit);
    
    // Reset current screen
    this.currentScreen = null;
    
    // Save updated data
    this.saveData();
  }
  
  /**
   * Update time pattern data with a new visit
   */
  private updateTimePattern(visit: ScreenVisit) {
    // Find existing pattern
    const existingPattern = this.timePatterns.find(p => 
      p.screen === visit.screen && 
      p.dayOfWeek === visit.dayOfWeek && 
      p.timeOfDay === visit.timeOfDay
    );
    
    if (existingPattern) {
      // Update count
      existingPattern.count += 1;
    } else {
      // Create new pattern
      this.timePatterns.push({
        screen: visit.screen,
        dayOfWeek: visit.dayOfWeek,
        timeOfDay: visit.timeOfDay,
        count: 1,
      });
    }
  }
  
  /**
   * Get contextual shortcut suggestions based on current time and day
   */
  public getShortcutSuggestions(maxSuggestions: number = 3): ShortcutSuggestion[] {
    const now = new Date();
    const currentDay = now.getDay();
    const currentHour = now.getHours();
    
    // Calculate scores for each screen based on patterns
    const screenScores = new Map<string, { score: number, count: number }>();
    
    // Score based on same hour and day
    this.timePatterns.forEach(pattern => {
      const key = pattern.screen;
      const score = this.calculatePatternScore(pattern, currentDay, currentHour);
      
      if (!screenScores.has(key)) {
        screenScores.set(key, { score: 0, count: 0 });
      }
      
      const current = screenScores.get(key)!;
      current.score += score * pattern.count;
      current.count += pattern.count;
    });
    
    // Convert to array and normalize scores
    const suggestions: ShortcutSuggestion[] = Array.from(screenScores.entries())
      .map(([screen, { score, count }]) => {
        // Normalize score based on count
        const normalizedScore = count > 0 ? score / count : 0;
        
        // Convert screen path to a title and icon
        const { title, icon } = this.getScreenDisplayInfo(screen);
        
        return {
          screen,
          title,
          confidence: normalizedScore,
          icon,
        };
      })
      .filter(suggestion => suggestion.confidence > 0.1) // Only suggestions with reasonable confidence
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, maxSuggestions);
    
    return suggestions;
  }
  
  /**
   * Calculate a score for a pattern based on time proximity to current time
   */
  private calculatePatternScore(pattern: TimePattern, currentDay: number, currentHour: number): number {
    // Calculate day similarity (0-1)
    const daySimilarity = pattern.dayOfWeek === currentDay ? 1 : 0.3;
    
    // Calculate hour similarity (0-1)
    const hourDiff = Math.abs(pattern.timeOfDay - currentHour);
    let hourSimilarity = 0;
    
    if (hourDiff === 0) {
      hourSimilarity = 1;
    } else if (hourDiff === 1) {
      hourSimilarity = 0.7;
    } else if (hourDiff === 2) {
      hourSimilarity = 0.4;
    } else if (hourDiff <= 4) {
      hourSimilarity = 0.2;
    }
    
    // Combine scores
    return daySimilarity * hourSimilarity;
  }
  
  /**
   * Get display information for a screen path
   */
  private getScreenDisplayInfo(screen: string): { title: string, icon: string } {
    // Map screen paths to user-friendly titles and icons
    switch (screen) {
      case '/':
        return { title: 'Home', icon: 'home' };
      case '/dashboard-demo':
        return { title: 'Dashboard', icon: 'grid' };
      case '/profile':
        return { title: 'Profile', icon: 'person' };
      case '/meals':
        return { title: 'Meals', icon: 'restaurant' };
      case '/water':
        return { title: 'Water', icon: 'water' };
      case '/sleep':
        return { title: 'Sleep', icon: 'bed' };
      case '/workouts':
        return { title: 'Workouts', icon: 'barbell' };
      default:
        return { 
          title: screen.replace(/^\//g, '').replace(/-/g, ' '), 
          icon: 'apps' 
        };
    }
  }
  
  /**
   * Save a custom shortcut
   */
  public async saveCustomShortcut(shortcut: ShortcutSuggestion) {
    try {
      // Load existing shortcuts
      const shortcutsJson = await AsyncStorage.getItem(SHORTCUTS_KEY);
      let shortcuts: ShortcutSuggestion[] = [];
      
      if (shortcutsJson) {
        shortcuts = JSON.parse(shortcutsJson);
      }
      
      // Check if shortcut already exists
      const existingIndex = shortcuts.findIndex(s => s.screen === shortcut.screen);
      if (existingIndex >= 0) {
        shortcuts[existingIndex] = shortcut;
      } else {
        shortcuts.push(shortcut);
      }
      
      // Save updated shortcuts
      await AsyncStorage.setItem(SHORTCUTS_KEY, JSON.stringify(shortcuts));
    } catch (error) {
      console.error('Failed to save custom shortcut:', error);
    }
  }
  
  /**
   * Get custom shortcuts
   */
  public async getCustomShortcuts(): Promise<ShortcutSuggestion[]> {
    try {
      const shortcutsJson = await AsyncStorage.getItem(SHORTCUTS_KEY);
      if (shortcutsJson) {
        return JSON.parse(shortcutsJson);
      }
    } catch (error) {
      console.error('Failed to load custom shortcuts:', error);
    }
    
    return [];
  }
  
  /**
   * Get recently visited screens
   * @param limit Maximum number of recent screens to return
   * @returns Array of screen suggestions
   */
  public getRecentScreens(limit: number = 5): ShortcutSuggestion[] {
    // Sort visits by recency (most recent first)
    const recentVisits = [...this.screenVisits]
      .sort((a, b) => b.timestamp - a.timestamp);
    
    // Get unique screens (first occurrence only)
    const uniqueScreens = new Map<string, ScreenVisit>();
    recentVisits.forEach(visit => {
      if (!uniqueScreens.has(visit.screen)) {
        uniqueScreens.set(visit.screen, visit);
      }
    });
    
    // Convert to shortcuts
    return Array.from(uniqueScreens.values())
      .slice(0, limit)
      .map(visit => {
        const { title, icon } = this.getScreenDisplayInfo(visit.screen);
        return {
          screen: visit.screen,
          title,
          icon,
          confidence: 1.0
        };
      });
  }
  
  /**
   * Get analytics data for navigation dashboard
   * @returns Object containing aggregated data for analytics
   */
  public getAnalyticsData() {
    // Aggregated screen visits
    const screenVisits: Record<string, number> = {};
    
    // Aggregated time of day data
    const timeOfDayData: Record<string, number> = {};
    
    // Aggregated day of week data
    const dayOfWeekData: Record<string, number> = {};
    
    // Count total visits
    const totalVisits = this.screenVisits.length;
    
    // Process each visit to build aggregated data
    this.screenVisits.forEach(visit => {
      // Count screen visits
      if (!screenVisits[visit.screen]) {
        screenVisits[visit.screen] = 0;
      }
      screenVisits[visit.screen]++;
      
      // Count time of day visits
      const timeKey = String(visit.timeOfDay);
      if (!timeOfDayData[timeKey]) {
        timeOfDayData[timeKey] = 0;
      }
      timeOfDayData[timeKey]++;
      
      // Count day of week visits
      const dayKey = String(visit.dayOfWeek);
      if (!dayOfWeekData[dayKey]) {
        dayOfWeekData[dayKey] = 0;
      }
      dayOfWeekData[dayKey]++;
    });
    
    return {
      screenVisits,
      timeOfDayData,
      dayOfWeekData,
      totalVisits
    };
  }
  
  /**
   * Get the number of screen visits recorded
   * @returns The total number of screen visits
   */
  public getVisitCount(): number {
    return this.screenVisits.length;
  }
  
  /**
   * Clear all navigation history and patterns
   */
  public async clearHistory() {
    this.screenVisits = [];
    this.timePatterns = [];
    await this.saveData();
  }
}

// Export a singleton instance
export const contextualNavigation = new ContextualNavigation(); 