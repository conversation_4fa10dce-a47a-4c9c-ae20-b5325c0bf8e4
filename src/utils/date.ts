/**
 * Utils for working with dates
 */

/**
 * Format a date into a readable string
 * @param date Date to format
 * @param format Optional format specification
 * @returns Formatted date string
 */
export function formatDate(date: Date | string | number, format: string = 'default'): string {
  // Convert to Date object if string or number
  const dateObj = new Date(date);
  
  // Return empty string for invalid dates
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  
  // Format based on specified format
  switch (format) {
    case 'short':
      // MM/DD/YYYY
      return `${dateObj.getMonth() + 1}/${dateObj.getDate()}/${dateObj.getFullYear()}`;
    
    case 'long':
      // Month DD, YYYY
      return `${dateObj.toLocaleString('default', { month: 'long' })} ${dateObj.getDate()}, ${dateObj.getFullYear()}`;
    
    case 'time':
      // HH:MM AM/PM
      return dateObj.toLocaleString('default', { hour: 'numeric', minute: '2-digit', hour12: true });
    
    case 'datetime':
      // MM/DD/YYYY, HH:MM AM/PM
      return `${formatDate(dateObj, 'short')}, ${formatDate(dateObj, 'time')}`;
    
    case 'iso':
      // ISO format: YYYY-MM-DDTHH:MM:SS.sssZ
      return dateObj.toISOString();
    
    case 'relative':
      // Relative time (e.g., "2 hours ago")
      return getRelativeTime(dateObj);
    
    case 'default':
    default:
      // Default format: Weekday, Month DD, YYYY
      return dateObj.toLocaleDateString('default', { 
        weekday: 'short',
        month: 'short', 
        day: 'numeric', 
        year: 'numeric' 
      });
  }
}

/**
 * Get relative time string (e.g., "2 hours ago")
 */
function getRelativeTime(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  const diffMonth = Math.floor(diffDay / 30);
  const diffYear = Math.floor(diffDay / 365);
  
  if (diffSec < 60) {
    return 'just now';
  } else if (diffMin < 60) {
    return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
  } else if (diffHour < 24) {
    return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
  } else if (diffDay < 30) {
    return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
  } else if (diffMonth < 12) {
    return `${diffMonth} month${diffMonth > 1 ? 's' : ''} ago`;
  } else {
    return `${diffYear} year${diffYear > 1 ? 's' : ''} ago`;
  }
}

/**
 * Get a formatted time from a date
 * @param date Date to extract time from
 * @returns Formatted time string
 */
export function formatTime(date: Date): string {
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
  });
}

/**
 * Format date and time together
 * @param date Date to format
 * @returns Formatted date and time string
 */
export function formatDateTime(date: Date): string {
  return `${formatDate(date)} at ${formatTime(date)}`;
}

/**
 * Get a relative time string (e.g., "2 hours ago")
 * @param date The date to compare against now
 * @returns Human-readable relative time
 */
export function getRelativeTimeString(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return 'just now';
  }
  
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} ${diffInMinutes === 1 ? 'minute' : 'minutes'} ago`;
  }
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} ${diffInHours === 1 ? 'hour' : 'hours'} ago`;
  }
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) {
    return `${diffInDays} ${diffInDays === 1 ? 'day' : 'days'} ago`;
  }
  
  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return `${diffInMonths} ${diffInMonths === 1 ? 'month' : 'months'} ago`;
  }
  
  const diffInYears = Math.floor(diffInMonths / 12);
  return `${diffInYears} ${diffInYears === 1 ? 'year' : 'years'} ago`;
} 