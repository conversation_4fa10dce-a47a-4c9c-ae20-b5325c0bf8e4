/**
 * Enhanced Weight Estimation Utility
 * 
 * Combines multiple data sources (shape analysis, reference objects,
 * food density database, and USDA API data) to provide the most
 * accurate food weight estimation possible.
 */

import { calculateFoodWeight, getFoodDensity } from './foodDensityMap';
import { determineFoodShape, estimateVolumeFrom2D, FoodShape, getFoodSpecificVolumeAdjustment } from './foodShapeRecognition';
import { getEnhancedFoodDensity } from '@/services/food-data/usdaApiService';
import { calculateRealVolume, DetectedReferenceObject } from './referenceObjectDetection';
import { getTablewareReferenceObjects, DetectedTableware } from './plateDetection';
import { estimateFoodWeightWithGPT4V, GPT4VWeightEstimation } from '@/services/openai/foodAnalysis';
import { uriToBase64 } from '@/services/vision/visionApi';
import { logWeightEstimation } from '@/services/weightConfidenceLoggingService';

/**
 * Source of weight estimation data
 */
export enum WeightEstimationSource {
  LIDAR = 'lidar',
  REFERENCE_OBJECT = 'referenceObject',
  IMAGE_BASED = 'imageBased',
  STATISTICAL_AVERAGE = 'statisticalAverage',
  USDA_API = 'usdaApi',
  GPT_4V = 'gpt4v'
}

/**
 * Enhanced weight estimation result
 */
export interface EnhancedWeightEstimate {
  foodName: string;
  weightGrams: number;
  confidence: number;
  source: WeightEstimationSource;
  details?: {
    shape?: FoodShape;
    density?: number;
    volume?: number;
    volumeUnit?: string;
    adjustmentFactor?: number;
    densitySource?: 'local' | 'usda' | 'default';
  };
}

/**
 * Parameters for weight estimation
 */
export interface WeightEstimationParams {
  foodName: string;
  // LiDAR data (if available)
  lidarVolume?: number;  // in cm³
  lidarConfidence?: number;
  // 2D image data
  pixelArea?: number;
  pixelWidth?: number;
  pixelHeight?: number;
  // Reference object data
  referenceObjects?: DetectedReferenceObject[];
  tableware?: DetectedTableware[];
  // Additional context
  imageBasedWeightEstimate?: number; // from AI/Vision API
  imageBasedConfidence?: number;
  visualCharacteristics?: {
    aspectRatio?: number;
    roundness?: number;
    containerPresent?: boolean;
    depth?: number;
  };
  // Image data for GPT-4V
  imageUri?: string;
  // User calibration data
  userCalibrationFactor?: number;
}

/**
 * Get an enhanced weight estimate using all available data sources
 * 
 * @param params Weight estimation parameters
 * @returns Enhanced weight estimate
 */
export async function getEnhancedWeightEstimate(
  params: WeightEstimationParams
): Promise<EnhancedWeightEstimate> {
  // Start with default values
  let weightGrams = 0;
  let confidence = 0;
  let source = WeightEstimationSource.STATISTICAL_AVERAGE;
  let densitySource: 'local' | 'usda' | 'default' = 'default';
  
  // Get food shape
  let shape = determineFoodShape(
    params.foodName, 
    {
      aspectRatio: params.visualCharacteristics?.aspectRatio,
      roundness: params.visualCharacteristics?.roundness,
      containerPresent: params.visualCharacteristics?.containerPresent,
      depth: params.visualCharacteristics?.depth
    }
  );
  
  // Try to get enhanced density from USDA API first, fall back to local database
  let density: number;
  try {
    density = await getEnhancedFoodDensity(params.foodName);
    densitySource = density !== 0.7 ? 'usda' : 'default';
    
    // If we got the default USDA density, try local database
    if (density === 0.7) {
      const localDensity = getFoodDensity(params.foodName);
      if (localDensity !== 0.7) {
        density = localDensity;
        densitySource = 'local';
      }
    }
  } catch (error) {
    // If USDA API fails, use local database
    density = getFoodDensity(params.foodName);
    densitySource = density !== 0.7 ? 'local' : 'default';
  }
  
  // Food-specific volume adjustment based on food characteristics
  const volumeAdjustment = getFoodSpecificVolumeAdjustment(params.foodName);
  
  // Get all available reference objects
  const allReferenceObjects = [
    ...(params.referenceObjects || []),
    ...(params.tableware ? getTablewareReferenceObjects(params.tableware) : [])
  ];
  
  // Try GPT-4V weight estimation first if image is available
  if (params.imageUri) {
    try {
      // Convert image to base64
      const imageBase64 = await uriToBase64(params.imageUri);
      
      // Call GPT-4V for weight estimation
      const gpt4vResult = await estimateFoodWeightWithGPT4V({
        imageBase64,
        foodName: params.foodName,
        referenceObjects: params.referenceObjects,
        tableware: params.tableware,
        pixelDimensions: params.pixelWidth && params.pixelHeight && params.pixelArea ? {
          width: params.pixelWidth,
          height: params.pixelHeight,
          area: params.pixelArea
        } : undefined
      });
      
      // Check if we got a valid result with reasonable confidence
      if (gpt4vResult.weightGrams > 0 && gpt4vResult.confidence >= 0.4) {
        weightGrams = gpt4vResult.weightGrams;
        confidence = gpt4vResult.confidence;
        source = WeightEstimationSource.GPT_4V;
        
        // Use GPT-4V estimated shape if available
        if (gpt4vResult.estimationDetails?.shape) {
          shape = gpt4vResult.estimationDetails.shape;
        }
        
        // Use GPT-4V estimated density if available and reasonable
        if (gpt4vResult.estimationDetails?.density && 
            gpt4vResult.estimationDetails.density >= 0.1 && 
            gpt4vResult.estimationDetails.density <= 2.0) {
          density = gpt4vResult.estimationDetails.density;
          densitySource = 'usda'; // Mark as from external source
        }
        
        // Return early with the result
        const result: EnhancedWeightEstimate = {
          foodName: params.foodName,
          weightGrams,
          confidence,
          source,
          details: {
            shape,
            density,
            volume: gpt4vResult.estimationDetails?.volume,
            volumeUnit: 'cm³',
            adjustmentFactor: volumeAdjustment,
            densitySource
          }
        };
        
        // Log the weight estimation for analysis
        // This won't block the return since we don't await it
        logWeightEstimation(
          result, 
          Boolean((params.referenceObjects && params.referenceObjects.length > 0) || 
          (params.tableware && params.tableware.length > 0))
        );
        
        return result;
      }
      
      // If GPT-4V failed or had low confidence, continue with other methods
      console.log('GPT-4V estimation had low confidence, trying other methods');
    } catch (error) {
      console.error('Error using GPT-4V for weight estimation:', error);
      // Continue with other methods
    }
  }
  
  // 1. Try LiDAR-based estimation first (most accurate when available)
  if (
    params.lidarVolume !== undefined && 
    params.lidarVolume > 0 && 
    params.lidarConfidence && 
    params.lidarConfidence > 0.6
  ) {
    // Calculate weight from LiDAR volume and food density
    const volume = params.lidarVolume * volumeAdjustment;
    weightGrams = volume * density;
    confidence = params.lidarConfidence;
    source = WeightEstimationSource.LIDAR;
  }
  // 2. Try reference object-based estimation next
  else if (
    allReferenceObjects.length > 0 && 
    params.pixelArea && 
    params.pixelHeight && 
    params.pixelWidth
  ) {
    // Calculate volume based on real-world measurements
    const estimatedHeightPixels = params.pixelHeight * 0.3; // Rough height estimate as 30% of width
    const volumeResult = calculateRealVolume(
      params.pixelArea,
      estimatedHeightPixels,
      allReferenceObjects
    );
    
    if (volumeResult.volumeMm3 > 0) {
      // Convert mm³ to cm³ and apply food-specific adjustment
      const volumeCm3 = (volumeResult.volumeMm3 / 1000) * volumeAdjustment;
      weightGrams = volumeCm3 * density;
      confidence = volumeResult.confidence * 0.9; // Slightly reduce confidence
      source = WeightEstimationSource.REFERENCE_OBJECT;
    }
  }
  // 3. Try image-based estimation with shape analysis
  else if (
    params.pixelArea && 
    params.pixelWidth && 
    params.pixelHeight
  ) {
    // Estimate volume based on 2D image and shape analysis
    const volumePixels3 = estimateVolumeFrom2D(
      params.foodName,
      params.pixelArea,
      params.pixelWidth,
      params.pixelHeight,
      {
        aspectRatio: params.visualCharacteristics?.aspectRatio,
        roundness: params.visualCharacteristics?.roundness,
        containerPresent: params.visualCharacteristics?.containerPresent,
        depth: params.visualCharacteristics?.depth
      }
    );
    
    // We need to convert pixel-based volume to real-world volume
    // Since we don't have reference objects, we'll use a reasonable
    // estimate based on common food sizes and screen dimensions
    
    // Assume average mobile screen width of 375pt and a typical food
    // item width of 10cm (100mm) for scaling
    const estimatedPixelsPerMm = params.pixelWidth / 100;
    const volumeScaleFactor = Math.pow(estimatedPixelsPerMm, 3);
    const volumeMm3 = volumePixels3 / volumeScaleFactor;
    const volumeCm3 = volumeMm3 / 1000 * volumeAdjustment;
    
    weightGrams = volumeCm3 * density;
    confidence = 0.6; // Moderate confidence for image-based analysis without references
    source = WeightEstimationSource.IMAGE_BASED;
  }
  // 4. Use image-based weight estimate from Vision API if available
  else if (
    params.imageBasedWeightEstimate && 
    params.imageBasedWeightEstimate > 0
  ) {
    weightGrams = params.imageBasedWeightEstimate;
    confidence = params.imageBasedConfidence || 0.7;
    source = WeightEstimationSource.IMAGE_BASED;
  } 
  // 5. Fall back to statistical average
  else {
    // Use calculateFoodWeight with null volume to get statistical estimate
    weightGrams = calculateFoodWeight(params.foodName, 0);
    confidence = 0.4; // Low confidence for statistical estimates
    source = WeightEstimationSource.STATISTICAL_AVERAGE;
  }
  
  // Apply user calibration factor if available
  if (params.userCalibrationFactor && params.userCalibrationFactor > 0) {
    weightGrams *= params.userCalibrationFactor;
    // Don't increase confidence based on calibration factor
  }
  
  // Cap weight and confidence to reasonable values
  weightGrams = Math.max(1, Math.round(weightGrams));
  confidence = Math.min(0.95, Math.max(0.1, confidence));
  
  const result: EnhancedWeightEstimate = {
    foodName: params.foodName,
    weightGrams,
    confidence,
    source,
    details: {
      shape,
      density,
      adjustmentFactor: volumeAdjustment,
      densitySource
    }
  };
  
  // Log the weight estimation for analysis
  // This won't block the return since we don't await it
  logWeightEstimation(
    result, 
    Boolean((params.referenceObjects && params.referenceObjects.length > 0) || 
    (params.tableware && params.tableware.length > 0))
  );
  
  return result;
}

/**
 * Get a weighted combination of multiple weight estimates
 * 
 * @param estimates Array of weight estimates for the same food
 * @returns Combined weight estimate
 */
export function combineWeightEstimates(
  estimates: EnhancedWeightEstimate[]
): EnhancedWeightEstimate {
  if (!estimates || estimates.length === 0) {
    throw new Error('No weight estimates provided');
  }
  
  if (estimates.length === 1) {
    return estimates[0];
  }
  
  // Use the first estimate as the base for food name and details
  const baseEstimate = estimates[0];
  
  // Calculate source confidence weights
  const sourceWeights: Record<WeightEstimationSource, number> = {
    [WeightEstimationSource.LIDAR]: 1.0,
    [WeightEstimationSource.GPT_4V]: 0.9, // High weight for GPT-4V
    [WeightEstimationSource.REFERENCE_OBJECT]: 0.8,
    [WeightEstimationSource.IMAGE_BASED]: 0.6,
    [WeightEstimationSource.USDA_API]: 0.5,
    [WeightEstimationSource.STATISTICAL_AVERAGE]: 0.3
  };
  
  // Calculate weighted sum
  let weightedSum = 0;
  let weightSum = 0;
  let maxConfidence = 0;
  let bestSource = WeightEstimationSource.STATISTICAL_AVERAGE;
  
  for (const estimate of estimates) {
    const sourceWeight = sourceWeights[estimate.source];
    const weight = estimate.confidence * sourceWeight;
    
    weightedSum += estimate.weightGrams * weight;
    weightSum += weight;
    
    // Track highest confidence estimate
    if (estimate.confidence * sourceWeight > maxConfidence) {
      maxConfidence = estimate.confidence * sourceWeight;
      bestSource = estimate.source;
    }
  }
  
  // Calculate final weighted average
  const averageWeight = weightSum > 0 ? weightedSum / weightSum : baseEstimate.weightGrams;
  
  // Calculate final confidence (based on weighted combination of all estimates)
  // Higher confidence if estimates agree with each other
  const weightVariance = calculateWeightVariance(estimates, averageWeight);
  const agreementFactor = Math.max(0.5, 1 - weightVariance);
  const finalConfidence = maxConfidence * agreementFactor;
  
  return {
    foodName: baseEstimate.foodName,
    weightGrams: Math.round(averageWeight),
    confidence: finalConfidence,
    source: bestSource,
    details: baseEstimate.details
  };
}

/**
 * Calculate the variance in weights as a percentage of the average
 * 
 * @param estimates Array of weight estimates
 * @param averageWeight Average weight
 * @returns Variance as a value from 0-1
 */
function calculateWeightVariance(
  estimates: EnhancedWeightEstimate[],
  averageWeight: number
): number {
  if (estimates.length <= 1 || averageWeight <= 0) {
    return 0;
  }
  
  // Calculate squared differences
  let sumSquaredDiff = 0;
  for (const estimate of estimates) {
    const diff = estimate.weightGrams - averageWeight;
    sumSquaredDiff += diff * diff;
  }
  
  // Calculate variance
  const variance = sumSquaredDiff / estimates.length;
  
  // Normalize to a 0-1 scale relative to the average weight
  const normalizedVariance = Math.min(1, Math.sqrt(variance) / averageWeight);
  
  return normalizedVariance;
} 