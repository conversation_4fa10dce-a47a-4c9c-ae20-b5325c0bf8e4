/**
 * Environment configuration utilities with T3-style validation
 * This module provides typed environment variable access with runtime validation
 */

const { validateEnvironment } = require('../../infra/environment-validation');

let validatedEnv: any | null = null;

/**
 * Get validated environment variables with runtime type safety
 * Following T3 pattern of validating once and caching the result
 */
function getEnv(): any {
  if (!validatedEnv) {
    try {
      validatedEnv = validateEnvironment();
    } catch (error) {
      throw new Error(
        `Environment validation failed. The app cannot continue with invalid configuration. ${error}`
      );
    }
  }
  return validatedEnv;
}

/**
 * Typed environment configuration object
 * All environment variables are validated and properly typed
 */
export const env = {
  // Core environment settings
  get ENVIRONMENT() {
    return getEnv().EXPO_PUBLIC_ENVIRONMENT;
  },
  
  get IS_DEV() {
    return this.ENVIRONMENT === 'development';
  },
  
  get IS_STAGING() {
    return this.ENVIRONMENT === 'staging';
  },
  
  get IS_PROD() {
    return this.ENVIRONMENT === 'production';
  },

  // Firebase configuration  get FIREBASE_PROJECT_ID() {
    return getEnv().EXPO_PUBLIC_FIREBASE_PROJECT_ID;
  },

  get FIREBASE_API_KEY() {
    return getEnv().EXPO_PUBLIC_FIREBASE_API_KEY;
  },

  get FIREBASE_AUTH_DOMAIN() {
    return getEnv().EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN;
  },

  get FIREBASE_STORAGE_BUCKET() {
    return getEnv().EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET;
  },

  get FIREBASE_MESSAGING_SENDER_ID() {
    return getEnv().EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID;
  },

  get FIREBASE_APP_ID() {
    return getEnv().EXPO_PUBLIC_FIREBASE_APP_ID;
  },

  get FIREBASE_WEB_CLIENT_ID() {
    return getEnv().EXPO_PUBLIC_FIREBASE_WEB_CLIENT_ID;
  },

  // Emulator configuration
  get USE_EMULATORS() {
    return getEnv().EXPO_PUBLIC_USE_EMULATORS;
  },

  get EMULATOR_AUTH() {
    return getEnv().EXPO_PUBLIC_FIREBASE_EMULATOR_AUTH;
  },

  get EMULATOR_FIRESTORE() {
    return getEnv().EXPO_PUBLIC_FIREBASE_EMULATOR_FIRESTORE;
  },

  get EMULATOR_STORAGE() {
    return getEnv().EXPO_PUBLIC_FIREBASE_EMULATOR_STORAGE;
  },

  get EMULATOR_FUNCTIONS() {
    return getEnv().EXPO_PUBLIC_FIREBASE_EMULATOR_FUNCTIONS;
  },

  // API configuration
  get STRIPE_PUBLISHABLE_KEY() {
    return getEnv().EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY;
  },

  // Development and debugging
  get DEBUG_MODE() {
    return getEnv().EXPO_PUBLIC_DEBUG_MODE;
  },  get LOG_LEVEL() {
    return getEnv().EXPO_PUBLIC_LOG_LEVEL;
  },

  // Auth refactor settings
  get AUTH_REFACTOR_ENABLED() {
    return getEnv().EXPO_PUBLIC_AUTH_REFACTOR_ENABLED;
  },

  get GOOGLE_TEST_EMAIL() {
    return getEnv().EXPO_PUBLIC_GOOGLE_TEST_EMAIL;
  },

  get GOOGLE_TEST_PASSWORD() {
    return getEnv().EXPO_PUBLIC_GOOGLE_TEST_PASSWORD;
  },

  // Analytics
  get POSTHOG_API_KEY() {
    return getEnv().EXPO_PUBLIC_POSTHOG_API_KEY;
  },

  get POSTHOG_HOST() {
    return getEnv().EXPO_PUBLIC_POSTHOG_HOST;
  },

  // Testing
  get ENABLE_TEST_ACCOUNTS() {
    return getEnv().EXPO_PUBLIC_ENABLE_TEST_ACCOUNTS;
  },

  get TEST_USER_EMAIL() {
    return getEnv().EXPO_PUBLIC_TEST_USER_EMAIL;
  },

  get TEST_USER_PASSWORD() {
    return getEnv().EXPO_PUBLIC_TEST_USER_PASSWORD;
  },
} as const;

/**
 * Force re-validation of environment variables
 * Useful for testing or when environment changes at runtime
 */
export function revalidateEnvironment(): void {
  validatedEnv = null;
  getEnv(); // This will trigger re-validation
}

/**
 * Check if we're running in development mode
 */
export const isDev = () => env.IS_DEV;/**
 * Check if we're running in production mode
 */
export const isProd = () => env.IS_PROD;

/**
 * Check if we're running in staging mode
 */
export const isStaging = () => env.IS_STAGING;

/**
 * Get Firebase configuration object for initialization
 */
export function getFirebaseConfig() {
  return {
    apiKey: env.FIREBASE_API_KEY,
    authDomain: env.FIREBASE_AUTH_DOMAIN,
    projectId: env.FIREBASE_PROJECT_ID,
    storageBucket: env.FIREBASE_STORAGE_BUCKET,
    messagingSenderId: env.FIREBASE_MESSAGING_SENDER_ID,
    appId: env.FIREBASE_APP_ID,
    webClientId: env.FIREBASE_WEB_CLIENT_ID,
  };
}

/**
 * Get emulator configuration
 */
export function getEmulatorConfig() {
  return {
    useEmulators: env.USE_EMULATORS,
    auth: env.EMULATOR_AUTH,
    firestore: env.EMULATOR_FIRESTORE,
    storage: env.EMULATOR_STORAGE,
    functions: env.EMULATOR_FUNCTIONS,
  };
}