import { Platform } from 'react-native';

/**
 * Applies fixes for LogBox error handling on web
 * This prevents transform-related styling errors in the LogBox component itself
 */
export function applyLogBoxFixes() {
  if (Platform.OS === 'web') {
    try {
      // Override YellowBox/LogBox styling for web platform
      // This prevents transform-related errors in error boundaries
      
      // Create a simple console logger for errors that won't trigger UI errors
      const originalError = console.error;
      console.error = (...args: any[]) => {
        // Filter out specific transform-related errors
        const errorText = args.join(' ');
        if (
          errorText.includes('CSSStyleDeclaration') ||
          errorText.includes('indexed property') ||
          errorText.includes('transform')
        ) {
          // Just log a simple version without the component stack
          console.log('[Error]', args[0]);
          return;
        }
        
        // Pass through other errors
        originalError.apply(console, args);
      };
      
      // Similarly override warning behavior
      const originalWarn = console.warn;
      console.warn = (...args: any[]) => {
        // Filter out specific transform-related warnings
        const warningText = args.join(' ');
        if (
          warningText.includes('CSSStyleDeclaration') ||
          warningText.includes('indexed property') ||
          warningText.includes('transform')
        ) {
          // Just log a simple version
          console.log('[Warning]', args[0]);
          return;
        }
        
        // Pass through other warnings
        originalWarn.apply(console, args);
      };
      
      console.log('[Debug] Applied LogBox fixes for web platform');
    } catch (err) {
      console.log('Failed to apply LogBox fixes:', err);
    }
  }
} 