// Firebase cleanup utility for development
// This helps resolve duplicate app errors during hot reloading

import { Platform } from 'react-native';

export function cleanupFirebaseApps() {
  if (__DEV__) {
    try {
      // Only import firebase if we're in dev mode
      const firebase = require('firebase/compat/app');
      
      if (firebase.apps && firebase.apps.length > 1) {
        console.log(`[firebase-cleanup] Found ${firebase.apps.length} Firebase apps, cleaning up duplicates...`);
        
        // Keep only the first app
        const appsToDelete = firebase.apps.slice(1);
        
        appsToDelete.forEach((app, index) => {
          try {
            app.delete();
            console.log(`[firebase-cleanup] Deleted duplicate app ${index + 1}`);
          } catch (deleteError) {
            console.warn(`[firebase-cleanup] Failed to delete app ${index + 1}:`, deleteError);
          }
        });
      }
    } catch (error) {
      // Silently fail - this is just a cleanup utility
      console.debug('[firebase-cleanup] Cleanup skipped:', error.message);
    }
  }
}

// Auto-cleanup on module load in development
if (__DEV__ && Platform.OS !== 'web') {
  cleanupFirebaseApps();
}