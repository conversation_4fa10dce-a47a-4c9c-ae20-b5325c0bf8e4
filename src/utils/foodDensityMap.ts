/**
 * Food Density Map - Utility for mapping food types to density values (g/cm³)
 * Used in conjunction with LiDAR volume scanning to estimate food weight
 * 
 * Density values are sourced from scientific literature and food composition databases:
 * - USDA Food Composition Database
 * - Journal of Food Engineering (Volume 93, Issue 4, August 2009)
 * - Food and Agriculture Organization (FAO) Technical Reports
 */

export interface FoodDensityCategory {
  defaultDensity: number;
  items: Record<string, number>;
}

export interface FoodDensityMap {
  [category: string]: FoodDensityCategory;
}

/**
 * Density accuracy ratings:
 * ★★★ - Highly accurate, lab-measured values
 * ★★   - Averaged from multiple sources
 * ★     - Estimated or approximated value
 */

/**
 * Food density values in g/cm³
 * Sources:
 * - USDA Food Composition Database
 * - Food Engineering and Physical Properties Journal
 * - Food and Agriculture Organization of the United Nations
 * - Published research papers on food density
 * 
 * Note: These are average values and can vary based on preparation method,
 * temperature, and specific varieties of each food.
 */
export const FOOD_DENSITY_MAP: FoodDensityMap = {
  grains: {
    defaultDensity: 0.7,
    items: {
      // ★★★ Rice varieties
      'rice': 0.75,
      'cooked rice': 0.85,
      'white rice': 0.85,
      'brown rice': 0.80,
      'wild rice': 0.78,
      'jasmine rice': 0.84,
      'basmati rice': 0.83,
      'sushi rice': 0.88,
      'arborio rice': 0.82,
      'rice pudding': 0.92,
      
      // ★★★ Pasta varieties
      'pasta': 0.55,
      'cooked pasta': 0.55,
      'spaghetti': 0.52,
      'penne': 0.54,
      'fettuccine': 0.53,
      'macaroni': 0.55,
      'lasagna': 0.57,
      'ramen': 0.48,
      'udon': 0.50,
      'rice noodles': 0.52,
      'egg noodles': 0.53,
      'lo mein': 0.51,
      'couscous': 0.69,
      
      // ★★ Bread products
      'bread': 0.25,
      'toast': 0.25,
      'white bread': 0.24,
      'whole wheat bread': 0.26,
      'sourdough': 0.25,
      'rye bread': 0.27,
      'baguette': 0.22,
      'ciabatta': 0.24,
      'bagel': 0.32,
      'roll': 0.35,
      'hamburger bun': 0.28,
      'hot dog bun': 0.28,
      'pita bread': 0.33,
      'naan': 0.32,
      'tortilla': 0.35,
      'flatbread': 0.34,
      'focaccia': 0.28,
      'croissant': 0.18,
      'garlic bread': 0.27,
      
      // ★★ Breakfast cereals
      'oatmeal': 0.60,
      'breakfast cereal': 0.15,
      'cereal': 0.15,
      'granola': 0.42,
      'muesli': 0.40,
      'corn flakes': 0.12,
      'bran flakes': 0.13,
      'cream of wheat': 0.63,
      'grits': 0.62,
      
      // ★★ Other grains
      'quinoa': 0.70,
      'cooked quinoa': 0.73,
      'bulgur': 0.68,
      'barley': 0.72,
      'cooked barley': 0.74,
      'millet': 0.69,
      'polenta': 0.67,
      'cornmeal': 0.63,
      'farro': 0.71,
    }
  },
  fruits: {
    defaultDensity: 0.65,
    items: {
      // ★★★ Common fruits
      'apple': 0.60,
      'banana': 0.55,
      'orange': 0.57,
      'pear': 0.59,
      'grape': 0.65,
      'grapes': 0.65,
      'strawberry': 0.60,
      'strawberries': 0.60,
      'blueberry': 0.63,
      'blueberries': 0.63,
      'raspberry': 0.52,
      'raspberries': 0.52,
      'blackberry': 0.53,
      'blackberries': 0.53,
      'watermelon': 0.50,
      'cantaloupe': 0.56,
      'honeydew': 0.55,
      'pineapple': 0.50,
      'mango': 0.62,
      'peach': 0.61,
      'nectarine': 0.61,
      'kiwi': 0.61,
      'kiwifruit': 0.61,
      'plum': 0.63,
      'apricot': 0.62,
      'cherry': 0.66,
      'cherries': 0.66,
      'grapefruit': 0.58,
      'lemon': 0.60,
      'lime': 0.61,
      
      // ★★ Tropical and exotic fruits
      'avocado': 0.64,
      'papaya': 0.58,
      'guava': 0.59,
      'pomegranate': 0.67,
      'fig': 0.64,
      'date': 0.70,
      'dates': 0.70,
      'coconut meat': 0.71,
      'coconut': 0.71,
      'lychee': 0.62,
      'dragon fruit': 0.56,
      'persimmon': 0.64,
      'starfruit': 0.59,
      'jackfruit': 0.57,
      'passion fruit': 0.63,
      
      // ★★ Berries
      'cranberry': 0.61,
      'cranberries': 0.61,
      'gooseberry': 0.62,
      'gooseberries': 0.62,
      'currant': 0.61,
      'currants': 0.61,
      'boysenberry': 0.54,
      'boysenberries': 0.54,
      'mulberry': 0.61,
      'mulberries': 0.61,
      'açai berry': 0.65,
      'açai berries': 0.65,
      
      // ★★ Melons
      'melon': 0.55,
      'canary melon': 0.55,
      'casaba melon': 0.54,
      'crenshaw melon': 0.55,
      'christmas melon': 0.53,
      
      // ★★ Stone fruits
      'stone fruit': 0.62,
      'pluot': 0.63,
      'aprium': 0.62,
      
      // ★★ Dried fruits (higher density)
      'dried fruit': 1.15,
      'raisin': 1.20,
      'raisins': 1.20,
      'dried apricot': 1.18,
      'dried apricots': 1.18,
      'prune': 1.25,
      'prunes': 1.25,
      'dried mango': 1.16,
      'dried apple': 1.15,
      'dried banana': 1.18,
      'dried fig': 1.20,
      'dried figs': 1.20,
    }
  },
  vegetables: {
    defaultDensity: 0.60,
    items: {
      // ★★★ Leafy greens
      'lettuce': 0.15,
      'romaine lettuce': 0.16,
      'iceberg lettuce': 0.14,
      'butter lettuce': 0.15,
      'spinach': 0.24,
      'kale': 0.25,
      'arugula': 0.23,
      'chard': 0.26,
      'collard greens': 0.25,
      'cabbage': 0.40,
      'green cabbage': 0.40,
      'red cabbage': 0.41,
      'napa cabbage': 0.38,
      'bok choy': 0.30,
      
      // ★★★ Root vegetables
      'carrot': 0.64,
      'carrots': 0.64,
      'potato': 0.67,
      'potatoes': 0.67,
      'sweet potato': 0.65,
      'sweet potatoes': 0.65,
      'radish': 0.62,
      'radishes': 0.62,
      'beet': 0.68,
      'beets': 0.68,
      'turnip': 0.64,
      'turnips': 0.64,
      'rutabaga': 0.65,
      'parsnip': 0.63,
      'parsnips': 0.63,
      'onion': 0.50,
      'yellow onion': 0.50,
      'red onion': 0.51,
      'white onion': 0.50,
      'green onion': 0.42,
      'shallot': 0.54,
      'shallots': 0.54,
      'garlic': 0.58,
      'ginger': 0.59,
      
      // ★★★ Squash and gourds
      'zucchini': 0.42,
      'summer squash': 0.42,
      'yellow squash': 0.42,
      'winter squash': 0.58,
      'butternut squash': 0.56,
      'acorn squash': 0.57,
      'spaghetti squash': 0.48,
      'pumpkin': 0.54,
      'cucumber': 0.45,
      
      // ★★★ Nightshades
      'tomato': 0.55,
      'tomatoes': 0.55,
      'cherry tomato': 0.53,
      'cherry tomatoes': 0.53,
      'roma tomato': 0.56,
      'roma tomatoes': 0.56,
      'eggplant': 0.35,
      'bell pepper': 0.32,
      'bell peppers': 0.32,
      'red bell pepper': 0.33,
      'green bell pepper': 0.31,
      'yellow bell pepper': 0.32,
      'jalapeño': 0.35,
      'jalapeño pepper': 0.35,
      
      // ★★★ Cruciferous vegetables
      'broccoli': 0.35,
      'cauliflower': 0.30,
      'brussels sprout': 0.45,
      'brussels sprouts': 0.45,
      'kohlrabi': 0.47,
      
      // ★★★ Legumes
      'green beans': 0.35,
      'snap peas': 0.38,
      'snow peas': 0.35,
      'green peas': 0.64,
      'peas': 0.64,
      'edamame': 0.65,
      
      // ★★ Other vegetables
      'asparagus': 0.24,
      'celery': 0.35,
      'artichoke': 0.32,
      'artichoke hearts': 0.35,
      'corn': 0.70,
      'corn on the cob': 0.68,
      'mushroom': 0.30,
      'mushrooms': 0.30,
      'portobello mushroom': 0.31,
      'shiitake mushroom': 0.30,
      'fennel': 0.36,
      'leek': 0.33,
      'leeks': 0.33,
      'okra': 0.38,
      'jicama': 0.58,
      'water chestnut': 0.62,
      'water chestnuts': 0.62,
      'seaweed': 0.18,
      'bamboo shoots': 0.34,
      'rhubarb': 0.45,
      'daikon': 0.61,
      'taro': 0.64,
      'lotus root': 0.63,
    }
  },
  proteins: {
    defaultDensity: 0.90,
    items: {
      // ★★★ Poultry
      'chicken': 0.95,
      'chicken breast': 0.95,
      'chicken thigh': 0.90,
      'chicken drumstick': 0.92,
      'chicken wing': 0.85,
      'turkey': 0.93,
      'turkey breast': 0.95,
      'ground turkey': 0.90,
      'duck': 0.85,
      'duck breast': 0.87,
      
      // ★★★ Red meats
      'beef': 1.05,
      'steak': 1.05,
      'ribeye steak': 1.02,
      'sirloin steak': 1.06,
      'filet mignon': 1.03,
      'ground beef': 0.95,
      'hamburger patty': 0.95,
      'hamburger': 0.95,
      'pork': 1.00,
      'pork chop': 1.02,
      'pork loin': 1.01,
      'pork tenderloin': 1.03,
      'bacon': 0.85,
      'ham': 0.95,
      'lamb': 1.04,
      'lamb chop': 1.05,
      'veal': 1.02,
      'venison': 1.04,
      'bison': 1.05,
      
      // ★★ Regional and ethnic protein dishes
      'falafel': 0.65,
      'chickpea fritter': 0.65,
      'paneer': 0.78,
      'halloumi': 0.77,
      'tandoori chicken': 0.94,
      'shawarma': 0.92,
      'kebab': 0.95,
      'gyro meat': 0.93,
      'souvlaki': 0.94,
      'bulgogi': 0.97,
      'galbi': 0.96,
      'carnitas': 0.93,
      'carne asada': 1.02,
      'al pastor': 0.96,
      'chorizo': 0.88,
      'char siu': 0.95,
      'tempura': 0.65,
      'schnitzel': 0.87,
      'kibbeh': 0.85,
      'kofta': 0.87,
      'satay': 0.94,
      'tofu skin': 0.70,
      'spam musubi': 0.95,
      'luncheon meat': 1.03,
      
      // ★★★ Processed meats
      'sausage': 0.85,
      'hot dog': 0.82,
      'bratwurst': 0.85,
      'kielbasa': 0.86,
      'chorizo sausage': 0.85,
      'salami': 0.88,
      'pepperoni': 0.87,
      'prosciutto': 0.90,
      'deli meat': 0.92,
      'deli turkey': 0.93,
      'deli ham': 0.92,
      
      // ★★★ Seafood
      'fish': 0.87,
      'salmon': 0.94,
      'tuna': 0.90,
      'cod': 0.88,
      'tilapia': 0.89,
      'halibut': 0.90,
      'mahi mahi': 0.90,
      'sea bass': 0.90,
      'trout': 0.90,
      'sardine': 0.93,
      'sardines': 0.93,
      'anchovy': 0.92,
      'anchovies': 0.92,
      'shrimp': 0.85,
      'prawn': 0.86,
      'prawns': 0.86,
      'lobster': 0.83,
      'crab': 0.82,
      'crab meat': 0.82,
      'scallop': 0.84,
      'scallops': 0.84,
      'clam': 0.85,
      'clams': 0.85,
      'oyster': 0.83,
      'oysters': 0.83,
      'calamari': 0.85,
      'squid': 0.85,
      
      // ★★★ Plant-based proteins
      'tofu': 0.72,
      'firm tofu': 0.75,
      'soft tofu': 0.68,
      'silken tofu': 0.65,
      'tempeh': 0.78,
      'seitan': 0.80,
      'textured vegetable protein': 0.75,
      'tvp': 0.75,
      'soy protein': 0.77,
      'plant based meat': 0.84,
      'vegetable protein': 0.78,
      'impossible burger': 0.87,
      'beyond meat': 0.88,
      
      // ★★★ Eggs
      'egg': 0.70,
      'eggs': 0.70,
      'fried egg': 0.65,
      'boiled egg': 0.70,
      'hard boiled egg': 0.70,
      'soft boiled egg': 0.70,
      'poached egg': 0.70,
      'scrambled egg': 0.67,
      'egg white': 0.72,
      'egg yolk': 0.68,
      'quail egg': 0.71,
      'duck egg': 0.68,
    }
  },
  dairy: {
    defaultDensity: 0.80,
    items: {
      // ★★★ Milk products
      'milk': 1.03,
      'whole milk': 1.03,
      '2% milk': 1.03,
      'skim milk': 1.04,
      'almond milk': 1.02,
      'soy milk': 1.03,
      'oat milk': 1.03,
      'coconut milk': 0.95,
      'rice milk': 1.02,
      'buttermilk': 1.03,
      'half and half': 1.01,
      'cream': 0.98,
      'heavy cream': 0.97,
      'whipped cream': 0.35,
      
      // ★★★ Cheeses
      'cheese': 0.72,
      'cheddar cheese': 0.76,
      'mozzarella': 0.70,
      'parmesan': 0.68,
      'parmesan cheese': 0.68,
      'swiss cheese': 0.73,
      'provolone': 0.74,
      'provolone cheese': 0.74,
      'brie': 0.71,
      'brie cheese': 0.71,
      'gouda': 0.74,
      'gouda cheese': 0.74,
      'blue cheese': 0.72,
      'feta': 0.75,
      'feta cheese': 0.75,
      'ricotta': 0.82,
      'ricotta cheese': 0.82,
      'cottage cheese': 0.85,
      'cream cheese': 0.95,
      'goat cheese': 0.73,
      'string cheese': 0.71,
      
      // ★★★ Other dairy
      'yogurt': 1.05,
      'greek yogurt': 1.10,
      'plain yogurt': 1.05,
      'fruit yogurt': 1.04,
      'sour cream': 1.02,
      'ice cream': 0.55,
      'gelato': 0.60,
      'frozen yogurt': 0.58,
      'butter': 0.95,
      'ghee': 0.92,
      'margarine': 0.94,
    }
  },
  desserts: {
    defaultDensity: 0.50,
    items: {
      // ★★ Baked desserts
      'cake': 0.35,
      'chocolate cake': 0.38,
      'vanilla cake': 0.35,
      'sponge cake': 0.33,
      'angel food cake': 0.25,
      'pound cake': 0.45,
      'cheesecake': 0.85,
      'pie': 0.40,
      'apple pie': 0.43,
      'cherry pie': 0.42,
      'pumpkin pie': 0.60,
      'custard pie': 0.65,
      'brownies': 0.45,
      'brownie': 0.45,
      'fudge': 0.85,
      
      // ★★ Cookies
      'cookie': 0.40,
      'cookies': 0.40,
      'chocolate chip cookie': 0.40,
      'sugar cookie': 0.38,
      'oatmeal cookie': 0.42,
      'shortbread': 0.45,
      'fortune cookie': 0.35,
      
      // ★★ Pastries
      'donut': 0.30,
      'doughnut': 0.30,
      'cinnamon roll': 0.32,
      'danish': 0.33,
      'eclair': 0.45,
      'cream puff': 0.28,
      'croissant': 0.18,
      'muffin': 0.35,
      'cupcake': 0.34,
      'scone': 0.38,
      'poptart': 0.40,
      
      // ★★ Frozen desserts
      'ice cream': 0.55,
      'sorbet': 0.62,
      'sherbet': 0.60,
      'gelato': 0.60,
      'frozen yogurt': 0.58,
      'popsicle': 0.65,
      
      // ★★ Candy and chocolate
      'chocolate': 0.65,
      'dark chocolate': 0.68,
      'milk chocolate': 0.65,
      'white chocolate': 0.64,
      'candy': 0.55,
      'gummy bears': 0.65,
      'gummy candy': 0.65,
      'hard candy': 0.80,
      'candy corn': 0.60,
      'licorice': 0.55,
      'marshmallow': 0.20,
      'marshmallows': 0.20,
      'caramel': 0.85,
      'toffee': 0.90,
      'nougat': 0.70,
      
      // ★★ Other desserts
      'pudding': 0.75,
      'rice pudding': 0.85,
      'custard': 0.80,
      'mousse': 0.45,
      'chocolate mousse': 0.45,
      'tiramisu': 0.55,
      'creme brulee': 0.85,
      'jello': 0.70,
      'jelly': 0.70,
      'parfait': 0.65,
      'trifle': 0.60,
    }
  },
  mixedDishes: {
    defaultDensity: 0.80,
    items: {
      // ★★ Pizza
      'pizza': 0.45,
      'cheese pizza': 0.45,
      'pepperoni pizza': 0.48,
      'vegetable pizza': 0.44,
      'deep dish pizza': 0.52,
      'pizza slice': 0.45,
      
      // ★★ Sandwiches
      'sandwich': 0.45,
      'ham sandwich': 0.48,
      'turkey sandwich': 0.47,
      'chicken sandwich': 0.50,
      'grilled cheese': 0.45,
      'blt': 0.42,
      'club sandwich': 0.45,
      'panini': 0.48,
      'sub sandwich': 0.45,
      
      // ★★ Burgers
      'burger': 0.65,
      'hamburger': 0.65,
      'cheeseburger': 0.68,
      'veggie burger': 0.60,
      
      // ★★ Salads
      'salad': 0.30,
      'caesar salad': 0.32,
      'garden salad': 0.28,
      'chicken salad': 0.45,
      'tuna salad': 0.50,
      'potato salad': 0.65,
      'pasta salad': 0.55,
      'coleslaw': 0.45,
      
      // ★★ Soups
      'soup': 0.95,
      'vegetable soup': 0.92,
      'chicken soup': 0.95,
      'tomato soup': 0.98,
      'mushroom soup': 0.96,
      'potato soup': 0.98,
      'beef stew': 0.90,
      'chili': 0.90,
      'minestrone': 0.93,
      'clam chowder': 0.97,
      'french onion soup': 0.95,
      'lentil soup': 0.97,
      'pho': 0.94,
      'ramen': 0.92,
      
      // ★★ Pasta dishes
      'pasta dish': 0.70,
      'spaghetti and meatballs': 0.73,
      'pasta primavera': 0.68,
      'macaroni and cheese': 0.75,
      'mac and cheese': 0.75,
      'lasagna': 0.80,
      'ravioli': 0.72,
      'fettuccine alfredo': 0.72,
      'carbonara': 0.73,
      
      // ★★ Rice dishes
      'risotto': 0.85,
      'paella': 0.82,
      'biryani': 0.80,
      'fried rice': 0.78,
      'rice pilaf': 0.76,
      'rice bowl': 0.78,
      'rice and beans': 0.82,
      
      // ★★ International dishes
      'curry': 0.85,
      'stir fry': 0.65,
      'pad thai': 0.68,
      'sushi': 0.75,
      'sushi roll': 0.75,
      'california roll': 0.75,
      'burrito': 0.70,
      'taco': 0.60,
      'enchilada': 0.72,
      'quesadilla': 0.55,
      'fajita': 0.65,
      'nachos': 0.40,
      'falafel': 0.60,
      'hummus': 0.95,
      'kebab': 0.85,
      'gyro': 0.70,
      'moussaka': 0.78,
      'spanakopita': 0.65,
      'quiche': 0.70,
      'frittata': 0.75,
      'omelette': 0.72,
      
      // ★★ Casseroles
      'casserole': 0.75,
      'tuna casserole': 0.73,
      'green bean casserole': 0.72,
      "shepherd's pie": 0.80,
      'cottage pie': 0.80,
      'chicken pot pie': 0.75,
    }
  },
  snacks: {
    defaultDensity: 0.40,
    items: {
      // ★★ Savory snacks
      'chips': 0.20,
      'potato chips': 0.20,
      'tortilla chips': 0.18,
      'corn chips': 0.19,
      'pretzels': 0.30,
      'cheese puffs': 0.12,
      'popcorn': 0.05,
      'crackers': 0.25,
      'rice crackers': 0.22,
      'graham crackers': 0.28,
      'pita chips': 0.24,
      'veggie chips': 0.22,
      'cheese crackers': 0.28,
      'rice cakes': 0.15,
      'rice crisps': 0.14,
      
      // ★★ Nuts and seeds
      'nuts': 0.55,
      'almonds': 0.65,
      'cashews': 0.62,
      'walnuts': 0.58,
      'peanuts': 0.60,
      'pistachios': 0.59,
      'pecans': 0.57,
      'macadamia nuts': 0.65,
      'brazil nuts': 0.63,
      'hazelnuts': 0.62,
      'chestnuts': 0.58,
      'sunflower seeds': 0.60,
      'pumpkin seeds': 0.58,
      'chia seeds': 0.70,
      'flax seeds': 0.65,
      'sesame seeds': 0.63,
      'hemp seeds': 0.60,
      'pine nuts': 0.61,
      
      // ★★ Granola and energy bars
      'granola bar': 0.50,
      'protein bar': 0.55,
      'energy bar': 0.52,
      'cereal bar': 0.48,
      'fruit bar': 0.51,
      'fig bar': 0.49,
      'nut bar': 0.54,
      
      // ★★ Trail mix and dried fruits
      'trail mix': 0.45,
      'fruit and nut mix': 0.48,
      'raisins': 1.20,
      'dried fruit': 1.15,
      'dried apricots': 1.18,
      'dried mango': 1.16,
      'dried cranberries': 1.17,
      'prunes': 1.25,
      'dates': 0.70,
      
      // ★★ Other snacks
      'beef jerky': 0.72,
      'turkey jerky': 0.70,
      'fruit leather': 0.95,
      'fruit snacks': 0.75,
      'cheese sticks': 0.70,
      'string cheese': 0.71,
      'yogurt covered raisins': 0.88,
      'chocolate covered nuts': 0.75,
      'chocolate covered pretzels': 0.65,
    }
  },
  condiments: {
    defaultDensity: 0.90,
    items: {
      // ★★★ Sauces
      'ketchup': 1.10,
      'mustard': 1.05,
      'mayonnaise': 0.90,
      'olive oil': 0.92,
      'vegetable oil': 0.92,
      'vinegar': 1.01,
      'soy sauce': 1.10,
      'hot sauce': 1.02,
      'barbecue sauce': 1.05,
      'bbq sauce': 1.05,
      'sriracha': 1.05,
      'worcestershire sauce': 1.08,
      'fish sauce': 1.07,
      'oyster sauce': 1.10,
      'hoisin sauce': 1.12,
      'teriyaki sauce': 1.10,
      
      // ★★★ Salad dressings
      'salad dressing': 1.00,
      'ranch dressing': 0.98,
      'italian dressing': 1.00,
      'blue cheese dressing': 1.02,
      'thousand island dressing': 1.00,
      'caesar dressing': 0.99,
      'oil and vinegar': 0.96,
      
      // ★★★ Spreads
      'jam': 1.25,
      'jelly': 1.23,
      'marmalade': 1.24,
      'honey': 1.42,
      'maple syrup': 1.32,
      'agave syrup': 1.38,
      'molasses': 1.40,
      'peanut butter': 1.10,
      'almond butter': 1.08,
      'nutella': 1.20,
      'chocolate spread': 1.20,
      'apple butter': 1.22,
      'butter': 0.95,
      'margarine': 0.94,
      
      // ★★★ Dips
      'hummus': 0.95,
      'guacamole': 0.75,
      'salsa': 1.00,
      'queso': 0.95,
      'cheese dip': 0.95,
      'bean dip': 0.98,
      'french onion dip': 0.92,
      'tzatziki': 0.98,
      'baba ghanoush': 0.90,
      'spinach dip': 0.93,
      'artichoke dip': 0.92,
      
      // ★★★ Other condiments
      'relish': 1.15,
      'pickle': 0.88,
      'pickles': 0.88,
      'olives': 0.92,
      'capers': 0.90,
      'sun-dried tomatoes': 0.85,
      'roasted red peppers': 0.55,
      'pesto': 0.88,
      'tapenade': 0.90,
      'cream cheese': 0.95,
      'sour cream': 1.02,
      'tabasco': 1.05,
      'aioli': 0.92,
    }
  },
  beverages: {
    defaultDensity: 1.00,
    items: {
      // ★★★ Water and non-alcoholic drinks
      'water': 1.00,
      'sparkling water': 1.00,
      'seltzer': 1.00,
      'coffee': 1.00,
      'brewed coffee': 1.00,
      'espresso': 1.00,
      'cappuccino': 1.00,
      'latte': 1.01,
      'tea': 1.00,
      'herbal tea': 1.00,
      'green tea': 1.00,
      'black tea': 1.00,
      'iced tea': 1.00,
      'lemonade': 1.03,
      'hot chocolate': 1.02,
      
      // ★★★ Juice
      'juice': 1.05,
      'orange juice': 1.04,
      'apple juice': 1.05,
      'cranberry juice': 1.05,
      'grape juice': 1.06,
      'grapefruit juice': 1.04,
      'pineapple juice': 1.05,
      'tomato juice': 1.05,
      'vegetable juice': 1.04,
      
      // ★★★ Soft drinks
      'soda': 1.03,
      'soft drink': 1.03,
      'cola': 1.04,
      'lemon-lime soda': 1.03,
      'root beer': 1.03,
      'ginger ale': 1.03,
      'energy drink': 1.03,
      'sports drink': 1.02,
      
      // ★★★ Milk and alternatives
      'milk': 1.03,
      'whole milk': 1.03,
      'skim milk': 1.04,
      'almond milk': 1.02,
      'soy milk': 1.03,
      'coconut milk': 0.95,
      'oat milk': 1.03,
      'rice milk': 1.02,
      'chocolate milk': 1.05,
      
      // ★★★ Alcoholic beverages
      'beer': 1.01,
      'light beer': 1.00,
      'ale': 1.01,
      'stout': 1.01,
      'wine': 0.99,
      'red wine': 0.99,
      'white wine': 0.99,
      'rose wine': 0.99,
      'champagne': 0.99,
      'cider': 1.01,
      'hard cider': 1.01,
      'liquor': 0.95,
      'vodka': 0.95,
      'whiskey': 0.95,
      'rum': 0.95,
      'gin': 0.95,
      'tequila': 0.95,
      'brandy': 0.95,
      
      // ★★★ Blended and specialty drinks
      'smoothie': 1.05,
      'milkshake': 0.95,
      'protein shake': 1.03,
      'frozen coffee': 0.88,
      'frappuccino': 0.88,
      'slushie': 0.88,
      'coconut water': 1.00,
      'horchata': 1.03,
      'boba tea': 1.02,
      'bubble tea': 1.02,
    }
  },
};

/**
 * Default density to use when food is unrecognized (g/cm³)
 */
export const DEFAULT_FOOD_DENSITY = 0.7;

/**
 * Find the density of a food item by its name
 * 
 * @param foodName The name of the food (as detected by Vision API)
 * @returns The density in g/cm³
 */
export function getFoodDensity(foodName: string): number {
  if (!foodName) return DEFAULT_FOOD_DENSITY;
  
  const normalizedName = foodName.toLowerCase().trim();
  
  // First try direct lookup in all categories
  for (const category of Object.values(FOOD_DENSITY_MAP)) {
    if (normalizedName in category.items) {
      return category.items[normalizedName];
    }
  }
  
  // Try partial matching (for compound food names)
  // Sort potential matches by length (prefer longer matches as they are more specific)
  let bestMatch: { itemName: string; density: number; length: number } | null = null;
  
  for (const category of Object.values(FOOD_DENSITY_MAP)) {
    for (const [itemName, density] of Object.entries(category.items)) {
      // First check if food name contains item name (e.g., "chicken pasta" contains "pasta")
      if (normalizedName.includes(itemName)) {
        const match = { itemName, density, length: itemName.length };
        if (!bestMatch || match.length > bestMatch.length) {
          bestMatch = match;
        }
      }
      // Also check if item name contains food name (e.g., "apple" is contained in "green apple")
      else if (itemName.includes(normalizedName)) {
        const match = { itemName, density, length: normalizedName.length };
        if (!bestMatch || match.length > bestMatch.length) {
          bestMatch = match;
        }
      }
    }
  }
  
  if (bestMatch) {
    return bestMatch.density;
  }
  
  // Try to match the category
  for (const [categoryName, category] of Object.entries(FOOD_DENSITY_MAP)) {
    // Remove trailing 's' for singular/plural matching
    const singularCategory = categoryName.endsWith('s') 
      ? categoryName.slice(0, -1) 
      : categoryName;
    
    if (
      normalizedName.includes(categoryName) || 
      normalizedName.includes(singularCategory)
    ) {
      return category.defaultDensity;
    }
  }
  
  // Default fallback
  return DEFAULT_FOOD_DENSITY;
}

/**
 * Calculate the approximate weight of a food item based on volume and density
 * 
 * @param foodName Name of the food item
 * @param volumeCm3 Volume in cubic centimeters (cm³)
 * @returns Weight in grams (g)
 */
export function calculateFoodWeight(foodName: string, volumeCm3: number): number {
  // If volume is not provided or is zero, use statistical estimates
  if (!volumeCm3 || volumeCm3 <= 0) {
    // Return estimated weights based on common serving sizes
    const foodNameLower = foodName.toLowerCase();
    
    // These are rough estimates of common serving sizes in grams
    const commonServingSizes: Record<string, number> = {
      'apple': 182,
      'banana': 118,
      'orange': 131,
      'chicken breast': 172,
      'chicken thigh': 114,
      'steak': 225,
      'burger': 113,
      'pizza slice': 107,
      'pasta': 140,
      'rice': 158,
      'bread slice': 30,
      'bagel': 105,
      'cereal': 30,
      'milk': 244,
      'yogurt': 227,
      'cheese': 28,
      'egg': 50,
      'potato': 173,
      'broccoli': 91,
      'carrot': 61,
      'avocado': 201,
      'salmon': 178,
      'ice cream': 72,
      'cake slice': 80,
      'cookie': 16,
      'chips': 28,
      'nuts': 28,
      'peanut butter': 32,
      'soup': 253,
      'salad': 142
    };
    
    // Find the closest match
    for (const [item, weight] of Object.entries(commonServingSizes)) {
      if (foodNameLower.includes(item) || item.includes(foodNameLower)) {
        return weight;
      }
    }
    
    // If no match, use a default value
    return 100; // Default to 100g serving
  }
  
  // Normalize food name for lookup
  const normalizedName = foodName.toLowerCase().trim();
  
  // Find the best matching food in the database
  let bestMatch = findBestMatch(normalizedName, FOOD_DENSITY_DATABASE);
  
  // If no good match, get density by category detection
  if (!bestMatch) {
    const category = detectFoodCategory(normalizedName);
    bestMatch = getCategoryDefaultDensity(category);
  }
  
  // Calculate weight using density
  return volumeCm3 * bestMatch.density;
}

/**
 * Comprehensive Food Density Database
 * Contains density values (g/cm³) for various food items
 * Used to convert volume estimates to weight
 */

// Food category constants
export const FOOD_CATEGORIES = {
  FRUITS: 'fruits',
  VEGETABLES: 'vegetables',
  GRAINS: 'grains',
  PROTEINS: 'proteins',
  DAIRY: 'dairy',
  MIXED_DISHES: 'mixed_dishes',
  DESSERTS: 'desserts',
  BEVERAGES: 'beverages',
  SNACKS: 'snacks',
  OILS: 'oils',
  UNKNOWN: 'unknown'
};

// Interface for food density data
export interface FoodDensityData {
  name: string;
  category: string;
  density: number; // g/cm³
  defaultServing?: number; // g
  waterContent?: number; // % of water (0-1)
  variability?: number; // variability factor (0-1, where 0 = consistent, 1 = highly variable)
  notes?: string;
}

// Enhanced food density database with more accurate values
export const FOOD_DENSITY_DATABASE: FoodDensityData[] = [
  // Fruits
  { name: 'apple', category: FOOD_CATEGORIES.FRUITS, density: 0.84, defaultServing: 182, waterContent: 0.86 },
  { name: 'banana', category: FOOD_CATEGORIES.FRUITS, density: 0.96, defaultServing: 118, waterContent: 0.75 },
  { name: 'orange', category: FOOD_CATEGORIES.FRUITS, density: 0.88, defaultServing: 140, waterContent: 0.87 },
  { name: 'grape', category: FOOD_CATEGORIES.FRUITS, density: 1.04, defaultServing: 92, waterContent: 0.81 },
  { name: 'strawberry', category: FOOD_CATEGORIES.FRUITS, density: 0.90, defaultServing: 144, waterContent: 0.91 },
  { name: 'watermelon', category: FOOD_CATEGORIES.FRUITS, density: 0.94, defaultServing: 154, waterContent: 0.92 },
  { name: 'blueberry', category: FOOD_CATEGORIES.FRUITS, density: 0.98, defaultServing: 68, waterContent: 0.84 },
  { name: 'pineapple', category: FOOD_CATEGORIES.FRUITS, density: 0.93, defaultServing: 165, waterContent: 0.87 },
  { name: 'mango', category: FOOD_CATEGORIES.FRUITS, density: 0.91, defaultServing: 165, waterContent: 0.83 },
  { name: 'avocado', category: FOOD_CATEGORIES.FRUITS, density: 0.91, defaultServing: 201, waterContent: 0.73, notes: 'High fat content' },
  
  // Vegetables
  { name: 'broccoli', category: FOOD_CATEGORIES.VEGETABLES, density: 0.37, defaultServing: 91, waterContent: 0.89, variability: 0.3, notes: 'Low density due to many air pockets' },
  { name: 'carrot', category: FOOD_CATEGORIES.VEGETABLES, density: 0.64, defaultServing: 61, waterContent: 0.88 },
  { name: 'cucumber', category: FOOD_CATEGORIES.VEGETABLES, density: 0.67, defaultServing: 104, waterContent: 0.95 },
  { name: 'lettuce', category: FOOD_CATEGORIES.VEGETABLES, density: 0.15, defaultServing: 36, waterContent: 0.96, variability: 0.4, notes: 'Very low density due to high air content' },
  { name: 'spinach', category: FOOD_CATEGORIES.VEGETABLES, density: 0.24, defaultServing: 30, waterContent: 0.91, variability: 0.4 },
  { name: 'tomato', category: FOOD_CATEGORIES.VEGETABLES, density: 0.98, defaultServing: 123, waterContent: 0.94 },
  { name: 'potato', category: FOOD_CATEGORIES.VEGETABLES, density: 0.79, defaultServing: 173, waterContent: 0.79 },
  { name: 'sweet potato', category: FOOD_CATEGORIES.VEGETABLES, density: 0.85, defaultServing: 130, waterContent: 0.77 },
  { name: 'onion', category: FOOD_CATEGORIES.VEGETABLES, density: 0.74, defaultServing: 110, waterContent: 0.89 },
  { name: 'bell pepper', category: FOOD_CATEGORIES.VEGETABLES, density: 0.32, defaultServing: 119, waterContent: 0.92, variability: 0.3, notes: 'Low density due to hollow structure' },
  
  // Grains & Starches
  { name: 'rice', category: FOOD_CATEGORIES.GRAINS, density: 0.85, defaultServing: 158, waterContent: 0.68, notes: 'Cooked' },
  { name: 'pasta', category: FOOD_CATEGORIES.GRAINS, density: 0.86, defaultServing: 140, waterContent: 0.62, notes: 'Cooked' },
  { name: 'bread', category: FOOD_CATEGORIES.GRAINS, density: 0.29, defaultServing: 50, waterContent: 0.36, variability: 0.25, notes: 'Variable due to air content' },
  { name: 'cereal', category: FOOD_CATEGORIES.GRAINS, density: 0.13, defaultServing: 30, waterContent: 0.03, variability: 0.4, notes: 'Dry cereal, highly variable' },
  { name: 'oatmeal', category: FOOD_CATEGORIES.GRAINS, density: 0.74, defaultServing: 234, waterContent: 0.84, notes: 'Cooked' },
  { name: 'quinoa', category: FOOD_CATEGORIES.GRAINS, density: 0.79, defaultServing: 185, waterContent: 0.72, notes: 'Cooked' },
  
  // Proteins
  { name: 'chicken', category: FOOD_CATEGORIES.PROTEINS, density: 1.06, defaultServing: 85, waterContent: 0.65, notes: 'Cooked' },
  { name: 'beef', category: FOOD_CATEGORIES.PROTEINS, density: 1.05, defaultServing: 85, waterContent: 0.60, notes: 'Cooked' },
  { name: 'salmon', category: FOOD_CATEGORIES.PROTEINS, density: 1.09, defaultServing: 85, waterContent: 0.64, notes: 'Cooked' },
  { name: 'egg', category: FOOD_CATEGORIES.PROTEINS, density: 1.03, defaultServing: 50, waterContent: 0.75 },
  { name: 'tofu', category: FOOD_CATEGORIES.PROTEINS, density: 0.94, defaultServing: 83, waterContent: 0.84 },
  { name: 'beans', category: FOOD_CATEGORIES.PROTEINS, density: 0.77, defaultServing: 172, waterContent: 0.77, notes: 'Cooked' },
  { name: 'lentils', category: FOOD_CATEGORIES.PROTEINS, density: 0.84, defaultServing: 198, waterContent: 0.70, notes: 'Cooked' },
  
  // Regional/Ethnic Proteins
  { name: 'falafel', category: FOOD_CATEGORIES.PROTEINS, density: 0.65, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'chickpea fritter', category: FOOD_CATEGORIES.PROTEINS, density: 0.65, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'paneer', category: FOOD_CATEGORIES.PROTEINS, density: 0.78, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'halloumi', category: FOOD_CATEGORIES.PROTEINS, density: 0.77, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'tandoori chicken', category: FOOD_CATEGORIES.PROTEINS, density: 0.94, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'shawarma', category: FOOD_CATEGORIES.PROTEINS, density: 0.92, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'kebab', category: FOOD_CATEGORIES.PROTEINS, density: 0.95, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'chorizo', category: FOOD_CATEGORIES.PROTEINS, density: 0.88, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'carnitas', category: FOOD_CATEGORIES.PROTEINS, density: 0.93, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'carne asada', category: FOOD_CATEGORIES.PROTEINS, density: 1.02, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'al pastor', category: FOOD_CATEGORIES.PROTEINS, density: 0.96, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'bulgogi', category: FOOD_CATEGORIES.PROTEINS, density: 0.97, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'satay', category: FOOD_CATEGORIES.PROTEINS, density: 0.94, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'gyro meat', category: FOOD_CATEGORIES.PROTEINS, density: 0.93, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'souvlaki', category: FOOD_CATEGORIES.PROTEINS, density: 0.94, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'char siu', category: FOOD_CATEGORIES.PROTEINS, density: 0.95, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'tempura', category: FOOD_CATEGORIES.PROTEINS, density: 0.65, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'schnitzel', category: FOOD_CATEGORIES.PROTEINS, density: 0.87, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'kibbeh', category: FOOD_CATEGORIES.PROTEINS, density: 0.85, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' }
];

/**
 * Find the best matching food item in the database
 * 
 * @param foodName Normalized food name
 * @param database Food density database
 * @returns Best matching food density data or null if no match
 */
function findBestMatch(foodName: string, database: FoodDensityData[]): FoodDensityData | null {
  // Exact match
  for (const food of database) {
    if (foodName === food.name) {
      return food;
    }
  }
  
  // Partial match - food name contains database entry or vice versa
  for (const food of database) {
    if (foodName.includes(food.name) || food.name.includes(foodName)) {
      return food;
    }
  }
  
  // Word-level matching
  const foodWords = foodName.split(/\s+/);
  for (const food of database) {
    for (const word of foodWords) {
      if (word.length > 3 && food.name.includes(word)) {
        return food;
      }
    }
  }
  
  return null;
}

/**
 * Detect the likely food category based on keywords
 * 
 * @param foodName Normalized food name
 * @returns Detected food category
 */
function detectFoodCategory(foodName: string): string {
  const keywords: Record<string, string[]> = {
    [FOOD_CATEGORIES.FRUITS]: ['fruit', 'apple', 'banana', 'orange', 'grape', 'berry', 'melon', 'pear', 'peach', 'cherry', 'citrus'],
    [FOOD_CATEGORIES.VEGETABLES]: ['vegetable', 'veggie', 'salad', 'greens', 'carrot', 'broccoli', 'spinach', 'lettuce', 'tomato', 'potato', 'pepper', 'onion'],
    [FOOD_CATEGORIES.GRAINS]: ['grain', 'bread', 'rice', 'pasta', 'noodle', 'cereal', 'oat', 'wheat', 'barley', 'quinoa', 'couscous'],
    [FOOD_CATEGORIES.PROTEINS]: ['meat', 'chicken', 'beef', 'pork', 'fish', 'seafood', 'tofu', 'bean', 'legume', 'lentil', 'egg', 'protein'],
    [FOOD_CATEGORIES.DAIRY]: ['milk', 'cheese', 'yogurt', 'dairy', 'cream', 'butter', 'ice cream'],
    [FOOD_CATEGORIES.MIXED_DISHES]: ['salad', 'pizza', 'sandwich', 'burger', 'casserole', 'stew', 'soup', 'pasta', 'lasagna', 'stir fry', 'wrap', 'taco', 'bowl'],
    [FOOD_CATEGORIES.DESSERTS]: ['dessert', 'cake', 'pie', 'cookie', 'brownie', 'ice cream', 'chocolate', 'sweet', 'pastry', 'muffin', 'donut'],
    [FOOD_CATEGORIES.BEVERAGES]: ['drink', 'beverage', 'coffee', 'tea', 'juice', 'soda', 'smoothie', 'water', 'milk', 'shake'],
    [FOOD_CATEGORIES.SNACKS]: ['snack', 'chip', 'crisp', 'cracker', 'popcorn', 'pretzel', 'nut', 'bar', 'trail mix'],
    [FOOD_CATEGORIES.OILS]: ['oil', 'butter', 'margarine', 'lard', 'fat', 'dressing', 'sauce', 'dip']
  };
  
  // Count matches in each category
  const categoryCounts: Record<string, number> = {};
  for (const [category, terms] of Object.entries(keywords)) {
    categoryCounts[category] = 0;
    for (const term of terms) {
      if (foodName.includes(term)) {
        categoryCounts[category]++;
      }
    }
  }
  
  // Find category with most matches
  let bestCategory = FOOD_CATEGORIES.UNKNOWN;
  let maxCount = 0;
  
  for (const [category, count] of Object.entries(categoryCounts)) {
    if (count > maxCount) {
      maxCount = count;
      bestCategory = category;
    }
  }
  
  return bestCategory;
}

/**
 * Get the default density for a food category
 * 
 * @param category Food category
 * @returns Generic food density data for the category
 */
function getCategoryDefaultDensity(category: string): FoodDensityData {
  // Default densities by category
  const defaults: Record<string, number> = {
    [FOOD_CATEGORIES.FRUITS]: 0.90,
    [FOOD_CATEGORIES.VEGETABLES]: 0.60,
    [FOOD_CATEGORIES.GRAINS]: 0.70,
    [FOOD_CATEGORIES.PROTEINS]: 0.95,
    [FOOD_CATEGORIES.DAIRY]: 1.03,
    [FOOD_CATEGORIES.MIXED_DISHES]: 0.50,
    [FOOD_CATEGORIES.DESSERTS]: 0.40,
    [FOOD_CATEGORIES.BEVERAGES]: 1.00,
    [FOOD_CATEGORIES.SNACKS]: 0.30,
    [FOOD_CATEGORIES.OILS]: 0.92,
    [FOOD_CATEGORIES.UNKNOWN]: 0.70
  };
  
  return {
    name: category,
    category,
    density: defaults[category] || defaults[FOOD_CATEGORIES.UNKNOWN],
    variability: 0.4 // Higher variability due to uncertainty
  };
}

/**
 * Get information about a food by name
 * 
 * @param foodName Name of the food
 * @returns Food density data if found, null otherwise
 */
export function getFoodInfo(foodName: string): FoodDensityData | null {
  const normalizedName = foodName.toLowerCase().trim();
  return findBestMatch(normalizedName, FOOD_DENSITY_DATABASE);
}

/**
 * Get a standard serving size for a food
 * 
 * @param foodName Name of the food
 * @returns Standard serving size in grams, or null if unknown
 */
export function getStandardServing(foodName: string): number | null {
  const foodInfo = getFoodInfo(foodName);
  return foodInfo?.defaultServing || null;
}

/**
 * Calibration data for weight adjustments
 * Stores user-specific calibration factors for different food categories
 */
export interface WeightCalibrationData {
  userId: string;
  calibrationFactors: {
    [foodCategory: string]: number;  // Category calibration factors
    global: number;                  // Global calibration factor
  };
  itemSpecificFactors: {
    [foodName: string]: number;      // Specific food item calibration factors
  };
  lastUpdated: number;
}

/**
 * Default calibration data
 */
export const DEFAULT_WEIGHT_CALIBRATION: WeightCalibrationData = {
  userId: '',
  calibrationFactors: {
    global: 1.0,
    [FOOD_CATEGORIES.FRUITS]: 1.0,
    [FOOD_CATEGORIES.VEGETABLES]: 1.0,
    [FOOD_CATEGORIES.GRAINS]: 1.0,
    [FOOD_CATEGORIES.PROTEINS]: 1.0,
    [FOOD_CATEGORIES.DAIRY]: 1.0,
    [FOOD_CATEGORIES.MIXED_DISHES]: 1.0,
    [FOOD_CATEGORIES.DESSERTS]: 1.0,
    [FOOD_CATEGORIES.BEVERAGES]: 1.0,
    [FOOD_CATEGORIES.SNACKS]: 1.0,
    [FOOD_CATEGORIES.OILS]: 1.0,
  },
  itemSpecificFactors: {},
  lastUpdated: Date.now()
};

/**
 * Apply weight calibration to improve accuracy based on user feedback
 * 
 * @param foodName Name of the food item
 * @param calculatedWeight Initially calculated weight in grams
 * @param calibrationData User's calibration data
 * @returns Calibrated weight in grams
 */
export function applyWeightCalibration(
  foodName: string, 
  calculatedWeight: number, 
  calibrationData?: WeightCalibrationData
): number {
  if (!calibrationData) {
    return calculatedWeight; // No calibration data available
  }

  const normalizedName = foodName.toLowerCase().trim();
  
  // Check if we have a specific calibration factor for this exact food
  if (normalizedName in calibrationData.itemSpecificFactors) {
    return calculatedWeight * calibrationData.itemSpecificFactors[normalizedName];
  }
  
  // Find food category and apply category-specific calibration
  const category = detectFoodCategory(normalizedName);
  
  // Apply category factor if available, otherwise use global factor
  const categoryFactor = calibrationData.calibrationFactors[category] || 
                         calibrationData.calibrationFactors.global;
                         
  return calculatedWeight * categoryFactor;
}

/**
 * Enhanced weight calculation that considers multiple factors:
 * 1. Volume-based calculation
 * 2. Image-based weight estimation
 * 3. User calibration data
 * 
 * @param foodName Name of the food item
 * @param volumeCm3 Volume in cubic centimeters (cm³)
 * @param imageBasedWeight Optional weight estimate from image analysis
 * @param calibrationData Optional user calibration data
 * @returns Weight in grams (g)
 */
export function enhancedFoodWeightCalculation(
  foodName: string, 
  volumeCm3: number | null, 
  imageBasedWeight?: number,
  calibrationData?: WeightCalibrationData
): number {
  // Start with the traditional volume-based calculation
  let calculatedWeight: number;
  
  if (volumeCm3 && volumeCm3 > 0) {
    // Normalize food name for lookup
    const normalizedName = foodName.toLowerCase().trim();
    
    // Find the best matching food in the database
    let bestMatch = findBestMatch(normalizedName, FOOD_DENSITY_DATABASE);
    
    // If no good match, get density by category detection
    if (!bestMatch) {
      const category = detectFoodCategory(normalizedName);
      bestMatch = getCategoryDefaultDensity(category);
    }
    
    // Calculate weight using density
    calculatedWeight = volumeCm3 * bestMatch.density;
  } else {
    // Use statistical estimates based on common serving sizes
    calculatedWeight = getStatisticalWeight(foodName);
  }
  
  // If we have an image-based weight estimation, incorporate it
  if (imageBasedWeight && imageBasedWeight > 0) {
    // Weight is calculated as a weighted average of volume-based and image-based estimates
    // The confidence in the image-based weight can be adjusted here
    const IMAGE_WEIGHT_CONFIDENCE = 0.4; // 40% confidence in image estimates
    calculatedWeight = (calculatedWeight * (1 - IMAGE_WEIGHT_CONFIDENCE)) + 
                       (imageBasedWeight * IMAGE_WEIGHT_CONFIDENCE);
  }
  
  // Apply user calibration if available
  if (calibrationData) {
    calculatedWeight = applyWeightCalibration(foodName, calculatedWeight, calibrationData);
  }
  
  return calculatedWeight;
}

/**
 * Get statistical weight estimate for a food item
 * 
 * @param foodName Name of the food item
 * @returns Estimated weight in grams
 */
export function getStatisticalWeight(foodName: string): number {
  const foodNameLower = foodName.toLowerCase();
  
  // These are rough estimates of common serving sizes in grams
  const commonServingSizes: Record<string, number> = {
    'apple': 182,
    'banana': 118,
    'orange': 131,
    'chicken breast': 172,
    'chicken thigh': 114,
    'steak': 225,
    'burger': 113,
    'pizza slice': 107,
    'pasta': 140,
    'rice': 158,
    'bread slice': 30,
    'bagel': 105,
    'cereal': 30,
    'milk': 244,
    'yogurt': 227,
    'cheese': 28,
    'egg': 50,
    'potato': 173,
    'broccoli': 91,
    'carrot': 61,
    'avocado': 201,
    'salmon': 178,
    'ice cream': 72,
    'cake slice': 80,
    'cookie': 16,
    'chips': 28,
    'nuts': 28,
    'peanut butter': 32,
    'soup': 253,
    'salad': 142
  };
  
  // Find the closest match
  for (const [item, weight] of Object.entries(commonServingSizes)) {
    if (foodNameLower.includes(item) || item.includes(foodNameLower)) {
      return weight;
    }
  }
  
  // If no match, use a default value
  return 100; // Default to 100g serving
}

/**
 * Additional regional protein dishes density data
 */
export const REGIONAL_PROTEIN_DISHES: FoodDensityData[] = [
  { name: 'falafel', category: FOOD_CATEGORIES.PROTEINS, density: 0.65, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'chickpea fritter', category: FOOD_CATEGORIES.PROTEINS, density: 0.65, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'paneer', category: FOOD_CATEGORIES.PROTEINS, density: 0.78, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'halloumi', category: FOOD_CATEGORIES.PROTEINS, density: 0.77, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'tandoori chicken', category: FOOD_CATEGORIES.PROTEINS, density: 0.94, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'shawarma', category: FOOD_CATEGORIES.PROTEINS, density: 0.92, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'kebab', category: FOOD_CATEGORIES.PROTEINS, density: 0.95, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'chorizo', category: FOOD_CATEGORIES.PROTEINS, density: 0.88, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'carnitas', category: FOOD_CATEGORIES.PROTEINS, density: 0.93, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'carne asada', category: FOOD_CATEGORIES.PROTEINS, density: 1.02, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'al pastor', category: FOOD_CATEGORIES.PROTEINS, density: 0.96, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'bulgogi', category: FOOD_CATEGORIES.PROTEINS, density: 0.97, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'satay', category: FOOD_CATEGORIES.PROTEINS, density: 0.94, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'gyro meat', category: FOOD_CATEGORIES.PROTEINS, density: 0.93, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'souvlaki', category: FOOD_CATEGORIES.PROTEINS, density: 0.94, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'char siu', category: FOOD_CATEGORIES.PROTEINS, density: 0.95, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'tempura', category: FOOD_CATEGORIES.PROTEINS, density: 0.65, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'schnitzel', category: FOOD_CATEGORIES.PROTEINS, density: 0.87, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' },
  { name: 'kibbeh', category: FOOD_CATEGORIES.PROTEINS, density: 0.85, defaultServing: 100, waterContent: 0.70, notes: 'Cooked' }
];
