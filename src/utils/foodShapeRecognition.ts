/**
 * Food Shape Recognition Utility
 * 
 * Provides enhanced food shape recognition for more accurate volume estimation
 * from 2D images.
 */

/**
 * Enum defining standard food shape categories for volume calculations
 */
export enum FoodShape {
  FLAT = 'flat',                  // Like a pancake, thin slice of bread
  REGULAR_CUBOID = 'regularCuboid', // Regular box shape (e.g., block of tofu)
  TALL_CYLINDER = 'tallCylinder',   // Taller than wide (e.g., drink glass)
  WIDE_CYLINDER = 'wideCylinder',   // Wider than tall (e.g., portioned rice)
  SPHERE = 'sphere',              // Round in all dimensions (e.g., apple)
  IRREGULAR_PILE = 'irregularPile', // Piled food with irregular shape
  SHALLOW_BOWL = 'shallowBowl',     // Food in a shallow bowl or plate
  DEEP_BOWL = 'deepBowl',          // Food in a deep bowl or cup
  LIQUID = 'liquid',              // Liquid in a container
  CUSTOM = 'custom'               // Custom shape requiring special handling
}

/**
 * Parameters for volume calculation specific to each shape type
 */
export interface ShapeParameters {
  height?: number;     // Height in mm or pixels
  width?: number;      // Width in mm or pixels
  length?: number;     // Length in mm or pixels
  diameter?: number;   // Diameter in mm or pixels
  radius?: number;     // Radius in mm or pixels
  depth?: number;      // Depth in mm or pixels
  area?: number;       // Area in mm² or pixels²
  containerHeight?: number; // Container height in mm or pixels
  containerWidth?: number;  // Container width in mm or pixels
  containerDepth?: number;  // Container depth in mm or pixels
  fillLevel?: number;  // Fill level (0-1) for containers
  customFactor?: number; // Custom multiplication factor
}

/**
 * Configuration for different shape types and their volume calculation methods
 */
const SHAPE_CONFIGURATIONS = {
  [FoodShape.FLAT]: {
    defaultHeightToWidthRatio: 0.1,
    volumeCalculation: (params: ShapeParameters) => {
      // Volume = area × height
      if (!params.area || !params.height) return 0;
      return params.area * params.height;
    }
  },
  [FoodShape.REGULAR_CUBOID]: {
    volumeCalculation: (params: ShapeParameters) => {
      // Volume = width × length × height
      if (!params.width || !params.length || !params.height) return 0;
      return params.width * params.length * params.height;
    }
  },
  [FoodShape.TALL_CYLINDER]: {
    defaultHeightToDiameterRatio: 2.5,
    volumeCalculation: (params: ShapeParameters) => {
      // Volume = π × (diameter/2)² × height
      if (!params.diameter || !params.height) return 0;
      const radius = params.diameter / 2;
      return Math.PI * radius * radius * params.height;
    }
  },
  [FoodShape.WIDE_CYLINDER]: {
    defaultHeightToDiameterRatio: 0.5,
    volumeCalculation: (params: ShapeParameters) => {
      // Volume = π × (diameter/2)² × height
      if (!params.diameter || !params.height) return 0;
      const radius = params.diameter / 2;
      return Math.PI * radius * radius * params.height;
    }
  },
  [FoodShape.SPHERE]: {
    volumeCalculation: (params: ShapeParameters) => {
      // Volume = (4/3) × π × radius³
      if (!params.radius && !params.diameter) return 0;
      const radius = params.radius || params.diameter! / 2;
      return (4/3) * Math.PI * Math.pow(radius, 3);
    }
  },
  [FoodShape.IRREGULAR_PILE]: {
    // Approximation factor for irregular piles (empirically determined)
    pileShapeFactor: 0.6,
    volumeCalculation: (params: ShapeParameters) => {
      // Approximated as a semi-ellipsoid: volume = (2/3) × π × width/2 × length/2 × height × shapeFactor
      if (!params.width || !params.length || !params.height) return 0;
      const shapeFactor = SHAPE_CONFIGURATIONS[FoodShape.IRREGULAR_PILE].pileShapeFactor;
      return (2/3) * Math.PI * (params.width/2) * (params.length/2) * params.height * shapeFactor;
    }
  },
  [FoodShape.SHALLOW_BOWL]: {
    volumeCalculation: (params: ShapeParameters) => {
      // Approximated as a portion of a hemisphere: volume = (2/3) × π × radius² × height × fillLevel
      if (!params.diameter || !params.height || !params.fillLevel) return 0;
      const radius = params.diameter / 2;
      return (2/3) * Math.PI * radius * radius * params.height * params.fillLevel;
    }
  },
  [FoodShape.DEEP_BOWL]: {
    volumeCalculation: (params: ShapeParameters) => {
      // Approximated as a cylinder: volume = π × radius² × fillHeight
      if (!params.diameter || !params.containerHeight || !params.fillLevel) return 0;
      const radius = params.diameter / 2;
      const fillHeight = params.containerHeight * params.fillLevel;
      return Math.PI * radius * radius * fillHeight;
    }
  },
  [FoodShape.LIQUID]: {
    volumeCalculation: (params: ShapeParameters) => {
      // Volume = container cross-sectional area × fill height
      if (!params.area && (!params.diameter && !params.width && !params.length)) return 0;
      if (!params.containerHeight || !params.fillLevel) return 0;
      
      let crossSectionalArea = params.area || 0;
      if (!crossSectionalArea) {
        if (params.diameter) {
          // Circular container
          const radius = params.diameter / 2;
          crossSectionalArea = Math.PI * radius * radius;
        } else if (params.width && params.length) {
          // Rectangular container
          crossSectionalArea = params.width * params.length;
        }
      }
      
      const fillHeight = params.containerHeight * params.fillLevel;
      return crossSectionalArea * fillHeight;
    }
  },
  [FoodShape.CUSTOM]: {
    volumeCalculation: (params: ShapeParameters) => {
      // Custom volume calculation with a provided factor
      if (!params.area || !params.height || !params.customFactor) return 0;
      return params.area * params.height * params.customFactor;
    }
  }
};

/**
 * Food shape detection keywords to help identify the likely shape from food name
 */
const SHAPE_DETECTION_KEYWORDS: Record<FoodShape, string[]> = {
  [FoodShape.FLAT]: [
    'pancake', 'slice', 'flat', 'thin', 'crepe', 'tortilla', 'bread slice', 'flatbread', 'pizza',
    'wafer', 'cracker', 'sheet', 'wrapper', 'toast', 'naan', 'pita', 'chip'
  ],
  [FoodShape.REGULAR_CUBOID]: [
    'block', 'cube', 'square', 'rectangular', 'bar', 'brick', 'tofu', 'cake', 'brownie',
    'sandwich', 'lasagna', 'cassarole', 'casserole', 'slice'
  ],
  [FoodShape.TALL_CYLINDER]: [
    'glass', 'drink', 'bottle', 'cup', 'tumbler', 'can', 'milkshake', 'smoothie'
  ],
  [FoodShape.WIDE_CYLINDER]: [
    'patty', 'burger', 'disc', 'disk', 'round', 'cookie', 'biscuit', 'rice', 'portion',
    'puck', 'muffin', 'cupcake', 'portioned'
  ],
  [FoodShape.SPHERE]: [
    'ball', 'apple', 'orange', 'round', 'sphere', 'egg', 'globe', 'fruit', 'onion', 
    'meatball', 'scoop', 'tomato', 'potato', 'avocado'
  ],
  [FoodShape.IRREGULAR_PILE]: [
    'pile', 'heap', 'mound', 'salad', 'vegetables', 'pasta', 'noodles', 'rice', 'mixed',
    'stir fry', 'platter', 'medley', 'assorted'
  ],
  [FoodShape.SHALLOW_BOWL]: [
    'bowl', 'plate', 'dish', 'platter', 'shallow bowl', 'served in'
  ],
  [FoodShape.DEEP_BOWL]: [
    'deep bowl', 'cup', 'mug', 'soup bowl', 'cereal bowl', 'deep dish'
  ],
  [FoodShape.LIQUID]: [
    'liquid', 'soup', 'broth', 'sauce', 'beverage', 'drink', 'milk', 'juice', 'water',
    'stock', 'smoothie', 'tea', 'coffee', 'alcohol', 'wine', 'beer'
  ],
  [FoodShape.CUSTOM]: [
    'special', 'custom', 'unique', 'specific', 'exceptional'
  ]
};

/**
 * Visual characteristics that help identify food shapes from images
 */
export interface VisualShapeCharacteristics {
  aspectRatio: number;     // Width to height ratio
  roundness: number;       // How circular (0-1)
  uniformity: number;      // How uniform in texture (0-1)
  depth: number;           // Estimated depth (0-1)
  surfaceVariation: number; // Variation in surface height (0-1)
  containerPresent: boolean; // Is the food in a container?
  referencePosition: 'beside' | 'under' | 'none'; // Position of reference object
}

/**
 * Determine the most likely shape of a food item from its name and visual characteristics
 * 
 * @param foodName Name of the food item
 * @param visualCharacteristics Optional visual characteristics from image analysis
 * @returns The most likely food shape
 */
export function determineFoodShape(
  foodName: string,
  visualCharacteristics?: Partial<VisualShapeCharacteristics>
): FoodShape {
  const normalizedName = foodName.toLowerCase().trim();
  
  // First check for liquid foods since they need special handling
  if (isLiquidFood(normalizedName)) {
    return FoodShape.LIQUID;
  }
  
  // If food is in a container, handle based on container type
  if (visualCharacteristics?.containerPresent) {
    const depth = visualCharacteristics.depth || 0.5; // Add default value
    if (depth > 0.7) {
      return FoodShape.DEEP_BOWL;
    } else {
      return FoodShape.SHALLOW_BOWL;
    }
  }
  
  // Check for keyword matches
  const shapeScores = Object.entries(SHAPE_DETECTION_KEYWORDS).map(([shape, keywords]) => {
    const matchScore = keywords.reduce((score, keyword) => {
      if (normalizedName.includes(keyword)) {
        // Longer keyword matches are more significant
        return score + keyword.length;
      }
      return score;
    }, 0);
    
    return { shape: shape as FoodShape, score: matchScore };
  });
  
  // Find shape with highest keyword match score
  const bestKeywordMatch = shapeScores.reduce((best, current) => 
    current.score > best.score ? current : best, { shape: FoodShape.REGULAR_CUBOID, score: 0 });
  
  // If we have a strong keyword match, use it
  if (bestKeywordMatch.score > 5) {
    return bestKeywordMatch.shape;
  }
  
  // Use visual characteristics if available
  if (visualCharacteristics) {
    return determineShapeFromVisualCharacteristics(visualCharacteristics);
  }
  
  // If we don't have a strong keyword match or visual data, 
  // use irregular pile as a safe default for most mixed foods
  return FoodShape.IRREGULAR_PILE;
}

/**
 * Determine if a food is a liquid based on its name
 */
function isLiquidFood(foodName: string): boolean {
  const liquidKeywords = SHAPE_DETECTION_KEYWORDS[FoodShape.LIQUID];
  return liquidKeywords.some(keyword => foodName.includes(keyword));
}

/**
 * Determine food shape from visual characteristics
 */
function determineShapeFromVisualCharacteristics(
  characteristics: Partial<VisualShapeCharacteristics>
): FoodShape {
  const { aspectRatio = 1, roundness = 0.5, uniformity = 0.5, depth = 0.5, surfaceVariation = 0.5 } = characteristics;
  
  // Very flat items
  if (aspectRatio > 8 || depth < 0.2) {
    return FoodShape.FLAT;
  }
  
  // Spherical items
  if (roundness > 0.85 && uniformity > 0.7 && Math.abs(aspectRatio - 1) < 0.2) {
    return FoodShape.SPHERE;
  }
  
  // Tall cylindrical items
  if (roundness > 0.7 && aspectRatio < 0.5) {
    return FoodShape.TALL_CYLINDER;
  }
  
  // Wide cylindrical items
  if (roundness > 0.7 && aspectRatio > 1.5 && aspectRatio < 5) {
    return FoodShape.WIDE_CYLINDER;
  }
  
  // Regular cuboid items
  if (roundness < 0.3 && uniformity > 0.7 && surfaceVariation < 0.3) {
    return FoodShape.REGULAR_CUBOID;
  }
  
  // Default to irregular pile for anything else
  return FoodShape.IRREGULAR_PILE;
}

/**
 * Calculate the volume of a food item based on its shape and dimensions
 * 
 * @param shape The shape of the food item
 * @param params Parameters needed for volume calculation
 * @returns Volume in cubic units (mm³ or pixels³ depending on input units)
 */
export function calculateVolumeByShape(shape: FoodShape, params: ShapeParameters): number {
  const shapeConfig = SHAPE_CONFIGURATIONS[shape];
  if (!shapeConfig || !shapeConfig.volumeCalculation) {
    return 0;
  }
  
  return shapeConfig.volumeCalculation(params);
}

/**
 * Estimate the height of a food item when only 2D information is available
 * 
 * @param shape The shape of the food item
 * @param width Width in mm or pixels
 * @param length Length in mm or pixels
 * @param foodName Optional food name for additional context
 * @returns Estimated height in same units as width/length
 */
export function estimateHeightFromShape(
  shape: FoodShape,
  width: number,
  length: number,
  foodName?: string
): number {
  switch (shape) {
    case FoodShape.FLAT:
      // Flat items typically have height = 10% of width
      return width * (SHAPE_CONFIGURATIONS[FoodShape.FLAT].defaultHeightToWidthRatio || 0.1);
      
    case FoodShape.REGULAR_CUBOID:
      // For cuboids, estimate height as average of width and length
      return (width + length) / 4; // 25% of average width/length
      
    case FoodShape.TALL_CYLINDER:
      // For tall cylinders, height is typically 2.5x diameter
      const diameter = Math.min(width, length);
      return diameter * (SHAPE_CONFIGURATIONS[FoodShape.TALL_CYLINDER].defaultHeightToDiameterRatio || 2.5);
      
    case FoodShape.WIDE_CYLINDER:
      // For wide cylinders, height is typically 50% of diameter
      const wideDiameter = Math.min(width, length);
      return wideDiameter * (SHAPE_CONFIGURATIONS[FoodShape.WIDE_CYLINDER].defaultHeightToDiameterRatio || 0.5);
      
    case FoodShape.SPHERE:
      // For spheres, height equals diameter
      return Math.min(width, length);
      
    case FoodShape.IRREGULAR_PILE:
      // For irregular piles, estimate based on width and length
      return Math.max(width, length) * 0.4;
      
    case FoodShape.SHALLOW_BOWL:
    case FoodShape.DEEP_BOWL:
    case FoodShape.LIQUID:
      // For bowls and liquids, need container dimensions which should be provided separately
      return Math.max(width, length) * 0.5; // Rough estimate only
      
    case FoodShape.CUSTOM:
      // Custom shapes require specific handling
      return Math.max(width, length) * 0.5; // Default estimate
      
    default:
      // Default estimate
      return Math.max(width, length) * 0.3;
  }
}

/**
 * Estimate volume from 2D image measurements
 * 
 * @param foodName Name of the food item
 * @param area Area in mm² or pixels²
 * @param width Width in mm or pixels
 * @param length Length in mm or pixels
 * @param visualCharacteristics Optional visual characteristics
 * @returns Estimated volume in cubic units (mm³ or pixels³)
 */
export function estimateVolumeFrom2D(
  foodName: string,
  area: number,
  width: number,
  length: number,
  visualCharacteristics?: Partial<VisualShapeCharacteristics>
): number {
  // Determine the food's shape
  const shape = determineFoodShape(foodName, visualCharacteristics);
  
  // Estimate height based on shape and dimensions
  const height = estimateHeightFromShape(shape, width, length, foodName);
  
  // Prepare parameters for volume calculation
  const params: ShapeParameters = {
    area,
    width,
    length,
    height,
    diameter: Math.min(width, length), // Approximate diameter from width/length
    radius: Math.min(width, length) / 2
  };
  
  // Add container parameters if food is in a container
  if (shape === FoodShape.SHALLOW_BOWL || shape === FoodShape.DEEP_BOWL || shape === FoodShape.LIQUID) {
    params.containerHeight = height * 1.5; // Estimate container height
    params.fillLevel = 0.8; // Assume 80% fill by default
  }
  
  // Calculate volume based on shape
  return calculateVolumeByShape(shape, params);
}

/**
 * Get adjustment factor for specific food types to calibrate volume estimation
 * 
 * @param foodName Name of the food
 * @returns Adjustment factor for volume calculation
 */
export function getFoodSpecificVolumeAdjustment(foodName: string): number {
  const normalizedName = foodName.toLowerCase();
  
  // Food-specific adjustments based on empirical data
  if (/salad|leafy|lettuce|spinach|kale/.test(normalizedName)) {
    return 0.7; // Leafy salads have many air pockets
  }
  
  if (/bread|toast|baked|cake|muffin|pastry|donut/.test(normalizedName)) {
    return 0.8; // Baked goods have air pockets
  }
  
  if (/ice cream|whipped|mousse|foam/.test(normalizedName)) {
    return 0.6; // Whipped/aerated foods have more air
  }
  
  if (/compacted|dense|firm|solid/.test(normalizedName)) {
    return 1.2; // Dense foods may need volume increased
  }
  
  if (/puffed|airy|light|crisp/.test(normalizedName)) {
    return 0.5; // Very airy foods
  }
  
  // Default: no adjustment
  return 1.0;
} 