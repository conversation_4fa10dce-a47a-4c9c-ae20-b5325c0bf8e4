/**
 * Health Data Connector
 * 
 * This utility connects to health data sources like Apple HealthKit, Google Fit,
 * or Health Connect to retrieve real health data for the dashboard.
 */
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { db } from '@/lib/firebase';
import { collection, doc, getDoc, setDoc, query, where, getDocs, Timestamp } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';


// Import platform-specific health services
let AppleHealthKit: any = null;
let GoogleFit: any = null;
let HealthConnect: any = null;

// Dynamically import the appropriate health service based on platform
if (Platform.OS === 'ios') {
  try {
    AppleHealthKit = require('react-native-health').default;
  } catch (error) {
    console.error('Failed to load HealthKit', error);
  }
} else if (Platform.OS === 'android') {
  try {
    GoogleFit = require('react-native-google-fit').default;
    HealthConnect = require('react-native-health-connect');
  } catch (error) {
    console.error('Failed to load Google Fit or Health Connect', error);
  }
}

// Interfaces for health data
export interface HealthStats {
  steps: number;
  caloriesBurned: number;
  activeMinutes: number;
  distance: number;  // in kilometers
  heartRate: number; // in bpm
  sleep: SleepData;
  water: WaterData;
  nutrition: NutritionData;
  lastUpdated: string;
}

export interface SleepData {
  hours: number;
  quality: 'Poor' | 'Fair' | 'Good' | 'Excellent';
  deepSleepMinutes: number;
  lightSleepMinutes: number;
  remSleepMinutes: number;
}

export interface WaterData {
  current: number; // glasses
  goal: number;    // glasses
  milliliters: number;
}

export interface NutritionData {
  caloriesConsumed: number;
  caloriesGoal: number;
  protein: number;  // grams
  carbs: number;    // grams
  fat: number;      // grams
  fiber: number;    // grams
}

export interface MoodData {
  mood: 'Sad' | 'Okay' | 'Good' | 'Happy' | 'Excited';
  energy: number; // 1-10
  stress: number; // 1-10
  notes: string;
}

// Storage keys for data that may not be in HealthKit/Google Fit
const WATER_STORAGE_KEY = 'health_water_data';
const MOOD_STORAGE_KEY = 'health_mood_data';
const NUTRITION_GOALS_KEY = 'health_nutrition_goals';
const HEALTH_DATA_CACHE_KEY = 'health_data_cache';

class HealthDataConnector {
  private hasPermission: boolean = false;
  private lastFetchTime: number = 0;
  private cachedData: HealthStats | null = null;
  private waterGoal: number = 8; // Default 8 glasses
  private caloriesGoal: number = 2000; // Default 2000 calories
  
  constructor() {
    // Load saved goals when instantiated
    this.loadSavedGoals();
    this.loadCachedData();
  }
  
  private async loadSavedGoals() {
    try {
      // Try to load goals from Firebase first
      const auth = getAuth();
      const currentUser = auth.currentUser;
      
      if (currentUser) {
        const userGoalsRef = doc(db, 'user_health_goals', currentUser.uid);
        const userGoalsSnap = await getDoc(userGoalsRef);
        
        if (userGoalsSnap.exists()) {
          const userGoals = userGoalsSnap.data();
          this.waterGoal = userGoals.water_goal ?? this.waterGoal;
          this.caloriesGoal = userGoals.calories_goal ?? this.caloriesGoal;
          return;
        }
      }
      
      // Fall back to AsyncStorage if no Firebase data or not authenticated
      const waterGoalData = await AsyncStorage.getItem(WATER_STORAGE_KEY);
      if (waterGoalData) {
        const data = JSON.parse(waterGoalData);
        if (data.goal) this.waterGoal = data.goal;
      }
      
      const nutritionGoals = await AsyncStorage.getItem(NUTRITION_GOALS_KEY);
      if (nutritionGoals) {
        const data = JSON.parse(nutritionGoals);
        if (data.caloriesGoal) this.caloriesGoal = data.caloriesGoal;
      }
    } catch (error) {
      console.error('Error loading saved health goals:', error);
    }
  }

  private async loadCachedData() {
    try {
      const cachedDataStr = await AsyncStorage.getItem(HEALTH_DATA_CACHE_KEY);
      if (cachedDataStr) {
        const cache = JSON.parse(cachedDataStr);
        if (cache.data && cache.timestamp) {
          this.cachedData = cache.data;
          this.lastFetchTime = cache.timestamp;
        }
      }
    } catch (error) {
      console.error('Error loading cached health data:', error);
    }
  }

  private async saveCachedData() {
    try {
      if (this.cachedData) {
        await AsyncStorage.setItem(HEALTH_DATA_CACHE_KEY, JSON.stringify({
          data: this.cachedData,
          timestamp: this.lastFetchTime
        }));
      }
    } catch (error) {
      console.error('Error saving cached health data:', error);
    }
  }
  
  // Request permissions to access health data
  async requestPermissions(): Promise<boolean> {
    try {
      if (Platform.OS === 'ios' && AppleHealthKit) {
        // Define the permissions we want for HealthKit
        const permissions = {
          permissions: {
            read: [
              AppleHealthKit.Constants.Permissions.Steps,
              AppleHealthKit.Constants.Permissions.StepCount,
              AppleHealthKit.Constants.Permissions.DistanceWalkingRunning,
              AppleHealthKit.Constants.Permissions.ActiveEnergyBurned,
              AppleHealthKit.Constants.Permissions.HeartRate,
              AppleHealthKit.Constants.Permissions.SleepAnalysis,
              AppleHealthKit.Constants.Permissions.Dietary,
              AppleHealthKit.Constants.Permissions.Water,
            ],
            write: [
              AppleHealthKit.Constants.Permissions.Steps,
              AppleHealthKit.Constants.Permissions.Water,
              AppleHealthKit.Constants.Permissions.Dietary,
            ],
          },
        };
        
        // Request permissions from HealthKit
        return new Promise((resolve) => {
          AppleHealthKit.initHealthKit(permissions, (error: string) => {
            if (error) {
              console.error('HealthKit initialization error:', error);
              resolve(false);
            } else {
              this.hasPermission = true;
              resolve(true);
            }
          });
        });
      } else if (Platform.OS === 'android') {
        // First try Health Connect if available
        if (HealthConnect) {
          try {
            const { availabilityStatus } = await HealthConnect.initialize();
            if (availabilityStatus === 'AVAILABLE') {
              // Health Connect is available, request permissions
              const requestPermissionsResult = await HealthConnect.requestPermissions([
                { accessType: 'READ', recordType: 'Steps' },
                { accessType: 'READ', recordType: 'Distance' },
                { accessType: 'READ', recordType: 'ActiveCaloriesBurned' },
                { accessType: 'READ', recordType: 'HeartRate' },
                { accessType: 'READ', recordType: 'Sleep' },
                { accessType: 'READ', recordType: 'Weight' },
                { accessType: 'READ', recordType: 'Hydration' },
                { accessType: 'READ', recordType: 'Nutrition' },
                { accessType: 'WRITE', recordType: 'Hydration' },
                { accessType: 'WRITE', recordType: 'Nutrition' },
              ]);
              
              this.hasPermission = requestPermissionsResult.granted;
              return this.hasPermission;
            }
          } catch (error) {
            console.error('Health Connect error:', error);
          }
        }
        
        // Fall back to Google Fit if Health Connect is not available or failed
        if (GoogleFit) {
          // Define permissions for Google Fit
          const permissions = [
            GoogleFit.Scopes.FITNESS_ACTIVITY_READ,
            GoogleFit.Scopes.FITNESS_ACTIVITY_WRITE,
            GoogleFit.Scopes.FITNESS_BODY_READ,
            GoogleFit.Scopes.FITNESS_BODY_WRITE,
            GoogleFit.Scopes.FITNESS_BLOOD_PRESSURE_READ,
            GoogleFit.Scopes.FITNESS_BLOOD_PRESSURE_WRITE,
            GoogleFit.Scopes.FITNESS_BLOOD_GLUCOSE_READ,
            GoogleFit.Scopes.FITNESS_BLOOD_GLUCOSE_WRITE,
            GoogleFit.Scopes.FITNESS_NUTRITION_WRITE,
            GoogleFit.Scopes.FITNESS_SLEEP_READ,
          ];
          
          try {
            // Request Google Fit permissions
            const authResult = await GoogleFit.authorize({
              scopes: permissions,
            });
            
            if (authResult.success) {
              this.hasPermission = true;
              return true;
            } else {
              console.error("Google Fit authorization denied:", authResult.message);
              return false;
            }
          } catch (error) {
            console.error("Google Fit authorization error:", error);
            return false;
          }
        }
        
        // Web or unsupported platform - use real-looking data silently
        this.hasPermission = true;
        return true;
      } else {
        // Web or unsupported platform - use real-looking data silently
        this.hasPermission = true;
        return true;
      }
    } catch (error) {
      console.error('Error requesting health permissions:', error);
      return false;
    }
  }
  
  // Check if we have permissions
  async checkPermissions(): Promise<boolean> {
    if (this.hasPermission) return true;
    
    try {
      if (Platform.OS === 'ios' && AppleHealthKit) {
        return new Promise((resolve) => {
          // Check a sample permission to see if we have access
          AppleHealthKit.isAvailable((error: string, result: boolean) => {
            if (error) {
              console.error('HealthKit availability check error:', error);
              resolve(false);
            } else {
              this.hasPermission = result;
              resolve(result);
            }
          });
        });
      } else if (Platform.OS === 'android') {
        // First try Health Connect
        if (HealthConnect) {
          try {
            const { availabilityStatus } = await HealthConnect.initialize();
            if (availabilityStatus === 'AVAILABLE') {
              const grantedPermissions = await HealthConnect.getGrantedPermissions();
              this.hasPermission = grantedPermissions.length > 0;
              return this.hasPermission;
            }
          } catch (error) {
            console.error('Health Connect check error:', error);
          }
        }
        
        // Fall back to Google Fit
        if (GoogleFit) {
          const result = await GoogleFit.checkIsAuthorized();
          this.hasPermission = result.isAuthorized;
          return result.isAuthorized;
        }
        
        return false;
      } else {
        // Web or unsupported platform
        return this.hasPermission;
      }
    } catch (error) {
      console.error('Error checking health permissions:', error);
      return false;
    }
  }
  
  // Fetch health data from the device
  async fetchHealthData(): Promise<HealthStats> {
    // Check if we have permission first
    const hasPermission = await this.checkPermissions();
    if (!hasPermission) {
      const granted = await this.requestPermissions();
      if (!granted) {
        throw new Error('Health data permissions not granted');
      }
    }
    
    // Check if we have recently fetched data (cache for 5 minutes)
    const now = Date.now();
    if (this.cachedData && now - this.lastFetchTime < 5 * 60 * 1000) {
      return this.cachedData;
    }
    
    // Get today's date
    const today = new Date();
    const todayString = today.toISOString().split('T')[0];
    
    try {
      // Try to get data from Firebase first (this way we can sync across devices)
      const auth = getAuth();
      const currentUser = auth.currentUser;
      
      if (currentUser) {
        const healthDataRef = doc(db, 'health_daily_summary', `${currentUser.uid}_${todayString}`);
        const healthDataSnap = await getDoc(healthDataRef);
          
        if (healthDataSnap.exists()) {
          const healthData = healthDataSnap.data();
          
          // Transform the data into our HealthStats format
          const stats: HealthStats = {
            steps: healthData.steps ?? 0,
            caloriesBurned: healthData.calories_burned ?? 0,
            activeMinutes: healthData.active_minutes ?? 0,
            distance: healthData.distance ?? 0,
            heartRate: healthData.heart_rate ?? 0,
            sleep: {
              hours: healthData.sleep_hours ?? 0,
              quality: healthData.sleep_quality ?? 'Fair',
              deepSleepMinutes: healthData.deep_sleep_minutes ?? 0,
              lightSleepMinutes: healthData.light_sleep_minutes ?? 0,
              remSleepMinutes: healthData.rem_sleep_minutes ?? 0,
            },
            water: {
              current: healthData.water_glasses ?? 0,
              goal: this.waterGoal,
              milliliters: healthData.water_ml ?? 0,
            },
            nutrition: {
              caloriesConsumed: healthData.calories_consumed ?? 0,
              caloriesGoal: this.caloriesGoal,
              protein: healthData.protein ?? 0,
              carbs: healthData.carbs ?? 0,
              fat: healthData.fat ?? 0,
              fiber: healthData.fiber ?? 0,
            },
            lastUpdated: healthData.updated_at ?? new Date().toISOString(),
          };
          
          // Update cache
          this.cachedData = stats;
          this.lastFetchTime = now;
          
          return stats;
        }
      }
      
      // If we don't have data in Firebase, fetch from health platforms
      let steps = 0;
      let caloriesBurned = 0;
      let activeMinutes = 0;
      let distance = 0;
      let heartRate = 0;
      let sleepHours = 0;
      let deepSleepMinutes = 0;
      let lightSleepMinutes = 0;
      let remSleepMinutes = 0;
      let waterGlasses = 0;
      let waterMilliliters = 0;
      let caloriesConsumed = 0;
      let protein = 0;
      let carbs = 0;
      let fat = 0;
      let fiber = 0;
      
      if (Platform.OS === 'ios' && AppleHealthKit) {
        // Fetch data from Apple HealthKit
        steps = await this.getAppleHealthSteps(todayString);
        caloriesBurned = await this.getAppleHealthCaloriesBurned(todayString);
        distance = await this.getAppleHealthDistance(todayString);
        activeMinutes = await this.getAppleHealthActiveMinutes(todayString);
        heartRate = await this.getAppleHealthHeartRate();
        
        const sleepData = await this.getAppleHealthSleep(todayString);
        sleepHours = sleepData.hours;
        deepSleepMinutes = sleepData.deepSleepMinutes;
        lightSleepMinutes = sleepData.lightSleepMinutes;
        remSleepMinutes = sleepData.remSleepMinutes;
        
        waterMilliliters = await this.getAppleHealthWater(todayString);
        waterGlasses = Math.round(waterMilliliters / 250);
        
        const nutritionData = await this.getAppleHealthNutrition(todayString);
        caloriesConsumed = nutritionData.calories;
        protein = nutritionData.protein;
        carbs = nutritionData.carbs;
        fat = nutritionData.fat;
        fiber = nutritionData.fiber;
      } else if (Platform.OS === 'android') {
        // Try Health Connect first if available
        if (HealthConnect) {
          try {
            const { availabilityStatus } = await HealthConnect.initialize();
            if (availabilityStatus === 'AVAILABLE') {
              // Get data from Health Connect
              steps = await this.getHealthConnectSteps(todayString);
              distance = await this.getHealthConnectDistance(todayString);
              caloriesBurned = await this.getHealthConnectCaloriesBurned(todayString);
              activeMinutes = await this.getHealthConnectActiveMinutes(todayString);
              heartRate = await this.getHealthConnectHeartRate();
              
              const sleepData = await this.getHealthConnectSleep(todayString);
              sleepHours = sleepData.hours;
              deepSleepMinutes = sleepData.deepSleepMinutes;
              lightSleepMinutes = sleepData.lightSleepMinutes;
              remSleepMinutes = sleepData.remSleepMinutes;
              
              // Health Connect supports hydration
              waterMilliliters = await this.getHealthConnectWater(todayString);
              waterGlasses = Math.round(waterMilliliters / 250);
              
              // Health Connect supports nutrition
              const nutritionData = await this.getHealthConnectNutrition(todayString);
              caloriesConsumed = nutritionData.calories;
              protein = nutritionData.protein;
              carbs = nutritionData.carbs;
              fat = nutritionData.fat;
              fiber = nutritionData.fiber;
            }
          } catch (error) {
            console.error('Health Connect data fetch error:', error);
          }
        }
        
        // If Health Connect values are 0, try Google Fit as fallback
        if (GoogleFit && steps === 0) {
          steps = await this.getGoogleFitSteps(todayString);
          caloriesBurned = await this.getGoogleFitCaloriesBurned(todayString);
          distance = await this.getGoogleFitDistance(todayString);
          activeMinutes = await this.getGoogleFitActiveMinutes(todayString);
          heartRate = await this.getGoogleFitHeartRate();
          
          const sleepData = await this.getGoogleFitSleep(todayString);
          sleepHours = sleepData.hours;
          deepSleepMinutes = sleepData.deepSleepMinutes;
          lightSleepMinutes = sleepData.lightSleepMinutes;
          remSleepMinutes = sleepData.remSleepMinutes;
          
          // For water and nutrition, we use our own storage
          const waterData = await this.getStoredWaterData(todayString);
          waterGlasses = waterData.glasses;
          waterMilliliters = waterData.milliliters;
          
          const nutritionData = await this.getStoredNutritionData(todayString);
          caloriesConsumed = nutritionData.calories;
          protein = nutritionData.protein;
          carbs = nutritionData.carbs;
          fat = nutritionData.fat;
          fiber = nutritionData.fiber;
        }
      } else {
        // For web or unsupported platforms, use zeros instead of mock data
        // First try to get data from local storage
        const waterData = await this.getStoredWaterData(todayString);
        waterGlasses = waterData.glasses;
        waterMilliliters = waterData.milliliters;
        
        const nutritionData = await this.getStoredNutritionData(todayString);
        caloriesConsumed = nutritionData.calories;
        protein = nutritionData.protein;
        carbs = nutritionData.carbs;
        fat = nutritionData.fat;
        fiber = nutritionData.fiber;
        
        // Use zeros instead of fixed mock values
        steps = 0;
        caloriesBurned = 0;
        distance = 0;
        activeMinutes = 0; 
        heartRate = 0;
        sleepHours = 0;
        deepSleepMinutes = 0;
        lightSleepMinutes = 0;
        remSleepMinutes = 0;
      }
      
      // Determine sleep quality
      let sleepQuality: 'Poor' | 'Fair' | 'Good' | 'Excellent';
      if (sleepHours < 6) sleepQuality = 'Poor';
      else if (sleepHours < 7) sleepQuality = 'Fair';
      else if (sleepHours < 8) sleepQuality = 'Good';
      else sleepQuality = 'Excellent';
      
      // Create the health data object
      const healthData: HealthStats = {
        steps,
        caloriesBurned: Math.round(caloriesBurned),
        activeMinutes,
        distance: parseFloat(distance.toFixed(2)),
        heartRate: Math.round(heartRate),
        sleep: {
          hours: parseFloat(sleepHours.toFixed(1)),
          quality: sleepQuality,
          deepSleepMinutes,
          lightSleepMinutes,
          remSleepMinutes,
        },
        water: {
          current: waterGlasses,
          goal: this.waterGoal,
          milliliters: waterMilliliters,
        },
        nutrition: {
          caloriesConsumed: Math.round(caloriesConsumed),
          caloriesGoal: this.caloriesGoal,
          protein: Math.round(protein),
          carbs: Math.round(carbs),
          fat: Math.round(fat),
          fiber: Math.round(fiber),
        },
        lastUpdated: new Date().toISOString(),
      };
      
      // Cache the data
      this.cachedData = healthData;
      this.lastFetchTime = now;
      await this.saveCachedData();
      
      // Save to Firebase for syncing across devices if user is logged in
      try {
        const auth = getAuth();
        const currentUser = auth.currentUser;
        
        if (currentUser) {
          const docId = `${currentUser.uid}_${todayString}`;
          const healthDataRef = doc(db, 'health_daily_summary', docId);
          
          await setDoc(healthDataRef, {
            user_id: currentUser.uid,
            date: todayString,
            steps: healthData.steps,
            calories_burned: healthData.caloriesBurned,
            active_minutes: healthData.activeMinutes,
            distance: healthData.distance,
            heart_rate: healthData.heartRate,
            sleep_hours: healthData.sleep.hours,
            sleep_quality: healthData.sleep.quality,
            deep_sleep_minutes: healthData.sleep.deepSleepMinutes,
            light_sleep_minutes: healthData.sleep.lightSleepMinutes,
            rem_sleep_minutes: healthData.sleep.remSleepMinutes,
            water_glasses: healthData.water.current,
            water_ml: healthData.water.milliliters,
            calories_consumed: healthData.nutrition.caloriesConsumed,
            calories_goal: healthData.nutrition.caloriesGoal,
            protein: healthData.nutrition.protein,
            carbs: healthData.nutrition.carbs,
            fat: healthData.nutrition.fat,
            fiber: healthData.nutrition.fiber,
            updated_at: healthData.lastUpdated
          }, { merge: true });
        }
      } catch (error) {
        console.error('Error saving health data to Firebase:', error);
      }
      
      return healthData;
    } catch (error) {
      console.error('Error fetching health data:', error);
      
      // If there's an error, return zeros instead of mock data
      const healthData: HealthStats = {
        steps: 0,
        caloriesBurned: 0,
        activeMinutes: 0,
        distance: 0,
        heartRate: 0,
        sleep: {
          hours: 0,
          quality: 'Good',
          deepSleepMinutes: 0,
          lightSleepMinutes: 0,
          remSleepMinutes: 0,
        },
        water: {
          current: 0,
          goal: this.waterGoal,
          milliliters: 0,
        },
        nutrition: {
          caloriesConsumed: 0,
          caloriesGoal: this.caloriesGoal,
          protein: 0,
          carbs: 0,
          fat: 0,
          fiber: 0,
        },
        lastUpdated: new Date().toISOString(),
      };
      
      // Cache this as fallback data
      this.cachedData = healthData;
      this.lastFetchTime = now;
      await this.saveCachedData();
      
      return healthData;
    }
  }
  
  // ----- Health Connect Methods (Android) -----
  
  private async getHealthConnectSteps(date: string): Promise<number> {
    try {
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0, 0);
      
      const endDate = new Date(date);
      endDate.setHours(23, 59, 59, 999);
      
      const response = await HealthConnect.readRecords('Steps', {
        timeRangeFilter: {
          operator: 'between',
          startTime: startDate.toISOString(),
          endTime: endDate.toISOString(),
        },
      });
      
      return response.reduce((sum, record) => sum + record.count, 0);
    } catch (error) {
      console.error('Error getting steps from Health Connect:', error);
      return 0;
    }
  }
  
  private async getHealthConnectDistance(date: string): Promise<number> {
    try {
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0, 0);
      
      const endDate = new Date(date);
      endDate.setHours(23, 59, 59, 999);
      
      const response = await HealthConnect.readRecords('Distance', {
        timeRangeFilter: {
          operator: 'between',
          startTime: startDate.toISOString(),
          endTime: endDate.toISOString(),
        },
      });
      
      return response.reduce((sum, record) => sum + record.meters, 0) / 1000; // Convert to km
    } catch (error) {
      console.error('Error getting distance from Health Connect:', error);
      return 0;
    }
  }
  
  private async getHealthConnectCaloriesBurned(date: string): Promise<number> {
    try {
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0, 0);
      
      const endDate = new Date(date);
      endDate.setHours(23, 59, 59, 999);
      
      const response = await HealthConnect.readRecords('ActiveCaloriesBurned', {
        timeRangeFilter: {
          operator: 'between',
          startTime: startDate.toISOString(),
          endTime: endDate.toISOString(),
        },
      });
      
      return response.reduce((sum, record) => sum + record.energy.inKilocalories, 0);
    } catch (error) {
      console.error('Error getting calories burned from Health Connect:', error);
      return 0;
    }
  }
  
  private async getHealthConnectActiveMinutes(date: string): Promise<number> {
    try {
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0, 0);
      
      const endDate = new Date(date);
      endDate.setHours(23, 59, 59, 999);
      
      const response = await HealthConnect.readRecords('ExerciseSession', {
        timeRangeFilter: {
          operator: 'between',
          startTime: startDate.toISOString(),
          endTime: endDate.toISOString(),
        },
      });
      
      let totalMinutes = 0;
      
      for (const record of response) {
        const startTime = new Date(record.startTime).getTime();
        const endTime = new Date(record.endTime).getTime();
        totalMinutes += (endTime - startTime) / (1000 * 60);
      }
      
      return Math.round(totalMinutes);
    } catch (error) {
      console.error('Error getting active minutes from Health Connect:', error);
      return 0;
    }
  }
  
  private async getHealthConnectHeartRate(): Promise<number> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setHours(endDate.getHours() - 1); // Last hour
      
      const response = await HealthConnect.readRecords('HeartRate', {
        timeRangeFilter: {
          operator: 'between',
          startTime: startDate.toISOString(),
          endTime: endDate.toISOString(),
        },
        limit: 10,
        ascendingOrder: false,
      });
      
      if (response.length > 0) {
        // Return most recent heart rate
        return response[0].beatsPerMinute;
      }
      
      return 0;
    } catch (error) {
      console.error('Error getting heart rate from Health Connect:', error);
      return 0;
    }
  }
  
  private async getHealthConnectSleep(date: string): Promise<{
    hours: number;
    deepSleepMinutes: number;
    lightSleepMinutes: number;
    remSleepMinutes: number;
  }> {
    try {
      const startDate = new Date(date);
      startDate.setHours(20, 0, 0, 0); // Start from 8 PM the previous day
      startDate.setDate(startDate.getDate() - 1);
      
      const endDate = new Date(date);
      endDate.setHours(12, 0, 0, 0); // End at noon
      
      const response = await HealthConnect.readRecords('SleepSession', {
        timeRangeFilter: {
          operator: 'between',
          startTime: startDate.toISOString(),
          endTime: endDate.toISOString(),
        },
      });
      
      let totalSleepMinutes = 0;
      let deepSleepMinutes = 0;
      let lightSleepMinutes = 0;
      let remSleepMinutes = 0;
      
      for (const record of response) {
        const startTime = new Date(record.startTime).getTime();
        const endTime = new Date(record.endTime).getTime();
        const durationMinutes = (endTime - startTime) / (1000 * 60);
        
        totalSleepMinutes += durationMinutes;
        
        // If we have stages data
        if (record.stages) {
          for (const stage of record.stages) {
            const stageStart = new Date(stage.startTime).getTime();
            const stageEnd = new Date(stage.endTime).getTime();
            const stageDuration = (stageEnd - stageStart) / (1000 * 60);
            
            if (stage.stage === 'deep') {
              deepSleepMinutes += stageDuration;
            } else if (stage.stage === 'light') {
              lightSleepMinutes += stageDuration;
            } else if (stage.stage === 'rem') {
              remSleepMinutes += stageDuration;
            }
          }
        } else {
          // If no stage data, estimate based on typical sleep cycles
          deepSleepMinutes += durationMinutes * 0.2; // ~20% deep sleep
          lightSleepMinutes += durationMinutes * 0.5; // ~50% light sleep
          remSleepMinutes += durationMinutes * 0.25; // ~25% REM sleep
        }
      }
      
      return {
        hours: totalSleepMinutes / 60,
        deepSleepMinutes,
        lightSleepMinutes,
        remSleepMinutes,
      };
    } catch (error) {
      console.error('Error getting sleep data from Health Connect:', error);
      return { hours: 0, deepSleepMinutes: 0, lightSleepMinutes: 0, remSleepMinutes: 0 };
    }
  }
  
  private async getHealthConnectWater(date: string): Promise<number> {
    try {
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0, 0);
      
      const endDate = new Date(date);
      endDate.setHours(23, 59, 59, 999);
      
      const response = await HealthConnect.readRecords('Hydration', {
        timeRangeFilter: {
          operator: 'between',
          startTime: startDate.toISOString(),
          endTime: endDate.toISOString(),
        },
      });
      
      return response.reduce((sum, record) => sum + record.volume.inMilliliters, 0);
    } catch (error) {
      console.error('Error getting water intake from Health Connect:', error);
      return 0;
    }
  }
  
  private async getHealthConnectNutrition(date: string): Promise<{
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  }> {
    try {
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0, 0);
      
      const endDate = new Date(date);
      endDate.setHours(23, 59, 59, 999);
      
      const response = await HealthConnect.readRecords('Nutrition', {
        timeRangeFilter: {
          operator: 'between',
          startTime: startDate.toISOString(),
          endTime: endDate.toISOString(),
        },
      });
      
      let calories = 0;
      let protein = 0;
      let carbs = 0;
      let fat = 0;
      let fiber = 0;
      
      for (const record of response) {
        calories += record.energy?.inKilocalories || 0;
        protein += record.protein?.inGrams || 0;
        carbs += record.carbohydrates?.inGrams || 0;
        fat += record.fat?.inGrams || 0;
        fiber += record.dietaryFiber?.inGrams || 0;
      }
      
      return {
        calories,
        protein,
        carbs,
        fat,
        fiber,
      };
    } catch (error) {
      console.error('Error getting nutrition data from Health Connect:', error);
      return { calories: 0, protein: 0, carbs: 0, fat: 0, fiber: 0 };
    }
  }
  
  // Track a water intake event
  async logWaterIntake(glasses: number): Promise<void> {
    try {
      const milliliters = glasses * 250;
      
      if (Platform.OS === 'ios' && AppleHealthKit) {
        // Log water to HealthKit
        const options = {
          amount: milliliters,
          unit: 'mL',
          date: new Date().toISOString(),
        };
        
        AppleHealthKit.saveWater(options, (error: string) => {
          if (error) {
            console.error('Error saving water to HealthKit:', error);
          }
        });
      } else if (Platform.OS === 'android') {
        // Try Health Connect first
        if (HealthConnect) {
          try {
            const { availabilityStatus } = await HealthConnect.initialize();
            if (availabilityStatus === 'AVAILABLE') {
              await HealthConnect.insertRecords([
                {
                  recordType: 'Hydration',
                  volume: {
                    inMilliliters: milliliters,
                  },
                  time: new Date().toISOString(),
                },
              ]);
            }
          } catch (error) {
            console.error('Error saving water to Health Connect:', error);
            
            // Fall back to Google Fit or local storage
            if (GoogleFit) {
              // Google Fit does not directly support water intake
              await this.storeWaterData(glasses, milliliters);
            } else {
              await this.storeWaterData(glasses, milliliters);
            }
          }
        } else if (GoogleFit) {
          // Google Fit does not directly support water intake
          await this.storeWaterData(glasses, milliliters);
        } else {
          // For web or unsupported platforms, store locally
          await this.storeWaterData(glasses, milliliters);
        }
      } else {
        // For web or unsupported platforms, store locally
        await this.storeWaterData(glasses, milliliters);
      }
      
      // Update our cached data if it exists
      if (this.cachedData) {
        this.cachedData.water.current += glasses;
        this.cachedData.water.milliliters += milliliters;
        this.cachedData.lastUpdated = new Date().toISOString();
        await this.saveCachedData();
      }
      
      // Also update in Firebase if user is logged in
      try {
        const auth = getAuth();
        const currentUser = auth.currentUser;
        
        if (currentUser) {
          const today = new Date().toISOString().split('T')[0];
          const docId = `${currentUser.uid}_${today}`;
          
          // First get current values
          const dailySummaryRef = doc(db, 'health_daily_summary', docId);
          const dailySummarySnap = await getDoc(dailySummaryRef);
          
          const currentData = dailySummarySnap.exists() ? dailySummarySnap.data() : null;
          const currentGlasses = currentData?.water_glasses ?? 0;
          const currentMl = currentData?.water_ml ?? 0;
          
          // Update with new values
          await setDoc(dailySummaryRef, {
            user_id: currentUser.uid,
            date: today,
            water_glasses: currentGlasses + glasses,
            water_ml: currentMl + milliliters,
            updated_at: new Date().toISOString()
          }, { merge: true });
        }
      } catch (error) {
        console.error('Error updating water intake in Firebase:', error);
      }
    } catch (error) {
      console.error('Error logging water intake:', error);
    }
  }
  
  // Track food intake
  async logFoodIntake(calories: number, protein: number, carbs: number, fat: number, fiber: number = 0): Promise<void> {
    try {
      if (Platform.OS === 'ios' && AppleHealthKit) {
        // Log nutrition data to HealthKit
        const now = new Date().toISOString();
        
        // Log calories
        const caloriesOptions = {
          energyConsumed: calories,
          date: now,
          unit: 'kcal',
        };
        
        AppleHealthKit.saveFood(caloriesOptions, (error: string) => {
          if (error) {
            console.error('Error saving calories to HealthKit:', error);
          }
        });
        
        // Log macronutrients
        if (protein > 0) {
          const proteinOptions = {
            nutritionType: AppleHealthKit.Constants.Nutrition.Protein,
            value: protein,
            date: now,
            unit: 'g',
          };
          
          AppleHealthKit.saveFood(proteinOptions, (error: string) => {
            if (error) {
              console.error('Error saving protein to HealthKit:', error);
            }
          });
        }
        
        if (carbs > 0) {
          const carbsOptions = {
            nutritionType: AppleHealthKit.Constants.Nutrition.Carbohydrates,
            value: carbs,
            date: now,
            unit: 'g',
          };
          
          AppleHealthKit.saveFood(carbsOptions, (error: string) => {
            if (error) {
              console.error('Error saving carbs to HealthKit:', error);
            }
          });
        }
        
        if (fat > 0) {
          const fatOptions = {
            nutritionType: AppleHealthKit.Constants.Nutrition.TotalFat,
            value: fat,
            date: now,
            unit: 'g',
          };
          
          AppleHealthKit.saveFood(fatOptions, (error: string) => {
            if (error) {
              console.error('Error saving fat to HealthKit:', error);
            }
          });
        }
        
        if (fiber > 0) {
          const fiberOptions = {
            nutritionType: AppleHealthKit.Constants.Nutrition.DietaryFiber,
            value: fiber,
            date: now,
            unit: 'g',
          };
          
          AppleHealthKit.saveFood(fiberOptions, (error: string) => {
            if (error) {
              console.error('Error saving fiber to HealthKit:', error);
            }
          });
        }
      } else if (Platform.OS === 'android') {
        // Try Health Connect first
        if (HealthConnect) {
          try {
            const { availabilityStatus } = await HealthConnect.initialize();
            if (availabilityStatus === 'AVAILABLE') {
              // Create a nutrition record
              await HealthConnect.insertRecords([
                {
                  recordType: 'Nutrition',
                  energy: {
                    inKilocalories: calories
                  },
                  protein: {
                    inGrams: protein
                  },
                  carbohydrates: {
                    inGrams: carbs
                  },
                  fat: {
                    inGrams: fat
                  },
                  dietaryFiber: {
                    inGrams: fiber
                  },
                  time: new Date().toISOString(),
                },
              ]);
            } else {
              // Fall back to local storage
              await this.storeNutritionData(calories, protein, carbs, fat, fiber);
            }
          } catch (error) {
            console.error('Error saving nutrition to Health Connect:', error);
            await this.storeNutritionData(calories, protein, carbs, fat, fiber);
          }
        } else if (GoogleFit) {
          // Store nutrition data for Android
          await this.storeNutritionData(calories, protein, carbs, fat, fiber);
        } else {
          // For web or unsupported platforms, store locally
          await this.storeNutritionData(calories, protein, carbs, fat, fiber);
        }
      } else {
        // For web or unsupported platforms, store locally
        await this.storeNutritionData(calories, protein, carbs, fat, fiber);
      }
      
      // Update our cached data if it exists
      if (this.cachedData) {
        this.cachedData.nutrition.caloriesConsumed += calories;
        this.cachedData.nutrition.protein += protein;
        this.cachedData.nutrition.carbs += carbs;
        this.cachedData.nutrition.fat += fat;
        this.cachedData.nutrition.fiber += fiber;
        this.cachedData.lastUpdated = new Date().toISOString();
        await this.saveCachedData();
      }
      
      // Also update in Firebase if user is logged in
      try {
        const auth = getAuth();
        const currentUser = auth.currentUser;
        
        if (currentUser) {
          const today = new Date().toISOString().split('T')[0];
          const docId = `${currentUser.uid}_${today}`;
          
          // First get current values
          const dailySummaryRef = doc(db, 'health_daily_summary', docId);
          const dailySummarySnap = await getDoc(dailySummaryRef);
          
          const currentData = dailySummarySnap.exists() ? dailySummarySnap.data() : null;
          const currentCalories = currentData?.calories_consumed ?? 0;
          const currentProtein = currentData?.protein ?? 0;
          const currentCarbs = currentData?.carbs ?? 0;
          const currentFat = currentData?.fat ?? 0;
          const currentFiber = currentData?.fiber ?? 0;
          
          // Update with new values
          await setDoc(dailySummaryRef, {
            user_id: currentUser.uid,
            date: today,
            calories_consumed: currentCalories + calories,
            protein: currentProtein + protein,
            carbs: currentCarbs + carbs,
            fat: currentFat + fat,
            fiber: currentFiber + fiber,
            updated_at: new Date().toISOString()
          }, { merge: true });
        }
      } catch (error) {
        console.error('Error updating nutrition in Firebase:', error);
      }
    } catch (error) {
      console.error('Error logging food intake:', error);
    }
  }
  
  // Helper methods for Apple HealthKit data retrieval
  private getAppleHealthSteps(date: string): Promise<number> {
    return new Promise((resolve) => {
      const options = {
        date: new Date(date).toISOString(),
      };
      
      AppleHealthKit.getStepCount(options, (err: string, results: { value: number }) => {
        if (err) {
          console.error('Error getting steps from HealthKit:', err);
          resolve(0);
        } else {
          resolve(results.value || 0);
        }
      });
    });
  }
  
  private getAppleHealthCaloriesBurned(date: string): Promise<number> {
    return new Promise((resolve) => {
      const options = {
        date: new Date(date).toISOString(),
      };
      
      AppleHealthKit.getActiveEnergyBurned(options, (err: string, results: { value: number }) => {
        if (err) {
          console.error('Error getting calories burned from HealthKit:', err);
          resolve(0);
        } else {
          resolve(results.value || 0);
        }
      });
    });
  }
  
  private getAppleHealthDistance(date: string): Promise<number> {
    return new Promise((resolve) => {
      const options = {
        date: new Date(date).toISOString(),
        unit: 'km',
      };
      
      AppleHealthKit.getDistanceWalkingRunning(options, (err: string, results: { value: number }) => {
        if (err) {
          console.error('Error getting distance from HealthKit:', err);
          resolve(0);
        } else {
          resolve(results.value || 0);
        }
      });
    });
  }
  
  private getAppleHealthActiveMinutes(date: string): Promise<number> {
    return new Promise((resolve) => {
      const options = {
        date: new Date(date).toISOString(),
      };
      
      AppleHealthKit.getAppleExerciseTime(options, (err: string, results: { value: number }) => {
        if (err) {
          console.error('Error getting active minutes from HealthKit:', err);
          resolve(0);
        } else {
          resolve(results.value || 0);
        }
      });
    });
  }
  
  private getAppleHealthHeartRate(): Promise<number> {
    return new Promise((resolve) => {
      const options = {
        unit: 'bpm',
        limit: 1, // Most recent
      };
      
      AppleHealthKit.getHeartRateSamples(options, (err: string, results: { value: number }[]) => {
        if (err) {
          console.error('Error getting heart rate from HealthKit:', err);
          resolve(0);
        } else {
          resolve(results[0]?.value || 0);
        }
      });
    });
  }
  
  private getAppleHealthSleep(date: string): Promise<{
    hours: number;
    deepSleepMinutes: number;
    lightSleepMinutes: number;
    remSleepMinutes: number;
  }> {
    return new Promise((resolve) => {
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0, 0);
      
      const endDate = new Date(date);
      endDate.setHours(23, 59, 59, 999);
      
      const options = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      };
      
      AppleHealthKit.getSleepSamples(options, (err: string, results: any[]) => {
        if (err) {
          console.error('Error getting sleep data from HealthKit:', err);
          resolve({ hours: 0, deepSleepMinutes: 0, lightSleepMinutes: 0, remSleepMinutes: 0 });
        } else {
          // Calculate sleep metrics from the samples
          let totalSleepMinutes = 0;
          let deepSleepMinutes = 0;
          let lightSleepMinutes = 0;
          let remSleepMinutes = 0;
          
          for (const sample of results) {
            const startTime = new Date(sample.startDate).getTime();
            const endTime = new Date(sample.endDate).getTime();
            const durationMinutes = (endTime - startTime) / (1000 * 60);
            
            totalSleepMinutes += durationMinutes;
            
            // Categorize by sleep type
            switch (sample.value) {
              case AppleHealthKit.Constants.SleepValues.DeepSleep:
                deepSleepMinutes += durationMinutes;
                break;
              case AppleHealthKit.Constants.SleepValues.LightSleep:
                lightSleepMinutes += durationMinutes;
                break;
              case AppleHealthKit.Constants.SleepValues.REMSleep:
                remSleepMinutes += durationMinutes;
                break;
              default:
                // Awake or unknown, don't count
                break;
            }
          }
          
          resolve({
            hours: totalSleepMinutes / 60,
            deepSleepMinutes,
            lightSleepMinutes,
            remSleepMinutes,
          });
        }
      });
    });
  }
  
  private getAppleHealthWater(date: string): Promise<number> {
    return new Promise((resolve) => {
      const options = {
        date: new Date(date).toISOString(),
        unit: 'ml',
      };
      
      AppleHealthKit.getWater(options, (err: string, results: { value: number }) => {
        if (err) {
          console.error('Error getting water intake from HealthKit:', err);
          resolve(0);
        } else {
          resolve(results.value || 0);
        }
      });
    });
  }
  
  private getAppleHealthNutrition(date: string): Promise<{
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  }> {
    return new Promise((resolve) => {
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0, 0);
      
      const endDate = new Date(date);
      endDate.setHours(23, 59, 59, 999);
      
      const options = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        type: AppleHealthKit.Constants.FoodNutrient.Calories,
      };
      
      let calories = 0;
      let protein = 0;
      let carbs = 0;
      let fat = 0;
      let fiber = 0;
      
      // Get calories
      AppleHealthKit.getDietarySamples(options, (err: string, results: any[]) => {
        if (err) {
          console.error('Error getting calorie intake from HealthKit:', err);
        } else {
          calories = results.reduce((total, sample) => total + sample.value, 0);
        }
        
        // Get protein
        options.type = AppleHealthKit.Constants.FoodNutrient.Protein;
        AppleHealthKit.getDietarySamples(options, (err: string, results: any[]) => {
          if (!err) {
            protein = results.reduce((total, sample) => total + sample.value, 0);
          }
          
          // Get carbs
          options.type = AppleHealthKit.Constants.FoodNutrient.Carbohydrates;
          AppleHealthKit.getDietarySamples(options, (err: string, results: any[]) => {
            if (!err) {
              carbs = results.reduce((total, sample) => total + sample.value, 0);
            }
            
            // Get fat
            options.type = AppleHealthKit.Constants.FoodNutrient.TotalFat;
            AppleHealthKit.getDietarySamples(options, (err: string, results: any[]) => {
              if (!err) {
                fat = results.reduce((total, sample) => total + sample.value, 0);
              }
              
              // Get fiber
              options.type = AppleHealthKit.Constants.FoodNutrient.DietaryFiber;
              AppleHealthKit.getDietarySamples(options, (err: string, results: any[]) => {
                if (!err) {
                  fiber = results.reduce((total, sample) => total + sample.value, 0);
                }
                
                resolve({ calories, protein, carbs, fat, fiber });
              });
            });
          });
        });
      });
    });
  }
  
  // Helper methods for Google Fit data retrieval
  private async getGoogleFitSteps(date: string): Promise<number> {
    try {
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0, 0);
      
      const endDate = new Date(date);
      endDate.setHours(23, 59, 59, 999);
      
      const options = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      };
      
      const result = await GoogleFit.getDailySteps(options);
      return result[0]?.steps || 0;
    } catch (error) {
      console.error('Error getting steps from Google Fit:', error);
      return 0;
    }
  }
  
  private async getGoogleFitCaloriesBurned(date: string): Promise<number> {
    try {
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0, 0);
      
      const endDate = new Date(date);
      endDate.setHours(23, 59, 59, 999);
      
      const options = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        dataType: GoogleFit.DataType.CALORIES,
      };
      
      const result = await GoogleFit.getDailyCalorieSamples(options);
      return result.reduce((total: number, sample: any) => total + sample.calorie, 0);
    } catch (error) {
      console.error('Error getting calories burned from Google Fit:', error);
      return 0;
    }
  }
  
  private async getGoogleFitDistance(date: string): Promise<number> {
    try {
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0, 0);
      
      const endDate = new Date(date);
      endDate.setHours(23, 59, 59, 999);
      
      const options = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      };
      
      const result = await GoogleFit.getDailyDistanceSamples(options);
      const totalMeters = result.reduce((total: number, sample: any) => total + sample.distance, 0);
      return totalMeters / 1000; // Convert to kilometers
    } catch (error) {
      console.error('Error getting distance from Google Fit:', error);
      return 0;
    }
  }
  
  private async getGoogleFitActiveMinutes(date: string): Promise<number> {
    try {
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0, 0);
      
      const endDate = new Date(date);
      endDate.setHours(23, 59, 59, 999);
      
      const options = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        basedOnActivity: true,
      };
      
      const result = await GoogleFit.getDailyActivitySamples(options);
      return result.reduce((total: number, sample: any) => {
        // Only count moderate to vigorous activity
        if (sample.intensity >= 3) {
          return total + sample.duration / 60000; // Convert ms to minutes
        }
        return total;
      }, 0);
    } catch (error) {
      console.error('Error getting active minutes from Google Fit:', error);
      return 0;
    }
  }
  
  private async getGoogleFitHeartRate(): Promise<number> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setHours(endDate.getHours() - 1); // Get heart rate data from the last hour
      
      const options = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      };
      
      const result = await GoogleFit.getHeartRateSamples(options);
      if (result.length > 0) {
        // Get most recent heart rate
        const latestSample = result.reduce((latest: any, sample: any) => {
          if (!latest || new Date(sample.startDate).getTime() > new Date(latest.startDate).getTime()) {
            return sample;
          }
          return latest;
        });
        
        return latestSample.value;
      }
      return 0;
    } catch (error) {
      console.error('Error getting heart rate from Google Fit:', error);
      return 0;
    }
  }
  
  private async getGoogleFitSleep(date: string): Promise<{
    hours: number;
    deepSleepMinutes: number;
    lightSleepMinutes: number;
    remSleepMinutes: number;
  }> {
    try {
      const startDate = new Date(date);
      startDate.setHours(0, 0, 0, 0);
      
      const endDate = new Date(date);
      endDate.setHours(23, 59, 59, 999);
      
      const options = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      };
      
      const result = await GoogleFit.getSleepSamples(options);
      
      let totalSleepMinutes = 0;
      let deepSleepMinutes = 0;
      let lightSleepMinutes = 0;
      let remSleepMinutes = 0;
      
      for (const sample of result) {
        const startTime = new Date(sample.startDate).getTime();
        const endTime = new Date(sample.endDate).getTime();
        const durationMinutes = (endTime - startTime) / (1000 * 60);
        
        // Map Google Fit sleep stages to our categories
        // 1 = Awake, 2 = Sleep, 3 = Out of bed, 4 = Light sleep, 5 = Deep sleep, 6 = REM
        switch (sample.sleepStage) {
          case 2: // Generic sleep
          case 4: // Light sleep
            totalSleepMinutes += durationMinutes;
            lightSleepMinutes += durationMinutes;
            break;
          case 5: // Deep sleep
            totalSleepMinutes += durationMinutes;
            deepSleepMinutes += durationMinutes;
            break;
          case 6: // REM
            totalSleepMinutes += durationMinutes;
            remSleepMinutes += durationMinutes;
            break;
          default:
            // Awake or out of bed, don't count
            break;
        }
      }
      
      return {
        hours: totalSleepMinutes / 60,
        deepSleepMinutes,
        lightSleepMinutes,
        remSleepMinutes,
      };
    } catch (error) {
      console.error('Error getting sleep data from Google Fit:', error);
      return { hours: 0, deepSleepMinutes: 0, lightSleepMinutes: 0, remSleepMinutes: 0 };
    }
  }
  
  // Helper methods for local storage (used for platforms without health services or for data Google Fit doesn't support well)
  private async getStoredWaterData(date: string): Promise<{ glasses: number; milliliters: number }> {
    try {
      const key = `${WATER_STORAGE_KEY}_${date}`;
      const data = await AsyncStorage.getItem(key);
      
      if (data) {
        const parsed = JSON.parse(data);
        return {
          glasses: parsed.glasses || 0,
          milliliters: parsed.milliliters || 0,
        };
      }
      
      return { glasses: 0, milliliters: 0 };
    } catch (error) {
      console.error('Error getting stored water data:', error);
      return { glasses: 0, milliliters: 0 };
    }
  }
  
  private async storeWaterData(glasses: number, milliliters: number): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const key = `${WATER_STORAGE_KEY}_${today}`;
      
      // Get existing data
      const existingData = await AsyncStorage.getItem(key) || '{"glasses": 0, "milliliters": 0}';
      const data = JSON.parse(existingData);
      
      // Update data
      data.glasses += glasses;
      data.milliliters += milliliters;
      
      // Store updated data
      await AsyncStorage.setItem(key, JSON.stringify(data));
      
      // Also update the goal storage
      const goalData = await AsyncStorage.getItem(WATER_STORAGE_KEY) || '{}';
      const parsed = JSON.parse(goalData);
      parsed.lastUpdated = new Date().toISOString();
      parsed.goal = this.waterGoal;
      await AsyncStorage.setItem(WATER_STORAGE_KEY, JSON.stringify(parsed));
    } catch (error) {
      console.error('Error storing water data:', error);
    }
  }
  
  private async getStoredNutritionData(date: string): Promise<{
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
  }> {
    try {
      const key = `nutrition_data_${date}`;
      const data = await AsyncStorage.getItem(key);
      
      if (data) {
        const parsed = JSON.parse(data);
        return {
          calories: parsed.calories || 0,
          protein: parsed.protein || 0,
          carbs: parsed.carbs || 0,
          fat: parsed.fat || 0,
          fiber: parsed.fiber || 0,
        };
      }
      
      return { calories: 0, protein: 0, carbs: 0, fat: 0, fiber: 0 };
    } catch (error) {
      console.error('Error getting stored nutrition data:', error);
      return { calories: 0, protein: 0, carbs: 0, fat: 0, fiber: 0 };
    }
  }
  
  private async storeNutritionData(
    calories: number,
    protein: number,
    carbs: number,
    fat: number,
    fiber: number
  ): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const key = `nutrition_data_${today}`;
      
      // Get existing data
      const existingData = await AsyncStorage.getItem(key) || '{"calories": 0, "protein": 0, "carbs": 0, "fat": 0, "fiber": 0}';
      const data = JSON.parse(existingData);
      
      // Update data
      data.calories += calories;
      data.protein += protein;
      data.carbs += carbs;
      data.fat += fat;
      data.fiber += fiber;
      
      // Store updated data
      await AsyncStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
      console.error('Error storing nutrition data:', error);
    }
  }
  
  private async getStoredMoodData(): Promise<(MoodData & { timestamp: string })[]> {
    try {
      const data = await AsyncStorage.getItem(MOOD_STORAGE_KEY);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Error getting stored mood data:', error);
      return [];
    }
  }
}

// Export a singleton instance
export const healthDataConnector = new HealthDataConnector(); 