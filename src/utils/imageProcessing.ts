/**
 * Shared utilities for image processing
 * This file contains reusable functions for image manipulation, optimization, and processing
 */

import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import { Platform } from 'react-native';

/**
 * Standard image sizes for different API scenarios
 */
export const IMAGE_SIZES = {
  THUMBNAIL: { width: 100, height: 100 },
  PREVIEW: { width: 300, height: 300 },
  STANDARD: { width: 800, height: 800 },
  HIGH_QUALITY: { width: 1200, height: 1200 },
};

/**
 * Web-compatible version of uriToBase64 function
 * Uses fetch and FileReader for web platforms
 */
export const webUriToBase64 = async (uri: string): Promise<string> => {
  try {
    const response = await fetch(uri);
    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error('Error converting image to base64 (web):', error);
    throw error;
  }
};

/**
 * Convert a base64 image to a Blob for web uploads
 */
export const base64ToBlob = async (base64: string, type = 'image/jpeg'): Promise<Blob> => {
  const response = await fetch(`data:${type};base64,${base64}`);
  return await response.blob();
};

/**
 * Resize an image to optimize for API uploads
 * @param uri Source image URI
 * @param maxWidth Maximum width (preserves aspect ratio)
 * @param quality JPEG quality (0-1)
 * @returns URI to the resized image
 */
export const resizeImage = async (
  uri: string,
  maxWidth = IMAGE_SIZES.STANDARD.width,
  quality = 0.8
): Promise<string> => {
  try {
    const result = await ImageManipulator.manipulateAsync(
      uri,
      [{ resize: { width: maxWidth } }],
      { compress: quality, format: ImageManipulator.SaveFormat.JPEG }
    );
    return result.uri;
  } catch (error) {
    console.error('Error resizing image:', error);
    return uri; // Return original if resize fails
  }
};

/**
 * Get base64 data from an image URI
 */
export const getBase64FromUri = async (uri: string): Promise<string> => {
  try {
    // Handle web platform differently
    if (Platform.OS === 'web') {
      const response = await fetch(uri);
      const blob = await response.blob();
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          const base64 = result.split(',')[1];
          resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    }
    
    // For native platforms
    const base64 = await FileSystem.readAsStringAsync(uri, {
      encoding: FileSystem.EncodingType.Base64,
    });
    return base64;
  } catch (error) {
    console.error('Error getting base64 from URI:', error);
    throw error;
  }
};

/**
 * Calculate image file size in KB
 */
export const getImageFileSize = async (uri: string): Promise<number> => {
  try {
    if (Platform.OS === 'web') {
      const response = await fetch(uri);
      const blob = await response.blob();
      return blob.size / 1024; // Convert bytes to KB
    }
    
    const fileInfo = await FileSystem.getInfoAsync(uri);
    // Check if the file exists and has size information
    if (fileInfo.exists && 'size' in fileInfo) {
      return fileInfo.size / 1024; // Convert bytes to KB
    }
    return 0;
  } catch (error) {
    console.error('Error getting image file size:', error);
    return 0;
  }
};

/**
 * Optimize image for API upload based on size constraints
 * @param uri Source image URI
 * @param maxSizeKB Maximum size in KB
 * @returns Optimized image URI
 */
export const optimizeImageForUpload = async (
  uri: string,
  maxSizeKB = 1024 // 1MB default limit
): Promise<string> => {
  try {
    let currentUri = uri;
    let currentSize = await getImageFileSize(currentUri);
    let quality = 0.9;
    let width = IMAGE_SIZES.HIGH_QUALITY.width;
    
    // Progressively reduce quality and size until under the limit
    while (currentSize > maxSizeKB && quality > 0.3) {
      if (currentSize > maxSizeKB * 3) {
        // If way too large, reduce dimensions more aggressively
        width = Math.floor(width * 0.7);
      } else {
        // Otherwise just reduce quality
        quality -= 0.1;
      }
      
      currentUri = await resizeImage(uri, width, quality);
      currentSize = await getImageFileSize(currentUri);
    }
    
    return currentUri;
  } catch (error) {
    console.error('Error optimizing image:', error);
    return uri; // Return original if optimization fails
  }
};

/**
 * Extract dominant colors from an image
 * This is a simplified implementation that works across all platforms without DOM dependencies
 * @param uri Source image URI
 * @param colorCount Number of dominant colors to extract (default: 3)
 * @returns Array of dominant colors as hex strings
 */
export const extractDominantColors = async (uri: string, colorCount = 3): Promise<string[]> => {
  try {
    // Use ImageManipulator to resize the image for faster processing
    const resizedImage = await ImageManipulator.manipulateAsync(
      uri,
      [{ resize: { width: 100 } }],
      { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
    );
    
    // Get image data as base64
    const base64 = await FileSystem.readAsStringAsync(resizedImage.uri, {
      encoding: FileSystem.EncodingType.Base64,
    });
    
    // Extract colors from the base64 data
    return extractColorsFromImageData(base64, colorCount);
  } catch (error) {
    console.error('Error extracting dominant colors:', error);
    // Return fallback colors
    return generateRandomColors(colorCount);
  }
};

/**
 * Extract colors from image data
 * This uses a simplified color quantization approach
 */
function extractColorsFromImageData(base64: string, colorCount: number): string[] {
  try {
    // Simple color frequency analysis
    const colorMap: Record<string, number> = {};
    const colorGroupingFactor = 24; // Higher = fewer distinct colors
    
    // Sample bytes from the base64 string
    // This is a very simplified approach that won't work perfectly for all images
    // but provides a reasonable approximation without needing browser APIs
    for (let i = 0; i < base64.length - 8; i += 40) {
      // Extract RGB values from base64 chunks
      const chunk = base64.substring(i, i + 4);
      const charCodes = Array.from(chunk).map(c => c.charCodeAt(0));
      
      // Skip if chunk doesn't contain valid color data
      if (charCodes.some(c => c < 32 || c > 126)) continue;
      
      // Use the character codes to generate RGB values (simplified approach)
      const r = (charCodes[0] % 256);
      const g = (charCodes[1] % 256);
      const b = (charCodes[2] % 256);
      
      // Quantize the colors to reduce the number of unique colors
      const quantizedR = Math.floor(r / colorGroupingFactor) * colorGroupingFactor;
      const quantizedG = Math.floor(g / colorGroupingFactor) * colorGroupingFactor;
      const quantizedB = Math.floor(b / colorGroupingFactor) * colorGroupingFactor;
      
      // Skip black and white
      if ((quantizedR + quantizedG + quantizedB < 30) || 
          (quantizedR > 220 && quantizedG > 220 && quantizedB > 220)) {
        continue;
      }
      
      const hex = rgbToHex(quantizedR, quantizedG, quantizedB);
      colorMap[hex] = (colorMap[hex] || 0) + 1;
    }
    
    // Sort colors by frequency
    const sortedColors = Object.entries(colorMap)
      .sort((a, b) => b[1] - a[1])
      .map(([color]) => color);
    
    // Return the top N colors
    if (sortedColors.length >= colorCount) {
      return sortedColors.slice(0, colorCount);
    }
    
    // If we didn't get enough colors, add some generated ones
    return [...sortedColors, ...generateRandomColors(colorCount - sortedColors.length)];
  } catch (error) {
    console.error('Error in color extraction:', error);
    return generateRandomColors(colorCount);
  }
}

/**
 * Generate random colors for fallback
 */
function generateRandomColors(count: number): string[] {
  const result: string[] = [];
  for (let i = 0; i < count; i++) {
    const r = Math.floor(Math.random() * 200 + 20);
    const g = Math.floor(Math.random() * 200 + 20);
    const b = Math.floor(Math.random() * 200 + 20);
    result.push(rgbToHex(r, g, b));
  }
  return result;
}

/**
 * Convert RGB values to hex color string
 */
function rgbToHex(r: number, g: number, b: number): string {
  return `#${[r, g, b].map(x => {
    const hex = Math.max(0, Math.min(255, x)).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  }).join('')}`;
}

/**
 * Convert a remote HTTP URL to a local URI or base64 data
 * Useful for offline caching of images
 */
export const downloadImage = async (
  url: string,
  saveToFileSystem = true
): Promise<string> => {
  try {
    if (saveToFileSystem) {
      const fileUri = `${FileSystem.cacheDirectory}${Date.now()}.jpg`;
      const downloadResult = await FileSystem.downloadAsync(url, fileUri);
      
      if (downloadResult.status !== 200) {
        throw new Error(`Download failed: ${downloadResult.status}`);
      }
      
      return downloadResult.uri;
    } else {
      // Return as base64
      const downloadResult = await fetch(url);
      const blob = await downloadResult.blob();
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    }
  } catch (error) {
    console.error('Error downloading image:', error);
    throw error;
  }
};

/**
 * Cross-platform file upload utility
 * Handles the differences between web and native platforms for file uploads
 */
export const uploadFileToStorage = async (
  uri: string,
  bucket: string,
  filePath: string,
  contentType: string = 'image/jpeg',
  options?: {
    resize?: { width: number; height?: number };
    quality?: number;
  }
): Promise<{ success: boolean; url?: string; error?: string }> => {
  try {
    const { storage } = await import('@/lib/firebase');
    const { ref, uploadBytes, getDownloadURL } = await import('firebase/storage');
    
    // Process image if needed
    let finalUri = uri;
    if (options?.resize) {
      const ImageManipulator = await import('expo-image-manipulator');
      const processed = await ImageManipulator.manipulateAsync(
        uri,
        [{ resize: options.resize }],
        { 
          compress: options.quality || 0.7, 
          format: ImageManipulator.SaveFormat.JPEG 
        }
      );
      finalUri = processed.uri;
    }
    
    // Get blob/arrayBuffer data in a platform-independent way
    let blob;
    if (Platform.OS === 'web') {
      // For web
      const response = await fetch(finalUri);
      blob = await response.blob();
    } else {
      // For native platforms
      const FileSystem = await import('expo-file-system');
      const base64 = await FileSystem.readAsStringAsync(finalUri, {
        encoding: FileSystem.EncodingType.Base64,
      });
      
      // Convert base64 to blob
      const response = await fetch(`data:${contentType};base64,${base64}`);
      blob = await response.blob();
    }
    
    // Upload to Firebase Storage
    const storageRef = ref(storage, `${bucket}/${filePath}`);
    const uploadResult = await uploadBytes(storageRef, blob, { contentType });
    
    // Get public URL
    const url = await getDownloadURL(uploadResult.ref);
    
    return { success: true, url };
  } catch (error) {
    console.error('Error in uploadFileToStorage:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}; 