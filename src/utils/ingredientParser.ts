/**
 * Utility for parsing ingredient strings into structured data
 * Handles various formats like "1 cup flour", "2 large eggs", etc.
 */

export interface ParsedIngredient {
  quantity: string;
  unit: string;
  name: string;
  isWhole: boolean;  // Whether this is a whole item (e.g., "1 apple" vs "1 cup flour")
  category: string;
}

// Common cooking units
const UNITS = [
  // Volume
  'cup', 'cups', 'c.', 'c',
  'tablespoon', 'tablespoons', 'tbsp', 'tbsp.', 'tbs', 'tbs.',
  'teaspoon', 'teaspoons', 'tsp', 'tsp.', 
  'fluid ounce', 'fluid ounces', 'fl oz', 'fl. oz.',
  'milliliter', 'milliliters', 'ml', 'ml.',
  'liter', 'liters', 'l', 'l.',
  'gallon', 'gallons', 'gal', 'gal.',
  'quart', 'quarts', 'qt', 'qt.',
  'pint', 'pints', 'pt', 'pt.',
  
  // Weight
  'pound', 'pounds', 'lb', 'lb.', 'lbs', 'lbs.',
  'ounce', 'ounces', 'oz', 'oz.',
  'gram', 'grams', 'g', 'g.',
  'kilogram', 'kilograms', 'kg', 'kg.',
  
  // Count
  'bunch', 'bunches',
  'clove', 'cloves',
  'slice', 'slices',
  'piece', 'pieces',
  'package', 'packages', 'pkg', 'pkg.',
  'can', 'cans',
  'jar', 'jars',
  'bottle', 'bottles',
  'pinch', 'pinches',
  'dash', 'dashes',
  
  // Size descriptors that act like units
  'small', 'medium', 'large', 'extra large', 'xl'
];

// Food categories for organization
const FOOD_CATEGORIES: Record<string, string[]> = {
  'Produce': [
    'fruit', 'vegetable', 'apple', 'banana', 'orange', 'lettuce', 'tomato', 
    'potato', 'carrot', 'onion', 'garlic', 'avocado', 'lemon', 'lime', 
    'berries', 'spinach', 'kale', 'cilantro', 'parsley', 'basil', 'mint',
    'cucumber', 'zucchini', 'squash', 'pumpkin', 'pepper', 'bell pepper',
    'celery', 'broccoli', 'cauliflower', 'mushroom', 'ginger', 'scallion'
  ],
  'Meat & Seafood': [
    'meat', 'chicken', 'beef', 'pork', 'fish', 'salmon', 'tuna', 'shrimp', 
    'turkey', 'sausage', 'bacon', 'steak', 'ground beef', 'ground turkey',
    'ham', 'lamb', 'veal', 'duck', 'tilapia', 'cod', 'crab', 'lobster',
    'scallop', 'clam', 'oyster', 'mussel', 'anchovy', 'sardine'
  ],
  'Dairy & Eggs': [
    'milk', 'cheese', 'yogurt', 'cream', 'butter', 'egg', 'dairy', 'sour cream',
    'cottage cheese', 'ricotta', 'mozzarella', 'cheddar', 'parmesan', 'feta',
    'brie', 'gouda', 'blue cheese', 'cream cheese', 'half and half', 'whipping cream'
  ],
  'Bakery': [
    'bread', 'roll', 'bun', 'bagel', 'muffin', 'tortilla', 'pita', 'croissant',
    'pastry', 'dough', 'cake', 'pie', 'cookie', 'brownie', 'biscuit', 'cracker'
  ],
  'Pantry': [
    'rice', 'pasta', 'cereal', 'flour', 'sugar', 'oil', 'vinegar', 'sauce', 
    'spice', 'herb', 'can', 'jar', 'dried', 'canned', 'boxed', 'salt', 'pepper',
    'oregano', 'thyme', 'rosemary', 'cumin', 'paprika', 'cinnamon', 'vanilla',
    'baking powder', 'baking soda', 'honey', 'maple syrup', 'soy sauce', 
    'ketchup', 'mustard', 'mayonnaise', 'olive oil', 'vegetable oil', 'peanut butter',
    'jelly', 'jam', 'beans', 'lentils', 'chickpeas', 'quinoa', 'oats', 'nuts', 'seeds'
  ],
  'Frozen': [
    'frozen', 'ice cream', 'popsicle', 'fries', 'frozen pizza', 'frozen vegetables',
    'frozen fruit', 'frozen meal', 'freezer', 'ice'
  ],
  'Beverages': [
    'water', 'juice', 'soda', 'coffee', 'tea', 'drink', 'milk', 'almond milk',
    'soy milk', 'wine', 'beer', 'alcohol', 'cocktail', 'smoothie', 'shake'
  ]
};

/**
 * Parse an ingredient string into structured data
 * 
 * @param ingredientString The raw ingredient string (e.g., "1 cup flour")
 * @returns A structured ParsedIngredient object
 */
export function parseIngredient(ingredientString: string): ParsedIngredient {
  // Default values
  let quantity = '';
  let unit = '';
  let name = ingredientString.trim();
  let isWhole = false;
  let category = 'Other';
  
  // Remove any "to taste" or optional notes
  name = name.replace(/\(.*?\)/g, '').trim(); // Remove content in parentheses
  name = name.replace(/,\s*to taste/i, '').trim();
  name = name.replace(/to taste/i, '').trim();
  name = name.replace(/optional/i, '').trim();
  
  // Split the ingredient string into words
  const words = name.split(/\s+/);
  
  // Try to find the quantity (first part is usually a number or fraction)
  if (words.length > 0) {
    const firstWord = words[0];
    
    // Check if first word is a number or fraction
    if (isQuantity(firstWord)) {
      quantity = firstWord;
      words.shift();
      
      // Check if there might be a range (e.g., "1-2" or "1 to 2")
      if (words.length > 0) {
        if (words[0] === '-' || words[0] === 'to') {
          if (words.length > 1 && isQuantity(words[1])) {
            quantity += ` ${words[0]} ${words[1]}`;
            words.splice(0, 2);
          }
        } else if (words[0].startsWith('-') && isQuantity(words[0].substring(1))) {
          quantity += ` ${words[0]}`;
          words.shift();
        }
      }
    }
  }
  
  // Try to find the unit
  if (words.length > 0) {
    const possibleUnit = words[0].toLowerCase();
    
    // Check if the next word is a known unit
    if (UNITS.includes(possibleUnit) || 
        (possibleUnit.endsWith('s') && UNITS.includes(possibleUnit.slice(0, -1)))) {
      unit = words[0];
      words.shift();
    }
    
    // Handle compound units like "fluid ounce"
    if (words.length > 1 && 
        UNITS.includes(`${possibleUnit} ${words[0].toLowerCase()}`)) {
      unit = `${unit} ${words[0]}`;
      words.shift();
    }
  }
  
  // The remaining words form the ingredient name
  name = words.join(' ').trim();
  
  // Determine if this is a whole item (no unit, or unit is a size descriptor)
  if (!unit || ['small', 'medium', 'large', 'extra large', 'xl'].includes(unit.toLowerCase())) {
    isWhole = true;
  }
  
  // Determine the category based on the ingredient name
  const nameLower = name.toLowerCase();
  for (const [categoryName, keywords] of Object.entries(FOOD_CATEGORIES)) {
    if (keywords.some(keyword => nameLower.includes(keyword))) {
      category = categoryName;
      break;
    }
  }
  
  return {
    quantity,
    unit,
    name,
    isWhole,
    category
  };
}

/**
 * Check if a string represents a quantity (number or fraction)
 */
function isQuantity(str: string): boolean {
  // Check for fractions like 1/2, ½, etc.
  if (
    str.includes('/') || 
    str.includes('½') || 
    str.includes('⅓') || 
    str.includes('⅔') || 
    str.includes('¼') || 
    str.includes('¾')
  ) {
    return true;
  }
  
  // Check for decimal numbers
  if (!isNaN(parseFloat(str))) {
    return true;
  }
  
  // Check for mixed numbers like "1½"
  const mixedNumberPattern = /^\d+[½⅓⅔¼¾⅕⅖⅗⅘⅙⅚⅛⅜⅝⅞]$/;
  return mixedNumberPattern.test(str);
}

/**
 * Generate a shopping list item from a parsed ingredient
 */
export function generateShoppingListItem(parsedIngredient: ParsedIngredient): string {
  let result = parsedIngredient.name;
  
  if (parsedIngredient.quantity || parsedIngredient.unit) {
    result += ` (${parsedIngredient.quantity} ${parsedIngredient.unit})`.trim();
  }
  
  return result;
}

/**
 * Consolidate similar ingredients in a shopping list
 * 
 * @param ingredients Array of ParsedIngredient objects
 * @returns Consolidated array of ParsedIngredient objects
 */
export function consolidateIngredients(ingredients: ParsedIngredient[]): ParsedIngredient[] {
  const consolidatedMap = new Map<string, ParsedIngredient>();
  
  ingredients.forEach(ingredient => {
    const nameLower = ingredient.name.toLowerCase();
    
    // Check if we already have this ingredient
    if (consolidatedMap.has(nameLower)) {
      const existing = consolidatedMap.get(nameLower)!;
      
      // Only attempt to combine if the units match or both are whole items
      if (existing.unit === ingredient.unit || (existing.isWhole && ingredient.isWhole)) {
        // For simplicity, we're just noting both quantities
        existing.quantity = `${existing.quantity}, ${ingredient.quantity}`;
      } else {
        // If units don't match, we keep both entries with slightly different names
        const newName = `${ingredient.name} (${ingredient.unit})`;
        consolidatedMap.set(newName.toLowerCase(), ingredient);
      }
    } else {
      // Check for similar ingredients (e.g., "diced onion" and "chopped onion")
      let foundSimilar = false;
      
      for (const [key, existingIngredient] of consolidatedMap.entries()) {
        // Check if one name contains the other
        if (
          nameLower.includes(existingIngredient.name.toLowerCase()) || 
          existingIngredient.name.toLowerCase().includes(nameLower)
        ) {
          // Use the shorter name as the canonical one
          const shorterName = 
            existingIngredient.name.length < ingredient.name.length ? 
            existingIngredient.name : ingredient.name;
          
          // Use the longer name as a description in the quantity
          const longerName = 
            existingIngredient.name.length >= ingredient.name.length ? 
            existingIngredient.name : ingredient.name;
          
          // Update the existing ingredient
          existingIngredient.name = shorterName;
          
          // Only attempt to combine if the units match
          if (existingIngredient.unit === ingredient.unit) {
            existingIngredient.quantity = `${existingIngredient.quantity}, ${ingredient.quantity} (${longerName})`;
          } else {
            existingIngredient.quantity = `${existingIngredient.quantity}, ${ingredient.quantity} ${ingredient.unit} (${longerName})`;
          }
          
          foundSimilar = true;
          break;
        }
      }
      
      if (!foundSimilar) {
        consolidatedMap.set(nameLower, { ...ingredient });
      }
    }
  });
  
  return Array.from(consolidatedMap.values());
}

/**
 * Process a list of recipe ingredients into a consolidated shopping list
 * 
 * @param ingredientStrings Array of ingredient strings from recipes
 * @returns Consolidated list of ParsedIngredient objects
 */
export function processRecipeIngredients(ingredientStrings: string[]): ParsedIngredient[] {
  // Parse all ingredients
  const parsedIngredients = ingredientStrings.map(str => parseIngredient(str));
  
  // Consolidate similar ingredients
  return consolidateIngredients(parsedIngredients);
} 