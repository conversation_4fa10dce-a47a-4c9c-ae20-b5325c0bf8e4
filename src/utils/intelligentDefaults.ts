/**
 * Utilities for analyzing user behavior patterns and generating intelligent defaults
 */

// Define types for different kinds of user activities
export interface ActivityEntry {
  timestamp: Date;
  userId: string;
}

export interface FoodLogEntry extends ActivityEntry {
  type: 'food';
  foods: {
    name: string;
    quantity?: number;
    unit?: string;
    mealTime?: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  }[];
  rawText?: string;
}

export interface WaterIntakeEntry extends ActivityEntry {
  type: 'water';
  amount: number; // in milliliters
  unit: 'ml' | 'oz' | 'cups';
}

export interface WorkoutEntry extends ActivityEntry {
  type: 'workout';
  activity: string;
  duration: number; // in minutes
  intensity?: 'low' | 'medium' | 'high';
}

export interface SleepEntry extends ActivityEntry {
  type: 'sleep';
  startTime: Date;
  endTime: Date;
  quality?: 'poor' | 'fair' | 'good' | 'excellent';
}

export type UserActivity = FoodLogEntry | WaterIntakeEntry | WorkoutEntry | SleepEntry;

// Pattern analysis configurations
export interface PatternConfig {
  timeRangeInDays: number;
  minOccurrences: number;
  confidenceThreshold: number;
}

export interface PatternResult<T extends UserActivity> {
  pattern: Partial<T>;
  confidence: number;
  suggestedTime?: {
    dayOfWeek?: number[];
    hourRange?: [number, number];
  };
  lastUsed?: Date;
}

// Default configuration
const DEFAULT_CONFIG: PatternConfig = {
  timeRangeInDays: 30,
  minOccurrences: 3,
  confidenceThreshold: 0.6,
};

/**
 * Analyze food logging patterns to detect recurring meals
 */
export function analyzeRecurringMeals(
  entries: FoodLogEntry[],
  config: Partial<PatternConfig> = {}
): PatternResult<FoodLogEntry>[] {
  const fullConfig = { ...DEFAULT_CONFIG, ...config };
  const patterns: PatternResult<FoodLogEntry>[] = [];
  
  if (entries.length < fullConfig.minOccurrences) {
    return patterns;
  }
  
  // Group entries by meal time
  const entriesByMealTime = groupByMealTime(entries);
  
  // Analyze each meal time separately
  for (const [mealTime, mealEntries] of Object.entries(entriesByMealTime)) {
    // Find recurring foods at this meal time
    const foodFrequency = countFoodFrequency(mealEntries);
    
    // Find foods that meet the minimum occurrences threshold
    const recurringFoods = Object.entries(foodFrequency)
      .filter(([_, count]) => count >= fullConfig.minOccurrences)
      .map(([foodName, count]) => ({ 
        foodName, 
        count, 
        confidence: count / mealEntries.length
      }))
      .filter(item => item.confidence >= fullConfig.confidenceThreshold);
    
    // Create pattern results for recurring foods
    for (const { foodName, confidence } of recurringFoods) {
      // Find entries with this food to analyze time patterns
      const entriesWithFood = mealEntries.filter(entry => 
        entry.foods.some(food => food.name.toLowerCase() === foodName.toLowerCase())
      );
      
      const timePattern = analyzeTimePattern(entriesWithFood);
      
      // Find the most common quantity and unit for this food
      const { quantity, unit } = findCommonMeasurements(entriesWithFood, foodName);
      
      patterns.push({
        pattern: {
          type: 'food',
          foods: [{
            name: foodName,
            quantity,
            unit,
            mealTime: mealTime as 'breakfast' | 'lunch' | 'dinner' | 'snack'
          }]
        },
        confidence,
        suggestedTime: timePattern,
        lastUsed: findMostRecent(entriesWithFood)
      });
    }
  }
  
  return patterns.sort((a, b) => b.confidence - a.confidence);
}

/**
 * Analyze water intake patterns to detect recurring habits
 */
export function analyzeWaterIntakePatterns(
  entries: WaterIntakeEntry[],
  config: Partial<PatternConfig> = {}
): PatternResult<WaterIntakeEntry>[] {
  const fullConfig = { ...DEFAULT_CONFIG, ...config };
  const patterns: PatternResult<WaterIntakeEntry>[] = [];
  
  if (entries.length < fullConfig.minOccurrences) {
    return patterns;
  }
  
  // Group entries by hour of day
  const entriesByHour = groupByHourOfDay(entries);
  
  // Find hours with consistent water intake
  for (const [hour, hourEntries] of Object.entries(entriesByHour)) {
    if (hourEntries.length >= fullConfig.minOccurrences) {
      // Calculate average amount for this hour
      const totalAmount = hourEntries.reduce((sum, entry) => sum + entry.amount, 0);
      const avgAmount = Math.round(totalAmount / hourEntries.length);
      
      // Find the most common unit
      const units = hourEntries.map(entry => entry.unit);
      const commonUnit = findMostCommonItem(units) as 'ml' | 'oz' | 'cups';
      
      // Calculate confidence as consistency in the amount
      const amountVariance = calculateVariance(hourEntries.map(entry => entry.amount));
      const normalizedVariance = Math.min(amountVariance / avgAmount, 1);
      const confidence = 1 - normalizedVariance;
      
      if (confidence >= fullConfig.confidenceThreshold) {
        const dayOfWeekPattern = analyzeDayOfWeekPattern(hourEntries);
        
        patterns.push({
          pattern: {
            type: 'water',
            amount: avgAmount,
            unit: commonUnit,
          },
          confidence,
          suggestedTime: {
            dayOfWeek: dayOfWeekPattern,
            hourRange: [parseInt(hour), parseInt(hour) + 1]
          },
          lastUsed: findMostRecent(hourEntries)
        });
      }
    }
  }
  
  return patterns.sort((a, b) => b.confidence - a.confidence);
}

/**
 * Analyze workout patterns to detect recurring habits
 */
export function analyzeWorkoutPatterns(
  entries: WorkoutEntry[],
  config: Partial<PatternConfig> = {}
): PatternResult<WorkoutEntry>[] {
  const fullConfig = { ...DEFAULT_CONFIG, ...config };
  const patterns: PatternResult<WorkoutEntry>[] = [];
  
  if (entries.length < fullConfig.minOccurrences) {
    return patterns;
  }
  
  // Group entries by activity type
  const entriesByActivity = groupByActivityType(entries);
  
  // Analyze each activity separately
  for (const [activity, activityEntries] of Object.entries(entriesByActivity)) {
    if (activityEntries.length >= fullConfig.minOccurrences) {
      // Calculate average duration for this activity
      const totalDuration = activityEntries.reduce((sum, entry) => sum + entry.duration, 0);
      const avgDuration = Math.round(totalDuration / activityEntries.length);
      
      // Find the most common intensity
      const intensities = activityEntries.map(entry => entry.intensity).filter(Boolean) as ('low' | 'medium' | 'high')[];
      const commonIntensity = intensities.length > 0 ? 
        findMostCommonItem(intensities) as 'low' | 'medium' | 'high' : 
        undefined;
      
      // Calculate confidence based on consistency
      const timePattern = analyzeTimePattern(activityEntries);
      const durationVariance = calculateVariance(activityEntries.map(entry => entry.duration));
      const normalizedVariance = Math.min(durationVariance / avgDuration, 1);
      const durationConsistency = 1 - normalizedVariance;
      
      // Combine time pattern confidence with duration consistency
      const confidence = (timePattern.dayOfWeek?.length ? 0.7 : 0.4) * durationConsistency;
      
      if (confidence >= fullConfig.confidenceThreshold) {
        patterns.push({
          pattern: {
            type: 'workout',
            activity,
            duration: avgDuration,
            intensity: commonIntensity,
          },
          confidence,
          suggestedTime: timePattern,
          lastUsed: findMostRecent(activityEntries)
        });
      }
    }
  }
  
  return patterns.sort((a, b) => b.confidence - a.confidence);
}

/**
 * Analyze sleep patterns to detect recurring habits
 */
export function analyzeSleepPatterns(
  entries: SleepEntry[],
  config: Partial<PatternConfig> = {}
): PatternResult<SleepEntry>[] {
  const fullConfig = { ...DEFAULT_CONFIG, ...config };
  const patterns: PatternResult<SleepEntry>[] = [];
  
  if (entries.length < fullConfig.minOccurrences) {
    return patterns;
  }
  
  // Group entries by day type (weekday vs weekend)
  const weekdayEntries = entries.filter(entry => {
    const day = entry.startTime.getDay();
    return day >= 1 && day <= 5; // Monday to Friday
  });
  
  const weekendEntries = entries.filter(entry => {
    const day = entry.startTime.getDay();
    return day === 0 || day === 6; // Sunday or Saturday
  });
  
  // Analyze weekday pattern
  if (weekdayEntries.length >= fullConfig.minOccurrences) {
    const weekdayPattern = analyzeSleepTimePattern(weekdayEntries);
    patterns.push(weekdayPattern);
  }
  
  // Analyze weekend pattern
  if (weekendEntries.length >= fullConfig.minOccurrences) {
    const weekendPattern = analyzeSleepTimePattern(weekendEntries);
    patterns.push(weekendPattern);
  }
  
  return patterns.sort((a, b) => b.confidence - a.confidence);
}

/**
 * Get intelligent defaults for a specific activity type based on current time and past patterns
 */
export function getIntelligentDefaults<T extends UserActivity>(
  activityType: T['type'],
  patterns: PatternResult<T>[],
  currentTime: Date = new Date()
): PatternResult<T> | null {
  if (patterns.length === 0) {
    return null;
  }
  
  const currentHour = currentTime.getHours();
  const currentDayOfWeek = currentTime.getDay();
  
  // Filter patterns that match current time
  const matchingPatterns = patterns.filter(pattern => {
    const timeMatch = pattern.suggestedTime;
    
    if (!timeMatch) return false;
    
    // Check day of week
    const dayMatches = !timeMatch.dayOfWeek || 
      timeMatch.dayOfWeek.includes(currentDayOfWeek);
    
    // Check hour range
    const hourMatches = !timeMatch.hourRange || 
      (currentHour >= timeMatch.hourRange[0] && currentHour <= timeMatch.hourRange[1]);
    
    return dayMatches && hourMatches;
  });
  
  if (matchingPatterns.length === 0) {
    // If no time-matching patterns, return the highest confidence pattern
    return patterns[0];
  }
  
  // Return the highest confidence matching pattern
  return matchingPatterns.sort((a, b) => b.confidence - a.confidence)[0];
}

/**
 * Generate multiple suggestions based on current context
 */
export function generateContextualSuggestions<T extends UserActivity>(
  activityType: T['type'],
  patterns: PatternResult<T>[],
  currentTime: Date = new Date(),
  maxSuggestions: number = 3
): PatternResult<T>[] {
  if (patterns.length === 0) {
    return [];
  }
  
  // Get patterns sorted by contextual relevance
  const contextSortedPatterns = sortPatternsByContext(patterns, currentTime);
  
  // Return top N suggestions
  return contextSortedPatterns.slice(0, maxSuggestions);
}

// Helper functions

/**
 * Group food entries by meal time
 */
function groupByMealTime(entries: FoodLogEntry[]): Record<string, FoodLogEntry[]> {
  const result: Record<string, FoodLogEntry[]> = {
    breakfast: [],
    lunch: [],
    dinner: [],
    snack: [],
    undefined: [], // for entries without a specified meal time
  };
  
  for (const entry of entries) {
    // Check if any food in the entry has a meal time
    const mealTimes = entry.foods
      .map(food => food.mealTime)
      .filter(Boolean) as ('breakfast' | 'lunch' | 'dinner' | 'snack')[];
    
    if (mealTimes.length > 0) {
      // If foods have meal times, distribute them accordingly
      const mealTime = findMostCommonItem(mealTimes);
      result[mealTime].push(entry);
    } else {
      // Infer meal time from the hour if not specified
      const hour = new Date(entry.timestamp).getHours();
      
      if (hour >= 5 && hour < 10) {
        result.breakfast.push(entry);
      } else if (hour >= 10 && hour < 15) {
        result.lunch.push(entry);
      } else if (hour >= 17 && hour < 22) {
        result.dinner.push(entry);
      } else {
        result.snack.push(entry);
      }
    }
  }
  
  return result;
}

/**
 * Count frequency of each food in a list of entries
 */
function countFoodFrequency(entries: FoodLogEntry[]): Record<string, number> {
  const result: Record<string, number> = {};
  
  for (const entry of entries) {
    for (const food of entry.foods) {
      const name = food.name.toLowerCase();
      result[name] = (result[name] || 0) + 1;
    }
  }
  
  return result;
}

/**
 * Group entries by hour of day
 */
function groupByHourOfDay<T extends ActivityEntry>(entries: T[]): Record<string, T[]> {
  const result: Record<string, T[]> = {};
  
  for (const entry of entries) {
    const hour = new Date(entry.timestamp).getHours();
    if (!result[hour]) {
      result[hour] = [];
    }
    result[hour].push(entry);
  }
  
  return result;
}

/**
 * Group workout entries by activity type
 */
function groupByActivityType(entries: WorkoutEntry[]): Record<string, WorkoutEntry[]> {
  const result: Record<string, WorkoutEntry[]> = {};
  
  for (const entry of entries) {
    const activity = entry.activity.toLowerCase();
    if (!result[activity]) {
      result[activity] = [];
    }
    result[activity].push(entry);
  }
  
  return result;
}

/**
 * Find the most common day of week pattern in entries
 */
function analyzeDayOfWeekPattern<T extends ActivityEntry>(entries: T[]): number[] {
  const dayCount: Record<number, number> = {
    0: 0, 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0
  };
  
  for (const entry of entries) {
    const day = new Date(entry.timestamp).getDay();
    dayCount[day] += 1;
  }
  
  // Calculate the average occurrences per day
  const totalCount = Object.values(dayCount).reduce((sum, count) => sum + count, 0);
  const avgCount = totalCount / 7;
  
  // Find days that occur significantly more often than average (>50% more)
  const significantDays = Object.entries(dayCount)
    .filter(([_, count]) => count > avgCount * 1.5)
    .map(([day]) => parseInt(day));
  
  return significantDays;
}

/**
 * Analyze time patterns (day of week and hour of day)
 */
function analyzeTimePattern<T extends ActivityEntry>(entries: T[]): {
  dayOfWeek?: number[];
  hourRange?: [number, number];
} {
  // Find day of week pattern
  const dayOfWeek = analyzeDayOfWeekPattern(entries);
  
  // Find hour of day pattern
  const hours = entries.map(entry => new Date(entry.timestamp).getHours());
  
  if (hours.length === 0) return { dayOfWeek };
  
  hours.sort((a, b) => a - b);
  
  // Find the most common hour
  const hourCount: Record<number, number> = {};
  let maxCount = 0;
  let mostCommonHour = hours[0];
  
  for (const hour of hours) {
    hourCount[hour] = (hourCount[hour] || 0) + 1;
    if (hourCount[hour] > maxCount) {
      maxCount = hourCount[hour];
      mostCommonHour = hour;
    }
  }
  
  // Create an hour range centered around the most common hour
  const hourRange: [number, number] = [
    Math.max(0, mostCommonHour - 1),
    Math.min(23, mostCommonHour + 1)
  ];
  
  return {
    dayOfWeek,
    hourRange
  };
}

/**
 * Find the most common item in an array
 */
function findMostCommonItem<T>(items: T[]): T {
  const counts: Record<string, number> = {};
  let maxCount = 0;
  let mostCommon: T = items[0];
  
  for (const item of items) {
    const key = String(item);
    counts[key] = (counts[key] || 0) + 1;
    if (counts[key] > maxCount) {
      maxCount = counts[key];
      mostCommon = item;
    }
  }
  
  return mostCommon;
}

/**
 * Calculate variance of a set of numbers
 */
function calculateVariance(values: number[]): number {
  if (values.length === 0) return 0;
  
  const mean = values.reduce((sum, value) => sum + value, 0) / values.length;
  const squaredDifferences = values.map(value => Math.pow(value - mean, 2));
  return squaredDifferences.reduce((sum, value) => sum + value, 0) / values.length;
}

/**
 * Find most recent entry in a list
 */
function findMostRecent<T extends ActivityEntry>(entries: T[]): Date | undefined {
  if (entries.length === 0) return undefined;
  
  return new Date(Math.max(...entries.map(entry => new Date(entry.timestamp).getTime())));
}

/**
 * Find common quantity and unit for a specific food
 */
function findCommonMeasurements(
  entries: FoodLogEntry[], 
  foodName: string
): { quantity?: number; unit?: string } {
  // Extract all instances of this food with quantity and unit
  const instances = entries.flatMap(entry => 
    entry.foods.filter(food => 
      food.name.toLowerCase() === foodName.toLowerCase() && food.quantity !== undefined
    )
  );
  
  if (instances.length === 0) {
    return {};
  }
  
  // Find most common unit
  const units = instances.map(food => food.unit).filter(Boolean) as string[];
  const unit = units.length > 0 ? findMostCommonItem(units) : undefined;
  
  // Find average quantity
  const quantities = instances.map(food => food.quantity).filter(Boolean) as number[];
  const quantity = quantities.length > 0 ? 
    Math.round((quantities.reduce((sum, q) => sum + q, 0) / quantities.length) * 10) / 10 : 
    undefined;
  
  return { quantity, unit };
}

/**
 * Analyze sleep time patterns
 */
function analyzeSleepTimePattern(entries: SleepEntry[]): PatternResult<SleepEntry> {
  if (entries.length === 0) {
    return { pattern: { type: 'sleep' }, confidence: 0 };
  }
  
  // Calculate average start and end times
  const startTimes = entries.map(entry => new Date(entry.startTime).getHours() * 60 + new Date(entry.startTime).getMinutes());
  const endTimes = entries.map(entry => new Date(entry.endTime).getHours() * 60 + new Date(entry.endTime).getMinutes());
  
  // Handle times that cross midnight
  const normalizedStartTimes = startTimes.map(time => time > 720 ? time : time + 1440);
  
  const avgStartMinutes = normalizedStartTimes.reduce((sum, time) => sum + time, 0) / entries.length;
  const avgEndMinutes = endTimes.reduce((sum, time) => sum + time, 0) / entries.length;
  
  // Convert back to hours and minutes
  const avgStartHour = Math.floor((avgStartMinutes % 1440) / 60);
  const avgStartMinute = Math.floor(avgStartMinutes % 60);
  
  const avgEndHour = Math.floor(avgEndMinutes / 60);
  const avgEndMinute = Math.floor(avgEndMinutes % 60);
  
  // Create Date objects for the average times
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const avgStartTime = new Date(today);
  avgStartTime.setHours(avgStartHour, avgStartMinute);
  
  const avgEndTime = new Date(today);
  avgEndTime.setHours(avgEndHour, avgEndMinute);
  
  // Find most common quality
  const qualities = entries.map(entry => entry.quality).filter(Boolean) as ('poor' | 'fair' | 'good' | 'excellent')[];
  const commonQuality = qualities.length > 0 ? findMostCommonItem(qualities) : undefined;
  
  // Calculate confidence based on consistency of times
  const startTimeVariance = calculateVariance(normalizedStartTimes) / 60; // in hours
  const endTimeVariance = calculateVariance(endTimes) / 60; // in hours
  
  // Lower variance means higher consistency
  const startTimeConsistency = Math.max(0, 1 - startTimeVariance / 2); // Normalize: 0-2 hours variance -> 1-0 consistency
  const endTimeConsistency = Math.max(0, 1 - endTimeVariance / 2);
  
  const confidence = (startTimeConsistency + endTimeConsistency) / 2;
  
  // Determine day of week pattern
  const daysOfWeek = entries.map(entry => new Date(entry.startTime).getDay());
  const dayOfWeek = entries[0].startTime.getDay() === 0 || entries[0].startTime.getDay() === 6 ? 
    [0, 6] : // weekend
    [1, 2, 3, 4, 5]; // weekday
  
  return {
    pattern: {
      type: 'sleep',
      startTime: avgStartTime,
      endTime: avgEndTime,
      quality: commonQuality,
    },
    confidence,
    suggestedTime: {
      dayOfWeek,
      hourRange: avgStartHour < 12 ? 
        [avgStartHour, avgStartHour + 1] : 
        [avgStartHour - 12, avgStartHour - 11]
    },
    lastUsed: findMostRecent(entries)
  };
}

/**
 * Sort patterns by contextual relevance to current time
 */
function sortPatternsByContext<T extends UserActivity>(
  patterns: PatternResult<T>[],
  currentTime: Date
): PatternResult<T>[] {
  const currentHour = currentTime.getHours();
  const currentDayOfWeek = currentTime.getDay();
  
  return [...patterns].sort((a, b) => {
    // First prioritize day of week match
    const aDayMatch = a.suggestedTime?.dayOfWeek?.includes(currentDayOfWeek) ?? false;
    const bDayMatch = b.suggestedTime?.dayOfWeek?.includes(currentDayOfWeek) ?? false;
    
    if (aDayMatch && !bDayMatch) return -1;
    if (!aDayMatch && bDayMatch) return 1;
    
    // Then prioritize hour match
    const aHourMatch = a.suggestedTime?.hourRange && 
      currentHour >= a.suggestedTime.hourRange[0] && 
      currentHour <= a.suggestedTime.hourRange[1];
    
    const bHourMatch = b.suggestedTime?.hourRange && 
      currentHour >= b.suggestedTime.hourRange[0] && 
      currentHour <= b.suggestedTime.hourRange[1];
    
    if (aHourMatch && !bHourMatch) return -1;
    if (!aHourMatch && bHourMatch) return 1;
    
    // Finally sort by confidence
    return b.confidence - a.confidence;
  });
}

export default {
  analyzeRecurringMeals,
  analyzeWaterIntakePatterns,
  analyzeWorkoutPatterns,
  analyzeSleepPatterns,
  getIntelligentDefaults,
  generateContextualSuggestions,
}; 