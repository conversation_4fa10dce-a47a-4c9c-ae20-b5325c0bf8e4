import { useEffect, useState, useCallback } from 'react';
import { Platform } from 'react-native';

// Key to store keyboard preferences in local storage
const KEYBOARD_NAV_ENABLED_KEY = 'keyboard_navigation_enabled';

// Types for keyboard navigation
export interface KeyboardShortcut {
  /**
   * The key or key combination to trigger this shortcut
   */
  key: string;
  
  /**
   * Modifier keys required (ctrl, alt, shift, meta)
   */
  modifiers?: {
    ctrl?: boolean;
    alt?: boolean;
    shift?: boolean;
    meta?: boolean;
  };
  
  /**
   * Function to execute when shortcut is triggered
   */
  action: () => void;
  
  /**
   * Description of what the shortcut does (for help screens)
   */
  description: string;
  
  /**
   * Category for grouping shortcuts
   */
  category?: 'navigation' | 'actions' | 'ui' | 'accessibility';
  
  /**
   * Whether this shortcut is enabled
   */
  enabled?: boolean;
}

/**
 * Hook to register and manage keyboard shortcuts
 */
export function useKeyboardShortcuts(
  shortcuts: KeyboardShortcut[],
  options?: {
    /**
     * Whether shortcuts are enabled initially
     */
    enabled?: boolean;
  }
) {
  // Only enable on web platform
  const isWeb = Platform.OS === 'web';
  const [isEnabled, setIsEnabled] = useState(options?.enabled ?? true);
  
  // Load saved preference
  useEffect(() => {
    if (!isWeb) return;
    
    const loadPreference = async () => {
      try {
        const storedValue = localStorage.getItem(KEYBOARD_NAV_ENABLED_KEY);
        if (storedValue !== null) {
          setIsEnabled(storedValue === 'true');
        }
      } catch (error) {
        console.error('Error loading keyboard navigation preference:', error);
      }
    };
    
    loadPreference();
  }, [isWeb]);
  
  // Save preference when changed
  useEffect(() => {
    if (!isWeb) return;
    
    try {
      localStorage.setItem(KEYBOARD_NAV_ENABLED_KEY, String(isEnabled));
    } catch (error) {
      console.error('Error saving keyboard navigation preference:', error);
    }
  }, [isEnabled, isWeb]);
  
  // Event handler for keyboard shortcuts
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!isEnabled || !isWeb) return;
      
      // Skip if user is typing in a text field
      if (
        document.activeElement instanceof HTMLInputElement ||
        document.activeElement instanceof HTMLTextAreaElement
      ) {
        return;
      }
      
      // Check each shortcut
      for (const shortcut of shortcuts) {
        if (shortcut.enabled === false) continue;
        
        // Check if key matches
        const keyMatches = event.key.toLowerCase() === shortcut.key.toLowerCase();
        
        // Check modifiers
        const modifiersMatch = !shortcut.modifiers || (
          (!shortcut.modifiers.ctrl || event.ctrlKey) &&
          (!shortcut.modifiers.alt || event.altKey) &&
          (!shortcut.modifiers.shift || event.shiftKey) &&
          (!shortcut.modifiers.meta || event.metaKey)
        );
        
        // Execute action if shortcut matches
        if (keyMatches && modifiersMatch) {
          event.preventDefault();
          shortcut.action();
          break;
        }
      }
    },
    [shortcuts, isEnabled, isWeb]
  );
  
  // Add event listener
  useEffect(() => {
    if (!isWeb) return;
    
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown, isWeb]);
  
  // Enable/disable shortcuts
  const enableShortcuts = useCallback(() => {
    setIsEnabled(true);
  }, []);
  
  const disableShortcuts = useCallback(() => {
    setIsEnabled(false);
  }, []);
  
  const toggleShortcuts = useCallback(() => {
    setIsEnabled(prev => !prev);
  }, []);
  
  return {
    isEnabled,
    enableShortcuts,
    disableShortcuts,
    toggleShortcuts,
  };
}

/**
 * Hook to add keyboard navigation to a list of focusable elements
 */
export function useKeyboardNavigation(options?: {
  /**
   * Initial index to focus
   */
  initialIndex?: number;
  
  /**
   * Whether navigation is enabled
   */
  enabled?: boolean;
  
  /**
   * Callback when focused item changes
   */
  onFocusChange?: (index: number) => void;
  
  /**
   * Whether to wrap around when reaching the end of the list
   */
  wrap?: boolean;
}) {
  // Only enable on web platform
  const isWeb = Platform.OS === 'web';
  const [focusedIndex, setFocusedIndex] = useState(options?.initialIndex ?? -1);
  const [isEnabled, setIsEnabled] = useState(options?.enabled ?? true);
  
  // Navigate to next item
  const focusNext = useCallback(() => {
    if (!isEnabled) return;
    
    setFocusedIndex(prev => {
      const elements = getFocusableElements();
      
      if (elements.length === 0) return prev;
      
      // Calculate next index
      const nextIndex = prev + 1;
      
      // Wrap around or clamp
      if (nextIndex >= elements.length) {
        return options?.wrap ? 0 : elements.length - 1;
      }
      
      return nextIndex;
    });
  }, [isEnabled, options?.wrap]);
  
  // Navigate to previous item
  const focusPrevious = useCallback(() => {
    if (!isEnabled) return;
    
    setFocusedIndex(prev => {
      const elements = getFocusableElements();
      
      if (elements.length === 0) return prev;
      
      // Calculate previous index
      const prevIndex = prev - 1;
      
      // Wrap around or clamp
      if (prevIndex < 0) {
        return options?.wrap ? elements.length - 1 : 0;
      }
      
      return prevIndex;
    });
  }, [isEnabled, options?.wrap]);
  
  // Focus a specific index
  const focusIndex = useCallback(
    (index: number) => {
      if (!isEnabled) return;
      
      const elements = getFocusableElements();
      
      if (index >= 0 && index < elements.length) {
        setFocusedIndex(index);
      }
    },
    [isEnabled]
  );
  
  // Get all focusable elements
  const getFocusableElements = useCallback(() => {
    if (!isWeb) return [];
    
    // Define selectors for interactive elements
    const selector =
      'a[href], button:not([disabled]), [tabindex]:not([tabindex="-1"]), input:not([disabled]), select:not([disabled]), textarea:not([disabled])';
    
    // Find all focusable elements
    return Array.from(document.querySelectorAll(selector));
  }, [isWeb]);
  
  // Update focus when index changes
  useEffect(() => {
    if (!isWeb || !isEnabled || focusedIndex < 0) return;
    
    const elements = getFocusableElements();
    
    if (focusedIndex < elements.length) {
      (elements[focusedIndex] as HTMLElement).focus();
      options?.onFocusChange?.(focusedIndex);
    }
  }, [focusedIndex, isEnabled, isWeb, getFocusableElements, options]);
  
  // Handle keyboard events
  useKeyboardShortcuts(
    [
      {
        key: 'Tab',
        modifiers: { shift: true },
        action: focusPrevious,
        description: 'Navigate to previous focusable element',
        category: 'navigation',
      },
      {
        key: 'Tab',
        action: focusNext,
        description: 'Navigate to next focusable element',
        category: 'navigation',
      },
    ],
    { enabled: isEnabled }
  );
  
  return {
    focusedIndex,
    focusNext,
    focusPrevious,
    focusIndex,
    isEnabled,
    enableNavigation: () => setIsEnabled(true),
    disableNavigation: () => setIsEnabled(false),
    toggleNavigation: () => setIsEnabled(prev => !prev),
  };
}

/**
 * Standard application keyboard shortcuts
 */
export const standardShortcuts: KeyboardShortcut[] = [
  // Navigation shortcuts
  {
    key: 'h',
    action: () => {
      // Navigate to home
      window.location.href = '/';
    },
    description: 'Go to Home screen',
    category: 'navigation',
  },
  {
    key: 'p',
    action: () => {
      // Navigate to profile
      window.location.href = '/profile';
    },
    description: 'Go to Profile screen',
    category: 'navigation',
  },
  {
    key: 's',
    action: () => {
      // Navigate to search
      const searchButton = document.querySelector('[aria-label="Global search"]') as HTMLElement;
      if (searchButton) {
        searchButton.click();
      }
    },
    description: 'Open search',
    category: 'navigation',
  },
  {
    key: 'Escape',
    action: () => {
      // Close modal or go back
      const backButton = document.querySelector('[aria-label="Go back"]') as HTMLElement;
      if (backButton) {
        backButton.click();
      }
    },
    description: 'Close modal or go back',
    category: 'navigation',
  },
  
  // Accessibility shortcuts
  {
    key: 'a',
    modifiers: { alt: true },
    action: () => {
      // Toggle accessibility options
      const accessibilityButton = document.querySelector('[aria-label="Accessibility options"]') as HTMLElement;
      if (accessibilityButton) {
        accessibilityButton.click();
      }
    },
    description: 'Open accessibility options',
    category: 'accessibility',
  },
  {
    key: 'd',
    modifiers: { alt: true },
    action: () => {
      // Toggle dark mode
      const darkModeToggle = document.querySelector('[aria-label="Toggle dark mode"]') as HTMLElement;
      if (darkModeToggle) {
        darkModeToggle.click();
      }
    },
    description: 'Toggle dark mode',
    category: 'accessibility',
  },
  {
    key: 'o',
    modifiers: { alt: true },
    action: () => {
      // Toggle one-handed mode
      const oneHandedToggle = document.querySelector('[aria-label="Toggle one-handed mode"]') as HTMLElement;
      if (oneHandedToggle) {
        oneHandedToggle.click();
      }
    },
    description: 'Toggle one-handed mode',
    category: 'accessibility',
  },
  
  // Help shortcut
  {
    key: '?',
    action: () => {
      // Show keyboard shortcuts help
      // TODO: Implement a keyboard shortcuts help modal
      console.log('Keyboard shortcuts help requested');
    },
    description: 'Show keyboard shortcuts help',
    category: 'ui',
  },
];

export default useKeyboardShortcuts; 