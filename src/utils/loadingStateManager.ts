import { useState, useEffect, useRef } from 'react';

interface SafeLoadingOptions {
  initialState?: boolean;
  timeoutDuration?: number;
  onTimeout?: () => void;
  debugName?: string;
}

/**
 * A utility hook to safely manage loading states with automatic timeout safety
 * 
 * @param options Configuration options
 * @returns Loading state management utilities
 */
export function useSafeLoading(options: SafeLoadingOptions = {}) {
  const {
    initialState = false,
    timeoutDuration = 10000, // Default 10 seconds timeout
    onTimeout,
    debugName = 'unnamed component',
  } = options;
  
  const [isLoading, setIsLoading] = useState(initialState);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const operationsInProgress = useRef<number>(0);
  
  // Clear timeout when component unmounts
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  /**
   * Safely start a loading operation with timeout protection
   */
  const startLoading = () => {
    operationsInProgress.current += 1;
    
    if (!isLoading) {
      setIsLoading(true);
      console.debug(`[${debugName}] Loading started`);
    }
    
    // Set up timeout to prevent stuck loading states
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      if (isLoading) {
        console.warn(`[${debugName}] Loading timeout reached after ${timeoutDuration}ms`);
        setIsLoading(false);
        operationsInProgress.current = 0;
        
        if (onTimeout) {
          onTimeout();
        }
      }
    }, timeoutDuration);
  };
  
  /**
   * Safely end a loading operation, only turning off loading when all operations complete
   */
  const endLoading = () => {
    operationsInProgress.current = Math.max(0, operationsInProgress.current - 1);
    
    if (operationsInProgress.current === 0) {
      setIsLoading(false);
      console.debug(`[${debugName}] Loading complete`);
      
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    }
  };
  
  /**
   * Execute an async function with loading state management
   * @param asyncFn The async function to execute
   * @param errorHandler Optional custom error handler
   * @returns The result of the async function
   */
  const executeWithLoading = async <T>(
    asyncFn: () => Promise<T>,
    errorHandler?: (error: unknown) => void
  ): Promise<T | undefined> => {
    startLoading();
    
    try {
      const result = await asyncFn();
      return result;
    } catch (error) {
      console.error(`[${debugName}] Error during async operation:`, error);
      
      if (errorHandler) {
        errorHandler(error);
      }
      
      return undefined;
    } finally {
      endLoading();
    }
  };
  
  /**
   * Force reset the loading state (use as a last resort for stuck states)
   */
  const forceResetLoading = () => {
    console.warn(`[${debugName}] Force resetting loading state`);
    operationsInProgress.current = 0;
    setIsLoading(false);
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };
  
  return {
    isLoading,
    setIsLoading,
    startLoading,
    endLoading,
    executeWithLoading,
    forceResetLoading,
  };
}

/**
 * Wraps a fetch operation with a timeout
 * @param fetchPromise The fetch promise to execute
 * @param timeoutMs Timeout in milliseconds
 * @returns Result of the fetch or error
 */
export async function fetchWithTimeout<T>(
  fetchPromise: Promise<T>,
  timeoutMs: number = 5000
): Promise<T> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error(`Request timed out after ${timeoutMs}ms`)), timeoutMs);
  });
  
  return Promise.race([fetchPromise, timeoutPromise]) as Promise<T>;
}

/**
 * Formats error messages for better troubleshooting
 * @param error The error object
 * @returns Formatted error message
 */
export function formatErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return 'Unknown error occurred';
} 