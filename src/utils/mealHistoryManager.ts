/**
 * Meal History Manager
 * Utility for storing, retrieving, and managing meal scan history
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

export interface FoodScanResult {
  foodName: string;
  volumeCm3: number;
  weightGrams: number;
  measurementMethod: 'lidar' | 'bounding-box' | 'statistical';
  confidence: number;
  isInContainer?: boolean;
  containerType?: string;
  depthQuality?: 'low' | 'medium' | 'high';
  originalWeightGrams?: number;
}

export interface ScanQualityMetrics {
  scanQuality: 'low' | 'medium' | 'high';
  meshCount?: number;
  surfaceStability?: 'unstable' | 'moderate' | 'stable';
  edgeDetectionQuality?: 'low' | 'medium' | 'high';
  calibrationFactor?: number;
  scannedFromAngles?: number;
  scanDurationSeconds?: number;
  hasDepthData?: boolean;
}

export interface MealScanRecord {
  id: string;
  timestamp: string;
  image: string;
  foodItems: FoodScanResult[];
  totalCalories?: number;
  totalWeight: number;
  nutritionSummary?: string;
  userAdjusted: boolean;
  tags: string[];
  mealType?: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  favorite: boolean;
  notes?: string;
  scanQualityMetrics?: ScanQualityMetrics;
  deviceModel?: string;
  containersDetected?: {
    type: string;
    confidence: number;
  }[];
  volumeVisualizationData?: string;
}

/**
 * Storage Keys
 */
const STORAGE_KEYS = {
  MEAL_HISTORY: 'meal_history',
  FAVORITES: 'favorite_meals',
  MEAL_TAGS: 'meal_tags',
};

/**
 * Add a meal scan to history
 * 
 * @param mealData Meal scan record to save
 * @returns Promise resolving to the saved meal record
 */
export async function saveMealScan(mealData: Omit<MealScanRecord, 'id'>): Promise<MealScanRecord> {
  try {
    // Generate unique ID for this scan
    const id = `meal_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
    const mealRecord: MealScanRecord = {
      ...mealData,
      id,
    };
    
    // Fetch current history
    const currentHistory = await getMealHistory();
    
    // Add new record
    const updatedHistory = [mealRecord, ...currentHistory];
    
    // Save updated history
    await AsyncStorage.setItem(STORAGE_KEYS.MEAL_HISTORY, JSON.stringify(updatedHistory));
    
    // If marked as favorite, add to favorites list
    if (mealRecord.favorite) {
      await addMealToFavorites(mealRecord.id);
    }
    
    // Store any new tags
    if (mealRecord.tags && mealRecord.tags.length > 0) {
      await updateMealTags(mealRecord.tags);
    }
    
    return mealRecord;
  } catch (error) {
    console.error('Failed to save meal scan:', error);
    throw new Error('Failed to save meal scan');
  }
}

/**
 * Get meal history
 * 
 * @param limit Optional limit for number of records to retrieve
 * @returns Promise resolving to array of meal records
 */
export async function getMealHistory(limit?: number): Promise<MealScanRecord[]> {
  try {
    const historyData = await AsyncStorage.getItem(STORAGE_KEYS.MEAL_HISTORY);
    
    if (!historyData) {
      return [];
    }
    
    const history: MealScanRecord[] = JSON.parse(historyData);
    
    // Sort by date (newest first)
    const sortedHistory = history.sort((a, b) => {
      return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
    });
    
    // Apply limit if provided
    if (limit && limit > 0) {
      return sortedHistory.slice(0, limit);
    }
    
    return sortedHistory;
  } catch (error) {
    console.error('Failed to get meal history:', error);
    return [];
  }
}

/**
 * Update an existing meal record
 * 
 * @param id ID of the meal to update
 * @param updatedData Updated meal data
 * @returns Promise resolving to the updated meal record or null if not found
 */
export async function updateMealRecord(
  id: string,
  updatedData: Partial<MealScanRecord>
): Promise<MealScanRecord | null> {
  try {
    const history = await getMealHistory();
    const mealIndex = history.findIndex(meal => meal.id === id);
    
    if (mealIndex === -1) {
      return null;
    }
    
    const updatedMeal = {
      ...history[mealIndex],
      ...updatedData,
    };
    
    history[mealIndex] = updatedMeal;
    
    await AsyncStorage.setItem(STORAGE_KEYS.MEAL_HISTORY, JSON.stringify(history));
    
    // Update favorites if needed
    if (updatedData.favorite !== undefined) {
      if (updatedData.favorite) {
        await addMealToFavorites(id);
      } else {
        await removeMealFromFavorites(id);
      }
    }
    
    return updatedMeal;
  } catch (error) {
    console.error('Failed to update meal record:', error);
    return null;
  }
}

/**
 * Delete a meal record from history
 * 
 * @param id ID of the meal to delete
 * @returns Promise resolving to boolean indicating success
 */
export async function deleteMealRecord(id: string): Promise<boolean> {
  try {
    const history = await getMealHistory();
    const updatedHistory = history.filter(meal => meal.id !== id);
    
    if (history.length === updatedHistory.length) {
      return false; // No record was deleted
    }
    
    await AsyncStorage.setItem(STORAGE_KEYS.MEAL_HISTORY, JSON.stringify(updatedHistory));
    
    // Also remove from favorites if present
    await removeMealFromFavorites(id);
    
    return true;
  } catch (error) {
    console.error('Failed to delete meal record:', error);
    return false;
  }
}

/**
 * Add a meal to favorites
 * 
 * @param id ID of the meal to add to favorites
 * @returns Promise resolving to boolean indicating success
 */
export async function addMealToFavorites(id: string): Promise<boolean> {
  try {
    const favoritesData = await AsyncStorage.getItem(STORAGE_KEYS.FAVORITES);
    const favorites = favoritesData ? JSON.parse(favoritesData) : [];
    
    if (!favorites.includes(id)) {
      favorites.push(id);
      await AsyncStorage.setItem(STORAGE_KEYS.FAVORITES, JSON.stringify(favorites));
    }
    
    return true;
  } catch (error) {
    console.error('Failed to add meal to favorites:', error);
    return false;
  }
}

/**
 * Remove a meal from favorites
 * 
 * @param id ID of the meal to remove from favorites
 * @returns Promise resolving to boolean indicating success
 */
export async function removeMealFromFavorites(id: string): Promise<boolean> {
  try {
    const favoritesData = await AsyncStorage.getItem(STORAGE_KEYS.FAVORITES);
    
    if (!favoritesData) {
      return true; // No favorites to remove from
    }
    
    const favorites = JSON.parse(favoritesData);
    const updatedFavorites = favorites.filter((favId: string) => favId !== id);
    
    await AsyncStorage.setItem(STORAGE_KEYS.FAVORITES, JSON.stringify(updatedFavorites));
    
    return true;
  } catch (error) {
    console.error('Failed to remove meal from favorites:', error);
    return false;
  }
}

/**
 * Get all favorite meals
 * 
 * @returns Promise resolving to array of favorite meal records
 */
export async function getFavoriteMeals(): Promise<MealScanRecord[]> {
  try {
    const favoritesData = await AsyncStorage.getItem(STORAGE_KEYS.FAVORITES);
    
    if (!favoritesData) {
      return [];
    }
    
    const favoriteIds = JSON.parse(favoritesData);
    const allMeals = await getMealHistory();
    
    // Filter meals that are in favorites
    return allMeals.filter(meal => favoriteIds.includes(meal.id));
  } catch (error) {
    console.error('Failed to get favorite meals:', error);
    return [];
  }
}

/**
 * Update the list of meal tags for better organization
 * 
 * @param newTags New tags to add to the system
 * @returns Promise resolving to complete list of tags
 */
export async function updateMealTags(newTags: string[]): Promise<string[]> {
  try {
    const tagsData = await AsyncStorage.getItem(STORAGE_KEYS.MEAL_TAGS);
    const existingTags = tagsData ? JSON.parse(tagsData) : [];
    
    // Add any new tags that don't already exist
    const uniqueNewTags = newTags.filter(tag => !existingTags.includes(tag));
    
    if (uniqueNewTags.length === 0) {
      return existingTags;
    }
    
    const updatedTags = [...existingTags, ...uniqueNewTags];
    
    await AsyncStorage.setItem(STORAGE_KEYS.MEAL_TAGS, JSON.stringify(updatedTags));
    
    return updatedTags;
  } catch (error) {
    console.error('Failed to update meal tags:', error);
    return [];
  }
}

/**
 * Get all meal tags
 * 
 * @returns Promise resolving to array of meal tags
 */
export async function getMealTags(): Promise<string[]> {
  try {
    const tagsData = await AsyncStorage.getItem(STORAGE_KEYS.MEAL_TAGS);
    
    if (!tagsData) {
      return [];
    }
    
    return JSON.parse(tagsData);
  } catch (error) {
    console.error('Failed to get meal tags:', error);
    return [];
  }
}

/**
 * Get meals by date range
 * 
 * @param startDate Start date of range (inclusive)
 * @param endDate End date of range (inclusive)
 * @returns Promise resolving to array of meal records in the date range
 */
export async function getMealsByDateRange(
  startDate: Date,
  endDate: Date
): Promise<MealScanRecord[]> {
  try {
    const history = await getMealHistory();
    
    const startTime = startDate.getTime();
    const endTime = endDate.getTime();
    
    return history.filter(meal => {
      const mealTime = new Date(meal.timestamp).getTime();
      return mealTime >= startTime && mealTime <= endTime;
    });
  } catch (error) {
    console.error('Failed to get meals by date range:', error);
    return [];
  }
}

/**
 * Get meals by tags
 * 
 * @param tags Array of tags to filter by (meals must have ALL tags)
 * @returns Promise resolving to array of matching meal records
 */
export async function getMealsByTags(tags: string[]): Promise<MealScanRecord[]> {
  try {
    if (!tags || tags.length === 0) {
      return [];
    }
    
    const history = await getMealHistory();
    
    return history.filter(meal => 
      tags.every(tag => meal.tags.includes(tag))
    );
  } catch (error) {
    console.error('Failed to get meals by tags:', error);
    return [];
  }
}

/**
 * Get a single meal record by ID
 * 
 * @param id ID of the meal to retrieve
 * @returns Promise resolving to the meal record or null if not found
 */
export async function getMealById(id: string): Promise<MealScanRecord | null> {
  try {
    const history = await getMealHistory();
    const meal = history.find(m => m.id === id);
    
    return meal || null;
  } catch (error) {
    console.error('Failed to get meal by ID:', error);
    return null;
  }
}

/**
 * Clear all meal history data
 * 
 * @returns Promise resolving to boolean indicating success
 */
export async function clearMealHistory(): Promise<boolean> {
  try {
    await AsyncStorage.removeItem(STORAGE_KEYS.MEAL_HISTORY);
    await AsyncStorage.removeItem(STORAGE_KEYS.FAVORITES);
    await AsyncStorage.removeItem(STORAGE_KEYS.MEAL_TAGS);
    
    return true;
  } catch (error) {
    console.error('Failed to clear meal history:', error);
    return false;
  }
}

/**
 * Export meal history data as JSON
 * 
 * @returns Promise resolving to JSON string of meal history
 */
export async function exportMealHistory(): Promise<string> {
  try {
    const history = await getMealHistory();
    return JSON.stringify(history);
  } catch (error) {
    console.error('Failed to export meal history:', error);
    throw new Error('Failed to export meal history');
  }
}

/**
 * Import meal history data from JSON
 * 
 * @param jsonData JSON string of meal history to import
 * @param overwrite Whether to overwrite existing history (true) or merge (false)
 * @returns Promise resolving to boolean indicating success
 */
export async function importMealHistory(
  jsonData: string,
  overwrite: boolean = false
): Promise<boolean> {
  try {
    const importedHistory = JSON.parse(jsonData) as MealScanRecord[];
    
    if (!Array.isArray(importedHistory)) {
      throw new Error('Invalid import data format');
    }
    
    if (overwrite) {
      // Replace all existing data
      await AsyncStorage.setItem(STORAGE_KEYS.MEAL_HISTORY, jsonData);
    } else {
      // Merge with existing data
      const currentHistory = await getMealHistory();
      
      // Get IDs of current meals to avoid duplicates
      const existingIds = new Set(currentHistory.map(meal => meal.id));
      
      // Filter out imported meals that already exist
      const newMeals = importedHistory.filter(meal => !existingIds.has(meal.id));
      
      // Combine current and new meals
      const mergedHistory = [...currentHistory, ...newMeals];
      
      await AsyncStorage.setItem(STORAGE_KEYS.MEAL_HISTORY, JSON.stringify(mergedHistory));
    }
    
    // Extract and update all tags
    const allTags = Array.from(new Set(
      importedHistory.flatMap(meal => meal.tags || [])
    ));
    
    if (allTags.length > 0) {
      await updateMealTags(allTags);
    }
    
    // Update favorites list
    const favoriteMeals = importedHistory
      .filter(meal => meal.favorite)
      .map(meal => meal.id);
    
    if (favoriteMeals.length > 0) {
      const currentFavorites = await AsyncStorage.getItem(STORAGE_KEYS.FAVORITES);
      const favorites = currentFavorites ? JSON.parse(currentFavorites) : [];
      
      // Add new favorites
      const allFavorites = Array.from(new Set([...favorites, ...favoriteMeals]));
      
      await AsyncStorage.setItem(STORAGE_KEYS.FAVORITES, JSON.stringify(allFavorites));
    }
    
    return true;
  } catch (error) {
    console.error('Failed to import meal history:', error);
    return false;
  }
} 