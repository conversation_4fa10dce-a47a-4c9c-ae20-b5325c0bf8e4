import { Me<PERSON>, MealItem } from '@/services/databaseService';

/**
 * Determines the meal type based on time of day
 * @param time Time in format "HH:MM" or "HH:MM:SS"
 * @returns Appropriate meal type label
 */
export function getMealTypeByTime(time: string): string {
  // Extract hours and minutes from time string
  const timeComponents = time.split(':');
  const hours = parseInt(timeComponents[0], 10);
  const minutes = parseInt(timeComponents[1], 10);
  
  // Convert to total minutes for easier comparison
  const totalMinutes = hours * 60 + minutes;
  
  // Time ranges for different meal types (in total minutes)
  const BREAKFAST_START = 5 * 60;      // 5:00 AM
  const BREAKFAST_END = 10 * 60;       // 10:00 AM
  const MORNING_SNACK_START = 10 * 60; // 10:00 AM
  const MORNING_SNACK_END = 11.5 * 60; // 11:30 AM
  const LUNCH_START = 11.5 * 60;       // 11:30 AM
  const LUNCH_END = 14 * 60;           // 2:00 PM
  const AFTERNOON_SNACK_START = 14 * 60; // 2:00 PM
  const AFTERNOON_SNACK_END = 17 * 60; // 5:00 PM
  const DINNER_START = 17 * 60;        // 5:00 PM
  const DINNER_END = 21 * 60;          // 9:00 PM
  const EVENING_SNACK_START = 21 * 60; // 9:00 PM
  
  // Determine meal type based on time
  if (totalMinutes >= BREAKFAST_START && totalMinutes < BREAKFAST_END) {
    return 'Breakfast';
  } else if (totalMinutes >= MORNING_SNACK_START && totalMinutes < MORNING_SNACK_END) {
    return 'Morning Snack';
  } else if (totalMinutes >= LUNCH_START && totalMinutes < LUNCH_END) {
    return 'Lunch';
  } else if (totalMinutes >= AFTERNOON_SNACK_START && totalMinutes < AFTERNOON_SNACK_END) {
    return 'Afternoon Snack';
  } else if (totalMinutes >= DINNER_START && totalMinutes < DINNER_END) {
    return 'Dinner';
  } else if (totalMinutes >= EVENING_SNACK_START || totalMinutes < BREAKFAST_START) {
    return 'Evening Snack';
  } else {
    return 'Snack'; // Default case
  }
}

/**
 * Formats a time string for display
 * @param time Time in format "HH:MM" or "HH:MM:SS" or "H:MM AM/PM"
 * @returns Formatted time string (e.g., "8:30 AM")
 */
export function formatTimeForDisplay(time: string): string {
  // If time already includes AM/PM, it's already formatted
  if (time.includes('AM') || time.includes('PM')) {
    return time;
  }
  
  // Extract hours and minutes from time string
  const timeComponents = time.split(':');
  const hours = parseInt(timeComponents[0], 10);
  const minutes = parseInt(timeComponents[1], 10);
  
  const period = hours >= 12 ? 'PM' : 'AM';
  const displayHours = hours % 12 || 12; // Convert 0 to 12 for 12 AM
  
  return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
}

/**
 * Gets appropriate color for a meal type
 * @param mealName The name of the meal
 * @returns Color code for the meal type
 */
export function getMealColor(mealName: string): string {
  const mealTypeLower = mealName.toLowerCase();
  
  if (mealTypeLower.includes('breakfast')) {
    return '#F59E0B'; // amber
  } else if (mealTypeLower.includes('lunch')) {
    return '#10B981'; // green
  } else if (mealTypeLower.includes('dinner')) {
    return '#8B5CF6'; // purple
  } else if (mealTypeLower.includes('morning snack')) {
    return '#3B82F6'; // blue
  } else if (mealTypeLower.includes('afternoon snack')) {
    return '#2563EB'; // darker blue
  } else if (mealTypeLower.includes('evening snack')) {
    return '#7C3AED'; // violet
  } else if (mealTypeLower.includes('snack')) {
    return '#4F46E5'; // indigo
  } else {
    return '#10B981'; // default green
  }
}

/**
 * Gets default image for a meal type
 * @param mealName The name of the meal
 * @returns URL for a default image based on meal type
 */
export function getMealDefaultImage(mealName: string): string {
  const mealTypeLower = mealName.toLowerCase();
  
  if (mealTypeLower.includes('breakfast')) {
    return 'https://images.pexels.com/photos/704569/pexels-photo-704569.jpeg?auto=compress&cs=tinysrgb&w=600';
  } else if (mealTypeLower.includes('lunch')) {
    return 'https://images.pexels.com/photos/1640772/pexels-photo-1640772.jpeg?auto=compress&cs=tinysrgb&w=600';
  } else if (mealTypeLower.includes('dinner')) {
    return 'https://images.pexels.com/photos/1279330/pexels-photo-1279330.jpeg?auto=compress&cs=tinysrgb&w=600';
  } else if (mealTypeLower.includes('morning snack')) {
    return 'https://images.pexels.com/photos/1092730/pexels-photo-1092730.jpeg?auto=compress&cs=tinysrgb&w=600';
  } else if (mealTypeLower.includes('afternoon snack') || mealTypeLower.includes('snack')) {
    return 'https://images.pexels.com/photos/4033296/pexels-photo-4033296.jpeg?auto=compress&cs=tinysrgb&w=600';
  } else {
    return 'https://images.pexels.com/photos/1092730/pexels-photo-1092730.jpeg?auto=compress&cs=tinysrgb&w=600';
  }
}

/**
 * Calculates total calories for a meal
 * @param items List of meal items
 * @returns Total calories
 */
export function calculateTotalCalories(items: MealItem[]): number {
  return items.reduce((sum, item) => sum + (item.calories || 0), 0);
}

/**
 * Determines if a meal was recorded recently (within the last hour)
 * @param mealTimestamp ISO timestamp of meal creation
 * @returns Boolean indicating if meal is recent
 */
export function isRecentMeal(mealTimestamp: string): boolean {
  const mealTime = new Date(mealTimestamp).getTime();
  const nowTime = new Date().getTime();
  const ONE_HOUR = 60 * 60 * 1000;
  
  return (nowTime - mealTime) < ONE_HOUR;
}

/**
 * Suggests the next meal based on current time
 * @returns Object containing suggested meal type and time
 */
export function suggestNextMeal(): { mealType: string; suggestedTime: string } {
  const now = new Date();
  const hours = now.getHours();
  
  if (hours < 10) {
    return {
      mealType: hours < 7 ? 'Breakfast' : 'Morning Snack',
      suggestedTime: hours < 7 ? '7:30 AM' : '10:30 AM'
    };
  } else if (hours < 12) {
    return {
      mealType: 'Lunch',
      suggestedTime: '12:30 PM'
    };
  } else if (hours < 16) {
    return {
      mealType: 'Afternoon Snack',
      suggestedTime: '3:30 PM'
    };
  } else if (hours < 19) {
    return {
      mealType: 'Dinner',
      suggestedTime: '7:00 PM'
    };
  } else {
    return {
      mealType: 'Evening Snack',
      suggestedTime: '9:30 PM'
    };
  }
}