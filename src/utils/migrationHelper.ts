

import { db } from '@/lib/firebase';
import { getDoc, getDocs, doc, collection, query, where } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Get the current user ID for Firebase operations
 */
export async function getCurrentUserId(): Promise<string> {
  const auth = getAuth();
  const user = auth.currentUser;
  
  if (!user) {
    throw new Error('No authenticated user found');
  }
  
  return user.uid;
}

/**
 * Tests a service that has been migrated to Firebase
 * 
 * @param serviceName The name of the service to test
 * @param options Test options including collection name and test data
 * @returns A result object with test status and details
 */
export async function testFirebaseService(
  serviceName: string,
  options: {
    collectionName: string;
    testData?: Record<string, any>;
    documentId?: string;
    queryField?: string;
    queryValue?: any;
  }
): Promise<{
  service: string;
  success: boolean;
  message: string;
  data?: any;
  error?: any;
}> {
  try {
    // Log the test being run
    console.log(`Testing Firebase service: ${serviceName}`);
    
    // Get a reference to the specified collection
    const collectionRef = collection(db, options.collectionName);
    
    let result: any;
    
    // If documentId is provided, get a specific document
    if (options.documentId) {
      const docRef = doc(db, options.collectionName, options.documentId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        result = {
          id: docSnap.id,
          ...docSnap.data()
        };
        return {
          service: serviceName,
          success: true,
          message: `Successfully retrieved document from ${options.collectionName}`,
          data: result
        };
      } else {
        return {
          service: serviceName,
          success: false,
          message: `Document not found in ${options.collectionName}`,
        };
      }
    }
    
    // If query parameters are provided, run a query
    if (options.queryField && options.queryValue !== undefined) {
      const q = query(
        collectionRef,
        where(options.queryField, '==', options.queryValue)
      );
      
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return {
          service: serviceName,
          success: false,
          message: `No documents found in ${options.collectionName} matching query`,
        };
      }
      
      // Convert query results to array
      result = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      return {
        service: serviceName,
        success: true,
        message: `Successfully queried ${querySnapshot.size} documents from ${options.collectionName}`,
        data: result
      };
    }
    
    // If no specific parameters, get all documents
    const querySnapshot = await getDocs(collectionRef);
    
    if (querySnapshot.empty) {
      return {
        service: serviceName,
        success: false,
        message: `No documents found in ${options.collectionName}`,
      };
    }
    
    // Convert query results to array
    result = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
    
    return {
      service: serviceName,
      success: true,
      message: `Successfully retrieved ${querySnapshot.size} documents from ${options.collectionName}`,
      data: result
    };
  } catch (error) {
    console.error(`Error testing Firebase service ${serviceName}:`, error);
    return {
      service: serviceName,
      success: false,
      message: `Error testing Firebase service: ${error instanceof Error ? error.message : 'Unknown error'}`,
      error
    };
  }
}

/**
 * Tests a service with cached data in AsyncStorage
 * 
 * @param serviceName The name of the service to test
 * @param storageKey The AsyncStorage key where data is cached
 * @returns A result object with test status and details
 */
export async function testCachedService(
  serviceName: string,
  storageKey: string
): Promise<{
  service: string;
  success: boolean;
  message: string;
  data?: any;
  error?: any;
}> {
  try {
    // Log the test being run
    console.log(`Testing cached service: ${serviceName}`);
    
    // Try to get data from AsyncStorage
    const cachedData = await AsyncStorage.getItem(storageKey);
    
    if (!cachedData) {
      return {
        service: serviceName,
        success: false,
        message: `No cached data found for key: ${storageKey}`,
      };
    }
    
    // Parse the cached data
    const data = JSON.parse(cachedData);
    
    return {
      service: serviceName,
      success: true,
      message: `Successfully retrieved cached data for ${serviceName}`,
      data
    };
  } catch (error) {
    console.error(`Error testing cached service ${serviceName}:`, error);
    return {
      service: serviceName,
      success: false,
      message: `Error testing cached service: ${error instanceof Error ? error.message : 'Unknown error'}`,
      error
    };
  }
}

/**
 * Tests a combination of Firebase and cached data for a service
 * 
 * @param serviceName The name of the service to test
 * @param options Test options including both Firebase and cache details
 * @returns A result object with test status and details
 */
export async function testHybridService(
  serviceName: string,
  options: {
    collectionName: string;
    storageKey: string;
    documentId?: string;
    queryField?: string;
    queryValue?: any;
  }
): Promise<{
  service: string;
  success: boolean;
  message: string;
  firebaseData?: any;
  cachedData?: any;
  consistent: boolean;
  error?: any;
}> {
  try {
    // Log the test being run
    console.log(`Testing hybrid service: ${serviceName}`);
    
    // Test Firebase service
    const firebaseResult = await testFirebaseService(serviceName, {
      collectionName: options.collectionName,
      documentId: options.documentId,
      queryField: options.queryField,
      queryValue: options.queryValue
    });
    
    // Test cached service
    const cacheResult = await testCachedService(serviceName, options.storageKey);
    
    // Check consistency
    const firebaseSuccess = firebaseResult.success;
    const cacheSuccess = cacheResult.success;
    
    // Both tests should succeed for full consistency
    const bothSucceeded = firebaseSuccess && cacheSuccess;
    
    // If we have both sets of data, try to compare them
    let dataConsistent = false;
    if (bothSucceeded) {
      // This is a simplified consistency check - in practice this might need more logic
      // depending on how data formats differ between Firebase and AsyncStorage
      dataConsistent = JSON.stringify(firebaseResult.data) === JSON.stringify(cacheResult.data);
    }
    
    const consistent = bothSucceeded && dataConsistent;
    
    return {
      service: serviceName,
      success: firebaseSuccess || cacheSuccess, // Consider successful if either works
      message: consistent 
        ? `${serviceName} service is fully consistent between Firebase and cache` 
        : `${serviceName} service has inconsistencies between Firebase and cache`,
      firebaseData: firebaseResult.data,
      cachedData: cacheResult.data,
      consistent,
      error: firebaseResult.error || cacheResult.error
    };
  } catch (error) {
    console.error(`Error testing hybrid service ${serviceName}:`, error);
    return {
      service: serviceName,
      success: false,
      message: `Error testing hybrid service: ${error instanceof Error ? error.message : 'Unknown error'}`,
      consistent: false,
      error
    };
  }
}

/**
 * Get a report on services migrated to Firebase
 * 
 * @param services List of services to check
 * @returns A report object with details about migration status
 */
export async function getMigrationReport(
  services: string[] = [
    'profile', 
    'challenge', 
    'water-intake',
    'nutrition-goal',
    'food-logs',
    'nutrition-data'
  ]
): Promise<{
  timestamp: string;
  services: {
    name: string;
    migrated: boolean;
    working: boolean;
    details?: string;
  }[];
  totalServices: number;
  migratedServices: number;
  migrationPercentage: number;
}> {
  try {
    const userId = await getCurrentUserId();
    
    const report = {
      timestamp: new Date().toISOString(),
      services: [],
      totalServices: services.length,
      migratedServices: 0,
      migrationPercentage: 0
    } as any;
    
    // Test each service
    for (const service of services) {
      let result = {
        name: service,
        migrated: false,
        working: false,
        details: ''
      };
      
      try {
        switch (service) {
          case 'profile':
            const profileTest = await testFirebaseService('User Profile', {
              collectionName: 'profiles',
              documentId: userId
            });
            result.migrated = true;
            result.working = profileTest.success;
            result.details = profileTest.message;
            break;
            
          case 'challenge':
            const challengeTest = await testFirebaseService('Challenge', {
              collectionName: 'user_challenges',
              queryField: 'user_id',
              queryValue: userId
            });
            result.migrated = true;
            result.working = challengeTest.success;
            result.details = challengeTest.message;
            break;
            
          case 'water-intake':
            const waterTest = await testHybridService('Water Intake', {
              collectionName: 'water_intake',
              storageKey: 'water_intake_' + new Date().toISOString().split('T')[0],
              queryField: 'user_id',
              queryValue: userId
            });
            result.migrated = true;
            result.working = waterTest.success;
            result.details = waterTest.message;
            break;
            
          case 'nutrition-goal':
            const nutritionTest = await testHybridService('Nutrition Goals', {
              collectionName: 'profiles',
              storageKey: 'user_nutrition_profile',
              documentId: userId
            });
            result.migrated = true;
            result.working = nutritionTest.success;
            result.details = nutritionTest.message;
            break;
            
          case 'food-logs':
            const foodLogsTest = await testFirebaseService('Food Logs', {
              collectionName: 'food_logs',
              queryField: 'userId',
              queryValue: userId
            });
            result.migrated = true;
            result.working = foodLogsTest.success;
            result.details = foodLogsTest.message;
            break;
            
          case 'nutrition-data':
            const nutritionDataTest = await testFirebaseService('Nutrition Data', {
              collectionName: 'nutrition_data'
            });
            result.migrated = true;
            result.working = nutritionDataTest.success;
            result.details = nutritionDataTest.message;
            break;
            
          default:
            result.details = 'Service not configured for testing';
        }
      } catch (error) {
        result.details = `Error testing: ${error instanceof Error ? error.message : 'Unknown error'}`;
      }
      
      // Add service to report
      report.services.push(result);
      
      // Update migration count
      if (result.migrated) {
        report.migratedServices++;
      }
    }
    
    // Calculate percentage
    report.migrationPercentage = Math.round((report.migratedServices / report.totalServices) * 100);
    
    return report;
  } catch (error) {
    console.error('Error generating migration report:', error);
    throw error;
  }
} 