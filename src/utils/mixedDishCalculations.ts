import { getFoodDensity, FOOD_CATEGORIES } from './foodDensityMap';

/**
 * Types of mixed dishes for specialized density calculations
 */
export enum MixedDishType {
  SALAD = 'salad',
  SOUP = 'soup',
  STIR_FRY = 'stirFry',
  CASSEROLE = 'casserole',
  PASTA_DISH = 'pastaDish',
  RICE_DISH = 'riceDish',
  SANDWICH = 'sandwich',
  STEW = 'stew',
  CURRY = 'curry',
  BOWL = 'bowl',
  PLATTER = 'platter',
  UNKNOWN = 'unknown'
}

/**
 * Known composition profiles for different mixed dish types
 * Each profile represents the estimated composition by percentage.
 * The percentages should add up to 1 (100%)
 */
interface CompositionProfile {
  grains?: number;
  vegetables?: number;
  proteins?: number;
  dairy?: number;
  sauces?: number;
  oils?: number;
  fruits?: number;
  nuts?: number;
}

/**
 * Standard composition profiles for common mixed dish types
 */
const COMPOSITION_PROFILES: Record<MixedDishType, CompositionProfile> = {
  [MixedDishType.SALAD]: {
    vegetables: 0.7,
    proteins: 0.15,
    oils: 0.05,
    dairy: 0.05,
    nuts: 0.03,
    fruits: 0.02
  },
  [MixedDishType.SOUP]: {
    vegetables: 0.4,
    proteins: 0.15,
    grains: 0.1,
    oils: 0.05,
    dairy: 0.05,
    sauces: 0.25
  },
  [MixedDishType.STIR_FRY]: {
    vegetables: 0.45,
    proteins: 0.3,
    grains: 0.15,
    oils: 0.05,
    sauces: 0.05
  },
  [MixedDishType.CASSEROLE]: {
    proteins: 0.3,
    vegetables: 0.25,
    grains: 0.25,
    dairy: 0.15,
    oils: 0.05
  },
  [MixedDishType.PASTA_DISH]: {
    grains: 0.5,
    proteins: 0.2,
    vegetables: 0.15,
    dairy: 0.1,
    sauces: 0.05
  },
  [MixedDishType.RICE_DISH]: {
    grains: 0.5,
    proteins: 0.2,
    vegetables: 0.2,
    oils: 0.05,
    sauces: 0.05
  },
  [MixedDishType.SANDWICH]: {
    grains: 0.4,
    proteins: 0.3,
    vegetables: 0.15,
    dairy: 0.1,
    sauces: 0.05
  },
  [MixedDishType.STEW]: {
    proteins: 0.35,
    vegetables: 0.35,
    grains: 0.1,
    sauces: 0.2
  },
  [MixedDishType.CURRY]: {
    proteins: 0.3,
    vegetables: 0.25,
    sauces: 0.25,
    grains: 0.15,
    oils: 0.05
  },
  [MixedDishType.BOWL]: {
    grains: 0.35,
    vegetables: 0.3,
    proteins: 0.25,
    sauces: 0.1
  },
  [MixedDishType.PLATTER]: {
    proteins: 0.35,
    grains: 0.25,
    vegetables: 0.3,
    sauces: 0.1
  },
  [MixedDishType.UNKNOWN]: {
    grains: 0.3,
    proteins: 0.3,
    vegetables: 0.3,
    sauces: 0.1
  }
};

/**
 * Average density values for different food components (g/cm³)
 */
const COMPONENT_DENSITIES: Record<string, number> = {
  grains: 0.75,
  vegetables: 0.6,
  proteins: 0.95,
  dairy: 0.85,
  sauces: 1.05,
  oils: 0.92,
  fruits: 0.65,
  nuts: 0.55
};

/**
 * Detect the type of mixed dish from food name
 * 
 * @param foodName The name of the mixed dish
 * @returns The detected dish type
 */
export function detectMixedDishType(foodName: string): MixedDishType {
  const normalizedName = foodName.toLowerCase();
  
  // Check for specific dish types based on keywords
  if (/salad|slaw/i.test(normalizedName)) {
    return MixedDishType.SALAD;
  }
  
  if (/soup|broth|chowder|bisque/i.test(normalizedName)) {
    return MixedDishType.SOUP;
  }
  
  if (/stir[\s-]?fry|stir[\s-]?fried/i.test(normalizedName)) {
    return MixedDishType.STIR_FRY;
  }
  
  if (/casserole|bake|gratin|hotdish/i.test(normalizedName)) {
    return MixedDishType.CASSEROLE;
  }
  
  if (/pasta|spaghetti|fettuccine|linguine|penne|macaroni|lasagna|noodle/i.test(normalizedName)) {
    return MixedDishType.PASTA_DISH;
  }
  
  if (/rice|risotto|pilaf|biryani|paella|jambalaya/i.test(normalizedName)) {
    return MixedDishType.RICE_DISH;
  }
  
  if (/sandwich|burger|sub|wrap|panini|toast/i.test(normalizedName)) {
    return MixedDishType.SANDWICH;
  }
  
  if (/stew|braise|pot[\s-]?roast|goulash/i.test(normalizedName)) {
    return MixedDishType.STEW;
  }
  
  if (/curry|masala|tikka|korma|vindaloo/i.test(normalizedName)) {
    return MixedDishType.CURRY;
  }
  
  if (/bowl|poke/i.test(normalizedName)) {
    return MixedDishType.BOWL;
  }
  
  if (/platter|plate|combo|combination|mixed/i.test(normalizedName)) {
    return MixedDishType.PLATTER;
  }
  
  return MixedDishType.UNKNOWN;
}

/**
 * Adjusts a composition profile based on known ingredients in the dish name
 * 
 * @param dishType The type of mixed dish
 * @param foodName The full name of the food with potential ingredient hints
 * @returns An adjusted composition profile
 */
function adjustProfileForIngredients(dishType: MixedDishType, foodName: string): CompositionProfile {
  const normalizedName = foodName.toLowerCase();
  const baseProfile = { ...COMPOSITION_PROFILES[dishType] };
  
  // Key ingredients that would significantly adjust the composition
  const ingredientPatterns = [
    { pattern: /chicken|turkey|beef|pork|fish|shrimp|seafood|tofu|meat/i, component: 'proteins', adjustment: 0.1 },
    { pattern: /veggie|vegetable|vegan|vegetarian/i, component: 'vegetables', adjustment: 0.1 },
    { pattern: /cheese|cream|yogurt|milk/i, component: 'dairy', adjustment: 0.1 },
    { pattern: /rice|quinoa|pasta|noodle|grain|bread/i, component: 'grains', adjustment: 0.1 },
    { pattern: /oil|vinaigrette|dressing/i, component: 'oils', adjustment: 0.05 },
    { pattern: /sauce|gravy|broth/i, component: 'sauces', adjustment: 0.1 },
    { pattern: /apple|berry|orange|fruit/i, component: 'fruits', adjustment: 0.1 },
    { pattern: /nut|seed|almond|walnut|pecan/i, component: 'nuts', adjustment: 0.05 }
  ];
  
  // Check for ingredient patterns and adjust the profile
  for (const { pattern, component, adjustment } of ingredientPatterns) {
    if (pattern.test(normalizedName)) {
      const currentValue = baseProfile[component as keyof CompositionProfile] || 0;
      baseProfile[component as keyof CompositionProfile] = currentValue + adjustment;
    }
  }
  
  // Normalize the profile to ensure percentages add up to 1 (100%)
  const total = Object.values(baseProfile).reduce((sum, value) => sum + value, 0);
  
  if (total > 0) {
    for (const component in baseProfile) {
      baseProfile[component as keyof CompositionProfile] = 
        (baseProfile[component as keyof CompositionProfile] || 0) / total;
    }
  }
  
  return baseProfile;
}

/**
 * Calculate the density for a mixed dish based on its estimated composition
 * 
 * @param foodName Name of the mixed dish
 * @returns Calculated density in g/cm³
 */
export function calculateMixedDishDensity(foodName: string): number {
  // If there's a direct match in the food density database, use that
  const directDensity = getFoodDensity(foodName);
  if (directDensity !== 0.7) { // Not the default value
    return directDensity;
  }
  
  // Detect dish type and get the composition profile
  const dishType = detectMixedDishType(foodName);
  const profile = adjustProfileForIngredients(dishType, foodName);
  
  // Calculate weighted average density based on the composition profile
  let weightedDensity = 0;
  let totalWeight = 0;
  
  for (const [component, percentage] of Object.entries(profile)) {
    if (percentage && percentage > 0) {
      weightedDensity += COMPONENT_DENSITIES[component] * percentage;
      totalWeight += percentage;
    }
  }
  
  // Safety check in case the profile is empty
  if (totalWeight <= 0) {
    return 0.8; // Fallback average density for mixed dishes
  }
  
  return weightedDensity / totalWeight;
}

/**
 * Calculate the volume distribution of components in a mixed dish
 * Useful for more detailed nutrition analysis of mixed dishes
 * 
 * @param foodName Name of the mixed dish
 * @param totalVolume Total volume of the dish in cm³
 * @returns Object with volume estimates for each component
 */
export function calculateMixedDishComponents(
  foodName: string,
  totalVolume: number
): Record<string, { volume: number; weight: number }> {
  const dishType = detectMixedDishType(foodName);
  const profile = adjustProfileForIngredients(dishType, foodName);
  const result: Record<string, { volume: number; weight: number }> = {};
  
  for (const [component, percentage] of Object.entries(profile)) {
    if (percentage && percentage > 0) {
      const componentVolume = totalVolume * percentage;
      const componentDensity = COMPONENT_DENSITIES[component];
      const componentWeight = componentVolume * componentDensity;
      
      result[component] = {
        volume: componentVolume,
        weight: componentWeight
      };
    }
  }
  
  return result;
}

/**
 * Estimate the nutritional value adjustment factor for a mixed dish
 * Some dishes have preparation methods that affect nutrition (e.g., fried vs. baked)
 * 
 * @param foodName Name of the mixed dish
 * @returns Adjustment factor for calorie estimation (1.0 = no adjustment)
 */
export function getNutritionalAdjustmentFactor(foodName: string): number {
  const normalizedName = foodName.toLowerCase();
  
  // Cooking method adjustments
  if (/fried|fry/i.test(normalizedName)) {
    return 1.3; // Fried foods generally higher in calories
  }
  
  if (/baked|roasted|grilled/i.test(normalizedName)) {
    return 1.0; // Standard cooking methods
  }
  
  if (/boiled|steamed|poached/i.test(normalizedName)) {
    return 0.9; // Lower-calorie cooking methods
  }
  
  if (/creamy|cream|cheese/i.test(normalizedName)) {
    return 1.2; // Dishes with cream/cheese tend to have higher calories
  }
  
  // Diet-specific adjustments
  if (/vegan|vegetarian/i.test(normalizedName)) {
    return 0.9; // Plant-based dishes often lower in calories
  }
  
  if (/keto|low[\s-]?carb/i.test(normalizedName)) {
    return 0.95; // Lower in carbs but higher in fats
  }
  
  return 1.0; // Default: no adjustment
} 