// Module mocks for React Native environment
import { Platform } from 'react-native';

// Only apply mocks on mobile platforms
if (Platform.OS !== 'web') {
  // Mock the 'stream' module
  if (!global.require) {
    global.require = (moduleName) => {
      if (moduleName === 'stream') {
        return require('./stream-shim').default;
      }
      
      // Return empty implementations for other common Node.js modules
      if (moduleName === 'crypto') {
        return {
          randomBytes: () => new Uint8Array(16),
          createHash: () => ({
            update: () => ({ digest: () => '0'.repeat(32) }),
          }),
        };
      }
      
      if (moduleName === 'fs' || moduleName === 'net' || 
          moduleName === 'tls' || moduleName === 'child_process') {
        return {};
      }
      
      // For any other modules, return a no-op object
      return {};
    };
  }
  
  // Ensure process.nextTick is available
  if (!global.process) {
    global.process = {};
  }
  
  if (!global.process.nextTick) {
    global.process.nextTick = setTimeout;
  }
  
  // Ensure Buffer is available
  if (!global.Buffer) {
    global.Buffer = {
      from: (data) => {
        if (typeof data === 'string') {
          return new TextEncoder().encode(data);
        }
        return new Uint8Array(data);
      },
      isBuffer: () => false,
      alloc: (size) => new Uint8Array(size),
    };
  }
}

export default {}; 