// Mock implementation of expo-notifications for TypeScript
// This file resolves the "Cannot find module" error when the actual package can't be installed

export async function getPermissionsAsync() {
  return { status: 'granted' };
}

export async function requestPermissionsAsync() {
  return { status: 'granted' };
}

// Add more mock functions as needed for your application
export default {
  getPermissionsAsync,
  requestPermissionsAsync
}; 