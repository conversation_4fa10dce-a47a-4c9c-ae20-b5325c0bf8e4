/**
 * This file contains utilities for processing natural language food entries.
 * Uses Nutritionix API for real food data and nutrition information.
 */
import { 
  getNutritionixNutrients, 
  searchNutritionixFoods, 
  extractNutritionixNutrition,
  getNutritionData
} from '../services/nutritionService';
import { 
  getNutritionByQuery
} from '../services/spoonacularService';
import { isSpoonacularConfigured } from '@/utils/config';

// Types
export interface FoodEntity {
  name: string;
  quantity?: number;
  unit?: string;
  mealTime?: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  date?: Date;
  confidence: number;
}

export interface ParsedFoodEntry {
  entities: FoodEntity[];
  rawText: string;
  suggestedCorrection?: string;
}

// Quantity units and their variations
const QUANTITY_UNITS = {
  gram: ['g', 'gram', 'grams'],
  kilogram: ['kg', 'kilogram', 'kilograms'],
  ounce: ['oz', 'ounce', 'ounces'],
  pound: ['lb', 'lbs', 'pound', 'pounds'],
  cup: ['cup', 'cups', 'c'],
  tablespoon: ['tbsp', 'tbs', 'tablespoon', 'tablespoons'],
  teaspoon: ['tsp', 'teaspoon', 'teaspoons'],
  milliliter: ['ml', 'milliliter', 'milliliters'],
  liter: ['l', 'liter', 'liters'],
  piece: ['piece', 'pieces', 'pc', 'pcs'],
  serving: ['serving', 'servings', 'serv'],
  slice: ['slice', 'slices'],
};

// Meal times and their variations
const MEAL_TIMES = {
  breakfast: ['breakfast', 'morning', 'brekkie', 'brekky'],
  lunch: ['lunch', 'noon', 'midday'],
  dinner: ['dinner', 'evening', 'supper', 'tea'],
  snack: ['snack', 'afternoon', 'evening snack', 'morning snack', 'night snack'],
};

// Food nutrition data interface
export interface FoodNutritionData {
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  servingSize: string;
  category: string;
}

/**
 * Parse a natural language food entry into structured data
 */
export async function parseFoodEntry(text: string): Promise<ParsedFoodEntry> {
  const entities: FoodEntity[] = [];
  const words = text.toLowerCase().split(/\s+/);
  
  // Find meal time mentions
  const mealTime = findMealTime(text);
  
  // Find date mentions
  const date = findDateMention(text);
  
  // First try to parse the entire text as a complete food entry using Nutritionix
  try {
    const nutritionixData = await getNutritionixNutrients(text);
    
    if (nutritionixData && nutritionixData.foods && nutritionixData.foods.length > 0) {
      // Nutritionix was able to parse the text
      for (const food of nutritionixData.foods) {
        entities.push({
          name: capitalizeFirst(food.food_name),
          quantity: food.serving_qty,
          unit: food.serving_unit,
          mealTime,
          date,
          confidence: 0.9, // High confidence as this comes from a real API
        });
      }
      
      return {
        entities,
        rawText: text,
      };
    }
  } catch (error) {
    console.error('Error parsing food entry with Nutritionix:', error);
    // Continue with fallback parsing if Nutritionix fails
  }
  
  // If Nutritionix couldn't parse the entire entry, try to parse individual food items
  let i = 0;
  while (i < words.length) {
    // Check for quantities like "2 apples" or "1 cup of rice"
    let quantity: number | undefined = undefined;
    let unit: string | undefined = undefined;
    
    // Check if word is a number or fraction
    if (/^([\d]+)(\/[\d]+)?$/.test(words[i]) || !isNaN(parseFloat(words[i]))) {
      quantity = parseFloat(words[i]);
      i++;
      
      // Check if next word is a unit
      if (i < words.length) {
        const unitFound = findUnit(words[i]);
        if (unitFound) {
          unit = unitFound;
          i++;
        }
      }
    }
    
    // Look for food mentions after the quantity/unit
    const foodStartIndex = i;
    let foodEndIndex = i;
    let foundFood = false;
    
    // Try different word combinations to find food items
    for (let len = 3; len >= 1; len--) {
      if (foodStartIndex + len <= words.length) {
        const potentialFood = words.slice(foodStartIndex, foodStartIndex + len).join(' ');
        
        try {
          // Search for food in Nutritionix database
          const searchResults = await searchNutritionixFoods(potentialFood);
          
          if (searchResults && searchResults.length > 0) {
            const foodMatch = searchResults[0]; // Take the first match
            
            entities.push({
              name: capitalizeFirst(foodMatch.food_name),
              quantity,
              unit,
              mealTime,
              date,
              confidence: calculateConfidence(foodMatch, quantity, unit, mealTime),
            });
            
            foodEndIndex = foodStartIndex + len - 1;
            foundFood = true;
            break;
          }
        } catch (error) {
          console.error('Error searching Nutritionix for food:', error);
          // Continue with next length if search fails
        }
      }
    }
    
    if (foundFood) {
      i = foodEndIndex + 1;
    } else {
      // If no food is found, assume it's a new unknown food
      if (words[i] && !isCommonWord(words[i])) {
        // Look ahead for multi-word foods
        let j = i + 1;
        while (j < words.length && !isCommonWord(words[j]) && !isQuantityOrUnit(words[j])) {
          j++;
        }
        
        if (j > i) {
          const foodName = words.slice(i, j).join(' ');
          entities.push({
            name: capitalizeFirst(foodName),
            quantity,
            unit,
            mealTime,
            date,
            confidence: 0.5, // Lower confidence for unknown foods
          });
          i = j;
        } else {
          i++;
        }
      } else {
        i++;
      }
    }
  }
  
  // Handle the case where no entities were found
  if (entities.length === 0 && text.trim().length > 0) {
    // Try to guess what it might be
    const potentialFoodName = text.trim().split(/\s+in\s+|\s+for\s+|\s+at\s+/)[0];
    entities.push({
      name: capitalizeFirst(potentialFoodName),
      mealTime,
      date,
      confidence: 0.3, // Very low confidence
    });
  }
  
  return {
    entities,
    rawText: text,
    suggestedCorrection: generateSuggestion(entities, text),
  };
}

/**
 * Calculate a confidence score for the entity
 */
function calculateConfidence(
  food: any,
  quantity?: number,
  unit?: string,
  mealTime?: string
): number {
  let confidence = 0.7; // Base confidence for known foods
  
  // Increase confidence if we have quantity and unit
  if (quantity !== undefined) confidence += 0.1;
  if (unit !== undefined) confidence += 0.1;
  if (mealTime !== undefined) confidence += 0.1;
  
  return Math.min(confidence, 1.0);
}

/**
 * Find a meal time in the text
 */
function findMealTime(text: string): FoodEntity['mealTime'] | undefined {
  const lowerText = text.toLowerCase();
  
  for (const [mealTime, variations] of Object.entries(MEAL_TIMES)) {
    for (const variation of variations) {
      if (lowerText.includes(variation)) {
        return mealTime as FoodEntity['mealTime'];
      }
    }
  }
  
  return undefined;
}

/**
 * Find a date mention in the text
 */
function findDateMention(text: string): Date | undefined {
  const lowerText = text.toLowerCase();
  const today = new Date();
  
  if (lowerText.includes('today')) {
    return today;
  }
  
  if (lowerText.includes('yesterday')) {
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    return yesterday;
  }
  
  if (lowerText.includes('tomorrow')) {
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow;
  }
  
  // Implement more sophisticated date parsing here if needed
  
  return undefined;
}

/**
 * Find a unit in the text
 */
function findUnit(text: string): string | undefined {
  const normalizedText = text.toLowerCase().trim();
  
  for (const [standardUnit, variations] of Object.entries(QUANTITY_UNITS)) {
    if (variations.includes(normalizedText)) {
      return standardUnit;
    }
  }
  
  return undefined;
}

/**
 * Check if a word is a common non-food word
 */
function isCommonWord(word: string): boolean {
  const commonWords = [
    'and', 'with', 'of', 'the', 'a', 'an', 'in', 'for', 'to', 'at',
    'on', 'by', 'from', 'had', 'has', 'have', 'is', 'was', 'were',
    'being', 'been', 'be', 'i', 'you', 'he', 'she', 'it', 'we', 'they',
  ];
  
  return commonWords.includes(word.toLowerCase());
}

/**
 * Check if a word is a quantity or unit
 */
function isQuantityOrUnit(word: string): boolean {
  if (!isNaN(parseFloat(word))) return true;
  
  for (const variations of Object.values(QUANTITY_UNITS)) {
    if (variations.includes(word.toLowerCase())) return true;
  }
  
  return false;
}

/**
 * Capitalize the first letter of a string
 */
function capitalizeFirst(str: string): string {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * Generate a suggested correction if entities are low confidence
 */
function generateSuggestion(entities: FoodEntity[], originalText: string): string | undefined {
  // If we have high confidence entities, no suggestion needed
  if (entities.every(e => e.confidence > 0.7)) {
    return undefined;
  }
  
  // For entities with low confidence, suggest a more structured format
  const lowConfidenceEntities = entities.filter(e => e.confidence <= 0.7);
  
  if (lowConfidenceEntities.length > 0) {
    const suggestions = lowConfidenceEntities.map(entity => {
      const quantity = entity.quantity ? `${entity.quantity} ` : '';
      const unit = entity.unit ? `${entity.unit} of ` : '';
      const mealTime = entity.mealTime ? ` for ${entity.mealTime}` : '';
      
      return `${quantity}${unit}${entity.name}${mealTime}`;
    });
    
    return `Did you mean: ${suggestions.join(', ')}?`;
  }
  
  return undefined;
}

/**
 * Get nutrition information for a food entity
 */
export async function getNutritionInfo(entity: FoodEntity): Promise<FoodNutritionData | null> {
  try {
    const query = entity.quantity && entity.unit
      ? `${entity.quantity} ${entity.unit} ${entity.name}`
      : entity.quantity
        ? `${entity.quantity} ${entity.name}`
        : entity.name;
    
    // First try using Spoonacular if configured
    if (isSpoonacularConfigured()) {
      try {
        const spoonacularData = await getNutritionByQuery(query);
        
        if (spoonacularData && Array.isArray(spoonacularData) && spoonacularData.length > 0) {
          const foodData = spoonacularData[0];
          return {
            name: entity.name,
            calories: Math.round(foodData.nutrition?.nutrients?.find((n: any) => n.name === "Calories")?.amount || 0),
            protein: Math.round(foodData.nutrition?.nutrients?.find((n: any) => n.name === "Protein")?.amount || 0),
            carbs: Math.round(foodData.nutrition?.nutrients?.find((n: any) => n.name === "Carbohydrates")?.amount || 0),
            fat: Math.round(foodData.nutrition?.nutrients?.find((n: any) => n.name === "Fat")?.amount || 0),
            servingSize: `${foodData.amount || 1} ${foodData.unit || 'serving'}`,
            category: 'Foods',
          };
        }
      } catch (spoonacularError) {
        console.error('Error getting nutrition from Spoonacular:', spoonacularError);
        // Continue to try other data sources
      }
    }
    
    // Fallback to Nutritionix API
    try {
      const nutritionixData = await getNutritionixNutrients(query);
      
      if (nutritionixData && nutritionixData.foods && nutritionixData.foods.length > 0) {
        const food = nutritionixData.foods[0];
        const nutrition = extractNutritionixNutrition(food);
        
        return {
          name: capitalizeFirst(food.food_name),
          calories: nutrition.calories,
          protein: nutrition.protein,
          carbs: nutrition.carbs,
          fat: nutrition.fat,
          servingSize: `${food.serving_qty} ${food.serving_unit} (${food.serving_weight_grams}g)`,
          category: food.food_name.includes('recipe') ? 'Recipes' : 'Foods',
        };
      }
    } catch (nutritionixError) {
      console.error('Error getting nutrition from Nutritionix:', nutritionixError);
      // Continue to try internal data
    }
    
    // Fall back to using our internal API
    const internalData = await getNutritionData(entity.name);
    if (internalData) {
      return {
        name: entity.name,
        calories: internalData.calories,
        protein: internalData.protein,
        carbs: internalData.carbs,
        fat: internalData.fat,
        servingSize: internalData.portions?.[0]?.name || 'unknown',
        category: 'Foods',
      };
    }
    
    return null;
  } catch (error) {
    console.error('Error getting nutrition info:', error);
    return null;
  }
}

// Export a function to batch process multiple food entries
export async function batchProcessEntries(entries: string[]): Promise<ParsedFoodEntry[]> {
  const results: ParsedFoodEntry[] = [];
  
  for (const entry of entries) {
    try {
      const result = await parseFoodEntry(entry);
      results.push(result);
    } catch (error) {
      console.error(`Error processing entry "${entry}":`, error);
      // Add a placeholder for failed entries
      results.push({
        entities: [],
        rawText: entry,
        suggestedCorrection: 'Failed to process this entry. Please try again with different wording.',
      });
    }
  }
  
  return results;
}

export default {
  parseFoodEntry,
  getNutritionInfo,
  batchProcessEntries,
}; 