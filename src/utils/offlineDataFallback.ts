import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

/**
 * Utility functions to provide offline data fallback for when Firebase is unavailable
 * Particularly important for iOS where IndexedDB is not supported in WebView
 */

// Storage keys
const APP_CONFIG_KEY = 'essential_app_config';
const USER_DATA_KEY = 'local_user_data';
const WATER_INTAKE_PREFIX = 'water_intake_';
const IS_OFFLINE_MODE_KEY = 'is_offline_mode';
const LAST_SYNC_KEY = 'last_firebase_sync';
const PENDING_OPERATIONS_KEY = 'pending_firebase_operations';

// Track if we've confirmed we're in offline mode
let confirmedOfflineMode = false;

// Get app configuration from either Firebase or local storage
export async function getAppConfig() {
  try {
    // Try to get from local storage first
    const localConfig = await AsyncStorage.getItem(APP_CONFIG_KEY);
    if (localConfig) {
      return JSON.parse(localConfig);
    }
    
    // If we don't have local config, return defaults
    return {
      water_tracking: {
        enabled: true,
        default_goal: 2000, // ml
        reminder_interval: 60, // minutes
      },
      offline_mode: {
        enabled: true,
        sync_on_reconnect: true,
        max_cache_days: 7
      },
      ui_config: {
        dark_mode: false,
        accessibility_features: true,
        animations_enabled: true
      },
      version: '1.0.0',
      updated_at: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error getting app config:', error);
    // Return sensible defaults if all else fails
    return {
      water_tracking: { enabled: true, default_goal: 2000 },
      offline_mode: { enabled: true },
      ui_config: { dark_mode: false }
    };
  }
}

// Store user data locally
export async function storeLocalUserData(userData: any) {
  try {
    await AsyncStorage.setItem(USER_DATA_KEY, JSON.stringify(userData));
    return true;
  } catch (error) {
    console.error('Error storing local user data:', error);
    return false;
  }
}

// Get user data from local storage
export async function getLocalUserData() {
  try {
    const userData = await AsyncStorage.getItem(USER_DATA_KEY);
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('Error getting local user data:', error);
    return null;
  }
}

// Set application to offline mode
export async function setOfflineMode(isOffline: boolean) {
  try {
    await AsyncStorage.setItem(IS_OFFLINE_MODE_KEY, isOffline ? 'true' : 'false');
    confirmedOfflineMode = isOffline;
    return true;
  } catch (error) {
    console.error('Error setting offline mode:', error);
    return false;
  }
}

// Check if the app is currently in offline mode
export async function isInOfflineMode() {
  // Return cached result if available
  if (confirmedOfflineMode) return true;
  
  try {
    const value = await AsyncStorage.getItem(IS_OFFLINE_MODE_KEY);
    confirmedOfflineMode = value === 'true';
    return confirmedOfflineMode;
  } catch {
    // Default to false if there's an error
    return false;
  }
}

// Record a pending operation to sync later
export async function recordPendingOperation(collection: string, operation: 'add' | 'update' | 'delete', data: any) {
  try {
    const pendingOpsJson = await AsyncStorage.getItem(PENDING_OPERATIONS_KEY);
    const pendingOps = pendingOpsJson ? JSON.parse(pendingOpsJson) : [];
    
    pendingOps.push({
      collection,
      operation,
      data,
      timestamp: new Date().toISOString()
    });
    
    await AsyncStorage.setItem(PENDING_OPERATIONS_KEY, JSON.stringify(pendingOps));
    return true;
  } catch (error) {
    console.error('Error recording pending operation:', error);
    return false;
  }
}

// Get all pending operations
export async function getPendingOperations() {
  try {
    const pendingOpsJson = await AsyncStorage.getItem(PENDING_OPERATIONS_KEY);
    return pendingOpsJson ? JSON.parse(pendingOpsJson) : [];
  } catch (error) {
    console.error('Error getting pending operations:', error);
    return [];
  }
}

// Clear pending operations (after successful sync)
export async function clearPendingOperations() {
  try {
    await AsyncStorage.removeItem(PENDING_OPERATIONS_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing pending operations:', error);
    return false;
  }
}

// Get water intake data from local storage
export async function getLocalWaterIntake(date: string) {
  try {
    const key = `${WATER_INTAKE_PREFIX}${date}`;
    const data = await AsyncStorage.getItem(key);
    return data ? JSON.parse(data) : { date, amount: 0, goal: 2000, unit: 'ml' };
  } catch (error) {
    console.error('Error getting local water intake:', error);
    return { date, amount: 0, goal: 2000, unit: 'ml' };
  }
}

// Store water intake data locally
export async function storeLocalWaterIntake(date: string, amount: number, goal: number = 2000, unit: string = 'ml') {
  try {
    const key = `${WATER_INTAKE_PREFIX}${date}`;
    await AsyncStorage.setItem(key, JSON.stringify({ date, amount, goal, unit }));
    
    // Also record as a pending operation if in offline mode
    if (await isInOfflineMode()) {
      await recordPendingOperation('water_intake', 'add', { 
        date, 
        amount, 
        goal, 
        unit,
        created_at: new Date().toISOString()
      });
    }
    
    return true;
  } catch (error) {
    console.error('Error storing local water intake:', error);
    return false;
  }
}

// Check if we're running on iOS (where IndexedDB and Firebase persistence often fail)
export function isIOSEnvironment() {
  return Platform.OS === 'ios';
}

// Check if we should use offline fallbacks (based on platform and potentially connection state)
export async function shouldUseOfflineFallbacks() {
  // Check if we're already confirmed in offline mode
  if (confirmedOfflineMode) return true;
  
  // Always use fallbacks on iOS for now
  if (isIOSEnvironment()) {
    return true;
  }
  
  // Check if user has explicitly enabled offline mode
  try {
    // First check AsyncStorage flag
    const isOffline = await AsyncStorage.getItem(IS_OFFLINE_MODE_KEY);
    if (isOffline === 'true') {
      confirmedOfflineMode = true;
      return true;
    }
    
    // Then check app config
    const config = await getAppConfig();
    return config?.offline_mode?.enabled === true;
  } catch {
    return false;
  }
}

export default {
  getAppConfig,
  storeLocalUserData,
  getLocalUserData,
  getLocalWaterIntake,
  storeLocalWaterIntake,
  isIOSEnvironment,
  shouldUseOfflineFallbacks,
  setOfflineMode,
  isInOfflineMode,
  recordPendingOperation,
  getPendingOperations,
  clearPendingOperations
}; 