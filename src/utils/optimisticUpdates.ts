import { useState, useCallback } from 'react';
import * as Haptics from 'expo-haptics';

interface OptimisticUpdateOptions<T> {
  /**
   * Initial data state
   */
  initialData: T;
  
  /**
   * Whether to trigger haptic feedback on updates
   */
  enableHaptics?: boolean;
  
  /**
   * Callback for when an error occurs
   */
  onError?: (error: Error, rollbackData: T) => void;
}

/**
 * A utility hook for implementing optimistic updates.
 * It allows the UI to be updated immediately before server confirmation,
 * with automatic rollback if the server request fails.
 */
export function useOptimisticUpdates<T>({
  initialData,
  enableHaptics = true,
  onError,
}: OptimisticUpdateOptions<T>) {
  // Current data state
  const [data, setData] = useState<T>(initialData);
  
  // Current loading state
  const [isLoading, setIsLoading] = useState(false);
  
  // Track update operations in progress
  const [pendingUpdates, setPendingUpdates] = useState<number>(0);
  
  /**
   * Perform an optimistic update
   * @param optimisticUpdateFn Function to update the data immediately
   * @param serverUpdateFn Function to send the update to the server
   * @returns Promise that resolves when the server update completes
   */
  const performOptimisticUpdate = useCallback(async <R>(
    optimisticUpdateFn: (currentData: T) => T,
    serverUpdateFn: () => Promise<R>,
  ): Promise<R> => {
    // Save the previous data state for potential rollback
    const previousData = { ...data };
    
    // Update the UI immediately with the optimistic change
    setData(optimisticUpdateFn(data));
    
    // Increase pending updates counter
    setPendingUpdates(count => count + 1);
    setIsLoading(true);
    
    // Provide haptic feedback if enabled
    if (enableHaptics) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    
    try {
      // Attempt to perform the actual server update
      const result = await serverUpdateFn();
      
      // Update complete successfully
      return result;
    } catch (error) {
      // Server update failed, rollback to previous state
      setData(previousData);
      
      // Provide error feedback
      if (enableHaptics) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      }
      
      // Call error handler if provided
      if (onError && error instanceof Error) {
        onError(error, previousData);
      }
      
      // Re-throw the error to allow caller to handle it
      throw error;
    } finally {
      // Decrease pending updates counter
      setPendingUpdates(count => Math.max(0, count - 1));
      
      // Update loading state based on pending updates
      if (pendingUpdates <= 1) {
        setIsLoading(false);
      }
    }
  }, [data, enableHaptics, onError, pendingUpdates]);
  
  return {
    data,
    setData,
    isLoading,
    pendingUpdates: pendingUpdates > 0,
    performOptimisticUpdate,
  };
}

/**
 * Create an optimistic promise that resolves immediately but tracks the
 * real promise in the background, rolling back changes if it fails.
 */
export function createOptimisticPromise<T>(
  optimisticResult: T,
  actualPromise: Promise<T>,
  onRollback?: (error: Error) => void,
): Promise<T> {
  // Return a promise that resolves immediately with the optimistic result
  return new Promise((resolve) => {
    // Immediately resolve with the optimistic result
    resolve(optimisticResult);
    
    // Handle the actual promise in the background
    actualPromise.catch((error) => {
      // Call rollback handler if the actual promise fails
      if (onRollback) {
        onRollback(error);
      }
    });
  });
}

/**
 * Helper function to create mutations that use optimistic updates
 */
export function createOptimisticMutation<T, P>(
  mutationFn: (params: P) => Promise<T>,
  getOptimisticResult: (params: P) => T,
  onError?: (error: Error) => void,
) {
  return (params: P): Promise<T> => {
    // Call the actual API mutation
    const promise = mutationFn(params);
    
    // Return an optimistic promise
    return createOptimisticPromise(
      getOptimisticResult(params),
      promise,
      onError,
    );
  };
} 