import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { initializeApp } from 'firebase/app';
import { firebaseConfig } from '@/firebase.config';

// Check if we're running in a React Native environment
const isReactNative = Platform.OS !== 'web';

/**
 * Patch Firebase to work with React Native persistence
 * This is a hack but it works around the Firebase Auth initialization issues
 */
export function patchFirebaseForReactNative() {
  try {
    if (!isReactNative) return;
    
    console.log('Patching Firebase for React Native persistence');
    
    // Create a mock implementation of the getReactNativePersistence function
    const mockReactNativePersistence = {
      type: 'LOCAL',
      storage: AsyncStorage,
      _storage: AsyncStorage,
      _shouldAllowMigration: true,
      async _get(key: string) {
        return AsyncStorage.getItem(key);
      },
      async _set(key: string, value: string) {
        return AsyncStorage.setItem(key, value);
      },
      async _remove(key: string) {
        return AsyncStorage.removeItem(key);
      }
    };
    
    // Monkey patch the global Firebase Auth implementation
    // @ts-ignore - Intentionally accessing internals
    if (global.firebase && global.firebase._delegate && global.firebase._delegate.auth) {
      // @ts-ignore - Intentionally accessing internals
      const originalInitializeAuth = global.firebase._delegate.auth.initializeAuth;
      
      // @ts-ignore - Intentionally accessing internals
      global.firebase._delegate.auth.initializeAuth = (app: any, options: any) => {
        // Use our mock persistence if none is provided
        if (!options || !options.persistence) {
          options = {
            ...options,
            persistence: mockReactNativePersistence
          };
        }
        return originalInitializeAuth(app, options);
      };
      
      console.log('Successfully patched Firebase Auth for React Native');
    } else {
      console.log('Firebase Auth global not found, unable to patch');
    }
  } catch (error) {
    console.error('Error patching Firebase:', error);
  }
}

/**
 * Ensure Firebase is initialized early
 */
export function ensureFirebaseInitialized() {
  try {
    // First try to get existing Firebase app
    const existingApp = global.firebase?.apps?.[0] || null;
    
    if (!existingApp) {
      // Initialize Firebase if it hasn't been already
      const app = initializeApp(firebaseConfig);
      console.log('Firebase initialized early in lifecycle');
      return app;
    }
    
    return existingApp;
  } catch (error) {
    console.error('Error ensuring Firebase is initialized:', error);
    return null;
  }
} 