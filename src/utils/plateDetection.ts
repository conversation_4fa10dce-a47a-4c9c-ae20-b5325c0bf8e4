/**
 * Plate Detection Utility
 * 
 * Specialized utility for detecting and measuring plates, bowls, and other tableware
 * to use as reference objects for more accurate food measurements.
 */

import { ReferenceObjectType, DetectedReferenceObject } from './referenceObjectDetection';

/**
 * Types of tableware that can be used as reference objects
 */
export enum TablewareType {
  DINNER_PLATE = 'dinnerPlate',
  SALAD_PLATE = 'saladPlate',
  BREAD_PLATE = 'breadPlate',
  CEREAL_BOWL = 'cerealBowl',
  SOUP_BOWL = 'soupBowl',
  SHALLOW_BOWL = 'shallowBowl',
  PLATTER = 'platter',
  STANDARD_MUG = 'standardMug',
  JUICE_GLASS = 'juiceGlass',
  WATER_GLASS = 'waterGlass',
  WINE_GLASS = 'wineGlass',
  UNKNOWN = 'unknown'
}

/**
 * Standard dimensions for common tableware in millimeters
 */
export interface TablewareDimensions {
  diameter?: number;  // Diameter in mm (for circular items)
  width?: number;     // Width in mm (for non-circular items)
  length?: number;    // Length in mm (for non-circular items)
  height?: number;    // Height in mm
  depth?: number;     // Depth in mm (for bowls)
  capacity?: number;  // Capacity in ml (for mugs, glasses)
}

/**
 * Database of standard tableware dimensions
 * All measurements in millimeters (mm) or milliliters (ml)
 */
export const TABLEWARE_DIMENSIONS: Record<TablewareType, TablewareDimensions> = {
  [TablewareType.DINNER_PLATE]: {
    diameter: 254    // 10 inch
  },
  [TablewareType.SALAD_PLATE]: {
    diameter: 190.5  // 7.5 inch
  },
  [TablewareType.BREAD_PLATE]: {
    diameter: 152.4  // 6 inch
  },
  [TablewareType.CEREAL_BOWL]: {
    diameter: 152.4, // 6 inch
    depth: 50.8      // 2 inch
  },
  [TablewareType.SOUP_BOWL]: {
    diameter: 177.8, // 7 inch
    depth: 38.1      // 1.5 inch
  },
  [TablewareType.SHALLOW_BOWL]: {
    diameter: 203.2, // 8 inch
    depth: 25.4      // 1 inch
  },
  [TablewareType.PLATTER]: {
    width: 304.8,    // 12 inch
    length: 406.4    // 16 inch
  },
  [TablewareType.STANDARD_MUG]: {
    diameter: 82.6,  // 3.25 inch
    height: 95.3,    // 3.75 inch
    capacity: 350    // 12 oz
  },
  [TablewareType.JUICE_GLASS]: {
    diameter: 63.5,  // 2.5 inch
    height: 101.6,   // 4 inch
    capacity: 180    // 6 oz
  },
  [TablewareType.WATER_GLASS]: {
    diameter: 69.9,  // 2.75 inch
    height: 146.1,   // 5.75 inch
    capacity: 340    // 11.5 oz
  },
  [TablewareType.WINE_GLASS]: {
    diameter: 76.2,  // 3 inch (at widest point)
    height: 215.9,   // 8.5 inch
    capacity: 300    // 10 oz
  },
  [TablewareType.UNKNOWN]: {
    diameter: 0,
    width: 0,
    length: 0,
    height: 0,
    depth: 0,
    capacity: 0
  }
};

/**
 * Detected tableware information
 */
export interface DetectedTableware {
  type: TablewareType;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  pixelsPerMm: number; // Conversion factor from pixels to mm
  confidence: number;  // Detection confidence (0-1)
  estimatedDiameter?: number; // Estimated diameter in mm (if circular)
  estimatedWidth?: number;    // Estimated width in mm (if non-circular)
  estimatedLength?: number;   // Estimated length in mm (if non-circular)
  isFoodOn: boolean;  // Is food on this tableware?
}

/**
 * Detect tableware in an image using computer vision
 * 
 * @param imageUri URI of the image to analyze
 * @param visionAPIResponse Optional pre-processed vision API response
 * @returns Array of detected tableware
 */
export async function detectTableware(
  imageUri: string,
  visionAPIResponse?: any
): Promise<DetectedTableware[]> {
  // If we already have vision API results, use them directly
  if (visionAPIResponse) {
    return processVisionResultsForTableware(visionAPIResponse);
  }
  
  // For now, return an empty array as a placeholder
  // In a real implementation, this would call an API or use on-device ML
  return [];
}

/**
 * Process vision API results to extract tableware
 * 
 * @param visionResults Results from vision API
 * @returns Array of detected tableware
 */
function processVisionResultsForTableware(visionResults: any): DetectedTableware[] {
  const detectedItems: DetectedTableware[] = [];
  
  // Early return if no results
  if (!visionResults || !visionResults.objects) {
    return detectedItems;
  }
  
  // Define detection patterns for tableware
  const detectionPatterns = [
    {
      pattern: /dinner plate|large plate|main plate/i,
      type: TablewareType.DINNER_PLATE
    },
    {
      pattern: /salad plate|side plate|small plate/i,
      type: TablewareType.SALAD_PLATE
    },
    {
      pattern: /bread plate|appetizer plate|tiny plate/i,
      type: TablewareType.BREAD_PLATE
    },
    {
      pattern: /cereal bowl|breakfast bowl/i,
      type: TablewareType.CEREAL_BOWL
    },
    {
      pattern: /soup bowl|deep bowl/i,
      type: TablewareType.SOUP_BOWL
    },
    {
      pattern: /shallow bowl|pasta bowl/i,
      type: TablewareType.SHALLOW_BOWL
    },
    {
      pattern: /platter|tray|serving plate/i,
      type: TablewareType.PLATTER
    },
    {
      pattern: /mug|coffee mug|tea mug/i,
      type: TablewareType.STANDARD_MUG
    },
    {
      pattern: /juice glass|small glass/i,
      type: TablewareType.JUICE_GLASS
    },
    {
      pattern: /water glass|drinking glass|tumbler/i,
      type: TablewareType.WATER_GLASS
    },
    {
      pattern: /wine glass|stemware/i,
      type: TablewareType.WINE_GLASS
    }
  ];
  
  // Generic plate/bowl/glass detection patterns for when specific type isn't recognized
  const genericPatterns = [
    {
      pattern: /plate|dish/i,
      type: TablewareType.DINNER_PLATE
    },
    {
      pattern: /bowl/i,
      type: TablewareType.CEREAL_BOWL
    },
    {
      pattern: /glass|cup/i,
      type: TablewareType.WATER_GLASS
    }
  ];
  
  // Process each detected object
  for (const obj of visionResults.objects) {
    const label = obj.name || '';
    const boundingBox = obj.boundingBox || obj.boundingPoly || {};
    const confidence = obj.score || obj.confidence || 0.5;
    
    // Skip if no bounding box or low confidence
    if (!boundingBox || confidence < 0.5) {
      continue;
    }
    
    // Format the bounding box into standard format
    const standardBoundingBox = standardizeBoundingBox(boundingBox);
    
    // Check if this object matches any tableware patterns
    let matchFound = false;
    let tablewareType: TablewareType = TablewareType.UNKNOWN;
    
    // First try specific tableware detection
    for (const { pattern, type } of detectionPatterns) {
      if (pattern.test(label)) {
        tablewareType = type;
        matchFound = true;
        break;
      }
    }
    
    // If no specific match, try generic patterns
    if (!matchFound) {
      for (const { pattern, type } of genericPatterns) {
        if (pattern.test(label)) {
          tablewareType = type;
          matchFound = true;
          break;
        }
      }
    }
    
    // If still no match, check for circular shape which could be a plate or bowl
    if (!matchFound && isCircularShape(standardBoundingBox, obj)) {
      // Determine if this is likely a plate or bowl based on size and context
      tablewareType = guessTablewareTypeFromShape(standardBoundingBox, visionResults);
      matchFound = true;
    }
    
    if (matchFound) {
      // Get standard dimensions for this tableware type
      const dimensions = TABLEWARE_DIMENSIONS[tablewareType];
      
      // Calculate pixels per mm based on dimensions
      const pixelsPerMm = calculateTablewarePixelsPerMm(
        tablewareType,
        dimensions,
        standardBoundingBox
      );
      
      // Determine if food is on this tableware
      const isFoodOn = detectFoodOnTableware(obj, visionResults);
      
      // Create the detected tableware object
      detectedItems.push({
        type: tablewareType,
        boundingBox: standardBoundingBox,
        pixelsPerMm,
        confidence: confidence * 0.9, // Slightly reduce confidence for tableware detection
        estimatedDiameter: dimensions.diameter,
        estimatedWidth: dimensions.width,
        estimatedLength: dimensions.length,
        isFoodOn
      });
    }
  }
  
  return detectedItems;
}

/**
 * Standardize bounding box format from different API responses
 */
function standardizeBoundingBox(boundingBox: any): {
  x: number;
  y: number;
  width: number;
  height: number;
} {
  // Handle polygon format with vertices
  if (boundingBox.vertices) {
    const xs = boundingBox.vertices.map((v: any) => v.x);
    const ys = boundingBox.vertices.map((v: any) => v.y);
    const minX = Math.min(...xs);
    const maxX = Math.max(...xs);
    const minY = Math.min(...ys);
    const maxY = Math.max(...ys);
    
    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    };
  } 
  
  // Handle normalized vertices (values from 0-1)
  if (boundingBox.normalizedVertices) {
    const vertices = boundingBox.normalizedVertices;
    // Would need image dimensions to convert - use placeholders
    return {
      x: 0,
      y: 0,
      width: 100,
      height: 100
    };
  } 
  
  // Standard format with x, y, width, height
  return {
    x: boundingBox.x || 0,
    y: boundingBox.y || 0,
    width: boundingBox.width || 0,
    height: boundingBox.height || 0
  };
}

/**
 * Determine if an object is circular in shape
 */
function isCircularShape(boundingBox: any, obj: any): boolean {
  // If API provides shape information directly
  if (obj.shape === 'circular' || obj.shape === 'round') {
    return true;
  }
  
  // Check aspect ratio - circular objects have aspect ratio close to 1
  const aspectRatio = boundingBox.width / boundingBox.height;
  return Math.abs(aspectRatio - 1) < 0.2; // Within 20% of perfect circle
}

/**
 * Guess tableware type based on shape and context
 */
function guessTablewareTypeFromShape(
  boundingBox: any,
  visionResults: any
): TablewareType {
  const imageWidth = visionResults.imageInfo?.width || 1000; // Fallback
  const relativeSize = boundingBox.width / imageWidth;
  
  // Large circular object is likely a dinner plate
  if (relativeSize > 0.4) {
    return TablewareType.DINNER_PLATE;
  }
  
  // Medium circular object could be a salad plate
  if (relativeSize > 0.25) {
    return TablewareType.SALAD_PLATE;
  }
  
  // Smaller circular object could be a bowl or bread plate
  if (relativeSize > 0.15) {
    // Without depth information, default to bread plate
    return TablewareType.BREAD_PLATE;
  }
  
  // Very small circular object might be a glass
  return TablewareType.WATER_GLASS;
}

/**
 * Calculate pixels per mm conversion for tableware
 */
function calculateTablewarePixelsPerMm(
  type: TablewareType,
  dimensions: TablewareDimensions,
  boundingBox: { width: number; height: number }
): number {
  // For circular items, use diameter
  if (dimensions.diameter) {
    // For circular tableware, average of width and height
    const avgDimension = (boundingBox.width + boundingBox.height) / 2;
    return avgDimension / dimensions.diameter;
  }
  
  // For rectangular items, use width and length
  if (dimensions.width && dimensions.length) {
    // Use the larger dimension for calculation
    // Assuming the longer side of the bounding box corresponds to the length
    if (boundingBox.width > boundingBox.height) {
      return boundingBox.width / dimensions.length;
    } else {
      return boundingBox.height / dimensions.length;
    }
  }
  
  // Default fallback
  return 1.0;
}

/**
 * Detect if food is on a piece of tableware
 */
function detectFoodOnTableware(tablewareObj: any, visionResults: any): boolean {
  // Check if there are food labels
  if (!visionResults.objects) {
    return false;
  }
  
  const tablewareBounds = tablewareObj.boundingBox || tablewareObj.boundingPoly || {};
  
  // Convert to standard format if needed
  const tablewareBox = standardizeBoundingBox(tablewareBounds);
  
  // Check each object to see if it's food and overlaps with the tableware
  for (const obj of visionResults.objects) {
    // Skip the tableware object itself
    if (obj === tablewareObj) {
      continue;
    }
    
    const label = obj.name || '';
    
    // Check if this is likely food
    if (isFoodLabel(label)) {
      const objBounds = obj.boundingBox || obj.boundingPoly || {};
      const objBox = standardizeBoundingBox(objBounds);
      
      // Check if food overlaps with tableware
      if (boxesOverlap(tablewareBox, objBox)) {
        return true;
      }
    }
  }
  
  return false;
}

/**
 * Determine if a label likely represents food
 */
function isFoodLabel(label: string): boolean {
  // List of common non-food objects to exclude
  const nonFoodPatterns = /plate|bowl|utensil|fork|knife|spoon|napkin|table|cloth|furniture|person|hand|finger/i;
  
  // If it matches a non-food pattern, it's not food
  if (nonFoodPatterns.test(label)) {
    return false;
  }
  
  // Most other objects in food photos are likely food
  return true;
}

/**
 * Check if two bounding boxes overlap
 */
function boxesOverlap(box1: any, box2: any): boolean {
  return !(
    box1.x + box1.width < box2.x ||  // box1 is to the left of box2
    box1.x > box2.x + box2.width ||  // box1 is to the right of box2
    box1.y + box1.height < box2.y || // box1 is above box2
    box1.y > box2.y + box2.height    // box1 is below box2
  );
}

/**
 * Convert detected tableware to a reference object for size calculation
 * 
 * @param tableware Detected tableware
 * @returns Reference object for size calculations
 */
export function tablewareToReferenceObject(tableware: DetectedTableware): DetectedReferenceObject {
  // Map tableware type to reference object type
  let refType: ReferenceObjectType;
  let subtype: string | undefined;
  
  switch (tableware.type) {
    case TablewareType.DINNER_PLATE:
    case TablewareType.SALAD_PLATE:
    case TablewareType.BREAD_PLATE:
      refType = ReferenceObjectType.PLATE;
      subtype = tableware.type === TablewareType.DINNER_PLATE ? 'medium' :
                tableware.type === TablewareType.SALAD_PLATE ? 'salad-plate' : 'bread-plate';
      break;
      
    case TablewareType.CEREAL_BOWL:
    case TablewareType.SOUP_BOWL:
    case TablewareType.SHALLOW_BOWL:
      refType = ReferenceObjectType.BOWL;
      subtype = tableware.type === TablewareType.CEREAL_BOWL ? 'cereal-bowl' :
                tableware.type === TablewareType.SOUP_BOWL ? 'large' : 'medium';
      break;
      
    default:
      // For other tableware, just use UNKNOWN type
      refType = ReferenceObjectType.UNKNOWN;
  }
  
  return {
    type: refType,
    subtype,
    boundingBox: tableware.boundingBox,
    pixelsPerMm: tableware.pixelsPerMm,
    confidence: tableware.confidence
  };
}

/**
 * Get a list of reference objects from detected tableware
 * 
 * @param detectedTableware Array of detected tableware
 * @returns Array of reference objects
 */
export function getTablewareReferenceObjects(
  detectedTableware: DetectedTableware[]
): DetectedReferenceObject[] {
  return detectedTableware.map(tablewareToReferenceObject);
}

/**
 * Calculate the scale of food relative to tableware
 * 
 * @param foodBoundingBox Bounding box of the food
 * @param tableware Detected tableware containing the food
 * @returns Percentage of tableware occupied by the food (0-1)
 */
export function calculateFoodToTablewareRatio(
  foodBoundingBox: { x: number; y: number; width: number; height: number },
  tableware: DetectedTableware
): number {
  // Calculate areas
  const foodArea = foodBoundingBox.width * foodBoundingBox.height;
  const tablewareArea = tableware.boundingBox.width * tableware.boundingBox.height;
  
  if (tablewareArea <= 0) {
    return 0;
  }
  
  // Only count the food area that overlaps with the tableware
  const overlapBox = getOverlapBox(foodBoundingBox, tableware.boundingBox);
  if (!overlapBox) {
    return 0;
  }
  
  const overlapArea = overlapBox.width * overlapBox.height;
  
  // Return the ratio of overlap area to tableware area
  return overlapArea / tablewareArea;
}

/**
 * Get the overlapping portion of two bounding boxes
 */
function getOverlapBox(
  box1: { x: number; y: number; width: number; height: number },
  box2: { x: number; y: number; width: number; height: number }
): { x: number; y: number; width: number; height: number } | null {
  // Calculate overlap bounds
  const xOverlap = Math.max(0, 
    Math.min(box1.x + box1.width, box2.x + box2.width) - Math.max(box1.x, box2.x)
  );
  
  const yOverlap = Math.max(0,
    Math.min(box1.y + box1.height, box2.y + box2.height) - Math.max(box1.y, box2.y)
  );
  
  // If no overlap in either dimension, return null
  if (xOverlap <= 0 || yOverlap <= 0) {
    return null;
  }
  
  return {
    x: Math.max(box1.x, box2.x),
    y: Math.max(box1.y, box2.y),
    width: xOverlap,
    height: yOverlap
  };
} 