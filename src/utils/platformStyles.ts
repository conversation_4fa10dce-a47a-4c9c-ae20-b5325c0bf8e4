import { Platform, StyleSheet, ViewStyle, TextStyle, ImageStyle } from 'react-native';

type CustomStyle = ViewStyle & TextStyle & ImageStyle;

/**
 * Normalizes React Native style properties for web platform
 * Converts kebab-case to camelCase for web compatibility
 */
export function normalizeStylesForWeb(styles: Record<string, any>): Record<string, any> {
  if (Platform.OS !== 'web') {
    return styles;
  }

  const normalizedStyles: Record<string, any> = {};
  
  for (const key in styles) {
    const value = styles[key];
    
    // Convert kebab-case to camelCase
    if (key.includes('-')) {
      const camelKey = key.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
      normalizedStyles[camelKey] = value;
    } else {
      normalizedStyles[key] = value;
    }
    
    // Handle specific property transformations
    if (key === 'transform-origin') {
      normalizedStyles.transformOrigin = value;
      delete normalizedStyles['transform-origin'];
    }
  }
  
  return normalizedStyles;
}

/**
 * Creates platform-specific stylesheets
 */
export function createPlatformStyles<T extends Record<string, CustomStyle>>(
  styles: T
): T {
  // For web, normalize the styles
  if (Platform.OS === 'web') {
    const normalized: Record<string, any> = {};
    
    for (const key in styles) {
      normalized[key] = normalizeStylesForWeb(styles[key]);
    }
    
    return StyleSheet.create(normalized) as T;
  }
  
  // For native, just create the styles normally
  return StyleSheet.create(styles);
}

/**
 * Helper to conditionally apply platform-specific styles
 */
export function platformSpecific<T>(
  base: T,
  ios?: Partial<T>,
  android?: Partial<T>,
  web?: Partial<T>
): T {
  let platformStyles: Partial<T> = {};
  
  if (Platform.OS === 'ios' && ios) {
    platformStyles = ios;
  } else if (Platform.OS === 'android' && android) {
    platformStyles = android;
  } else if (Platform.OS === 'web' && web) {
    platformStyles = web;
  }
  
  return { ...base, ...platformStyles };
} 