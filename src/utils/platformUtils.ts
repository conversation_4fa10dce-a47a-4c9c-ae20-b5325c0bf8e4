import { Platform, StyleSheet } from 'react-native';

/**
 * Utility function to determine if native driver should be used for animations
 * Will return false for web platform and true for native platforms
 */
export function canUseNativeDriver(): boolean {
  return Platform.OS !== 'web';
}

/**
 * Safely apply transform styles for web platform
 * React Native Web has issues with transform styles, this utility helps mitigate those issues
 */
export function safeTransformForWeb(styles: any = {}) {
  if (Platform.OS !== 'web') {
    return styles;
  }

  // Create a new style object without transform property
  const { transform, ...otherStyles } = styles;
  
  // Return the styles without any transform property on web
  return otherStyles;
}

/**
 * Creates a style object with web-safe transforms
 * This prevents "Failed to set an indexed property on 'CSSStyleDeclaration'" errors
 */
export function createWebSafeStyles(baseStyles: any) {
  return StyleSheet.create({
    ...baseStyles,
    ...(Platform.OS === 'web' && {
      // Override any transform styles with empty objects for web
      ...Object.keys(baseStyles).reduce((acc, key) => {
        const style = baseStyles[key];
        if (style && typeof style === 'object' && 'transform' in style) {
          acc[key] = { ...style, transform: undefined };
        }
        return acc;
      }, {})
    })
  });
}

/**
 * Apply safe web transform styles
 * Use this for inline styles that include transforms
 */
export function withSafeWebTransform(style: any) {
  if (Platform.OS !== 'web') {
    return style;
  }
  
  if (Array.isArray(style)) {
    return style.map(s => {
      if (s && typeof s === 'object' && 'transform' in s) {
        const { transform, ...rest } = s;
        return rest;
      }
      return s;
    });
  } else if (style && typeof style === 'object' && 'transform' in style) {
    const { transform, ...rest } = style;
    return rest;
  }
  
  return style;
} 