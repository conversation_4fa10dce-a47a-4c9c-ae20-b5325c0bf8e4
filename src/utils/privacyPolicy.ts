/**
 * Privacy Policy Management Utilities
 * 
 * These utilities manage privacy settings and data retention policies
 * to ensure compliance with privacy regulations.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert, Platform } from 'react-native';
import * as FileSystem from 'expo-file-system';
import { SECURE_STORAGE_KEYS, clearAllSecureData } from '../services/secureStorage';

// Storage keys
const PRIVACY_KEYS = {
  CONSENT_STATUS: 'privacy_consent_status',
  ANALYTICS_ENABLED: 'privacy_analytics_enabled',
  RETENTION_POLICY: 'privacy_retention_policy',
  DATA_DELETION_DATE: 'privacy_data_deletion_date',
  LAST_USAGE_DATE: 'privacy_last_usage_date',
  TERMS_VERSION_ACCEPTED: 'privacy_terms_version',
};

// Latest privacy policy version number
export const CURRENT_PRIVACY_POLICY_VERSION = '1.0.0';

// Data retention periods in days
export const RETENTION_PERIODS = {
  SHORT: 30, // 30 days
  MEDIUM: 90, // 3 months
  LONG: 365, // 1 year
  INDEFINITE: -1, // No automatic deletion
};

// Privacy consent status
export type ConsentStatus = 'unknown' | 'accepted' | 'declined';

// Privacy settings interface
export interface PrivacySettings {
  consentStatus: ConsentStatus;
  analyticsEnabled: boolean;
  retentionPeriod: number;
  acceptedVersion: string;
  dataCategories: {
    personalInfo: boolean;
    foodItems: boolean;
    nutritionData: boolean;
    cameraImages: boolean;
    locationData: boolean;
  };
}

// Default privacy settings
const DEFAULT_PRIVACY_SETTINGS: PrivacySettings = {
  consentStatus: 'unknown',
  analyticsEnabled: false,
  retentionPeriod: RETENTION_PERIODS.MEDIUM,
  acceptedVersion: '',
  dataCategories: {
    personalInfo: true,
    foodItems: true,
    nutritionData: true,
    cameraImages: false,
    locationData: false,
  },
};

/**
 * Get the current privacy settings
 */
export async function getPrivacySettings(): Promise<PrivacySettings> {
  try {
    const settings = await AsyncStorage.getItem(PRIVACY_KEYS.CONSENT_STATUS);
    if (!settings) {
      return DEFAULT_PRIVACY_SETTINGS;
    }
    
    return { ...DEFAULT_PRIVACY_SETTINGS, ...JSON.parse(settings) };
  } catch (error) {
    console.error('Failed to get privacy settings:', error);
    return DEFAULT_PRIVACY_SETTINGS;
  }
}

/**
 * Save privacy settings
 */
export async function savePrivacySettings(settings: Partial<PrivacySettings>): Promise<boolean> {
  try {
    // Get current settings
    const currentSettings = await getPrivacySettings();
    
    // Merge with new settings
    const newSettings = { ...currentSettings, ...settings };
    
    // If consent status was updated to accepted, record the acceptance time
    if (settings.consentStatus === 'accepted' && currentSettings.consentStatus !== 'accepted') {
      newSettings.acceptedVersion = CURRENT_PRIVACY_POLICY_VERSION;
      
      // Update the last usage date to start tracking retention periods
      await AsyncStorage.setItem(PRIVACY_KEYS.LAST_USAGE_DATE, new Date().toISOString());
      
      // Calculate the data deletion date based on retention period
      if (newSettings.retentionPeriod > 0) {
        const deletionDate = new Date();
        deletionDate.setDate(deletionDate.getDate() + newSettings.retentionPeriod);
        await AsyncStorage.setItem(PRIVACY_KEYS.DATA_DELETION_DATE, deletionDate.toISOString());
      } else {
        // Remove deletion date if retention is indefinite
        await AsyncStorage.removeItem(PRIVACY_KEYS.DATA_DELETION_DATE);
      }
    }
    
    // Save the new settings
    await AsyncStorage.setItem(PRIVACY_KEYS.CONSENT_STATUS, JSON.stringify(newSettings));
    
    return true;
  } catch (error) {
    console.error('Failed to save privacy settings:', error);
    return false;
  }
}

/**
 * Check if privacy consent is needed (either never given or policy has been updated)
 */
export async function isConsentNeeded(): Promise<boolean> {
  const settings = await getPrivacySettings();
  
  // Need consent if never given
  if (settings.consentStatus === 'unknown') {
    return true;
  }
  
  // Need consent if policy version has changed
  if (settings.acceptedVersion !== CURRENT_PRIVACY_POLICY_VERSION &&
      settings.acceptedVersion !== '') {
    return true;
  }
  
  return false;
}

/**
 * Update the last usage date to reflect active app usage
 * This extends the data retention period
 */
export async function updateLastUsageDate(): Promise<void> {
  try {
    const settings = await getPrivacySettings();
    
    // Only update if user has given consent
    if (settings.consentStatus === 'accepted') {
      // Update last usage date
      await AsyncStorage.setItem(PRIVACY_KEYS.LAST_USAGE_DATE, new Date().toISOString());
      
      // Update deletion date if retention period is not indefinite
      if (settings.retentionPeriod > 0) {
        const deletionDate = new Date();
        deletionDate.setDate(deletionDate.getDate() + settings.retentionPeriod);
        await AsyncStorage.setItem(PRIVACY_KEYS.DATA_DELETION_DATE, deletionDate.toISOString());
      }
    }
  } catch (error) {
    console.error('Failed to update last usage date:', error);
  }
}

/**
 * Check if user data should be deleted based on retention policy
 */
export async function shouldDeleteUserData(): Promise<boolean> {
  try {
    const settings = await getPrivacySettings();
    
    // No deletion needed if retention is indefinite
    if (settings.retentionPeriod < 0) {
      return false;
    }
    
    // Get deletion date
    const deletionDateStr = await AsyncStorage.getItem(PRIVACY_KEYS.DATA_DELETION_DATE);
    if (!deletionDateStr) {
      return false;
    }
    
    const deletionDate = new Date(deletionDateStr);
    const now = new Date();
    
    // If current date is past deletion date, data should be deleted
    return now > deletionDate;
  } catch (error) {
    console.error('Failed to check data deletion status:', error);
    return false;
  }
}

/**
 * Delete all user data for privacy compliance
 */
export async function deleteAllUserData(): Promise<boolean> {
  try {
    // Clear secure storage (API keys, auth tokens, etc.)
    await clearAllSecureData();
    
    // Clear app-specific storage
    const keys = await AsyncStorage.getAllKeys();
    const appKeys = keys.filter(key => !key.startsWith('privacy_'));
    await AsyncStorage.multiRemove(appKeys);
    
    // Reset privacy settings (but keep the consent record)
    const settings = await getPrivacySettings();
    await savePrivacySettings({
      ...DEFAULT_PRIVACY_SETTINGS,
      consentStatus: settings.consentStatus,
      acceptedVersion: settings.acceptedVersion,
    });
    
    // Delete cached images
    if (Platform.OS !== 'web') {
      const cacheDir = FileSystem.cacheDirectory;
      const tempDir = FileSystem.documentDirectory;
      
      if (cacheDir) {
        await FileSystem.deleteAsync(cacheDir, { idempotent: true });
      }
      
      if (tempDir) {
        const files = await FileSystem.readDirectoryAsync(tempDir);
        for (const file of files) {
          if (file.endsWith('.jpg') || file.endsWith('.png')) {
            await FileSystem.deleteAsync(`${tempDir}${file}`, { idempotent: true });
          }
        }
      }
    }
    
    return true;
  } catch (error) {
    console.error('Failed to delete user data:', error);
    return false;
  }
}

/**
 * Show privacy policy consent dialog
 * 
 * @returns Promise that resolves to true if consent is given, false otherwise
 */
export async function showPrivacyConsentDialog(): Promise<boolean> {
  return new Promise((resolve) => {
    Alert.alert(
      'Privacy Policy',
      'Our app collects data to improve your experience. We use this data to provide nutrition analysis, ' +
      'track your meals, and improve our services. Would you like to review our full Privacy Policy?',
      [
        {
          text: 'View Policy',
          onPress: () => {
            // In a real app, this would open a modal with the full policy text
            // After reading, the user would be prompted again for consent
            
            Alert.alert(
              'Accept Privacy Policy',
              'Do you accept our Privacy Policy and consent to data collection as described?',
              [
                {
                  text: 'Decline',
                  style: 'cancel',
                  onPress: async () => {
                    await savePrivacySettings({ consentStatus: 'declined' });
                    resolve(false);
                  }
                },
                {
                  text: 'Accept',
                  onPress: async () => {
                    await savePrivacySettings({ consentStatus: 'accepted' });
                    resolve(true);
                  }
                }
              ],
              { cancelable: false }
            );
          }
        },
        {
          text: 'Decline',
          style: 'cancel',
          onPress: async () => {
            await savePrivacySettings({ consentStatus: 'declined' });
            resolve(false);
          }
        },
        {
          text: 'Accept',
          onPress: async () => {
            await savePrivacySettings({ consentStatus: 'accepted' });
            resolve(true);
          }
        }
      ],
      { cancelable: false }
    );
  });
}

/**
 * Check and enforce data retention policy
 * Should be called during app startup
 */
export async function enforceRetentionPolicy(): Promise<void> {
  try {
    // Check if data should be deleted
    const shouldDelete = await shouldDeleteUserData();
    if (shouldDelete) {
      console.log('Enforcing data retention policy - deleting old data');
      await deleteAllUserData();
      
      // Notify user that data was deleted due to inactivity
      Alert.alert(
        'Data Deletion Notice',
        'Your data has been deleted in accordance with our retention policy due to inactivity. ' +
        'You can continue using the app with a fresh start.'
      );
    } else {
      // Update last usage date to extend the retention period
      await updateLastUsageDate();
    }
  } catch (error) {
    console.error('Failed to enforce retention policy:', error);
  }
} 