/**
 * Utility for rate limiting API calls to avoid Firebase quota limits
 */

// Simple token bucket implementation for rate limiting
export class RateLimiter {
  private tokens: number;
  private lastRefill: number;
  private maxTokens: number;
  private refillRate: number; // tokens per second
  private enabled: boolean;

  /**
   * Create a new rate limiter
   * @param maxTokens Maximum number of tokens in the bucket
   * @param refillRate Number of tokens to add per second
   * @param enabled Whether rate limiting is enabled
   */
  constructor(maxTokens: number = 100, refillRate: number = 20, enabled: boolean = true) {
    this.tokens = maxTokens;
    this.lastRefill = Date.now();
    this.maxTokens = maxTokens;
    this.refillRate = refillRate;
    this.enabled = enabled;
  }

  /**
   * Check if a request can be made and consume a token if possible
   * @param cost Number of tokens to consume (default: 1)
   * @returns True if the request can proceed, false if it should be throttled
   */
  public canMakeRequest(cost: number = 1): boolean {
    // If disabled, always allow requests
    if (!this.enabled) {
      return true;
    }

    // Refill tokens based on time elapsed
    this.refillTokens();

    // Check if we have enough tokens
    if (this.tokens >= cost) {
      this.tokens -= cost;
      return true;
    }

    return false;
  }

  /**
   * Wait until a request can be made
   * @param cost Number of tokens to consume (default: 1)
   * @returns A promise that resolves when the request can proceed
   */
  public async waitForAvailability(cost: number = 1): Promise<void> {
    // If disabled or tokens available, resolve immediately
    if (!this.enabled || this.canMakeRequest(cost)) {
      return;
    }

    // Calculate how long to wait
    const tokensNeeded = cost - this.tokens;
    const waitTime = (tokensNeeded / this.refillRate) * 1000;

    // Wait for tokens to refill
    await new Promise(resolve => setTimeout(resolve, waitTime));

    // Refill tokens after waiting
    this.refillTokens();
    this.tokens -= cost;
  }

  /**
   * Refill tokens based on time elapsed since last refill
   * @private
   */
  private refillTokens(): void {
    const now = Date.now();
    const elapsedSeconds = (now - this.lastRefill) / 1000;
    
    // Add tokens based on time elapsed
    const newTokens = elapsedSeconds * this.refillRate;
    this.tokens = Math.min(this.maxTokens, this.tokens + newTokens);
    
    // Update last refill time
    this.lastRefill = now;
  }

  /**
   * Enable or disable rate limiting
   * @param enabled Whether rate limiting should be enabled
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * Reset the rate limiter to initial state
   */
  public reset(): void {
    this.tokens = this.maxTokens;
    this.lastRefill = Date.now();
  }
}

// Create singleton instances for different Firebase services
export const firestoreReadLimiter = new RateLimiter(500, 50); // 500 reads with 50/second refill
export const firestoreWriteLimiter = new RateLimiter(250, 20); // 250 writes with 20/second refill
export const storageLimiter = new RateLimiter(100, 10); // 100 storage operations with 10/second refill
export const authLimiter = new RateLimiter(20, 1); // 20 auth operations with 1/second refill

/**
 * Wrap an async function with rate limiting
 * @param fn Function to wrap
 * @param limiter Rate limiter to use
 * @param cost Token cost of the operation
 * @returns Rate-limited function
 */
export function withRateLimit<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  limiter: RateLimiter,
  cost: number = 1
): (...args: Parameters<T>) => Promise<ReturnType<T>> {
  return async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    await limiter.waitForAvailability(cost);
    return fn(...args);
  };
}

/**
 * Create a throttled version of a function that limits how often it can be called
 * @param fn Function to throttle
 * @param limit Maximum calls per interval
 * @param interval Interval in milliseconds
 * @returns Throttled function
 */
export function throttle<T extends (...args: any[]) => any>(
  fn: T,
  limit: number = 5,
  interval: number = 1000
): (...args: Parameters<T>) => ReturnType<T> | undefined {
  let inThrottle = false;
  let lastRun = 0;
  let count = 0;
  let timeout: NodeJS.Timeout | null = null;

  return function(...args: Parameters<T>): ReturnType<T> | undefined {
    const now = Date.now();

    // Reset count after interval
    if (now - lastRun >= interval) {
      count = 0;
      lastRun = now;
      inThrottle = false;
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
    }

    // Check if over limit
    if (count >= limit) {
      inThrottle = true;
    }

    // If not throttled, execute function
    if (!inThrottle) {
      count++;
      
      // Set up reset timeout if not already set
      if (!timeout) {
        timeout = setTimeout(() => {
          count = 0;
          inThrottle = false;
          lastRun = Date.now();
          timeout = null;
        }, interval);
      }

      return fn(...args);
    }
  };
}

export default {
  RateLimiter,
  firestoreReadLimiter,
  firestoreWriteLimiter,
  storageLimiter,
  authLimiter,
  withRateLimit,
  throttle
}; 