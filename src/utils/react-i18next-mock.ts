import React from 'react';
import { useTranslation as useAppTranslation } from '../contexts/TranslationContext';

// Mock implementation of useTranslation hook that wraps the app's existing useTranslation
export function useTranslation(namespace?: string | string[]) {
  const { t, locale, isRTL } = useAppTranslation();
  
  return {
    t,
    i18n: {
      language: locale,
      isRTL
    },
    ready: true
  };
}

// Mock implementation of withTranslation HOC
export function withTranslation(namespace?: string | string[]) {
  return function(Component: React.ComponentType<any>) {
    return function WrappedComponent(props: any) {
      const { t, locale, isRTL } = useAppTranslation();
      
      return React.createElement(Component, {
        ...props,
        t,
        i18n: {
          language: locale,
          isRTL
        }
      });
    };
  };
}

// Mock implementation of Trans component
export function Trans(props: any) {
  const { t } = useAppTranslation();
  const { i18nKey, ...restProps } = props;
  
  if (i18nKey) {
    return React.createElement(
      React.Fragment,
      null,
      t(i18nKey)
    );
  }
  
  return React.createElement(
    React.Fragment,
    null,
    props.children || ''
  );
} 