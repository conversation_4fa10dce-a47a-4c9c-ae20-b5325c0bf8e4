/**
 * Reference Object Detection Utility
 * 
 * Handles the detection, measurement, and use of reference objects
 * for improved size estimation of food items
 */

/**
 * Types of common reference objects with known dimensions
 */
export enum ReferenceObjectType {
  CREDIT_CARD = 'creditCard',
  DEBIT_CARD = 'debitCard',
  ID_CARD = 'idCard',
  BUSINESS_CARD = 'businessCard',
  PLAYING_CARD = 'playingCard',
  SMARTPHONE = 'smartphone',
  COIN = 'coin',
  PLATE = 'plate',
  BOWL = 'bowl',
  UTENSIL = 'utensil',
  BOTTLE_CAP = 'bottleCap',
  USB_DRIVE = 'usbDrive',
  UNKNOWN = 'unknown'
}

/**
 * Standard dimensions for reference objects in millimeters
 */
export interface ReferenceDimensions {
  width?: number;  // mm (required for rectangular objects)
  height?: number; // mm (if applicable)
  diameter?: number; // mm (for circular objects)
  depth?: number; // mm (if 3D)
}

/**
 * Database of known reference object dimensions
 * Dimensions in millimeters (mm)
 */
export const REFERENCE_OBJECT_DIMENSIONS: Record<ReferenceObjectType, ReferenceDimensions | Record<string, ReferenceDimensions>> = {
  [ReferenceObjectType.CREDIT_CARD]: {
    width: 85.6,
    height: 53.98
  },
  [ReferenceObjectType.DEBIT_CARD]: {
    width: 85.6,
    height: 53.98
  },
  [ReferenceObjectType.ID_CARD]: {
    width: 85.6,
    height: 53.98
  },
  [ReferenceObjectType.BUSINESS_CARD]: {
    'standard': { width: 88.9, height: 50.8 },
    'european': { width: 85, height: 55 },
    'japanese': { width: 91, height: 55 }
  },
  [ReferenceObjectType.PLAYING_CARD]: {
    'standard': { width: 63.5, height: 88.9 },
    'bridge': { width: 56, height: 88.9 },
    'poker': { width: 63.5, height: 88.9 }
  },
  [ReferenceObjectType.SMARTPHONE]: {
    // These are averages, as phone sizes vary significantly
    'small': { width: 65, height: 135 },
    'medium': { width: 70, height: 145 },
    'large': { width: 75, height: 155 }
  },
  [ReferenceObjectType.COIN]: {
    // US coins
    'penny': { diameter: 19.05 },
    'nickel': { diameter: 21.21 },
    'dime': { diameter: 17.91 },
    'quarter': { diameter: 24.26 },
    'half-dollar': { diameter: 30.61 },
    'dollar': { diameter: 26.49 },
    // Euro coins
    'euro-1-cent': { diameter: 16.25 },
    'euro-2-cent': { diameter: 18.75 },
    'euro-5-cent': { diameter: 21.25 },
    'euro-10-cent': { diameter: 19.75 },
    'euro-20-cent': { diameter: 22.25 },
    'euro-50-cent': { diameter: 24.25 },
    'euro-1': { diameter: 23.25 },
    'euro-2': { diameter: 25.75 },
    // UK coins
    'pound-1-penny': { diameter: 20.3 },
    'pound-2-penny': { diameter: 25.9 },
    'pound-5-penny': { diameter: 18 },
    'pound-10-penny': { diameter: 24.5 },
    'pound-20-penny': { diameter: 21.4 },
    'pound-50-penny': { diameter: 27.3 },
    'pound-1': { diameter: 23.43 },
    'pound-2': { diameter: 28.4 }
  },
  [ReferenceObjectType.PLATE]: {
    'small': { diameter: 203.2 },  // 8 inch
    'medium': { diameter: 254 },   // 10 inch
    'large': { diameter: 304.8 },  // 12 inch
    'bread-plate': { diameter: 152.4 }, // 6 inch
    'salad-plate': { diameter: 190.5 }  // 7.5 inch
  },
  [ReferenceObjectType.BOWL]: {
    'small': { diameter: 127 },    // 5 inch
    'medium': { diameter: 152.4 }, // 6 inch
    'large': { diameter: 177.8 },  // 7 inch
    'cereal-bowl': { diameter: 152.4 } // 6 inch
  },
  [ReferenceObjectType.UTENSIL]: {
    'fork': { width: 20, height: 180 },
    'knife': { width: 20, height: 215 },
    'spoon': { width: 40, height: 170 },
    'teaspoon': { width: 30, height: 140 }
  },
  [ReferenceObjectType.BOTTLE_CAP]: {
    'soda': { diameter: 28 },
    'water': { diameter: 30 },
    'beer': { diameter: 26 }
  },
  [ReferenceObjectType.USB_DRIVE]: {
    width: 58,
    height: 18,
    depth: 9
  },
  [ReferenceObjectType.UNKNOWN]: {
    width: 0,
    height: 0
  }
};

/**
 * Detected reference object information
 */
export interface DetectedReferenceObject {
  type: ReferenceObjectType;
  subtype?: string;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  pixelsPerMm: number; // Conversion factor from pixels to mm
  confidence: number;  // Detection confidence (0-1)
}

/**
 * Detect reference objects in an image using computer vision
 * 
 * @param imageUri URI of the image to analyze
 * @param visionAPIResponse Optional pre-processed vision API response
 * @returns Array of detected reference objects
 */
export async function detectReferenceObjects(
  imageUri: string,
  visionAPIResponse?: any
): Promise<DetectedReferenceObject[]> {
  // If we already have vision API results, use them directly
  if (visionAPIResponse) {
    return processVisionResults(visionAPIResponse);
  }
  
  // Otherwise we would call the Vision API here
  // For now, we'll return an empty array as a placeholder
  // In a real implementation, this would call an API or use on-device ML
  
  return [];
}

/**
 * Process vision API results to extract reference objects
 * 
 * @param visionResults Results from vision API
 * @returns Array of detected reference objects
 */
function processVisionResults(visionResults: any): DetectedReferenceObject[] {
  const detectedObjects: DetectedReferenceObject[] = [];
  
  // Early return if no results
  if (!visionResults || !visionResults.objects) {
    return detectedObjects;
  }
  
  // Define detection patterns for reference objects
  const detectionPatterns = [
    {
      pattern: /card|credit card|debit card|id card|payment card/i,
      type: ReferenceObjectType.CREDIT_CARD
    },
    {
      pattern: /business card/i,
      type: ReferenceObjectType.BUSINESS_CARD,
      subtype: 'standard'
    },
    {
      pattern: /playing card|poker card|game card/i,
      type: ReferenceObjectType.PLAYING_CARD,
      subtype: 'poker'
    },
    {
      pattern: /phone|smartphone|mobile phone|iphone|android/i,
      type: ReferenceObjectType.SMARTPHONE,
      subtype: 'medium'
    },
    {
      pattern: /coin|penny|nickel|dime|quarter|cent|euro|pound/i,
      type: ReferenceObjectType.COIN,
      processSubtype: (label: string) => {
        // Try to identify specific coin type
        if (/penny|1 cent/i.test(label)) return 'penny';
        if (/nickel|5 cent/i.test(label)) return 'nickel';
        if (/dime|10 cent/i.test(label)) return 'dime';
        if (/quarter|25 cent/i.test(label)) return 'quarter';
        return 'quarter'; // Default to quarter if unspecified
      }
    },
    {
      pattern: /plate|dinner plate/i,
      type: ReferenceObjectType.PLATE,
      subtype: 'medium'
    },
    {
      pattern: /bowl|cereal bowl|soup bowl/i,
      type: ReferenceObjectType.BOWL,
      subtype: 'medium'
    },
    {
      pattern: /fork|knife|spoon|utensil/i,
      type: ReferenceObjectType.UTENSIL,
      processSubtype: (label: string) => {
        if (/fork/i.test(label)) return 'fork';
        if (/knife/i.test(label)) return 'knife';
        if (/spoon/i.test(label)) return 'spoon';
        if (/teaspoon/i.test(label)) return 'teaspoon';
        return 'fork'; // Default to fork
      }
    },
    {
      pattern: /bottle cap|cap/i,
      type: ReferenceObjectType.BOTTLE_CAP,
      subtype: 'soda'
    },
    {
      pattern: /usb|flash drive|thumb drive/i,
      type: ReferenceObjectType.USB_DRIVE
    }
  ];
  
  // Process each detected object
  for (const obj of visionResults.objects) {
    const label = obj.name || '';
    const boundingBox = obj.boundingBox || obj.boundingPoly || {};
    const confidence = obj.score || obj.confidence || 0.5;
    
    // Skip if no bounding box or low confidence
    if (!boundingBox || confidence < 0.5) {
      continue;
    }
    
    // Find matching reference object type
    let matchFound = false;
    
    for (const { pattern, type, subtype, processSubtype } of detectionPatterns) {
      if (pattern.test(label)) {
        // Format the bounding box into standard format
        let standardBoundingBox = {
          x: 0,
          y: 0,
          width: 0,
          height: 0
        };
        
        // Handle different bounding box formats
        if (boundingBox.vertices) {
          // Polygon format with vertices
          const xs = boundingBox.vertices.map((v: any) => v.x);
          const ys = boundingBox.vertices.map((v: any) => v.y);
          const minX = Math.min(...xs);
          const maxX = Math.max(...xs);
          const minY = Math.min(...ys);
          const maxY = Math.max(...ys);
          
          standardBoundingBox = {
            x: minX,
            y: minY,
            width: maxX - minX,
            height: maxY - minY
          };
        } else if (boundingBox.normalizedVertices) {
          // Normalized vertices (values from 0-1)
          // Would need image dimensions to convert to pixels
          // For now, just skip these
          continue;
        } else {
          // Standard format with x, y, width, height
          standardBoundingBox = {
            x: boundingBox.x || 0,
            y: boundingBox.y || 0,
            width: boundingBox.width || 0,
            height: boundingBox.height || 0
          };
        }
        
        // Determine subtype if needed
        let finalSubtype = subtype;
        if (processSubtype) {
          finalSubtype = processSubtype(label);
        }
        
        // Calculate pixels per mm based on reference dimensions
        let pixelsPerMm = calculatePixelsPerMm(
          type, 
          finalSubtype, 
          standardBoundingBox.width, 
          standardBoundingBox.height
        );
        
        detectedObjects.push({
          type,
          subtype: finalSubtype,
          boundingBox: standardBoundingBox,
          pixelsPerMm,
          confidence
        });
        
        matchFound = true;
        break;
      }
    }
    
    // If no match, check for plate or bowl by shape analysis
    if (!matchFound && boundingBox && 
        obj.shape === 'circular' || 
        obj.shape === 'round' || 
        (boundingBox.width && boundingBox.height && 
         Math.abs(boundingBox.width - boundingBox.height) < 0.1 * Math.max(boundingBox.width, boundingBox.height))) {
      
      // It's likely circular - could be a plate or bowl
      // Check size relative to the image to guess
      const imageWidth = visionResults.imageInfo?.width || 1000; // Fallback
      const relativeSize = boundingBox.width / imageWidth;
      
      // Assume plate if it's a large circular object
      if (relativeSize > 0.3) {
        const pixelsPerMm = calculatePixelsPerMm(
          ReferenceObjectType.PLATE, 
          'medium', 
          boundingBox.width, 
          boundingBox.height
        );
        
        detectedObjects.push({
          type: ReferenceObjectType.PLATE,
          subtype: 'medium',
          boundingBox,
          pixelsPerMm,
          confidence: 0.7 // Lower confidence for shape-based detection
        });
      }
    }
  }
  
  return detectedObjects;
}

/**
 * Calculate the pixels per millimeter conversion based on reference object dimensions
 * 
 * @param type Reference object type
 * @param subtype Reference object subtype (if applicable)
 * @param pixelWidth Width in pixels
 * @param pixelHeight Height in pixels
 * @returns Pixels per millimeter conversion factor
 */
function calculatePixelsPerMm(
  type: ReferenceObjectType,
  subtype: string | undefined,
  pixelWidth: number,
  pixelHeight: number
): number {
  // Get the reference dimensions
  let refDimensions: ReferenceDimensions;
  
  const typeDimensions = REFERENCE_OBJECT_DIMENSIONS[type];
  
  if (subtype && typeof typeDimensions !== 'object') {
    // If subtype is specified but dimensions don't have subtypes
    refDimensions = typeDimensions as ReferenceDimensions;
  } else if (subtype && typeof typeDimensions === 'object') {
    // If we have a subtype and dimensions have subtypes
    const subtypeDimensions = (typeDimensions as Record<string, ReferenceDimensions>)[subtype];
    refDimensions = subtypeDimensions || (typeDimensions as Record<string, ReferenceDimensions>)['standard'] || { width: 0 };
  } else {
    // No subtype, use the main dimensions
    refDimensions = typeDimensions as ReferenceDimensions;
  }
  
  // Calculate the conversion factor
  if (refDimensions.diameter) {
    // For circular objects, use diameter
    return pixelWidth / refDimensions.diameter; // or average of width and height
  } else if (refDimensions.width && refDimensions.height) {
    // For rectangular objects, use the average of width and height ratios
    const widthRatio = pixelWidth / refDimensions.width;
    const heightRatio = pixelHeight / refDimensions.height;
    return (widthRatio + heightRatio) / 2;
  } else if (refDimensions.width) {
    // If only width is available
    return pixelWidth / refDimensions.width;
  }
  
  // Default fallback (should not normally reach here)
  return 1.0;
}

/**
 * Calculate real-world measurements using detected reference objects
 * 
 * @param pixelWidth Width in pixels
 * @param pixelHeight Height in pixels
 * @param detectedReferences Array of detected reference objects
 * @returns Dimensions in millimeters and confidence level
 */
export function calculateRealDimensions(
  pixelWidth: number,
  pixelHeight: number,
  detectedReferences: DetectedReferenceObject[]
): { widthMm: number; heightMm: number; confidence: number } {
  // If no reference objects, return zeros with zero confidence
  if (!detectedReferences.length) {
    return { widthMm: 0, heightMm: 0, confidence: 0 };
  }
  
  // Sort references by confidence
  const sortedReferences = [...detectedReferences].sort((a, b) => b.confidence - a.confidence);
  
  // Use the highest confidence reference
  const bestReference = sortedReferences[0];
  
  // Calculate dimensions using the reference's pixels per mm conversion
  const widthMm = pixelWidth / bestReference.pixelsPerMm;
  const heightMm = pixelHeight / bestReference.pixelsPerMm;
  
  return {
    widthMm,
    heightMm,
    confidence: bestReference.confidence
  };
}

/**
 * Calculate real-world area using detected reference objects
 * 
 * @param pixelArea Area in pixels
 * @param detectedReferences Array of detected reference objects
 * @returns Area in square millimeters and confidence level
 */
export function calculateRealArea(
  pixelArea: number,
  detectedReferences: DetectedReferenceObject[]
): { areaMm2: number; confidence: number } {
  // If no reference objects, return zero with zero confidence
  if (!detectedReferences.length) {
    return { areaMm2: 0, confidence: 0 };
  }
  
  // Sort references by confidence
  const sortedReferences = [...detectedReferences].sort((a, b) => b.confidence - a.confidence);
  
  // Use the highest confidence reference
  const bestReference = sortedReferences[0];
  
  // Calculate area using the reference's pixels per mm conversion
  // Square the conversion factor because we're converting from square pixels to square mm
  const areaMm2 = pixelArea / (bestReference.pixelsPerMm * bestReference.pixelsPerMm);
  
  return {
    areaMm2,
    confidence: bestReference.confidence
  };
}

/**
 * Calculate real-world volume from 2D image and reference objects
 * 
 * @param pixelArea Base area in pixels
 * @param estimatedHeightPixels Estimated height in pixels
 * @param detectedReferences Array of detected reference objects
 * @returns Volume in cubic millimeters and confidence level
 */
export function calculateRealVolume(
  pixelArea: number,
  estimatedHeightPixels: number,
  detectedReferences: DetectedReferenceObject[]
): { volumeMm3: number; confidence: number } {
  // Get real area first
  const { areaMm2, confidence: areaConfidence } = calculateRealArea(pixelArea, detectedReferences);
  
  // If we couldn't calculate area, return zero
  if (areaMm2 <= 0) {
    return { volumeMm3: 0, confidence: 0 };
  }
  
  // Sort references by confidence
  const sortedReferences = [...detectedReferences].sort((a, b) => b.confidence - a.confidence);
  
  // Use the highest confidence reference
  const bestReference = sortedReferences[0];
  
  // Calculate height in mm
  const heightMm = estimatedHeightPixels / bestReference.pixelsPerMm;
  
  // Calculate volume
  const volumeMm3 = areaMm2 * heightMm;
  
  // Volume estimation is less accurate than area, so reduce confidence
  const volumeConfidence = areaConfidence * 0.8;
  
  return {
    volumeMm3,
    confidence: volumeConfidence
  };
} 