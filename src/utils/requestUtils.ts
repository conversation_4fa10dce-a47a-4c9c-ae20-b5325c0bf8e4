import { Platform, Alert } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Global connection state tracking
let isNetworkConnected = true;
let isServerReachable = true;
let hasShownOfflineAlert = false;
let lastConnectionCheck = 0;

// Timeout settings (can be adjusted based on app requirements)
const DEFAULT_TIMEOUT = 8000; // 8 seconds
const MIN_CONNECTION_CHECK_INTERVAL = 5000; // Don't check more often than every 5 seconds

// Storage keys
const OFFLINE_MODE_KEY = 'offlineMode';
const CONNECTION_STATE_KEY = 'connectionState';

// Initialize connection monitoring
export function initConnectionMonitoring() {
  // Subscribe to network info changes
  const unsubscribe = NetInfo.addEventListener(state => {
    const wasConnected = isNetworkConnected;
    isNetworkConnected = state.isConnected ?? false;
    
    // If connection state changed
    if (wasConnected !== isNetworkConnected) {
      // Save state for app restarts
      AsyncStorage.setItem(CONNECTION_STATE_KEY, JSON.stringify({
        isNetworkConnected,
        isServerReachable,
        lastChecked: new Date().toISOString()
      }));
      
      // Log transition
      if (isNetworkConnected) {
        console.log('Device reconnected to network');
      } else {
        console.log('Device disconnected from network');
        isServerReachable = false;
      }
    }
  });

  // Load previous connection state
  AsyncStorage.getItem(CONNECTION_STATE_KEY).then(data => {
    if (data) {
      try {
        const savedState = JSON.parse(data);
        isServerReachable = savedState.isServerReachable;
      } catch (e) {
        console.error('Error parsing saved connection state:', e);
      }
    }
  });
  
  return unsubscribe;
}

// Check current network connectivity
export async function checkNetworkStatus(): Promise<boolean> {
  try {
    const now = Date.now();
    
    // Don't check too frequently to avoid excessive API calls
    if (now - lastConnectionCheck < MIN_CONNECTION_CHECK_INTERVAL) {
      return isNetworkConnected && isServerReachable;
    }
    
    lastConnectionCheck = now;
    
    // Get current network state
    const state = await NetInfo.fetch();
    isNetworkConnected = state.isConnected ?? false;
    
    return isNetworkConnected;
  } catch (error) {
    console.error('Error checking network status:', error);
    return false;
  }
}

// Check if we have a server connection
export async function checkServerReachable(): Promise<boolean> {
  if (!isNetworkConnected) return false;
  
  try {
    // Ping an endpoint that should respond quickly
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000);
    
    const response = await fetch('https://www.google.com', { 
      method: 'HEAD',
      signal: controller.signal as any
    });
    
    clearTimeout(timeoutId);
    isServerReachable = response.ok;
    return isServerReachable;
  } catch (error) {
    console.error('Server unreachable:', error);
    isServerReachable = false;
    return false;
  }
}

// Show offline mode alert (if not already shown)
export function showOfflineModeAlert() {
  if (!hasShownOfflineAlert && Platform.OS !== 'web') {
    hasShownOfflineAlert = true;
    
    Alert.alert(
      'Offline Mode',
      'Unable to connect to the server. The app will continue to work with limited functionality.',
      [{ text: 'OK' }]
    );
  }
}

// Helper for entering offline mode gracefully
export async function enterOfflineMode() {
  if (!hasShownOfflineAlert) {
    showOfflineModeAlert();
    await AsyncStorage.setItem(OFFLINE_MODE_KEY, 'true');
  }
}

// Execute a function with proper timeout handling
export async function safeRequest<T>(
  requestFn: () => Promise<T>,
  options: {
    timeout?: number;
    fallbackData?: T;
    fallbackFn?: () => Promise<T>;
    errorMessage?: string;
    retryCount?: number;
    useCache?: boolean;
    cacheKey?: string;
    cacheTTL?: number; // in milliseconds
  } = {}
): Promise<{ 
  data: T | null; 
  error: Error | null; 
  timedOut: boolean;
  fromCache: boolean;
}> {
  // Default options
  const {
    timeout = DEFAULT_TIMEOUT,
    fallbackData = null,
    fallbackFn,
    errorMessage = 'Request failed',
    retryCount = 0,
    useCache = false,
    cacheKey,
    cacheTTL = 1000 * 60 * 60 // 1 hour default
  } = options;
  
  // Try to get cached data if applicable
  let fromCache = false;
  if (useCache && cacheKey) {
    try {
      const cachedData = await AsyncStorage.getItem(cacheKey);
      const cacheTimestamp = await AsyncStorage.getItem(`${cacheKey}_timestamp`);
      
      if (cachedData && cacheTimestamp) {
        const timestamp = parseInt(cacheTimestamp, 10);
        // Use cache if it's within TTL
        if (Date.now() - timestamp < cacheTTL) {
          fromCache = true;
          return { 
            data: JSON.parse(cachedData) as T, 
            error: null, 
            timedOut: false,
            fromCache: true
          };
        }
      }
    } catch (error) {
      console.error('Error reading from cache:', error);
    }
  }
  
  // Check network status first
  const isConnected = await checkNetworkStatus();
  if (!isConnected) {
    console.log('Network disconnected, using fallback data');
    
    // Try fallback function if available
    if (fallbackFn) {
      try {
        const fallbackResult = await fallbackFn();
        return { data: fallbackResult, error: null, timedOut: false, fromCache };
      } catch (fallbackError) {
        console.error('Error in fallback function:', fallbackError);
      }
    }
    
    // No network, enter offline mode
    await enterOfflineMode();
    
    return { 
      data: fallbackData, 
      error: new Error('Network unavailable'), 
      timedOut: false,
      fromCache 
    };
  }
  
  try {
    // Use AbortController for cleaner timeout handling
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    // Wrap the request function to pass the AbortController's signal
    const wrappedRequestFn = async () => {
      try {
        // Check if requestFn accepts signal parameter
        const requestResult = await requestFn();
        return requestResult;
      } catch (error) {
        // Propagate AbortError as a timeout
        if (error instanceof DOMException && error.name === 'AbortError') {
          throw new Error('Request timed out');
        }
        throw error;
      }
    };
    
    // Execute the request
    const result = await wrappedRequestFn();
    
    // Clear the timeout since the request completed
    clearTimeout(timeoutId);
    
    // If successful, cache the result if caching is enabled
    if (useCache && cacheKey && result) {
      try {
        await AsyncStorage.setItem(cacheKey, JSON.stringify(result));
        await AsyncStorage.setItem(`${cacheKey}_timestamp`, Date.now().toString());
      } catch (cacheError) {
        console.error('Error caching data:', cacheError);
      }
    }
    
    return { data: result, error: null, timedOut: false, fromCache };
  } catch (error) {
    const isTimeout = error instanceof Error && error.message === 'Request timed out';
    
    console.error(
      isTimeout ? 'Request timed out' : errorMessage,
      error
    );
    
    // If we have retries left, try again with reduced retry count
    if (retryCount > 0) {
      console.log(`Retrying request (${retryCount} attempts left)...`);
      return safeRequest(requestFn, {
        ...options,
        retryCount: retryCount - 1
      });
    }
    
    // If it's a timeout, enter offline mode
    if (isTimeout) {
      await enterOfflineMode();
    }
    
    // Try fallback function if available
    if (fallbackFn) {
      try {
        const fallbackResult = await fallbackFn();
        return { 
          data: fallbackResult, 
          error: error instanceof Error ? error : new Error(String(error)), 
          timedOut: isTimeout,
          fromCache
        };
      } catch (fallbackError) {
        console.error('Error in fallback function:', fallbackError);
      }
    }
    
    return { 
      data: fallbackData, 
      error: error instanceof Error ? error : new Error(String(error)), 
      timedOut: isTimeout,
      fromCache
    };
  }
}

// Initialize connection monitoring on import
initConnectionMonitoring(); 