// Minimal Node.js stream module implementation for React Native

// Simple Duplex class implementation
class Duplex {
  constructor(options) {
    this.readable = true;
    this.writable = true;
    this.options = options || {};
  }

  // Required methods
  pipe() { return this; }
  on() { return this; }
  once() { return this; }
  emit() { return false; }
  end() {}
  destroy() {}
  write() { return true; }
  read() { return null; }
  pause() { return this; }
  resume() { return this; }
}

// Add other stream types inheriting from Duplex
class Readable extends Duplex {
  constructor(options) {
    super(options);
    this.writable = false;
  }
}

class Writable extends Duplex {
  constructor(options) {
    super(options);
    this.readable = false;
  }
}

class Transform extends Duplex {
  constructor(options) {
    super(options);
  }
}

class PassThrough extends Transform {
  constructor(options) {
    super(options);
  }
}

// Export the class definitions
const Stream = Duplex;
Stream.Duplex = Duplex;
Stream.Readable = Readable;
Stream.Writable = Writable;
Stream.Transform = Transform;
Stream.PassThrough = PassThrough;

// Add Stream class and its components to global scope
global.Stream = Stream;
global.Duplex = Duplex;
global.Readable = Readable;
global.Writable = Writable;
global.Transform = Transform;
global.PassThrough = PassThrough;

export default Stream; 