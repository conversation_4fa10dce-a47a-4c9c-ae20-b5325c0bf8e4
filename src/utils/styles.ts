/**
 * Utility functions for handling styles
 */
import { Platform, StyleSheet } from 'react-native';

interface ShadowProps {
  shadowColor?: string;
  shadowOffset?: { width: number; height: number };
  shadowOpacity?: number;
  shadowRadius?: number;
}

/**
 * Converts React Native shadow properties to a CSS boxShadow string
 * 
 * @param shadowColor - Shadow color (e.g., '#000')
 * @param shadowOffset - Shadow offset object with width and height
 * @param shadowOpacity - Shadow opacity (0-1)
 * @param shadowRadius - Shadow blur radius
 * @returns CSS boxShadow string
 */
export function convertShadowToBoxShadow({
  shadowColor = '#000',
  shadowOffset = { width: 0, height: 0 },
  shadowOpacity = 0.3,
  shadowRadius = 3,
}: ShadowProps): string {
  const { width, height } = shadowOffset;
  
  // Convert color with opacity
  let color = shadowColor;
  
  // If color is in hex format and we need to add opacity
  if (shadowColor.startsWith('#') && shadowOpacity < 1) {
    // Convert hex to rgba
    const r = parseInt(shadowColor.substr(1, 2), 16);
    const g = parseInt(shadowColor.substr(3, 2), 16);
    const b = parseInt(shadowColor.substr(5, 2), 16);
    color = `rgba(${r}, ${g}, ${b}, ${shadowOpacity})`;
  }
  
  return `${width}px ${height}px ${shadowRadius}px ${color}`;
}

/**
 * Converts React Native textShadow properties to a CSS textShadow string
 * 
 * @param shadowColor - Shadow color (e.g., '#000')
 * @param shadowOffset - Shadow offset object with width and height
 * @param shadowRadius - Shadow blur radius
 * @returns CSS textShadow string
 */
export function convertTextShadowToTextShadow({
  shadowColor = 'rgba(0, 0, 0, 0.75)',
  shadowOffset = { width: 0, height: 0 },
  shadowRadius = 2,
}: Omit<ShadowProps, 'shadowOpacity'>): string {
  const { width, height } = shadowOffset;
  return `${width}px ${height}px ${shadowRadius}px ${shadowColor}`;
}

/**
 * Creates a platform-aware style object that handles deprecated shadow properties
 * @param styles The original style object
 * @returns Platform-specific style object with modern shadow properties
 */
export function createPlatformStyles<T extends StyleSheet.NamedStyles<T>>(styles: T): T {
  const updatedStyles = { ...styles };
  
  // Only process styles for web platform
  if (Platform.OS !== 'web') {
    return updatedStyles;
  }
  
  // Process each style object
  Object.keys(updatedStyles).forEach(key => {
    const style = updatedStyles[key];
    
    // Check for box shadow properties
    if (
      style.shadowColor !== undefined || 
      style.shadowOffset !== undefined || 
      style.shadowOpacity !== undefined || 
      style.shadowRadius !== undefined
    ) {
      // Extract shadow properties
      const shadowProps: ShadowProps = {
        shadowColor: style.shadowColor,
        shadowOffset: style.shadowOffset,
        shadowOpacity: style.shadowOpacity,
        shadowRadius: style.shadowRadius
      };
      
      // Add boxShadow property
      style.boxShadow = convertShadowToBoxShadow(shadowProps);
      
      // Remove the old properties
      delete style.shadowColor;
      delete style.shadowOffset;
      delete style.shadowOpacity;
      delete style.shadowRadius;
    }
    
    // Check for text shadow properties
    if (
      style.textShadowColor !== undefined || 
      style.textShadowOffset !== undefined || 
      style.textShadowRadius !== undefined
    ) {
      // Extract text shadow properties
      const textShadowProps = {
        shadowColor: style.textShadowColor,
        shadowOffset: style.textShadowOffset,
        shadowRadius: style.textShadowRadius
      };
      
      // Add textShadow property
      style.textShadow = convertTextShadowToTextShadow(textShadowProps);
      
      // Remove the old properties
      delete style.textShadowColor;
      delete style.textShadowOffset;
      delete style.textShadowRadius;
    }
  });
  
  return updatedStyles;
}

/**
 * Creates platform-specific styles
 * @param styleCreator Function that creates styles
 * @returns Platform-specific styles with modern shadow properties
 */
export function createStyles<T extends StyleSheet.NamedStyles<T>>(
  styleCreator: () => T
): T {
  const styles = styleCreator();
  return createPlatformStyles(styles);
} 