/**
 * Volume Estimator - Utility for processing LiDAR scan data and fallback mechanisms
 * for devices without LiDAR capabilities
 */

import { Platform } from 'react-native';
import * as Device from 'expo-device';
import { classifyFoodImage, ClassificationResult } from '@/services/classificationService';

/**
 * Result from a volume estimation operation
 */
export interface VolumeEstimationResult {
  /** Volume in cubic centimeters (cm³) */
  volumeCm3: number;
  /** Whether the estimation uses precise LiDAR data or a fallback method */
  isPrecise: boolean;
  /** Confidence score (0-1) for the estimation accuracy */
  confidence: number;
  /** Method used for the estimation */
  method: 'lidar' | 'bounding-box' | 'statistical';
  /** Any error that occurred during estimation */
  error?: string;
  /** Whether a container was detected and removed */
  containerDetected?: boolean;
}

/**
 * Bounding box from Vision API or image processing
 */
export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

/**
 * Food item information used for estimation
 */
export interface FoodItem {
  /** Name of the food item */
  name: string;
  /** Bounding box if available */
  boundingBox?: BoundingBox;
  /** Confidence of the food detection (0-1) */
  confidence?: number;
  /** Unique ID for the food item in the image */
  id?: string;
}

/**
 * Type of device based on LiDAR capability
 */
export enum DeviceCapability {
  LIDAR_SUPPORTED = 'lidar_supported',
  NO_LIDAR = 'no_lidar',
  UNKNOWN = 'unknown',
}

/**
 * Container information for volume correction
 */
export interface ContainerInfo {
  /** Type of container */
  type: 'plate' | 'bowl' | 'cup' | 'glass' | 'mug' | 'unknown';
  /** Estimated container volume in cubic centimeters */
  volumeCm3: number;
  /** Container depth/height in centimeters */
  depthCm: number;
  /** Container diameter or width in centimeters */
  diameterCm: number;
}

/**
 * Standard container dimensions for volume correction
 * These values are average estimates and can be refined
 */
export const STANDARD_CONTAINERS: Record<string, ContainerInfo> = {
  'dinner_plate': {
    type: 'plate',
    volumeCm3: 150, // Approximate volume of a standard dinner plate
    depthCm: 2.5,   // Average depth of a dinner plate
    diameterCm: 27  // Average diameter of a dinner plate
  },
  'salad_plate': {
    type: 'plate',
    volumeCm3: 100,
    depthCm: 2.0,
    diameterCm: 20
  },
  'soup_bowl': {
    type: 'bowl',
    volumeCm3: 500,
    depthCm: 7.0,
    diameterCm: 16
  },
  'cereal_bowl': {
    type: 'bowl',
    volumeCm3: 350,
    depthCm: 5.5,
    diameterCm: 15
  },
  'coffee_mug': {
    type: 'mug',
    volumeCm3: 350,
    depthCm: 10.0,
    diameterCm: 8
  },
  'water_glass': {
    type: 'glass',
    volumeCm3: 250,
    depthCm: 12.0,
    diameterCm: 7
  }
};

/**
 * Result for multiple food item volumes
 */
export interface MultiFoodVolumeResult {
  /** Combined total volume in cubic centimeters (cm³) */
  totalVolumeCm3: number;
  /** Array of individual food item volume results */
  items: {
    /** ID matching the original food item */
    id: string;
    /** Name of the food item */
    name: string;
    /** Volume estimate for the specific food item */
    volumeEstimation: VolumeEstimationResult;
    /** Container information if detected */
    container?: ContainerInfo;
  }[];
  /** Whether the estimation uses precise LiDAR data */
  isPrecise: boolean;
  /** Overall confidence score of the estimation */
  confidence: number;
}

/**
 * Average depth estimates (in cm) for different food categories
 * Used for fallback estimation on non-LiDAR devices
 */
const AVERAGE_FOOD_DEPTHS: Record<string, number> = {
  soup: 4.0,
  salad: 5.0,
  pizza: 2.0,
  sandwich: 6.0,
  pasta: 3.5,
  rice: 3.0,
  steak: 2.5,
  chicken: 2.5,
  fish: 2.0,
  burger: 7.0,
  fries: 4.0,
  vegetable: 3.0,
  fruit: 5.0,
  cake: 6.0,
  cookie: 1.0,
  default: 3.0,
};

/**
 * Statistical volume estimates (in cm³) for standard portions of common foods
 * Used as a last resort fallback when no other data is available
 */
const STATISTICAL_VOLUMES: Record<string, number> = {
  'apple': 200,
  'banana': 150,
  'orange': 190,
  'chicken breast': 300,
  'steak': 250,
  'salmon fillet': 200,
  'rice': 180,
  'pasta': 220,
  'bread slice': 80,
  'burger': 350,
  'pizza slice': 180,
  'salad': 300,
  'soup': 400,
  'oatmeal': 250,
  'yogurt': 200,
  'sandwich': 350,
  'taco': 180,
  'burrito': 400,
  'cake slice': 150,
  'cookie': 50,
  'default': 200,
};

/**
 * Correction factors for volume estimation based on device capability
 * and food categories - helps improve accuracy of estimates
 */
const CORRECTION_FACTORS: Record<string, number> = {
  lidar: 1.0,         // No correction for LiDAR scans
  boundingBox: 0.8,   // Bounding box tends to overestimate
  statistical: 1.0,   // Statistical estimates are pre-calibrated
  
  // Food-specific corrections
  soup: 1.2,          // Liquids need slight adjustment
  salad: 0.7,         // Salads have many air pockets
  sandwich: 0.85,     // Sandwiches aren't perfectly filled
  pizza: 1.1,         // Pizza is denser than it appears
};

/**
 * Check if the current device supports LiDAR scanning
 * 
 * @returns Promise that resolves to DeviceCapability enum value
 */
export async function checkDeviceLiDARSupport(): Promise<DeviceCapability> {
  // Only iOS devices can have LiDAR
  if (Platform.OS !== 'ios') {
    return DeviceCapability.NO_LIDAR;
  }

  try {
    const deviceType = await Device.getDeviceTypeAsync();
    const brand = Device.brand;
    const osVersion = Device.osVersion;
    const modelName = Device.modelName;

    // Check for iPhone 12 Pro, 12 Pro Max, 13 Pro, 13 Pro Max, 14 Pro, 14 Pro Max,
    // iPad Pro 11-inch (2nd generation or later), or iPad Pro 12.9-inch (4th generation or later)
    const lidarModels = [
      // iPhones with LiDAR
      'iPhone13,3', 'iPhone13,4',  // iPhone 12 Pro, 12 Pro Max
      'iPhone14,2', 'iPhone14,3',  // iPhone 13 Pro, 13 Pro Max
      'iPhone15,2', 'iPhone15,3',  // iPhone 14 Pro, 14 Pro Max
      'iPhone16,1', 'iPhone16,2',  // iPhone 15 Pro, 15 Pro Max
      
      // iPads with LiDAR
      'iPad8,9', 'iPad8,10',       // iPad Pro 11-inch (2nd gen)
      'iPad8,11', 'iPad8,12',      // iPad Pro 12.9-inch (4th gen)
      'iPad13,4', 'iPad13,5', 'iPad13,6', 'iPad13,7', // iPad Pro 11-inch (3rd gen)
      'iPad13,8', 'iPad13,9', 'iPad13,10', 'iPad13,11', // iPad Pro 12.9-inch (5th gen)
      'iPad14,3', 'iPad14,4',      // iPad Pro 11-inch (4th gen)
      'iPad14,5', 'iPad14,6',      // iPad Pro 12.9-inch (6th gen)
    ];

    // Simple check for Pro models in the model name
    const hasProInName = modelName && (
      modelName.includes('Pro') && 
      (modelName.includes('iPhone') || modelName.includes('iPad'))
    );

    if (lidarModels.includes(modelName || '') || hasProInName) {
      // Further verification could be done by attempting to initialize ARKit with LiDAR
      return DeviceCapability.LIDAR_SUPPORTED;
    }

    return DeviceCapability.NO_LIDAR;
  } catch (error) {
    console.error('Error checking device LiDAR support:', error);
    return DeviceCapability.UNKNOWN;
  }
}

/**
 * Process raw volume data from LiDAR scan with container detection and removal
 * 
 * @param rawVolumeCm3 Raw volume in cubic centimeters from ARKit
 * @param foodName Detected food name
 * @param containerType Optional container type for volume correction
 * @returns Processed volume estimation result
 */
export function processLiDARVolumeData(
  rawVolumeCm3: number,
  foodName: string,
  containerType?: string
): VolumeEstimationResult {
  if (rawVolumeCm3 <= 0) {
    return {
      volumeCm3: 0,
      isPrecise: false,
      confidence: 0,
      method: 'lidar',
      error: 'Invalid volume data received from LiDAR scan',
    };
  }

  // Apply container correction if a container type is specified
  let correctedVolumeCm3 = rawVolumeCm3;
  let containerAdjusted = false;
  let confidenceAdjustment = 0;
  
  if (containerType && STANDARD_CONTAINERS[containerType]) {
    const container = STANDARD_CONTAINERS[containerType];
    
    // Subtract container volume from total volume
    correctedVolumeCm3 = Math.max(0, rawVolumeCm3 - container.volumeCm3);
    containerAdjusted = true;
    
    // Slightly lower confidence when container correction is applied
    // since we're making an assumption about container dimensions
    confidenceAdjustment = -0.05;
  } else {
    // Apply heuristic container detection based on food type
    const lowerFoodName = foodName.toLowerCase();
    
    // For soups, stews, and liquid foods, likely in a bowl or container
    if (lowerFoodName.includes('soup') || 
        lowerFoodName.includes('stew') || 
        lowerFoodName.includes('cereal') || 
        lowerFoodName.includes('oatmeal')) {
        
      // Apply an approximate bowl volume correction
      const bowlVolume = STANDARD_CONTAINERS['soup_bowl'].volumeCm3;
      correctedVolumeCm3 = Math.max(0, rawVolumeCm3 - bowlVolume);
      containerAdjusted = true;
      confidenceAdjustment = -0.1; // Lower confidence due to assumption
    }
    
    // For plates/flat foods, likely on a dinner plate
    else if (lowerFoodName.includes('salad') || 
             lowerFoodName.includes('pasta') || 
             lowerFoodName.includes('rice')) {
               
      // Apply an approximate plate volume correction
      const plateVolume = STANDARD_CONTAINERS['dinner_plate'].volumeCm3;
      correctedVolumeCm3 = Math.max(0, rawVolumeCm3 - plateVolume);
      containerAdjusted = true;
      confidenceAdjustment = -0.1; // Lower confidence due to assumption
    }
  }

  // Apply food-specific correction factors
  let correctionFactor = CORRECTION_FACTORS.lidar;

  // Check if there's a specific correction for this food
  const lowerFoodName = foodName.toLowerCase();
  for (const [category, factor] of Object.entries(CORRECTION_FACTORS)) {
    if (lowerFoodName.includes(category)) {
      correctionFactor = factor;
      break;
    }
  }

  // Calculate confidence based on volume size
  // Very small or very large volumes might be less accurate
  let confidence = 0.95; // LiDAR is generally highly accurate
  if (correctedVolumeCm3 < 50) {
    // Small items are harder to scan precisely
    confidence = 0.85;
  } else if (correctedVolumeCm3 > 2000) {
    // Very large volumes might have scanning artifacts
    confidence = 0.80;
  }
  
  // Apply confidence adjustment for container detection
  confidence = Math.max(0.5, confidence + confidenceAdjustment);

  return {
    volumeCm3: correctedVolumeCm3 * correctionFactor,
    isPrecise: true,
    confidence,
    method: 'lidar',
    containerDetected: containerAdjusted
  };
}

/**
 * Estimate volume using bounding box and average depth
 * Used as a fallback for non-LiDAR devices
 * 
 * @param boundingBox Bounding box from Vision API
 * @param foodName Detected food name
 * @param imageWidth Original image width
 * @param imageHeight Original image height
 * @returns Volume estimation result
 */
export function estimateVolumeFromBoundingBox(
  boundingBox: BoundingBox,
  foodName: string,
  imageWidth: number,
  imageHeight: number
): VolumeEstimationResult {
  // Convert image-space bounding box to approximate real-world area
  // This is a very rough approximation and will need calibration
  
  // Assuming the average plate diameter is 25cm and that it occupies ~60% of image width
  // We can scale the bounding box accordingly
  const plateWidthCm = 25;
  const plateToImageRatio = plateWidthCm / (imageWidth * 0.6);
  
  // Calculate width and height in cm
  const widthCm = boundingBox.width * plateToImageRatio * imageWidth;
  const heightCm = boundingBox.height * plateToImageRatio * imageHeight;
  
  // Get average depth for this type of food
  let depthCm = AVERAGE_FOOD_DEPTHS.default;
  const lowerFoodName = foodName.toLowerCase();
  
  for (const [category, depth] of Object.entries(AVERAGE_FOOD_DEPTHS)) {
    if (lowerFoodName.includes(category)) {
      depthCm = depth;
      break;
    }
  }
  
  // Calculate basic volume (width × height × depth)
  const volumeCm3 = widthCm * heightCm * depthCm;
  
  // Apply correction factor for bounding box method
  const correctedVolume = volumeCm3 * CORRECTION_FACTORS.boundingBox;
  
  return {
    volumeCm3: correctedVolume,
    isPrecise: false,
    confidence: 0.6, // Bounding box method is less accurate
    method: 'bounding-box',
  };
}

/**
 * Get statistical volume estimate for a food item
 * Used as a last resort when no other data is available
 * 
 * @param foodName Detected food name
 * @returns Volume estimation result
 */
export function getStatisticalVolumeEstimate(foodName: string): VolumeEstimationResult {
  const lowerFoodName = foodName.toLowerCase();
  let volumeCm3 = STATISTICAL_VOLUMES.default;
  
  // Find the closest match in our statistical database
  for (const [itemName, volume] of Object.entries(STATISTICAL_VOLUMES)) {
    if (lowerFoodName.includes(itemName) || itemName.includes(lowerFoodName)) {
      volumeCm3 = volume;
      break;
    }
  }
  
  return {
    volumeCm3,
    isPrecise: false,
    confidence: 0.4, // Statistical method is least accurate
    method: 'statistical',
  };
}

/**
 * Get the best available volume estimate based on device capabilities
 * and available data
 * 
 * @param foodItem Food item information
 * @param deviceCapability Current device's LiDAR capability
 * @param lidarVolumeCm3 Volume from LiDAR scan if available
 * @param imageWidth Original image width
 * @param imageHeight Original image height
 * @returns The best available volume estimation
 */
export function getBestVolumeEstimate(
  foodItem: FoodItem,
  deviceCapability: DeviceCapability,
  lidarVolumeCm3?: number,
  imageWidth?: number,
  imageHeight?: number
): VolumeEstimationResult {
  
  // If we have LiDAR volume data, use it
  if (deviceCapability === DeviceCapability.LIDAR_SUPPORTED && lidarVolumeCm3 !== undefined) {
    return processLiDARVolumeData(lidarVolumeCm3, foodItem.name);
  }
  
  // If we have a bounding box and image dimensions, use bounding box method
  if (foodItem.boundingBox && imageWidth && imageHeight) {
    return estimateVolumeFromBoundingBox(
      foodItem.boundingBox,
      foodItem.name,
      imageWidth,
      imageHeight
    );
  }
  
  // Last resort: use statistical estimates
  return getStatisticalVolumeEstimate(foodItem.name);
}

/**
 * Detect container from scan data and food type
 * 
 * @param boundingBox3D 3D bounding box of the scan
 * @param foodName Type of food detected
 * @returns Information about the detected container
 */
export function detectContainer(
  boundingBox3D: {
    width: number;
    height: number;
    depth: number;
  },
  foodName: string
): ContainerInfo | undefined {
  if (!boundingBox3D) return undefined;
  
  const { width, height, depth } = boundingBox3D;
  const lowerFoodName = foodName.toLowerCase();
  
  // Calculate aspect ratio (height-to-width ratio)
  const aspectRatio = height / Math.max(width, depth);
  
  // Taller containers (glasses, mugs)
  if (aspectRatio > 1.5) {
    if (lowerFoodName.includes('coffee') || 
        lowerFoodName.includes('tea') || 
        lowerFoodName.includes('hot')) {
      return STANDARD_CONTAINERS['coffee_mug'];
    }
    
    if (lowerFoodName.includes('water') || 
        lowerFoodName.includes('juice') || 
        lowerFoodName.includes('drink')) {
      return STANDARD_CONTAINERS['water_glass'];
    }
  }
  
  // Wide, shallow containers (plates)
  if (aspectRatio < 0.2) {
    if (width > 20 || depth > 20) {
      return STANDARD_CONTAINERS['dinner_plate'];
    } else {
      return STANDARD_CONTAINERS['salad_plate'];
    }
  }
  
  // Medium-depth containers (bowls)
  if (aspectRatio >= 0.2 && aspectRatio <= 0.6) {
    if (lowerFoodName.includes('soup') || 
        lowerFoodName.includes('stew')) {
      return STANDARD_CONTAINERS['soup_bowl'];
    }
    
    if (lowerFoodName.includes('cereal') || 
        lowerFoodName.includes('oatmeal')) {
      return STANDARD_CONTAINERS['cereal_bowl'];
    }
  }
  
  // No container detected or couldn't categorize
  return undefined;
}

/**
 * Distribute total volume among multiple food items
 * When only one total volume measurement is available from LiDAR,
 * this function distributes it among multiple detected food items
 * 
 * @param foodItems Array of detected food items
 * @param totalVolumeCm3 Total volume measured by LiDAR
 * @param imageWidth Original image width for bounding box calculations
 * @param imageHeight Original image height for bounding box calculations
 * @param boundingBox3D 3D bounding box of the scan
 * @returns Distribution of volumes with individual estimates
 */
export function distributeVolumeAmongFoodItems(
  foodItems: FoodItem[],
  totalVolumeCm3: number,
  imageWidth: number,
  imageHeight: number,
  boundingBox3D?: {
    width: number;
    height: number;
    depth: number;
  }
): MultiFoodVolumeResult {
  // Look for container in the overall scene if we have 3D bounding box data
  let containerAdjustedVolume = totalVolumeCm3;
  let containerInfo: ContainerInfo | undefined = undefined;
  
  if (boundingBox3D && foodItems.length > 0) {
    // Try to detect a container based on the first food item
    containerInfo = detectContainer(boundingBox3D, foodItems[0].name);
    
    if (containerInfo) {
      // Subtract container volume from total volume
      containerAdjustedVolume = Math.max(0, totalVolumeCm3 - containerInfo.volumeCm3);
    }
  }
  
  if (!foodItems || foodItems.length === 0) {
    return {
      totalVolumeCm3: 0,
      items: [],
      isPrecise: false,
      confidence: 0
    };
  }
  
  // If there's only one food item, assign the total volume to it
  if (foodItems.length === 1) {
    const item = foodItems[0];
    const volumeEstimation = processLiDARVolumeData(containerAdjustedVolume, item.name);
    
    return {
      totalVolumeCm3: containerAdjustedVolume,
      items: [{
        id: item.id || '1',
        name: item.name,
        volumeEstimation,
        container: containerInfo
      }],
      isPrecise: volumeEstimation.isPrecise,
      confidence: volumeEstimation.confidence
    };
  }
  
  // For multiple items, we need to distribute the volume
  
  // First, check if we have bounding boxes for the items
  const allHaveBoundingBoxes = foodItems.every(item => item.boundingBox);
  
  if (allHaveBoundingBoxes && imageWidth && imageHeight) {
    // Calculate estimated volumes based on bounding box sizes
    // This helps determine the relative proportion of each item
    const itemsWithEstimates = foodItems.map(item => {
      const boundingBox = item.boundingBox!;
      const pixelArea = boundingBox.width * boundingBox.height;
      return {
        ...item,
        pixelArea,
        relativeSizePercent: 0 // Will be calculated next
      };
    });
    
    // Calculate total pixel area
    const totalPixelArea = itemsWithEstimates.reduce(
      (sum, item) => sum + item.pixelArea,
      0
    );
    
    // Calculate relative size percentages
    const itemsWithPercentages = itemsWithEstimates.map(item => ({
      ...item,
      relativeSizePercent: (item.pixelArea / totalPixelArea) * 100
    }));
    
    // Distribute volume based on relative size
    const result: MultiFoodVolumeResult = {
      totalVolumeCm3: containerAdjustedVolume,
      items: [],
      isPrecise: true,
      confidence: 0
    };
    
    // Calculate individual volumes and add to result
    let totalConfidence = 0;
    
    result.items = itemsWithPercentages.map(item => {
      // Distribute volume proportionally
      const itemVolumeCm3 = containerAdjustedVolume * (item.relativeSizePercent / 100);
      
      // Process with appropriate food-specific adjustments
      const volumeEstimation = processLiDARVolumeData(itemVolumeCm3, item.name);
      
      totalConfidence += volumeEstimation.confidence;
      
      return {
        id: item.id || Math.random().toString(36).substring(7),
        name: item.name,
        volumeEstimation,
        // Add container info only to the first item to avoid counting it multiple times
        // Convert null to undefined explicitly
        container: item === itemsWithPercentages[0] ? containerInfo : undefined
      };
    });
    
    // Average confidence across all items
    result.confidence = totalConfidence / result.items.length;
    
    return result;
  } else {
    // If we don't have bounding boxes, distribute volume equally
    const volumePerItem = containerAdjustedVolume / foodItems.length;
    
    const result: MultiFoodVolumeResult = {
      totalVolumeCm3: containerAdjustedVolume,
      items: [],
      isPrecise: true,
      confidence: 0
    };
    
    let totalConfidence = 0;
    
    result.items = foodItems.map((item, index) => {
      const volumeEstimation = processLiDARVolumeData(volumePerItem, item.name);
      totalConfidence += volumeEstimation.confidence;
      
      return {
        id: item.id || Math.random().toString(36).substring(7),
        name: item.name,
        volumeEstimation,
        // Add container info only to the first item to avoid counting it multiple times
        // Convert null to undefined explicitly
        container: index === 0 ? containerInfo : undefined
      };
    });
    
    // Average confidence across all items
    result.confidence = totalConfidence / result.items.length;
    
    return result;
  }
}

export interface ContainerDetectionResult {
  isContainer: boolean;
  containerType?: 'plate' | 'bowl' | 'cup' | 'glass' | 'other';
  confidenceScore: number;
  boundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

/**
 * Detects common food containers in an image and provides information to exclude them from measurements
 * @param imageUri - URI of the captured image
 * @returns Information about detected containers
 */
export async function detectContainers(imageUri: string): Promise<ContainerDetectionResult[]> {
  try {
    // First check if we have cached container detection results for this image
    const cachedResults = containerDetectionCache.get(imageUri);
    if (cachedResults) {
      return cachedResults;
    }
    
    // Use the classification service to detect objects
    const containerLabels = [
      'plate', 'bowl', 'cup', 'glass', 'mug', 'dish', 'saucer', 
      'platter', 'container', 'tupperware'
    ];
    
    // Call the classification service
    const visionResults = await classifyFoodImage(imageUri);
    
    // Filter results to only include containers
    const containerResults: ContainerDetectionResult[] = [];
    
    for (const object of visionResults) {
      const objectLabel = object.label.toLowerCase();
      const isContainer = containerLabels.some(label => objectLabel.includes(label));
      
      if (isContainer) {
        let containerType: 'plate' | 'bowl' | 'cup' | 'glass' | 'other' = 'other';
        
        if (objectLabel.includes('plate')) containerType = 'plate';
        else if (objectLabel.includes('bowl')) containerType = 'bowl';
        else if (objectLabel.includes('cup') || objectLabel.includes('mug')) containerType = 'cup';
        else if (objectLabel.includes('glass')) containerType = 'glass';
        
        containerResults.push({
          isContainer: true,
          containerType,
          confidenceScore: object.confidence,
          boundingBox: object.boundingBox
        });
      }
    }
    
    // Cache the results
    containerDetectionCache.set(imageUri, containerResults);
    
    return containerResults;
  } catch (error) {
    console.error('Error detecting containers:', error);
    return [];
  }
}

// Cache for container detection results
const containerDetectionCache = new Map<string, ContainerDetectionResult[]>();

// Modify the existing getMultiFoodVolumeEstimates function to account for containers
export function getMultiFoodVolumeEstimates(
  foodItems: FoodItem[],
  deviceCapability: DeviceCapability,
  totalVolumeCm3?: number,
  imageWidth?: number,
  imageHeight?: number,
  containerData?: ContainerDetectionResult[]
): MultiFoodVolumeResult {
  if (!foodItems || foodItems.length === 0) {
    return {
      totalVolumeCm3: 0,
      items: [],
      isPrecise: false,
      confidence: 0
    };
  }
  
  // If we have LiDAR data, distribute it among food items
  if (deviceCapability === DeviceCapability.LIDAR_SUPPORTED && totalVolumeCm3 !== undefined) {
    return distributeVolumeAmongFoodItems(
      foodItems,
      totalVolumeCm3,
      imageWidth || 0,
      imageHeight || 0,
      undefined
    );
  }
  
  // If we don't have LiDAR data, use bounding box or statistical estimates for each item
  const result: MultiFoodVolumeResult = {
    totalVolumeCm3: 0,
    items: [],
    isPrecise: false,
    confidence: 0
  };
  
  let totalConfidence = 0;
  
  result.items = foodItems.map(item => {
    const volumeEstimation = getBestVolumeEstimate(
      item,
      deviceCapability,
      undefined,
      imageWidth,
      imageHeight
    );
    
    result.totalVolumeCm3 += volumeEstimation.volumeCm3;
    totalConfidence += volumeEstimation.confidence;
    
    return {
      id: item.id || Math.random().toString(36).substring(7),
      name: item.name,
      volumeEstimation
    };
  });
  
  // Average confidence across all items
  result.confidence = totalConfidence / result.items.length;
  
  // If we have container data, adjust volume estimates to exclude containers
  if (containerData && containerData.length > 0) {
    // Subtract container volumes or adjust bounding boxes
    const containerAreas = containerData.map(container => {
      if (container.boundingBox && imageWidth && imageHeight) {
        // Calculate the container area in pixels
        const area = container.boundingBox.width * container.boundingBox.height;
        return { area, confidence: container.confidenceScore };
      }
      return null;
    }).filter(Boolean);
    
    // Adjust the volume calculations based on container detection
    if (containerAreas.length > 0 && totalVolumeCm3) {
      // Apply a weighted container volume adjustment
      // This is a simplified approach - in a real implementation you would
      // need more sophisticated container volume estimation
      const containerVolumeAdjustment = 0.15 * totalVolumeCm3;
      totalVolumeCm3 = Math.max(0, totalVolumeCm3 - containerVolumeAdjustment);
      
      // Re-distribute volumes to food items
      // ... (calculation code here)
    }
  }
  
  return result;
}

/**
 * Extract volumetric data from ARKit bounding box for 3D segmentation
 * Used to separate volume of different objects when multiple items are present
 * 
 * @param boundingBox3D ARKit bounding box data
 * @param foodName Food item name
 * @returns Volume estimation for the specific object
 */
export function extractVolumeFromBoundingBox3D(
  boundingBox3D: {
    width: number;
    height: number;
    depth: number;
  },
  foodName: string
): VolumeEstimationResult {
  if (!boundingBox3D || !boundingBox3D.width || !boundingBox3D.height || !boundingBox3D.depth) {
    return {
      volumeCm3: 0,
      isPrecise: false,
      confidence: 0,
      method: 'statistical',
      error: 'Invalid bounding box data'
    };
  }
  
  // Calculate raw volume
  const rawVolumeCm3 = boundingBox3D.width * boundingBox3D.height * boundingBox3D.depth;
  
  // Apply shape factor based on food type
  // Different foods have different "fill factors" within their bounding box
  const foodNameLower = foodName.toLowerCase();
  let shapeFactor = 0.8; // Default: assume 80% of bounding box is filled
  
  // Adjust shape factor based on food type
  if (foodNameLower.includes('apple') || foodNameLower.includes('orange') || 
      foodNameLower.includes('ball') || foodNameLower.includes('fruit')) {
    // Spherical objects have a lower fill factor
    shapeFactor = 0.52; // π/6 ≈ 0.52 for sphere in cuboid
  } else if (foodNameLower.includes('salad') || foodNameLower.includes('chips')) {
    // Irregular piles have more air gaps
    shapeFactor = 0.6;
  } else if (foodNameLower.includes('steak') || foodNameLower.includes('fillet')) {
    // Flat items with regular shape
    shapeFactor = 0.9;
  } else if (foodNameLower.includes('soup') || foodNameLower.includes('liquid')) {
    // Liquids tend to fill their container
    shapeFactor = 0.95;
  }
  
  // Apply correction factor similar to normal LiDAR processing
  let correctionFactor = CORRECTION_FACTORS.lidar;
  for (const [category, factor] of Object.entries(CORRECTION_FACTORS)) {
    if (foodNameLower.includes(category)) {
      correctionFactor = factor;
      break;
    }
  }
  
  // Calculate final volume with shape and correction factors
  const adjustedVolume = rawVolumeCm3 * shapeFactor * correctionFactor;
  
  return {
    volumeCm3: adjustedVolume,
    isPrecise: true,
    confidence: 0.85, // Slightly lower confidence than direct mesh measurement
    method: 'lidar'
  };
} 