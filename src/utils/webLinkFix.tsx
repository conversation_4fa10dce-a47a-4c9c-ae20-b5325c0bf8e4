import React, { useEffect } from 'react';
import { Platform, TouchableOpacity, StyleSheet, StatusBar } from 'react-native';
import { Link } from 'expo-router';

/**
 * WebPlatformFixes component adds global CSS to fix React Native Web issues
 * Must be added at the top level of your app
 */
export function WebPlatformFixes() {
  // Only run on web platform
  if (Platform.OS === 'web') {
    useEffect(() => {
      // Get existing style element or create new one
      let styleEl = document.getElementById('react-native-web-fixes');
      if (!styleEl) {
        styleEl = document.createElement('style');
        styleEl.id = 'react-native-web-fixes';
        document.head.appendChild(styleEl);
      }
  
      // Add CSS rules to fix transform and link styles
      styleEl.textContent = `
        /* Fix for transform styles in links */
        a, a > *, [role="link"], [role="link"] > * {
          transform: none !important;
        }
        
        /* Fix for link styles */
        .web-link-wrapper {
          display: flex;
          flex-direction: column;
          cursor: pointer;
          text-decoration: none;
          color: inherit;
          outline: none;
        }
        
        /* Prevent blue highlights on mobile taps */
        * {
          -webkit-tap-highlight-color: transparent;
        }
      `;
      
      return () => {
        // Clean up on unmount if needed
        if (styleEl) {
          styleEl.remove();
        }
      };
    }, []);
  }
  
  // Return null as this component doesn't render anything
  return null;
}

/**
 * SafeWebLink component
 * Fixes transform style issues with Link components on web platform
 */
export function SafeWebLink({ 
  href, 
  children, 
  style, 
  ...rest
}: {
  href: any;
  children: React.ReactNode;
  style?: any;
  [key: string]: any;
}) {
  // On web platform, we need to handle transform properties specially
  if (Platform.OS === 'web') {
    return (
      <Link href={href} {...rest}>
        {/* Wrap in a div with explicit styles for web that don't use transform */}
        <div className="web-link-wrapper">
          {children}
        </div>
      </Link>
    );
  }
  
  // On native platforms, use asChild pattern normally
  return (
    <Link href={href} asChild {...rest}>
      {children}
    </Link>
  );
}

/**
 * A safe version of TouchableOpacity that works properly with links on web
 */
export function SafeTouchable({ style, children, ...rest }: any) {
  // Remove transform styles for web platform
  const safeStyle = Platform.OS === 'web' 
    ? removeTransformStyles(style)
    : style;
    
  return (
    <TouchableOpacity style={safeStyle} {...rest}>
      {children}
    </TouchableOpacity>
  );
}

// Helper function to remove transform styles
function removeTransformStyles(style: any) {
  if (!style) return style;
  
  if (Array.isArray(style)) {
    return style.map(s => {
      if (s && typeof s === 'object' && 'transform' in s) {
        const { transform, ...rest } = s;
        return rest;
      }
      return s;
    });
  }
  
  if (style && typeof style === 'object' && 'transform' in style) {
    const { transform, ...rest } = style;
    return rest;
  }
  
  return style;
} 