import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, ScrollView, Image, Linking } from 'react-native';
import { isWeb } from './platform';

// At the top of your file
declare const window: any;

// Helper function to safely open URLs
const openURL = (url: string) => {
  // Use React Native's Linking API for all platforms
  Linking.canOpenURL(url).then(supported => {
    if (supported) {
      Linking.openURL(url);
    } else {
      console.log("Don't know how to open URI: " + url);
    }
  });
};

// This is a simplified router for web that doesn't rely on expo-router
// which has compatibility issues with React's new hooks in web mode

interface WebRouterProps {
  children?: React.ReactNode;
  initialRoute?: string;
  fallbackComponent?: React.ReactNode;
}

export const WebRouter: React.FC<WebRouterProps> = ({ 
  children, 
  initialRoute = 'home',
  fallbackComponent 
}) => {
  const [currentRoute, setCurrentRoute] = useState(initialRoute);

  if (!isWeb) {
    // When not on web, just render the children (which should use expo-router)
    return <>{children}</>;
  }

  const renderContent = () => {
    switch (currentRoute) {
      case 'home':
        return <HomeScreen onNavigate={setCurrentRoute} />;
      case 'features':
        return <FeaturesScreen onNavigate={setCurrentRoute} />;
      case 'download':
        return <DownloadScreen onNavigate={setCurrentRoute} />;
      default:
        return <HomeScreen onNavigate={setCurrentRoute} />;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.logo}>Health App</Text>
        <View style={styles.nav}>
          <TouchableOpacity 
            style={[styles.navItem, currentRoute === 'home' && styles.activeNavItem]} 
            onPress={() => setCurrentRoute('home')}
          >
            <Text style={[styles.navText, currentRoute === 'home' && styles.activeNavText]}>Home</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.navItem, currentRoute === 'features' && styles.activeNavItem]} 
            onPress={() => setCurrentRoute('features')}
          >
            <Text style={[styles.navText, currentRoute === 'features' && styles.activeNavText]}>Features</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.navItem, currentRoute === 'download' && styles.activeNavItem]} 
            onPress={() => setCurrentRoute('download')}
          >
            <Text style={[styles.navText, currentRoute === 'download' && styles.activeNavText]}>Get App</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      <ScrollView style={styles.content}>
        {renderContent()}
      </ScrollView>
      
      <View style={styles.footer}>
        <Text style={styles.footerText}>© {new Date().getFullYear()} Health App. All rights reserved.</Text>
      </View>
    </SafeAreaView>
  );
};

// Simple screen components for the web version
const HomeScreen = ({ onNavigate }: { onNavigate: (route: string) => void }) => (
  <View style={styles.screen}>
    <View style={styles.hero}>
      <Text style={styles.heroTitle}>Track Your Health Journey</Text>
      <Text style={styles.heroSubtitle}>
        Scan your meals, track nutrition, and get personalized recommendations
      </Text>
      <TouchableOpacity 
        style={styles.heroButton}
        onPress={() => onNavigate('download')}
      >
        <Text style={styles.heroButtonText}>Download Now</Text>
      </TouchableOpacity>
    </View>
    
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Why Use Health App?</Text>
      <View style={styles.cards}>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Food Scanning</Text>
          <Text style={styles.cardDescription}>
            Instantly scan your meals and get nutritional information
          </Text>
        </View>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Smart Recommendations</Text>
          <Text style={styles.cardDescription}>
            Get personalized alternative recipe suggestions
          </Text>
        </View>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Progress Tracking</Text>
          <Text style={styles.cardDescription}>
            Track your meals and nutrition goals over time
          </Text>
        </View>
      </View>
    </View>
  </View>
);

const FeaturesScreen = ({ onNavigate }: { onNavigate: (route: string) => void }) => (
  <View style={styles.screen}>
    <Text style={styles.pageTitle}>App Features</Text>
    
    <View style={styles.featureItem}>
      <View style={styles.featureContent}>
        <Text style={styles.featureTitle}>Meal Scanning</Text>
        <Text style={styles.featureDescription}>
          Take a photo of your meal and our AI will identify the food, calculate calories,
          and provide detailed nutritional breakdown including macros.
        </Text>
      </View>
    </View>
    
    <View style={styles.featureItem}>
      <View style={styles.featureContent}>
        <Text style={styles.featureTitle}>Alternative Recipes</Text>
        <Text style={styles.featureDescription}>
          For any meal you scan, get healthier alternative recipe suggestions that match
          your dietary preferences while maintaining similar flavor profiles.
        </Text>
      </View>
    </View>
    
    <View style={styles.featureItem}>
      <View style={styles.featureContent}>
        <Text style={styles.featureTitle}>Nutrition History</Text>
        <Text style={styles.featureDescription}>
          Keep track of your meal history and nutrition trends over time with
          detailed charts and analytics.
        </Text>
      </View>
    </View>
    
    <TouchableOpacity 
      style={styles.ctaButton}
      onPress={() => onNavigate('download')}
    >
      <Text style={styles.ctaButtonText}>Get the App</Text>
    </TouchableOpacity>
  </View>
);

const DownloadScreen = ({ onNavigate }: { onNavigate: (route: string) => void }) => {
  // App download links
  const appStoreLink = 'https://apps.apple.com/app/healthapp';
  const playStoreLink = 'https://play.google.com/store/apps/details?id=com.healthapp';
  
  // QR Code value - this would point to a universal link or app landing page
  const qrValue = 'https://healthapp.example.com/download';
  
  // Handle link press for web platforms
  const handleLinkPress = (url: string) => {
    if (isWeb) {
      // Use optional chaining to safely access window.open
      if (typeof window !== 'undefined') {
        window.open(url, '_blank');
      }
    } else {
      // Use Linking for native platforms
      Linking.openURL(url).catch(err => 
        console.error('Error opening link:', err)
      );
    }
  };
  
  return (
    <View style={styles.screen}>
      <Text style={styles.pageTitle}>Download the App</Text>
      
      <View style={styles.downloadSection}>
        <Text style={styles.downloadTitle}>Scan QR Code with Your Phone</Text>
        <Text style={styles.downloadDescription}>
          Use your phone's camera to scan this QR code to download the Health App
        </Text>
        <View style={styles.qrContainer}>
          <View style={styles.qrCode}>
            {/* Use QR code API service for all platforms */}
            <Image 
              source={{ 
                uri: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrValue)}`
              }}
              style={{ width: 200, height: 200 }}
            />
          </View>
        </View>
      </View>
      
      <View style={styles.platformLinks}>
        <Text style={styles.platformTitle}>Available On</Text>
        <View style={styles.platformButtons}>
          <TouchableOpacity 
            style={styles.platformButton}
            onPress={() => handleLinkPress(appStoreLink)}
          >
            <Text style={styles.platformButtonText}>iOS App Store</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.platformButton}
            onPress={() => handleLinkPress(playStoreLink)}
          >
            <Text style={styles.platformButtonText}>Google Play</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    padding: 20,
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
    backgroundColor: 'white',
  },
  logo: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#4a90e2',
  },
  nav: {
    flexDirection: 'row',
  },
  navItem: {
    marginLeft: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
  activeNavItem: {
    backgroundColor: '#e9f0fd',
  },
  navText: {
    fontSize: 16,
    color: '#555',
  },
  activeNavText: {
    color: '#4a90e2',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  screen: {
    padding: 20,
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
    backgroundColor: 'white',
    alignItems: 'center',
  },
  footerText: {
    color: '#6c757d',
  },
  hero: {
    alignItems: 'center',
    padding: 40,
    backgroundColor: '#4a90e2',
    borderRadius: 10,
    marginBottom: 30,
  },
  heroTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 16,
    textAlign: 'center',
  },
  heroSubtitle: {
    fontSize: 18,
    color: 'white',
    opacity: 0.9,
    textAlign: 'center',
    marginBottom: 30,
    maxWidth: 600,
  },
  heroButton: {
    backgroundColor: 'white',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 30,
  },
  heroButtonText: {
    color: '#4a90e2',
    fontSize: 18,
    fontWeight: 'bold',
  },
  section: {
    marginBottom: 40,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#333',
  },
  cards: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 20,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    width: 300,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 8,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  cardDescription: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
  pageTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 30,
    color: '#333',
  },
  featureItem: {
    flexDirection: 'row',
    marginBottom: 30,
    backgroundColor: 'white',
    borderRadius: 10,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 8,
    elevation: 3,
  },
  featureImage: {
    width: 150,
    height: 150,
  },
  featureContent: {
    flex: 1,
    padding: 20,
  },
  featureTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  featureDescription: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
  ctaButton: {
    backgroundColor: '#4a90e2',
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 30,
    alignSelf: 'center',
    marginTop: 20,
  },
  ctaButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  downloadSection: {
    alignItems: 'center',
    marginBottom: 40,
  },
  downloadTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  downloadDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    maxWidth: 500,
  },
  qrContainer: {
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 8,
    elevation: 3,
  },
  qrCode: {
    width: 200,
    height: 200,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  qrPlaceholder: {
    color: '#999',
    fontWeight: 'bold',
  },
  platformLinks: {
    alignItems: 'center',
  },
  platformTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333',
  },
  platformButtons: {
    flexDirection: 'row',
    gap: 20,
  },
  platformButton: {
    backgroundColor: '#333',
    paddingVertical: 12,
    paddingHorizontal: 25,
    borderRadius: 8,
  },
  platformButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
}); 