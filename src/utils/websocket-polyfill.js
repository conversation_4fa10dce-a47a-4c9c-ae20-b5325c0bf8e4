// Mobile-focused WebSocket polyfill to fix 'stream' module dependency issues
import { Platform } from 'react-native';

// Only apply the polyfill on mobile platforms
if (Platform.OS !== 'web') {
  // Store the original implementation if it exists
  const OriginalWebSocket = global.WebSocket;
  
  // Define our wrapped implementation that avoids Node.js dependencies
  class CustomWebSocket {
    constructor(url, protocols) {
      // Create the native WebSocket
      this._socket = new OriginalWebSocket(url, protocols);
      
      // Forward basic properties
      this.binaryType = this._socket.binaryType;
      this.bufferedAmount = 0;
      this.extensions = '';
      this.protocol = '';
      this.readyState = this._socket.readyState || 0;
      this.url = url;
      
      // Set up callbacks
      this.onopen = null;
      this.onmessage = null;
      this.onclose = null;
      this.onerror = null;
      
      // Set up event forwarding
      this._socket.onopen = (event) => {
        this.readyState = this._socket.readyState;
        this.onopen && this.onopen(event);
      };
      
      this._socket.onmessage = (event) => {
        this.onmessage && this.onmessage(event);
      };
      
      this._socket.onclose = (event) => {
        this.readyState = this._socket.readyState;
        this.onclose && this.onclose(event);
      };
      
      this._socket.onerror = (event) => {
        this.onerror && this.onerror(event);
      };
    }
    
    // Implement required methods
    send(data) {
      if (this._socket.readyState === 1) {
        this._socket.send(data);
      }
    }
    
    close(code, reason) {
      this._socket.close(code, reason);
    }
    
    // Add EventTarget-like methods if needed by libraries
    addEventListener(type, listener) {
      if (type === 'open') {
        const originalOnOpen = this.onopen;
        this.onopen = (event) => {
          originalOnOpen && originalOnOpen(event);
          listener(event);
        };
      } else if (type === 'message') {
        const originalOnMessage = this.onmessage;
        this.onmessage = (event) => {
          originalOnMessage && originalOnMessage(event);
          listener(event);
        };
      } else if (type === 'close') {
        const originalOnClose = this.onclose;
        this.onclose = (event) => {
          originalOnClose && originalOnClose(event);
          listener(event);
        };
      } else if (type === 'error') {
        const originalOnError = this.onerror;
        this.onerror = (event) => {
          originalOnError && originalOnError(event);
          listener(event);
        };
      }
    }
    
    removeEventListener(type, listener) {
      // Simple implementation - in a real polyfill you'd want to actually remove specific listeners
      if (type === 'open') this.onopen = null;
      else if (type === 'message') this.onmessage = null;
      else if (type === 'close') this.onclose = null;
      else if (type === 'error') this.onerror = null;
    }
  }
  
  // Define the constants
  CustomWebSocket.CONNECTING = 0;
  CustomWebSocket.OPEN = 1;
  CustomWebSocket.CLOSING = 2;
  CustomWebSocket.CLOSED = 3;
  
  // Replace the global WebSocket implementation
  global.WebSocket = CustomWebSocket;
  
  // Add Duplex class to global to satisfy 'stream' module requirements
  if (!global.Duplex) {
    global.Duplex = class Duplex {
      constructor() {
        this.readable = true;
        this.writable = true;
      }
      
      // Minimal implementation of required methods
      pipe() { return this; }
      on() { return this; }
      once() { return this; }
      emit() { return false; }
      end() {}
      destroy() {}
    };
  }
}

export default {}; 