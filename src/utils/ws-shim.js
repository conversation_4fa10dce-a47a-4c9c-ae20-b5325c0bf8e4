// Shim for the 'ws' WebSocket library
import { Platform } from 'react-native';

// Define our ws module outside the condition for proper export
const ws = Platform.OS !== 'web' ? createWsShim() : { WebSocket: global.WebSocket };

// Only define shims on mobile platforms
function createWsShim() {
  // Define a minimal WebSocket implementation that uses the native implementation
  class WebSocket {
    constructor(address, protocols, options = {}) {
      this.address = address;
      this.protocols = protocols;
      this.options = options;
      this.readyState = WebSocket.CONNECTING;
      
      // Use native WebSocket
      if (global.WebSocket) {
        try {
          this._socket = new global.WebSocket(address, protocols);
          
          // Set up listeners
          this._socket.onopen = (e) => {
            this.readyState = WebSocket.OPEN;
            if (this.onopen) this.onopen(e);
          };
          
          this._socket.onmessage = (e) => {
            if (this.onmessage) this.onmessage(e);
          };
          
          this._socket.onclose = (e) => {
            this.readyState = WebSocket.CLOSED;
            if (this.onclose) this.onclose(e);
          };
          
          this._socket.onerror = (e) => {
            if (this.onerror) this.onerror(e);
          };
        } catch (err) {
          console.error('WebSocket init error:', err);
          this.readyState = WebSocket.CLOSED;
          if (this.onerror) this.onerror(new Error('WebSocket initialization failed'));
        }
      } else {
        console.error('Native WebSocket not available');
        this.readyState = WebSocket.CLOSED;
        if (this.onerror) this.onerror(new Error('WebSocket not supported'));
      }
    }
    
    // Basic methods
    send(data) {
      if (this._socket && this.readyState === WebSocket.OPEN) {
        this._socket.send(data);
        return true;
      }
      return false;
    }
    
    close(code, reason) {
      if (this._socket) {
        this.readyState = WebSocket.CLOSING;
        this._socket.close(code, reason);
      }
    }
    
    // Event handlers (to be set by user)
    onopen = null;
    onmessage = null;
    onclose = null;
    onerror = null;
    
    // Event emitter interface (minimal implementation)
    on(event, listener) {
      if (event === 'open') this.onopen = listener;
      else if (event === 'message') this.onmessage = listener;
      else if (event === 'close') this.onclose = listener;
      else if (event === 'error') this.onerror = listener;
      return this;
    }
    
    once(event, listener) {
      const wrapper = (...args) => {
        this.removeListener(event, wrapper);
        listener.apply(this, args);
      };
      return this.on(event, wrapper);
    }
    
    removeListener(event, listener) {
      if (event === 'open' && this.onopen === listener) this.onopen = null;
      else if (event === 'message' && this.onmessage === listener) this.onmessage = null;
      else if (event === 'close' && this.onclose === listener) this.onclose = null;
      else if (event === 'error' && this.onerror === listener) this.onerror = null;
      return this;
    }
    
    removeAllListeners(event) {
      if (!event || event === 'open') this.onopen = null;
      if (!event || event === 'message') this.onmessage = null;
      if (!event || event === 'close') this.onclose = null;
      if (!event || event === 'error') this.onerror = null;
      return this;
    }
    
    // Other required methods
    emit() { return false; }
    ping() { return true; }
    pong() { return true; }
    terminate() { this.close(); }
  }
  
  // Static properties
  WebSocket.CONNECTING = 0;
  WebSocket.OPEN = 1;
  WebSocket.CLOSING = 2;
  WebSocket.CLOSED = 3;
  
  // Create a mock Server class
  class Server {
    constructor() {
      this.clients = [];
      this.options = {};
    }
    
    // Required methods (minimal implementation)
    on() { return this; }
    once() { return this; }
    close() {}
    handleUpgrade() {}
    emit() { return false; }
  }
  
  // Create the module exports
  const wsModule = {
    WebSocket,
    Server,
    createWebSocketStream: () => null,
  };
  
  // Set up module.exports-style exports for CommonJS interop
  if (!global.ws) {
    global.ws = wsModule;
  }
  
  return wsModule;
}

// Export for direct imports
export default ws; 