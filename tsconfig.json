{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "strictNullChecks": true, "noImplicitAny": false, "noImplicitThis": true, "alwaysStrict": true, "jsx": "react-native", "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "noEmit": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["node"], "typeRoots": ["./node_modules/@types", "./types"]}, "include": ["src/**/*.ts", "src/**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "nativewind-env.d.ts", "src/types/**/*.d.ts", "deno.d.ts"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}