import React, { useState } from 'react';
import { BlogList } from './blog/components/BlogList';
import { BlogPost } from './blog/components/BlogPost';
import './styles.css';

// Web app components
const HomeScreen = ({ onNavigate }: { onNavigate: (route: string, params?: any) => void }) => (
  <div className="screen">
    <div className="hero">
      <h1 className="hero-title">Track Your Health Journey</h1>
      <p className="hero-subtitle">
        Scan your meals, track nutrition, and get personalized recommendations
      </p>
      <button 
        className="hero-button"
        onClick={() => onNavigate('download')}
      >
        Download Now
      </button>
    </div>
    
    <div className="section">
      <h2 className="section-title">Why Use ChooseHealthy?</h2>
      <div className="cards">
        <div className="card">
          <h3 className="card-title">Food Scanning</h3>
          <p className="card-description">
            Instantly scan your meals and get nutritional information
          </p>
        </div>
        <div className="card">
          <h3 className="card-title">Smart Recommendations</h3>
          <p className="card-description">
            Get personalized alternative recipe suggestions
          </p>
        </div>
        <div className="card">
          <h3 className="card-title">Progress Tracking</h3>
          <p className="card-description">
            Track your meals and nutrition goals over time
          </p>
        </div>
      </div>
    </div>
    
    {/* Blog Preview Section */}
    <div className="section">
      <h2 className="section-title">Latest Health Tips</h2>
      <p className="section-subtitle">
        Expert advice and insights from our nutrition blog
      </p>
      <button 
        className="blog-preview-button"
        onClick={() => onNavigate('blog')}
      >
        Read Our Blog →
      </button>
    </div>
  </div>
);

const FeaturesScreen = ({ onNavigate }: { onNavigate: (route: string, params?: any) => void }) => (
  <div className="screen">
    <h1 className="page-title">App Features</h1>
    
    <div className="feature-item">
      <div className="feature-content">
        <h3 className="feature-title">Meal Scanning</h3>
        <p className="feature-description">
          Take a photo of your meal and our AI will identify the food, calculate calories,
          and provide detailed nutritional breakdown including macros.
        </p>
      </div>
    </div>
    
    <div className="feature-item">
      <div className="feature-content">
        <h3 className="feature-title">Alternative Recipes</h3>
        <p className="feature-description">
          For any meal you scan, get healthier alternative recipe suggestions that match
          your dietary preferences while maintaining similar flavor profiles.
        </p>
      </div>
    </div>
    
    <div className="feature-item">
      <div className="feature-content">
        <h3 className="feature-title">Nutrition History</h3>
        <p className="feature-description">
          Keep track of your meal history and nutrition trends over time with
          detailed charts and analytics.
        </p>
      </div>
    </div>
    
    <button 
      className="cta-button"
      onClick={() => onNavigate('download')}
    >
      Get the App
    </button>
  </div>
);

const DownloadScreen = ({ onNavigate }: { onNavigate: (route: string, params?: any) => void }) => {
  const appStoreLink = 'https://apps.apple.com/app/choosehealthy';
  const playStoreLink = 'https://play.google.com/store/apps/details?id=com.choosehealthy';
  const qrValue = 'https://choosehealthy.app/download';
  
  return (
    <div className="screen">
      <h1 className="page-title">Download the App</h1>
      
      <div className="download-section">
        <h2 className="download-title">Scan QR Code with Your Phone</h2>
        <p className="download-description">
          Use your phone's camera to scan this QR code to download ChooseHealthy
        </p>
        <div className="qr-container">
          <img 
            src={`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrValue)}`}
            alt="Download QR Code"
            style={{ width: 200, height: 200 }}
          />
        </div>
      </div>
      
      <div className="platform-links">
        <h3 className="platform-title">Available On</h3>
        <div className="platform-buttons">
          <button 
            className="platform-button"
            onClick={() => window.open(appStoreLink, '_blank')}
          >
            iOS App Store
          </button>
          <button 
            className="platform-button"
            onClick={() => window.open(playStoreLink, '_blank')}
          >
            Google Play
          </button>
        </div>
      </div>
    </div>
  );
};

// Main web app component
export default function WebApp() {
  const [currentRoute, setCurrentRoute] = useState('home');
  const [routeParams, setRouteParams] = useState<any>({});
  
  const navigate = (route: string, params?: any) => {
    setCurrentRoute(route);
    setRouteParams(params || {});
  };
  
  const renderContent = () => {
    switch (currentRoute) {
      case 'home':
        return <HomeScreen onNavigate={navigate} />;
      case 'features':
        return <FeaturesScreen onNavigate={navigate} />;
      case 'download':
        return <DownloadScreen onNavigate={navigate} />;
      case 'blog':
        return <BlogList onNavigate={navigate} selectedCategory={routeParams.category} />;
      case 'blog-post':
        return <BlogPost slug={routeParams.slug} onNavigate={navigate} />;
      default:
        return <HomeScreen onNavigate={navigate} />;
    }
  };
  
  return (
    <div className="container">
      <header className="header">
        <button onClick={() => navigate('home')} className="logo-button">
          <span className="logo">ChooseHealthy</span>
        </button>
        <nav className="nav">
          <button 
            className={`nav-item ${currentRoute === 'home' ? 'active' : ''}`}
            onClick={() => navigate('home')}
          >
            Home
          </button>
          <button 
            className={`nav-item ${currentRoute === 'features' ? 'active' : ''}`}
            onClick={() => navigate('features')}
          >
            Features
          </button>
          <button 
            className={`nav-item ${currentRoute.startsWith('blog') ? 'active' : ''}`}
            onClick={() => navigate('blog')}
          >
            Blog
          </button>
          <button 
            className={`nav-item ${currentRoute === 'download' ? 'active' : ''}`}
            onClick={() => navigate('download')}
          >
            Get App
          </button>
        </nav>
      </header>
      
      <main className="content">
        {renderContent()}
      </main>
      
      <footer className="footer">
        <p className="footer-text">© {new Date().getFullYear()} ChooseHealthy. All rights reserved.</p>
      </footer>
    </div>
  );
}