import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, ScrollView, Image } from 'react-native';
import { BlogList } from './blog/components/BlogList';
import { BlogPost } from './blog/components/BlogPost';

// Simple context providers for web
const ThemeProvider = ({ children }: { children: React.ReactNode }) => <>{children}</>;
const AuthProvider = ({ children }: { children: React.ReactNode }) => <>{children}</>;

// Web app components
const HomeScreen = ({ onNavigate }: { onNavigate: (route: string, params?: any) => void }) => (
  <View style={styles.screen}>
    <View style={styles.hero}>
      <Text style={styles.heroTitle}>Track Your Health Journey</Text>
      <Text style={styles.heroSubtitle}>
        Scan your meals, track nutrition, and get personalized recommendations
      </Text>
      <TouchableOpacity 
        style={styles.heroButton}
        onPress={() => onNavigate('download')}
      >
        <Text style={styles.heroButtonText}>Download Now</Text>
      </TouchableOpacity>
    </View>
    
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Why Use ChooseHealthy?</Text>
      <View style={styles.cards}>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Food Scanning</Text>
          <Text style={styles.cardDescription}>
            Instantly scan your meals and get nutritional information
          </Text>
        </View>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Smart Recommendations</Text>
          <Text style={styles.cardDescription}>
            Get personalized alternative recipe suggestions
          </Text>
        </View>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Progress Tracking</Text>
          <Text style={styles.cardDescription}>
            Track your meals and nutrition goals over time
          </Text>
        </View>
      </View>
    </View>
    
    {/* Blog Preview Section */}
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Latest Health Tips</Text>
      <Text style={styles.sectionSubtitle}>
        Expert advice and insights from our nutrition blog
      </Text>
      <TouchableOpacity 
        style={styles.blogPreviewButton}
        onPress={() => onNavigate('blog')}
      >
        <Text style={styles.blogPreviewButtonText}>Read Our Blog →</Text>
      </TouchableOpacity>
    </View>
  </View>
);

const FeaturesScreen = ({ onNavigate }: { onNavigate: (route: string, params?: any) => void }) => (
  <View style={styles.screen}>
    <Text style={styles.pageTitle}>App Features</Text>
    
    <View style={styles.featureItem}>
      <View style={styles.featureContent}>
        <Text style={styles.featureTitle}>Meal Scanning</Text>
        <Text style={styles.featureDescription}>
          Take a photo of your meal and our AI will identify the food, calculate calories,
          and provide detailed nutritional breakdown including macros.
        </Text>
      </View>
    </View>
    
    <View style={styles.featureItem}>
      <View style={styles.featureContent}>
        <Text style={styles.featureTitle}>Alternative Recipes</Text>
        <Text style={styles.featureDescription}>
          For any meal you scan, get healthier alternative recipe suggestions that match
          your dietary preferences while maintaining similar flavor profiles.
        </Text>
      </View>
    </View>
    
    <View style={styles.featureItem}>
      <View style={styles.featureContent}>
        <Text style={styles.featureTitle}>Nutrition History</Text>
        <Text style={styles.featureDescription}>
          Keep track of your meal history and nutrition trends over time with
          detailed charts and analytics.
        </Text>
      </View>
    </View>
    
    <TouchableOpacity 
      style={styles.ctaButton}
      onPress={() => onNavigate('download')}
    >
      <Text style={styles.ctaButtonText}>Get the App</Text>
    </TouchableOpacity>
  </View>
);