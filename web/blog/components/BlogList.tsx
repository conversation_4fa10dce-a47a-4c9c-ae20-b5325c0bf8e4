import React from 'react';
import { BlogPost, getAllBlogPosts, blogCategories } from '../blogData';

interface BlogListProps {
  onNavigate: (route: string, params?: any) => void;
  selectedCategory?: string;
}

export const BlogList: React.FC<BlogListProps> = ({ onNavigate, selectedCategory }) => {
  const allPosts = getAllBlogPosts();
  const filteredPosts = selectedCategory 
    ? allPosts.filter(post => post.category === selectedCategory)
    : allPosts;

  const handlePostPress = (post: BlogPost) => {
    onNavigate('blog-post', { slug: post.slug });
  };

  const handleCategoryPress = (category: string) => {
    onNavigate('blog', { category });
  };

  return (
    <div className="blog-container">
      <div className="blog-header">
        <h1 className="blog-title">Health & Nutrition Blog</h1>
        <p className="blog-subtitle">
          Expert insights, tips, and guides for your healthy lifestyle journey
        </p>
      </div>

      {/* Category Filter */}
      <div className="category-section">
        <h3 className="category-title">Categories</h3>
        <div className="category-scroll">
          <button 
            className={`category-chip ${!selectedCategory ? 'active' : ''}`}
            onClick={() => onNavigate('blog')}
          >
            All Posts
          </button>
          {blogCategories.map(category => (
            <button
              key={category.id}
              className={`category-chip ${selectedCategory === category.name ? 'active' : ''}`}
              onClick={() => handleCategoryPress(category.name)}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Blog Posts */}
      <div className="posts-section">
        {filteredPosts.length === 0 ? (
          <div className="empty-state">
            <p className="empty-text">No posts found in this category</p>
          </div>
        ) : (
          filteredPosts.map(post => (
            <article
              key={post.id}
              className="post-card"
              onClick={() => handlePostPress(post)}
            >
              <div className="post-content">
                <div className="post-meta">
                  <span className="category">{post.category}</span>
                  <span className="read-time">{post.readTime}</span>
                </div>
                <h2 className="post-title">{post.title}</h2>
                <p className="post-excerpt">{post.excerpt}</p>
                <div className="post-footer">
                  <span className="author">By {post.author}</span>
                  <span className="date">{new Date(post.publishDate).toLocaleDateString()}</span>
                </div>
                <div className="tags">
                  {post.tags.slice(0, 3).map(tag => (
                    <span key={tag} className="tag">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </article>
          ))
        )}
      </div>
      
      <style jsx>{`
        .blog-container {
          background: #f8f9fa;
        }
        
        .blog-header {
          padding: 30px;
          background: #4a90e2;
          text-align: center;
          color: white;
        }
        
        .blog-title {
          font-size: 28px;
          font-weight: bold;
          margin-bottom: 10px;
        }
        
        .blog-subtitle {
          font-size: 16px;
          opacity: 0.9;
          max-width: 600px;
          margin: 0 auto;
        }
        
        .category-section {
          padding: 20px;
          background: white;
        }
        
        .category-title {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 15px;
        }
        
        .category-scroll {
          display: flex;
          gap: 10px;
          overflow-x: auto;
          padding-bottom: 10px;
        }
        
        .category-chip {
          background: #f1f3f4;
          border: none;
          padding: 8px 16px;
          border-radius: 20px;
          color: #666;
          font-weight: 500;
          cursor: pointer;
          white-space: nowrap;
          transition: all 0.2s;
        }
        
        .category-chip:hover {
          background: #e1e3e4;
        }
        
        .category-chip.active {
          background: #4a90e2;
          color: white;
        }
        
        .posts-section {
          padding: 20px;
        }
        
        .empty-state {
          text-align: center;
          padding: 40px;
        }
        
        .empty-text {
          font-size: 16px;
          color: #666;
        }
        
        .post-card {
          background: white;
          border-radius: 12px;
          margin-bottom: 20px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          cursor: pointer;
          transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .post-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .post-content {
          padding: 20px;
        }
        
        .post-meta {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
        }
        
        .category {
          font-size: 12px;
          color: #4a90e2;
          font-weight: bold;
          text-transform: uppercase;
        }
        
        .read-time {
          font-size: 12px;
          color: #666;
        }
        
        .post-title {
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 10px;
          line-height: 1.3;
        }
        
        .post-excerpt {
          font-size: 14px;
          color: #666;
          line-height: 1.4;
          margin-bottom: 15px;
        }
        
        .post-footer {
          display: flex;
          justify-content: space-between;
          margin-bottom: 15px;
        }
        
        .author, .date {
          font-size: 12px;
          color: #666;
        }
        
        .tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }
        
        .tag {
          background: #f1f3f4;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 10px;
          color: #666;
        }
        
        @media (max-width: 768px) {
          .category-scroll {
            flex-wrap: wrap;
          }
          
          .post-meta {
            flex-direction: column;
            gap: 5px;
          }
        }
      `}</style>
    </div>
  );
};