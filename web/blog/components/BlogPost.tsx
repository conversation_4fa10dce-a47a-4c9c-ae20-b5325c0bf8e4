import React from 'react';
import { BlogPost as BlogPostType, getBlogPostBySlug, getRelatedPosts } from '../blogData';

interface BlogPostProps {
  slug: string;
  onNavigate: (route: string, params?: any) => void;
}

export const BlogPost: React.FC<BlogPostProps> = ({ slug, onNavigate }) => {
  const post = getBlogPostBySlug(slug);
  
  if (!post) {
    return (
      <div className="container">
        <div className="error-container">
          <h1 className="error-title">Post Not Found</h1>
          <p className="error-text">The blog post you're looking for doesn't exist.</p>
          <button 
            className="back-button"
            onClick={() => onNavigate('blog')}
          >
            ← Back to Blog
          </button>
        </div>
      </div>
    );
  }

  const relatedPosts = getRelatedPosts(post);

  return (
    <div className="blog-post-container">
      {/* Header */}
      <header className="post-header">
        <button 
          className="back-link"
          onClick={() => onNavigate('blog')}
        >
          ← Back to Blog
        </button>
        
        <div className="category-badge">
          {post.category}
        </div>
        
        <h1 className="post-title">{post.title}</h1>
        
        <div className="post-meta">
          <span className="author">By {post.author}</span>
          <span className="date">{new Date(post.publishDate).toLocaleDateString()}</span>
          <span className="read-time">{post.readTime}</span>
        </div>
        
        <div className="post-tags">
          {post.tags.map(tag => (
            <span key={tag} className="tag">
              {tag}
            </span>
          ))}
        </div>
      </header>

      {/* Content */}
      <article className="content-container">
        <div className="post-content">
          {post.content.split('\n\n').map((paragraph, index) => {
            if (paragraph.startsWith('#')) {
              const level = paragraph.match(/^#+/)?.[0].length || 1;
              const text = paragraph.replace(/^#+\s*/, '');
              const Tag = `h${Math.min(level + 1, 6)}` as keyof JSX.IntrinsicElements;
              return <Tag key={index} className="content-heading">{text}</Tag>;
            }
            return <p key={index} className="content-paragraph">{paragraph}</p>;
          })}
        </div>
      </article>

      {/* Related Posts */}
      {relatedPosts.length > 0 && (
        <section className="related-section">
          <h2 className="related-title">Related Articles</h2>
          {relatedPosts.map(relatedPost => (
            <article
              key={relatedPost.id}
              className="related-post"
              onClick={() => onNavigate('blog-post', { slug: relatedPost.slug })}
            >
              <h3 className="related-post-title">{relatedPost.title}</h3>
              <p className="related-post-excerpt">{relatedPost.excerpt}</p>
              <span className="related-post-meta">
                {relatedPost.category} • {relatedPost.readTime}
              </span>
            </article>
          ))}
        </section>
      )}
      
      <style jsx>{`
        .blog-post-container {
          background: #f8f9fa;
          min-height: 100vh;
        }
        
        .error-container {
          padding: 40px;
          text-align: center;
        }
        
        .error-title {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 10px;
        }
        
        .error-text {
          font-size: 16px;
          color: #666;
          margin-bottom: 20px;
        }
        
        .back-button {
          background: #4a90e2;
          color: white;
          padding: 10px 20px;
          border: none;
          border-radius: 8px;
          font-weight: bold;
          cursor: pointer;
        }
        
        .post-header {
          background: white;
          padding: 30px;
          border-bottom: 1px solid #e9ecef;
        }
        
        .back-link {
          background: none;
          border: none;
          color: #4a90e2;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          margin-bottom: 20px;
          display: block;
        }
        
        .category-badge {
          background: #4a90e2;
          color: white;
          padding: 6px 12px;
          border-radius: 16px;
          font-size: 12px;
          font-weight: bold;
          text-transform: uppercase;
          display: inline-block;
          margin-bottom: 15px;
        }
        
        .post-title {
          font-size: 28px;
          font-weight: bold;
          line-height: 1.3;
          margin-bottom: 15px;
        }
        
        .post-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 15px;
          margin-bottom: 20px;
        }
        
        .author, .date, .read-time {
          font-size: 14px;
          color: #666;
        }
        
        .post-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }
        
        .tag {
          background: #f1f3f4;
          padding: 5px 10px;
          border-radius: 14px;
          font-size: 12px;
          color: #666;
        }
        
        .content-container {
          background: white;
          margin: 20px;
          border-radius: 12px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .post-content {
          padding: 30px;
        }
        
        .content-heading {
          margin: 20px 0 10px 0;
          color: #333;
        }
        
        .content-paragraph {
          font-size: 16px;
          line-height: 1.6;
          margin-bottom: 16px;
          color: #333;
        }
        
        .related-section {
          margin: 20px;
          background: white;
          border-radius: 12px;
          padding: 20px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .related-title {
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 15px;
        }
        
        .related-post {
          padding: 15px 0;
          border-bottom: 1px solid #f1f3f4;
          cursor: pointer;
          transition: background-color 0.2s;
        }
        
        .related-post:hover {
          background: #f8f9fa;
          margin: 0 -20px;
          padding: 15px 20px;
        }
        
        .related-post:last-child {
          border-bottom: none;
        }
        
        .related-post-title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        
        .related-post-excerpt {
          font-size: 14px;
          color: #666;
          margin-bottom: 5px;
        }
        
        .related-post-meta {
          font-size: 12px;
          color: #999;
        }
        
        @media (max-width: 768px) {
          .post-header, .content-container, .related-section {
            margin: 10px;
          }
          
          .post-content {
            padding: 20px;
          }
          
          .post-title {
            font-size: 24px;
          }
          
          .post-meta {
            flex-direction: column;
            gap: 5px;
          }
        }
      `}</style>
    </div>
  );
};