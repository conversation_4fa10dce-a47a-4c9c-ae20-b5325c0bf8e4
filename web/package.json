{"name": "choosehealthy-web-blog", "version": "1.0.0", "private": true, "scripts": {"start": "webpack serve --mode development --open", "build": "webpack --mode production", "dev": "webpack serve --mode development --open --hot"}, "dependencies": {"react": "18.2.0", "react-dom": "18.2.0"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@babel/preset-react": "^7.22.0", "@babel/preset-typescript": "^7.23.0", "babel-loader": "^9.1.0", "css-loader": "^6.8.0", "html-webpack-plugin": "^5.5.0", "style-loader": "^3.3.0", "typescript": "^5.2.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0"}}