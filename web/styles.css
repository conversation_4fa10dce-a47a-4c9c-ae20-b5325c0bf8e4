/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f8f9fa;
  color: #333;
  line-height: 1.6;
}

button {
  border: none;
  cursor: pointer;
  font-family: inherit;
}

/* Layout */
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.logo-button {
  background: none;
  padding: 0;
}

.logo {
  font-size: 22px;
  font-weight: bold;
  color: #4a90e2;
}

.nav {
  display: flex;
  gap: 20px;
}

.nav-item {
  padding: 8px 12px;
  border-radius: 6px;
  background: none;
  color: #555;
  font-size: 16px;
  transition: all 0.2s;
}

.nav-item:hover {
  background: #f1f3f4;
}

.nav-item.active {
  background: #e9f0fd;
  color: #4a90e2;
  font-weight: bold;
}

.content {
  flex: 1;
}

.footer {
  padding: 20px;
  border-top: 1px solid #e9ecef;
  background: white;
  text-align: center;
}

.footer-text {
  color: #6c757d;
}

/* Screen layouts */
.screen {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Hero section */
.hero {
  text-align: center;
  padding: 40px;
  background: #4a90e2;
  border-radius: 10px;
  margin-bottom: 30px;
  color: white;
}

.hero-title {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 16px;
}

.hero-subtitle {
  font-size: 18px;
  opacity: 0.9;
  margin-bottom: 30px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-button {
  background: white;
  color: #4a90e2;
  padding: 12px 30px;
  border-radius: 30px;
  font-size: 18px;
  font-weight: bold;
  transition: transform 0.2s;
}

.hero-button:hover {
  transform: translateY(-2px);
}

/* Sections */
.section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  text-align: center;
}

.section-subtitle {
  font-size: 16px;
  color: #666;
  text-align: center;
  margin-bottom: 20px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.blog-preview-button {
  background: #28a745;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  display: block;
  margin: 0 auto;
}

/* Cards */
.cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  justify-items: center;
}

.card {
  background: white;
  border-radius: 10px;
  padding: 20px;
  width: 100%;
  max-width: 300px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.card-description {
  color: #666;
  line-height: 1.5;
}

/* Page titles */
.page-title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 30px;
}

/* Features */
.feature-item {
  background: white;
  border-radius: 10px;
  margin-bottom: 30px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.feature-content {
  padding: 20px;
}

.feature-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
}

.feature-description {
  color: #666;
  line-height: 1.5;
}

.cta-button {
  background: #4a90e2;
  color: white;
  padding: 15px 30px;
  border-radius: 30px;
  font-size: 18px;
  font-weight: bold;
  display: block;
  margin: 20px auto 0;
}

/* Download page */
.download-section {
  text-align: center;
  margin-bottom: 40px;
}

.download-title {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 10px;
}

.download-description {
  color: #666;
  margin-bottom: 30px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.qr-container {
  background: white;
  border-radius: 10px;
  padding: 20px;
  display: inline-block;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.platform-links {
  text-align: center;
}

.platform-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
}

.platform-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.platform-button {
  background: #333;
  color: white;
  padding: 12px 25px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
}

/* Responsive */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 20px;
  }
  
  .nav {
    gap: 10px;
  }
  
  .hero-title {
    font-size: 24px;
  }
  
  .hero-subtitle {
    font-size: 16px;
  }
  
  .cards {
    grid-template-columns: 1fr;
  }
  
  .platform-buttons {
    flex-direction: column;
    align-items: center;
  }
}